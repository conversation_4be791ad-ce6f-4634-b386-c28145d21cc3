/*
 * Function: ?Insert_DefaultWeeklyGuildPvpPointSumRecord@CRFWorldDatabase@@QEAA_NXZ
 * Address: 0x1404A6EB0
 */

bool __fastcall CRFWorldDatabase::Insert_DefaultWeeklyGuildPvpPointSumRecord(CRFWorldDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-158h]@1
  char Dst; // [sp+30h] [bp-128h]@4
  unsigned __int64 v6; // [sp+140h] [bp-18h]@4
  CRFWorldDatabase *v7; // [sp+160h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (unsigned __int64)&v4 ^ _security_cookie;
  memset_0(&Dst, 0, 0x100ui64);
  sprintf(&Dst, "{ CALL pInsert_DefaultRecordWeeklyGuildPVPPointSum }");
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v7->vfptr, &Dst, 0);
}
