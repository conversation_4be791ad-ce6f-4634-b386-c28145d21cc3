/*
 * Function: ??0?$_Deque_map@IV?$allocator@I@std@@@std@@IEAA@V?$allocator@I@1@@Z
 * Address: 0x14065ABD0
 */

std::_Container_base *__fastcall std::_Deque_map<unsigned int,std::allocator<unsigned int>>::_Deque_map<unsigned int,std::allocator<unsigned int>>(std::_Container_base *a1, __int64 a2)
{
  std::_Container_base *v3; // [sp+30h] [bp+8h]@1
  __int64 v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  std::_Container_base::_Container_base(a1);
  std::allocator<unsigned int *>::allocator<unsigned int *>(&v3[1], v4);
  return v3;
}
