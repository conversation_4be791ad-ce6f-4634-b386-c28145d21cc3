/*
 * Function: Define_the_symbol__ATL_MIXED::_dynamic_initializer_for__clash___63
 * Address: 0x1406DC2A0
 */

void Define_the_symbol__ATL_MIXED::_dynamic_initializer_for__clash___63()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-28h]@1

  v0 = &v2;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  Define_the_symbol__ATL_MIXED::Thank_you::Thank_you(&Define_the_symbol__ATL_MIXED::clash);
}
