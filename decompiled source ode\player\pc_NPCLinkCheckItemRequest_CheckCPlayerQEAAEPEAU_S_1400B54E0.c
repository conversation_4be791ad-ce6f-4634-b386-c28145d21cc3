/*
 * Function: ?pc_NPCLinkCheckItemRequest_Check@CPlayer@@QEAAEPEAU_STORAGE_POS_INDIV@@@Z
 * Address: 0x1400B54E0
 */

char __fastcall CPlayer::pc_NPCLinkCheckItemRequest_Check(CPlayer *this, _STORAGE_POS_INDIV *pStorage)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v5; // [sp+0h] [bp-48h]@1
  _STORAGE_LIST *v6; // [sp+20h] [bp-28h]@7
  _STORAGE_LIST::_db_con *v7; // [sp+28h] [bp-20h]@7
  _base_fld *v8; // [sp+30h] [bp-18h]@35
  CPlayer *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pStorage && pStorage->byStorageCode < 8 )
  {
    v6 = 0i64;
    v7 = 0i64;
    v6 = v9->m_Param.m_pStoragePtr[pStorage->byStorageCode];
    v7 = _STORAGE_LIST::GetPtrFromSerial(v6, pStorage->wItemSerial);
    if ( v7 && v6 )
    {
      if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v9->m_id.wIndex) == 99 )
      {
        result = 13;
      }
      else if ( v9->m_bInGuildBattle )
      {
        result = 5;
      }
      else if ( v9->m_byStandType == 1 )
      {
        result = 6;
      }
      else if ( CPlayer::IsSiegeMode(v9) )
      {
        result = 7;
      }
      else if ( CPlayer::IsRidingUnit(v9) )
      {
        result = 8;
      }
      else if ( v9->m_pCurMap->m_pMapSet->m_nMapType == 2 )
      {
        result = 9;
      }
      else if ( CGameObject::GetCurSecNum((CGameObject *)&v9->vfptr) == -1 || v9->m_bMapLoading )
      {
        result = 10;
      }
      else if ( v9->m_bCorpse )
      {
        result = 11;
      }
      else if ( _effect_parameter::GetEff_State(&v9->m_EP, 20) )
      {
        result = 12;
      }
      else if ( _effect_parameter::GetEff_State(&v9->m_EP, 28) )
      {
        result = 12;
      }
      else if ( v7 )
      {
        if ( v7->m_byTableCode == 35 )
        {
          v8 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 35, v7->m_wItemIndex);
          if ( v8 )
          {
            if ( v7->m_dwDur )
              result = 0;
            else
              result = 4;
          }
          else
          {
            result = 3;
          }
        }
        else
        {
          result = 2;
        }
      }
      else
      {
        result = 2;
      }
    }
    else
    {
      result = 1;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
