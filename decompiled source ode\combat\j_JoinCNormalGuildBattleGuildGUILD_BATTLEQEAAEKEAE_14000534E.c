/*
 * Function: j_?Join@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAEKEAEAHAEAVCNormalGuildBattleLogger@2@@Z
 * Address: 0x14000534E
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::Join(GUILD_BATTLE::CNormalGuildBattleGuild *this, unsigned int dwSerial, char GuildBattleNumber, int *iMemberInx, GUILD_BATTLE::CNormalGuildBattleLogger *kLogger)
{
  return GUILD_BATTLE::CNormalGuildBattleGuild::Join(this, dwSerial, GuildBattleNumber, iMemberInx, kLogger);
}
