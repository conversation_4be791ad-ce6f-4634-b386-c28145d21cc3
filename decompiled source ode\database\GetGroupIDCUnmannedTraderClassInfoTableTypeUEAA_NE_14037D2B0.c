/*
 * Function: ?GetGroupID@CUnmannedTraderClassInfoTableType@@UEAA_NEGAEAE@Z
 * Address: 0x14037D2B0
 */

char __fastcall CUnmannedTraderClassInfoTableType::GetGroupID(CUnmannedTraderClassInfoTableType *this, char byTableCode, unsigned __int16 wItemTableIndex, char *byClass)
{
  char result; // al@2

  if ( this->m_byTableCode == (unsigned __int8)byTableCode )
  {
    *byClass = this->m_dwID;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
