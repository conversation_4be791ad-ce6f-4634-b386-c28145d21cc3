/*
 * Function: ?AddNovusItem@CLuaLootingMgr@@QEAA_NPEBDPEAVCMapData@@GPEAMGKKE@Z
 * Address: 0x140404EE0
 */

char __fastcall CLuaLootingMgr::AddNovusItem(CLuaLootingMgr *this, const char *strItemCode, CMapData *pMap, unsigned __int16 wLayerIndex, float *fPos, unsigned __int16 wLootRange, unsigned int dwOverlapCnt, unsigned int dwItemNum, char byCreateType)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v12; // [sp+0h] [bp-68h]@1
  unsigned int dwIndex; // [sp+20h] [bp-48h]@7
  CLuaLooting_Novus_Item::_State *v14; // [sp+28h] [bp-40h]@9
  CLuaLooting_Novus_Item *v15; // [sp+30h] [bp-38h]@9
  unsigned __int8 v16; // [sp+38h] [bp-30h]@9
  _base_fld *v17; // [sp+40h] [bp-28h]@9
  unsigned int v18; // [sp+48h] [bp-20h]@9
  char v19; // [sp+4Ch] [bp-1Ch]@9
  char v20; // [sp+4Dh] [bp-1Bh]@9
  unsigned __int8 j; // [sp+4Eh] [bp-1Ah]@9
  unsigned int v22; // [sp+50h] [bp-18h]@19
  CLuaLootingMgr *v23; // [sp+70h] [bp+8h]@1
  char *szRecordCode; // [sp+78h] [bp+10h]@1
  CMapData *v25; // [sp+80h] [bp+18h]@1
  unsigned __int16 v26; // [sp+88h] [bp+20h]@1

  v26 = wLayerIndex;
  v25 = pMap;
  szRecordCode = (char *)strItemCode;
  v23 = this;
  v9 = &v12;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  if ( pMap && fPos )
  {
    dwIndex = US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::SearchSlotIndex<US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::ORDER_INC>(
                &v23->m_Loot_Novus_ItemArEx,
                &CLuaLooting_Novus_Item::_State::ms_cEmpty);
    if ( dwIndex == -1 )
    {
      result = 0;
    }
    else
    {
      v14 = US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::GetStateAtPtr(
              &v23->m_Loot_Novus_ItemArEx,
              dwIndex);
      v15 = US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::GetAtPtr(
              &v23->m_Loot_Novus_ItemArEx,
              dwIndex);
      v16 = -1;
      v17 = 0i64;
      v18 = 0;
      v19 = 0;
      v20 = 0;
      for ( j = 0; (signed int)j < 37; ++j )
      {
        v17 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + j, szRecordCode);
        if ( v17 )
        {
          v16 = j;
          break;
        }
      }
      if ( IsOverLapItem(v16) )
        v18 = dwOverlapCnt;
      else
        v18 = GetItemDurPoint(v16, v17->m_dwIndex);
      v19 = GetDefItemUpgSocketNum(v16, v17->m_dwIndex);
      if ( (signed int)(unsigned __int8)v19 > 0 )
        v20 = rand() % (unsigned __int8)v19 + 1;
      v22 = GetBitAfterSetLimSocket(v20);
      v15->m_Item.m_byTableCode = v16;
      v15->m_Item.m_wItemIndex = v17->m_dwIndex;
      v15->m_Item.m_dwDur = v18;
      v15->m_Item.m_dwLv = v22;
      v15->m_byCreateType = byCreateType;
      v15->m_dwLootCount = dwItemNum;
      v15->m_wLootRange = wLootRange;
      v15->m_fLootPos[0] = *fPos;
      v15->m_fLootPos[1] = fPos[1];
      v15->m_fLootPos[2] = fPos[2];
      v15->m_pMap = v25;
      v15->m_wLayerIndex = v26;
      v14->m_bExist = 1;
      ++v23->m_dwAddNodeCount;
      v23->m_dwNextLootingTime = v23->m_dwLoopLootingTerm + GetLoopTime();
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
