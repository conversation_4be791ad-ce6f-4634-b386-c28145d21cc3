/*
 * Function: ?CheckSellComplete@CUnmannedTraderUserInfo@@QEAAEPEAVCPlayer@@0KKPEAVCLogFile@@@Z
 * Address: 0x1403564E0
 */

char __fastcall CUnmannedTraderUserInfo::CheckSellComplete(CUnmannedTraderUserInfo *this, CPlayer *pkSellPlayer, CPlayer *pkBuyer, unsigned int dwRegistSerial, unsigned int dwRealPrice, CLogFile *pkLogger)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char *v8; // rax@6
  char v9; // al@7
  CUnmannedTraderRegistItemInfo *v10; // rax@13
  CUnmannedTraderRegistItemInfo *v11; // rax@16
  unsigned __int16 v12; // ax@16
  CUnmannedTraderRegistItemInfo *v13; // rax@17
  char *v14; // rax@17
  CUnmannedTraderRegistItemInfo *v15; // rax@17
  unsigned int v16; // eax@20
  __int64 v17; // [sp+0h] [bp-B8h]@1
  CLogFile *v18; // [sp+20h] [bp-98h]@6
  int v19; // [sp+28h] [bp-90h]@12
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > result; // [sp+38h] [bp-80h]@4
  _STORAGE_LIST::_db_con *v21; // [sp+58h] [bp-60h]@16
  bool v22; // [sp+60h] [bp-58h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v23; // [sp+68h] [bp-50h]@4
  char v24; // [sp+80h] [bp-38h]@7
  char v25; // [sp+81h] [bp-37h]@15
  char v26; // [sp+82h] [bp-36h]@19
  char v27; // [sp+83h] [bp-35h]@21
  char v28; // [sp+84h] [bp-34h]@22
  __int64 v29; // [sp+88h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v30; // [sp+90h] [bp-28h]@4
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *_Right; // [sp+98h] [bp-20h]@4
  int v32; // [sp+A0h] [bp-18h]@17
  CUnmannedTraderUserInfo *v33; // [sp+C0h] [bp+8h]@1
  CPlayer *v34; // [sp+C8h] [bp+10h]@1
  CPlayer *v35; // [sp+D0h] [bp+18h]@1
  int dwRegistSeriala; // [sp+D8h] [bp+20h]@1

  dwRegistSeriala = dwRegistSerial;
  v35 = pkBuyer;
  v34 = pkSellPlayer;
  v33 = this;
  v6 = &v17;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v29 = -2i64;
  CUnmannedTraderUserInfo::Find(v33, &result, dwRegistSerial);
  v30 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
          &v33->m_vecRegistItemInfo,
          &v23);
  _Right = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)v30;
  v22 = std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator==(
          (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&result._Mycont,
          (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v30->_Mycont);
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v23);
  if ( v22 )
  {
    if ( pkLogger )
    {
      v8 = CPlayerDB::GetCharNameA(&v34->m_Param);
      LODWORD(v18) = dwRegistSeriala;
      CLogFile::Write(
        pkLogger,
        "CUnmannedTraderRegistItemInfo::CheckSellComplete(...)\r\n"
        "\t\terror_used_invaled_item_local_sellinfo_lack!\r\n"
        "\t\tName(%s) Serial(%u) Invalid DB Data!\r\n"
        "\t\tFind( dwRegistSeria(%u) ) NULL!\r\n",
        v8,
        v34->m_id.dwSerial);
    }
    v24 = 37;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    v9 = v24;
  }
  else if ( v34 && v35 && pkLogger )
  {
    v11 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator*(&result);
    v12 = CUnmannedTraderRegistItemInfo::GetItemSerial(v11);
    v21 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v34->m_Param.m_dbInven.m_nListNum, v12);
    if ( v21 )
    {
      v16 = CPlayerDB::GetDalant(&v34->m_Param);
      if ( CanAddMoneyForMaxLimMoney(dwRealPrice, v16) )
      {
        v28 = 0;
        std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
        v9 = v28;
      }
      else
      {
        v27 = 34;
        std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
        v9 = v27;
      }
    }
    else
    {
      v13 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator*(&result);
      v32 = CUnmannedTraderRegistItemInfo::GetItemSerial(v13);
      v14 = CPlayerDB::GetCharNameA(&v34->m_Param);
      v19 = v32;
      LODWORD(v18) = dwRegistSeriala;
      CLogFile::Write(
        pkLogger,
        "CUnmannedTraderRegistItemInfo::CheckSellComplete(...)\r\n"
        "\t\terror_used_invaled_item_local_sellinfo_lack!\r\n"
        "\t\tName(%s) Serial(%u) Invalid DB Data!\r\n"
        "\t\tdwRegistSerial(%u)\r\n"
        "\t\tpkPlayer->m_Param.m_dbInven.GetPtrFromSerial( m_wItemSerial(%u) ) == NULL!\r\n",
        v14,
        v34->m_id.dwSerial);
      v15 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator*(&result);
      CUnmannedTraderRegistItemInfo::ClearToWaitState(v15);
      if ( *(&g_Player.m_bOper + 50856 * v33->m_wInx) )
        CUnmannedTraderUserInfo::NotifyRegistItem(v33);
      v26 = 37;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
      v9 = v26;
    }
  }
  else
  {
    if ( pkLogger )
    {
      v19 = dwRegistSeriala;
      v18 = pkLogger;
      CLogFile::Write(
        pkLogger,
        "CUnmannedTraderRegistItemInfo::CheckSellComplete(...)\r\n"
        "\t\t( NULL == pkSellPlayer(%p) || NULL == pkBuyer(%p) || NULL == pkLogger(%p) )\r\n"
        "\t\tdwRegistSeria(%u) error_used_internal_error!\r\n",
        v34,
        v35);
    }
    v10 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator*(&result);
    CUnmannedTraderRegistItemInfo::ClearToWaitState(v10);
    if ( *(&g_Player.m_bOper + 50856 * v33->m_wInx) )
      CUnmannedTraderUserInfo::NotifyRegistItem(v33);
    v25 = 99;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    v9 = v25;
  }
  return v9;
}
