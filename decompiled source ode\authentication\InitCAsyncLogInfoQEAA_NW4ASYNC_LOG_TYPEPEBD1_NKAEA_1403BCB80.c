/*
 * Function: ?Init@CAsyncLogInfo@@QEAA_NW4ASYNC_LOG_TYPE@@PEBD1_NKAEAVCLogFile@@@Z
 * Address: 0x1403BCB80
 */

char __usercall CAsyncLogInfo::Init@<al>(CAsyncLogInfo *this@<rcx>, ASYNC_LOG_TYPE eType@<edx>, const char *szDirPath@<r8>, const char *szTypeName@<r9>, signed __int64 a5@<rax>, bool bAddDateFileName, unsigned int dwUpdateFileNameDelay, CLogFile *logLoading)
{
  void *v8; // rsp@1
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v12; // rax@28
  __int64 v13; // [sp-20h] [bp-29B8h]@1
  const char *v14; // [sp+0h] [bp-2998h]@7
  char *v15; // [sp+8h] [bp-2990h]@13
  char DstBuf; // [sp+20h] [bp-2978h]@10
  char v17; // [sp+21h] [bp-2977h]@10
  int v18; // [sp+2824h] [bp-174h]@10
  char szTime; // [sp+2840h] [bp-158h]@11
  char v20; // [sp+2841h] [bp-157h]@11
  char *v21; // [sp+2950h] [bp-48h]@17
  char *v22; // [sp+2958h] [bp-40h]@19
  char *v23; // [sp+2960h] [bp-38h]@21
  CMyTimer *v24; // [sp+2968h] [bp-30h]@30
  CMyTimer *v25; // [sp+2970h] [bp-28h]@27
  __int64 v26; // [sp+2978h] [bp-20h]@4
  CMyTimer *v27; // [sp+2980h] [bp-18h]@28
  unsigned __int64 v28; // [sp+2988h] [bp-10h]@4
  CAsyncLogInfo *v29; // [sp+29A0h] [bp+8h]@1
  ASYNC_LOG_TYPE v30; // [sp+29A8h] [bp+10h]@1
  const char *v31; // [sp+29B0h] [bp+18h]@1
  char *Str; // [sp+29B8h] [bp+20h]@1

  Str = (char *)szTypeName;
  v31 = szDirPath;
  v30 = eType;
  v29 = this;
  v8 = alloca(a5);
  v9 = &v13;
  for ( i = 2668i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v26 = -2i64;
  v28 = (unsigned __int64)&v13 ^ _security_cookie;
  if ( (eType & 0x80000000) != 0 || (signed int)eType >= 15 || !szTypeName )
  {
    v14 = szTypeName;
    CLogFile::Write(
      logLoading,
      "CAsyncLogInfo::Init( int iType(%d), char * szDirPath(%s), char * szTypeName(%s) ) :  iType Invalid!",
      eType,
      szDirPath);
    return 0;
  }
  v29->m_eType = eType;
  if ( !szDirPath )
    return 0;
  DstBuf = 0;
  memset(&v17, 0, 0x27FFui64);
  v18 = 0;
  if ( bAddDateFileName )
  {
    szTime = 0;
    memset(&v20, 0, 0xFFui64);
    if ( !GetDateTimeStr(&szTime) )
      return 0;
    v15 = &szTime;
    v14 = Str;
    v18 = sprintf_s(&DstBuf, 0x2800ui64, "%s/%s_%s.log", v31);
  }
  else
  {
    v14 = szTypeName;
    v18 = sprintf_s(&DstBuf, 0x2800ui64, "%s/%s.log", szDirPath);
  }
  if ( v18 > 0 )
  {
    v21 = (char *)operator new[](v18 + 1);
    v29->m_szLogFileName = v21;
    if ( v29->m_szLogFileName )
    {
      strcpy_s(v29->m_szLogFileName, v18 + 1, &DstBuf);
      DeleteFileA(v29->m_szLogFileName);
      v14 = Str;
      v18 = sprintf_s(&DstBuf, 0x2800ui64, "%s/%s", v31);
      v22 = (char *)operator new[](v18 + 1);
      v29->m_szLogDirPath = v22;
      if ( v29->m_szLogDirPath )
      {
        strcpy_s(v29->m_szLogDirPath, v18 + 1, &DstBuf);
        v18 = strlen_0(Str);
        v23 = (char *)operator new[](v18 + 1);
        v29->m_szTypeName = v23;
        if ( v29->m_szTypeName )
        {
          strcpy_s(v29->m_szTypeName, v18 + 1, Str);
          if ( dwUpdateFileNameDelay == -1 )
          {
            result = 1;
          }
          else if ( dwUpdateFileNameDelay >= 0x2710 )
          {
            v25 = (CMyTimer *)operator new(0x18ui64);
            if ( v25 )
            {
              CMyTimer::CMyTimer(v25);
              v27 = (CMyTimer *)v12;
            }
            else
            {
              v27 = 0i64;
            }
            v24 = v27;
            v29->m_pkTimer = v27;
            if ( v29->m_pkTimer )
            {
              CMyTimer::BeginTimer(v29->m_pkTimer, dwUpdateFileNameDelay);
              result = 1;
            }
            else
            {
              result = 0;
            }
          }
          else
          {
            result = 0;
          }
        }
        else
        {
          LODWORD(v15) = v18;
          v14 = Str;
          CLogFile::Write(
            logLoading,
            "CAsyncLogInfo::Init( int iType(%d), char * szDirPath(%s), char * szTypeName(%s) ) :  m_szTypeName = new char"
            "[iLen(%d)+1] NULL!",
            v30,
            v31);
          result = 0;
        }
      }
      else
      {
        LODWORD(v15) = v18;
        v14 = Str;
        CLogFile::Write(
          logLoading,
          "CAsyncLogInfo::Init( int iType(%d), char * szDirPath(%s), szTypeName(%s) ) :  m_szLogDirPath = new char[iLen(%d)+1] NULL!",
          v30,
          v31);
        result = 0;
      }
    }
    else
    {
      LODWORD(v15) = v18;
      v14 = Str;
      CLogFile::Write(
        logLoading,
        "CAsyncLogInfo::Init( int iType(%d), char * szDirPath(%s), char * szTypeName(%s) ) :  m_szLogFileName = new char["
        "iLen(%d)+1] NULL!",
        v30,
        v31);
      result = 0;
    }
  }
  else
  {
    LODWORD(v15) = v18;
    v14 = Str;
    CLogFile::Write(
      logLoading,
      "CAsyncLogInfo::Init( int iType(%d), char * szDirPath(%s), char * szTypeName(%s) ) :  sprintf_s( %%s/%%s_%%d.log ) Ret(%d)!",
      v30,
      v31);
    result = 0;
  }
  return result;
}
