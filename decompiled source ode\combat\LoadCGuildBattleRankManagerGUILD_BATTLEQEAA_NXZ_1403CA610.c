/*
 * Function: ?Load@CGuildBattleRankManager@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403CA610
 */

char __fastcall GUILD_BATTLE::CGuildBattleRankManager::Load(GUILD_BATTLE::CGuildBattleRankManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  char j; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CGuildBattleRankManager *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; (signed int)(unsigned __int8)j < 3; ++j )
  {
    if ( !GUILD_BATTLE::CGuildBattleRankManager::Load(v6, j) )
      return 0;
  }
  return 1;
}
