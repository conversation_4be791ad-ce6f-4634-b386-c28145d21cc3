/*
 * Function: ?Select_BattleTournamentInfo@CRFWorldDatabase@@QEAA_NPEAUTournament<PERSON><PERSON><PERSON>@@H@Z
 * Address: 0x1404C5EA0
 */

char __fastcall CRFWorldDatabase::Select_BattleTournamentInfo(CRFWorldDatabase *this, TournamentWinner *pWinnerInfo, int nMax)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-108h]@1
  void *SQLStmt; // [sp+20h] [bp-E8h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-E0h]@22
  char DstBuf; // [sp+40h] [bp-C8h]@4
  SQLLEN v10; // [sp+D8h] [bp-30h]@22
  __int16 v11; // [sp+E4h] [bp-24h]@9
  int j; // [sp+E8h] [bp-20h]@14
  unsigned __int64 v13; // [sp+F8h] [bp-10h]@4
  CRFWorldDatabase *v14; // [sp+110h] [bp+8h]@1
  TournamentWinner *v15; // [sp+118h] [bp+10h]@1
  int v16; // [sp+120h] [bp+18h]@1

  v16 = nMax;
  v15 = pWinnerInfo;
  v14 = this;
  v3 = &v6;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = (unsigned __int64)&v6 ^ _security_cookie;
  sprintf_s(
    &DstBuf,
    0x80ui64,
    "Select top %d CharacterSerial,CharacterName,BattleWinGrade from tbl_battletournament",
    (unsigned int)nMax);
  if ( v14->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v14->vfptr, &DstBuf);
  if ( v14->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v14->vfptr) )
  {
    v11 = SQLExecDirect_0(v14->m_hStmtSelect, &DstBuf, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 1;
      }
      else
      {
        SQLStmt = v14->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v11, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v11, v14->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      for ( j = 0; ; ++j )
      {
        v11 = SQLFetch_0(v14->m_hStmtSelect);
        if ( v11 == 100 )
          break;
        if ( v11 && v11 != 1 )
        {
          SQLStmt = v14->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v11, &DstBuf, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v11, v14->m_hStmtSelect);
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          return 0;
        }
        if ( j >= v16 )
          break;
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v14->m_hStmtSelect, 1u, 4, &v15[j], 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = (void *)17;
        v11 = SQLGetData_0(v14->m_hStmtSelect, 2u, 1, v15[j].wszCharName, 17i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v14->m_hStmtSelect, 3u, -6, &v15[j].byGrade, 0i64, &v10);
        if ( v11 && v11 != 1 )
        {
          if ( v11 != 100 )
          {
            SQLStmt = v14->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v11, &DstBuf, "SQLGetData", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v11, v14->m_hStmtSelect);
          }
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          return 0;
        }
      }
      if ( v14->m_hStmtSelect )
        SQLCloseCursor_0(v14->m_hStmtSelect);
      if ( v14->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v14->vfptr, "%s Success", &DstBuf);
      result = 1;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v14->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 0;
  }
  return result;
}
