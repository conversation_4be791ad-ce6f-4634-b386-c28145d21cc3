/*
 * Function: ?MoveScreenPoint@CMapExtend@@QEAAXPEAVCPoint@@@Z
 * Address: 0x1401A1730
 */

void __fastcall CMapExtend::MoveScreenPoint(CMapExtend *this, CPoint *pt)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-68h]@1
  CPoint *v5; // [sp+20h] [bp-48h]@6
  CPoint *v6; // [sp+28h] [bp-40h]@6
  CPoint *v7; // [sp+30h] [bp-38h]@6
  int v8[3]; // [sp+48h] [bp-20h]@6
  int v9; // [sp+54h] [bp-14h]@9
  CMapExtend *v10; // [sp+70h] [bp+8h]@1

  v10 = this;
  v2 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v10->m_bSetArea && __PAIR__(v10->m_ptStartScreen.y, v10->m_ptStartScreen.x) != __PAIR__(pt->y, pt->x) )
  {
    v10->m_bMove = 1;
    v5 = pt;
    v6 = &v10->m_ptStartScreen;
    v7 = &v10->m_ptMoveScreen;
    *(&v10->m_ptMoveScreen.x + v10->m_Rate.nStandard) = *(&pt->x + v10->m_Rate.nStandard);
    v8[v10->m_Rate.nStandard] = *(&v6->x + v10->m_Rate.nStandard) - *(&v5->x + v10->m_Rate.nStandard);
    if ( v8[v10->m_Rate.nStandard] < 0 )
      v8[v10->m_Rate.nStandard] = -v8[v10->m_Rate.nStandard];
    if ( v8[v10->m_Rate.nStandard] > 200 )
      v9 = 0;
    v8[v10->m_Rate.nPartner] = (signed int)ffloor((float)v8[v10->m_Rate.nStandard] * v10->m_Rate.fSide[v10->m_Rate.nPartner]);
    if ( *(&v6->x + v10->m_Rate.nPartner) < *(&v5->x + v10->m_Rate.nPartner) )
      *(&v7->x + v10->m_Rate.nPartner) = v8[v10->m_Rate.nPartner] + *(&v6->x + v10->m_Rate.nPartner);
    else
      *(&v7->x + v10->m_Rate.nPartner) = *(&v6->x + v10->m_Rate.nPartner) - v8[v10->m_Rate.nPartner];
  }
}
