/*
 * Function: ??0CPartyModeKillMonsterExpNotify@@QEAA@XZ
 * Address: 0x14008E4F0
 */

void __fastcall CPartyModeKillMonsterExpNotify::CPartyModeKillMonsterExpNotify(CPartyModeKillMonsterExpNotify *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  CPartyModeKillMonsterExpNotify *v4; // [sp+40h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_bKillMonster = 0;
  v4->m_byMemberCnt = 0;
  `eh vector constructor iterator'(
    v4->m_kInfo,
    0x10ui64,
    8,
    (void (__cdecl *)(void *))CPartyModeKillMonsterExpNotify::CExpInfo::CExpInfo,
    (void (__cdecl *)(void *))CPartyModeKillMonsterExpNotify::CExpInfo::~CExpInfo);
}
