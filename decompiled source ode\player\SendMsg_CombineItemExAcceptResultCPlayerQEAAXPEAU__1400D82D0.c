/*
 * Function: ?SendMsg_CombineItemExAcceptResult@CPlayer@@QEAAXPEAU_combine_ex_item_accept_result_zocl@@@Z
 * Address: 0x1400D82D0
 */

void __fastcall CPlayer::SendMsg_CombineItemExAcceptResult(CPlayer *this, _combine_ex_item_accept_result_zocl *pSend)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-58h]@1
  char pbyType; // [sp+34h] [bp-24h]@4
  char v6; // [sp+35h] [bp-23h]@4
  CPlayer *v7; // [sp+60h] [bp+8h]@1

  v7 = this;
  v2 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  pbyType = 7;
  v6 = 36;
  CNetProcess::LoadSendMsg(unk_1414F2088, v7->m_ObjID.m_wIndex, &pbyType, &pSend->byErrCode, 1u);
}
