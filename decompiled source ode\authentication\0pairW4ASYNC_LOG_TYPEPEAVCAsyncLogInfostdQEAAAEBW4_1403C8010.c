/*
 * Function: ??0?$pair@W4<PERSON>YNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@std@@QEAA@AEBW4ASYNC_LOG_TYPE@@AEBQEAVCAsyncLogInfo@@@Z
 * Address: 0x1403C8010
 */

void __fastcall std::pair<enum  ASYNC_LOG_TYPE,CAsyncLogInfo *>::pair<enum  ASYNC_LOG_TYPE,CAsyncLogInfo *>(std::pair<enum ASYNC_LOG_TYPE,CAsyncLogInfo *> *this, ASYNC_LOG_TYPE *_Val1, CAsyncLogInfo *const *_Val2)
{
  this->first = *_Val1;
  this->second = *_Val2;
}
