/*
 * Function: ?IsApplyPcbangPrimium@CPlayer@@QEBA_NXZ
 * Address: 0x140067D00
 */

bool __fastcall CPlayer::IsApplyPcbangPrimium(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager *v3; // rax@4
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *pUser; // [sp+30h] [bp+8h]@1

  pUser = this;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = CTSingleton<CNationSettingManager>::Instance();
  return CNationSettingManager::IsApplyPcbangPrimium(v3, pUser);
}
