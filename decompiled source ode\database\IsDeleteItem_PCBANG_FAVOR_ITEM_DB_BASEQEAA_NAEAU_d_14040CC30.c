/*
 * Function: ?IsDeleteItem@_PCBANG_FAVOR_ITEM_DB_BASE@@QEAA_NAEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x14040CC30
 */

char __fastcall _PCBANG_FAVOR_ITEM_DB_BASE::IsDeleteItem(_PCBANG_FAVOR_ITEM_DB_BASE *this, _STORAGE_LIST::_db_con *Item)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  _PCBANG_FAVOR_ITEM_DB_BASE *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  for ( j = 0; j < 50; ++j )
  {
    if ( v6->lnUID[j] != -1i64 && v6->lnUID[j] == Item->m_lnUID )
      return 1;
  }
  return 0;
}
