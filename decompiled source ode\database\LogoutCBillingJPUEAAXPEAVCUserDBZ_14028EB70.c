/*
 * Function: ?Logout@CBillingJP@@UEAAXPEAVCUserDB@@@Z
 * Address: 0x14028EB70
 */

void __fastcall CBillingJP::Logout(CBillingJP *this, CUserDB *pUserDB)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@6
  __int64 v5; // [sp+0h] [bp-A8h]@1
  char Dst; // [sp+38h] [bp-70h]@6
  char v7; // [sp+45h] [bp-63h]@6
  char v8; // [sp+55h] [bp-53h]@7
  __int16 v9; // [sp+5Ch] [bp-4Ch]@6
  char pbyType; // [sp+74h] [bp-34h]@8
  char v11; // [sp+75h] [bp-33h]@8
  unsigned __int64 v12; // [sp+90h] [bp-18h]@4
  CBillingJP *v13; // [sp+B0h] [bp+8h]@1
  CUserDB *v14; // [sp+B8h] [bp+10h]@1

  v14 = pUserDB;
  v13 = this;
  v2 = &v5;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( v13->m_bOper && pUserDB->m_BillingInfo.iType <= 100 )
  {
    v9 = pUserDB->m_BillingInfo.iType;
    memcpy_0(&Dst, pUserDB->m_szAccountID, 0xDui64);
    v4 = inet_ntoa((struct in_addr)v14->m_dwIP);
    memcpy_0(&v7, v4, 0x10ui64);
    if ( v14 != (CUserDB *)-79074 )
      memcpy_0(&v8, v14->m_BillingInfo.szCMS, 7ui64);
    pbyType = 29;
    v11 = 6;
    CNetProcess::LoadSendMsg(qword_1414F20A0, 0, &pbyType, &Dst, 0x26u);
  }
}
