/*
 * Function: ?IsOutExtraStopPos@CPlayer@@QEAA_NPEAM@Z
 * Address: 0x140057790
 */

bool __usercall CPlayer::IsOutExtraStopPos@<al>(CPlayer *this@<rcx>, float *pfStopPos@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CPlayer *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  GetSqrt(v7->m_fTarPos, pfStopPos);
  return a3 > 20.0;
}
