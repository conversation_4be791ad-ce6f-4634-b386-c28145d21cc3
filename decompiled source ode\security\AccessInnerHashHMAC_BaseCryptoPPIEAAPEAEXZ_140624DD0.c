/*
 * Function: ?AccessInnerHash@HMAC_Base@CryptoPP@@IEAAPEAEXZ
 * Address: 0x140624DD0
 */

char *__fastcall CryptoPP::HMAC_Base::AccessInnerHash(CryptoPP::HMAC_Base *this)
{
  __int64 v1; // rax@1
  __int64 v2; // ST28_8@1
  CryptoPP::HMAC_Base *v4; // [sp+40h] [bp+8h]@1

  v4 = this;
  LODWORD(v1) = ((int (*)(void))this->vfptr[9].__vecDelDtor)();
  v2 = (unsigned int)(2 * (*(int (__fastcall **)(__int64))(*(_QWORD *)v1 + 64i64))(v1));
  return &CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v4->m_buf)[v2];
}
