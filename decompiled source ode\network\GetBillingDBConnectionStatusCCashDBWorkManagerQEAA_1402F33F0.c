/*
 * Function: ?GetBillingDBConnectionStatus@CCashDBWorkManager@@QEAA_NXZ
 * Address: 0x1402F33F0
 */

bool __fastcall CCashDBWorkManager::GetBillingDBConnectionStatus(CCashDBWorkManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CCashDBWorkManager *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return CashDbWorker::GetBillingDBConnectionStatus(v5->m_pWorker);
}
