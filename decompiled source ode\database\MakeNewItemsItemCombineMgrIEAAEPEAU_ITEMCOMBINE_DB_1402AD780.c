/*
 * Function: ?MakeNewItems@ItemCombineMgr@@IEAAEPEAU_ITEMCOMBINE_DB_BASE@@PEAU_combine_ex_item_accept_request_clzo@@PEAU_combine_ex_item_accept_result_zocl@@@Z
 * Address: 0x1402AD780
 */

char __fastcall ItemCombineMgr::MakeNewItems(ItemCombineMgr *this, _ITEMCOMBINE_DB_BASE *pPlayerItemDB, _combine_ex_item_accept_request_clzo *pRecv, _combine_ex_item_accept_result_zocl *pSend)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // ecx@51
  float *v7; // rax@53
  CPlayer *v8; // rcx@53
  CPlayer *v9; // rdx@53
  int v10; // ecx@60
  __int64 v12; // [sp+0h] [bp-258h]@1
  bool bAdd[8]; // [sp+20h] [bp-238h]@50
  char byCreateCode[8]; // [sp+28h] [bp-230h]@53
  CMapData *pMap; // [sp+30h] [bp-228h]@53
  unsigned __int16 wLayerIndex; // [sp+38h] [bp-220h]@53
  float *pStdPos; // [sp+40h] [bp-218h]@53
  bool bHide; // [sp+48h] [bp-210h]@53
  int j; // [sp+50h] [bp-208h]@4
  char Dst[48]; // [sp+68h] [bp-1F0h]@13
  char pbyRewardTypeList[56]; // [sp+98h] [bp-1C0h]@13
  unsigned __int64 lnUIDs; // [sp+D0h] [bp-188h]@4
  char v23; // [sp+D8h] [bp-180h]@4
  char v24; // [sp+194h] [bp-C4h]@13
  unsigned __int8 v25; // [sp+195h] [bp-C3h]@16
  char *v26; // [sp+198h] [bp-C0h]@33
  int nTableCode; // [sp+1A0h] [bp-B8h]@33
  int nIndex; // [sp+1A4h] [bp-B4h]@33
  _STORAGE_LIST::_db_con pItem; // [sp+1B8h] [bp-A0h]@33
  char v30; // [sp+1F4h] [bp-64h]@36
  char v31; // [sp+1F5h] [bp-63h]@38
  char v32; // [sp+1F6h] [bp-62h]@41
  _TimeItem_fld *v33; // [sp+1F8h] [bp-60h]@47
  __time32_t Time; // [sp+204h] [bp-54h]@49
  _STORAGE_LIST::_db_con *v35; // [sp+218h] [bp-40h]@50
  int v36; // [sp+228h] [bp-30h]@5
  int v37; // [sp+22Ch] [bp-2Ch]@9
  int v38; // [sp+230h] [bp-28h]@11
  int v39; // [sp+234h] [bp-24h]@22
  int v40; // [sp+238h] [bp-20h]@28
  int v41; // [sp+23Ch] [bp-1Ch]@39
  unsigned __int64 v42; // [sp+240h] [bp-18h]@4
  ItemCombineMgr *v43; // [sp+260h] [bp+8h]@1
  _ITEMCOMBINE_DB_BASE *pCombineDB; // [sp+268h] [bp+10h]@1
  _combine_ex_item_accept_request_clzo *v45; // [sp+270h] [bp+18h]@1

  v45 = pRecv;
  pCombineDB = pPlayerItemDB;
  v43 = this;
  v4 = &v12;
  for ( i = 148i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v42 = (unsigned __int64)&v12 ^ _security_cookie;
  j = 0;
  lnUIDs = 0i64;
  memset(&v23, 0, 0xB8ui64);
  if ( pRecv->SelectItemBuff.bySelectNum >= 24 )
    v36 = 24;
  else
    v36 = pRecv->SelectItemBuff.bySelectNum;
  if ( v36 >= pPlayerItemDB->m_bySelectItemCount )
  {
    v38 = pPlayerItemDB->m_bySelectItemCount;
  }
  else
  {
    if ( pRecv->SelectItemBuff.bySelectNum >= 24 )
      v37 = 24;
    else
      v37 = pRecv->SelectItemBuff.bySelectNum;
    v38 = v37;
  }
  v24 = v38;
  memset_0(Dst, 0, 0x18ui64);
  memset_0(pbyRewardTypeList, 0, 0x18ui64);
  if ( pCombineDB->m_byDlgType )
  {
    if ( pCombineDB->m_byDlgType == 1 )
    {
      if ( pCombineDB->m_byItemListNum >= 24 )
        v39 = 24;
      else
        v39 = pCombineDB->m_byItemListNum;
      v24 = v39;
      memset_0(Dst, 1, (unsigned __int8)v39);
    }
  }
  else
  {
    for ( j = 0; j < (unsigned __int8)v24; ++j )
    {
      v25 = v45->SelectItemBuff.bySelectIndexList[j];
      if ( (signed int)v25 < 24 )
        Dst[v25] = 1;
    }
  }
  for ( j = 0; ; ++j )
  {
    v40 = pCombineDB->m_byItemListNum >= 24 ? 24 : pCombineDB->m_byItemListNum;
    if ( j >= v40 )
      break;
    if ( Dst[j] && _COMBINEKEY::IsFilled(&pCombineDB->m_List[j].Key) )
    {
      v26 = &pCombineDB->m_List[j].Key.byRewardIndex;
      nTableCode = pCombineDB->m_List[j].Key.byTableCode;
      nIndex = pCombineDB->m_List[j].Key.wItemIndex;
      _STORAGE_LIST::_db_con::_db_con(&pItem);
      pItem.m_byTableCode = nTableCode;
      pItem.m_wItemIndex = nIndex;
      if ( IsOverLapItem(nTableCode) )
        pItem.m_dwDur = *((_DWORD *)v26 + 1);
      else
        pItem.m_dwDur = GetItemDurPoint(nTableCode, nIndex);
      v30 = GetItemKindCode(nTableCode);
      if ( v30 )
      {
        if ( v30 == 1 )
        {
          pItem.m_dwDur = *((_DWORD *)v26 + 1);
          pItem.m_dwLv = *((_DWORD *)v26 + 2);
        }
      }
      else if ( *((_DWORD *)v26 + 2) == 0xFFFFFFF )
      {
        v31 = GetDefItemUpgSocketNum(nTableCode, nIndex);
        if ( (signed int)(unsigned __int8)v31 <= 0 )
          v41 = 0;
        else
          v41 = rand() % (unsigned __int8)v31 + 1;
        v32 = v41;
        pItem.m_dwLv = GetBitAfterSetLimSocket(v41);
      }
      else
      {
        pItem.m_dwLv = *((_DWORD *)v26 + 2);
      }
      if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&v43->m_pMaster->m_Param.m_dbInven.m_nListNum) == 255 )
      {
        v7 = v43->m_pMaster->m_fCurPos;
        v8 = v43->m_pMaster;
        v9 = v43->m_pMaster;
        bHide = 0;
        pStdPos = v7;
        wLayerIndex = v8->m_wMapLayerIndex;
        pMap = v9->m_pCurMap;
        byCreateCode[0] = 3;
        *(_QWORD *)bAdd = 0i64;
        CreateItemBox(&pItem, v43->m_pMaster, 0xFFFFFFFF, 0, 0i64, 3, pMap, wLayerIndex, v7, 0);
        pbyRewardTypeList[j] = 2;
      }
      else
      {
        pItem.m_wSerial = CPlayerDB::GetNewItemSerial(&v43->m_pMaster->m_Param);
        v33 = TimeItem::FindTimeRec(nTableCode, nIndex);
        if ( v33 && v33->m_nCheckType )
        {
          _time32(&Time);
          pItem.m_byCsMethod = v33->m_nCheckType;
          pItem.m_dwT = v33->m_nUseTime + Time;
          pItem.m_dwLendRegdTime = Time;
        }
        bAdd[0] = 1;
        v35 = CPlayer::Emb_AddStorage(v43->m_pMaster, 0, (_STORAGE_LIST::_storage_con *)&pItem.m_bLoad, 0, 1);
        if ( !v35 )
        {
          v6 = v43->m_pMaster->m_ObjID.m_wIndex;
          *(_QWORD *)bAdd = (char *)v43->m_pMaster + 50608;
          CMgrAvatorItemHistory::add_storage_fail(
            &CPlayer::s_MgrItemHistory,
            v6,
            &pItem,
            "RewardChangeClass - Emb_AddStorage() Fail",
            *(char **)bAdd);
          continue;
        }
        CPlayer::SendMsg_RewardAddItem(v43->m_pMaster, &pItem, 0);
        pbyRewardTypeList[j] = 1;
        *(&lnUIDs + j) = v35->m_lnUID;
      }
      if ( pCombineDB->m_byItemListNum == 1 )
      {
        if ( pCombineDB->m_dwResultEffectType == 1 )
          CPlayer::SendMsg_FanfareItem(v43->m_pMaster, 1, &pItem, 0i64);
      }
      else
      {
        CPlayer::SendMsg_FanfareItem(v43->m_pMaster, 1, &pItem, 0i64);
      }
    }
  }
  v10 = v43->m_pMaster->m_ObjID.m_wIndex;
  pMap = (CMapData *)v43->m_pMaster->m_szItemHistoryFileName;
  CMgrAvatorItemHistory::combine_ex_reward_item(
    &CPlayer::s_MgrItemHistory,
    v10,
    v24,
    pCombineDB,
    pbyRewardTypeList,
    &lnUIDs,
    (char *)pMap);
  return 0;
}
