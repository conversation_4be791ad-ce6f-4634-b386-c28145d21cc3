/*
 * Function: ?Initialize@?$DL_PrivateKey_EC@VECP@CryptoPP@@@CryptoPP@@QEAAXAEAVRandomNumberGenerator@2@AEBV?$DL_GroupParameters_EC@VECP@CryptoPP@@@2@@Z
 * Address: 0x140559550
 */

int __fastcall CryptoPP::DL_PrivateKey_EC<CryptoPP::ECP>::Initialize(__int64 a1, __int64 a2, __int64 a3)
{
  int result; // eax@2

  if ( a3 )
    result = (**(int (__fastcall ***)(_QWORD, _QWORD, _QWORD))(a1 + 16))(
               a1 + 16,
               a2,
               a3 + *(_DWORD *)(*(_QWORD *)(a3 + 8) + 4i64) + 8);
  else
    result = (**(int (__fastcall ***)(_QWORD, _QWORD, _QWORD))(a1 + 16))(a1 + 16, a2, 0i64);
  return result;
}
