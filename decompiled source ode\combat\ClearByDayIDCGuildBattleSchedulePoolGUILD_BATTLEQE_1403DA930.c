/*
 * Function: ?ClearByDayID@CGuildBattleSchedulePool@GUILD_BATTLE@@QEAAXI@Z
 * Address: 0x1403DA930
 */

void __fastcall GUILD_BATTLE::CGuildBattleSchedulePool::ClearByDayID(GUILD_BATTLE::CGuildBattleSchedulePool *this, unsigned int uiDayID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int v5; // [sp+20h] [bp-18h]@10
  unsigned int v6; // [sp+24h] [bp-14h]@10
  unsigned int j; // [sp+28h] [bp-10h]@14
  GUILD_BATTLE::CGuildBattleSchedulePool *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v8->m_dwMaxScheduleCnt && v8->m_ppkSchedule && v8->m_uiMapCnt && (!uiDayID || uiDayID == 1) )
  {
    v5 = 0;
    v6 = 0;
    if ( uiDayID )
    {
      if ( uiDayID == 1 )
      {
        v5 = 23 * v8->m_uiMapCnt;
        v6 = 2 * v5;
      }
    }
    else
    {
      v6 = 23 * v8->m_uiMapCnt;
    }
    for ( j = v5; j < v6; ++j )
    {
      if ( v8->m_ppkSchedule[j] )
        GUILD_BATTLE::CGuildBattleSchedule::Clear(v8->m_ppkSchedule[j]);
    }
  }
}
