/*
 * Function: ??0?$_Deque_const_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@QEAA@_KPEBV_Container_base@1@@Z
 * Address: 0x140600CE0
 */

__int64 __fastcall std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>(__int64 a1, __int64 a2, __int64 a3)
{
  __int64 v4; // [sp+30h] [bp+8h]@1
  __int64 v5; // [sp+38h] [bp+10h]@1
  __int64 v6; // [sp+40h] [bp+18h]@1

  v6 = a3;
  v5 = a2;
  v4 = a1;
  std::_Ranit<CryptoPP::MeterFilter::MessageRange,__int64,CryptoPP::MeterFilter::MessageRange const *,CryptoPP::MeterFilter::MessageRange const &>::_Ranit<CryptoPP::MeterFilter::MessageRange,__int64,CryptoPP::MeterFilter::MessageRange const *,CryptoPP::MeterFilter::MessageRange const &>();
  *(_QWORD *)(v4 + 16) = v6;
  *(_QWORD *)(v4 + 24) = v5;
  return v4;
}
