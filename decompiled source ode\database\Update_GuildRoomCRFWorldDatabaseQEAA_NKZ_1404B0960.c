/*
 * Function: ?Update_GuildRoom@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x1404B0960
 */

bool __fastcall CRFWorldDatabase::Update_GuildRoom(CRFWorldDatabase *this, unsigned int dwGuildSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-158h]@1
  char Dst; // [sp+30h] [bp-128h]@4
  unsigned __int64 v7; // [sp+140h] [bp-18h]@4
  CRFWorldDatabase *v8; // [sp+160h] [bp+8h]@1
  unsigned int v9; // [sp+168h] [bp+10h]@1

  v9 = dwGuildSerial;
  v8 = this;
  v2 = &v5;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = (unsigned __int64)&v5 ^ _security_cookie;
  memset_0(&Dst, 0, 0x100ui64);
  sprintf(&Dst, "update tbl_GuildRoom set dck = 1, logdate = getdate() where dck = 0 and guildserial = %d", v9);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v8->vfptr, &Dst, 1);
}
