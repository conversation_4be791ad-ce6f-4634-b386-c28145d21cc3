/*
 * Function: j_??9?$_Vector_const_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEBA_NAEBV01@@Z
 * Address: 0x14000DF4E
 */

bool __fastcall std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator!=(std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *_Right)
{
  return std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator!=(this, _Right);
}
