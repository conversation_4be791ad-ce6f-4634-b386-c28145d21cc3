/*
 * Function: ?Initialize@AutoMineMachine@@QEAA_NEE@Z
 * Address: 0x1402D0570
 */

char __fastcall AutoMineMachine::Initialize(AutoMineMachine *this, char byRace, char byCollisionType)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v5; // eax@8
  unsigned int v6; // ecx@8
  unsigned int v7; // edx@8
  unsigned int v8; // eax@8
  unsigned int v9; // ecx@8
  unsigned int v10; // edx@8
  char result; // al@9
  __int64 v12; // [sp+0h] [bp-468h]@1
  bool bDate[4]; // [sp+20h] [bp-448h]@8
  char Dest; // [sp+40h] [bp-428h]@8
  char v15; // [sp+41h] [bp-427h]@8
  unsigned __int64 v16; // [sp+450h] [bp-18h]@4
  AutoMineMachine *v17; // [sp+470h] [bp+8h]@1
  char v18; // [sp+478h] [bp+10h]@1
  char v19; // [sp+480h] [bp+18h]@1

  v19 = byCollisionType;
  v18 = byRace;
  v17 = this;
  v3 = &v12;
  for ( i = 280i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v16 = (unsigned __int64)&v12 ^ _security_cookie;
  CreateDirectoryA("..\\ZoneServerLog\\systemlog\\Concession", 0i64);
  v17->m_byRace = v18;
  v17->m_byCollisionType = v19;
  if ( !v17->m_byCollisionType )
    v17->m_dwMiningTerm = 30000;
  if ( v17->m_byCollisionType == 1 )
    v17->m_dwMiningTerm = 60000;
  Dest = 0;
  memset(&v15, 0, 0x3FFui64);
  v5 = GetKorLocalTime();
  v6 = v17->m_byCollisionType;
  v7 = v17->m_byRace;
  *(_DWORD *)bDate = v5;
  sprintf(&Dest, "..\\ZoneServerLog\\systemlog\\Concession\\AutoMine_R%d_T%d_%d.log", v7, v6);
  CLogFile::SetWriteLogFile(&v17->m_Log, &Dest, 1, 0, 1, 1);
  v8 = GetKorLocalTime();
  v9 = v17->m_byCollisionType;
  v10 = v17->m_byRace;
  *(_DWORD *)bDate = v8;
  sprintf(&Dest, "..\\ZoneServerLog\\systemlog\\Concession\\err_automineR%d_T%d_%d.log", v10, v9);
  CLogFile::SetWriteLogFile(&v17->m_sysLog, &Dest, 1, 0, 1, 1);
  if ( AutoMineMachine::_InitMineOre(v17) )
  {
    if ( TInventory<_INVENKEY>::create(&v17->m_Inven, 2, 40, 99) )
    {
      result = 1;
    }
    else
    {
      CLogFile::Write(&v17->m_sysLog, "Failed AutoMineMachine[%d]::m_Inven.create()", v17->m_byRace);
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(&v17->m_sysLog, "Failed AutoMineMachine[%d]::_set_mineore()", v17->m_byRace);
    result = 0;
  }
  return result;
}
