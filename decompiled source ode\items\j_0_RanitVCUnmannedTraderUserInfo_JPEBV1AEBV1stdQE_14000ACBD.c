/*
 * Function: j_??0?$_Ranit@VCUnmannedTraderUserInfo@@_JPEBV1@AEBV1@@std@@QEAA@XZ
 * Address: 0x14000ACBD
 */

void __fastcall std::_<PERSON>t<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &>::_<PERSON>t<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &>(std::_<PERSON>t<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &> *this)
{
  std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &>::_<PERSON>t<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &>(this);
}
