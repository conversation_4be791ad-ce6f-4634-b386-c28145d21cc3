/*
 * Function: ?Add@CPartyModeKillMonsterExpNotify@@QEAA_NPEAVCPlayer@@M@Z
 * Address: 0x1401692F0
 */

char __fastcall CPartyModeKillMonsterExpNotify::Add(CPartyModeKillMonsterExpNotify *this, CPlayer *pkMember, float fExp)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v6; // [sp+0h] [bp-28h]@1
  CPartyModeKillMonsterExpNotify *v7; // [sp+30h] [bp+8h]@1
  CPlayer *pkMembera; // [sp+38h] [bp+10h]@1

  pkMembera = pkMember;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pkMember && CPartyPlayer::IsPartyMode(pkMember->m_pPartyMgr) )
  {
    if ( v7->m_byMemberCnt < 8 )
    {
      CPartyModeKillMonsterExpNotify::CExpInfo::SetGetExp(&v7->m_kInfo[v7->m_byMemberCnt], pkMembera, fExp);
      ++v7->m_byMemberCnt;
      result = 1;
    }
    else
    {
      v7->m_byMemberCnt = 8;
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
