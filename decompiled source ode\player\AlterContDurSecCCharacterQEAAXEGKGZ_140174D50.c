/*
 * Function: ?AlterContDurSec@CCharacter@@QEAAXEGKG@Z
 * Address: 0x140174D50
 */

void __fastcall CCharacter::AlterContDurSec(CCharacter *this, char byContCode, unsigned __int16 wListIndex, unsigned int dwStartSec, unsigned __int16 wNewDur)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  bool *v7; // [sp+0h] [bp-18h]@1
  CCharacter *v8; // [sp+20h] [bp+8h]@1

  v8 = this;
  v5 = (__int64 *)&v7;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v7 = &v8->m_SFCont[(unsigned __int8)byContCode][wListIndex].m_bExist;
  if ( *v7 )
  {
    *((_DWORD *)v7 + 2) = dwStartSec;
    *((_WORD *)v7 + 6) = wNewDur;
  }
}
