/*
 * Function: ?Drop@CItemDropMgr@@QEAA_NH@Z
 * Address: 0x1402CFDE0
 */

char __fastcall CItemDropMgr::Drop(CItemDropMgr *this, int nCnt)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@6
  CItemDropMgr *v7; // [sp+40h] [bp+8h]@1
  int v8; // [sp+48h] [bp+10h]@1

  v8 = nCnt;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( nCnt > 0 )
  {
    for ( j = 0; j < v8; ++j )
    {
      if ( !CItemDropMgr::FrontDrop(v7) )
        return 0;
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
