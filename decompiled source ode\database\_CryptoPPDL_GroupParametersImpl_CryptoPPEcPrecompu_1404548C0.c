/*
 * Function: _CryptoPP::DL_GroupParametersImpl_CryptoPP::EcPrecomputation_CryptoPP::ECP__CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__CryptoPP::DL_GroupParameters_CryptoPP::ECPPoint___::DL_GroupParametersImpl_CryptoPP::EcPrecomputation_CryptoPP::ECP__CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__CryptoPP::DL_GroupParameters_CryptoPP::ECPPoint____::_1_::dtor$1
 * Address: 0x1404548C0
 */

void __fastcall CryptoPP::DL_GroupParametersImpl_CryptoPP::EcPrecomputation_CryptoPP::ECP__CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__CryptoPP::DL_GroupParameters_CryptoPP::ECPPoint___::DL_GroupParametersImpl_CryptoPP::EcPrecomputation_CryptoPP::ECP__CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__CryptoPP::DL_GroupParameters_CryptoPP::ECPPoint____::_1_::dtor_1(__int64 a1, __int64 a2)
{
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::~DL_GroupParameters<CryptoPP::ECPPoint>((CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *)(*(_QWORD *)(a2 + 64) + 32i64));
}
