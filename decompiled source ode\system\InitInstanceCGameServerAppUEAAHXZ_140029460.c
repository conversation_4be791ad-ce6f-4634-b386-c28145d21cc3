/*
 * Function: ?InitInstance@CGameServerApp@@UEAAHXZ
 * Address: 0x140029460
 */

__int64 __fastcall CGameServerApp::InitInstance(CGameServerApp *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CRuntimeClass *v3; // rax@5
  struct CDocTemplate *v4; // rax@5
  __int64 result; // rax@10
  __int64 v6; // [sp+0h] [bp-118h]@1
  CRuntimeClass *v7; // [sp+20h] [bp-F8h]@5
  struct CDocTemplate *v8; // [sp+30h] [bp-E8h]@7
  CCommandLineInfo v9; // [sp+48h] [bp-D0h]@7
  CCommandLineInfo v10; // [sp+98h] [bp-80h]@9
  struct CDocTemplate *v11; // [sp+D8h] [bp-40h]@7
  void *v12; // [sp+E0h] [bp-38h]@4
  unsigned int v13; // [sp+E8h] [bp-30h]@10
  unsigned int v14; // [sp+ECh] [bp-2Ch]@11
  __int64 v15; // [sp+F0h] [bp-28h]@4
  CRuntimeClass *v16; // [sp+F8h] [bp-20h]@5
  CRuntimeClass *v17; // [sp+100h] [bp-18h]@5
  struct CDocTemplate *v18; // [sp+108h] [bp-10h]@5
  CGameServerApp *v19; // [sp+120h] [bp+8h]@1

  v19 = this;
  v1 = &v6;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v15 = -2i64;
  AfxEnableControlContainer(0i64);
  CWinApp::SetRegistryKey((CWinApp *)&v19->vfptr, "Local AppWizard-Generated Applications");
  CWinApp::LoadStdProfileSettings((CWinApp *)&v19->vfptr, 4u);
  v12 = CObject::operator new(0xC8ui64, THIS_FILE_1, 78);
  if ( v12 )
  {
    v16 = CGameServerView::GetThisClass();
    v17 = CMainFrame::GetThisClass();
    v3 = CGameServerDoc::GetThisClass();
    v7 = v16;
    LODWORD(v4) = CSingleDocTemplate::CSingleDocTemplate(v12, 128i64, v3, v17);
    v18 = v4;
  }
  else
  {
    v18 = 0i64;
  }
  v11 = v18;
  v8 = v18;
  CWinApp::AddDocTemplate((CWinApp *)&v19->vfptr, v18);
  CCommandLineInfo::CCommandLineInfo(&v9);
  CWinApp::ParseCommandLine((CWinApp *)&v19->vfptr, &v9);
  if ( ATL::operator==(&v9.m_strFileName, "cjdrnrwkd") )
    unk_1799C9AE8 = 1;
  CCommandLineInfo::CCommandLineInfo(&v10);
  if ( (unsigned int)CWinApp::ProcessShellCommand((CWinApp *)&v19->vfptr, &v10) )
  {
    CWnd::ShowWindow(v19->m_pMainWnd, 5);
    CWnd::UpdateWindow(v19->m_pMainWnd);
    v14 = 1;
    CCommandLineInfo::~CCommandLineInfo(&v10);
    CCommandLineInfo::~CCommandLineInfo(&v9);
    result = v14;
  }
  else
  {
    v13 = 0;
    CCommandLineInfo::~CCommandLineInfo(&v10);
    CCommandLineInfo::~CCommandLineInfo(&v9);
    result = v13;
  }
  return result;
}
