/*
 * Function: ?AutoRecover@CPlayer@@QEAAXXZ
 * Address: 0x140056270
 */

void __fastcall CPlayer::AutoRecover(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // xmm0_4@4
  float v4; // xmm0_4@6
  float v5; // xmm0_4@6
  float v6; // xmm0_4@7
  float v7; // xmm0_4@8
  char *v8; // rcx@11
  char *v9; // rax@11
  int v10; // eax@16
  float v11; // xmm0_4@26
  int v12; // eax@28
  int v13; // eax@49
  int v14; // eax@53
  int v15; // eax@55
  signed int v16; // eax@64
  int v17; // eax@66
  int v18; // eax@68
  int v19; // eax@81
  int v20; // eax@82
  int v21; // eax@83
  int v22; // eax@84
  int v23; // eax@85
  int v24; // eax@86
  int v25; // eax@87
  int v26; // eax@88
  int v27; // eax@89
  int v28; // eax@90
  int v29; // eax@91
  int v30; // eax@92
  __int64 v31; // [sp+0h] [bp-D8h]@1
  float *pfStartPos; // [sp+20h] [bp-B8h]@11
  unsigned int v33; // [sp+28h] [bp-B0h]@11
  int v34; // [sp+30h] [bp-A8h]@11
  int v35; // [sp+38h] [bp-A0h]@11
  float v36; // [sp+40h] [bp-98h]@4
  int v37; // [sp+44h] [bp-94h]@14
  int v38; // [sp+48h] [bp-90h]@14
  int v39; // [sp+4Ch] [bp-8Ch]@14
  int v40; // [sp+50h] [bp-88h]@14
  int v41; // [sp+54h] [bp-84h]@14
  int v42; // [sp+58h] [bp-80h]@14
  int v43; // [sp+5Ch] [bp-7Ch]@14
  int v44; // [sp+60h] [bp-78h]@14
  int v45; // [sp+64h] [bp-74h]@17
  int v46; // [sp+68h] [bp-70h]@29
  float v47; // [sp+6Ch] [bp-6Ch]@29
  float v48; // [sp+70h] [bp-68h]@37
  int v49; // [sp+74h] [bp-64h]@71
  float v50; // [sp+78h] [bp-60h]@6
  float v51; // [sp+7Ch] [bp-5Ch]@7
  int *v52; // [sp+80h] [bp-58h]@11
  int v53; // [sp+88h] [bp-50h]@11
  CUserDB *v54; // [sp+90h] [bp-48h]@11
  float *v55; // [sp+98h] [bp-40h]@11
  float v56; // [sp+A0h] [bp-38h]@26
  int v57; // [sp+A4h] [bp-34h]@49
  int v58; // [sp+A8h] [bp-30h]@53
  float v59; // [sp+ACh] [bp-2Ch]@64
  float v60; // [sp+B0h] [bp-28h]@66
  float v61; // [sp+B4h] [bp-24h]@68
  CGameObjectVtbl *v62; // [sp+B8h] [bp-20h]@74
  CGameObjectVtbl *v63; // [sp+C0h] [bp-18h]@82
  CPlayer *v64; // [sp+E0h] [bp+8h]@1

  v64 = this;
  v1 = &v31;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(float *)&v3 = FLOAT_1_0;
  v36 = FLOAT_1_0;
  if ( !v64->m_byUserDgr )
  {
    if ( v64->m_byLastRecvMapIndex == v64->m_pCurMap->m_pMapSet->m_dwIndex )
    {
      v4 = v64->m_fCurPos[0] - v64->m_fLastRecvPos[0];
      abs(v4);
      v50 = v4;
      v5 = v64->m_fCurPos[2] - v64->m_fLastRecvPos[2];
      abs(v5);
      if ( v50 <= v5 )
      {
        v7 = v64->m_fCurPos[2] - v64->m_fLastRecvPos[2];
        abs(v7);
        v51 = v7;
      }
      else
      {
        v6 = v64->m_fCurPos[0] - v64->m_fLastRecvPos[0];
        abs(v6);
        v51 = v6;
      }
      v36 = v51;
    }
    *(float *)&v3 = v36;
    if ( v36 > 400.0 )
    {
      pfStartPos = v64->m_fLastRecvPos;
      CPlayer::OutOfMap(v64, v64->m_pCurMap, v64->m_wMapLayerIndex, 4, v64->m_fLastRecvPos);
      v8 = (char *)v64->m_pCurMap->m_pMapSet;
      LOBYTE(pfStartPos) = 4;
      CPlayer::SendMsg_GotoRecallResult(v64, 0, *v8, v64->m_fLastRecvPos, 4);
      v52 = (int *)v64->m_pCurMap->m_pMapSet;
      v53 = (signed int)ffloor(v36);
      v54 = v64->m_pUserDB;
      v55 = (float *)v64->m_pUserDB->m_szAccountID;
      v9 = CPlayerDB::GetCharNameA(&v64->m_Param);
      v35 = *v52;
      v34 = v53;
      v33 = v54->m_dwAccountSerial;
      pfStartPos = v55;
      CLogFile::Write(
        &stru_1799C9660,
        "move dist check >> char: %s (%d) id: %s (%d) dist: %d, map: %d",
        v9,
        v64->m_dwObjSerial);
      return;
    }
    memcpy_0(v64->m_fLastRecvPos, v64->m_fCurPos, 0xCui64);
    v64->m_byLastRecvMapIndex = v64->m_pCurMap->m_pMapSet->m_dwIndex;
  }
  if ( !CPlayer::IsRidingUnit(v64) )
  {
    v37 = ((int (__fastcall *)(CPlayer *))v64->vfptr->GetHP)(v64);
    v38 = CPlayer::GetFP(v64);
    v39 = CPlayer::GetSP(v64);
    v40 = CPlayer::GetDP(v64);
    v41 = 0;
    v42 = 0;
    v43 = 0;
    v44 = 0;
    if ( !_effect_parameter::GetEff_State(&v64->m_EP, 10) )
    {
      if ( !_effect_parameter::GetEff_State(&v64->m_EP, 15) )
      {
        v10 = ((int (__fastcall *)(CPlayer *))v64->vfptr->GetMaxHP)(v64);
        if ( v37 < v10 )
        {
          v45 = 0;
          if ( v64->m_bMove )
            v45 = v64->m_byMoveType ? 0 : 2;
          else
            v45 = v64->m_byStandType == 1 ? 6 : 4;
          if ( v45 > 0 )
          {
            v11 = (float)v45;
            v56 = (float)v45;
            _effect_parameter::GetEff_Rate(&v64->m_EP, 14);
            *(float *)&v3 = v56 * v11;
            v41 = (signed int)ffloor(*(float *)&v3);
          }
        }
      }
      if ( !_effect_parameter::GetEff_State(&v64->m_EP, 16) )
      {
        v12 = CPlayer::GetMaxFP(v64);
        if ( v38 < v12 )
        {
          _effect_parameter::GetEff_Plus(&v64->m_EP, 25);
          v46 = (signed int)ffloor(*(float *)&v3);
          _effect_parameter::GetEff_Rate(&v64->m_EP, 15);
          v47 = *(float *)&v3;
          if ( v64->m_bMove )
          {
            if ( !v64->m_byMoveType )
            {
              *(float *)&v3 = (float)(1.0 * v47) + (float)(v46 / 100);
              v42 = (signed int)ffloor(*(float *)&v3);
            }
          }
          else if ( v64->m_byStandType == 1 )
          {
            *(float *)&v3 = (float)(3.0 * v47) + (float)(v46 / 100);
            v42 = (signed int)ffloor(*(float *)&v3);
          }
          else
          {
            *(float *)&v3 = (float)(2.0 * v47) + (float)(v46 / 100);
            v42 = (signed int)ffloor(*(float *)&v3);
          }
        }
      }
      if ( !_effect_parameter::GetEff_State(&v64->m_EP, 17) )
      {
        _effect_parameter::GetEff_Rate(&v64->m_EP, 16);
        v48 = *(float *)&v3;
        if ( v64->m_bMove )
        {
          if ( v64->m_bMove && !v64->m_byMoveType )
          {
            *(float *)&v3 = 1.0 * v48;
            v43 = (signed int)ffloor(1.0 * v48);
          }
        }
        else if ( v64->m_byStandType == 1 )
        {
          *(float *)&v3 = 3.0 * v48;
          v43 = (signed int)ffloor(3.0 * v48);
        }
        else
        {
          *(float *)&v3 = 2.0 * v48;
          v43 = (signed int)ffloor(2.0 * v48);
        }
      }
    }
    if ( !v43 )
    {
      *(float *)&v3 = v36;
      if ( v36 > 0.0 )
      {
        if ( v64->m_bMove
          && v64->m_byMoveType == 1
          && (v57 = v64->m_byPosRaceTown, v13 = CPlayerDB::GetRaceCode(&v64->m_Param), v57 != v13) )
        {
          v43 = -2;
        }
        else if ( v64->m_bMove )
        {
          if ( v64->m_byMoveType == 2 )
          {
            v58 = v64->m_byPosRaceTown;
            v14 = CPlayerDB::GetRaceCode(&v64->m_Param);
            if ( v58 != v14 )
              v43 = -60;
          }
        }
      }
    }
    v15 = CPlayer::GetMaxDP(v64);
    if ( v40 < v15 )
      v44 = s_nRevDefPoint;
    _effect_parameter::GetEff_Plus(&v64->m_EP, 32);
    if ( *(float *)&v3 != 0.0 )
    {
      _effect_parameter::GetEff_Plus(&v64->m_EP, 32);
      *(float *)&v3 = (float)v41 + *(float *)&v3;
      v41 = (signed int)ffloor(*(float *)&v3);
    }
    _effect_parameter::GetEff_Plus(&v64->m_EP, 33);
    if ( *(float *)&v3 != 0.0 )
    {
      _effect_parameter::GetEff_Plus(&v64->m_EP, 33);
      *(float *)&v3 = (float)v42 + *(float *)&v3;
      v42 = (signed int)ffloor(*(float *)&v3);
    }
    _effect_parameter::GetEff_Plus(&v64->m_EP, 34);
    if ( *(float *)&v3 != 0.0 )
    {
      _effect_parameter::GetEff_Plus(&v64->m_EP, 34);
      *(float *)&v3 = (float)v43 + *(float *)&v3;
      v43 = (signed int)ffloor(*(float *)&v3);
    }
    _effect_parameter::GetEff_Have(&v64->m_EP, 12);
    if ( *(float *)&v3 > 0.0 )
    {
      _effect_parameter::GetEff_Have(&v64->m_EP, 12);
      v59 = *(float *)&v3;
      v16 = ((int (__fastcall *)(CPlayer *))v64->vfptr->GetMaxHP)(v64);
      *(float *)&v3 = (float)v41 + (float)(v59 * (float)v16);
      v41 = (signed int)ffloor(*(float *)&v3);
    }
    _effect_parameter::GetEff_Have(&v64->m_EP, 13);
    if ( *(float *)&v3 > 0.0 )
    {
      _effect_parameter::GetEff_Have(&v64->m_EP, 13);
      v60 = *(float *)&v3;
      v17 = CPlayer::GetMaxFP(v64);
      *(float *)&v3 = (float)v42 + (float)(v60 * (float)v17);
      v42 = (signed int)ffloor(*(float *)&v3);
    }
    _effect_parameter::GetEff_Have(&v64->m_EP, 14);
    if ( *(float *)&v3 > 0.0 )
    {
      _effect_parameter::GetEff_Have(&v64->m_EP, 14);
      v61 = *(float *)&v3;
      v18 = CPlayer::GetMaxSP(v64);
      v43 = (signed int)ffloor((float)v43 + (float)(v61 * (float)v18));
    }
    if ( v41 )
    {
      if ( v41 < 0 )
      {
        v49 = ((int (__fastcall *)(CPlayer *))v64->vfptr->GetMaxHP)(v64) / 10;
        if ( v41 + v37 <= v49 )
          v41 = 0;
      }
      if ( v41 )
      {
        v62 = v64->vfptr;
        ((void (__fastcall *)(CPlayer *, _QWORD, _QWORD))v62->SetHP)(v64, (unsigned int)(v41 + v37), 0i64);
      }
    }
    if ( v42 )
      CPlayer::SetFP(v64, v42 + v38, 0);
    if ( v43 )
      CPlayer::SetSP(v64, v43 + v39, 0);
    if ( v44 )
      CPlayer::SetDP(v64, v44 + v40, 0);
    v19 = ((int (__fastcall *)(_QWORD))v64->vfptr->GetMaxHP)(v64);
    if ( v37 > v19 )
    {
      v20 = ((int (__fastcall *)(_QWORD))v64->vfptr->GetMaxHP)(v64);
      v63 = v64->vfptr;
      ((void (__fastcall *)(CPlayer *, _QWORD, _QWORD))v63->SetHP)(v64, (unsigned int)v20, 0i64);
    }
    v21 = CPlayer::GetMaxDP(v64);
    if ( v40 > v21 )
    {
      v22 = CPlayer::GetMaxDP(v64);
      CPlayer::SetDP(v64, v22, 0);
    }
    v23 = CPlayer::GetMaxFP(v64);
    if ( v38 > v23 )
    {
      v24 = CPlayer::GetMaxFP(v64);
      CPlayer::SetFP(v64, v24, 0);
    }
    v25 = CPlayer::GetMaxSP(v64);
    if ( v39 > v25 )
    {
      v26 = CPlayer::GetMaxSP(v64);
      CPlayer::SetSP(v64, v26, 0);
    }
    v27 = ((int (__fastcall *)(CPlayer *))v64->vfptr->GetHP)(v64);
    if ( v37 != v27
      || (v28 = CPlayer::GetFP(v64), v38 != v28)
      || (v29 = CPlayer::GetSP(v64), v39 != v29)
      || (v30 = CPlayer::GetDP(v64), v40 != v30) )
    {
      CPlayer::SendMsg_Recover(v64);
    }
  }
}
