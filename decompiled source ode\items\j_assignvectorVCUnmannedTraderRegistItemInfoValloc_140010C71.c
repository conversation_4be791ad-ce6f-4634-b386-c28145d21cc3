/*
 * Function: j_?assign@?$vector@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@QEAAX_KAEBVCUnmannedTraderRegistItemInfo@@@Z
 * Address: 0x140010C71
 */

void __fastcall std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::assign(std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *this, unsigned __int64 _Count, CUnmannedTraderRegistItemInfo *_Val)
{
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::assign(this, _Count, _<PERSON>);
}
