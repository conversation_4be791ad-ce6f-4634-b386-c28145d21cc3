/*
 * Function: j_?Request@CMoveMapLimitInfoList@@QEAAEHHHKHPEADPEAVCMoveMapLimitRightInfo@@@Z
 * Address: 0x14000D9C2
 */

char __fastcall CMoveMapLimitInfoList::Request(CMoveMapLimitInfoList *this, int iLimitType, int iRequetType, int iMapInx, unsigned int dwStoreRecordIndex, int iUserInx, char *pRequest, CMoveMapLimitRightInfo *pkRight)
{
  return CMoveMapLimitInfoList::Request(
           this,
           iLimitType,
           iRequetType,
           iMapInx,
           dwStoreRecordIndex,
           iUserInx,
           pRequest,
           pkRight);
}
