/*
 * Function: ?wa_PartyLootShareSystem@@YAXPEAU_CLID@@E@Z
 * Address: 0x140046E80
 */

void __fastcall wa_PartyLootShareSystem(_CLID *pidBoss, char byLootShareMode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  CPartyPlayer *v5; // [sp+20h] [bp-28h]@4
  int j; // [sp+28h] [bp-20h]@8
  bool *v7; // [sp+30h] [bp-18h]@10
  CPlayer *v8; // [sp+38h] [bp-10h]@11
  _CLID *v9; // [sp+50h] [bp+8h]@1
  char v10; // [sp+58h] [bp+10h]@1

  v10 = byLootShareMode;
  v9 = pidBoss;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = (CPartyPlayer *)((char *)&g_PartyPlayer + 128 * (unsigned __int64)v9->wIndex);
  if ( v5->m_id.dwSerial == v9->dwSerial
    && v5->m_bLogin
    && CPartyPlayer::IsPartyBoss(v5)
    && CPartyPlayer::SetLootShareMode(v5, v10) )
  {
    for ( j = 0; j < 8; ++j )
    {
      v7 = &v5->m_pPartyMember[j]->m_bLogin;
      if ( !v7 )
        break;
      v8 = &g_Player + *((_WORD *)v7 + 12);
      CPlayer::SendMsg_PartyAlterLootShareResult(v8, v10);
    }
  }
}
