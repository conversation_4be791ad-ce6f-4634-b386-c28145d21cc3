/*
 * Function: ??$_Uninit_copy@PEAGPEAGV?$allocator@G@std@@@std@@YAPEAGPEAG00AEAV?$allocator@G@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140650CF0
 */

signed __int64 __fastcall std::_Uninit_copy<unsigned short *,unsigned short *,std::allocator<unsigned short>>(const void *a1, __int64 a2, char *a3)
{
  __int64 v4; // [sp+20h] [bp-18h]@1
  signed __int64 v5; // [sp+28h] [bp-10h]@1

  v4 = (a2 - (signed __int64)a1) >> 1;
  v5 = (signed __int64)&a3[2 * v4];
  if ( v4 )
    memmove_s(a3, 2 * v4, a1, 2 * v4);
  return v5;
}
