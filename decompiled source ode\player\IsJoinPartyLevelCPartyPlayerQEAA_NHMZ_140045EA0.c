/*
 * Function: ?IsJoinPartyLevel@CPartyPlayer@@QEAA_NHM@Z
 * Address: 0x140045EA0
 */

char __fastcall CPartyPlayer::IsJoinPartyLevel(CPartyPlayer *this, int nJoinerLevel, float fProf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@5
  int v7; // eax@11
  int v8; // eax@13
  __int64 v9; // [sp+0h] [bp-78h]@1
  CPlayer *v10; // [sp+20h] [bp-58h]@5
  unsigned __int8 v11; // [sp+28h] [bp-50h]@8
  unsigned __int8 v12; // [sp+29h] [bp-4Fh]@8
  CPartyPlayer **v13; // [sp+30h] [bp-48h]@8
  int j; // [sp+38h] [bp-40h]@8
  CPlayer *v15; // [sp+40h] [bp-38h]@11
  int v16; // [sp+48h] [bp-30h]@11
  CGameObjectVtbl *v17; // [sp+50h] [bp-28h]@11
  int v18; // [sp+58h] [bp-20h]@13
  CGameObjectVtbl *v19; // [sp+60h] [bp-18h]@13
  CPartyPlayer *v20; // [sp+80h] [bp+8h]@1
  int v21; // [sp+88h] [bp+10h]@1

  v21 = nJoinerLevel;
  v20 = this;
  v3 = &v9;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( CPartyPlayer::IsPartyMode(v20) )
  {
    v11 = -1;
    v12 = 0;
    v13 = CPartyPlayer::GetPtrPartyMember(v20);
    for ( j = 0; j < 8 && v13[j]; ++j )
    {
      v15 = &g_Player + v13[j]->m_wZoneIndex;
      v16 = v11;
      v17 = v15->vfptr;
      v7 = ((int (__fastcall *)(CPlayer *))v17->GetLevel)(v15);
      if ( v16 > v7 )
        v11 = ((int (__fastcall *)(CPlayer *))v15->vfptr->GetLevel)(v15);
      v18 = v12;
      v19 = v15->vfptr;
      v8 = ((int (__fastcall *)(CPlayer *))v19->GetLevel)(v15);
      if ( v18 < v8 )
        v12 = ((int (__fastcall *)(CPlayer *))v15->vfptr->GetLevel)(v15);
    }
    if ( v11 == 255 || !v12 || v11 > (signed int)v12 )
      return 0;
    if ( v21 > v12 && (float)(v21 - v11) > (float)(fProf + 10.0) )
      return 0;
    if ( v21 < v11 && (float)(v12 - v21) > (float)(fProf + 10.0) )
      return 0;
  }
  else
  {
    v10 = &g_Player + v20->m_wZoneIndex;
    v5 = ((int (__fastcall *)(CPlayer *))v10->vfptr->GetLevel)(v10);
    if ( (float)abs_0(v5 - v21) > (float)(fProf + 10.0) )
      return 0;
  }
  return 1;
}
