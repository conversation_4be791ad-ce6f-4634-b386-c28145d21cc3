/*
 * Function: ?RewardItem@CNormalGuildBattle@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403E6960
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::RewardItem(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  const char *v3; // rax@4
  const char *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-58h]@1
  unsigned int v6; // [sp+20h] [bp-38h]@4
  unsigned int v7; // [sp+30h] [bp-28h]@4
  char *v8; // [sp+38h] [bp-20h]@4
  unsigned int v9; // [sp+40h] [bp-18h]@4
  char *v10; // [sp+48h] [bp-10h]@4
  GUILD_BATTLE::CNormalGuildBattle *v11; // [sp+60h] [bp+8h]@1

  v11 = this;
  v1 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v11->m_k1P);
  v8 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v11->m_k1P);
  v3 = GUILD_BATTLE::CNormalGuildBattleGuild::GetColorName(&v11->m_k1P);
  v6 = v7;
  GUILD_BATTLE::CNormalGuildBattleLogger::Log(&v11->m_kLogger, "CNormalGuildBattle::RewardItem() %s %s(%d)", v3, v8);
  GUILD_BATTLE::CNormalGuildBattleGuild::RewardItem(&v11->m_k1P, &v11->m_kLogger);
  v9 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v11->m_k2P);
  v10 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v11->m_k2P);
  v4 = GUILD_BATTLE::CNormalGuildBattleGuild::GetColorName(&v11->m_k2P);
  v6 = v9;
  GUILD_BATTLE::CNormalGuildBattleLogger::Log(&v11->m_kLogger, "CNormalGuildBattle::RewardItem() %s %s(%d)", v4, v10);
  GUILD_BATTLE::CNormalGuildBattleGuild::RewardItem(&v11->m_k2P, &v11->m_kLogger);
}
