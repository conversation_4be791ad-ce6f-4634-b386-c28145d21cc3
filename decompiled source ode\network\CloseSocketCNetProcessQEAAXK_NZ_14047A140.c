/*
 * Function: ?CloseSocket@CNetProcess@@QEAAXK_N@Z
 * Address: 0x14047A140
 */

void __fastcall CNetProcess::CloseSocket(CNetProcess *this, unsigned int dwSocketIndex, bool bSlowClose)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CNetProcess *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CNetWorking::CloseSocket(v6->m_pNetwork, v6->m_nIndex, dwSocketIndex, bSlowClose);
}
