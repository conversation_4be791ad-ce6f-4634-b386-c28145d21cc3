/*
 * Function: ??$fill@PEAVCUnmannedTraderSchedule@@V1@@std@@YAXPEAVCUnmannedTraderSchedule@@0AEBV1@@Z
 * Address: 0x140396990
 */

void __fastcall std::fill<CUnmannedTraderSchedule *,CUnmannedTraderSchedule>(CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last, CUnmannedTraderSchedule *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderSchedule *_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Fill<CUnmannedTraderSchedule *,CUnmannedTraderSchedule>(_Firsta, _Last, _Val);
}
