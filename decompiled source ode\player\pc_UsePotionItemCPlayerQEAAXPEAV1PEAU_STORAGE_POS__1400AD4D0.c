/*
 * Function: ?pc_UsePotionItem@CPlayer@@QEAAXPEAV1@PEAU_STORAGE_POS_INDIV@@@Z
 * Address: 0x1400AD4D0
 */

void __fastcall CPlayer::pc_UsePotionItem(CPlayer *this, CPlayer *pTargetPlayer, _STORAGE_POS_INDIV *pItem)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@19
  int v6; // eax@23
  int v7; // eax@26
  __int64 v8; // [sp+0h] [bp-78h]@1
  unsigned int nCurTime; // [sp+20h] [bp-58h]@19
  _DWORD bSend[2]; // [sp+28h] [bp-50h]@19
  char v11; // [sp+30h] [bp-48h]@4
  _STORAGE_LIST *v12; // [sp+38h] [bp-40h]@8
  _STORAGE_LIST::_db_con *pUseItem; // [sp+40h] [bp-38h]@8
  _PotionItem_fld *pfB; // [sp+48h] [bp-30h]@8
  char v15; // [sp+50h] [bp-28h]@8
  unsigned int dwCurrTime; // [sp+54h] [bp-24h]@8
  int nCashType; // [sp+58h] [bp-20h]@16
  char v18; // [sp+5Ch] [bp-1Ch]@29
  _base_fld *v19; // [sp+60h] [bp-18h]@32
  int v20; // [sp+68h] [bp-10h]@19
  unsigned int v21; // [sp+6Ch] [bp-Ch]@19
  CPlayer *pUsePlayer; // [sp+80h] [bp+8h]@1
  CPlayer *pTargetCharacter; // [sp+88h] [bp+10h]@1
  _STORAGE_POS_INDIV *v24; // [sp+90h] [bp+18h]@1

  v24 = pItem;
  pTargetCharacter = pTargetPlayer;
  pUsePlayer = this;
  v3 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = 0;
  if ( pItem && !pItem->byStorageCode && pTargetPlayer )
  {
    v12 = pUsePlayer->m_Param.m_pStoragePtr[pItem->byStorageCode];
    pUseItem = 0i64;
    pfB = 0i64;
    v15 = 0;
    dwCurrTime = -1;
    if ( CPlayer::IsRidingUnit(pUsePlayer) )
    {
      v11 = 7;
    }
    else
    {
      pUseItem = _STORAGE_LIST::GetPtrFromSerial(v12, v24->wItemSerial);
      if ( pUseItem )
      {
        if ( pUseItem->m_byTableCode == 13 )
        {
          if ( pUseItem->m_bLock )
          {
            v11 = 11;
          }
          else
          {
            nCashType = GetUsePcCashType(pUseItem->m_byTableCode, pUseItem->m_wItemIndex);
            if ( !CPlayer::IsUsableAccountType(pUsePlayer, nCashType) )
            {
              CPlayer::SendMsg_PremiumCashItemUse(pUsePlayer, 0xFFFFu);
              CPlayer::SendMsg_UsePotionResult(pUsePlayer, 25, v24->wItemSerial, 0);
              return;
            }
            dwCurrTime = timeGetTime();
            pfB = (_PotionItem_fld *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 13, pUseItem->m_wItemIndex);
            if ( pfB )
            {
              if ( pfB->m_nUseState == 3 || CPlayerDB::GetHP(&pUsePlayer->m_Param) )
              {
                v6 = CPlayerDB::GetLevel(&pUsePlayer->m_Param);
                if ( v6 >= pfB->m_nLevelLim )
                {
                  if ( pfB->m_nUpLevelLim == -1
                    || (v7 = CPlayerDB::GetLevel(&pUsePlayer->m_Param), v7 <= pfB->m_nUpLevelLim) )
                  {
                    v11 = CPotionMgr::UsePotion(
                            &g_PotionMgr,
                            pUsePlayer,
                            (CCharacter *)&pTargetCharacter->vfptr,
                            pfB,
                            dwCurrTime);
                  }
                  else
                  {
                    v11 = 25;
                  }
                }
                else
                {
                  v11 = 25;
                }
              }
              else
              {
                v11 = 6;
              }
            }
            else
            {
              v20 = pUseItem->m_wItemIndex;
              v21 = pUseItem->m_byTableCode;
              v5 = CPlayerDB::GetCharNameA(&pUsePlayer->m_Param);
              bSend[0] = v20;
              nCurTime = v21;
              CLogFile::Write(
                &stru_1799C8E78,
                "CPlayer::pc_UsePotionItem(...) : User %s(%u) Item Table(%u) Index(%u) Not Exist!",
                v5,
                pUsePlayer->m_dwObjSerial);
              v11 = 48;
            }
          }
        }
        else
        {
          v11 = 3;
        }
      }
      else
      {
        CPlayer::SendMsg_AdjustAmountInform(pUsePlayer, v24->byStorageCode, v24->wItemSerial, 0);
        v11 = 2;
      }
    }
    v18 = 0;
    if ( !v11 )
    {
      LOBYTE(bSend[0]) = 0;
      LOBYTE(nCurTime) = 0;
      v18 = CPlayer::Emb_AlterDurPoint(pUsePlayer, v12->m_nListCode, pUseItem->m_byStorageIndex, -1, 0, 0);
      if ( pfB->m_bIsCash == 1 )
        CMgrAvatorItemHistory::cash_item_use(
          &CPlayer::s_MgrItemHistory,
          pUsePlayer->m_ObjID.m_wIndex,
          pUseItem,
          pUsePlayer->m_szItemHistoryFileName);
      v19 = CRecordData::GetRecord(&stru_184A6FC18, pfB->m_strEffCode);
      if ( v19 && (v19[13].m_dwIndex == 50 || v19[13].m_dwIndex == 51) )
        CMgrAvatorItemHistory::consume_del_item(
          &CPlayer::s_MgrItemHistory,
          pUsePlayer->m_ObjID.m_wIndex,
          pUseItem,
          pUsePlayer->m_szItemHistoryFileName);
      CPlayer::SetPotionActDelay(pUsePlayer, pfB->m_nDelayType, dwCurrTime, (signed int)ffloor(pfB->m_fActDelay));
    }
    CPlayer::SendMsg_UsePotionResult(pUsePlayer, v11, v24->wItemSerial, v18);
  }
}
