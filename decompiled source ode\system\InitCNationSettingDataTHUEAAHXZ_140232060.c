/*
 * Function: ?Init@CNationSettingDataTH@@UEAAHXZ
 * Address: 0x140232060
 */

signed __int64 __fastcall CNationSettingDataTH::Init(CNationSettingDataTH *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CNationSettingDataTH *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CNationSettingData::GetFireGuardEnableSetting((CNationSettingData *)&v5->vfptr) )
  {
    if ( CNationSettingData::GetTimeLimitEnableSetting((CNationSettingData *)&v5->vfptr) )
    {
      MyMessageBox("CNationSettingDataTH::Init()", "Time Limit System Setting Eanbled Invalid!");
      CLogFile::Write(&stru_1799C8F30, "CNationSettingDataTH::Init() : Time Limit System Setting Eanbled Invalid!");
      result = 4294967294i64;
    }
    else if ( (unsigned __int8)((int (__fastcall *)(CNationSettingDataTH *))v5->vfptr->ReadSystemPass)(v5) )
    {
      result = 0i64;
    }
    else
    {
      MyMessageBox("CNationSettingDataTH::Init()", "All Event Error!");
      CLogFile::Write(&stru_1799C8F30, "CNationSettingDataTH::Init() : All Event Error!");
      result = 0xFFFFFFFFi64;
    }
  }
  else
  {
    MyMessageBox("CNationSettingDataTH::Init()", "FireGuard Setting Enabled Invalid!");
    CLogFile::Write(&stru_1799C8F30, "CNationSettingDataTH::Init() : FireGuard Setting Enabled Invalid!");
    result = 0xFFFFFFFFi64;
  }
  return result;
}
