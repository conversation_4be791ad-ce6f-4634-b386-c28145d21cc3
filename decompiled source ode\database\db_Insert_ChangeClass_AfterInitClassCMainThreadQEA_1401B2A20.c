/*
 * Function: ?db_Insert_ChangeClass_AfterInitClass@CMainThread@@QEAAEKEPEAD0HEGEEEEE@Z
 * Address: 0x1401B2A20
 */

char __fastcall CMainThread::db_Insert_ChangeClass_AfterInitClass(CMainThread *this, unsigned int dwCharacSerial, char byType, char *szPrevClassCode, char *szNextClassCode, int nClassInitCnt, char byLastClassGrade, unsigned __int16 dwYear, char byMonth, char byDay, char byHour, char byMin, char bySec)
{
  __int64 *v13; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v16; // [sp+0h] [bp-78h]@1
  char *szNextClass; // [sp+20h] [bp-58h]@4
  int v18; // [sp+28h] [bp-50h]@4
  char v19; // [sp+30h] [bp-48h]@4
  unsigned __int16 v20; // [sp+38h] [bp-40h]@4
  char v21; // [sp+40h] [bp-38h]@4
  char v22; // [sp+48h] [bp-30h]@4
  char v23; // [sp+50h] [bp-28h]@4
  char v24; // [sp+58h] [bp-20h]@4
  char v25; // [sp+60h] [bp-18h]@4
  CMainThread *v26; // [sp+80h] [bp+8h]@1

  v26 = this;
  v13 = &v16;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v13 = -858993460;
    v13 = (__int64 *)((char *)v13 + 4);
  }
  v25 = bySec;
  v24 = byMin;
  v23 = byHour;
  v22 = byDay;
  v21 = byMonth;
  v20 = dwYear;
  v19 = byLastClassGrade;
  v18 = nClassInitCnt;
  szNextClass = szNextClassCode;
  if ( CRFWorldDatabase::InsertChangeClassLogAfterInitClass(
         v26->m_pWorldDB,
         dwCharacSerial,
         byType,
         szPrevClassCode,
         szNextClassCode,
         nClassInitCnt,
         byLastClassGrade,
         dwYear,
         byMonth,
         byDay,
         byHour,
         byMin,
         bySec) )
  {
    result = 0;
  }
  else
  {
    result = 24;
  }
  return result;
}
