/*
 * Function: j_??$_Uninit_move@PEAPEAVCMoveMapLimitRight@@PEAPEAV1@V?$allocator@PEAVCMoveMapLimitRight@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAVCMoveMapLimitRight@@PEAPEAV1@00AEAV?$allocator@PEAVCMoveMapLimitRight@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000F105
 */

CMoveMapLimitRight **__fastcall std::_Uninit_move<CMoveMapLimitRight * *,CMoveMapLimitRight * *,std::allocator<CMoveMapLimitRight *>,std::_Undefined_move_tag>(CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, CMoveMapLimitRight **_Dest, std::allocator<CMoveMapLimitRight *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<CMoveMapLimitRight * *,CMoveMapLimitRight * *,std::allocator<CMoveMapLimitRight *>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
