/*
 * Function: __tmainCRTStartup
 * Address: 0x1404DD340
 */

signed __int64 __cdecl _tmainCRTStartup()
{
  _STARTUPINFOA StartupInfo; // [sp+30h] [bp-B8h]@1
  char *lpCmdLine; // [sp+A0h] [bp-48h]@23
  int v6; // [sp+A8h] [bp-40h]@1
  int v7; // [sp+ACh] [bp-3Ch]@1
  __int64 v8; // [sp+B0h] [bp-38h]@1
  __int64 v9; // [sp+B8h] [bp-30h]@1
  __int64 v10; // [sp+D0h] [bp-18h]@2
  int v11; // [sp+D8h] [bp-10h]@28
  int nCmdShow; // [sp+DCh] [bp-Ch]@37

  v6 = 0;
  GetStartupInfoA(&StartupInfo);
  v9 = 0i64;
  v8 = *((_QWORD *)NtCurrentTeb() + 1);
  v7 = 0;
  while ( 1 )
  {
    v10 = v8;
    v9 = _InterlockedCompareExchange((volatile signed __int64 *)&_native_startup_lock, v8, 0i64);
    if ( !v9 )
      break;
    if ( v9 == v8 )
    {
      v7 = 1;
      break;
    }
    Sleep(0x3E8u);
  }
  if ( _native_startup_state == 1 )
  {
    amsg_exit_0(31i64);
  }
  else if ( _native_startup_state )
  {
    has_cctor = 1;
  }
  else
  {
    _native_startup_state = 1;
    if ( initterm_e_0(&_xi_a, &_xi_z) )
      return 255i64;
  }
  if ( _native_startup_state == 1 )
  {
    initterm_0(&_xc_a, &_xc_z);
    _native_startup_state = 2;
  }
  if ( _native_startup_state != 2
    && _CrtDbgReportW(2i64, L"f:\\dd\\vctools\\crt_bld\\self_64_amd64\\crt\\src\\crtexe.c", 515i64, 0i64) == 1 )
  {
    __debugbreak();
  }
  if ( !v7 )
  {
    _RAX = 0i64;
    _RCX = &_native_startup_lock;
    __asm { xchg    rax, [rcx] }
  }
  if ( *(_QWORD *)_dyn_tls_init_callback && IsNonwritableInCurrentImage(_dyn_tls_init_callback) )
    ((void (__fastcall *)(_QWORD, signed __int64, _QWORD))_dyn_tls_init_callback)(0i64, 2i64, 0i64);
  _CrtSetCheckCount(1i64);
  for ( lpCmdLine = (char *)_acmdln; (signed int)(unsigned __int8)*lpCmdLine > 32 || *lpCmdLine && v6; ++lpCmdLine )
  {
    if ( *lpCmdLine == 34 )
    {
      v11 = v6 == 0;
      v6 = v6 == 0;
    }
    if ( _ismbblead((unsigned __int8)*lpCmdLine) )
    {
      if ( lpCmdLine )
        ++lpCmdLine;
    }
  }
  while ( *lpCmdLine && (signed int)(unsigned __int8)*lpCmdLine <= 32 )
    ++lpCmdLine;
  if ( StartupInfo.dwFlags & 1 )
    nCmdShow = StartupInfo.wShowWindow;
  else
    nCmdShow = 10;
  mainret = WinMain(&_ImageBase, 0i64, lpCmdLine, nCmdShow);
  if ( !managedapp )
    exit(mainret);
  if ( !has_cctor )
    _cexit();
  return (unsigned int)mainret;
}
