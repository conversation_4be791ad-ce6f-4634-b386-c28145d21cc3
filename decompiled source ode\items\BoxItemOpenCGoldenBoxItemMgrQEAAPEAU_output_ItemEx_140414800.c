/*
 * Function: ?BoxItemOpen@CGoldenBoxItemMgr@@QEAAPEAU_output@_ItemExchange_fld@@E@Z
 * Address: 0x140414800
 */

_ItemExchange_fld::_output *__fastcall CGoldenBoxItemMgr::BoxItemOpen(CGoldenBoxItemMgr *this, char byIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-58h]@1
  int v6; // [sp+20h] [bp-38h]@4
  unsigned int v7; // [sp+24h] [bp-34h]@4
  int j; // [sp+28h] [bp-30h]@4
  char *Str; // [sp+30h] [bp-28h]@7
  CGoldenBoxItemMgr::_BoxItemOpen_output *v10; // [sp+38h] [bp-20h]@4
  unsigned __int64 v11; // [sp+40h] [bp-18h]@4
  CGoldenBoxItemMgr *v12; // [sp+60h] [bp+8h]@1
  char v13; // [sp+68h] [bp+10h]@1

  v13 = byIndex;
  v12 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = rand() % 10000;
  v7 = 0;
  CGoldenBoxItemMgr::RateCheck(v12, v13);
  v11 = v12->m_golden_box_item.m_bygolden_item_num[(unsigned __int8)v13];
  v10 = (CGoldenBoxItemMgr::_BoxItemOpen_output *)operator new[](saturated_mul(0x10ui64, v11));
  v12->m_pBoxItemOpen = v10;
  for ( j = 0; j < v12->m_golden_box_item.m_bygolden_item_num[(unsigned __int8)v13]; ++j )
  {
    v12->m_pBoxItemOpen[j].m_dwProb = v12->m_golden_box_item.m_golden_box_item_info[(unsigned __int8)v13][j].m_wRate;
    strcpy_0(
      v12->m_pBoxItemOpen[j].m_itmPdOutput,
      v12->m_golden_box_event.m_ini.m_golden_box_item_list[(unsigned __int8)v13][j].m_szLimcode);
    v12->m_pBoxItemOpen[j].m_nPdProCnt = 1;
    Str = v12->m_pBoxItemOpen[j].m_itmPdOutput;
    if ( !Str )
      return 0i64;
    if ( strlen_0(Str) < 2 )
      return 0i64;
    if ( !strncmp(Str, "-", 1ui64) || !strncmp(Str, "-1", 2ui64) )
      return 0i64;
    v7 += *((_DWORD *)Str + 3);
    if ( (signed int)CGoldenBoxItemMgr::Get_BoxItem_Count(
                       v12,
                       v13,
                       v12->m_golden_box_item.m_golden_box_item_info[(unsigned __int8)v13][j].m_dwIndex) > 0
      && v6 < v7 )
    {
      return (_ItemExchange_fld::_output *)Str;
    }
  }
  return 0i64;
}
