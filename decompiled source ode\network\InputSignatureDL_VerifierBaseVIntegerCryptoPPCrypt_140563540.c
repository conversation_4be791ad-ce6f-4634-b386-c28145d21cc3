/*
 * Function: ?InputSignature@?$DL_VerifierBase@VInteger@CryptoPP@@@CryptoPP@@UEBAXAEAVPK_MessageAccumulator@2@PEBE_K@Z
 * Address: 0x140563540
 */

int __fastcall CryptoPP::DL_VerifierBase<CryptoPP::Integer>::InputSignature(__int64 a1, __int64 a2, char *a3)
{
  __int64 v3; // ST20_8@1
  __int64 v4; // rax@1
  __int64 v5; // ST30_8@1
  __int64 v6; // rax@1
  __int64 v7; // ST28_8@1
  unsigned __int64 v8; // rax@1
  unsigned __int64 len; // ST38_8@1
  unsigned __int64 v10; // rax@1
  __int64 v11; // rax@1
  __int64 v12; // ST48_8@1
  unsigned __int64 v13; // ST50_8@1
  char *v14; // ST58_8@1
  __int64 v15; // rax@1
  __int64 v17; // [sp+80h] [bp+8h]@1
  char *t; // [sp+90h] [bp+18h]@1

  t = a3;
  v17 = a1;
  v3 = a2;
  LODWORD(v4) = (*(int (**)(void))(*(_QWORD *)a1 + 136i64))();
  v5 = v4;
  LODWORD(v6) = CryptoPP::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::Integer>>::GetAbstractGroupParameters(v17 + 16);
  v7 = v6;
  LODWORD(v8) = (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v5 + 24i64))(v5, v6);
  len = v8;
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::Assign(
    (CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v3 + 80),
    t,
    v8);
  LODWORD(v10) = (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v5 + 32i64))(v5, v7);
  CryptoPP::Integer::Decode((CryptoPP::Integer *)(v3 + 144), (const unsigned __int8 *)&t[len], v10, 0);
  LODWORD(v11) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v17 + 144i64))(v17);
  v12 = v11;
  v13 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v3 + 80));
  v14 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v3 + 80));
  LODWORD(v15) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v3 + 144i64))(v3);
  return (*(int (__fastcall **)(__int64, __int64, char *, unsigned __int64))(*(_QWORD *)v12 + 32i64))(
           v12,
           v15,
           v14,
           v13);
}
