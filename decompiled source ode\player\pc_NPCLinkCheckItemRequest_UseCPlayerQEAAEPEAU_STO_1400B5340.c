/*
 * Function: ?pc_NPCLinkCheckItemRequest_Use@CPlayer@@QEAAEPEAU_STORAGE_POS_INDIV@@@Z
 * Address: 0x1400B5340
 */

char __fastcall CPlayer::pc_NPCLinkCheckItemRequest_Use(CPlayer *this, _STORAGE_POS_INDIV *pStorage)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-68h]@1
  bool bUpdate; // [sp+20h] [bp-48h]@7
  bool bSend; // [sp+28h] [bp-40h]@7
  char v8; // [sp+30h] [bp-38h]@4
  unsigned int v9; // [sp+34h] [bp-34h]@5
  _STORAGE_LIST *v10; // [sp+38h] [bp-30h]@5
  _STORAGE_LIST::_db_con *pUseItem; // [sp+40h] [bp-28h]@5
  _base_fld *v12; // [sp+48h] [bp-20h]@5
  int v13; // [sp+50h] [bp-18h]@7
  CPlayer *v14; // [sp+70h] [bp+8h]@1
  _STORAGE_POS_INDIV *pStoragea; // [sp+78h] [bp+10h]@1

  pStoragea = pStorage;
  v14 = this;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = CPlayer::pc_NPCLinkCheckItemRequest_Check(v14, pStorage);
  if ( !v8 )
  {
    v9 = 0;
    v10 = 0i64;
    pUseItem = 0i64;
    v10 = v14->m_Param.m_pStoragePtr[pStoragea->byStorageCode];
    pUseItem = _STORAGE_LIST::GetPtrFromSerial(v10, pStoragea->wItemSerial);
    v12 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 35, pUseItem->m_wItemIndex);
    if ( v12 )
    {
      if ( !*(_DWORD *)&v12[5].m_strCode[56] )
      {
        v13 = pUseItem->m_dwDur;
        bSend = 0;
        bUpdate = 0;
        v9 = CPlayer::Emb_AlterDurPoint(v14, v10->m_nListCode, pUseItem->m_byStorageIndex, -1, 0, 0);
        pStoragea->byNum = v9;
        if ( v13 - 1 == v9 )
        {
          if ( *(_DWORD *)&v12[7].m_strCode[8] == 1 )
            CMgrAvatorItemHistory::cash_item_use(
              &CPlayer::s_MgrItemHistory,
              v14->m_ObjID.m_wIndex,
              pUseItem,
              v14->m_szItemHistoryFileName);
          v8 = 0;
          pStoragea->wItemSerial = pUseItem->m_wItemIndex;
        }
        else
        {
          v8 = 4;
        }
      }
    }
    else
    {
      v8 = 3;
    }
  }
  return v8;
}
