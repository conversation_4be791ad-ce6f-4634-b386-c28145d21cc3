/*
 * Function: __HrLoadAllImportsForDll
 * Address: 0x14067693C
 */

__int64 __fastcall _HrLoadAllImportsForDll(const char *szDll)
{
  const char *v1; // r10@1
  signed int v2; // er9@1
  ImgDelayDescr *i; // rdi@2
  char *v4; // r8@3
  char *v5; // rcx@3
  char v6; // al@4
  const char *v7; // rdx@5
  signed __int64 v8; // rcx@5
  char v9; // al@6
  const char *v10; // rdx@8
  signed __int64 j; // rcx@9
  unsigned int v12; // eax@14
  __int64 v13; // rcx@16
  __int64 (__cdecl **v14)(); // rbx@16
  __int64 (__cdecl **k)(); // rax@16
  unsigned __int64 v16; // rsi@18

  v1 = szDll;
  v2 = -2147024770;
  if ( *(_DWORD *)((char *)&_ImageBase + (signed int)off_14000003C + 244) )
  {
    for ( i = (ImgDelayDescr *)((char *)&_ImageBase + *(_DWORD *)((char *)&_ImageBase + (signed int)off_14000003C + 240));
          ;
          ++i )
    {
      v12 = i->rvaDLLName;
      if ( !v12 )
        break;
      v4 = (char *)&_ImageBase + v12;
      v5 = (char *)&_ImageBase + v12;
      do
        v6 = *v5++;
      while ( v6 );
      v7 = v1;
      v8 = v5 - v4 - 1;
      do
        v9 = *v7++;
      while ( v9 );
      if ( v8 == v7 - v1 - 1 )
      {
        v10 = v1;
        if ( !v8 )
          break;
        for ( j = v8 - 1; j; --j )
        {
          if ( *v10 != *v4 )
            break;
          ++v10;
          ++v4;
        }
        if ( *v10 == (unsigned __int8)*v4 )
          break;
      }
    }
    if ( i->rvaDLLName )
    {
      v13 = 0i64;
      v14 = (__int64 (__cdecl **)())((char *)&_ImageBase + i->rvaIAT);
      for ( k = v14; *k; v13 = (unsigned int)(v13 + 1) )
        ++k;
      v16 = (unsigned __int64)&v14[v13];
      while ( (unsigned __int64)v14 < v16 )
      {
        _delayLoadHelper2(i, v14);
        ++v14;
      }
      v2 = 0;
    }
  }
  return (unsigned int)v2;
}
