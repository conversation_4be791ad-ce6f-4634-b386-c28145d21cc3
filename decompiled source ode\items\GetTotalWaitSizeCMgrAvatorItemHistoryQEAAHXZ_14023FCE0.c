/*
 * Function: ?GetTotalWaitSize@CMgrAvatorItemHistory@@QEAAHXZ
 * Address: 0x14023FCE0
 */

__int64 __fastcall CMgrAvatorItemHistory::GetTotalWaitSize(CMgrAvatorItemHistory *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  int v4; // eax@4
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@4
  int v8; // [sp+24h] [bp-14h]@4
  CMgrAvatorItemHistory *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v1 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = CNetIndexList::size(&v9->m_listLogData_10K);
  v3 = CNetIndexList::size(&v9->m_listLogData_1K);
  v8 = v3 + v7;
  v4 = CNetIndexList::size(&v9->m_listLogData_200);
  return (unsigned int)(v4 + v8);
}
