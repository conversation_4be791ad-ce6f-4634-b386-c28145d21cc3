/*
 * Function: ??0?$CipherModeFinalTemplate_ExternalCipher@VCBC_CTS_Encryption@CryptoPP@@@CryptoPP@@QEAA@AEAV?$SimpleKeyedTransformation@VBlockTransformation@CryptoPP@@@1@PEBEH@Z
 * Address: 0x14055B810
 */

CryptoPP::CBC_CTS_Encryption *__fastcall CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Encryption>::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Encryption>(CryptoPP::CBC_CTS_Encryption *a1, __int64 a2, __int64 a3, unsigned int a4)
{
  CryptoPP::CBC_CTS_Encryption *v5; // [sp+40h] [bp+8h]@1
  __int64 v6; // [sp+48h] [bp+10h]@1
  __int64 v7; // [sp+50h] [bp+18h]@1
  unsigned int v8; // [sp+58h] [bp+20h]@1

  v8 = a4;
  v7 = a3;
  v6 = a2;
  v5 = a1;
  CryptoPP::CBC_CTS_Encryption::CBC_CTS_Encryption(a1);
  v5->vfptr = (CryptoPP::ClonableVtbl *)&CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Encryption>::`vftable'{for `CryptoPP::StreamTransformation'};
  v5->vfptr = (CryptoPP::SimpleKeyingInterfaceVtbl *)&CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Encryption>::`vftable'{for `CryptoPP::SimpleKeyingInterface'};
  CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Encryption>::SetCipherWithIV(v5, v6, v7, v8);
  return v5;
}
