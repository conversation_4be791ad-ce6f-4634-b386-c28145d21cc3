/*
 * Function: ?IsUseReleaseRaceBuffPotion@CPlayer@@QEAA_NXZ
 * Address: 0x1400A3A50
 */

bool __fastcall CPlayer::IsUseReleaseRaceBuffPotion(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@4
  __int64 v5; // [sp+0h] [bp-58h]@1
  MiningTicket::_AuthKeyTicket Src; // [sp+34h] [bp-24h]@4
  char v7; // [sp+44h] [bp-14h]@4
  char v8; // [sp+45h] [bp-13h]@4
  char v9; // [sp+46h] [bp-12h]@4
  char v10; // [sp+47h] [bp-11h]@4
  CPlayer *v11; // [sp+60h] [bp+8h]@1

  v11 = this;
  v1 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = CHolyStoneSystem::GetNumOfTime(&g_HolySys);
  v8 = CHolyStoneSystem::GetStartHour(&g_HolySys);
  v9 = CHolyStoneSystem::GetStartDay(&g_HolySys);
  v10 = CHolyStoneSystem::GetStartMonth(&g_HolySys);
  v3 = CHolyStoneSystem::GetStartYear(&g_HolySys);
  MiningTicket::_AuthKeyTicket::Set(&Src, v3, v10, v9, v8, v7);
  return MiningTicket::_AuthKeyTicket::operator==(&v11->m_dwRaceBuffClearKey, &Src) != 0;
}
