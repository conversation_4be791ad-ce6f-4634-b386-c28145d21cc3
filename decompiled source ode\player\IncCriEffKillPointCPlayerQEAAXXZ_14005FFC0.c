/*
 * Function: ?IncCriEffKillPoint@CPlayer@@QEAAXXZ
 * Address: 0x14005FFC0
 */

void __fastcall CPlayer::IncCriEffKillPoint(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  struct CHolyStone *v4; // [sp+20h] [bp-18h]@5
  CPlayer *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5->m_byHSKQuestCode != 100 )
  {
    v4 = &g_Stone[CPlayerDB::GetRaceCode(&v5->m_Param)];
    if ( v4->m_bOper )
    {
      if ( v4->m_pCurMap == v5->m_pCurMap )
      {
        ++v5->m_wKillPoint;
        CPlayer::SendMsg_HSKQuestActCum(v5);
        v5->m_pUserDB->m_AvatorData.m_wKillPoint = v5->m_wKillPoint;
      }
    }
  }
}
