/*
 * Function: ??$max_element@PEAVCNormalGuildBattleGuildMember@GUILD_BATTLE@@VCTopKillPrediCate@CNormalGuildBattleGuild@2@@std@@YAPEAVCNormalGuildBattleGuildMember@GUILD_BATTLE@@PEAV12@0VCTopKillPrediCate@CNormalGuildBattleGuild@2@@Z
 * Address: 0x1403EB430
 */

GUILD_BATTLE::CNormalGuildBattleGuildMember *__fastcall std::max_element<GUILD_BATTLE::CNormalGuildBattleGuildMember *,GUILD_BATTLE::CNormalGuildBattleGuild::CTopKillPrediCate>(GUILD_BATTLE::CNormalGuildBattleGuildMember *_First, GUILD_BATTLE::CNormalGuildBattleGuildMember *_Last, GUILD_BATTLE::CNormalGuildBattleGuild::CTopKillPrediCate _Pred)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleGuildMember *_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return std::_Max_element<GUILD_BATTLE::CNormalGuildBattleGuildMember *,GUILD_BATTLE::CNormalGuildBattleGuild::CTopKillPrediCate>(
           _Firsta,
           _Last,
           _Pred);
}
