/*
 * Function: ?GuildRoomRestTimeRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C8C20
 */

char __fastcall CNetworkEX::GuildRoomRestTimeRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-18h]@1

  v3 = &v6;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return 1;
}
