/*
 * Function: ?<PERSON><PERSON><PERSON><PERSON>@CAttack@@QEAAXPEAU_attack_param@@_N@Z
 * Address: 0x14016A210
 */

void __usercall CAttack::AttackForce(CAttack *this@<rcx>, _attack_param *pParam@<rdx>, bool bUseEffBullet@<r8b>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed int v6; // eax@7
  float v7; // xmm1_4@7
  float v8; // xmm0_4@21
  float v9; // xmm0_4@21
  CPvpUserAndGuildRankingSystem *v10; // rax@27
  __int64 v11; // rdx@39
  float *v12; // rcx@40
  _attack_param *v13; // rax@43
  CCharacter **v14; // rcx@43
  _attack_param *v15; // rdx@43
  _attack_param *v16; // r8@43
  _attack_param *v17; // rax@44
  CCharacter **v18; // rcx@44
  _attack_param *v19; // rdx@44
  _attack_param *v20; // r8@44
  __int64 v21; // [sp+0h] [bp-98h]@1
  int nEffAttPower[2]; // [sp+20h] [bp-78h]@39
  bool bUseEffBulleta; // [sp+28h] [bp-70h]@39
  _base_fld *v24; // [sp+30h] [bp-68h]@4
  char v25; // [sp+38h] [bp-60h]@4
  int v26; // [sp+3Ch] [bp-5Ch]@7
  float v27; // [sp+40h] [bp-58h]@21
  float v28; // [sp+44h] [bp-54h]@21
  CCharacter *v29; // [sp+48h] [bp-50h]@26
  char v30; // [sp+50h] [bp-48h]@27
  float v31; // [sp+54h] [bp-44h]@7
  int v32; // [sp+58h] [bp-40h]@10
  int v33; // [sp+5Ch] [bp-3Ch]@13
  unsigned int dwSerial; // [sp+60h] [bp-38h]@27
  int v35; // [sp+64h] [bp-34h]@27
  char v36; // [sp+68h] [bp-30h]@27
  int v37; // [sp+6Ch] [bp-2Ch]@33
  __int64 v38; // [sp+70h] [bp-28h]@39
  __int64 v39; // [sp+78h] [bp-20h]@40
  int *v40; // [sp+80h] [bp-18h]@40
  int nAttPnt; // [sp+88h] [bp-10h]@43
  int v42; // [sp+8Ch] [bp-Ch]@44
  CAttack *v43; // [sp+A0h] [bp+8h]@1
  bool v44; // [sp+B0h] [bp+18h]@1

  v44 = bUseEffBullet;
  v43 = this;
  v4 = &v21;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v43->m_nDamagedObjNum = 0;
  v43->m_bIsCrtAtt = 0;
  v43->m_pp = pParam;
  v24 = v43->m_pp->pFld;
  v25 = 1;
  CCharacter::BreakStealth(v43->m_pAttChar);
  if ( v43->m_pp->pDst )
  {
    if ( _effect_parameter::GetEff_State(&v43->m_pp->pDst->m_EP, 8) )
    {
      v25 = 0;
    }
    else
    {
      _effect_parameter::GetEff_Plus(&v43->m_pAttChar->m_EP, 31);
      v31 = a4 + 100.0;
      v6 = ((int (__fastcall *)(CCharacter *))v43->m_pp->pDst->vfptr->GetAvoidRate)(v43->m_pp->pDst);
      v7 = v31 - (float)v6;
      v26 = (signed int)ffloor(v7);
      if ( !v43->m_pAttChar->m_ObjID.m_byID )
      {
        _effect_parameter::GetEff_Plus(&v43->m_pAttChar->m_EP, 40);
        v26 = (signed int)ffloor((float)v26 + v7);
      }
      if ( v26 <= 0 )
        v32 = 0;
      else
        v32 = v26;
      v26 = v32;
      if ( v32 >= 100 )
        v33 = 100;
      else
        v33 = v26;
      v26 = v33;
      if ( rand() % 100 >= v26 )
        v25 = 0;
    }
  }
  if ( !v25 )
  {
    if ( v43->m_pp->pDst )
    {
      v43->m_DamList[0].m_pChar = v43->m_pp->pDst;
      v43->m_DamList[0].m_nDamage = 0;
      v43->m_nDamagedObjNum = 1;
    }
    return;
  }
  v27 = (float)(v43->m_pp->nAddAttPnt + CAttack::_CalcForceAttPnt(v43, 0));
  v8 = v27;
  _effect_parameter::GetEff_Rate(&v43->m_pAttChar->m_EP, 4);
  v27 = v27 * v8;
  v28 = (float)(v43->m_pp->nAddAttPnt + CAttack::_CalcForceAttPnt(v43, v44));
  v9 = v28;
  _effect_parameter::GetEff_Rate(&v43->m_pAttChar->m_EP, 4);
  v28 = v28 * v9;
  if ( !v43->m_pAttChar->m_ObjID.m_byID
    && (CHolyStoneSystem::GetDestroyerSerial(&g_HolySys) == v43->m_pAttChar->m_dwObjSerial
     || CPlayer::IsLastAttBuff((CPlayer *)v43->m_pAttChar)) )
  {
    v27 = v27 * 1.3;
    v28 = v28 * 1.3;
  }
  if ( !v43->m_pAttChar->m_ObjID.m_byID )
  {
    v29 = v43->m_pAttChar;
    if ( !BYTE2(v29[1].m_fCurPos[2]) )
    {
      dwSerial = CPlayerDB::GetCharSerial((CPlayerDB *)&v29[1].m_fOldPos[2]);
      v35 = CPlayerDB::GetRaceCode((CPlayerDB *)&v29[1].m_fOldPos[2]);
      v10 = CPvpUserAndGuildRankingSystem::Instance();
      v30 = CPvpUserAndGuildRankingSystem::GetBossType(v10, v35, dwSerial);
      v36 = v30;
      if ( v30 )
      {
        if ( v36 == 2 || v36 == 6 )
        {
          v27 = v27 * 1.2;
          v28 = v28 * 1.2;
        }
      }
      else
      {
        v27 = v27 * 1.3;
        v28 = v28 * 1.3;
      }
    }
  }
  v37 = *(_DWORD *)&v24[11].m_strCode[4];
  if ( v37 >= 0 )
  {
    if ( v37 <= 2 )
    {
      if ( v43->m_pp->pDst )
      {
        v43->m_DamList[0].m_pChar = v43->m_pp->pDst;
        if ( v44 )
        {
          v13 = v43->m_pp;
          v14 = &v43->m_pp->pDst;
          v15 = v43->m_pp;
          v16 = v43->m_pp;
          nAttPnt = (signed int)ffloor(v28);
          bUseEffBulleta = v13->bBackAttack;
          *(_QWORD *)nEffAttPower = *v14;
          v43->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                          v43->m_pAttChar,
                                          nAttPnt,
                                          v16->nPart,
                                          v15->nTol,
                                          *(CCharacter **)nEffAttPower,
                                          bUseEffBulleta);
        }
        else
        {
          v17 = v43->m_pp;
          v18 = &v43->m_pp->pDst;
          v19 = v43->m_pp;
          v20 = v43->m_pp;
          v42 = (signed int)ffloor(v27);
          bUseEffBulleta = v17->bBackAttack;
          *(_QWORD *)nEffAttPower = *v18;
          v43->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                          v43->m_pAttChar,
                                          v42,
                                          v20->nPart,
                                          v19->nTol,
                                          *(CCharacter **)nEffAttPower,
                                          bUseEffBulleta);
        }
        v43->m_nDamagedObjNum = 1;
      }
      goto LABEL_48;
    }
    if ( v37 != 4 )
    {
      if ( v37 == 5 )
      {
        v11 = *(_DWORD *)&v24[4].m_strCode[60];
        v38 = *(_DWORD *)&v24[4].m_strCode[60];
        bUseEffBulleta = v44;
        nEffAttPower[0] = (signed int)ffloor(v28);
        CAttack::FlashDamageProc(
          v43,
          s_nLimitDist[v38],
          (signed int)ffloor(v27),
          s_nLimitAngle[1][v11],
          nEffAttPower[0],
          v44);
LABEL_48:
        CAttack::CalcAvgDamage(v43);
        return;
      }
      if ( v37 != 6 )
        return;
    }
    v12 = v43->m_pp->fArea;
    v39 = *(_DWORD *)&v24[4].m_strCode[60];
    v40 = s_nLimitRadius;
    bUseEffBulleta = v44;
    nEffAttPower[0] = (signed int)ffloor(v28);
    CAttack::AreaDamageProc(v43, s_nLimitRadius[v39], (signed int)ffloor(v27), v12, nEffAttPower[0], v44);
    goto LABEL_48;
  }
}
