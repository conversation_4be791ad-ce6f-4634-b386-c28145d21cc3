/*
 * Function: ?AIInit@CAnimus@@QEAAXXZ
 * Address: 0x140125FC0
 */

void __fastcall CAnimus::AIInit(CAnimus *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  _animus_fld *v3; // rax@4
  _animus_fld *v4; // rcx@4
  _animus_fld *v5; // rdx@4
  _animus_fld *v6; // r8@4
  _animus_fld *v7; // r9@4
  float v8; // xmm0_4@4
  __int64 v9; // [sp+0h] [bp-58h]@1
  int maxprob; // [sp+20h] [bp-38h]@4
  int len; // [sp+28h] [bp-30h]@4
  int castdelay; // [sp+30h] [bp-28h]@4
  int delay; // [sp+38h] [bp-20h]@4
  int el; // [sp+40h] [bp-18h]@4
  CAnimus *v15; // [sp+60h] [bp+8h]@1

  v15 = this;
  v1 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v15->m_pRecord = GetAnimusFldFromExp(v15->m_byClassCode, v15->m_dwExp);
  v15->m_nMaxHP = v15->m_pRecord->m_nMaxHP;
  v15->m_nMaxFP = v15->m_pRecord->m_nMaxFP;
  v15->m_dwAIMode = 1;
  v15->m_pTarget = 0i64;
  v15->m_Mightiness = FLOAT_1_0;
  CAITimer::Init(&v15->m_AITimer[1], 0x64u);
  CAITimer::Init(v15->m_AITimer, 0x64u);
  CAITimer::Init(&v15->m_AITimer[2], 0xEA60u);
  CAITimer::Set(&v15->m_AITimer[2], 0);
  v3 = v15->m_pRecord;
  v4 = v15->m_pRecord;
  v5 = v15->m_pRecord;
  v6 = v15->m_pRecord;
  v7 = v15->m_pRecord;
  v8 = (float)v15->m_pRecord->m_nAttFcStd * v15->m_Mightiness;
  el = -1;
  delay = v3->m_nAttSpd;
  castdelay = v4->m_nAttMoTime1;
  len = v5->m_nAttExt;
  maxprob = v6->m_nMaxAFSelProb;
  SKILL::Init(v15->m_Skill, 0, (signed int)ffloor(v8), v7->m_nMinAFSelProb, maxprob, len, castdelay, delay, -1);
}
