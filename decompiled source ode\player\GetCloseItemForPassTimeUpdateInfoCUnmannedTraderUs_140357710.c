/*
 * Function: ?GetCloseItemForPassTimeUpdateInfo@CUnmannedTraderUserInfo@@QEAA?AW4STATE@CUnmannedTraderItemState@@KAEAPEAVCPlayer@@@Z
 * Address: 0x140357710
 */

signed __int64 __fastcall CUnmannedTraderUserInfo::GetCloseItemForPassTimeUpdateInfo(CUnmannedTraderUserInfo *this, unsigned int dwRegistSerial, CPlayer **pkOwner)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 v5; // rax@5
  CUnmannedTraderRegistItemInfo *v6; // rax@10
  CUnmannedTraderRegistItemInfo *v7; // rax@12
  __int64 v8; // [sp+0h] [bp-98h]@1
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > result; // [sp+28h] [bp-70h]@6
  bool v10; // [sp+44h] [bp-54h]@6
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v11; // [sp+48h] [bp-50h]@6
  unsigned int v12; // [sp+60h] [bp-38h]@7
  unsigned int v13; // [sp+64h] [bp-34h]@9
  unsigned int v14; // [sp+68h] [bp-30h]@11
  unsigned int v15; // [sp+6Ch] [bp-2Ch]@13
  unsigned int v16; // [sp+70h] [bp-28h]@14
  __int64 v17; // [sp+78h] [bp-20h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v18; // [sp+80h] [bp-18h]@6
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *_Right; // [sp+88h] [bp-10h]@6
  CUnmannedTraderUserInfo *v20; // [sp+A0h] [bp+8h]@1
  unsigned int dwRegistSeriala; // [sp+A8h] [bp+10h]@1
  CPlayer **v22; // [sp+B0h] [bp+18h]@1

  v22 = pkOwner;
  dwRegistSeriala = dwRegistSerial;
  v20 = this;
  v3 = &v8;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v17 = -2i64;
  if ( CUnmannedTraderUserInfo::IsNull(v20) )
  {
    v5 = 6i64;
  }
  else
  {
    CUnmannedTraderUserInfo::Find(v20, &result, dwRegistSeriala);
    v18 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
            &v20->m_vecRegistItemInfo,
            &v11);
    _Right = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)v18;
    v10 = std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator==(
            (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&result._Mycont,
            (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v18->_Mycont);
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v11);
    if ( v10 )
    {
      v12 = -1;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
      v5 = v12;
    }
    else
    {
      *v22 = CUnmannedTraderUserInfo::FindOwner(v20);
      if ( *v22 )
      {
        v6 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(&result);
        if ( CUnmannedTraderRegistItemInfo::IsRegist(v6) )
        {
          v7 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(&result);
          if ( CUnmannedTraderRegistItemInfo::IsOverRegistTime(v7) )
          {
            v16 = 13;
            std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
            v5 = v16;
          }
          else
          {
            v15 = -1;
            std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
            v5 = v15;
          }
        }
        else
        {
          v14 = -1;
          std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
          v5 = v14;
        }
      }
      else
      {
        v13 = 6;
        std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
        v5 = v13;
      }
    }
  }
  return v5;
}
