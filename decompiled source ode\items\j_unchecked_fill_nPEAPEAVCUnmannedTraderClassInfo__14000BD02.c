/*
 * Function: j_??$unchecked_fill_n@PEAPEAVCUnmannedTraderClassInfo@@_KPEAV1@@stdext@@YAXPEAPEAVCUnmannedTraderClassInfo@@_KAEBQEAV1@@Z
 * Address: 0x14000BD02
 */

void __fastcall stdext::unchecked_fill_n<CUnmannedTraderClassInfo * *,unsigned __int64,CUnmannedTraderClassInfo *>(CUnmannedTraderClassInfo **_First, unsigned __int64 _Count, CUnmannedTraderClassInfo *const *_Val)
{
  stdext::unchecked_fill_n<CUnmannedTraderClassInfo * *,unsigned __int64,CUnmannedTraderClassInfo *>(
    _First,
    _Count,
    _Val);
}
