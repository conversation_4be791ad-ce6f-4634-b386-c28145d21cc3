/*
 * Function: ?end@?$vector@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEAA?AV?$_Vector_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@2@XZ
 * Address: 0x14058DFE0
 */

__int64 __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::end(__int64 a1, __int64 a2)
{
  __int64 v3; // [sp+48h] [bp+10h]@1

  v3 = a2;
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>(
    a2,
    *(_QWORD *)(a1 + 24));
  return v3;
}
