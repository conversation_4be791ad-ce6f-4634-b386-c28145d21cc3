/*
 * Function: ?Get@CNormalGuildBattleStateListPool@GUILD_BATTLE@@QEAAPEAVCNormalGuildBattleStateList@2@K@Z
 * Address: 0x1403F26D0
 */

GUILD_BATTLE::CNormalGuildBattleStateList *__fastcall GUILD_BATTLE::CNormalGuildBattleStateListPool::Get(GUILD_BATTLE::CNormalGuildBattleStateListPool *this, unsigned int dwID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v4; // rax@6
  GUILD_BATTLE::CNormalGuildBattleStateList *result; // rax@6
  GUILD_BATTLE::CGuildBattleLogger *v6; // rax@8
  __int64 v7; // [sp+0h] [bp-38h]@1
  unsigned int v8; // [sp+20h] [bp-18h]@6
  GUILD_BATTLE::CNormalGuildBattleStateListPool *v9; // [sp+40h] [bp+8h]@1
  unsigned int v10; // [sp+48h] [bp+10h]@1

  v10 = dwID;
  v9 = this;
  v2 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v9->m_pkStateList && v9->m_dwMaxCount > dwID )
  {
    if ( &v9->m_pkStateList[dwID] )
    {
      GUILD_BATTLE::CGuildBattleStateList::Clear((GUILD_BATTLE::CGuildBattleStateList *)&v9->m_pkStateList[dwID].vfptr);
      result = &v9->m_pkStateList[v10];
    }
    else
    {
      v6 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v6,
        "CNormalGuildBattleStateListPool::Get( %u ) : 0 == &m_pkStateList[%u] Invalid!",
        v10,
        v10);
      result = 0i64;
    }
  }
  else
  {
    v4 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    v8 = v10;
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v4,
      "CNormalGuildBattleStateListPool::Get( %u ) : 0 == m_pkStateList || %u <= %u Invalid!",
      v10,
      v9->m_dwMaxCount);
    result = 0i64;
  }
  return result;
}
