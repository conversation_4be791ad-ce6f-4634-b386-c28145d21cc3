/*
 * Function: ?ct_ReqChangeHonorGuild@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140295AC0
 */

char __fastcall ct_ReqChangeHonorGuild(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CHonorGuild *v3; // rax@6
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4

  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 3; ++j )
  {
    v3 = CHonorGuild::Instance();
    CHonorGuild::ChangeHonorGuild(v3, j);
  }
  return 1;
}
