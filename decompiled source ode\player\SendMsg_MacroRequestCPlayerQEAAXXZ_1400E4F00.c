/*
 * Function: ?SendMsg_MacroRequest@CPlayer@@QEAAXXZ
 * Address: 0x1400E4F00
 */

void __fastcall CPlayer::SendMsg_MacroRequest(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-448h]@1
  int Dst[3]; // [sp+40h] [bp-408h]@4
  int v5[3]; // [sp+4Ch] [bp-3FCh]@8
  int v6[30]; // [sp+58h] [bp-3F0h]@14
  char Dest[824]; // [sp+D0h] [bp-378h]@20
  _AIOC_A_MACRODATA *v8; // [sp+408h] [bp-40h]@4
  int k; // [sp+410h] [bp-38h]@6
  int j; // [sp+414h] [bp-34h]@4
  char pbyType; // [sp+424h] [bp-24h]@22
  char v12; // [sp+425h] [bp-23h]@22
  CPlayer *v13; // [sp+450h] [bp+8h]@1

  v13 = this;
  v1 = &v3;
  for ( i = 272i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = &v13->m_pUserDB->m_AvatorData.dbMacro;
  memset_0(Dst, 0, 0x3BAui64);
  for ( j = 0; j < 1; ++j )
  {
    for ( k = 0; k < 3; ++k )
    {
      Dst[k] = v8->mcr_Potion[j].Potion[k];
      v5[k] = v8->mcr_Potion[j].PotionValue[k];
    }
  }
  for ( j = 0; j < 3; ++j )
  {
    for ( k = 0; k < 10; ++k )
      v6[k + 10 * j] = v8->mcr_Action[j].Action[k];
  }
  for ( j = 0; j < 2; ++j )
  {
    for ( k = 0; k < 5; ++k )
      strncpy(&Dest[81 * (k + 5 * j)], (const char *)&v8->mcr_Chat[j] + 256 * (signed __int64)k, 0x51ui64);
  }
  pbyType = 3;
  v12 = 50;
  CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, (char *)Dst, 0x3BAu);
}
