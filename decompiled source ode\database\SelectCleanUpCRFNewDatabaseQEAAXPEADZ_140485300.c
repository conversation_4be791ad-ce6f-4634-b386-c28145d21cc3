/*
 * Function: ?SelectCleanUp@CRFNewDatabase@@QEAAXPEAD@Z
 * Address: 0x140485300
 */

void __fastcall CRFNewDatabase::SelectCleanUp(CRFNewDatabase *this, char *strQuery)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CRFNewDatabase *v5; // [sp+30h] [bp+8h]@1
  char *v6; // [sp+38h] [bp+10h]@1

  v6 = strQuery;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v5->m_hStmtSelect )
    SQLCloseCursor_0(v5->m_hStmtSelect);
  if ( v5->m_bSaveDBLog )
    CRFNewDatabase::FmtLog(v5, "%s Success", v6);
}
