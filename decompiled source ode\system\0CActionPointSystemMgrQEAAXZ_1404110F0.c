/*
 * Function: ??0CActionPointSystemMgr@@QEAA@XZ
 * Address: 0x1404110F0
 */

void __fastcall CActionPointSystemMgr::CActionPointSystemMgr(CActionPointSystemMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CActionPointSystemMgr *__t; // [sp+30h] [bp+8h]@1

  __t = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  `vector constructor iterator'(
    __t,
    0x38ui64,
    3,
    (void *(__cdecl *)(void *))_action_point_system_ini::_action_point_system_ini);
}
