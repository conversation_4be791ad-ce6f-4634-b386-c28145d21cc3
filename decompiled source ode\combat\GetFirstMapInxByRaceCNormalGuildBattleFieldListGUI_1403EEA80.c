/*
 * Function: ?GetFirstMapInxByRace@CNormalGuildBattleFieldList@GUILD_BATTLE@@QEAA_NEAEAE@Z
 * Address: 0x1403EEA80
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleFieldList::GetFirstMapInxByRace(GUILD_BATTLE::CNormalGuildBattleFieldList *this, char byRace, char *byInx)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleFieldList *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( (signed int)(unsigned __int8)byRace < 3 )
  {
    if ( v7->m_byUseFieldCnt[(unsigned __int8)byRace] && v7->m_ppkUseFieldByRace[(unsigned __int8)byRace] )
    {
      *byInx = GUILD_BATTLE::CNormalGuildBattleField::GetMapID(*v7->m_ppkUseFieldByRace[(unsigned __int8)byRace]);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
