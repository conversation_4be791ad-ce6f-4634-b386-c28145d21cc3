/*
 * Function: ?ReadRecord_Ex@CRecordData@@QEAA_NPEAD0K0@Z
 * Address: 0x1402AF040
 */

char __fastcall CRecordData::ReadRecord_Ex(CRecordData *this, char *szFile1, char *szFile2, unsigned int dwStructSize, char *pszErrMsg)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v8; // eax@8
  size_t v9; // rax@13
  size_t v10; // rax@16
  __int64 v11; // [sp+0h] [bp-238h]@1
  CRecordData Src; // [sp+30h] [bp-208h]@4
  CRecordData v13; // [sp+100h] [bp-138h]@4
  int j; // [sp+1B4h] [bp-84h]@8
  int v15; // [sp+1B8h] [bp-80h]@8
  char *v16; // [sp+1C0h] [bp-78h]@16
  char v17; // [sp+1D0h] [bp-68h]@5
  char v18; // [sp+1D1h] [bp-67h]@7
  char **v19; // [sp+1D8h] [bp-60h]@8
  char *v20; // [sp+1E0h] [bp-58h]@10
  char v21; // [sp+1E8h] [bp-50h]@17
  __int64 v22; // [sp+1F0h] [bp-48h]@4
  int v23; // [sp+1F8h] [bp-40h]@8
  unsigned __int64 v24; // [sp+200h] [bp-38h]@8
  __int64 v25; // [sp+208h] [bp-30h]@13
  char **v26; // [sp+210h] [bp-28h]@13
  __int64 v27; // [sp+218h] [bp-20h]@16
  char **v28; // [sp+220h] [bp-18h]@16
  unsigned __int64 v29; // [sp+228h] [bp-10h]@4
  CRecordData *v30; // [sp+240h] [bp+8h]@1
  char *szFile; // [sp+248h] [bp+10h]@1
  char *v32; // [sp+250h] [bp+18h]@1
  unsigned int dwStructSizea; // [sp+258h] [bp+20h]@1

  dwStructSizea = dwStructSize;
  v32 = szFile2;
  szFile = szFile1;
  v30 = this;
  v5 = &v11;
  for ( i = 140i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v22 = -2i64;
  v29 = (unsigned __int64)&v11 ^ _security_cookie;
  CRecordData::CRecordData(&Src);
  CRecordData::CRecordData(&v13);
  if ( CRecordData::ReadRecord(&Src, szFile, dwStructSizea, pszErrMsg) )
  {
    if ( CRecordData::ReadRecord(&v13, v32, dwStructSizea, pszErrMsg) )
    {
      memcpy_0(&v30->m_Header, &Src.m_Header, 0xCui64);
      v23 = CRecordData::GetRecordNum(&Src);
      v8 = CRecordData::GetRecordNum(&v13);
      v30->m_Header.m_nRecordNum = v8 + v23;
      j = 0;
      v15 = 0;
      v24 = v30->m_Header.m_nRecordNum;
      v19 = (char **)operator new[](saturated_mul(8ui64, v24));
      v30->m_ppsRecord = v19;
      for ( j = 0; j < v30->m_Header.m_nRecordNum; ++j )
      {
        v20 = (char *)operator new[](v30->m_Header.m_nRecordSize);
        v30->m_ppsRecord[j] = v20;
      }
      for ( j = 0; j < Src.m_Header.m_nRecordNum; memcpy_0(v26[v15++], Src.m_ppsRecord[j++], v9) )
      {
        v9 = v30->m_Header.m_nRecordSize;
        v25 = v15;
        v26 = v30->m_ppsRecord;
      }
      j = 0;
      while ( j < v13.m_Header.m_nRecordNum )
      {
        v10 = v30->m_Header.m_nRecordSize;
        v27 = v15;
        v28 = v30->m_ppsRecord;
        memcpy_0(v28[v15], v13.m_ppsRecord[j], v10);
        v16 = v30->m_ppsRecord[v15];
        *(_DWORD *)v16 = v15;
        ++j;
        ++v15;
      }
      v30->m_bLoad = 1;
      v21 = 1;
      CRecordData::~CRecordData(&v13);
      CRecordData::~CRecordData(&Src);
      result = v21;
    }
    else
    {
      v18 = 0;
      CRecordData::~CRecordData(&v13);
      CRecordData::~CRecordData(&Src);
      result = v18;
    }
  }
  else
  {
    v17 = 0;
    CRecordData::~CRecordData(&v13);
    CRecordData::~CRecordData(&Src);
    result = v17;
  }
  return result;
}
