/*
 * Function: ??0_monster_set@_event_set@@QEAA@XZ
 * Address: 0x1402A9E80
 */

void __fastcall _event_set::_monster_set::_monster_set(_event_set::_monster_set *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _event_set::_monster_set *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _event_set::_monster_set::_state::_state(&v4->m_State);
}
