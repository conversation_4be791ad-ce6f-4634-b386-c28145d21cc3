/*
 * Function: ?GetChild@CMonsterHierarchy@@QEAAPEAVCMonster@@HH@Z
 * Address: 0x140157DA0
 */

CMonster *__fastcall CMonsterHierarchy::GetChild(CMonsterHierarchy *this, int nKind, int nIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CMonster *result; // rax@14
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@6
  unsigned int v8; // [sp+24h] [bp-14h]@11
  CMonsterHierarchy *v9; // [sp+40h] [bp+8h]@1
  int nKindIndex; // [sp+48h] [bp+10h]@1
  int v11; // [sp+50h] [bp+18h]@1

  v11 = nIndex;
  nKindIndex = nKind;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( nKind < 0
    || ((signed int)(unsigned __int8)CMonsterHierarchy::ChildKindCount(v9) <= 3 ? (v7 = (unsigned __int8)CMonsterHierarchy::ChildKindCount(v9)) : (v7 = 3),
        nKindIndex >= v7
     || v11 < 0
     || (CMonsterHierarchy::GetChildCount(v9, nKindIndex) <= 0xA ? (v8 = CMonsterHierarchy::GetChildCount(
                                                                           v9,
                                                                           nKindIndex)) : (v8 = 10),
         v11 >= v8)) )
  {
    result = 0i64;
  }
  else
  {
    result = v9->m_pChildMon[nKindIndex][v11];
  }
  return result;
}
