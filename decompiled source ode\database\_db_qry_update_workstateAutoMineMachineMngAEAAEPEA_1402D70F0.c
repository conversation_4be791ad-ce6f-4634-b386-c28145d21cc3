/*
 * Function: ?_db_qry_update_workstate@AutoMineMachineMng@@AEAAEPEAD@Z
 * Address: 0x1402D70F0
 */

char __fastcall AutoMineMachineMng::_db_qry_update_workstate(AutoMineMachineMng *this, char *pdata)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-48h]@1
  bool bWorking; // [sp+20h] [bp-28h]@4
  char *v7; // [sp+30h] [bp-18h]@4

  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = pdata;
  bWorking = pdata[7];
  if ( CRFWorldDatabase::update_amine_workstate(pkDB, pdata[1], pdata[2], *(_DWORD *)(pdata + 3), bWorking) )
    result = 0;
  else
    result = 24;
  return result;
}
