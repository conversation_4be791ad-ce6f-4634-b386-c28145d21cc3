/*
 * Function: ?SendMsg_Assist_Force@CMonster@@QEAAXEPEAVCCharacter@@PEAU_force_fld@@H@Z
 * Address: 0x14014D690
 */

void __fastcall CMonster::SendMsg_Assist_Force(CMonster *this, char byErrCode, CCharacter *pDst, _force_fld *pForc_fld, int nSFLv)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@11
  char v9; // [sp+39h] [bp-4Fh]@11
  char v10; // [sp+3Ah] [bp-4Eh]@11
  char v11; // [sp+3Bh] [bp-4Dh]@11
  unsigned __int16 v12; // [sp+3Ch] [bp-4Ch]@11
  unsigned int v13; // [sp+3Eh] [bp-4Ah]@11
  char v14; // [sp+42h] [bp-46h]@11
  unsigned __int16 v15; // [sp+43h] [bp-45h]@11
  unsigned int v16; // [sp+45h] [bp-43h]@11
  char pbyType; // [sp+64h] [bp-24h]@11
  char v18; // [sp+65h] [bp-23h]@11
  CMonster *v19; // [sp+90h] [bp+8h]@1

  v19 = this;
  v5 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( pDst
    && pForc_fld
    && (v19->m_nCirclePlayerNum <= 500 || (CMonster *)pDst != v19)
    && (!byErrCode || byErrCode == 100) )
  {
    szMsg = byErrCode;
    v14 = pDst->m_ObjID.m_byID;
    v15 = pDst->m_ObjID.m_wIndex;
    v16 = pDst->m_dwObjSerial;
    v11 = v19->m_ObjID.m_byID;
    v12 = v19->m_ObjID.m_wIndex;
    v13 = v19->m_dwObjSerial;
    v9 = pForc_fld->m_dwIndex;
    v10 = nSFLv;
    pbyType = 17;
    v18 = 3;
    CGameObject::CircleReport((CGameObject *)&v19->vfptr, &pbyType, &szMsg, 18, 0);
  }
}
