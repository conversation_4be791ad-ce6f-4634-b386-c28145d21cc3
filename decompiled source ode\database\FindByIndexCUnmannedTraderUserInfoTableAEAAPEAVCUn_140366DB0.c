/*
 * Function: ?FindByIndex@CUnmannedTraderUserInfoTable@@AEAAPEAVCUnmannedTraderUserInfo@@G@Z
 * Address: 0x140366DB0
 */

CUnmannedTraderUserInfo *__fastcall CUnmannedTraderUserInfoTable::FindByIndex(CUnmannedTraderUserInfoTable *this, unsigned __int16 wInx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfo *result; // rax@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderUserInfoTable *v6; // [sp+30h] [bp+8h]@1
  unsigned __int16 v7; // [sp+38h] [bp+10h]@1

  v7 = wInx;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::size(&v6->m_veckInfo) > wInx )
    result = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator[](
               &v6->m_veckInfo,
               v7);
  else
    result = 0i64;
  return result;
}
