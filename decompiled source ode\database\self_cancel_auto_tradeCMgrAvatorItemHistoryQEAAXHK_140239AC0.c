/*
 * Function: ?self_cancel_auto_trade@CMgrAvatorItemHistory@@QEAAXHKPEAU_db_con@_STORAGE_LIST@@PEAD@Z
 * Address: 0x140239AC0
 */

void __fastcall CMgrAvatorItemHistory::self_cancel_auto_trade(CMgrAvatorItemHistory *this, int n, unsigned int dwRegistSerial, _STORAGE_LIST::_db_con *pRegItem, char *pszFileName)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char *v7; // rax@4
  __int64 v8; // [sp+0h] [bp-78h]@1
  unsigned __int64 v9; // [sp+20h] [bp-58h]@4
  char *v10; // [sp+28h] [bp-50h]@4
  unsigned __int64 v11; // [sp+30h] [bp-48h]@4
  char *v12; // [sp+38h] [bp-40h]@4
  char *v13; // [sp+40h] [bp-38h]@4
  _base_fld *v14; // [sp+50h] [bp-28h]@4
  char *v15; // [sp+58h] [bp-20h]@4
  char *v16; // [sp+60h] [bp-18h]@4
  int nTableCode; // [sp+68h] [bp-10h]@4
  CMgrAvatorItemHistory *v18; // [sp+80h] [bp+8h]@1
  unsigned int v19; // [sp+90h] [bp+18h]@1
  _STORAGE_LIST::_db_con *v20; // [sp+98h] [bp+20h]@1

  v20 = pRegItem;
  v19 = dwRegistSerial;
  v18 = this;
  v5 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v14 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pRegItem->m_byTableCode, pRegItem->m_wItemIndex);
  v15 = v18->m_szCurTime;
  v16 = v18->m_szCurDate;
  nTableCode = v20->m_byTableCode;
  v7 = DisplayItemUpgInfo(nTableCode, v20->m_dwLv);
  v13 = v15;
  v12 = v16;
  v11 = v20->m_lnUID;
  v10 = v7;
  v9 = v20->m_dwDur;
  sprintf(sData, "CANCEL_AUTO_TRADE: reg(%u) %s_%u_@%s[%I64u] [%s %s]\r\n", v19, v14->m_strCode);
  CMgrAvatorItemHistory::WriteFile(v18, pszFileName, sData);
}
