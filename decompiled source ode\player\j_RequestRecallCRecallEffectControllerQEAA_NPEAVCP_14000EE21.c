/*
 * Function: j_?RequestRecall@CRecallEffectController@@QEAA_NPEAVCPlayer@@PEAVCCharacter@@_N22@Z
 * Address: 0x14000EE21
 */

bool __fastcall CRecallEffectController::RequestRecall(CRecallEffectController *this, CPlayer *pkPerformer, CCharacter *pkDest, bool bRecallParty, bool bStone, bool bBattleModeUse)
{
  return CRecallEffectController::RequestRecall(this, pkPerformer, pkDest, bRecallParty, bStone, bBattleModeUse);
}
