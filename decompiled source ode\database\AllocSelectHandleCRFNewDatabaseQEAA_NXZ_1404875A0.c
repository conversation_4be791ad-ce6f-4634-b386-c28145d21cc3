/*
 * Function: ?AllocSelectHandle@CRFNewDatabase@@QEAA_NXZ
 * Address: 0x1404875A0
 */

char __fastcall CRFNewDatabase::AllocSelectHandle(CRFNewDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  __int16 v5; // [sp+20h] [bp-18h]@4
  CRFNewDatabase *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = SQLAllocHandle_0(3, v6->m_hDbc, &v6->m_hStmtSelect);
  if ( v5 )
  {
    CRFNewDatabase::ErrLog(v6, "SELECT SQLAllocHandle Fail");
    result = 0;
  }
  else
  {
    result = 1;
  }
  return result;
}
