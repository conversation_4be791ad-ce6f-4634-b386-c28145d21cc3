/*
 * Function: j_??$_Uninit_copy@PEAUAreaData@@PEAU1@V?$allocator@UAreaData@@@std@@@std@@YAPEAUAreaData@@PEAU1@00AEAV?$allocator@UAreaData@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000DE0E
 */

AreaData *__fastcall std::_Uninit_copy<AreaData *,AreaData *,std::allocator<AreaData>>(AreaData *_First, AreaData *_Last, AreaData *_Dest, std::allocator<AreaData> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<AreaData *,AreaData *,std::allocator<AreaData>>(_First, _Last, _Dest, _Al, __formal, a6);
}
