/*
 * Function: ?SendMsg_AlterContEffectTime@CPlayer@@QEAAXE@Z
 * Address: 0x1400E07F0
 */

void __fastcall CPlayer::SendMsg_AlterContEffectTime(CPlayer *this, char byContType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@9
  __int64 v5; // [sp+0h] [bp-B8h]@1
  _alter_cont_effect_time_zocl v6; // [sp+38h] [bp-80h]@4
  unsigned int v7; // [sp+64h] [bp-54h]@4
  int v8; // [sp+68h] [bp-50h]@4
  int j; // [sp+6Ch] [bp-4Ch]@4
  bool *v10; // [sp+70h] [bp-48h]@7
  _base_fld *v11; // [sp+78h] [bp-40h]@8
  unsigned int v12; // [sp+80h] [bp-38h]@8
  char pbyType; // [sp+94h] [bp-24h]@9
  char v14; // [sp+95h] [bp-23h]@9
  CPlayer *v15; // [sp+C0h] [bp+8h]@1
  char v16; // [sp+C8h] [bp+10h]@1

  v16 = byContType;
  v15 = this;
  v2 = &v5;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _alter_cont_effect_time_zocl::_alter_cont_effect_time_zocl(&v6);
  v7 = _sf_continous::GetSFContCurTime();
  v8 = 0;
  for ( j = 0; j < 8; ++j )
  {
    v10 = &v15->m_SFCont[(unsigned __int8)v16][j].m_bExist;
    if ( *v10 )
    {
      v11 = CRecordData::GetRecord(&stru_1799C8410 + v10[1], *((_WORD *)v10 + 1));
      v6.List[v8].wEffectCode = CCharacter::CalcEffectBit((CCharacter *)&v15->vfptr, v10[1], *((_WORD *)v10 + 1));
      v12 = v7 - *((_DWORD *)v10 + 2);
      v6.List[v8++].zLeftSec = *((_WORD *)v10 + 6) - v12;
    }
  }
  v6.byEffectNum = v8;
  pbyType = 17;
  v14 = 24;
  v4 = _alter_cont_effect_time_zocl::size(&v6);
  CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, &v6.byEffectNum, v4);
}
