/*
 * Function: _CUnmannedTraderSubClassInfoCode::GetGroupID_::_1_::dtor$2
 * Address: 0x140383620
 */

void __fastcall CUnmannedTraderSubClassInfoCode::GetGroupID_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>((std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)(a2 + 120));
}
