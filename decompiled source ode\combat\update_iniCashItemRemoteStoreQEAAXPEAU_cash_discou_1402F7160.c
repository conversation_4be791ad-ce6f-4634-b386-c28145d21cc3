/*
 * Function: ?update_ini@CashItemRemoteStore@@QEAAXPEAU_cash_discount_ini_@@@Z
 * Address: 0x1402F7160
 */

void __fastcall CashItemRemoteStore::update_ini(CashItemRemoteStore *this, _cash_discount_ini_ *pNewIni)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned __int8 j; // [sp+20h] [bp-18h]@5
  CashItemRemoteStore *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pNewIni )
  {
    v6->m_cde.m_ini.m_bUseCashDiscount = pNewIni->m_bUseCashDiscount;
    v6->m_cde.m_ini.m_bRepeat = pNewIni->m_bRepeat;
    v6->m_cde.m_ini.m_byRepeatDay = pNewIni->m_byRepeatDay;
    for ( j = 0; (signed int)j < 3; ++j )
    {
      v6->m_cde.m_ini.m_wYear[j] = pNewIni->m_wYear[j];
      v6->m_cde.m_ini.m_byMonth[j] = pNewIni->m_byMonth[j];
      v6->m_cde.m_ini.m_byDay[j] = pNewIni->m_byDay[j];
      v6->m_cde.m_ini.m_byHour[j] = pNewIni->m_byHour[j];
      v6->m_cde.m_ini.m_byMinute[j] = pNewIni->m_byMinute[j];
      v6->m_cde.m_ini.m_cdeTime[j] = pNewIni->m_cdeTime[j];
    }
    CashItemRemoteStore::SetNextDiscountEventTime(v6);
  }
}
