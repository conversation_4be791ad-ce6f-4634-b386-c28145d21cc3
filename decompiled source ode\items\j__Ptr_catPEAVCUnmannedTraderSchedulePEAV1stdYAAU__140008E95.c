/*
 * Function: j_??$_Ptr_cat@PEAVCUnmannedTraderSchedule@@PEAV1@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAPEAVCUnmannedTraderSchedule@@0@Z
 * Address: 0x140008E95
 */

std::_Nonscalar_ptr_iterator_tag __fastcall std::_Ptr_cat<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *>(CUnmannedTraderSchedule **__formal, CUnmannedTraderSchedule **a2)
{
  return std::_Ptr_cat<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *>(__formal, a2);
}
