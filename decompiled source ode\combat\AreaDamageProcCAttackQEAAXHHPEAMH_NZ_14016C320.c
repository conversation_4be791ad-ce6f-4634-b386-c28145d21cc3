/*
 * Function: ?AreaDamageProc@CAttack@@QEAAXHHPEAMH_N@Z
 * Address: 0x14016C320
 */

void __fastcall CAttack::AreaDamageProc(CAttack *this, int nLimitRadius, int nAttPower, float *pTar, int nEffAttPower, bool bUseEffBullet)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  CCharacter **v8; // rcx@9
  _attack_param *v9; // rdx@9
  _attack_param *v10; // r8@9
  CCharacter **v11; // rcx@10
  _attack_param *v12; // rdx@10
  _attack_param *v13; // r8@10
  _sec_info *v14; // rax@19
  CCharacter *v15; // rdx@33
  int v16; // eax@33
  CPlayer *v17; // rax@39
  CCharacter *v18; // rdx@53
  int v19; // eax@53
  float v20; // xmm0_4@60
  float v21; // xmm1_4@61
  int v22; // eax@66
  __int64 v23; // [sp+0h] [bp-128h]@1
  CCharacter *pDst; // [sp+20h] [bp-108h]@9
  bool bBackAttack; // [sp+28h] [bp-100h]@9
  CMapData *v26; // [sp+30h] [bp-F8h]@4
  int nSecNum; // [sp+38h] [bp-F0h]@4
  _pnt_rect pRect; // [sp+48h] [bp-E0h]@14
  bool pbInGuildBattle; // [sp+74h] [bp-B4h]@14
  int j; // [sp+84h] [bp-A4h]@14
  int k; // [sp+88h] [bp-A0h]@16
  unsigned int dwSecIndex; // [sp+8Ch] [bp-9Ch]@19
  CObjectList *v33; // [sp+90h] [bp-98h]@19
  CObjectList *v34; // [sp+98h] [bp-90h]@20
  CGameObject *pObject; // [sp+A0h] [bp-88h]@22
  CPlayer *v36; // [sp+A8h] [bp-80h]@35
  CPlayer *v37; // [sp+B0h] [bp-78h]@36
  AutominePersonal *v38; // [sp+B8h] [bp-70h]@39
  CAnimus *v39; // [sp+C0h] [bp-68h]@45
  CPlayer *v40; // [sp+C8h] [bp-60h]@48
  CCharacter *v41; // [sp+D0h] [bp-58h]@53
  CPlayer *v42; // [sp+D8h] [bp-50h]@53
  float v43; // [sp+E0h] [bp-48h]@60
  int v44; // [sp+E4h] [bp-44h]@64
  int v45; // [sp+E8h] [bp-40h]@33
  CGameObjectVtbl *v46; // [sp+F0h] [bp-38h]@33
  int v47; // [sp+F8h] [bp-30h]@53
  CGameObjectVtbl *v48; // [sp+100h] [bp-28h]@53
  float v49; // [sp+108h] [bp-20h]@61
  float v50; // [sp+10Ch] [bp-1Ch]@62
  float v51; // [sp+110h] [bp-18h]@62
  _attack_param *v52; // [sp+118h] [bp-10h]@66
  CAttack *v53; // [sp+130h] [bp+8h]@1
  int v54; // [sp+138h] [bp+10h]@1
  int nAttPnt; // [sp+140h] [bp+18h]@1
  float *pPos; // [sp+148h] [bp+20h]@1

  pPos = pTar;
  nAttPnt = nAttPower;
  v54 = nLimitRadius;
  v53 = this;
  v6 = &v23;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v26 = v53->m_pAttChar->m_pCurMap;
  nSecNum = CMapData::GetSectorIndex(v26, pTar);
  if ( nSecNum != -1 )
  {
    if ( v53->m_pp->pDst && v53->m_pAttChar != v53->m_pp->pDst )
    {
      v53->m_DamList[0].m_pChar = v53->m_pp->pDst;
      if ( v53->m_pp->bMatchless )
      {
        v53->m_DamList[0].m_nDamage = ((int (__fastcall *)(CCharacter *))v53->m_pp->pDst->vfptr->GetHP)(v53->m_pp->pDst);
      }
      else if ( bUseEffBullet )
      {
        v8 = &v53->m_pp->pDst;
        v9 = v53->m_pp;
        v10 = v53->m_pp;
        bBackAttack = v53->m_pp->bBackAttack;
        pDst = *v8;
        v53->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                        v53->m_pAttChar,
                                        nEffAttPower,
                                        v10->nPart,
                                        v9->nTol,
                                        pDst,
                                        bBackAttack);
      }
      else
      {
        v11 = &v53->m_pp->pDst;
        v12 = v53->m_pp;
        v13 = v53->m_pp;
        bBackAttack = v53->m_pp->bBackAttack;
        pDst = *v11;
        v53->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                        v53->m_pAttChar,
                                        nAttPnt,
                                        v13->nPart,
                                        v12->nTol,
                                        pDst,
                                        bBackAttack);
      }
      v53->m_nDamagedObjNum = 1;
    }
    CMapData::GetRectInRadius(v26, &pRect, 1, nSecNum);
    pbInGuildBattle = 0;
    for ( j = pRect.nStarty; ; ++j )
    {
      if ( j > pRect.nEndy )
        return;
      for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
      {
        v14 = CMapData::GetSecInfo(v26);
        dwSecIndex = v14->m_nSecNumW * j + k;
        v33 = CMapData::GetSectorListObj(v53->m_pAttChar->m_pCurMap, v53->m_pAttChar->m_wMapLayerIndex, dwSecIndex);
        if ( v33 )
        {
          v34 = (CObjectList *)v33->m_Head.m_pNext;
          while ( 1 )
          {
            while ( 1 )
            {
              if ( (_object_list_point *)v34 == &v33->m_Tail )
                goto LABEL_17;
              pObject = (CGameObject *)v34->vfptr;
              v34 = (CObjectList *)v34->m_Head.m_pItem;
              if ( v53->m_nDamagedObjNum >= 30 )
                return;
              if ( !pObject->m_ObjID.m_byKind
                && pObject != (CGameObject *)v53->m_pAttChar
                && (CCharacter *)pObject != v53->m_pp->pDst )
              {
                if ( pObject->m_bLive )
                {
                  if ( !pObject->m_bCorpse )
                  {
                    if ( (unsigned __int8)((int (__fastcall *)(CGameObject *, _QWORD))pObject->vfptr->IsBeAttackedAble)(
                                            pObject,
                                            0i64) )
                    {
                      pbInGuildBattle = 0;
                      if ( !CAttack::CheckGuildBattleLimit(v53, pObject, &pbInGuildBattle) )
                      {
                        if ( pbInGuildBattle
                          || (v45 = ((int (__fastcall *)(CGameObject *))pObject->vfptr->GetObjRace)(pObject),
                              v15 = v53->m_pAttChar,
                              v46 = v53->m_pAttChar->vfptr,
                              v16 = ((int (__fastcall *)(CCharacter *))v46->GetObjRace)(v15),
                              v45 != v16)
                          || !v53->m_pAttChar->m_ObjID.m_byID
                          && ((v36 = (CPlayer *)v53->m_pAttChar, pObject->m_ObjID.m_byID)
                           || (v37 = (CPlayer *)pObject, CPlayer::IsPunished((CPlayer *)pObject, 1, 0))
                           || CPlayer::IsChaosMode(v36))
                          && (pObject->m_ObjID.m_byID != 11
                           || (v38 = (AutominePersonal *)pObject,
                               v17 = AutominePersonal::GetOwner((AutominePersonal *)pObject),
                               v17 != v36)
                           && CPlayer::IsChaosMode(v36)) )
                        {
                          if ( (unsigned __int8)((int (__fastcall *)(CGameObject *, CCharacter *))pObject->vfptr->IsBeDamagedAble)(
                                                  pObject,
                                                  v53->m_pAttChar) )
                          {
                            if ( v53->m_pAttChar->m_ObjID.m_byID
                              || (v39 = CPlayer::GetRecallAnimus((CPlayer *)v53->m_pAttChar)) == 0i64
                              || (CGameObject *)v39 != pObject )
                            {
                              if ( v53->m_pAttChar->m_ObjID.m_byID
                                || (v40 = (CPlayer *)v53->m_pAttChar,
                                    ((int (__fastcall *)(CPlayer *))v40->vfptr->GetObjRace)(v40) != 1)
                                || CPlayer::IsChaosMode(v40)
                                || pObject->m_ObjID.m_byID != 3 )
                              {
                                if ( pObject->m_ObjID.m_byID != 7 )
                                {
                                  if ( pObject->m_ObjID.m_byID != 4
                                    || (v41 = v53->m_pAttChar,
                                        v42 = (CPlayer *)v53->m_pAttChar,
                                        v47 = ((int (__fastcall *)(__int64))v41->vfptr->GetObjRace)((__int64)v41),
                                        v18 = v53->m_pAttChar,
                                        v48 = v53->m_pAttChar->vfptr,
                                        v19 = ((int (__fastcall *)(CCharacter *))v48->GetObjRace)(v18),
                                        v47 != v19)
                                    || CPlayer::IsChaosMode(v42) )
                                  {
                                    if ( (unsigned __int8)((int (__fastcall *)(CCharacter *))v53->m_pAttChar->vfptr->IsAttackableInTown)(v53->m_pAttChar)
                                      || (unsigned __int8)((int (__fastcall *)(CGameObject *))pObject->vfptr->IsAttackableInTown)(pObject)
                                      || !(unsigned __int8)((int (__fastcall *)(CCharacter *))v53->m_pAttChar->vfptr->IsInTown)(v53->m_pAttChar)
                                      && !(unsigned __int8)((int (__fastcall *)(CGameObject *))pObject->vfptr->IsInTown)(pObject) )
                                    {
                                      v20 = v53->m_pAttChar->m_fCurPos[1] - pObject->m_fCurPos[1];
                                      abs(v20);
                                      v43 = v20;
                                      if ( v20 <= 350.0 )
                                      {
                                        GetSqrt(pPos, pObject->m_fCurPos);
                                        v49 = v20;
                                        ((void (__fastcall *)(CGameObject *))pObject->vfptr->GetWidth)(pObject);
                                        v21 = v49 - (float)(v20 / 2.0);
                                        if ( v21 <= 0.0 )
                                        {
                                          v51 = 0.0;
                                        }
                                        else
                                        {
                                          GetSqrt(pPos, pObject->m_fCurPos);
                                          v50 = v21;
                                          ((void (__fastcall *)(CGameObject *))pObject->vfptr->GetWidth)(pObject);
                                          v51 = v50 - (float)(v21 / 2.0);
                                        }
                                        v44 = (signed int)ffloor(v51);
                                        if ( v44 <= v54 )
                                          break;
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
            v53->m_DamList[v53->m_nDamagedObjNum].m_pChar = (CCharacter *)pObject;
            if ( v53->m_pp->bMatchless )
              break;
            v52 = v53->m_pp;
            v22 = CCharacter::GetAttackRandomPart((CCharacter *)pObject);
            bBackAttack = 0;
            pDst = (CCharacter *)pObject;
            v53->m_DamList[v53->m_nDamagedObjNum].m_nDamage = CCharacter::GetAttackDamPoint(
                                                                v53->m_pAttChar,
                                                                nAttPnt,
                                                                v22,
                                                                v52->nTol,
                                                                (CCharacter *)pObject,
                                                                0);
            if ( v53->m_DamList[v53->m_nDamagedObjNum].m_nDamage != -2 )
            {
              v53->m_DamList[v53->m_nDamagedObjNum].m_nDamage = (signed int)ffloor((float)v53->m_DamList[v53->m_nDamagedObjNum].m_nDamage * (float)((float)(v54 - v44) / (float)v54));
              if ( v53->m_DamList[v53->m_nDamagedObjNum].m_nDamage < 1 )
                continue;
            }
LABEL_70:
            ++v53->m_nDamagedObjNum;
          }
          v53->m_DamList[v53->m_nDamagedObjNum].m_nDamage = ((int (__fastcall *)(CGameObject *))pObject->vfptr->GetHP)(pObject);
          goto LABEL_70;
        }
LABEL_17:
        ;
      }
    }
  }
}
