/*
 * Function: ?Goal@CNormalGuildBattle@GUILD_BATTLE@@QEAAEKH@Z
 * Address: 0x1403E4CA0
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattle::Goal(GUILD_BATTLE::CNormalGuildBattle *this, unsigned int dwCharacSerial, int iPortalInx)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@9
  GUILD_BATTLE::CCurrentGuildBattleInfoManager *v6; // rax@13
  GUILD_BATTLE::CCurrentGuildBattleInfoManager *v7; // rax@13
  GUILD_BATTLE::CCurrentGuildBattleInfoManager *v8; // rax@14
  GUILD_BATTLE::CCurrentGuildBattleInfoManager *v9; // rax@14
  GUILD_BATTLE::CGuildBattleLogger *v10; // rax@16
  GUILD_BATTLE::CGuildBattleLogger *v11; // rax@18
  __int64 v12; // [sp+0h] [bp-98h]@1
  int v13[2]; // [sp+20h] [bp-78h]@19
  char *v14; // [sp+28h] [bp-70h]@19
  bool v15; // [sp+30h] [bp-68h]@4
  GUILD_BATTLE::CNormalGuildBattleGuildMember *pkMember; // [sp+38h] [bp-60h]@4
  CPlayer *pkPlayer; // [sp+40h] [bp-58h]@4
  char v18; // [sp+48h] [bp-50h]@10
  char *wszGuildName; // [sp+50h] [bp-48h]@12
  unsigned int dwGoalCnt; // [sp+58h] [bp-40h]@13
  char v21; // [sp+5Ch] [bp-3Ch]@13
  unsigned int uiMapID; // [sp+60h] [bp-38h]@13
  unsigned int dwScore; // [sp+64h] [bp-34h]@13
  char v24; // [sp+68h] [bp-30h]@13
  unsigned int v25; // [sp+6Ch] [bp-2Ch]@13
  unsigned int v26; // [sp+70h] [bp-28h]@14
  char v27; // [sp+74h] [bp-24h]@14
  unsigned int v28; // [sp+78h] [bp-20h]@14
  unsigned int v29; // [sp+7Ch] [bp-1Ch]@14
  char v30; // [sp+80h] [bp-18h]@14
  unsigned int v31; // [sp+84h] [bp-14h]@14
  GUILD_BATTLE::CNormalGuildBattle *v32; // [sp+A0h] [bp+8h]@1
  unsigned int dwSerial; // [sp+A8h] [bp+10h]@1
  int iPortalInxa; // [sp+B0h] [bp+18h]@1

  iPortalInxa = iPortalInx;
  dwSerial = dwCharacSerial;
  v32 = this;
  v3 = &v12;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = 1;
  pkMember = GUILD_BATTLE::CNormalGuildBattleGuild::GetMemberPtr(&v32->m_k1P, dwCharacSerial);
  pkPlayer = 0i64;
  if ( !pkMember || (pkPlayer = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(pkMember)) == 0i64 )
  {
    pkMember = GUILD_BATTLE::CNormalGuildBattleGuild::GetMemberPtr(&v32->m_k2P, dwSerial);
    if ( pkMember )
    {
      pkPlayer = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(pkMember);
      v15 = 0;
    }
  }
  if ( pkPlayer )
  {
    v18 = GUILD_BATTLE::CNormalGuildBattleField::IsGoal(v32->m_pkField, pkPlayer, iPortalInxa);
    if ( v18 )
    {
      result = v18;
    }
    else
    {
      wszGuildName = 0i64;
      if ( v15 )
      {
        wszGuildName = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v32->m_k1P);
        GUILD_BATTLE::CNormalGuildBattleGuild::Goal(&v32->m_k1P, pkMember);
        dwGoalCnt = GUILD_BATTLE::CNormalGuildBattleGuild::GetGoalCnt(&v32->m_k1P);
        v21 = GUILD_BATTLE::CNormalGuildBattleGuild::GetColorInx(&v32->m_k1P);
        uiMapID = GUILD_BATTLE::CNormalGuildBattleField::GetMapID(v32->m_pkField);
        v6 = GUILD_BATTLE::CCurrentGuildBattleInfoManager::Instance();
        GUILD_BATTLE::CCurrentGuildBattleInfoManager::UpdateGoalCnt(v6, uiMapID, v21, dwGoalCnt);
        dwScore = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(&v32->m_k1P);
        v24 = GUILD_BATTLE::CNormalGuildBattleGuild::GetColorInx(&v32->m_k1P);
        v25 = GUILD_BATTLE::CNormalGuildBattleField::GetMapID(v32->m_pkField);
        v7 = GUILD_BATTLE::CCurrentGuildBattleInfoManager::Instance();
        GUILD_BATTLE::CCurrentGuildBattleInfoManager::UpdateScore(v7, v25, v24, dwScore);
      }
      else
      {
        wszGuildName = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v32->m_k2P);
        GUILD_BATTLE::CNormalGuildBattleGuild::Goal(&v32->m_k2P, pkMember);
        v26 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGoalCnt(&v32->m_k2P);
        v27 = GUILD_BATTLE::CNormalGuildBattleGuild::GetColorInx(&v32->m_k2P);
        v28 = GUILD_BATTLE::CNormalGuildBattleField::GetMapID(v32->m_pkField);
        v8 = GUILD_BATTLE::CCurrentGuildBattleInfoManager::Instance();
        GUILD_BATTLE::CCurrentGuildBattleInfoManager::UpdateGoalCnt(v8, v28, v27, v26);
        v29 = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(&v32->m_k2P);
        v30 = GUILD_BATTLE::CNormalGuildBattleGuild::GetColorInx(&v32->m_k2P);
        v31 = GUILD_BATTLE::CNormalGuildBattleField::GetMapID(v32->m_pkField);
        v9 = GUILD_BATTLE::CCurrentGuildBattleInfoManager::Instance();
        GUILD_BATTLE::CCurrentGuildBattleInfoManager::UpdateScore(v9, v31, v30, v29);
      }
      if ( !GUILD_BATTLE::CNormalGuildBattleField::ClearBall(v32->m_pkField) )
      {
        v10 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v10,
          "CNormalGuildBattle::Goal( %u ) : m_pkField->ClearBall() Fail!",
          dwSerial);
      }
      GUILD_BATTLE::CNormalGuildBattleField::ClearRegen(v32->m_pkField);
      if ( !GUILD_BATTLE::CNormalGuildBattleStateList::AdvanceRegenState(v32->m_pkStateList) )
      {
        v11 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v11,
          "CNormalGuildBattle::Goal( %u ) : m_pkStateList->AdvanceRegenState() Fail!",
          dwSerial);
      }
      GUILD_BATTLE::CNormalGuildBattle::SendGoalMsg(v32, v15, wszGuildName, pkPlayer, iPortalInxa);
      v14 = CPlayerDB::GetCharNameW(&pkPlayer->m_Param);
      *(_QWORD *)v13 = wszGuildName;
      GUILD_BATTLE::CNormalGuildBattleLogger::Log(
        &v32->m_kLogger,
        "CNormalGuildBattle::Goal( %u ) : Portal(%d) Goal! Guild(%s) Character(%s)",
        dwSerial,
        (unsigned int)iPortalInxa);
      result = 0;
    }
  }
  else
  {
    result = -111;
  }
  return result;
}
