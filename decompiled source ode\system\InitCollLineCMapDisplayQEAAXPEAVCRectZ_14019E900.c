/*
 * Function: ?InitCollLine@CMapDisplay@@QEAAXPEAVCRect@@@Z
 * Address: 0x14019E900
 */

void __fastcall CMapDisplay::InitCollLine(CMapDisplay *this, CRect *prcWnd)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  int j; // [sp+24h] [bp-14h]@4
  CMapData *pMap; // [sp+28h] [bp-10h]@6
  CMapDisplay *v8; // [sp+40h] [bp+8h]@1
  CRect *prcWnda; // [sp+48h] [bp+10h]@1

  prcWnda = prcWnd;
  v8 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = dword_141470B98;
  for ( j = 0; j < v5; ++j )
  {
    pMap = (CMapData *)(1504i64 * j + unk_141470BC8);
    CCollLineDraw::InitLine(&v8->m_CollLineDraw[(signed __int64)j], pMap, prcWnda);
  }
}
