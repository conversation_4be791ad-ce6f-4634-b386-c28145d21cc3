/*
 * Function: ?SendMsg_MoveError@CPlayer@@QEAAXE@Z
 * Address: 0x1400D6340
 */

void __fastcall CPlayer::SendMsg_MoveError(CPlayer *this, char byRetCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  char Dst; // [sp+39h] [bp-4Fh]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v8; // [sp+65h] [bp-23h]@4
  CPlayer *v9; // [sp+90h] [bp+8h]@1

  v9 = this;
  v2 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  szMsg = byRetCode;
  memcpy_0(&Dst, v9->m_fCurPos, 0xCui64);
  pbyType = 4;
  v8 = 3;
  CNetProcess::LoadSendMsg(unk_1414F2088, v9->m_ObjID.m_wIndex, &pbyType, &szMsg, 0xDu);
}
