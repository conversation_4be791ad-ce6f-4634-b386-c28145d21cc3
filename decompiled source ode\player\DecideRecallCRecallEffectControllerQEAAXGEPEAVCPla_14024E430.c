/*
 * Function: ?Decide<PERSON><PERSON>ll@CRecallEffectController@@QEAAXGEPEAVCPlayer@@@Z
 * Address: 0x14024E430
 */

void __fastcall CRecallEffectController::DecideRecall(CRecallEffectController *this, unsigned __int16 dwRequestID, char byAgree, CPlayer *pkObj)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CPlayer *v6; // rax@13
  CPlayer *v7; // rax@19
  CPlayer *v8; // rax@26
  int v9; // eax@26
  int v10; // eax@29
  GUILD_BATTLE::CNormalGuildBattleManager *v11; // rax@54
  GUILD_BATTLE::CNormalGuildBattle *v12; // rax@54
  GUILD_BATTLE::CNormalGuildBattleField *v13; // rax@54
  CPlayer *v14; // rax@54
  GUILD_BATTLE::CNormalGuildBattleManager *v15; // rax@54
  GUILD_BATTLE::CNormalGuildBattle *v16; // rax@54
  GUILD_BATTLE::CNormalGuildBattleField *v17; // rax@54
  CPlayer *v18; // rax@60
  CPlayer *v19; // rax@60
  bool v20; // al@61
  __int64 v21; // [sp+0h] [bp-68h]@1
  CRecallRequest *pkRequest; // [sp+28h] [bp-40h]@10
  char v23; // [sp+34h] [bp-34h]@10
  int v24; // [sp+38h] [bp-30h]@26
  int v25; // [sp+3Ch] [bp-2Ch]@26
  CMapData *v26; // [sp+40h] [bp-28h]@54
  CMapData *v27; // [sp+48h] [bp-20h]@54
  unsigned int dwGuildSerial; // [sp+50h] [bp-18h]@54
  unsigned int v29; // [sp+54h] [bp-14h]@54
  CRecallEffectController *v30; // [sp+70h] [bp+8h]@1
  char v31; // [sp+80h] [bp+18h]@1
  CPlayer *pkDest; // [sp+88h] [bp+20h]@1

  pkDest = pkObj;
  v31 = byAgree;
  v30 = this;
  v4 = &v21;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pkObj && pkObj->m_bOper )
  {
    if ( byAgree && byAgree != 1 )
    {
      CRecallEffectController::SendDecideRecallErrorResultToDest(v30, 6, pkObj, -1);
      return;
    }
    pkRequest = 0i64;
    v23 = CRecallEffectController::GetResistedRecall(v30, dwRequestID, &pkRequest);
    if ( v23 )
    {
      CRecallEffectController::SendDecideRecallErrorResultToDest(v30, v23, pkDest, -1);
      return;
    }
    if ( v31 == 1 )
    {
      v6 = CRecallRequest::GetOwner(pkRequest);
      CRecallEffectController::SendRecallReqeustResult(v30, 4, v6);
      CRecallEffectController::Close(v30, pkRequest, 1);
      return;
    }
    if ( CPlayer::IsSiegeMode(pkDest) )
    {
      CRecallEffectController::SendDecideRecallErrorResultToDest(v30, 26, pkDest, -1);
      return;
    }
    if ( CHolyStoneSystem::GetDestroyerState(&g_HolySys) == 2
      && CHolyStoneSystem::GetDestroyerSerial(&g_HolySys) == pkDest->m_dwObjSerial )
    {
      CRecallEffectController::SendDecideRecallErrorResultToDest(v30, 20, pkDest, -1);
      return;
    }
    v7 = CRecallRequest::GetOwner(pkRequest);
    if ( CGameObject::GetCurSecNum((CGameObject *)&v7->vfptr) == -1
      || CRecallRequest::GetOwner(pkRequest)->m_bMapLoading )
    {
      CRecallEffectController::SendDecideRecallErrorResultToDest(v30, 21, pkDest, -1);
      return;
    }
    if ( CRecallRequest::IsRecallParty(pkRequest) )
    {
      if ( pkDest->m_pCurMap != CRecallRequest::GetOwner(pkRequest)->m_pCurMap )
      {
        v18 = CRecallRequest::GetOwner(pkRequest);
        CRecallEffectController::SendDecideRecallErrorResultToDest(v30, 12, pkDest, v18->m_pCurMap->m_nMapCode);
        v19 = CRecallRequest::GetOwner(pkRequest);
        CRecallEffectController::SendRecallReqeustResult(v30, 13, v19);
        CRecallEffectController::Close(v30, pkRequest, 1);
        return;
      }
    }
    else
    {
      if ( !CRecallRequest::IsBattleModeUse(pkRequest)
        && (unsigned __int8)((int (__fastcall *)(CPlayer *))pkDest->vfptr->Is_Battle_Mode)(pkDest) )
      {
        CRecallEffectController::SendDecideRecallErrorResultToDest(v30, 18, pkDest, -1);
        return;
      }
      v8 = CRecallRequest::GetOwner(pkRequest);
      v24 = CMapData::GetLevelLimit(v8->m_pCurMap);
      v25 = CRecallRequest::GetOwner(pkRequest)->m_pCurMap->m_pMapSet->m_nUpLevelLim;
      v9 = ((int (__fastcall *)(CPlayer *))pkDest->vfptr->GetLevel)(pkDest);
      if ( v9 < v24 )
      {
        CRecallEffectController::SendDecideRecallErrorResultToDest(v30, 19, pkDest, -1);
        return;
      }
      if ( v25 != -1 )
      {
        v10 = ((int (__fastcall *)(CPlayer *))pkDest->vfptr->GetLevel)(pkDest);
        if ( v10 > v25 )
        {
          CRecallEffectController::SendDecideRecallErrorResultToDest(v30, 19, pkDest, -1);
          return;
        }
      }
      if ( pkDest->m_pDHChannel || CRecallRequest::GetOwner(pkRequest)->m_pDHChannel )
      {
        if ( pkDest->m_pDHChannel && CRecallRequest::GetOwner(pkRequest)->m_pDHChannel )
        {
          if ( pkDest->m_pDHChannel != CRecallRequest::GetOwner(pkRequest)->m_pDHChannel )
          {
            CRecallEffectController::SendDecideRecallErrorResultToDest(v30, 22, pkDest, -1);
            return;
          }
        }
        else
        {
          if ( !pkDest->m_pDHChannel && CRecallRequest::GetOwner(pkRequest)->m_pDHChannel )
          {
            CRecallEffectController::SendDecideRecallErrorResultToDest(v30, 22, pkDest, -1);
            return;
          }
          if ( pkDest->m_pDHChannel && !CRecallRequest::GetOwner(pkRequest)->m_pDHChannel )
          {
            CRecallEffectController::SendDecideRecallErrorResultToDest(v30, 23, pkDest, -1);
            return;
          }
        }
      }
      if ( pkDest->m_bInGuildBattle || CRecallRequest::GetOwner(pkRequest)->m_bInGuildBattle )
      {
        if ( pkDest->m_bInGuildBattle && !CRecallRequest::GetOwner(pkRequest)->m_bInGuildBattle )
        {
          CRecallEffectController::SendDecideRecallErrorResultToDest(v30, 25, pkDest, -1);
          return;
        }
        if ( !pkDest->m_bInGuildBattle || CRecallRequest::GetOwner(pkRequest)->m_bInGuildBattle )
        {
          CRecallEffectController::SendDecideRecallErrorResultToDest(v30, 9, pkDest, -1);
          return;
        }
        if ( pkDest->m_bInGuildBattle && CRecallRequest::GetOwner(pkRequest)->m_bInGuildBattle )
        {
          dwGuildSerial = CPlayerDB::GetGuildSerial(&pkDest->m_Param);
          v11 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
          v12 = GUILD_BATTLE::CNormalGuildBattleManager::GetBattleByGuildSerial(v11, dwGuildSerial);
          v13 = GUILD_BATTLE::CNormalGuildBattle::GetField(v12);
          v26 = GUILD_BATTLE::CNormalGuildBattleField::GetMap(v13);
          v14 = CRecallRequest::GetOwner(pkRequest);
          v29 = CPlayerDB::GetGuildSerial(&v14->m_Param);
          v15 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
          v16 = GUILD_BATTLE::CNormalGuildBattleManager::GetBattleByGuildSerial(v15, v29);
          v17 = GUILD_BATTLE::CNormalGuildBattle::GetField(v16);
          v27 = GUILD_BATTLE::CNormalGuildBattleField::GetMap(v17);
          if ( v26 != v27 )
          {
            CRecallEffectController::SendDecideRecallErrorResultToDest(v30, 25, pkDest, -1);
            return;
          }
          if ( pkDest->m_bTakeGravityStone )
          {
            CRecallEffectController::SendDecideRecallErrorResultToDest(v30, 10, pkDest, -1);
            return;
          }
        }
      }
    }
    v20 = CRecallRequest::IsRecallAfterStoneState(pkRequest);
    v23 = CRecallRequest::Recall(pkRequest, pkDest, v20);
    if ( v23 )
      CRecallEffectController::SendDecideRecallErrorResultToDest(v30, v23, pkDest, -1);
    CRecallEffectController::Close(v30, pkRequest, 1);
  }
}
