/*
 * Function: ?exist_automine@CRFWorldDatabase@@QEAAHEE@Z
 * Address: 0x1404A87C0
 */

signed __int64 __fastcall CRFWorldDatabase::exist_automine(CRFWorldDatabase *this, char byCollisionType, char byRace)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v6; // [sp+0h] [bp-178h]@1
  void *SQLStmt; // [sp+20h] [bp-158h]@13
  __int16 v8; // [sp+30h] [bp-148h]@9
  char Dest; // [sp+50h] [bp-128h]@4
  char v10; // [sp+51h] [bp-127h]@4
  int v11; // [sp+154h] [bp-24h]@14
  unsigned __int8 v12; // [sp+158h] [bp-20h]@16
  unsigned __int64 v13; // [sp+168h] [bp-10h]@4
  CRFWorldDatabase *v14; // [sp+180h] [bp+8h]@1

  v14 = this;
  v3 = &v6;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = (unsigned __int64)&v6 ^ _security_cookie;
  Dest = 0;
  memset(&v10, 0, 0xFFui64);
  sprintf(&Dest, "{ CALL pselect_automine_inven(%d, %d) }", (unsigned __int8)byCollisionType, (unsigned __int8)byRace);
  if ( v14->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v14->vfptr, &Dest);
  if ( v14->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v14->vfptr) )
  {
    v8 = SQLExecDirect_0(v14->m_hStmtSelect, &Dest, -3);
    if ( v8 && v8 != 1 )
    {
      if ( v8 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v14->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v8, &Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v8, v14->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v11 = 0;
      v8 = SQLFetch_0(v14->m_hStmtSelect);
      if ( v8 && v8 != 1 )
      {
        v12 = 0;
        if ( v8 == 100 )
        {
          v12 = 2;
        }
        else
        {
          SQLStmt = v14->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v8, &Dest, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v8, v14->m_hStmtSelect);
          v12 = 1;
        }
        if ( v14->m_hStmtSelect )
          SQLCloseCursor_0(v14->m_hStmtSelect);
        result = v12;
      }
      else
      {
        if ( v14->m_hStmtSelect )
          SQLCloseCursor_0(v14->m_hStmtSelect);
        if ( v14->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v14->vfptr, "%s Success", &Dest);
        result = 0i64;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v14->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1i64;
  }
  return result;
}
