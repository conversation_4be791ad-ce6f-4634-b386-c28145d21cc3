/*
 * Function: ??$_Unchecked_move_backward@PEAVCUnmannedTraderSchedule@@PEAV1@@stdext@@YAPEAVCUnmannedTraderSchedule@@PEAV1@00@Z
 * Address: 0x1403969F0
 */

CUnmannedTraderSchedule *__fastcall stdext::_Unchecked_move_backward<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *>(CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last, CUnmannedTraderSchedule *_Dest)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  std::random_access_iterator_tag *v5; // rax@4
  __int64 v7; // [sp+0h] [bp-48h]@1
  std::_Undefined_move_tag v8; // [sp+20h] [bp-28h]@4
  std::_Range_checked_iterator_tag v9; // [sp+28h] [bp-20h]@4
  std::_Range_checked_iterator_tag v10; // [sp+30h] [bp-18h]@4
  std::_Undefined_move_tag v11; // [sp+31h] [bp-17h]@4
  CUnmannedTraderSchedule *v12; // [sp+32h] [bp-16h]@4
  CUnmannedTraderSchedule *_Firsta; // [sp+50h] [bp+8h]@1
  CUnmannedTraderSchedule *_Lasta; // [sp+58h] [bp+10h]@1
  CUnmannedTraderSchedule *__formal; // [sp+60h] [bp+18h]@1

  __formal = _Dest;
  _Lasta = _Last;
  _Firsta = _First;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  memset(&v10, 0, sizeof(v10));
  v11 = std::_Move_cat<CUnmannedTraderSchedule *>(&__formal);
  LOBYTE(v5) = std::_Iter_random<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *>(&v12, &_Firsta);
  v9 = v10;
  v8 = v11;
  return std::_Move_backward_opt<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *,std::random_access_iterator_tag,std::_Undefined_move_tag>(
           _Firsta,
           _Lasta,
           __formal,
           (std::random_access_iterator_tag)v5->0,
           v11,
           v10);
}
