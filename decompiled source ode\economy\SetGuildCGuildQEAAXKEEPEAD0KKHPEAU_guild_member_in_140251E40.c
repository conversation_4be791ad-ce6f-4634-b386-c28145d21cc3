/*
 * Function: ?SetGuild@CGuild@@QEAAXKEEPEAD0KKHPEAU_guild_member_info@@NNKEHPEAU_io_money_data@@KKK@Z
 * Address: 0x140251E40
 */

void __fastcall CGuild::SetGuild(CGuild *this, unsigned int dwSerial, char byGrade, char byRace, char *pwszName, char *pwszGreetingMsg, unsigned int dwEmblemBack, unsigned int dwEmblemMark, int nNum, _guild_member_info *pEstMember, long double dTotalDalant, long double dTotalGold, unsigned int dwMasterSerial, char byMasterPrevGrade, int nIOMoneyHisNum, _io_money_data *pIOMonHisList, unsigned int dwGuildBattleTotalWinCnt, unsigned int dwGuildBattleTotalDrawCnt, unsigned int dwGuildBattleTotalLoseCnt)
{
  __int64 *v19; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v21; // eax@23
  CNationSettingManager *v22; // rax@27
  __int64 v23; // [sp+0h] [bp-68h]@1
  char *pwszMasterName; // [sp+20h] [bp-48h]@28
  int j; // [sp+30h] [bp-38h]@9
  int k; // [sp+34h] [bp-34h]@15
  unsigned int pdwLastConnTime; // [sp+44h] [bp-24h]@23
  int v28; // [sp+54h] [bp-14h]@5
  char *v29; // [sp+58h] [bp-10h]@26
  CGuild *pGuild; // [sp+70h] [bp+8h]@1
  unsigned int dwGuildSerial; // [sp+78h] [bp+10h]@1
  char v32; // [sp+88h] [bp+20h]@1

  v32 = byRace;
  dwGuildSerial = dwSerial;
  pGuild = this;
  v19 = &v23;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v19 = -858993460;
    v19 = (__int64 *)((char *)v19 + 4);
  }
  pGuild->m_dwSerial = dwSerial;
  pGuild->m_byGrade = byGrade;
  strcpy_0(pGuild->m_wszName, pwszName);
  W2M(pGuild->m_wszName, pGuild->m_aszName, 0x11u);
  strcpy_s(pGuild->m_wszGreetingMsg, 0x100ui64, pwszGreetingMsg);
  CMgrGuildHistory::GetNewFileName(&CGuild::s_MgrHistory, dwGuildSerial, pGuild->m_szHistoryFileName);
  pGuild->m_nApplierNum = 0;
  pGuild->m_nMemberNum = nNum;
  memcpy_0(pGuild->m_MemberData, pEstMember, 48i64 * nNum);
  pGuild->m_bDBWait = 0;
  pGuild->m_bIOWait = 0;
  pGuild->m_bRankWait = 0;
  pGuild->m_byMoneyOutputKind = 0;
  pGuild->m_dwEmblemBack = dwEmblemBack;
  pGuild->m_dwEmblemMark = dwEmblemMark;
  pGuild->m_dTotalDalant = dTotalDalant;
  pGuild->m_dTotalGold = dTotalGold;
  pGuild->m_byRace = v32;
  if ( nIOMoneyHisNum >= 100 )
    v28 = 100;
  else
    v28 = nIOMoneyHisNum;
  pGuild->m_nIOMoneyHistoryNum = v28;
  if ( pGuild->m_nIOMoneyHistoryNum > 0 )
    memcpy_0(pGuild->m_IOMoneyHistory, pIOMonHisList, (signed __int64)pGuild->m_nIOMoneyHistoryNum << 6);
  _guild_master_info::init(&pGuild->m_MasterData);
  pGuild->m_MasterData.dwSerial = dwMasterSerial;
  pGuild->m_MasterData.byPrevGrade = byMasterPrevGrade;
  for ( j = 0; j < pGuild->m_nMemberNum; ++j )
  {
    if ( pGuild->m_MasterData.dwSerial == pGuild->m_MemberData[j].dwSerial && pGuild->m_MasterData.dwSerial != -1 )
      pGuild->m_MasterData.pMember = &pGuild->m_MemberData[j];
    if ( pGuild->m_MemberData[j].byClassInGuild == 1 )
    {
      for ( k = 0; k < 3; ++k )
      {
        if ( !pGuild->m_pGuildCommittee[k] )
        {
          pGuild->m_pGuildCommittee[k] = &pGuild->m_MemberData[j];
          break;
        }
      }
    }
  }
  pGuild->m_dwGuildBattleTotWin = dwGuildBattleTotalWinCnt;
  pGuild->m_dwGuildBattleTotDraw = dwGuildBattleTotalDrawCnt;
  pGuild->m_dwGuildBattleTotLose = dwGuildBattleTotalLoseCnt;
  if ( _guild_master_info::IsFill(&pGuild->m_MasterData) )
  {
    pdwLastConnTime = -1;
    v21 = GetConnectTime_AddBySec(-1814400);
    CRFWorldDatabase::Select_GuildMasterLastConn(pkDB, pGuild->m_MasterData.dwSerial, v21, &pdwLastConnTime);
    if ( pdwLastConnTime != -1 )
      pGuild->m_bPossibleElectMaster = 1;
  }
  else
  {
    pGuild->m_bPossibleElectMaster = 1;
  }
  CGuild::MakeDownMemberPacket(pGuild);
  CGuild::MakeDownApplierPacket(pGuild);
  CGuild::MakeQueryInfoPacket(pGuild);
  CGuild::MakeMoneyIOPacket(pGuild);
  CGuild::MakeBuddyPacket(pGuild);
  pGuild->m_dwLastLoopTime = GetLoopTime();
  if ( pGuild->m_MasterData.pMember )
  {
    v29 = pGuild->m_MasterData.pMember->wszName;
  }
  else
  {
    v22 = CTSingleton<CNationSettingManager>::Instance();
    v29 = CNationSettingManager::GetNoneString(v22);
  }
  pwszMasterName = v29;
  CGuildList::AddList(&CGuild::s_GuildList, pGuild->m_byRace, pGuild->m_byGrade, pGuild->m_wszName, v29);
  CMgrGuildHistory::load_guild(&CGuild::s_MgrHistory, pGuild, pGuild->m_szHistoryFileName);
}
