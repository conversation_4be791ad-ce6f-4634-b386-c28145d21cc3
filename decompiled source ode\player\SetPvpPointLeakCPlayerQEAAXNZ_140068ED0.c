/*
 * Function: ?SetPvpPointLeak@CPlayer@@QEAAXN@Z
 * Address: 0x140068ED0
 */

void __fastcall CPlayer::SetPvpPointLeak(CPlayer *this, long double dValue)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp-28h] [bp-28h]@1
  CPlayer *v5; // [sp+8h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5->m_Param.m_dPvpPointLeak = dValue;
  CUserDB::Update_PvpPointLeak(v5->m_pUserDB, dValue);
}
