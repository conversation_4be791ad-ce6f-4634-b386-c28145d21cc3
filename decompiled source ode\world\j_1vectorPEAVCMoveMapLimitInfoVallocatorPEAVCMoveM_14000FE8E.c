/*
 * Function: j_??1?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEAA@XZ
 * Address: 0x14000FE8E
 */

void __fastcall std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this)
{
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(this);
}
