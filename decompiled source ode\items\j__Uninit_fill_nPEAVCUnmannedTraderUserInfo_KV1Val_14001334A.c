/*
 * Function: j_??$_Uninit_fill_n@PEAVCUnmannedTraderUserInfo@@_KV1@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@YAXPEAVCUnmannedTraderUserInfo@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderUserInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14001334A
 */

void __fastcall std::_Uninit_fill_n<CUnmannedTraderUserInfo *,unsigned __int64,CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(CUnmannedTraderUserInfo *_First, unsigned __int64 _Count, CUnmannedTraderUserInfo *_Val, std::allocator<CUnmannedTraderUserInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CUnmannedTraderUserInfo *,unsigned __int64,CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(
    _First,
    _Count,
    _Val,
    _Al,
    __formal,
    a6);
}
