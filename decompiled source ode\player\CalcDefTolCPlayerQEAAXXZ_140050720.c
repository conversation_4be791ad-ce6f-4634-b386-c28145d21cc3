/*
 * Function: ?CalcDefTol@CPlayer@@QEAAXXZ
 * Address: 0x140050720
 */

void __fastcall CPlayer::CalcDefTol(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-D8h]@1
  int j; // [sp+20h] [bp-B8h]@5
  char *v5; // [sp+28h] [bp-B0h]@7
  _base_fld *v6; // [sp+30h] [bp-A8h]@8
  char *v7; // [sp+38h] [bp-A0h]@8
  int k; // [sp+40h] [bp-98h]@8
  char *v9; // [sp+48h] [bp-90h]@12
  _base_fld *v10; // [sp+50h] [bp-88h]@13
  char *v11; // [sp+58h] [bp-80h]@13
  int l; // [sp+60h] [bp-78h]@13
  char *v13; // [sp+68h] [bp-70h]@16
  _base_fld *v14; // [sp+70h] [bp-68h]@17
  char *v15; // [sp+78h] [bp-60h]@17
  int m; // [sp+80h] [bp-58h]@17
  char *v17; // [sp+88h] [bp-50h]@23
  char *v18; // [sp+90h] [bp-48h]@24
  _base_fld *v19; // [sp+98h] [bp-40h]@25
  _base_fld *v20; // [sp+A0h] [bp-38h]@28
  int n; // [sp+A8h] [bp-30h]@32
  int ii; // [sp+ACh] [bp-2Ch]@37
  _base_fld *v23; // [sp+B0h] [bp-28h]@39
  char *v24; // [sp+B8h] [bp-20h]@40
  int jj; // [sp+C0h] [bp-18h]@40
  CPlayer *v26; // [sp+E0h] [bp+8h]@1

  v26 = this;
  v1 = &v3;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(v26->m_nTolValue, 0, 0x10ui64);
  if ( !CPlayer::IsRidingUnit(v26) )
  {
    for ( j = 0; j < 5; ++j )
    {
      v5 = &v26->m_Param.m_dbEquip.m_pStorageList[j].m_bLoad;
      if ( *v5 )
      {
        v6 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + j, *(_WORD *)(v5 + 3));
        v7 = &v6[5].m_strCode[28];
        for ( k = 0; k < 4; ++k )
          v26->m_nTolValue[k] = (signed int)ffloor((float)v26->m_nTolValue[k] + *(float *)&v7[4 * k]);
      }
    }
    v9 = &v26->m_Param.m_dbEquip.m_pStorageList[7].m_bLoad;
    if ( *v9 )
    {
      v10 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 7, *(_WORD *)(v9 + 3));
      v11 = &v10[5].m_strCode[28];
      for ( l = 0; l < 4; ++l )
        v26->m_nTolValue[l] = (signed int)ffloor((float)v26->m_nTolValue[l] + *(float *)&v11[4 * l]);
    }
    v13 = &v26->m_Param.m_dbEquip.m_pStorageList[5].m_bLoad;
    if ( *v13 )
    {
      v14 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 5, *(_WORD *)(v13 + 3));
      v15 = &v14[5].m_strCode[28];
      for ( m = 0; m < 4; ++m )
        v26->m_nTolValue[m] = (signed int)ffloor((float)v26->m_nTolValue[m] + *(float *)&v15[4 * m]);
    }
    for ( j = 0; ; ++j )
    {
      if ( j >= 7 )
        return;
      v17 = &v26->m_Param.m_dbEmbellish.m_pStorageList[j].m_bLoad;
      if ( *v17 )
      {
        v18 = 0i64;
        if ( v17[1] == 8 )
        {
          v19 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 8, *(_WORD *)(v17 + 3));
          if ( !v19 )
            continue;
          v18 = &v19[4].m_strCode[52];
        }
        else
        {
          if ( v17[1] != 9 )
            continue;
          v20 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 9, *(_WORD *)(v17 + 3));
          if ( !v20 )
            continue;
          v18 = &v20[4].m_strCode[52];
        }
        if ( v18 )
        {
          for ( n = 0; n < 4; ++n )
            v26->m_nTolValue[n] = (signed int)ffloor((float)v26->m_nTolValue[n] + *(float *)&v18[4 * n]);
        }
      }
    }
  }
  for ( ii = 0; ii < 6; ++ii )
  {
    v23 = CRecordData::GetRecord(&stru_1799C86D0 + ii, v26->m_pUsingUnit->byPart[ii]);
    if ( v23 )
    {
      v24 = &v23[5].m_strCode[20];
      for ( jj = 0; jj < 4; ++jj )
        v26->m_nTolValue[jj] += *(_DWORD *)&v24[4 * jj];
    }
  }
}
