/*
 * Function: ?Restart@?$IteratedHashBase@IV?$SimpleKeyedTransformation@VHashTransformation@CryptoPP@@@CryptoPP@@@CryptoPP@@UEAAXXZ
 * Address: 0x140572140
 */

int __fastcall CryptoPP::IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::Restart(__int64 a1)
{
  *(_DWORD *)(a1 + 20) = 0;
  *(_DWORD *)(a1 + 16) = 0;
  return (*(int (**)(void))(*(_QWORD *)a1 + 144i64))();
}
