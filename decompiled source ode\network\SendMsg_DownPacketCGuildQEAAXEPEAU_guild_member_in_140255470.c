/*
 * Function: ?SendMsg_DownPacket@CGuild@@QEAAXEPEAU_guild_member_info@@@Z
 * Address: 0x140255470
 */

void __fastcall CGuild::SendMsg_DownPacket(CGuild *this, char bDowntype, _guild_member_info *pMem)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomSystem *v5; // rax@5
  CGuildRoomSystem *v6; // rax@5
  unsigned __int16 v7; // ax@5
  unsigned int v8; // ecx@5
  unsigned __int16 v9; // ax@7
  unsigned int v10; // ecx@7
  unsigned __int16 v11; // ax@10
  unsigned int v12; // ecx@10
  unsigned __int16 v13; // ax@10
  unsigned int v14; // ecx@10
  __int64 v15; // [sp+0h] [bp-E8h]@1
  unsigned __int16 nLen; // [sp+20h] [bp-C8h]@5
  char v17; // [sp+30h] [bp-B8h]@5
  int tt; // [sp+44h] [bp-A4h]@5
  char pbyType; // [sp+64h] [bp-84h]@5
  char v20; // [sp+65h] [bp-83h]@5
  char v21; // [sp+84h] [bp-64h]@7
  char v22; // [sp+85h] [bp-63h]@7
  char v23; // [sp+A4h] [bp-44h]@10
  char v24; // [sp+A5h] [bp-43h]@10
  char v25; // [sp+C4h] [bp-24h]@10
  char v26; // [sp+C5h] [bp-23h]@10
  CGuild *v27; // [sp+F0h] [bp+8h]@1
  _guild_member_info *pMema; // [sp+100h] [bp+18h]@1

  pMema = pMem;
  v27 = this;
  v3 = &v15;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pMem->pPlayer )
  {
    v27->m_DownPacket_Member->byDownType = bDowntype;
    v17 = 2;
    tt = 0;
    v5 = CGuildRoomSystem::GetInstance();
    v17 = CGuildRoomSystem::GetRoomType(v5, v27->m_dwSerial);
    v6 = CGuildRoomSystem::GetInstance();
    CGuildRoomSystem::GetRestTime(v6, v27->m_dwSerial, &tt);
    v27->m_DownPacket_Member->byGuildRoomType = v17;
    v27->m_DownPacket_Member->GuildRoomRestTime = tt;
    pbyType = 27;
    v20 = 3;
    v7 = _guild_member_download_zocl::size(v27->m_DownPacket_Member);
    v8 = pMema->pPlayer->m_ObjID.m_wIndex;
    nLen = v7;
    CNetProcess::LoadSendMsg(unk_1414F2088, v8, &pbyType, &v27->m_DownPacket_Member->byDownType, v7);
    if ( pMema->byClassInGuild == 1 || pMema->byClassInGuild == 2 )
    {
      v21 = 27;
      v22 = 4;
      v9 = _guild_applier_download_zocl::size(v27->m_DownPacket_Applier);
      v10 = pMema->pPlayer->m_ObjID.m_wIndex;
      nLen = v9;
      CNetProcess::LoadSendMsg(unk_1414F2088, v10, &v21, (char *)v27->m_DownPacket_Applier, v9);
    }
    if ( v27->m_bNowProcessSgtMter )
      CGuild::SendMsg_VoteProcessInform_Continue(v27, pMema);
    v23 = 27;
    v24 = 41;
    v11 = _guild_money_io_download_zocl::size(v27->m_MoneyIO_List);
    v12 = pMema->pPlayer->m_ObjID.m_wIndex;
    nLen = v11;
    CNetProcess::LoadSendMsg(unk_1414F2088, v12, &v23, (char *)v27->m_MoneyIO_List, v11);
    CGuild::MakeBuddyPacket(v27);
    v25 = 27;
    v26 = 43;
    v13 = _guild_member_buddy_download_zocl::size(v27->m_Buddy_List);
    v14 = pMema->pPlayer->m_ObjID.m_wIndex;
    nLen = v13;
    CNetProcess::LoadSendMsg(unk_1414F2088, v14, &v25, (char *)v27->m_Buddy_List, v13);
  }
}
