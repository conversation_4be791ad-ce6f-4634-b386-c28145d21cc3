/*
 * Function: ?Update_DelBuddy@CUserDB@@QEAA_NE@Z
 * Address: 0x140116420
 */

char __fastcall CUserDB::Update_DelBuddy(CUserDB *this, char bySlotIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUserDB *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _BUDDY_DB_BASE::_LIST::Init((_BUDDY_DB_BASE::_LIST *)&v6->m_AvatorData.dbBuddy + (unsigned __int8)bySlotIndex);
  v6->m_bDataUpdate = 1;
  return 1;
}
