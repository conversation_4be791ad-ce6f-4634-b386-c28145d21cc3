/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAVCUnmannedTraderSortType@@_KPEAV1@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@stdext@@YAXPEAPEAVCUnmannedTraderSortType@@_KAEBQEAV1@AEAV?$allocator@PEAVCUnmannedTraderSortType@@@std@@@Z
 * Address: 0x14001145F
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CUnmannedTraderSortType * *,unsigned __int64,CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(CUnmannedTraderSortType **_First, unsigned __int64 _Count, CUnmannedTraderSortType *const *_Val, std::allocator<CUnmannedTraderSortType *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderSortType * *,unsigned __int64,CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(
    _First,
    _Count,
    _Val,
    _Al);
}
