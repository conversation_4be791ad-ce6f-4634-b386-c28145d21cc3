/*
 * Function: ?SendMsg_MonsterAggroData@CPlayer@@QEAAXPEAVCCharacter@@@Z
 * Address: 0x1400E5270
 */

void __fastcall CPlayer::SendMsg_MonsterAggroData(CPlayer *this, CCharacter *pCharacter)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@10
  __int64 v5; // [sp+0h] [bp-288h]@1
  CCharacter *v6; // [sp+30h] [bp-258h]@5
  _target_monster_aggro_inform_zocl v7; // [sp+50h] [bp-238h]@5
  int j; // [sp+224h] [bp-64h]@5
  char *v9; // [sp+228h] [bp-60h]@7
  CAggroNode *v10; // [sp+230h] [bp-58h]@7
  CCharacter *v11; // [sp+238h] [bp-50h]@8
  CCharacter *v12; // [sp+240h] [bp-48h]@10
  CCharacter *v13; // [sp+248h] [bp-40h]@12
  char pbyType; // [sp+254h] [bp-34h]@15
  char v15; // [sp+255h] [bp-33h]@15
  unsigned __int64 v16; // [sp+270h] [bp-18h]@4
  CPlayer *v17; // [sp+290h] [bp+8h]@1

  v17 = this;
  v2 = &v5;
  for ( i = 160i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v16 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( pCharacter )
  {
    v6 = pCharacter;
    _target_monster_aggro_inform_zocl::_target_monster_aggro_inform_zocl(&v7);
    v7.dwSerial = v6->m_dwObjSerial;
    v7.fTimer = (float)(signed int)GetLoopTime() - (float)(signed int)v6[2].m_dwObjSerial;
    for ( j = 0; j < 10; ++j )
    {
      v9 = &v7.m_AggroNode[j].m_IsData;
      v10 = (CAggroNode *)(&v6[1].m_SFContAura[1][5].m_byLv + 24 * j);
      if ( CAggroNode::IsLive(v10) )
      {
        v11 = v10->m_pCharacter;
        if ( !v11->m_ObjID.m_byKind )
        {
          if ( v11->m_ObjID.m_byID )
          {
            if ( v11->m_ObjID.m_byID == 3 )
            {
              v13 = v11;
              sprintf(v9 + 1, "[AniMaster:%s]", (char *)&v11[1].m_fAbsPos[0] + 1);
            }
          }
          else
          {
            v12 = v11;
            v4 = CPlayerDB::GetCharNameA((CPlayerDB *)&v11[1].m_fOldPos[2]);
            sprintf(v9 + 1, "%s", v4);
          }
        }
        *v9 = 1;
        *(_DWORD *)(v9 + 33) = v10->m_nAggroData;
        *(_DWORD *)(v9 + 37) = v10->m_nDamageData;
        *(_DWORD *)(v9 + 41) = v10->m_nKingPowerDamage;
      }
    }
    pbyType = 13;
    v15 = 99;
    CNetProcess::LoadSendMsg(unk_1414F2088, v17->m_ObjID.m_wIndex, &pbyType, (char *)&v7, 0x1CAu);
  }
}
