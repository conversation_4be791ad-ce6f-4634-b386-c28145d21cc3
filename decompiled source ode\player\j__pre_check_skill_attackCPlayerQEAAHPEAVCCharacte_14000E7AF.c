/*
 * Function: j_?_pre_check_skill_attack@CPlayer@@QEAAHP<PERSON>VC<PERSON>hara<PERSON>@@PEAMEPEAU_skill_fld@@GPEAPEAU_db_con@_STORAGE_LIST@@PEAPEAU_BulletItem_fld@@HPEAGG34@Z
 * Address: 0x14000E7AF
 */

int __fastcall CPlayer::_pre_check_skill_attack(CPlayer *this, CCharacter *pDst, float *pfAttackPos, char byEffectCode, _skill_fld *pSkillFld, unsigned __int16 wBulletSerial, _STORAGE_LIST::_db_con **ppBulletProp, _BulletItem_fld **ppfldBullet, int nEffectGroup, unsigned __int16 *pdwDecPoint, unsigned __int16 wEffBtSerial, _STORAGE_LIST::_db_con **ppEffBtProp, _BulletItem_fld **ppfldEffBt)
{
  return CPlayer::_pre_check_skill_attack(
           this,
           pDst,
           pfAttackPos,
           byEffectC<PERSON>,
           pSkillFld,
           wBulletSerial,
           ppBulletProp,
           ppfldBullet,
           nEffectGroup,
           pdwDecPoint,
           wEffBtSerial,
           ppEffBtProp,
           ppfldEffBt);
}
