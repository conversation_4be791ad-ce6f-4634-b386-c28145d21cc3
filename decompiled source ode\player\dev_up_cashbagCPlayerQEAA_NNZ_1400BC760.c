/*
 * Function: ?dev_up_cashbag@CPlayer@@QEAA_NN@Z
 * Address: 0x1400BC760
 */

char __fastcall CPlayer::dev_up_cashbag(CPlayer *this, long double dPoint)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp-28h] [bp-28h]@1
  CPlayer *v6; // [sp+8h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( ((int (__fastcall *)(CPlayer *))v6->vfptr->GetLevel)(v6) >= 39 )
  {
    if ( v6->m_Param.m_pClassData->m_nGrade >= 1 )
    {
      CPlayer::AlterPvPCashBag(v6, dPoint, 0);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
