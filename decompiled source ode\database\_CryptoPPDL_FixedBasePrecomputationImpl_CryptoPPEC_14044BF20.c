/*
 * Function: _CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint_::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__::_1_::dtor$1
 * Address: 0x14044BF20
 */

void __fastcall CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint_::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__::_1_::dtor_1(__int64 a1, __int64 a2)
{
  CryptoPP::Integer::~Integer((CryptoPP::Integer *)(*(_QWORD *)(a2 + 64) + 104i64));
}
