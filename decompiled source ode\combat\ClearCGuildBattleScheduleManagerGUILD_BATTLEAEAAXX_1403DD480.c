/*
 * Function: ?Clear@CGuildBattleScheduleManager@GUILD_BATTLE@@AEAAXXZ
 * Address: 0x1403DD480
 */

void __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::Clear(GUILD_BATTLE::CGuildBattleScheduleManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v3; // rax@4
  CGuildBattleController *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int v6; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CGuildBattleScheduleManager *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = ATL::CTime::GetDay(v7->m_pkOldDayTime);
  v3 = GUILD_BATTLE::CGuildBattleLogger::Instance();
  GUILD_BATTLE::CGuildBattleLogger::Log(v3, "CGuildBattleScheduleManager::Clear() : %d Day!", v6);
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Clear(v7->m_pkTodaySchedule);
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Clear(v7->m_pkTomorrowSchedule);
  v4 = CGuildBattleController::Instance();
  CGuildBattleController::Clear(v4);
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::PushDQSClear(v7->m_pkTodaySchedule);
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::PushDQSClear(v7->m_pkTomorrowSchedule);
}
