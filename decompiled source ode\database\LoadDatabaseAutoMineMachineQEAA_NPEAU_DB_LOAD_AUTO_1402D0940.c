/*
 * Function: ?LoadDatabase@AutoMineMachine@@QEAA_NPEAU_DB_LOAD_AUTOMINE_MACHINE@@@Z
 * Address: 0x1402D0940
 */

char __fastcall AutoMineMachine::LoadDatabase(AutoMineMachine *this, _DB_LOAD_AUTOMINE_MACHINE *pdata)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@5
  unsigned int v5; // ecx@5
  char result; // al@5
  int v7; // eax@10
  int v8; // ecx@10
  int v9; // edx@10
  unsigned int v10; // er8@10
  int v11; // eax@13
  int v12; // ecx@13
  int v13; // edx@13
  char *v14; // rax@14
  int v15; // ecx@14
  __int64 v16; // [sp+0h] [bp-78h]@1
  int nNum; // [sp+20h] [bp-58h]@10
  char *v18; // [sp+28h] [bp-50h]@10
  int v19; // [sp+30h] [bp-48h]@10
  int v20; // [sp+38h] [bp-40h]@10
  int j; // [sp+40h] [bp-38h]@10
  int nP; // [sp+44h] [bp-34h]@12
  int nS; // [sp+48h] [bp-30h]@12
  int v24; // [sp+4Ch] [bp-2Ch]@12
  CLogFile *v25; // [sp+50h] [bp-28h]@5
  CLogFile *v26; // [sp+58h] [bp-20h]@10
  CLogFile *v27; // [sp+60h] [bp-18h]@13
  CLogFile *v28; // [sp+68h] [bp-10h]@14
  AutoMineMachine *v29; // [sp+80h] [bp+8h]@1
  _DB_LOAD_AUTOMINE_MACHINE *v30; // [sp+88h] [bp+10h]@1

  v30 = pdata;
  v29 = this;
  v2 = &v16;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v29->m_byRace == pdata->byRace )
  {
    v29->m_pOwnerGuild = GetGuildDataFromSerial(g_Guild, 500, pdata->dwGuildSerial);
    v29->m_bRunning = v30->bWorking;
    v29->m_bySelectedOre = v30->bySelectedOre;
    v29->m_dwMiningTime = timeGetTime();
    if ( v29->m_byCollisionType )
    {
      if ( v29->m_byCollisionType == 1 )
        v29->m_dwMiningTerm = 60000;
    }
    else
    {
      v29->m_dwMiningTerm = 30000;
    }
    v29->m_Battery.m_nCurGage = v30->dwBatteryGage;
    v29->m_Battery.m_nMaxGage = 86400;
    v7 = v30->bySlotCnt;
    v8 = v29->m_bySelectedOre;
    v9 = v29->m_byRace;
    v10 = v29->m_bRunning;
    v26 = &v29->m_Log;
    v20 = v7;
    v19 = v29->m_Battery.m_nCurGage;
    LODWORD(v18) = v8;
    nNum = v9;
    CLogFile::Write(
      &v29->m_Log,
      "[Load DB]GuildSerial:%d Run:%d, Race:%d, Ore:%d, Gage:%d, SlotCnt:%d",
      v30->dwGuildSerial,
      v10);
    for ( j = 0; j < v30->bySlotCnt; ++j )
    {
      nP = v30->slot[j].nLumpIndex;
      nS = v30->slot[j].item.bySlotIndex;
      v24 = v30->slot[j].nOverlapNum;
      if ( TInventory<_INVENKEY>::push(&v29->m_Inven, nP, nS, &v30->slot[j].item, v24) )
      {
        v11 = v30->slot[j].item.wItemIndex;
        v12 = v30->slot[j].item.byTableCode;
        v13 = v30->slot[j].item.bySlotIndex;
        v27 = &v29->m_sysLog;
        v20 = v24;
        v19 = v11;
        LODWORD(v18) = v12;
        nNum = v13;
        CLogFile::Write(
          &v29->m_sysLog,
          "[ERR-Load DB]::m_Inven.push(%d,%d,[%d/%d/%d],%d)",
          (unsigned int)nP,
          (unsigned int)nS);
        return 0;
      }
      v14 = GetItemKorName(v30->slot[j].item.byTableCode, v30->slot[j].item.wItemIndex);
      v15 = v30->slot[j].item.bySlotIndex;
      v28 = &v29->m_Log;
      v19 = v24;
      v18 = v14;
      nNum = v15;
      CLogFile::Write(
        &v29->m_Log,
        "[Load DB]Page:%d,Slot:%d [ORE Pos:%d,name_%s, num_%d]",
        (unsigned int)nP,
        (unsigned int)nS);
    }
    result = 1;
  }
  else
  {
    v4 = pdata->byRace;
    v5 = v29->m_byRace;
    v25 = &v29->m_sysLog;
    CLogFile::Write(&v29->m_sysLog, "[ERR-Load DB]::Invalid value of race code.(%d-%d)", v5, v4);
    result = 0;
  }
  return result;
}
