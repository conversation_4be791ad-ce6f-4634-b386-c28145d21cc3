/*
 * Function: j_??$_Ptr_cat@PEAPEAVCUnmannedTraderSortType@@PEAPEAV1@@std@@YA?AU_Scalar_ptr_iterator_tag@0@AEAPEAPEAVCUnmannedTraderSortType@@0@Z
 * Address: 0x140012EF9
 */

std::_Scalar_ptr_iterator_tag __fastcall std::_Ptr_cat<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *>(CUnmannedTraderSortType ***__formal, CUnmannedTraderSortType ***a2)
{
  return std::_Ptr_cat<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *>(__formal, a2);
}
