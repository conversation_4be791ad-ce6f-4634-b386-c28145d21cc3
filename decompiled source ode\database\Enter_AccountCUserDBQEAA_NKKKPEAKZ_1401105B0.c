/*
 * Function: ?Enter_Account@CUserDB@@QEAA_NKKKPEAK@Z
 * Address: 0x1401105B0
 */

char __fastcall CUserDB::Enter_Account(CUserDB *this, unsigned int dwAccountSerial, unsigned int dwIP, unsigned int dwProtocolVer, unsigned int *pdwMasterKey)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v8; // ax@36
  unsigned __int16 v9; // ax@53
  unsigned __int16 v10; // ax@54
  __int64 v11; // [sp+0h] [bp-158h]@1
  unsigned int nLen[2]; // [sp+20h] [bp-138h]@54
  bool bFirst; // [sp+28h] [bp-130h]@54
  char *pszFileName; // [sp+30h] [bp-128h]@54
  int v15; // [sp+40h] [bp-118h]@4
  _WAIT_ENTER_ACCOUNT *v16; // [sp+48h] [bp-110h]@4
  int j; // [sp+50h] [bp-108h]@8
  _WAIT_ENTER_ACCOUNT *v18; // [sp+58h] [bp-100h]@11
  char v19; // [sp+60h] [bp-F8h]@13
  int k; // [sp+64h] [bp-F4h]@13
  char v21; // [sp+68h] [bp-F0h]@23
  CUserDB *v22; // [sp+70h] [bp-E8h]@31
  char Dst; // [sp+88h] [bp-D0h]@36
  char pbyType; // [sp+A4h] [bp-B4h]@36
  char v25; // [sp+A5h] [bp-B3h]@36
  char v26; // [sp+B4h] [bp-A4h]@40
  int l; // [sp+B8h] [bp-A0h]@47
  _enter_world_request_wrac v28; // [sp+C8h] [bp-90h]@53
  char v29; // [sp+F4h] [bp-64h]@53
  char v30; // [sp+F5h] [bp-63h]@53
  _enter_world_result_zone v31; // [sp+114h] [bp-44h]@54
  char v32; // [sp+134h] [bp-24h]@54
  char v33; // [sp+135h] [bp-23h]@54
  CUserDB *v34; // [sp+160h] [bp+8h]@1
  unsigned int v35; // [sp+168h] [bp+10h]@1
  unsigned int v36; // [sp+170h] [bp+18h]@1
  unsigned int v37; // [sp+178h] [bp+20h]@1

  v37 = dwProtocolVer;
  v36 = dwIP;
  v35 = dwAccountSerial;
  v34 = this;
  v5 = &v11;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v5 = -*********;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v15 = 0;
  v16 = 0i64;
  if ( v34->m_bActive )
  {
    CLogFile::Write(&stru_1799C8E78, "%d.. Enter_Account : m_bActive == true", dwAccountSerial);
    return 0;
  }
  if ( !_SYNC_STATE::chk_enter(&v34->m_ss) )
  {
    CLogFile::Write(&stru_1799C8E78, "%d.. Enter_Account : if(!m_ss.chk_enter())", v35);
    return 0;
  }
  for ( j = 0; j < 2532; ++j )
  {
    v18 = (_WAIT_ENTER_ACCOUNT *)((char *)&unk_14154F25C + 168 * j);
    if ( v18->m_bLoad && v18->m_dwAccountSerial == v35 )
    {
      v19 = 1;
      for ( k = 0; k < 4; ++k )
      {
        if ( v18->m_dwKey[k] != pdwMasterKey[k] )
        {
          v19 = 0;
          break;
        }
      }
      if ( v19 )
      {
        _WAIT_ENTER_ACCOUNT::Release(v18);
        v16 = v18;
        break;
      }
    }
  }
  if ( !v16 )
  {
    CLogFile::Write(&stru_1799C8E78, "%d.. Enter_Account : pWaitData == NULL", v35);
    return 0;
  }
  v21 = 1;
  if ( !v16->m_byUserDgr && v37 != 126455 )
  {
    CLogFile::Write(&stru_1799C8E78, "%s.. Enter_Account : if(dwProtocolVer != __PROTOCOL_VER)", v16->m_szAccountID);
    v21 = 0;
  }
  if ( *(&g_Player.m_bLoad + 50856 * v34->m_idWorld.wIndex) )
  {
    CLogFile::Write(&stru_1799C8E78, "%s.. Enter_Account : if(g_Player[m_idWorld.wIndex].m_bLoad)", v16->m_szAccountID);
    v21 = 0;
  }
  for ( j = 0; j < 2532; ++j )
  {
    v22 = &g_UserDB[j];
    if ( v22->m_bActive )
    {
      if ( v35 == v22->m_dwAccountSerial )
      {
        CLogFile::Write(
          &stru_1799C8E78,
          "%s.. Enter_Account : if(dwAccountSerial == p->m_dwAccountSerial)",
          v16->m_szAccountID);
        v21 = 0;
      }
    }
  }
  if ( !v21 )
  {
    memcpy_0(&Dst, &v34->m_gidGlobal, 8ui64);
    pbyType = 1;
    v25 = 5;
    v8 = _logout_account_request_wrac::size((_logout_account_request_wrac *)&Dst);
    CNetProcess::LoadSendMsg(unk_1414F2090, 0, &pbyType, &Dst, v8);
    return 0;
  }
  v34->m_bActive = 1;
  v34->m_dwOperLobbyTime = timeGetTime();
  v34->m_dwAccountSerial = v35;
  strcpy_0(v34->m_szAccountID, v16->m_szAccountID);
  v34->m_byUserDgr = v16->m_byUserDgr;
  v34->m_bySubDgr = v16->m_bySubDgr;
  v34->m_dwIP = v36;
  memcpy_0(&v34->m_gidGlobal, &v16->m_gidGlobal, 8ui64);
  v34->m_dwTotalPlayMin = 0;
  v34->m_dwSerial = -1;
  v34->m_bDBWaitState = 0;
  v34->m_pDBPushData = 0i64;
  v34->m_bChatLock = v16->m_bChatLock;
  v34->m_bWndFullMode = 0;
  v34->m_bDataUpdate = 0;
  v34->m_nTrans = v16->m_nTrans;
  for ( j = 0; j < 50; ++j )
  {
    v34->m_NotArrangedChar[j].dwSerial = -1;
    v34->m_dwArrangePassCase0[j] = -1;
  }
  v26 = 0;
  CUserDB::ClearBillingData(v34);
  CUserDB::SetBillingData(v34, &v16->m_BillingInfo);
  if ( v34->m_byUserDgr )
  {
    v34->m_byUILock = 2;
  }
  else
  {
    if ( !v34->m_BillingInfo.iType )
    {
      CLogFile::Write(&stru_1799C8E78, "%s.. Enter_Account Billing Type = 0", v16->m_szAccountID);
      return 0;
    }
    if ( !v34->m_BillingInfo.lRemainTime && (v34->m_BillingInfo.iType == 5 || v34->m_BillingInfo.iType == 2) )
    {
      CLogFile::Write(&stru_1799C8E78, "%s.. Enter_Account Billing lRemainTime = 0", v16->m_szAccountID);
      return 0;
    }
    v34->m_byUILock = v16->m_byUILock;
    v34->m_byUILock_InitFailCnt = v16->m_byUILock_FailCnt;
    v34->m_byUILock_FailCnt = v34->m_byUILock_InitFailCnt;
    v34->m_byUILock_HintIndex = v16->m_byUILock_HintIndex;
    strcpy_s(v34->m_szUILock_PW, 0xDui64, v16->m_szUILock_PW);
    strcpy_s(v34->m_szAccount_PW, 0xDui64, v16->m_szAccount_PW);
    strcpy_s(v34->m_uszUILock_HintAnswer, 0x11ui64, v16->m_uszUILock_HintAnswer);
    v34->m_byUILock_InitFindPassFailCount = v16->m_byUILockFindPassFailCount;
    v34->m_byUILockFindPassFailCount = v34->m_byUILock_InitFindPassFailCount;
    for ( l = 0; l < 3; ++l )
    {
      v34->m_dwRequestMoveCharacterSerialList[l] = v16->m_dwRequestMoveCharacterSerialList[l];
      v34->m_dwTournamentCharacterSerialList[l] = v16->m_dwTournamentCharacterSerialList[l];
    }
  }
  if ( !v26 )
  {
    memcpy_0(&v28, &v34->m_gidGlobal, 8ui64);
    memcpy_0(&v28.idLocal, &v34->m_idWorld, 6ui64);
    v28.ulConnectIP = v34->m_ipAddress;
    v29 = 1;
    v30 = 10;
    v9 = _enter_world_request_wrac::size(&v28);
    CNetProcess::LoadSendMsg(unk_1414F2090, 0, &v29, (char *)&v28, v9);
  }
  CMgrAccountLobbyHistory::GetNewFileName(
    &CUserDB::s_MgrLobbyHistory,
    v34->m_dwAccountSerial,
    v34->m_szLobbyHistoryFileName);
  pszFileName = v34->m_szLobbyHistoryFileName;
  bFirst = 1;
  nLen[0] = v34->m_dwIP;
  CMgrAccountLobbyHistory::enter_lobby(
    &CUserDB::s_MgrLobbyHistory,
    v34->m_dwAccountSerial,
    v34->m_szAccountID,
    v34->m_byUserDgr,
    nLen[0],
    1,
    v34->m_szLobbyHistoryFileName);
  v31.byResult = v26;
  v31.byUserGrade = v34->m_byUserDgr;
  v31.bySvrType = unk_1799C9AE9;
  v32 = 1;
  v33 = 2;
  v10 = _enter_world_result_zone::size(&v31);
  CNetProcess::LoadSendMsg(unk_1414F2088, v34->m_idWorld.wIndex, &v32, &v31.byResult, v10);
  return 1;
}
