/*
 * Function: j_??0?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEAA@XZ
 * Address: 0x1400040CA
 */

void __fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this)
{
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(this);
}
