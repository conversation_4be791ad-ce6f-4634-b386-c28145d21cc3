/*
 * Function: ?_LootItem_Qst@CMonster@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x140145390
 */

char __fastcall CMonster::_LootItem_Qst(CMonster *this, CPlayer *pOwner)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-E8h]@1
  float *pStdPos; // [sp+20h] [bp-C8h]@17
  bool bHide; // [sp+28h] [bp-C0h]@17
  CPlayer *pAttacker; // [sp+30h] [bp-B8h]@17
  int bHolyScanner; // [sp+38h] [bp-B0h]@17
  char byEventItemLootAuth; // [sp+40h] [bp-A8h]@17
  char v11; // [sp+50h] [bp-98h]@4
  int j; // [sp+54h] [bp-94h]@4
  char *v13; // [sp+58h] [bp-90h]@7
  unsigned __int64 dwExp; // [sp+60h] [bp-88h]@7
  char v15; // [sp+68h] [bp-80h]@7
  char v16; // [sp+69h] [bp-7Fh]@8
  char v17; // [sp+6Ah] [bp-7Eh]@8
  _STORAGE_LIST::_db_con pItem; // [sp+78h] [bp-70h]@14
  _TimeItem_fld *v19; // [sp+B8h] [bp-30h]@14
  __time32_t Time; // [sp+C4h] [bp-24h]@16
  CMonster *v21; // [sp+F0h] [bp+8h]@1

  v21 = this;
  v2 = &v5;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = 0;
  for ( j = 0; j < v21->m_nEventItemNum; ++j )
  {
    v13 = &v21->m_eventItem[j].byItemTable;
    dwExp = __PAIR__(0xFFFFFFF, GetItemDurPoint((unsigned __int8)*v13, v21->m_eventItem[j].wItemIndex));
    v15 = GetItemKindCode((unsigned __int8)*v13);
    if ( v15 )
    {
      if ( v15 != 1 )
        continue;
      HIDWORD(dwExp) = GetMaxParamFromExp(*((_WORD *)v13 + 1), (unsigned int)dwExp);
    }
    else
    {
      v16 = GetDefItemUpgSocketNum((unsigned __int8)*v13, *((_WORD *)v13 + 1));
      v17 = 0;
      if ( (signed int)(unsigned __int8)v16 > 0 )
        v17 = rand() % (unsigned __int8)v16 + 1;
      HIDWORD(dwExp) = GetBitAfterSetLimSocket(v17);
    }
    _STORAGE_LIST::_db_con::_db_con(&pItem);
    pItem.m_byTableCode = *v13;
    pItem.m_wItemIndex = *((_WORD *)v13 + 1);
    pItem.m_dwDur = (unsigned int)dwExp;
    pItem.m_dwLv = HIDWORD(dwExp);
    v19 = TimeItem::FindTimeRec((unsigned __int8)*v13, *((_WORD *)v13 + 1));
    if ( v19 && v19->m_nCheckType )
    {
      _time32(&Time);
      pItem.m_byCsMethod = v19->m_nCheckType;
      pItem.m_dwT = v19->m_nUseTime + Time;
      pItem.m_dwLendRegdTime = Time;
    }
    byEventItemLootAuth = 3;
    bHolyScanner = 0;
    pAttacker = 0i64;
    bHide = 0;
    pStdPos = v21->m_fCurPos;
    if ( CreateItemBox(&pItem, 5, v21->m_pCurMap, v21->m_wMapLayerIndex, v21->m_fCurPos, 0, 0i64, 0, 3) )
      v11 = 1;
  }
  return v11;
}
