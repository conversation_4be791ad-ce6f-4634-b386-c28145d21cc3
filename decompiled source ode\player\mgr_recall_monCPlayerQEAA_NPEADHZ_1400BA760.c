/*
 * Function: ?mgr_recall_mon@CPlayer@@QEAA_NPEADH@Z
 * Address: 0x1400BA760
 */

char __fastcall CPlayer::mgr_recall_mon(CPlayer *this, char *pszMonCode, int nCreateNum)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-68h]@1
  CMonster *pParent; // [sp+20h] [bp-48h]@10
  bool bRobExp; // [sp+28h] [bp-40h]@10
  bool bRewardExp; // [sp+30h] [bp-38h]@10
  bool bDungeon; // [sp+38h] [bp-30h]@10
  bool bWithoutFail; // [sp+40h] [bp-28h]@10
  bool bApplyRopExpField; // [sp+48h] [bp-20h]@10
  int v13; // [sp+50h] [bp-18h]@8
  int j; // [sp+54h] [bp-14h]@8
  CPlayer *v15; // [sp+70h] [bp+8h]@1
  char *pszMonsterCode; // [sp+78h] [bp+10h]@1
  int v17; // [sp+80h] [bp+18h]@1

  v17 = nCreateNum;
  pszMonsterCode = pszMonCode;
  v15 = this;
  v3 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v15->m_pCurMap->m_pMapSet->m_nMapType == 1 )
  {
    result = 0;
  }
  else
  {
    if ( nCreateNum > 100 )
      v17 = 100;
    v13 = 0;
    for ( j = 0; j < v17; ++j )
    {
      bApplyRopExpField = 1;
      bWithoutFail = 0;
      bDungeon = 0;
      bRewardExp = 1;
      bRobExp = 0;
      pParent = 0i64;
      if ( !CreateRepMonster(v15->m_pCurMap, v15->m_wMapLayerIndex, v15->m_fCurPos, pszMonsterCode, 0i64, 0, 1, 0, 0, 1) )
        break;
      ++v13;
    }
    result = 1;
  }
  return result;
}
