/*
 * Function: ?Get@CMoveMapLimitRightInfoList@@QEAAPEAVCMoveMapLimitRightInfo@@H@Z
 * Address: 0x1403A1FE0
 */

CMoveMapLimitRightInfo *__fastcall CMoveMapLimitRightInfoList::Get(CMoveMapLimitRightInfoList *this, int iInx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CMoveMapLimitRightInfo *result; // rax@6
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMoveMapLimitRightInfoList *v6; // [sp+30h] [bp+8h]@1
  int v7; // [sp+38h] [bp+10h]@1

  v7 = iInx;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( iInx >= 0
    && std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::size(&v6->m_vecRight) > iInx )
  {
    result = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::operator[](&v6->m_vecRight, v7);
  }
  else
  {
    result = 0i64;
  }
  return result;
}
