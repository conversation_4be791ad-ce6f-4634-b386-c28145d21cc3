/*
 * Function: ?dev_drop_item@CPlayer@@QEAA_NPEADH0H@Z
 * Address: 0x1400C0630
 */

char __usercall CPlayer::dev_drop_item@<al>(CPlayer *this@<rcx>, char *pszItemCode@<rdx>, int nNum@<r8d>, char *pszUpTalCode@<r9>, signed __int64 a5@<rax>, int nUpNum)
{
  void *v6; // rsp@1
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char v10; // al@10
  __int64 v11; // [sp-20h] [bp-13F8h]@1
  char __t[5012]; // [sp+20h] [bp-13B8h]@4
  char v13; // [sp+13B4h] [bp-24h]@4
  int v14; // [sp+13B8h] [bp-20h]@4
  int j; // [sp+13BCh] [bp-1Ch]@8
  void *Src; // [sp+13C0h] [bp-18h]@11
  _base_fld *v17; // [sp+13C8h] [bp-10h]@13
  CPlayer *v18; // [sp+13E0h] [bp+8h]@1
  char *psItemCode; // [sp+13E8h] [bp+10h]@1
  int v20; // [sp+13F0h] [bp+18h]@1
  char *v21; // [sp+13F8h] [bp+20h]@1

  v21 = pszUpTalCode;
  v20 = nNum;
  psItemCode = pszItemCode;
  v18 = this;
  v6 = alloca(a5);
  v7 = &v11;
  for ( i = 1276i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  `vector constructor iterator'(__t, 0x32ui64, 100, (void *(__cdecl *)(void *))_STORAGE_LIST::_db_con::_db_con);
  v13 = 0;
  v14 = GetItemTableCode(psItemCode);
  if ( v14 == -1 )
  {
    result = 0;
  }
  else if ( v14 == 18 )
  {
    for ( j = 0; ; ++j )
    {
      v10 = CPlayerDB::GetBagNum(&v18->m_Param);
      if ( j >= 20 * (unsigned __int8)v10 )
        break;
      Src = &v18->m_Param.m_dbInven.m_pStorageList[j];
      if ( *(_BYTE *)Src )
      {
        if ( *((_BYTE *)Src + 1) == 18 )
        {
          v17 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + *((_BYTE *)Src + 1), *(_WORD *)((char *)Src + 3));
          if ( v17 )
          {
            if ( !strcmp_0(v17->m_strCode, psItemCode) )
            {
              CPlayer::SendMsg_DeleteStorageInform(v18, 0, *(_WORD *)((char *)Src + 17));
              CPlayer::Emb_DelStorage(v18, 0, j, 0, 1, "CPlayer::dev_inven_empty()");
            }
            memcpy_0(&__t[50 * (unsigned __int8)v13++], Src, 0x32ui64);
          }
        }
      }
    }
    CMgrAvatorItemHistory::cheat_del_item(
      &CPlayer::s_MgrItemHistory,
      v18->m_ObjID.m_wIndex,
      (_STORAGE_LIST::_db_con *)__t,
      v13,
      v18->m_szItemHistoryFileName);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
