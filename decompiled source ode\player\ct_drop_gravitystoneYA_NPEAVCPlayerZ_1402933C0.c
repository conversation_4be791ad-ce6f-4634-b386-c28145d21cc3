/*
 * Function: ?ct_drop_gravitystone@@YA_NPEAVCPlayer@@@Z
 * Address: 0x1402933C0
 */

bool __fastcall ct_drop_gravitystone(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  CGuildBattleController *v4; // rax@8
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *pkPlayer; // [sp+30h] [bp+8h]@1

  pkPlayer = pOne;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( pkPlayer )
  {
    if ( s_nWordCount )
    {
      result = 0;
    }
    else
    {
      v4 = CGuildBattleController::Instance();
      result = CGuildBattleController::CheatDropStone(v4, pkPlayer);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
