/*
 * Function: ?Set_Box_Count@CGoldenBoxItemMgr@@QEAAXE@Z
 * Address: 0x140413F10
 */

void __fastcall CGoldenBoxItemMgr::Set_Box_Count(CGoldenBoxItemMgr *this, char byIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CGoldenBoxItemMgr *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v5->m_golden_box_item.m_wBoxMax[(unsigned __int8)byIndex] )
  {
    --v5->m_golden_box_item.m_wBoxMax[(unsigned __int8)byIndex];
    CGoldenBoxItemMgr::Set_ToStruct(v5);
  }
}
