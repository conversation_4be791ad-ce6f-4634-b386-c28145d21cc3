/*
 * Function: _std::vector_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo___::_Insert_n_::_1_::catch$0
 * Address: 0x140368A90
 */

void __fastcall __noreturn std::vector_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo___::_Insert_n_::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1

  v2 = a2;
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Destroy(
    *(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > **)(a2 + 272),
    *(CUnmannedTraderUserInfo **)(a2 + 176),
    *(CUnmannedTraderUserInfo **)(a2 + 184));
  std::allocator<CUnmannedTraderUserInfo>::deallocate(
    (std::allocator<CUnmannedTraderUserInfo> *)(*(_QWORD *)(v2 + 272) + 8i64),
    *(CUnmannedTraderUserInfo **)(v2 + 176),
    *(_QWORD *)(v2 + 168));
  CxxThrowException_0(0i64, 0i64);
}
