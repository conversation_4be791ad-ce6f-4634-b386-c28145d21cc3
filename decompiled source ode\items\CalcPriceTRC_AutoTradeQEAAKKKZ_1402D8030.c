/*
 * Function: ?Calc<PERSON><PERSON>@TRC_AutoTrade@@QEAAKKK@Z
 * Address: 0x1402D8030
 */

unsigned int __fastcall TRC_AutoTrade::CalcPrice(TRC_AutoTrade *this, unsigned int nGuildSerial, unsigned int nPrice)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  TRC_AutoTrade *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return ControllerTaxRate::calcTaxRate(&v7->m_Controller, nPrice);
}
