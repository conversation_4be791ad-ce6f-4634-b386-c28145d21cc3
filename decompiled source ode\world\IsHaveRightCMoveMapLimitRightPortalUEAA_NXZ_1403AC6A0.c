/*
 * Function: ?IsHaveRight@CMoveMapLimitRightPortal@@UEAA_NXZ
 * Address: 0x1403AC6A0
 */

bool __fastcall CMoveMapLimitRightPortal::IsHaveRight(CMoveMapLimitRightPortal *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  unsigned __int16 v4; // ax@8
  __int64 v5; // [sp+0h] [bp-48h]@1
  char byCurrentHour; // [sp+20h] [bp-28h]@8
  char byNumOfTime; // [sp+28h] [bp-20h]@8
  char v8; // [sp+30h] [bp-18h]@8
  char v9; // [sp+31h] [bp-17h]@8
  char v10; // [sp+32h] [bp-16h]@8
  char v11; // [sp+33h] [bp-15h]@8
  CMoveMapLimitRightPortal *v12; // [sp+50h] [bp+8h]@1

  v12 = this;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v12->m_pkRight )
  {
    if ( CHolyStoneSystem::IsMinigeTicketCheck(&g_HolySys) )
    {
      v8 = CHolyStoneSystem::GetNumOfTime(&g_HolySys);
      v9 = CHolyStoneSystem::GetStartHour(&g_HolySys);
      v10 = CHolyStoneSystem::GetStartDay(&g_HolySys);
      v11 = CHolyStoneSystem::GetStartMonth(&g_HolySys);
      v4 = CHolyStoneSystem::GetStartYear(&g_HolySys);
      byNumOfTime = v8;
      byCurrentHour = v9;
      result = MiningTicket::AuthLastCriTicket(v12->m_pkRight, v4, v11, v10, v9, v8) != 0;
    }
    else
    {
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
