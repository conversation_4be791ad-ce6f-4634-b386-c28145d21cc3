/*
 * Function: ?db_input_guild_money@CMainThread@@QEAAEKKKKPEAN0PEAEPEAD@Z
 * Address: 0x1401B0FF0
 */

char __fastcall CMainThread::db_input_guild_money(CMainThread *this, unsigned int dwPusherSerial, unsigned int dwGuildSerial, unsigned int dwAddDalant, unsigned int dwAddGold, long double *dTotalDalant, long double *dTotalGold, char *byDate, char *pwszName)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v12; // ecx@10
  int v13; // edx@10
  unsigned int v14; // edi@10
  __int64 v15; // [sp+0h] [bp-248h]@1
  long double dResultDalant; // [sp+20h] [bp-228h]@10
  long double dResultGold; // [sp+28h] [bp-220h]@10
  char *wszDate; // [sp+30h] [bp-218h]@10
  unsigned int dwAvatorSerial; // [sp+38h] [bp-210h]@10
  char *v20; // [sp+40h] [bp-208h]@10
  _worlddb_guild_info::__guild_info pGuildData; // [sp+60h] [bp-1E8h]@6
  CCheckSumGuildData v22; // [sp+1C8h] [bp-80h]@8
  char DstBuf; // [sp+1F8h] [bp-50h]@10
  char v24; // [sp+1F9h] [bp-4Fh]@10
  char v25; // [sp+214h] [bp-34h]@12
  char v26; // [sp+220h] [bp-28h]@9
  char v27; // [sp+221h] [bp-27h]@11
  char v28; // [sp+222h] [bp-26h]@13
  char v29; // [sp+223h] [bp-25h]@14
  __int64 v30; // [sp+228h] [bp-20h]@4
  unsigned __int64 v31; // [sp+230h] [bp-18h]@4
  CMainThread *v32; // [sp+250h] [bp+8h]@1
  unsigned int v33; // [sp+258h] [bp+10h]@1
  unsigned int dwGuildSeriala; // [sp+260h] [bp+18h]@1
  signed int dwDalant; // [sp+268h] [bp+20h]@1

  dwDalant = dwAddDalant;
  dwGuildSeriala = dwGuildSerial;
  v33 = dwPusherSerial;
  v32 = this;
  v9 = &v15;
  for ( i = 144i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v30 = -2i64;
  v31 = (unsigned __int64)&v15 ^ _security_cookie;
  if ( CRFWorldDatabase::Update_InputGuildMoney(v32->m_pWorldDB, dwGuildSerial, dwAddDalant, dwAddGold) )
  {
    if ( CRFWorldDatabase::Select_GuildData(v32->m_pWorldDB, dwGuildSeriala, &pGuildData) )
    {
      *dTotalDalant = pGuildData.dDalant;
      *dTotalGold = pGuildData.dGold;
      CCheckSumGuildData::CCheckSumGuildData(&v22, dwGuildSeriala);
      CCheckSumGuildData::Encode(&v22, pGuildData.dDalant, pGuildData.dGold);
      if ( CCheckSumGuildData::Update(&v22, v32->m_pWorldDB) )
      {
        DstBuf = 0;
        memset(&v24, 0, 8ui64);
        v12 = (unsigned __int8)byDate[2];
        v13 = (unsigned __int8)byDate[1];
        v14 = (unsigned __int8)*byDate;
        LODWORD(wszDate) = (unsigned __int8)byDate[3];
        LODWORD(dResultGold) = v12;
        LODWORD(dResultDalant) = v13;
        sprintf_s(&DstBuf, 9ui64, "%02d%02d%02d%02d", v14);
        v20 = pwszName;
        dwAvatorSerial = v33;
        wszDate = &DstBuf;
        dResultGold = *dTotalGold;
        dResultDalant = *dTotalDalant;
        if ( CRFWorldDatabase::Insert_GuildMoneyHistory(
               v32->m_pWorldDB,
               dwGuildSeriala,
               (double)dwDalant,
               (double)(signed int)dwAddGold,
               dResultDalant,
               dResultGold,
               &DstBuf,
               v33,
               pwszName) )
        {
          v25 = CMainThread::check_min_max_guild_money(&g_Main, dwGuildSeriala, dTotalDalant, dTotalGold);
          if ( v25 )
          {
            v28 = v25;
            CCheckSumGuildData::~CCheckSumGuildData(&v22);
            result = v28;
          }
          else
          {
            v29 = 0;
            CCheckSumGuildData::~CCheckSumGuildData(&v22);
            result = v29;
          }
        }
        else
        {
          v27 = 24;
          CCheckSumGuildData::~CCheckSumGuildData(&v22);
          result = v27;
        }
      }
      else
      {
        v26 = 24;
        CCheckSumGuildData::~CCheckSumGuildData(&v22);
        result = v26;
      }
    }
    else
    {
      result = 24;
    }
  }
  else
  {
    result = 24;
  }
  return result;
}
