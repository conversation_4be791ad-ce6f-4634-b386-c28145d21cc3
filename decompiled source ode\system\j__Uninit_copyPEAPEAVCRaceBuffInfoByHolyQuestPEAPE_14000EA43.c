/*
 * Function: j_??$_Uninit_copy@PEAPEAVCRaceBuffInfoByHolyQuest@@PEAPEAV1@V?$allocator@PEAVCRaceBuffInfoByHolyQuest@@@std@@@std@@YAPEAPEAVCRaceBuffInfoByHolyQuest@@PEAPEAV1@00AEAV?$allocator@PEAVCRaceBuffInfoByHolyQuest@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000EA43
 */

CRaceBuffInfoByHolyQuest **__fastcall std::_Uninit_copy<CRaceBuffInfoByHolyQuest * *,CRaceBuffInfoByHolyQuest * *,std::allocator<CRaceBuffInfoByHolyQuest *>>(CRaceBuffInfoByHolyQuest **_First, CRaceBuffInfoByHolyQuest **_Last, CRaceBuffInfoByHolyQuest **_Dest, std::allocator<CRaceBuffInfoByHolyQuest *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CRaceBuffInfoByHolyQuest * *,CRaceBuffInfoByHolyQuest * *,std::allocator<CRaceBuffInfoByHolyQuest *>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
