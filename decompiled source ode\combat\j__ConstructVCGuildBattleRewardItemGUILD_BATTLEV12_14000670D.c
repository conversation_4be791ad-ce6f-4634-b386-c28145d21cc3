/*
 * Function: j_??$_Construct@VCGuildBattleRewardItem@GUILD_BATTLE@@V12@@std@@YAXPEAVCGuildBattleRewardItem@GUILD_BATTLE@@AEBV12@@Z
 * Address: 0x14000670D
 */

void __fastcall std::_Construct<GUILD_BATTLE::CGuildBattleRewardItem,GUILD_BATTLE::CGuildBattleRewardItem>(GUILD_BATTLE::CGuildBattleRewardItem *_Ptr, GUILD_BATTLE::CGuildBattleRewardItem *_Val)
{
  std::_Construct<GUILD_BATTLE::CGuildBattleRewardItem,GUILD_BATTLE::CGuildBattleRewardItem>(_Ptr, _Val);
}
