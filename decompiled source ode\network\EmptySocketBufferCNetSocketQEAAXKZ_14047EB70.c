/*
 * Function: ?EmptySocketBuffer@CNetSocket@@QEAAXK@Z
 * Address: 0x14047EB70
 */

void __fastcall CNetSocket::EmptySocketBuffer(CNetSocket *this, unsigned int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-D8h]@1
  char buf; // [sp+30h] [bp-A8h]@5
  int v6; // [sp+B4h] [bp-24h]@5
  unsigned __int64 v7; // [sp+C0h] [bp-18h]@4
  CNetSocket *v8; // [sp+E0h] [bp+8h]@1
  unsigned int v9; // [sp+E8h] [bp+10h]@1

  v9 = n;
  v8 = this;
  v2 = &v4;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = (unsigned __int64)&v4 ^ _security_cookie;
  do
    v6 = recv(v8->m_Socket[v9].m_Socket, &buf, 128, 0);
  while ( v6 && v6 != -1 );
}
