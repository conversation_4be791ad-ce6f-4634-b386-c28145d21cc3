/*
 * Function: ?InsertDefalutRecord@CUnmannedTraderController@@IEAA_NXZ
 * Address: 0x140350610
 */

char __fastcall CUnmannedTraderController::InsertDefalutRecord(CUnmannedTraderController *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  CUnmannedTraderController *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = CRFWorldDatabase::Select_UnmannedTraderSingleItemEmptyRecordCnt(pkDB);
  if ( v5 >= 0 )
  {
    if ( CUnmannedTraderEnvironmentValue::Unmanned_Trader_Min_Limit_Empty_Record_Cnt <= v5
      || CRFWorldDatabase::Insert_UnmannedTraderSingleDefaultRecord(
           pkDB,
           CUnmannedTraderEnvironmentValue::Unmanned_Trader_Default_Empty_Record_Cnt) )
    {
      result = 1;
    }
    else
    {
      CUnmannedTraderController::Log(
        v6,
        "CUnmannedTraderController::InsertDefalutRecord()\r\n"
        "\t\tg_Main.m_pWorldDB->Insert_UnmannedTraderSingleDefaultRecord( %u ) Fail!\r\n",
        CUnmannedTraderEnvironmentValue::Unmanned_Trader_Default_Empty_Record_Cnt);
      result = 0;
    }
  }
  else
  {
    CUnmannedTraderController::Log(
      v6,
      "CUnmannedTraderController::InsertDefalutRecord()\r\n"
      "\t\tiCnt(%d) = g_Main.m_pWorldDB->Select_UnmannedTraderItemEmptyRecordCnt()!\r\n",
      (unsigned int)v5);
    result = 0;
  }
  return result;
}
