/*
 * Function: ??1CUnmannedTraderLazyCleaner@@QEAA@XZ
 * Address: 0x140392A90
 */

void __fastcall CUnmannedTraderLazyCleaner::~CUnmannedTraderLazyCleaner(CUnmannedTraderLazyCleaner *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  CMyTimer *v4; // [sp+20h] [bp-28h]@4
  CMyTimer *v5; // [sp+28h] [bp-20h]@4
  CUnmannedTraderLazyCleaner *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = v6->m_pkTimer;
  v4 = v5;
  if ( v5 )
    ((void (__fastcall *)(CMyTimer *, signed __int64))v4->vfptr->__vecDelDtor)(v4, 1i64);
}
