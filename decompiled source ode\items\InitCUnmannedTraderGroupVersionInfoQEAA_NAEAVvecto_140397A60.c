/*
 * Function: ?Init@CUnmannedTraderGroupVersionInfo@@QEAA_NAEAV?$vector@U?$pair@KK@std@@V?$allocator@U?$pair@KK@std@@@2@@std@@@Z
 * Address: 0x140397A60
 */

char __fastcall CUnmannedTraderGroupVersionInfo::Init(CUnmannedTraderGroupVersionInfo *this, std::vector<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *vecInfo)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@5
  std::pair<unsigned long,unsigned long> *v5; // rax@8
  CUnmannedTraderGroupDivisionVersionInfo *v6; // rax@8
  CUnmannedTraderGroupDivisionVersionInfo *v7; // rax@8
  unsigned __int64 v8; // rax@11
  __int64 v9; // [sp+0h] [bp-108h]@1
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > result; // [sp+28h] [bp-E0h]@6
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > v11; // [sp+48h] [bp-C0h]@10
  bool v12; // [sp+60h] [bp-A8h]@7
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > v13; // [sp+68h] [bp-A0h]@7
  CUnmannedTraderGroupDivisionVersionInfo v14; // [sp+80h] [bp-88h]@8
  char v15; // [sp+B0h] [bp-58h]@9
  bool v16; // [sp+B1h] [bp-57h]@11
  __int64 v17; // [sp+B8h] [bp-50h]@4
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *v18; // [sp+C0h] [bp-48h]@7
  std::_Vector_const_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *_Right; // [sp+C8h] [bp-40h]@7
  std::pair<unsigned long,unsigned long> *v20; // [sp+D0h] [bp-38h]@8
  CUnmannedTraderGroupDivisionVersionInfo *v21; // [sp+D8h] [bp-30h]@8
  CUnmannedTraderGroupDivisionVersionInfo *_Val; // [sp+E0h] [bp-28h]@8
  unsigned __int64 v23; // [sp+E8h] [bp-20h]@11
  int v24; // [sp+F0h] [bp-18h]@11
  CUnmannedTraderGroupVersionInfo *v25; // [sp+110h] [bp+8h]@1
  std::vector<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *v26; // [sp+118h] [bp+10h]@1

  v26 = vecInfo;
  v25 = this;
  v2 = &v9;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v17 = -2i64;
  if ( std::vector<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::empty(vecInfo) )
  {
    v4 = 0;
  }
  else
  {
    std::vector<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::begin(
      v26,
      &result);
    while ( 1 )
    {
      v18 = std::vector<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::end(
              v26,
              &v13);
      _Right = (std::_Vector_const_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *)v18;
      v12 = std::_Vector_const_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::operator!=(
              (std::_Vector_const_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *)&result._Mycont,
              (std::_Vector_const_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *)&v18->_Mycont);
      std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::~_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>(&v13);
      if ( !v12 )
        break;
      v20 = std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::operator*(&result);
      v5 = std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::operator*(&result);
      CUnmannedTraderGroupDivisionVersionInfo::CUnmannedTraderGroupDivisionVersionInfo(&v14, v5->first, v20->second);
      v21 = v6;
      _Val = v6;
      std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::push_back(
        &v25->m_vecVerInfo,
        v6);
      CUnmannedTraderGroupDivisionVersionInfo::~CUnmannedTraderGroupDivisionVersionInfo(&v14);
      v7 = std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::back(&v25->m_vecVerInfo);
      if ( CUnmannedTraderGroupDivisionVersionInfo::IsEmpty(v7) )
      {
        v15 = 0;
        std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::~_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>(&result);
        return v15;
      }
      std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::operator++(
        &result,
        &v11,
        0);
      std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::~_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>(&v11);
    }
    v23 = std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::size(&v25->m_vecVerInfo);
    v8 = std::vector<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::size(v26);
    v24 = v23 == v8;
    v16 = v23 == v8;
    std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::~_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>(&result);
    v4 = v16;
  }
  return v4;
}
