/*
 * Function: ?SetReadyState@CNormalGuildBattle@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403E3B30
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::SetReadyState(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattle *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v4->m_bInit )
  {
    if ( v4->m_pkStateList )
      GUILD_BATTLE::CGuildBattleStateList::SetReady((GUILD_BATTLE::CGuildBattleStateList *)&v4->m_pkStateList->vfptr);
  }
}
