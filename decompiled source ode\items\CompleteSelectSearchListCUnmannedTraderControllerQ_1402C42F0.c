/*
 * Function: ?CompleteSelectSearchList@CUnmannedTraderController@@QEAAXEEPEAD@Z
 * Address: 0x1402C42F0
 */

void __fastcall CUnmannedTraderController::CompleteSelectSearchList(CUnmannedTraderController *this, char byDBRet, char byProcRet, char *pLoadData)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfoTable *v6; // rax@4
  __int64 v7; // [sp+0h] [bp-28h]@1
  char v8; // [sp+38h] [bp+10h]@1
  char v9; // [sp+40h] [bp+18h]@1
  char *pLoadDataa; // [sp+48h] [bp+20h]@1

  pLoadDataa = pLoadData;
  v9 = byProcRet;
  v8 = byDBRet;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v6 = CUnmannedTraderUserInfoTable::Instance();
  CUnmannedTraderUserInfoTable::CompleteSearch(v6, v8, v9, pLoadDataa);
}
