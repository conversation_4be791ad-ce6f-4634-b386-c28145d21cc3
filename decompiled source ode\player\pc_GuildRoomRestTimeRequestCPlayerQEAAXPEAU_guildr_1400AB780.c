/*
 * Function: ?pc_GuildRoomRestTimeRequest@CPlayer@@QEAAXPEAU_guildroom_resttime_request_clzo@@@Z
 * Address: 0x1400AB780
 */

void __fastcall CPlayer::pc_GuildRoomRestTimeRequest(CPlayer *this, _guildroom_resttime_request_clzo *pProtocol)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomSystem *v4; // rax@8
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  char v7; // [sp+21h] [bp-17h]@4
  CGuild *v8; // [sp+28h] [bp-10h]@6
  CPlayer *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  v7 = 3;
  if ( v9->m_Param.m_pGuild )
  {
    v8 = v9->m_Param.m_pGuild;
    if ( pProtocol->dwGuildSerial == v8->m_dwSerial )
    {
      v4 = CGuildRoomSystem::GetInstance();
      if ( CGuildRoomSystem::IsRoomRented(v4, v8->m_dwSerial) )
      {
        CPlayer::SendMsg_GuildRoomRestTimeResult(v9);
      }
      else
      {
        v6 = 2;
        CPlayer::SendMsg_GuildRoomRestTimeResult(v9);
      }
    }
    else
    {
      v6 = 1;
      CPlayer::SendMsg_GuildRoomRestTimeResult(v9);
    }
  }
  else
  {
    v6 = 1;
    CPlayer::SendMsg_GuildRoomRestTimeResult(v9);
  }
}
