/*
 * Function: ?IsBossMonster@CMonster@@QEAA_NXZ
 * Address: 0x14007D4E0
 */

bool __fastcall CMonster::IsBossMonster(CMonster *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // [sp+0h] [bp-18h]@1
  int v5; // [sp+4h] [bp-14h]@5
  CMonster *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  if ( v6->m_pMonRec )
  {
    v4 = v6->m_pMonRec->m_bMonsterCondition == 1;
    v5 = v4;
  }
  else
  {
    v5 = 0;
  }
  return v5;
}
