/*
 * Function: ?SF_ReturnBindPosition@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 * Address: 0x1400A0590
 */

char __fastcall CPlayer::SF_ReturnBindPosition(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-78h]@1
  CPlayer *v7; // [sp+30h] [bp-48h]@6
  float pfPos; // [sp+48h] [bp-30h]@6
  CMapData *pIntoMap; // [sp+68h] [bp-10h]@6

  v3 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pDstObj->m_ObjID.m_byID )
  {
    result = 0;
  }
  else
  {
    v7 = (CPlayer *)pDstObj;
    pIntoMap = CPlayer::GetBindMap((CPlayer *)pDstObj, &pfPos, 1);
    if ( pIntoMap )
    {
      CPlayer::OutOfMap(v7, pIntoMap, 0, 3, &pfPos);
      CPlayer::SendMsg_MovePortal(v7, pIntoMap->m_pMapSet->m_dwIndex, &pfPos, 2);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  return result;
}
