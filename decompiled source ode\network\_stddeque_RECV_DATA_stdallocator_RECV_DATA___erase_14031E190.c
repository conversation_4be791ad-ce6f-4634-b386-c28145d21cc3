/*
 * Function: _std::deque_RECV_DATA_std::allocator_RECV_DATA___::erase_::_1_::dtor$12
 * Address: 0x14031E190
 */

void __fastcall std::deque_RECV_DATA_std::allocator_RECV_DATA___::erase_::_1_::dtor_12(__int64 a1, __int64 a2)
{
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>((std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)(a2 + 424));
}
