/*
 * Function: ?SendMsg_ResultCode@PatriarchElectProcessor@@QEAAXKE@Z
 * Address: 0x1402BAFB0
 */

void __fastcall PatriarchElectProcessor::SendMsg_ResultCode(PatriarchElectProcessor *this, unsigned int n, char byCode)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@4
  __int64 v6; // [sp+0h] [bp-78h]@1
  _pt_result_code_zocl v7; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4
  unsigned int dwClientIndex; // [sp+88h] [bp+10h]@1
  char v11; // [sp+90h] [bp+18h]@1

  v11 = byCode;
  dwClientIndex = n;
  v3 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  _pt_result_code_zocl::_pt_result_code_zocl(&v7);
  v7.byRetCode = v11;
  pbyType = 56;
  v9 = -1;
  v5 = _pt_result_code_zocl::size(&v7);
  CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &v7.byRetCode, v5);
}
