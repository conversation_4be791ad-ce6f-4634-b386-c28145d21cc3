/*
 * Function: ?CompleteBuyRollBack@CUnmannedTraderController@@QEAAXEPEAD@Z
 * Address: 0x14034ECF0
 */

void __fastcall CUnmannedTraderController::CompleteBuyRollBack(CUnmannedTraderController *this, char byRet, char *pLoadData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@9
  int v6; // ecx@9
  int v7; // edx@9
  __int64 v8; // [sp+0h] [bp-78h]@1
  int v9; // [sp+20h] [bp-58h]@9
  int v10; // [sp+28h] [bp-50h]@9
  int v11; // [sp+30h] [bp-48h]@9
  int v12; // [sp+38h] [bp-40h]@9
  const char *v13; // [sp+40h] [bp-38h]@9
  char *v14; // [sp+50h] [bp-28h]@4
  int j; // [sp+58h] [bp-20h]@4
  const char *v16; // [sp+60h] [bp-18h]@7
  unsigned int v17; // [sp+68h] [bp-10h]@9
  CUnmannedTraderController *v18; // [sp+80h] [bp+8h]@1

  v18 = this;
  v3 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = pLoadData;
  for ( j = 0; j < (unsigned __int8)v14[10]; ++j )
  {
    if ( v14[12 * j + 12] )
      v16 = "Fail";
    else
      v16 = "Success";
    v5 = (unsigned __int8)v14[8];
    v6 = *(_WORD *)v14;
    v7 = (unsigned __int8)v14[12 * j + 20];
    v17 = (unsigned __int8)v14[9];
    v13 = v16;
    v12 = *((_DWORD *)v14 + 1);
    v11 = v5;
    v10 = v6;
    v9 = v7;
    CUnmannedTraderController::Log(
      v18,
      "CUnmannedTraderController::CompleteBuyRollBack( BYTE byRet, char * pLoadData )\r\n"
      "\t\tType(%u) Regist Serial(%u), Old State(%u) wInx(%u) Race(%u) BuyerSerial(%u) RollBack %s\r\n",
      v17,
      *(_DWORD *)&v14[12 * j + 16]);
  }
}
