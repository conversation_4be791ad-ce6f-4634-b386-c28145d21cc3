/*
 * Function: ?DelUnit@_LIST@_UNIT_DB_BASE@@QEAAXXZ
 * Address: 0x1401082C0
 */

void __fastcall _UNIT_DB_BASE::_LIST::DelUnit(_UNIT_DB_BASE::_LIST *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _UNIT_DB_BASE::_LIST *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->byFrame = -1;
  memset_0(v4->byPart, 255, 6ui64);
  memset_0(v4->dwBullet, -1, 8ui64);
  memset_0(v4->dwSpare, -1, 0x20ui64);
  v4->dwGauge = 0;
  v4->nPullingFee = 0;
  v4->dwCutTime = 0;
  v4->wBooster = 0;
}
