/*
 * Function: ?SetModulusAndSubgroupGenerator@?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@UEAAXAEBVInteger@2@0@Z
 * Address: 0x140552530
 */

__int64 __fastcall CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::SetModulusAndSubgroupGenerator(__int64 a1, const struct CryptoPP::Integer *a2, __int64 a3)
{
  __int64 v3; // rax@1
  __int64 v5; // [sp+40h] [bp+8h]@1
  __int64 v6; // [sp+50h] [bp+18h]@1

  v6 = a3;
  v5 = a1;
  CryptoPP::ModExpPrecomputation::SetModulus((CryptoPP::ModExpPrecomputation *)(a1 + 72), a2);
  LODWORD(v3) = (*(int (__fastcall **)(signed __int64))(*(_QWORD *)(v5 + 8) + 40i64))(v5 + 8);
  (*(void (__fastcall **)(signed __int64, __int64, __int64))(*(_QWORD *)(v5 + 88) + 8i64))(v5 + 88, v3, v6);
  return CryptoPP::DL_GroupParameters<CryptoPP::Integer>::ParametersChanged(v5 + 8);
}
