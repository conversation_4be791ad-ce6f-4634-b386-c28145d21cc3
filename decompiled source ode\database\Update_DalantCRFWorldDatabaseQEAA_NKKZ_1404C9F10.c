/*
 * Function: ?Update_Dalant@CRFWorldDatabase@@QEAA_NKK@Z
 * Address: 0x1404C9F10
 */

bool __fastcall CRFWorldDatabase::Update_Dalant(CRFWorldDatabase *this, unsigned int dwSerial, unsigned int dwDalant)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-D8h]@1
  char Dest; // [sp+30h] [bp-A8h]@4
  char v8; // [sp+31h] [bp-A7h]@4
  unsigned __int64 v9; // [sp+C0h] [bp-18h]@4
  CRFWorldDatabase *v10; // [sp+E0h] [bp+8h]@1

  v10 = this;
  v3 = &v6;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = (unsigned __int64)&v6 ^ _security_cookie;
  Dest = 0;
  memset(&v8, 0, 0x7Fui64);
  sprintf(&Dest, "UPDATE tbl_base SET Dalant = %d WHERE Serial = %d", dwDalant, dwSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &Dest, 1);
}
