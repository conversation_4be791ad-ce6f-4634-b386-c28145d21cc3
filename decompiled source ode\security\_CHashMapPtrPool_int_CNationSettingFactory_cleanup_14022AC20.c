/*
 * Function: _CHashMapPtrPool_int_CNationSettingFactory_::cleanup_::_1_::dtor$3
 * Address: 0x14022AC20
 */

void __fastcall CHashMapPtrPool_int_CNationSettingFactory_::cleanup_::_1_::dtor_3(__int64 a1, __int64 a2)
{
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Const_iterator<0>::~_Const_iterator<0>((std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> *)(a2 + 104));
}
