/*
 * Function: ?SendMsg_OpenPortalByReact@CDarkHoleChannel@@QEAAXH@Z
 * Address: 0x14026BE00
 */

void __fastcall CDarkHoleChannel::SendMsg_OpenPortalByReact(CDarkHoleChannel *this, int nPortalIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@7
  __int64 v5; // [sp+0h] [bp-78h]@1
  _darkhole_open_portal_by_react_inform_zocl v6; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  int j; // [sp+64h] [bp-14h]@4
  _dh_player_mgr *v10; // [sp+68h] [bp-10h]@6
  CDarkHoleChannel *v11; // [sp+80h] [bp+8h]@1

  v11 = this;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6.wPortalIndex = nPortalIndex;
  pbyType = 35;
  v8 = 7;
  for ( j = 0; j < 32; ++j )
  {
    v10 = &v11->m_Quester[j];
    if ( _dh_player_mgr::IsFill(v10) )
    {
      v4 = _darkhole_open_portal_by_react_inform_zocl::size(&v6);
      CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_Quester[j].pOne->m_ObjID.m_wIndex, &pbyType, (char *)&v6, v4);
    }
  }
}
