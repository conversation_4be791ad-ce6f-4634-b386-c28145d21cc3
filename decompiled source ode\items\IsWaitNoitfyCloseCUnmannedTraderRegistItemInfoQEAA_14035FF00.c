/*
 * Function: ?IsWaitNoitfyClose@CUnmannedTraderRegistItemInfo@@QEAA_NXZ
 * Address: 0x14035FF00
 */

bool __fastcall CUnmannedTraderRegistItemInfo::IsWaitNoitfyClose(CUnmannedTraderRegistItemInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  CUnmannedTraderRegistItemInfo *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return CUnmannedTraderItemState::GetState(&v6->m_kState) == 6
      || CUnmannedTraderItemState::GetState(&v6->m_kState) == 13;
}
