/*
 * Function: ?Free@CEnglandBillingMgr@@QEAA_NXZ
 * Address: 0x140319730
 */

bool __fastcall CEnglandBillingMgr::Free(CEnglandBillingMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CEngNetworkBillEX *v3; // rax@4
  __int64 v5; // [sp+0h] [bp-28h]@1
  CEnglandBillingMgr *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = CTSingleton<CEngNetworkBillEX>::Instance();
  BNetwork::FreeDLL((BNetwork *)&v3->vfptr);
  CLogFile::Write(&v6->m_logBill, "CEnglandBillingMgr::Free() Fail");
  return 0;
}
