/*
 * Function: j_?pc_<PERSON><PERSON><PERSON><PERSON>_Siege@CPlayer@@QEAAXPEAVCCharacter@@PEAMEGG@Z
 * Address: 0x140003F67
 */

void __fastcall CPlayer::pc_PlayAttack_Siege(CPlayer *this, CCharacter *pDst, float *pfAttackPos, char by<PERSON>tt<PERSON><PERSON>, unsigned __int16 wBulletSerial, unsigned __int16 wEffBtSerial)
{
  CPlayer::pc_PlayAttack_Siege(this, pDst, pfAttackPos, byAttPart, wBulletSerial, wEffBtSerial);
}
