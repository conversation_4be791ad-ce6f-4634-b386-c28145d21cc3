/*
 * Function: ?NotifyCommitteeMemberPosition@CNormalGuildBattle@GUILD_BATTLE@@QEAAXKK@Z
 * Address: 0x1403E55B0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::NotifyCommitteeMemberPosition(GUILD_BATTLE::CNormalGuildBattle *this, unsigned int dwGuildSerial, unsigned int dwChracSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-58h]@1
  int v6; // [sp+20h] [bp-38h]@6
  int v7; // [sp+30h] [bp-28h]@4
  CPlayer *pkPlayer; // [sp+38h] [bp-20h]@5
  CPlayer *v9; // [sp+40h] [bp-18h]@9
  GUILD_BATTLE::CNormalGuildBattle *v10; // [sp+60h] [bp+8h]@1
  unsigned int v11; // [sp+68h] [bp+10h]@1
  int dwSerial; // [sp+70h] [bp+18h]@1

  dwSerial = dwChracSerial;
  v11 = dwGuildSerial;
  v10 = this;
  v3 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = -1;
  if ( dwGuildSerial == GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v10->m_k1P) )
  {
    pkPlayer = GUILD_BATTLE::CNormalGuildBattleGuild::GetMemberPlayer(&v10->m_k1P, dwSerial);
    if ( pkPlayer )
    {
      GUILD_BATTLE::CNormalGuildBattleGuild::SendOhterNotifyCommitteeMemberPosition(&v10->m_k1P, pkPlayer);
    }
    else
    {
      v6 = dwSerial;
      GUILD_BATTLE::CNormalGuildBattleLogger::Log(
        &v10->m_kLogger,
        "CNormalGuildBattle::NotifyCommitteeMemberPosition( dwGuildSerial(%u), dwChracSerial(%u) ) : NULL == m_k1P.GetMemberPlayer( %u )",
        v11,
        (unsigned int)dwSerial);
    }
  }
  else if ( v11 == GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v10->m_k2P) )
  {
    v9 = GUILD_BATTLE::CNormalGuildBattleGuild::GetMemberPlayer(&v10->m_k2P, dwSerial);
    if ( v9 )
    {
      GUILD_BATTLE::CNormalGuildBattleGuild::SendOhterNotifyCommitteeMemberPosition(&v10->m_k2P, v9);
    }
    else
    {
      v6 = dwSerial;
      GUILD_BATTLE::CNormalGuildBattleLogger::Log(
        &v10->m_kLogger,
        "CNormalGuildBattle::NotifyCommitteeMemberPosition( dwGuildSerial(%u), dwChracSerial(%u) ) : NULL == m_k2P.GetMemberPlayer( %u )",
        v11,
        (unsigned int)dwSerial);
    }
  }
}
