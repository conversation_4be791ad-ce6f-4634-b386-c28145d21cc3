/*
 * Function: ?InitShip@CTransportShip@@QEAA_NPEAVCMapData@@00E@Z
 * Address: 0x1402639C0
 */

char __fastcall CTransportShip::InitShip(CTransportShip *this, CMapData *pLinkShipMap, CMapData *pLinkMainbaseMap, CMapData *pLinkPlatformMap, char byRaceCode_Layer)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // eax@8
  __int64 v9; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  int n; // [sp+24h] [bp-14h]@6
  _base_fld *v12; // [sp+28h] [bp-10h]@9
  CTransportShip *v13; // [sp+40h] [bp+8h]@1
  CMapData *v14; // [sp+48h] [bp+10h]@1
  CMapData *v15; // [sp+50h] [bp+18h]@1
  CMapData *v16; // [sp+58h] [bp+20h]@1

  v16 = pLinkPlatformMap;
  v15 = pLinkMainbaseMap;
  v14 = pLinkShipMap;
  v13 = this;
  v5 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v13->m_bAnchor = 1;
  v13->m_byDirect = 0;
  v13->m_bHurry = 0;
  v13->m_dwNextHurryTime = -1;
  v13->m_dwEventCreateTime = timeGetTime();
  v13->m_pLinkShipMap = v14;
  v13->m_pLinkPortMap[0] = v15;
  v13->m_pLinkPortMap[1] = v16;
  v13->m_byRaceCode_Layer = byRaceCode_Layer;
  for ( j = 0; j < 2; ++j )
  {
    CTransportShip::__mgr_ticket::init(&v13->m_MgrTicket[j]);
    v13->m_MgrTicket[j].pLinkTicketItem = 0i64;
    for ( n = 0; ; ++n )
    {
      v7 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 28);
      if ( n >= v7 )
        break;
      v12 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 28, n);
      if ( v12[3].m_strCode[2 * v13->m_byRaceCode_Layer + 4] != 48
        && !strcmp_0(&v12[4].m_strCode[8], v13->m_pLinkPortMap[j]->m_pMapSet->m_strCode) )
      {
        v13->m_MgrTicket[j].pLinkTicketItem = (_TicketItem_fld *)v12;
        break;
      }
    }
    if ( !v13->m_MgrTicket[j].pLinkTicketItem )
    {
      MyMessageBox("CTransportShip::InitShip", "Ticket Link Error");
      return 0;
    }
  }
  memset_0(v13->m_NewMember, 0, 0x9E40ui64);
  memset_0(v13->m_OldMember, 0, 0x9E40ui64);
  return 1;
}
