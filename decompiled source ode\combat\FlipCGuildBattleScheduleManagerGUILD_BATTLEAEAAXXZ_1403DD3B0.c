/*
 * Function: ?Flip@CGuildBattleScheduleManager@GUILD_BATTLE@@AEAAXXZ
 * Address: 0x1403DD3B0
 */

void __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::Flip(GUILD_BATTLE::CGuildBattleScheduleManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v3; // rax@4
  CGuildBattleController *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *v6; // [sp+20h] [bp-18h]@4
  unsigned int v7; // [sp+28h] [bp-10h]@4
  GUILD_BATTLE::CGuildBattleScheduleManager *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = ATL::CTime::GetDay(v8->m_pkOldDayTime);
  v3 = GUILD_BATTLE::CGuildBattleLogger::Instance();
  GUILD_BATTLE::CGuildBattleLogger::Log(v3, "CGuildBattleScheduleManager::Flip() : %d Day!", v7);
  v6 = v8->m_pkTodaySchedule;
  v8->m_pkTodaySchedule = v8->m_pkTomorrowSchedule;
  v8->m_pkTomorrowSchedule = v6;
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Flip(v8->m_pkTodaySchedule);
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Clear(v8->m_pkTomorrowSchedule);
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::PushDQSClear(v8->m_pkTomorrowSchedule);
  v4 = CGuildBattleController::Instance();
  CGuildBattleController::Flip(v4);
}
