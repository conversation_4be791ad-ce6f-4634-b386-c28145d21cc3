/*
 * Function: ?IsForwardTransformation@?$CFB_EncryptionTemplate@V?$AbstractPolicyHolder@VCFB_CipherAbstractPolicy@CryptoPP@@V?$SimpleKeyedTransformation@VStreamTransformation@CryptoPP@@@2@@CryptoPP@@@CryptoPP@@EEBA_NXZ
 * Address: 0x14055AD80
 */

char CryptoPP::CFB_EncryptionTemplate<CryptoPP::AbstractPolicyHolder<CryptoPP::CFB_CipherAbstractPolicy,CryptoPP::SimpleKeyedTransformation<CryptoPP::StreamTransformation>>>::IsForwardTransformation()
{
  return 1;
}
