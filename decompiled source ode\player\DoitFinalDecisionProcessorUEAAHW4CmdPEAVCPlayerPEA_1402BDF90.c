/*
 * Function: ?Doit@FinalDecisionProcessor@@UEAAHW4Cmd@@PEAVCPlayer@@PEAD@Z
 * Address: 0x1402BDF90
 */

__int64 __fastcall FinalDecisionProcessor::Doit(FinalDecisionProcessor *this, Cmd eCmd, CPlayer *pOne, char *pdata)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  unsigned int v8; // [sp+20h] [bp-18h]@4
  Cmd v9; // [sp+24h] [bp-14h]@4
  FinalDecisionProcessor *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = 0;
  v9 = eCmd;
  if ( eCmd == 9 )
  {
    FinalDecisionProcessor::_FinalDecision(v10);
  }
  else if ( v9 == 10 )
  {
    FinalDecisionProcessor::_SetWinner(v10);
  }
  else if ( v9 == 13 )
  {
    FinalDecisionProcessor::_ReqNetFinalDecision(v10, pOne);
  }
  else
  {
    v8 = 255;
  }
  return v8;
}
