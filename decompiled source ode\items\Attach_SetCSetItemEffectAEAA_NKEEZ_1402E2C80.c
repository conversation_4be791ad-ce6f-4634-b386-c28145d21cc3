/*
 * Function: ?Attach_Set@CSetItemEffect@@AEAA_NKEE@Z
 * Address: 0x1402E2C80
 */

char __fastcall CSetItemEffect::Attach_Set(CSetItemEffect *this, unsigned int dwSetItem, char bySetItemNum, char bySetEffectNum)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@8
  CSetItemEffect *v9; // [sp+40h] [bp+8h]@1
  unsigned int dwSetItema; // [sp+48h] [bp+10h]@1
  char v11; // [sp+50h] [bp+18h]@1
  char v12; // [sp+58h] [bp+20h]@1

  v12 = bySetEffectNum;
  v11 = bySetItemNum;
  dwSetItema = dwSetItem;
  v9 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v9->m_byTotalSetCount <= 6 )
  {
    if ( CSetItemEffect::IsSetOnComplete(v9, dwSetItem, bySetItemNum, bySetEffectNum) )
    {
      result = 0;
    }
    else
    {
      for ( j = 0; j < 6; ++j )
      {
        if ( !v9->m_setCount[j].m_bCheckSetEffect )
        {
          v9->m_setCount[j].m_bCheckSetEffect = 1;
          v9->m_setCount[j].m_dwSetItem = dwSetItema;
          v9->m_setCount[j].m_bySetItemNum = v11;
          v9->m_setCount[j].m_bySetEffectNum = v12;
          ++v9->m_byTotalSetCount;
          return 1;
        }
      }
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
