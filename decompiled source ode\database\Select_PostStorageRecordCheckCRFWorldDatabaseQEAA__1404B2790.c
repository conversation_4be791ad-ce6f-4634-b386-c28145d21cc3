/*
 * Function: ?Select_PostStorageRecordCheck@CRFWorldDatabase@@QEAA_NXZ
 * Address: 0x1404B2790
 */

char __fastcall CRFWorldDatabase::Select_PostStorageRecordCheck(CRFWorldDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v4; // [sp+0h] [bp-128h]@1
  void *SQLStmt; // [sp+20h] [bp-108h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-100h]@21
  SQLLEN v7; // [sp+38h] [bp-F0h]@21
  __int16 v8; // [sp+44h] [bp-E4h]@9
  int TargetValue; // [sp+54h] [bp-D4h]@4
  char DstBuf; // [sp+80h] [bp-A8h]@4
  char v11; // [sp+81h] [bp-A7h]@4
  unsigned __int64 v12; // [sp+110h] [bp-18h]@4
  CRFWorldDatabase *v13; // [sp+130h] [bp+8h]@1

  v13 = this;
  v1 = &v4;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = (unsigned __int64)&v4 ^ _security_cookie;
  TargetValue = 0;
  DstBuf = 0;
  memset(&v11, 0, 0x7Fui64);
  sprintf_s(&DstBuf, 0x80ui64, "select top 1 Serial from tbl_PostStorage where dck=1");
  if ( v13->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v13->vfptr, &DstBuf);
  if ( v13->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v13->vfptr) )
  {
    v8 = SQLExecDirect_0(v13->m_hStmtSelect, &DstBuf, -3);
    if ( v8 && v8 != 1 )
    {
      if ( v8 == 100 )
      {
        result = 0;
      }
      else
      {
        SQLStmt = v13->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v8, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v8, v13->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      v8 = SQLFetch_0(v13->m_hStmtSelect);
      if ( v8 && v8 != 1 )
      {
        if ( v8 != 100 )
        {
          SQLStmt = v13->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v8, &DstBuf, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v8, v13->m_hStmtSelect);
        }
        if ( v13->m_hStmtSelect )
          SQLCloseCursor_0(v13->m_hStmtSelect);
        result = 0;
      }
      else
      {
        StrLen_or_IndPtr = &v7;
        SQLStmt = 0i64;
        v8 = SQLGetData_0(v13->m_hStmtSelect, 1u, -18, &TargetValue, 0i64, &v7);
        if ( v8 && v8 != 1 )
        {
          SQLStmt = v13->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v8, &DstBuf, "SQLGetData", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v8, v13->m_hStmtSelect);
          if ( v13->m_hStmtSelect )
            SQLCloseCursor_0(v13->m_hStmtSelect);
          result = 0;
        }
        else
        {
          if ( v13->m_hStmtSelect )
            SQLCloseCursor_0(v13->m_hStmtSelect);
          if ( v13->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v13->vfptr, "%s Success", &DstBuf);
          result = 1;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v13->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 0;
  }
  return result;
}
