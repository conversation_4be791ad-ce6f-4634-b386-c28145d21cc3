/*
 * Function: j_??$unchecked_uninitialized_copy@V?$_Vector_iterator@U?$pair@KK@std@@V?$allocator@U?$pair@KK@std@@@2@@std@@PEAU?$pair@KK@2@V?$allocator@U?$pair@KK@std@@@2@@stdext@@YAPEAU?$pair@KK@std@@V?$_Vector_iterator@U?$pair@KK@std@@V?$allocator@U?$pair@KK@std@@@2@@2@0PEAU12@AEAV?$allocator@U?$pair@KK@std@@@2@@Z
 * Address: 0x140003224
 */

std::pair<unsigned long,unsigned long> *__fastcall stdext::unchecked_uninitialized_copy<std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>,std::pair<unsigned long,unsigned long> *,std::allocator<std::pair<unsigned long,unsigned long>>>(std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *_First, std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *_Last, std::pair<unsigned long,unsigned long> *_Dest, std::allocator<std::pair<unsigned long,unsigned long> > *_Al)
{
  return stdext::unchecked_uninitialized_copy<std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>,std::pair<unsigned long,unsigned long> *,std::allocator<std::pair<unsigned long,unsigned long>>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
