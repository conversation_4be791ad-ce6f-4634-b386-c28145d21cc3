/*
 * Function: ?ReadScript@CMapDataTable@@QEAA_NPEAD@Z
 * Address: 0x140198900
 */

char __fastcall CMapDataTable::ReadScript(CMapDataTable *this, char *szFileName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-128h]@1
  FILE *File; // [sp+20h] [bp-108h]@6
  char Str1; // [sp+40h] [bp-E8h]@9
  unsigned int v8; // [sp+C4h] [bp-64h]@8
  unsigned int v9; // [sp+C8h] [bp-60h]@16
  char v10[16]; // [sp+D4h] [bp-54h]@19
  int j; // [sp+E4h] [bp-44h]@19
  int __n[2]; // [sp+F0h] [bp-38h]@13
  void *v13; // [sp+F8h] [bp-30h]@16
  void *__t; // [sp+100h] [bp-28h]@13
  __int64 v15; // [sp+108h] [bp-20h]@4
  void *v16; // [sp+110h] [bp-18h]@14
  unsigned __int64 v17; // [sp+118h] [bp-10h]@4
  CMapDataTable *v18; // [sp+130h] [bp+8h]@1

  v18 = this;
  v2 = &v5;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v15 = -2i64;
  v17 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( v18->m_pRecord )
  {
    result = 0;
  }
  else
  {
    File = fopen(szFileName, "rt");
    if ( File )
    {
      v8 = 0;
      while ( fscanf(File, "%s", &Str1) != -1 )
      {
        if ( !strcmp_0(&Str1, "*") )
          ++v8;
      }
      v18->m_dwRecordNum = v8;
      *(_QWORD *)__n = v18->m_dwRecordNum;
      __t = operator new[](saturated_mul(0xB4ui64, *(unsigned __int64 *)__n));
      if ( __t )
      {
        `vector constructor iterator'(__t, 0xB4ui64, __n[0], (void *(__cdecl *)(void *))_map_fld::_map_fld);
        v16 = __t;
      }
      else
      {
        v16 = 0i64;
      }
      v13 = v16;
      v18->m_pRecord = (_map_fld *)v16;
      rewind(File);
      v9 = 0;
      while ( fscanf(File, "%s", &Str1) != -1 )
      {
        if ( !strcmp_0(&Str1, "*") )
        {
          fscanf(File, "%s", v18->m_pRecord[v9].m_strCode);
          fscanf(File, "%s", v18->m_pRecord[v9].m_strFileName);
          fscanf(File, "%d", &v18->m_pRecord[v9].m_nMapType);
          fscanf(File, "%d", &v18->m_pRecord[v9].m_nMapClass);
          fscanf(File, "%d", &v18->m_pRecord[v9].m_nLayerNum);
          fscanf(File, "%d", &v18->m_pRecord[v9].m_nRaceVillageCode);
          fscanf(File, "%d", &v18->m_pRecord[v9].m_nMonsterSetFileNum);
          fscanf(File, "%d", &v18->m_pRecord[v9].m_nRadius);
          fscanf(File, "%d", &v18->m_pRecord[v9].m_nLevelLimit);
          fscanf(File, "%d", &v18->m_pRecord[v9].m_nUpLevelLim);
          fscanf(File, "%d", &v18->m_pRecord[v9].m_nPotionLim);
          fscanf(File, "%s", v10);
          for ( j = 0; j < 3; ++j )
            v18->m_pRecord[v9].m_nRacePvpUsable[j] = v10[j] - 48;
          v18->m_pRecord[v9].m_dwIndex = v9;
          ++v9;
        }
      }
      fclose(File);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  return result;
}
