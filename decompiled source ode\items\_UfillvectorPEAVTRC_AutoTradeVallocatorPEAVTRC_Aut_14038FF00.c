/*
 * Function: ?_Ufill@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@IEAAPEAPEAVTRC_AutoTrade@@PEAPEAV3@_KAEBQEAV3@@Z
 * Address: 0x14038FF00
 */

TRC_AutoTrade **__fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Ufill(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, TRC_AutoTrade **_Ptr, unsigned __int64 _Count, TRC_AutoTrade *const *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v8; // [sp+30h] [bp+8h]@1
  TRC_AutoTrade **_First; // [sp+38h] [bp+10h]@1
  unsigned __int64 _Counta; // [sp+40h] [bp+18h]@1

  _Counta = _Count;
  _First = _Ptr;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  stdext::unchecked_uninitialized_fill_n<TRC_AutoTrade * *,unsigned __int64,TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(
    _Ptr,
    _Count,
    _Val,
    &v8->_Alval);
  return &_First[_Counta];
}
