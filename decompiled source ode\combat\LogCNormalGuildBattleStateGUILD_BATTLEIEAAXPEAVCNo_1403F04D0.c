/*
 * Function: ?Log@CNormalGuildBattleState@GUILD_BATTLE@@IEAAXPEAVCNormalGuildBattle@2@PEADZZ
 * Address: 0x1403F04D0
 */

void GUILD_BATTLE::CNormalGuildBattleState::Log(GUILD_BATTLE::CNormalGuildBattleState *this, GUILD_BATTLE::CNormalGuildBattle *pkBattle, char *szFormat, ...)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleGuild *v5; // rax@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v6; // rax@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v7; // rax@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v8; // rax@4
  GUILD_BATTLE::CNormalGuildBattleField *v9; // rax@4
  unsigned int v10; // eax@4
  GUILD_BATTLE::CNormalGuildBattleLogger *v11; // rax@4
  __int64 v12; // [sp+0h] [bp-CB8h]@1
  unsigned int v13; // [sp+20h] [bp-C98h]@4
  char *v14; // [sp+28h] [bp-C90h]@4
  unsigned int v15; // [sp+30h] [bp-C88h]@4
  char *v16; // [sp+38h] [bp-C80h]@4
  char Dest[3096]; // [sp+50h] [bp-C68h]@4
  va_list Args; // [sp+C68h] [bp-50h]@4
  int v19; // [sp+C74h] [bp-44h]@4
  char *v20; // [sp+C80h] [bp-38h]@4
  unsigned int v21; // [sp+C88h] [bp-30h]@4
  char *v22; // [sp+C90h] [bp-28h]@4
  unsigned int v23; // [sp+C98h] [bp-20h]@4
  unsigned int v24; // [sp+C9Ch] [bp-1Ch]@4
  unsigned __int64 v25; // [sp+CA0h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleState *v26; // [sp+CC0h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattle *v27; // [sp+CC8h] [bp+10h]@0
  va_list va; // [sp+CD8h] [bp+20h]@1

  va_start(va, szFormat);
  v26 = this;
  v3 = &v12;
  for ( i = 812i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v25 = (unsigned __int64)&v12 ^ _security_cookie;
  Args = (va_list)va;
  v19 = vsprintf(Dest, szFormat, (va_list)va);
  Args = 0i64;
  v5 = GUILD_BATTLE::CNormalGuildBattle::GetBlue(v27);
  v20 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v5);
  v6 = GUILD_BATTLE::CNormalGuildBattle::GetBlue(v27);
  v21 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(v6);
  v7 = GUILD_BATTLE::CNormalGuildBattle::GetRed(v27);
  v22 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v7);
  v8 = GUILD_BATTLE::CNormalGuildBattle::GetRed(v27);
  v23 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(v8);
  v9 = GUILD_BATTLE::CNormalGuildBattle::GetField(v27);
  v24 = GUILD_BATTLE::CNormalGuildBattleField::GetMapID(v9);
  v10 = GUILD_BATTLE::CNormalGuildBattle::GetID(v27);
  v16 = v20;
  v15 = v21;
  v14 = v22;
  v13 = v23;
  sprintf(&Dest[v19], " ( ID:%u Map:%u Red:%u(%s) Blue:%u(%s) )", v10, v24);
  GUILD_BATTLE::CGuildBattleState::Log((GUILD_BATTLE::CGuildBattleState *)&v26->vfptr, Dest);
  v11 = GUILD_BATTLE::CNormalGuildBattle::GetLogger(v27);
  GUILD_BATTLE::CNormalGuildBattleLogger::Log(v11, Dest);
}
