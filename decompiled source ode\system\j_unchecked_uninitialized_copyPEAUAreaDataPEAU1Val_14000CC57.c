/*
 * Function: j_??$unchecked_uninitialized_copy@PEAUAreaData@@PEAU1@V?$allocator@UAreaData@@@std@@@stdext@@YAPEAUAreaData@@PEAU1@00AEAV?$allocator@UAreaData@@@std@@@Z
 * Address: 0x14000CC57
 */

AreaData *__fastcall stdext::unchecked_uninitialized_copy<AreaData *,AreaData *,std::allocator<AreaData>>(AreaData *_First, AreaData *_Last, AreaData *_Dest, std::allocator<AreaData> *_Al)
{
  return stdext::unchecked_uninitialized_copy<AreaData *,AreaData *,std::allocator<AreaData>>(_First, _Last, _Dest, _Al);
}
