/*
 * Function: ?SetReStartFlag@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAA_NK@Z
 * Address: 0x1403E0900
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::SetReStartFlag(GUILD_BATTLE::CNormalGuildBattleGuild *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = GUILD_BATTLE::CNormalGuildBattleGuild::GetMember(v7, dwSerial);
  if ( v6 >= 0 )
  {
    GUILD_BATTLE::CNormalGuildBattleGuildMember::SetReStartFlag(&v7->m_kMember[v6]);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
