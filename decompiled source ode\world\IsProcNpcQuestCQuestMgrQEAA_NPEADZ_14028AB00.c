/*
 * Function: ?IsProcNpcQuest@CQuestMgr@@QEAA_NPEAD@Z
 * Address: 0x14028AB00
 */

char __fastcall CQuestMgr::IsProcNpcQuest(CQuestMgr *this, char *pszCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@4
  _QUEST_DB_BASE *v7; // [sp+28h] [bp-20h]@7
  _base_fld *v8; // [sp+30h] [bp-18h]@8
  CQuestMgr *v9; // [sp+50h] [bp+8h]@1
  const char *Str2; // [sp+58h] [bp+10h]@1

  Str2 = pszCode;
  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 30; ++j )
  {
    v7 = (_QUEST_DB_BASE *)((char *)v9->m_pQuestData + 13 * j);
    if ( v7->m_List[0].byQuestType == 1 )
    {
      v8 = CRecordData::GetRecord(CQuestMgr::s_tblQuest, v7->m_List[0].wIndex);
      if ( !strncmp(v8->m_strCode, Str2, 7ui64) )
        return 1;
    }
  }
  return 0;
}
