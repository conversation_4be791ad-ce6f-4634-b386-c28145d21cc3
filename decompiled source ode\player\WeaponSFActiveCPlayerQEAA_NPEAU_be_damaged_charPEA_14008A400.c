/*
 * Function: ?WeaponSFActive@CPlayer@@QEAA_NPEAU_be_damaged_char@@PEAH1G@Z
 * Address: 0x14008A400
 */

bool __fastcall CPlayer::WeaponSFActive(CPlayer *this, _be_damaged_char *pDamList, int *nDamagedObjNum, int *nShotNum, unsigned __int16 wBulletSerial)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v8; // [sp+0h] [bp-78h]@1
  _skill_fld *pSkillFld; // [sp+20h] [bp-58h]@13
  bool v10; // [sp+40h] [bp-38h]@6
  int nEffectCode; // [sp+44h] [bp-34h]@6
  _skill_fld *v12; // [sp+48h] [bp-30h]@10
  _force_fld *pForceFld; // [sp+50h] [bp-28h]@15
  _skill_fld *v14; // [sp+58h] [bp-20h]@20
  int v15; // [sp+60h] [bp-18h]@6
  CPlayer *v16; // [sp+80h] [bp+8h]@1
  _be_damaged_char *pDamLista; // [sp+88h] [bp+10h]@1
  int *nDamagedObjNuma; // [sp+90h] [bp+18h]@1
  int *nShotNuma; // [sp+98h] [bp+20h]@1

  nShotNuma = nShotNum;
  nDamagedObjNuma = nDamagedObjNum;
  pDamLista = pDamList;
  v16 = this;
  v5 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( pDamList )
  {
    v10 = 0;
    nEffectCode = v16->m_pmWpn.nActiveType;
    v15 = nEffectCode;
    if ( nEffectCode )
    {
      if ( v15 == 1 )
      {
        pForceFld = (_force_fld *)CRecordData::GetRecordByHash(
                                    &stru_1799C8410 + nEffectCode,
                                    v16->m_pmWpn.strActiveCode_key,
                                    0,
                                    4);
        if ( pForceFld )
        {
          if ( pForceFld->m_bAttackable )
            v10 = CPlayer::pc_WPActiveAttack_Force(v16, pDamLista, nDamagedObjNuma, pForceFld);
          else
            v10 = CPlayer::WPActiveForce(v16, pDamLista, *nDamagedObjNuma, pForceFld);
        }
      }
      else if ( v15 == 2 )
      {
        v14 = (_skill_fld *)CRecordData::GetRecordByHash(
                              &stru_1799C8410 + nEffectCode,
                              v16->m_pmWpn.strActiveCode_key,
                              0,
                              5);
        if ( v14 )
        {
          if ( v14->m_nAttackable <= 0 )
          {
            LODWORD(pSkillFld) = nEffectCode;
            v10 = CPlayer::WPActiveSkill(v16, pDamLista, *nDamagedObjNuma, v14, nEffectCode);
          }
          else
          {
            v10 = CPlayer::pc_WPActiveAttack_Skill(
                    v16,
                    pDamLista,
                    nDamagedObjNuma,
                    nShotNuma,
                    v14,
                    nEffectCode,
                    wBulletSerial);
          }
        }
      }
    }
    else
    {
      v12 = (_skill_fld *)CRecordData::GetRecordByHash(&stru_1799C8410, v16->m_pmWpn.strActiveCode_key, 0, 5);
      if ( v12 )
      {
        if ( v12->m_nAttackable <= 0 )
        {
          LODWORD(pSkillFld) = nEffectCode;
          v10 = CPlayer::WPActiveSkill(v16, pDamLista, *nDamagedObjNuma, v12, nEffectCode);
        }
        else
        {
          v10 = CPlayer::pc_WPActiveAttack_Skill(
                  v16,
                  pDamLista,
                  nDamagedObjNuma,
                  nShotNuma,
                  v12,
                  nEffectCode,
                  wBulletSerial);
        }
      }
    }
    result = v10;
  }
  else
  {
    result = 0;
  }
  return result;
}
