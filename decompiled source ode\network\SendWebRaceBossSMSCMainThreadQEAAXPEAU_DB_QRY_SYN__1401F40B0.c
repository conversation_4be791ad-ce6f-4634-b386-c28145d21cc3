/*
 * Function: ?SendWebRaceBossSMS@CMainThread@@QEAAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1401F40B0
 */

void __fastcall CMainThread::SendWebRaceBossSMS(CMainThread *this, _DB_QRY_SYN_DATA *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CRaceBossMsgController *v4; // rax@6
  __int64 v5; // [sp+0h] [bp-58h]@1
  char *pwszMsg; // [sp+20h] [bp-38h]@6
  unsigned int dwWebSendDBID; // [sp+28h] [bp-30h]@6
  char *v8; // [sp+30h] [bp-28h]@4
  char *v9; // [sp+38h] [bp-20h]@6
  char *wszName; // [sp+40h] [bp-18h]@6

  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = pData->m_sData;
  if ( pData->m_byResult )
    *((_DWORD *)v8 + 69) = 0;
  v9 = v8 + 4;
  wszName = v8 + 259;
  v4 = CRaceBossMsgController::Instance();
  dwWebSendDBID = *(_DWORD *)v8;
  pwszMsg = v9;
  CRaceBossMsgController::Send(v4, v8[280], *((_DWORD *)v8 + 69), wszName, v9, dwWebSendDBID);
}
