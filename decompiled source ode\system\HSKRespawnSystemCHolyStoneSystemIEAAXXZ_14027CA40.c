/*
 * Function: ?HSKRespawnSystem@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x14027CA40
 */

void __fastcall CHolyStoneSystem::HSKRespawnSystem(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char v3; // al@7
  char v4; // al@8
  COreAmountMgr *v5; // rax@16
  COreAmountMgr *v6; // rax@16
  COreAmountMgr *v7; // rax@16
  COreAmountMgr *v8; // rax@16
  __int64 v9; // [sp+0h] [bp-48h]@1
  int nChangeReason; // [sp+20h] [bp-28h]@7
  _trand_tbl *v11; // [sp+30h] [bp-18h]@4
  int j; // [sp+38h] [bp-10h]@11
  CHolyStoneSystem *v13; // [sp+50h] [bp+8h]@1

  v13 = this;
  v1 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v13->m_SaveData.m_dwPassTimeInScene += CMyTimer::GetTerm(&v13->m_tmrHSKSystem);
  v11 = _GetTransTBL(v13->m_SaveData.m_nSceneCode);
  if ( v11 && GetLoopTime() >= v13->m_dwCheckTime[v11->nCheckTimeIndex] )
  {
    if ( v11->nNextState == 4 )
    {
      v3 = CHolyStoneSystem::GetNumOfTime(v13);
      nChangeReason = 4;
      CHolyStoneSystem::SetScene(v13, v3, v11->nNextState, 0, 4);
    }
    else
    {
      v4 = CHolyStoneSystem::GetNumOfTime(v13);
      nChangeReason = 0;
      CHolyStoneSystem::SetScene(v13, v4, v11->nNextState, 0, 0);
    }
  }
  if ( CMyTimer::CountingTimer(&v13->m_tmrCumPlayer) )
  {
    v13->m_SaveData.m_dwCumPlayerNum += CPlayer::s_nLiveNum;
    ++v13->m_SaveData.m_dwCumCount;
  }
  for ( j = 0; j < 3; ++j )
  {
    if ( g_Stone[j].m_bOper )
      v13->m_SaveData.m_nStoneHP_Buffer[j] = ((int (__fastcall *)(struct CHolyStone *))g_Stone[j].vfptr->GetHP)(&g_Stone[j]);
  }
  v5 = COreAmountMgr::Instance();
  v13->m_SaveData.m_dwOreRemainAmount = COreAmountMgr::GetRemainOre(v5);
  v6 = COreAmountMgr::Instance();
  v13->m_SaveData.m_dwOreTotalAmount = COreAmountMgr::GetTotalOre(v6);
  v7 = COreAmountMgr::Instance();
  v13->m_SaveData.m_byOreTransferCount = COreAmountMgr::GetOreTransferCount(v7);
  v8 = COreAmountMgr::Instance();
  v13->m_SaveData.m_dwOreTransferAmount = COreAmountMgr::GetOreTransferAmount(v8);
  CHolyStoneSystemDataMgr::SaveStateData(&v13->m_SaveData);
}
