/*
 * Function: ?DropItem@CHolyStone@@QEAAXXZ
 * Address: 0x140137FA0
 */

void __fastcall CHolyStone::DropItem(CHolyStone *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-158h]@1
  float *pNewPos; // [sp+20h] [bp-138h]@33
  bool bHide; // [sp+28h] [bp-130h]@33
  CPlayer *pAttacker; // [sp+30h] [bp-128h]@33
  int bHolyScanner; // [sp+38h] [bp-120h]@33
  char byEventItemLootAuth; // [sp+40h] [bp-118h]@33
  int v9; // [sp+50h] [bp-108h]@4
  _base_fld *v10; // [sp+58h] [bp-100h]@9
  unsigned __int16 v11; // [sp+60h] [bp-F8h]@11
  unsigned __int16 v12; // [sp+64h] [bp-F4h]@11
  char v13; // [sp+68h] [bp-F0h]@16
  int v14; // [sp+6Ch] [bp-ECh]@16
  unsigned int v15; // [sp+70h] [bp-E8h]@16
  unsigned int v16; // [sp+74h] [bp-E4h]@16
  _base_fld *v17; // [sp+78h] [bp-E0h]@16
  __int64 v18; // [sp+80h] [bp-D8h]@18
  __int64 v19; // [sp+88h] [bp-D0h]@18
  int v20; // [sp+90h] [bp-C8h]@18
  int j; // [sp+94h] [bp-C4h]@18
  float pStdPos; // [sp+A8h] [bp-B0h]@24
  int v23; // [sp+ACh] [bp-ACh]@24
  int v24; // [sp+B0h] [bp-A8h]@24
  CMapData *pMap; // [sp+C8h] [bp-90h]@24
  int v26; // [sp+D0h] [bp-88h]@25
  char v27; // [sp+D4h] [bp-84h]@28
  char v28; // [sp+D5h] [bp-83h]@28
  unsigned int v29; // [sp+D8h] [bp-80h]@30
  _STORAGE_LIST::_db_con pItem; // [sp+E8h] [bp-70h]@30
  _TimeItem_fld *v31; // [sp+128h] [bp-30h]@30
  __time32_t Time; // [sp+134h] [bp-24h]@32
  int v33; // [sp+144h] [bp-14h]@16
  CHolyStone *v34; // [sp+160h] [bp+8h]@1

  v34 = this;
  v1 = &v3;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = 0;
  if ( v34->m_nCurrLootIndex == -1 )
  {
    CMyTimer::StopTimer(&v34->m_tmrDropTime);
  }
  else
  {
    while ( v34->m_nCurrLootIndex <= v34->m_nEndLootIndex )
    {
      v10 = CRecordData::GetRecord(&stru_1799C6638, v34->m_nCurrLootIndex);
      if ( v10 && *(_DWORD *)&v10[1].m_strCode[8] > 0 )
      {
        v11 = 400;
        v12 = *(_WORD *)&v10[1].m_strCode[4];
        v12 *= v34->m_wMagnifications;
        if ( (signed int)v34->m_wRange > 100 )
          v11 = v34->m_wRange;
        v12 += v34->m_wAddCountWithPlayer;
        while ( v34->m_nCurrDropIndex < v12 )
        {
          v13 = 0;
          v14 = rand();
          v33 = v14 << 16;
          v15 = rand() + v33;
          v16 = 0;
          v17 = 0i64;
          if ( v34->m_pRecordSet )
          {
            v17 = v34->m_pRecordSet;
            v16 = v10[1].m_dwIndex;
            if ( v15 < v16 )
            {
              v18 = 0i64;
              v19 = 0i64;
              v20 = 0;
              for ( j = 0; j < 10; ++j )
              {
                v20 = rand() % *(_DWORD *)&v10[1].m_strCode[8];
                v19 = *(_QWORD *)(unk_1799C66F0 + 8i64 * v10->m_dwIndex) + 8i64 * v20;
                if ( *(_DWORD *)(v19 + 4) )
                {
                  v13 = 1;
                  break;
                }
              }
              if ( v13 )
              {
                pStdPos = 0.0;
                v23 = 0;
                v24 = 0;
                pMap = v34->m_pCurMap;
                if ( CMapData::GetRandPosVirtualDumExcludeStdRange(pMap, v34->m_fCurPos, v11, 100, &pStdPos) )
                {
                  v26 = 0;
                  if ( IsOverLapItem(*(_BYTE *)v19) )
                    v26 = 1;
                  else
                    v26 = GetItemDurPoint(*(_BYTE *)v19, *(_WORD *)(v19 + 2));
                  v27 = GetDefItemUpgSocketNum(*(_BYTE *)v19, *(_WORD *)(v19 + 2));
                  v28 = 0;
                  if ( (signed int)(unsigned __int8)v27 > 0 )
                    v28 = rand() % (unsigned __int8)v27 + 1;
                  v29 = GetBitAfterSetLimSocket(v28);
                  _STORAGE_LIST::_db_con::_db_con(&pItem);
                  pItem.m_byTableCode = *(_BYTE *)v19;
                  pItem.m_wItemIndex = *(_WORD *)(v19 + 2);
                  pItem.m_dwDur = (unsigned int)v26;
                  pItem.m_dwLv = v29;
                  v31 = TimeItem::FindTimeRec(*(_BYTE *)v19, *(_WORD *)(v19 + 2));
                  if ( v31 && v31->m_nCheckType )
                  {
                    _time32(&Time);
                    pItem.m_byCsMethod = v31->m_nCheckType;
                    pItem.m_dwT = v31->m_nUseTime + Time;
                    pItem.m_dwLendRegdTime = Time;
                  }
                  byEventItemLootAuth = 3;
                  bHolyScanner = 0;
                  pAttacker = 0i64;
                  bHide = 0;
                  pNewPos = &pStdPos;
                  if ( CreateItemBox(&pItem, 4, pMap, v34->m_wMapLayerIndex, &pStdPos, 0, 0i64, 0, 3) )
                  {
                    ++v9;
                    CLogFile::Write(
                      &stru_1799C95A8,
                      "Cristal Event Item >> %s, %d",
                      &v10[1].m_strCode[8 * v20 + 12],
                      pItem.m_dwDur);
                  }
                  if ( v9 >= v34->m_wDropCntOnce )
                    return;
                }
              }
            }
          }
          ++v34->m_nCurrDropIndex;
        }
        v34->m_nCurrDropIndex = 0;
      }
      ++v34->m_nCurrLootIndex;
    }
    v34->m_nCurrLootIndex = -1;
    CMyTimer::StopTimer(&v34->m_tmrDropTime);
  }
}
