/*
 * Function: j_?CheckReRegist@CUnmannedTraderUserInfo@@AEAAEEPEAVCLogFile@@GEEGKKPEAE11PEAK2@Z
 * Address: 0x14000CC39
 */

char __fastcall CUnmannedTraderUserInfo::CheckReRegist(CUnmannedTraderUserInfo *this, char byType, CLogFile *pkLogger, unsigned __int16 wItemSerial, char byAmount, char byItemTableCode, unsigned __int16 wItemIndex, unsigned int dwRegistSerial, unsigned int dwPrice, char *pbyDivision, char *pbyClass, char *pbySubClass, unsigned int *pdwTax, unsigned int *pdwListIndex)
{
  return CUnmannedTraderUserInfo::CheckReRegist(
           this,
           byType,
           pkLogger,
           wItemSerial,
           byAmount,
           byItemTableCode,
           wItemIndex,
           dwRegistSerial,
           dwPrice,
           pbyDivision,
           pbyClass,
           pbySubClass,
           pdwTax,
           pdwListIndex);
}
