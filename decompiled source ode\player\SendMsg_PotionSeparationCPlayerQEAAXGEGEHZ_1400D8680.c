/*
 * Function: ?SendMsg_PotionSeparation@CPlayer@@QEAAXGEGEH@Z
 * Address: 0x1400D8680
 */

void __fastcall CPlayer::SendMsg_PotionSeparation(CPlayer *this, unsigned __int16 wParentSerial, char byParentAmount, unsigned __int16 wChildSerial, char byChildAmount, int nRet)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  unsigned __int16 v10; // [sp+35h] [bp-43h]@4
  char v11; // [sp+37h] [bp-41h]@4
  unsigned __int16 v12; // [sp+38h] [bp-40h]@4
  char v13; // [sp+3Ah] [bp-3Eh]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v15; // [sp+55h] [bp-23h]@4
  CPlayer *v16; // [sp+80h] [bp+8h]@1

  v16 = this;
  v6 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10 = wParentSerial;
  v11 = byParentAmount;
  v12 = wChildSerial;
  v13 = byChildAmount;
  szMsg = nRet;
  pbyType = 13;
  v15 = 13;
  CNetProcess::LoadSendMsg(unk_1414F2088, v16->m_ObjID.m_wIndex, &pbyType, &szMsg, 7u);
}
