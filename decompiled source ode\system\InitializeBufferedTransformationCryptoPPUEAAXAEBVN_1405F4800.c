/*
 * Function: ?Initialize@BufferedTransformation@CryptoPP@@UEAAXAEBVNameValuePairs@2@H@Z
 * Address: 0x1405F4800
 */

void __fastcall CryptoPP::BufferedTransformation::Initialize(CryptoPP::BufferedTransformation *this, const struct CryptoPP::NameValuePairs *a2)
{
  __int64 v2; // rax@1
  CryptoPP::BufferedTransformation *v3; // [sp+30h] [bp+8h]@1
  const struct CryptoPP::NameValuePairs *v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = this;
  LODWORD(v2) = ((int (*)(void))this->vfptr[20].Clone)();
  if ( v2 )
    _wassert(L"!AttachedTransformation()", L"D:\\RF Project\\RF_Server64\\28 Crypto++\\cryptlib.cpp", 0xDFu);
  ((void (__fastcall *)(CryptoPP::BufferedTransformation *, const struct CryptoPP::NameValuePairs *))v3->vfptr[3].Clone)(
    v3,
    v4);
}
