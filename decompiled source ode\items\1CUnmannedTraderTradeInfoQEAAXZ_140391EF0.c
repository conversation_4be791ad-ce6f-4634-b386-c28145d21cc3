/*
 * Function: ??1CUnmannedTraderTradeInfo@@QEAA@XZ
 * Address: 0x140391EF0
 */

void __fastcall CUnmannedTraderTradeInfo::~CUnmannedTraderTradeInfo(CUnmannedTraderTradeInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  CMyTimer *v5; // [sp+20h] [bp-28h]@4
  CMyTimer *v6; // [sp+28h] [bp-20h]@4
  __int64 v7; // [sp+30h] [bp-18h]@5
  CUnmannedTraderTradeInfo *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = v8->m_pkTimer;
  v5 = v6;
  if ( v6 )
  {
    LODWORD(v3) = ((int (__fastcall *)(CMyTimer *, signed __int64))v5->vfptr->__vecDelDtor)(v5, 1i64);
    v7 = v3;
  }
  else
  {
    v7 = 0i64;
  }
  v8->m_pkTimer = 0i64;
  v8->m_ui64TotalOldIncome = 0i64;
  v8->m_ui64TotalCurrentIncome = 0i64;
}
