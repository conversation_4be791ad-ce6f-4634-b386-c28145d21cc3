/*
 * Function: ??1?$simple_ptr@VDL_SignatureMessageEncodingMethod_NR@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x14063C2A0
 */

int __fastcall CryptoPP::simple_ptr<CryptoPP::DL_SignatureMessageEncodingMethod_NR>::~simple_ptr<CryptoPP::DL_SignatureMessageEncodingMethod_NR>(_QWORD *a1)
{
  int (__fastcall ***v1)(_QWORD, _QWORD); // rax@1

  v1 = (int (__fastcall ***)(_QWORD, _QWORD))*a1;
  if ( *a1 )
    LODWORD(v1) = (**(int (__fastcall ***)(int (__fastcall ***)(_QWORD, _QWORD), signed __int64))*a1)(
                    (int (__fastcall ***)(_QWORD, _QWORD))*a1,
                    1i64);
  return (unsigned __int64)v1;
}
