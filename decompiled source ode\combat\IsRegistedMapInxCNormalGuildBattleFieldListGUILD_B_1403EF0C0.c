/*
 * Function: ?IsRegistedMapInx@CNormalGuildBattleFieldList@GUILD_BATTLE@@AEAAHEQEAPEAD@Z
 * Address: 0x1403EF0C0
 */

signed __int64 __fastcall GUILD_BATTLE::CNormalGuildBattleFieldList::IsRegistedMapInx(GUILD_BATTLE::CNormalGuildBattleFieldList *this, char byRace, char **szParseBuff)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v5; // eax@8
  __int64 v7; // [sp+0h] [bp-38h]@1
  int v8; // [sp+20h] [bp-18h]@4
  unsigned __int8 j; // [sp+24h] [bp-14h]@4
  unsigned __int8 k; // [sp+25h] [bp-13h]@6
  GUILD_BATTLE::CNormalGuildBattleFieldList *v11; // [sp+40h] [bp+8h]@1
  char v12; // [sp+48h] [bp+10h]@1
  char **v13; // [sp+50h] [bp+18h]@1

  v13 = szParseBuff;
  v12 = byRace;
  v11 = this;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = 0;
  for ( j = 0; j < (signed int)(unsigned __int8)v12; ++j )
  {
    for ( k = 0; k < (signed int)v11->m_byUseFieldCnt[j]; ++k )
    {
      v8 = atoi(v13[k]);
      v5 = GUILD_BATTLE::CNormalGuildBattleField::GetMapID(v11->m_ppkUseFieldByRace[j][k]);
      if ( v5 == v8 )
        return (unsigned int)v8;
    }
  }
  return 0xFFFFFFFFi64;
}
