/*
 * Function: ??A?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEAAAEAPEAVCMoveMapLimitInfo@@_K@Z
 * Address: 0x1403A7950
 */

CMoveMapLimitInfo **__fastcall std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::operator[](std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, unsigned __int64 _Pos)
{
  return &this->_Myfirst[_Pos];
}
