/*
 * Function: ?SendMsg_BuyItemStoreResult@CPlayer@@QEAAXPEAVCItemStore@@HPEAU_buy_offer@@E@Z
 * Address: 0x1400D70C0
 */

void __usercall CPlayer::SendMsg_BuyItemStoreResult(CPlayer *this@<rcx>, CItemStore *pStore@<rdx>, int nOfferNum@<r8d>, _buy_offer *pCard@<r9>, double a5@<xmm0>, char byErrCode)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v8; // ax@14
  __int64 v9; // [sp+0h] [bp-3C8h]@1
  _buy_store_success_zocl v10; // [sp+40h] [bp-388h]@5
  int k; // [sp+334h] [bp-94h]@5
  int l; // [sp+338h] [bp-90h]@8
  int m; // [sp+33Ch] [bp-8Ch]@11
  char pbyType; // [sp+344h] [bp-84h]@14
  char v15; // [sp+345h] [bp-83h]@14
  char szMsg[4]; // [sp+368h] [bp-60h]@15
  unsigned int v17; // [sp+36Ch] [bp-5Ch]@15
  double v18; // [sp+370h] [bp-58h]@15
  int v19[3]; // [sp+378h] [bp-50h]@17
  char v20; // [sp+384h] [bp-44h]@18
  int j; // [sp+394h] [bp-34h]@15
  char v22; // [sp+3A4h] [bp-24h]@18
  char v23; // [sp+3A5h] [bp-23h]@18
  CPlayer *v24; // [sp+3D0h] [bp+8h]@1
  CItemStore *v25; // [sp+3D8h] [bp+10h]@1
  int v26; // [sp+3E0h] [bp+18h]@1
  _buy_offer *v27; // [sp+3E8h] [bp+20h]@1

  v27 = pCard;
  v26 = nOfferNum;
  v25 = pStore;
  v24 = this;
  v6 = &v9;
  for ( i = 240i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( byErrCode )
  {
    *(_DWORD *)szMsg = CPlayerDB::GetDalant(&v24->m_Param);
    v17 = CPlayerDB::GetGold(&v24->m_Param);
    CPvpOrderView::GetPvpCash(&v24->m_kPvpOrderView);
    v18 = a5;
    for ( j = 0; j < 3; ++j )
      v19[j] = CUserDB::GetActPoint(v24->m_pUserDB, j);
    v20 = byErrCode;
    v22 = 12;
    v23 = 3;
    CNetProcess::LoadSendMsg(unk_1414F2088, v24->m_ObjID.m_wIndex, &v22, szMsg, 0x1Du);
  }
  else
  {
    _buy_store_success_zocl::_buy_store_success_zocl(&v10);
    v10.dwLeftDalant = CPlayerDB::GetDalant(&v24->m_Param);
    v10.dwLeftGold = CPlayerDB::GetGold(&v24->m_Param);
    v10.dwConsumDanlant = CItemStore::GetLastTradeDalant(v25);
    v10.dwConsumGold = CItemStore::GetLastTradeGold(v25);
    CPvpOrderView::GetPvpCash(&v24->m_kPvpOrderView);
    v10.dwLeftPoint = (signed int)floor(a5);
    v10.dwConsumPoint = CItemStore::GetLastTradePoint(v25);
    for ( k = 0; k < 3; ++k )
      v10.dwConsumActPoint[k] = CItemStore::GetLastTradeActPoint(v25, k);
    for ( l = 0; l < 3; ++l )
      v10.dwLeftActPoint[l] = CUserDB::GetActPoint(v24->m_pUserDB, l);
    _effect_parameter::GetEff_Have(&v24->m_EP, 1);
    v10.byDiscountRate = 100 * (signed int)ffloor(*(float *)&a5);
    v10.byBuyNum = v26;
    for ( m = 0; m < v26; ++m )
    {
      v10.OfferList[m].wSerial = v27[m].wSerial;
      v10.OfferList[m].byCsMethod = v27[m].Item.m_byCsMethod;
      v10.OfferList[m].dwT = v27[m].Item.m_dwT;
    }
    pbyType = 12;
    v15 = 2;
    v8 = _buy_store_success_zocl::size(&v10);
    CNetProcess::LoadSendMsg(unk_1414F2088, v24->m_ObjID.m_wIndex, &pbyType, (char *)&v10, v8);
  }
}
