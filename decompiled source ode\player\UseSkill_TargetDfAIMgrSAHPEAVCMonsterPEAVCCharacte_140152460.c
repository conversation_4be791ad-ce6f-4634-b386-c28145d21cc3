/*
 * Function: ?UseSkill_Target@DfAIMgr@@SAHPEAVCMonster@@PEAVCCharacter@@PEAVCMonsterSkill@@@Z
 * Address: 0x140152460
 */

signed __int64 __fastcall DfAIMgr::UseSkill_Target(CMonster *pMon, CCharacter *pTarget, CMonsterSkill *pSkill)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CMonster *v7; // [sp+30h] [bp+8h]@1
  CCharacter *pDst; // [sp+38h] [bp+10h]@1
  CMonsterSkill *pskill; // [sp+40h] [bp+18h]@1

  pskill = pSkill;
  pDst = pTarget;
  v7 = pMon;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( !v7 || !pTarget || !pSkill )
    return 0i64;
  if ( !CMonsterSkill::GetType(pSkill) )
    return 0i64;
  if ( CMonsterSkill::GetUseType(pskill) )
  {
    if ( CMonsterSkill::GetUseType(pskill) != 1 && CMonsterSkill::GetUseType(pskill) != 2 )
    {
      if ( CMonsterSkill::GetUseType(pskill) == 3 && CMonster::AssistSF(v7, pDst, pskill) )
        return 1i64;
    }
    else if ( CMonster::AssistSF(v7, pDst, pskill) )
    {
      return 1i64;
    }
  }
  else if ( CMonster::Attack(v7, pDst, pskill) )
  {
    return 1i64;
  }
  return 0i64;
}
