/*
 * Function: ?dev_item_make_no_use_matrial@CPlayer@@QEAA_N_N@Z
 * Address: 0x1400BB470
 */

char __fastcall CPlayer::dev_item_make_no_use_matrial(CPlayer *this, bool noUsingMatrial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v6->m_bCheat_makeitem_no_use_matrial == noUsingMatrial )
  {
    result = 0;
  }
  else
  {
    v6->m_bCheat_makeitem_no_use_matrial = noUsingMatrial;
    CPlayer::SendMsg_MakeItemCheatSendButtonEnable(v6, v6->m_bCheat_makeitem_no_use_matrial);
    result = 1;
  }
  return result;
}
