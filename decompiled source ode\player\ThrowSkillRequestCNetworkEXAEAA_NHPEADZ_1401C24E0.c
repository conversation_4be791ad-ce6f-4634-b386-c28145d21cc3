/*
 * Function: ?ThrowSkillRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C24E0
 */

char __fastcall CNetworkEX::ThrowSkillRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@9
  __int64 v7; // [sp+0h] [bp-38h]@1
  char *v8; // [sp+20h] [bp-18h]@4
  CPlayer *v9; // [sp+28h] [bp-10h]@4
  CNetworkEX *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = pBuf;
  v9 = &g_Player + n;
  if ( !v9->m_bOper || v9->m_pmTrd.bDTradeMode || v9->m_bCorpse )
  {
    result = 1;
  }
  else if ( CMainThread::GetObjectA(&g_Main, 0, (unsigned __int8)v8[2], *(_WORD *)(v8 + 3)) )
  {
    CPlayer::pc_ThrowSkillRequest(v9, *(_WORD *)v8, (_CHRID *)(v8 + 2), (unsigned __int16 *)v8 + 5);
    result = 1;
  }
  else
  {
    v6 = CPlayerDB::GetCharNameA(&v9->m_Param);
    CLogFile::Write(
      &v10->m_LogFile,
      "odd.. %s: ThrowSkillRequest()..  if(!g_Main.GetObject(obj_kind_char, pRecv->idDst.byID, pRecv->idDst.wIndex))",
      v6);
    result = 0;
  }
  return result;
}
