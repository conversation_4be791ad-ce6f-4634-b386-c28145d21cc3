/*
 * Function: ?_db_complete_update_event_classrefine@CMainThread@@QEAAXGK@Z
 * Address: 0x1401BFD30
 */

void __fastcall CMainThread::_db_complete_update_event_classrefine(CMainThread *this, unsigned __int16 wSock, unsigned int dwAvatorSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // rax@4
  __int64 v6; // [sp+0h] [bp-48h]@1

  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  LODWORD(v5) = (*(int (__fastcall **)(_QWORD *, _QWORD, _QWORD))(*qword_1799C9AF0 + 64i64))(
                  qword_1799C9AF0,
                  wSock,
                  dwAvatorSerial);
  *(_BYTE *)(v5 + 8) = 0;
}
