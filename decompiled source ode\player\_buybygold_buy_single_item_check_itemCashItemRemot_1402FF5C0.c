/*
 * Function: ?_buybygold_buy_single_item_check_item@CashItemRemoteStore@@AEAA?AW4CS_RCODE@@PEAVCPlayer@@PEAU__item@_request_csi_buy_clzo@@PEAU_param_cashitem_dblog@@AEAPEAU_CashShop_fld@@@Z
 * Address: 0x1402FF5C0
 */

signed __int64 __fastcall CashItemRemoteStore::_buybygold_buy_single_item_check_item(CashItemRemoteStore *this, CPlayer *pOne, _request_csi_buy_clzo::__item *pSrc, _param_cashitem_dblog *pSheet, _CashShop_fld **pCsFld)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  unsigned int v8; // eax@20
  char v9; // al@25
  __int64 v10; // [sp+0h] [bp-38h]@1
  unsigned int v11; // [sp+20h] [bp-18h]@20
  int v12; // [sp+24h] [bp-14h]@20
  CS_RCODE v13; // [sp+28h] [bp-10h]@25
  CashItemRemoteStore *v14; // [sp+40h] [bp+8h]@1
  CPlayer *v15; // [sp+48h] [bp+10h]@1
  _request_csi_buy_clzo::__item *pCsItem; // [sp+50h] [bp+18h]@1
  _param_cashitem_dblog *v17; // [sp+58h] [bp+20h]@1

  v17 = pSheet;
  pCsItem = pSrc;
  v15 = pOne;
  v14 = this;
  v5 = &v10;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( pSrc->byEventType == 1 && !pSheet->in_bAdjustDiscount )
    return 18i64;
  if ( pSrc->byEventType == 2 && !pSheet->in_bSetDiscount )
    return 18i64;
  if ( pSrc->byEventType == 3 && !pSheet->in_bOneN_One )
    return 18i64;
  if ( pSrc->byEventType == 5 && !pSheet->in_bLimited_Sale )
    return 22i64;
  *pCsFld = (_CashShop_fld *)CRecordData::GetRecord(&v14->_kRecGoods, pSrc->wStoreIdx);
  if ( !*pCsFld )
    return 11i64;
  if ( v17->in_bLimited_Sale && pCsItem->byEventType == 5 )
  {
    v11 = 0;
    v12 = 0;
    v12 = pCsItem->byDiscount * pCsItem->nPrice / 100;
    v11 = pCsItem->nPrice - v12;
    v8 = CPlayerDB::GetGold(&v15->m_Param);
    if ( v8 < v11 )
      return 5i64;
  }
  else if ( CPlayerDB::GetGold(&v15->m_Param) < (*pCsFld)->m_nCsPrice )
  {
    return 5i64;
  }
  v9 = CPlayerDB::GetRaceSexCode(&v15->m_Param);
  v13 = CashItemRemoteStore::_check_buyitem(v14, v9, pCsItem, *pCsFld);
  if ( v13 )
    result = (unsigned int)v13;
  else
    result = 0i64;
  return result;
}
