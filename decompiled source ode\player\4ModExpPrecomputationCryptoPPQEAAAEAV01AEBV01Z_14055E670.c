/*
 * Function: ??4ModExpPrecomputation@CryptoPP@@QEAAAEAV01@AEBV01@@Z
 * Address: 0x14055E670
 */

__int64 __fastcall CryptoPP::ModExpPrecomputation::operator=(__int64 a1, __int64 a2)
{
  __int64 v3; // [sp+30h] [bp+8h]@1
  __int64 v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::DL_GroupPrecomputation<CryptoPP::Integer>::operator=();
  CryptoPP::value_ptr<CryptoPP::MontgomeryRepresentation>::operator=(v3 + 8, v4 + 8);
  return v3;
}
