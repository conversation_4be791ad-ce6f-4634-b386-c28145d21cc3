/*
 * Function: ?UILockInfo_Init@CUserDB@@QEAAXPEAD@Z
 * Address: 0x140118340
 */

void __fastcall CUserDB::UILockInfo_Init(CUserDB *this, char *pMsg)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-88h]@1
  char *v5; // [sp+30h] [bp-58h]@5
  char szMsg; // [sp+44h] [bp-44h]@5
  char v7; // [sp+45h] [bp-43h]@5
  char pbyType; // [sp+64h] [bp-24h]@14
  char v9; // [sp+65h] [bp-23h]@14
  CUserDB *v10; // [sp+90h] [bp+8h]@1

  v10 = this;
  v2 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pMsg )
  {
    v5 = pMsg;
    szMsg = 0;
    memset(&v7, 0, sizeof(v7));
    if ( v10->m_byUILock )
    {
      szMsg = 11;
    }
    else if ( *v5 )
    {
      if ( *v5 == 1 )
      {
        szMsg = 11;
      }
      else if ( *v5 == 2 )
      {
        szMsg = 12;
      }
      else
      {
        szMsg = 15;
      }
    }
    else
    {
      szMsg = 0;
      v7 = v5[16];
      v10->m_byUILock = 1;
      strcpy_0(v10->m_szUILock_PW, v5 + 3);
      v10->m_byUILock_HintIndex = v5[16];
      strcpy_0(v10->m_uszUILock_HintAnswer, v5 + 17);
    }
    pbyType = 13;
    v9 = -128;
    CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_idWorld.wIndex, &pbyType, &szMsg, 2u);
  }
}
