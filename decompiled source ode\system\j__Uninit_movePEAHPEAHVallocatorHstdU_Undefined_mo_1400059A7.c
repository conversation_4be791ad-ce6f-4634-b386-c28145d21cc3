/*
 * Function: j_??$_Uninit_move@PEAHPEAHV?$allocator@H@std@@U_Undefined_move_tag@2@@std@@YAPEAHPEAH00AEAV?$allocator@H@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400059A7
 */

int *__fastcall std::_Uninit_move<int *,int *,std::allocator<int>,std::_Undefined_move_tag>(int *_First, int *_Last, int *_Dest, std::allocator<int> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<int *,int *,std::allocator<int>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
