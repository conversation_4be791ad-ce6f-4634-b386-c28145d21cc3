/*
 * Function: ??0PK_DefaultEncryptionFilter@CryptoPP@@QEAA@AEAVRandomNumberGenerator@1@AEBVPK_Encryptor@1@PEAVBufferedTransformation@1@AEBVNameValuePairs@1@@Z
 * Address: 0x1405F6A50
 */

CryptoPP::PK_DefaultEncryptionFilter *__fastcall CryptoPP::PK_DefaultEncryptionFilter::PK_DefaultEncryptionFilter(CryptoPP::PK_DefaultEncryptionFilter *this, struct CryptoPP::RandomNumberGenerator *a2, const struct CryptoPP::PK_Encryptor *a3, struct CryptoPP::BufferedTransformation *a4, const struct CryptoPP::NameValuePairs *a5)
{
  CryptoPP::PK_DefaultEncryptionFilter *v6; // [sp+40h] [bp+8h]@1
  struct CryptoPP::RandomNumberGenerator *v7; // [sp+48h] [bp+10h]@1
  const struct CryptoPP::PK_Encryptor *v8; // [sp+50h] [bp+18h]@1
  struct CryptoPP::BufferedTransformation *v9; // [sp+58h] [bp+20h]@1

  v9 = a4;
  v8 = a3;
  v7 = a2;
  v6 = this;
  CryptoPP::Unflushable<CryptoPP::Filter>::Unflushable<CryptoPP::Filter>((__int64)this);
  *(_QWORD *)v6 = &CryptoPP::PK_DefaultEncryptionFilter::`vftable'{for `CryptoPP::Algorithm'};
  *((_QWORD *)v6 + 1) = &CryptoPP::PK_DefaultEncryptionFilter::`vftable'{for `CryptoPP::Waitable'};
  *((_QWORD *)v6 + 6) = v7;
  *((_QWORD *)v6 + 7) = v8;
  *((_QWORD *)v6 + 8) = a5;
  CryptoPP::ByteQueue::ByteQueue((CryptoPP::ByteQueue *)((char *)v6 + 72), 0i64);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    (CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)((char *)v6 + 152),
    0i64);
  CryptoPP::Filter::Detach(v6, v9);
  return v6;
}
