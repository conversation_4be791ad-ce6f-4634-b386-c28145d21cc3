/*
 * Function: _stdext::hash_map_ScheduleMSG_____ptr64_unsigned_long_stdext::hash_compare_ScheduleMSG_____ptr64_std::less_ScheduleMSG_____ptr64____std::allocator_std::pair_ScheduleMSG_____ptr64_const_unsigned_long_____::operator[]_::_1_::dtor$2
 * Address: 0x140420820
 */

void __fastcall stdext::hash_map_ScheduleMSG_____ptr64_unsigned_long_stdext::hash_compare_ScheduleMSG_____ptr64_std::less_ScheduleMSG_____ptr64____std::allocator_std::pair_ScheduleMSG_____ptr64_const_unsigned_long_____::operator[]_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::pair<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,bool>::~pair<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,bool>((std::pair<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0>,bool> *)(a2 + 120));
}
