/*
 * Function: j_??4?$_Ranit@VCUnmannedTraderSchedule@@_JPEBV1@AEBV1@@std@@QEAAAEAU01@AEBU01@@Z
 * Address: 0x14000B6D1
 */

std::_<PERSON>t<CUnmannedTraderSchedule,__int64,CUnmannedTraderSchedule const *,CUnmannedTraderSchedule const &> *__fastcall std::_Ranit<CUnmannedTraderSchedule,__int64,CUnmannedTraderSchedule const *,CUnmannedTraderSchedule const &>::operator=(std::_<PERSON>t<CUnmannedTraderSchedule,__int64,CUnmannedTraderSchedule const *,CUnmannedTraderSchedule const &> *this, std::_Ranit<CUnmannedTraderSchedule,__int64,CUnmannedTraderSchedule const *,CUnmannedTraderSchedule const &> *__that)
{
  return std::_<PERSON>t<CUnmannedTraderSchedule,__int64,CUnmannedTraderSchedule const *,CUnmannedTraderSchedule const &>::operator=(
           this,
           __that);
}
