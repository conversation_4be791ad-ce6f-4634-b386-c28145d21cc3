/*
 * Function: ?GetGenAttackProb@CTrap@@UEAAHPEAVCCharacter@@H_N@Z
 * Address: 0x14013F290
 */

__int64 __fastcall CTrap::GetGenAttackProb(CTrap *this, CCharacter *pDst, int nPart, bool bBackAttack)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed int v6; // eax@4
  int v7; // eax@4
  __int64 v9; // [sp+0h] [bp-48h]@1
  unsigned int v10; // [sp+20h] [bp-28h]@4
  double v11; // [sp+28h] [bp-20h]@4
  float v12; // [sp+30h] [bp-18h]@4
  int v13; // [sp+34h] [bp-14h]@5
  unsigned int v14; // [sp+38h] [bp-10h]@8
  CTrap *v15; // [sp+50h] [bp+8h]@1
  CCharacter *v16; // [sp+58h] [bp+10h]@1
  bool v17; // [sp+68h] [bp+20h]@1

  v17 = bBackAttack;
  v16 = pDst;
  v15 = this;
  v4 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = (double)*(signed int *)&v15->m_pRecordSet[5].m_strCode[40];
  v12 = (float)((int (__fastcall *)(CCharacter *))pDst->vfptr->GetLevel)(pDst) * 10.0;
  v6 = ((int (__fastcall *)(CCharacter *, _QWORD))v16->vfptr->GetDefSkill)(v16, v17);
  v10 = (signed int)floor(v11 - (float)(v12 + (float)v6) / 4.0 + 95.0);
  v7 = ((int (__fastcall *)(CCharacter *))v16->vfptr->GetAvoidRate)(v16);
  v10 -= v7;
  if ( (signed int)v10 <= 5 )
    v13 = 5;
  else
    v13 = v10;
  v10 = v13;
  if ( v13 >= 95 )
    v14 = 95;
  else
    v14 = v10;
  return v14;
}
