/*
 * Function: ?SendMsg_BackTowerResult@CPlayer@@QEAAXEGG@Z
 * Address: 0x1400E06A0
 */

void __fastcall CPlayer::SendMsg_BackTowerResult(CPlayer *this, char byErrCode, unsigned __int16 wItemSerial, unsigned __int16 wLeftHP)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  unsigned __int16 v8; // [sp+35h] [bp-43h]@4
  unsigned __int16 v9; // [sp+37h] [bp-41h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v11; // [sp+55h] [bp-23h]@4
  CPlayer *v12; // [sp+80h] [bp+8h]@1

  v12 = this;
  v4 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = byErrCode;
  v8 = wItemSerial;
  v9 = wLeftHP;
  pbyType = 17;
  v11 = 21;
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, &szMsg, 5u);
}
