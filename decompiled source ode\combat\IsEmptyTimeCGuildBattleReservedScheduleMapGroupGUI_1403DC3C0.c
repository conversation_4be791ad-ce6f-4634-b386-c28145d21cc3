/*
 * Function: ?IsEmptyTime@CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAAEIKK@Z
 * Address: 0x1403DC3C0
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::IsEmptyTime(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this, unsigned int uiFieldInx, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v8->m_uiMapCnt > uiFieldInx )
  {
    if ( v8->m_ppkReservedSchedule )
      result = GUILD_BATTLE::CGuildBattleReservedSchedule::IsEmptyTime(
                 v8->m_ppkReservedSchedule[uiFieldInx],
                 dwStartTimeInx,
                 dwElapseTimeCnt);
    else
      result = 110;
  }
  else
  {
    result = 120;
  }
  return result;
}
