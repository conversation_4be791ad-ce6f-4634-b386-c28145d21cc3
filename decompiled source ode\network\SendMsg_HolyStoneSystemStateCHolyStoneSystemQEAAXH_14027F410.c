/*
 * Function: ?SendMsg_HolyStoneSystemState@CHolyStoneSystem@@QEAAXH@Z
 * Address: 0x14027F410
 */

void __fastcall CHolyStoneSystem::SendMsg_HolyStoneSystemState(CHolyStoneSystem *this, int nPlayerIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  CHolyStoneSystem *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = v6->m_SaveData.m_nSceneCode;
  switch ( v5 )
  {
    case 0:
      CHolyStoneSystem::SendMsg_WaitStone(v6, nPlayerIndex);
      break;
    case 1:
      CHolyStoneSystem::SendMsg_EnterStone(v6, nPlayerIndex);
      break;
    case 2:
      CHolyStoneSystem::SendMsg_WaitKeeper(v6, nPlayerIndex, 0);
      break;
    case 3:
      CHolyStoneSystem::SendMsg_EnterKeeper(v6, nPlayerIndex);
      break;
    case 4:
      CHolyStoneSystem::SendMsg_EnterKeeper(v6, nPlayerIndex);
      break;
    case 5:
      CHolyStoneSystem::SendMsg_WaitKeeper(v6, nPlayerIndex, 1);
      break;
    case 6:
      CHolyStoneSystem::SendMsg_EnterKeeper(v6, nPlayerIndex);
      break;
    default:
      return;
  }
}
