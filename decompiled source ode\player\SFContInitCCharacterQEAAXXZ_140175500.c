/*
 * Function: ?SFContInit@CCharacter@@QEAAXXZ
 * Address: 0x140175500
 */

void __fastcall CCharacter::SFContInit(CCharacter *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  int j; // [sp+30h] [bp-18h]@4
  int k; // [sp+34h] [bp-14h]@6
  CCharacter *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 2; ++j )
  {
    for ( k = 0; k < 8; ++k )
    {
      if ( v6->m_SFCont[j][k].m_bExist )
        CCharacter::RemoveSFContEffect(v6, j, k, 1, 0);
      if ( v6->m_SFContAura[j][k].m_bExist )
        CCharacter::RemoveSFContEffect(v6, j, k, 1, 1);
    }
  }
  v6->m_bLastContEffectUpdate = 0;
  v6->m_wLastContEffect = -1;
  CCharacter::SendMsg_LastEffectChangeInform(v6);
}
