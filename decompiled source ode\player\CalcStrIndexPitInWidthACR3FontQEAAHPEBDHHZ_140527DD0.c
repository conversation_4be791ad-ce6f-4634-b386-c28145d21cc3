/*
 * Function: ?CalcStrIndexPitInWidthA@CR3Font@@QEAAHPEBDHH@Z
 * Address: 0x140527DD0
 */

__int64 __fastcall CR3Font::CalcStrIndexPitInWidthA(CR3Font *this, const char *a2, int a3, int a4)
{
  HDC v4; // rcx@1
  tagSIZE Size; // [sp+40h] [bp-18h]@1
  int nFit; // [sp+60h] [bp+8h]@1

  v4 = (HDC)*((_QWORD *)this + 15);
  nFit = 0;
  GetTextExtentExPointA(v4, a2, a4, a3, &nFit, 0i64, &Size);
  return (unsigned int)nFit;
}
