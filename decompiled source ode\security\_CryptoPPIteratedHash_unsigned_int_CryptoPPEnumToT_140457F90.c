/*
 * Function: _CryptoPP::IteratedHash_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_CryptoPP::HashTransformation_::IteratedHash_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_CryptoPP::HashTransformation__::_1_::dtor$0_0
 * Address: 0x140457F90
 */

void __fastcall CryptoPP::IteratedHash_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_CryptoPP::HashTransformation_::IteratedHash_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_CryptoPP::HashTransformation__::_1_::dtor_0_0(__int64 a1, __int64 a2)
{
  CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation>::~IteratedHashBase<unsigned int,CryptoPP::HashTransformation>(*(CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation> **)(a2 + 64));
}
