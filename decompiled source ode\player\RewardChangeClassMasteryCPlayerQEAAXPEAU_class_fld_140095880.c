/*
 * Function: ?RewardChangeClassMastery@CPlayer@@QEAAXPEAU_class_fld@@@Z
 * Address: 0x140095880
 */

void __usercall CPlayer::RewardChangeClassMastery(CPlayer *this@<rcx>, _class_fld *pClassFld@<rdx>, long double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@4
  int v6; // ecx@4
  long double v7; // xmm0_8@4
  int v8; // eax@49
  int v9; // ecx@49
  __int64 v10; // [sp+0h] [bp-118h]@1
  char byReason[8]; // [sp+20h] [bp-F8h]@4
  unsigned int dwExpRate[2]; // [sp+28h] [bp-F0h]@4
  int nGrade; // [sp+30h] [bp-E8h]@4
  int *pnMaxPoint; // [sp+38h] [bp-E0h]@4
  _MASTERY_PARAM *pData; // [sp+40h] [bp-D8h]@4
  unsigned int *pdwAlter; // [sp+48h] [bp-D0h]@4
  char *pszFileName; // [sp+50h] [bp-C8h]@4
  char byLogType; // [sp+58h] [bp-C0h]@4
  char *pszTitle; // [sp+60h] [bp-B8h]@4
  int j; // [sp+70h] [bp-A8h]@4
  int v21; // [sp+74h] [bp-A4h]@29
  int k; // [sp+78h] [bp-A0h]@29
  int v23; // [sp+7Ch] [bp-9Ch]@31
  unsigned int dwAlter; // [sp+80h] [bp-98h]@33
  int v25; // [sp+84h] [bp-94h]@37
  int v26; // [sp+88h] [bp-90h]@44
  unsigned int v27; // [sp+8Ch] [bp-8Ch]@45
  char *v28; // [sp+90h] [bp-88h]@4
  unsigned int *v29; // [sp+98h] [bp-80h]@4
  _MASTERY_PARAM *v30; // [sp+A0h] [bp-78h]@4
  int *v31; // [sp+A8h] [bp-70h]@4
  int v32; // [sp+B0h] [bp-68h]@4
  long double v33; // [sp+B8h] [bp-60h]@4
  _DWORD *v34; // [sp+C0h] [bp-58h]@33
  _DWORD *v35; // [sp+C8h] [bp-50h]@33
  char *v36; // [sp+D0h] [bp-48h]@49
  char *v37; // [sp+D8h] [bp-40h]@49
  unsigned int *v38; // [sp+E0h] [bp-38h]@49
  _MASTERY_PARAM *v39; // [sp+E8h] [bp-30h]@49
  int *v40; // [sp+F0h] [bp-28h]@49
  int v41; // [sp+F8h] [bp-20h]@49
  long double v42; // [sp+100h] [bp-18h]@49
  CPlayer *v43; // [sp+120h] [bp+8h]@1
  _class_fld *v44; // [sp+128h] [bp+10h]@1

  v44 = pClassFld;
  v43 = this;
  v3 = &v10;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v28 = v43->m_szLvHistoryFileName;
  v29 = v43->m_Param.m_dwAlterMastery;
  v30 = &v43->m_pmMst;
  v31 = v43->m_nMaxPoint;
  v32 = v43->m_Param.m_byPvPGrade;
  CPlayerDB::GetExp(&v43->m_Param);
  v33 = a3;
  v5 = CPlayerDB::GetLevel(&v43->m_Param);
  v6 = v43->m_ObjID.m_wIndex;
  pszTitle = 0i64;
  byLogType = 0;
  pszFileName = v28;
  pdwAlter = v29;
  pData = v30;
  pnMaxPoint = v31;
  nGrade = v32;
  dwExpRate[0] = v43->m_dwExpRate;
  v7 = v33;
  *(long double *)byReason = v33;
  CMgrAvatorLvHistory::update_mastery(
    &CPlayer::s_MgrLvHistory,
    v6,
    v43->m_byUserDgr,
    v5,
    v33,
    dwExpRate[0],
    v32,
    v31,
    v30,
    v29,
    v28,
    0,
    0i64);
  CPlayerDB::InitAlterMastery(&v43->m_Param);
  for ( j = 0; j < 2; ++j )
  {
    if ( v44->m_MasteryLim.m_nBnsMMastery[j] )
    {
      LOBYTE(nGrade) = 0;
      *(_QWORD *)dwExpRate = "Player::RewardChangeClassMastery()---0";
      byReason[0] = 1;
      CPlayer::Emb_AlterStat(
        v43,
        0,
        j,
        v44->m_MasteryLim.m_nBnsMMastery[j],
        1,
        "Player::RewardChangeClassMastery()---0",
        0);
    }
  }
  if ( v44->m_MasteryLim.m_nBnsSMastery )
  {
    LOBYTE(nGrade) = 0;
    *(_QWORD *)dwExpRate = "Player::RewardChangeClassMastery()---1";
    byReason[0] = 1;
    CPlayer::Emb_AlterStat(v43, 6, 0, v44->m_MasteryLim.m_nBnsSMastery, 1, "Player::RewardChangeClassMastery()---1", 0);
  }
  if ( v44->m_MasteryLim.m_nBnsDefMastery )
  {
    LOBYTE(nGrade) = 0;
    *(_QWORD *)dwExpRate = "Player::RewardChangeClassMastery()---2";
    byReason[0] = 1;
    CPlayer::Emb_AlterStat(
      v43,
      1,
      0,
      v44->m_MasteryLim.m_nBnsDefMastery,
      1,
      "Player::RewardChangeClassMastery()---2",
      0);
  }
  if ( v44->m_MasteryLim.m_nBnsPryMastery )
  {
    LOBYTE(nGrade) = 0;
    *(_QWORD *)dwExpRate = "Player::RewardChangeClassMastery()---3";
    byReason[0] = 1;
    CPlayer::Emb_AlterStat(
      v43,
      2,
      0,
      v44->m_MasteryLim.m_nBnsPryMastery,
      1,
      "Player::RewardChangeClassMastery()---3",
      0);
  }
  for ( j = 0; j < 3; ++j )
  {
    if ( v44->m_MasteryLim.m_nBnsMakeMastery[j] )
    {
      LOBYTE(nGrade) = 0;
      *(_QWORD *)dwExpRate = "Player::RewardChangeClassMastery()---4";
      byReason[0] = 1;
      CPlayer::Emb_AlterStat(
        v43,
        5,
        j,
        v44->m_MasteryLim.m_nBnsMakeMastery[j],
        1,
        "Player::RewardChangeClassMastery()---4",
        0);
    }
  }
  for ( j = 0; j < 24; ++j )
  {
    if ( v44->m_MasteryLim.m_nBnsForceMastery[j] )
    {
      LOBYTE(nGrade) = 0;
      *(_QWORD *)dwExpRate = "Player::RewardChangeClassMastery()---5";
      byReason[0] = 1;
      CPlayer::Emb_AlterStat(
        v43,
        4,
        j,
        v44->m_MasteryLim.m_nBnsForceMastery[j],
        1,
        "Player::RewardChangeClassMastery()---5",
        0);
    }
  }
  for ( j = 0; j < 8; ++j )
  {
    if ( v44->m_MasteryLim.m_nBnsSkillMastery[j] )
    {
      v21 = 0;
      for ( k = 0; k < CPlayer::s_SkillIndexPerMastery[49 * j]; ++k )
      {
        v23 = *(&CPlayer::s_SkillIndexPerMastery[49 * j + 1] + k);
        v21 += v43->m_pmMst.m_BaseCum.m_dwSkillCum[v23];
      }
      if ( v21 )
      {
        for ( k = 0; k < CPlayer::s_SkillIndexPerMastery[49 * j]; ++k )
        {
          v26 = *(&CPlayer::s_SkillIndexPerMastery[49 * j + 1] + k);
          if ( v43->m_pmMst.m_BaseCum.m_dwSkillCum[v26] )
          {
            *(float *)&v7 = (float)((float)((float)(signed int)v43->m_pmMst.m_BaseCum.m_dwSkillCum[v26] / (float)v21)
                                  * (float)v44->m_MasteryLim.m_nBnsSkillMastery[j])
                          + 1.0;
            v27 = (signed int)ffloor(*(float *)&v7);
            if ( (signed int)v27 > 0 )
              CPlayer::Emb_AlterStat(v43, 3, v26, v27, 1, "Player::RewardChangeClassMastery()---7", 0);
          }
        }
      }
      else
      {
        v34 = CPlayer::s_SkillIndexPerMastery;
        dwAlter = v44->m_MasteryLim.m_nBnsSkillMastery[j] / CPlayer::s_SkillIndexPerMastery[49 * j];
        v35 = CPlayer::s_SkillIndexPerMastery;
        if ( v44->m_MasteryLim.m_nBnsSkillMastery[j] % CPlayer::s_SkillIndexPerMastery[49 * j] )
          ++dwAlter;
        for ( k = 0; k < CPlayer::s_SkillIndexPerMastery[49 * j]; ++k )
        {
          v25 = *(&CPlayer::s_SkillIndexPerMastery[49 * j + 1] + k);
          if ( (signed int)dwAlter > 0 )
            CPlayer::Emb_AlterStat(v43, 3, v25, dwAlter, 1, "Player::RewardChangeClassMastery()---6", 0);
        }
      }
    }
  }
  v36 = v44->m_strCode;
  v37 = v43->m_szLvHistoryFileName;
  v38 = v43->m_Param.m_dwAlterMastery;
  v39 = &v43->m_pmMst;
  v40 = v43->m_nMaxPoint;
  v41 = v43->m_Param.m_byPvPGrade;
  CPlayerDB::GetExp(&v43->m_Param);
  v42 = v7;
  v8 = CPlayerDB::GetLevel(&v43->m_Param);
  v9 = v43->m_ObjID.m_wIndex;
  pszTitle = v36;
  byLogType = 3;
  pszFileName = v37;
  pdwAlter = v38;
  pData = v39;
  pnMaxPoint = v40;
  nGrade = v41;
  dwExpRate[0] = v43->m_dwExpRate;
  *(long double *)byReason = v42;
  CMgrAvatorLvHistory::update_mastery(
    &CPlayer::s_MgrLvHistory,
    v9,
    v43->m_byUserDgr,
    v8,
    v42,
    dwExpRate[0],
    v41,
    v40,
    v39,
    v38,
    v37,
    3,
    v36);
  CPlayerDB::InitAlterMastery(&v43->m_Param);
  v43->m_dwUMWHLastTime = GetLoopTime();
}
