/*
 * Function: j_??$_Destroy_range@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@YAXPEAPEAVCUnmannedTraderSortType@@0AEAV?$allocator@PEAVCUnmannedTraderSortType@@@0@@Z
 * Address: 0x14000A137
 */

void __fastcall std::_Destroy_range<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(CUnmannedTraderSortType **_First, CUnmannedTraderSortType **_Last, std::allocator<CUnmannedTraderSortType *> *_Al)
{
  std::_Destroy_range<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(_First, _Last, _Al);
}
