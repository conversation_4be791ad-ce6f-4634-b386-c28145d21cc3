/*
 * Function: ?UpdateTodayTable@CWeeklyGuildRankManager@@AEAAHPEADPEAU_pvppoint_guild_rank_info@@@Z
 * Address: 0x1402CE250
 */

signed __int64 __fastcall CWeeklyGuildRankManager::UpdateTodayTable(CWeeklyGuildRankManager *this, char *szDate, _pvppoint_guild_rank_info *pkInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  __int64 v6; // [sp+0h] [bp-168h]@1
  char v7; // [sp+20h] [bp-148h]@4
  char DstBuf; // [sp+40h] [bp-128h]@4
  char v9; // [sp+144h] [bp-24h]@8
  unsigned __int64 v10; // [sp+150h] [bp-18h]@4
  CWeeklyGuildRankManager *v11; // [sp+170h] [bp+8h]@1
  char *szDatea; // [sp+178h] [bp+10h]@1
  _pvppoint_guild_rank_info *Dst; // [sp+180h] [bp+18h]@1

  Dst = pkInfo;
  szDatea = szDate;
  v11 = this;
  v3 = &v6;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = (unsigned __int64)&v6 ^ _security_cookie;
  v7 = 0;
  sprintf_s(&DstBuf, 0xFFui64, "tbl_PvpPointGuildRank%s", szDate);
  if ( !CRFNewDatabase::TableExist((CRFNewDatabase *)&pkDB->vfptr, &DstBuf) )
  {
    if ( !CWeeklyGuildRankManager::CreatePvpPointGuildRank(v11, szDatea) )
      return 0xFFFFFFFFi64;
    v7 = 1;
  }
  memset_0(Dst, 0, 0x6D70ui64);
  v9 = CRFWorldDatabase::Select_PvpPointGuildRank(pkDB, szDatea, Dst);
  if ( v9 == 2 )
  {
    result = 2i64;
  }
  else if ( v9 == 1 )
  {
    result = 4294967294i64;
  }
  else
  {
    result = v7 != 0;
  }
  return result;
}
