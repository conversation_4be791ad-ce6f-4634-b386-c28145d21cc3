/*
 * Function: ?BuyByGold@CashItemRemoteStore@@AEAA_NGPEAD@Z
 * Address: 0x1402FECC0
 */

char __fastcall CashItemRemoteStore::BuyByGold(CashItemRemoteStore *this, unsigned __int16 wSock, char *pPacket)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  unsigned int v6; // eax@7
  __int64 v7; // [sp+0h] [bp-568h]@1
  CPlayer *pOne; // [sp+40h] [bp-528h]@4
  _request_csi_buy_clzo *pRecv; // [sp+48h] [bp-520h]@7
  _param_cashitem_dblog pSheet; // [sp+60h] [bp-508h]@7
  int eCode; // [sp+1D4h] [bp-394h]@7
  _result_csi_buy_zocl Dst; // [sp+1F0h] [bp-378h]@9
  _request_csi_buy_clzo::__item *pSrc; // [sp+528h] [bp-40h]@9
  bool v14; // [sp+534h] [bp-34h]@9
  int j; // [sp+544h] [bp-24h]@9
  char v16; // [sp+548h] [bp-20h]@8
  char v17; // [sp+549h] [bp-1Fh]@12
  char v18; // [sp+54Ah] [bp-1Eh]@14
  __int64 v19; // [sp+550h] [bp-18h]@4
  CashItemRemoteStore *v20; // [sp+570h] [bp+8h]@1
  unsigned __int16 v21; // [sp+578h] [bp+10h]@1

  v21 = wSock;
  v20 = this;
  v3 = &v7;
  for ( i = 344i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v19 = -2i64;
  pOne = &g_Player + wSock;
  if ( pOne->m_bOper && pOne->m_bLive )
  {
    pRecv = (_request_csi_buy_clzo *)pPacket;
    v6 = CPlayerDB::GetCharSerial(&pOne->m_Param);
    _param_cashitem_dblog::_param_cashitem_dblog(&pSheet, v6);
    CashItemRemoteStore::_buybygold_set_cashitem_dblog_sheet(v20, pOne, &pSheet);
    eCode = CashItemRemoteStore::_buybygold_check_valid(v20, pOne, pRecv, &pSheet);
    if ( eCode )
    {
      ICsSendInterface::SendMsg_Error(v21, eCode);
      v16 = 1;
      _param_cashitem_dblog::~_param_cashitem_dblog(&pSheet);
      result = v16;
    }
    else
    {
      _result_csi_buy_zocl::_result_csi_buy_zocl(&Dst);
      memset_0(&Dst, 0, 0x326ui64);
      pSrc = 0i64;
      v14 = 0;
      for ( j = 0; j < pRecv->nNum; ++j )
      {
        pSrc = &pRecv->item[j];
        eCode = CashItemRemoteStore::_buybygold_buy_single_item(v20, pOne, pRecv, pSrc, &pSheet, &v14, &Dst);
        if ( eCode )
        {
          ICsSendInterface::SendMsg_Error(v21, eCode);
          v17 = 1;
          _param_cashitem_dblog::~_param_cashitem_dblog(&pSheet);
          return v17;
        }
      }
      CashItemRemoteStore::_buybygold_complete(v20, pOne, &Dst, pRecv, pSrc, &pSheet, v14);
      v18 = 1;
      _param_cashitem_dblog::~_param_cashitem_dblog(&pSheet);
      result = v18;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
