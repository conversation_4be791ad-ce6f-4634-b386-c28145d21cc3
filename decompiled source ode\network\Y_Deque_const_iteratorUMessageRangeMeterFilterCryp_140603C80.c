/*
 * Function: ??Y?$_Deque_const_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@QEAAAEAV01@_J@Z
 * Address: 0x140603C80
 */

__int64 __fastcall std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+=(__int64 a1, __int64 a2)
{
  *(_QWORD *)(a1 + 24) += a2;
  return a1;
}
