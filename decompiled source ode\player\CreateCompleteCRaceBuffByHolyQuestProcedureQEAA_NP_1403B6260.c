/*
 * Function: ?CreateComplete@CRaceBuffByHolyQuestProcedure@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x1403B6260
 */

bool __fastcall CRaceBuffByHolyQuestProcedure::CreateComplete(CRaceBuffByHolyQuestProcedure *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  char v5; // al@7
  unsigned int v6; // eax@11
  __int64 v7; // [sp+0h] [bp-38h]@1
  int iType; // [sp+20h] [bp-18h]@7
  bool v9; // [sp+24h] [bp-14h]@7
  CRaceBuffByHolyQuestProcedure *v10; // [sp+40h] [bp+8h]@1
  CPlayer *pkDest; // [sp+48h] [bp+10h]@1

  pkDest = pkPlayer;
  v10 = this;
  v2 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pkPlayer && pkPlayer->m_bOper )
  {
    v9 = CPlayer::IsHaveMentalTicket(pkPlayer);
    v5 = CPlayerDB::GetRaceCode(&pkDest->m_Param);
    iType = CRaceBuffHolyQuestResultInfo::GetResultType(&v10->m_kBuffHolyQestResultInfo, v5, v9);
    if ( iType >= 0 )
    {
      if ( CPlayer::IsUseReleaseRaceBuffPotion(pkDest) )
      {
        result = 0;
      }
      else
      {
        v6 = CRaceBuffHolyQuestResultInfo::GetContinueCnt(&v10->m_kBuffHolyQestResultInfo, iType);
        result = CRaceBuffInfoByHolyQuestList::CreateComplete(&v10->m_kBuffInfo, v6, iType, pkDest);
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
