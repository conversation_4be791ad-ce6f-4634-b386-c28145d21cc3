/*
 * Function: ??0__list@_qry_case_post_send@@QEAA@XZ
 * Address: 0x1403285E0
 */

void __fastcall _qry_case_post_send::__list::__list(_qry_case_post_send::__list *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _qry_case_post_send::__list *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _INVENKEY::_INVENKEY(&v4->key);
}
