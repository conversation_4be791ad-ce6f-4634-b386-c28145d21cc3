/*
 * Function: ??0table@lua_tinker@@QEAA@PEAUlua_State@@@Z
 * Address: 0x1404463D0
 */

void __fastcall lua_tinker::table::table(lua_tinker::table *this, struct lua_State *L)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@5
  __int64 v5; // rax@5
  __int64 v6; // [sp+0h] [bp-48h]@1
  lua_tinker::table_obj *v7; // [sp+20h] [bp-28h]@7
  lua_tinker::table_obj *v8; // [sp+28h] [bp-20h]@4
  __int64 v9; // [sp+30h] [bp-18h]@4
  lua_tinker::table_obj *v10; // [sp+38h] [bp-10h]@5
  lua_tinker::table *v11; // [sp+50h] [bp+8h]@1
  struct lua_State *La; // [sp+58h] [bp+10h]@1

  La = L;
  v11 = this;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = -2i64;
  lua_createtable(L, 0i64, 0i64);
  v8 = (lua_tinker::table_obj *)operator new(0x20ui64);
  if ( v8 )
  {
    v4 = lua_gettop(La);
    lua_tinker::table_obj::table_obj(v8, La, v4);
    v10 = (lua_tinker::table_obj *)v5;
  }
  else
  {
    v10 = 0i64;
  }
  v7 = v10;
  v11->m_obj = v10;
  lua_tinker::table_obj::inc_ref(v11->m_obj);
}
