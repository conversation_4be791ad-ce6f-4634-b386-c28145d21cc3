/*
 * Function: ?ProcCheckGetGravityStone@CNormalGuildBattleManager@GUILD_BATTLE@@IEAAEGKKK@Z
 * Address: 0x1403D52C0
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleManager::ProcCheckGetGravityStone(GUILD_BATTLE::CNormalGuildBattleManager *this, unsigned __int16 wIndex, unsigned int dwObjSerial, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CNormalGuildBattle *v9; // [sp+20h] [bp-18h]@6
  GUILD_BATTLE::CNormalGuildBattleManager *v10; // [sp+40h] [bp+8h]@1
  unsigned __int16 v11; // [sp+48h] [bp+10h]@1
  unsigned int dwObjSeriala; // [sp+50h] [bp+18h]@1

  dwObjSeriala = dwObjSerial;
  v11 = wIndex;
  v10 = this;
  v5 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( dwGuildSerial == -1 )
  {
    result = -115;
  }
  else
  {
    v9 = 0i64;
    v9 = GUILD_BATTLE::CNormalGuildBattleManager::GetBattleByGuildSerial(v10, dwGuildSerial);
    if ( v9 )
    {
      if ( GUILD_BATTLE::CNormalGuildBattle::IsInBattleRegenState(v9) )
        result = GUILD_BATTLE::CNormalGuildBattle::GetGravityStone(v9, v11, dwObjSeriala, dwCharacSerial);
      else
        result = -112;
    }
    else
    {
      result = -114;
    }
  }
  return result;
}
