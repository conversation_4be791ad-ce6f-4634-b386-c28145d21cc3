/*
 * Function: ?Init@CNationCodeStrTable@@QEAA_NXZ
 * Address: 0x14020AC40
 */

char __fastcall CNationCodeStrTable::Init(CNationCodeStrTable *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  CNationCodeStrTable *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = CNationCodeStrTable::RegistCode(v6);
  if ( v5 )
    MyMessageBox("CNationCodeStrTable::Init()", "Regist NationCode(%d) Fail!", (unsigned int)v5);
  return 1;
}
