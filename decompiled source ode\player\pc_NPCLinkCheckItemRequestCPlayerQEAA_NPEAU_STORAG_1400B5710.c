/*
 * Function: ?pc_NPCLinkCheckItemRequest@CPlayer@@QEAA_NPEAU_STORAGE_POS_INDIV@@@Z
 * Address: 0x1400B5710
 */

char __fastcall CPlayer::pc_NPCLinkCheckItemRequest(CPlayer *this, _STORAGE_POS_INDIV *pStorage)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  CPlayer *v7; // [sp+40h] [bp+8h]@1
  _STORAGE_POS_INDIV *pStoragea; // [sp+48h] [bp+10h]@1

  pStoragea = pStorage;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = CPlayer::pc_NPCLinkCheckItemRequest_Use(v7, pStorage);
  CPlayer::SendMsg_NPCLinkItemCheckResult(v7, v6, pStoragea);
  return 1;
}
