/*
 * Function: ?SendMsg_QueryPacket_Info@CGuild@@QEAAXH@Z
 * Address: 0x1402559B0
 */

void __fastcall CGuild::SendMsg_QueryPacket_Info(CGuild *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-58h]@1
  unsigned __int16 nLen; // [sp+20h] [bp-38h]@4
  char pbyType; // [sp+34h] [bp-24h]@4
  char v7; // [sp+35h] [bp-23h]@4
  CGuild *v8; // [sp+60h] [bp+8h]@1

  v8 = this;
  v2 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  pbyType = 27;
  v7 = 34;
  nLen = 43;
  CNetProcess::LoadSendMsg(unk_1414F2088, n, &pbyType, (char *)v8->m_QueryPacket_Info, 0x2Bu);
}
