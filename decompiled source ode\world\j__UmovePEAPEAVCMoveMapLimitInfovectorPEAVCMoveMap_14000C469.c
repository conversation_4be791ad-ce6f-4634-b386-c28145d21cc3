/*
 * Function: j_??$_Umove@PEAPEAVCMoveMapLimitInfo@@@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@IEAAPEAPEAVCMoveMapLimitInfo@@PEAPEAV2@00@Z
 * Address: 0x14000C469
 */

CMoveMapLimitInfo **__fastcall std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Umove<CMoveMapLimitInfo * *>(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, CMoveMapLimitInfo **_Ptr)
{
  return std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Umove<CMoveMapLimitInfo * *>(
           this,
           _First,
           _Last,
           _Ptr);
}
