/*
 * Function: ?Update_INI@CashItemRemoteStore@@QEAAXPEAU_cash_event_ini@@E@Z
 * Address: 0x1402F8C40
 */

void __fastcall CashItemRemoteStore::Update_INI(CashItemRemoteStore *this, _cash_event_ini *pNewIni, char byEventType)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v5; // eax@5
  __int64 v6; // [sp+0h] [bp-E8h]@1
  int v7; // [sp+20h] [bp-C8h]@5
  int v8; // [sp+28h] [bp-C0h]@5
  int v9; // [sp+30h] [bp-B8h]@5
  int v10; // [sp+38h] [bp-B0h]@5
  int v11; // [sp+40h] [bp-A8h]@5
  int v12; // [sp+48h] [bp-A0h]@5
  int v13; // [sp+50h] [bp-98h]@5
  int v14; // [sp+58h] [bp-90h]@5
  int v15; // [sp+60h] [bp-88h]@5
  int v16; // [sp+68h] [bp-80h]@5
  int v17; // [sp+70h] [bp-78h]@5
  int v18; // [sp+78h] [bp-70h]@5
  int v19; // [sp+80h] [bp-68h]@5
  int v20; // [sp+88h] [bp-60h]@5
  int v21; // [sp+90h] [bp-58h]@5
  int v22; // [sp+98h] [bp-50h]@5
  int v23; // [sp+A0h] [bp-48h]@5
  int v24; // [sp+A8h] [bp-40h]@5
  int j; // [sp+B0h] [bp-38h]@7
  CLogFile *v26; // [sp+B8h] [bp-30h]@5
  CashItemRemoteStore *v27; // [sp+F0h] [bp+8h]@1
  _cash_event_ini *v28; // [sp+F8h] [bp+10h]@1
  char v29; // [sp+100h] [bp+18h]@1

  v29 = byEventType;
  v28 = pNewIni;
  v27 = this;
  v3 = &v6;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pNewIni )
  {
    v24 = pNewIni->m_byMinute[1];
    v23 = pNewIni->m_byHour[1];
    v22 = pNewIni->m_byDay[1];
    v21 = pNewIni->m_byMonth[1];
    v20 = pNewIni->m_wYear[1];
    v19 = pNewIni->m_byMinute[0];
    v18 = pNewIni->m_byHour[0];
    v17 = pNewIni->m_byDay[0];
    v16 = pNewIni->m_byMonth[0];
    v15 = pNewIni->m_wYear[0];
    v14 = v27->m_cash_event[(unsigned __int8)byEventType].m_ini.m_byMinute[1];
    v13 = v27->m_cash_event[(unsigned __int8)byEventType].m_ini.m_byHour[1];
    v12 = v27->m_cash_event[(unsigned __int8)byEventType].m_ini.m_byDay[1];
    v11 = v27->m_cash_event[(unsigned __int8)byEventType].m_ini.m_byMonth[1];
    v10 = v27->m_cash_event[(unsigned __int8)byEventType].m_ini.m_wYear[1];
    v9 = v27->m_cash_event[(unsigned __int8)byEventType].m_ini.m_byMinute[0];
    v8 = v27->m_cash_event[(unsigned __int8)byEventType].m_ini.m_byHour[0];
    v7 = v27->m_cash_event[(unsigned __int8)byEventType].m_ini.m_byDay[0];
    CLogFile::Write(
      &v27->m_cash_event[(unsigned __int8)byEventType].m_event_log,
      "Updated INI File : [%d/%d/%d %d:%d ~ %d/%d/%d %d:%d] -> [%d/%d/%d %d:%d ~ %d/%d/%d %d:%d",
      v27->m_cash_event[(unsigned __int8)byEventType].m_ini.m_wYear[0],
      v27->m_cash_event[(unsigned __int8)byEventType].m_ini.m_byMonth[0]);
    v5 = v27->m_cash_event[(unsigned __int8)v29].m_ini.m_byRepeatDay;
    v26 = &v27->m_cash_event[(unsigned __int8)v29].m_event_log;
    CLogFile::Write(
      v26,
      "Repeat Information >> Repeat:%d , RepeatDay:%d",
      v27->m_cash_event[(unsigned __int8)v29].m_ini.m_bRepeat,
      v5);
    v9 = v27->m_cash_event[(unsigned __int8)v29].m_ini.m_byMinute[2];
    v8 = v27->m_cash_event[(unsigned __int8)v29].m_ini.m_byHour[2];
    v7 = v27->m_cash_event[(unsigned __int8)v29].m_ini.m_byDay[2];
    CLogFile::Write(
      &v27->m_cash_event[(unsigned __int8)v29].m_event_log,
      "Expire Date >> [%d/%d/%d %d:%d]",
      v27->m_cash_event[(unsigned __int8)v29].m_ini.m_wYear[2],
      v27->m_cash_event[(unsigned __int8)v29].m_ini.m_byMonth[2]);
    if ( v27->m_cash_event[(unsigned __int8)v29].m_ini.m_bUseCashEvent != v28->m_bUseCashEvent )
      CLogFile::Write(
        &v27->m_cash_event[(unsigned __int8)v29].m_event_log,
        "Updated Use of INI File : [%d -> %d]",
        v27->m_cash_event[(unsigned __int8)v29].m_ini.m_bUseCashEvent,
        v28->m_bUseCashEvent);
    v27->m_cash_event[(unsigned __int8)v29].m_ini.m_bUseCashEvent = v28->m_bUseCashEvent;
    v27->m_cash_event[(unsigned __int8)v29].m_ini.m_bRepeat = v28->m_bRepeat;
    v27->m_cash_event[(unsigned __int8)v29].m_ini.m_byRepeatDay = v28->m_byRepeatDay;
    for ( j = 0; j < 3; ++j )
    {
      v27->m_cash_event[(unsigned __int8)v29].m_ini.m_wYear[j] = v28->m_wYear[j];
      v27->m_cash_event[(unsigned __int8)v29].m_ini.m_byMonth[j] = v28->m_byMonth[j];
      v27->m_cash_event[(unsigned __int8)v29].m_ini.m_byDay[j] = v28->m_byDay[j];
      v27->m_cash_event[(unsigned __int8)v29].m_ini.m_byHour[j] = v28->m_byHour[j];
      v27->m_cash_event[(unsigned __int8)v29].m_ini.m_byMinute[j] = v28->m_byMinute[j];
      v27->m_cash_event[(unsigned __int8)v29].m_ini.m_EventTime[j] = v28->m_EventTime[j];
    }
    if ( !v29 )
    {
      v27->m_cash_event[0].m_ini.m_byDiscout[0] = v28->m_byDiscout[0];
      v27->m_cash_event[0].m_ini.m_byDiscout[1] = v28->m_byDiscout[1];
      v27->m_cash_event[0].m_ini.m_byDiscout[2] = v28->m_byDiscout[2];
      v27->m_cash_event[0].m_ini.m_byDiscout[3] = v28->m_byDiscout[3];
    }
    CashItemRemoteStore::SetNextEventTime(v27, v29);
  }
}
