/*
 * Function: ?mgr_item_telekinesis@CPlayer@@QEAA_NXZ
 * Address: 0x1400B9480
 */

char __fastcall CPlayer::mgr_item_telekinesis(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@6
  _sec_info *v5; // rax@11
  char *v6; // rax@28
  char *v7; // rdx@29
  int v8; // er10@29
  char *v9; // rax@31
  __int64 v10; // [sp+0h] [bp-158h]@1
  bool bAdd[8]; // [sp+20h] [bp-138h]@28
  unsigned int dwThrowerSerial[2]; // [sp+28h] [bp-130h]@28
  char *pszThrowerID; // [sp+30h] [bp-128h]@28
  unsigned __int16 wMonRecIndex; // [sp+38h] [bp-120h]@29
  char *pMapCode; // [sp+40h] [bp-118h]@29
  float *pfPos; // [sp+48h] [bp-110h]@29
  char *pszFileName; // [sp+50h] [bp-108h]@29
  char v18; // [sp+60h] [bp-F8h]@6
  CMapData *v19; // [sp+68h] [bp-F0h]@6
  _pnt_rect pRect; // [sp+78h] [bp-E0h]@6
  int j; // [sp+94h] [bp-C4h]@6
  int k; // [sp+98h] [bp-C0h]@8
  unsigned int dwSecIndex; // [sp+9Ch] [bp-BCh]@11
  CObjectList *v24; // [sp+A0h] [bp-B8h]@11
  CObjectList *v25; // [sp+A8h] [bp-B0h]@12
  CItemBox *v26; // [sp+B0h] [bp-A8h]@14
  CItemBox *v27; // [sp+B8h] [bp-A0h]@15
  _STORAGE_LIST::_db_con Dst; // [sp+C8h] [bp-90h]@18
  int v29; // [sp+104h] [bp-54h]@19
  __int64 v30; // [sp+108h] [bp-50h]@19
  char v31; // [sp+110h] [bp-48h]@22
  _base_fld *v32; // [sp+118h] [bp-40h]@28
  _base_fld *v33; // [sp+120h] [bp-38h]@28
  bool v34; // [sp+128h] [bp-30h]@32
  _base_fld *v35; // [sp+130h] [bp-28h]@37
  char v36; // [sp+138h] [bp-20h]@40
  DWORD v37; // [sp+13Ch] [bp-1Ch]@28
  char *pszTakerID; // [sp+140h] [bp-18h]@31
  CPlayer *v39; // [sp+160h] [bp+8h]@1

  v39 = this;
  v1 = &v10;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !v39->m_pUserDB )
    return 0;
  v18 = 0;
  v19 = v39->m_pCurMap;
  v4 = CGameObject::GetCurSecNum((CGameObject *)&v39->vfptr);
  CMapData::GetRectInRadius(v19, &pRect, 100, v4);
  for ( j = pRect.nStarty; j < pRect.nEndy; ++j )
  {
    for ( k = pRect.nStartx; k < pRect.nEndx; ++k )
    {
      v5 = CMapData::GetSecInfo(v19);
      dwSecIndex = v5->m_nSecNumW * j + k;
      v24 = CMapData::GetSectorListObj(v19, v39->m_wMapLayerIndex, dwSecIndex);
      if ( v24 )
      {
        v25 = (CObjectList *)v24->m_Head.m_pNext;
        while ( 1 )
        {
          while ( 1 )
          {
            while ( 1 )
            {
              if ( (_object_list_point *)v25 == &v24->m_Tail )
                goto LABEL_9;
              v26 = (CItemBox *)v25->vfptr;
              v25 = (CObjectList *)v25->m_Head.m_pItem;
              if ( v26->m_ObjID.m_byKind == 1 )
              {
                v27 = v26;
                if ( CItemBox::IsTakeRight(v26, v39)
                  && !_effect_parameter::GetEff_State(&v39->m_EP, 20)
                  && !_effect_parameter::GetEff_State(&v39->m_EP, 28) )
                {
                  _STORAGE_LIST::_db_con::_db_con(&Dst);
                  memcpy_0(&Dst, &v27->m_Item, 0x32ui64);
                  if ( Dst.m_byTableCode != 19 )
                    break;
                }
              }
            }
            v29 = IsSaveItem((unsigned __int8)Dst.m_byTableCode);
            v30 = 0i64;
            if ( v29 )
              break;
LABEL_32:
            v34 = 0;
            if ( v27->m_byCreateCode == 5 && v39->m_pDHChannel )
            {
              *(_QWORD *)dwThrowerSerial = v27;
              *(_DWORD *)bAdd = Dst.m_dwDur;
              v34 = CDarkHoleChannel::CheckEvent(
                      v39->m_pDHChannel,
                      0,
                      (unsigned __int8)Dst.m_byTableCode,
                      Dst.m_wItemIndex,
                      Dst.m_dwDur,
                      (CGameObject *)&v27->vfptr);
            }
            if ( v27->m_byThrowerID == 1 )
            {
              if ( !v34 )
              {
                v35 = CRecordData::GetRecord(
                        (CRecordData *)&unk_1799C6AA0 + (unsigned __int8)Dst.m_byTableCode,
                        Dst.m_wItemIndex);
                CPlayer::Emb_CheckActForQuest(v39, 4, v35->m_strCode, 1u, 0);
                CPlayer::CheckMentalTakeAndUpdateLastMetalTicket(v39, v35->m_strCode);
              }
              if ( CPartyPlayer::IsPartyMode(v39->m_pPartyMgr)
                && v27->m_dwPartyBossSerial == v39->m_pPartyMgr->m_pPartyBoss->m_id.dwSerial )
              {
                v36 = 1;
                if ( IsOverLapItem((unsigned __int8)Dst.m_byTableCode) )
                  v36 = Dst.m_dwDur;
                bAdd[0] = v36;
                CPlayer::SendMsg_PartyLootItemInform(v39, v39->m_dwObjSerial, Dst.m_byTableCode, Dst.m_wItemIndex, v36);
              }
            }
            CPlayer::SendMsg_RewardAddItem(v39, &Dst, 0);
            CItemBox::Destroy(v27);
          }
          if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&v39->m_Param.m_dbInven.m_nListNum) != 255 )
          {
            Dst.m_wSerial = CPlayerDB::GetNewItemSerial(&v39->m_Param);
            if ( CPlayer::Emb_AddStorage(v39, 0, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 0, 1) )
            {
              v18 = 1;
              v31 = 1;
              if ( !IsProtectItem((unsigned __int8)Dst.m_byTableCode) && v27->m_byThrowerID == 1 )
                v31 = 0;
              if ( v31 )
              {
                if ( v27->m_wMonRecIndex != 0xFFFF && v27->m_bBossMob )
                {
                  v32 = CRecordData::GetRecord(&stru_1799C6210, v27->m_wMonRecIndex);
                  v33 = CRecordData::GetRecord(
                          (CRecordData *)&unk_1799C6AA0 + (unsigned __int8)Dst.m_byTableCode,
                          Dst.m_wItemIndex);
                  v37 = timeGetTime() / 0x3E8;
                  v6 = CPlayerDB::GetCharNameA(&v39->m_Param);
                  LODWORD(pszThrowerID) = v37;
                  dwThrowerSerial[0] = v39->m_dwObjSerial;
                  *(_QWORD *)bAdd = v6;
                  CLogFile::Write(
                    &CMonster::s_logTrace_Boss_Looting,
                    "\t Mob: %s Item: %s => %s ( %d ), Sec: %d",
                    v32->m_strCode,
                    v33->m_strCode);
                }
                v7 = v39->m_pCurMap->m_pMapSet->m_strCode;
                v8 = v39->m_ObjID.m_wIndex;
                pszFileName = v39->m_szItemHistoryFileName;
                pfPos = v39->m_fCurPos;
                pMapCode = v7;
                wMonRecIndex = v27->m_wMonRecIndex;
                pszThrowerID = v27->m_szThrowerID;
                dwThrowerSerial[0] = v27->m_dwThrowerCharSerial;
                *(_QWORD *)bAdd = (char *)v27 + 228;
                CMgrAvatorItemHistory::take_ground_item(
                  &CPlayer::s_MgrItemHistory,
                  v8,
                  v27->m_byCreateCode,
                  &Dst,
                  v27->m_aszThrowerName,
                  dwThrowerSerial[0],
                  v27->m_szThrowerID,
                  wMonRecIndex,
                  v7,
                  v39->m_fCurPos,
                  v39->m_szItemHistoryFileName);
                if ( v27->m_dwThrowerCharSerial != -1 && v27->m_dwThrowerCharSerial != v39->m_dwObjSerial )
                {
                  pszTakerID = v39->m_pUserDB->m_szAccountID;
                  v9 = CPlayerDB::GetCharNameA(&v39->m_Param);
                  *(_QWORD *)dwThrowerSerial = v27->m_szThrowerItemHistoryFileName;
                  *(_QWORD *)bAdd = pszTakerID;
                  CMgrAvatorItemHistory::trans_ground_item(
                    &CPlayer::s_MgrItemHistory,
                    &Dst,
                    v9,
                    v39->m_dwObjSerial,
                    pszTakerID,
                    *(char **)dwThrowerSerial);
                }
              }
              goto LABEL_32;
            }
          }
        }
      }
LABEL_9:
      ;
    }
  }
  return v18;
}
