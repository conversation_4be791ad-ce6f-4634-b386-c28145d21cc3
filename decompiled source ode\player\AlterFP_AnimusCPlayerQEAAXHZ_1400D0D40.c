/*
 * Function: ?AlterFP_Animus@CPlayer@@QEAAXH@Z
 * Address: 0x1400D0D40
 */

void __fastcall CPlayer::AlterFP_Animus(CPlayer *this, int nNewFP)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _STORAGE_LIST::_db_con *v4; // rax@5
  _STORAGE_LIST::_db_con *v5; // rax@6
  _STORAGE_LIST::_db_con *v6; // rcx@6
  __int64 v7; // [sp+0h] [bp-48h]@1
  bool bUpdate; // [sp+20h] [bp-28h]@6
  unsigned int *v9; // [sp+30h] [bp-18h]@5
  CPlayer *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v2 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v10->m_pRecalledAnimusItem )
  {
    v4 = v10->m_pRecalledAnimusItem;
    v9 = &v4->m_dwLv;
    HIWORD(v4->m_dwLv) = nNewFP;
    CPlayer::SendMsg_AnimusFPInform(v10);
    if ( v10->m_pUserDB )
    {
      v5 = v10->m_pRecalledAnimusItem;
      v6 = v10->m_pRecalledAnimusItem;
      bUpdate = 0;
      CUserDB::Update_ItemUpgrade(v10->m_pUserDB, 4, v6->m_byStorageIndex, v5->m_dwLv, 0);
    }
  }
}
