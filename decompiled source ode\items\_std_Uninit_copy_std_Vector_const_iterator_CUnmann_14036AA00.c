/*
 * Function: _std::_Uninit_copy_std::_Vector_const_iterator_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo____CUnmannedTraderRegistItemInfo_____ptr64_std::allocator_CUnmannedTraderRegistItemInfo____::_1_::catch$0
 * Address: 0x14036AA00
 */

void __fastcall __noreturn std::_Uninit_copy_std::_Vector_const_iterator_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo____CUnmannedTraderRegistItemInfo_____ptr64_std::allocator_CUnmannedTraderRegistItemInfo____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 112); *(_QWORD *)(i + 32) += 104i64 )
    std::allocator<CUnmannedTraderRegistItemInfo>::destroy(
      *(std::allocator<CUnmannedTraderRegistItemInfo> **)(i + 120),
      *(CUnmannedTraderRegistItemInfo **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
