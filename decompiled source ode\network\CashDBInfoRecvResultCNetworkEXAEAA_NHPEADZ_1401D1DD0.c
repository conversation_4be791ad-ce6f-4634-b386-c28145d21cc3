/*
 * Function: ?CashDBInfoRecvResult@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D1DD0
 */

char __fastcall CNetworkEX::CashDBInfoRecvResult(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  char *szIP; // [sp+30h] [bp-18h]@4

  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szIP = pBuf;
  CMainThread::pc_CashDBInfoRecvResult(&g_Main, pBuf, pBuf + 48, pBuf + 80, pBuf + 112, *((_DWORD *)pBuf + 36));
  return 1;
}
