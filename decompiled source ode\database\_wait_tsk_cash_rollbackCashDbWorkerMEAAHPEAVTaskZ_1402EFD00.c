/*
 * Function: ?_wait_tsk_cash_rollback@CashDbWorker@@MEAAHPEAVTask@@@Z
 * Address: 0x1402EFD00
 */

__int64 __fastcall CashDbWorker::_wait_tsk_cash_rollback(CashDbWorker *this, Task *pkTsk)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  char *v6; // [sp+20h] [bp-28h]@4
  int j; // [sp+28h] [bp-20h]@4
  WorkerVtbl *v8; // [sp+30h] [bp-18h]@9
  CashDbWorker *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = Task::GetTaskBuf(pkTsk);
  for ( j = 0; j < *((_DWORD *)v6 + 8); ++j )
  {
    if ( CRFCashItemDatabase::CallProc_RFONLINE_Cancel(v9->_pkDb, (_param_cash_rollback::__list *)&v6[48 * j + 112]) )
    {
      *(_DWORD *)&v6[48 * j + 148] = 0;
      *((_DWORD *)v6 + 9) = 0;
      v6[48 * j + 144] = 1;
    }
    else
    {
      v8 = v9->vfptr;
      v6[48 * j + 144] = ((int (__fastcall *)(CashDbWorker *, _QWORD))v8[5].~Worker)(
                           v9,
                           (unsigned __int8)v6[48 * j + 144]);
      if ( v6[48 * j + 144] )
      {
        *(_DWORD *)&v6[48 * j + 148] = 0;
        *((_DWORD *)v6 + 9) = 0;
      }
      else
      {
        *((_DWORD *)v6 + 9) = *(_DWORD *)&v6[48 * j + 148];
      }
    }
  }
  return 0i64;
}
