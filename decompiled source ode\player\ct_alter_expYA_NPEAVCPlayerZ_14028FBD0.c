/*
 * Function: ?ct_alter_exp@@YA_NPEAVCPlayer@@@Z
 * Address: 0x14028FBD0
 */

char __fastcall ct_alter_exp(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  double v4; // xmm0_8@6
  __int64 v5; // [sp+0h] [bp-38h]@1
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6 )
  {
    v4 = atof(s_pwszDstCheat[0]);
    CPlayer::AlterExp(v6, v4, 1, 0, 0);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
