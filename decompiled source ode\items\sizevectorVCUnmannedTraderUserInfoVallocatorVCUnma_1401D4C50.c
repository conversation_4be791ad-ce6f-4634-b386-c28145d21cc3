/*
 * Function: ?size@?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@QEBA_KXZ
 * Address: 0x1401D4C50
 */

__int64 __fastcall std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::size(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-18h]@1
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5->_Myfirst )
    v4 = (unsigned int)((char *)v5->_Mylast - (char *)v5->_Myfirst) / 104i64;
  else
    v4 = 0i64;
  return v4;
}
