/*
 * Function: j_?SellComplete@CUnmannedTraderRegistItemInfo@@QEAAXKKK_JQEBD1@Z
 * Address: 0x140004160
 */

void __fastcall CUnmannedTraderRegistItemInfo::SellComplete(CUnmannedTraderRegistItemInfo *this, unsigned int dwPrice, unsigned int dwBuyerSerial, unsigned int dwTax, __int64 tResultTime, const char *const wszBuyerName, const char *const szBuyerAccount)
{
  CUnmannedTraderRegistItemInfo::SellComplete(
    this,
    dwPrice,
    dwBuyerSerial,
    dwTax,
    tResultTime,
    wszBuyerName,
    szBuyerAccount);
}
