/*
 * Function: ?CreateItemBox@@YAPEAVCItemBox@@PEAU_db_con@_STORAGE_LIST@@PEAVCPlayer@@K_NPEAVC<PERSON>haracter@@EPEAVCMapData@@GPEAM2@Z
 * Address: 0x140166AD0
 */

CItemBox *__fastcall CreateItemBox(_STORAGE_LIST::_db_con *pItem, CPlayer *pOwner, unsigned int dwPartyBossSerial, bool bPartyShare, CCharacter *pThrower, char byCreateCode, CMapData *pMap, unsigned __int16 wLayerIndex, float *pStdPos, bool bHide)
{
  __int64 *v10; // rdi@1
  signed __int64 i; // rcx@1
  CItemBox *result; // rax@10
  __int64 v13; // [sp+0h] [bp-D8h]@1
  CItemBox *v14; // [sp+20h] [bp-B8h]@4
  int j; // [sp+28h] [bp-B0h]@4
  _itembox_create_setdata Dst; // [sp+40h] [bp-98h]@11
  _STORAGE_LIST::_db_con *Src; // [sp+E0h] [bp+8h]@1
  CPlayer *v18; // [sp+E8h] [bp+10h]@1
  unsigned int v19; // [sp+F0h] [bp+18h]@1
  bool v20; // [sp+F8h] [bp+20h]@1

  v20 = bPartyShare;
  v19 = dwPartyBossSerial;
  v18 = pOwner;
  Src = pItem;
  v10 = &v13;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v10 = -858993460;
    v10 = (__int64 *)((char *)v10 + 4);
  }
  v14 = 0i64;
  for ( j = 0; j < 5064; ++j )
  {
    if ( !g_ItemBox[j].m_bLive )
    {
      v14 = &g_ItemBox[j];
      break;
    }
  }
  if ( v14 )
  {
    _itembox_create_setdata::_itembox_create_setdata(&Dst);
    memcpy_0(&Dst.Item, Src, 0x32ui64);
    Dst.m_pRecordSet = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + Src->m_byTableCode, Src->m_wItemIndex);
    if ( Dst.m_pRecordSet )
    {
      Dst.byCreateCode = byCreateCode;
      Dst.pOwner = v18;
      Dst.bParty = v20;
      Dst.pThrower = pThrower;
      Dst.m_pMap = pMap;
      Dst.m_nLayerIndex = wLayerIndex;
      Dst.dwPartyBossSerial = v19;
      CMapData::GetRandPosInRange(pMap, pStdPos, 10, Dst.m_fStartPos);
      if ( CItemBox::Create(v14, &Dst, bHide) )
        result = v14;
      else
        result = 0i64;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
