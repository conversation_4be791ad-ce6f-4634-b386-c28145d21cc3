/*
 * Function: ??8?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@QEBA_NAEBV01@@Z
 * Address: 0x140552660
 */

bool __fastcall CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::operator==(__int64 a1, __int64 a2)
{
  CryptoPP::Integer *v2; // rax@1
  CryptoPP::Integer *b; // ST20_8@1
  CryptoPP::Integer *v4; // rax@1
  CryptoPP::Integer *v5; // rax@2
  CryptoPP::Integer *v6; // ST30_8@2
  CryptoPP::Integer *v7; // rax@2
  CryptoPP::Integer *v8; // rax@3
  CryptoPP::Integer *v9; // ST40_8@3
  CryptoPP::Integer *v10; // rax@3
  bool v12; // [sp+48h] [bp-10h]@4
  __int64 v13; // [sp+60h] [bp+8h]@1
  __int64 v14; // [sp+68h] [bp+10h]@1

  v14 = a2;
  v13 = a1;
  LODWORD(v2) = (*(int (__fastcall **)(__int64))(*(_QWORD *)a2 + 32i64))(a2);
  b = v2;
  LODWORD(v4) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v13 + 32i64))(v13);
  v12 = CryptoPP::operator==(v4, b)
     && (LODWORD(v5) = CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::GetGenerator(v14),
         v6 = v5,
         LODWORD(v7) = CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::GetGenerator(v13),
         CryptoPP::operator==(v7, v6))
     && (LODWORD(v8) = (*(int (__fastcall **)(signed __int64))(*(_QWORD *)(v14 + 8) + 64i64))(v14 + 8),
         v9 = v8,
         LODWORD(v10) = (*(int (__fastcall **)(signed __int64))(*(_QWORD *)(v13 + 8) + 64i64))(v13 + 8),
         CryptoPP::operator==(v10, v9));
  return v12;
}
