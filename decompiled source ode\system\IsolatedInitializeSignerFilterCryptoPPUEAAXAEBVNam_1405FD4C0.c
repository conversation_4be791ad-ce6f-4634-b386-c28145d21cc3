/*
 * Function: ?IsolatedInitialize@SignerFilter@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x1405FD4C0
 */

void __fastcall CryptoPP::SignerFilter::IsolatedInitialize(CryptoPP::SignerFilter *this, const struct CryptoPP::NameValuePairs *a2)
{
  const char *v2; // rax@1
  __int64 v3; // rax@1
  CryptoPP::SignerFilter *v4; // [sp+30h] [bp+8h]@1
  const struct CryptoPP::NameValuePairs *v5; // [sp+38h] [bp+10h]@1

  v5 = a2;
  v4 = this;
  v2 = CryptoPP::Name::PutMessage((CryptoPP::Name *)this);
  v4->m_putMessage = CryptoPP::NameValuePairs::GetValueWithDefault<bool>(v5, v2, 0i64);
  LODWORD(v3) = ((int (__fastcall *)(CryptoPP::PK_Signer *, CryptoPP::RandomNumberGenerator *))v4->m_signer->vfptr[1].__vecDelDtor)(
                  v4->m_signer,
                  v4->m_rng);
  CryptoPP::member_ptr<CryptoPP::PK_MessageAccumulator>::reset(&v4->m_messageAccumulator, v3);
}
