/*
 * Function: ?CompleteReRegistRollBack@CUnmannedTraderUserInfoTable@@QEAAXGKPEAD@Z
 * Address: 0x1403648A0
 */

void __fastcall CUnmannedTraderUserInfoTable::CompleteReRegistRollBack(CUnmannedTraderUserInfoTable *this, unsigned __int16 wInx, unsigned int dwOwnerSerial, char *pData)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  char *v7; // [sp+20h] [bp-28h]@4
  CUnmannedTraderUserInfo *v8; // [sp+28h] [bp-20h]@4
  CPlayer *v9; // [sp+30h] [bp-18h]@4
  CUnmannedTraderUserInfoTable *v10; // [sp+50h] [bp+8h]@1
  char *pDataa; // [sp+68h] [bp+20h]@1

  pDataa = pData;
  v10 = this;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7 = pData;
  v8 = CUnmannedTraderUserInfoTable::FindUser(v10, *((_WORD *)pData + 1), *((_DWORD *)pData + 2));
  v9 = 0i64;
  if ( !CUnmannedTraderUserInfo::IsNull(v8) )
  {
    v9 = CUnmannedTraderUserInfo::FindOwner(v8);
    if ( v9 )
    {
      if ( v9->m_bOper )
        CUnmannedTraderUserInfo::CompleteReRegistRollBack(v8, pDataa, v10->m_pkLogger);
    }
  }
}
