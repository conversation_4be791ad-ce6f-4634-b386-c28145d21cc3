/*
 * Function: failwithmessage
 * Address: 0x1404DDA60
 */

void __fastcall failwithmessage(void *retaddr, int crttype, int errnum, const char *msg)
{
  const char *v4; // r13@1
  __int64 v5; // r12@1
  int v6; // er14@1
  void *v7; // rsi@1
  int (*v8)(int, const char *, int, const char *, const char *, ...); // rbp@1
  int (*v9)(int, const wchar_t *, int, const wchar_t *, const wchar_t *, ...); // rdi@1
  unsigned int v10; // eax@3
  wchar_t *v11; // rbx@5
  int v12; // eax@8
  bool v13; // zf@16
  int v14; // eax@17
  char *v15; // rbx@19
  int v16; // eax@21
  char *v17; // r9@23
  int pline; // [sp+40h] [bp-EA8h]@15
  wchar_t moduleName; // [sp+50h] [bp-E98h]@15
  wchar_t source; // [sp+260h] [bp-C88h]@15
  char v21; // [sp+470h] [bp-A78h]@22
  wchar_t WideCharStr; // [sp+680h] [bp-868h]@4
  char MultiByteStr; // [sp+A80h] [bp-468h]@18

  v4 = msg;
  v5 = errnum;
  v6 = crttype;
  v7 = retaddr;
  v8 = 0i64;
  v9 = _RTC_GetErrorFuncW(retaddr);
  if ( !v9 )
    v8 = _RTC_GetErrorFunc(v7);
  v10 = MultiByteToWideChar(0xFDE9u, 0, v4, -1, 0i64, 0);
  if ( v10 < 0x200 && MultiByteToWideChar(0xFDE9u, 0, v4, -1, &WideCharStr, v10) )
    v11 = &WideCharStr;
  else
    v11 = L"Runtime Check Error.\n\r Unable to display RTC Message.";
  if ( (unsigned int)DebuggerProbe(0x1002u) )
  {
    v12 = DebuggerRuntime(v5, RTC_NoFalsePositives[v5], v7, v11);
    if ( v12 )
      return;
  }
  else
  {
    LOBYTE(v12) = 1;
  }
  if ( !v8 && !v9
    || (_BYTE)v12 && IsDebuggerPresent()
    || ((_RTC_GetSrcLine((char *)v7 - 5, &source, 0x104u, &pline, &moduleName, 0x104u), !v9) ? ((v14 = WideCharToMultiByte(0xFDE9u, 0, &source, -1, 0i64, 0, 0i64, 0i64)) == 0
                                                                                             || !WideCharToMultiByte(
                                                                                                   0xFDE9u,
                                                                                                   0,
                                                                                                   &source,
                                                                                                   -1,
                                                                                                   &MultiByteStr,
                                                                                                   v14,
                                                                                                   0i64,
                                                                                                   0i64) ? (v15 = "Unknown Filename") : (v15 = &MultiByteStr),
                                                                                                (v16 = WideCharToMultiByte(0xFDE9u, 0, &moduleName, -1, 0i64, 0, 0i64, 0i64)) == 0
                                                                                             || !WideCharToMultiByte(
                                                                                                   0xFDE9u,
                                                                                                   0,
                                                                                                   &moduleName,
                                                                                                   -1,
                                                                                                   &v21,
                                                                                                   v16,
                                                                                                   0i64,
                                                                                                   0i64) ? (v17 = "Unknown Module Name") : (v17 = &v21),
                                                                                                v13 = ((int (__fastcall *)(_QWORD, char *, _QWORD, char *))v8)((unsigned int)v6, v15, (unsigned int)pline, v17) == 1) : (v13 = ((int (__fastcall *)(_QWORD, wchar_t *, _QWORD, wchar_t *))v9)((unsigned int)v6, &source, (unsigned int)pline, &moduleName) == 1),
        v13) )
  {
    DebugBreak();
  }
}
