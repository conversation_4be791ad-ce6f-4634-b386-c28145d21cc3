/*
 * Function: j_?Update_Player_Vote_Info@CRFWorldDatabase@@QEAA_NKKEEK@Z
 * Address: 0x140006B9A
 */

bool __fastcall CRFWorldDatabase::Update_Player_Vote_Info(CRFWorldDatabase *this, unsigned int dwSerial, unsigned int dwAccPlayTime, char IsVote, char VoteEnable, unsigned int dwScanerData)
{
  return CRFWorldDatabase::Update_Player_Vote_Info(this, dwSerial, dwAccPlayTime, IsVote, VoteEnable, dwScanerData);
}
