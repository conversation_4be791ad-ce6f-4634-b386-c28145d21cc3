/*
 * Function: ?GetPvpPointLeak@CPlayer@@QEAANXZ
 * Address: 0x140068FB0
 */

long double __fastcall CPlayer::GetPvpPointLeak(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-18h]@1
  CPlayer *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return v5->m_Param.m_dPvpPointLeak;
}
