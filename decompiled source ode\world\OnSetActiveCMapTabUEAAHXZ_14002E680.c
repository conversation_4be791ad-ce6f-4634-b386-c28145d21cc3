/*
 * Function: ?OnSetActive@CMapTab@@UEAAHXZ
 * Address: 0x14002E680
 */

__int64 __fastcall CMapTab::OnSetActive(CMapTab *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  CMapTab *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CMsgData::PackingMsg(&stru_1415B7048, 0x3EFu, 0, 0, 0);
  return CPropertyPage::OnSetActive((CPropertyPage *)&v5->vfptr);
}
