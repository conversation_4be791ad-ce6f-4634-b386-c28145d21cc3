/*
 * Function: ?IsCharInSector@CAttack@@SAHQEAM00MM@Z
 * Address: 0x14016D920
 */

signed __int64 __fastcall CAttack::IsCharInSector(float *chkpos, float *src, float *dest, float angle, float radius)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  float v8; // xmm0_4@12
  float v9; // xmm0_4@12
  __int64 v10; // [sp+0h] [bp-88h]@1
  float v; // [sp+28h] [bp-60h]@10
  float v12; // [sp+2Ch] [bp-5Ch]@10
  float v13; // [sp+30h] [bp-58h]@10
  float v14; // [sp+58h] [bp-30h]@12
  float v15; // [sp+5Ch] [bp-2Ch]@12
  float v16; // [sp+60h] [bp-28h]@12
  float v17; // [sp+74h] [bp-14h]@10
  float *v18; // [sp+90h] [bp+8h]@1
  float *v19; // [sp+98h] [bp+10h]@1
  float *v20; // [sp+A0h] [bp+18h]@1

  v20 = dest;
  v19 = src;
  v18 = chkpos;
  v5 = &v10;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( *src != *dest || src[2] != dest[2] )
  {
    if ( *v18 != *dest || v18[2] != dest[2] )
    {
      v = *v18 - *dest;
      v12 = v18[1] - dest[1];
      v13 = v18[2] - dest[2];
      v17 = sqrtf((float)((float)(v * v) + (float)(v12 * v12)) + (float)(v13 * v13));
      if ( v17 <= radius )
      {
        v14 = *v20 - *v19;
        v15 = v20[1] - v19[1];
        v16 = v20[2] - v19[2];
        Normalize(&v);
        Normalize(&v14);
        v17 = (float)((float)(v * v14) + (float)(v12 * v15)) + (float)(v13 * v16);
        v8 = (float)((float)(v * v14) + (float)(v12 * v15)) + (float)(v13 * v16);
        acos(v17);
        v9 = v8 * 360.0 / 6.283184051513672;
        result = (float)(angle / 2.0) > v9;
      }
      else
      {
        result = 0i64;
      }
    }
    else
    {
      result = 1i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
