/*
 * Function: ?IsolatedInitialize@BaseN_Decoder@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x14063F020
 */

void __fastcall CryptoPP::BaseN_Decoder::IsolatedInitialize(CryptoPP::BaseN_Decoder *this, const struct CryptoPP::NameValuePairs *a2)
{
  const char *v2; // rax@1
  CryptoPP::Name *v3; // rcx@1
  const char *v4; // rax@1
  signed int i; // [sp+20h] [bp-B8h]@5
  CryptoPP::InvalidArgument v6; // [sp+28h] [bp-B0h]@3
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+78h] [bp-60h]@3
  unsigned __int8 v8; // [sp+A8h] [bp-30h]@3
  __int64 v9; // [sp+B0h] [bp-28h]@1
  CryptoPP::BaseN_Decoder *v10; // [sp+B8h] [bp-20h]@1
  int *v11; // [sp+C0h] [bp-18h]@1
  CryptoPP::BaseN_Decoder *v12; // [sp+E0h] [bp+8h]@1
  const struct CryptoPP::NameValuePairs *v13; // [sp+E8h] [bp+10h]@1

  v13 = a2;
  v12 = this;
  v9 = -2i64;
  v10 = (CryptoPP::BaseN_Decoder *)((char *)this + 48);
  v2 = CryptoPP::Name::DecodingLookupArray(this);
  CryptoPP::NameValuePairs::GetRequiredParameter<int const *>(v13, "BaseN_Decoder", v2, v10);
  v11 = (int *)((char *)v12 + 60);
  v4 = CryptoPP::Name::Log2Base(v3);
  CryptoPP::NameValuePairs::GetRequiredIntParameter(
    (CryptoPP::NameValuePairs *)v13,
    "BaseN_Decoder",
    v4,
    (int *)v12 + 15);
  if ( *((_DWORD *)v12 + 15) <= 0 || *((_DWORD *)v12 + 15) >= 8 )
  {
    memset(&v8, 0, sizeof(v8));
    std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
      &s,
      "BaseN_Decoder: Log2Base must be between 1 and 7 inclusive",
      v8);
    CryptoPP::InvalidArgument::InvalidArgument(&v6, &s);
    CxxThrowException_0((__int64)&v6, (__int64)&TI3_AVInvalidArgument_CryptoPP__);
  }
  *((_DWORD *)v12 + 18) = 0;
  *((_DWORD *)v12 + 17) = 0;
  for ( i = *((_DWORD *)v12 + 15); i % -8; i += *((_DWORD *)v12 + 15) )
    ;
  *((_DWORD *)v12 + 16) = i / 8;
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::New(
    (CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)((char *)v12 + 80),
    *((_DWORD *)v12 + 16));
}
