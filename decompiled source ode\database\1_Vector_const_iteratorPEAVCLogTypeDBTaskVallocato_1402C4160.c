/*
 * Function: ??1?$_Vector_const_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAA@XZ
 * Address: 0x1402C4160
 */

void __fastcall std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &>::~_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &>((std::_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &> *)&v4->_Mycont);
}
