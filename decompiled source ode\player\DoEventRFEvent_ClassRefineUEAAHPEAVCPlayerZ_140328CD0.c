/*
 * Function: ?DoEvent@RFEvent_ClassRefine@@UEAAHPEAVCPlayer@@@Z
 * Address: 0x140328CD0
 */

__int64 __fastcall RFEvent_ClassRefine::DoEvent(RFEvent_ClassRefine *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  RFEvent_ClassRefine *v7; // [sp+40h] [bp+8h]@1
  CPlayer *v8; // [sp+48h] [bp+10h]@1

  v8 = pOne;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = ((int (__fastcall *)(RFEvent_ClassRefine *))v7->vfptr[1].__vecDelDtor)(v7);
  if ( v6 )
  {
    result = (unsigned __int8)v6;
  }
  else
  {
    v6 = CPlayer::pc_InitClass(v8);
    if ( v6 )
    {
      result = (unsigned __int8)v6;
    }
    else
    {
      ++v7->_pkParticipant[v8->m_ObjID.m_wIndex].nCurRefineCnt;
      v7->_pkParticipant[v8->m_ObjID.m_wIndex].bChange = 1;
      result = 0i64;
    }
  }
  return result;
}
