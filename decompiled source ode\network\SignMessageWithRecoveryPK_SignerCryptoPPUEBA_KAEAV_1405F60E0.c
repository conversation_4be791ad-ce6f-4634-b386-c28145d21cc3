/*
 * Function: ?SignMessageWithRecovery@PK_Signer@CryptoPP@@UEBA_KAEAVRandomNumberGenerator@2@PEBE_K12PEAE@Z
 * Address: 0x1405F60E0
 */

__int64 __fastcall CryptoPP::PK_Signer::SignMessageWithRecovery(CryptoPP::PK_Signer *this, struct CryptoPP::RandomNumberGenerator *a2, const unsigned __int8 *a3, __int64 a4, const unsigned __int8 *a5, unsigned __int64 a6, unsigned __int8 *a7)
{
  __int64 v7; // rax@1
  __int64 v8; // rax@1
  __int64 v9; // rax@1
  __int64 v10; // rax@1
  __int64 v11; // rax@1
  char v13; // [sp+30h] [bp-38h]@1
  __int64 v14; // [sp+38h] [bp-30h]@1
  __int64 v15; // [sp+40h] [bp-28h]@1
  CryptoPP::PK_SignatureSchemeVtbl *v16; // [sp+48h] [bp-20h]@1
  __int64 v17; // [sp+50h] [bp-18h]@1
  CryptoPP::PK_SignatureSchemeVtbl *v18; // [sp+58h] [bp-10h]@1
  CryptoPP::PK_Signer *v19; // [sp+70h] [bp+8h]@1
  struct CryptoPP::RandomNumberGenerator *v20; // [sp+78h] [bp+10h]@1
  const unsigned __int8 *v21; // [sp+80h] [bp+18h]@1
  __int64 v22; // [sp+88h] [bp+20h]@1

  v22 = a4;
  v21 = a3;
  v20 = a2;
  v19 = this;
  v15 = -2i64;
  LODWORD(v7) = ((int (*)(void))this->vfptr[1].__vecDelDtor)();
  std::auto_ptr<CryptoPP::PK_MessageAccumulator>::auto_ptr<CryptoPP::PK_MessageAccumulator>(&v13, v7);
  LODWORD(v8) = std::auto_ptr<CryptoPP::PK_MessageAccumulator>::operator*(&v13);
  v16 = v19->vfptr;
  (*(void (__fastcall **)(CryptoPP::PK_Signer *, __int64, const unsigned __int8 *, __int64))&v16[1].gap8[0])(
    v19,
    v8,
    v21,
    v22);
  LODWORD(v9) = std::auto_ptr<CryptoPP::PK_MessageAccumulator>::operator->(&v13);
  v17 = v9;
  (*(void (__fastcall **)(__int64, const unsigned __int8 *, unsigned __int64))(*(_QWORD *)v9 + 24i64))(v9, a5, a6);
  LODWORD(v10) = std::auto_ptr<CryptoPP::PK_MessageAccumulator>::operator*(&v13);
  v18 = v19->vfptr;
  LODWORD(v11) = ((int (__fastcall *)(CryptoPP::PK_Signer *, struct CryptoPP::RandomNumberGenerator *, __int64, unsigned __int8 *))v18[1].MaxRecoverableLength)(
                   v19,
                   v20,
                   v10,
                   a7);
  v14 = v11;
  std::auto_ptr<CryptoPP::PK_MessageAccumulator>::~auto_ptr<CryptoPP::PK_MessageAccumulator>(&v13);
  return v14;
}
