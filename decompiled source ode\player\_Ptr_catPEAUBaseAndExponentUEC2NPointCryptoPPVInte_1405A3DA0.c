/*
 * Function: ??$_Ptr_cat@PEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@PEAU12@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAPEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@0@Z
 * Address: 0x1405A3DA0
 */

char std::_Ptr_cat<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> *>()
{
  char v1; // [sp+0h] [bp-18h]@0

  return v1;
}
