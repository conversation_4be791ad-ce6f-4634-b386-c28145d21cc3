/*
 * Function: ?CheckTime@LendItemSheet@@QEAAHXZ
 * Address: 0x14030D790
 */

__int64 __fastcall LendItemSheet::CheckTime(LendItemSheet *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  unsigned __int64 v4; // rax@8
  _STORAGE_LIST::_db_con *v5; // rax@20
  __int64 v6; // [sp+0h] [bp-C8h]@1
  bool bDelete[8]; // [sp+20h] [bp-A8h]@20
  char *strErrorCodePos; // [sp+28h] [bp-A0h]@20
  __time32_t Time; // [sp+34h] [bp-94h]@6
  unsigned int v10; // [sp+44h] [bp-84h]@6
  int j; // [sp+48h] [bp-80h]@6
  LendItemSheet::Cell *v12; // [sp+50h] [bp-78h]@9
  int *v13; // [sp+58h] [bp-70h]@14
  void *Src; // [sp+60h] [bp-68h]@14
  _STORAGE_LIST::_db_con Dst; // [sp+78h] [bp-50h]@20
  unsigned __int64 v16; // [sp+B8h] [bp-10h]@8
  LendItemSheet *v17; // [sp+D0h] [bp+8h]@1

  v17 = this;
  v1 = &v6;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( ListHeap<LendItemSheet::Cell>::empty(&v17->_heapFixRow) )
  {
    result = 0i64;
  }
  else
  {
    _time32(&Time);
    v10 = 0;
    for ( j = 0; ; ++j )
    {
      v16 = j;
      v4 = ListHeap<LendItemSheet::Cell>::size(&v17->_heapFixRow);
      if ( v16 >= v4 )
        break;
      v12 = ListHeap<LendItemSheet::Cell>::top(&v17->_heapFixRow);
      if ( !v12 || v12->_pkItem->m_dwT > Time )
        break;
      if ( v17->_pkOwner->m_pUserDB )
      {
        if ( v12->_nStorageCode < 8 )
        {
          v13 = &v17->_pkOwner->m_Param.m_pStoragePtr[v12->_nStorageCode]->m_nListNum;
          Src = (void *)(*(_QWORD *)(v13 + 3) + 50i64 * v12->_pkItem->m_byStorageIndex);
          if ( Src )
          {
            if ( *(_BYTE *)Src )
            {
              if ( *((_BYTE *)Src + 32) )
              {
                _STORAGE_LIST::_db_con::_db_con(&Dst);
                memcpy_0(&Dst, Src, 0x32ui64);
                CPlayer::SendMsg_LendItemTimeExpired(v17->_pkOwner, v12->_nStorageCode, v12->_pkItem->m_wSerial);
                v5 = v12->_pkItem;
                strErrorCodePos = "LendItemSheet::CheckTime";
                bDelete[0] = 1;
                if ( !CPlayer::Emb_DelStorage(
                        v17->_pkOwner,
                        v12->_nStorageCode,
                        v5->m_byStorageIndex,
                        0,
                        1,
                        "LendItemSheet::CheckTime") )
                  v10 = -5;
                *(_QWORD *)bDelete = (char *)v17->_pkOwner + 50608;
                CMgrAvatorItemHistory::lenditem_del_from_inven(
                  &CPlayer::s_MgrItemHistory,
                  Dst.m_byTableCode,
                  Dst.m_wItemIndex,
                  Dst.m_lnUID,
                  *(char **)bDelete);
              }
              else
              {
                v10 = -4;
              }
            }
            else
            {
              v10 = -3;
            }
          }
          else
          {
            v10 = -2;
          }
        }
        else
        {
          v10 = -1;
        }
      }
    }
    result = v10;
  }
  return result;
}
