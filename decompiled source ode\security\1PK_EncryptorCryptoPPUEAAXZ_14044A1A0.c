/*
 * Function: ??1PK_Encryptor@CryptoPP@@UEAA@XZ
 * Address: 0x14044A1A0
 */

void __fastcall CryptoPP::PK_Encryptor::~PK_Encryptor(CryptoPP::PK_Encryptor *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CryptoPP::PublicKeyAlgorithm *v5; // [sp+28h] [bp-10h]@5
  CryptoPP::PK_Encryptor *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  if ( v6 )
    v5 = (CryptoPP::PublicKeyAlgorithm *)&v6->vfptr;
  else
    v5 = 0i64;
  CryptoPP::PublicKeyAlgorithm::~PublicKeyAlgorithm(v5);
  CryptoPP::PK_CryptoSystem::~PK_CryptoSystem((CryptoPP::PK_CryptoSystem *)&v6->vfptr);
}
