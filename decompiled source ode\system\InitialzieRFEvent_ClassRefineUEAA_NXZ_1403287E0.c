/*
 * Function: ?Initialzie@RFEvent_ClassRefine@@UEAA_NXZ
 * Address: 0x1403287E0
 */

char __fastcall RFEvent_ClassRefine::Initialzie(RFEvent_ClassRefine *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CAsyncLogger *v4; // rax@8
  CAsyncLogger *v5; // rax@11
  __int64 v6; // [sp+0h] [bp-48h]@1
  bool bAddDateFileName[4]; // [sp+20h] [bp-28h]@11
  unsigned int dwUpdateFileNameDelay; // [sp+28h] [bp-20h]@11
  _event_participant_classrefine *v9; // [sp+30h] [bp-18h]@6
  const char *v10; // [sp+38h] [bp-10h]@9
  RFEvent_ClassRefine *v11; // [sp+50h] [bp+8h]@1

  v11 = this;
  v1 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( GetLastWriteFileTime(".\\Initialize\\WorldSystem.ini", &v11->m_ftWrite) )
  {
    RFEvent_ClassRefine::ReadClassRefineEventInfo(v11);
    memcpy_0(&v11->_kEvent, &v11->_kModifyEvent, 0x10ui64);
    v9 = (_event_participant_classrefine *)operator new[](0xC5D0ui64);
    v11->_pkParticipant = v9;
    if ( v11->_pkParticipant )
    {
      memset_0(v11->_pkParticipant, 0, 0xC5D0ui64);
      v11->m_bUserDataReset = 0;
      CMyTimer::BeginTimer(&v11->m_tmDataFileCheckTime, 0x2710u);
      v4 = CAsyncLogger::Instance();
      CAsyncLogger::Regist(
        v4,
        ALT_CLASSREFINE_SERVICE_LOG,
        "..\\ZoneServerLog\\ServiceLog\\ClassRefine",
        "ClassRefine_EventLog",
        1,
        0xFFFFFFFF);
      if ( v11->_kEvent.bEnable )
        v10 = "TRUE";
      else
        v10 = "FALSE";
      v5 = CAsyncLogger::Instance();
      dwUpdateFileNameDelay = v11->_kEvent.nEndDate;
      *(_DWORD *)bAddDateFileName = v11->_kEvent.nStartDate;
      CAsyncLogger::FormatLog(v5, 9, "[EVENT_CLASSREFINE] : %s : StartDate=%d, EndDate=%d", v10);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
