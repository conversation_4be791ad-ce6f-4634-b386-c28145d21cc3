/*
 * Function: ?IsSetOn@CSetItemEffect@@AEAA_NK@Z
 * Address: 0x1402E2BF0
 */

char __fastcall CSetItemEffect::IsSetOn(CSetItemEffect *this, unsigned int dwSetItem)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CSetItemEffect *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  for ( j = 0; j < 6; ++j )
  {
    if ( v6->m_setCount[j].m_bCheckSetEffect && v6->m_setCount[j].m_dwSetItem == dwSetItem )
      return 1;
  }
  return 0;
}
