/*
 * Function: ?DTradeAskRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D2FC0
 */

char __usercall CNetworkEX::DTradeAskRequest@<al>(CNetworkEX *this@<rcx>, int n@<edx>, char *pBuf@<r8>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v7; // rax@14
  __int64 v8; // [sp+0h] [bp-48h]@1
  unsigned __int16 *v9; // [sp+20h] [bp-28h]@4
  CPlayer *v10; // [sp+28h] [bp-20h]@4
  CPlayer *v11; // [sp+30h] [bp-18h]@4
  CNetworkEX *v12; // [sp+50h] [bp+8h]@1

  v12 = this;
  v4 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = (unsigned __int16 *)pBuf;
  v10 = &g_Player + n;
  v11 = &g_Player + *(_WORD *)pBuf;
  if ( !v10->m_bOper || v10->m_pmTrd.bDTradeMode || v10->m_bCorpse )
  {
    result = 1;
  }
  else
  {
    _effect_parameter::GetEff_Have(&v10->m_EP, 50);
    if ( a4 <= 0.0 )
    {
      if ( _effect_parameter::GetEff_State(&v10->m_EP, 26) || _effect_parameter::GetEff_State(&v11->m_EP, 26) )
      {
        CPlayer::SendMsg_DTradeAskResult(v10, 32);
        result = 1;
      }
      else if ( (signed int)*v9 < 2532 )
      {
        CPlayer::pc_DTradeAskRequest(v10, *v9);
        result = 1;
      }
      else
      {
        v7 = CPlayerDB::GetCharNameA(&v10->m_Param);
        CLogFile::Write(&v12->m_LogFile, "odd.. %s: DTradeAskRequest().. if(pRecv->wDstIndex >= MAX_PLAYER)", v7);
        result = 0;
      }
    }
    else
    {
      CPlayer::SendMsg_JadeEffectErr(v10, 3);
      result = 1;
    }
  }
  return result;
}
