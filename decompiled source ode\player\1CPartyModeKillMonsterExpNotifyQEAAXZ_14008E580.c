/*
 * Function: ??1CPartyModeKillMonsterExpNotify@@QEAA@XZ
 * Address: 0x14008E580
 */

void __fastcall CPartyModeKillMonsterExpNotify::~CPartyModeKillMonsterExpNotify(CPartyModeKillMonsterExpNotify *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CPartyModeKillMonsterExpNotify *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  `eh vector destructor iterator'(
    v4->m_kInfo,
    0x10ui64,
    8,
    (void (__cdecl *)(void *))CPartyModeKillMonsterExpNotify::CExpInfo::~CExpInfo);
}
