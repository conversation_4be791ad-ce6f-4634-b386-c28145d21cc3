/*
 * Function: ?_insert_info@TRC_AutoTrade@@SAEPEAD@Z
 * Address: 0x1402D9050
 */

char __fastcall TRC_AutoTrade::_insert_info(char *pdata)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-58h]@1
  unsigned int dwMatterDst; // [sp+20h] [bp-38h]@4
  char *wszMatterDst; // [sp+28h] [bp-30h]@4
  char byCurrTax; // [sp+30h] [bp-28h]@4
  unsigned int dwNext; // [sp+38h] [bp-20h]@4
  char *v9; // [sp+40h] [bp-18h]@4
  bool v10; // [sp+48h] [bp-10h]@4
  int v11; // [sp+4Ch] [bp-Ch]@5
  char *v12; // [sp+60h] [bp+8h]@1

  v12 = pdata;
  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = v12;
  dwNext = *(_DWORD *)(v12 + 91);
  byCurrTax = v12[95];
  wszMatterDst = v12 + 27;
  dwMatterDst = *(_DWORD *)(v12 + 23);
  v10 = CRFWorldDatabase::insert_atrade_taxrate(
          pkDB,
          *v12,
          *(_DWORD *)(v12 + 1),
          v12 + 5,
          dwMatterDst,
          v12 + 27,
          byCurrTax,
          dwNext);
  if ( v10 )
    v11 = 0;
  else
    v11 = 24;
  return v11;
}
