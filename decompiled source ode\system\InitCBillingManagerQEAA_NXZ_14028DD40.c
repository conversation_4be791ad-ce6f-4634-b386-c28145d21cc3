/*
 * Function: ?Init@CBillingManager@@QEAA_NXZ
 * Address: 0x14028DD40
 */

char __fastcall CBillingManager::Init(CBillingManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CNationSettingManager *v4; // rax@8
  __int64 v5; // [sp+0h] [bp-38h]@1
  CBilling *v6; // [sp+20h] [bp-18h]@8
  CBillingManager *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CBillingManager::LoadINI(v7) )
  {
    if ( v7->m_pBill->m_bOper )
    {
      v4 = CTSingleton<CNationSettingManager>::Instance();
      v6 = CNationSettingManager::CreateBilling(v4);
      if ( v6 )
      {
        v7->m_pBill = v6;
        result = 1;
      }
      else
      {
        result = 1;
      }
    }
    else
    {
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
