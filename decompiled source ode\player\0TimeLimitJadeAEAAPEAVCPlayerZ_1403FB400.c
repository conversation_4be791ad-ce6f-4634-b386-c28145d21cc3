/*
 * Function: ??0TimeLimitJade@@AEAA@PEAVCPlayer@@@Z
 * Address: 0x1403FB400
 */

void __fastcall TimeLimitJade::TimeLimitJade(TimeLimitJade *this, CPlayer *p)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  __int64 v5; // [sp+20h] [bp-18h]@4
  TimeLimitJade *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = -2i64;
  v6->_pkOwner = p;
  ListHeap<TimeLimitJade::WaitCell>::ListHeap<TimeLimitJade::WaitCell>(&v6->_heapWaitRow);
  ListHeap<TimeLimitJade::UseCell>::ListHeap<TimeLimitJade::UseCell>(&v6->_heapUseRow);
}
