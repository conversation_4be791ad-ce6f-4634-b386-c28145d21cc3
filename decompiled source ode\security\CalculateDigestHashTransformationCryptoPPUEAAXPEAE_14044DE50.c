/*
 * Function: ?CalculateDigest@HashTransformation@CryptoPP@@UEAAXPEAEPEBE_K@Z
 * Address: 0x14044DE50
 */

void __fastcall CryptoPP::HashTransformation::CalculateDigest(CryptoPP::HashTransformation *this, char *digest, const char *input, unsigned __int64 length)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CryptoPP::HashTransformation *v7; // [sp+30h] [bp+8h]@1
  char *v8; // [sp+38h] [bp+10h]@1

  v8 = digest;
  v7 = this;
  v4 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  ((void (__fastcall *)(CryptoPP::HashTransformation *, const char *, unsigned __int64))v7->vfptr[1].Clone)(
    v7,
    input,
    length);
  ((void (__fastcall *)(CryptoPP::HashTransformation *, char *))v7->vfptr[2].Clone)(v7, v8);
}
