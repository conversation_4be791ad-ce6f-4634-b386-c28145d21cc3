/*
 * Function: ?Guild_Buy_Emblem_Complete@CPlayer@@SAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1400AA500
 */

void __fastcall CPlayer::Guild_Buy_Emblem_Complete(_DB_QRY_SYN_DATA *pData)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-F8h]@1
  long double v4; // [sp+20h] [bp-D8h]@6
  __int64 v5; // [sp+28h] [bp-D0h]@6
  char *v6; // [sp+30h] [bp-C8h]@6
  char *pbyDate; // [sp+38h] [bp-C0h]@6
  bool bInPut[4]; // [sp+40h] [bp-B8h]@6
  int v9; // [sp+50h] [bp-A8h]@4
  unsigned int v10; // [sp+54h] [bp-A4h]@4
  unsigned int dwEmblemBack; // [sp+58h] [bp-A0h]@4
  unsigned int dwEmblemMark; // [sp+5Ch] [bp-9Ch]@4
  unsigned int dwIOerSerial; // [sp+60h] [bp-98h]@4
  char Dest; // [sp+78h] [bp-80h]@4
  int v15; // [sp+94h] [bp-64h]@4
  int v16; // [sp+98h] [bp-60h]@4
  long double v17; // [sp+A0h] [bp-58h]@4
  long double v18; // [sp+A8h] [bp-50h]@4
  char Dst; // [sp+B4h] [bp-44h]@4
  char v20; // [sp+B5h] [bp-43h]@4
  char *v21; // [sp+C8h] [bp-30h]@4
  CGuild *v22; // [sp+D0h] [bp-28h]@4
  unsigned __int64 v23; // [sp+E0h] [bp-18h]@4
  _DB_QRY_SYN_DATA *v24; // [sp+100h] [bp+8h]@1

  v24 = pData;
  v1 = &v3;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v23 = (unsigned __int64)&v3 ^ _security_cookie;
  v9 = -1;
  v10 = -1;
  dwEmblemBack = -1;
  dwEmblemMark = -1;
  dwIOerSerial = -1;
  v15 = 0;
  v16 = 0;
  v17 = 0.0;
  v18 = 0.0;
  Dst = 0;
  memset(&v20, 0, 3ui64);
  v21 = v24->m_sData;
  v9 = *(_DWORD *)&v24->m_sData[24];
  v10 = *(_DWORD *)&v24->m_sData[0];
  dwEmblemBack = *(_DWORD *)&v24->m_sData[4];
  dwEmblemMark = *(_DWORD *)&v24->m_sData[8];
  dwIOerSerial = *(_DWORD *)&v24->m_sData[16];
  v16 = *(_DWORD *)&v24->m_sData[12];
  v18 = *(double *)&v24->m_sData[56];
  v17 = *(double *)&v24->m_sData[48];
  memcpy_0(&Dst, &v24->m_sData[20], 4ui64);
  strcpy_0(&Dest, v21 + 28);
  v22 = &g_Guild[v9];
  if ( v22->m_dwSerial == v10 )
  {
    v22->m_bIOWait = 0;
    if ( v21[64] )
    {
      *(_DWORD *)bInPut = (unsigned __int8)v21[64];
      LODWORD(pbyDate) = *((_DWORD *)v21 + 4);
      v6 = v21 + 28;
      LODWORD(v5) = v16;
      v4 = v17;
      CLogFile::Write(
        &stru_1799C8E78,
        "CPlayer::Guild_Buy_Emblem_Complete(...) : \r\n"
        "\t\tGuild(%u) TotD(%f) TotG(%f) SubD(%d) %s(%u)\r\n"
        "\t\tqry_case_buyemblem Ret(%u) Fail!",
        v10,
        v18);
    }
    else if ( !v24->m_byResult )
    {
      v22->m_byMoneyOutputKind = 1;
      CGuild::IOMoney(v22, &Dest, dwIOerSerial, (double)v16, 0.0, v18, v17, &Dst, 0);
      CGuild::UpdateEmblem(v22, dwEmblemBack, dwEmblemMark);
    }
  }
}
