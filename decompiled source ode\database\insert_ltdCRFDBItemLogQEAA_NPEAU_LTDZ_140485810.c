/*
 * Function: ?insert_ltd@CRFDBItemLog@@QEAA_NPEAU_LTD@@@Z
 * Address: 0x140485810
 */

bool __fastcall CRFDBItemLog::insert_ltd(CRFDBItemLog *this, _LTD *pl)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@4
  int v5; // ecx@4
  int v6; // edx@4
  int v7; // edi@4
  int v8; // er8@4
  int v9; // er9@4
  int v10; // er10@4
  int v11; // er11@4
  unsigned int v12; // ebx@4
  __int64 v14; // [sp+0h] [bp-2B8h]@1
  int v15; // [sp+20h] [bp-298h]@4
  int v16; // [sp+28h] [bp-290h]@4
  int v17; // [sp+30h] [bp-288h]@4
  int v18; // [sp+38h] [bp-280h]@4
  int v19; // [sp+40h] [bp-278h]@4
  int v20; // [sp+48h] [bp-270h]@4
  int v21; // [sp+50h] [bp-268h]@4
  int v22; // [sp+58h] [bp-260h]@4
  unsigned int v23; // [sp+60h] [bp-258h]@4
  unsigned int v24; // [sp+68h] [bp-250h]@4
  char Dest; // [sp+80h] [bp-238h]@4
  char v26; // [sp+81h] [bp-237h]@4
  unsigned __int64 v27; // [sp+290h] [bp-28h]@4
  CRFDBItemLog *v28; // [sp+2C0h] [bp+8h]@1
  _LTD *v29; // [sp+2C8h] [bp+10h]@1

  v29 = pl;
  v28 = this;
  v2 = &v14;
  for ( i = 168i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v27 = (unsigned __int64)&v14 ^ _security_cookie;
  Dest = 0;
  memset(&v26, 0, 0x1FFui64);
  v4 = pl->m_bySubLogType;
  v5 = pl->m_byMainLogType;
  v6 = pl->m_timeLocal.wMilliseconds;
  v7 = v29->m_timeLocal.wSecond;
  v8 = v29->m_timeLocal.wMinute;
  v9 = v29->m_timeLocal.wHour;
  v10 = v29->m_timeLocal.wDay;
  v11 = v29->m_timeLocal.wMonth;
  v12 = v29->m_timeLocal.wYear;
  v24 = v29->m_dwCharSerial;
  v23 = v29->m_dwAccountSerial;
  v22 = v4;
  v21 = v5;
  v20 = v6;
  v19 = v7;
  v18 = v8;
  v17 = v9;
  v16 = v10;
  v15 = v11;
  sprintf(
    &Dest,
    "insert into [dbo].[tbl_ltd_%d](LogSerial, MainType, SubType, AccountSerial, CharSerial) values ('%04d-%02d-%02d %02d"
    ":%02d:%02d.%03d',%d,%d,%d,%d)",
    v28->m_dwKorTime,
    v12);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v28->vfptr, &Dest, 1);
}
