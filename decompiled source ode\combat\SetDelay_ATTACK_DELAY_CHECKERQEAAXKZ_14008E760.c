/*
 * Function: ?SetDelay@_ATTACK_DELAY_CHECKER@@QEAAXK@Z
 * Address: 0x14008E760
 */

void __fastcall _ATTACK_DELAY_CHECKER::SetDelay(_ATTACK_DELAY_CHECKER *this, unsigned int delay)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  DWORD v5; // [sp+20h] [bp-18h]@4
  int v6; // [sp+24h] [bp-14h]@6
  int j; // [sp+28h] [bp-10h]@6
  _ATTACK_DELAY_CHECKER *v8; // [sp+40h] [bp+8h]@1
  unsigned int v9; // [sp+48h] [bp+10h]@1

  v9 = delay;
  v8 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = v8->m_nNextAddTime + timeGetTime();
  if ( v8->byTemp_EffectCode == 255 )
  {
    v8->dwNextGenTime = v9 + v5;
    v8->dwLastGnAttackTime = v5;
  }
  else
  {
    v6 = -1;
    for ( j = 0; j < 10; ++j )
    {
      if ( _ATTACK_DELAY_CHECKER::_eff_list::fill((_ATTACK_DELAY_CHECKER::_eff_list *)v8 + j) )
      {
        if ( v8->EFF[j].byEffectCode == v8->byTemp_EffectCode && v8->EFF[j].wEffectIndex == v8->wTemp_EffectIndex )
        {
          v8->EFF[j].dwNextTime = v9 + v5;
          v6 = -1;
          break;
        }
        if ( v8->EFF[j].dwNextTime < v5 )
        {
          _ATTACK_DELAY_CHECKER::_eff_list::init((_ATTACK_DELAY_CHECKER::_eff_list *)v8 + j);
          v6 = j;
        }
      }
      else
      {
        v6 = j;
      }
    }
    if ( v6 != -1 )
    {
      v8->EFF[v6].byEffectCode = v8->byTemp_EffectCode;
      v8->EFF[v6].wEffectIndex = v8->wTemp_EffectIndex;
      v8->EFF[v6].dwNextTime = v9 + v5;
    }
    if ( v8->byTemp_EffectCode != 2 && v8->byTemp_EffectMastery != 255 )
    {
      v6 = -1;
      for ( j = 0; j < 10; ++j )
      {
        if ( _ATTACK_DELAY_CHECKER::_mas_list::fill(&v8->MAS[j]) )
        {
          if ( v8->MAS[j].byEffectCode == v8->byTemp_EffectCode && v8->MAS[j].byMastery == v8->byTemp_EffectMastery )
          {
            v8->MAS[j].dwNextTime = v9 + v5;
            v6 = -1;
            break;
          }
          if ( v8->MAS[j].dwNextTime < v5 )
          {
            _ATTACK_DELAY_CHECKER::_mas_list::init(&v8->MAS[j]);
            v6 = j;
          }
        }
        else
        {
          v6 = j;
        }
      }
      if ( v6 != -1 )
      {
        v8->MAS[v6].byEffectCode = v8->byTemp_EffectCode;
        v8->MAS[v6].byMastery = v8->byTemp_EffectMastery;
        v8->MAS[v6].dwNextTime = v9 + v5;
      }
    }
    v8->dwNextEffTime = v5 + 1000;
    v8->dwLastSFAttackTime = v5;
  }
}
