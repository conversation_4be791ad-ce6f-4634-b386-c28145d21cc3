/*
 * Function: j_?IsExistGroupID@CUnmannedTraderDivisionInfo@@QEAA_NEEEEAEAK@Z
 * Address: 0x14000C7D4
 */

bool __fastcall CUnmannedTraderDivisionInfo::IsExistGroupID(CUnmannedTraderDivisionInfo *this, char byDivision, char byClass, char bySubClass, char bySortType, unsigned int *dwListIndex)
{
  return CUnmannedTraderDivisionInfo::IsExistGroupID(this, byDivision, byClass, bySubClass, bySortType, dwListIndex);
}
