/*
 * Function: ?Emb_StartQuest@CPlayer@@QEAA_NEPEAU_happen_event_cont@@@Z
 * Address: 0x1400CBE20
 */

char __fastcall CPlayer::Emb_StartQuest(CPlayer *this, char bySelectQuest, _happen_event_cont *pHappenEvent)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-88h]@1
  bool bUpdate; // [sp+20h] [bp-68h]@44
  bool bSend[8]; // [sp+28h] [bp-60h]@44
  unsigned __int16 *v9; // [sp+30h] [bp-58h]@6
  int v10; // [sp+38h] [bp-50h]@8
  int j; // [sp+3Ch] [bp-4Ch]@8
  unsigned int v12; // [sp+40h] [bp-48h]@18
  _QUEST_DB_BASE *v13; // [sp+48h] [bp-40h]@21
  _QUEST_DB_BASE *v14; // [sp+50h] [bp-38h]@28
  _QUEST_DB_BASE::_LIST *pSlotData; // [sp+58h] [bp-30h]@30
  int k; // [sp+60h] [bp-28h]@30
  int l; // [sp+64h] [bp-24h]@38
  _STORAGE_LIST::_db_con *pItem; // [sp+68h] [bp-20h]@42
  unsigned int dwDur; // [sp+70h] [bp-18h]@44
  CPlayer *v20; // [sp+90h] [bp+8h]@1
  _happen_event_cont *v21; // [sp+A0h] [bp+18h]@1

  v21 = pHappenEvent;
  v20 = this;
  v3 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( !v20->m_pUserDB )
    return 0;
  v9 = (unsigned __int16 *)CQuestMgr::GetQuestFromEvent(&v20->m_QuestMgr, bySelectQuest);
  if ( !v9 )
    return 0;
  v10 = -1;
  for ( j = 0; j < 30; ++j )
  {
    if ( v20->m_Param.m_QuestDB.m_List[j].byQuestType == 255 )
    {
      v10 = j;
      break;
    }
  }
  if ( v21->m_pEvent->m_nQuestType )
  {
    if ( CQuestMgr::GetCountQuestType(&v20->m_QuestMgr, v21->m_pEvent->m_nQuestType) >= 5 )
      return 0;
  }
  else if ( CQuestMgr::GetCountQuestType(&v20->m_QuestMgr, v21->m_pEvent->m_nQuestType) >= 20 )
  {
    v10 = -1;
    v12 = 0;
    for ( j = 0; j < 30; ++j )
    {
      v13 = (_QUEST_DB_BASE *)((char *)&v20->m_Param.m_QuestDB + 13 * j);
      if ( v13->m_List[0].byQuestType == v21->m_pEvent->m_nQuestType )
      {
        if ( v13->m_List[0].dwPassSec == -1 )
          return 0;
        if ( v13->m_List[0].dwPassSec >= v12 )
        {
          v12 = v13->m_List[0].dwPassSec;
          v10 = j;
        }
      }
    }
    if ( v10 != -1 )
    {
      v14 = (_QUEST_DB_BASE *)((char *)&v20->m_Param.m_QuestDB + 13 * v10);
      CPlayer::SendMsg_QuestFailure(v20, 5, v10);
      CQuestMgr::DeleteQuestData(&v20->m_QuestMgr, v10);
      CUserDB::Update_QuestDelete(v20->m_pUserDB, v10);
    }
  }
  if ( v10 == -1 )
  {
    result = 0;
  }
  else
  {
    pSlotData = &v20->m_Param.m_QuestDB.m_List[v10];
    pSlotData->byQuestType = v21->m_pEvent->m_nQuestType;
    pSlotData->wIndex = *v9;
    pSlotData->dwPassSec = 0;
    for ( k = 0; k < 3; ++k )
    {
      pSlotData->wNum[k] = -1;
      if ( *(_DWORD *)&v9[136 * k + 52] != -1 )
        pSlotData->wNum[k] = 0;
      if ( *(_DWORD *)&v9[136 * k + 150] == -1 )
        pSlotData->wNum[k] = -1;
    }
    CUserDB::Update_QuestInsert(v20->m_pUserDB, v10, pSlotData);
    CPlayer::SendMsg_InsertNewQuest(v20, v10, pSlotData);
    if ( v21->m_pEvent->m_nQuestType == 1 )
    {
      for ( l = 0; l < 5; ++l )
      {
        if ( v21->m_pEvent->m_CondNode[l].m_nCondType == 8 )
        {
          pItem = _STORAGE_LIST::GetPtrFromItemCode(
                    (_STORAGE_LIST *)&v20->m_Param.m_dbInven.m_nListNum,
                    v21->m_pEvent->m_CondNode[l].m_sCondVal);
          if ( pItem )
          {
            if ( IsOverLapItem(pItem->m_byTableCode) )
            {
              bSend[0] = 0;
              bUpdate = 0;
              dwDur = CPlayer::Emb_AlterDurPoint(v20, 0, pItem->m_byStorageIndex, -1, 0, 0);
              if ( dwDur )
                CPlayer::SendMsg_AdjustAmountInform(v20, 0, pItem->m_wSerial, dwDur);
              else
                CMgrAvatorItemHistory::delete_npc_quest_item(
                  &CPlayer::s_MgrItemHistory,
                  v20->m_ObjID.m_wIndex,
                  pItem,
                  v20->m_szItemHistoryFileName);
            }
            else
            {
              *(_QWORD *)bSend = "CPlayer::pcChatAllRequest()";
              bUpdate = 1;
              if ( !CPlayer::Emb_DelStorage(v20, 0, pItem->m_byStorageIndex, 0, 1, "CPlayer::pcChatAllRequest()") )
                return 0;
              CMgrAvatorItemHistory::delete_npc_quest_item(
                &CPlayer::s_MgrItemHistory,
                v20->m_ObjID.m_wIndex,
                pItem,
                v20->m_szItemHistoryFileName);
            }
          }
        }
      }
    }
    result = 1;
  }
  return result;
}
