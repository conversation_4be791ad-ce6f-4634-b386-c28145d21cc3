/*
 * Function: ?Inherit<PERSON><PERSON>@CPartyPlayer@@QEAA_NPEAV1@@Z
 * Address: 0x140045830
 */

char __fastcall CPartyPlayer::InheritBoss(CPartyPlayer *this, CPartyPlayer *pSuccessor)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-58h]@1
  int j; // [sp+20h] [bp-38h]@9
  char v7; // [sp+24h] [bp-34h]@20
  int k; // [sp+28h] [bp-30h]@20
  CPartyPlayer *v9; // [sp+30h] [bp-28h]@23
  int l; // [sp+38h] [bp-20h]@23
  CPartyPlayer *v11; // [sp+40h] [bp-18h]@25
  CPlayer *v12; // [sp+48h] [bp-10h]@40
  CPartyPlayer *v13; // [sp+60h] [bp+8h]@1
  CPartyPlayer *v14; // [sp+68h] [bp+10h]@1

  v14 = pSuccessor;
  v13 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CPartyPlayer::IsPartyBoss(v13) )
  {
    if ( v14->m_pPartyBoss == v13 )
    {
      if ( v13->m_pLootAuthor == v14 )
      {
        for ( j = 0; j < 8 && v13->m_pPartyMember[j]; ++j )
        {
          if ( v13->m_pLootAuthor == v13->m_pPartyMember[j] )
          {
            if ( j == 7 )
            {
              v13->m_pLootAuthor = v13->m_pPartyMember[0];
            }
            else if ( v13->m_pPartyMember[j + 1] )
            {
              v13->m_pLootAuthor = v13->m_pPartyMember[j + 1];
            }
            else
            {
              v13->m_pLootAuthor = v13->m_pPartyMember[0];
            }
            break;
          }
        }
      }
      v7 = 0;
      for ( k = 0; k < 8; ++k )
      {
        if ( v13->m_pPartyMember[k] == v14 )
        {
          v9 = v13->m_pPartyMember[0];
          for ( l = 0; l < k; ++l )
          {
            v11 = v9;
            v9 = v13->m_pPartyMember[l + 1];
            v13->m_pPartyMember[l + 1] = v11;
          }
          v13->m_pPartyMember[0] = v14;
          v7 = 1;
          break;
        }
      }
      if ( v7 )
      {
        for ( k = 0; k < 8; ++k )
          v14->m_pPartyMember[k] = v13->m_pPartyMember[k];
        v14->m_byLootShareSystem = v13->m_byLootShareSystem;
        v14->m_pLootAuthor = v13->m_pLootAuthor;
        for ( k = 0; k < 8 && v14->m_pPartyMember[k]; ++k )
          v14->m_pPartyMember[k]->m_pPartyBoss = v14;
        for ( k = 0; k < 8 && v14->m_pPartyMember[k]; ++k )
        {
          v12 = &g_Player + v14->m_pPartyMember[k]->m_wZoneIndex;
          if ( v12->m_bOper )
          {
            if ( CPlayer::GetGroupTarget(v12, 0)->pObject )
              CPlayer::SendMsg_ReleaseGroupTargetObjectResult(v12, 0);
          }
        }
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
