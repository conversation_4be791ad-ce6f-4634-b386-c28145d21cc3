/*
 * Function: ?PostSend@CPostSystemManager@@QEAAEPEAD@Z
 * Address: 0x140325EF0
 */

char __fastcall CPostSystemManager::PostSend(CPostSystemManager *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@15
  __int64 v6; // [sp+0h] [bp-E8h]@1
  char *wszSendName; // [sp+20h] [bp-C8h]@15
  char *wszRecvName; // [sp+28h] [bp-C0h]@15
  char *wszTitle; // [sp+30h] [bp-B8h]@15
  char *wszContent; // [sp+38h] [bp-B0h]@15
  int nK; // [sp+40h] [bp-A8h]@15
  unsigned __int64 dwD; // [sp+48h] [bp-A0h]@15
  unsigned int dwU; // [sp+50h] [bp-98h]@15
  unsigned int dwGold; // [sp+58h] [bp-90h]@15
  char byErr; // [sp+60h] [bp-88h]@15
  unsigned __int16 wStorageIndex; // [sp+68h] [bp-80h]@15
  char *pbyNumber; // [sp+70h] [bp-78h]@15
  bool bGetNumber; // [sp+78h] [bp-70h]@15
  unsigned __int64 lnUID; // [sp+80h] [bp-68h]@15
  char *v20; // [sp+90h] [bp-58h]@4
  unsigned int j; // [sp+98h] [bp-50h]@4
  char *v22; // [sp+A0h] [bp-48h]@6
  int v23; // [sp+A8h] [bp-40h]@7
  char v24; // [sp+ACh] [bp-3Ch]@10
  unsigned int dwOwner; // [sp+B0h] [bp-38h]@10
  char v26; // [sp+C4h] [bp-24h]@14

  v2 = &v6;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v20 = pData;
  for ( j = 0; j < *(_DWORD *)v20; ++j )
  {
    v22 = &v20[312 * j + 8];
    if ( !*v22 )
    {
      v23 = 0;
      v23 = CRFWorldDatabase::Select_PostRecvStorageCheck(pkDB, *((_DWORD *)v22 + 3));
      if ( v23 >= 50 )
        *v22 = 10;
    }
    if ( *v22 )
    {
      v24 = 100;
      dwOwner = *((_DWORD *)v22 + 4);
    }
    else
    {
      v24 = 0;
      dwOwner = *((_DWORD *)v22 + 3);
    }
    if ( !CRFWorldDatabase::Select_PostStorageRecordCheck(pkDB) )
      CRFWorldDatabase::Insert_PostStorageRecord(pkDB);
    v26 = 0;
    if ( CRFWorldDatabase::Select_PostStorageEmptyRecordSerial(pkDB, (unsigned int *)v22 + 2) )
    {
      v4 = _INVENKEY::CovDBKey((_INVENKEY *)v22 + 69);
      lnUID = *((_QWORD *)v22 + 37);
      bGetNumber = 1;
      pbyNumber = &v26;
      wStorageIndex = 255;
      byErr = *v22;
      dwGold = *((_DWORD *)v22 + 76);
      dwU = *((_DWORD *)v22 + 72);
      dwD = *((_QWORD *)v22 + 35);
      nK = v4;
      wszContent = v22 + 75;
      wszTitle = v22 + 54;
      wszRecvName = v22 + 37;
      wszSendName = v22 + 20;
      CRFWorldDatabase::Update_PostStorageSendToRecver(
        pkDB,
        dwOwner,
        *((_DWORD *)v22 + 2),
        v24,
        v22 + 20,
        v22 + 37,
        v22 + 54,
        v22 + 75,
        v4,
        dwD,
        dwU,
        dwGold,
        byErr,
        0xFFu,
        &v26,
        1,
        lnUID);
    }
  }
  return 0;
}
