/*
 * Function: ?CheckRegister@CPostSystemManager@@QEAAEPEAVCPlayer@@PEAU_STORAGE_POS_INDIV@@KAEAPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x140325A50
 */

char __fastcall CPostSystemManager::CheckRegister(CPostSystemManager *this, CPlayer *pOne, _STORAGE_POS_INDIV *pItemInfo, unsigned int dwGold, _STORAGE_LIST::_db_con **pItem)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned int v8; // eax@9
  __int64 v9; // [sp+0h] [bp-38h]@1
  unsigned __int64 v10; // [sp+20h] [bp-18h]@8
  _STORAGE_LIST *v11; // [sp+28h] [bp-10h]@15
  CPostSystemManager *v12; // [sp+40h] [bp+8h]@1
  CPlayer *v13; // [sp+48h] [bp+10h]@1
  _STORAGE_POS_INDIV *v14; // [sp+50h] [bp+18h]@1
  unsigned int v15; // [sp+58h] [bp+20h]@1

  v15 = dwGold;
  v14 = pItemInfo;
  v13 = pOne;
  v12 = this;
  v5 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, pOne->m_id.wIndex) == 99 )
    return 17;
  if ( CNetIndexList::size(&v12->m_listEmpty) <= 0 )
    return 1;
  v10 = v15 + 5i64;
  if ( v10 > 0x7A120 )
    return 4;
  v8 = CPlayerDB::GetGold(&v13->m_Param);
  if ( v10 > v8 )
    return 4;
  if ( v14->byStorageCode >= 8 || v14->byStorageCode )
    return 3;
  if ( v14->wItemSerial == 0xFFFF )
    goto LABEL_30;
  v11 = v13->m_Param.m_pStoragePtr[v14->byStorageCode];
  *pItem = _STORAGE_LIST::GetPtrFromSerial(v11, v14->wItemSerial);
  if ( !*pItem )
    return 3;
  if ( (*pItem)->m_byTableCode == 19 || (*pItem)->m_bLock )
    return 6;
  if ( (*pItem)->m_byCsMethod )
    return 6;
  if ( !IsExchangeItem((*pItem)->m_byTableCode, (*pItem)->m_wItemIndex) )
    return 2;
  if ( IsOverLapItem((*pItem)->m_byTableCode) && v14->byNum > (*pItem)->m_dwDur )
    result = 5;
  else
LABEL_30:
    result = 0;
  return result;
}
