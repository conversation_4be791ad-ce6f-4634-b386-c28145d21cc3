/*
 * Function: j_??$_Umove@PEAVCUnmannedTraderUserInfo@@@?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@IEAAPEAVCUnmannedTraderUserInfo@@PEAV2@00@Z
 * Address: 0x1400112F7
 */

CUnmannedTraderUserInfo *__fastcall std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Umove<CUnmannedTraderUserInfo *>(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this, CUnmannedTraderUserInfo *_First, CUnmannedTraderUserInfo *_Last, CUnmannedTraderUserInfo *_Ptr)
{
  return std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Umove<CUnmannedTraderUserInfo *>(
           this,
           _First,
           _Last,
           _Ptr);
}
