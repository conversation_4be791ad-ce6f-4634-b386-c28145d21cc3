/*
 * Function: ??H?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEBA?AV01@_J@Z
 * Address: 0x14031E420
 */

std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator+(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *result, __int64 _Off)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v5; // rax@4
  __int64 v7; // [sp+0h] [bp-68h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> v8; // [sp+28h] [bp-40h]@4
  int v9; // [sp+54h] [bp-14h]@4
  __int64 v10; // [sp+58h] [bp-10h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__that; // [sp+70h] [bp+8h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v12; // [sp+78h] [bp+10h]@1
  __int64 _Offa; // [sp+80h] [bp+18h]@1

  _Offa = _Off;
  v12 = result;
  __that = this;
  v3 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = -2i64;
  v9 = 0;
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
    &v8,
    __that);
  v5 = std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator+=(&v8, _Offa);
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
    v12,
    v5);
  v9 |= 1u;
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(&v8);
  return v12;
}
