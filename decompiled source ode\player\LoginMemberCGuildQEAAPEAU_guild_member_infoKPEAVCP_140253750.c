/*
 * Function: ?LoginMember@CGuild@@QEAAPEAU_guild_member_info@@KPEAVCPlayer@@@Z
 * Address: 0x140253750
 */

_guild_member_info *__fastcall CGuild::LoginMember(CGuild *this, unsigned int dwMemberSerial, CPlayer *pPtr)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _guild_member_info *v8; // [sp+28h] [bp-10h]@7
  CGuild *v9; // [sp+40h] [bp+8h]@1
  unsigned int v10; // [sp+48h] [bp+10h]@1
  CPlayer *v11; // [sp+50h] [bp+18h]@1

  v11 = pPtr;
  v10 = dwMemberSerial;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; j < 50; ++j )
  {
    v8 = &v9->m_MemberData[j];
    if ( _guild_member_info::IsFill(v8) && v8->dwSerial == v10 )
    {
      v8->pPlayer = v11;
      return v8;
    }
  }
  return 0i64;
}
