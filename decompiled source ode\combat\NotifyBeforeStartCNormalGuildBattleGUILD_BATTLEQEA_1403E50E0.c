/*
 * Function: ?NotifyBeforeStart@CNormalGuildBattle@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403E50E0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::NotifyBeforeStart(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  ATL::CTimeSpan *v3; // rax@4
  ATL::CTimeSpan *v4; // rax@4
  ATL::CTimeSpan *v5; // rax@4
  ATL::CTimeSpan *v6; // rax@4
  ATL::CTimeSpan *v7; // rax@4
  char v8; // al@14
  char v9; // al@15
  __int64 v10; // [sp+0h] [bp-108h]@1
  char pbyType; // [sp+34h] [bp-D4h]@4
  char v12; // [sp+35h] [bp-D3h]@4
  char szMsg; // [sp+58h] [bp-B0h]@4
  char Dest; // [sp+59h] [bp-AFh]@7
  char v15; // [sp+6Ah] [bp-9Eh]@10
  CPlayer *v16; // [sp+88h] [bp-80h]@10
  int j; // [sp+90h] [bp-78h]@10
  ATL::CTimeSpan v18; // [sp+A0h] [bp-68h]@4
  ATL::CTimeSpan v19; // [sp+A8h] [bp-60h]@4
  ATL::CTimeSpan result; // [sp+B0h] [bp-58h]@4
  ATL::CTimeSpan v21; // [sp+B8h] [bp-50h]@4
  ATL::CTimeSpan v22; // [sp+C0h] [bp-48h]@4
  ATL::CTimeSpan *v23; // [sp+C8h] [bp-40h]@4
  ATL::CTimeSpan *v24; // [sp+D0h] [bp-38h]@4
  char *Source; // [sp+D8h] [bp-30h]@5
  char *v26; // [sp+E0h] [bp-28h]@8
  int v27; // [sp+E8h] [bp-20h]@14
  int v28; // [sp+ECh] [bp-1Ch]@15
  unsigned __int64 v29; // [sp+F0h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattle *v30; // [sp+110h] [bp+8h]@1

  v30 = this;
  v1 = &v10;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v29 = (unsigned __int64)&v10 ^ _security_cookie;
  pbyType = 27;
  v12 = 88;
  ATL::CTimeSpan::CTimeSpan(&v21, 0, 0, 1, 0);
  v23 = v3;
  ATL::CTimeSpan::CTimeSpan(&v19, 0, 0, 4, 0);
  v24 = v4;
  ATL::CTimeSpan::CTimeSpan(&v18, 0, 0, 5, 0);
  v6 = ATL::CTimeSpan::operator+(v5, &result, (ATL::CTimeSpan)v24->m_timeSpan);
  v7 = ATL::CTimeSpan::operator+(v6, &v22, (ATL::CTimeSpan)v23->m_timeSpan);
  szMsg = ATL::CTimeSpan::GetMinutes(v7);
  if ( GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v30->m_pkRed) )
    Source = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v30->m_pkRed);
  else
    Source = "None";
  strcpy_0(&Dest, Source);
  if ( GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v30->m_pkBlue) )
    v26 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v30->m_pkBlue);
  else
    v26 = "None";
  strcpy_0(&v15, v26);
  v16 = 0i64;
  for ( j = 0; j < 2532; ++j )
  {
    v16 = &g_Player + j;
    if ( v16->m_bLive )
    {
      v27 = CPlayerDB::GetRaceCode(&v16->m_Param);
      v8 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildRace(&v30->m_k1P);
      if ( v27 == (unsigned __int8)v8
        || (v28 = CPlayerDB::GetRaceCode(&v16->m_Param),
            v9 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildRace(&v30->m_k2P),
            v28 == (unsigned __int8)v9) )
      {
        CNetProcess::LoadSendMsg(unk_1414F2088, v16->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x23u);
      }
    }
  }
}
