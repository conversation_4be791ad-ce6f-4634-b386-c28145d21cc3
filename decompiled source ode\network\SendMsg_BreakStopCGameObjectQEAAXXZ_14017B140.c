/*
 * Function: ?SendMsg_BreakStop@CGameObject@@QEAAXXZ
 * Address: 0x14017B140
 */

void __fastcall CGameObject::SendMsg_BreakStop(CGameObject *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-98h]@1
  char szMsg; // [sp+38h] [bp-60h]@4
  unsigned int v5; // [sp+39h] [bp-5Fh]@4
  __int16 pShort; // [sp+3Dh] [bp-5Bh]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v8; // [sp+65h] [bp-33h]@4
  unsigned __int64 v9; // [sp+80h] [bp-18h]@4
  CGameObject *v10; // [sp+A0h] [bp+8h]@1

  v10 = this;
  v1 = &v3;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = (unsigned __int64)&v3 ^ _security_cookie;
  szMsg = v10->m_ObjID.m_byID;
  v5 = v10->m_dwObjSerial;
  FloatToShort(v10->m_fCurPos, &pShort, 3);
  pbyType = 4;
  v8 = 30;
  CGameObject::CircleReport(v10, &pbyType, &szMsg, 11, 0);
}
