/*
 * Function: ?SendMsg_BaseDownloadResult@CPlayer@@QEAAXXZ
 * Address: 0x1400D8820
 */

void __usercall CPlayer::SendMsg_BaseDownloadResult(CPlayer *this@<rcx>, long double a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@10
  float *v5; // rax@10
  CHonorGuild *v6; // rax@28
  CGuildMasterEffect *v7; // rax@31
  char v8; // al@31
  CGuildMasterEffect *v9; // rax@33
  CGuildMasterEffect *v10; // rax@33
  unsigned __int16 v11; // ax@34
  __int64 v12; // [sp+0h] [bp-1B8h]@1
  _base_download_result_zocl Dst; // [sp+40h] [bp-178h]@4
  int j; // [sp+154h] [bp-64h]@4
  unsigned __int16 *v15; // [sp+158h] [bp-60h]@10
  char *v16; // [sp+160h] [bp-58h]@12
  char *v17; // [sp+168h] [bp-50h]@18
  char pbyType; // [sp+174h] [bp-44h]@34
  char v19; // [sp+175h] [bp-43h]@34
  CGuild *v20; // [sp+190h] [bp-28h]@28
  int v21; // [sp+198h] [bp-20h]@28
  int v22; // [sp+19Ch] [bp-1Ch]@31
  unsigned __int64 v23; // [sp+1A0h] [bp-18h]@4
  CPlayer *v24; // [sp+1C0h] [bp+8h]@1

  v24 = this;
  v2 = &v12;
  for ( i = 108i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v23 = (unsigned __int64)&v12 ^ _security_cookie;
  v24->m_bBaseDownload = 1;
  Dst.byRetCode = 0;
  Dst.wZoneIndex = v24->m_ObjID.m_wIndex;
  Dst.dwExpRate = v24->m_dwExpRate;
  for ( j = 0; j < 3; ++j )
  {
    if ( v24->m_Param.m_pClassHistory[j] )
      Dst.wClassHistory[j] = v24->m_Param.m_pClassHistory[j]->m_dwIndex;
    else
      Dst.wClassHistory[j] = -1;
  }
  Dst.wHP = CPlayerDB::GetHP(&v24->m_Param);
  Dst.wSP = CPlayerDB::GetSP(&v24->m_Param);
  Dst.wFP = CPlayerDB::GetFP(&v24->m_Param);
  Dst.wDP = CPlayerDB::GetDP(&v24->m_Param);
  CPlayerDB::GetPvPPoint(&v24->m_Param);
  Dst.dPvpPoint = a2;
  CPvpOrderView::GetPvpCash(&v24->m_kPvpOrderView);
  Dst.dPvpCashBag = a2;
  CPvpOrderView::GetPvpTempCash(&v24->m_kPvpOrderView);
  Dst.dPvpTempCash = a2;
  Dst.dwPvpRank = v24->m_Param.m_dbChar.m_dwRank;
  Dst.byPvpClass = v24->m_Param.m_byPvPGrade;
  Dst.wMaxHP = v24->m_nMaxPoint[0];
  Dst.wMaxSP = v24->m_nMaxPoint[2];
  Dst.wMaxFP = v24->m_nMaxPoint[1];
  Dst.wMaxDP = CPlayer::GetMaxDP(v24);
  v4 = CPlayerDB::GetMapCode(&v24->m_Param);
  v15 = (unsigned __int16 *)CMapDataTable::GetRecord(&stru_141470B80, v4);
  Dst.wMapIndex = *v15;
  v5 = CPlayerDB::GetCurPos(&v24->m_Param);
  memcpy_0(Dst.fPos, v5, 0xCui64);
  Dst.byUseTrunkSlotNum = v24->m_Param.m_dbTrunk.m_nUsedNum;
  for ( j = 0; j < 8; ++j )
  {
    v16 = &v24->m_Param.m_dbEquip.m_pStorageList[j].m_bLoad;
    if ( *v16 )
    {
      Dst.EquipList[j].sTableCode = v16[1];
      Dst.EquipList[j].wItemIndex = *(_WORD *)(v16 + 3);
      Dst.EquipList[j].dwUptInfo = *(_DWORD *)(v16 + 13);
      Dst.EquipList[j].byCsMethod = v16[32];
      Dst.EquipList[j].dwT = *(_DWORD *)(v16 + 33);
    }
    else
    {
      Dst.EquipList[j].sTableCode = -1;
    }
  }
  for ( j = 0; j < 7; ++j )
  {
    v17 = &v24->m_Param.m_dbEmbellish.m_pStorageList[j].m_bLoad;
    if ( *v17 )
    {
      Dst.EmbellishList[j].sTableCode = v17[1];
      Dst.EmbellishList[j].wItemIndex = *(_WORD *)(v17 + 3);
      Dst.EmbellishList[j].wNum = *(_WORD *)(v17 + 5);
      Dst.EmbellishList[j].sClientIndex = v17[2];
      Dst.EmbellishList[j].byCsMethod = v17[32];
      Dst.EmbellishList[j].dwT = *(_DWORD *)(v17 + 33);
    }
    else
    {
      Dst.EmbellishList[j].sTableCode = -1;
    }
  }
  if ( CHolyStoneSystem::GetHolyMasterRace(&g_HolySys) == -1 )
  {
    if ( CHolyStoneSystem::GetSceneCode(&g_HolySys) == 1 )
      Dst.byHolyMasterState = -1;
    else
      Dst.byHolyMasterState = 100;
  }
  else
  {
    Dst.byHolyMasterState = CHolyStoneSystem::GetHolyMasterRace(&g_HolySys);
  }
  Dst.byPlusKey = v24->m_byPlusKey;
  Dst.wXorKey = v24->m_wXorKey;
  if ( v24->m_Param.m_pGuild )
  {
    Dst.dwGuildSerial = v24->m_Param.m_pGuild->m_dwSerial;
    v20 = v24->m_Param.m_pGuild;
    v21 = CPlayerDB::GetRaceCode(&v24->m_Param);
    v6 = CHonorGuild::Instance();
    Dst.byHonorGuildRank = CHonorGuild::FindHonorGuildRank(v6, v21, v20->m_dwSerial);
  }
  else
  {
    Dst.dwGuildSerial = -1;
    Dst.byHonorGuildRank = -1;
  }
  Dst.byGuildGrade = 0;
  Dst.byEffectValue[0] = 0;
  Dst.byEffectValue[1] = 0;
  if ( v24->m_Param.m_pGuild )
  {
    v7 = CGuildMasterEffect::GetInstance();
    v22 = (unsigned __int8)CGuildMasterEffect::get_AdjustableGrade(v7);
    v8 = CGuild::GetGrade(v24->m_Param.m_pGuild);
    if ( v22 <= (unsigned __int8)v8 && v24->m_Param.m_byClassInGuild == 2 )
    {
      Dst.byGuildGrade = CGuild::GetGrade(v24->m_Param.m_pGuild);
      v9 = CGuildMasterEffect::GetInstance();
      Dst.byEffectValue[0] = CGuildMasterEffect::get_AttactValueByGrade(v9, Dst.byGuildGrade);
      v10 = CGuildMasterEffect::GetInstance();
      Dst.byEffectValue[1] = CGuildMasterEffect::get_DefenceValueByGrade(v10, Dst.byGuildGrade);
    }
  }
  pbyType = 3;
  v19 = 4;
  v11 = _base_download_result_zocl::size(&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2088, v24->m_ObjID.m_wIndex, &pbyType, &Dst.byRetCode, v11);
}
