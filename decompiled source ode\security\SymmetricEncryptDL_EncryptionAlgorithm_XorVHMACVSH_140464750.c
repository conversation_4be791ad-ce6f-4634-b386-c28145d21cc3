/*
 * Function: ?SymmetricEncrypt@?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@CryptoPP@@UEBAXAEAVRandomNumberGenerator@2@PEBE1_KPEAEAEBVNameValuePairs@2@@Z
 * Address: 0x140464750
 */

void __fastcall CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>::SymmetricEncrypt(CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *this, CryptoPP::RandomNumberGenerator *rng, const char *key, const char *plaintext, unsigned __int64 plaintextLength, char *ciphertext, CryptoPP::NameValuePairs *parameters)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  const char *v9; // rax@4
  const unsigned __int8 *v10; // rax@4
  __int64 v11; // [sp+0h] [bp-1E8h]@1
  char *xorBlock; // [sp+20h] [bp-1C8h]@4
  unsigned __int8 *v13; // [sp+30h] [bp-1B8h]@4
  char *keya; // [sp+38h] [bp-1B0h]@4
  CryptoPP::ConstByteArrayParameter value; // [sp+48h] [bp-1A0h]@4
  CryptoPP::HMAC<CryptoPP::SHA1> v16; // [sp+98h] [bp-150h]@4
  __int64 v17; // [sp+1C0h] [bp-28h]@4
  unsigned __int64 v18; // [sp+1C8h] [bp-20h]@4
  unsigned __int64 v19; // [sp+1D0h] [bp-18h]@4
  unsigned __int8 *v20; // [sp+208h] [bp+20h]@1

  v20 = (unsigned __int8 *)plaintext;
  v7 = &v11;
  for ( i = 120i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v17 = -2i64;
  v19 = (unsigned __int64)&v11 ^ _security_cookie;
  v13 = (unsigned __int8 *)key;
  keya = (char *)&key[plaintextLength];
  CryptoPP::ConstByteArrayParameter::ConstByteArrayParameter(&value, 0i64, 0);
  v9 = CryptoPP::Name::EncodingParameters();
  CryptoPP::NameValuePairs::GetValue<CryptoPP::ConstByteArrayParameter>(parameters, v9, &value);
  CryptoPP::xorbuf(
    (CryptoPP *)ciphertext,
    v20,
    v13,
    (const unsigned __int8 *)plaintextLength,
    (unsigned __int64)xorBlock);
  CryptoPP::HMAC<CryptoPP::SHA1>::HMAC<CryptoPP::SHA1>(&v16, keya, 0x10ui64);
  CryptoPP::HMAC_Base::Update((CryptoPP::HMAC_Base *)&v16.vfptr, (const unsigned __int8 *)ciphertext, plaintextLength);
  v18 = CryptoPP::ConstByteArrayParameter::size(&value);
  v10 = (const unsigned __int8 *)CryptoPP::ConstByteArrayParameter::begin(&value);
  CryptoPP::HMAC_Base::Update((CryptoPP::HMAC_Base *)&v16.vfptr, v10, v18);
  CryptoPP::HashTransformation::Final((CryptoPP::HashTransformation *)&v16.vfptr, &ciphertext[plaintextLength]);
  CryptoPP::HMAC<CryptoPP::SHA1>::~HMAC<CryptoPP::SHA1>(&v16);
  CryptoPP::ConstByteArrayParameter::~ConstByteArrayParameter(&value);
}
