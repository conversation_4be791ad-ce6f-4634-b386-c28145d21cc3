/*
 * Function: ?IsNormalStringDefProc@CNationSettingData@@IEAA_NPEBD0@Z
 * Address: 0x140211F40
 */

char __fastcall CNationSettingData::IsNormalStringDefProc(CNationSettingData *this, const char *szString, const char *szException)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v6; // eax@10
  __int64 v7; // [sp+0h] [bp-4A8h]@1
  LPWSTR lpWideCharStr; // [sp+20h] [bp-488h]@6
  int cchWideChar; // [sp+28h] [bp-480h]@6
  wchar_t WideCharStr; // [sp+40h] [bp-468h]@6
  char v11; // [sp+42h] [bp-466h]@6
  wchar_t _Str; // [sp+260h] [bp-248h]@6
  char v13; // [sp+262h] [bp-246h]@6
  int j; // [sp+464h] [bp-44h]@8
  wchar_t _SubStr; // [sp+474h] [bp-34h]@20
  __int16 v16; // [sp+476h] [bp-32h]@20
  CNationSettingDataVtbl *v17; // [sp+490h] [bp-18h]@17
  unsigned __int64 v18; // [sp+498h] [bp-10h]@4
  CNationSettingData *v19; // [sp+4B0h] [bp+8h]@1
  char *strData; // [sp+4B8h] [bp+10h]@1
  LPCSTR lpMultiByteStr; // [sp+4C0h] [bp+18h]@1

  lpMultiByteStr = szException;
  strData = (char *)szString;
  v19 = this;
  v3 = &v7;
  for ( i = 296i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v18 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( CNationSettingData::CheckDBCSCompleteString(v19, v19->m_iANSICodePage, szString, 0i64) )
  {
    WideCharStr = 0;
    memset(&v11, 0, 0x1FEui64);
    _Str = 0;
    memset(&v13, 0, 0x1FEui64);
    cchWideChar = 256;
    lpWideCharStr = &WideCharStr;
    MultiByteToWideChar(v19->m_iANSICodePage, 0, strData, -1, &WideCharStr, 256);
    if ( lpMultiByteStr )
    {
      cchWideChar = 256;
      lpWideCharStr = &_Str;
      MultiByteToWideChar(v19->m_iANSICodePage, 0, lpMultiByteStr, -1, &_Str, 256);
    }
    for ( j = 0; ; ++j )
    {
      v6 = lstrlenW(&WideCharStr);
      if ( j >= v6 )
        break;
      if ( ((signed int)*(&WideCharStr + j) < 65 || (signed int)*(&WideCharStr + j) > 90)
        && ((signed int)*(&WideCharStr + j) < 97 || (signed int)*(&WideCharStr + j) > 122)
        && !iswctype(*(&WideCharStr + j), 4u) )
      {
        v17 = v19->vfptr;
        if ( !(unsigned __int8)((int (__fastcall *)(CNationSettingData *, _QWORD))v17->IsNormalChar)(
                                 v19,
                                 *(&WideCharStr + j))
          && !iswctype(*(&WideCharStr + j), 0x103u) )
        {
          if ( !lpMultiByteStr )
            return 0;
          _SubStr = *(&WideCharStr + j);
          v16 = 0;
          if ( !wcsstr(&_Str, &_SubStr) )
            return 0;
        }
      }
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
