/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAPEAUScheduleMSG@@PEAPEAU1@V?$allocator@PEAUScheduleMSG@@@std@@@stdext@@YAPEAPEAUSchedule<PERSON>G@@PEAPEAU1@00AEAV?$allocator@PEAUScheduleMSG@@@std@@@Z
 * Address: 0x1400104C9
 */

ScheduleMSG **__fastcall stdext::_Unchecked_uninitialized_move<ScheduleMSG * *,ScheduleMSG * *,std::allocator<ScheduleMSG *>>(ScheduleMSG **_First, ScheduleMSG **_Last, ScheduleMSG **_Dest, std::allocator<ScheduleMSG *> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<ScheduleMSG * *,ScheduleMSG * *,std::allocator<ScheduleMSG *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
