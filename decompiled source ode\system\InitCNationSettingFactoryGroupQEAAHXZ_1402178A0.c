/*
 * Function: ?Init@CNationSettingFactoryGroup@@QEAAHXZ
 * Address: 0x1402178A0
 */

signed __int64 __fastcall CNationSettingFactoryGroup::Init(CNationSettingFactoryGroup *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingFactory *v3; // rax@5
  signed __int64 result; // rax@8
  CNationSettingFactory *v5; // rax@10
  CNationSettingFactory *v6; // rax@15
  CNationSettingFactory *v7; // rax@20
  CNationSettingFactory *v8; // rax@25
  CNationSettingFactory *v9; // rax@30
  CNationSettingFactory *v10; // rax@35
  CNationSettingFactory *v11; // rax@40
  CNationSettingFactory *v12; // rax@45
  CNationSettingFactory *v13; // rax@50
  CNationSettingFactory *v14; // rax@55
  CNationSettingFactory *v15; // rax@60
  __int64 v16; // [sp+0h] [bp-158h]@1
  CNationSettingFactory *pData; // [sp+20h] [bp-138h]@7
  CNationSettingFactoryKR *v18; // [sp+28h] [bp-130h]@4
  CNationSettingFactory *v19; // [sp+30h] [bp-128h]@12
  CNationSettingFactoryGB *v20; // [sp+38h] [bp-120h]@9
  CNationSettingFactory *v21; // [sp+40h] [bp-118h]@17
  CNationSettingFactoryID *v22; // [sp+48h] [bp-110h]@14
  CNationSettingFactory *v23; // [sp+50h] [bp-108h]@22
  CNationSettingFactoryJP *v24; // [sp+58h] [bp-100h]@19
  CNationSettingFactory *v25; // [sp+60h] [bp-F8h]@27
  CNationSettingFactoryPH *v26; // [sp+68h] [bp-F0h]@24
  CNationSettingFactory *v27; // [sp+70h] [bp-E8h]@32
  CNationSettingFactoryRU *v28; // [sp+78h] [bp-E0h]@29
  CNationSettingFactory *v29; // [sp+80h] [bp-D8h]@37
  CNationSettingFactoryBR *v30; // [sp+88h] [bp-D0h]@34
  CNationSettingFactory *v31; // [sp+90h] [bp-C8h]@42
  CNationSettingFactoryTW *v32; // [sp+98h] [bp-C0h]@39
  CNationSettingFactory *v33; // [sp+A0h] [bp-B8h]@47
  CNationSettingFactoryCN *v34; // [sp+A8h] [bp-B0h]@44
  CNationSettingFactory *v35; // [sp+B0h] [bp-A8h]@52
  CNationSettingFactoryUS *v36; // [sp+B8h] [bp-A0h]@49
  CNationSettingFactory *v37; // [sp+C0h] [bp-98h]@57
  CNationSettingFactoryES *v38; // [sp+C8h] [bp-90h]@54
  CNationSettingFactory *v39; // [sp+D0h] [bp-88h]@62
  CNationSettingFactoryTH *v40; // [sp+D8h] [bp-80h]@59
  __int64 v41; // [sp+E0h] [bp-78h]@4
  CNationSettingFactory *v42; // [sp+E8h] [bp-70h]@5
  CNationSettingFactory *v43; // [sp+F0h] [bp-68h]@10
  CNationSettingFactory *v44; // [sp+F8h] [bp-60h]@15
  CNationSettingFactory *v45; // [sp+100h] [bp-58h]@20
  CNationSettingFactory *v46; // [sp+108h] [bp-50h]@25
  CNationSettingFactory *v47; // [sp+110h] [bp-48h]@30
  CNationSettingFactory *v48; // [sp+118h] [bp-40h]@35
  CNationSettingFactory *v49; // [sp+120h] [bp-38h]@40
  CNationSettingFactory *v50; // [sp+128h] [bp-30h]@45
  CNationSettingFactory *v51; // [sp+130h] [bp-28h]@50
  CNationSettingFactory *v52; // [sp+138h] [bp-20h]@55
  CNationSettingFactory *v53; // [sp+140h] [bp-18h]@60
  CNationSettingFactoryGroup *v54; // [sp+160h] [bp+8h]@1

  v54 = this;
  v1 = &v16;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v41 = -2i64;
  v18 = (CNationSettingFactoryKR *)operator new(0x10ui64);
  if ( v18 )
  {
    CNationSettingFactoryKR::CNationSettingFactoryKR(v18);
    v42 = v3;
  }
  else
  {
    v42 = 0i64;
  }
  pData = v42;
  if ( CHashMapPtrPool<int,CNationSettingFactory>::regist(&v54->m_kPool, v42) )
  {
    result = 0xFFFFFFFFi64;
  }
  else
  {
    v20 = (CNationSettingFactoryGB *)operator new(0x10ui64);
    if ( v20 )
    {
      CNationSettingFactoryGB::CNationSettingFactoryGB(v20);
      v43 = v5;
    }
    else
    {
      v43 = 0i64;
    }
    v19 = v43;
    if ( CHashMapPtrPool<int,CNationSettingFactory>::regist(&v54->m_kPool, v43) )
    {
      result = 4294967294i64;
    }
    else
    {
      v22 = (CNationSettingFactoryID *)operator new(0x10ui64);
      if ( v22 )
      {
        CNationSettingFactoryID::CNationSettingFactoryID(v22);
        v44 = v6;
      }
      else
      {
        v44 = 0i64;
      }
      v21 = v44;
      if ( CHashMapPtrPool<int,CNationSettingFactory>::regist(&v54->m_kPool, v44) )
      {
        result = 4294967293i64;
      }
      else
      {
        v24 = (CNationSettingFactoryJP *)operator new(0x10ui64);
        if ( v24 )
        {
          CNationSettingFactoryJP::CNationSettingFactoryJP(v24);
          v45 = v7;
        }
        else
        {
          v45 = 0i64;
        }
        v23 = v45;
        if ( CHashMapPtrPool<int,CNationSettingFactory>::regist(&v54->m_kPool, v45) )
        {
          result = 4294967292i64;
        }
        else
        {
          v26 = (CNationSettingFactoryPH *)operator new(0x10ui64);
          if ( v26 )
          {
            CNationSettingFactoryPH::CNationSettingFactoryPH(v26);
            v46 = v8;
          }
          else
          {
            v46 = 0i64;
          }
          v25 = v46;
          if ( CHashMapPtrPool<int,CNationSettingFactory>::regist(&v54->m_kPool, v46) )
          {
            result = 4294967291i64;
          }
          else
          {
            v28 = (CNationSettingFactoryRU *)operator new(0x10ui64);
            if ( v28 )
            {
              CNationSettingFactoryRU::CNationSettingFactoryRU(v28);
              v47 = v9;
            }
            else
            {
              v47 = 0i64;
            }
            v27 = v47;
            if ( CHashMapPtrPool<int,CNationSettingFactory>::regist(&v54->m_kPool, v47) )
            {
              result = 4294967290i64;
            }
            else
            {
              v30 = (CNationSettingFactoryBR *)operator new(0x10ui64);
              if ( v30 )
              {
                CNationSettingFactoryBR::CNationSettingFactoryBR(v30);
                v48 = v10;
              }
              else
              {
                v48 = 0i64;
              }
              v29 = v48;
              if ( CHashMapPtrPool<int,CNationSettingFactory>::regist(&v54->m_kPool, v48) )
              {
                result = 4294967289i64;
              }
              else
              {
                v32 = (CNationSettingFactoryTW *)operator new(0x10ui64);
                if ( v32 )
                {
                  CNationSettingFactoryTW::CNationSettingFactoryTW(v32);
                  v49 = v11;
                }
                else
                {
                  v49 = 0i64;
                }
                v31 = v49;
                if ( CHashMapPtrPool<int,CNationSettingFactory>::regist(&v54->m_kPool, v49) )
                {
                  result = 4294967288i64;
                }
                else
                {
                  v34 = (CNationSettingFactoryCN *)operator new(0x10ui64);
                  if ( v34 )
                  {
                    CNationSettingFactoryCN::CNationSettingFactoryCN(v34);
                    v50 = v12;
                  }
                  else
                  {
                    v50 = 0i64;
                  }
                  v33 = v50;
                  if ( CHashMapPtrPool<int,CNationSettingFactory>::regist(&v54->m_kPool, v50) )
                  {
                    result = 4294967287i64;
                  }
                  else
                  {
                    v36 = (CNationSettingFactoryUS *)operator new(0x10ui64);
                    if ( v36 )
                    {
                      CNationSettingFactoryUS::CNationSettingFactoryUS(v36);
                      v51 = v13;
                    }
                    else
                    {
                      v51 = 0i64;
                    }
                    v35 = v51;
                    if ( CHashMapPtrPool<int,CNationSettingFactory>::regist(&v54->m_kPool, v51) )
                    {
                      result = 4294967286i64;
                    }
                    else
                    {
                      v38 = (CNationSettingFactoryES *)operator new(0x10ui64);
                      if ( v38 )
                      {
                        CNationSettingFactoryES::CNationSettingFactoryES(v38);
                        v52 = v14;
                      }
                      else
                      {
                        v52 = 0i64;
                      }
                      v37 = v52;
                      if ( CHashMapPtrPool<int,CNationSettingFactory>::regist(&v54->m_kPool, v52) )
                      {
                        result = 4294967285i64;
                      }
                      else
                      {
                        v40 = (CNationSettingFactoryTH *)operator new(0x10ui64);
                        if ( v40 )
                        {
                          CNationSettingFactoryTH::CNationSettingFactoryTH(v40);
                          v53 = v15;
                        }
                        else
                        {
                          v53 = 0i64;
                        }
                        v39 = v53;
                        if ( CHashMapPtrPool<int,CNationSettingFactory>::regist(&v54->m_kPool, v53) )
                          result = 4294967284i64;
                        else
                          result = 0i64;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return result;
}
