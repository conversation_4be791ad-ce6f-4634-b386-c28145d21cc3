/*
 * Function: ?Destroy@CLogTypeDBTaskPool@@AEAAXXZ
 * Address: 0x1402C28D0
 */

void __fastcall CLogTypeDBTaskPool::Destroy(CLogTypeDBTaskPool *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-98h]@1
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > result; // [sp+28h] [bp-70h]@5
  bool v5; // [sp+44h] [bp-54h]@6
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > v6; // [sp+48h] [bp-50h]@6
  CLogTypeDBTask *v7; // [sp+60h] [bp-38h]@7
  CLogTypeDBTask *v8; // [sp+68h] [bp-30h]@7
  __int64 v9; // [sp+70h] [bp-28h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v10; // [sp+78h] [bp-20h]@6
  std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_Right; // [sp+80h] [bp-18h]@6
  void *v12; // [sp+88h] [bp-10h]@8
  CLogTypeDBTaskPool *v13; // [sp+A0h] [bp+8h]@1

  v13 = this;
  v1 = &v3;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = -2i64;
  if ( !std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::empty(&v13->m_vecDat) )
  {
    std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::begin(&v13->m_vecDat, &result);
    while ( 1 )
    {
      v10 = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::end(&v13->m_vecDat, &v6);
      _Right = (std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)v10;
      v5 = std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator!=(
             (std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&result._Mycont,
             (std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&v10->_Mycont);
      std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(&v6);
      if ( !v5 )
        break;
      v8 = *std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator*(&result);
      v7 = v8;
      if ( v8 )
        v12 = CLogTypeDBTask::`scalar deleting destructor'(v7, 1u);
      else
        v12 = 0i64;
      std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator++(&result);
    }
    std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::clear(&v13->m_vecDat);
    std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(&result);
  }
}
