/*
 * Function: ?Move@CCharacter@@QEAAXM@Z
 * Address: 0x140172C50
 */

void __usercall CCharacter::Move(CCharacter *this@<rcx>, float fSpeed@<xmm1>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  float v5; // xmm0_4@13
  float v6; // xmm0_4@13
  __int64 v7; // [sp+0h] [bp-98h]@1
  int v8; // [sp+20h] [bp-78h]@5
  int v9; // [sp+24h] [bp-74h]@5
  float v10; // [sp+28h] [bp-70h]@5
  char Dst; // [sp+38h] [bp-60h]@5
  float v12; // [sp+3Ch] [bp-5Ch]@13
  double v13; // [sp+58h] [bp-40h]@13
  int v14; // [sp+60h] [bp-38h]@13
  CLevel *v15; // [sp+68h] [bp-30h]@6
  CLevel *v16; // [sp+70h] [bp-28h]@7
  CLevel *v17; // [sp+78h] [bp-20h]@13
  CLevel *v18; // [sp+80h] [bp-18h]@14
  CCharacter *v19; // [sp+A0h] [bp+8h]@1

  v19 = this;
  v3 = &v7;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v19->m_bMove )
  {
    memcpy_0(v19->m_fOldPos, v19->m_fCurPos, 0xCui64);
    GetYAngle(v19->m_fCurPos, v19->m_fTarPos);
    v8 = (signed int)ffloor(a3);
    GetSqrt(v19->m_fCurPos, v19->m_fTarPos);
    v9 = (signed int)ffloor(a3);
    v10 = (float)(R3GetLoopTime() * 15.0) * fSpeed;
    memcpy_0(&Dst, v19->m_fCurPos, 0xCui64);
    if ( v10 < (float)v9 )
    {
      v13 = 6.283185307 * (double)v8 / 65535.0;
      v5 = sin_0(v13);
      v19->m_fCurPos[0] = v19->m_fCurPos[0] - (float)(v5 * v10);
      v6 = cos_0(v13);
      v19->m_fCurPos[2] = v19->m_fCurPos[2] - (float)(v6 * v10);
      v19->m_fCurPos[1] = v12;
      v17 = &v19->m_pCurMap->m_Level;
      v14 = CLevel::GetNextYposForServerFar(v17, (float *const )&Dst, v19->m_fCurPos, &v19->m_fCurPos[1]);
      if ( !v14 )
      {
        v18 = &v19->m_pCurMap->m_Level;
        if ( !(unsigned int)CLevel::GetNextYposFarProgress(v18, (float *const )&Dst, v19->m_fCurPos, &v19->m_fCurPos[1]) )
        {
          memcpy_0(v19->m_fCurPos, &Dst, 0xCui64);
          memcpy_0(v19->m_fTarPos, v19->m_fCurPos, 0xCui64);
          CCharacter::Stop(v19);
          CGameObject::SendMsg_BreakStop((CGameObject *)&v19->vfptr);
        }
      }
    }
    else
    {
      v19->m_fTarPos[1] = v19->m_fCurPos[1];
      v15 = &v19->m_pCurMap->m_Level;
      if ( CLevel::GetNextYposForServer(v15, v19->m_fTarPos, &v19->m_fTarPos[1]) )
      {
        memcpy_0(v19->m_fCurPos, v19->m_fTarPos, 0xCui64);
      }
      else
      {
        v16 = &v19->m_pCurMap->m_Level;
        if ( (unsigned int)CLevel::GetNextYposForServerFar(v16, v19->m_fCurPos, v19->m_fTarPos, &v19->m_fTarPos[1]) )
        {
          memcpy_0(v19->m_fCurPos, v19->m_fTarPos, 0xCui64);
        }
        else
        {
          memcpy_0(v19->m_fCurPos, &Dst, 0xCui64);
          memcpy_0(v19->m_fTarPos, v19->m_fCurPos, 0xCui64);
        }
      }
      CCharacter::Stop(v19);
    }
  }
  else
  {
    v19->m_bMove = 0;
  }
}
