/*
 * Function: j_??$_Fill@PEAVCUnmannedTraderRegistItemInfo@@V1@@std@@YAXPEAVCUnmannedTraderRegistItemInfo@@0AEBV1@@Z
 * Address: 0x14000C441
 */

void __fastcall std::_Fill<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo>(CUnmannedTraderRegistItemInfo *_First, CUnmannedTraderRegistItemInfo *_Last, CUnmannedTraderRegistItemInfo *_Val)
{
  std::_Fill<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo>(_First, _Last, _Val);
}
