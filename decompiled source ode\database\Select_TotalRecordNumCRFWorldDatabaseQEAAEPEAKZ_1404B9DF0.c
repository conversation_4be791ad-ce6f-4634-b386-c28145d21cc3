/*
 * Function: ?Select_TotalRecordNum@CRFWorldDatabase@@QEAAEPEAK@Z
 * Address: 0x1404B9DF0
 */

char __fastcall CRFWorldDatabase::Select_TotalRecordNum(CRFWorldDatabase *this, unsigned int *pdwTotalNum)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-108h]@1
  void *SQLStmt; // [sp+20h] [bp-E8h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-E0h]@22
  SQLLEN v8; // [sp+38h] [bp-D0h]@22
  __int16 v9; // [sp+44h] [bp-C4h]@9
  char DstBuf; // [sp+60h] [bp-A8h]@4
  char v11; // [sp+61h] [bp-A7h]@4
  char v12; // [sp+E4h] [bp-24h]@16
  unsigned __int64 v13; // [sp+F0h] [bp-18h]@4
  CRFWorldDatabase *v14; // [sp+110h] [bp+8h]@1
  unsigned int *TargetValue; // [sp+118h] [bp+10h]@1

  TargetValue = pdwTotalNum;
  v14 = this;
  v2 = &v5;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  DstBuf = 0;
  memset(&v11, 0, 0x7Fui64);
  sprintf_s(&DstBuf, 0x80ui64, "select count(serial) from tbl_StoreLimitItem_061212");
  if ( v14->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v14->vfptr, &DstBuf);
  if ( v14->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v14->vfptr) )
  {
    v9 = SQLExecDirect_0(v14->m_hStmtSelect, &DstBuf, -3);
    if ( v9 && v9 != 1 )
    {
      if ( v9 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v14->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v9, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v9, v14->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v9 = SQLFetch_0(v14->m_hStmtSelect);
      if ( v9 && v9 != 1 )
      {
        v12 = 0;
        if ( v9 == 100 )
        {
          v12 = 2;
        }
        else
        {
          SQLStmt = v14->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v9, &DstBuf, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v9, v14->m_hStmtSelect);
          v12 = 1;
        }
        if ( v14->m_hStmtSelect )
          SQLCloseCursor_0(v14->m_hStmtSelect);
        result = v12;
      }
      else
      {
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v14->m_hStmtSelect, 1u, -18, TargetValue, 0i64, &v8);
        if ( v9 && v9 != 1 )
        {
          SQLStmt = v14->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v9, &DstBuf, "SQLGetData", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v9, v14->m_hStmtSelect);
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          result = 1;
        }
        else
        {
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          if ( v14->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v14->vfptr, "%s Success", &DstBuf);
          result = 0;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v14->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1;
  }
  return result;
}
