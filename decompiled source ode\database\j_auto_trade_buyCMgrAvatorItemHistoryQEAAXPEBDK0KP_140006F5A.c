/*
 * Function: j_?auto_trade_buy@CMgrAvatorItemHistory@@QEAAXPEBDK0KPEAU_db_con@_STORAGE_LIST@@KKKPEAD@Z
 * Address: 0x140006F5A
 */

void __fastcall CMgrAvatorItemHistory::auto_trade_buy(CMgrAvatorItemHistory *this, const char *szSellerName, unsigned int dwSellerSerial, const char *szSellerID, unsigned int dwRegistSerial, _STORAGE_LIST::_db_con *pItem, unsigned int dwPrice, unsigned int dwLeftDalant, unsigned int dwLeftGold, char *pszFileName)
{
  CMgrAvatorItemHistory::auto_trade_buy(
    this,
    szSellerName,
    dwSellerSerial,
    szSellerID,
    dwRegistSerial,
    pItem,
    dwPrice,
    dwLeftDalant,
    dwLeftGold,
    pszFileName);
}
