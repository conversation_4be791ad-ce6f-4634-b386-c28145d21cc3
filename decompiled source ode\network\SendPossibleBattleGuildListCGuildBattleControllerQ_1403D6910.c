/*
 * Function: ?SendPossibleBattleGuildList@CGuildBattleController@@QEAAXHEEK@Z
 * Address: 0x1403D6910
 */

void __fastcall CGuildBattleController::SendPossibleBattleGuildList(CGuildBattleController *this, int n, char byRace, char byPage, unsigned int dwVer)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  GUILD_BATTLE::CPossibleBattleGuildListManager *v8; // [sp+30h] [bp-18h]@4
  int na; // [sp+58h] [bp+10h]@1
  char v10; // [sp+60h] [bp+18h]@1
  char v11; // [sp+68h] [bp+20h]@1

  v11 = byPage;
  v10 = byRace;
  na = n;
  v5 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v8 = GUILD_BATTLE::CPossibleBattleGuildListManager::Instance();
  GUILD_BATTLE::CPossibleBattleGuildListManager::Send(v8, na, v10, v11, dwVer);
}
