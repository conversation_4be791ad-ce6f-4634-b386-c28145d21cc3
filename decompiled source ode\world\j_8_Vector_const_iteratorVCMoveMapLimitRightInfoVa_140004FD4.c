/*
 * Function: j_??8?$_Vector_const_iterator@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEBA_NAEBV01@@Z
 * Address: 0x140004FD4
 */

bool __fastcall std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::operator==(std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *_Right)
{
  return std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::operator==(
           this,
           _Right);
}
