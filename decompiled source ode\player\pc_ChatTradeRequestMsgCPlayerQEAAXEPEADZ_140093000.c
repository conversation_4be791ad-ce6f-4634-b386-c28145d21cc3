/*
 * Function: ?pc_ChatTradeRequestMsg@CPlayer@@QEAAXEPEAD@Z
 * Address: 0x140093000
 */

void __fastcall CPlayer::pc_ChatTradeRequestMsg(CPlayer *this, char bySubType, char *pwszTradeMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CMoneySupplyMgr *v5; // rax@13
  char *v6; // rax@14
  CChatStealSystem *v7; // rax@14
  int v8; // eax@19
  __int64 v9; // [sp+0h] [bp-1D8h]@1
  int v10; // [sp+30h] [bp-1A8h]@9
  _announ_message_receipt_udp Dst; // [sp+50h] [bp-188h]@14
  char pbyType; // [sp+184h] [bp-54h]@14
  char v13; // [sp+185h] [bp-53h]@14
  int v14; // [sp+194h] [bp-44h]@14
  int j; // [sp+198h] [bp-40h]@14
  CPlayer *v16; // [sp+1A0h] [bp-38h]@18
  int nLv; // [sp+1B0h] [bp-28h]@13
  int v18; // [sp+1B4h] [bp-24h]@13
  int v19; // [sp+1B8h] [bp-20h]@19
  unsigned __int64 v20; // [sp+1C0h] [bp-18h]@4
  CPlayer *pPlayer; // [sp+1E0h] [bp+8h]@1
  char v22; // [sp+1E8h] [bp+10h]@1
  const char *Str; // [sp+1F0h] [bp+18h]@1

  Str = pwszTradeMsg;
  v22 = bySubType;
  pPlayer = this;
  v3 = &v9;
  for ( i = 116i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v20 = (unsigned __int64)&v9 ^ _security_cookie;
  if ( pPlayer->m_pUserDB
    && !pPlayer->m_pUserDB->m_bChatLock
    && !CPlayer::IsPunished(pPlayer, 0, 1)
    && CPlayerDB::GetDalant(&pPlayer->m_Param) >= 0x3E8 )
  {
    CPlayer::SubDalant(pPlayer, 0x3E8u);
    CPlayer::SendMsg_AlterMoneyInform(pPlayer, 0);
    v10 = CPlayerDB::GetLevel(&pPlayer->m_Param);
    if ( v10 == 30 || v10 == 40 || v10 == 50 || v10 == 60 )
    {
      nLv = CPlayerDB::GetLevel(&pPlayer->m_Param);
      v18 = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
      v5 = CMoneySupplyMgr::Instance();
      CMoneySupplyMgr::UpdateFeeMoneyData(v5, v18, nLv, 0x3E8u);
    }
    _announ_message_receipt_udp::_announ_message_receipt_udp(&Dst);
    Dst.byMessageType = 19;
    Dst.dwSenderSerial = pPlayer->m_dwObjSerial;
    Dst.bySenderRace = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
    v6 = CPlayerDB::GetCharNameW(&pPlayer->m_Param);
    strcpy_0(Dst.wszSenderName, v6);
    Dst.bySize = strlen_0(Str);
    memcpy_0(Dst.wszChatData, Str, (unsigned __int8)Dst.bySize);
    Dst.wszChatData[(unsigned __int8)Dst.bySize] = 0;
    Dst.byPvpGrade = pPlayer->m_Param.m_byPvPGrade;
    Dst.bySubType = v22;
    pbyType = 2;
    v13 = 11;
    v7 = CChatStealSystem::Instance();
    CChatStealSystem::StealChatMsg(v7, pPlayer, Dst.byMessageType, (char *)Str);
    v14 = _announ_message_receipt_udp::size(&Dst);
    for ( j = 0; j < 2532; ++j )
    {
      v16 = &g_Player + j;
      if ( v16->m_bLive )
      {
        v19 = CPlayerDB::GetRaceCode(&v16->m_Param);
        v8 = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
        if ( v19 == v8 )
          CNetProcess::LoadSendMsg(unk_1414F2088, v16->m_ObjID.m_wIndex, &pbyType, &Dst.byMessageType, v14);
      }
    }
  }
}
