/*
 * Function: ?SendEnterResult@CReturnGateController@@IEAAXHPEAVCPlayer@@@Z
 * Address: 0x140251000
 */

void __fastcall CReturnGateController::SendEnterResult(CReturnGateController *this, int iResult, CPlayer *pkObj)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4

  v3 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szMsg = iResult;
  pbyType = 8;
  v8 = 10;
  CNetProcess::LoadSendMsg(unk_1414F2088, pkObj->m_ObjID.m_wIndex, &pbyType, &szMsg, 1u);
}
