/*
 * Function: _CryptoPP::Singleton_CryptoPP::DL_EncryptionAlgorithm_Xor_CryptoPP::HMAC_CryptoPP::SHA1__0__CryptoPP::NewObject_CryptoPP::DL_EncryptionAlgorithm_Xor_CryptoPP::HMAC_CryptoPP::SHA1__0____0_::Ref_::_2_::_dynamic_atexit_destructor_for__s_pObject__
 * Address: 0x1406E9780
 */

void __cdecl CryptoPP::Singleton_CryptoPP::DL_EncryptionAlgorithm_Xor_CryptoPP::HMAC_CryptoPP::SHA1__0__CryptoPP::NewObject_CryptoPP::DL_EncryptionAlgorithm_Xor_CryptoPP::HMAC_CryptoPP::SHA1__0____0_::Ref_::_2_::_dynamic_atexit_destructor_for__s_pObject__()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-28h]@1

  v0 = &v2;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  CryptoPP::simple_ptr<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>::~simple_ptr<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>(&s_pObject_1);
}
