/*
 * Function: ?ErrorMsgLog@CRFNewDatabase@@IEAAXFPEA_W0PEAX@Z
 * Address: 0x140487060
 */

void __fastcall CRFNewDatabase::ErrorMsgLog(CRFNewDatabase *this, __int16 sqlRet, wchar_t *strQuery, wchar_t *strKind, void *SQLStmt)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  int v8; // [sp+20h] [bp-18h]@4
  CRFNewDatabase *v9; // [sp+40h] [bp+8h]@1
  __int16 v10; // [sp+48h] [bp+10h]@1
  wchar_t *v11; // [sp+58h] [bp+20h]@1

  v11 = strKind;
  v10 = sqlRet;
  v9 = this;
  v5 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  CRFNewDatabase::ErrFmtLog(v9, L"Query : %s");
  CRFNewDatabase::DiagRecWLog(v9, v10, 3, SQLStmt);
  v8 = v10;
  if ( v10 == -2 )
  {
    CRFNewDatabase::ErrFmtLog(v9, L"%s Returns : SQL_INVALID_HANDLE", v11);
  }
  else
  {
    switch ( v8 )
    {
      case -1:
        CRFNewDatabase::ErrFmtLog(v9, L"%s Returns : SQL_ERROR", v11);
        break;
      case 2:
        CRFNewDatabase::ErrFmtLog(v9, L"%s Returns : SQL_STILL_EXECUTING", v11);
        break;
      case 99:
        CRFNewDatabase::ErrFmtLog(v9, L"%s Returns : SQL_NEED_DATA", v11);
        break;
      case 100:
        CRFNewDatabase::ErrFmtLog(v9, L"%s Returns : SQL_NO_DATA", v11);
        break;
      default:
        CRFNewDatabase::ErrFmtLog(v9, L"%s Returns : sqlRet = %d", v11, (unsigned int)v10);
        break;
    }
  }
}
