/*
 * Function: ?CheckRadarItemDelay@CMainThread@@AEAAXXZ
 * Address: 0x1401FA050
 */

void __fastcall CMainThread::CheckRadarItemDelay(CMainThread *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@5
  CUserDB *v5; // [sp+28h] [bp-10h]@8
  CMainThread *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CMyTimer::CountingTimer(&v6->m_tmrCheckRadarDelay) )
  {
    for ( j = 0; j < 2532; ++j )
    {
      v5 = &g_UserDB[j];
      if ( v5->m_bActive )
        CUserDB::CalcRadarDelay(v5);
    }
  }
}
