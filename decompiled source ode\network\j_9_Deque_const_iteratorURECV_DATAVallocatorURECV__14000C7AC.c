/*
 * Function: j_??9?$_Deque_const_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEBA_NAEBV01@@Z
 * Address: 0x14000C7AC
 */

bool __fastcall std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator!=(std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this, std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Right)
{
  return std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator!=(this, _Right);
}
