/*
 * Function: ?SendMsg_MaxHFSP@CPlayer@@QEAAXXZ
 * Address: 0x1400D6480
 */

void __fastcall CPlayer::SendMsg_MaxHFSP(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[2]; // [sp+34h] [bp-44h]@4
  __int16 v5; // [sp+36h] [bp-42h]@4
  __int16 v6; // [sp+38h] [bp-40h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  CPlayer *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(_WORD *)szMsg = ((int (__fastcall *)(CPlayer *))v9->vfptr->GetMaxHP)(v9);
  v5 = CPlayer::GetMaxFP(v9);
  v6 = CPlayer::GetMaxSP(v9);
  pbyType = 11;
  v8 = 3;
  CNetProcess::LoadSendMsg(unk_1414F2088, v9->m_ObjID.m_wIndex, &pbyType, szMsg, 6u);
}
