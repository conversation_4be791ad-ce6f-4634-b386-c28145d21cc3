/*
 * Function: ?reset@?$member_ptr@VPK_MessageAccumulator@CryptoPP@@@CryptoPP@@QEAAXPEAVPK_MessageAccumulator@2@@Z
 * Address: 0x140600EE0
 */

_QWORD *__fastcall CryptoPP::member_ptr<CryptoPP::PK_MessageAccumulator>::reset(_QWORD *a1, __int64 a2)
{
  _QWORD *result; // rax@3
  _QWORD *v3; // [sp+50h] [bp+8h]@1
  __int64 v4; // [sp+58h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  if ( *a1 )
    (**(void (__fastcall ***)(_QWORD, _QWORD))*a1)(*a1, 1i64);
  result = v3;
  *v3 = v4;
  return result;
}
