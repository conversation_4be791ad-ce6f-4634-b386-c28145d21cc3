/*
 * Function: j_?pc_TrunkIoMergeRequest@CPlayer@@QEAAXEEGGG@Z
 * Address: 0x14000C603
 */

void __fastcall CPlayer::pc_TrunkIoMergeRequest(CPlayer *this, char byStartStorageIndex, char byTarStorageIndex, unsigned __int16 wStartItemSerial, unsigned __int16 wTarItemSerial, unsigned __int16 wMoveAmount)
{
  CPlayer::pc_TrunkIoMergeRequest(
    this,
    byStartStorageIndex,
    byTarStorageIndex,
    wStartItemSerial,
    wTarItemSerial,
    wMoveAmount);
}
