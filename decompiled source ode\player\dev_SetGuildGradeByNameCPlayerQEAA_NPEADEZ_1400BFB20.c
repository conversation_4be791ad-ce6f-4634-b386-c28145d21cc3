/*
 * Function: ?dev_SetGuildGradeByName@CPlayer@@QEAA_NPEADE@Z
 * Address: 0x1400BFB20
 */

char __fastcall CPlayer::dev_SetGuildGradeByName(CPlayer *this, char *uszGuildName, char byGrade)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  CGuild *v7; // [sp+20h] [bp-18h]@4
  char *pwszGuildName; // [sp+48h] [bp+10h]@1
  char v9; // [sp+50h] [bp+18h]@1

  v9 = byGrade;
  pwszGuildName = uszGuildName;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = 0i64;
  v7 = GetGuildPtrFromName(g_Guild, 500, uszGuildName);
  if ( v7 )
  {
    if ( (signed int)(unsigned __int8)v9 >= 1 && (signed int)(unsigned __int8)v9 <= 8 )
    {
      if ( pwszGuildName )
      {
        CGuild::UpdateGrade(v7, v9);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
