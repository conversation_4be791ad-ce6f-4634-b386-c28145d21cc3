/*
 * Function: ?GetRecordFromRes@CItemUpgradeTable@@QEAAPEAU_ItemUpgrade_fld@@K@Z
 * Address: 0x1400B84E0
 */

_base_fld *__fastcall CItemUpgradeTable::GetRecordFromRes(CItemUpgradeTable *this, unsigned int dwResIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int n; // [sp+20h] [bp-18h]@4
  CItemUpgradeTable *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( n = 0; n < v7->m_nResNum; ++n )
  {
    if ( v7->m_pwResIndex[n] == dwResIndex )
      return CRecordData::GetRecord(&v7->m_tblItemUpgrade, n);
  }
  return 0i64;
}
