/*
 * Function: ??E?$_Vector_const_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEAAAEAV01@XZ
 * Address: 0x1405ACC60
 */

__int64 __fastcall std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator++(__int64 a1)
{
  *(_QWORD *)(a1 + 16) += 96i64;
  return a1;
}
