/*
 * Function: ?IncreaseVersion@CUnmannedTraderGroupItemInfoTable@@QEAA_NEG@Z
 * Address: 0x14036B360
 */

char __fastcall CUnmannedTraderGroupItemInfoTable::IncreaseVersion(CUnmannedTraderGroupItemInfoTable *this, char byTableCode, unsigned __int16 wItemTableIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v6; // [sp+0h] [bp-88h]@1
  char *byClass; // [sp+20h] [bp-68h]@10
  int v8; // [sp+28h] [bp-60h]@14
  _base_fld *v9; // [sp+30h] [bp-58h]@9
  char byDivision; // [sp+44h] [bp-44h]@11
  char v11; // [sp+64h] [bp-24h]@11
  CUnmannedTraderGroupItemInfoTable *v12; // [sp+90h] [bp+8h]@1
  char v13; // [sp+98h] [bp+10h]@1
  unsigned __int16 v14; // [sp+A0h] [bp+18h]@1

  v14 = wItemTableIndex;
  v13 = byTableCode;
  v12 = this;
  v3 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( (unsigned __int8)byTableCode != 255 && wItemTableIndex != 0xFFFF )
  {
    if ( (signed int)(unsigned __int8)byTableCode < 37 )
    {
      v9 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)byTableCode, wItemTableIndex);
      if ( v9 )
      {
        byDivision = -1;
        v11 = -1;
        if ( CUnmannedTraderGroupIDInfo::GetGroupID(&v12->m_kGroupIDInfo, v13, v14, &byDivision, &v11) )
        {
          if ( CUnmannedTraderGroupVersionInfo::IncreaseVersion(&v12->m_kVerInfo, byDivision, v11) )
          {
            result = 1;
          }
          else
          {
            v8 = (unsigned __int8)v11;
            LODWORD(byClass) = (unsigned __int8)byDivision;
            CUnmannedTraderGroupItemInfoTable::Log(
              v12,
              "UnmannedTraderGroupItemInfoTable::IncreaseVersion( BYTE byTableCode(%u), WORD wItemTableIndex(%u) )\r\n"
              "\t\tm_kVerInfo.IncreaseVersion( byDivisioin(%u), byClass(%u) ) Fail!\r\n",
              (unsigned __int8)v13,
              v14);
            result = 0;
          }
        }
        else
        {
          byClass = v9->m_strCode;
          CUnmannedTraderGroupItemInfoTable::Log(
            v12,
            "UnmannedTraderGroupItemInfoTable::IncreaseVersion( BYTE byTableCode(%u), WORD wItemTableIndex(%u) )\r\n"
            "\t\tm_kGroupIDInfo.GetDivsionAndClassID( byTableCode, pFld->m_strCode(%s) Invalid!\r\n",
            (unsigned __int8)v13,
            v14);
          result = 0;
        }
      }
      else
      {
        byClass = (char *)4;
        CUnmannedTraderGroupItemInfoTable::Log(
          v12,
          "UnmannedTraderGroupItemInfoTable::IncreaseVersion( BYTE byTableCode(%u), WORD wItemTableIndex(%u) )\r\n"
          "\t\tg_Main.m_tblItemData[byTableCode].GetRecord( wItemTableIndex ) NULL!\r\n",
          (unsigned __int8)v13,
          v14);
        result = 0;
      }
    }
    else
    {
      CUnmannedTraderGroupItemInfoTable::Log(
        v12,
        "UnmannedTraderGroupItemInfoTable::IncreaseVersion( BYTE byTableCode(%u), WORD wItemTableIndex(%u) )\r\n"
        "\t\titem_tbl_num <= byTableCode!\r\n",
        (unsigned __int8)byTableCode,
        wItemTableIndex);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
