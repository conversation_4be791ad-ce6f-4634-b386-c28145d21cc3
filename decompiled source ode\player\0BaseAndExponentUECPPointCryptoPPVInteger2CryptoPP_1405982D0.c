/*
 * Function: ??0?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@QEAA@AEBU01@@Z
 * Address: 0x1405982D0
 */

CryptoPP::ECPPoint *__fastcall CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(CryptoPP::ECPPoint *a1, CryptoPP::ECPPoint *a2)
{
  CryptoPP::ECPPoint *v3; // [sp+40h] [bp+8h]@1
  CryptoPP::ECPPoint *__that; // [sp+48h] [bp+10h]@1

  __that = a2;
  v3 = a1;
  CryptoPP::ECPPoint::ECPPoint(a1, a2);
  CryptoPP::Integer::Integer((CryptoPP::Integer *)&v3[1], (const struct CryptoPP::Integer *)&__that[1]);
  return v3;
}
