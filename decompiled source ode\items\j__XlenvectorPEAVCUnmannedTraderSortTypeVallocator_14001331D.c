/*
 * Function: j_?_<PERSON><PERSON>@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@KAXXZ
 * Address: 0x14001331D
 */

void __fastcall __noreturn std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_<PERSON><PERSON>(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this)
{
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_<PERSON><PERSON>(this);
}
