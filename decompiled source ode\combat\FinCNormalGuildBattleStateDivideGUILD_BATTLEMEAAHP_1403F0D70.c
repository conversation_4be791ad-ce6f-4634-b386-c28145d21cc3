/*
 * Function: ?Fin@CNormalGuildBattleStateDivide@GUILD_BATTLE@@MEAAHPEAVCNormalGuildBattle@2@@Z
 * Address: 0x1403F0D70
 */

__int64 __fastcall GUILD_BATTLE::CNormalGuildBattleStateDivide::Fin(GUILD_BATTLE::CNormalGuildBattleStateDivide *this, GUILD_BATTLE::CNormalGuildBattle *pkBattle)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleStateDivide *v6; // [sp+30h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattle *pkBattlea; // [sp+38h] [bp+10h]@1

  pkBattlea = pkBattle;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  GUILD_BATTLE::CNormalGuildBattle::DividePvpPoint(pkBattle);
  GUILD_BATTLE::CNormalGuildBattleState::Log(
    (GUILD_BATTLE::CNormalGuildBattleState *)&v6->vfptr,
    pkBattlea,
    "Fin : DividePvpPoint");
  GUILD_BATTLE::CNormalGuildBattle::RewardGuildBattleMoney(pkBattlea);
  GUILD_BATTLE::CNormalGuildBattleState::Log(
    (GUILD_BATTLE::CNormalGuildBattleState *)&v6->vfptr,
    pkBattlea,
    "Fin : RewardGuildBattleMoney");
  GUILD_BATTLE::CNormalGuildBattle::GuildBattleResultLog(pkBattlea);
  GUILD_BATTLE::CNormalGuildBattleState::Log(
    (GUILD_BATTLE::CNormalGuildBattleState *)&v6->vfptr,
    pkBattlea,
    "Fin : GuildBattleResultLog");
  return 0i64;
}
