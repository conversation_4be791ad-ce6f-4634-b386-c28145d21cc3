/*
 * Function: ?SendSMS_CompleteQuest@CHolyStoneSystem@@QEAAXEPEADH0E@Z
 * Address: 0x14027ED20
 */

void __fastcall CHolyStoneSystem::SendSMS_CompleteQuest(CHolyStoneSystem *this, char byDestroyedRace, char *pwszMasterName, int nControlSec, char *szMasterClass, char byMasterLv)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v8; // rax@11
  CPvpUserAndGuildRankingSystem *v9; // rax@16
  __int64 v10; // [sp+0h] [bp-1A8h]@1
  int Dst; // [sp+40h] [bp-168h]@7
  char Dest; // [sp+44h] [bp-164h]@7
  char v13[16]; // [sp+65h] [bp-143h]@17
  char v14[35]; // [sp+75h] [bp-133h]@17
  char v15; // [sp+98h] [bp-110h]@7
  char v16; // [sp+99h] [bp-10Fh]@7
  char v17; // [sp+9Ah] [bp-10Eh]@7
  char v18; // [sp+ABh] [bp-FDh]@22
  char v19; // [sp+ACh] [bp-FCh]@22
  char v20; // [sp+ADh] [bp-FBh]@22
  char v21; // [sp+AEh] [bp-FAh]@7
  char v22; // [sp+AFh] [bp-F9h]@7
  char v23; // [sp+B3h] [bp-F5h]@7
  char v24[85]; // [sp+B4h] [bp-F4h]@13
  char v25; // [sp+109h] [bp-9Fh]@7
  char v26; // [sp+10Ah] [bp-9Eh]@7
  unsigned __int16 v27; // [sp+10Bh] [bp-9Dh]@7
  char v28; // [sp+10Dh] [bp-9Bh]@7
  char v29; // [sp+10Eh] [bp-9Ah]@7
  char v30; // [sp+10Fh] [bp-99h]@8
  char v31; // [sp+110h] [bp-98h]@8
  __int16 v32; // [sp+111h] [bp-97h]@8
  char v33; // [sp+113h] [bp-95h]@8
  char v34; // [sp+114h] [bp-94h]@8
  tm *v35; // [sp+128h] [bp-80h]@7
  __int64 _Time; // [sp+138h] [bp-70h]@7
  _PVP_RANK_DATA *v37; // [sp+148h] [bp-60h]@9
  int j; // [sp+150h] [bp-58h]@11
  int k; // [sp+154h] [bp-54h]@14
  int v40; // [sp+158h] [bp-50h]@20
  int v41; // [sp+15Ch] [bp-4Ch]@20
  int v42; // [sp+160h] [bp-48h]@20
  int v43; // [sp+164h] [bp-44h]@20
  char pbyType; // [sp+174h] [bp-34h]@22
  char v45; // [sp+175h] [bp-33h]@22
  int v46; // [sp+190h] [bp-18h]@11
  unsigned __int64 v47; // [sp+198h] [bp-10h]@4
  CHolyStoneSystem *v48; // [sp+1B0h] [bp+8h]@1
  char v49; // [sp+1B8h] [bp+10h]@1
  const char *Source; // [sp+1C0h] [bp+18h]@1
  int v51; // [sp+1C8h] [bp+20h]@1

  v51 = nControlSec;
  Source = pwszMasterName;
  v49 = byDestroyedRace;
  v48 = this;
  v6 = &v10;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v47 = (unsigned __int64)&v10 ^ _security_cookie;
  if ( unk_1799C608D && unk_1799C608E )
  {
    memset_0(&Dst, 0, 0xD5ui64);
    Dst = unk_1799C608C;
    strcpy_0(&Dest, byte_1799C5B78);
    v15 = CHolyStoneSystem::GetHolyMasterRace(v48);
    v16 = v49;
    strcpy_0(&v17, Source);
    v27 = CHolyStoneSystem::GetStartYear(v48);
    v28 = CHolyStoneSystem::GetStartMonth(v48);
    v29 = CHolyStoneSystem::GetStartDay(v48);
    v25 = CHolyStoneSystem::GetStartHour(v48);
    v26 = CHolyStoneSystem::GetStartMin(v48);
    v21 = byMasterLv;
    memcpy_0(&v22, szMasterClass, 4ui64);
    v23 = 0;
    time_8(&_Time);
    v35 = localtime_6(&_Time);
    if ( v35 )
    {
      v32 = v35->tm_year + 1900;
      v33 = v35->tm_mon + 1;
      v34 = v35->tm_mday;
      v30 = v35->tm_hour;
      v31 = v35->tm_min;
    }
    v37 = 0i64;
    if ( CHolyStoneSystem::GetHolyMasterRace(v48) >= 0 && CHolyStoneSystem::GetHolyMasterRace(v48) < 3 )
    {
      v46 = CHolyStoneSystem::GetHolyMasterRace(v48);
      v8 = CPvpUserAndGuildRankingSystem::Instance();
      v37 = CPvpUserAndGuildRankingSystem::GetCurrentPvpRankData(v8, v46, 0);
      for ( j = 0; j < 5; ++j )
        strcpy_0(&v24[17 * j], v37[j + 1].wszName);
    }
    for ( k = 0; k < 3; ++k )
    {
      v9 = CPvpUserAndGuildRankingSystem::Instance();
      v37 = CPvpUserAndGuildRankingSystem::GetCurrentPvpRankData(v9, k, 0);
      if ( v37->wszName[0] )
      {
        strcpy_0(&v13[17 * k], v37->wszName);
        v14[17 * k] = 0;
      }
      else
      {
        v13[17 * k] = 0;
      }
    }
    v40 = GetCurrentHour();
    v41 = (v51 / 60 / 60 + v40) % 24;
    v42 = GetCurrentMin();
    v43 = (v51 / 60 + v42) % 60;
    if ( v43 < v42 )
      v41 = (v41 + 1) % 24;
    v18 = v41;
    v19 = v43;
    v20 = CHolyStoneSystem::GetNumOfTime(v48);
    pbyType = 51;
    v45 = 3;
    if ( unk_1799C9ADE )
      CNetProcess::LoadSendMsg(unk_1414F2098, unk_1799C9ADD, &pbyType, (char *)&Dst, 0xD5u);
    CLogFile::Write(&v48->m_logQuestDestroy, ">> Complete Quest! Next(%d:%d)", (unsigned int)v41, (unsigned int)v43);
  }
}
