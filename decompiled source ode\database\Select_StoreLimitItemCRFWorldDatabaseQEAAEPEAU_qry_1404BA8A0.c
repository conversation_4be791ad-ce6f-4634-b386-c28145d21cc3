/*
 * Function: ?Select_StoreLimitItem@CRFWorldDatabase@@QEAAEPEAU_qry_case_all_store_limit_item@@@Z
 * Address: 0x1404BA8A0
 */

char __fastcall CRFWorldDatabase::Select_StoreLimitItem(CRFWorldDatabase *this, _qry_case_all_store_limit_item *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  _qry_case_all_store_limit_item::__list *v5; // rax@32
  char *v6; // rax@32
  int *v7; // rax@32
  unsigned int *v8; // rax@32
  unsigned __int64 *v9; // rax@32
  __int64 v10; // [sp+0h] [bp-2C8h]@1
  void *SQLStmt; // [sp+20h] [bp-2A8h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-2A0h]@19
  SQLLEN v13; // [sp+38h] [bp-290h]@19
  __int16 v14; // [sp+44h] [bp-284h]@9
  char DstBuf; // [sp+60h] [bp-268h]@4
  char v16; // [sp+61h] [bp-267h]@4
  int j; // [sp+264h] [bp-64h]@17
  unsigned int v18; // [sp+268h] [bp-60h]@14
  int TargetValue; // [sp+274h] [bp-54h]@19
  int v20; // [sp+294h] [bp-34h]@26
  int v21; // [sp+2A4h] [bp-24h]@14
  unsigned __int64 v22; // [sp+2B0h] [bp-18h]@4
  CRFWorldDatabase *v23; // [sp+2D0h] [bp+8h]@1
  _qry_case_all_store_limit_item *v24; // [sp+2D8h] [bp+10h]@1

  v24 = pData;
  v23 = this;
  v2 = &v10;
  for ( i = 176i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v22 = (unsigned __int64)&v10 ^ _security_cookie;
  DstBuf = 0;
  memset(&v16, 0, 0x1FFui64);
  sprintf_s(
    &DstBuf,
    0x200ui64,
    "select k0,k1,k2,k3,k4,k5,k6,k7,k8,k9,k10,k11,k12,k13,k14,k15,num0,num1,num2,num3,num4,num5,num6,num7,num8,num9,num10"
    ",num11,num12,num13,num14,num15,serial,type,typeserial,storeinx,resettime from tbl_StoreLimitItem_061212 where dck=0");
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v23->vfptr, &DstBuf);
  if ( v23->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v23->vfptr) )
  {
    v14 = SQLExecDirect_0(v23->m_hStmtSelect, &DstBuf, -3);
    if ( v14 && v14 != 1 )
    {
      if ( v14 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v23->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v14, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v14, v23->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v18 = 0;
      v21 = 16;
LABEL_15:
      v14 = SQLFetch_0(v23->m_hStmtSelect);
      if ( v14 && v14 != 1 )
      {
        v24->dwCount = v18;
        if ( v23->m_hStmtSelect )
          SQLCloseCursor_0(v23->m_hStmtSelect);
        if ( v23->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v23->vfptr, "%s Success", &DstBuf);
        result = 0;
      }
      else
      {
        for ( j = 1; j <= v21; ++j )
        {
          StrLen_or_IndPtr = &v13;
          SQLStmt = 0i64;
          v14 = SQLGetData_0(v23->m_hStmtSelect, j, 4, &TargetValue, 0i64, &v13);
          if ( v14 && v14 != 1 )
          {
            SQLStmt = v23->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v14, &DstBuf, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v14, v23->m_hStmtSelect);
            if ( v23->m_hStmtSelect )
              SQLCloseCursor_0(v23->m_hStmtSelect);
            return 1;
          }
          _INVENKEY::LoadDBKey((_INVENKEY *)(&v24->pStoreList[v18].dwLimitInitTime + j), TargetValue);
        }
        while ( 1 )
        {
          if ( j > 2 * v21 )
          {
            v5 = &v24->pStoreList[v18];
            StrLen_or_IndPtr = &v13;
            SQLStmt = 0i64;
            v14 = SQLGetData_0(v23->m_hStmtSelect, j, -18, v5, 0i64, &v13);
            v6 = &v24->pStoreList[v18].byType;
            StrLen_or_IndPtr = &v13;
            SQLStmt = 0i64;
            v14 = SQLGetData_0(v23->m_hStmtSelect, j + 1, 5, v6, 0i64, &v13);
            v7 = &v24->pStoreList[v18].nTypeSerial;
            StrLen_or_IndPtr = &v13;
            SQLStmt = 0i64;
            v14 = SQLGetData_0(v23->m_hStmtSelect, j + 2, -18, v7, 0i64, &v13);
            v8 = &v24->pStoreList[v18].dwStoreIndex;
            StrLen_or_IndPtr = &v13;
            SQLStmt = 0i64;
            v14 = SQLGetData_0(v23->m_hStmtSelect, j + 3, -18, v8, 0i64, &v13);
            v9 = &v24->pStoreList[v18].dwLimitInitTime;
            StrLen_or_IndPtr = &v13;
            SQLStmt = 0i64;
            v14 = SQLGetData_0(v23->m_hStmtSelect, j + 4, -25, v9, 0i64, &v13);
            ++v18;
            goto LABEL_15;
          }
          StrLen_or_IndPtr = &v13;
          SQLStmt = 0i64;
          v14 = SQLGetData_0(v23->m_hStmtSelect, j, 4, &v20, 0i64, &v13);
          if ( v14 )
          {
            if ( v14 != 1 )
              break;
          }
          v24->pStoreList[v18].ItemData[j++ - v21 - 1].nLimitNum = v20;
        }
        SQLStmt = v23->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v14, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v14, v23->m_hStmtSelect);
        if ( v23->m_hStmtSelect )
          SQLCloseCursor_0(v23->m_hStmtSelect);
        result = 1;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v23->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1;
  }
  return result;
}
