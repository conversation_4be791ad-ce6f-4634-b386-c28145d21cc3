/*
 * Function: j_?IsExistGroupID@CUnmannedTraderGroupIDInfo@@QEAA_NEEEEAEAK@Z
 * Address: 0x14000A2FE
 */

bool __fastcall CUnmannedTraderGroupIDInfo::IsExistGroupID(CUnmannedTraderGroupIDInfo *this, char byDivision, char byClass, char bySubClass, char bySortType, unsigned int *dwListIndex)
{
  return CUnmannedTraderGroupIDInfo::IsExistGroupID(this, byDivision, byClass, bySubClass, bySortType, dwListIndex);
}
