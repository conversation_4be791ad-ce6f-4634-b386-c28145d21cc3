/*
 * Function: ?ct_start_cri@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140290370
 */

bool __fastcall ct_start_cri(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  int nNumOfTime; // [sp+20h] [bp-18h]@6
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6 )
  {
    nNumOfTime = 0;
    if ( s_nWordCount >= 1 )
      nNumOfTime = atoi(s_pwszDstCheat[0]);
    result = CPlayer::mgr_holystone_start(v6, nNumOfTime);
  }
  else
  {
    result = 0;
  }
  return result;
}
