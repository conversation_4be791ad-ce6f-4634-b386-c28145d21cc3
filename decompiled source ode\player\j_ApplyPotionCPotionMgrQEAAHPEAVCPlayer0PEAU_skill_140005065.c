/*
 * Function: j_?ApplyPotion@CPotionMgr@@QEAAHPEAVCPlayer@@0PEAU_skill_fld@@PEAU_CheckPotion_fld@@PEBU_PotionItem_fld@@_N@Z
 * Address: 0x140005065
 */

int __fastcall CPotionMgr::ApplyPotion(CPotionMgr *this, CPlayer *pUsePlayer, CPlayer *pApplyPlayer, _skill_fld *pEffecFld, _CheckPotion_fld *pCheckFld, _PotionItem_fld *pfB, bool bCommonPotion)
{
  return CPotionMgr::ApplyPotion(this, pUsePlayer, pApplyPlayer, pEffecFld, pCheckFld, pfB, bCommonPotion);
}
