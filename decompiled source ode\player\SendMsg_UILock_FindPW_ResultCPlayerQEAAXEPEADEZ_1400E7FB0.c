/*
 * Function: ?SendMsg_UILock_FindPW_Result@CPlayer@@QEAAXEPEADE@Z
 * Address: 0x1400E7FB0
 */

void __fastcall CPlayer::SendMsg_UILock_FindPW_Result(CPlayer *this, char byRet, char *uszUILockPW, char byFindPassFailCount)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-98h]@1
  char szMsg; // [sp+38h] [bp-60h]@4
  char v8; // [sp+39h] [bp-5Fh]@4
  char Dst; // [sp+3Ah] [bp-5Eh]@5
  char pbyType; // [sp+64h] [bp-34h]@6
  char v11; // [sp+65h] [bp-33h]@6
  unsigned __int64 v12; // [sp+80h] [bp-18h]@4
  CPlayer *v13; // [sp+A0h] [bp+8h]@1

  v13 = this;
  v4 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = (unsigned __int64)&v6 ^ _security_cookie;
  memset(&v8, 0, 0xEui64);
  szMsg = byRet;
  v8 = byFindPassFailCount;
  if ( uszUILockPW )
    strcpy_s(&Dst, 0xDui64, uszUILockPW);
  pbyType = 13;
  v11 = -121;
  CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, &szMsg, 0xFu);
}
