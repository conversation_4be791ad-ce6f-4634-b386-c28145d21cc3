/*
 * Function: ?SF_Stun@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 * Address: 0x14009FAD0
 */

char __fastcall CPlayer::SF_Stun(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v6; // [sp+0h] [bp-38h]@1
  CCharacter *v7; // [sp+20h] [bp-18h]@5
  CCharacter *v8; // [sp+48h] [bp+10h]@1

  v8 = pDstObj;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( !pDstObj->m_ObjID.m_byID && (v7 = pDstObj, BYTE2(pDstObj[1].m_fCurPos[2])) && LOBYTE(v7[1].m_fCurPos[2]) )
  {
    result = 0;
  }
  else
  {
    LOBYTE(pDstObj) = 1;
    (*(void (__fastcall **)(CCharacter *, CCharacter *))&v8->vfptr->gap8[0])(v8, pDstObj);
    result = 1;
  }
  return result;
}
