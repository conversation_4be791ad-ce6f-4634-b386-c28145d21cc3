/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAVTRC_AutoTrade@@_KPEAV1@V?$allocator@PEAVTRC_AutoTrade@@@std@@@stdext@@YAXPEAPEAVTRC_AutoTrade@@_KAEBQEAV1@AEAV?$allocator@PEAVTRC_AutoTrade@@@std@@@Z
 * Address: 0x140007E05
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<TRC_AutoTrade * *,unsigned __int64,TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(TRC_AutoTrade **_First, unsigned __int64 _Count, TRC_AutoTrade *const *_Val, std::allocator<TRC_AutoTrade *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<TRC_AutoTrade * *,unsigned __int64,TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(
    _First,
    _Count,
    _<PERSON>,
    _<PERSON>);
}
