/*
 * Function: j_??$unchecked_uninitialized_copy@PEAEPEAEV?$allocator@E@std@@@stdext@@YAPEAEPEAE00AEAV?$allocator@E@std@@@Z
 * Address: 0x14001026C
 */

char *__fastcall stdext::unchecked_uninitialized_copy<unsigned char *,unsigned char *,std::allocator<unsigned char>>(char *_First, char *_Last, char *_Dest, std::allocator<unsigned char> *_Al)
{
  return stdext::unchecked_uninitialized_copy<unsigned char *,unsigned char *,std::allocator<unsigned char>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
