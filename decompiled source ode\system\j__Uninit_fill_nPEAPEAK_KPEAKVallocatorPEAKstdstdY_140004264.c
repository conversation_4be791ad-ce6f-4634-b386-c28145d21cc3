/*
 * Function: j_??$_Uninit_fill_n@PEAPEAK_KPEAKV?$allocator@PEAK@std@@@std@@YAXPEAPEAK_KAEBQEAKAEAV?$allocator@PEAK@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140004264
 */

void __fastcall std::_Uninit_fill_n<unsigned long * *,unsigned __int64,unsigned long *,std::allocator<unsigned long *>>(unsigned int **_First, unsigned __int64 _Count, unsigned int *const *_Val, std::allocator<unsigned long *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<unsigned long * *,unsigned __int64,unsigned long *,std::allocator<unsigned long *>>(
    _First,
    _Count,
    _<PERSON>,
    __formal,
    a5,
    a6);
}
