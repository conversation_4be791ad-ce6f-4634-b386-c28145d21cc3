/*
 * Function: _CUnmannedTraderClassInfoFactory::CUnmannedTraderClassInfoFactory_::_1_::dtor$0
 * Address: 0x1403848D0
 */

void __fastcall CUnmannedTraderClassInfoFactory::CUnmannedTraderClassInfoFactory_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(*(std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > **)(a2 + 112));
}
