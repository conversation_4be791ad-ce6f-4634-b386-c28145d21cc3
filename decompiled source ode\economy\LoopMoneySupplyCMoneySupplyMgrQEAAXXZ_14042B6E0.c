/*
 * Function: ?LoopMoneySupply@CMoneySupplyMgr@@QEAAXXZ
 * Address: 0x14042B6E0
 */

void __fastcall CMoneySupplyMgr::LoopMoneySupply(CMoneySupplyMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@6
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int v5; // [sp+20h] [bp-18h]@5
  void *Dst; // [sp+28h] [bp-10h]@6
  CMoneySupplyMgr *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( unk_1799C608D )
  {
    v5 = GetLoopTime();
    if ( v5 >= v7->m_dwLastSendTime + 60000 )
    {
      _MONEY_SUPPLY_DATA::init(&v7->m_MS_Senddata);
      v3 = _MONEY_SUPPLY_DATA::size(&v7->m_MS_data);
      Dst = &v7->m_MS_Senddata;
      memcpy_0(&v7->m_MS_Senddata, &v7->m_MS_data, v3);
      _MONEY_SUPPLY_DATA::init(&v7->m_MS_data);
      CMoneySupplyMgr::SendMsg_MoneySupplyDataToWeb(v7, &v7->m_MS_Senddata);
      v7->m_dwLastSendTime = v5;
    }
  }
}
