/*
 * Function: ?IsPassMasteryLimitLvDiff@CPlayer@@QEAA_NH@Z
 * Address: 0x14007D8F0
 */

bool __fastcall CPlayer::IsPassMasteryLimitLvDiff(CPlayer *this, int iDstLevel)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = ((int (__fastcall *)(CPlayer *))v7->vfptr->GetLevel)(v7) - iDstLevel;
  if ( v6 <= 3 )
    result = v6 >= -10;
  else
    result = 0;
  return result;
}
