/*
 * Function: ?StartVote@CVoteSystem@@QEAA_NPEADEE@Z
 * Address: 0x1402AF8B0
 */

char __fastcall CVoteSystem::StartVote(CVoteSystem *this, char *pwszContent, char byLimGrade, char byRaceCode)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned __int16 v7; // ax@11
  __int64 v8; // [sp+0h] [bp-598h]@1
  _starting_vote_inform_zocl v9; // [sp+40h] [bp-558h]@6
  char pbyType; // [sp+564h] [bp-34h]@6
  char v11; // [sp+565h] [bp-33h]@6
  unsigned int dwClientIndex; // [sp+574h] [bp-24h]@6
  CPlayer *v13; // [sp+578h] [bp-20h]@9
  unsigned __int64 v14; // [sp+588h] [bp-10h]@4
  CVoteSystem *v15; // [sp+5A0h] [bp+8h]@1
  const char *Source; // [sp+5A8h] [bp+10h]@1
  char v17; // [sp+5B0h] [bp+18h]@1

  v17 = byLimGrade;
  Source = pwszContent;
  v15 = this;
  v4 = &v8;
  for ( i = 356i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v14 = (unsigned __int64)&v8 ^ _security_cookie;
  if ( v15->m_bActive )
  {
    result = 0;
  }
  else
  {
    ++v15->m_nSerial;
    v15->m_byRaceCode = byRaceCode;
    memset_0(v15->m_dwPoint, 0, 0xCui64);
    CNetIndexList::ResetList(&v15->m_listVote);
    v15->m_byLimGrade = v17;
    v15->m_dwLastBroadcastTime = GetLoopTime();
    v15->m_dwStartVoteTime = GetLoopTime();
    v15->m_bActive = 1;
    v15->m_bHurry = 0;
    v15->m_bPunishment = 0;
    _starting_vote_inform_zocl::_starting_vote_inform_zocl(&v9);
    v9.nVoteSerial = v15->m_nSerial;
    v9.byLimGrade = v15->m_byLimGrade;
    v9.wLeftSec = 300;
    strcpy_0(v9.wszContent, Source);
    v9.wContentSize = strlen_0(Source) + 1;
    pbyType = 26;
    v11 = 3;
    for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
    {
      v13 = &g_Player + (signed int)dwClientIndex;
      if ( v13->m_bLive )
      {
        if ( CPlayerDB::GetRaceCode(&v13->m_Param) == v15->m_byRaceCode )
        {
          v7 = _starting_vote_inform_zocl::size(&v9);
          CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, (char *)&v9, v7);
        }
      }
    }
    v15->m_SendStarted.nVoteSerial = v15->m_nSerial;
    v15->m_SendStarted.byLimGrade = v15->m_byLimGrade;
    strcpy_0(v15->m_SendStarted.wszContent, Source);
    v15->m_SendStarted.wContentSize = strlen_0(Source) + 1;
    v15->m_SendStarted.wLeftSec = 300;
    result = 1;
  }
  return result;
}
