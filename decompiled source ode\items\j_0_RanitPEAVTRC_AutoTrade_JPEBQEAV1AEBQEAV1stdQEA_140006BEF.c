/*
 * Function: j_??0?$_<PERSON>t@PEAVTRC_AutoTrade@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@XZ
 * Address: 0x140006BEF
 */

void __fastcall std::_<PERSON><PERSON><TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &>::_<PERSON><PERSON><TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &>(std::_Ranit<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &> *this)
{
  std::_Ranit<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &>::_Ranit<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &>(this);
}
