/*
 * Function: j_??H?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEBA?AV01@_J@Z
 * Address: 0x140001410
 */

std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator+(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *result, __int64 _Off)
{
  return std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator+(this, result, _Off);
}
