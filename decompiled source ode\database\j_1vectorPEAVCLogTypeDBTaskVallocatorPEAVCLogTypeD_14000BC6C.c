/*
 * Function: j_??1?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAA@XZ
 * Address: 0x14000BC6C
 */

void __fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this)
{
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(this);
}
