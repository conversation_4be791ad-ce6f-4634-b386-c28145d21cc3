/*
 * Function: ?SendMsg_InformTaxIncome@CPlayer@@QEAAXEKPEAD@Z
 * Address: 0x1400E74A0
 */

void __fastcall CPlayer::SendMsg_InformTaxIncome(CPlayer *this, char byRet, unsigned int dwComm, char *pwszDate)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v6; // ax@4
  __int64 v7; // [sp+0h] [bp-B8h]@1
  char Dest; // [sp+34h] [bp-84h]@4
  char v9; // [sp+36h] [bp-82h]@4
  char Str; // [sp+54h] [bp-64h]@4
  char v11; // [sp+56h] [bp-62h]@4
  _pt_inform_commission_income_zocl v12; // [sp+74h] [bp-44h]@4
  char pbyType; // [sp+94h] [bp-24h]@4
  char v14; // [sp+95h] [bp-23h]@4
  CPlayer *v15; // [sp+C0h] [bp+8h]@1
  char v16; // [sp+C8h] [bp+10h]@1
  unsigned int v17; // [sp+D0h] [bp+18h]@1
  char *v18; // [sp+D8h] [bp+20h]@1

  v18 = pwszDate;
  v17 = dwComm;
  v16 = byRet;
  v15 = this;
  v4 = &v7;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  strncpy(&Dest, pwszDate + 4, 2ui64);
  v9 = 0;
  strncpy(&Str, v18 + 6, 2ui64);
  v11 = 0;
  v12.byRet = v16;
  v12.byMonth = atoi(&Dest);
  v12.byDay = atoi(&Str);
  v12.dwCommission = v17;
  pbyType = 13;
  v14 = 122;
  v6 = _pt_inform_commission_income_zocl::size(&v12);
  CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, &v12.byRet, v6);
}
