/*
 * Function: ?Select_OldVerPatriarchGroup@CRFWorldDatabase@@QEAAHEPEAU_candidate_info@@@Z
 * Address: 0x1404BC610
 */

signed __int64 __fastcall CRFWorldDatabase::Select_OldVerPatriarchGroup(CRFWorldDatabase *this, char by<PERSON><PERSON>, _candidate_info *p)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v6; // [sp+0h] [bp-288h]@1
  void *SQLStmt; // [sp+20h] [bp-268h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-260h]@24
  char Dest; // [sp+40h] [bp-248h]@4
  SQLLEN v10; // [sp+258h] [bp-30h]@24
  __int16 v11; // [sp+264h] [bp-24h]@9
  _candidate_info::ClassType j; // [sp+268h] [bp-20h]@14
  unsigned __int8 v13; // [sp+26Ch] [bp-1Ch]@18
  unsigned __int8 v14; // [sp+26Dh] [bp-1Bh]@26
  unsigned __int8 v15; // [sp+26Eh] [bp-1Ah]@34
  unsigned __int64 v16; // [sp+278h] [bp-10h]@4
  CRFWorldDatabase *v17; // [sp+290h] [bp+8h]@1
  _candidate_info *v18; // [sp+2A0h] [bp+18h]@1

  v18 = p;
  v17 = this;
  v3 = &v6;
  for ( i = 160i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v16 = (unsigned __int64)&v6 ^ _security_cookie;
  sprintf(
    &Dest,
    "select top 5 AvatorSerial, AvatorName from [dbo].[tbl_history_electraceboss_winner_%d] where serial = (select top 1 "
    "serial from [dbo].[tbl_history_electraceboss_winner_%d] order by serial desc) and rank > 0 order by nID ASC",
    (unsigned __int8)byRace,
    (unsigned __int8)byRace);
  if ( v17->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v17->vfptr, &Dest);
  if ( v17->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v17->vfptr) )
  {
    v11 = SQLExecDirect_0(v17->m_hStmtSelect, &Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v17->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v11, &Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v11, v17->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      for ( j = 0; (signed int)j < 5; ++j )
      {
        v11 = SQLFetch_0(v17->m_hStmtSelect);
        if ( v11 && v11 != 1 )
        {
          v13 = 0;
          if ( v11 == 100 )
          {
            v13 = 2;
          }
          else
          {
            SQLStmt = v17->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v11, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v11, v17->m_hStmtSelect);
            v13 = 1;
          }
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          return v13;
        }
        v18[j].eClassType = j;
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v17->m_hStmtSelect, 2u, 4, &v18[j].dwAvatorSerial, 0i64, &v10);
        if ( v11 && v11 != 1 )
        {
          v14 = 0;
          if ( v11 == 100 )
          {
            v14 = 2;
          }
          else
          {
            SQLStmt = v17->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v11, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v11, v17->m_hStmtSelect);
            v14 = 1;
          }
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          return v14;
        }
        StrLen_or_IndPtr = &v10;
        SQLStmt = (void *)17;
        v11 = SQLGetData_0(v17->m_hStmtSelect, 3u, 1, v18[j].wszName, 17i64, &v10);
        if ( v11 && v11 != 1 )
        {
          v15 = 0;
          if ( v11 == 100 )
          {
            v15 = 2;
          }
          else
          {
            SQLStmt = v17->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v11, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v11, v17->m_hStmtSelect);
            v15 = 1;
          }
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          return v15;
        }
        v18[j].bLoad = 1;
      }
      if ( v17->m_hStmtSelect )
        SQLCloseCursor_0(v17->m_hStmtSelect);
      if ( v17->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v17->vfptr, "%s Success", &Dest);
      result = 0i64;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v17->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1i64;
  }
  return result;
}
