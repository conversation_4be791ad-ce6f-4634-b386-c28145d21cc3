/*
 * Function: ??$_Copy_opt@PEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@PEAU12@Urandom_access_iterator_tag@std@@@std@@YAPEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@PEAU12@00Urandom_access_iterator_tag@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140617D20
 */

CryptoPP::ECPPoint *__fastcall std::_Copy_opt<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,std::random_access_iterator_tag>(CryptoPP::ECPPoint *a1, CryptoPP::ECPPoint *a2, CryptoPP::ECPPoint *a3)
{
  CryptoPP::ECPPoint *i; // [sp+30h] [bp+8h]@1
  CryptoPP::ECPPoint *v5; // [sp+38h] [bp+10h]@1
  CryptoPP::ECPPoint *v6; // [sp+40h] [bp+18h]@1

  v6 = a3;
  v5 = a2;
  for ( i = a1; i != v5; i = (CryptoPP::ECPPoint *)((char *)i + 128) )
  {
    CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::operator=(v6, i);
    v6 = (CryptoPP::ECPPoint *)((char *)v6 + 128);
  }
  return v6;
}
