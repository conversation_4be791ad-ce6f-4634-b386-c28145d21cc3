/*
 * Function: ?GetBoosterAddSpeed@CEquipItemSFAgent@@QEAAMXZ
 * Address: 0x140121890
 */

float __fastcall CEquipItemSFAgent::GetBoosterAddSpeed(CEquipItemSFAgent *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  float result; // xmm0_4@9
  __int64 v4; // [sp+0h] [bp-68h]@1
  _sf_continous *v5; // [sp+30h] [bp-38h]@4
  char v6; // [sp+38h] [bp-30h]@5
  int n; // [sp+3Ch] [bp-2Ch]@5
  _base_fld *v8; // [sp+40h] [bp-28h]@6
  _STORAGE_LIST::_db_con *v9; // [sp+48h] [bp-20h]@7
  _base_fld *v10; // [sp+50h] [bp-18h]@8
  CEquipItemSFAgent *v11; // [sp+70h] [bp+8h]@1

  v11 = this;
  v1 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = CEquipItemSFAgent::GetEquipSFCont(v11, 7);
  if ( v5
    && (v6 = v5->m_byEffectCode, n = v5->m_wEffectIndex, !v6)
    && (v8 = CRecordData::GetRecord(&stru_1799C8410, n)) != 0i64
    && (v9 = CEquipItemSFAgent::GetEquip(v11, 7)) != 0i64
    && (v10 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 7, v9->m_wItemIndex)) != 0i64 )
  {
    result = *(float *)&v10[7].m_strCode[60];
  }
  else
  {
    result = 0.0;
  }
  return result;
}
