/*
 * Function: ?CheckLootItem@CMonster@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x140144120
 */

void __fastcall CMonster::CheckLootItem(CMonster *this, CPlayer *pOwner)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  char v6; // [sp+24h] [bp-14h]@4
  char v7; // [sp+25h] [bp-13h]@4
  CMonster *v8; // [sp+40h] [bp+8h]@1
  CPlayer *pOwnera; // [sp+48h] [bp+10h]@1

  pOwnera = pOwner;
  v8 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = -1;
  v6 = 0;
  v7 = 0;
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, pOwner->m_id.wIndex) == 99 )
  {
    v7 = 1;
    CPlayer::SendMsg_TLStatusPenalty(pOwnera, 4);
  }
  else
  {
    if ( CMonster::_LootItem_Std(v8, pOwnera) )
      v7 = 1;
    if ( CMonster::_LootItem_Rwp(v8, pOwnera) )
      v7 = 1;
    if ( CMonster::_LootItem_EventSet(v8, pOwnera) )
      v7 = 1;
    if ( CMonster::_LootItem_Qst(v8, pOwnera) )
      v7 = 1;
  }
  if ( v7 )
  {
    if ( CPartyPlayer::IsPartyMode(pOwnera->m_pPartyMgr) )
      CPartyPlayer::SetNextLootAuthor(pOwnera->m_pPartyMgr);
  }
}
