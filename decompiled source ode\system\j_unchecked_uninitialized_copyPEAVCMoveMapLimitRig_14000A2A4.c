/*
 * Function: j_??$unchecked_uninitialized_copy@PEAVCMoveMapLimitRightInfo@@PEAV1@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@stdext@@YAPEAVCMoveMapLimitRightInfo@@PEAV1@00AEAV?$allocator@VCMoveMapLimitRightInfo@@@std@@@Z
 * Address: 0x14000A2A4
 */

CMoveMapLimitRightInfo *__fastcall stdext::unchecked_uninitialized_copy<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *,std::allocator<CMoveMapLimitRightInfo>>(CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, CMoveMapLimitRightInfo *_Dest, std::allocator<CMoveMapLimitRightInfo> *_Al)
{
  return stdext::unchecked_uninitialized_copy<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *,std::allocator<CMoveMapLimitRightInfo>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
