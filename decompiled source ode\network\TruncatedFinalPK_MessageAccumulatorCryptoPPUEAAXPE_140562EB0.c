/*
 * Function: ?TruncatedFinal@PK_MessageAccumulator@CryptoPP@@UEAAXPEAE_K@Z
 * Address: 0x140562EB0
 */

void __fastcall __noreturn CryptoPP::PK_MessageAccumulator::TruncatedFinal(CryptoPP::PK_MessageAccumulator *this, unsigned __int8 *a2)
{
  CryptoPP::NotImplemented v2; // [sp+20h] [bp-98h]@1
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+70h] [bp-48h]@1
  unsigned __int8 v4; // [sp+A0h] [bp-18h]@1
  __int64 v5; // [sp+A8h] [bp-10h]@1

  v5 = -2i64;
  memset(&v4, 0, sizeof(v4));
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
    &s,
    "PK_MessageAccumulator: TruncatedFinal() should not be called",
    v4);
  CryptoPP::NotImplemented::NotImplemented(&v2, &s);
  CxxThrowException_0((__int64)&v2, (__int64)&TI3_AVNotImplemented_CryptoPP__);
}
