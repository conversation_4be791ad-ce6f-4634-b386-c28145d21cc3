/*
 * Function: j_??$_Ptr_cat@PEAPEAVCUnmannedTraderSubClassInfo@@PEAPEAV1@@std@@YA?AU_Scalar_ptr_iterator_tag@0@AEAPEAPEAVCUnmannedTraderSubClassInfo@@0@Z
 * Address: 0x14000429B
 */

std::_Scalar_ptr_iterator_tag __fastcall std::_Ptr_cat<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo * *>(CUnmannedTraderSubClassInfo ***__formal, CUnmannedTraderSubClassInfo ***a2)
{
  return std::_Ptr_cat<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo * *>(__formal, a2);
}
