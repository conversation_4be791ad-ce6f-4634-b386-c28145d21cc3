/*
 * Function: ?UpdateClearGuildBattleDayInfo@CNormalGuildBattleManager@GUILD_BATTLE@@QEAA_NKK@Z
 * Address: 0x1403D42E0
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleManager::UpdateClearGuildBattleDayInfo(GUILD_BATTLE::CNormalGuildBattleManager *this, unsigned int dwStartSID, unsigned int dwEndSID)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  unsigned int dwStartID; // [sp+38h] [bp+10h]@1
  unsigned int dwEndID; // [sp+40h] [bp+18h]@1

  dwEndID = dwEndSID;
  dwStartID = dwStartSID;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( CRFWorldDatabase::UpdateClearGuildBattleInfo(pkDB, dwStartSID, dwEndSID) )
  {
    result = 1;
  }
  else
  {
    CLogFile::Write(
      &stru_1799C9380,
      "CGuildBattleReservedScheduleMapGroup::UpdateClearGuildBattleDayInfo() : g_Main.m_pWorldDB->UpdateClearGuildBattleI"
      "nfo( %u, %u ) Fail!",
      dwStartID,
      dwEndID);
    result = 0;
  }
  return result;
}
