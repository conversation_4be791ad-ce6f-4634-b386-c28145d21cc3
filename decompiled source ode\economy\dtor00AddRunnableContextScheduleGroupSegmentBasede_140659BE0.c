/*
 * Function: ?dtor$0@?0??AddRunnableContext@ScheduleGroupSegmentBase@details@Concurrency@@IEAAXPEAVInternalContextBase@23@Vlocation@3@@Z@4HA_7
 * Address: 0x140659BE0
 */

void __fastcall `Concurrency::details::ScheduleGroupSegmentBase::AddRunnableContext'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  std::_Deque_iterator<unsigned int,std::allocator<unsigned int>,0>::~_Deque_iterator<unsigned int,std::allocator<unsigned int>,0>(*(std::_Ranit<unsigned int,__int64,unsigned int const *,unsigned int const &> **)(a2 + 192));
}
