/*
 * Function: ?_check_exp_after_attack@CPlayer@@QEAAHHPEAU_be_damaged_char@@AEAVCPartyModeKillMonsterExpNotify@@@Z
 * Address: 0x140089930
 */

__int64 __fastcall CPlayer::_check_exp_after_attack(CPlayer *this, int nDamagedObjNum, _be_damaged_char *pList, CPartyModeKillMonsterExpNotify *kPartyExpNotify)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  unsigned int v8; // [sp+20h] [bp-28h]@4
  bool v9; // [sp+24h] [bp-24h]@4
  int j; // [sp+28h] [bp-20h]@4
  CCharacter **v11; // [sp+30h] [bp-18h]@6
  int nDam; // [sp+38h] [bp-10h]@10
  CPlayer *v13; // [sp+50h] [bp+8h]@1
  int v14; // [sp+58h] [bp+10h]@1
  _be_damaged_char *v15; // [sp+60h] [bp+18h]@1
  CPartyModeKillMonsterExpNotify *kPartyExpNotifya; // [sp+68h] [bp+20h]@1

  kPartyExpNotifya = kPartyExpNotify;
  v15 = pList;
  v14 = nDamagedObjNum;
  v13 = this;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = 0;
  v9 = CPlayer::IsRidingUnit(v13);
  for ( j = 0; j < v14; ++j )
  {
    v11 = &v15[j].m_pChar;
    if ( *((_DWORD *)v11 + 2) > 0 )
    {
      v8 += *((_DWORD *)v11 + 2);
      v8 += *((_DWORD *)v11 + 4);
    }
    if ( (unsigned __int8)((int (__fastcall *)(CCharacter *))(*v11)->vfptr->IsRewardExp)(*v11) )
    {
      if ( *((_DWORD *)v11 + 2) > 1 )
      {
        nDam = *((_DWORD *)v11 + 4) + *((_DWORD *)v11 + 2);
        CPlayer::CalcExp(v13, *v11, nDam, kPartyExpNotifya);
      }
    }
  }
  return v8;
}
