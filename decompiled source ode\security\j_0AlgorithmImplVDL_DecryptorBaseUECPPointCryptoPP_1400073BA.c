/*
 * Function: j_??0?$AlgorithmImpl@V?$DL_DecryptorBase@UECPPoint@CryptoPP@@@CryptoPP@@U?$ECIES@VECP@CryptoPP@@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@2@$0A@@2@@CryptoPP@@QEAA@XZ
 * Address: 0x1400073BA
 */

void __fastcall CryptoPP::AlgorithmImpl<CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,0>>::AlgorithmImpl<CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,0>>(CryptoPP::AlgorithmImpl<CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0>,0> > *this)
{
  CryptoPP::AlgorithmImpl<CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,0>>::AlgorithmImpl<CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,0>>(this);
}
