/*
 * Function: ?Save@?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@CryptoPP@@UEBAXAEBV?$DL_GroupPrecomputation@UECPPoint@CryptoPP@@@2@AEAVBufferedTransformation@2@@Z
 * Address: 0x140578720
 */

int __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::Save(__int64 a1, __int64 *a2, struct CryptoPP::BufferedTransformation *a3)
{
  unsigned __int64 v3; // rax@2
  CryptoPP::ECPPoint *v4; // rax@3
  CryptoPP::DERGeneralEncoder v6; // [sp+30h] [bp-98h]@1
  unsigned __int64 _Pos; // [sp+90h] [bp-38h]@1
  __int64 v8; // [sp+98h] [bp-30h]@1
  __int64 v9; // [sp+A0h] [bp-28h]@1
  unsigned __int64 v10; // [sp+A8h] [bp-20h]@2
  __int64 v11; // [sp+B0h] [bp-18h]@3
  __int64 v12; // [sp+D0h] [bp+8h]@1
  __int64 *v13; // [sp+D8h] [bp+10h]@1

  v13 = a2;
  v12 = a1;
  v8 = -2i64;
  CryptoPP::DERSequenceEncoder::DERSequenceEncoder((CryptoPP::DERSequenceEncoder *)&v6, a3, 48);
  CryptoPP::DEREncodeUnsigned<unsigned int>((CryptoPP::BufferedTransformation *)&v6.vfptr, 1u, 2u);
  v9 = *(_QWORD *)(v12 + 104);
  (*(void (__fastcall **)(signed __int64, CryptoPP::DERGeneralEncoder *))(v9 + 16))(v12 + 104, &v6);
  for ( LODWORD(_Pos) = 0; ; LODWORD(_Pos) = _Pos + 1 )
  {
    v10 = (unsigned int)_Pos;
    v3 = std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::size((std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *)(v12 + 144));
    if ( v10 >= v3 )
      break;
    v4 = std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::operator[](
           (std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *)(v12 + 144),
           (unsigned int)_Pos);
    v11 = *v13;
    (*(void (__fastcall **)(__int64 *, CryptoPP::DERGeneralEncoder *, CryptoPP::ECPPoint *))(v11 + 40))(v13, &v6, v4);
  }
  CryptoPP::DERGeneralEncoder::MessageEnd(&v6);
  return CryptoPP::DERSequenceEncoder::~DERSequenceEncoder((CryptoPP::DERSequenceEncoder *)&v6);
}
