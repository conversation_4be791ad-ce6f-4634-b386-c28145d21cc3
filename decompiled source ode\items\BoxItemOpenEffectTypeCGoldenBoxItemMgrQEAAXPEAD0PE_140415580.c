/*
 * Function: ?BoxItemOpenEffectType@CGoldenBoxItemMgr@@QEAAXPEAD0PEAEPEA_N@Z
 * Address: 0x140415580
 */

void __fastcall CGoldenBoxItemMgr::BoxItemOpenEffectType(CGoldenBoxItemMgr *this, char *szUseItem, char *szNewItem, char *pbyType, bool *bCircle)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  const char *Str1; // [sp+38h] [bp+10h]@1
  const char *v9; // [sp+40h] [bp+18h]@1
  char *v10; // [sp+48h] [bp+20h]@1

  v10 = pbyType;
  v9 = szNewItem;
  Str1 = szUseItem;
  v5 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  *bCircle = 1;
  if ( !strcmp_0(szUseItem, "bxgol01") )
  {
    *v10 = 9;
  }
  else if ( !strcmp_0(Str1, "bxgol02") )
  {
    *v10 = 10;
  }
  else if ( !strcmp_0(Str1, "bxgol03") )
  {
    *v10 = 11;
  }
  else if ( !strcmp_0(Str1, "bxgol05") )
  {
    *v10 = 10;
  }
  else if ( !strcmp_0(Str1, "iogld01") )
  {
    if ( !strcmp_0(v9, "iygld01") || !strcmp_0(v9, "iygld02") )
    {
      *v10 = 8;
      *bCircle = 0;
    }
  }
  else if ( !strcmp_0(Str1, "iogld02") )
  {
    *v10 = 12;
  }
  else if ( !strcmp_0(Str1, "bxgol04") )
  {
    if ( !strcmp_0(v9, "iygld11") )
    {
      *v10 = 6;
      *bCircle = 0;
    }
    else if ( !strcmp_0(v9, "iygld12") )
    {
      *v10 = 7;
    }
  }
  else
  {
    *v10 = 0;
  }
}
