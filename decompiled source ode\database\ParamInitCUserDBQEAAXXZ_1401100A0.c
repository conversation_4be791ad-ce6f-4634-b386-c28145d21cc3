/*
 * Function: ?ParamInit@CUserDB@@QEAAXXZ
 * Address: 0x1401100A0
 */

void __fastcall CUserDB::ParamInit(CUserDB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CUserDB *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->m_idWorld.dwSerial = -1;
  v5->m_gidGlobal.dwSerial = -1;
  _AVATOR_DATA::InitData(&v5->m_AvatorData);
  _AVATOR_DATA::InitData(&v5->m_AvatorData_bk);
  for ( j = 0; j < 3; ++j )
    _REGED::init(&v5->m_RegedList[j]);
  v5->m_bActive = 0;
  v5->m_dwSerial = -1;
  v5->m_bDBWaitState = 0;
  v5->m_pDBPushData = 0i64;
  v5->m_bChatLock = 0;
  v5->m_bNoneUpdateData = 0;
  v5->m_byUserDgr = 0;
  v5->m_bySubDgr = 0;
  _SYNC_STATE::Init(&v5->m_ss);
  v5->m_dwTotalPlayMin = 0;
  v5->m_bDataUpdate = 0;
  v5->m_dwLastContSaveTime = 0;
  CRadarItemMgr::Init(&v5->m_RadarItemMgr);
  v5->m_byUILock = -1;
  v5->m_byUILock_InitFailCnt = -1;
  v5->m_byUILock_FailCnt = -1;
  v5->m_szUILock_PW[0] = 0;
  v5->m_szAccount_PW[0] = 0;
  v5->m_byUILock_HintIndex = -1;
  v5->m_uszUILock_HintAnswer[0] = 0;
  v5->m_byUILock_InitFindPassFailCount = -1;
  v5->m_byUILockFindPassFailCount = 0;
  memset_0(v5->m_dwRequestMoveCharacterSerialList, 0, 0xCui64);
  memset_0(v5->m_dwTournamentCharacterSerialList, 0, 0xCui64);
}
