/*
 * Function: ?CheckTakeGravityStone@CGuildBattleController@@QEAAXHPEAVCPlayer@@@Z
 * Address: 0x1403D6020
 */

void __fastcall CGuildBattleController::CheckTakeGravityStone(CGuildBattleController *this, int iPortalInx, CPlayer *pkPlayer)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleManager *v5; // rax@7
  __int64 v6; // [sp+0h] [bp-58h]@1
  int v7; // [sp+30h] [bp-28h]@4
  int n; // [sp+34h] [bp-24h]@4
  unsigned int dwGuildSerial; // [sp+38h] [bp-20h]@7
  unsigned int v10; // [sp+3Ch] [bp-1Ch]@7
  unsigned int v11; // [sp+40h] [bp-18h]@5
  int iPortalInxa; // [sp+68h] [bp+10h]@1
  CPlayer *v13; // [sp+70h] [bp+18h]@1

  v13 = pkPlayer;
  iPortalInxa = iPortalInx;
  v3 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = CPlayerDB::GetRaceCode(&pkPlayer->m_Param);
  n = v13->m_ObjID.m_wIndex;
  if ( v13->m_Param.m_pGuild )
    v11 = v13->m_Param.m_pGuild->m_dwSerial;
  else
    v11 = -1;
  dwGuildSerial = v11;
  v10 = v13->m_pUserDB->m_dwSerial;
  v5 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
  GUILD_BATTLE::CNormalGuildBattleManager::CheckTakeGravityStone(v5, iPortalInxa, n, dwGuildSerial, v10);
}
