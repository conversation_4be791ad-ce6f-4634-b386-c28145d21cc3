/*
 * Function: ?AlterExp_MasterReport@CAnimus@@QEAAX_J@Z
 * Address: 0x1401292B0
 */

void __fastcall CAnimus::AlterExp_MasterReport(CAnimus *this, __int64 nAlterExp)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CAnimus *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v5->m_pMaster )
    CPlayer::AlterExp_Animus(v5->m_pMaster, nAlterExp);
}
