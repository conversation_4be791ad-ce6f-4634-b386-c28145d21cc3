/*
 * Function: ?dev_set_animus_lv@CPlayer@@QEAA_NH@Z
 * Address: 0x1400BFC60
 */

char __fastcall CPlayer::dev_set_animus_lv(CPlayer *this, int nAnimusLv)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  _STORAGE_LIST::_db_con *v5; // rcx@21
  __int64 v6; // [sp+0h] [bp-128h]@1
  bool bUpdate[2]; // [sp+20h] [bp-108h]@21
  _animus_fld *v8; // [sp+30h] [bp-F8h]@9
  _animus_fld *v9; // [sp+38h] [bp-F0h]@9
  unsigned int *v10; // [sp+40h] [bp-E8h]@9
  int j; // [sp+48h] [bp-E0h]@9
  char szMsg[2]; // [sp+58h] [bp-D0h]@22
  unsigned __int64 v13; // [sp+5Ah] [bp-CEh]@22
  char pbyType; // [sp+84h] [bp-A4h]@22
  char v15; // [sp+85h] [bp-A3h]@22
  char v16[2]; // [sp+A4h] [bp-84h]@22
  __int16 v17; // [sp+A6h] [bp-82h]@22
  char v18; // [sp+C4h] [bp-64h]@22
  char v19; // [sp+C5h] [bp-63h]@22
  char v20[2]; // [sp+E4h] [bp-44h]@22
  __int16 v21; // [sp+E6h] [bp-42h]@22
  char v22; // [sp+104h] [bp-24h]@22
  char v23; // [sp+105h] [bp-23h]@22
  CPlayer *v24; // [sp+130h] [bp+8h]@1
  int dwLv; // [sp+138h] [bp+10h]@1

  dwLv = nAnimusLv;
  v24 = this;
  v2 = &v6;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v24->m_pUserDB )
  {
    if ( nAnimusLv <= CPlayerDB::GetMaxLevel(&v24->m_Param) && dwLv )
    {
      v8 = 0i64;
      v9 = 0i64;
      v10 = 0i64;
      for ( j = 0; j < 4; ++j )
      {
        if ( v24->m_Param.m_dbAnimus.m_pStorageList[j].m_bLoad && !v24->m_Param.m_dbAnimus.m_pStorageList[j].m_bLock )
        {
          v10 = &v24->m_Param.m_dbAnimus.m_pStorageList[j].m_dwLv;
          if ( dwLv == 1 )
          {
            v24->m_Param.m_dbAnimus.m_pStorageList[j].m_dwDur = 0i64;
            v9 = GetAnimusFldFromLv(v24->m_Param.m_dbAnimus.m_pStorageList[j].m_wItemIndex, 1u);
            *(_WORD *)v10 = v9->m_nMaxHP;
            *((_WORD *)v10 + 1) = v9->m_nMaxFP;
          }
          else
          {
            v8 = GetAnimusFldFromLv(v24->m_Param.m_dbAnimus.m_pStorageList[j].m_wItemIndex, dwLv - 1);
            v9 = GetAnimusFldFromLv(v24->m_Param.m_dbAnimus.m_pStorageList[j].m_wItemIndex, dwLv);
            if ( !v8 || !v9 )
              return 0;
            v24->m_Param.m_dbAnimus.m_pStorageList[j].m_dwDur = v8->m_nForLvUpExp + 1;
            *(_WORD *)v10 = v9->m_nMaxHP;
            *((_WORD *)v10 + 1) = v9->m_nMaxFP;
          }
          if ( v24->m_pUserDB )
          {
            v5 = v24->m_Param.m_dbAnimus.m_pStorageList;
            bUpdate[0] = 0;
            CUserDB::Update_ItemDur(v24->m_pUserDB, 4, j, v5[j].m_dwDur, 0);
          }
          *(_WORD *)szMsg = v24->m_Param.m_dbAnimus.m_pStorageList[j].m_wSerial;
          v13 = v24->m_Param.m_dbAnimus.m_pStorageList[j].m_dwDur;
          pbyType = 22;
          v15 = 11;
          CNetProcess::LoadSendMsg(unk_1414F2088, v24->m_ObjID.m_wIndex, &pbyType, szMsg, 0xAu);
          *(_WORD *)v16 = v24->m_Param.m_dbAnimus.m_pStorageList[j].m_wSerial;
          v17 = *(_WORD *)v10;
          v18 = 22;
          v19 = 9;
          CNetProcess::LoadSendMsg(unk_1414F2088, v24->m_ObjID.m_wIndex, &v18, v16, 4u);
          *(_WORD *)v20 = v24->m_Param.m_dbAnimus.m_pStorageList[j].m_wSerial;
          v21 = *((_WORD *)v10 + 1);
          v22 = 22;
          v23 = 10;
          CNetProcess::LoadSendMsg(unk_1414F2088, v24->m_ObjID.m_wIndex, &v22, v20, 4u);
        }
      }
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
