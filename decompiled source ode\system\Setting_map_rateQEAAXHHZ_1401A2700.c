/*
 * Function: ?Setting@_map_rate@@QEAAXHH@Z
 * Address: 0x1401A2700
 */

void __fastcall _map_rate::Setting(_map_rate *this, int x, int y)
{
  if ( x < y )
  {
    this->fSide[1] = FLOAT_1_0;
    this->fSide[0] = (float)x / (float)y;
    this->nStandard = 1;
    this->nPartner = 0;
  }
  else
  {
    this->fSide[0] = FLOAT_1_0;
    this->fSide[1] = (float)y / (float)x;
    this->nStandard = 0;
    this->nPartner = 1;
  }
}
