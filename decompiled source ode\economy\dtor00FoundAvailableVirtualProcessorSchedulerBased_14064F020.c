/*
 * Function: ?dtor$0@?0??FoundAvailableVirtualProcessor@SchedulerBase@details@Concurrency@@QEAA_NAEAVClaimTicket@VirtualProcessor@23@Vlocation@3@K@Z@4HA_10
 * Address: 0x14064F020
 */

int __fastcall `Concurrency::details::SchedulerBase::FoundAvailableVirtualProcessor'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  __int64 v2; // rcx@1

  v2 = *(_QWORD *)(a2 + 240);
  return std::_Vb_iterator<std::vector<bool,std::allocator<bool>>>::~_Vb_iterator<std::vector<bool,std::allocator<bool>>>();
}
