/*
 * Function: ?ct_tl_info_view@@YA_NPEAVCPlayer@@@Z
 * Address: 0x1402997C0
 */

char __fastcall ct_tl_info_view(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@8
  char *v5; // rax@12
  char *v6; // rax@13
  __int64 v7; // [sp+0h] [bp-2C8h]@1
  bool bFilter[4]; // [sp+20h] [bp-2A8h]@8
  char *pwszMessage; // [sp+28h] [bp-2A0h]@8
  char byPvpGrade[4]; // [sp+30h] [bp-298h]@8
  unsigned int v11; // [sp+40h] [bp-288h]@4
  unsigned int v12; // [sp+44h] [bp-284h]@4
  __int16 v13; // [sp+48h] [bp-280h]@4
  char Dest; // [sp+58h] [bp-270h]@4
  char v15; // [sp+59h] [bp-26Fh]@4
  char DstBuf; // [sp+90h] [bp-238h]@4
  char v17; // [sp+91h] [bp-237h]@4
  CPlayer *v18; // [sp+298h] [bp-30h]@10
  int j; // [sp+2A0h] [bp-28h]@10
  unsigned __int64 v20; // [sp+2B0h] [bp-18h]@4
  CPlayer *v21; // [sp+2D0h] [bp+8h]@1

  v21 = pOne;
  v1 = &v7;
  for ( i = 176i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v20 = (unsigned __int64)&v7 ^ _security_cookie;
  v11 = 0;
  v12 = 0;
  v13 = 0;
  Dest = 0;
  memset(&v15, 0, 0x10ui64);
  DstBuf = 0;
  memset(&v17, 0, 0x1FFui64);
  if ( s_nWordCount > 0 )
  {
    if ( s_nWordCount >= 1 )
    {
      v18 = 0i64;
      for ( j = 0; j < 2532; ++j )
      {
        v18 = &g_Player + j;
        v5 = CPlayerDB::GetCharNameW(&v18->m_Param);
        if ( !strcmp_0(v5, s_pwszDstCheat[0]) )
        {
          v11 = v18->m_pUserDB->m_AvatorData.dbTimeLimitInfo.dwFatigue;
          v12 = v18->m_pUserDB->m_AvatorData.dbTimeLimitInfo.dwLastLogoutTime;
          v13 = v18->m_pUserDB->m_AvatorData.dbTimeLimitInfo.byTLStatus;
          v6 = CPlayerDB::GetCharNameW(&v18->m_Param);
          strcpy_0(&Dest, v6);
          *(_DWORD *)byPvpGrade = v12;
          LODWORD(pwszMessage) = v13;
          *(_DWORD *)bFilter = v11;
          sprintf_s(
            &DstBuf,
            0x200ui64,
            "Player Time Limit Info : Name = %s, Fatigue = %d, Status = %d, LogoutTime = %d",
            &Dest);
          goto LABEL_16;
        }
      }
    }
    sprintf_s(&DstBuf, 0x200ui64, "Player Search Fail!! -> Name = %s", s_pwszDstCheat[0]);
  }
  else
  {
    if ( !v21 || !v21->m_bOper )
      return 0;
    v11 = v21->m_pUserDB->m_AvatorData.dbTimeLimitInfo.dwFatigue;
    v12 = v21->m_pUserDB->m_AvatorData.dbTimeLimitInfo.dwLastLogoutTime;
    v13 = v21->m_pUserDB->m_AvatorData.dbTimeLimitInfo.byTLStatus;
    v4 = CPlayerDB::GetCharNameW(&v21->m_Param);
    strcpy_0(&Dest, v4);
    *(_DWORD *)byPvpGrade = v12;
    LODWORD(pwszMessage) = v13;
    *(_DWORD *)bFilter = v11;
    sprintf_s(
      &DstBuf,
      0x200ui64,
      "Player Time Limit Info : Name = %s, Fatigue = %d, Status = %d, LogoutTime = %d",
      &Dest);
  }
LABEL_16:
  CLogFile::Write(&s_logCheat, &DstBuf);
  CPlayer::SendData_ChatTrans(v21, 0, 0xFFFFFFFF, -1, 0, &DstBuf, -1, 0i64);
  return 1;
}
