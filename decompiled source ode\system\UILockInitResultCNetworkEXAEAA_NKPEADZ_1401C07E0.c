/*
 * Function: ?UILockInitResult@CNetworkEX@@AEAA_NKPEAD@Z
 * Address: 0x1401C07E0
 */

char __fastcall CNetworkEX::UILockInitResult(CNetworkEX *this, unsigned int n, char *pMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  char *v7; // [sp+20h] [bp-18h]@4

  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = pMsg;
  if ( (signed int)*(_WORD *)(pMsg + 1) < 2532 )
  {
    CMainThread::pc_UILockInitResult(&g_Main, pMsg);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
