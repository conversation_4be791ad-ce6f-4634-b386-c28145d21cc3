/*
 * Function: _CryptoPP::DL_DecryptorBase_CryptoPP::ECPPoint_::Decrypt_::_1_::dtor$2
 * Address: 0x1404562C0
 */

void __fastcall CryptoPP::DL_DecryptorBase_CryptoPP::ECPPoint_::Decrypt_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  CryptoPP::Sec<PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~Se<PERSON><PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>((CryptoPP::Se<PERSON><PERSON><PERSON><unsigned char,CryptoPP::Allocator<PERSON>ith<PERSON>leanup<unsigned char,0> > *)(a2 + 376));
}
