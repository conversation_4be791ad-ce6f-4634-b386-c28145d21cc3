/*
 * Function: ?db_update_guildmaster@CMainThread@@QEAAEPEAU_qry_case_update_guildmaster@@@Z
 * Address: 0x1401B2BB0
 */

char __fastcall CMainThread::db_update_guildmaster(CMainThread *this, _qry_case_update_guildmaster *pSheet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMainThread *v6; // [sp+30h] [bp+8h]@1
  _qry_case_update_guildmaster *v7; // [sp+38h] [bp+10h]@1

  v7 = pSheet;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CRFWorldDatabase::Update_GuildMaster(
         v6->m_pWorldDB,
         pSheet->in_guildserial,
         pSheet->in_guild_new_masterSerial,
         pSheet->in_guild_new_masterPrevGrade) )
  {
    if ( v7->in_guild_prev_masterSerial == -1
      || v7->in_guildserial == -1
      || v7->in_guild_new_masterSerial == -1
      || CRFWorldDatabase::Update_UserGuildData(v6->m_pWorldDB, v7->in_guild_prev_masterSerial, v7->in_guildserial, 0) )
    {
      if ( v7->in_guild_new_masterSerial == -1
        || v7->in_guildserial == -1
        || CRFWorldDatabase::Update_UserGuildData(v6->m_pWorldDB, v7->in_guild_new_masterSerial, v7->in_guildserial, 2) )
      {
        result = 0;
      }
      else
      {
        result = 24;
      }
    }
    else
    {
      result = 24;
    }
  }
  else
  {
    result = 24;
  }
  return result;
}
