/*
 * Function: ?_db_Update_Inven@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD@Z
 * Address: 0x1401ABFC0
 */

char __fastcall CMainThread::_db_Update_Inven(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *pSzQuery)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // eax@8
  int v8; // eax@11
  int v9; // eax@12
  int v10; // eax@30
  size_t v11; // rax@32
  __int64 v13; // [sp+0h] [bp-158h]@1
  unsigned int v14; // [sp+20h] [bp-138h]@30
  unsigned __int64 v15; // [sp+28h] [bp-130h]@30
  unsigned int v16; // [sp+30h] [bp-128h]@30
  unsigned int v17; // [sp+38h] [bp-120h]@30
  unsigned int v18; // [sp+40h] [bp-118h]@30
  unsigned int v19; // [sp+48h] [bp-110h]@30
  unsigned int v20; // [sp+50h] [bp-108h]@30
  unsigned __int64 v21; // [sp+58h] [bp-100h]@30
  char Source; // [sp+70h] [bp-E8h]@4
  char v23; // [sp+71h] [bp-E7h]@4
  char *Dest; // [sp+F8h] [bp-60h]@4
  size_t Size; // [sp+100h] [bp-58h]@4
  unsigned int j; // [sp+108h] [bp-50h]@4
  unsigned int v27; // [sp+10Ch] [bp-4Ch]@18
  unsigned int v28; // [sp+110h] [bp-48h]@18
  unsigned int v29; // [sp+114h] [bp-44h]@26
  int v30; // [sp+120h] [bp-38h]@11
  __int64 v31; // [sp+128h] [bp-30h]@30
  __int64 v32; // [sp+130h] [bp-28h]@30
  __int64 v33; // [sp+138h] [bp-20h]@30
  unsigned __int64 v34; // [sp+140h] [bp-18h]@4
  unsigned int v35; // [sp+168h] [bp+10h]@1
  _AVATOR_DATA *v36; // [sp+170h] [bp+18h]@1
  _AVATOR_DATA *v37; // [sp+178h] [bp+20h]@1

  v37 = pOldData;
  v36 = pNewData;
  v35 = dwSerial;
  v5 = &v13;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v34 = (unsigned __int64)&v13 ^ _security_cookie;
  Source = 0;
  memset(&v23, 0, 0x7Fui64);
  Dest = pSzQuery;
  sprintf(pSzQuery, "UPDATE tbl_inven SET ");
  LODWORD(Size) = strlen_0(Dest);
  BYTE4(Size) = 20 * v36->dbAvator.m_byBagNum;
  for ( j = 0; (signed int)j < BYTE4(Size); ++j )
  {
    if ( _INVENKEY::IsFilled((_INVENKEY *)((char *)&v36->dbInven + 37 * (signed int)j)) )
    {
      if ( _INVENKEY::IsFilled((_INVENKEY *)((char *)&v37->dbInven + 37 * (signed int)j)) )
      {
        v30 = _INVENKEY::CovDBKey((_INVENKEY *)((char *)&v36->dbInven + 37 * (signed int)j));
        v8 = _INVENKEY::CovDBKey((_INVENKEY *)((char *)&v37->dbInven + 37 * (signed int)j));
        if ( v30 != v8 )
        {
          v9 = _INVENKEY::CovDBKey((_INVENKEY *)((char *)&v36->dbInven + 37 * (signed int)j));
          sprintf(&Source, "K%d=%d,", j, (unsigned int)v9);
          strcat_0(Dest, &Source);
        }
        if ( v36->dbInven.m_List[j].dwDur != v37->dbInven.m_List[j].dwDur )
        {
          sprintf(&Source, "D%d=%I64d,", j, v36->dbInven.m_List[j].dwDur);
          strcat_0(Dest, &Source);
        }
        if ( v36->dbInven.m_List[j].dwUpt != v37->dbInven.m_List[j].dwUpt )
        {
          sprintf(&Source, "U%d=%d,", j, v36->dbInven.m_List[j].dwUpt);
          strcat_0(Dest, &Source);
        }
        if ( v36->dbInven.m_List[j].byCsMethod )
        {
          v27 = 0;
          v28 = 0;
          if ( v36->dbInven.m_List[j].byCsMethod == 1 )
          {
            v27 = v36->dbInven.m_List[j].dwT - Time;
            v28 = v37->dbInven.m_List[j].dwT - Time;
            sprintf(&Source, "T%d=%d,", j, v27);
            strcat_0(Dest, &Source);
          }
          else if ( v36->dbInven.m_List[j].byCsMethod == 2 )
          {
            v27 = v36->dbInven.m_List[j].dwT;
            v28 = v37->dbInven.m_List[j].dwT;
            if ( v27 != v28 )
            {
              sprintf(&Source, "T%d=%d,", j, v27);
              strcat_0(Dest, &Source);
            }
          }
        }
        if ( v36->dbInven.m_List[j].lnUID != v37->dbInven.m_List[j].lnUID )
        {
          sprintf(&Source, "S%d=%I64d,", j, v36->dbInven.m_List[j].lnUID);
          strcat_0(Dest, &Source);
        }
      }
      else
      {
        v29 = 0;
        if ( v36->dbInven.m_List[j].byCsMethod == 1 )
        {
          v29 = v36->dbInven.m_List[j].dwT - Time;
        }
        else if ( v36->dbInven.m_List[j].byCsMethod == 2 )
        {
          v29 = v36->dbInven.m_List[j].dwT;
        }
        v31 = 37i64 * (signed int)j;
        v32 = 37i64 * (signed int)j;
        v33 = 37i64 * (signed int)j;
        v10 = _INVENKEY::CovDBKey((_INVENKEY *)((char *)&v36->dbInven + 37 * (signed int)j));
        v21 = v36->dbInven.m_List[(unsigned __int64)v31 / 0x25].lnUID;
        v20 = j;
        v19 = v29;
        v18 = j;
        v17 = v36->dbInven.m_List[(unsigned __int64)v32 / 0x25].dwUpt;
        v16 = j;
        v15 = v36->dbInven.m_List[(unsigned __int64)v33 / 0x25].dwDur;
        v14 = j;
        sprintf(&Source, "K%d=%d,D%d=%I64d,U%d=%d,T%d=%d,S%d=%I64d,", j, (unsigned int)v10);
        strcat_0(Dest, &Source);
      }
    }
    else if ( _INVENKEY::IsFilled((_INVENKEY *)((char *)&v37->dbInven + 37 * (signed int)j)) )
    {
      v7 = _INVENKEY::CovDBKey((_INVENKEY *)((char *)&v36->dbInven + 37 * (signed int)j));
      sprintf(&Source, "K%d=%d,", j, (unsigned int)v7);
      strcat_0(Dest, &Source);
    }
  }
  v11 = strlen_0(Dest);
  if ( v11 <= (unsigned int)Size )
  {
    memset_0(Dest, 0, (unsigned int)Size);
  }
  else
  {
    sprintf(&Source, "WHERE Serial=%d", v35);
    Dest[strlen_0(Dest) - 1] = 32;
    strcat_0(Dest, &Source);
  }
  return 1;
}
