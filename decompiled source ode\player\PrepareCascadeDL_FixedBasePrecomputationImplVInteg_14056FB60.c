/*
 * Function: ?PrepareCascade@?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@CryptoPP@@AEBAXAEBV?$DL_GroupPrecomputation@VInteger@CryptoPP@@@2@AEAV?$vector@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@AEBVInteger@2@@Z
 * Address: 0x14056FB60
 */

void __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::PrepareCascade(__int64 a1, __int64 a2, __int64 a3, struct CryptoPP::Integer *a4)
{
  __int64 v4; // rax@1
  unsigned __int64 v5; // rax@6
  signed __int64 v6; // rax@9
  __int64 v7; // rax@9
  __int64 v8; // rax@9
  signed __int64 v9; // rax@10
  __int64 v10; // rax@10
  signed __int64 v11; // rax@12
  __int64 v12; // rax@12
  char v13; // [sp+20h] [bp-208h]@5
  unsigned int i; // [sp+24h] [bp-204h]@5
  __int64 v15; // [sp+28h] [bp-200h]@1
  CryptoPP::Integer v16; // [sp+30h] [bp-1F8h]@1
  CryptoPP::Integer b; // [sp+58h] [bp-1D0h]@1
  CryptoPP::Integer v18; // [sp+80h] [bp-1A8h]@1
  char v19; // [sp+A8h] [bp-180h]@9
  CryptoPP::Integer result; // [sp+F8h] [bp-130h]@9
  char v21; // [sp+120h] [bp-108h]@10
  char v22; // [sp+170h] [bp-B8h]@12
  __int64 v23; // [sp+1C0h] [bp-68h]@1
  int v24; // [sp+1C8h] [bp-60h]@3
  unsigned __int64 v25; // [sp+1D0h] [bp-58h]@6
  CryptoPP::Integer *v26; // [sp+1D8h] [bp-50h]@9
  CryptoPP::Integer *v27; // [sp+1E0h] [bp-48h]@9
  __int64 v28; // [sp+1E8h] [bp-40h]@9
  __int64 v29; // [sp+1F0h] [bp-38h]@9
  __int64 v30; // [sp+1F8h] [bp-30h]@9
  __int64 v31; // [sp+200h] [bp-28h]@10
  __int64 v32; // [sp+208h] [bp-20h]@10
  __int64 v33; // [sp+210h] [bp-18h]@12
  __int64 v34; // [sp+218h] [bp-10h]@12
  __int64 v35; // [sp+230h] [bp+8h]@1
  __int64 v36; // [sp+240h] [bp+18h]@1
  struct CryptoPP::Integer *v37; // [sp+248h] [bp+20h]@1

  v37 = a4;
  v36 = a3;
  v35 = a1;
  v23 = -2i64;
  LODWORD(v4) = (*(int (__fastcall **)(__int64))(*(_QWORD *)a2 + 24i64))(a2);
  v15 = v4;
  CryptoPP::Integer::Integer(&b);
  CryptoPP::Integer::Integer(&v16);
  CryptoPP::Integer::Integer(&v18, v37);
  v24 = (unsigned __int8)(*(int (__fastcall **)(__int64))(*(_QWORD *)v15 + 40i64))(v15) && *(_DWORD *)(v35 + 48) > 1u;
  v13 = v24;
  for ( i = 0; ; ++i )
  {
    v25 = i + 1;
    v5 = std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::size(v35 + 96);
    if ( v25 >= v5 )
      break;
    CryptoPP::Integer::DivideByPowerOf2(&b, &v16, &v18, *(_DWORD *)(v35 + 48));
    std::swap<CryptoPP::Integer>(&v16, &v18);
    if ( v13 && CryptoPP::Integer::GetBit(&b, (unsigned int)(*(_DWORD *)(v35 + 48) - 1)) )
    {
      CryptoPP::Integer::operator++(&v18);
      v26 = CryptoPP::operator-(&result, (CryptoPP::Integer *)(v35 + 56), &b);
      v27 = v26;
      v6 = std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::operator[](v35 + 96, i);
      v28 = *(_QWORD *)v15;
      LODWORD(v7) = (*(int (__fastcall **)(__int64, signed __int64))(v28 + 32))(v15, v6);
      LODWORD(v8) = CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>(
                      &v19,
                      v7,
                      v27);
      v29 = v8;
      v30 = v8;
      std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::push_back(
        v36,
        v8);
      CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>(&v19);
      CryptoPP::Integer::~Integer(&result);
    }
    else
    {
      v9 = std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::operator[](v35 + 96, i);
      LODWORD(v10) = CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>(
                       &v21,
                       v9,
                       &b);
      v31 = v10;
      v32 = v10;
      std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::push_back(
        v36,
        v10);
      CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>(&v21);
    }
  }
  v11 = std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::operator[](v35 + 96, i);
  LODWORD(v12) = CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>(
                   &v22,
                   v11,
                   &v18);
  v33 = v12;
  v34 = v12;
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::push_back(
    v36,
    v12);
  CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>(&v22);
  CryptoPP::Integer::~Integer(&v18);
  CryptoPP::Integer::~Integer(&v16);
  CryptoPP::Integer::~Integer(&b);
}
