/*
 * Function: ?SendSellInfom@CUnmannedTraderUserInfo@@QEAAXGGKKK@Z
 * Address: 0x140358090
 */

void __fastcall CUnmannedTraderUserInfo::SendSellInfom(CUnmannedTraderUserInfo *this, unsigned __int16 wInx, unsigned __int16 wItemSerial, unsigned int dwAddDalant, unsigned int dwTaxDalant, unsigned int dwTotalDalant)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-88h]@1
  char szMsg[2]; // [sp+38h] [bp-50h]@4
  unsigned int v10; // [sp+3Ah] [bp-4Eh]@4
  unsigned int v11; // [sp+3Eh] [bp-4Ah]@4
  unsigned int v12; // [sp+42h] [bp-46h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v14; // [sp+65h] [bp-23h]@4

  v6 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v6 = -*********;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  *(_WORD *)szMsg = wItemSerial;
  v10 = dwAddDalant;
  v11 = dwTaxDalant;
  v12 = dwTotalDalant;
  pbyType = 30;
  v14 = 9;
  CNetProcess::LoadSendMsg(unk_1414F2088, wInx, &pbyType, szMsg, 0xEu);
}
