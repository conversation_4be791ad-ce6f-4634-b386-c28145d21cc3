/*
 * Function: ?Select_UnmannedTraderSearchPageInfo@CRFWorldDatabase@@QEAAEEEEEEKKPEBDPEAU_unmannedtrader_page_info@@@Z
 * Address: 0x1404AE8B0
 */

char __fastcall CRFWorldDatabase::Select_UnmannedTraderSearchPageInfo(CRFWorldDatabase *this, char byType, char byRace, char byClass1, char byClass2, char byClass3, unsigned int dwMaxRowCount, unsigned int dwExcludeRowCount, const char *szSortQuery, _unmannedtrader_page_info *pkInfo)
{
  __int64 *v10; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@16
  char *v13; // rax@25
  char *v14; // rax@25
  char *v15; // rax@25
  char *v16; // rax@25
  char *v17; // rax@25
  char *v18; // rax@25
  char *v19; // rax@25
  char *v20; // rax@25
  char *v21; // rax@25
  char *v22; // rax@25
  __int64 v23; // [sp+0h] [bp-A48h]@1
  SQLLEN BufferLength; // [sp+20h] [bp-A28h]@12
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-A20h]@12
  int v26; // [sp+30h] [bp-A18h]@12
  int v27; // [sp+38h] [bp-A10h]@12
  int v28; // [sp+40h] [bp-A08h]@12
  int v29; // [sp+48h] [bp-A00h]@12
  unsigned int v30; // [sp+50h] [bp-9F8h]@12
  int v31; // [sp+58h] [bp-9F0h]@12
  int v32; // [sp+60h] [bp-9E8h]@12
  int v33; // [sp+68h] [bp-9E0h]@12
  int v34; // [sp+70h] [bp-9D8h]@12
  int v35; // [sp+78h] [bp-9D0h]@12
  int v36; // [sp+80h] [bp-9C8h]@12
  char *v37; // [sp+88h] [bp-9C0h]@12
  char *v38; // [sp+90h] [bp-9B8h]@12
  char szLog; // [sp+B0h] [bp-998h]@12
  char DstBuf; // [sp+8D0h] [bp-178h]@4
  char v41; // [sp+8D1h] [bp-177h]@4
  SQLLEN v42; // [sp+968h] [bp-E0h]@25
  __int16 v43; // [sp+974h] [bp-D4h]@17
  int v44; // [sp+978h] [bp-D0h]@4
  __int16 TargetValue; // [sp+988h] [bp-C0h]@25
  unsigned __int16 v46; // [sp+98Ah] [bp-BEh]@25
  unsigned __int16 v47; // [sp+98Ch] [bp-BCh]@25
  unsigned __int16 v48; // [sp+98Eh] [bp-BAh]@25
  unsigned __int16 v49; // [sp+990h] [bp-B8h]@25
  unsigned __int16 v50; // [sp+992h] [bp-B6h]@25
  tm _Tm; // [sp+9B8h] [bp-90h]@25
  __int64 v52; // [sp+9E8h] [bp-60h]@25
  char *v53; // [sp+9F8h] [bp-50h]@7
  char *v54; // [sp+A00h] [bp-48h]@10
  unsigned __int64 v55; // [sp+A08h] [bp-40h]@4
  CRFWorldDatabase *v56; // [sp+A50h] [bp+8h]@1
  char v57; // [sp+A58h] [bp+10h]@1
  char v58; // [sp+A60h] [bp+18h]@1
  char v59; // [sp+A68h] [bp+20h]@1

  v59 = byClass1;
  v58 = byRace;
  v57 = byType;
  v56 = this;
  v10 = &v23;
  for ( i = 646i64; i; --i )
  {
    *(_DWORD *)v10 = -858993460;
    v10 = (__int64 *)((char *)v10 + 4);
  }
  v55 = (unsigned __int64)&v23 ^ _security_cookie;
  DstBuf = 0;
  memset(&v41, 0, 0x7Fui64);
  v44 = 0;
  if ( szSortQuery )
    sprintf_s(&DstBuf, 0x80ui64, "order by %s", szSortQuery);
  if ( DstBuf )
    v53 = &DstBuf;
  else
    v53 = (char *)&unk_140855215;
  if ( DstBuf )
    v54 = &DstBuf;
  else
    v54 = (char *)&unk_140855216;
  v38 = v53;
  v37 = v54;
  v36 = (unsigned __int8)byClass3;
  v35 = (unsigned __int8)byClass2;
  v34 = (unsigned __int8)v59;
  v33 = (unsigned __int8)v57;
  v32 = (unsigned __int8)v58;
  v31 = (unsigned __int8)v57;
  v30 = dwExcludeRowCount;
  v29 = (unsigned __int8)byClass3;
  v28 = (unsigned __int8)byClass2;
  v27 = (unsigned __int8)v59;
  v26 = (unsigned __int8)v57;
  LODWORD(StrLen_or_IndPtr) = (unsigned __int8)v58;
  LODWORD(BufferLength) = (unsigned __int8)v57;
  sprintf_s(
    &szLog,
    0x800ui64,
    "select top %u si.[serial], si.[k], si.[d], si.[u], s.[price], s.[owner], b.[Name], s.[regdate], s.[sellturm], si.[s]"
    ", si.[t] from [dbo].[tbl_utsingleiteminfo] as si join [dbo].[tbl_utsellinfo] as s on s.type=%u and s.race = %u and s"
    ".serial=si.serial join [dbo].[tbl_utresultinfo] as r on r.type=%u and s.serial=r.serial left join [dbo].[tbl_base] a"
    "s b on s.owner = b.serial where r.state in (1, 2) and si.class1=%u and si.class2=%u and si.class3=%u and si.serial n"
    "ot in ( select top %u si.serial from [dbo].[tbl_utsingleiteminfo] as si join [dbo].[tbl_utsellinfo] as s on s.type=%"
    "u and s.race = %u and s.serial = si.serial join [dbo].[tbl_utresultinfo] as r on r.type=%u and s.serial=r.serial whe"
    "re r.state in (1, 2) and si.class1=%u and si.class2=%u and si.class3=%u %s ) %s",
    dwMaxRowCount);
  if ( v56->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v56->vfptr, &szLog);
  if ( v56->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v56->vfptr) )
  {
    v43 = SQLExecDirect_0(v56->m_hStmtSelect, &szLog, -3);
    if ( v43 && v43 != 1 )
    {
      if ( v43 == 100 )
      {
        result = 2;
      }
      else
      {
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v56->vfptr, v43, v56->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      pkInfo->dwCnt = 0;
      do
      {
        v43 = SQLFetch_0(v56->m_hStmtSelect);
        if ( v43 && v43 != 1 )
          break;
        v13 = (char *)&pkInfo->list[pkInfo->dwCnt];
        StrLen_or_IndPtr = &v42;
        BufferLength = 0i64;
        v43 = SQLGetData_0(v56->m_hStmtSelect, 1u, 4, v13, 0i64, &v42);
        v14 = (char *)&pkInfo->list[pkInfo->dwCnt].dwK;
        StrLen_or_IndPtr = &v42;
        BufferLength = 0i64;
        v43 = SQLGetData_0(v56->m_hStmtSelect, 2u, 4, v14, 0i64, &v42);
        v15 = (char *)&pkInfo->list[pkInfo->dwCnt].dwD;
        StrLen_or_IndPtr = &v42;
        BufferLength = 0i64;
        v43 = SQLGetData_0(v56->m_hStmtSelect, 3u, -25, v15, 0i64, &v42);
        v16 = (char *)&pkInfo->list[pkInfo->dwCnt].dwU;
        StrLen_or_IndPtr = &v42;
        BufferLength = 0i64;
        v43 = SQLGetData_0(v56->m_hStmtSelect, 4u, 4, v16, 0i64, &v42);
        v17 = (char *)&pkInfo->list[pkInfo->dwCnt].dwPrice;
        StrLen_or_IndPtr = &v42;
        BufferLength = 0i64;
        v43 = SQLGetData_0(v56->m_hStmtSelect, 5u, -18, v17, 0i64, &v42);
        v18 = (char *)&pkInfo->list[pkInfo->dwCnt].dwOwner;
        StrLen_or_IndPtr = &v42;
        BufferLength = 0i64;
        v43 = SQLGetData_0(v56->m_hStmtSelect, 6u, 4, v18, 0i64, &v42);
        pkInfo->list[pkInfo->dwCnt].wszOwnerName[0] = 0;
        v19 = pkInfo->list[pkInfo->dwCnt].wszOwnerName;
        StrLen_or_IndPtr = &v42;
        BufferLength = 17i64;
        v43 = SQLGetData_0(v56->m_hStmtSelect, 7u, 1, v19, 17i64, &v42);
        StrLen_or_IndPtr = &v42;
        BufferLength = 0i64;
        v43 = SQLGetData_0(v56->m_hStmtSelect, 8u, 93, &TargetValue, 0i64, &v42);
        v20 = &pkInfo->list[pkInfo->dwCnt].bySellturm;
        StrLen_or_IndPtr = &v42;
        BufferLength = 0i64;
        v43 = SQLGetData_0(v56->m_hStmtSelect, 9u, -6, v20, 0i64, &v42);
        v21 = (char *)&pkInfo->list[pkInfo->dwCnt].lnUID;
        StrLen_or_IndPtr = &v42;
        BufferLength = 0i64;
        v43 = SQLGetData_0(v56->m_hStmtSelect, 0xAu, -25, v21, 0i64, &v42);
        v22 = (char *)&pkInfo->list[pkInfo->dwCnt].dwT;
        StrLen_or_IndPtr = &v42;
        BufferLength = 0i64;
        v43 = SQLGetData_0(v56->m_hStmtSelect, 0xBu, 4, v22, 0i64, &v42);
        _Tm.tm_year = TargetValue - 1900;
        _Tm.tm_mon = v46 - 1;
        _Tm.tm_mday = v47;
        _Tm.tm_hour = v48;
        _Tm.tm_min = v49;
        _Tm.tm_sec = v50;
        _Tm.tm_isdst = -1;
        v52 = mktime_3(&_Tm);
        if ( v52 == -1 )
          v52 = 0i64;
        pkInfo->list[pkInfo->dwCnt++].tRegdate = v52;
      }
      while ( dwMaxRowCount > pkInfo->dwCnt );
      if ( v56->m_hStmtSelect )
        SQLCloseCursor_0(v56->m_hStmtSelect);
      if ( v56->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v56->vfptr, "%s Success", &szLog);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v56->vfptr, "ReConnectDataBase Fail. Query : %s", &szLog);
    result = 1;
  }
  return result;
}
