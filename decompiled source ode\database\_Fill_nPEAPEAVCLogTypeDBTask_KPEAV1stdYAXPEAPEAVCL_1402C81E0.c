/*
 * Function: ??$_Fill_n@PEAPEAVCLogTypeDBTask@@_KPEAV1@@std@@YAXPEAPEAVCLogTypeDBTask@@_KAEBQEAV1@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1402C81E0
 */

void __fastcall std::_Fill_n<CLogTypeDBTask * *,unsigned __int64,CLogTypeDBTask *>(CLogTypeDBTask **_First, unsigned __int64 _Count, CLogTypeDBTask *const *_Val, std::_Range_checked_iterator_tag __formal)
{
  memset64(_First, (unsigned __int64)*_Val, _Count);
}
