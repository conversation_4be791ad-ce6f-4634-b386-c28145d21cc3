/*
 * Function: ?_db_Load_PotionDelay@CMainThread@@AEAAEKPEAU_POTION_NEXT_USE_TIME_DB_BASE@@@Z
 * Address: 0x1401B6990
 */

char __fastcall CMainThread::_db_Load_PotionDelay(CMainThread *this, unsigned int dwSerial, _POTION_NEXT_USE_TIME_DB_BASE *pDbPotionDelay)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-E8h]@1
  _worlddb_potion_delay_info pPotionDelayInfo; // [sp+30h] [bp-B8h]@4
  char v8; // [sp+D4h] [bp-14h]@4
  DWORD v9; // [sp+D8h] [bp-10h]@11
  int j; // [sp+DCh] [bp-Ch]@11
  CMainThread *v11; // [sp+F0h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+F8h] [bp+10h]@1
  _POTION_NEXT_USE_TIME_DB_BASE *v13; // [sp+100h] [bp+18h]@1

  v13 = pDbPotionDelay;
  dwSeriala = dwSerial;
  v11 = this;
  v3 = &v6;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  _worlddb_potion_delay_info::_worlddb_potion_delay_info(&pPotionDelayInfo);
  v8 = CRFWorldDatabase::Select_PotionDelay(v11->m_pWorldDB, dwSeriala, &pPotionDelayInfo);
  if ( v8 == 1 )
    return 24;
  if ( v8 != 2 )
    goto LABEL_17;
  if ( !CRFWorldDatabase::Insert_PotionDelay(v11->m_pWorldDB, dwSeriala) )
    return 24;
  if ( CRFWorldDatabase::Select_PotionDelay(v11->m_pWorldDB, dwSeriala, &pPotionDelayInfo) )
  {
    result = 24;
  }
  else
  {
LABEL_17:
    v9 = timeGetTime();
    for ( j = 0; j < 38; ++j )
      v13->dwPotionNextUseTime[j] = v9 + pPotionDelayInfo.dwPotionDelay[j];
    result = 0;
  }
  return result;
}
