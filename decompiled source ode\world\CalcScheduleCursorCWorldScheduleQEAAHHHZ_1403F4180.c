/*
 * Function: ?CalcScheduleCursor@CWorldSchedule@@QEAAHHH@Z
 * Address: 0x1403F4180
 */

signed __int64 __fastcall CWorldSchedule::CalcScheduleCursor(CWorldSchedule *this, int nHour, int nMin)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@5
  int v6; // eax@6
  __int64 v8; // [sp+0h] [bp-58h]@1
  int v9; // [sp+20h] [bp-38h]@4
  int n; // [sp+24h] [bp-34h]@4
  int v11; // [sp+28h] [bp-30h]@6
  _base_fld *v12; // [sp+30h] [bp-28h]@8
  _base_fld *v13; // [sp+38h] [bp-20h]@8
  unsigned int v14; // [sp+40h] [bp-18h]@8
  unsigned int v15; // [sp+44h] [bp-14h]@8
  CWorldSchedule *v16; // [sp+60h] [bp+8h]@1

  v16 = this;
  v3 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = nMin + 60 * nHour;
  for ( n = 0; ; ++n )
  {
    v5 = CRecordData::GetRecordNum(&v16->m_tblSch);
    if ( n >= v5 )
      break;
    v11 = n + 1;
    v6 = CRecordData::GetRecordNum(&v16->m_tblSch);
    if ( v11 >= v6 )
      v11 = 0;
    v12 = CRecordData::GetRecord(&v16->m_tblSch, n);
    v13 = CRecordData::GetRecord(&v16->m_tblSch, v11);
    v14 = *(_DWORD *)&v12[1].m_strCode[0] + 60 * v12[1].m_dwIndex;
    v15 = *(_DWORD *)&v13[1].m_strCode[0] + 60 * v13[1].m_dwIndex;
    if ( (signed int)v14 <= (signed int)v15 )
    {
      if ( v9 >= (signed int)v14 && v9 <= (signed int)v15 )
        return (unsigned int)n;
    }
    else if ( v9 >= (signed int)v14 )
    {
      return (unsigned int)n;
    }
  }
  return 0xFFFFFFFFi64;
}
