/*
 * Function: ?erase@?$deque@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@QEAA?AV?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@2@V32@0@Z
 * Address: 0x14031DC40
 */

std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::deque<RECV_DATA,std::allocator<RECV_DATA>>::erase(std::deque<RECV_DATA,std::allocator<RECV_DATA> > *this, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *result, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_First, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Last)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v6; // rax@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v7; // rax@5
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v8; // rax@5
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v9; // rax@9
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v10; // rax@9
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v11; // rax@12
  __int64 v13; // [sp+0h] [bp-268h]@1
  __int64 _Off; // [sp+20h] [bp-248h]@4
  __int64 v15; // [sp+28h] [bp-240h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> resulta; // [sp+30h] [bp-238h]@4
  bool v17; // [sp+50h] [bp-218h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> v18; // [sp+58h] [bp-210h]@4
  char v19; // [sp+78h] [bp-1F0h]@5
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v20; // [sp+98h] [bp-1D0h]@5
  char v21; // [sp+A0h] [bp-1C8h]@5
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v22; // [sp+C0h] [bp-1A8h]@5
  char v23; // [sp+C8h] [bp-1A0h]@5
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v24; // [sp+E8h] [bp-180h]@5
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> v25; // [sp+F0h] [bp-178h]@5
  char v26; // [sp+110h] [bp-158h]@9
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v27; // [sp+130h] [bp-138h]@9
  char v28; // [sp+138h] [bp-130h]@9
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v29; // [sp+158h] [bp-110h]@9
  char v30; // [sp+160h] [bp-108h]@9
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v31; // [sp+180h] [bp-E8h]@9
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> v32; // [sp+188h] [bp-E0h]@9
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> v33; // [sp+1A8h] [bp-C0h]@12
  int v34; // [sp+1C8h] [bp-A0h]@4
  __int64 v35; // [sp+1D0h] [bp-98h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v36; // [sp+1D8h] [bp-90h]@4
  std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Right; // [sp+1E0h] [bp-88h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v38; // [sp+1E8h] [bp-80h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v39; // [sp+1F0h] [bp-78h]@4
  int v40; // [sp+1F8h] [bp-70h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v41; // [sp+200h] [bp-68h]@5
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v42; // [sp+208h] [bp-60h]@5
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v43; // [sp+210h] [bp-58h]@5
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v44; // [sp+218h] [bp-50h]@5
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v45; // [sp+220h] [bp-48h]@5
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v46; // [sp+228h] [bp-40h]@9
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v47; // [sp+230h] [bp-38h]@9
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v48; // [sp+238h] [bp-30h]@9
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v49; // [sp+240h] [bp-28h]@9
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v50; // [sp+248h] [bp-20h]@9
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v51; // [sp+250h] [bp-18h]@12
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v52; // [sp+258h] [bp-10h]@12
  std::deque<RECV_DATA,std::allocator<RECV_DATA> > *v53; // [sp+270h] [bp+8h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v54; // [sp+278h] [bp+10h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v55; // [sp+280h] [bp+18h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__that; // [sp+288h] [bp+20h]@1

  __that = _Last;
  v55 = _First;
  v54 = result;
  v53 = this;
  v4 = &v13;
  for ( i = 152i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v35 = -2i64;
  v34 = 0;
  v36 = std::deque<RECV_DATA,std::allocator<RECV_DATA>>::begin(v53, &resulta);
  _Right = (std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)v36;
  _Off = std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator-(
           v55,
           (std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v36->_Mycont);
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(&resulta);
  v15 = std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator-(
          __that,
          (std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v55->_Mycont);
  v38 = std::deque<RECV_DATA,std::allocator<RECV_DATA>>::end(v53, &v18);
  v39 = v38;
  v6 = std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator-(
         v38,
         (std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&__that->_Mycont);
  v40 = _Off < v6;
  v17 = _Off < v6;
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(&v18);
  if ( v17 )
  {
    v20 = (std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v19;
    v22 = (std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v21;
    v24 = (std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v23;
    std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
      (std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v19,
      __that);
    v41 = v7;
    v42 = v7;
    std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
      v22,
      v55);
    v43 = v8;
    v44 = v8;
    v45 = std::deque<RECV_DATA,std::allocator<RECV_DATA>>::begin(v53, v24);
    std::copy_backward<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>>(
      &v25,
      v45,
      v44,
      v42);
    std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(&v25);
    while ( v15 )
    {
      std::deque<RECV_DATA,std::allocator<RECV_DATA>>::pop_front(v53);
      --v15;
    }
  }
  else
  {
    v27 = (std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v26;
    v29 = (std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v28;
    v31 = (std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v30;
    std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
      (std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v26,
      v55);
    v46 = v9;
    v47 = v9;
    v48 = std::deque<RECV_DATA,std::allocator<RECV_DATA>>::end(v53, v29);
    v49 = v48;
    std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
      v31,
      __that);
    v50 = v10;
    std::copy<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>>(
      &v32,
      v10,
      v49,
      v47);
    std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(&v32);
    while ( v15 )
    {
      std::deque<RECV_DATA,std::allocator<RECV_DATA>>::pop_back(v53);
      --v15;
    }
  }
  v11 = std::deque<RECV_DATA,std::allocator<RECV_DATA>>::begin(v53, &v33);
  v51 = v11;
  v52 = v11;
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator+(v11, v54, _Off);
  v34 |= 1u;
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(&v33);
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(v55);
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(__that);
  return v54;
}
