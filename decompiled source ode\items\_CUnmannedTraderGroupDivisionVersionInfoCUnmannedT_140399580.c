/*
 * Function: _CUnmannedTraderGroupDivisionVersionInfo::CUnmannedTraderGroupDivisionVersionInfo_::_1_::dtor$0
 * Address: 0x140399580
 */

void __fastcall CUnmannedTraderGroupDivisionVersionInfo::CUnmannedTraderGroupDivisionVersionInfo_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::vector<unsigned long,std::allocator<unsigned long>>::~vector<unsigned long,std::allocator<unsigned long>>((std::vector<unsigned long,std::allocator<unsigned long> > *)(*(_QWORD *)(a2 + 64) + 8i64));
}
