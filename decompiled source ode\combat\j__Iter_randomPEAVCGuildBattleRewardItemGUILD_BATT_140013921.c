/*
 * Function: j_??$_Iter_random@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAVCGuildBattleRewardItem@GUILD_BATTLE@@0@Z
 * Address: 0x140013921
 */

std::random_access_iterator_tag __fastcall std::_Iter_random<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *>(GUILD_BATTLE::CGuildBattleRewardItem *const *__formal, GUILD_BATTLE::CGuildBattleRewardItem *const *a2)
{
  return std::_Iter_random<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *>(__formal, a2);
}
