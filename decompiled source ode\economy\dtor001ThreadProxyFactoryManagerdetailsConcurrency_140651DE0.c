/*
 * Function: ?dtor$0@?0???**************************@details@Concurrency@@QEAA@XZ@4HA_4
 * Address: 0x140651DE0
 */

void __fastcall `Concurrency::details::ThreadProxyFactoryManager::~ThreadProxyFactoryManager'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  CryptoPP::Sec<PERSON>lock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~<PERSON><PERSON><PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>((CryptoPP::Sec<PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(*(_QWORD *)(a2 + 64) + 24i64));
}
