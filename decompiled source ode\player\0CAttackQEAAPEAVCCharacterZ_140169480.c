/*
 * Function: ??0CAttack@@QEAA@PEAVCCharacter@@@Z
 * Address: 0x140169480
 */

void __fastcall CAttack::CAttack(CAttack *this, CCharacter *pThis)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CAttack *v5; // [sp+30h] [bp+8h]@1
  CCharacter *v6; // [sp+38h] [bp+10h]@1

  v6 = pThis;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  `vector constructor iterator'(
    v5->m_DamList,
    0x18ui64,
    30,
    (void *(__cdecl *)(void *))_be_damaged_char::_be_damaged_char);
  v5->m_pAttChar = v6;
  v5->m_bIsCrtAtt = 0;
  v5->m_bFailure = 0;
  v5->m_pp = &CAttack::s_DefParam;
  v5->m_nDamagedObjNum = 0;
}
