/*
 * Function: j_?exchange_money@CMgrAvatorItemHistory@@QEAAXHKKKKPEAD@Z
 * Address: 0x14000F0E7
 */

void __fastcall CMgrAvatorItemHistory::exchange_money(CMgrAvatorItemHistory *this, int n, unsigned int dwCurDalant, unsigned int dwCurGold, unsigned int dwNewDalant, unsigned int dwNewGold, char *pszFileName)
{
  CMgrAvatorItemHistory::exchange_money(this, n, dwCurDalant, dwCurGold, dwNewDalant, dwNewGold, pszFileName);
}
