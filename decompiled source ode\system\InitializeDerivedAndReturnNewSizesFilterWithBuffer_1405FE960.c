/*
 * Function: ?InitializeDerivedAndReturnNewSizes@FilterWithBufferedInput@CryptoPP@@MEAAXAEBVNameValuePairs@2@AEA_K11@Z
 * Address: 0x1405FE960
 */

void __fastcall CryptoPP::FilterWithBufferedInput::InitializeDerivedAndReturnNewSizes(CryptoPP::FilterWithBufferedInput *this, const struct CryptoPP::NameValuePairs *a2, unsigned __int64 *a3, unsigned __int64 *a4, unsigned __int64 *a5)
{
  ((void (*)(void))this->vfptr[24].__vecDelDtor)();
}
