/*
 * Function: ?ReleaseSFCont@CEquipItemSFAgent@@QEAAXH@Z
 * Address: 0x140121670
 */

void __fastcall CEquipItemSFAgent::ReleaseSFCont(CEquipItemSFAgent *this, int nEquipTblIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-58h]@1
  bool bAura; // [sp+20h] [bp-38h]@13
  _sf_continous *v6; // [sp+30h] [bp-28h]@5
  int j; // [sp+38h] [bp-20h]@6
  int k; // [sp+3Ch] [bp-1Ch]@8
  _sf_continous *v9; // [sp+40h] [bp-18h]@11
  CEquipItemSFAgent *v10; // [sp+60h] [bp+8h]@1

  v10 = this;
  v2 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v10->m_pMaster )
  {
    v6 = CEquipItemSFAgent::GetEquipSFCont(v10, nEquipTblIndex);
    if ( v6 )
    {
      for ( j = 0; j < 2; ++j )
      {
        for ( k = 0; k < 8; ++k )
        {
          v9 = &v10->m_pMaster->m_SFCont[j][k];
          if ( v9->m_bExist && v6 == v9 )
          {
            bAura = 0;
            CCharacter::RemoveSFContEffect((CCharacter *)&v10->m_pMaster->vfptr, j, k, 0, 0);
            return;
          }
        }
      }
    }
  }
}
