/*
 * Function: ?SendMsgAlterState@CGravityStoneRegener@@AEAAXXZ
 * Address: 0x14012EF50
 */

void __fastcall CGravityStoneRegener::SendMsgAlterState(CGravityStoneRegener *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  char v5; // [sp+38h] [bp-40h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v7; // [sp+55h] [bp-23h]@4
  int v8; // [sp+64h] [bp-14h]@4
  CGravityStoneRegener *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(_DWORD *)szMsg = v9->m_iPortalInx;
  v8 = v9->m_eState == 2;
  v5 = v8;
  pbyType = 4;
  v7 = -83;
  CGameObject::CircleReport((CGameObject *)&v9->vfptr, &pbyType, szMsg, 5, 0);
}
