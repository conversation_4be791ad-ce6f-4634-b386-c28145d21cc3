/*
 * Function: ?<PERSON><PERSON><PERSON><PERSON>@CNormalGuildBattle@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403E49E0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::Ask<PERSON>oin(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v3; // rax@4
  char *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattle *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v6->m_k2P);
  GUILD_BATTLE::CNormalGuildBattleGuild::AskJoin(&v6->m_k1P, v3);
  v4 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v6->m_k1P);
  GUILD_BATTLE::CNormalGuildBattleGuild::AskJoin(&v6->m_k2P, v4);
}
