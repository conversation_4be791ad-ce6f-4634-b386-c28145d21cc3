/*
 * Function: ??0_dh_player_mgr@@QEAA@XZ
 * Address: 0x14026EA90
 */

void __fastcall _dh_player_mgr::_dh_player_mgr(_dh_player_mgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _dh_player_mgr *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _dh_player_mgr::Init(v4);
}
