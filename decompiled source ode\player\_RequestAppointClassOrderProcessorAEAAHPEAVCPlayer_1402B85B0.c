/*
 * Function: ?_RequestAppoint@ClassOrderProcessor@@AEAAHPEAVCPlayer@@PEAD@Z
 * Address: 0x1402B85B0
 */

signed __int64 __fastcall ClassOrderProcessor::_RequestAppoint(ClassOrderProcessor *this, CPlayer *pOne, char *pData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v5; // rax@4
  unsigned int v6; // eax@4
  signed __int64 result; // rax@5
  __int64 v8; // rax@6
  char v9; // al@8
  unsigned __int16 v10; // ax@10
  __int64 v11; // [sp+0h] [bp-98h]@1
  char *pwszAvatorName; // [sp+20h] [bp-78h]@9
  char *v13; // [sp+30h] [bp-68h]@6
  char v14; // [sp+38h] [bp-60h]@8
  CPlayer *pUser; // [sp+40h] [bp-58h]@8
  int v16; // [sp+48h] [bp-50h]@8
  char pbyType; // [sp+54h] [bp-44h]@10
  char v18; // [sp+55h] [bp-43h]@10
  _pt_propose_appoint_zocl v19; // [sp+74h] [bp-24h]@10
  unsigned int v20; // [sp+84h] [bp-14h]@4
  int v21; // [sp+88h] [bp-10h]@4
  ClassOrderProcessor *v22; // [sp+A0h] [bp+8h]@1
  CPlayer *v23; // [sp+A8h] [bp+10h]@1
  char *v24; // [sp+B0h] [bp+18h]@1

  v24 = pData;
  v23 = pOne;
  v22 = this;
  v3 = &v11;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v20 = CPlayerDB::GetCharSerial(&pOne->m_Param);
  v21 = CPlayerDB::GetRaceCode(&v23->m_Param);
  v5 = CPvpUserAndGuildRankingSystem::Instance();
  v6 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v5, v21, 0);
  if ( v20 == v6 )
  {
    v13 = v24;
    v8 = (unsigned __int8)*v24;
    if ( (signed int)(unsigned __int8)*v24 < 4 )
    {
      v14 = *v13 + 5;
      pUser = (CPlayer *)CMainThread::GetCharW(&g_Main, v13 + 1);
      v9 = CPlayerDB::GetRaceCode(&v23->m_Param);
      v16 = ClassOrderProcessor::_CheckUserInfo(v22, v9, v14, pUser);
      if ( v16 )
      {
        pwszAvatorName = v13 + 1;
        ClassOrderProcessor::SendMsg_QueryAppointResult(v22, v23->m_id.wIndex, v16, *v13, v13 + 1);
        result = 0i64;
      }
      else
      {
        pUser->m_byPatriarchAppointPropose = v14;
        pUser->m_dwPatriarchAppointTime = timeGetTime();
        pbyType = 56;
        v18 = 11;
        _pt_propose_appoint_zocl::_pt_propose_appoint_zocl(&v19);
        v19.byClassType = *v13;
        v10 = _pt_propose_appoint_zocl::size(&v19);
        CNetProcess::LoadSendMsg(unk_1414F2088, pUser->m_id.wIndex, &pbyType, &v19.byClassType, v10);
        result = 0i64;
      }
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 20i64;
  }
  return result;
}
