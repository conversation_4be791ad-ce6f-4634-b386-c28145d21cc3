/*
 * Function: ?auto_trade_sell@CMgrAvatorItemHistory@@QEAAXPEBDK0KPEAU_db_con@_STORAGE_LIST@@KKKKPEAD@Z
 * Address: 0x14023A200
 */

void __fastcall CMgrAvatorItemHistory::auto_trade_sell(CMgrAvatorItemHistory *this, const char *szBuyerName, unsigned int dwBuyerSerial, const char *szBuyerID, unsigned int dwRegistSerial, _STORAGE_LIST::_db_con *pItem, unsigned int dwPrice, unsigned int dwTax, unsigned int dwLeftDalant, unsigned int dwLeftGold, char *pszFileName)
{
  __int64 *v11; // rdi@1
  signed __int64 i; // rcx@1
  char *v13; // rax@4
  __int64 v14; // [sp+0h] [bp-88h]@1
  unsigned __int64 v15; // [sp+20h] [bp-68h]@4
  char *v16; // [sp+28h] [bp-60h]@4
  unsigned __int64 v17; // [sp+30h] [bp-58h]@4
  unsigned int v18; // [sp+38h] [bp-50h]@4
  unsigned int v19; // [sp+40h] [bp-48h]@4
  unsigned int v20; // [sp+48h] [bp-40h]@4
  unsigned int v21; // [sp+50h] [bp-38h]@4
  char *v22; // [sp+58h] [bp-30h]@4
  char *v23; // [sp+60h] [bp-28h]@4
  _base_fld *v24; // [sp+70h] [bp-18h]@4
  CMgrAvatorItemHistory *v25; // [sp+90h] [bp+8h]@1

  v25 = this;
  v11 = &v14;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v11 = -*********;
    v11 = (__int64 *)((char *)v11 + 4);
  }
  sData[0] = 0;
  v23 = v25->m_szCurTime;
  v22 = v25->m_szCurDate;
  v21 = dwLeftGold;
  v20 = dwLeftDalant;
  v19 = dwTax;
  v18 = dwPrice;
  v17 = (unsigned __int64)szBuyerID;
  LODWORD(v16) = dwBuyerSerial;
  v15 = (unsigned __int64)szBuyerName;
  sprintf_s(
    sBuf,
    0x2800ui64,
    "AUTO TRADE(SELL): reg(%u) buyer(%s:%d id:%s) recv(D:%u) tax(%u) $D:%u $G:%u [%s %s]\r\n",
    dwRegistSerial);
  strcat_s(sData, 0x4E20ui64, sBuf);
  v24 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
  v13 = DisplayItemUpgInfo(pItem->m_byTableCode, pItem->m_dwLv);
  v17 = pItem->m_lnUID;
  v16 = v13;
  v15 = pItem->m_dwDur;
  sprintf_s(sBuf, 0x2800ui64, "\t- %s_%u_@%s[%I64u]\r\n", v24->m_strCode);
  strcat_s(sData, 0x4E20ui64, sBuf);
  CMgrAvatorItemHistory::WriteFile(v25, pszFileName, sData);
}
