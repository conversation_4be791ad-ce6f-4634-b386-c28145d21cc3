/*
 * Function: ?ChannelMessageSeriesEnd@BufferedTransformation@CryptoPP@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H_N@Z
 * Address: 0x1405F4BA0
 */

int __fastcall CryptoPP::BufferedTransformation::ChannelMessageSeriesEnd(__int64 a1, __int64 a2, unsigned int a3, unsigned __int8 a4)
{
  CryptoPP::BufferedTransformation::NoChannelSupport v5; // [sp+20h] [bp-58h]@3
  __int64 v6; // [sp+80h] [bp+8h]@1
  unsigned int v7; // [sp+90h] [bp+18h]@1
  unsigned __int8 v8; // [sp+98h] [bp+20h]@1

  v8 = a4;
  v7 = a3;
  v6 = a1;
  if ( !(unsigned __int8)std::basic_string<char,std::char_traits<char>,std::allocator<char>>::empty(a2) )
  {
    CryptoPP::BufferedTransformation::NoChannelSupport::NoChannelSupport(&v5);
    CxxThrowException_0((__int64)&v5, (__int64)&TI4_AUNoChannelSupport_BufferedTransformation_CryptoPP__);
  }
  return (*(int (__fastcall **)(__int64, _QWORD, _QWORD))(*(_QWORD *)v6 + 96i64))(v6, v7, v8);
}
