/*
 * Function: ?ConvertCodeIntoItem@@YAPEAU_db_con@_STORAGE_LIST@@PEADEE@Z
 * Address: 0x140166EA0
 */

_STORAGE_LIST::_db_con *__fastcall ConvertCodeIntoItem(char *pszItemCode, char byOverlapNum, char bySocketConfig)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-58h]@1
  char v7; // [sp+20h] [bp-38h]@4
  _base_fld *v8; // [sp+28h] [bp-30h]@6
  unsigned __int64 dwExp; // [sp+30h] [bp-28h]@8
  char v10; // [sp+38h] [bp-20h]@14
  char v11; // [sp+39h] [bp-1Fh]@18
  char v12; // [sp+3Ah] [bp-1Eh]@18
  char v13; // [sp+3Bh] [bp-1Dh]@21
  __int64 v14; // [sp+40h] [bp-18h]@4
  char *psItemCode; // [sp+60h] [bp+8h]@1
  char v16; // [sp+68h] [bp+10h]@1
  char v17; // [sp+70h] [bp+18h]@1

  v17 = bySocketConfig;
  v16 = byOverlapNum;
  psItemCode = pszItemCode;
  v3 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = -2i64;
  v7 = GetItemTableCode(psItemCode);
  if ( (unsigned __int8)v7 == 255 )
    return 0i64;
  v8 = CRecordData::GetRecordByHash((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)v7, psItemCode, 2, 5);
  if ( !v8 )
    return 0i64;
  LODWORD(dwExp) = GetItemDurPoint((unsigned __int8)v7, v8->m_dwIndex);
  if ( IsOverLapItem((unsigned __int8)v7) )
  {
    if ( (signed int)(unsigned __int8)v16 > 0 )
    {
      if ( (signed int)(unsigned __int8)v16 > 99 )
        v16 = 99;
    }
    else
    {
      v16 = 1;
    }
    LODWORD(dwExp) = (unsigned __int8)v16;
  }
  HIDWORD(dwExp) = 0xFFFFFFF;
  v10 = GetItemKindCode((unsigned __int8)v7);
  if ( v10 )
  {
    if ( v10 != 1 )
      return 0i64;
    HIDWORD(dwExp) = GetMaxParamFromExp(v8->m_dwIndex, (unsigned int)dwExp);
  }
  else if ( v17 )
  {
    if ( v17 == 1 )
    {
      v11 = GetDefItemUpgSocketNum((unsigned __int8)v7, v8->m_dwIndex);
      v12 = 0;
      if ( (signed int)(unsigned __int8)v11 > 0 )
        v12 = rand() % (unsigned __int8)v11 + 1;
      HIDWORD(dwExp) = GetBitAfterSetLimSocket(v12);
    }
    else
    {
      v13 = GetDefItemUpgSocketNum((unsigned __int8)v7, v8->m_dwIndex);
      HIDWORD(dwExp) = GetBitAfterSetLimSocket(v13);
    }
  }
  else
  {
    HIDWORD(dwExp) = GetBitAfterSetLimSocket(0);
  }
  if ( !(_S1_0 & 1) )
  {
    _S1_0 |= 1u;
    _STORAGE_LIST::_db_con::_db_con(&item);
  }
  item.m_byTableCode = v7;
  item.m_wItemIndex = v8->m_dwIndex;
  item.m_dwDur = (unsigned int)dwExp;
  item.m_dwLv = HIDWORD(dwExp);
  return &item;
}
