/*
 * Function: ?PushCreateGuildBattleRankTable@CGuildBattleController@@QEAAXXZ
 * Address: 0x1402CFC50
 */

void __fastcall CGuildBattleController::PushCreateGuildBattleRankTable(CGuildBattleController *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleRankManager *v3; // rax@4
  __int64 v4; // [sp+0h] [bp-28h]@1

  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = GUILD_BATTLE::CGuildBattleRankManager::Instance();
  GUILD_BATTLE::CGuildBattleRankManager::PushCreateGuildBattleRankTable(v3);
}
