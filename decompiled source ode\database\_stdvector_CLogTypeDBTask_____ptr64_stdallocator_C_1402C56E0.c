/*
 * Function: _std::vector_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64___::erase_::_1_::dtor$0
 * Address: 0x1402C56E0
 */

void __fastcall std::vector_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64___::erase_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(*(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > **)(a2 + 104));
}
