/*
 * Function: ?Clear@CReservedGuildSchedulePage@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403CC220
 */

char __fastcall GUILD_BATTLE::CReservedGuildSchedulePage::Clear(GUILD_BATTLE::CReservedGuildSchedulePage *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v3; // rax@6
  char result; // al@6
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  int v7; // [sp+24h] [bp-14h]@4
  unsigned int v8; // [sp+28h] [bp-10h]@6
  GUILD_BATTLE::CReservedGuildSchedulePage *Dst; // [sp+40h] [bp+8h]@1

  Dst = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = GetCurDay();
  v7 = GetNextDay();
  if ( v6 != -1 && v7 != -1 )
  {
    Dst->m_dwVer = 0;
    memset_0(Dst, 0, 0x14ui64);
    memset_0(Dst->m_dw2PGuildSerial, 0, 0x14ui64);
    memset_0(Dst->m_pkList, 0, 0xD3ui64);
    Dst->m_pkList->byToDay = v6;
    Dst->m_pkList->byTomorrow = v7;
    Dst->m_pkList->byPage = Dst->m_ucPageInx;
    Dst->m_pkList->bySelfScheduleInx = -1;
    result = 1;
  }
  else
  {
    v8 = Dst->m_ucPageInx;
    v3 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v3,
      "CReservedGuildSchedulePage::InitClear(%u) : -1 == iToDay || -1 == iTomorrow Fail!",
      v8);
    result = 0;
  }
  return result;
}
