/*
 * Function: ?SendMsg_NewViewOther@CPlayer@@QEAAXE@Z
 * Address: 0x1400D40C0
 */

void __fastcall CPlayer::SendMsg_NewViewOther(CPlayer *this, char byViewType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-A8h]@1
  char szMsg[2]; // [sp+38h] [bp-70h]@4
  unsigned int v6; // [sp+3Ah] [bp-6Eh]@4
  __int16 v7; // [sp+3Eh] [bp-6Ah]@4
  __int16 pShort; // [sp+40h] [bp-68h]@4
  char v9; // [sp+46h] [bp-62h]@4
  char v10; // [sp+47h] [bp-61h]@4
  unsigned __int64 v11; // [sp+48h] [bp-60h]@4
  unsigned __int16 v12; // [sp+50h] [bp-58h]@4
  char v13; // [sp+52h] [bp-56h]@4
  char pbyType; // [sp+74h] [bp-34h]@4
  char v15; // [sp+75h] [bp-33h]@4
  unsigned __int64 v16; // [sp+90h] [bp-18h]@4
  CPlayer *v17; // [sp+B0h] [bp+8h]@1
  char v18; // [sp+B8h] [bp+10h]@1

  v18 = byViewType;
  v17 = this;
  v2 = &v4;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v16 = (unsigned __int64)&v4 ^ _security_cookie;
  *(_WORD *)szMsg = v17->m_ObjID.m_wIndex;
  v6 = v17->m_dwObjSerial;
  v7 = CPlayer::GetVisualVer(v17);
  FloatToShort(v17->m_fCurPos, &pShort, 3);
  v9 = CPlayerDB::GetRaceSexCode(&v17->m_Param);
  v10 = v18;
  v11 = CPlayer::GetStateFlag(v17);
  v12 = v17->m_wLastContEffect;
  v13 = v17->m_byGuildBattleColorInx;
  pbyType = 3;
  v15 = 36;
  CGameObject::CircleReport((CGameObject *)&v17->vfptr, &pbyType, szMsg, 27, 0);
}
