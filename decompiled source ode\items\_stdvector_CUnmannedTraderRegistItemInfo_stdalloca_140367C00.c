/*
 * Function: _std::vector_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo___::vector_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo____::_1_::catch$0
 * Address: 0x140367C00
 */

void __fastcall __noreturn std::vector_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo___::vector_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo____::_1_::catch_0(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Tidy(*(std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > **)(a2 + 176));
  CxxThrowException_0(0i64, 0i64);
}
