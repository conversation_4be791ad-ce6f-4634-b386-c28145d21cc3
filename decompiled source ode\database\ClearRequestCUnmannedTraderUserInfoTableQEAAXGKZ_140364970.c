/*
 * Function: ?ClearRequest@CUnmannedTraderUserInfoTable@@QEAAXGK@Z
 * Address: 0x140364970
 */

void __fastcall CUnmannedTraderUserInfoTable::ClearRequest(CUnmannedTraderUserInfoTable *this, unsigned __int16 wInx, unsigned int dwOwner)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  CUnmannedTraderUserInfo *v6; // [sp+20h] [bp-18h]@4
  CPlayer *v7; // [sp+28h] [bp-10h]@4
  CUnmannedTraderUserInfoTable *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = CUnmannedTraderUserInfoTable::FindUser(v8, wInx, dwOwner);
  v7 = 0i64;
  if ( !CUnmannedTraderUserInfo::IsNull(v6) )
  {
    v7 = CUnmannedTraderUserInfo::FindOwner(v6);
    if ( v7 )
    {
      if ( v7->m_bOper )
        CUnmannedTraderUserInfo::ClearRequest(v6);
    }
  }
}
