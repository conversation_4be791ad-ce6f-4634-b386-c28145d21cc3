/*
 * Function: ?ExecUpdateQuery@CRFNewDatabase@@QEAA_NPEAD_N@Z
 * Address: 0x1404872F0
 */

char __fastcall CRFNewDatabase::ExecUpdateQuery(CRFNewDatabase *this, char *strQuery, bool bNoDataError)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-48h]@1
  void *SQLStmt; // [sp+20h] [bp-28h]@14
  __int16 v8; // [sp+30h] [bp-18h]@9
  CRFNewDatabase *v9; // [sp+50h] [bp+8h]@1
  char *szSqlStr; // [sp+58h] [bp+10h]@1
  bool v11; // [sp+60h] [bp+18h]@1

  v11 = bNoDataError;
  szSqlStr = strQuery;
  v9 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v9->m_bSaveDBLog )
    CRFNewDatabase::FmtLog(v9, "Query : %s", strQuery);
  if ( v9->m_hStmtUpdate || CRFNewDatabase::ReConnectDataBase(v9) )
  {
    v8 = SQLExecDirectA_0(v9->m_hStmtUpdate, szSqlStr, -3);
    if ( v8 && v8 != 1 )
    {
      if ( v8 != 100 || v11 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog(v9, v8, szSqlStr, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction(v9, v8, v9->m_hStmtUpdate);
        result = 0;
      }
      else
      {
        result = 1;
      }
    }
    else
    {
      if ( v9->m_bSaveDBLog )
        CRFNewDatabase::FmtLog(v9, "ExecUpdateQuery : %s Query Success", szSqlStr);
      result = 1;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog(v9, "ReConnectDataBase Fail. Query : %s", szSqlStr);
    result = 0;
  }
  return result;
}
