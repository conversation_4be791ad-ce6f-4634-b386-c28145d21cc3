/*
 * Function: ?Regist@CUnmannedTraderUserInfo@@QEAAXEPEAU_a_trade_reg_item_request_clzo@@PEAVCLogFile@@@Z
 * Address: 0x1403537F0
 */

void __fastcall CUnmannedTraderUserInfo::Regist(CUnmannedTraderUserInfo *this, char byType, _a_trade_reg_item_request_clzo *pRequest, CLogFile *pkLogger)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-118h]@1
  char *byTempSlotIndex; // [sp+20h] [bp-F8h]@6
  char v8; // [sp+54h] [bp-C4h]@5
  char v9; // [sp+74h] [bp-A4h]@5
  char v10; // [sp+94h] [bp-84h]@5
  char v11; // [sp+B4h] [bp-64h]@5
  unsigned int v12; // [sp+D4h] [bp-44h]@5
  unsigned int dwRetParam1; // [sp+F4h] [bp-24h]@5
  char v14; // [sp+104h] [bp-14h]@5
  CUnmannedTraderUserInfo *v15; // [sp+120h] [bp+8h]@1
  char v16; // [sp+128h] [bp+10h]@1
  _a_trade_reg_item_request_clzo *pRequesta; // [sp+130h] [bp+18h]@1
  CLogFile *pkLoggera; // [sp+138h] [bp+20h]@1

  pkLoggera = pkLogger;
  pRequesta = pRequest;
  v16 = byType;
  v15 = this;
  v4 = &v6;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( (signed int)(unsigned __int8)byType < 2 )
  {
    v8 = -1;
    v9 = 0;
    v10 = 0;
    v11 = 0;
    v12 = 0;
    dwRetParam1 = 0;
    CUnmannedTraderUserInfo::CountRegistItem(v15);
    v14 = CUnmannedTraderUserInfo::CheckRegist(v15, v16, pRequesta, pkLoggera, &v8, &v9, &v10, &v11, &v12, &dwRetParam1);
    if ( v14 )
    {
      LODWORD(byTempSlotIndex) = dwRetParam1;
      CUnmannedTraderUserInfo::SendRegistItemErrorResult(v15, v15->m_wInx, v14, pRequesta->wItemSerial, dwRetParam1);
    }
    else
    {
      v14 = CUnmannedTraderUserInfo::RegistItem(v15, v16, pRequesta, v8, v9, v10, v11, v12, dwRetParam1);
      if ( v14 )
      {
        LODWORD(byTempSlotIndex) = dwRetParam1;
        CUnmannedTraderUserInfo::SendRegistItemErrorResult(v15, v15->m_wInx, v14, pRequesta->wItemSerial, dwRetParam1);
      }
    }
  }
}
