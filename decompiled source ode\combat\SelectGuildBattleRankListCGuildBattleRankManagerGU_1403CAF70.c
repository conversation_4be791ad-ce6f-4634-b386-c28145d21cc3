/*
 * Function: ?SelectGuildBattleRankList@CGuildBattleRankManager@GUILD_BATTLE@@QEAA_NEPEAE@Z
 * Address: 0x1403CAF70
 */

bool __fastcall GUILD_BATTLE::CGuildBattleRankManager::SelectGuildBattleRankList(GUILD_BATTLE::CGuildBattleRankManager *this, char byRace, char *pOutData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  void *Dst; // [sp+20h] [bp-18h]@4
  char v8; // [sp+48h] [bp+10h]@1

  v8 = byRace;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  Dst = pOutData;
  memset_0(pOutData, 0, 0x55F4ui64);
  return CRFWorldDatabase::SelectGuildBattleRankList(pkDB, v8, (_worlddb_guild_battle_rank_list *)Dst) != 0;
}
