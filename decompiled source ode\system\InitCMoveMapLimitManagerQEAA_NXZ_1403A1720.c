/*
 * Function: ?Init@CMoveMapLimitManager@@QEAA_NXZ
 * Address: 0x1403A1720
 */

char __fastcall CMoveMapLimitManager::Init(CMoveMapLimitManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-68h]@1
  std::vector<int,std::allocator<int> > vecRightTypeList; // [sp+28h] [bp-40h]@6
  char v6; // [sp+54h] [bp-14h]@7
  char v7; // [sp+55h] [bp-13h]@9
  char v8; // [sp+56h] [bp-12h]@10
  __int64 v9; // [sp+58h] [bp-10h]@4
  CMoveMapLimitManager *v10; // [sp+70h] [bp+8h]@1

  v10 = this;
  v1 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = -2i64;
  if ( CMoveMapLimitEnviromentValues::Init() )
  {
    std::vector<int,std::allocator<int>>::vector<int,std::allocator<int>>(&vecRightTypeList);
    if ( CMoveMapLimitInfoList::Init(&v10->m_kLimitInfo, &vecRightTypeList) )
    {
      if ( CMoveMapLimitRightInfoList::Init(&v10->m_kRightInfo, &vecRightTypeList) )
      {
        v8 = 1;
        std::vector<int,std::allocator<int>>::~vector<int,std::allocator<int>>(&vecRightTypeList);
        result = v8;
      }
      else
      {
        v7 = 0;
        std::vector<int,std::allocator<int>>::~vector<int,std::allocator<int>>(&vecRightTypeList);
        result = v7;
      }
    }
    else
    {
      v6 = 0;
      std::vector<int,std::allocator<int>>::~vector<int,std::allocator<int>>(&vecRightTypeList);
      result = v6;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
