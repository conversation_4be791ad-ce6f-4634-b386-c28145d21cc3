/*
 * Function: ?SendData_PartyMemberFP@CPlayer@@QEAAXXZ
 * Address: 0x1400DDBB0
 */

void __fastcall CPlayer::SendData_PartyMemberFP(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-88h]@1
  CPartyPlayer **v4; // [sp+30h] [bp-58h]@5
  char szMsg[4]; // [sp+44h] [bp-44h]@6
  unsigned __int16 v6; // [sp+48h] [bp-40h]@6
  int v7; // [sp+54h] [bp-34h]@6
  char pbyType; // [sp+64h] [bp-24h]@6
  char v9; // [sp+65h] [bp-23h]@6
  int j; // [sp+74h] [bp-14h]@6
  CPlayer *v11; // [sp+90h] [bp+8h]@1

  v11 = this;
  v1 = &v3;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v11->m_pPartyMgr )
  {
    v4 = CPartyPlayer::GetPtrPartyMember(v11->m_pPartyMgr);
    if ( v4 )
    {
      *(_DWORD *)szMsg = v11->m_dwObjSerial;
      v6 = v11->m_wPointRate_PartySend[1];
      v7 = CPartyPlayer::GetPopPartyMember(v11->m_pPartyMgr);
      pbyType = 16;
      v9 = 21;
      for ( j = 0; j < v7; ++j )
      {
        if ( v4[j] != v11->m_pPartyMgr )
          CNetProcess::LoadSendMsg(unk_1414F2088, v4[j]->m_wZoneIndex, &pbyType, szMsg, 6u);
      }
    }
  }
}
