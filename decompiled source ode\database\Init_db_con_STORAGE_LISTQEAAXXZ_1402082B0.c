/*
 * Function: ?Init@_db_con@_STORAGE_LIST@@QEAAXXZ
 * Address: 0x1402082B0
 */

void __fastcall _STORAGE_LIST::_db_con::Init(_STORAGE_LIST::_db_con *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _STORAGE_LIST::_db_con *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_pInList = 0i64;
  v4->m_byStorageIndex = -1;
  _STORAGE_LIST::_storage_con::Init((_STORAGE_LIST::_storage_con *)&v4->m_bLoad);
}
