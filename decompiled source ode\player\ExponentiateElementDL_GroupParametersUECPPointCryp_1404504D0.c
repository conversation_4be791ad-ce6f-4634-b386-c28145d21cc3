/*
 * Function: ?ExponentiateElement@?$DL_GroupParameters@UECPPoint@CryptoPP@@@CryptoPP@@UEBA?AUECPPoint@2@AEBU32@AEBVInteger@2@@Z
 * Address: 0x1404504D0
 */

CryptoPP::ECPPoint *__fastcall CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::ExponentiateElement(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *this, CryptoPP::ECPPoint *result, CryptoPP::ECPPoint *base, CryptoPP::Integer *exponent)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CryptoPP::GeneratableCryptoMaterialVtbl *v6; // rax@4
  __int64 v8; // [sp+0h] [bp-B8h]@1
  int v9; // [sp+20h] [bp-98h]@4
  CryptoPP::ECPPoint __that; // [sp+40h] [bp-78h]@4
  int v11; // [sp+A4h] [bp-14h]@4
  __int64 v12; // [sp+A8h] [bp-10h]@4
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *v13; // [sp+C0h] [bp+8h]@1
  CryptoPP::ECPPoint *v14; // [sp+C8h] [bp+10h]@1
  CryptoPP::ECPPoint *v15; // [sp+D0h] [bp+18h]@1
  CryptoPP::Integer *v16; // [sp+D8h] [bp+20h]@1

  v16 = exponent;
  v15 = base;
  v14 = result;
  v13 = this;
  v4 = &v8;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = -2i64;
  v11 = 0;
  CryptoPP::ECPPoint::ECPPoint(&__that);
  v6 = v13->vfptr;
  v9 = 1;
  ((void (__fastcall *)(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *, CryptoPP::ECPPoint *, CryptoPP::ECPPoint *, CryptoPP::Integer *))v6[20].__vecDelDtor)(
    v13,
    &__that,
    v15,
    v16);
  CryptoPP::ECPPoint::ECPPoint(v14, &__that);
  v11 |= 1u;
  CryptoPP::ECPPoint::~ECPPoint(&__that);
  return v14;
}
