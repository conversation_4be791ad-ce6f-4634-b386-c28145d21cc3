/*
 * Function: ?_PushItemMove@LtdWriter@@AEAAXEPEAU_LTD_PARAM@@PEAU_LTD@@@Z
 * Address: 0x14024B300
 */

void __fastcall LtdWriter::_PushItemMove(LtdWriter *this, char bySubLogType, _LTD_PARAM *pParam, _LTD *pl)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-58h]@1
  bool bExpend[8]; // [sp+20h] [bp-38h]@5
  int nMoveType; // [sp+28h] [bp-30h]@9
  char j; // [sp+30h] [bp-28h]@18
  unsigned __int8 k; // [sp+31h] [bp-27h]@24
  int v11; // [sp+34h] [bp-24h]@4
  int v12; // [sp+38h] [bp-20h]@7
  int v13; // [sp+3Ch] [bp-1Ch]@11
  int v14; // [sp+40h] [bp-18h]@15
  int v15; // [sp+44h] [bp-14h]@21
  int v16; // [sp+48h] [bp-10h]@27
  LtdWriter *v17; // [sp+60h] [bp+8h]@1
  _LTD_PARAM *v18; // [sp+70h] [bp+18h]@1
  _LTD *pla; // [sp+78h] [bp+20h]@1

  pla = pl;
  v18 = pParam;
  v17 = this;
  v4 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = (unsigned __int8)bySubLogType;
  switch ( bySubLogType )
  {
    case 0x3C:
    case 0x46:
      bExpend[0] = 1;
      LtdWriter::_SetLtd(v17, pParam->m_pUserDB, pl, 0, 1);
      LtdWriter::_SetExpend(v17, v18->m_wszEtc, &pla->m_Expend);
      break;
    case 0:
    case 0xA:
    case 0x1E:
    case 0x28:
      bExpend[0] = 1;
      LtdWriter::_SetLtd(v17, pParam->m_pUserDB, pl, 1, 1);
      if ( v18->m_DstItem.m_pByOverlapNum )
        v12 = *v18->m_DstItem.m_pByOverlapNum;
      else
        v12 = 1;
      nMoveType = 0;
      *(_QWORD *)bExpend = (char *)pla + 60;
      LtdWriter::_SetItemInfo(v17, 0, v18->m_DstItem.m_pItems, v12, &pla->m_ItemInfo, 0);
      LtdWriter::_SetExpend(v17, v18->m_wszEtc, &pla->m_Expend);
      break;
    case 0x50:
      bExpend[0] = 0;
      LtdWriter::_SetLtd(v17, pParam->m_pUserDB, pl, 1, 0);
      if ( v18->m_DstItem.m_pByOverlapNum )
        v13 = *v18->m_DstItem.m_pByOverlapNum;
      else
        v13 = 1;
      nMoveType = 0;
      *(_QWORD *)bExpend = (char *)pla + 60;
      LtdWriter::_SetItemInfo(v17, 0, v18->m_DstItem.m_pItems, v13, &pla->m_ItemInfo, 0);
      break;
    case 0x5A:
      bExpend[0] = 1;
      LtdWriter::_SetLtd(v17, pParam->m_pUserDB, pl, 1, 1);
      if ( v18->m_DstItem.m_pByOverlapNum )
        v14 = *v18->m_DstItem.m_pByOverlapNum;
      else
        v14 = 1;
      nMoveType = 0;
      *(_QWORD *)bExpend = (char *)pla + 60;
      LtdWriter::_SetItemInfo(v17, 0, v18->m_DstItem.m_pItems, v14, &pla->m_ItemInfo, 0);
      LtdWriter::_SetExpend(v17, v18->m_wszEtc, &pla->m_Expend);
      break;
    case 0x14:
    case 0x32:
    case 0x64:
      bExpend[0] = 1;
      LtdWriter::_SetLtd(v17, pParam->m_pUserDB, pl, 1, 1);
      for ( j = 0; (unsigned __int8)j < (signed int)v18->m_DstItem.m_byNum; ++j )
      {
        if ( v18->m_DstItem.m_pByOverlapNum )
          v15 = *v18->m_DstItem.m_pByOverlapNum;
        else
          v15 = 1;
        LtdWriter::_SetItemInfo(v17, j, &v18->m_DstItem.m_pItems[(unsigned __int8)j], v15, &pla->m_ItemInfo, 1);
      }
      for ( k = 0; k < (signed int)v18->m_MtrItem1.m_byNum; ++k )
      {
        if ( v18->m_MtrItem1.m_pByOverlapNum )
          v16 = *v18->m_MtrItem1.m_pByOverlapNum;
        else
          v16 = 1;
        LtdWriter::_SetItemInfo(v17, k + j, &v18->m_MtrItem1.m_pItems[k], v16, &pla->m_ItemInfo, 2);
      }
      LtdWriter::_SetExpend(v17, v18->m_wszEtc, &pla->m_Expend);
      break;
    default:
      return;
  }
}
