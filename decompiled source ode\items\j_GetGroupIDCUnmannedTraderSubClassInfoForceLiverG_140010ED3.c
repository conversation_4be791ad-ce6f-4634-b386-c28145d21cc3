/*
 * Function: j_?GetGroupID@CUnmannedTraderSubClassInfoForceLiverGrade@@UEAA_NEGAEAE@Z
 * Address: 0x140010ED3
 */

bool __fastcall CUnmannedTraderSubClassInfoForceLiverGrade::GetGroupID(CUnmannedTraderSubClassInfoForceLiverGrade *this, char byTableCode, unsigned __int16 wItemTableIndex, char *bySubClass)
{
  return CUnmannedTraderSubClassInfoForceLiverGrade::GetGroupID(this, byTableCode, wItemTableIndex, bySubClass);
}
