/*
 * Function: ?Initialize@?$DL_PrivateKey_GFP@VDL_GroupParameters_DSA@CryptoPP@@@CryptoPP@@QEAAXAEAVRandomNumberGenerator@2@AEBVInteger@2@1@Z
 * Address: 0x1405535E0
 */

int __fastcall CryptoPP::DL_PrivateKey_GFP<CryptoPP::DL_GroupParameters_DSA>::Initialize(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  __int64 v4; // rax@1
  __int64 v5; // rax@1
  char v7; // [sp+20h] [bp-108h]@1
  char v8; // [sp+68h] [bp-C0h]@1
  __int64 v9; // [sp+F0h] [bp-38h]@1
  __int64 v10; // [sp+F8h] [bp-30h]@1
  __int64 v11; // [sp+100h] [bp-28h]@1
  __int64 v12; // [sp+108h] [bp-20h]@1
  __int64 v13; // [sp+110h] [bp-18h]@1
  void (__fastcall **v14)(_QWORD, _QWORD, _QWORD); // [sp+118h] [bp-10h]@1
  __int64 v15; // [sp+130h] [bp+8h]@1
  __int64 v16; // [sp+138h] [bp+10h]@1
  __int64 v17; // [sp+148h] [bp+20h]@1

  v17 = a4;
  v16 = a2;
  v15 = a1;
  v9 = -2i64;
  LOBYTE(a4) = 1;
  LODWORD(v4) = CryptoPP::MakeParameters<CryptoPP::Integer>(&v7, "Modulus", a3, a4);
  v10 = v4;
  v11 = v4;
  LODWORD(v5) = CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,CryptoPP::Integer>::operator()<CryptoPP::Integer>(
                  v4,
                  &v8,
                  "SubgroupGenerator",
                  v17);
  v12 = v5;
  v13 = v5;
  v14 = *(void (__fastcall ***)(_QWORD, _QWORD, _QWORD))(v15 + 16);
  (*v14)(v15 + 16, v16, v5);
  CryptoPP::AlgorithmParameters<CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,CryptoPP::Integer>,CryptoPP::Integer>::~AlgorithmParameters<CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,CryptoPP::Integer>,CryptoPP::Integer>(&v8);
  return CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,CryptoPP::Integer>::~AlgorithmParameters<CryptoPP::NullNameValuePairs,CryptoPP::Integer>(&v7);
}
