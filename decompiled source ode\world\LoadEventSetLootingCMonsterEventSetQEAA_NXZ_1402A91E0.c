/*
 * Function: ?LoadEventSetLooting@CMonsterEventSet@@QEAA_NXZ
 * Address: 0x1402A91E0
 */

char __usercall CMonsterEventSet::LoadEventSetLooting@<al>(CMonsterEventSet *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  size_t v6; // rax@11
  size_t v7; // rax@13
  __int16 v8; // ax@17
  __int16 v9; // ax@17
  __int16 v10; // ax@22
  __int16 v11; // ax@22
  char v12; // al@22
  __int64 v13; // [sp-20h] [bp-1918h]@1
  char v14; // [sp+0h] [bp-18F8h]@4
  FILE *File; // [sp+18h] [bp-18E0h]@6
  char Buf; // [sp+40h] [bp-18B8h]@7
  char Dst; // [sp+460h] [bp-1498h]@4
  char v18; // [sp+860h] [bp-1098h]@4
  char v19; // [sp+C60h] [bp-C98h]@4
  char v20; // [sp+1060h] [bp-898h]@4
  char v21; // [sp+1460h] [bp-498h]@4
  char *ppszDst; // [sp+1878h] [bp-80h]@4
  char *Str; // [sp+1880h] [bp-78h]@4
  char *v24; // [sp+1888h] [bp-70h]@4
  char *v25; // [sp+1890h] [bp-68h]@4
  char *v26; // [sp+1898h] [bp-60h]@4
  _FILETIME ftWrite; // [sp+18B8h] [bp-40h]@4
  int v28; // [sp+18C4h] [bp-34h]@6
  char *Dest; // [sp+18C8h] [bp-30h]@17
  _event_set_looting *v30; // [sp+18D0h] [bp-28h]@20
  char *v31; // [sp+18D8h] [bp-20h]@22
  unsigned __int64 v32; // [sp+18E8h] [bp-10h]@4
  CMonsterEventSet *v33; // [sp+1900h] [bp+8h]@1

  v33 = this;
  v2 = alloca(a2);
  v3 = &v13;
  for ( i = 1604i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v32 = (unsigned __int64)&v13 ^ _security_cookie;
  v14 = 0;
  ppszDst = &Dst;
  Str = &v18;
  v24 = &v19;
  v25 = &v20;
  v26 = &v21;
  if ( GetLastWriteFileTime(".\\Initialize\\EventSetLooting.ini", &ftWrite) )
  {
    v33->m_ftLootingWrite = ftWrite;
    memset_0(v33->m_EventSetLootingList, 0, 0x57670ui64);
    v28 = 0;
    if ( fopen_s(&File, ".\\Initialize\\EventSetLooting.ini", "r+t") )
    {
      CLogFile::Write(
        &stru_1799C8F30,
        "Event Set Looting INI Load Error >> can't open INI file : %s",
        ".\\Initialize\\EventSetLooting.ini");
      v33->m_bLoadEventLooting = 0;
      result = 0;
    }
    else
    {
      while ( fgets(&Buf, 1024, File) )
      {
        if ( Buf != 59 && Buf != 10 )
        {
          v6 = strlen_0("[Looting]");
          if ( !strncmp(&Buf, "[Looting]", v6) )
          {
            v14 = 0;
          }
          else
          {
            v7 = strlen_0("[Item]");
            if ( !strncmp(&Buf, "[Item]", v7) )
            {
              v14 = 1;
            }
            else
            {
              memset_0(&Dst, 0, 0x1400ui64);
              if ( v14 )
              {
                if ( ParsingCommandA(&Buf, 5, &ppszDst, 1023) == 5 )
                {
                  v30 = CMonsterEventSet::GetEvenSetLooting(v33, ppszDst);
                  if ( !v30 )
                  {
                    v33->m_bLoadEventLooting = 0;
                    fclose(File);
                    CLogFile::Write(
                      &stru_1799C8F30,
                      "Event Set Looting INI Load Error >> can't find [Looting] data : %s",
                      ppszDst);
                    return 0;
                  }
                  v31 = v30->stEventItemList[v30->nItemCount].strCode;
                  strcpy_0(v31, Str);
                  v10 = atoi(v24);
                  *((_WORD *)v31 + 32) = v10;
                  v11 = atoi(v25);
                  *((_WORD *)v31 + 33) = v11;
                  v12 = atoi(v26);
                  v31[68] = v12;
                  ++v30->nItemCount;
                }
              }
              else if ( ParsingCommandA(&Buf, 5, &ppszDst, 1023) == 5 )
              {
                Dest = v33->m_EventSetLootingList[v28].strCode;
                strcpy_0(Dest, ppszDst);
                v8 = atoi(Str);
                *((_WORD *)Dest + 32) = v8;
                v9 = atoi(v24);
                *((_WORD *)Dest + 33) = v9;
                *((_DWORD *)Dest + 17) = atoi(v25);
                Dest[72] = atoi(v26);
                ++v28;
              }
            }
          }
        }
      }
      fclose(File);
      v33->m_bLoadEventLooting = 1;
      result = 1;
    }
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8F30,
      "Event Set Looting INI Load Error >> can't find INI file : %s",
      ".\\Initialize\\EventSetLooting.ini");
    v33->m_bLoadEventLooting = 0;
    result = 0;
  }
  return result;
}
