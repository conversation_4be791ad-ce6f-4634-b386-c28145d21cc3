/*
 * Function: ?SendWinLoseResult@CNormalGuildBattle@GUILD_BATTLE@@IEAAXXZ
 * Address: 0x1403E79F0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::SendWinLoseResult(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v3; // rax@7
  char *v4; // rax@7
  char *v5; // rax@7
  char *v6; // rax@7
  __int64 v7; // [sp+0h] [bp-D8h]@1
  char Dest; // [sp+28h] [bp-B0h]@7
  char v9; // [sp+39h] [bp-9Fh]@7
  char byType; // [sp+64h] [bp-74h]@7
  char v11; // [sp+65h] [bp-73h]@7
  char pMsg; // [sp+88h] [bp-50h]@7
  char v13; // [sp+99h] [bp-3Fh]@7
  unsigned __int64 v14; // [sp+C0h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattle *v15; // [sp+E0h] [bp+8h]@1

  v15 = this;
  v1 = &v7;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v14 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( v15->m_pkWin )
  {
    if ( v15->m_pkLose )
    {
      byType = 27;
      v11 = 77;
      v3 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v15->m_pkWin);
      strcpy_0(&Dest, v3);
      v4 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v15->m_pkLose);
      strcpy_0(&v9, v4);
      GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(v15->m_pkWin, &byType, &Dest, 0x22u);
      v11 = 78;
      v5 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v15->m_pkWin);
      strcpy_0(&pMsg, v5);
      v6 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v15->m_pkLose);
      strcpy_0(&v13, v6);
      GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(v15->m_pkLose, &byType, &pMsg, 0x22u);
    }
  }
}
