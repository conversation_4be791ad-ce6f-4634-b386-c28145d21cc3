/*
 * Function: ?get_localitem@AutominePersonalMgr@@AEBAPEAU_db_con@_STORAGE_LIST@@PEAVCPlayer@@G@Z
 * Address: 0x1402E1850
 */

_STORAGE_LIST::_db_con *__fastcall AutominePersonalMgr::get_localitem(AutominePersonalMgr *this, CPlayer *pOne, unsigned __int16 wItemSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  _STORAGE_LIST::_db_con *result; // rax@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  _STORAGE_LIST::_db_con *v7; // [sp+20h] [bp-18h]@4

  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = _STORAGE_LIST::GetPtrFromSerial(pOne->m_Param.m_pStoragePtr[0], wItemSerial);
  if ( v7 )
  {
    if ( v7->m_byTableCode == 33 )
      result = v7;
    else
      result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
