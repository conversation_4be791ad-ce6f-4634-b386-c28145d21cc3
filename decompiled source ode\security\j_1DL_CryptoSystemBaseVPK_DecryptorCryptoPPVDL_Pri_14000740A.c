/*
 * Function: j_??1?$DL_CryptoSystemBase@VPK_Decryptor@CryptoPP@@V?$DL_PrivateKey@UECPPoint@CryptoPP@@@2@@CryptoPP@@UEAA@XZ
 * Address: 0x14000740A
 */

void __fastcall CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::~DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>(CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *this)
{
  CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::~DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>(this);
}
