/*
 * Function: ?pc_PostDeleteRequest@CPlayer@@QEAAXK@Z
 * Address: 0x1400C9320
 */

void __fastcall CPlayer::pc_PostDeleteRequest(CPlayer *this, unsigned int dwIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  CPostData *pPost; // [sp+20h] [bp-18h]@4
  int nNumber; // [sp+28h] [bp-10h]@10
  CPlayer *v7; // [sp+40h] [bp+8h]@1
  int nIndex; // [sp+48h] [bp+10h]@1

  nIndex = dwIndex;
  v7 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  pPost = CPostStorage::GetPostDataFromInx(&v7->m_Param.m_PostStorage, dwIndex);
  if ( pPost )
  {
    if ( CPlayer::UpdateDelPost(v7, pPost->m_dwPSSerial, nIndex) )
    {
      if ( _INVENKEY::IsFilled(&pPost->m_Key) || pPost->m_dwGold )
        CMgrAvatorItemHistory::post_delete(&CPlayer::s_MgrItemHistory, pPost, v7->m_szItemHistoryFileName);
      nNumber = pPost->m_nNumber;
      CPlayer::DelPostData(v7, nIndex);
      CPlayer::SortPost(v7, nNumber);
      CPlayer::SendMsg_PostDelete(v7, 0, nIndex);
    }
    else
    {
      CPlayer::SendMsg_PostDelete(v7, 8, nIndex);
    }
  }
  else
  {
    CPlayer::SendMsg_PostDelete(v7, 11, nIndex);
  }
}
