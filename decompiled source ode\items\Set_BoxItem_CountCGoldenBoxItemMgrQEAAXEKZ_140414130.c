/*
 * Function: ?Set_BoxItem_Count@CGoldenBoxItemMgr@@QEAAXEK@Z
 * Address: 0x140414130
 */

void __fastcall CGoldenBoxItemMgr::Set_BoxItem_Count(CGoldenBoxItemMgr *this, char byIndex, unsigned int dwIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CGoldenBoxItemMgr *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; j < v7->m_golden_box_item.m_bygolden_item_num[(unsigned __int8)byIndex]; ++j )
  {
    if ( v7->m_golden_box_item.m_golden_box_item_info[(unsigned __int8)byIndex][j].m_dwIndex == dwIndex )
    {
      if ( !v7->m_golden_box_item.m_golden_box_item_info[(unsigned __int8)byIndex][j].m_wNum )
        return;
      --v7->m_golden_box_item.m_golden_box_item_info[(unsigned __int8)byIndex][j].m_wNum;
      break;
    }
  }
  CGoldenBoxItemMgr::Set_ToStruct(v7);
}
