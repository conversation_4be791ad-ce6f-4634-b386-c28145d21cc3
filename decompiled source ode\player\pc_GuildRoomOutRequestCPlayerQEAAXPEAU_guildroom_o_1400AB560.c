/*
 * Function: ?pc_GuildRoomOutRequest@CPlayer@@QEAAXPEAU_guildroom_out_request_clzo@@@Z
 * Address: 0x1400AB560
 */

void __fastcall CPlayer::pc_GuildRoomOutRequest(CPlayer *this, _guildroom_out_request_clzo *pProtocol)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomSystem *v4; // rax@15
  CGuildRoomSystem *v5; // rax@17
  __int64 v6; // [sp+0h] [bp-58h]@1
  char v7; // [sp+30h] [bp-28h]@4
  CGuild *v8; // [sp+38h] [bp-20h]@13
  CUserDB *v9; // [sp+40h] [bp-18h]@17
  int n; // [sp+48h] [bp-10h]@17
  CPlayer *v11; // [sp+60h] [bp+8h]@1
  _guildroom_out_request_clzo *v12; // [sp+68h] [bp+10h]@1

  v12 = pProtocol;
  v11 = this;
  v2 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = 0;
  if ( CGameObject::GetCurSecNum((CGameObject *)&v11->vfptr) == -1 || v11->m_bMapLoading )
  {
    v7 = 4;
    CPlayer::SendMsg_GuildRoomOutResult(v11, 4, 0, 0, 0i64);
  }
  else if ( CPlayer::IsRidingUnit(v11) )
  {
    v7 = 5;
    CPlayer::SendMsg_GuildRoomOutResult(v11, 5, 0, 0, 0i64);
  }
  else if ( v11->m_byStandType == 1 )
  {
    v7 = 6;
    CPlayer::SendMsg_GuildRoomOutResult(v11, 6, 0, 0, 0i64);
  }
  else if ( v11->m_Param.m_pGuild )
  {
    v8 = v11->m_Param.m_pGuild;
    if ( v12->dwGuildSerial == v8->m_dwSerial )
    {
      v4 = CGuildRoomSystem::GetInstance();
      if ( CGuildRoomSystem::IsRoomRented(v4, v8->m_dwSerial) )
      {
        v9 = v11->m_pUserDB;
        n = v11->m_ObjID.m_wIndex;
        v5 = CGuildRoomSystem::GetInstance();
        if ( !CGuildRoomSystem::SetPlayerOut(v5, v8->m_dwSerial, n, v9->m_dwSerial) )
        {
          v7 = 2;
          CPlayer::SendMsg_GuildRoomOutResult(v11, 2, 0, 0, 0i64);
        }
      }
      else
      {
        v7 = 2;
        CPlayer::SendMsg_GuildRoomOutResult(v11, 2, 0, 0, 0i64);
      }
    }
    else
    {
      v7 = 1;
      CPlayer::SendMsg_GuildRoomOutResult(v11, 1, 0, 0, 0i64);
    }
  }
  else
  {
    v7 = 1;
    CPlayer::SendMsg_GuildRoomOutResult(v11, 1, 0, 0, 0i64);
  }
}
