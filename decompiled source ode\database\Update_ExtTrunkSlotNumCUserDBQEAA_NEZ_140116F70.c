/*
 * Function: ?Update_ExtTrunkSlotNum@CUserDB@@QEAA_NE@Z
 * Address: 0x140116F70
 */

char __fastcall CUserDB::Update_ExtTrunkSlotNum(CUserDB *this, char byExtSlotNum)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-18h]@1
  CUserDB *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6->m_AvatorData.dbTrunk.byExtSlotNum = byExtSlotNum;
  v6->m_bDataUpdate = 1;
  return 1;
}
