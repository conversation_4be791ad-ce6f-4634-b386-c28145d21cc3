/*
 * Function: ?Select_ReturnPost@CRFWorldDatabase@@QEAAEKKPEAU_return_post_list@@@Z
 * Address: 0x1404B3A70
 */

char __fastcall CRFWorldDatabase::Select_ReturnPost(CRFWorldDatabase *this, unsigned int dwOwner, unsigned int dwMax, _return_post_list *pRetData)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  _return_post_list::__list *v7; // rax@44
  char *v8; // rax@44
  char *v9; // rax@44
  char *v10; // rax@44
  char *v11; // rax@44
  int *v12; // rax@44
  unsigned __int64 *v13; // rax@44
  unsigned int *v14; // rax@44
  unsigned int *v15; // rax@44
  char *v16; // rax@44
  unsigned __int64 *v17; // rax@44
  __int64 v18; // [sp+0h] [bp-2A8h]@1
  void *SQLStmt; // [sp+20h] [bp-288h]@4
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-280h]@22
  SQLLEN v21; // [sp+38h] [bp-270h]@22
  __int16 v22; // [sp+44h] [bp-264h]@9
  int TargetValue; // [sp+54h] [bp-254h]@4
  char DstBuf; // [sp+80h] [bp-228h]@4
  char v25; // [sp+81h] [bp-227h]@4
  char v26; // [sp+284h] [bp-24h]@16
  unsigned __int64 v27; // [sp+290h] [bp-18h]@4
  CRFWorldDatabase *v28; // [sp+2B0h] [bp+8h]@1
  unsigned int v29; // [sp+2B8h] [bp+10h]@1
  unsigned int v30; // [sp+2C0h] [bp+18h]@1
  _return_post_list *v31; // [sp+2C8h] [bp+20h]@1

  v31 = pRetData;
  v30 = dwMax;
  v29 = dwOwner;
  v28 = this;
  v4 = &v18;
  for ( i = 168i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v27 = (unsigned __int64)&v18 ^ _security_cookie;
  TargetValue = 0;
  DstBuf = 0;
  memset(&v25, 0, 0x1FFui64);
  pRetData->dwCount = 0;
  pRetData->bContinue = 0;
  LODWORD(SQLStmt) = 100;
  sprintf_s(
    &DstBuf,
    0x200ui64,
    "select count(serial) from tbl_PostStorage where owner=%d and poststate=%d and dck=0",
    dwOwner);
  if ( v28->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v28->vfptr, &DstBuf);
  if ( !v28->m_hStmtSelect && !CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v28->vfptr) )
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v28->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    return 1;
  }
  v22 = SQLExecDirectA_0(v28->m_hStmtSelect, &DstBuf, -3);
  if ( v22 && v22 != 1 )
  {
    if ( v22 == 100 )
    {
      result = 2;
    }
    else
    {
      SQLStmt = v28->m_hStmtSelect;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v28->vfptr, v22, &DstBuf, "SQLExecDirectA", SQLStmt);
      CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v28->vfptr, v22, v28->m_hStmtSelect);
      result = 1;
    }
    return result;
  }
  v22 = SQLFetch_0(v28->m_hStmtSelect);
  if ( v22 && v22 != 1 )
  {
    v26 = 0;
    if ( v22 == 100 )
    {
      v26 = 2;
    }
    else
    {
      SQLStmt = v28->m_hStmtSelect;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v28->vfptr, v22, &DstBuf, "SQLFetch", SQLStmt);
      CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v28->vfptr, v22, v28->m_hStmtSelect);
      v26 = 1;
    }
    if ( v28->m_hStmtSelect )
      SQLCloseCursor_0(v28->m_hStmtSelect);
    result = v26;
  }
  else
  {
    StrLen_or_IndPtr = &v21;
    SQLStmt = 0i64;
    v22 = SQLGetData_0(v28->m_hStmtSelect, 1u, -18, &TargetValue, 0i64, &v21);
    if ( !v22 || v22 == 1 )
    {
      if ( v28->m_hStmtSelect )
        SQLCloseCursor_0(v28->m_hStmtSelect);
      if ( v28->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v28->vfptr, "%s Success", &DstBuf);
      if ( TargetValue )
      {
        LODWORD(StrLen_or_IndPtr) = 100;
        LODWORD(SQLStmt) = v29;
        sprintf_s(
          &DstBuf,
          0x200ui64,
          "select top %d serial,poststate,recvname,title,content,k,d,u,gold,err,uid from tbl_PostStorage where owner=%d a"
          "nd poststate=%d and dck=0",
          v30);
        if ( v28->m_bSaveDBLog )
          CRFNewDatabase::Log((CRFNewDatabase *)&v28->vfptr, &DstBuf);
        if ( !v28->m_hStmtSelect && !CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v28->vfptr) )
        {
          CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v28->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
          return 1;
        }
        v22 = SQLExecDirectA_0(v28->m_hStmtSelect, &DstBuf, -3);
        if ( v22 && v22 != 1 )
        {
          if ( v22 == 100 )
          {
            result = 2;
          }
          else
          {
            SQLStmt = v28->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v28->vfptr, v22, &DstBuf, "SQLExecDirectA", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v28->vfptr, v22, v28->m_hStmtSelect);
            result = 1;
          }
          return result;
        }
        while ( 1 )
        {
          v22 = SQLFetch_0(v28->m_hStmtSelect);
          if ( v22 )
          {
            if ( v22 != 1 )
              break;
          }
          v7 = &v31->List[v31->dwCount];
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v28->m_hStmtSelect, 1u, -18, v7, 0i64, &v21);
          v8 = &v31->List[v31->dwCount].byState;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v28->m_hStmtSelect, 2u, -6, v8, 0i64, &v21);
          v9 = v31->List[v31->dwCount].wszRecvName;
          StrLen_or_IndPtr = &v21;
          SQLStmt = (void *)17;
          v22 = SQLGetData_0(v28->m_hStmtSelect, 3u, 1, v9, 17i64, &v21);
          v10 = v31->List[v31->dwCount].wszTitle;
          StrLen_or_IndPtr = &v21;
          SQLStmt = (void *)21;
          v22 = SQLGetData_0(v28->m_hStmtSelect, 4u, 1, v10, 21i64, &v21);
          v11 = v31->List[v31->dwCount].wszContent;
          StrLen_or_IndPtr = &v21;
          SQLStmt = (void *)201;
          v22 = SQLGetData_0(v28->m_hStmtSelect, 5u, 1, v11, 201i64, &v21);
          v12 = &v31->List[v31->dwCount].nK;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v28->m_hStmtSelect, 6u, 4, v12, 0i64, &v21);
          v13 = &v31->List[v31->dwCount].dwDur;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v28->m_hStmtSelect, 7u, -25, v13, 0i64, &v21);
          v14 = &v31->List[v31->dwCount].dwUpt;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v28->m_hStmtSelect, 8u, -18, v14, 0i64, &v21);
          v15 = &v31->List[v31->dwCount].dwGold;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v28->m_hStmtSelect, 9u, -18, v15, 0i64, &v21);
          v16 = &v31->List[v31->dwCount].byErr;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v28->m_hStmtSelect, 0xAu, -6, v16, 0i64, &v21);
          v17 = &v31->List[v31->dwCount].lnUID;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v28->m_hStmtSelect, 0xBu, -25, v17, 0i64, &v21);
          ++v31->dwCount;
        }
        if ( v28->m_hStmtSelect )
          SQLCloseCursor_0(v28->m_hStmtSelect);
        if ( v28->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v28->vfptr, "%s Success", &DstBuf);
      }
      return 0;
    }
    SQLStmt = v28->m_hStmtSelect;
    CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v28->vfptr, v22, &DstBuf, "SQLGetData", SQLStmt);
    CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v28->vfptr, v22, v28->m_hStmtSelect);
    if ( v28->m_hStmtSelect )
      SQLCloseCursor_0(v28->m_hStmtSelect);
    result = 1;
  }
  return result;
}
