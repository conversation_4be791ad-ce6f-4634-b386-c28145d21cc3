/*
 * Function: ?SendCancelRegistSuccessResult@CUnmannedTraderUserInfo@@QEAAXGGK@Z
 * Address: 0x140357F30
 */

void __fastcall CUnmannedTraderUserInfo::SendCancelRegistSuccessResult(CUnmannedTraderUserInfo *this, unsigned __int16 wInx, unsigned __int16 wItemSerial, unsigned int dwRegistSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v6; // ax@4
  __int64 v7; // [sp+0h] [bp-78h]@1
  _a_trade_clear_item_result_zocl v8; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v10; // [sp+55h] [bp-23h]@4
  unsigned __int16 v11; // [sp+88h] [bp+10h]@1

  v11 = wInx;
  v4 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8.byRetCode = 0;
  v8.wItemSerial = wItemSerial;
  v8.dwRegistSerial = dwRegistSerial;
  pbyType = 30;
  v10 = 12;
  v6 = _a_trade_clear_item_result_zocl::size(&v8);
  CNetProcess::LoadSendMsg(unk_1414F2088, v11, &pbyType, &v8.byRetCode, v6);
}
