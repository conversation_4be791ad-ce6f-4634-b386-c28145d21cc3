/*
 * Function: ??E?$_Vector_const_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAAAEAV01@XZ
 * Address: 0x1402C5E80
 */

std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *__fastcall std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator++(std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this)
{
  ++this->_Myptr;
  return this;
}
