/*
 * Function: ?Insert_NpcData@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x14049EEA0
 */

bool __fastcall CRFWorldDatabase::Insert_NpcData(CRFWorldDatabase *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-158h]@1
  char Dest; // [sp+30h] [bp-128h]@4
  unsigned __int64 v7; // [sp+140h] [bp-18h]@4
  CRFWorldDatabase *v8; // [sp+160h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = (unsigned __int64)&v5 ^ _security_cookie;
  sprintf(&Dest, "{ CALL pInsert_NpcData( %d ) }", dwSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v8->vfptr, &Dest, 1);
}
