/*
 * Function: ?DividedBy@Integer@CryptoPP@@QEBA?AV12@_K@Z
 * Address: 0x1405EA980
 */

struct CryptoPP::Integer *__fastcall CryptoPP::Integer::DividedBy(CryptoPP::Integer *this, struct CryptoPP::Integer *retstr, __int64 a3)
{
  unsigned __int64 v4; // [sp+20h] [bp-48h]@1
  CryptoPP::Integer v5; // [sp+28h] [bp-40h]@1
  int v6; // [sp+50h] [bp-18h]@1
  __int64 v7; // [sp+58h] [bp-10h]@1
  const struct CryptoPP::Integer *v8; // [sp+70h] [bp+8h]@1
  CryptoPP::Integer *v9; // [sp+78h] [bp+10h]@1
  __int64 v10; // [sp+80h] [bp+18h]@1

  v10 = a3;
  v9 = retstr;
  v8 = this;
  v7 = -2i64;
  v6 = 0;
  CryptoPP::Integer::Integer(&v5);
  CryptoPP::Integer::Divide(&v4, &v5, v8, v10);
  CryptoPP::Integer::Integer(v9, &v5);
  v6 |= 1u;
  CryptoPP::Integer::~Integer(&v5);
  return v9;
}
