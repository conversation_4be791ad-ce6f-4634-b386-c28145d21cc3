/*
 * Function: ?SetUpdateDBDataDoNotCheck@CItemStoreManager@@QEAAXXZ
 * Address: 0x14034ABB0
 */

void __fastcall CItemStoreManager::SetUpdateDBDataDoNotCheck(CItemStoreManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  int k; // [sp+24h] [bp-14h]@6
  CItemStoreManager *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < v6->m_nMapItemStoreListNum; ++j )
  {
    for ( k = 0; k < v6->m_MapItemStoreList[j].m_nItemStoreNum; ++k )
    {
      if ( v6->m_MapItemStoreList[j].m_ItemStore[k].m_nLimitStorageItemNum > 0
        && !v6->m_MapItemStoreList[j].m_ItemStore[k].m_bDBDataCheck )
      {
        CItemStore::UpdateLimitItemNum(&v6->m_MapItemStoreList[j].m_ItemStore[k], 1);
      }
    }
  }
}
