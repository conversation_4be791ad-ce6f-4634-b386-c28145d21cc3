/*
 * Function: ?GetM<PERSON>ber@CNormalGuildBattleGuild@GUILD_BATTLE@@IEAAHK@Z
 * Address: 0x1403E2A60
 */

signed __int64 __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::GetMember(GUILD_BATTLE::CNormalGuildBattleGuild *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@6
  GUILD_BATTLE::CNormalGuildBattleGuild *v7; // [sp+40h] [bp+8h]@1
  unsigned int v8; // [sp+48h] [bp+10h]@1

  v8 = dwSerial;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7->m_pkGuild )
  {
    for ( j = 0; (signed int)j < 50; ++j )
    {
      if ( !GUILD_BATTLE::CNormalGuildBattleGuildMember::IsEmpty(&v7->m_kMember[j])
        && v8 == GUILD_BATTLE::CNormalGuildBattleGuildMember::GetSerial(&v7->m_kMember[j]) )
      {
        return j;
      }
    }
    result = 0xFFFFFFFFi64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
