/*
 * Function: ?DoDayChangedWork@CUnmannedTraderScheduler@@AEAAXXZ
 * Address: 0x140393F00
 */

void __fastcall CUnmannedTraderScheduler::DoDayChangedWork(CUnmannedTraderScheduler *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v3; // rax@6
  CUnmannedTraderSchedule *v4; // rax@7
  __int64 v5; // [sp+0h] [bp-68h]@1
  int j; // [sp+20h] [bp-48h]@5
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > result; // [sp+28h] [bp-40h]@8
  __int64 v8; // [sp+40h] [bp-28h]@4
  unsigned __int64 v9; // [sp+48h] [bp-20h]@6
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v10; // [sp+50h] [bp-18h]@8
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *__that; // [sp+58h] [bp-10h]@8
  CUnmannedTraderScheduler *iOldDay; // [sp+70h] [bp+8h]@1

  iOldDay = this;
  v1 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = -2i64;
  if ( IsDayChanged(&iOldDay->m_iOldDay) )
  {
    for ( j = 0; ; ++j )
    {
      v9 = j;
      v3 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::size(&iOldDay->m_veckSchdule);
      if ( v9 >= v3 )
        break;
      v4 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator[](
             &iOldDay->m_veckSchdule,
             j);
      CUnmannedTraderSchedule::Clear(v4);
    }
    v10 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::end(
            &iOldDay->m_veckSchdule,
            &result);
    __that = v10;
    std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator=(
      &iOldDay->m_iterSchedule,
      v10);
    std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(&result);
  }
}
