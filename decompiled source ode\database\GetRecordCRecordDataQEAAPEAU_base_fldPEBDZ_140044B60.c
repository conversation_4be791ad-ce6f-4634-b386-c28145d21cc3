/*
 * Function: ?GetRecord@CRecordData@@QEAAPEAU_base_fld@@PEBD@Z
 * Address: 0x140044B60
 */

_base_fld *__fastcall CRecordData::GetRecord(CRecordData *this, const char *szRecordCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int n; // [sp+20h] [bp-18h]@4
  _base_fld *v7; // [sp+28h] [bp-10h]@6
  CRecordData *v8; // [sp+40h] [bp+8h]@1
  char *Str2; // [sp+48h] [bp+10h]@1

  Str2 = (char *)szRecordCode;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( n = 0; n < v8->m_Header.m_nRecordNum; ++n )
  {
    v7 = CRecordData::GetRecord(v8, n);
    if ( !strcmp_0(v7->m_strCode, Str2) )
      return v7;
  }
  return 0i64;
}
