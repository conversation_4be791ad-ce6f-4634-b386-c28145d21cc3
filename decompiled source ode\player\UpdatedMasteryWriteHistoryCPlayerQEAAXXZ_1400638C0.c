/*
 * Function: ?UpdatedMasteryWriteHistory@CPlayer@@QEAAXXZ
 * Address: 0x1400638C0
 */

void __usercall CPlayer::UpdatedMasteryWriteHistory(CPlayer *this@<rcx>, long double a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@5
  int v5; // ecx@5
  __int64 v6; // [sp+0h] [bp-B8h]@1
  long double v7; // [sp+20h] [bp-98h]@5
  unsigned int dwExpRate; // [sp+28h] [bp-90h]@5
  int nGrade; // [sp+30h] [bp-88h]@5
  int *pnMaxPoint; // [sp+38h] [bp-80h]@5
  _MASTERY_PARAM *pData; // [sp+40h] [bp-78h]@5
  unsigned int *pdwAlter; // [sp+48h] [bp-70h]@5
  char *pszFileName; // [sp+50h] [bp-68h]@5
  char byLogType; // [sp+58h] [bp-60h]@5
  char *pszTitle; // [sp+60h] [bp-58h]@5
  unsigned int v16; // [sp+70h] [bp-48h]@4
  unsigned int v17; // [sp+74h] [bp-44h]@4
  char *v18; // [sp+78h] [bp-40h]@5
  unsigned int *v19; // [sp+80h] [bp-38h]@5
  _MASTERY_PARAM *v20; // [sp+88h] [bp-30h]@5
  int *v21; // [sp+90h] [bp-28h]@5
  int v22; // [sp+98h] [bp-20h]@5
  long double v23; // [sp+A0h] [bp-18h]@5
  CPlayer *v24; // [sp+C0h] [bp+8h]@1

  v24 = this;
  v2 = &v6;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v16 = GetLoopTime();
  v17 = v16 - v24->m_dwUMWHLastTime;
  if ( v17 > 0xDBBA0 )
  {
    v24->m_dwUMWHLastTime = v16;
    v18 = v24->m_szLvHistoryFileName;
    v19 = v24->m_Param.m_dwAlterMastery;
    v20 = &v24->m_pmMst;
    v21 = v24->m_nMaxPoint;
    v22 = v24->m_Param.m_byPvPGrade;
    CPlayerDB::GetExp(&v24->m_Param);
    v23 = a2;
    v4 = CPlayerDB::GetLevel(&v24->m_Param);
    v5 = v24->m_ObjID.m_wIndex;
    pszTitle = 0i64;
    byLogType = 0;
    pszFileName = v18;
    pdwAlter = v19;
    pData = v20;
    pnMaxPoint = v21;
    nGrade = v22;
    dwExpRate = v24->m_dwExpRate;
    v7 = v23;
    CMgrAvatorLvHistory::update_mastery(
      &CPlayer::s_MgrLvHistory,
      v5,
      v24->m_byUserDgr,
      v4,
      v23,
      dwExpRate,
      v22,
      v21,
      v20,
      v19,
      v18,
      0,
      0i64);
    CPlayerDB::InitAlterMastery(&v24->m_Param);
  }
}
