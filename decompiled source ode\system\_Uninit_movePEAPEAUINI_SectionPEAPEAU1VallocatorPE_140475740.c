/*
 * Function: ??$_Uninit_move@PEAPEAUINI_Section@@PEAPEAU1@V?$allocator@PEAUINI_Section@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAUINI_Section@@PEAPEAU1@00AEAV?$allocator@PEAUINI_Section@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140475740
 */

INI_Section **__fastcall std::_Uninit_move<INI_Section * *,INI_Section * *,std::allocator<INI_Section *>,std::_Undefined_move_tag>(INI_Section **_First, INI_Section **_Last, INI_Section **_Dest, std::allocator<INI_Section *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-28h]@1
  INI_Section **_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  return stdext::unchecked_uninitialized_copy<INI_Section * *,INI_Section * *,std::allocator<INI_Section *>>(
           _Firsta,
           _Last,
           _Dest,
           _Al);
}
