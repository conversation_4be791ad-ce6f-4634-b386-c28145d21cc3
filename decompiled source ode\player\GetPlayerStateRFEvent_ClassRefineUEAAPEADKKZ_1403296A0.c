/*
 * Function: ?GetPlayerState@RFEvent_ClassRefine@@UEAAPEADKK@Z
 * Address: 0x1403296A0
 */

_event_participant_classrefine *__fastcall RFEvent_ClassRefine::GetPlayerState(RFEvent_ClassRefine *this, unsigned int nIdx, unsigned int nAvator)
{
  _event_participant_classrefine *result; // rax@2

  if ( nIdx < 0x9E4 )
  {
    if ( this->_pkParticipant[nIdx].nAvatorSerial == nAvator )
      result = &this->_pkParticipant[nIdx];
    else
      result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
