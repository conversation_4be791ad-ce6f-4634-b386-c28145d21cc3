/*
 * Function: ?SignAndRestart@?$DL_SignerBase@UECPPoint@CryptoPP@@@CryptoPP@@UEBA_KAEAVRandomNumberGenerator@2@AEAVPK_MessageAccumulator@2@PEAE_N@Z
 * Address: 0x140564720
 */

__int64 __fastcall CryptoPP::DL_SignerBase<CryptoPP::ECPPoint>::SignAndRestart(__int64 a1, struct CryptoPP::RandomNumberGenerator *a2, __int64 a3, unsigned __int8 *a4, char a5)
{
  CryptoPP::CryptoMaterial *v5; // rax@1
  __int64 v6; // rax@1
  __int64 v7; // rax@1
  unsigned __int64 v8; // rax@1
  __int64 *v9; // rax@1
  __int64 v10; // rax@1
  const void *v11; // rax@1
  __int64 v12; // rax@1
  char *v13; // rax@1
  char v14; // ST30_1@1
  char *v15; // rax@1
  char *v16; // rax@2
  CryptoPP::Integer *v17; // rax@3
  __int64 v18; // rax@3
  __int64 v19; // rax@3
  __int64 v20; // rax@3
  unsigned __int64 v21; // rax@3
  unsigned __int64 v22; // rax@3
  __int64 v23; // rax@5
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v25; // [sp+50h] [bp-2D8h]@1
  __int64 v26; // [sp+68h] [bp-2C0h]@1
  CryptoPP::Integer v27; // [sp+70h] [bp-2B8h]@3
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *v28; // [sp+98h] [bp-290h]@1
  __int64 v29; // [sp+A0h] [bp-288h]@1
  CryptoPP::Integer v30; // [sp+A8h] [bp-280h]@3
  __int64 v31; // [sp+D0h] [bp-258h]@1
  CryptoPP::Integer v32; // [sp+D8h] [bp-250h]@3
  unsigned __int64 v33; // [sp+100h] [bp-228h]@3
  CryptoPP::Integer v34; // [sp+108h] [bp-220h]@1
  char v35; // [sp+130h] [bp-1F8h]@1
  CryptoPP::Integer b; // [sp+140h] [bp-1E8h]@3
  CryptoPP::Integer result; // [sp+168h] [bp-1C0h]@3
  CryptoPP::Integer v38; // [sp+190h] [bp-198h]@3
  CryptoPP::ECPPoint v39; // [sp+1B8h] [bp-170h]@3
  CryptoPP::Integer v40; // [sp+210h] [bp-118h]@3
  __int64 v41; // [sp+238h] [bp-F0h]@5
  char v42; // [sp+240h] [bp-E8h]@1
  __int64 v43; // [sp+250h] [bp-D8h]@1
  __int64 v44; // [sp+258h] [bp-D0h]@1
  __int64 v45; // [sp+260h] [bp-C8h]@1
  __int64 *v46; // [sp+268h] [bp-C0h]@1
  __int64 v47; // [sp+270h] [bp-B8h]@1
  char *v48; // [sp+278h] [bp-B0h]@1
  __int64 v49; // [sp+280h] [bp-A8h]@1
  __int64 v50; // [sp+288h] [bp-A0h]@1
  unsigned __int64 v51; // [sp+290h] [bp-98h]@1
  __int64 v52; // [sp+298h] [bp-90h]@1
  unsigned __int64 v53; // [sp+2A0h] [bp-88h]@1
  unsigned __int64 v54; // [sp+2A8h] [bp-80h]@2
  CryptoPP::ClonableVtbl *v55; // [sp+2B0h] [bp-78h]@2
  struct CryptoPP::Integer *v56; // [sp+2B8h] [bp-70h]@3
  struct CryptoPP::Integer *v57; // [sp+2C0h] [bp-68h]@3
  CryptoPP::GeneratableCryptoMaterialVtbl *v58; // [sp+2C8h] [bp-60h]@3
  struct CryptoPP::Integer *v59; // [sp+2D0h] [bp-58h]@3
  struct CryptoPP::Integer *v60; // [sp+2D8h] [bp-50h]@3
  __int64 v61; // [sp+2E0h] [bp-48h]@3
  __int64 v62; // [sp+2E8h] [bp-40h]@3
  __int64 v63; // [sp+2F0h] [bp-38h]@3
  __int64 v64; // [sp+2F8h] [bp-30h]@3
  void (__fastcall **v65)(_QWORD, _QWORD, _QWORD, _QWORD); // [sp+300h] [bp-28h]@3
  __int64 v66; // [sp+330h] [bp+8h]@1
  struct CryptoPP::RandomNumberGenerator *v67; // [sp+338h] [bp+10h]@1
  __int64 v68; // [sp+340h] [bp+18h]@1
  unsigned __int8 *v69; // [sp+348h] [bp+20h]@1

  v69 = a4;
  v68 = a3;
  v67 = a2;
  v66 = a1;
  v43 = -2i64;
  v44 = *(_QWORD *)(a1 + 8);
  LODWORD(v5) = (*(int (__fastcall **)(signed __int64))(v44 + 32))(a1 + 8);
  CryptoPP::CryptoMaterial::DoQuickSanityCheck(v5);
  v26 = v68;
  LODWORD(v6) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v66 + 120i64))(v66);
  v31 = v6;
  v28 = CryptoPP::DL_Base<CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::GetAbstractGroupParameters((CryptoPP::DL_Base<CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *)(v66 + 16));
  v45 = *(_QWORD *)(v66 + 16);
  LODWORD(v7) = (*(int (__fastcall **)(signed __int64))(v45 + 8))(v66 + 16);
  v29 = v7;
  LODWORD(v8) = CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Signer,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::MessageRepresentativeLength(v66);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v25,
    v8);
  LODWORD(v9) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v66 + 128i64))(v66);
  v46 = v9;
  LODWORD(v10) = CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Signer,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::MessageRepresentativeBitLength(v66);
  v47 = v10;
  v48 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v25);
  v49 = *(_QWORD *)v66;
  LODWORD(v11) = (*(int (__fastcall **)(__int64, char *))(v49 + 136))(v66, &v35);
  qmemcpy(&v42, v11, 0x10ui64);
  LODWORD(v12) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v26 + 144i64))(v26);
  v50 = v12;
  v51 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v26 + 8));
  v13 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v26 + 8));
  v52 = *v46;
  v14 = *(_BYTE *)(v26 + 184);
  (*(void (__fastcall **)(__int64 *, struct CryptoPP::RandomNumberGenerator *, char *, unsigned __int64))(v52 + 48))(
    v46,
    v67,
    v13,
    v51);
  *(_BYTE *)(v26 + 184) = 1;
  v53 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v25);
  v15 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v25);
  CryptoPP::Integer::Integer(&v34, (const unsigned __int8 *)v15, v53, 0);
  if ( (unsigned __int8)((int (__fastcall *)(struct CryptoPP::RandomNumberGenerator *))v67->vfptr[2].__vecDelDtor)(v67) )
  {
    v54 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v25);
    v16 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v25);
    v55 = v67->vfptr;
    ((void (__fastcall *)(struct CryptoPP::RandomNumberGenerator *, char *, unsigned __int64))v55[1].Clone)(
      v67,
      v16,
      v54);
  }
  CryptoPP::Integer::Integer(&b, 1);
  CryptoPP::Integer::Integer(&v38, 1);
  v56 = (struct CryptoPP::Integer *)CryptoPP::Integer::One();
  v57 = (struct CryptoPP::Integer *)CryptoPP::Integer::Zero();
  v58 = v28->vfptr;
  LODWORD(v17) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *))v58[8].__vecDelDtor)(v28);
  v59 = CryptoPP::operator-(&result, v17, &b);
  v60 = v59;
  CryptoPP::Integer::Integer((enum CryptoPP::Integer::RandomNumberType)&v30, v67, &v38, v59, 0, v57, v56);
  CryptoPP::Integer::~Integer(&result);
  CryptoPP::Integer::~Integer(&v38);
  CryptoPP::Integer::~Integer(&b);
  CryptoPP::Integer::Integer(&v32);
  CryptoPP::Integer::Integer(&v27);
  LODWORD(v18) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *, CryptoPP::ECPPoint *, CryptoPP::Integer *))v28->vfptr[3].__vecDelDtor)(
                   v28,
                   &v39,
                   &v30);
  v61 = v18;
  v62 = v18;
  LODWORD(v19) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *, CryptoPP::Integer *, __int64))v28->vfptr[15].__vecDelDtor)(
                   v28,
                   &v40,
                   v18);
  v63 = v19;
  v64 = v19;
  CryptoPP::Integer::operator=(&v32);
  CryptoPP::Integer::~Integer(&v40);
  CryptoPP::ECPPoint::~ECPPoint(&v39);
  LODWORD(v20) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v29 + 16i64))(v29);
  v65 = *(void (__fastcall ***)(_QWORD, _QWORD, _QWORD, _QWORD))v31;
  (*v65)(v31, v28, v20, &v30);
  LODWORD(v21) = (*(int (__fastcall **)(__int64, CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *))(*(_QWORD *)v31 + 24i64))(
                   v31,
                   v28);
  v33 = v21;
  CryptoPP::Integer::Encode(&v32, v69, v21, 0);
  LODWORD(v22) = (*(int (__fastcall **)(__int64, CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *))(*(_QWORD *)v31 + 32i64))(
                   v31,
                   v28);
  CryptoPP::Integer::Encode(&v27, &v69[v33], v22, 0);
  if ( a5 )
    CryptoPP::DL_SignerBase<CryptoPP::ECPPoint>::RestartMessageAccumulator(v66, v67, v26);
  LODWORD(v23) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v66 + 8i64))(v66);
  v41 = v23;
  CryptoPP::Integer::~Integer(&v27);
  CryptoPP::Integer::~Integer(&v32);
  CryptoPP::Integer::~Integer(&v30);
  CryptoPP::Integer::~Integer(&v34);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v25);
  return v41;
}
