/*
 * Function: ?StaticAlgorithmName@?$CipherModeFinalTemplate_CipherHolder@V?$BlockCipherFinal@$0A@VBase@DES@CryptoPP@@@CryptoPP@@V?$ConcretePolicyHolder@VEmpty@CryptoPP@@V?$CFB_DecryptionTemplate@V?$AbstractPolicyHolder@VCFB_CipherAbstractPolicy@CryptoPP@@VCFB_ModePolicy@2@@CryptoPP@@@2@VCFB_CipherAbstractPolicy@2@@2@@CryptoPP@@SA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
 * Address: 0x14061C3D0
 */

__int64 __fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>,CryptoPP::ConcretePolicyHolder<CryptoPP::Empty,CryptoPP::CFB_DecryptionTemplate<CryptoPP::AbstractPolicyHolder<CryptoPP::CFB_CipherAbstractPolicy,CryptoPP::CFB_ModePolicy>>,CryptoPP::CFB_CipherAbstractPolicy>>::StaticAlgorithmName(__int64 a1)
{
  __int64 v1; // rax@1
  __int64 v2; // rax@1
  char v4; // [sp+20h] [bp-A8h]@1
  char v5; // [sp+50h] [bp-78h]@1
  int v6; // [sp+80h] [bp-48h]@1
  __int64 v7; // [sp+88h] [bp-40h]@1
  const char *v8; // [sp+90h] [bp-38h]@1
  __int64 v9; // [sp+98h] [bp-30h]@1
  __int64 v10; // [sp+A0h] [bp-28h]@1
  __int64 v11; // [sp+A8h] [bp-20h]@1
  __int64 v12; // [sp+B0h] [bp-18h]@1
  __int64 v13; // [sp+D0h] [bp+8h]@1

  v13 = a1;
  v7 = -2i64;
  v6 = 0;
  v8 = CryptoPP::CFB_ModePolicy::StaticAlgorithmName();
  LODWORD(v1) = CryptoPP::AlgorithmImpl<CryptoPP::SimpleKeyingInterfaceImpl<CryptoPP::TwoBases<CryptoPP::SimpleKeyedTransformation<CryptoPP::BlockTransformation>,CryptoPP::DES_Info>,CryptoPP::TwoBases<CryptoPP::SimpleKeyedTransformation<CryptoPP::BlockTransformation>,CryptoPP::DES_Info>>,CryptoPP::SimpleKeyingInterfaceImpl<CryptoPP::TwoBases<CryptoPP::SimpleKeyedTransformation<CryptoPP::BlockTransformation>,CryptoPP::DES_Info>,CryptoPP::TwoBases<CryptoPP::SimpleKeyedTransformation<CryptoPP::BlockTransformation>,CryptoPP::DES_Info>>>::StaticAlgorithmName(&v4);
  v9 = v1;
  v10 = v1;
  LODWORD(v2) = std::operator+<char,std::char_traits<char>,std::allocator<char>>(&v5, v1, "/");
  v11 = v2;
  v12 = v2;
  std::operator+<char,std::char_traits<char>,std::allocator<char>>(v13, v2, v8);
  v6 |= 1u;
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(&v5);
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(&v4);
  return v13;
}
