/*
 * Function: j_?NotifyCloseItem@CUnmannedTraderUserInfo@@AEAAXPEAU_qry_case_unmandtrader_log_in_proc_update_complete@@PEAVCLogFile@@@Z
 * Address: 0x140006212
 */

void __fastcall CUnmannedTraderUserInfo::NotifyCloseItem(CUnmannedTraderUserInfo *this, _qry_case_unmandtrader_log_in_proc_update_complete *pkResult, CLogFile *pkLogger)
{
  CUnmannedTraderUserInfo::NotifyCloseItem(this, pkResult, pkLogger);
}
