/*
 * Function: ??0CMapOperation@@QEAA@XZ
 * Address: 0x140195E20
 */

void __fastcall CMapOperation::CMapOperation(CMapOperation *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CMapOperation *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  v5->vfptr = (CMapOperationVtbl *)&CMapOperation::`vftable';
  CMapDataTable::CMapDataTable(&v5->m_tblMapData);
  std::vector<std::pair<int,int>,std::allocator<std::pair<int,int>>>::vector<std::pair<int,int>,std::allocator<std::pair<int,int>>>(&v5->m_vecStandardMapCodeTable);
  CMyTimer::CMyTimer(&v5->m_tmrObjTerm);
  CMyTimer::CMyTimer(&v5->m_tmrSystem);
  CMyTimer::CMyTimer(&v5->m_tmrRecover);
  InitR3Engine(1);
  v5->m_bReSpawnMonster = 1;
  v5->m_nMapNum = 0;
  v5->m_nStdMapNum = 0;
  v5->m_Map = 0i64;
  v5->m_nLoopStartPoint = 0;
  CMyTimer::BeginTimer(&v5->m_tmrObjTerm, 0x32u);
  CMyTimer::BeginTimer(&v5->m_tmrRecover, 0x7D0u);
  CMyTimer::BeginTimer(&v5->m_tmrSystem, 0x3E8u);
  v5->m_nRegionNum = 0;
  v5->m_SettlementMapData[0][0] = 0i64;
  v5->m_SettlementMapData[0][1] = 0i64;
  v5->m_SettlementMapData[1][0] = 0i64;
  v5->m_SettlementMapData[1][1] = 0i64;
  v5->m_SettlementMapData[2][0] = 0i64;
  v5->m_SettlementMapData[2][1] = 0i64;
}
