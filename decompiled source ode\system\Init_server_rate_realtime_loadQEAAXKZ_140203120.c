/*
 * Function: ?Init@_server_rate_realtime_load@@QEAAXK@Z
 * Address: 0x140203120
 */

void __fastcall _server_rate_realtime_load::Init(_server_rate_realtime_load *this, unsigned int dwReadTerm)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  _server_rate_realtime_load *Dst; // [sp+30h] [bp+8h]@1
  unsigned int dwTerm; // [sp+38h] [bp+10h]@1

  dwTerm = dwReadTerm;
  Dst = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  memset_0(Dst, 0, 0x68ui64);
  CMyTimer::BeginTimer(&Dst->m_tmDataFileCheckTime, dwTerm);
}
