/*
 * Function: ?SendMsg_RealMovePoint@CAnimus@@UEAAXH@Z
 * Address: 0x14012A330
 */

void __fastcall CAnimus::SendMsg_RealMovePoint(CAnimus *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-A8h]@1
  char szMsg[2]; // [sp+38h] [bp-70h]@4
  unsigned __int16 v6; // [sp+3Ah] [bp-6Eh]@4
  unsigned int v7; // [sp+3Ch] [bp-6Ch]@4
  __int16 pShort; // [sp+40h] [bp-68h]@4
  __int16 v9; // [sp+46h] [bp-62h]@4
  __int16 v10; // [sp+48h] [bp-60h]@4
  char v11; // [sp+4Ah] [bp-5Eh]@5
  unsigned __int16 v12; // [sp+4Bh] [bp-5Dh]@7
  unsigned int v13; // [sp+4Dh] [bp-5Bh]@7
  float v14; // [sp+51h] [bp-57h]@7
  char pbyType; // [sp+74h] [bp-34h]@7
  char v16; // [sp+75h] [bp-33h]@7
  unsigned __int64 v17; // [sp+90h] [bp-18h]@4
  CAnimus *v18; // [sp+B0h] [bp+8h]@1
  int dwClientIndex; // [sp+B8h] [bp+10h]@1

  dwClientIndex = n;
  v18 = this;
  v2 = &v4;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v17 = (unsigned __int64)&v4 ^ _security_cookie;
  *(_WORD *)szMsg = v18->m_pRecordSet->m_dwIndex;
  v6 = v18->m_ObjID.m_wIndex;
  v7 = v18->m_dwObjSerial;
  FloatToShort(v18->m_fCurPos, &pShort, 3);
  v9 = (signed int)ffloor(v18->m_fTarPos[0]);
  v10 = (signed int)ffloor(v18->m_fTarPos[2]);
  if ( v18->m_pRecord )
    v11 = v18->m_pRecord->m_nLevel;
  else
    v11 = 1;
  v12 = v18->m_wLastContEffect;
  v13 = v18->m_pMaster->m_dwObjSerial;
  v14 = v18->m_fMoveSpeed;
  pbyType = 4;
  v16 = 24;
  CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, szMsg, 0x1Du);
}
