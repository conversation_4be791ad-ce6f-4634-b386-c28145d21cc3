/*
 * Function: ?GetVersion@CUnmannedTraderGroupDivisionVersionInfo@@QEAA_NEAEAK@Z
 * Address: 0x140360990
 */

char __fastcall CUnmannedTraderGroupDivisionVersionInfo::GetVersion(CUnmannedTraderGroupDivisionVersionInfo *this, char byClass, unsigned int *dwVer)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v6; // [sp+0h] [bp-28h]@1
  CUnmannedTraderGroupDivisionVersionInfo *v7; // [sp+30h] [bp+8h]@1
  char v8; // [sp+38h] [bp+10h]@1
  unsigned int *v9; // [sp+40h] [bp+18h]@1

  v9 = dwVer;
  v8 = byClass;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( std::vector<unsigned long,std::allocator<unsigned long>>::size(&v7->m_vecuiVersion) <= (unsigned __int8)byClass
    || std::vector<unsigned long,std::allocator<unsigned long>>::empty(&v7->m_vecuiVersion) )
  {
    result = 0;
  }
  else
  {
    *v9 = *std::vector<unsigned long,std::allocator<unsigned long>>::operator[](
             &v7->m_vecuiVersion,
             (unsigned __int8)v8);
    result = 1;
  }
  return result;
}
