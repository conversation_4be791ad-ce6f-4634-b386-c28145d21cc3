/*
 * Function: ?SetRoomMapInfo@CGuildRoomInfo@@QEAAXPEAVCMapData@@GEE@Z
 * Address: 0x1402E5A80
 */

void __fastcall CGuildRoomInfo::SetRoomMapInfo(CGuildRoomInfo *this, CMapData *pMap, unsigned __int16 wMapLayer, char byRoomType, char byRace)
{
  this->m_pRoomMap = pMap;
  this->m_wRoomMapLayer = wMapLayer;
  this->m_byRoomType = byRoomType;
  this->m_byRace = byRace;
  this->m_pRoomStartDummy = pMap->m_pPortal->m_pDumPos;
  this->m_pLayerSet = &pMap->m_ls[this->m_wRoomMapLayer];
}
