/*
 * Function: ?pc_UseFireCracker@CPlayer@@QEAAHG@Z
 * Address: 0x1400B45E0
 */

signed __int64 __fastcall CPlayer::pc_UseFireCracker(CPlayer *this, unsigned __int16 wItemSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  __int64 v5; // [sp+0h] [bp-48h]@1
  bool bUpdate; // [sp+20h] [bp-28h]@27
  bool bSend; // [sp+28h] [bp-20h]@27
  _STORAGE_LIST::_db_con *pItem; // [sp+30h] [bp-18h]@19
  _base_fld *v9; // [sp+38h] [bp-10h]@23
  CPlayer *v10; // [sp+50h] [bp+8h]@1
  unsigned __int16 v11; // [sp+58h] [bp+10h]@1

  v11 = wItemSerial;
  v10 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CGameObject::GetCurSecNum((CGameObject *)&v10->vfptr) == -1 || v10->m_bMapLoading )
  {
    result = 0xFFFFFFFFi64;
  }
  else if ( v10->m_bCorpse )
  {
    result = 4294967294i64;
  }
  else if ( v10->m_byStandType == 1 )
  {
    result = 4294967293i64;
  }
  else if ( CPlayer::IsSiegeMode(v10) )
  {
    result = 4294967292i64;
  }
  else if ( CPlayer::IsRidingUnit(v10) )
  {
    result = 4294967291i64;
  }
  else if ( _effect_parameter::GetEff_State(&v10->m_EP, 20) )
  {
    result = 4294967290i64;
  }
  else if ( _effect_parameter::GetEff_State(&v10->m_EP, 28) )
  {
    result = 4294967290i64;
  }
  else
  {
    pItem = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v10->m_Param.m_dbInven.m_nListNum, v11);
    if ( pItem )
    {
      if ( pItem->m_byTableCode == 32 )
      {
        v9 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 32, pItem->m_wItemIndex);
        if ( v9 )
        {
          if ( pItem->m_bLock )
          {
            result = 4294967288i64;
          }
          else
          {
            bSend = 0;
            bUpdate = 0;
            CPlayer::Emb_AlterDurPoint(v10, 0, pItem->m_byStorageIndex, -1, 0, 0);
            CMgrAvatorItemHistory::consume_del_item(
              &CPlayer::s_MgrItemHistory,
              v10->m_ObjID.m_wIndex,
              pItem,
              v10->m_szItemHistoryFileName);
            result = pItem->m_wItemIndex;
          }
        }
        else
        {
          result = 4294967289i64;
        }
      }
      else
      {
        result = 4294967289i64;
      }
    }
    else
    {
      CPlayer::SendMsg_AdjustAmountInform(v10, 0, v11, 0);
      result = 4294967289i64;
    }
  }
  return result;
}
