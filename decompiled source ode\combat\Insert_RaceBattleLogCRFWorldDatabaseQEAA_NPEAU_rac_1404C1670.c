/*
 * Function: ?Insert_RaceBattleLog@CRFWorldDatabase@@QEAA_NPEAU_race_battle_log_info@@@Z
 * Address: 0x1404C1670
 */

bool __fastcall CRFWorldDatabase::Insert_RaceBattleLog(CRFWorldDatabase *this, _race_battle_log_info *pInfo)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@4
  int v5; // ecx@4
  int v6; // edx@4
  __int64 v8; // [sp+0h] [bp-1C8h]@1
  int v9; // [sp+20h] [bp-1A8h]@4
  unsigned int v10; // [sp+28h] [bp-1A0h]@4
  int v11; // [sp+30h] [bp-198h]@4
  int v12; // [sp+38h] [bp-190h]@4
  unsigned int v13; // [sp+40h] [bp-188h]@4
  unsigned int v14; // [sp+48h] [bp-180h]@4
  unsigned int v15; // [sp+50h] [bp-178h]@4
  char szToday; // [sp+68h] [bp-160h]@4
  char v17; // [sp+69h] [bp-15Fh]@4
  char Dest; // [sp+A0h] [bp-128h]@4
  char v19; // [sp+A1h] [bp-127h]@4
  unsigned __int64 v20; // [sp+1B0h] [bp-18h]@4
  CRFWorldDatabase *v21; // [sp+1D0h] [bp+8h]@1
  _race_battle_log_info *v22; // [sp+1D8h] [bp+10h]@1

  v22 = pInfo;
  v21 = this;
  v2 = &v8;
  for ( i = 112i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v20 = (unsigned __int64)&v8 ^ _security_cookie;
  szToday = 0;
  memset(&v17, 0, 9ui64);
  Dest = 0;
  memset(&v19, 0, 0xFFui64);
  GetTodayStr(&szToday);
  v4 = v22->byLoseRace;
  v5 = v22->byWinRace;
  v6 = v22->byNth;
  v15 = v22->dwBossSerilal2;
  v14 = v22->dwBossSerilal1;
  v13 = v22->dwBossSerilal0;
  v12 = v4;
  v11 = v5;
  v10 = v22->dwEndTime;
  v9 = v6;
  sprintf(
    &Dest,
    "{ CALL pInsert_RaceBattleLog_070427( '%s', '%s', %d, %d, %d, %d, %d, %d, %d ) }",
    &unk_1799C5B99,
    &szToday);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v21->vfptr, &Dest, 1);
}
