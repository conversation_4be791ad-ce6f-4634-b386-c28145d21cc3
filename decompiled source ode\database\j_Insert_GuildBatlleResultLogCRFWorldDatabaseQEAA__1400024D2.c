/*
 * Function: j_?Insert_GuildBatlleResultLog@CRFWorldDatabase@@QEAA_NPEAD0K0K0KKKKKKKKEK0K0EK0@Z
 * Address: 0x1400024D2
 */

bool __fastcall CRFWorldDatabase::Insert_GuildBatlleResultLog(CRFWorldDatabase *this, char *szStartTime, char *szEndTime, unsigned int dwRedSerial, char *wszRedName, unsigned int dwBlueSerial, char *wszBlueName, unsigned int dwRedScore, unsigned int dwBlueScore, unsigned int dwRedMaxJoinCnt, unsigned int dwBlueMaxJoinCnt, unsigned int dwRedGoalCntSum, unsigned int dwBlueGoalCntSum, unsigned int dwRedKillCntSum, unsigned int dwBlueKillCntSum, char by<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, unsigned int dwMaxGoalCharacSerial, char *wszMaxGoalCharacName, unsigned int dwMaxKillCharacSerial, char *wszMaxKillCharacName, char byJoinLimit, unsigned int dwGuildBattleCostGold, char *szBattleMapCode)
{
  return CRFWorldDatabase::Insert_GuildBatlleResultLog(
           this,
           szStartTime,
           szEndTime,
           dwRedSerial,
           wszRedName,
           dwBlueSerial,
           wszBlueName,
           dwRedScore,
           dwBlueScore,
           dwRedMaxJoinCnt,
           dwBlueMaxJoinCnt,
           dwRedGoalCntSum,
           dwBlueGoalCntSum,
           dwRedKillCntSum,
           dwBlueKillCntSum,
           byBattleResult,
           dwMaxGoalCharacSerial,
           wszMaxGoalCharacName,
           dwMaxKillCharacSerial,
           wszMaxKillCharacName,
           byJoinLimit,
           dwGuildBattleCostGold,
           szBattleMapCode);
}
