/*
 * Function: ?GenerateRandom@GeneratableCryptoMaterial@CryptoPP@@UEAAXAEAVRandomNumberGenerator@2@AEBVNameValuePairs@2@@Z
 * Address: 0x14044BB80
 */

void __fastcall __noreturn CryptoPP::GeneratableCryptoMaterial::GenerateRandom(CryptoPP::GeneratableCryptoMaterial *this, CryptoPP::RandomNumberGenerator *rng, CryptoPP::NameValuePairs *params)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-B8h]@1
  CryptoPP::NotImplemented v6; // [sp+20h] [bp-98h]@4
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+70h] [bp-48h]@4
  unsigned __int8 v8; // [sp+A0h] [bp-18h]@4
  __int64 v9; // [sp+A8h] [bp-10h]@4

  v3 = &v5;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = -2i64;
  memset(&v8, 0, sizeof(v8));
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
    &s,
    "GeneratableCryptoMaterial: this object does not support key/parameter generation",
    v8);
  CryptoPP::NotImplemented::NotImplemented(&v6, &s);
  CxxThrowException_0(&v6, &TI3_AVNotImplemented_CryptoPP__);
}
