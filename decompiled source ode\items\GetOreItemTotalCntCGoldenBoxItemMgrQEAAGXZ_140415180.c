/*
 * Function: ?GetOreItemTotalCnt@CGoldenBoxItemMgr@@QEAAGXZ
 * Address: 0x140415180
 */

__int16 __fastcall CGoldenBoxItemMgr::GetOreItemTotalCnt(CGoldenBoxItemMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  __int16 v6; // [sp+20h] [bp-18h]@4
  int n; // [sp+24h] [bp-14h]@4
  _base_fld *v8; // [sp+28h] [bp-10h]@6

  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = 0;
  for ( n = 0; ; ++n )
  {
    v3 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 17);
    if ( n >= v3 )
      break;
    v8 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 17, n);
    if ( !*(_DWORD *)&v8[3].m_strCode[4] )
      ++v6;
  }
  return v6;
}
