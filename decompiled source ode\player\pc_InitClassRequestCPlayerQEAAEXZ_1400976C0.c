/*
 * Function: ?pc_InitClassRequest@CPlayer@@QEAAEXZ
 * Address: 0x1400976C0
 */

char __fastcall CPlayer::pc_InitClassRequest(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@4
  char result; // al@5
  char v5; // al@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int dwSub; // [sp+20h] [bp-18h]@4
  int v8; // [sp+24h] [bp-14h]@6
  CPlayer *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v1 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  dwSub = CPlayer::GetInitClassCost(v9);
  v3 = CPlayerDB::GetGold(&v9->m_Param);
  if ( dwSub <= v3 )
  {
    v5 = CPlayer::pc_InitClass(v9);
    v8 = (unsigned __int8)v5;
    if ( !v5 )
      CPlayer::SubGold(v9, dwSub);
    ++v9->m_pUserDB->m_AvatorData.dbAvator.m_dwClassInitCnt;
    result = v8;
  }
  else
  {
    result = 3;
  }
  return result;
}
