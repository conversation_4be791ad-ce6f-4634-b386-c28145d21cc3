/*
 * Function: j_??$unchecked_uninitialized_copy@V?$_Vector_const_iterator@URoomCharInfo@@V?$allocator@URoomCharInfo@@@std@@@std@@PEAURoomCharInfo@@V?$allocator@URoomCharInfo@@@2@@stdext@@YAPEAURoomCharInfo@@V?$_Vector_const_iterator@URoomCharInfo@@V?$allocator@URoomCharInfo@@@std@@@std@@0PEAU1@AEAV?$allocator@URoomCharInfo@@@3@@Z
 * Address: 0x140001D43
 */

RoomCharInfo *__fastcall stdext::unchecked_uninitialized_copy<std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo>>,RoomCharInfo *,std::allocator<RoomCharInfo>>(std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo> > *_First, std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo> > *_Last, RoomCharInfo *_Dest, std::allocator<RoomCharInfo> *_Al)
{
  return stdext::unchecked_uninitialized_copy<std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo>>,RoomCharInfo *,std::allocator<RoomCharInfo>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
