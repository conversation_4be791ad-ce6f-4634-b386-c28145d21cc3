/*
 * Function: ?SendMsg_Change_MonsterTarget@CMonster@@QEAAXPEAVCCharacter@@@Z
 * Address: 0x140148820
 */

void __fastcall CMonster::SendMsg_Change_MonsterTarget(CMonster *this, CCharacter *pChar)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+38h] [bp-40h]@4
  unsigned int v6; // [sp+3Ch] [bp-3Ch]@4
  char pbyType; // [sp+54h] [bp-24h]@7
  char v8; // [sp+55h] [bp-23h]@7
  CMonster *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  *(_DWORD *)szMsg = v9->m_dwObjSerial;
  v6 = -1;
  if ( pChar && !pChar->m_ObjID.m_byID )
    v6 = pChar->m_dwObjSerial;
  pbyType = 11;
  v8 = -103;
  CGameObject::CircleReport((CGameObject *)&v9->vfptr, &pbyType, szMsg, 8, 0);
}
