/*
 * Function: ?CompleteClear@CUnmannedTraderSchedule@@QEAAXEE@Z
 * Address: 0x1403978C0
 */

void __fastcall CUnmannedTraderSchedule::CompleteClear(CUnmannedTraderSchedule *this, char byDBQueryRet, char byProcRet)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  CUnmannedTraderSchedule::STATE v6; // [sp+20h] [bp-18h]@6
  CUnmannedTraderSchedule *v7; // [sp+40h] [bp+8h]@1
  char v8; // [sp+48h] [bp+10h]@1
  char v9; // [sp+50h] [bp+18h]@1

  v9 = byProcRet;
  v8 = byDBQueryRet;
  v7 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CUnmannedTraderSchedule::Clear(v7);
  if ( v8 || v9 )
    v6 = 4;
  else
    v6 = 3;
  v7->m_eState = v6;
}
