/*
 * Function: ?GetLevelContSFTime@MonsterSetInfoData@@QEAAEEE@Z
 * Address: 0x14015D4A0
 */

char __fastcall MonsterSetInfoData::GetLevelContSFTime(MonsterSetInfoData *this, char byEffectCode, char byLevel)
{
  char result; // al@4

  if ( (signed int)(unsigned __int8)byEffectCode > 3
    || (signed int)(unsigned __int8)byLevel <= 0
    || (signed int)(unsigned __int8)byLevel > 7 )
  {
    result = 1;
  }
  else
  {
    result = this->m_byLevel_ContSFTime[(unsigned __int8)byEffectCode][(unsigned __int8)byLevel - 1];
  }
  return result;
}
