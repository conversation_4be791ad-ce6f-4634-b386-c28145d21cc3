/*
 * Function: ?GetSignatureAlgorithm@?$DL_ObjectImpl@V?$DL_VerifierBase@VInteger@CryptoPP@@@CryptoPP@@U?$DL_SignatureSchemeOptions@V?$DL_SS@UDL_SignatureKeys_GFP@CryptoPP@@V?$DL_Algorithm_GDSA@VInteger@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@H@CryptoPP@@UDL_SignatureKeys_GFP@2@V?$DL_Algorithm_GDSA@VInteger@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@@2@V?$DL_PublicKey_GFP@VDL_GroupParameters_GFP@CryptoPP@@@2@@CryptoPP@@MEBAAEBV?$DL_ElgamalLikeSignatureAlgorithm@VInteger@CryptoPP@@@2@XZ
 * Address: 0x1406343D0
 */

__int64 CryptoPP::DL_ObjectImpl<CryptoPP::DL_VerifierBase<CryptoPP::Integer>,CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DL_SS<CryptoPP::DL_SignatureKeys_GFP,CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1,int>,CryptoPP::DL_SignatureKeys_GFP,CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>,CryptoPP::DL_PublicKey_GFP<CryptoPP::DL_GroupParameters_GFP>>::GetSignatureAlgorithm()
{
  __int64 v0; // rax@1
  char v2; // [sp+20h] [bp-18h]@1
  char v3; // [sp+21h] [bp-17h]@1

  memset(&v3, 0, sizeof(v3));
  v0 = CryptoPP::Singleton<CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::NewObject<CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>>,0>::Singleton<CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::NewObject<CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>>,0>((__int64)&v2);
  return CryptoPP::Singleton<CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::NewObject<CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>>,0>::Ref(v0);
}
