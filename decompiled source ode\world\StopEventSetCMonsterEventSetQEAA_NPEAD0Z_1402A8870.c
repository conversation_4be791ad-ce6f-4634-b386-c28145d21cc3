/*
 * Function: ?StopEventSet@CMonsterEventSet@@QEAA_NPEAD0@Z
 * Address: 0x1402A8870
 */

char __fastcall CMonsterEventSet::StopEventSet(CMonsterEventSet *this, char *pszEventCode, char *pwszErrCode)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@10
  __int64 v6; // [sp+0h] [bp-58h]@1
  int v7; // [sp+20h] [bp-38h]@4
  int j; // [sp+24h] [bp-34h]@4
  _event_set *v9; // [sp+28h] [bp-30h]@7
  int k; // [sp+30h] [bp-28h]@11
  _event_set::_monster_set *v11; // [sp+38h] [bp-20h]@14
  int l; // [sp+40h] [bp-18h]@15
  _event_set::_monster_set::_state::_mon *v13; // [sp+48h] [bp-10h]@18
  CMonsterEventSet *v14; // [sp+60h] [bp+8h]@1
  const char *Str2; // [sp+68h] [bp+10h]@1
  char *Dest; // [sp+70h] [bp+18h]@1

  Dest = pwszErrCode;
  Str2 = pszEventCode;
  v14 = this;
  v3 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = 0;
  for ( j = 0; j < 10; ++j )
  {
    if ( !strcmp_0(v14->m_EventSet[j].m_strId, Str2) )
    {
      v9 = &v14->m_EventSet[j];
      if ( !v14->m_EventSet[j].m_bOper )
      {
        if ( Dest )
          sprintf(Dest, "now stoped");
        return 0;
      }
      ++v7;
      for ( k = 0; k < 10; ++k )
      {
        v11 = &v9->m_MonSet[k];
        if ( v9->m_MonSet[k].bIsSet )
        {
          for ( l = 0; l < v11->m_State.nRespawnNum; ++l )
          {
            v13 = &v11->m_State.MonInfo[l];
            if ( v13->pMon && v13->pMon->m_bLive && v13->pMon->m_dwObjSerial == v13->dwSerial )
              CMonster::Destroy(v13->pMon, 1, 0i64);
          }
          _event_set::_monster_set::_state::init(&v11->m_State);
        }
      }
      v9->m_bOper = 0;
    }
  }
  if ( v7 )
  {
    CLogFile::Write(&stru_1799C95A8, "Stop Event Set (by cheat) >> %s", Str2);
    result = 1;
  }
  else
  {
    if ( Dest )
      sprintf(Dest, "can't find event set id");
    result = 0;
  }
  return result;
}
