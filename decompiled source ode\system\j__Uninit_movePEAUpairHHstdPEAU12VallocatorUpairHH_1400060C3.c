/*
 * Function: j_??$_Uninit_move@PEAU?$pair@HH@std@@PEAU12@V?$allocator@U?$pair@HH@std@@@2@U_Undefined_move_tag@2@@std@@YAPEAU?$pair@HH@0@PEAU10@00AEAV?$allocator@U?$pair@HH@std@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400060C3
 */

std::pair<int,int> *__fastcall std::_Uninit_move<std::pair<int,int> *,std::pair<int,int> *,std::allocator<std::pair<int,int>>,std::_Undefined_move_tag>(std::pair<int,int> *_First, std::pair<int,int> *_Last, std::pair<int,int> *_Dest, std::allocator<std::pair<int,int> > *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<std::pair<int,int> *,std::pair<int,int> *,std::allocator<std::pair<int,int>>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
