/*
 * Function: ?Load@CGuildBattleReservedScheduleListManager@GUILD_BATTLE@@QEAA_NHIHH@Z
 * Address: 0x1403CD540
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Load(GUILD_BATTLE::CGuildBattleReservedScheduleListManager *this, int iCurDay, unsigned int uiOldMapCnt, int iToday, int iTomorrow)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v8; // rax@8
  GUILD_BATTLE::CGuildBattleLogger *v9; // rax@10
  GUILD_BATTLE::CGuildBattleLogger *v10; // rax@14
  __int64 v11; // [sp+0h] [bp-38h]@1
  int v12; // [sp+20h] [bp-18h]@8
  int v13; // [sp+28h] [bp-10h]@8
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager *v14; // [sp+40h] [bp+8h]@1
  int v15; // [sp+48h] [bp+10h]@1
  unsigned int v16; // [sp+50h] [bp+18h]@1
  int v17; // [sp+58h] [bp+20h]@1

  v17 = iToday;
  v16 = uiOldMapCnt;
  v15 = iCurDay;
  v14 = this;
  v5 = &v11;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( v14->m_uiMapCnt != uiOldMapCnt )
    return 1;
  if ( iToday == iCurDay )
  {
    if ( !GUILD_BATTLE::CGuildBattleReservedScheduleListManager::LoadTodaySchedule(v14) )
    {
      v8 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v13 = iTomorrow;
      v12 = v17;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v8,
        "CGuildBattleReservedScheduleListManager::Load(%d,%u,%d,%d) : LoadTodaySchedule() Fail!",
        (unsigned int)v15,
        v16);
      return 0;
    }
    if ( !GUILD_BATTLE::CGuildBattleReservedScheduleListManager::LoadTomorrowSchedule(v14) )
    {
      v9 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v13 = iTomorrow;
      v12 = v17;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v9,
        "CGuildBattleReservedScheduleListManager::Load(%d,%u,%d,%d) : LoadTomorrowSchedule() Fail!",
        (unsigned int)v15,
        v16);
      return 0;
    }
  }
  else if ( iTomorrow == iCurDay )
  {
    if ( !GUILD_BATTLE::CGuildBattleReservedScheduleListManager::LoadTomorrowSchedule(v14) )
    {
      v10 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v13 = iTomorrow;
      v12 = v17;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v10,
        "CGuildBattleReservedScheduleListManager::Load(%d,%u,%d,%d) : LoadTomorrowSchedule() Fail!",
        (unsigned int)v15,
        v16);
      return 0;
    }
    GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Flip(v14);
  }
  return 1;
}
