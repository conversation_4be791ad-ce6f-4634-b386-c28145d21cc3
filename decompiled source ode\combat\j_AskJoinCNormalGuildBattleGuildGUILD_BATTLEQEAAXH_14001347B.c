/*
 * Function: j_?<PERSON><PERSON><PERSON><PERSON>@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXHKEPEADAEAVCNormalGuildBattleLogger@2@@Z
 * Address: 0x14001347B
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::Ask<PERSON><PERSON><PERSON>(GUILD_BATTLE::CNormalGuildBattleGuild *this, int n, unsigned int dwSerial, char GuildBattleNumber, char *wszDestGuild, GUILD_BATTLE::CNormalGuildBattleLogger *kLogger)
{
  GUILD_BATTLE::CNormalGuildBattleGuild::<PERSON><PERSON><PERSON><PERSON>(this, n, dwSerial, GuildBattleNumber, wszDestGuild, kLogger);
}
