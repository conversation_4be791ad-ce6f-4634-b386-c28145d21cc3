/*
 * Function: ?ReleaseDisplay@CMapDisplay@@QEAAJXZ
 * Address: 0x14019EBC0
 */

HRESULT __fastcall CMapDisplay::ReleaseDisplay(CMapDisplay *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-A8h]@1
  int k; // [sp+20h] [bp-88h]@16
  int j; // [sp+24h] [bp-84h]@14
  CSurface *v7; // [sp+28h] [bp-80h]@5
  CSurface *v8; // [sp+30h] [bp-78h]@5
  CSurface *v9; // [sp+38h] [bp-70h]@10
  CSurface *v10; // [sp+40h] [bp-68h]@10
  CSurface *v11; // [sp+48h] [bp-60h]@19
  CSurface *v12; // [sp+50h] [bp-58h]@19
  CSurface *v13; // [sp+58h] [bp-50h]@26
  CSurface *v14; // [sp+60h] [bp-48h]@26
  CSurface *v15; // [sp+68h] [bp-40h]@31
  CSurface *v16; // [sp+70h] [bp-38h]@31
  void *v17; // [sp+78h] [bp-30h]@6
  void *v18; // [sp+80h] [bp-28h]@11
  void *v19; // [sp+88h] [bp-20h]@20
  void *v20; // [sp+90h] [bp-18h]@27
  void *v21; // [sp+98h] [bp-10h]@32
  CMapDisplay *v22; // [sp+B0h] [bp+8h]@1

  v22 = this;
  v1 = &v4;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v22->m_pSFMap )
  {
    v8 = v22->m_pSFMap;
    v7 = v8;
    if ( v8 )
      v17 = CSurface::`scalar deleting destructor'(v7, 1u);
    else
      v17 = 0i64;
    v22->m_pSFMap = 0i64;
  }
  if ( v22->m_pSFBuf )
  {
    v10 = v22->m_pSFBuf;
    v9 = v10;
    if ( v10 )
      v18 = CSurface::`scalar deleting destructor'(v9, 1u);
    else
      v18 = 0i64;
    v22->m_pSFBuf = 0i64;
  }
  for ( j = 0; j < 2; ++j )
  {
    for ( k = 0; k < 13; ++k )
    {
      if ( v22->m_pSFObj[j][k] )
      {
        v12 = v22->m_pSFObj[j][k];
        v11 = v12;
        if ( v12 )
          v19 = CSurface::`scalar deleting destructor'(v11, 1u);
        else
          v19 = 0i64;
        v22->m_pSFObj[j][k] = 0i64;
      }
    }
  }
  if ( v22->m_pSFSelect )
  {
    v14 = v22->m_pSFSelect;
    v13 = v14;
    if ( v14 )
      v20 = CSurface::`scalar deleting destructor'(v13, 1u);
    else
      v20 = 0i64;
    v22->m_pSFSelect = 0i64;
  }
  if ( v22->m_pSFCircle )
  {
    v16 = v22->m_pSFCircle;
    v15 = v16;
    if ( v16 )
      v21 = CSurface::`scalar deleting destructor'(v15, 1u);
    else
      v21 = 0i64;
    v22->m_pSFCircle = 0i64;
  }
  v22->m_MapExtend.m_bExtendMode = 0;
  return CDisplay::DestroyObjects((CDisplay *)&v22->vfptr);
}
