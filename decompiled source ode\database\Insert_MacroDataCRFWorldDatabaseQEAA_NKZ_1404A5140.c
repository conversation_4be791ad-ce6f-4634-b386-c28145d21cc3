/*
 * Function: ?Insert_MacroData@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x1404A5140
 */

char __fastcall CRFWorldDatabase::Insert_MacroData(CRFWorldDatabase *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-4D8h]@1
  int v6; // [sp+20h] [bp-4B8h]@6
  int v7; // [sp+28h] [bp-4B0h]@6
  int v8; // [sp+30h] [bp-4A8h]@6
  int v9; // [sp+38h] [bp-4A0h]@6
  int v10; // [sp+40h] [bp-498h]@6
  int v11; // [sp+48h] [bp-490h]@6
  int v12; // [sp+50h] [bp-488h]@6
  int v13; // [sp+58h] [bp-480h]@6
  int v14; // [sp+60h] [bp-478h]@6
  int v15; // [sp+68h] [bp-470h]@6
  int v16; // [sp+70h] [bp-468h]@6
  int v17; // [sp+78h] [bp-460h]@6
  int v18; // [sp+80h] [bp-458h]@6
  unsigned int j; // [sp+90h] [bp-448h]@4
  bool v20; // [sp+94h] [bp-444h]@4
  char Dst; // [sp+B0h] [bp-428h]@4
  char v22; // [sp+B1h] [bp-427h]@4
  unsigned __int64 v23; // [sp+4C0h] [bp-18h]@4
  CRFWorldDatabase *v24; // [sp+4E0h] [bp+8h]@1
  unsigned int v25; // [sp+4E8h] [bp+10h]@1

  v25 = dwSerial;
  v24 = this;
  v2 = &v5;
  for ( i = 308i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v23 = (unsigned __int64)&v5 ^ _security_cookie;
  v20 = 0;
  Dst = 0;
  memset(&v22, 0, 0x3FFui64);
  for ( j = 0; (signed int)j < 3; ++j )
  {
    memset_0(&Dst, 0, 0x400ui64);
    v18 = -1;
    v17 = -1;
    v16 = -1;
    v15 = -1;
    v14 = -1;
    v13 = -1;
    v12 = -1;
    v11 = -1;
    v10 = -1;
    v9 = -1;
    v8 = -1;
    v7 = -1;
    v6 = -1;
    sprintf(
      &Dst,
      "Insert into tbl_Macro values(%d, %d,%d, %d, %d, %d, %d, %d, %d, %d, %d, %d, %d, %d, %d, '', '', '', '', '', 0, 0, 0)",
      v25,
      j);
    v20 = CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v24->vfptr, &Dst, 1);
    if ( !v20 )
      return 0;
  }
  return 1;
}
