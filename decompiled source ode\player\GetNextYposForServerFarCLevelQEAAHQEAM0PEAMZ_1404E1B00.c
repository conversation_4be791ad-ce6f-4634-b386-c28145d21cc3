/*
 * Function: ?GetNextYposForServerFar@CLevel@@QEAAHQEAM0PEAM@Z
 * Address: 0x1404E1B00
 */

__int64 __usercall CLevel::GetNextYposForServerFar@<rax>(CLevel *this@<rcx>, float *const a2@<rdx>, float *const a3@<r8>, float *a4@<r9>, signed __int64 a5@<rax>)
{
  void *v5; // rsp@1
  float v6; // xmm2_4@1
  float v7; // xmm3_4@1
  float *v8; // r13@1
  float *v9; // rbp@1
  CLevel *v10; // r12@1
  float v11; // xmm1_4@2
  __int64 result; // rax@4
  float v13; // xmm1_4@5
  float v14; // xmm7_4@5
  float v15; // xmm0_4@5
  int v16; // xmm1_4@5
  float v17; // xmm9_4@6
  float v18; // xmm6_4@6
  float v19; // xmm7_4@7
  float v20; // xmm8_4@7
  float v21; // xmm6_4@7
  float v22; // xmm11_4@7
  float v23; // xmm10_4@7
  float v24; // xmm12_4@7
  float v25; // xmm0_4@7
  float v26; // xmm11_4@7
  int v27; // xmm12_4@7
  float v28; // xmm10_4@7
  float v29; // xmm6_4@9
  CBsp *v30; // rcx@11
  __int32 v31; // ebx@11
  signed int v32; // esi@11
  float v33; // xmm5_4@11
  float *v34; // rdi@12
  CBsp *v35; // rcx@13
  float v36; // xmm0_4@13
  float v37; // xmm1_4@19
  int v38; // xmm0_4@21
  __int64 v39; // [sp-20h] [bp-FB78h]@1
  unsigned __int32 v40; // [sp+8h] [bp-FB50h]@13
  float v41; // [sp+10h] [bp-FB48h]@5
  float v42; // [sp+14h] [bp-FB44h]@5
  int v43; // [sp+18h] [bp-FB40h]@5
  __int32 v44; // [sp+20h] [bp-FB38h]@1
  float v45; // [sp+28h] [bp-FB30h]@5
  float v46; // [sp+2Ch] [bp-FB2Ch]@5
  float v47; // [sp+30h] [bp-FB28h]@5
  float v48; // [sp+38h] [bp-FB20h]@11
  float v49; // [sp+3Ch] [bp-FB1Ch]@11
  int v50; // [sp+40h] [bp-FB18h]@11
  float v51; // [sp+48h] [bp-FB10h]@11
  float v52; // [sp+4Ch] [bp-FB0Ch]@11
  int v53; // [sp+50h] [bp-FB08h]@11
  float v54; // [sp+58h] [bp-FB00h]@11
  float v55; // [sp+5Ch] [bp-FAFCh]@11
  int v56; // [sp+60h] [bp-FAF8h]@11
  float v57; // [sp+70h] [bp-FAE8h]@11
  unsigned __int64 v58; // [sp+FA70h] [bp-E8h]@1

  v5 = alloca(a5);
  v58 = (unsigned __int64)&v39 ^ _security_cookie;
  v6 = *a2;
  v7 = *a3;
  v8 = a4;
  v9 = a3;
  v10 = this;
  v44 = 0;
  if ( v6 != v7 || (v11 = a2[1], v11 != a3[1]) || a2[2] != a3[2] )
  {
    v13 = a2[2];
    v14 = FLOAT_9999_0;
    v46 = a2[1];
    v15 = a3[1];
    v47 = v13;
    v16 = *((int *)a3 + 2);
    v45 = v6;
    v41 = v7;
    v42 = v15;
    v43 = v16;
    while ( 1 )
    {
      v17 = FLOAT_N100000_0;
      v18 = GetDist(&v45, &v41);
      if ( v18 <= 30.0 )
      {
        v27 = v43;
        v28 = v42;
        v26 = v41;
      }
      else
      {
        v19 = v45;
        v20 = v46;
        v21 = v47;
        v22 = v41 - v45;
        v23 = v42 - v46;
        v24 = *(float *)&v43 - v47;
        v25 = sqrtf_0((float)((float)(v23 * v23) + (float)(v22 * v22)) + (float)(v24 * v24));
        v26 = (float)((float)(v22 / v25) * 25.0) + v19;
        v14 = FLOAT_9999_0;
        *(float *)&v27 = (float)((float)(v24 / v25) * 25.0) + v21;
        v18 = FLOAT_30_0;
        v41 = v26;
        v43 = v27;
        v28 = (float)((float)(v23 / v25) * 25.0) + v20;
      }
      v29 = v18 * 1.73;
      if ( v29 < 23.0 )
        v29 = FLOAT_23_0;
      v30 = v10->mBsp;
      v48 = v26;
      v50 = v27;
      v42 = v28 + (float)(0.0 - v29);
      v49 = v29 + v28;
      CBsp::GetLeafList(v30, &v48, &v41, &v44, (__int16 *)&v57, 0x7D00u);
      v31 = 0;
      v32 = 0;
      v51 = v41;
      v53 = v43;
      v54 = v41;
      v56 = v43;
      v42 = v42 + v29;
      v33 = v42;
      v52 = v42 + v14;
      v55 = v42 - v14;
      if ( v44 <= 0 )
        break;
      v34 = &v57;
      while ( 1 )
      {
        v35 = v10->mBsp;
        v40 = *(_WORD *)v34;
        v36 = CBsp::GetYposInLeaf(v35, &v51, &v54, v29, v33, v40);
        if ( v36 != -32000.0 && v36 > v17 )
        {
          v32 = 1;
          v17 = v36;
        }
        ++v31;
        v34 = (float *)((char *)v34 + 2);
        if ( v31 >= v44 )
          break;
        v33 = v42;
      }
      if ( !v32 )
        break;
      v37 = *v9;
      if ( v41 == *v9 && *(float *)&v43 == v9[2] )
      {
        *v8 = v17;
        return 1i64;
      }
      v47 = *(float *)&v43;
      v38 = *((int *)v9 + 2);
      v45 = v41;
      v43 = v38;
      v41 = v37;
      v46 = v17;
      v42 = v17;
    }
    result = 0i64;
  }
  else
  {
    *a4 = v11;
    result = 1i64;
  }
  return result;
}
