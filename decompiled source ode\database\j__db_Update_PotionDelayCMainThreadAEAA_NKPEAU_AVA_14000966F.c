/*
 * Function: j_?_db_Update_PotionDelay@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEADH@Z
 * Address: 0x14000966F
 */

bool __fastcall CMainThread::_db_Update_PotionDelay(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *szPotionDelayQuery, int nSize)
{
  return CMainThread::_db_Update_PotionDelay(this, dwSerial, pNewData, pOldData, szPotionDelayQuery, nSize);
}
