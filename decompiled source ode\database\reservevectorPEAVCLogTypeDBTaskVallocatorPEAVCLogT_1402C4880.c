/*
 * Function: ?reserve@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAAX_K@Z
 * Address: 0x1402C4880
 */

void __fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::reserve(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, unsigned __int64 _Count)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v4; // rcx@4
  __int64 v5; // [sp+0h] [bp-98h]@1
  CLogTypeDBTask **v6; // [sp+20h] [bp-78h]@7
  unsigned __int64 v7; // [sp+28h] [bp-70h]@7
  char v8; // [sp+30h] [bp-68h]@7
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *result; // [sp+48h] [bp-50h]@7
  char v10; // [sp+50h] [bp-48h]@7
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v11; // [sp+68h] [bp-30h]@7
  __int64 v12; // [sp+70h] [bp-28h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v13; // [sp+78h] [bp-20h]@7
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v14; // [sp+80h] [bp-18h]@7
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v15; // [sp+88h] [bp-10h]@7
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v16; // [sp+A0h] [bp+8h]@1
  unsigned __int64 _Counta; // [sp+A8h] [bp+10h]@1

  _Counta = _Count;
  v16 = this;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = -2i64;
  if ( std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::max_size(v16) < _Count )
    std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Xlen(v4);
  if ( std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::capacity(v16) < _Counta )
  {
    v6 = std::allocator<CLogTypeDBTask *>::allocate(&v16->_Alval, _Counta);
    result = (std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&v8;
    v11 = (std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&v10;
    v13 = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::end(
            v16,
            (std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&v8);
    v14 = v13;
    v15 = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::begin(v16, v11);
    std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Umove<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>>(
      v16,
      v15,
      v14,
      v6);
    v7 = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::size(v16);
    if ( v16->_Myfirst )
    {
      std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Destroy(v16, v16->_Myfirst, v16->_Mylast);
      std::allocator<CLogTypeDBTask *>::deallocate(&v16->_Alval, v16->_Myfirst, v16->_Myend - v16->_Myfirst);
    }
    v16->_Myend = &v6[_Counta];
    v16->_Mylast = &v6[v7];
    v16->_Myfirst = v6;
  }
}
