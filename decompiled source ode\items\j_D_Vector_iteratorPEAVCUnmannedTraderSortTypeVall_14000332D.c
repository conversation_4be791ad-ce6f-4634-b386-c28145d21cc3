/*
 * Function: j_??D?$_Vector_iterator@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEBAAEAPEAVCUnmannedTraderSortType@@XZ
 * Address: 0x14000332D
 */

CUnmannedTraderSortType **__fastcall std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::operator*(std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this)
{
  return std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::operator*(this);
}
