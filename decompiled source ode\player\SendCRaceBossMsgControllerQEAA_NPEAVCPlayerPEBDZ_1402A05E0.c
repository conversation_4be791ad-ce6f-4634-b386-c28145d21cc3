/*
 * Function: ?Send@CRaceBossMsgController@@QEAA_NPEAVCPlayer@@PEBD@Z
 * Address: 0x1402A05E0
 */

char __fastcall CRaceBossMsgController::Send(CRaceBossMsgController *this, CPlayer *pkSender, const char *pwszMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CPvpUserAndGuildRankingSystem *v6; // rax@8
  unsigned int v7; // eax@8
  char *v8; // rax@10
  __int64 v9; // [sp+0h] [bp-78h]@1
  char v10; // [sp+40h] [bp-38h]@8
  unsigned int dwSerial; // [sp+44h] [bp-34h]@8
  RACE_BOSS_MSG::CMsg *pkMsg; // [sp+58h] [bp-20h]@10
  int v13; // [sp+64h] [bp-14h]@10
  CRaceBossMsgController *v14; // [sp+80h] [bp+8h]@1
  CPlayer *v15; // [sp+88h] [bp+10h]@1
  char *v16; // [sp+90h] [bp+18h]@1

  v16 = (char *)pwszMsg;
  v15 = pkSender;
  v14 = this;
  v3 = &v9;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pkSender )
  {
    if ( unk_1799C9ADE )
    {
      v10 = CPlayerDB::GetRaceCode(&pkSender->m_Param);
      dwSerial = CPlayerDB::GetCharSerial(&v15->m_Param);
      v6 = CPvpUserAndGuildRankingSystem::Instance();
      v7 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v6, v10, 0);
      if ( v7 == dwSerial )
      {
        pkMsg = 0i64;
        v8 = CPlayerDB::GetCharNameW(&v15->m_Param);
        v13 = RACE_BOSS_MSG::CMsgListManager::Send(&v14->m_kManager, v10, dwSerial, v8, v16, &pkMsg, 0);
        if ( v13 )
        {
          CRaceBossMsgController::SendMsgRequestResult(v14, v15->m_ObjID.m_wIndex, v13);
          result = 0;
        }
        else
        {
          CRaceBossMsgController::SendMsgRequestResult(v14, v15->m_ObjID.m_wIndex, 0);
          CRaceBossMsgController::SendComfirmWeb(v14, v10, pkMsg);
          CRaceBossMsgController::SendConfirmCtrl(v14, v10, pkMsg);
          result = 1;
        }
      }
      else
      {
        CRaceBossMsgController::SendMsgRequestResult(v14, v15->m_ObjID.m_wIndex, 3);
        result = 0;
      }
    }
    else
    {
      CRaceBossMsgController::SendMsgRequestResult(v14, pkSender->m_ObjID.m_wIndex, 1);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
