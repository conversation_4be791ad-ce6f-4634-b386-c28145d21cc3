/*
 * Function: ?UpdateTomorrowComplete@CGuildBattleReservedScheduleListManager@GUILD_BATTLE@@QEAAXKPEAE@Z
 * Address: 0x1403CD7A0
 */

void __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateTomorrowComplete(GUILD_BATTLE::CGuildBattleReservedScheduleListManager *this, unsigned int dwMapID, char *pLoadData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v5; // rax@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  _worlddb_guild_battle_reserved_schedule_info *kInfo; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager *v8; // [sp+40h] [bp+8h]@1
  unsigned int uiMapInx; // [sp+48h] [bp+10h]@1

  uiMapInx = dwMapID;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  kInfo = (_worlddb_guild_battle_reserved_schedule_info *)pLoadData;
  if ( !GUILD_BATTLE::CReservedGuildScheduleDayGroup::Load(
          v8->m_pkTomorrow,
          1,
          dwMapID,
          (_worlddb_guild_battle_reserved_schedule_info *)pLoadData) )
  {
    v5 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v5,
      "CGuildBattleReservedScheduleListManager::UpdateTomorrowComplete(%u) : m_pkToday->Load( %u ) Fail!",
      uiMapInx,
      uiMapInx);
  }
}
