/*
 * Function: ?VerifyAndRestart@TF_VerifierBase@CryptoPP@@UEBA_NAEAVPK_MessageAccumulator@2@@Z
 * Address: 0x1406232D0
 */

char __fastcall CryptoPP::TF_VerifierBase::VerifyAndRestart(CryptoPP::TF_VerifierBase *this, struct CryptoPP::PK_MessageAccumulator *a2)
{
  __int64 v2; // rax@1
  __int64 v3; // rax@1
  int v4; // eax@1
  unsigned __int64 v5; // rax@1
  unsigned __int64 v6; // rax@1
  __int64 v7; // rax@4
  __int64 v8; // rax@4
  char v10; // [sp+30h] [bp-E8h]@1
  __int64 v11; // [sp+38h] [bp-E0h]@1
  struct CryptoPP::PK_MessageAccumulator *v12; // [sp+40h] [bp-D8h]@1
  __int64 v13; // [sp+48h] [bp-D0h]@1
  char v14; // [sp+50h] [bp-C8h]@4
  CryptoPP::PK_SignatureScheme::KeyTooShort v15; // [sp+58h] [bp-C0h]@2
  char v16; // [sp+B0h] [bp-68h]@4
  CryptoPP::TF_Base<CryptoPP::TrapdoorFunction,CryptoPP::PK_SignatureMessageEncodingMethod>Vtbl *v17; // [sp+C0h] [bp-58h]@1
  __int64 v18; // [sp+C8h] [bp-50h]@1
  __int64 v19; // [sp+D0h] [bp-48h]@1
  unsigned __int64 v20; // [sp+D8h] [bp-40h]@1
  __int64 v21; // [sp+E0h] [bp-38h]@4
  char *v22; // [sp+E8h] [bp-30h]@4
  CryptoPP::ClonableVtbl *v23; // [sp+F0h] [bp-28h]@4
  CryptoPP::TF_VerifierBase *v24; // [sp+120h] [bp+8h]@1

  v24 = this;
  v12 = a2;
  ((void (__fastcall *)(CryptoPP::TF_VerifierBase *, char *))this->vfptr[1].RecoverablePartFirst)(this, &v10);
  v17 = v24->vfptr;
  LODWORD(v2) = ((int (__fastcall *)(signed __int64))v17->GetMessageEncodingInterface)((signed __int64)&v24->vfptr);
  v13 = v2;
  LODWORD(v3) = ((int (__fastcall *)(struct CryptoPP::PK_MessageAccumulator *))v12->vfptr[9].__vecDelDtor)(v12);
  v18 = v3;
  v4 = (*(int (__fastcall **)(__int64))(*(_QWORD *)v3 + 56i64))(v3);
  v19 = *(_QWORD *)v13;
  LODWORD(v5) = (*(int (__fastcall **)(__int64, __int64, _QWORD))(v19 + 8))(v13, v11, (unsigned int)v4);
  v20 = v5;
  LODWORD(v6) = CryptoPP::TF_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::TF_Base<CryptoPP::TrapdoorFunction,CryptoPP::PK_SignatureMessageEncodingMethod>>::MessageRepresentativeBitLength(v24);
  if ( v6 < v20 )
  {
    CryptoPP::PK_SignatureScheme::KeyTooShort::KeyTooShort(&v15);
    CxxThrowException_0((__int64)&v15, (__int64)&TI4_AVKeyTooShort_PK_SignatureScheme_CryptoPP__);
  }
  LODWORD(v7) = CryptoPP::TF_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::TF_Base<CryptoPP::TrapdoorFunction,CryptoPP::PK_SignatureMessageEncodingMethod>>::MessageRepresentativeBitLength(v24);
  v21 = v7;
  v22 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)&v12[4]);
  qmemcpy(&v16, &v10, 0x10ui64);
  v23 = v12->vfptr;
  LODWORD(v8) = ((int (__fastcall *)(struct CryptoPP::PK_MessageAccumulator *))v23[9].__vecDelDtor)(v12);
  v14 = (*(int (__fastcall **)(__int64, __int64, char *, _QWORD))(*(_QWORD *)v13 + 56i64))(
          v13,
          v8,
          &v16,
          LOBYTE(v12[23].vfptr));
  LOBYTE(v12[23].vfptr) = 1;
  return v14;
}
