/*
 * Function: SQLInstallTranslatorExW
 * Address: 0x1404DB480
 */

int __fastcall SQLInstallTranslatorExW(const unsigned __int16 *lpszTranslator, const unsigned __int16 *lpszPathIn, unsigned __int16 *lpszPathOut, unsigned __int16 cbPathOutMax, unsigned __int16 *pcbPathOut, unsigned __int16 fRequest, unsigned int *lpdwUsageCount)
{
  const unsigned __int16 *v7; // rbp@1
  unsigned __int16 v8; // bx@1
  unsigned __int16 *v9; // rdi@1
  const unsigned __int16 *v10; // rsi@1
  __int64 (__cdecl *v11)(); // rax@1
  int result; // eax@2

  v7 = lpszTranslator;
  v8 = cbPathOutMax;
  v9 = lpszPathOut;
  v10 = lpszPathIn;
  v11 = ODBC___GetSetupProc("SQLInstallTranslatorExW");
  if ( v11 )
    result = ((int (__fastcall *)(const unsigned __int16 *, const unsigned __int16 *, unsigned __int16 *, _QWORD))v11)(
               v7,
               v10,
               v9,
               v8);
  else
    result = 0;
  return result;
}
