/*
 * Function: j_?grade_up_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@00EEKPEAD@Z
 * Address: 0x1400025F9
 */

void __fastcall CMgrAvatorItemHistory::grade_up_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pItem, _STORAGE_LIST::_db_con *pTalik, _STORAGE_LIST::_db_con *pJ<PERSON>el, char by<PERSON><PERSON><PERSON><PERSON><PERSON>, char byErrCode, unsigned int dwAfterLv, char *pszFileName)
{
  CMgrAvatorItemHistory::grade_up_item(this, n, pItem, pTalik, pJewel, byJewel<PERSON>um, byErrCode, dwAfterLv, pszFileName);
}
