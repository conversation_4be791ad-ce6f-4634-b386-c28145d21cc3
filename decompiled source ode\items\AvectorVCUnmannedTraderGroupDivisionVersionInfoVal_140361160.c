/*
 * Function: ??A?$vector@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@QEAAAEAVCUnmannedTraderGroupDivisionVersionInfo@@_K@Z
 * Address: 0x140361160
 */

CUnmannedTraderGroupDivisionVersionInfo *__fastcall std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::operator[](std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *this, unsigned __int64 _Pos)
{
  return &this->_Myfirst[_Pos];
}
