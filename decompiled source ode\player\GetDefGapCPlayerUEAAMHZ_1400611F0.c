/*
 * Function: ?GetDefGap@CPlayer@@UEAAMH@Z
 * Address: 0x1400611F0
 */

float __fastcall CPlayer::GetDefGap(CPlayer *this, int nPart)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-58h]@1
  int n; // [sp+30h] [bp-28h]@7
  char *v7; // [sp+38h] [bp-20h]@7
  _base_fld *v8; // [sp+40h] [bp-18h]@10
  _base_fld *v9; // [sp+48h] [bp-10h]@14
  CPlayer *v10; // [sp+60h] [bp+8h]@1
  int v11; // [sp+68h] [bp+10h]@1

  v11 = nPart;
  v10 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CPlayer::IsRidingUnit(v10) )
  {
    if ( v10->m_pUsingUnit )
    {
      v9 = CRecordData::GetRecord(&stru_1799C8BA0, v10->m_pUsingUnit->byFrame);
      return *(float *)&v9[1].m_strCode[4];
    }
  }
  else
  {
    if ( v11 >= 8 )
      return FLOAT_0_5;
    n = v10->m_Param.m_dbChar.m_byDftPart[v11];
    v7 = &v10->m_Param.m_dbEquip.m_pStorageList[v11].m_bLoad;
    if ( *v7 )
    {
      if ( CPlayer::GetEffectEquipCode(v10, 1, v11) == 1 )
        n = *(_WORD *)(v7 + 3);
    }
    v8 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v11, n);
    if ( v8 )
      return *(float *)&v8[5].m_strCode[56];
  }
  return FLOAT_0_5;
}
