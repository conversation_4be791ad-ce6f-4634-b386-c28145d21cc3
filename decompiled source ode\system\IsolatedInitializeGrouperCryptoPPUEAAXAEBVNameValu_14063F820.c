/*
 * Function: ?IsolatedInitialize@Grouper@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x14063F820
 */

void __fastcall CryptoPP::Grouper::IsolatedInitialize(CryptoPP::Grouper *this, const struct CryptoPP::NameValuePairs *a2)
{
  const char *v2; // rax@1
  CryptoPP::Name *v3; // rcx@1
  const char *v4; // rax@2
  CryptoPP::Name *v5; // rcx@2
  const char *v6; // rax@3
  const char *v7; // rax@4
  const char *v8; // rax@4
  const char *v9; // rax@4
  CryptoPP::ConstByteArrayParameter value; // [sp+20h] [bp-88h]@1
  CryptoPP::ConstByteArrayParameter v11; // [sp+50h] [bp-58h]@1
  __int64 v12; // [sp+80h] [bp-28h]@1
  unsigned __int64 len; // [sp+88h] [bp-20h]@4
  unsigned __int64 v14; // [sp+90h] [bp-18h]@4
  CryptoPP::Grouper *v15; // [sp+B0h] [bp+8h]@1
  const struct CryptoPP::NameValuePairs *v16; // [sp+B8h] [bp+10h]@1

  v16 = a2;
  v15 = this;
  v12 = -2i64;
  v2 = CryptoPP::Name::GroupSize(this);
  *((_QWORD *)v15 + 12) = CryptoPP::NameValuePairs::GetIntValueWithDefault((CryptoPP::NameValuePairs *)v16, v2, 0);
  CryptoPP::ConstByteArrayParameter::ConstByteArrayParameter(&value, 0i64, 0);
  CryptoPP::ConstByteArrayParameter::ConstByteArrayParameter(&v11, 0i64, 0);
  if ( *((_QWORD *)v15 + 12) )
  {
    v4 = CryptoPP::Name::Separator(v3);
    CryptoPP::NameValuePairs::GetRequiredParameter<CryptoPP::ConstByteArrayParameter>(v16, "Grouper", v4, &value);
  }
  else
  {
    v6 = CryptoPP::Name::Separator(v3);
    CryptoPP::NameValuePairs::GetValue<CryptoPP::ConstByteArrayParameter>((CryptoPP::NameValuePairs *)v16, v6, &value);
  }
  v7 = CryptoPP::Name::Terminator(v5);
  CryptoPP::NameValuePairs::GetValue<CryptoPP::ConstByteArrayParameter>((CryptoPP::NameValuePairs *)v16, v7, &v11);
  len = CryptoPP::ConstByteArrayParameter::size(&value);
  v8 = CryptoPP::ConstByteArrayParameter::begin(&value);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::Assign(
    (CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)v15 + 2,
    v8,
    len);
  v14 = CryptoPP::ConstByteArrayParameter::size(&v11);
  v9 = CryptoPP::ConstByteArrayParameter::begin(&v11);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::Assign(
    (CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)v15 + 3,
    v9,
    v14);
  *((_QWORD *)v15 + 13) = 0i64;
  CryptoPP::ConstByteArrayParameter::~ConstByteArrayParameter(&v11);
  CryptoPP::ConstByteArrayParameter::~ConstByteArrayParameter(&value);
}
