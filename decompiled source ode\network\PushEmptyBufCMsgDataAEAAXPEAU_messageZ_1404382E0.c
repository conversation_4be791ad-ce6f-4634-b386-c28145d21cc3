/*
 * Function: ?PushEmptyBuf@CMsgData@@AEAAXPEAU_message@@@Z
 * Address: 0x1404382E0
 */

void __fastcall CMsgData::PushEmptyBuf(CMsgData *this, _message *pMsg)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMsgData *v5; // [sp+30h] [bp+8h]@1
  _message *v6; // [sp+38h] [bp+10h]@1

  v6 = pMsg;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CMyCriticalSection::Lock(&v5->m_csEmpty);
  v6->pPrev = &v5->m_gmListEmptyHead;
  v6->pNext = v5->m_gmListEmptyHead.pNext;
  v5->m_gmListEmptyHead.pNext->pPrev = v6;
  v5->m_gmListEmptyHead.pNext = v6;
  CMyCriticalSection::Unlock(&v5->m_csEmpty);
}
