/*
 * Function: ?GetGroupPrecomputation@?$DL_GroupParametersImpl@V?$EcPrecomputation@VECP@CryptoPP@@@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@2@V?$DL_GroupParameters@UECPPoint@CryptoPP@@@2@@CryptoPP@@UEBAAEBV?$DL_GroupPrecomputation@UECPPoint@CryptoPP@@@2@XZ
 * Address: 0x1404502C0
 */

CryptoPP::DL_GroupPrecomputation<CryptoPP::ECPPoint> *__fastcall CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>>::GetGroupPrecomputation(CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> > *this)
{
  return (CryptoPP::DL_GroupPrecomputation<CryptoPP::ECPPoint> *)this->gap18;
}
