/*
 * Function: ?IsINIFileChanged@CMonsterEventSet@@QEAA_NPEBDU_FILETIME@@@Z
 * Address: 0x1402A9150
 */

bool __fastcall CMonsterEventSet::IsINIFileChanged(CMonsterEventSet *this, const char *pszFileName, _FILETIME ftCurr)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-48h]@1
  _FILETIME ftWrite; // [sp+28h] [bp-20h]@4
  _FILETIME v8; // [sp+60h] [bp+18h]@1

  v8 = ftCurr;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( GetLastWriteFileTime(pszFileName, &ftWrite) )
    result = v8 != ftWrite;
  else
    result = 0;
  return result;
}
