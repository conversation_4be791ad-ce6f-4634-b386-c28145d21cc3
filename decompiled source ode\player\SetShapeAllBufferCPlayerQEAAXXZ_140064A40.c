/*
 * Function: ?SetS<PERSON>pe<PERSON>llBuffer@CPlayer@@QEAAXXZ
 * Address: 0x140064A40
 */

void __fastcall CPlayer::SetShape<PERSON>llBuffer(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CHonorGuild *v3; // rax@16
  char *v4; // rax@18
  CHonorGuild *v5; // rax@57
  __int64 v6; // [sp+0h] [bp-58h]@1
  int j; // [sp+20h] [bp-38h]@4
  CGuild *v8; // [sp+28h] [bp-30h]@16
  int v9; // [sp+30h] [bp-28h]@16
  char v10; // [sp+34h] [bp-24h]@26
  char v11; // [sp+38h] [bp-20h]@49
  CGuild *v12; // [sp+40h] [bp-18h]@57
  int v13; // [sp+48h] [bp-10h]@57
  CPlayer *v14; // [sp+60h] [bp+8h]@1

  v14 = this;
  v1 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v14->m_bufShapeAll.wIndex = v14->m_ObjID.m_wIndex;
  v14->m_bufShapeAll.dwSerial = v14->m_dwObjSerial;
  v14->m_bufShapeAll.wEquipVer = CPlayer::GetVisualVer(v14);
  v14->m_bufShapeAll.byRecIndex = CPlayerDB::GetRaceSexCode(&v14->m_Param);
  v14->m_bufShapeAll.byFaceIndex = v14->m_Param.m_dbChar.m_byDftPart_Face;
  v14->m_bufShapeAll.byHairIndex = v14->m_Param.m_dbChar.m_byDftPart[4];
  v14->m_bufShapeAll.byCashChangeStateFlag = v14->m_CashChangeStateFlag.m_byStateFlag;
  for ( j = 0; j < 8; ++j )
  {
    if ( v14->m_Param.m_dbEquip.m_pStorageList[j].m_bLoad )
    {
      v14->m_bufShapeAll.ModelPerPart[j].wPartIndex = v14->m_Param.m_dbEquip.m_pStorageList[j].m_wItemIndex;
      v14->m_bufShapeAll.ModelPerPart[j].byLv = GetItemUpgedLv(v14->m_Param.m_dbEquip.m_pStorageList[j].m_dwLv);
    }
    else if ( j >= 5 )
    {
      v14->m_bufShapeAll.ModelPerPart[j].wPartIndex = -1;
    }
    else
    {
      v14->m_bufShapeAll.ModelPerPart[j].wPartIndex = v14->m_Param.m_dbChar.m_byDftPart[j];
      v14->m_bufShapeAll.ModelPerPart[j].byLv = 0;
    }
  }
  if ( v14->m_bSpyGM )
    v14->m_bufShapeAll.byUserGrade = 0;
  else
    v14->m_bufShapeAll.byUserGrade = v14->m_byUserDgr;
  if ( v14->m_Param.m_pGuild )
  {
    v14->m_bufShapeAll.dwGuildSerial = v14->m_Param.m_pGuild->m_dwSerial;
    v8 = v14->m_Param.m_pGuild;
    v9 = CPlayerDB::GetRaceCode(&v14->m_Param);
    v3 = CHonorGuild::Instance();
    v14->m_bufShapeAll.byHonorGuildRank = CHonorGuild::FindHonorGuildRank(v3, v9, v8->m_dwSerial);
  }
  else
  {
    v14->m_bufShapeAll.dwGuildSerial = -1;
    v14->m_bufShapeAll.byHonorGuildRank = -1;
  }
  v4 = CPlayerDB::GetCharNameW(&v14->m_Param);
  strcpy_0(v14->m_bufShapeAll.wszName, v4);
  v14->m_bufShapeAll.bySpecialPart = -1;
  if ( CPlayer::IsRidingUnit(v14) )
  {
    v14->m_bufShapeAll.bySpecialPart = 0;
  }
  else if ( v14->m_pSiegeItem )
  {
    v14->m_bufShapeAll.bySpecialPart = 1;
  }
  else if ( v14->m_bTakeSoccerBall )
  {
    v14->m_bufShapeAll.bySpecialPart = 2;
  }
  else if ( v14->m_byMoveType == 2 )
  {
    v14->m_bufShapeAll.bySpecialPart = 3;
  }
  v10 = v14->m_bufShapeAll.bySpecialPart;
  if ( v10 )
  {
    if ( v10 == 1 )
    {
      v14->m_bufShapeAll.byFrameIndex = v14->m_pSiegeItem->m_wItemIndex;
    }
    else if ( v10 == 2 )
    {
      v14->m_bufShapeAll.byFrameIndex = v14->m_pSoccerItem->m_wItemIndex;
    }
  }
  else
  {
    v14->m_bufShapeAll.byFrameIndex = v14->m_pUsingUnit->byFrame;
    memcpy_0(v14->m_bufShapeAll.byUnitPartIndex, v14->m_pUsingUnit->byPart, 6ui64);
  }
  v14->m_bufShapeAll.byColor = v14->m_byGuildBattleColorInx;
  v14->m_bufSpapePart.wIndex = v14->m_ObjID.m_wIndex;
  v14->m_bufSpapePart.dwSerial = v14->m_dwObjSerial;
  v14->m_bufSpapePart.wEquipVer = CPlayer::GetVisualVer(v14);
  v14->m_bufSpapePart.byCashChangeStateFlag = v14->m_CashChangeStateFlag.m_byStateFlag;
  for ( j = 0; j < 8; ++j )
  {
    if ( v14->m_Param.m_dbEquip.m_pStorageList[j].m_bLoad )
    {
      v14->m_bufSpapePart.ModelPerPart[j].wPartIndex = v14->m_Param.m_dbEquip.m_pStorageList[j].m_wItemIndex;
      v14->m_bufSpapePart.ModelPerPart[j].byLv = GetItemUpgedLv(v14->m_Param.m_dbEquip.m_pStorageList[j].m_dwLv);
    }
    else if ( j >= 5 )
    {
      v14->m_bufSpapePart.ModelPerPart[j].wPartIndex = -1;
    }
    else
    {
      v14->m_bufSpapePart.ModelPerPart[j].wPartIndex = v14->m_Param.m_dbChar.m_byDftPart[j];
      v14->m_bufSpapePart.ModelPerPart[j].byLv = 0;
    }
  }
  v14->m_bufSpapePart.bySpecialPart = -1;
  if ( CPlayer::IsRidingUnit(v14) )
  {
    v14->m_bufSpapePart.bySpecialPart = 0;
  }
  else if ( v14->m_pSiegeItem )
  {
    v14->m_bufSpapePart.bySpecialPart = 1;
  }
  else if ( v14->m_bTakeSoccerBall )
  {
    v14->m_bufSpapePart.bySpecialPart = 2;
  }
  else if ( v14->m_byMoveType == 2 )
  {
    v14->m_bufSpapePart.bySpecialPart = 3;
  }
  v11 = v14->m_bufSpapePart.bySpecialPart;
  if ( v11 )
  {
    if ( v11 == 1 )
    {
      v14->m_bufSpapePart.byFrameIndex = v14->m_pSiegeItem->m_wItemIndex;
    }
    else if ( v11 == 2 )
    {
      v14->m_bufSpapePart.byFrameIndex = v14->m_pSoccerItem->m_wItemIndex;
    }
  }
  else
  {
    v14->m_bufSpapePart.byFrameIndex = v14->m_pUsingUnit->byFrame;
    memcpy_0(v14->m_bufSpapePart.byUnitPartIndex, v14->m_pUsingUnit->byPart, 6ui64);
  }
  if ( v14->m_Param.m_pGuild )
  {
    v12 = v14->m_Param.m_pGuild;
    v13 = CPlayerDB::GetRaceCode(&v14->m_Param);
    v5 = CHonorGuild::Instance();
    v14->m_bufSpapePart.byHonorGuildRank = CHonorGuild::FindHonorGuildRank(v5, v13, v12->m_dwSerial);
  }
  else
  {
    v14->m_bufSpapePart.byHonorGuildRank = -1;
  }
}
