/*
 * Function: j_?construct@?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@QEAAXPEAPEAVCUnmannedTraderClassInfo@@AEBQEAV3@@Z
 * Address: 0x1400127FB
 */

void __fastcall std::allocator<CUnmannedTraderClassInfo *>::construct(std::allocator<CUnmannedTraderClassInfo *> *this, CUnmannedTraderClassInfo **_Ptr, CUnmannedTraderClassInfo *const *_Val)
{
  std::allocator<CUnmannedTraderClassInfo *>::construct(this, _Ptr, _Val);
}
