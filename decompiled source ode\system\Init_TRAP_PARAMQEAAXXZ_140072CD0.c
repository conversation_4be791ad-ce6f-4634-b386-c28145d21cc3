/*
 * Function: ?Init@_TRAP_PARAM@@QEAAXXZ
 * Address: 0x140072CD0
 */

void __fastcall _TRAP_PARAM::Init(_TRAP_PARAM *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _TRAP_PARAM *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 20; ++j )
    _TRAP_PARAM::_param::init((_TRAP_PARAM::_param *)v5 + j);
  v5->m_nCount = 0;
}
