/*
 * Function: ??1?$hash_map@HPEAVCAsyncLogInfo@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@@stdext@@QEAA@XZ
 * Address: 0x1403C1170
 */

void __fastcall stdext::hash_map<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::~hash_map<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>(stdext::hash_map<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  stdext::hash_map<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  stdext::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>,0>>::~_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>,0>>((stdext::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CAsyncLogInfo *> >,0> > *)&v4->_Myfirstiter);
}
