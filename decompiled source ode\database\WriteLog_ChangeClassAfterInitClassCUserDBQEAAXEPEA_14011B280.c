/*
 * Function: ?WriteLog_ChangeClassAfterInitClass@CUserDB@@QEAAXEPEAD@Z
 * Address: 0x14011B280
 */

void __fastcall CUserDB::WriteLog_ChangeClassAfterInitClass(CUserDB *this, char byType, char *szPrevClass)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-A8h]@1
  char *pQryData; // [sp+20h] [bp-88h]@6
  int nSize; // [sp+28h] [bp-80h]@6
  _log_change_class_after_init_class v8; // [sp+38h] [bp-70h]@6
  tm *v9; // [sp+68h] [bp-40h]@6
  __int64 _Time; // [sp+78h] [bp-30h]@6
  unsigned __int64 v11; // [sp+90h] [bp-18h]@4
  CUserDB *v12; // [sp+B0h] [bp+8h]@1
  char v13; // [sp+B8h] [bp+10h]@1

  v13 = byType;
  v12 = this;
  v3 = &v5;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( byType != 1 || v12->m_AvatorData.dbAvator.m_dwClassInitCnt )
  {
    v8.dwCharacSerial = v12->m_dwSerial;
    strcpy_0(v8.szPrevClassCode, szPrevClass);
    strcpy_0(v8.szNextClassCode, v12->m_AvatorData.dbAvator.m_szClassCode);
    v8.nClassInitCnt = v12->m_AvatorData.dbAvator.m_dwClassInitCnt;
    v8.byLastClassGrade = v12->m_AvatorData.dbAvator.m_byLastClassGrade;
    v8.byType = v13;
    time_2(&_Time);
    v9 = localtime_0(&_Time);
    v8.dwYear = v9->tm_year + 1900;
    v8.byMonth = v9->tm_mon + 1;
    v8.byDay = v9->tm_mday;
    v8.byHour = v9->tm_hour;
    v8.byMin = v9->tm_min;
    v8.bySec = v9->tm_sec;
    nSize = _log_change_class_after_init_class::size(&v8);
    pQryData = (char *)&v8;
    CMainThread::PushDQSData(&g_Main, v12->m_dwAccountSerial, &v12->m_idWorld, 24, (char *)&v8, nSize);
  }
}
