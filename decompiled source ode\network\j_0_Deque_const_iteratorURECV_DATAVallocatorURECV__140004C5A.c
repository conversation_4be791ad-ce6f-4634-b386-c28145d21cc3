/*
 * Function: j_??0?$_Deque_const_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEAA@AEBV01@@Z
 * Address: 0x140004C5A
 */

void __fastcall std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this, std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Right)
{
  std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
    this,
    _Right);
}
