/*
 * Function: j_??$unchecked_uninitialized_copy@PEAU?$pair@K<PERSON>@std@@PEAU12@V?$allocator@U?$pair@KK@std@@@2@@stdext@@YAPEAU?$pair@K<PERSON>@std@@PEAU12@00AEAV?$allocator@U?$pair@KK@std@@@2@@Z
 * Address: 0x140009C6E
 */

std::pair<unsigned long,unsigned long> *__fastcall stdext::unchecked_uninitialized_copy<std::pair<unsigned long,unsigned long> *,std::pair<unsigned long,unsigned long> *,std::allocator<std::pair<unsigned long,unsigned long>>>(std::pair<unsigned long,unsigned long> *_First, std::pair<unsigned long,unsigned long> *_Last, std::pair<unsigned long,unsigned long> *_Dest, std::allocator<std::pair<unsigned long,unsigned long> > *_Al)
{
  return stdext::unchecked_uninitialized_copy<std::pair<unsigned long,unsigned long> *,std::pair<unsigned long,unsigned long> *,std::allocator<std::pair<unsigned long,unsigned long>>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
