/*
 * Function: _std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Assign_n_::_1_::dtor$1
 * Address: 0x1403AF770
 */

void __fastcall std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::_Assign_n_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::~_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(*(std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > **)(a2 + 112));
}
