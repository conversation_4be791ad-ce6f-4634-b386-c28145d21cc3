/*
 * Function: ?PushChildMon@CMonsterHierarchy@@QEAAHHPEAVCMonster@@@Z
 * Address: 0x140157990
 */

signed __int64 __fastcall CMonsterHierarchy::PushChildMon(CMonsterHierarchy *this, int nKind, CMonster *pMon)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@8
  CMonsterHierarchy *v8; // [sp+40h] [bp+8h]@1
  int v9; // [sp+48h] [bp+10h]@1
  CMonster *pMona; // [sp+50h] [bp+18h]@1

  pMona = pMon;
  v9 = nKind;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( CMonsterHierarchy::SearchChildMon(v8, pMon) )
  {
    result = 1i64;
  }
  else
  {
    if ( v9 >= 0 && v9 < 3 )
    {
      for ( j = 0; j < 0xA; ++j )
      {
        if ( !v8->m_pChildMon[v9][j] )
        {
          v8->m_pChildMon[v9][j] = pMona;
          ++v8->m_dwMonCount[v9];
          ++v8->m_dwTotalCount;
          return 1i64;
        }
      }
    }
    result = 0i64;
  }
  return result;
}
