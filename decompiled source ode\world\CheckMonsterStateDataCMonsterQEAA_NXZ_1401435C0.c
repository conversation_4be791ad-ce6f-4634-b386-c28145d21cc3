/*
 * Function: ?CheckMonsterStateData@CMonster@@QEAA_NXZ
 * Address: 0x1401435C0
 */

char __fastcall CMonster::CheckMonsterStateData(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  bool v6; // [sp+21h] [bp-17h]@4
  char v7; // [sp+22h] [bp-16h]@4
  CMonster *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8->m_MonsterStateData.m_wSendChunkData &= 0xFFC3u;
  v5 = CMonster::GetMoveType(v8);
  v8->m_MonsterStateData.m_wSendChunkData = v5 & 1 | v8->m_MonsterStateData.m_wSendChunkData & 0xFFFE;
  v6 = _effect_parameter::GetEff_State(&v8->m_EP, 7);
  v8->m_MonsterStateData.m_wSendChunkData = 2 * v6 | v8->m_MonsterStateData.m_wSendChunkData & 0xFFFD;
  v7 = CMonster::GetCombatState(v8);
  v8->m_MonsterStateData.m_wSendChunkData = ((v7 & 1) << 6) | v8->m_MonsterStateData.m_wSendChunkData & 0xFFBF;
  if ( MonsterStateData::operator!=(&v8->m_MonsterStateData, &v8->m_BeforeMonsterStateData) )
  {
    v8->m_BeforeMonsterStateData.m_wSendChunkData = v8->m_MonsterStateData.m_wSendChunkData;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
