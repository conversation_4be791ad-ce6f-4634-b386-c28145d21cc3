/*
 * Function: ?SQLExecDirect_RetErrCode@CRFNewDatabase@@QEAAEPEAD@Z
 * Address: 0x140484ED0
 */

char __fastcall CRFNewDatabase::SQLExecDirect_RetErrCode(CRFNewDatabase *this, char *strQuery)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-48h]@1
  void *SQLStmt; // [sp+20h] [bp-28h]@13
  __int16 v7; // [sp+30h] [bp-18h]@9
  CRFNewDatabase *v8; // [sp+50h] [bp+8h]@1
  SQLCHAR *szLog; // [sp+58h] [bp+10h]@1

  szLog = strQuery;
  v8 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v8->m_bSaveDBLog )
    CRFNewDatabase::Log(v8, strQuery);
  if ( v8->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase(v8) )
  {
    v7 = SQLExecDirectA_0(v8->m_hStmtSelect, szLog, -3);
    if ( v7 && v7 != 1 )
    {
      if ( v7 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v8->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog(v8, v7, szLog, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction(v8, v7, v8->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog(v8, "ReConnectDataBase Fail. Query : %s", szLog);
    result = 1;
  }
  return result;
}
