/*
 * Function: ?GetEquip@CEquipItemSFAgent@@QEAAPEAU_db_con@_STORAGE_LIST@@H@Z
 * Address: 0x1401210A0
 */

_STORAGE_LIST::_db_con *__fastcall CEquipItemSFAgent::GetEquip(CEquipItemSFAgent *this, int nEquipTblIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _STORAGE_LIST::_db_con *result; // rax@5
  _STORAGE_LIST::_db_con *v5; // [sp+0h] [bp-18h]@1
  CEquipItemSFAgent *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = (__int64 *)&v5;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v6->m_pMaster )
  {
    v5 = 0i64;
    if ( nEquipTblIndex < 8 && nEquipTblIndex >= 0 )
      v5 = &v6->m_pMaster->m_Param.m_dbEquip.m_pStorageList[nEquipTblIndex];
    if ( v5 )
    {
      if ( v5->m_bLoad )
        result = v5;
      else
        result = 0i64;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
