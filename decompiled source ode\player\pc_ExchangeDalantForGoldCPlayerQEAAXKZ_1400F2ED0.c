/*
 * Function: ?pc_ExchangeDalantForGold@CPlayer@@QEAAXK@Z
 * Address: 0x1400F2ED0
 */

void __fastcall CPlayer::pc_ExchangeDalantForGold(CPlayer *this, unsigned int dwDalant)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@4
  unsigned int v5; // eax@6
  unsigned int v6; // eax@9
  unsigned int v7; // eax@12
  CMoneySupplyMgr *v8; // rax@16
  __int64 v9; // [sp+0h] [bp-88h]@1
  char v10; // [sp+40h] [bp-48h]@4
  unsigned int dwCurDalant; // [sp+44h] [bp-44h]@4
  unsigned int dwCurGold; // [sp+48h] [bp-40h]@4
  int v13; // [sp+4Ch] [bp-3Ch]@4
  unsigned int v14; // [sp+50h] [bp-38h]@4
  unsigned int nAmount; // [sp+54h] [bp-34h]@4
  unsigned __int64 ui64AddGold; // [sp+58h] [bp-30h]@4
  unsigned int v17; // [sp+60h] [bp-28h]@6
  char *v18; // [sp+68h] [bp-20h]@12
  unsigned int v19; // [sp+70h] [bp-18h]@12
  int nLv; // [sp+74h] [bp-14h]@16
  int v21; // [sp+78h] [bp-10h]@16
  CPlayer *p; // [sp+90h] [bp+8h]@1
  unsigned int v23; // [sp+98h] [bp+10h]@1

  v23 = dwDalant;
  p = this;
  v2 = &v9;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = 0;
  dwCurDalant = CPlayerDB::GetDalant(&p->m_Param);
  dwCurGold = CPlayerDB::GetGold(&p->m_Param);
  v4 = CPlayerDB::GetRaceCode(&p->m_Param);
  v13 = eGetRate(v4);
  v14 = v23 - v23 % v13;
  nAmount = (signed int)ffloor((float)(signed int)(v23 - v23 % v13) * 0.1);
  LODWORD(ui64AddGold) = v14 / v13;
  if ( IsBeNearStore(p, 10) )
  {
    v17 = nAmount + v14;
    v5 = CPlayerDB::GetDalant(&p->m_Param);
    if ( v17 <= v5 )
    {
      if ( (_DWORD)ui64AddGold )
      {
        v6 = CPlayerDB::GetGold(&p->m_Param);
        if ( !CanAddMoneyForMaxLimGold((unsigned int)ui64AddGold, v6) )
          v10 = 2;
      }
    }
    else
    {
      v10 = 1;
    }
  }
  else
  {
    v10 = 13;
  }
  if ( !v10 )
  {
    CPlayer::SubDalant(p, nAmount + v14);
    CPlayer::AddGold(p, ui64AddGold, 0);
    v18 = p->m_szItemHistoryFileName;
    v19 = CPlayerDB::GetGold(&p->m_Param);
    v7 = CPlayerDB::GetDalant(&p->m_Param);
    CMgrAvatorItemHistory::exchange_money(
      &CPlayer::s_MgrItemHistory,
      p->m_ObjID.m_wIndex,
      dwCurDalant,
      dwCurGold,
      v7,
      v19,
      v18);
    HIDWORD(ui64AddGold) = CPlayerDB::GetLevel(&p->m_Param);
    if ( HIDWORD(ui64AddGold) == 30
      || HIDWORD(ui64AddGold) == 40
      || HIDWORD(ui64AddGold) == 50
      || HIDWORD(ui64AddGold) == 60 )
    {
      nLv = CPlayerDB::GetLevel(&p->m_Param);
      v21 = CPlayerDB::GetRaceCode(&p->m_Param);
      v8 = CMoneySupplyMgr::Instance();
      CMoneySupplyMgr::UpdateFeeMoneyData(v8, v21, nLv, nAmount);
    }
  }
  CPlayer::SendMsg_ExchangeMoneyResult(p, v10);
}
