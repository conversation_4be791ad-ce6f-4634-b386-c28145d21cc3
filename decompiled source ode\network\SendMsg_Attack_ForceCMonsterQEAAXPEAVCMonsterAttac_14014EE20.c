/*
 * Function: ?SendMsg_Attack_Force@CMonster@@QEAAXPEAVCMonsterAttack@@@Z
 * Address: 0x14014EE20
 */

void __fastcall CMonster::SendMsg_Attack_Force(CMonster *this, CMonsterAttack *pAt)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@7
  __int64 v5; // [sp+0h] [bp-1D8h]@1
  _attack_force_result_zocl v6; // [sp+40h] [bp-198h]@4
  int j; // [sp+194h] [bp-44h]@4
  char pbyType; // [sp+1A4h] [bp-34h]@7
  char v9; // [sp+1A5h] [bp-33h]@7
  unsigned __int64 v10; // [sp+1C0h] [bp-18h]@4
  CMonster *v11; // [sp+1E0h] [bp+8h]@1
  CMonsterAttack *v12; // [sp+1E8h] [bp+10h]@1

  v12 = pAt;
  v11 = this;
  v2 = &v5;
  for ( i = 116i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  _attack_force_result_zocl::_attack_force_result_zocl(&v6);
  v6.byAtterID = v11->m_ObjID.m_byID;
  v6.dwAtterSerial = v11->m_dwObjSerial;
  v6.byForceIndex = v12->m_pp->pFld->m_dwIndex;
  v6.byForceLv = v12->m_pp->nLevel;
  v6.zAreaPos[0] = (signed int)ffloor(v12->m_pp->fArea[0]);
  v6.zAreaPos[1] = (signed int)ffloor(v12->m_pp->fArea[2]);
  v6.byAttackPart = v12->m_pp->nPart;
  v6.bCritical = v12->m_bIsCrtAtt;
  v6.byListNum = v12->m_nDamagedObjNum;
  for ( j = 0; j < v12->m_nDamagedObjNum; ++j )
  {
    v6.DamList[j].byDstID = v12->m_DamList[j].m_pChar->m_ObjID.m_byID;
    v6.DamList[j].dwDstSerial = v12->m_DamList[j].m_pChar->m_dwObjSerial;
    v6.DamList[j].wDamage = v12->m_DamList[j].m_nDamage;
  }
  pbyType = 5;
  v9 = 9;
  v4 = _attack_force_result_zocl::size(&v6);
  CGameObject::CircleReport((CGameObject *)&v11->vfptr, &pbyType, &v6.byAtterID, v4, 1);
}
