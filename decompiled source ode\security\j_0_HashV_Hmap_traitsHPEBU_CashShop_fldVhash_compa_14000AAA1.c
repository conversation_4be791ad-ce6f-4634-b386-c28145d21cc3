/*
 * Function: j_??0?$_Hash@V?$_Hmap_traits@HPEBU_CashShop_fld@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEBU_CashShop_fld@@@std@@@std@@$0A@@stdext@@@stdext@@QEAA@AEBV?$hash_compare@HU?$less@H@std@@@1@AEBV?$allocator@U?$pair@$$CBHPEBU_CashShop_fld@@@std@@@std@@@Z
 * Address: 0x14000AAA1
 */

void __fastcall stdext::_Hash<stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_CashShop_fld const *>>,0>>::_Hash<stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_CashShop_fld const *>>,0>>(stdext::_Hash<stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_CashShop_fld const *> >,0> > *this, stdext::hash_compare<int,std::less<int> > *_Parg, std::allocator<std::pair<int const ,_CashShop_fld const *> > *_Al)
{
  stdext::_Hash<stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_CashShop_fld const *>>,0>>::_Hash<stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_CashShop_fld const *>>,0>>(
    this,
    _Parg,
    _Al);
}
