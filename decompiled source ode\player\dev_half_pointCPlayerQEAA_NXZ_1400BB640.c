/*
 * Function: ?dev_half_point@CPlayer@@QEAA_NXZ
 * Address: 0x1400BB640
 */

char __fastcall CPlayer::dev_half_point(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed int v3; // eax@4
  __int64 v4; // r8@4
  int v5; // eax@4
  int v6; // eax@4
  int v7; // eax@4
  __int64 v9; // [sp+0h] [bp-38h]@1
  CGameObjectVtbl *v10; // [sp+20h] [bp-18h]@4
  CPlayer *v11; // [sp+40h] [bp+8h]@1

  v11 = this;
  v1 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = ((int (__fastcall *)(CPlayer *))v11->vfptr->GetHP)(v11);
  v10 = v11->vfptr;
  LOBYTE(v4) = 1;
  ((void (__fastcall *)(CPlayer *, _QWORD, __int64))v10->SetHP)(v11, v3 / 2, v4);
  v5 = CPlayer::GetFP(v11);
  CPlayer::SetFP(v11, v5 / 2, 1);
  v6 = CPlayer::GetSP(v11);
  CPlayer::SetSP(v11, v6 / 2, 1);
  v7 = CPlayer::GetDP(v11);
  CPlayer::SetDP(v11, v7 / 2, 1);
  CPlayer::SendMsg_Recover(v11);
  return 1;
}
