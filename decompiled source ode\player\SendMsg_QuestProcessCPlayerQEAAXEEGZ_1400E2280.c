/*
 * Function: ?SendMsg_QuestProcess@CPlayer@@QEAAXEEG@Z
 * Address: 0x1400E2280
 */

void __fastcall CPlayer::SendMsg_QuestProcess(CPlayer *this, char byQuestDBSlot, char byActIndex, unsigned __int16 wCount)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char v8; // [sp+35h] [bp-43h]@4
  unsigned __int16 v9; // [sp+36h] [bp-42h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v11; // [sp+55h] [bp-23h]@4
  CPlayer *v12; // [sp+80h] [bp+8h]@1

  v12 = this;
  v4 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = byQuestDBSlot;
  v8 = byActIndex;
  v9 = wCount;
  pbyType = 24;
  v11 = 6;
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, &szMsg, 4u);
}
