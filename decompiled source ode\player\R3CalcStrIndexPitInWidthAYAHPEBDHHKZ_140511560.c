/*
 * Function: ?R3CalcStrIndexPitInWidthA@@YAHPEBDHHK@Z
 * Address: 0x140511560
 */

int __fastcall R3CalcStrIndexPitInWidthA(const char *a1, int a2, int a3, int a4)
{
  int v4; // eax@1
  int v5; // er8@1
  const char *v6; // rdx@1
  int result; // eax@4

  v4 = a3;
  v5 = a2;
  v6 = a1;
  if ( v4 == -1 )
    v4 = strlen(a1);
  if ( _bittest(&a4, 0x1Eu) )
  {
    result = CR3Font::CalcStrIndexPitInWidthA((CR3Font *)&unk_184A85FE0, v6, v5, v4);
  }
  else if ( _bittest(&a4, 0x1Fu) )
  {
    result = CR3Font::CalcStrIndexPitInWidthA((CR3Font *)&unk_184A86810, v6, v5, v4);
  }
  else
  {
    result = CR3Font::CalcStrIndexPitInWidthA((CR3Font *)&unk_184A857B0, v6, v5, v4);
  }
  return result;
}
