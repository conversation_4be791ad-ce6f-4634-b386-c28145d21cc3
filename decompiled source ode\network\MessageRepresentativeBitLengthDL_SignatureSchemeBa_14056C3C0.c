/*
 * Function: ?MessageRepresentativeBitLength@?$DL_SignatureSchemeBase@VPK_Verifier@CryptoPP@@V?$DL_PublicKey@UEC2NPoint@CryptoPP@@@2@@CryptoPP@@IEBA_KXZ
 * Address: 0x14056C3C0
 */

__int64 __fastcall CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::DL_PublicKey<CryptoPP::EC2NPoint>>::MessageRepresentativeBitLength(__int64 a1)
{
  __int64 v1; // rax@1
  CryptoPP::Integer *v2; // rax@1

  LODWORD(v1) = CryptoPP::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::EC2NPoint>>::GetAbstractGroupParameters(a1 + 16);
  LODWORD(v2) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v1 + 64i64))(v1);
  return CryptoPP::Integer::BitCount(v2);
}
