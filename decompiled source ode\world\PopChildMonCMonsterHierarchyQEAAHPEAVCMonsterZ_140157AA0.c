/*
 * Function: ?PopChildMon@CMonsterHierarchy@@QEAAHPEAVCMonster@@@Z
 * Address: 0x140157AA0
 */

signed __int64 __fastcall CMonsterHierarchy::PopChildMon(CMonsterHierarchy *this, CMonster *pMon)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@5
  unsigned int k; // [sp+24h] [bp-14h]@7
  CMonsterHierarchy *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pMon )
  {
    for ( j = 0; j < 3; ++j )
    {
      for ( k = 0; k < 0xA; ++k )
      {
        if ( v8->m_pChildMon[j][k] == pMon )
        {
          CMonsterHierarchy::SetParent(&v8->m_pChildMon[j][k]->m_MonHierarcy, 0i64);
          v8->m_pChildMon[j][k] = 0i64;
          --v8->m_dwMonCount[j];
          --v8->m_dwTotalCount;
          return 1i64;
        }
      }
    }
  }
  return 0i64;
}
