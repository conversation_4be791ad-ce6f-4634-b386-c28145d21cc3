/*
 * Function: ?CheckSearch@CUnmannedTraderUserInfo@@AEAAEEPEAU_unmannedtrader_search_list_request_clzo@@AEAK1PEAVCLogFile@@@Z
 * Address: 0x14035B920
 */

char __fastcall CUnmannedTraderUserInfo::CheckSearch(CUnmannedTraderUserInfo *this, char byType, _unmannedtrader_search_list_request_clzo *pRequest, unsigned int *dwListIndex, unsigned int *dwCurVer, CLogFile *pkLogger)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CUnmannedTraderGroupItemInfoTable *v9; // rax@13
  CUnmannedTraderGroupItemInfoTable *v10; // rax@15
  __int64 v11; // [sp+0h] [bp-48h]@1
  char bySortType; // [sp+20h] [bp-28h]@13
  unsigned int *dwListIndexa; // [sp+28h] [bp-20h]@13
  CPlayer *p; // [sp+30h] [bp-18h]@6
  CUnmannedTraderUserInfo *v15; // [sp+50h] [bp+8h]@1
  _unmannedtrader_search_list_request_clzo *v16; // [sp+60h] [bp+18h]@1
  unsigned int *v17; // [sp+68h] [bp+20h]@1

  v17 = dwListIndex;
  v16 = pRequest;
  v15 = this;
  v6 = &v11;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( (signed int)v15->m_wInx < 2532 )
  {
    p = &g_Player + v15->m_wInx;
    if ( p->m_dwObjSerial == v15->m_dwUserSerial )
    {
      if ( CUnmannedTraderRequestLimiter::IsEmpty(&v15->m_kRequestState) )
      {
        if ( v16->bUseNpcLink || IsBeNearStore(p, -1) )
        {
          v9 = CUnmannedTraderGroupItemInfoTable::Instance();
          dwListIndexa = v17;
          bySortType = v16->bySortType;
          if ( CUnmannedTraderGroupItemInfoTable::IsExistGroupID(
                 v9,
                 v16->byDivision,
                 v16->byClass,
                 v16->bySubClass,
                 bySortType,
                 v17) )
          {
            v10 = CUnmannedTraderGroupItemInfoTable::Instance();
            if ( CUnmannedTraderGroupItemInfoTable::GetVersion(v10, v16->byDivision, v16->byClass, dwCurVer) )
            {
              if ( *dwCurVer == v16->dwVer )
                result = 10;
              else
                result = 0;
            }
            else
            {
              result = 51;
            }
          }
          else
          {
            result = 3;
          }
        }
        else
        {
          result = 2;
        }
      }
      else
      {
        result = 95;
      }
    }
    else
    {
      result = 99;
    }
  }
  else
  {
    result = 99;
  }
  return result;
}
