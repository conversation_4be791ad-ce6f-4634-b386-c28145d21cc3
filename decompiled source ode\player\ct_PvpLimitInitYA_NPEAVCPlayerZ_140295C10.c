/*
 * Function: ?ct_PvpLimitInit@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140295C10
 */

char __fastcall ct_PvpLimitInit(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CPvpPointLimiter *v3; // rax@4
  __int64 v5; // [sp+0h] [bp-48h]@1
  CPvpPointLimiter result; // [sp+20h] [bp-28h]@4
  __int64 v7; // [sp+28h] [bp-20h]@4
  CPvpPointLimiter *v8; // [sp+30h] [bp-18h]@4
  CPvpPointLimiter *v9; // [sp+38h] [bp-10h]@4
  CPlayer *v10; // [sp+50h] [bp+8h]@1

  v10 = pOne;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = -2i64;
  v3 = CPlayer::GetPvpPointLimiter(v10, &result);
  v8 = v3;
  v9 = v3;
  CPvpPointLimiter::CheatUpdate(v3, v10->m_Param.m_dbChar.m_dPvPPoint);
  CPvpPointLimiter::~CPvpPointLimiter(&result);
  return 1;
}
