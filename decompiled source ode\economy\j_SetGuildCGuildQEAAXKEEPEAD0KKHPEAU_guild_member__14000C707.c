/*
 * Function: j_?SetGuild@CGuild@@QEAAXKEEPEAD0KKHPEAU_guild_member_info@@NNKEHPEAU_io_money_data@@KKK@Z
 * Address: 0x14000C707
 */

void __fastcall CGuild::SetGuild(CGuild *this, unsigned int dwSerial, char byGrade, char byRace, char *pwszName, char *pwszGreetingMsg, unsigned int dwEmblemBack, unsigned int dwEmblemMark, int nNum, _guild_member_info *pEstMember, long double dTotalDalant, long double dTotalGold, unsigned int dwMasterSerial, char byMasterPrevGrade, int nIOMoneyHisNum, _io_money_data *pIOMonHisList, unsigned int dwGuildBattleTotalWinCnt, unsigned int dwGuildBattleTotalDrawCnt, unsigned int dwGuildBattleTotalLoseCnt)
{
  CGuild::SetGuild(
    this,
    dwSerial,
    byGrade,
    byRace,
    pwszName,
    pwszGreetingMsg,
    dwEmblemBack,
    dwEmblemMark,
    nNum,
    pEstMember,
    dTotalDalant,
    dTotalGold,
    dwMasterSerial,
    byMasterPrevGrade,
    nIOMoneyHisNum,
    pIOMonHisList,
    dwGuildBattleTotalWinCnt,
    dwGuildBattleTotalDrawCnt,
    dwGuildBattleTotalLoseCnt);
}
