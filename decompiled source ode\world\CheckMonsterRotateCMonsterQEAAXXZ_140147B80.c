/*
 * Function: ?CheckMonsterRotate@CMonster@@QEAAXXZ
 * Address: 0x140147B80
 */

void __fastcall CMonster::CheckMonsterRotate(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CMonster *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v4->m_bRotateMonster
    && v4->m_fCreatePos[0] == v4->m_fCurPos[0]
    && v4->m_fCreatePos[2] == v4->m_fCurPos[2]
    && (v4->m_fStartLookAtPos[0] != v4->m_fLookAtPos[0] || v4->m_fStartLookAtPos[2] != v4->m_fLookAtPos[2]) )
  {
    CMonster::UpdateLookAtPos(v4, v4->m_fStartLookAtPos);
    memcpy_0(v4->m_fLookAtPos, v4->m_fStartLookAtPos, 0xCui64);
    CMonster::SendMsg_Change_MonsterRotate(v4);
  }
}
