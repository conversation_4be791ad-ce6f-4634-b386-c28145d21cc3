/*
 * Function: ?init@si_interpret@@QEAAXXZ
 * Address: 0x1402E36B0
 */

void __fastcall si_interpret::init(si_interpret *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned __int8 j; // [sp+20h] [bp-18h]@4
  si_interpret *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->byEffectTypeCount = 0;
  for ( j = 0; (signed int)j < 8; ++j )
    si_effect::init(&v5->effect_type[j]);
}
