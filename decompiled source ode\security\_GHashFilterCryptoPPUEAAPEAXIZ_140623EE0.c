/*
 * Function: ??_GHash<PERSON>ilter@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x140623EE0
 */

CryptoPP::HashFilter *__fastcall CryptoPP::HashFilter::`scalar deleting destructor'(CryptoPP::HashFilter *a1, int a2)
{
  CryptoPP::HashFilter *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::HashFilter::~HashFilter(a1);
  if ( v4 & 1 )
    operator delete(v3);
  return v3;
}
