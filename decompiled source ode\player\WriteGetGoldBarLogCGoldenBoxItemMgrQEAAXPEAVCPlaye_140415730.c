/*
 * Function: ?WriteGetGoldBarLog@CGoldenBoxItemMgr@@QEAAXPEAVCPlayer@@PEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x140415730
 */

void __fastcall CGoldenBoxItemMgr::WriteGetGoldBarLog(CGoldenBoxItemMgr *this, CPlayer *pOne, _STORAGE_LIST::_db_con *pItem)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CAsyncLogger *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-98h]@1
  unsigned int v7; // [sp+20h] [bp-78h]@4
  char *v8; // [sp+28h] [bp-70h]@4
  unsigned int v9; // [sp+30h] [bp-68h]@4
  char *v10; // [sp+38h] [bp-60h]@4
  char *v11; // [sp+40h] [bp-58h]@4
  _base_fld *v12; // [sp+50h] [bp-48h]@4
  char *v13; // [sp+58h] [bp-40h]@4
  char *v14; // [sp+60h] [bp-38h]@4
  CUserDB *v15; // [sp+68h] [bp-30h]@4
  char *v16; // [sp+70h] [bp-28h]@4
  unsigned int v17; // [sp+78h] [bp-20h]@4
  char *v18; // [sp+80h] [bp-18h]@4
  CPlayer *v19; // [sp+A8h] [bp+10h]@1
  _STORAGE_LIST::_db_con *v20; // [sp+B0h] [bp+18h]@1

  v20 = pItem;
  v19 = pOne;
  v3 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
  v13 = GetItemKorName(v20->m_byTableCode, v20->m_wItemIndex);
  v14 = v12->m_strCode;
  v15 = v19->m_pUserDB;
  v16 = v19->m_pUserDB->m_szAccountID;
  v17 = CPlayerDB::GetCharSerial(&v19->m_Param);
  v18 = CPlayerDB::GetCharNameA(&v19->m_Param);
  v5 = CAsyncLogger::Instance();
  v11 = v13;
  v10 = v14;
  v9 = v15->m_dwAccountSerial;
  v8 = v16;
  v7 = v17;
  CAsyncLogger::FormatLog(v5, 13, "Name: %s(%d), AccountID: %s(%d), Item: %s(%s) ", v18);
}
