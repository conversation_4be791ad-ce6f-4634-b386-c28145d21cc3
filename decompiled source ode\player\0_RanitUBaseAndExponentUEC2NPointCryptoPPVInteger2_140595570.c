/*
 * Function: ??0?$_Ranit@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@_JPEBU12@AEBU12@@std@@QEAA@AEBU01@@Z
 * Address: 0x140595570
 */

std::_Iterator_base *__fastcall std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,__int64,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const &>::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,__int64,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const &>(std::_Iterator_base *a1, std::_Iterator_base *a2)
{
  std::_Iterator_base *v3; // [sp+30h] [bp+8h]@1

  v3 = a1;
  std::_Iterator_base::_Iterator_base(a1, a2);
  return v3;
}
