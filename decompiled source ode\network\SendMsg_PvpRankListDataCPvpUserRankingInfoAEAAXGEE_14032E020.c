/*
 * Function: ?SendMsg_PvpRankListData@CPvpUserRankingInfo@@AEAAXGEEE@Z
 * Address: 0x14032E020
 */

void __usercall CPvpUserRankingInfo::SendMsg_PvpRankListData(CPvpUserRankingInfo *this@<rcx>, unsigned __int16 wIndex@<dx>, char by<PERSON><PERSON>@<r8b>, char by<PERSON><PERSON><PERSON>@<r9b>, signed __int64 a5@<rax>, char byPage)
{
  void *v6; // rsp@1
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v9; // ax@9
  __int64 v10; // [sp-20h] [bp-1048h]@1
  _PVP_RANK_PACKED_DATA *v11; // [sp+10h] [bp-1018h]@6
  _pvp_rank_list_result_data_zocl Dst; // [sp+30h] [bp-FF8h]@9
  char pbyType; // [sp+FF4h] [bp-34h]@9
  char v14; // [sp+FF5h] [bp-33h]@9
  unsigned __int64 v15; // [sp+1010h] [bp-18h]@4
  CPvpUserRankingInfo *v16; // [sp+1030h] [bp+8h]@1
  unsigned __int16 v17; // [sp+1038h] [bp+10h]@1
  char v18; // [sp+1040h] [bp+18h]@1
  char v19; // [sp+1048h] [bp+20h]@1

  v19 = byVersion;
  v18 = byRace;
  v17 = wIndex;
  v16 = this;
  v6 = alloca(a5);
  v7 = &v10;
  for ( i = 1040i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v15 = (unsigned __int64)&v10 ^ _security_cookie;
  if ( (signed int)(unsigned __int8)byRace < 3 && (signed int)(unsigned __int8)byPage < 10 )
  {
    v11 = &(*std::vector<_PVP_RANK_PACKED_DATA *,std::allocator<_PVP_RANK_PACKED_DATA *>>::operator[](
               &v16->m_vecPackedRankData,
               (unsigned __int8)byRace))[(unsigned __int8)byPage];
    if ( v11 )
    {
      if ( !v11->wDataLen )
        CPvpUserRankingInfo::SendMsg_PvpRankListNodata(v16, v17, v18, byPage, 1);
      _pvp_rank_list_result_data_zocl::_pvp_rank_list_result_data_zocl(&Dst);
      Dst.byRace = v18;
      Dst.byVersion = v19;
      Dst.byPage = byPage;
      Dst.wRankDataLen = v11->wDataLen;
      memcpy_0(Dst.szPvpRankData, v11->szPackedData, v11->wDataLen);
      pbyType = 13;
      v14 = 20;
      v9 = _pvp_rank_list_result_data_zocl::size(&Dst);
      CNetProcess::LoadSendMsg(unk_1414F2088, v17, &pbyType, &Dst.byRace, v9);
    }
  }
}
