/*
 * Function: ??$_Push_heap@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@_JUMessageRange@MeterFilter@CryptoPP@@@std@@YAXV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@_J1UMessageRange@MeterFilter@CryptoPP@@@Z
 * Address: 0x1406057B0
 */

int __fastcall std::_Push_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,__int64,CryptoPP::MeterFilter::MessageRange>(__int64 a1, __int64 a2, __int64 a3, const void *a4)
{
  __int64 v4; // rax@3
  const void *v5; // rax@9
  char *v6; // rax@9
  __int64 v7; // rax@10
  char *v8; // rax@10
  __int64 i; // [sp+20h] [bp-138h]@1
  char v11; // [sp+28h] [bp-130h]@6
  char v12; // [sp+30h] [bp-128h]@3
  char v13; // [sp+50h] [bp-108h]@9
  char v14; // [sp+70h] [bp-E8h]@9
  char v15; // [sp+90h] [bp-C8h]@10
  int v16; // [sp+B0h] [bp-A8h]@1
  char v17; // [sp+B8h] [bp-A0h]@10
  char v18; // [sp+D0h] [bp-88h]@9
  __int64 v19; // [sp+E8h] [bp-70h]@1
  __int64 v20; // [sp+F0h] [bp-68h]@3
  __int64 v21; // [sp+F8h] [bp-60h]@3
  int v22; // [sp+100h] [bp-58h]@4
  __int64 v23; // [sp+108h] [bp-50h]@9
  __int64 v24; // [sp+110h] [bp-48h]@9
  __int64 v25; // [sp+118h] [bp-40h]@9
  __int64 v26; // [sp+120h] [bp-38h]@9
  __int64 v27; // [sp+128h] [bp-30h]@10
  __int64 v28; // [sp+130h] [bp-28h]@10
  __int64 v29; // [sp+160h] [bp+8h]@1
  __int64 v30; // [sp+168h] [bp+10h]@1
  __int64 v31; // [sp+170h] [bp+18h]@1
  const void *v32; // [sp+178h] [bp+20h]@1

  v32 = a4;
  v31 = a3;
  v30 = a2;
  v29 = a1;
  v19 = -2i64;
  v16 = 0;
  for ( i = (a2 - 1) / 2; ; i = (i - 1) / 2 )
  {
    v22 = v31 < v30
       && (v20 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
                   v29,
                   (__int64)&v12,
                   i),
           v21 = v20,
           v16 |= 1u,
           LODWORD(v4) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*(),
           CryptoPP::MeterFilter::MessageRange::operator<(v4, (__int64)v32));
    v11 = v22;
    if ( v16 & 1 )
    {
      v16 &= 0xFFFFFFFE;
      std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
    }
    if ( !v11 )
      break;
    v23 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
            v29,
            (__int64)&v14,
            i);
    v24 = v23;
    LODWORD(v5) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
    qmemcpy(&v18, v5, 0x18ui64);
    v25 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
            v29,
            (__int64)&v13,
            v30);
    v26 = v25;
    LODWORD(v6) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
    qmemcpy(v6, &v18, 0x18ui64);
    std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
    std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
    v30 = i;
  }
  qmemcpy(&v17, v32, 0x18ui64);
  v7 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
         v29,
         (__int64)&v15,
         v30);
  v27 = v7;
  v28 = v7;
  LODWORD(v8) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
  qmemcpy(v8, &v17, 0x18ui64);
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
}
