/*
 * Function: ?InitializeTLMgr@TimeLimitMgr@@QEAAXXZ
 * Address: 0x14040D4E0
 */

void __fastcall TimeLimitMgr::InitializeTLMgr(TimeLimitMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  int k; // [sp+24h] [bp-14h]@7
  TimeLimitMgr *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6->m_dwPlayFDegree = 60000 * v6->m_pwTime[v6->m_wPeriodCnt - 1] / 100;
  v6->m_dwLogoutFDegree = 60000 * v6->m_dwLogoutTerm / 0x64;
  CMyTimer::BeginTimer(&v6->m_tmLoopTime, v6->m_dwPlayFDegree);
  for ( j = 0; j < 2532; ++j )
    memset_0(&v6->m_lstTLStaus[j], 0, 0x28ui64);
  for ( k = 0; k < 2532; ++k )
    v6->m_lstTLStaus[k].m_bUpdateLogout = 1;
}
