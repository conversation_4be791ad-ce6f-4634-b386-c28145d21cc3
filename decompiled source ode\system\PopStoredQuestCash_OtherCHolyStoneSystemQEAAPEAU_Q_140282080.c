/*
 * Function: ?PopStoredQuestCash_Other@CHolyStoneSystem@@QEAAPEAU_QUEST_CASH_OTHER@@K@Z
 * Address: 0x140282080
 */

_QUEST_CASH_OTHER *__fastcall CHolyStoneSystem::PopStoredQuestCash_Other(CHolyStoneSystem *this, unsigned int dwAvatorSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CHolyStoneSystem *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 5064; ++j )
  {
    if ( v7->m_cashQuestOther[j].dwAvatorSerial == dwAvatorSerial )
    {
      _QUEST_CASH_OTHER::init(&v7->m_cashQuestOther[j]);
      return &v7->m_cashQuestOther[j];
    }
  }
  return 0i64;
}
