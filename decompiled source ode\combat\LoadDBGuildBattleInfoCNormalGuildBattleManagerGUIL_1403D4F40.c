/*
 * Function: ?LoadDBGuildBattleInfo@CNormalGuildBattleManager@GUILD_BATTLE@@IEAA_NKAEAU_worlddb_guild_battle_info@@@Z
 * Address: 0x1403D4F40
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleManager::LoadDBGuildBattleInfo(GUILD_BATTLE::CNormalGuildBattleManager *this, unsigned int dwStartID, _worlddb_guild_battle_info *kInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v5; // rax@5
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleManager *v8; // [sp+30h] [bp+8h]@1
  unsigned int dwStartIDa; // [sp+38h] [bp+10h]@1
  _worlddb_guild_battle_info *Dst; // [sp+40h] [bp+18h]@1

  Dst = kInfo;
  dwStartIDa = dwStartID;
  v8 = this;
  v3 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  memset_0(kInfo, 0, 0x568ui64);
  if ( CRFWorldDatabase::LoadGuildBattleInfo(pkDB, dwStartIDa, 23 * v8->m_uiMapCnt, Dst) )
  {
    result = 1;
  }
  else
  {
    v5 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v5,
      "CNormalGuildBattleManager::LoadDBGuildBattleInfo() : g_Main.m_pWorldDB->LoadGuildBattleInfo() Fail!");
    result = 0;
  }
  return result;
}
