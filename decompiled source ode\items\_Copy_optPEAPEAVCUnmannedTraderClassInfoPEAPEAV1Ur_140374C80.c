/*
 * Function: ??$_Copy_opt@PEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@Urandom_access_iterator_tag@std@@@std@@YAPEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@00Urandom_access_iterator_tag@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140374C80
 */

CUnmannedTraderClassInfo **__fastcall std::_Copy_opt<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *,std::random_access_iterator_tag>(CUnmannedTraderClassInfo **_First, CUnmannedTraderClassInfo **_Last, CUnmannedTraderClassInfo **_Dest, std::random_access_iterator_tag __formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-38h]@1
  __int64 v10; // [sp+20h] [bp-18h]@4
  CUnmannedTraderClassInfo **v11; // [sp+28h] [bp-10h]@4
  CUnmannedTraderClassInfo **Src; // [sp+40h] [bp+8h]@1

  Src = _First;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10 = _Last - Src;
  v11 = &_Dest[v10];
  if ( v10 > 0 )
    memmove_s(_Dest, 8 * v10, Src, 8 * v10);
  return v11;
}
