/*
 * Function: ?DrawDisplay@CMapDisplay@@QEAAXXZ
 * Address: 0x14019F260
 */

void __fastcall CMapDisplay::DrawDisplay(CMapDisplay *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  tagRECT *prc; // [sp+20h] [bp-18h]@6
  CMapDisplay *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5->m_bDisplayMode )
  {
    if ( CMyTimer::CountingTimer(&v5->m_tmrDraw) )
    {
      CDisplay::UpdateBounds((CDisplay *)&v5->vfptr);
      CMapDisplay::DrawMap(v5);
      CMapDisplay::DrawCollisionLine(v5);
      CMapDisplay::DrawDummy(v5);
      CMapDisplay::DrawObject(v5);
      CMapDisplay::DrawTextA(v5);
      CMapExtend::DrawRect(&v5->m_MapExtend);
      prc = 0i64;
      CDisplay::Blt((CDisplay *)&v5->vfptr, 0, 0, v5->m_pSFMap, 0i64);
      CDisplay::Present((CDisplay *)&v5->vfptr);
    }
  }
}
