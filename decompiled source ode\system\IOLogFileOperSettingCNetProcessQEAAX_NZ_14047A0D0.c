/*
 * Function: ?IOLogFileOperSetting@CNetProcess@@QEAAX_N@Z
 * Address: 0x14047A0D0
 */

void __fastcall CNetProcess::IOLogFileOperSetting(CNetProcess *this, bool bOper)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CNetProcess *v5; // [sp+30h] [bp+8h]@1
  bool v6; // [sp+38h] [bp+10h]@1

  v6 = bOper;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CLogFile::SetWriteAble(v5->m_LogFile, bOper);
  CLogFile::SetWriteAble(&v5->m_LogFile[1], v6);
}
