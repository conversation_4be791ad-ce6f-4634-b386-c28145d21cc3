/*
 * Function: ??0_reged_char_result_zone@@QEAA@XZ
 * Address: 0x14011F680
 */

void __fastcall _reged_char_result_zone::_reged_char_result_zone(_reged_char_result_zone *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _reged_char_result_zone *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  `vector constructor iterator'(
    v4->RegedList,
    0x45ui64,
    3,
    (void *(__cdecl *)(void *))_REGED_AVATOR_DB::_REGED_AVATOR_DB);
  v4->byCharNum = 0;
}
