/*
 * Function: ?_check_equip_part@CPlayer@@QEAA_NPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1400FF2D0
 */

bool __fastcall CPlayer::_check_equip_part(CPlayer *this, _STORAGE_LIST::_db_con *pFixingItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@4
  int v5; // ecx@4
  bool result; // al@5
  __int64 v7; // [sp+0h] [bp-68h]@1
  _base_fld *v8; // [sp+20h] [bp-48h]@9
  int v9; // [sp+28h] [bp-40h]@9
  char v10; // [sp+2Ch] [bp-3Ch]@12
  int j; // [sp+30h] [bp-38h]@12
  __int64 v12; // [sp+38h] [bp-30h]@14
  char *v13; // [sp+40h] [bp-28h]@22
  _base_fld *v14; // [sp+48h] [bp-20h]@23
  int nTableCode; // [sp+50h] [bp-18h]@4
  CPlayer *v16; // [sp+70h] [bp+8h]@1
  _STORAGE_LIST::_db_con *pCon; // [sp+78h] [bp+10h]@1

  pCon = pFixingItem;
  v16 = this;
  v2 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = CPlayerDB::GetRaceSexCode(&v16->m_Param);
  v5 = pCon->m_wItemIndex;
  nTableCode = pCon->m_byTableCode;
  if ( !IsItemEquipCivil(nTableCode, v5, v4) )
    return 0;
  if ( !CPlayer::IsEffectableEquip(v16, (_STORAGE_LIST::_storage_con *)&pCon->m_bLoad) )
    return 0;
  if ( pCon->m_byTableCode != 6 )
    goto LABEL_28;
  v8 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 6, pCon->m_wItemIndex);
  v9 = *(_DWORD *)&v8[4].m_strCode[12];
  if ( v9 == 100 && v16->m_Param.m_dbEquip.m_pStorageList[5].m_bLoad )
    return 0;
  v10 = 0;
  for ( j = 0; j < 4; ++j )
  {
    v12 = (__int64)*v16->m_Param.m_ppHistoryEffect[j];
    if ( !v12 || *(_DWORD *)(v12 + 72) >= 4u )
      break;
    if ( v8[7].m_strCode[*(_DWORD *)(v12 + 72) + 12] == 49 )
    {
      v10 = 1;
      break;
    }
  }
  if ( v10 )
  {
LABEL_28:
    result = 1;
    if ( pCon->m_byTableCode == 5 )
    {
      v13 = &v16->m_Param.m_dbEquip.m_pStorageList[6].m_bLoad;
      if ( *v13 )
      {
        v14 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 6, *(_WORD *)(v13 + 3));
        if ( *(_DWORD *)&v14[4].m_strCode[12] == 100 )
          result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
