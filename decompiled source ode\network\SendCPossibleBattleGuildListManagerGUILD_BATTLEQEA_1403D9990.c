/*
 * Function: ?Send@CPossibleBattleGuildListManager@GUILD_BATTLE@@QEAAXHEEK@Z
 * Address: 0x1403D9990
 */

void __fastcall GUILD_BATTLE::CPossibleBattleGuildListManager::Send(GUILD_BATTLE::CPossibleBattleGuildListManager *this, int n, char byRace, char byPage, unsigned int dwVer)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  char v8; // [sp+30h] [bp-18h]@4
  GUILD_BATTLE::CPossibleBattleGuildListManager *v9; // [sp+50h] [bp+8h]@1
  int na; // [sp+58h] [bp+10h]@1

  na = n;
  v9 = this;
  v5 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v8 = GUILD_BATTLE::CPossibleBattleGuildListManager::SendInfo(v9, n, byRace, byPage, dwVer);
  if ( v8 )
  {
    if ( v8 == 1 )
      v8 = 0;
    GUILD_BATTLE::CPossibleBattleGuildListManager::SendErrorResult(v9, na, v8);
  }
}
