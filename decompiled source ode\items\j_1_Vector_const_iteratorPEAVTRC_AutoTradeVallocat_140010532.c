/*
 * Function: j_??1?$_Vector_const_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAA@XZ
 * Address: 0x140010532
 */

void __fastcall std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::~_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this)
{
  std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::~_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(this);
}
