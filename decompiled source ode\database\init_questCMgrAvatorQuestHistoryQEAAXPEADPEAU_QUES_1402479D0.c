/*
 * Function: ?init_quest@CMgrAvatorQuestHistory@@QEAAXPEADPEAU_QUEST_DB_BASE@@0@Z
 * Address: 0x1402479D0
 */

void __fastcall CMgrAvatorQuestHistory::init_quest(CMgrAvatorQuestHistory *this, char *pszAvatorName, _QUEST_DB_BASE *pQuestDB, char *pszFileName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  unsigned int j; // [sp+20h] [bp-28h]@4
  _QUEST_DB_BASE *v8; // [sp+28h] [bp-20h]@7
  _base_fld *v9; // [sp+30h] [bp-18h]@8
  CMgrAvatorQuestHistory *v10; // [sp+50h] [bp+8h]@1
  _QUEST_DB_BASE *v11; // [sp+60h] [bp+18h]@1
  char *pszFileNamea; // [sp+68h] [bp+20h]@1

  pszFileNamea = pszFileName;
  v11 = pQuestDB;
  v10 = this;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  sData_1[0] = 0;
  sprintf(sBuf_1, "%s [%s]\n", pszAvatorName, v10->m_szCurTime);
  strcat_0(sData_1, sBuf_1);
  sprintf(sBuf_1, "START\n");
  strcat_0(sData_1, sBuf_1);
  for ( j = 0; (signed int)j < 30; ++j )
  {
    v8 = (_QUEST_DB_BASE *)((char *)v11 + 13 * (signed int)j);
    if ( v8->m_List[0].byQuestType != 255 )
    {
      v9 = CRecordData::GetRecord(CQuestMgr::s_tblQuest, v8->m_List[0].wIndex);
      if ( v9 )
      {
        sprintf(sBuf_1, "\t%d: %s\n", j, v9->m_strCode);
        strcat_0(sData_1, sBuf_1);
      }
    }
  }
  sprintf(sBuf_1, "\n");
  strcat_0(sData_1, sBuf_1);
  CMgrAvatorQuestHistory::WriteFile(v10, pszFileNamea, sData_1);
}
