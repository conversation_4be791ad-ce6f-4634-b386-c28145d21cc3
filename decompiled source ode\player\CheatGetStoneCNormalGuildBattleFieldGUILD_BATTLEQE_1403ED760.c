/*
 * Function: ?CheatGetStone@CNormalGuildBattleField@GUILD_BATTLE@@QEAAEPEAVCPlayer@@@Z
 * Address: 0x1403ED760
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleField::CheatGetStone(GUILD_BATTLE::CNormalGuildBattleField *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v6->m_bInit )
    result = CGravityStone::CheatGet(v6->m_pkBall, pkPlayer);
  else
    result = 0;
  return result;
}
