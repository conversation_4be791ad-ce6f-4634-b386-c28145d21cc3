/*
 * Function: ??Y?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEAAAEAV01@_J@Z
 * Address: 0x14031E980
 */

std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator+=(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this, __int64 _Off)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator+=(
    (std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v6->_Mycont,
    _Off);
  return v6;
}
