/*
 * Function: ?SendMsg_ManageGuildCommitteeResult@CGuild@@QEAAX_NPEAD@Z
 * Address: 0x140259B00
 */

void __fastcall CGuild::SendMsg_ManageGuildCommitteeResult(CGuild *this, bool bAppoint, char *pwszCommitteeName)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-98h]@1
  char szMsg; // [sp+38h] [bp-60h]@4
  char Dest; // [sp+39h] [bp-5Fh]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v9; // [sp+65h] [bp-33h]@4
  int j; // [sp+74h] [bp-24h]@4
  _guild_member_info *v11; // [sp+78h] [bp-20h]@7
  unsigned __int64 v12; // [sp+88h] [bp-10h]@4
  CGuild *v13; // [sp+A0h] [bp+8h]@1

  v13 = this;
  v3 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = (unsigned __int64)&v5 ^ _security_cookie;
  szMsg = bAppoint;
  strcpy_0(&Dest, pwszCommitteeName);
  pbyType = 27;
  v9 = 125;
  for ( j = 0; j < 50; ++j )
  {
    v11 = &v13->m_MemberData[j];
    if ( _guild_member_info::IsFill(v11) )
    {
      if ( v11->pPlayer )
        CNetProcess::LoadSendMsg(unk_1414F2088, v11->pPlayer->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x12u);
    }
  }
}
