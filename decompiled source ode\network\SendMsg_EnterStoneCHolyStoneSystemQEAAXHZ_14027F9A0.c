/*
 * Function: ?SendMsg_EnterStone@CHolyStoneSystem@@QEAAXH@Z
 * Address: 0x14027F9A0
 */

void __fastcall CHolyStoneSystem::SendMsg_EnterStone(CHolyStoneSystem *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-98h]@1
  CHolyScheduleData::__HolyScheduleNode *v5; // [sp+30h] [bp-68h]@4
  unsigned int v6; // [sp+38h] [bp-60h]@5
  char szMsg; // [sp+48h] [bp-50h]@5
  __int16 v8; // [sp+49h] [bp-4Fh]@5
  __int16 v9; // [sp+4Bh] [bp-4Dh]@6
  __int16 v10; // [sp+4Dh] [bp-4Bh]@9
  __int16 v11; // [sp+4Fh] [bp-49h]@11
  unsigned int v12; // [sp+64h] [bp-34h]@5
  char pbyType; // [sp+74h] [bp-24h]@11
  char v14; // [sp+75h] [bp-23h]@11
  unsigned int dwClientIndex; // [sp+84h] [bp-14h]@12
  CHolyStoneSystem *v16; // [sp+A0h] [bp+8h]@1
  int v17; // [sp+A8h] [bp+10h]@1

  v17 = n;
  v16 = this;
  v2 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = CHolyScheduleData::GetIndex(&v16->m_ScheculeData, v16->m_SaveData.m_byNumOfTime);
  if ( v5 )
  {
    v6 = GetLoopTime();
    szMsg = v17 == -1;
    v8 = (v16->m_dwCheckTime[1] - v16->m_dwCheckTime[0]) / 0x3E8;
    v12 = v16->m_dwCheckTime[1] - v5->m_nSceneTime[0];
    if ( v12 <= v6 )
      v9 = -(v6 - v12) / 0x3E8;
    else
      v9 = (v12 - v6) / 0x3E8;
    if ( v16->m_dwCheckTime[1] <= v6 )
      v10 = -(v6 - v16->m_dwCheckTime[1]) / 0x3E8;
    else
      v10 = (v16->m_dwCheckTime[1] - v6) / 0x3E8;
    v11 = (timeGetTime() - v16->m_dwCheckTime[0]) / 0x3E8;
    pbyType = 25;
    v14 = 1;
    if ( v17 == -1 )
    {
      for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
      {
        if ( *(&g_Player.m_bLive + 50856 * (signed int)dwClientIndex) )
          CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &szMsg, 9u);
      }
    }
    else
    {
      CNetProcess::LoadSendMsg(unk_1414F2088, v17, &pbyType, &szMsg, 9u);
    }
  }
}
