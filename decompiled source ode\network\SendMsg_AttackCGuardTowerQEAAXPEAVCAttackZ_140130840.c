/*
 * Function: ?SendMsg_Attack@CGuardTower@@QEAAXPEAVCAttack@@@Z
 * Address: 0x140130840
 */

void __fastcall CGuardTower::SendMsg_Attack(CGuardTower *this, CAttack *pAt)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-88h]@1
  char szMsg[4]; // [sp+38h] [bp-50h]@4
  char v6; // [sp+3Ch] [bp-4Ch]@4
  bool v7; // [sp+3Dh] [bp-4Bh]@4
  char v8; // [sp+3Eh] [bp-4Ah]@4
  unsigned int v9; // [sp+3Fh] [bp-49h]@4
  __int16 v10; // [sp+43h] [bp-45h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v12; // [sp+65h] [bp-23h]@4
  CGuardTower *v13; // [sp+90h] [bp+8h]@1

  v13 = this;
  v2 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  *(_DWORD *)szMsg = v13->m_dwObjSerial;
  v6 = pAt->m_pp->nPart;
  v7 = pAt->m_bIsCrtAtt;
  v8 = pAt->m_DamList[0].m_pChar->m_ObjID.m_byID;
  v9 = pAt->m_DamList[0].m_pChar->m_dwObjSerial;
  v10 = pAt->m_DamList[0].m_nDamage;
  pbyType = 5;
  v12 = 15;
  CGameObject::CircleReport((CGameObject *)&v13->vfptr, &pbyType, szMsg, 13, 0);
}
