/*
 * Function: ??0?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEAA@XZ
 * Address: 0x1403A2060
 */

void __fastcall std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<CMoveMapLimitInfo *> v3; // al@4
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  std::allocator<CMoveMapLimitInfo *> *v6; // [sp+28h] [bp-10h]@4
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (std::allocator<CMoveMapLimitInfo *> *)&v5;
  std::allocator<CMoveMapLimitInfo *>::allocator<CMoveMapLimitInfo *>((std::allocator<CMoveMapLimitInfo *> *)&v5);
  std::_Vector_val<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Vector_val<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(
    (std::_Vector_val<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)&v7->_Myfirstiter,
    v3);
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Buy(v7, 0i64);
}
