/*
 * Function: j_??$_Umove@PEAVCUnmannedTraderGroupDivisionVersionInfo@@@?$vector@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@IEAAPEAVCUnmannedTraderGroupDivisionVersionInfo@@PEAV2@00@Z
 * Address: 0x14000BD2F
 */

CUnmannedTraderGroupDivisionVersionInfo *__fastcall std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Umove<CUnmannedTraderGroupDivisionVersionInfo *>(std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *this, CUnmannedTraderGroupDivisionVersionInfo *_First, CUnmannedTraderGroupDivisionVersionInfo *_Last, CUnmannedTraderGroupDivisionVersionInfo *_Ptr)
{
  return std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Umove<CUnmannedTraderGroupDivisionVersionInfo *>(
           this,
           _First,
           _Last,
           _Ptr);
}
