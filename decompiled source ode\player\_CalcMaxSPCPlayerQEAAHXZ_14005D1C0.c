/*
 * Function: ?_<PERSON><PERSON><PERSON>ax<PERSON>@CPlayer@@QEAAHXZ
 * Address: 0x14005D1C0
 */

__int64 __fastcall CPlayer::_CalcMaxSP(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  float v4; // xmm0_4@4
  float v5; // xmm0_4@4
  __int64 v7; // [sp+0h] [bp-38h]@1
  float v8; // [sp+20h] [bp-18h]@4
  float v9; // [sp+24h] [bp-14h]@4
  float v10; // [sp+28h] [bp-10h]@4
  CPlayer *v11; // [sp+40h] [bp+8h]@1

  v11 = this;
  v1 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = (float)_MASTERY_PARAM::GetMasteryPerMast(&v11->m_pmMst, 0, 0) * 0.2;
  v3 = _MASTERY_PARAM::GetMasteryPerMast(&v11->m_pmMst, 0, 1);
  v8 = (float)(v9 + (float)((float)v3 * 0.80000001)) + (float)v11->m_nAddPointByClass[2];
  v10 = (float)CPlayerDB::GetLevel(&v11->m_Param);
  v4 = v8;
  pow(v8, 2);
  v5 = v10 * v4;
  sqrt(v5);
  return (unsigned int)(signed int)ffloor((float)(v5 * 2.5) + 160.0);
}
