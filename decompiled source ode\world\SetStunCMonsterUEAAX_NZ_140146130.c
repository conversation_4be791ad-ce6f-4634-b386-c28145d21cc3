/*
 * Function: ?SetStun@CMonster@@UEAAX_N@Z
 * Address: 0x140146130
 */

void __fastcall CMonster::SetStun(CMonster *this, bool bStun)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float v4; // xmm0_4@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMonster *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6->m_bStun = bStun;
  if ( v6->m_bStun )
  {
    v4 = (float)(signed int)(GetLoopTime() + 1000) + v6->m_pMonRec->m_fCrtMoTime;
    v6->m_dwNextFreeStunTime = (signed int)ffloor(v4);
    if ( v6->m_bMove )
    {
      CMonster::GetMoveSpeed(v6);
      CCharacter::MoveBreak((CCharacter *)&v6->vfptr, v4);
      CCharacter::Stop((CCharacter *)&v6->vfptr);
    }
  }
}
