/*
 * Function: ?pc_SetItemCheckRequest@CPlayer@@QEAA_NKEE_N@Z
 * Address: 0x1400B47F0
 */

char __fastcall CPlayer::pc_SetItemCheckRequest(CPlayer *this, unsigned int dwSetItem, char bySetItemNum, char bySetEffectNum, bool bSet)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  CSUItemSystem *v7; // rax@4
  char result; // al@5
  CSUItemSystem *v9; // rax@9
  unsigned int v10; // eax@15
  __int64 v11; // [sp+0h] [bp-C8h]@1
  CSetItemType *v12; // [sp+30h] [bp-98h]@4
  si_interpret *pSI; // [sp+38h] [bp-90h]@4
  int v14; // [sp+40h] [bp-88h]@8
  char v15; // [sp+44h] [bp-84h]@9
  int nTableCode; // [sp+48h] [bp-80h]@9
  char pStrCode; // [sp+60h] [bp-68h]@9
  char v18; // [sp+61h] [bp-67h]@9
  char v19; // [sp+A4h] [bp-24h]@10
  int v20; // [sp+B0h] [bp-18h]@13
  char v21; // [sp+B4h] [bp-14h]@15
  char v22; // [sp+B5h] [bp-13h]@15
  unsigned __int64 v23; // [sp+B8h] [bp-10h]@4
  CPlayer *v24; // [sp+D0h] [bp+8h]@1
  int set_pos; // [sp+D8h] [bp+10h]@1
  char v26; // [sp+E0h] [bp+18h]@1
  char v27; // [sp+E8h] [bp+20h]@1

  v27 = bySetEffectNum;
  v26 = bySetItemNum;
  set_pos = dwSetItem;
  v24 = this;
  v5 = &v11;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v23 = (unsigned __int64)&v11 ^ _security_cookie;
  v12 = 0i64;
  pSI = 0i64;
  v7 = CSUItemSystem::Instance();
  v12 = CSUItemSystem::GetCSetItemType(v7);
  if ( v12 )
  {
    pSI = CSetItemType::Getsi_interpret(v12, set_pos);
    if ( pSI )
    {
      v14 = 9;
      if ( bSet )
      {
        v15 = 0;
        pStrCode = 0;
        memset(&v18, 0, 0x3Fui64);
        v9 = CSUItemSystem::Instance();
        nTableCode = CSUItemSystem::GetSetItemTableInfo(v9, set_pos, &pStrCode, 64);
        if ( nTableCode > -1 )
        {
          v19 = GetItemEquipGrade(nTableCode, &pStrCode);
          if ( CPlayer::IsEquipAbleGrade(v24, v19) )
            v15 = 1;
        }
        if ( v15 )
        {
          v14 = CSetItemEffect::SetOnEffect(&v24->m_clsSetItem, &v24->m_pUserDB->m_AvatorData, set_pos, v26, v27);
          v20 = v14;
          switch ( v14 )
          {
            case 0:
              CPlayer::ApplySetItemEffect(v24, pSI, set_pos, v26, v27, 1);
              break;
            case 8:
              v21 = CSetItemEffect::GetResetEffectNum(&v24->m_clsSetItem);
              v22 = CSetItemEffect::GetResetItemNum(&v24->m_clsSetItem);
              v10 = CSetItemEffect::GetResetIdx(&v24->m_clsSetItem);
              CPlayer::ApplySetItemEffect(v24, pSI, v10, v22, v21, 0);
              CPlayer::ApplySetItemEffect(v24, pSI, set_pos, v26, v27, 1);
              break;
            case 4:
            case 3:
            case 2:
            case 7:
              break;
            default:
              v14 = 9;
              break;
          }
        }
      }
      else
      {
        v14 = CSetItemEffect::SetOffEffect(&v24->m_clsSetItem, set_pos, v26, v27);
        if ( v14 == 1 )
          CPlayer::ApplySetItemEffect(v24, pSI, set_pos, v26, v27, 0);
      }
      CPlayer::SendMsg_SetItemCheckResult(v24, v14, set_pos, v27);
      result = 1;
    }
    else
    {
      CPlayer::SendMsg_SetItemCheckResult(v24, 2, 0, 0);
      result = 0;
    }
  }
  else
  {
    CPlayer::SendMsg_SetItemCheckResult(v24, 7, 0, 0);
    result = 0;
  }
  return result;
}
