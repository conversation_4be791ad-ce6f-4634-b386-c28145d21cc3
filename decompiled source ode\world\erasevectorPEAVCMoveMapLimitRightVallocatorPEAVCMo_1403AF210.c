/*
 * Function: ?erase@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEAA?AV?$_Vector_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@2@V32@0@Z
 * Address: 0x1403AF210
 */

std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *__fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::erase(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *result, std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_First, std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_Last)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  CMoveMapLimitRight **_Firsta; // [sp+20h] [bp-28h]@5
  int v9; // [sp+28h] [bp-20h]@4
  __int64 v10; // [sp+30h] [bp-18h]@4
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v11; // [sp+50h] [bp+8h]@1
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v12; // [sp+58h] [bp+10h]@1
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v13; // [sp+60h] [bp+18h]@1
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_Right; // [sp+68h] [bp+20h]@1

  _Right = _Last;
  v13 = _First;
  v12 = result;
  v11 = this;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v10 = -2i64;
  v9 = 0;
  if ( std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator!=(
         (std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&_First->_Mycont,
         (std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&_Last->_Mycont) )
  {
    _Firsta = stdext::unchecked_copy<CMoveMapLimitRight * *,CMoveMapLimitRight * *>(
                _Right->_Myptr,
                v11->_Mylast,
                v13->_Myptr);
    std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Destroy(v11, _Firsta, v11->_Mylast);
    v11->_Mylast = _Firsta;
  }
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(
    v12,
    v13);
  v9 |= 1u;
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(v13);
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(_Right);
  return v12;
}
