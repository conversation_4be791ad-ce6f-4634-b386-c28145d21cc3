/*
 * Function: ??$_Uninit_copy@V?$_Vector_const_iterator@URoomCharInfo@@V?$allocator@URoomCharInfo@@@std@@@std@@PEAURoomCharInfo@@V?$allocator@URoomCharInfo@@@2@@std@@YAPEAURoomCharInfo@@V?$_Vector_const_iterator@URoomCharInfo@@V?$allocator@URoomCharInfo@@@std@@@0@0PEAU1@AEAV?$allocator@URoomCharInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1402EE3D0
 */

RoomCharInfo *__fastcall std::_Uninit_copy<std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo>>,RoomCharInfo *,std::allocator<RoomCharInfo>>(std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo> > *_First, std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo> > *_Last, RoomCharInfo *_Dest, std::allocator<RoomCharInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-58h]@1
  RoomCharInfo *v10; // [sp+20h] [bp-38h]@4
  RoomCharInfo *v11; // [sp+28h] [bp-30h]@7
  __int64 v12; // [sp+30h] [bp-28h]@4
  bool v13; // [sp+38h] [bp-20h]@5
  RoomCharInfo *_Val; // [sp+40h] [bp-18h]@6
  std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo> > *v15; // [sp+60h] [bp+8h]@1
  std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo> > *_Right; // [sp+68h] [bp+10h]@1
  RoomCharInfo *_Ptr; // [sp+70h] [bp+18h]@1
  std::allocator<RoomCharInfo> *v18; // [sp+78h] [bp+20h]@1

  v18 = _Al;
  _Ptr = _Dest;
  _Right = _Last;
  v15 = _First;
  v6 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v12 = -2i64;
  v10 = _Dest;
  while ( 1 )
  {
    v13 = std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo>>::operator!=(v15, _Right);
    if ( !v13 )
      break;
    _Val = std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo>>::operator*(v15);
    std::allocator<RoomCharInfo>::construct(v18, _Ptr, _Val);
    ++_Ptr;
    std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo>>::operator++(v15);
  }
  v11 = _Ptr;
  std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo>>::~_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo>>(v15);
  std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo>>::~_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo>>(_Right);
  return v11;
}
