/*
 * Function: ?GetMapItemStoreListByNum@CItemStoreManager@@QEAAPEAVCMapItemStoreList@@H@Z
 * Address: 0x140348940
 */

CMapItemStoreList *__fastcall CItemStoreManager::GetMapItemStoreListByNum(CItemStoreManager *this, int nNum)
{
  CMapItemStoreList *result; // rax@3

  if ( nNum >= 0 && nNum < this->m_nMapItemStoreListNum )
    result = &this->m_MapItemStoreList[nNum];
  else
    result = 0i64;
  return result;
}
