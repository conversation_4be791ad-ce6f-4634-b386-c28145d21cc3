/*
 * Function: Inconsistent_definition_of_symbol__ATL_MIXED::_dynamic_initializer_for__clash___13
 * Address: 0x1406D9F20
 */

__int64 Inconsistent_definition_of_symbol__ATL_MIXED::_dynamic_initializer_for__clash___13()
{
  char *v0; // rdi@1
  signed __int64 i; // rcx@1
  char v3; // [sp+0h] [bp-18h]@1

  v0 = &v3;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 += 4;
  }
  return 0i64;
}
