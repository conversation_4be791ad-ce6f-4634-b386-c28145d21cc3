/*
 * Function: ?CallProc_RFOnlineAuth_Jap@CRFCashItemDatabase@@QEAAHAEAU_param_cash_select@@@Z
 * Address: 0x1404836A0
 */

signed __int64 __fastcall CRFCashItemDatabase::CallProc_RFOnlineAuth_Jap(CRFCashItemDatabase *this, _param_cash_select *rParam)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v5; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@22
  SQLLEN v8; // [sp+38h] [bp-150h]@22
  __int16 v9; // [sp+44h] [bp-144h]@9
  char DstBuf; // [sp+60h] [bp-128h]@4
  char v11; // [sp+61h] [bp-127h]@4
  unsigned __int8 v12; // [sp+164h] [bp-24h]@16
  unsigned __int8 v13; // [sp+165h] [bp-23h]@24
  unsigned __int64 v14; // [sp+170h] [bp-18h]@4
  CRFCashItemDatabase *v15; // [sp+190h] [bp+8h]@1
  _param_cash_select *v16; // [sp+198h] [bp+10h]@1

  v16 = rParam;
  v15 = this;
  v2 = &v5;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v14 = (unsigned __int64)&v5 ^ _security_cookie;
  DstBuf = 0;
  memset(&v11, 0, 0xFFui64);
  sprintf_s(
    &DstBuf,
    0x100ui64,
    "declare @out_amount int exec dbo.SP_RF_CHK_GEM_GAMEON @uid = '%s', @s_amount = @out_amount output select @out_amount",
    rParam->in_szAcc);
  if ( v15->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v15->vfptr, &DstBuf);
  if ( v15->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v15->vfptr) )
  {
    v9 = SQLExecDirect_0(v15->m_hStmtSelect, &DstBuf, -3);
    if ( v9 && v9 != 1 )
    {
      if ( v9 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v15->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v9, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v9, v15->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v9 = SQLFetch_0(v15->m_hStmtSelect);
      if ( v9 && v9 != 1 )
      {
        v12 = 0;
        if ( v9 == 100 )
        {
          v12 = 2;
        }
        else
        {
          SQLStmt = v15->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v9, &DstBuf, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v9, v15->m_hStmtSelect);
          v12 = 1;
        }
        if ( v15->m_hStmtSelect )
          SQLCloseCursor_0(v15->m_hStmtSelect);
        result = v12;
      }
      else
      {
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v15->m_hStmtSelect, 1u, 4, &v16->out_dwCashAmount, 0i64, &v8);
        if ( v9 && v9 != 1 )
        {
          v13 = 0;
          if ( v9 == 100 )
          {
            v13 = 2;
          }
          else
          {
            SQLStmt = v15->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v9, &DstBuf, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v9, v15->m_hStmtSelect);
            v13 = 1;
          }
          if ( v15->m_hStmtSelect )
            SQLCloseCursor_0(v15->m_hStmtSelect);
          result = v13;
        }
        else
        {
          if ( v15->m_hStmtSelect )
            SQLCloseCursor_0(v15->m_hStmtSelect);
          if ( v15->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v15->vfptr, "%s Success", &DstBuf);
          result = 0i64;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v15->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1i64;
  }
  return result;
}
