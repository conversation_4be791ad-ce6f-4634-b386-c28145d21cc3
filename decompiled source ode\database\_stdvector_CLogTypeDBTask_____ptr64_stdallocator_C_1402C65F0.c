/*
 * Function: _std::vector_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64___::_Insert_n_::_1_::catch$1
 * Address: 0x1402C65F0
 */

void __fastcall __noreturn std::vector_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64___::_Insert_n_::_1_::catch_1(__int64 a1, __int64 a2)
{
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Destroy(
    *(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > **)(a2 + 160),
    (CLogTypeDBTask **)(*(_QWORD *)(*(_QWORD *)(a2 + 168) + 16i64) + 8i64 * *(_QWORD *)(a2 + 176)),
    (CLogTypeDBTask **)(*(_QWORD *)(*(_QWORD *)(a2 + 160) + 24i64) + 8i64 * *(_QWORD *)(a2 + 176)));
  CxxThrowException_0(0i64, 0i64);
}
