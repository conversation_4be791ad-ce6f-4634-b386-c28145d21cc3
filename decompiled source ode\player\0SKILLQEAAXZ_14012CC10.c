/*
 * Function: ??0S<PERSON><PERSON><PERSON>@@QEAA@XZ
 * Address: 0x14012CC10
 */

void __fastcall SKILL::SKILL(SKILL *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  SKILL *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_Type = -1;
  v4->m_Element = -1;
  v4->m_MinDmg = 0;
  v4->m_StdDmg = 0;
  v4->m_MaxDmg = 0;
  v4->m_CritDmg = 0;
  v4->m_MinProb = 0;
  v4->m_MaxProb = 0;
  v4->m_IsCritical = 0;
  _attack_param::_attack_param(&v4->m_param);
  v4->m_Len = 0;
  v4->m_CastDelay = 0;
  v4->m_Delay = 0;
  v4->m_bLoad = 0;
  v4->m_Active = 0;
  v4->m_BefTime = 0;
}
