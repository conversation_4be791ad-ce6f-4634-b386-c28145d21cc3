/*
 * Function: ?NotifyBallPosition@CNormalGuildBattle@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403E53E0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::NotifyBallPosition(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-88h]@1
  __int16 Dst; // [sp+28h] [bp-60h]@4
  __int16 v5; // [sp+2Ah] [bp-5Eh]@5
  char v6; // [sp+2Ch] [bp-5Ch]@5
  unsigned int v7; // [sp+2Dh] [bp-5Bh]@5
  CPlayer *v8; // [sp+48h] [bp-40h]@4
  char byType; // [sp+54h] [bp-34h]@5
  char v10; // [sp+55h] [bp-33h]@5
  unsigned __int64 v11; // [sp+70h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattle *v12; // [sp+90h] [bp+8h]@1

  v12 = this;
  v1 = &v3;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11 = (unsigned __int64)&v3 ^ _security_cookie;
  memset_0(&Dst, 0, 9ui64);
  v8 = GUILD_BATTLE::CNormalGuildBattleField::GetBallOwner(v12->m_pkField);
  if ( v8 )
  {
    Dst = (signed int)ffloor(v8->m_fCurPos[0]);
    v5 = (signed int)ffloor(v8->m_fCurPos[2]);
    v6 = v8->m_byGuildBattleColorInx;
    v7 = v8->m_dwObjSerial;
    byType = 27;
    v10 = 80;
    GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(&v12->m_k1P, &byType, (char *)&Dst, 9u);
    GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(&v12->m_k2P, &byType, (char *)&Dst, 9u);
  }
}
