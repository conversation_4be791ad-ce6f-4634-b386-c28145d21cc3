/*
 * Function: ??$push_functor@PEAVCMonster@@PEADPEADMMM@lua_tinker@@YAXPEAUlua_State@@P6APEAVCMonster@@PEAD1MMM@Z@Z
 * Address: 0x140408A80
 */

void __fastcall lua_tinker::push_functor<CMonster *,char *,char *,float,float,float>(struct lua_State *L, CMonster *(__cdecl *func)(char *, char *, float, float, float))
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  struct lua_State *v5; // [sp+30h] [bp+8h]@1

  v5 = L;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  lua_pushcclosure(v5, lua_tinker::functor<char *,char *,float,float,float>::invoke<CMonster *>, 1i64);
}
