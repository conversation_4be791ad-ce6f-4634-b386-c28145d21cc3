/*
 * Function: j_?clear@?$vector@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@QEAAXXZ
 * Address: 0x14000FB32
 */

void __fastcall std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::clear(std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this)
{
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::clear(this);
}
