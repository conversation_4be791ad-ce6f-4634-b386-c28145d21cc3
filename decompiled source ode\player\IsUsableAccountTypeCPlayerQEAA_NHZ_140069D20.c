/*
 * Function: ?IsUsableAccountType@CPlayer@@QEAA_NH@Z
 * Address: 0x140069D20
 */

bool __fastcall CPlayer::IsUsableAccountType(CPlayer *this, int nCashType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( nCashType )
  {
    if ( nCashType == 1 )
      result = CPlayer::IsApplyPcbangPrimium(v6) != 0;
    else
      result = 0;
  }
  else
  {
    result = 1;
  }
  return result;
}
