/*
 * Function: _CUnmannedTraderClassInfoTableType::_CUnmannedTraderClassInfoTableType_::_1_::dtor$1
 * Address: 0x14037CD80
 */

void __fastcall CUnmannedTraderClassInfoTableType::_CUnmannedTraderClassInfoTableType_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>((std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)(*(_QWORD *)(a2 + 64) + 280i64));
}
