/*
 * Function: ?GetGroupID@CUnmannedTraderGroupIDInfo@@QEAA_NEGAEAE0@Z
 * Address: 0x1403866C0
 */

char __fastcall CUnmannedTraderGroupIDInfo::GetGroupID(CUnmannedTraderGroupIDInfo *this, char byTableCode, unsigned __int16 wItemTableIndex, char *byDivision, char *byClass)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char v7; // al@5
  CUnmannedTraderDivisionInfo **v8; // rax@8
  __int64 v9; // [sp+0h] [bp-B8h]@1
  char *v10; // [sp+20h] [bp-98h]@8
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > result; // [sp+38h] [bp-80h]@6
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > v12; // [sp+58h] [bp-60h]@10
  bool v13; // [sp+70h] [bp-48h]@7
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > v14; // [sp+78h] [bp-40h]@7
  char v15; // [sp+90h] [bp-28h]@9
  bool v16; // [sp+91h] [bp-27h]@11
  __int64 v17; // [sp+98h] [bp-20h]@4
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *v18; // [sp+A0h] [bp-18h]@7
  std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *_Right; // [sp+A8h] [bp-10h]@7
  CUnmannedTraderGroupIDInfo *v20; // [sp+C0h] [bp+8h]@1
  char v21; // [sp+C8h] [bp+10h]@1
  unsigned __int16 v22; // [sp+D0h] [bp+18h]@1
  char *byDivisiona; // [sp+D8h] [bp+20h]@1

  byDivisiona = byDivision;
  v22 = wItemTableIndex;
  v21 = byTableCode;
  v20 = this;
  v5 = &v9;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v17 = -2i64;
  if ( std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::empty(&v20->m_vecDivisionInfo) )
  {
    v7 = 0;
  }
  else
  {
    std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::begin(
      &v20->m_vecDivisionInfo,
      &result);
    while ( 1 )
    {
      v18 = std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::end(
              &v20->m_vecDivisionInfo,
              &v14);
      _Right = (std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)v18;
      v13 = std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator!=(
              (std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)&result._Mycont,
              (std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)&v18->_Mycont);
      std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&v14);
      if ( !v13 )
        break;
      v8 = std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator*(&result);
      v10 = byClass;
      if ( CUnmannedTraderDivisionInfo::GetGroupID(*v8, v21, v22, byDivisiona, byClass) )
      {
        v15 = 1;
        std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&result);
        return v15;
      }
      std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator++(
        &result,
        &v12,
        0);
      std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&v12);
    }
    v16 = 0;
    std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&result);
    v7 = v16;
  }
  return v7;
}
