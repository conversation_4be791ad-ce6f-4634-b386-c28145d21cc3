/*
 * Function: ?SetHaveEffect@CPlayer@@QEAAX_N@Z
 * Address: 0x140050D30
 */

void __usercall CPlayer::SetHaveEffect(CPlayer *this@<rcx>, bool bLogin@<dl>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  _effect_parameter::__param_data *v5; // rcx@13
  _effect_parameter::__param_data *v6; // rax@14
  char v7; // al@18
  TimeLimitJadeMng *v8; // rax@23
  char v9; // al@27
  TimeLimitJadeMng *v10; // rax@37
  signed __int64 v11; // r8@50
  float v12; // xmm0_4@50
  _effect_parameter::__param_data *v13; // rcx@63
  _effect_parameter::__param_data *v14; // rcx@66
  _effect_parameter::__param_data *v15; // rax@67
  float v16; // xmm0_4@69
  __int64 v17; // [sp+0h] [bp-88h]@1
  int nDiffCnt; // [sp+20h] [bp-68h]@13
  float v19; // [sp+30h] [bp-58h]@5
  float v20; // [sp+34h] [bp-54h]@5
  int nEffCode; // [sp+38h] [bp-50h]@5
  int j; // [sp+3Ch] [bp-4Ch]@16
  _STORAGE_LIST::_db_con *pkItem; // [sp+40h] [bp-48h]@19
  _base_fld *v24; // [sp+48h] [bp-40h]@21
  int k; // [sp+50h] [bp-38h]@25
  _STORAGE_LIST::_db_con *v26; // [sp+58h] [bp-30h]@28
  _base_fld *v27; // [sp+60h] [bp-28h]@30
  int nCashType; // [sp+68h] [bp-20h]@31
  int l; // [sp+6Ch] [bp-1Ch]@39
  int m; // [sp+70h] [bp-18h]@56
  float v31; // [sp+74h] [bp-14h]@63
  float v32; // [sp+78h] [bp-10h]@74
  int count; // [sp+7Ch] [bp-Ch]@74
  CPlayer *v34; // [sp+90h] [bp+8h]@1
  bool v35; // [sp+98h] [bp+10h]@1

  v35 = bLogin;
  v34 = this;
  v3 = &v17;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v34->m_EP.m_pDataParam )
  {
    _effect_parameter::GetEff_Have(&v34->m_EP, 4);
    v19 = a3;
    _effect_parameter::GetEff_Have(&v34->m_EP, 77);
    v20 = a3;
    for ( nEffCode = 12; nEffCode < 83; ++nEffCode )
    {
      if ( (nEffCode < 59 || nEffCode > 65) && v34->m_EP.m_pDataParam->m_fEff_Have[nEffCode] != 0.0 )
      {
        if ( nEffCode != 79 && nEffCode != 80 )
        {
          v6 = v34->m_EP.m_pDataParam;
          nDiffCnt = 0;
          CPlayer::apply_have_item_std_effect(v34, nEffCode, v6->m_fEff_Have[nEffCode], 0, 0);
        }
        else
        {
          v5 = v34->m_EP.m_pDataParam;
          nDiffCnt = v35;
          CPlayer::apply_have_item_std_effect(v34, nEffCode, v5->m_fEff_Have[nEffCode], 0, v35);
        }
      }
    }
    for ( j = 0; ; ++j )
    {
      v7 = CPlayerDB::GetBagNum(&v34->m_Param);
      if ( j >= 20 * (unsigned __int8)v7 )
        break;
      pkItem = &v34->m_Param.m_dbInven.m_pStorageList[j];
      if ( pkItem->m_bLoad )
      {
        if ( pkItem->m_byTableCode == 18 )
        {
          v24 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pkItem->m_byTableCode, pkItem->m_wItemIndex);
          if ( v24 )
          {
            if ( *(_DWORD *)&v24[5].m_strCode[44] != -1 )
            {
              v8 = TimeLimitJadeMng::Instance();
              TimeLimitJadeMng::DeleteList(v8, v34->m_ObjID.m_wIndex, pkItem);
            }
          }
        }
      }
    }
    _effect_parameter::InitEffHave(&v34->m_EP);
    for ( k = 0; ; ++k )
    {
      v9 = CPlayerDB::GetBagNum(&v34->m_Param);
      if ( k >= 20 * (unsigned __int8)v9 )
        break;
      v26 = &v34->m_Param.m_dbInven.m_pStorageList[k];
      if ( v26->m_bLoad )
      {
        if ( v26->m_byTableCode == 18 )
        {
          v27 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v26->m_byTableCode, v26->m_wItemIndex);
          if ( v27 )
          {
            nCashType = GetUsePcCashType(v26->m_byTableCode, v26->m_wItemIndex);
            if ( CPlayer::IsUsableAccountType(v34, nCashType) )
            {
              if ( *(_DWORD *)&v27[5].m_strCode[44] == -1 || CPlayer::IsRidingUnit(v34) )
              {
                if ( *(_DWORD *)&v27[5].m_strCode[52] != 1 )
                {
                  for ( l = 0; l < (signed int)v27[6].m_dwIndex; ++l )
                  {
                    if ( *(_DWORD *)&v27[6].m_strCode[12 * l] > -1
                      && (*(_DWORD *)&v27[6].m_strCode[12 * l] < 59 || *(_DWORD *)&v27[6].m_strCode[12 * l] > 65)
                      && (!CPlayer::IsRidingUnit(v34)
                       || *(_DWORD *)&v27[6].m_strCode[12 * l] >= 29
                       || *(_DWORD *)&v27[6].m_strCode[12 * l] <= 32)
                      && *(_DWORD *)&v27[6].m_strCode[12 * l] < 83
                      && v34->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v27[6].m_strCode[12 * l]] < *(float *)&v27[6].m_strCode[12 * l + 8] )
                    {
                      v11 = v26->m_dwDur;
                      v12 = (float)(signed int)v11;
                      if ( v11 < 0 )
                        v12 = v12 + 1.8446744e19;
                      v34->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v27[6].m_strCode[12 * l]] = v34->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v27[6].m_strCode[12 * l]]
                                                                                                + (float)(*(float *)&v27[6].m_strCode[12 * l + 4] * v12);
                      if ( v34->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v27[6].m_strCode[12 * l]] > *(float *)&v27[6].m_strCode[12 * l + 8] )
                        v34->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v27[6].m_strCode[12 * l]] = *(float *)&v27[6].m_strCode[12 * l + 8];
                    }
                  }
                }
              }
              else
              {
                v10 = TimeLimitJadeMng::Instance();
                TimeLimitJadeMng::InsertList(v10, v34->m_ObjID.m_wIndex, v26);
              }
            }
            else if ( v35 )
            {
              CPlayer::SendMsg_PremiumCashItemUse(v34, v26->m_wSerial);
            }
          }
        }
      }
    }
    v34->m_EP.m_pDataParam->m_fEff_Have[0] = v34->m_EP.m_pDataParam->m_fEff_Have[0] + 1.0;
    v34->m_EP.m_pDataParam->m_fEff_Have[2] = v34->m_EP.m_pDataParam->m_fEff_Have[2] + 1.0;
    v34->m_EP.m_pDataParam->m_fEff_Have[5] = v34->m_EP.m_pDataParam->m_fEff_Have[5] + 1.0;
    v34->m_EP.m_pDataParam->m_fEff_Have[6] = v34->m_EP.m_pDataParam->m_fEff_Have[6] + 1.0;
    v34->m_EP.m_pDataParam->m_fEff_Have[7] = v34->m_EP.m_pDataParam->m_fEff_Have[7] + 1.0;
    v34->m_EP.m_pDataParam->m_fEff_Have[8] = v34->m_EP.m_pDataParam->m_fEff_Have[8] + 1.0;
    v34->m_EP.m_pDataParam->m_fEff_Have[9] = v34->m_EP.m_pDataParam->m_fEff_Have[9] + 1.0;
    for ( m = 12; m < 83; ++m )
    {
      if ( (m < 59 || m > 65) && v34->m_EP.m_pDataParam->m_fEff_Have[m] != 0.0 )
      {
        if ( m == 77 )
        {
          v31 = (float)(v34->m_EP.m_pDataParam->m_fEff_Have[77] - v20) * 100.0;
          v13 = v34->m_EP.m_pDataParam;
          nDiffCnt = (signed int)ffloor(v31);
          CPlayer::apply_have_item_std_effect(v34, 77, v13->m_fEff_Have[77], 1, nDiffCnt);
        }
        if ( m != 79 && m != 80 )
        {
          v15 = v34->m_EP.m_pDataParam;
          nDiffCnt = 0;
          CPlayer::apply_have_item_std_effect(v34, m, v15->m_fEff_Have[m], 1, 0);
        }
        else
        {
          v14 = v34->m_EP.m_pDataParam;
          nDiffCnt = v35;
          CPlayer::apply_have_item_std_effect(v34, m, v14->m_fEff_Have[m], 1, v35);
        }
      }
    }
    v34->m_EP.m_pDataParam->m_fEff_Have[71] = v34->m_EP.m_pDataParam->m_fEff_Have[71] + 1.0;
    v34->m_EP.m_pDataParam->m_fEff_Have[72] = v34->m_EP.m_pDataParam->m_fEff_Have[72] + 1.0;
    v34->m_EP.m_pDataParam->m_fEff_Have[73] = v34->m_EP.m_pDataParam->m_fEff_Have[73] + 1.0;
    v34->m_EP.m_pDataParam->m_fEff_Have[74] = v34->m_EP.m_pDataParam->m_fEff_Have[74] + 1.0;
    v16 = v34->m_EP.m_pDataParam->m_fEff_Have[75] + 1.0;
    v34->m_EP.m_pDataParam->m_fEff_Have[75] = v16;
    _effect_parameter::GetEff_Have(&v34->m_EP, 4);
    if ( v16 <= v19 )
    {
      _effect_parameter::GetEff_Have(&v34->m_EP, 4);
      if ( v19 > v16 )
        v34->m_bDownCheckEquipEffect = 1;
    }
    else
    {
      v34->m_bUpCheckEquipEffect = 1;
    }
    if ( v34->m_EP.m_pDataParam->m_fEff_Have[2] > 1.0 )
    {
      v32 = v34->m_EP.m_pDataParam->m_fEff_Have[2] - 1.0;
      v32 = v32 * 10.0;
      count = (signed int)ffloor(v32) % 10;
      CMgrAvatorItemHistory::exp_prof_log(&CPlayer::s_MgrItemHistory, count, v34->m_szItemHistoryFileName);
    }
  }
}
