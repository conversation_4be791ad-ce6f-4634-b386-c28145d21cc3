/*
 * Function: ?ResizeBuffers@CBC_Decryption@CryptoPP@@MEAAXXZ
 * Address: 0x140452FF0
 */

void __fastcall CryptoPP::CBC_Decryption::ResizeBuffers(CryptoPP::CBC_Decryption *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@4
  __int64 v4; // [sp+0h] [bp-28h]@1
  CryptoPP::CBC_Decryption *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CryptoPP::BlockOrientedCipherModeBase::ResizeBuffers((CryptoPP::BlockOrientedCipherModeBase *)&v5->vfptr);
  v3 = CryptoPP::CipherModeBase::BlockSize((CryptoPP::CipherModeBase *)&v5->vfptr);
  CryptoPP::Sec<PERSON>lock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::New(&v5->m_temp, v3);
}
