/*
 * Function: ?GetParam@CHackShieldExSystem@@AEAAPEAUBASE_HACKSHEILD_PARAM@@K@Z
 * Address: 0x140417080
 */

BASE_HACKSHEILD_PARAM *__fastcall CHackShieldExSystem::GetParam(CHackShieldExSystem *this, unsigned int n)
{
  BASE_HACKSHEILD_PARAM *result; // rax@2

  if ( __PAIR__(this->m_bActive, this->m_bInit) )
  {
    if ( n >= 0x9E4 )
      result = 0i64;
    else
      result = this->m_ppNodeArray[n];
  }
  else
  {
    result = 0i64;
  }
  return result;
}
