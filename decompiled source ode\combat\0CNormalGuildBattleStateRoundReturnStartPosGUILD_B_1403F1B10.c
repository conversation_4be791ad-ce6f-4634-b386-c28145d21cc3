/*
 * Function: ??0CNormalGuildBattleStateRoundReturnStartPos@GUILD_BATTLE@@QEAA@XZ
 * Address: 0x1403F1B10
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos::CNormalGuildBattleStateRoundReturnStartPos(GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  CMyTimer *v5; // [sp+20h] [bp-28h]@7
  CMyTimer *v6; // [sp+28h] [bp-20h]@4
  __int64 v7; // [sp+30h] [bp-18h]@4
  CMyTimer *v8; // [sp+38h] [bp-10h]@5
  GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = -2i64;
  GUILD_BATTLE::CNormalGuildBattleStateRound::CNormalGuildBattleStateRound((GUILD_BATTLE::CNormalGuildBattleStateRound *)&v9->vfptr);
  v9->vfptr = (GUILD_BATTLE::CGuildBattleStateVtbl *)&GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos::`vftable';
  v9->m_pkTimer = 0i64;
  v6 = (CMyTimer *)operator new(0x18ui64);
  if ( v6 )
  {
    CMyTimer::CMyTimer(v6);
    v8 = (CMyTimer *)v3;
  }
  else
  {
    v8 = 0i64;
  }
  v5 = v8;
  v9->m_pkTimer = v8;
  if ( !v9->m_pkTimer )
    v9->m_pkTimer = 0i64;
}
