/*
 * Function: j_?CompleteCancelRegistItem@CUnmannedTraderUserInfo@@AEAA_NKGPEAVCLogFile@@@Z
 * Address: 0x14000597F
 */

bool __fastcall CUnmannedTraderUserInfo::CompleteCancelRegistItem(CUnmannedTraderUserInfo *this, unsigned int dwRegistSerial, unsigned __int16 dwItemSerial, CLogFile *pkLogger)
{
  return CUnmannedTraderUserInfo::CompleteCancelRegistItem(this, dwRegistSerial, dwItemSerial, pkLogger);
}
