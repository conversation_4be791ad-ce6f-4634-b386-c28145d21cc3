/*
 * Function: ?SendDecideRecallErrorResultToDest@CRecallEffectController@@IEAAXEPEAVCPlayer@@H@Z
 * Address: 0x14024F3A0
 */

void __fastcall CRecallEffectController::SendDecideRecallErrorResultToDest(CRecallEffectController *this, char byErr, CPlayer *pkDest, int nCallerMapCode)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  int v8; // [sp+35h] [bp-43h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v10; // [sp+55h] [bp-23h]@4

  v4 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = byErr;
  v8 = nCallerMapCode;
  pbyType = 17;
  v10 = 35;
  CNetProcess::LoadSendMsg(unk_1414F2088, pkDest->m_ObjID.m_wIndex, &pbyType, &szMsg, 5u);
}
