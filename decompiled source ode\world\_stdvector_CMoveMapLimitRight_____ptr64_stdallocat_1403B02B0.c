/*
 * Function: _std::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::_Insert_n_::_1_::catch$0
 * Address: 0x1403B02B0
 */

void __fastcall __noreturn std::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::_Insert_n_::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1

  v2 = a2;
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Destroy(
    *(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > **)(a2 + 160),
    *(CMoveMapLimitRight ***)(a2 + 64),
    *(CMoveMapLimitRight ***)(a2 + 72));
  std::allocator<CMoveMapLimitRight *>::deallocate(
    (std::allocator<CMoveMapLimitRight *> *)(*(_QWORD *)(v2 + 160) + 8i64),
    *(CMoveMapLimitRight ***)(v2 + 64),
    *(_QWORD *)(v2 + 56));
  CxxThrowException_0(0i64, 0i64);
}
