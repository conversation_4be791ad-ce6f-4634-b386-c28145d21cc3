/*
 * Function: ?GetCurrentRegItemStateStr@CUnmannedTraderUserInfo@@AEAAXPEADH@Z
 * Address: 0x14035C900
 */

void __fastcall CUnmannedTraderUserInfo::GetCurrentRegItemStateStr(CUnmannedTraderUserInfo *this, char *szStateStr, int iBuffSize)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderRegistItemInfo *v5; // rax@6
  unsigned int v6; // eax@6
  wchar_t *v7; // rax@6
  CUnmannedTraderRegistItemInfo *v8; // rax@6
  CUnmannedTraderItemState::STATE v9; // eax@6
  __int64 v10; // [sp+0h] [bp-8F8h]@1
  CUnmannedTraderItemState::STATE v11; // [sp+20h] [bp-8D8h]@6
  char *v12; // [sp+28h] [bp-8D0h]@6
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > result; // [sp+38h] [bp-8C0h]@4
  char szTran; // [sp+70h] [bp-888h]@6
  char DstBuf; // [sp+490h] [bp-468h]@6
  int v16; // [sp+894h] [bp-64h]@4
  unsigned int j; // [sp+898h] [bp-60h]@4
  bool v18; // [sp+8A8h] [bp-50h]@5
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v19; // [sp+8B0h] [bp-48h]@5
  __int64 v20; // [sp+8C8h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v21; // [sp+8D0h] [bp-28h]@5
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *_Right; // [sp+8D8h] [bp-20h]@5
  unsigned __int64 v23; // [sp+8E0h] [bp-18h]@4
  CUnmannedTraderUserInfo *v24; // [sp+900h] [bp+8h]@1
  char *Dst; // [sp+908h] [bp+10h]@1
  int v26; // [sp+910h] [bp+18h]@1

  v26 = iBuffSize;
  Dst = szStateStr;
  v24 = this;
  v3 = &v10;
  for ( i = 572i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v20 = -2i64;
  v23 = (unsigned __int64)&v10 ^ _security_cookie;
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::begin(
    &v24->m_vecRegistItemInfo,
    &result);
  v16 = 0;
  for ( j = 0; ; ++j )
  {
    v21 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
            &v24->m_vecRegistItemInfo,
            &v19);
    _Right = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)v21;
    v18 = std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator!=(
            (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&result._Mycont,
            (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v21->_Mycont);
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v19);
    if ( !v18 )
      break;
    v5 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(&result);
    v6 = CUnmannedTraderRegistItemInfo::GetState(v5);
    v7 = CUnmannedTraderItemState::GetStateStrW(v6);
    WCHARTOMULTI(v7, &szTran, 0x400u);
    v8 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(&result);
    v9 = CUnmannedTraderRegistItemInfo::GetState(v8);
    v12 = &szTran;
    v11 = v9;
    sprintf_s(&DstBuf, 0x400ui64, "%d:(%d)%s ", j);
    strcat_s(Dst, v26, &DstBuf);
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator++(&result);
  }
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
}
