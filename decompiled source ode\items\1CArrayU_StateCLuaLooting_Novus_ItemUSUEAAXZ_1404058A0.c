/*
 * Function: ??1?$CArray@U_State@CLuaLooting_Novus_Item@@@US@@UEAA@XZ
 * Address: 0x1404058A0
 */

void __fastcall US::CArray<CLuaLooting_Novus_Item::_State>::~CArray<CLuaLooting_Novus_Item::_State>(US::CArray<CLuaLooting_Novus_Item::_State> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  void *v4; // [sp+20h] [bp-18h]@5
  US::CArray<CLuaLooting_Novus_Item::_State> *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->vfptr = (US::CArray<CLuaLooting_Novus_Item::_State>Vtbl *)&US::CArray<CLuaLooting_Novus_Item::_State>::`vftable';
  if ( v5->m_bAlloc )
  {
    v4 = v5->m_pBuffer;
    operator delete[](v4);
  }
}
