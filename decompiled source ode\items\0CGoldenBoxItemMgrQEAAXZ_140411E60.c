/*
 * Function: ??0CGoldenBoxItemMgr@@QEAA@XZ
 * Address: 0x140411E60
 */

void __fastcall CGoldenBoxItemMgr::CGoldenBoxItemMgr(CGoldenBoxItemMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CGoldenBoxItemMgr *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CLogFile::CLogFile(&v5->_kLogger);
  CMyTimer::CMyTimer(&v5->m_tmLoopTimer);
  _golden_box_item_event::_golden_box_item_event(&v5->m_golden_box_event);
  _golden_box_item::_golden_box_item(&v5->m_golden_box_item);
  _db_golden_box_item::_db_golden_box_item(&v5->m_golden_box_item_New);
  _db_golden_box_item::_db_golden_box_item(&v5->m_golden_box_item_Old);
  _db_golden_box_item::_db_golden_box_item(&v5->m_temp_db);
  v5->m_nDBSerial = 0;
}
