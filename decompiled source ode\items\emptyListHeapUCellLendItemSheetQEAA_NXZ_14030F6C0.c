/*
 * Function: ?empty@?$ListHeap@UCell@LendItemSheet@@@@QEAA_NXZ
 * Address: 0x14030F6C0
 */

bool __fastcall ListHeap<LendItemSheet::Cell>::empty(ListHeap<LendItemSheet::Cell> *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // [sp+0h] [bp-18h]@1
  ListHeap<LendItemSheet::Cell> *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  return v5->_listData.m_dwCount == 0;
}
