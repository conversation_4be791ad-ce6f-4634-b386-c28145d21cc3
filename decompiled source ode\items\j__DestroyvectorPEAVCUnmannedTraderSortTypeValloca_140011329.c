/*
 * Function: j_?_Destroy@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@IEAAXPEAPEAVCUnmannedTraderSortType@@0@Z
 * Address: 0x140011329
 */

void __fastcall std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Destroy(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, CUnmannedTraderSortType **_First, CUnmannedTraderSortType **_Last)
{
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Destroy(this, _First, _Last);
}
