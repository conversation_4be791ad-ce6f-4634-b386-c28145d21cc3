/*
 * Function: j_?SubCompleteBuyProcBuy@CUnmannedTraderUserInfoTable@@AEAA_NPEAVCPlayer@@PEAVCUnmannedTraderUserInfo@@_JQEBU__list@_qry_case_unmandtrader_buy_update_wait@@PEAU4_unmannedtrader_buy_item_result_zocl@@PEAU4_qry_case_unmandtrader_buy_update_complete@@PEAEPEAKPEAVCUnmannedTraderTradeInfo@@@Z
 * Address: 0x14000EAF7
 */

bool __fastcall CUnmannedTraderUserInfoTable::SubCompleteBuyProcBuy(CUnmannedTraderUserInfoTable *this, CPlayer *pkBuyPlayer, CUnmannedTraderUserInfo *pkBuyUser, __int64 tResultTime, _qry_case_unmandtrader_buy_update_wait::__list *const pkQueryList, _unmannedtrader_buy_item_result_zocl::__list *pSendResultList, _qry_case_unmandtrader_buy_update_complete::__list *pUpdateCompleteList, char *byCompleteUpdateNum, unsigned int *pdwPayDalant, CUnmannedTraderTradeInfo *pkTaradInfo)
{
  return CUnmannedTraderUserInfoTable::SubCompleteBuyProcBuy(
           this,
           pkBuyPlayer,
           pkBuyUser,
           tResultTime,
           pkQueryList,
           pSendResultList,
           pUpdateCompleteList,
           byCompleteUpdateNum,
           pdwPayDalant,
           pkTaradInfo);
}
