/*
 * Function: ?apply_normal_item_std_effect@CPlayer@@QEAAXHM_N@Z
 * Address: 0x140061B10
 */

void __fastcall CPlayer::apply_normal_item_std_effect(CPlayer *this, int nEffCode, float fVal, bool bEquip)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  int nParamIndex; // [sp+20h] [bp-18h]@7
  int j; // [sp+24h] [bp-14h]@13
  int k; // [sp+28h] [bp-10h]@32
  int v10; // [sp+2Ch] [bp-Ch]@4
  CPlayer *v11; // [sp+40h] [bp+8h]@1
  bool v12; // [sp+58h] [bp+20h]@1

  v12 = bEquip;
  v11 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v10 = nEffCode - 1;
  switch ( nEffCode )
  {
    case 1:
      _effect_parameter::SetEff_Rate(&v11->m_EP, 11, fVal, bEquip);
      break;
    case 2:
      _effect_parameter::SetEff_Rate(&v11->m_EP, 7, fVal, bEquip);
      break;
    case 3:
      for ( nParamIndex = 0; nParamIndex < 2; ++nParamIndex )
        _effect_parameter::SetEff_Plus(&v11->m_EP, nParamIndex, fVal, v12);
      _effect_parameter::SetEff_Plus(&v11->m_EP, 2, fVal, v12);
      break;
    case 4:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 3, fVal, bEquip);
      break;
    case 5:
      _effect_parameter::SetEff_Rate(&v11->m_EP, 9, fVal, bEquip);
      _effect_parameter::SetEff_Rate(&v11->m_EP, 10, fVal, v12);
      break;
    case 6:
      for ( j = 0; j < 2; ++j )
      {
        _effect_parameter::SetEff_Rate(&v11->m_EP, j, fVal, v12);
        _effect_parameter::SetEff_Rate(&v11->m_EP, j + 2, fVal, v12);
      }
      _effect_parameter::SetEff_Rate(&v11->m_EP, 4, fVal, v12);
      _effect_parameter::SetEff_Rate(&v11->m_EP, 29, fVal, v12);
      break;
    case 7:
      _effect_parameter::SetEff_Rate(&v11->m_EP, 6, fVal, bEquip);
      break;
    case 8:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 19, fVal, bEquip);
      break;
    case 9:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 21, 1.0, bEquip);
      break;
    case 10:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 22, 1.0, bEquip);
      break;
    case 11:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 23, 1.0, bEquip);
      break;
    case 12:
      if ( !v11->m_bInGuildBattle || !v11->m_bTakeGravityStone )
        _effect_parameter::SetEff_Plus(&v11->m_EP, 20, fVal, bEquip);
      break;
    case 13:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 24, 1.0, bEquip);
      break;
    case 14:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 25, fVal, bEquip);
      break;
    case 15:
      _effect_parameter::SetEff_Rate(&v11->m_EP, 4, fVal, bEquip);
      break;
    case 16:
      _effect_parameter::SetEff_Rate(&v11->m_EP, 10, fVal, bEquip);
      break;
    case 17:
      _effect_parameter::SetEff_Rate(&v11->m_EP, 12, fVal, bEquip);
      break;
    case 18:
      _effect_parameter::SetEff_Rate(&v11->m_EP, 13, fVal, bEquip);
      break;
    case 19:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 14, fVal, bEquip);
      break;
    case 20:
      for ( k = 0; k < 2; ++k )
      {
        _effect_parameter::SetEff_Plus(&v11->m_EP, k + 4, fVal, v12);
        _effect_parameter::SetEff_Plus(&v11->m_EP, k + 6, fVal, v12);
      }
      _effect_parameter::SetEff_Plus(&v11->m_EP, 36, fVal, v12);
      break;
    case 21:
      _effect_parameter::SetEff_Rate(&v11->m_EP, 6, fVal, bEquip);
      break;
    case 22:
      _effect_parameter::SetEff_Rate(&v11->m_EP, 8, fVal, bEquip);
      break;
    case 23:
      _effect_parameter::SetEff_Rate(&v11->m_EP, 14, fVal, bEquip);
      break;
    case 24:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 3, fVal, bEquip);
      break;
    case 25:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 11, fVal, bEquip);
      break;
    case 26:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 8, fVal, bEquip);
      break;
    case 27:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 37, fVal, bEquip);
      break;
    case 28:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 29, fVal, bEquip);
      break;
    case 29:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 15, fVal, bEquip);
      _effect_parameter::SetEff_Plus(&v11->m_EP, 16, fVal, v12);
      _effect_parameter::SetEff_Plus(&v11->m_EP, 17, fVal, v12);
      _effect_parameter::SetEff_Plus(&v11->m_EP, 18, fVal, v12);
      break;
    case 30:
      _effect_parameter::SetEff_Rate(&v11->m_EP, 9, fVal, bEquip);
      break;
    case 31:
      _effect_parameter::SetEff_Rate(&v11->m_EP, 8, fVal, bEquip);
      break;
    case 32:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 28, fVal, bEquip);
      break;
    case 33:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 10, fVal, bEquip);
      break;
    case 34:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 12, fVal, bEquip);
      break;
    case 35:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 13, fVal, bEquip);
      break;
    case 36:
      _effect_parameter::SetEff_Plus(&v11->m_EP, 38, fVal, bEquip);
      break;
    default:
      return;
  }
}
