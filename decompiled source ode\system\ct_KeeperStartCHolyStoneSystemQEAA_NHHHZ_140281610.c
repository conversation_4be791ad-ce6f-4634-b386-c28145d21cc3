/*
 * Function: ?ct_KeeperStart@CHolyStoneSystem@@QEAA_NHHH@Z
 * Address: 0x140281610
 */

char __fastcall CHolyStoneSystem::ct_KeeperStart(CHolyStoneSystem *this, int nKeeperState, int nRace, int nPassTime)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v7; // [sp+0h] [bp-38h]@1
  int nChangeReason; // [sp+20h] [bp-18h]@6
  CHolyStoneSystem *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v9->m_SaveData.m_nSceneCode < 2 || v9->m_SaveData.m_nSceneCode >= 6 )
  {
    result = 0;
  }
  else
  {
    v9->m_SaveData.m_nHolyMasterRace = nRace;
    nChangeReason = 1;
    CHolyStoneSystem::SetScene(v9, v9->m_SaveData.m_byNumOfTime, nKeeperState, nPassTime, 1);
    result = 1;
  }
  return result;
}
