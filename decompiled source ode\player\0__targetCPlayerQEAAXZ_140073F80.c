/*
 * Function: ??0__target@CPlayer@@QEAA@XZ
 * Address: 0x140073F80
 */

void __fastcall CPlayer::__target::__target(CPlayer::__target *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CPlayer::__target *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _target_monster_contsf_allinform_zocl::_target_monster_contsf_allinform_zocl(&v4->m_PrevTargetMonsterContInfo);
  _target_player_damage_contsf_allinform_zocl::_target_player_damage_contsf_allinform_zocl(&v4->m_PrevTargetPlayerDamageContInfo);
  CPlayer::__target::init(v4);
}
