/*
 * Function: ?ct_regen_gravitystone@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140293120
 */

char __fastcall ct_regen_gravitystone(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CGuildBattleController *v4; // rax@7
  CGuildBattleController *v5; // rax@9
  __int64 v6; // [sp+0h] [bp-108h]@1
  int iRengenPos; // [sp+40h] [bp-C8h]@9
  int v8; // [sp+44h] [bp-C4h]@9
  char Dest; // [sp+60h] [bp-A8h]@11
  int v10; // [sp+F0h] [bp-18h]@7
  unsigned __int64 v11; // [sp+F8h] [bp-10h]@4
  CPlayer *pkPlayer; // [sp+110h] [bp+8h]@1

  pkPlayer = pOne;
  v1 = &v6;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( pkPlayer )
  {
    if ( s_nWordCount )
    {
      if ( s_nWordCount == 1 )
      {
        iRengenPos = atoi(s_pwszDstCheat[0]);
        v5 = CGuildBattleController::Instance();
        v8 = CGuildBattleController::CheatRegenStone(v5, pkPlayer, iRengenPos);
        if ( v8 >= 0 )
        {
          sprintf(&Dest, "Regen Stone(%d) PortalInx : %d", (unsigned int)iRengenPos, (unsigned int)v8);
          CPlayer::SendData_ChatTrans(pkPlayer, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
          result = 1;
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      v4 = CGuildBattleController::Instance();
      v10 = CGuildBattleController::CheatRegenStone(v4, pkPlayer, -1) == 0;
      result = v10;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
