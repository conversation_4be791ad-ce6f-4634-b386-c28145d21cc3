/*
 * Function: ?ReStart@CNormalGuildBattle@GUILD_BATTLE@@QEAAEPEAVCPlayer@@KK@Z
 * Address: 0x1403E42A0
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattle::ReStart(GUILD_BATTLE::CNormalGuildBattle *this, CPlayer *pkPlayer, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  GUILD_BATTLE::CGuildBattleSchedulePool *v7; // rax@17
  GUILD_BATTLE::CGuildBattleLogger *v8; // rax@18
  char *v9; // rax@19
  signed __int64 v10; // rcx@19
  CGuild *v11; // rdx@19
  __int64 v12; // [sp+0h] [bp-D8h]@1
  unsigned __int16 nLen[2]; // [sp+20h] [bp-B8h]@18
  __int64 v14; // [sp+28h] [bp-B0h]@19
  int v15; // [sp+30h] [bp-A8h]@19
  char *v16; // [sp+38h] [bp-A0h]@19
  int v17; // [sp+40h] [bp-98h]@19
  char v18; // [sp+50h] [bp-88h]@4
  int v19; // [sp+54h] [bp-84h]@4
  char Dst; // [sp+68h] [bp-70h]@4
  unsigned int v21; // [sp+69h] [bp-6Fh]@14
  unsigned int v22; // [sp+6Dh] [bp-6Bh]@16
  unsigned int v23; // [sp+71h] [bp-67h]@14
  unsigned int v24; // [sp+75h] [bp-63h]@16
  char byHour; // [sp+79h] [bp-5Fh]@19
  char byMin; // [sp+7Ah] [bp-5Eh]@19
  char bySec; // [sp+7Bh] [bp-5Dh]@19
  int iRedPortalInx; // [sp+7Ch] [bp-5Ch]@17
  int iBluePortalInx; // [sp+80h] [bp-58h]@17
  int piRegenPortalInx; // [sp+84h] [bp-54h]@17
  GUILD_BATTLE::CGuildBattleSchedule *v31; // [sp+98h] [bp-40h]@17
  char pbyType; // [sp+A4h] [bp-34h]@19
  char v33; // [sp+A5h] [bp-33h]@19
  int v34; // [sp+B4h] [bp-24h]@18
  int v35; // [sp+B8h] [bp-20h]@19
  GUILD_BATTLE::CNormalGuildBattleLogger *v36; // [sp+C0h] [bp-18h]@19
  GUILD_BATTLE::CNormalGuildBattle *v37; // [sp+E0h] [bp+8h]@1
  CPlayer *v38; // [sp+E8h] [bp+10h]@1
  unsigned int v39; // [sp+F0h] [bp+18h]@1
  int dwSerial; // [sp+F8h] [bp+20h]@1

  dwSerial = dwCharacSerial;
  v39 = dwGuildSerial;
  v38 = pkPlayer;
  v37 = this;
  v4 = &v12;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v18 = 0;
  v19 = -1;
  memset_0(&Dst, 0, 0x28ui64);
  if ( v39 == GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v37->m_k1P) )
  {
    if ( !GUILD_BATTLE::CNormalGuildBattleGuild::IsMember(&v37->m_k1P, dwSerial) )
      return -111;
    Dst = GUILD_BATTLE::CNormalGuildBattleGuild::GetColorInx(&v37->m_k1P);
  }
  else
  {
    if ( v39 != GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v37->m_k2P) )
      return -115;
    if ( !GUILD_BATTLE::CNormalGuildBattleGuild::IsMember(&v37->m_k2P, dwSerial) )
      return -111;
    Dst = GUILD_BATTLE::CNormalGuildBattleGuild::GetColorInx(&v37->m_k2P);
  }
  if ( v37->m_pkRed )
  {
    v23 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGoalCnt(v37->m_pkRed);
    v21 = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(v37->m_pkRed);
  }
  if ( v37->m_pkBlue )
  {
    v24 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGoalCnt(v37->m_pkBlue);
    v22 = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(v37->m_pkBlue);
  }
  GUILD_BATTLE::CNormalGuildBattleField::GetPortalIndexInfo(
    v37->m_pkField,
    &iRedPortalInx,
    &iBluePortalInx,
    &piRegenPortalInx);
  v7 = GUILD_BATTLE::CGuildBattleSchedulePool::Instance();
  v31 = GUILD_BATTLE::CGuildBattleSchedulePool::GetRef(v7, v37->m_dwID);
  if ( v31 )
  {
    GUILD_BATTLE::CGuildBattleSchedule::GetLeftTime(v31, &byHour, &byMin, &bySec);
    __trace("(%u)CNormalGuildBattle::Start : byColor(%u)\n", (unsigned int)dwSerial, (unsigned __int8)Dst);
    pbyType = 27;
    v33 = 84;
    CNetProcess::LoadSendMsg(unk_1414F2088, v38->m_ObjID.m_wIndex, &pbyType, &Dst, 0x28u);
    v35 = CPlayerDB::GetRaceSexCode(&v38->m_Param);
    v9 = CPlayerDB::GetCharNameW(&v38->m_Param);
    v10 = (signed __int64)v38->m_Param.m_pGuild->m_wszName;
    v11 = v38->m_Param.m_pGuild;
    v36 = &v37->m_kLogger;
    v17 = v35;
    v16 = v9;
    v15 = dwSerial;
    v14 = v10;
    *(_DWORD *)nLen = v11->m_dwSerial;
    GUILD_BATTLE::CNormalGuildBattleLogger::Log(
      &v37->m_kLogger,
      "CNormalGuildBattle::Start( %u, %u ) : (%d) %s Guild : (%d) %s race(%d) Start!",
      v39,
      (unsigned int)dwSerial);
    result = 0;
  }
  else
  {
    v34 = GUILD_BATTLE::CNormalGuildBattleField::GetMapCode(v37->m_pkField);
    v8 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    *(_DWORD *)nLen = v34;
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v8,
      "CNormalGuildBattle::Start( %u, %u ) : CGuildBattleSchedulePool::Instance()->GetRef(%u) NULL!",
      v39,
      (unsigned int)dwSerial);
    result = 110;
  }
  return result;
}
