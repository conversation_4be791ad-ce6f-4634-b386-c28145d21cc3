/*
 * Function: ?CreateComplete@CNationSettingDataCN@@UEAAXPEAVCPlayer@@@Z
 * Address: 0x140230870
 */

void __fastcall CNationSettingDataCN::CreateComplete(CNationSettingDataCN *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CChiNetworkEX *v4; // rax@4
  CChiNetworkEX *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-28h]@1
  CPlayer *pOnea; // [sp+38h] [bp+10h]@1

  pOnea = pOne;
  v2 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = CChiNetworkEX::Instance();
  CChiNetworkEX::Send_Login(v4, pOnea);
  v5 = CChiNetworkEX::Instance();
  CChiNetworkEX::Send_IP(v5, pOnea);
}
