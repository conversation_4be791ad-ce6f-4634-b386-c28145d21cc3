/*
 * Function: ?IsRangePerMastery@_STAT_DB_BASE@@SA_NEE@Z
 * Address: 0x14007C660
 */

char __fastcall _STAT_DB_BASE::IsRangePerMastery(char byMasteryClass, char byIndex)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  int v5; // [sp+0h] [bp-18h]@1
  char v6; // [sp+20h] [bp+8h]@1

  v6 = byMasteryClass;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  v5 = (unsigned __int8)v6;
  switch ( v6 )
  {
    case 0:
      if ( (signed int)(unsigned __int8)byIndex >= 2 )
        goto $LN12_25;
      result = 1;
      break;
    case 1:
$LN12_25:
      if ( byIndex )
        goto $LN10_26;
      result = 1;
      break;
    case 2:
$LN10_26:
      if ( byIndex )
        goto $LN8_26;
      result = 1;
      break;
    case 3:
$LN8_26:
      if ( (signed int)(unsigned __int8)byIndex >= 48 )
        goto $LN6_29;
      result = 1;
      break;
    case 4:
$LN6_29:
      if ( (signed int)(unsigned __int8)byIndex >= 24 )
        goto $LN4_29;
      result = 1;
      break;
    case 5:
$LN4_29:
      if ( (signed int)(unsigned __int8)byIndex >= 3 )
        goto $LN2_31;
      result = 1;
      break;
    case 6:
$LN2_31:
      if ( byIndex )
        goto LABEL_19;
      result = 1;
      break;
    default:
LABEL_19:
      result = 0;
      break;
  }
  return result;
}
