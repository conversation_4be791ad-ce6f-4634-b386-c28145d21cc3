/*
 * Function: ?InsertGuildBattleScheduleDefaultRecord@CRFWorldDatabase@@QEAA_NIIEE@Z
 * Address: 0x1404A1070
 */

char __fastcall CRFWorldDatabase::InsertGuildBattleScheduleDefaultRecord(CRFWorldDatabase *this, unsigned int uiDayCnt, unsigned int uiMapCnt, char byMaxHour, char byUnitTimeCntPerTime)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v8; // [sp+0h] [bp-468h]@1
  unsigned int v9; // [sp+20h] [bp-448h]@13
  unsigned int v10; // [sp+24h] [bp-444h]@13
  unsigned int v11; // [sp+28h] [bp-440h]@13
  char Dest; // [sp+40h] [bp-428h]@19
  unsigned int j; // [sp+444h] [bp-24h]@13
  unsigned int k; // [sp+448h] [bp-20h]@15
  unsigned int l; // [sp+44Ch] [bp-1Ch]@17
  unsigned __int64 v16; // [sp+458h] [bp-10h]@4
  CRFWorldDatabase *v17; // [sp+470h] [bp+8h]@1
  unsigned int v18; // [sp+478h] [bp+10h]@1
  unsigned int v19; // [sp+480h] [bp+18h]@1
  char v20; // [sp+488h] [bp+20h]@1

  v20 = byMaxHour;
  v19 = uiMapCnt;
  v18 = uiDayCnt;
  v17 = this;
  v5 = &v8;
  for ( i = 280i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v16 = (unsigned __int64)&v8 ^ _security_cookie;
  if ( uiDayCnt && uiMapCnt && byUnitTimeCntPerTime )
  {
    if ( uiDayCnt <= 0x1E && (signed int)(unsigned __int8)byMaxHour <= 24 )
    {
      if ( CRFWorldDatabase::DeleteGuildBattleScheduleInfo(v17) )
      {
        v9 = 0;
        v10 = 0;
        v11 = (unsigned __int8)byUnitTimeCntPerTime * (unsigned __int8)v20;
        for ( j = 0; j < v18; ++j )
        {
          for ( k = 0; k < v19; ++k )
          {
            v10 = k + v19 * j;
            for ( l = 0; l < v11; ++l )
            {
              v9 = j * v11 * v19 + l + k * v11;
              sprintf(
                &Dest,
                "INSERT INTO [dbo].[tbl_GuildBattleScheduleInfo]( [ID], [SLID], [State], [StartTime], [BattleTurm] )VALUE"
                "S( %d, %d, 0, 0, 0 )",
                v9,
                v10);
              if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v17->vfptr, &Dest, 1) )
              {
                CRFWorldDatabase::DeleteGuildBattleScheduleInfo(v17);
                return 0;
              }
            }
          }
        }
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
