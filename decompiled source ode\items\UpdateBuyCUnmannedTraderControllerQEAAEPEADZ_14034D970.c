/*
 * Function: ?UpdateBuy@CUnmannedTraderController@@QEAAEPEAD@Z
 * Address: 0x14034D970
 */

char __fastcall CUnmannedTraderController::UpdateBuy(CUnmannedTraderController *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-C8h]@1
  char *byProcRet; // [sp+20h] [bp-A8h]@8
  unsigned int dwTax; // [sp+28h] [bp-A0h]@18
  _SYSTEMTIME *kCurTime; // [sp+30h] [bp-98h]@18
  char *v9; // [sp+40h] [bp-88h]@4
  char Dst; // [sp+58h] [bp-70h]@4
  _unmannedtrader_buy_item_info kData; // [sp+88h] [bp-40h]@4
  char v12; // [sp+B4h] [bp-14h]@4
  int v13; // [sp+B8h] [bp-10h]@4
  int j; // [sp+BCh] [bp-Ch]@4
  CUnmannedTraderController *v15; // [sp+D0h] [bp+8h]@1

  v15 = this;
  v2 = &v5;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = pData;
  memset_0(&Dst, 0, 0x10ui64);
  GetLocalTime((LPSYSTEMTIME)&Dst);
  time_17((__int64 *)v9 + 2);
  memset_0(&kData, 0, 0x28ui64);
  v12 = 0;
  v13 = 0;
  for ( j = 0; j < (unsigned __int8)v9[25]; ++j )
  {
    if ( !v9[96 * j + 77] )
    {
      byProcRet = &v9[96 * j + 77];
      v12 = CUnmannedTraderController::CheckDBItemState(
              v15,
              v9[24],
              *(_DWORD *)&v9[96 * j + 32],
              &v9[96 * j + 84],
              byProcRet);
      if ( !v9[96 * j + 77] )
      {
        v12 = CRFWorldDatabase::Select_UnmannedTraderBuySingleItemInfo(
                pkDB,
                v9[24],
                *(_DWORD *)&v9[96 * j + 32],
                &kData);
        if ( v12 == 1 )
        {
          v9[96 * j + 77] = 31;
        }
        else if ( v12 == 2 )
        {
          v9[96 * j + 77] = 32;
        }
        else if ( kData.dwPrice == *(_DWORD *)&v9[96 * j + 108] )
        {
          v9[96 * j + 85] = kData.byInveninx;
          *(_DWORD *)&v9[96 * j + 88] = kData.dwK;
          *(_QWORD *)&v9[96 * j + 96] = kData.dwD;
          *(_DWORD *)&v9[96 * j + 104] = kData.dwU;
          *(_DWORD *)&v9[96 * j + 112] = kData.dwT;
          if ( kData.lnUID )
            *(_QWORD *)&v9[96 * j + 120] = kData.lnUID;
          else
            *(_QWORD *)&v9[96 * j + 120] = UIDGenerator::getuid(unk_1799C608C);
          kCurTime = (_SYSTEMTIME *)&Dst;
          dwTax = *(_DWORD *)&v9[96 * j + 80];
          LODWORD(byProcRet) = *((_DWORD *)v9 + 1);
          if ( !CRFWorldDatabase::Update_UnmannedTraderResutlInfo(
                  pkDB,
                  v9[24],
                  *(_DWORD *)&v9[96 * j + 32],
                  10,
                  (unsigned int)byProcRet,
                  dwTax,
                  (_SYSTEMTIME *)&Dst) )
            v9[96 * j + 77] = 33;
        }
        else
        {
          v9[96 * j + 77] = 44;
        }
      }
    }
  }
  return 0;
}
