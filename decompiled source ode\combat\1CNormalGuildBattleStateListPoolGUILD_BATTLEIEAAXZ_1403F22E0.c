/*
 * Function: ??1CNormalGuildBattleStateListPool@GUILD_BATTLE@@IEAA@XZ
 * Address: 0x1403F22E0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleStateListPool::~CNormalGuildBattleStateListPool(GUILD_BATTLE::CNormalGuildBattleStateListPool *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  GUILD_BATTLE::CNormalGuildBattleStateList *v4; // [sp+20h] [bp-28h]@5
  GUILD_BATTLE::CNormalGuildBattleStateList *v5; // [sp+28h] [bp-20h]@5
  void *v6; // [sp+30h] [bp-18h]@6
  GUILD_BATTLE::CNormalGuildBattleStateListPool *v7; // [sp+50h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7->m_pkStateList )
  {
    v5 = v7->m_pkStateList;
    v4 = v5;
    if ( v5 )
      v6 = GUILD_BATTLE::CNormalGuildBattleStateList::`vector deleting destructor'(v4, 3u);
    else
      v6 = 0i64;
    v7->m_pkStateList = 0i64;
  }
}
