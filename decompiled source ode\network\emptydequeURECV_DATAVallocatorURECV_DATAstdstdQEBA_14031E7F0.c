/*
 * Function: ?empty@?$deque@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@QEBA_NXZ
 * Address: 0x14031E7F0
 */

bool __fastcall std::deque<RECV_DATA,std::allocator<RECV_DATA>>::empty(std::deque<RECV_DATA,std::allocator<RECV_DATA> > *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // [sp+0h] [bp-18h]@1
  std::deque<RECV_DATA,std::allocator<RECV_DATA> > *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  return v5->_Mysize == 0;
}
