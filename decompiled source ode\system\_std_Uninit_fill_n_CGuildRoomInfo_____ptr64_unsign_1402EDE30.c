/*
 * Function: _std::_Uninit_fill_n_CGuildRoomInfo_____ptr64_unsigned___int64_CGuildRoomInfo_std::allocator_CGuildRoomInfo____::_1_::catch$0
 * Address: 0x1402EDE30
 */

void __fastcall __noreturn std::_Uninit_fill_n_CGuildRoomInfo_____ptr64_unsigned___int64_CGuildRoomInfo_std::allocator_CGuildRoomInfo____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 64); *(_QWORD *)(i + 32) += 96i64 )
    std::allocator<CGuildRoomInfo>::destroy(*(std::allocator<CGuildRoomInfo> **)(i + 88), *(CGuildRoomInfo **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
