/*
 * Function: ?IsRidingShip@CPlayer@@QEAA_NXZ
 * Address: 0x1400F0D40
 */

bool __fastcall CPlayer::IsRidingShip(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  CPlayer *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return v5->m_pCurMap == g_TransportShip[10162 * CPlayerDB::GetRaceCode(&v5->m_Param) + 2];
}
