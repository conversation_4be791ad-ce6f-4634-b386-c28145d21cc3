/*
 * Function: ?SendMsg_HolyKeeperAttackAbleState@CHolyStoneSystem@@QEAAX_N@Z
 * Address: 0x14027FEE0
 */

void __fastcall CHolyStoneSystem::SendMsg_HolyKeeperAttackAbleState(CHolyStoneSystem *this, bool bAttackAble)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  char v6; // [sp+38h] [bp-40h]@4
  bool v7; // [sp+39h] [bp-3Fh]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4
  unsigned int dwClientIndex; // [sp+64h] [bp-14h]@4
  CHolyStoneSystem *v11; // [sp+80h] [bp+8h]@1
  bool v12; // [sp+88h] [bp+10h]@1

  v12 = bAttackAble;
  v11 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  *(_DWORD *)szMsg = g_Keeper->m_dwObjSerial;
  v6 = CHolyStoneSystem::GetHolyMasterRace(v11);
  v7 = v12;
  pbyType = 25;
  v9 = 22;
  for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
  {
    if ( *(&g_Player.m_bLive + 50856 * (signed int)dwClientIndex) )
      CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, szMsg, 6u);
  }
}
