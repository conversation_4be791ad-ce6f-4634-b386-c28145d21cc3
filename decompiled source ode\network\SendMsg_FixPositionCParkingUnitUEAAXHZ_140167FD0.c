/*
 * Function: ?SendMsg_FixPosition@CParkingUnit@@UEAAXH@Z
 * Address: 0x140167FD0
 */

void __fastcall CParkingUnit::SendMsg_FixPosition(CParkingUnit *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-98h]@1
  char szMsg[2]; // [sp+38h] [bp-60h]@4
  unsigned int v6; // [sp+3Ah] [bp-5Eh]@4
  char v7; // [sp+3Eh] [bp-5Ah]@4
  char Dst; // [sp+3Fh] [bp-59h]@4
  __int16 pShort; // [sp+45h] [bp-53h]@4
  unsigned int v10; // [sp+4Bh] [bp-4Dh]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v12; // [sp+65h] [bp-33h]@4
  unsigned __int64 v13; // [sp+80h] [bp-18h]@4
  CParkingUnit *v14; // [sp+A0h] [bp+8h]@1
  int dwClientIndex; // [sp+A8h] [bp+10h]@1

  dwClientIndex = n;
  v14 = this;
  v2 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (unsigned __int64)&v4 ^ _security_cookie;
  *(_WORD *)szMsg = v14->m_ObjID.m_wIndex;
  v6 = v14->m_dwObjSerial;
  v7 = v14->m_byFrame;
  memcpy_0(&Dst, v14->m_byPartCode, 6ui64);
  FloatToShort(v14->m_fCurPos, &pShort, 3);
  v10 = v14->m_pOwner->m_dwObjSerial;
  pbyType = 4;
  v12 = 16;
  CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, szMsg, 0x17u);
}
