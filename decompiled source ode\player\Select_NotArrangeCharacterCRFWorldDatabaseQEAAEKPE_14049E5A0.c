/*
 * Function: ?Select_NotArrangeCharacter@CRFWorldDatabase@@QEAAEKPEAU_worlddb_arrange_char_info@@@Z
 * Address: 0x14049E5A0
 */

char __fastcall CRFWorldDatabase::Select_NotArrangeCharacter(CRFWorldDatabase *this, unsigned int dwAccountSerial, _worlddb_arrange_char_info *pCharData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@16
  SQLLEN v9; // [sp+38h] [bp-150h]@16
  __int16 v10; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  int v12; // [sp+164h] [bp-24h]@4
  int v13; // [sp+168h] [bp-20h]@4
  unsigned __int64 v14; // [sp+178h] [bp-10h]@4
  CRFWorldDatabase *v15; // [sp+190h] [bp+8h]@1
  _worlddb_arrange_char_info *v16; // [sp+1A0h] [bp+18h]@1

  v16 = pCharData;
  v15 = this;
  v3 = &v6;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = (unsigned __int64)&v6 ^ _security_cookie;
  v12 = 0;
  v13 = 0;
  sprintf(&Dest, "{ CALL pSelect_NotArrangeCharacter( %d ) }", dwAccountSerial);
  if ( v15->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v15->vfptr, &Dest);
  if ( v15->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v15->vfptr) )
  {
    v10 = SQLExecDirect_0(v15->m_hStmtSelect, &Dest, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v15->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v10, &Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v10, v15->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      while ( 1 )
      {
        v10 = SQLFetch_0(v15->m_hStmtSelect);
        if ( v10 )
        {
          if ( v10 != 1 )
            break;
        }
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 1u, -18, &v16->ArrangeChar[v12], 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 2u, -6, &v16->ArrangeChar[v12].byLv, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 3u, -6, &v16->ArrangeChar[v12].byRaceSexCode, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 4u, 4, &v16->ArrangeChar[v12].dwDalant, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 5u, 4, &v16->ArrangeChar[v12].dwGold, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)17;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 6u, 1, v16->ArrangeChar[v12].wszName, 17i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)33;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 7u, 1, v16->ArrangeChar[v12].szServer, 33i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)5;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 8u, 1, v16->ArrangeChar[v12++].szClassCode, 5i64, &v9);
      }
      v16->byCount = v12;
      if ( v15->m_hStmtSelect )
        SQLCloseCursor_0(v15->m_hStmtSelect);
      if ( v15->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v15->vfptr, "%s Success", &Dest);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v15->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
