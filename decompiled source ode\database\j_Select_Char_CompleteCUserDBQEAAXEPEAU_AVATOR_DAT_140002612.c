/*
 * Function: j_?Select_Char_Complete@CUserDB@@QEAAXEPEAU_AVATOR_DATA@@PEA_NKKK1ENN_N1E@Z
 * Address: 0x140002612
 */

void __fastcall CUserDB::Select_Char_Complete(CUserDB *this, char byRetCode, _AVATOR_DATA *pLoadData, bool *pbAddItem, unsigned int dwAddDalant, unsigned int dwAddGold, unsigned int dwCheckSum, bool *pbTrunkAddItem, char byTrunkOldSlot, long double dTrunkOldDalant, long double dTrunkOldGold, bool bCreateTrunk<PERSON>ree, bool *pbExtTrunkAddItem, char byExtTrunkOldSlot)
{
  CUserDB::Select_Char_Complete(
    this,
    byRetCode,
    pLoadData,
    pbAddItem,
    dwAddDalant,
    dwAddGold,
    dwCheckSum,
    pbTrunkAddItem,
    byTrunkOldSlot,
    dTrunkOldDalant,
    dTrunkOldGold,
    bCreateTrunkFree,
    pbExtTrunkAddItem,
    byExtTrunkOldSlot);
}
