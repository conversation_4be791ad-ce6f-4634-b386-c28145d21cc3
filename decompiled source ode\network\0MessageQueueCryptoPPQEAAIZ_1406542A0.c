/*
 * Function: ??0MessageQueue@CryptoPP@@QEAA@I@Z
 * Address: 0x1406542A0
 */

CryptoPP::MessageQueue *__fastcall CryptoPP::MessageQueue::MessageQueue(CryptoPP::MessageQueue *this, unsigned int a2)
{
  __int64 v3; // [sp+20h] [bp-28h]@1
  int v4; // [sp+28h] [bp-20h]@1
  __int64 v5; // [sp+30h] [bp-18h]@1
  CryptoPP::MessageQueue *v6; // [sp+50h] [bp+8h]@1
  unsigned int v7; // [sp+58h] [bp+10h]@1

  v7 = a2;
  v6 = this;
  v5 = -2i64;
  CryptoPP::AutoSignaling<CryptoPP::BufferedTransformation>::AutoSignaling<CryptoPP::BufferedTransformation>(
    this,
    0xFFFFFFFFi64);
  *(_QWORD *)v6 = &CryptoPP::MessageQueue::`vftable'{for `CryptoPP::Algorithm'};
  *((_QWORD *)v6 + 1) = &CryptoPP::MessageQueue::`vftable'{for `CryptoPP::Waitable'};
  CryptoPP::ByteQueue::ByteQueue((CryptoPP::ByteQueue *)((char *)v6 + 32), v7);
  v3 = 0i64;
  std::deque<unsigned __int64,std::allocator<unsigned __int64>>::deque<unsigned __int64,std::allocator<unsigned __int64>>(
    (char *)v6 + 112,
    1i64,
    &v3);
  v4 = 0;
  std::deque<unsigned int,std::allocator<unsigned int>>::deque<unsigned int,std::allocator<unsigned int>>(
    (char *)v6 + 168,
    1i64,
    &v4);
  return v6;
}
