/*
 * Function: ?DQSCompleteInAtradTaxMoney@CUnmannedTraderTaxRateManager@@QEAAXEPEAD@Z
 * Address: 0x14038E390
 */

void __fastcall CUnmannedTraderTaxRateManager::DQSCompleteInAtradTaxMoney(CUnmannedTraderTaxRateManager *this, char byRace, char *pdata)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  TRC_AutoTrade **v5; // rax@7
  __int64 v6; // [sp+0h] [bp-28h]@1
  CUnmannedTraderTaxRateManager *v7; // [sp+30h] [bp+8h]@1
  char v8; // [sp+38h] [bp+10h]@1
  char *pdataa; // [sp+40h] [bp+18h]@1

  pdataa = pdata;
  v8 = byRace;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( !std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::empty(&v7->m_vecTRC)
    && std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::size(&v7->m_vecTRC) > (unsigned __int8)v8 )
  {
    v5 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](&v7->m_vecTRC, (unsigned __int8)v8);
    TRC_AutoTrade::AddGDalant(*v5, pdataa);
  }
}
