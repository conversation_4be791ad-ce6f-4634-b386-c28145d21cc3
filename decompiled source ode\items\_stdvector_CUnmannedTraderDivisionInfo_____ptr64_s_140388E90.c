/*
 * Function: _std::vector_CUnmannedTraderDivisionInfo_____ptr64_std::allocator_CUnmannedTraderDivisionInfo_____ptr64___::erase_::_1_::dtor$2
 * Address: 0x140388E90
 */

void __fastcall std::vector_CUnmannedTraderDivisionInfo_____ptr64_std::allocator_CUnmannedTraderDivisionInfo_____ptr64___::erase_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 40) & 1 )
  {
    *(_DWORD *)(a2 + 40) &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(*(std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > **)(a2 + 88));
  }
}
