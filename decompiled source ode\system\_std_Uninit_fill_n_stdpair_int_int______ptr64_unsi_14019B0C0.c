/*
 * Function: _std::_Uninit_fill_n_std::pair_int_int______ptr64_unsigned___int64_std::pair_int_int__std::allocator_std::pair_int_int______::_1_::catch$0
 * Address: 0x14019B0C0
 */

void __fastcall __noreturn std::_Uninit_fill_n_std::pair_int_int______ptr64_unsigned___int64_std::pair_int_int__std::allocator_std::pair_int_int______::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 64); *(_QWORD *)(i + 32) += 8i64 )
    std::allocator<std::pair<int,int>>::destroy(
      *(std::allocator<std::pair<int,int> > **)(i + 88),
      *(std::pair<int,int> **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
