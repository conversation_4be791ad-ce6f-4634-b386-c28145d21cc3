/*
 * Function: ?SendMsg_Destroy@CParkingUnit@@QEAAXE@Z
 * Address: 0x140167E20
 */

void __fastcall CParkingUnit::SendMsg_Destroy(CParkingUnit *this, char byDestoryType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg[2]; // [sp+34h] [bp-44h]@4
  unsigned int v6; // [sp+36h] [bp-42h]@4
  char v7; // [sp+3Ah] [bp-3Eh]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4
  CParkingUnit *v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  *(_WORD *)szMsg = v10->m_ObjID.m_wIndex;
  v6 = v10->m_dwObjSerial;
  v7 = byDestoryType;
  pbyType = 3;
  v9 = 29;
  CGameObject::CircleReport((CGameObject *)&v10->vfptr, &pbyType, szMsg, 7, 0);
}
