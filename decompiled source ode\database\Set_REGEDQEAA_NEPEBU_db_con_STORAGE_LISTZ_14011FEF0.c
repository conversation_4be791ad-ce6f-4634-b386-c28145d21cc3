/*
 * Function: ?Set@_REGED@@QEAA_NEPEBU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x14011FEF0
 */

char __fastcall _REGED::Set(_REGED *this, char bySlot, _STORAGE_LIST::_db_con *pItem)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  _REGED *v7; // [sp+30h] [bp+8h]@1
  char v8; // [sp+38h] [bp+10h]@1
  _STORAGE_LIST::_db_con *v9; // [sp+40h] [bp+18h]@1

  v9 = pItem;
  v8 = bySlot;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( _EQUIPKEY::IsFilled(&v7->m_EquipKey[(unsigned __int8)bySlot]) )
  {
    result = 0;
  }
  else
  {
    v7->m_EquipKey[(unsigned __int8)v8].zItemIndex = v9->m_wItemIndex;
    v7->m_dwFixEquipLv[(unsigned __int8)v8] = v9->m_dwLv;
    v7->m_lnUID[(unsigned __int8)v8] = v9->m_lnUID;
    v7->m_dwET[(unsigned __int8)v8] = v9->m_dwT;
    v7->m_byCsMethod[(unsigned __int8)v8] = v9->m_byCsMethod;
    v7->m_dwLendRegdTime[(unsigned __int8)v8] = v9->m_dwLendRegdTime;
    result = 1;
  }
  return result;
}
