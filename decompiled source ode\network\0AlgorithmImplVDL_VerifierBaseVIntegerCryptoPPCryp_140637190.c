/*
 * Function: ??0?$AlgorithmImpl@V?$DL_VerifierBase@VInteger@CryptoPP@@@CryptoPP@@V?$DL_SS@UDL_SignatureKeys_GFP@CryptoPP@@V?$DL_Algorithm_NR@VInteger@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_NR@2@VSHA1@2@H@2@@CryptoPP@@QEAA@XZ
 * Address: 0x140637190
 */

__int64 __fastcall CryptoPP::AlgorithmImpl<CryptoPP::DL_VerifierBase<CryptoPP::Integer>,CryptoPP::DL_SS<CryptoPP::DL_SignatureKeys_GFP,CryptoPP::DL_Algorithm_NR<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_NR,CryptoPP::SHA1,int>>::AlgorithmImpl<CryptoPP::DL_VerifierBase<CryptoPP::Integer>,CryptoPP::DL_SS<CryptoPP::DL_SignatureKeys_GFP,CryptoPP::DL_Algorithm_NR<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_NR,CryptoPP::SHA1,int>>(__int64 a1)
{
  __int64 v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  CryptoPP::DL_VerifierBase<CryptoPP::Integer>::DL_VerifierBase<CryptoPP::Integer>(a1);
  return v2;
}
