/*
 * Function: ?Init@_LIST@_QUEST_DB_BASE@@QEAAXXZ
 * Address: 0x1400768F0
 */

void __fastcall _QUEST_DB_BASE::_LIST::Init(_QUEST_DB_BASE::_LIST *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  _QUEST_DB_BASE::_LIST *v4; // [sp+20h] [bp+8h]@1

  v4 = this;
  v1 = &j;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  v4->byQuestType = -1;
  v4->wIndex = -1;
  for ( j = 0; j < 3; ++j )
    v4->wNum[j] = -1;
  v4->dwPassSec = 0;
}
