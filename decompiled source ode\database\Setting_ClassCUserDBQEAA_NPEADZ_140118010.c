/*
 * Function: ?Setting_Class@CUserDB@@QEAA_NPEAD@Z
 * Address: 0x140118010
 */

char __fastcall CUserDB::Setting_Class(CUserDB *this, char *pszClassCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUserDB *v6; // [sp+30h] [bp+8h]@1
  const char *szRecordCode; // [sp+38h] [bp+10h]@1

  szRecordCode = pszClassCode;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CRecordData::GetRecord(&stru_1799C6420, pszClassCode) )
  {
    strcpy_0(v6->m_AvatorData.dbAvator.m_szClassCode, szRecordCode);
    v6->m_bDataUpdate = 1;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
