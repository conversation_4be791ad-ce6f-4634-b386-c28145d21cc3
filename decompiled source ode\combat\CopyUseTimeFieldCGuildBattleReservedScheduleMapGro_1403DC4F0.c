/*
 * Function: ?CopyUseTimeField@CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAA_NIPEA_N@Z
 * Address: 0x1403DC4F0
 */

bool __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::CopyUseTimeField(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this, unsigned int uiMapID, bool *pbField)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v7->m_uiMapCnt > uiMapID )
    result = GUILD_BATTLE::CGuildBattleReservedSchedule::CopyUseTimeField(v7->m_ppkReservedSchedule[uiMapID], pbField);
  else
    result = 0;
  return result;
}
