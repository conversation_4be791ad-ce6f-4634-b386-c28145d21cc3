/*
 * Function: ?LoadXML@CUnmannedTraderSubClassInfoForceLiverGrade@@UEAA_NPEAVTiXmlElement@@AEAVCLogFile@@KK@Z
 * Address: 0x140383D40
 */

char __fastcall CUnmannedTraderSubClassInfoForceLiverGrade::LoadXML(CUnmannedTraderSubClassInfoForceLiverGrade *this, TiXmlElement *elemSubClass, CLogFile *kLogger, unsigned int dwDivisionID, unsigned int dwClassID)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v8; // [sp+0h] [bp-58h]@1
  unsigned int v9; // [sp+20h] [bp-38h]@7
  int v10; // [sp+28h] [bp-30h]@7
  int v11; // [sp+34h] [bp-24h]@4
  CUnmannedTraderSubClassInfoForceLiverGrade *v12; // [sp+60h] [bp+8h]@1
  CLogFile *v13; // [sp+70h] [bp+18h]@1
  unsigned int v14; // [sp+78h] [bp+20h]@1

  v14 = dwDivisionID;
  v13 = kLogger;
  v12 = this;
  v5 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v11 = -1;
  if ( TiXmlElement::Attribute(elemSubClass, "grade", &v11) && v11 >= 0 && v11 <= 255 )
  {
    v12->m_byGrade = v11;
    result = 1;
  }
  else
  {
    v10 = v11;
    v9 = v12->m_dwID;
    CLogFile::Write(
      v13,
      "CUnmannedTraderSubClassInfoCode::LoadXML( TiXmlElement * elemSubClass, CLogFile & kLogger, DWORD dwDivisionID, DWO"
      "RD dwClassID )\r\n"
      "\t\tDivisionID(%u), ClassID(%u) 0 == elemSubClass->Attribute( grade, &iGrade ) || 0 > iGrade || 255 < iGrade(%d) )!\r\n",
      v14,
      dwClassID);
    result = 0;
  }
  return result;
}
