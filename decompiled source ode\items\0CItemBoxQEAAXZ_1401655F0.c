/*
 * Function: ??0CItemBox@@QEAA@XZ
 * Address: 0x1401655F0
 */

void __fastcall CItemBox::CItemBox(CItemBox *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+28h] [bp-10h]@4
  CItemBox *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CGameObject::CGameObject((CGameObject *)&v5->vfptr);
  v5->vfptr = (CGameObjectVtbl *)&CItemBox::`vftable';
  _STORAGE_LIST::_db_con::_db_con(&v5->m_Item);
  v5->m_dwOwnerSerial = -1;
  v5->m_wOwnerIndex = -1;
  v5->m_dwThrowerSerial = -1;
  v5->m_wThrowerIndex = -1;
  v5->m_dwThrowerCharSerial = -1;
  v5->m_byThrowerID = -1;
  v5->m_dwPartyBossSerial = -1;
  v5->m_bPartyShare = 0;
  v5->m_byThrowerDegree = -1;
  v5->m_szThrowerItemHistoryFileName = (char *)operator new[](0x40ui64);
  v5->m_byThrowerRaceCode = -1;
  v5->m_dwEventPartyBoss = -1;
  v5->m_dwEventGuildSerial = -1;
  v5->m_byEventRaceCode = -1;
  v5->m_byEventLootAuth = 3;
  v5->m_bHolyScanner = 0;
  v5->m_dwLootStartTime = 0;
  v5->m_nStateCode = 0;
}
