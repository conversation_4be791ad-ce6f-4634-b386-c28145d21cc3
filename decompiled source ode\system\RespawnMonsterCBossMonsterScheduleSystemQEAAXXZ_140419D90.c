/*
 * Function: ?RespawnMonster@CBossMonsterScheduleSystem@@QEAAXXZ
 * Address: 0x140419D90
 */

void __fastcall CBossMonsterScheduleSystem::RespawnMonster(CBossMonsterScheduleSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // ecx@16
  __int64 v4; // [sp+0h] [bp-A8h]@1
  _dummy_position *pDumPosition; // [sp+20h] [bp-88h]@16
  bool bRobExp; // [sp+28h] [bp-80h]@16
  bool bRewardExp; // [sp+30h] [bp-78h]@16
  bool bDungeon; // [sp+38h] [bp-70h]@16
  bool bWithoutFail; // [sp+40h] [bp-68h]@16
  bool bApplyRopExpField; // [sp+48h] [bp-60h]@16
  int j; // [sp+50h] [bp-58h]@6
  int *v12; // [sp+58h] [bp-50h]@9
  CMapData *pMap; // [sp+60h] [bp-48h]@10
  int k; // [sp+68h] [bp-40h]@10
  __int64 v15; // [sp+70h] [bp-38h]@13
  int v16; // [sp+78h] [bp-30h]@14
  int l; // [sp+7Ch] [bp-2Ch]@14
  _mon_block *v18; // [sp+80h] [bp-28h]@16
  _mon_active *pActiveRec; // [sp+88h] [bp-20h]@16
  int v20; // [sp+90h] [bp-18h]@16
  CMonster *v21; // [sp+98h] [bp-10h]@16
  CBossMonsterScheduleSystem *v22; // [sp+B0h] [bp+8h]@1

  v22 = this;
  v1 = &v4;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !v22->m_bRespawnMonster && v22->m_pCurTBL )
  {
    for ( j = 0; j < v22->m_pCurTBL->m_nCount; ++j )
    {
      v12 = &v22->m_pCurTBL->m_MapScheduleList[j]->m_nIndex;
      if ( v12 )
      {
        pMap = CMapOperation::GetMap(v22->m_pMapOper, (char *)v12 + 4);
        for ( k = 0; k < v12[96]; ++k )
        {
          v15 = *(_QWORD *)(*((_QWORD *)v12 + 49) + 8i64 * k);
          if ( v15 )
          {
            v16 = *(_WORD *)(v15 + 160);
            for ( l = 0; l < v16; ++l )
            {
              v18 = *(_mon_block **)(v15 + 128);
              pActiveRec = *(_mon_active **)(v15 + 136);
              v20 = _mon_block::SelectDummyIndex(v18);
              v3 = pActiveRec->m_wMonRecIndex;
              bApplyRopExpField = 1;
              bWithoutFail = 0;
              bDungeon = 0;
              bRewardExp = 1;
              bRobExp = 1;
              pDumPosition = v18->m_pDumPos[v20];
              v21 = CreateRespawnMonster(pMap, 0, v3, pActiveRec, pDumPosition, 1, 1, 0, 0, 1);
            }
          }
        }
      }
    }
    v22->m_bRespawnMonster = 1;
  }
}
