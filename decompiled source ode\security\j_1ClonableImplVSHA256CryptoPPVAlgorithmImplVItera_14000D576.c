/*
 * Function: j_??1?$ClonableImpl@VSHA256@CryptoPP@@V?$AlgorithmImpl@V?$IteratedHash@IU?$EnumToType@W4ByteOrder@CryptoPP@@$00@CryptoPP@@$0EA@VHashTransformation@2@@CryptoPP@@VSHA256@2@@2@@CryptoPP@@UEAA@XZ
 * Address: 0x14000D576
 */

void __fastcall CryptoPP::ClonableImpl<CryptoPP::SHA256,CryptoPP::AlgorithmImpl<CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>,CryptoPP::SHA256>>::~ClonableImpl<CryptoPP::SHA256,CryptoPP::AlgorithmImpl<CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>,CryptoPP::SHA256>>(CryptoPP::ClonableImpl<CryptoPP::SHA256,CryptoPP::AlgorithmImpl<CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>,CryptoPP::SHA256> > *this)
{
  CryptoPP::ClonableImpl<CryptoPP::SHA256,CryptoPP::AlgorithmImpl<CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>,CryptoPP::SHA256>>::~ClonableImpl<CryptoPP::SHA256,CryptoPP::AlgorithmImpl<CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>,CryptoPP::SHA256>>(this);
}
