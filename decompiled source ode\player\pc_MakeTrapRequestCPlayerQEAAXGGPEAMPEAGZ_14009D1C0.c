/*
 * Function: ?pc_MakeTrapRequest@CPlayer@@QEAAXGGPEAMPEAG@Z
 * Address: 0x14009D1C0
 */

void __usercall CPlayer::pc_MakeTrapRequest(CPlayer *this@<rcx>, unsigned __int16 wSkillIndex@<dx>, unsigned __int16 wTrapItemSerial@<r8w>, float *pfPos@<r9>, float a5@<xmm0>, unsigned __int16 *pConsumeSerial)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  float v8; // xmm0_4@29
  int v9; // eax@31
  int v10; // eax@40
  int v11; // eax@41
  __int64 v12; // [sp+0h] [bp-128h]@1
  int *pnConsume; // [sp+20h] [bp-108h]@43
  bool *pbOverLap; // [sp+28h] [bp-100h]@46
  char v15; // [sp+30h] [bp-F8h]@4
  _TrapItem_fld *pEstTrapItemInfo; // [sp+38h] [bp-F0h]@4
  _STORAGE_LIST::_db_con *pItem; // [sp+40h] [bp-E8h]@4
  _base_fld *v18; // [sp+48h] [bp-E0h]@4
  int v19; // [sp+50h] [bp-D8h]@4
  int pnClassGrade; // [sp+64h] [bp-C4h]@4
  _STORAGE_LIST::_db_con *ppConsumeItems; // [sp+88h] [bp-A0h]@17
  char v22; // [sp+90h] [bp-98h]@17
  int v23; // [sp+B8h] [bp-70h]@17
  char v24; // [sp+BCh] [bp-6Ch]@17
  bool v25; // [sp+E4h] [bp-44h]@17
  char v26; // [sp+E5h] [bp-43h]@17
  bool (__fastcall *v27)(CCharacter *, CCharacter *, float, char *); // [sp+F8h] [bp-30h]@27
  unsigned int dwTrapObjSerial; // [sp+100h] [bp-28h]@39
  int nFP; // [sp+104h] [bp-24h]@43
  CTrap *pTrap; // [sp+108h] [bp-20h]@43
  float v31; // [sp+110h] [bp-18h]@29
  int v32; // [sp+114h] [bp-14h]@41
  CPlayer *pMaster; // [sp+130h] [bp+8h]@1
  unsigned __int16 v34; // [sp+140h] [bp+18h]@1
  float *fTar; // [sp+148h] [bp+20h]@1

  fTar = pfPos;
  v34 = wTrapItemSerial;
  pMaster = this;
  v6 = &v12;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v15 = 0;
  pEstTrapItemInfo = 0i64;
  pItem = 0i64;
  v18 = CRecordData::GetRecord(&stru_1799C8410 + 2, wSkillIndex);
  v19 = 0;
  pnClassGrade = -1;
  if ( pMaster->m_byPosRaceTown == 255 )
  {
    if ( CTrap::IsHaveEmpty() )
    {
      if ( pMaster->m_byMoveType == 2 )
      {
        v15 = 13;
      }
      else if ( _TRAP_PARAM::GetNum(&pMaster->m_pmTrp) < pMaster->m_Param.m_nMakeTrapMaxNum )
      {
        if ( CPlayerDB::IsActableClassSkill(&pMaster->m_Param, v18->m_strCode, &pnClassGrade) )
        {
          if ( pMaster->m_bSFDelayNotCheck
            || _ATTACK_DELAY_CHECKER::IsDelay(&pMaster->m_AttDelayChker, 2, v18->m_dwIndex, pnClassGrade) )
          {
            ppConsumeItems = 0i64;
            memset(&v22, 0, 0x10ui64);
            v23 = 0;
            memset(&v24, 0, 8ui64);
            v25 = 0;
            memset(&v26, 0, 2ui64);
            if ( CPlayer::GetUseConsumeItem(
                   pMaster,
                   (_consume_item_list *)&v18[9].m_strCode[16],
                   pConsumeSerial,
                   &ppConsumeItems,
                   &v23,
                   &v25) )
            {
              GetSqrt(pMaster->m_fCurPos, fTar);
              if ( a5 <= 40.0 )
              {
                pItem = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&pMaster->m_Param.m_dbInven.m_nListNum, v34);
                if ( pItem )
                {
                  if ( pItem->m_byTableCode == 26 )
                  {
                    pEstTrapItemInfo = (_TrapItem_fld *)CRecordData::GetRecord(
                                                          (CRecordData *)&unk_1799C6AA0 + 26,
                                                          pItem->m_wItemIndex);
                    v15 = IsOtherInvalidObjNear((CGameObject *)&pMaster->vfptr, fTar, 0i64, pEstTrapItemInfo);
                    if ( !v15 )
                    {
                      if ( v18[13].m_dwIndex == -1 )
                      {
                        v15 = 12;
                      }
                      else
                      {
                        v27 = (bool (__fastcall *)(CCharacter *, CCharacter *, float, char *))g_TempEffectFunc[v18[13].m_dwIndex];
                        if ( v27 == DE_LayTrap )
                        {
                          v31 = (float)*(signed int *)&v18[9].m_strCode[8];
                          v8 = v31;
                          _effect_parameter::GetEff_Rate(&pMaster->m_EP, 7);
                          a5 = v31 * v8;
                          v19 = (signed int)ffloor(a5);
                          v9 = CPlayer::GetFP(pMaster);
                          if ( v19 <= v9 )
                          {
                            if ( pEstTrapItemInfo->m_nLevelLim >= 40
                              && (!pMaster->m_Param.m_pClassHistory[0]
                               || pMaster->m_Param.m_pClassHistory[0]->m_nClass != 1
                               || !pMaster->m_Param.m_pClassHistory[1]
                               || pMaster->m_Param.m_pClassHistory[1]->m_nClass != 1) )
                            {
                              v15 = 19;
                            }
                          }
                          else
                          {
                            v15 = 14;
                          }
                        }
                        else
                        {
                          v15 = 12;
                        }
                      }
                    }
                  }
                  else
                  {
                    v15 = 3;
                  }
                }
                else
                {
                  v15 = 2;
                }
              }
              else
              {
                v15 = 9;
              }
            }
            else
            {
              v15 = 20;
            }
          }
          else
          {
            v15 = 18;
          }
        }
        else
        {
          v15 = 13;
        }
      }
      else
      {
        v15 = 11;
      }
    }
    else
    {
      v15 = 1;
    }
  }
  else
  {
    v15 = 16;
  }
  dwTrapObjSerial = -1;
  if ( !v15 )
  {
    v10 = CPlayer::GetFP(pMaster);
    if ( v10 - v19 <= 0 )
    {
      v32 = 0;
    }
    else
    {
      v11 = CPlayer::GetFP(pMaster);
      v32 = v11 - v19;
    }
    nFP = v32;
    CPlayer::SetFP(pMaster, v32, 1);
    LODWORD(pnConsume) = pItem->m_wItemIndex;
    pTrap = CreateTrap(pMaster->m_pCurMap, pMaster->m_wMapLayerIndex, fTar, pMaster, (int)pnConsume);
    if ( pTrap )
    {
      if ( IsOverLapItem(26) )
      {
        LOBYTE(pbOverLap) = 1;
        LOBYTE(pnConsume) = 1;
        CPlayer::Emb_AlterDurPoint(pMaster, 0, pItem->m_byStorageIndex, -1, 1, 1);
      }
      else
      {
        pbOverLap = (bool *)"CPlayer::pc_MakeTrapRequest()";
        LOBYTE(pnConsume) = 1;
        if ( !CPlayer::Emb_DelStorage(pMaster, 0, pItem->m_byStorageIndex, 0, 1, "CPlayer::pc_MakeTrapRequest()") )
        {
          CPlayer::SendMsg_CreateTrapResult(pMaster, 12, dwTrapObjSerial);
          return;
        }
      }
      CMgrAvatorItemHistory::consume_del_item(
        &CPlayer::s_MgrItemHistory,
        pMaster->m_ObjID.m_wIndex,
        pItem,
        pMaster->m_szItemHistoryFileName);
      _TRAP_PARAM::PushItem(&pMaster->m_pmTrp, pTrap, pTrap->m_dwObjSerial);
      CPlayer::SendMsg_MadeTrapNumInform(pMaster, pMaster->m_pmTrp.m_nCount);
      dwTrapObjSerial = pTrap->m_dwObjSerial;
      CPlayer::DeleteUseConsumeItem(pMaster, &ppConsumeItems, &v23, &v25);
      _effect_parameter::GetEff_Plus(&pMaster->m_EP, 12);
      _ATTACK_DELAY_CHECKER::SetDelay(
        &pMaster->m_AttDelayChker,
        (signed int)ffloor(*(float *)&v18[9].m_strCode[52] + a5));
    }
    else
    {
      v15 = 1;
    }
  }
  CPlayer::SendMsg_CreateTrapResult(pMaster, v15, dwTrapObjSerial);
}
