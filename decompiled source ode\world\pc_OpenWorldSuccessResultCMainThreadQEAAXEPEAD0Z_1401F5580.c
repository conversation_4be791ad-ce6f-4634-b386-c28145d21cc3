/*
 * Function: ?pc_OpenWorldSuccessResult@CMainThread@@QEAAXEPEAD0@Z
 * Address: 0x1401F5580
 */

void __fastcall CMainThread::pc_OpenWorldSuccessResult(CMainThread *this, char byWorldCode, char *pszDBName, char *pszDBIP)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-178h]@1
  char Buffer; // [sp+30h] [bp-148h]@4
  char v8; // [sp+D0h] [bp-A8h]@4
  unsigned __int64 v9; // [sp+160h] [bp-18h]@4
  CMainThread *v10; // [sp+180h] [bp+8h]@1
  char v11; // [sp+188h] [bp+10h]@1
  char *pszDBNamea; // [sp+190h] [bp+18h]@1
  char *pszDBIPa; // [sp+198h] [bp+20h]@1

  pszDBIPa = pszDBIP;
  pszDBNamea = pszDBName;
  v11 = byWorldCode;
  v10 = this;
  v4 = &v6;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = (unsigned __int64)&v6 ^ _security_cookie;
  _strdate(&Buffer);
  _strtime(&v8);
  __trace("%s-%s: Open World", &Buffer, &v8);
  v10->m_byWorldCode = v11;
  WriteServerStartHistory("DBInit Begin >> name: %s, ip: %s", pszDBNamea, pszDBIPa);
  if ( CMainThread::DatabaseInit(v10, pszDBNamea, pszDBIPa) )
  {
    v10->m_bWorldOpen = 1;
    CMainThread::SerivceSelfStart(v10);
    WriteServerStartHistory("DBInit Complete >>");
  }
  else
  {
    WriteServerStartHistory("DBInit Fail >>");
    CLogFile::WriteString(&v10->m_logSystemError, "init DB fail");
    CWnd::SendMessageA(g_pFrame, 0x10u, 0i64, 0i64);
  }
}
