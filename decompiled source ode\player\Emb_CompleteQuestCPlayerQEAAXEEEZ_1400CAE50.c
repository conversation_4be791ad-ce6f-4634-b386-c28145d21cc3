/*
 * Function: ?Emb_CompleteQuest@CPlayer@@QEAAXEEE@Z
 * Address: 0x1400CAE50
 */

void __usercall CPlayer::Emb_CompleteQuest(CPlayer *this@<rcx>, char byQuestDBSlot@<dl>, char byR<PERSON><PERSON><PERSON>temIndex@<r8b>, char byLinkQuestIndex@<r9b>, float *a5@<xmm0>)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // eax@10
  int v8; // ecx@10
  float *v9; // xmm0_8@10
  int v10; // eax@12
  int v11; // ecx@12
  char v12; // al@16
  __int64 v13; // [sp+0h] [bp-198h]@1
  float *pfStartPos; // [sp+20h] [bp-178h]@10
  unsigned int dwExpRate; // [sp+28h] [bp-170h]@10
  int nGrade; // [sp+30h] [bp-168h]@10
  int *pnMaxPoint; // [sp+38h] [bp-160h]@10
  _MASTERY_PARAM *pData; // [sp+40h] [bp-158h]@10
  unsigned int *pdwAlter; // [sp+48h] [bp-150h]@10
  char *pszFileName; // [sp+50h] [bp-148h]@10
  char byLogType; // [sp+58h] [bp-140h]@10
  char *pszTitle; // [sp+60h] [bp-138h]@10
  void *Src; // [sp+70h] [bp-128h]@5
  _QUEST_DB_BASE::_LIST Dst; // [sp+88h] [bp-110h]@5
  unsigned __int16 *v25; // [sp+A8h] [bp-F0h]@5
  int j; // [sp+B0h] [bp-E8h]@6
  _Quest_fld *pQuestFld; // [sp+B8h] [bp-E0h]@10
  unsigned __int8 v28; // [sp+C0h] [bp-D8h]@11
  _QUEST_DB_BASE::_NPC_QUEST_HISTORY *pHisData; // [sp+C8h] [bp-D0h]@11
  float pNewPos; // [sp+D8h] [bp-C0h]@15
  _dummy_position *pPos; // [sp+F8h] [bp-A0h]@14
  int v32; // [sp+100h] [bp-98h]@18
  unsigned __int8 v33; // [sp+104h] [bp-94h]@24
  _QUEST_DB_BASE::_LIST *pSlotData; // [sp+108h] [bp-90h]@28
  int k; // [sp+110h] [bp-88h]@28
  int l; // [sp+114h] [bp-84h]@34
  char *v37; // [sp+120h] [bp-78h]@10
  unsigned int *v38; // [sp+128h] [bp-70h]@10
  _MASTERY_PARAM *v39; // [sp+130h] [bp-68h]@10
  int *v40; // [sp+138h] [bp-60h]@10
  int v41; // [sp+140h] [bp-58h]@10
  float *v42; // [sp+148h] [bp-50h]@10
  char *v43; // [sp+150h] [bp-48h]@12
  char *v44; // [sp+158h] [bp-40h]@12
  unsigned int *v45; // [sp+160h] [bp-38h]@12
  _MASTERY_PARAM *v46; // [sp+168h] [bp-30h]@12
  int *v47; // [sp+170h] [bp-28h]@12
  int v48; // [sp+178h] [bp-20h]@12
  float *v49; // [sp+180h] [bp-18h]@12
  unsigned __int64 v50; // [sp+188h] [bp-10h]@4
  CPlayer *v51; // [sp+1A0h] [bp+8h]@1
  char v52; // [sp+1A8h] [bp+10h]@1
  char v53; // [sp+1B0h] [bp+18h]@1
  char v54; // [sp+1B8h] [bp+20h]@1

  v54 = byLinkQuestIndex;
  v53 = byRewardItemIndex;
  v52 = byQuestDBSlot;
  v51 = this;
  v5 = &v13;
  for ( i = 100i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v50 = (unsigned __int64)&v13 ^ _security_cookie;
  if ( v51->m_pUserDB )
  {
    Src = (char *)&v51->m_Param.m_QuestDB + 13 * (unsigned __int8)byQuestDBSlot;
    _QUEST_DB_BASE::_LIST::_LIST(&Dst);
    memcpy_0(&Dst, Src, 0xDui64);
    v25 = 0i64;
    if ( *(_BYTE *)Src != 255 )
    {
      for ( j = 0; j < 3; ++j )
      {
        if ( *(_WORD *)((char *)Src + 2 * j + 3) != 0xFFFF )
          return;
      }
      v37 = v51->m_szLvHistoryFileName;
      v38 = v51->m_Param.m_dwAlterMastery;
      v39 = &v51->m_pmMst;
      v40 = v51->m_nMaxPoint;
      v41 = v51->m_Param.m_byPvPGrade;
      CPlayerDB::GetExp(&v51->m_Param);
      v42 = a5;
      v7 = CPlayerDB::GetLevel(&v51->m_Param);
      v8 = v51->m_ObjID.m_wIndex;
      pszTitle = 0i64;
      byLogType = 0;
      pszFileName = v37;
      pdwAlter = v38;
      pData = v39;
      pnMaxPoint = v40;
      nGrade = v41;
      dwExpRate = v51->m_dwExpRate;
      v9 = v42;
      pfStartPos = v42;
      CMgrAvatorLvHistory::update_mastery(
        &CPlayer::s_MgrLvHistory,
        v8,
        v51->m_byUserDgr,
        v7,
        *(long double *)&v42,
        dwExpRate,
        v41,
        v40,
        v39,
        v38,
        v37,
        0,
        0i64);
      CPlayerDB::InitAlterMastery(&v51->m_Param);
      pQuestFld = (_Quest_fld *)CRecordData::GetRecord(CQuestMgr::s_tblQuest, *(_WORD *)((char *)Src + 1));
      v25 = (unsigned __int16 *)CPlayer::_Reward_Quest(v51, pQuestFld, v53);
      CPlayer::SendMsg_QuestComplete(v51, v52);
      if ( *(_BYTE *)Src == 1 )
      {
        v28 = CQuestMgr::InsertNpcQuestHistory(
                &v51->m_QuestMgr,
                pQuestFld->m_strCode,
                pQuestFld->m_nDifficultyLevel,
                pQuestFld->m_dRepeatTime);
        pHisData = &v51->m_Param.m_QuestDB.m_History[v28];
        CUserDB::Update_NPCQuestHistory(v51->m_pUserDB, v28, pHisData);
        CPlayer::SendMsg_NpcQuestHistoryInform(v51, v28);
      }
      CQuestMgr::DeleteQuestData(&v51->m_QuestMgr, v52);
      CUserDB::Update_QuestDelete(v51->m_pUserDB, v52);
      v43 = pQuestFld->m_strCode;
      v44 = v51->m_szLvHistoryFileName;
      v45 = v51->m_Param.m_dwAlterMastery;
      v46 = &v51->m_pmMst;
      v47 = v51->m_nMaxPoint;
      v48 = v51->m_Param.m_byPvPGrade;
      CPlayerDB::GetExp(&v51->m_Param);
      v49 = v9;
      v10 = CPlayerDB::GetLevel(&v51->m_Param);
      v11 = v51->m_ObjID.m_wIndex;
      pszTitle = v43;
      byLogType = 2;
      pszFileName = v44;
      pdwAlter = v45;
      pData = v46;
      pnMaxPoint = v47;
      nGrade = v48;
      dwExpRate = v51->m_dwExpRate;
      pfStartPos = v49;
      CMgrAvatorLvHistory::update_mastery(
        &CPlayer::s_MgrLvHistory,
        v11,
        v51->m_byUserDgr,
        v10,
        *(long double *)&v49,
        dwExpRate,
        v48,
        v47,
        v46,
        v45,
        v44,
        2,
        v43);
      CPlayerDB::InitAlterMastery(&v51->m_Param);
      v51->m_dwUMWHLastTime = GetLoopTime();
      if ( pQuestFld->m_nLinkDummyCond == 1 )
      {
        if ( strcmp_0(pQuestFld->m_strLinkDummyCode, "-1") )
        {
          pPos = CMapData::GetDummyPostion(v51->m_pCurMap, pQuestFld->m_strLinkDummyCode);
          if ( pPos )
          {
            if ( CMapData::GetRandPosInDummy(v51->m_pCurMap, pPos, &pNewPos, 1) )
            {
              pfStartPos = &pNewPos;
              CPlayer::OutOfMap(v51, v51->m_pCurMap, v51->m_wMapLayerIndex, 3, &pNewPos);
              v12 = CPlayerDB::GetMapCode(&v51->m_Param);
              CPlayer::SendMsg_GotoRecallResult(v51, 0, v12, &pNewPos, 4);
            }
          }
        }
      }
      if ( !v25 )
      {
        v32 = 0;
        for ( j = 0; j < 5; ++j )
        {
          if ( strncmp(pQuestFld->m_strLinkQuest[(signed __int64)j], "-1", 2ui64) )
            ++v32;
        }
        if ( v32 > 0 )
        {
          v33 = v54;
          if ( (unsigned __int8)v54 == 255 )
            v33 = rand() % v32;
          v25 = (unsigned __int16 *)CRecordData::GetRecord(
                                      CQuestMgr::s_tblQuest,
                                      pQuestFld->m_strLinkQuest[(unsigned __int64)v33]);
        }
      }
      if ( v25 )
      {
        pSlotData = &v51->m_Param.m_QuestDB.m_List[(unsigned __int8)v52];
        pSlotData->byQuestType = Dst.byQuestType;
        pSlotData->wIndex = *v25;
        pSlotData->dwPassSec = 0;
        for ( k = 0; k < 3; ++k )
        {
          if ( *(_DWORD *)&v25[136 * k + 52] != -1 )
            pSlotData->wNum[k] = 0;
        }
        CUserDB::Update_QuestInsert(v51->m_pUserDB, v52, pSlotData);
        CPlayer::SendMsg_InsertNextQuest(v51, v52, pSlotData);
      }
      for ( l = 0; l < 3; ++l )
      {
        if ( _happen_event_cont::isset(&v51->m_QuestMgr.m_pTempHappenEvent[l]) )
        {
          memcpy_0(&v51->m_QuestMgr.m_LastHappenEvent, &v51->m_QuestMgr.m_pTempHappenEvent[l], 0x18ui64);
          CPlayer::Emb_StartQuest(v51, -1, &v51->m_QuestMgr.m_pTempHappenEvent[l]);
          if ( v51->m_QuestMgr.m_pTempHappenEvent[l].m_QtHpType == 8 )
          {
            CPlayerDB::SetMaxLevel(&v51->m_Param, 50);
            if ( v51->m_pUserDB )
              CUserDB::Update_MaxLevel(v51->m_pUserDB, 50);
          }
          _happen_event_cont::init(&v51->m_QuestMgr.m_pTempHappenEvent[l]);
        }
      }
    }
  }
}
