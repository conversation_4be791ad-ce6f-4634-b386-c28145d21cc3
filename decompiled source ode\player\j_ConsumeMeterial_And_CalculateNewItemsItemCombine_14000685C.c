/*
 * Function: j_?ConsumeMeterial_And_CalculateNewItems@ItemCombineMgr@@IEAAEPEAPEAU_db_con@_STORAGE_LIST@@EPEAU_list@_combine_ex_item_request_clzo@@PEAU_combine_ex_item_result_zocl@@PEAU_ItemCombine_exp_fld@@EH@Z
 * Address: 0x14000685C
 */

char __fastcall ItemCombineMgr::ConsumeMeterial_And_CalculateNewItems(ItemCombineMgr *this, _STORAGE_LIST::_db_con **pMt_Sv_Inv, char byMt<PERSON>lotNum, _combine_ex_item_request_clzo::_list *pipMaterials, _combine_ex_item_result_zocl *pSaveData, _ItemCombine_exp_fld *pfld, char byLinkTableIndex, int nType)
{
  return ItemCombineMgr::ConsumeMeterial_And_CalculateNewItems(
           this,
           pMt_Sv_Inv,
           byMtSlotNum,
           pipMaterials,
           pSaveData,
           pfld,
           byLinkTableIndex,
           nType);
}
