/*
 * Function: ?<PERSON><PERSON>@CPlayer@@QEAA_NPEAVCCharacter@@@Z
 * Address: 0x14004F140
 */

char __usercall CPlayer::<PERSON><PERSON>@<al>(CPlayer *this@<rcx>, <PERSON><PERSON><PERSON> *pAtter@<rdx>, double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // r8@5
  unsigned int v6; // eax@6
  char *v7; // rax@21
  int v8; // eax@29
  int v9; // eax@31
  double v10; // xmm0_8@34
  long double v11; // xmm0_8@41
  int v12; // eax@41
  CGuildBattleController *v13; // rax@46
  unsigned int v14; // eax@49
  unsigned int v15; // eax@49
  CGuildBattleController *v16; // rax@56
  unsigned int v17; // eax@59
  unsigned int v18; // eax@59
  unsigned int v19; // eax@65
  unsigned int v20; // eax@66
  unsigned int v21; // eax@71
  unsigned int v22; // eax@71
  CGameObjectVtbl *v23; // rax@73
  __int64 v24; // r8@73
  unsigned int v25; // eax@78
  unsigned int v26; // eax@78
  CGuildBattleController *v27; // rax@81
  __int64 v28; // rax@91
  __int64 v30; // [sp+0h] [bp-188h]@1
  char *pszFileName; // [sp+20h] [bp-168h]@6
  unsigned __int16 wNewExpRate; // [sp+28h] [bp-160h]@6
  char *pCause; // [sp+30h] [bp-158h]@6
  char *v34; // [sp+38h] [bp-150h]@6
  _sf_continous *v35; // [sp+40h] [bp-148h]@4
  int v36; // [sp+48h] [bp-140h]@6
  char v37; // [sp+54h] [bp-134h]@6
  char v38; // [sp+64h] [bp-124h]@6
  unsigned __int16 v39; // [sp+68h] [bp-120h]@15
  unsigned __int16 v40; // [sp+6Ch] [bp-11Ch]@18
  int v41; // [sp+70h] [bp-118h]@27
  unsigned int v42; // [sp+74h] [bp-114h]@31
  float v43; // [sp+78h] [bp-110h]@31
  double v44; // [sp+80h] [bp-108h]@34
  float v45; // [sp+88h] [bp-100h]@34
  double v46; // [sp+90h] [bp-F8h]@34
  CPlayer *pkSrcPlayer; // [sp+98h] [bp-F0h]@43
  CCharacter *v48; // [sp+A0h] [bp-E8h]@52
  CPlayer *v49; // [sp+A8h] [bp-E0h]@52
  CCharacter *v50; // [sp+B0h] [bp-D8h]@62
  CPlayer *v51; // [sp+B8h] [bp-D0h]@62
  CCharacter *v52; // [sp+C0h] [bp-C8h]@69
  CPlayer *v53; // [sp+C8h] [bp-C0h]@69
  _base_fld *v54; // [sp+D0h] [bp-B8h]@72
  char v55; // [sp+E4h] [bp-A4h]@73
  int v56; // [sp+F4h] [bp-94h]@73
  unsigned int v57; // [sp+F8h] [bp-90h]@73
  __int16 v58; // [sp+FCh] [bp-8Ch]@73
  CCharacter *v59; // [sp+100h] [bp-88h]@76
  CPlayer *v60; // [sp+108h] [bp-80h]@76
  CPlayer *v61; // [sp+110h] [bp-78h]@87
  struct CHolyStone *v62; // [sp+118h] [bp-70h]@93
  CGameObjectVtbl *v63; // [sp+120h] [bp-68h]@6
  char *v64; // [sp+128h] [bp-60h]@21
  char *pszDeathName; // [sp+130h] [bp-58h]@21
  CGameObjectVtbl *v66; // [sp+138h] [bp-50h]@21
  int v67; // [sp+140h] [bp-48h]@31
  CGameObjectVtbl *v68; // [sp+148h] [bp-40h]@31
  int v69; // [sp+150h] [bp-38h]@31
  float v70; // [sp+154h] [bp-34h]@32
  double v71; // [sp+158h] [bp-30h]@35
  double v72; // [sp+160h] [bp-28h]@38
  char *v73; // [sp+168h] [bp-20h]@41
  char *v74; // [sp+170h] [bp-18h]@41
  CPlayer *v75; // [sp+190h] [bp+8h]@1
  CCharacter *v76; // [sp+198h] [bp+10h]@1

  v76 = pAtter;
  v75 = this;
  v3 = &v30;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CCharacter::Stop((CCharacter *)&v75->vfptr);
  CPlayer::DTradeInit(v75);
  v35 = CPlayer::GetAfterEffect(v75);
  if ( v35 )
  {
    CCharacter::SFContInit((CCharacter *)&v75->vfptr);
    if ( v75->m_bAfterEffect )
    {
      v36 = 1;
      v6 = v35->m_wEffectIndex;
      v63 = v75->vfptr;
      v34 = (char *)v76;
      pCause = &v37;
      LOBYTE(wNewExpRate) = 1;
      LOWORD(pszFileName) = v35->m_wDurSec;
      LOBYTE(v5) = 3;
      v38 = ((int (__fastcall *)(CPlayer *, _QWORD, __int64, _QWORD))v63[1].__vecDelDtor)(v75, 0i64, v5, v6);
      if ( !v38 && (signed int)v35->m_wDurSec > 0 )
        v75->m_bAfterEffect = 1;
    }
  }
  else
  {
    CCharacter::SFContInit((CCharacter *)&v75->vfptr);
  }
  if ( v75->m_bMineMode )
  {
    v75->m_bMineMode = 0;
    v75->m_dwMineNextTime = -1;
    CPlayer::SendMsg_MineCancle(v75);
  }
  if ( v75->m_pSiegeItem )
    CPlayer::SetSiege(v75, 0i64);
  CGameObject::SetBreakTranspar((CGameObject *)&v75->vfptr, 0);
  CPlayer::SendMsg_Die(v75);
  _effect_parameter::GetEff_Have(&v75->m_EP, 51);
  *(float *)&a3 = *(float *)&a3 * 100.0;
  v39 = (signed int)ffloor(*(float *)&a3);
  if ( (signed int)v39 > 100 )
    v39 = 100;
  if ( (signed int)v39 > 0 )
  {
    v40 = rand() % 100;
    if ( v40 <= (signed int)v39 )
      CPlayer::SendMsg_RevivalOfJade(v75, v39);
  }
  if ( v76 )
  {
    v64 = v75->m_szLvHistoryFileName;
    pszDeathName = CPlayerDB::GetCharNameW(&v75->m_Param);
    v66 = v76->vfptr;
    LODWORD(v7) = ((int (__fastcall *)(CCharacter *))v66->GetObjName)(v76);
    CMgrAvatorLvHistory::die(&CPlayer::s_MgrLvHistory, v75->m_ObjID.m_wIndex, v7, pszDeathName, v64);
  }
  if ( v76
    && !(unsigned __int8)((int (__fastcall *)(CPlayer *))v75->vfptr->IsInTown)(v75)
    && !v75->m_pCurMap->m_pMapSet->m_nMapType )
  {
    CPlayerDB::GetExp(&v75->m_Param);
    if ( a3 > 0.0 && v76->m_ObjID.m_byID == 1 )
    {
      v41 = CPlayerDB::GetMaxLevel(&v75->m_Param);
      if ( LOBYTE(v76[1].m_fCurPos[0]) )
      {
        if ( CPlayerDB::GetLevel(&v75->m_Param) > 7 )
        {
          v8 = CPlayerDB::GetLevel(&v75->m_Param);
          if ( v8 <= v41 )
          {
            if ( rand() % 2 )
            {
              v42 = v75->m_dwExpRate;
              v67 = CPlayerDB::GetLevel(&v75->m_Param);
              v68 = v76->vfptr;
              v9 = ((int (__fastcall *)(CCharacter *))v68->GetLevel)(v76);
              v69 = abs_0(v67 - v9);
              v43 = (float)(v69 + rand() % 5 + 1);
              v70 = v43 >= 20.0 ? FLOAT_20_0 : v43;
              *(_QWORD *)&v10 = LODWORD(v70);
              *(float *)&v10 = v70 * 0.0099999998;
              v43 = v70 * 0.0099999998;
              CPlayerDB::GetExp(&v75->m_Param);
              v44 = v10;
              v45 = (float)(rand() % 100) / 100.0;
              v46 = v44 * v43 * v45;
              v71 = v46 <= 0.0 ? 0.0 : v46;
              v46 = v71;
              v72 = v44 <= v71 ? v44 : v46;
              v46 = v72;
              if ( v72 > 1.0 )
              {
                v11 = -0.0 - v46;
                CPlayer::AlterExp(v75, -0.0 - v46, 0, 0, 0);
                v73 = v75->m_szLvHistoryFileName;
                v74 = (char *)(*(_QWORD *)&v76[1].m_fAbsPos[1] + 4i64);
                CPlayerDB::GetExp(&v75->m_Param);
                v12 = v75->m_ObjID.m_wIndex;
                v34 = v73;
                pCause = v74;
                wNewExpRate = v75->m_dwExpRate;
                CMgrAvatorLvHistory::down_exp(&CPlayer::s_MgrLvHistory, v12, v44, v42, v11, wNewExpRate, v74, v73);
              }
            }
          }
        }
      }
    }
    if ( v76->m_ObjID.m_byID )
    {
      switch ( v76->m_ObjID.m_byID )
      {
        case 3:
          v48 = v76;
          v49 = *(CPlayer **)&v76[1].m_bLive;
          if ( v49
            && v75->m_bInGuildBattle
            && v49->m_bInGuildBattle
            && v75->m_byGuildBattleColorInx != v49->m_byGuildBattleColorInx )
          {
            v16 = CGuildBattleController::Instance();
            CGuildBattleController::Kill(v16, v49, v75);
          }
          if ( v49 && !CPlayer::IsChaosMode(v49) )
          {
            v17 = CPlayerDB::GetCharSerial(&v75->m_Param);
            CPvpOrderView::UpdatePvPKill(&v49->m_kPvpOrderView, v49->m_ObjID.m_wIndex, v17);
            v18 = CPlayerDB::GetCharSerial(&v49->m_Param);
            CPvpOrderView::UpdatePvPDeath(&v75->m_kPvpOrderView, v75->m_ObjID.m_wIndex, v18);
          }
          break;
        case 7:
          v50 = v76;
          v51 = (CPlayer *)v76[1].m_pRecordSet;
          if ( v51 && !CPlayer::IsChaosMode(v51) )
          {
            if ( v51->m_bOper )
            {
              v19 = CPlayerDB::GetCharSerial(&v75->m_Param);
              CPvpOrderView::UpdatePvPKill(&v51->m_kPvpOrderView, v51->m_ObjID.m_wIndex, v19);
            }
            v20 = CPlayerDB::GetCharSerial(&v51->m_Param);
            CPvpOrderView::UpdatePvPDeath(&v75->m_kPvpOrderView, v75->m_ObjID.m_wIndex, v20);
          }
          break;
        case 0xC:
          v52 = v76;
          v53 = *(CPlayer **)&v76[8].m_SFContAura[0][7].m_byLv;
          if ( v53 && !CPlayer::IsChaosMode(v53) )
          {
            v21 = CPlayerDB::GetCharSerial(&v75->m_Param);
            CPvpOrderView::UpdatePvPKill(&v53->m_kPvpOrderView, v53->m_ObjID.m_wIndex, v21);
            v22 = CPlayerDB::GetCharSerial(&v53->m_Param);
            CPvpOrderView::UpdatePvPDeath(&v75->m_kPvpOrderView, v75->m_ObjID.m_wIndex, v22);
          }
          v54 = CRecordData::GetRecord(&stru_1799C8410 + 3, "17");
          if ( v54 )
          {
            v56 = 1;
            v57 = _sf_continous::GetSFContCurTime();
            v58 = *(_WORD *)&v54[16].m_strCode[24];
            v23 = v75->vfptr;
            v34 = (char *)v76;
            pCause = &v55;
            LOBYTE(wNewExpRate) = 1;
            LOWORD(pszFileName) = *(_WORD *)&v54[16].m_strCode[24];
            LOBYTE(v24) = 3;
            ((void (__fastcall *)(CPlayer *, _QWORD, __int64, _QWORD))v23[1].__vecDelDtor)(
              v75,
              v54[13].m_strCode[32],
              v24,
              v54->m_dwIndex);
            v75->m_bAfterEffect = 1;
          }
          break;
        case 4:
          v59 = v76;
          v60 = (CPlayer *)v76[1].m_pRecordSet;
          if ( v60 )
          {
            if ( !CPlayer::IsChaosMode(v60) )
            {
              v25 = CPlayerDB::GetCharSerial(&v75->m_Param);
              CPvpOrderView::UpdatePvPKill(&v60->m_kPvpOrderView, v60->m_ObjID.m_wIndex, v25);
              v26 = CPlayerDB::GetCharSerial(&v60->m_Param);
              CPvpOrderView::UpdatePvPDeath(&v75->m_kPvpOrderView, v75->m_ObjID.m_wIndex, v26);
            }
          }
          break;
      }
    }
    else
    {
      pkSrcPlayer = (CPlayer *)v76;
      if ( v75->m_bInGuildBattle
        && pkSrcPlayer->m_bInGuildBattle
        && v75->m_byGuildBattleColorInx != pkSrcPlayer->m_byGuildBattleColorInx )
      {
        v13 = CGuildBattleController::Instance();
        CGuildBattleController::Kill(v13, pkSrcPlayer, v75);
      }
      if ( pkSrcPlayer && !CPlayer::IsChaosMode(pkSrcPlayer) )
      {
        v14 = CPlayerDB::GetCharSerial(&v75->m_Param);
        CPvpOrderView::UpdatePvPKill(&pkSrcPlayer->m_kPvpOrderView, pkSrcPlayer->m_ObjID.m_wIndex, v14);
        v15 = CPlayerDB::GetCharSerial(&pkSrcPlayer->m_Param);
        CPvpOrderView::UpdatePvPDeath(&v75->m_kPvpOrderView, v75->m_ObjID.m_wIndex, v15);
      }
    }
  }
  if ( v75->m_bInGuildBattle && v75->m_bTakeGravityStone )
  {
    CPlayer::SendMsg_Notify_Gravity_Stone_Owner_Die(v75);
    v27 = CGuildBattleController::Instance();
    CGuildBattleController::DropGravityStone(v27, v75);
  }
  if ( v75->m_pRecalledAnimusChar
    && !_DELAY_PROCESS::Push(&CPlayer::s_AnimusReturnDelay, v75->m_ObjID.m_wIndex, v75->m_dwObjSerial) )
  {
    CPlayer::_AnimusReturn(v75, 2);
  }
  if ( v76 )
  {
    if ( !v76->m_ObjID.m_byID )
    {
      v61 = (CPlayer *)v76;
      if ( CPlayer::IsRidingUnit((CPlayer *)v76) )
      {
        _effect_parameter::SetLock(&v61->m_EP, 1);
        CPlayer::CalcDefTol(v61);
        CPlayer::SetHaveEffect(v61, 0);
      }
    }
  }
  v75->m_bCorpse = 1;
  v75->m_byModeType = 0;
  v75->m_byMoveType = 1;
  v75->m_byStandType = 0;
  v75->m_dwSelfDestructionTime = 0;
  LODWORD(v75->m_fSelfDestructionDamage) = 0;
  if ( v75->m_pCurMap->m_pMapSet->m_nMapType == 1 )
    v75->m_dwNextTimeDungeonDie = GetLoopTime() + 5000;
  v28 = v75->m_byHSKTime;
  if ( v75->m_byHSKTime <= 2 && !v75->m_byCristalBattleDBInfo )
  {
    v62 = &g_Stone[CPlayerDB::GetRaceCode(&v75->m_Param)];
    if ( v62->m_bOper )
    {
      if ( v62->m_pCurMap == v75->m_pCurMap )
        v75->m_pUserDB->m_AvatorData.m_wDiePoint = ++v75->m_wDiePoint;
    }
  }
  CQuestMgr::CheckFailLoop(&v75->m_QuestMgr, 2, 0i64);
  CPlayer::SenseState(v75);
  return 1;
}
