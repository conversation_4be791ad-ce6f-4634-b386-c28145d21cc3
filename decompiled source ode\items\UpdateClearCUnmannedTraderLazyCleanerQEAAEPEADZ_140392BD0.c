/*
 * Function: ?UpdateClear@CUnmannedTraderLazyCleaner@@QEAAEPEAD@Z
 * Address: 0x140392BD0
 */

char __fastcall CUnmannedTraderLazyCleaner::UpdateClear(CUnmannedTraderLazyCleaner *this, char *p)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-68h]@1
  bool *pbRemain; // [sp+20h] [bp-48h]@4
  char Dst; // [sp+38h] [bp-30h]@4
  char v8; // [sp+54h] [bp-14h]@4
  char v9; // [sp+55h] [bp-13h]@4
  char v10; // [sp+56h] [bp-12h]@4
  char v11; // [sp+57h] [bp-11h]@4
  CUnmannedTraderLazyCleaner *v12; // [sp+70h] [bp+8h]@1

  v12 = this;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  pbRemain = (bool *)p;
  memset_0(&Dst, 0, 0x10ui64);
  GetLocalTime((LPSYSTEMTIME)&Dst);
  v8 = CUnmannedTraderLazyCleaner::ProcUpdate(v12, 5, (_SYSTEMTIME *)&Dst, pbRemain);
  v9 = CUnmannedTraderLazyCleaner::ProcUpdate(v12, 3, (_SYSTEMTIME *)&Dst, pbRemain + 1);
  v10 = CUnmannedTraderLazyCleaner::ProcUpdate(v12, 11, (_SYSTEMTIME *)&Dst, pbRemain + 2);
  v11 = CUnmannedTraderLazyCleaner::ProcUpdate(v12, 7, (_SYSTEMTIME *)&Dst, pbRemain + 3);
  if ( v8 != 1 && v9 != 1 && v10 != 1 && v11 != 1 )
    result = 0;
  else
    result = 24;
  return result;
}
