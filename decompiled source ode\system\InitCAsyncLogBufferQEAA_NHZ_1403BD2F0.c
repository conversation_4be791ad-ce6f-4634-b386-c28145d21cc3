/*
 * Function: ?Init@CAsyncLogBuffer@@QEAA_NH@Z
 * Address: 0x1403BD2F0
 */

char __fastcall CAsyncLogBuffer::Init(CAsyncLogBuffer *this, int iMaxBufferSize)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  char *v6; // [sp+20h] [bp-18h]@6
  CAsyncLogBuffer *v7; // [sp+40h] [bp+8h]@1
  int v8; // [sp+48h] [bp+10h]@1

  v8 = iMaxBufferSize;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( iMaxBufferSize )
  {
    v6 = (char *)operator new[](iMaxBufferSize);
    v7->m_szBuffer = v6;
    if ( v7->m_szBuffer )
    {
      v7->m_iMaxBuffSize = v8;
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
