/*
 * Function: ?SetUnitPassiveValue@CNationSettingManager@@QEAAXAEAM@Z
 * Address: 0x140078EF0
 */

void __fastcall CNationSettingManager::SetUnitPassiveValue(CNationSettingManager *this, float *fUnitPv_DefFc)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CNationSettingManager *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  ((void (__fastcall *)(CNationSettingData *))v5->m_pData->vfptr->SetUnitPassiveValue)(v5->m_pData);
}
