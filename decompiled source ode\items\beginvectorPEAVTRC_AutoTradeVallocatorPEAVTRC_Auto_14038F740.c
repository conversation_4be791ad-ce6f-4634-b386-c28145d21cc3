/*
 * Function: ?begin@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAA?AV?$_Vector_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@2@XZ
 * Address: 0x14038F740
 */

std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *__fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::begin(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *result)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v7; // [sp+40h] [bp+8h]@1
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v8; // [sp+48h] [bp+10h]@1

  v8 = result;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(
    result,
    v7->_Myfirst);
  return v8;
}
