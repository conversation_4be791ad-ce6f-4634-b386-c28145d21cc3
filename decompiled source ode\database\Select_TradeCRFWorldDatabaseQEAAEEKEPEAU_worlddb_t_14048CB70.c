/*
 * Function: ?Select_Trade@CRFWorldDatabase@@QEAAEEKEPEAU_worlddb_trade_info@@@Z
 * Address: 0x14048CB70
 */

char __fastcall CRFWorldDatabase::Select_Trade(CRFWorldDatabase *this, char byType, unsigned int dwSerial, char byRace, _worlddb_trade_info *pTradeData)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char *v8; // rax@19
  char *v9; // rax@19
  char *v10; // rax@19
  char *v11; // rax@19
  char *v12; // rax@19
  char *v13; // rax@19
  char *v14; // rax@19
  char *v15; // rax@19
  char *v16; // rax@19
  __int64 v17; // [sp+0h] [bp-528h]@1
  void *SQLStmt; // [sp+20h] [bp-508h]@6
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-500h]@6
  char Dest; // [sp+40h] [bp-4E8h]@6
  SQLLEN v21; // [sp+458h] [bp-D0h]@19
  __int16 v22; // [sp+464h] [bp-C4h]@11
  int v23; // [sp+468h] [bp-C0h]@6
  __int16 TargetValue; // [sp+478h] [bp-B0h]@19
  unsigned __int16 v25; // [sp+47Ah] [bp-AEh]@19
  unsigned __int16 v26; // [sp+47Ch] [bp-ACh]@19
  unsigned __int16 v27; // [sp+47Eh] [bp-AAh]@19
  unsigned __int16 v28; // [sp+480h] [bp-A8h]@19
  unsigned __int16 v29; // [sp+482h] [bp-A6h]@19
  __int16 v30; // [sp+4A8h] [bp-80h]@19
  unsigned __int16 v31; // [sp+4AAh] [bp-7Eh]@21
  unsigned __int16 v32; // [sp+4ACh] [bp-7Ch]@21
  unsigned __int16 v33; // [sp+4AEh] [bp-7Ah]@21
  unsigned __int16 v34; // [sp+4B0h] [bp-78h]@21
  unsigned __int16 v35; // [sp+4B2h] [bp-76h]@21
  tm _Tm; // [sp+4D8h] [bp-50h]@19
  __int64 v37; // [sp+508h] [bp-20h]@19
  unsigned __int64 v38; // [sp+518h] [bp-10h]@4
  CRFWorldDatabase *v39; // [sp+530h] [bp+8h]@1

  v39 = this;
  v5 = &v17;
  for ( i = 328i64; i; --i )
  {
    *(_DWORD *)v5 = -*********;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v38 = (unsigned __int64)&v17 ^ _security_cookie;
  if ( pTradeData )
  {
    v23 = 0;
    LODWORD(StrLen_or_IndPtr) = (unsigned __int8)byRace;
    LODWORD(SQLStmt) = dwSerial;
    sprintf(
      &Dest,
      "select top %u r.[state], s.[serial], i.[inveninx], s.[price], s.[regdate], s.[sellturm], r.[buyer], r.[tax], r.[re"
      "sultdate], b.[Name], b.[Account] from [dbo].[tbl_utsellinfo] as s join [dbo].[tbl_utresultinfo] as r on s.[type] ="
      " %u and s.[owner] = %u and s.[race] = %u and s.[type] = r.[type] and s.[serial] = r.[serial] and r.[state] in ( 1,"
      " 2, 4, 6, 10, 13 ) join [dbo].[tbl_utsingleiteminfo] as i on r.[serial]  = i.[serial] left join [dbo].[tbl_base] a"
      "s b on r.[buyer] = b.[serial] order by s.[regdate]",
      20i64,
      (unsigned __int8)byType);
    if ( v39->m_bSaveDBLog )
      CRFNewDatabase::Log((CRFNewDatabase *)&v39->vfptr, &Dest);
    if ( v39->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v39->vfptr) )
    {
      v22 = SQLExecDirect_0(v39->m_hStmtSelect, &Dest, -3);
      if ( v22 && v22 != 1 )
      {
        if ( v22 == 100 )
        {
          result = 2;
        }
        else
        {
          SQLStmt = v39->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v39->vfptr, v22, &Dest, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v39->vfptr, v22, v39->m_hStmtSelect);
          result = 1;
        }
      }
      else
      {
        pTradeData->dwCnt = 0;
        do
        {
          v22 = SQLFetch_0(v39->m_hStmtSelect);
          if ( v22 && v22 != 1 )
            break;
          v8 = &pTradeData->list[pTradeData->dwCnt].byState;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v39->m_hStmtSelect, 1u, -6, v8, 0i64, &v21);
          v9 = (char *)&pTradeData->list[pTradeData->dwCnt].dwRegistSerial;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v39->m_hStmtSelect, 2u, -18, v9, 0i64, &v21);
          v10 = &pTradeData->list[pTradeData->dwCnt].byInvenIndex;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v39->m_hStmtSelect, 3u, -6, v10, 0i64, &v21);
          v11 = (char *)&pTradeData->list[pTradeData->dwCnt].dwPrice;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v39->m_hStmtSelect, 4u, -18, v11, 0i64, &v21);
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v39->m_hStmtSelect, 5u, 93, &TargetValue, 0i64, &v21);
          v12 = &pTradeData->list[pTradeData->dwCnt].bySellTurm;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v39->m_hStmtSelect, 6u, -6, v12, 0i64, &v21);
          v13 = (char *)&pTradeData->list[pTradeData->dwCnt].dwBuyerSerial;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v39->m_hStmtSelect, 7u, 4, v13, 0i64, &v21);
          v14 = (char *)&pTradeData->list[pTradeData->dwCnt].dwTax;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v39->m_hStmtSelect, 8u, 4, v14, 0i64, &v21);
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v39->m_hStmtSelect, 9u, 93, &v30, 0i64, &v21);
          v15 = pTradeData->list[pTradeData->dwCnt].wszBuyerName;
          StrLen_or_IndPtr = &v21;
          SQLStmt = (void *)17;
          v22 = SQLGetData_0(v39->m_hStmtSelect, 0xAu, 1, v15, 17i64, &v21);
          v16 = pTradeData->list[pTradeData->dwCnt].szBuyerAccount;
          StrLen_or_IndPtr = &v21;
          SQLStmt = (void *)13;
          v22 = SQLGetData_0(v39->m_hStmtSelect, 0xBu, 1, v16, 13i64, &v21);
          _Tm.tm_year = TargetValue - 1900;
          _Tm.tm_mon = v25 - 1;
          _Tm.tm_mday = v26;
          _Tm.tm_hour = v27;
          _Tm.tm_min = v28;
          _Tm.tm_sec = v29;
          _Tm.tm_isdst = -1;
          v37 = mktime_3(&_Tm);
          if ( v37 == -1 )
            v37 = 0i64;
          pTradeData->list[pTradeData->dwCnt].tStartTime = v37;
          _Tm.tm_year = v30 - 1900;
          _Tm.tm_mon = v31 - 1;
          _Tm.tm_mday = v32;
          _Tm.tm_hour = v33;
          _Tm.tm_min = v34;
          _Tm.tm_sec = v35;
          _Tm.tm_isdst = -1;
          v37 = mktime_3(&_Tm);
          if ( v37 == -1 )
            v37 = 0i64;
          pTradeData->list[pTradeData->dwCnt++].tResultTime = v37;
        }
        while ( pTradeData->dwCnt < 0xA );
        if ( v39->m_hStmtSelect )
          SQLCloseCursor_0(v39->m_hStmtSelect);
        if ( v39->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v39->vfptr, "%s Success", &Dest);
        result = 0;
      }
    }
    else
    {
      CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v39->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
      result = 1;
    }
  }
  else
  {
    result = 2;
  }
  return result;
}
