/*
 * Function: ?CalcCurSPRate@CPlayer@@QEAAGXZ
 * Address: 0x1400EFC40
 */

__int64 __fastcall CPlayer::CalcCurSPRate(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  int v6; // [sp+24h] [bp-14h]@4
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = 10000 * CPlayer::GetSP(v7);
  v6 = CPlayer::GetMaxSP(v7);
  return (unsigned int)(v5 / v6);
}
