/*
 * Function: ??0CUnmannedTraderGroupDivisionVersionInfo@@QEAA@HI@Z
 * Address: 0x140397DA0
 */

void __fastcall CUnmannedTraderGroupDivisionVersionInfo::CUnmannedTraderGroupDivisionVersionInfo(CUnmannedTraderGroupDivisionVersionInfo *this, int iType, unsigned int uiMaxCnt)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int _Val; // [sp+20h] [bp-18h]@4
  CUnmannedTraderGroupDivisionVersionInfo *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7->m_iType = iType;
  _Val = 1;
  std::vector<unsigned long,std::allocator<unsigned long>>::vector<unsigned long,std::allocator<unsigned long>>(
    &v7->m_vecuiVersion,
    uiMaxCnt,
    &_Val);
}
