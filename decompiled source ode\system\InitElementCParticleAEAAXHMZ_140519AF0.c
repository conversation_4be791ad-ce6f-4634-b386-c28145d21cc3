/*
 * Function: ?InitElement@CParticle@@AEAAXHM@Z
 * Address: 0x140519AF0
 */

void __fastcall CParticle::InitElement(CParticle *this, int a2, float a3)
{
  int v3; // esi@1
  CParticle *v4; // rbx@1
  unsigned __int64 v5; // rdi@1
  float v6; // xmm0_4@3
  float v7; // xmm1_4@3
  float v8; // xmm0_4@3
  float v9; // xmm1_4@3
  float v10; // xmm0_4@3
  unsigned int v11; // eax@6
  float v12; // xmm0_4@7
  float v13; // xmm1_4@7
  float v14; // xmm0_4@7
  float v15; // xmm1_4@7
  float v16; // xmm0_4@7
  int v17; // eax@7
  float v18; // xmm0_4@11
  float v19; // xmm1_4@11
  float v20; // xmm0_4@11
  float v21; // xmm1_4@11
  float v22; // xmm0_4@11
  int v23; // eax@11
  float v24; // xmm0_4@14
  float v25; // xmm1_4@14
  float v26; // xmm0_4@14
  float v27; // xmm1_4@14
  float v28; // xmm0_4@14
  float v29; // xmm0_4@15
  float v30; // xmm0_4@15
  _PARTICLE_ELEMENT *v31; // rax@18
  _PARTICLE_ELEMENT *v32; // rax@21
  __m128d v33; // xmm0@21
  float v34; // xmm7_4@21
  float v35; // xmm0_4@24
  float v36; // xmm6_4@24
  float v37; // xmm0_4@27
  float v38; // xmm6_4@27
  float v39; // xmm0_4@27
  float v40; // xmm0_4@27
  float v41; // xmm6_4@27
  float v42; // xmm0_4@27
  float v43; // xmm0_4@27
  float v44; // xmm6_4@27
  float v45; // xmm0_4@27
  float v46; // xmm0_4@30
  float v47; // xmm6_4@30
  float v48; // xmm0_4@30
  float v49; // xmm0_4@33
  float v50; // xmm0_4@36
  float v51; // xmm0_4@39
  float v52; // xmm0_4@39
  float v53; // xmm0_4@39
  float v54; // xmm0_4@39
  float v55; // xmm0_4@39
  float v56; // xmm0_4@39
  float v57; // xmm0_4@39
  float v58; // [sp+20h] [bp-78h]@9
  float v59; // [sp+24h] [bp-74h]@7
  float v60; // [sp+28h] [bp-70h]@7
  float v61; // [sp+30h] [bp-68h]@7
  float v62; // [sp+34h] [bp-64h]@11
  float v63; // [sp+38h] [bp-60h]@7
  float v64; // [sp+40h] [bp-58h]@3
  float v65; // [sp+44h] [bp-54h]@3
  float v66; // [sp+48h] [bp-50h]@3
  float v67; // [sp+50h] [bp-48h]@18
  float v68; // [sp+54h] [bp-44h]@18
  float v69; // [sp+58h] [bp-40h]@18

  v3 = a2;
  v4 = this;
  v5 = a2;
  LODWORD(this->mElement[v5].mTime) = 0;
  this->mElement[v5].mFlag = 0;
  if ( _bittest((const signed __int32 *)&this->mFlag, 0x11u) )
    this->mElement[v5].mFlag |= 1u;
  v6 = this->mStartPower[0][0];
  sub_140517000(v6, this->mStartPower[1][0]);
  v7 = v4->mStartPower[1][1];
  v64 = v6;
  v8 = v4->mStartPower[0][1];
  sub_140517000(v8, v7);
  v9 = v4->mStartPower[1][2];
  v65 = v8;
  v10 = v4->mStartPower[0][2];
  sub_140517000(v10, v9);
  v66 = v10;
  if ( _bittest((const signed __int32 *)&v4->mFlag, 0xFu) )
  {
    Vector3fTransform(v4->mElement[v5].mDir, &v64, v4->mRotMat);
  }
  else
  {
    v4->mElement[v5].mDir[0] = v64;
    v4->mElement[v5].mDir[1] = v65;
    v4->mElement[v5].mDir[2] = v66;
  }
  LODWORD(v4->mElement[v5].mDirStep[0]) = 0;
  LODWORD(v4->mElement[v5].mDirStep[1]) = 0;
  LODWORD(v4->mElement[v5].mDirStep[2]) = 0;
  v11 = v4->mFlag;
  if ( _bittest((const signed int *)&v11, 0x1Fu) )
  {
    v12 = v4->mStartPos[0][0];
    sub_140517000(v12, v4->mStartPos[1][0]);
    v13 = v4->mStartPos[1][2];
    v61 = v12;
    v14 = v4->mStartPos[0][2];
    sub_140517000(v14, v13);
    v15 = v4->mStartPos[1][1];
    v63 = v14;
    v16 = v4->mStartPos[0][1];
    sub_140517000(v16, v15);
    v59 = v16;
    sub_140517000(-0.0 - v61, v61);
    v60 = sqrtf_0((float)(1.0 - (float)((float)((float)(-0.0 - v61) * (float)(-0.0 - v61)) / (float)(v61 * v61))) * (float)(v63 * v63));
    v17 = rand();
    if ( (((unsigned __int64)v17 >> 32) ^ v17 & 1) == (unsigned __int64)v17 >> 32 )
      v60 = -0.0 - v60;
    v58 = -0.0 - v61;
  }
  else if ( _bittest((const signed int *)&v11, 0x1Eu) )
  {
    v18 = v4->mStartPos[0][0];
    sub_140517000(v18, v4->mStartPos[1][0]);
    v19 = v4->mStartPos[1][1];
    v61 = v18;
    v20 = v4->mStartPos[0][1];
    sub_140517000(v20, v19);
    v21 = v4->mStartPos[1][2];
    v62 = v20;
    v22 = v4->mStartPos[0][2];
    sub_140517000(v22, v21);
    v60 = v22;
    sub_140517000(-0.0 - v61, v61);
    v59 = sqrtf_0((float)(1.0 - (float)((float)((float)(-0.0 - v61) * (float)(-0.0 - v61)) / (float)(v61 * v61))) * (float)(v62 * v62));
    v23 = rand();
    if ( (((unsigned __int64)v23 >> 32) ^ v23 & 1) == (unsigned __int64)v23 >> 32 )
      v59 = -0.0 - v59;
    v58 = -0.0 - v61;
  }
  else
  {
    v24 = v4->mStartPos[0][0];
    sub_140517000(v24, v4->mStartPos[1][0]);
    v25 = v4->mStartPos[1][1];
    v58 = v24;
    v26 = v4->mStartPos[0][1];
    sub_140517000(v26, v25);
    v27 = v4->mStartPos[1][2];
    v59 = v26;
    v28 = v4->mStartPos[0][2];
    sub_140517000(v28, v27);
    v60 = v28;
  }
  v29 = v4->mStartZRot[0];
  sub_140517000(v29, v4->mStartZRot[1]);
  v4->mElement[v5].mZRot = v29;
  v30 = v4->mStartYRot[0];
  sub_140517000(v30, v4->mStartYRot[1]);
  v4->mElement[v5].mYRot = v30;
  if ( _bittest((const signed __int32 *)&v4->mFlag, 0xFu) )
  {
    Vector3fTransform(&v61, &v58, v4->mRotMat);
    v4->mElement[v5].mPos[0] = v61 + v4->mCreatePos[0];
    v4->mElement[v5].mPos[1] = v62 + v4->mCreatePos[1];
    v4->mElement[v5].mPos[2] = v63 + v4->mCreatePos[2];
  }
  else
  {
    v4->mElement[v5].mPos[0] = v58;
    v4->mElement[v5].mPos[1] = v59;
    v4->mElement[v5].mPos[2] = v60;
  }
  GetEntityAnimationPos(&v67, v4);
  v4->mElement[v5].mPos[0] = v67 + v4->mElement[v5].mPos[0];
  v4->mElement[v5].mPos[1] = v68 + v4->mElement[v5].mPos[1];
  v4->mElement[v5].mPos[2] = v69 + v4->mElement[v5].mPos[2];
  LODWORD(v4->mElement[v5].mNowFrame) = 0;
  v4->mElement[v5].mNowTrack = 0;
  v4->mElement[v5].mIsLive = 0;
  v4->mElement[v5].mTime = a3;
  v31 = v4->mElement;
  if ( v31[v5].mTime > (float)(v4->mLiveTime / v4->mTimeSpeed) )
    LODWORD(v31[v5].mTime) = 0;
  CParticle::GetPartcleStep(v4, v3, v4->mElement[v5].mTime);
  if ( v4->mTrackCnt )
  {
    v32 = v4->mElement;
    v33 = (__m128d)LODWORD(v32[v5].mTime);
    v34 = v4->mTimeTrack[0] - (float)(*(float *)&v33.m128d_f64[0] * v4->mTimeSpeed);
    v33.m128d_f64[0] = v34;
    *(float *)&v33.m128d_f64[0] = COERCE_DOUBLE(_mm_and_pd(v33, (__m128d)_mask__AbsDouble_));
    if ( *(float *)&v33.m128d_f64[0] < 0.3 )
      v34 = FLOAT_1_0;
    if ( v4->mTrackFlag[0] & 0x80 )
    {
      v35 = v4->mStartARGB[0][0];
      v36 = (float)v4->mATrack[0];
      sub_140517000(v35, v4->mStartARGB[0][1]);
      v4->mElement[v5].mARGBStep[0] = (float)(v36 - v35) / v34;
    }
    else
    {
      LODWORD(v32[v5].mARGBStep[0]) = 0;
    }
    if ( v4->mTrackFlag[0] & 0x40 )
    {
      v37 = (float)v4->mRTrack[0][0];
      sub_140517000(v37, (float)v4->mRTrack[0][1]);
      v38 = v37;
      v39 = v4->mStartARGB[1][0];
      sub_140517000(v39, v4->mStartARGB[1][1]);
      v4->mElement[v5].mARGBStep[1] = (float)(v38 - v39) / v34;
      v40 = (float)v4->mGTrack[0][0];
      sub_140517000(v40, (float)v4->mGTrack[0][1]);
      v41 = v40;
      v42 = v4->mStartARGB[2][0];
      sub_140517000(v42, v4->mStartARGB[2][1]);
      v4->mElement[v5].mARGBStep[2] = (float)(v41 - v42) / v34;
      v43 = (float)v4->mBTrack[0][0];
      sub_140517000(v43, (float)v4->mBTrack[0][1]);
      v44 = v43;
      v45 = v4->mStartARGB[3][0];
      sub_140517000(v45, v4->mStartARGB[3][1]);
      v4->mElement[v5].mARGBStep[3] = (float)(v44 - v45) / v34;
    }
    else
    {
      LODWORD(v4->mElement[v5].mARGBStep[1]) = 0;
      LODWORD(v4->mElement[v5].mARGBStep[2]) = 0;
      LODWORD(v4->mElement[v5].mARGBStep[3]) = 0;
    }
    if ( v4->mTrackFlag[0] & 0x20 )
    {
      v46 = v4->mScaleTrack[0][0];
      sub_140517000(v46, v4->mScaleTrack[0][1]);
      v47 = v46;
      v48 = v4->mStartScale[0];
      sub_140517000(v48, v4->mStartScale[1]);
      v4->mElement[v5].mScaleStep = (float)(v47 - v48) / v34;
    }
    else
    {
      LODWORD(v4->mElement[v5].mScaleStep) = 0;
    }
    if ( v4->mTrackFlag[0] & 8 )
    {
      v49 = v4->mZRotTrack[0][0];
      sub_140517000(v49, v4->mZRotTrack[0][1]);
      v4->mElement[v5].mZRotStep = v49 / v34;
    }
    else
    {
      LODWORD(v4->mElement[v5].mZRotStep) = 0;
    }
    if ( v4->mTrackFlag[0] & 4 )
    {
      v50 = v4->mYRotTrack[0][0];
      sub_140517000(v50, v4->mYRotTrack[0][1]);
      v4->mElement[v5].mYRotStep = v50 / v34;
    }
    else
    {
      LODWORD(v4->mElement[v5].mYRotStep) = 0;
    }
  }
  else
  {
    LODWORD(v4->mElement[v5].mARGBStep[0]) = 0;
    LODWORD(v4->mElement[v5].mARGBStep[1]) = 0;
    LODWORD(v4->mElement[v5].mARGBStep[2]) = 0;
    LODWORD(v4->mElement[v5].mARGBStep[3]) = 0;
    LODWORD(v4->mElement[v5].mScaleStep) = 0;
  }
  v51 = v4->mStartARGB[0][0];
  sub_140517000(v51, v4->mStartARGB[0][1]);
  v4->mElement[v5].mARGB[0] = v51;
  v52 = v4->mStartARGB[1][0];
  sub_140517000(v52, v4->mStartARGB[1][1]);
  v4->mElement[v5].mARGB[1] = v52;
  v53 = v4->mStartARGB[2][0];
  sub_140517000(v53, v4->mStartARGB[2][1]);
  v4->mElement[v5].mARGB[2] = v53;
  v54 = v4->mStartARGB[3][0];
  sub_140517000(v54, v4->mStartARGB[3][1]);
  v4->mElement[v5].mARGB[3] = v54;
  v55 = v4->mStartScale[0];
  sub_140517000(v55, v4->mStartScale[1]);
  v4->mElement[v5].mScale = v55;
  v56 = v4->mStartZRot[0];
  sub_140517000(v56, v4->mStartZRot[1]);
  v4->mElement[v5].mZRot = v56;
  v57 = v4->mStartYRot[0];
  sub_140517000(v57, v4->mStartYRot[1]);
  v4->mElement[v5].mYRot = v57;
}
