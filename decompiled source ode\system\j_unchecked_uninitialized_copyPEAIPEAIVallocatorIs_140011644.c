/*
 * Function: j_??$unchecked_uninitialized_copy@PEAIPEAIV?$allocator@I@std@@@stdext@@YAPEAIPEAI00AEAV?$allocator@I@std@@@Z
 * Address: 0x140011644
 */

unsigned int *__fastcall stdext::unchecked_uninitialized_copy<unsigned int *,unsigned int *,std::allocator<unsigned int>>(unsigned int *_First, unsigned int *_Last, unsigned int *_Dest, std::allocator<unsigned int> *_Al)
{
  return stdext::unchecked_uninitialized_copy<unsigned int *,unsigned int *,std::allocator<unsigned int>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
