/*
 * Function: ?ct_full_animus_gauge@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140295D20
 */

bool __fastcall ct_full_animus_gauge(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1

  v5 = pOne;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( s_nWordCount <= 1 )
    result = CPlayer::dev_full_animus_gauge(v5);
  else
    result = 0;
  return result;
}
