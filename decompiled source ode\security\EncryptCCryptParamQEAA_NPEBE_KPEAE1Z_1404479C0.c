/*
 * Function: ?Encrypt@CCryptParam@@QEAA_NPEBE_KPEAE1@Z
 * Address: 0x1404479C0
 */

char __fastcall CCryptParam::Encrypt(CCryptParam *this, const char *pText, unsigned __int64 tLength, char *pCipherText, unsigned __int64 tCipherTextLength)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned __int64 v8; // rax@6
  __int64 v9; // [sp+0h] [bp-A8h]@1
  char *v10; // [sp+20h] [bp-88h]@8
  CryptoPP::NullNameValuePairs *v11; // [sp+28h] [bp-80h]@8
  unsigned __int64 v12; // [sp+30h] [bp-78h]@6
  __int64 v13; // [sp+78h] [bp-30h]@4
  CryptoPP::PK_CryptoSystemVtbl *v14; // [sp+80h] [bp-28h]@6
  unsigned __int64 v15; // [sp+88h] [bp-20h]@6
  CryptoPP::PK_CryptoSystemVtbl *v16; // [sp+90h] [bp-18h]@8
  CCryptParam *v17; // [sp+B0h] [bp+8h]@1
  const char *v18; // [sp+B8h] [bp+10h]@1
  unsigned __int64 v19; // [sp+C0h] [bp+18h]@1
  char *v20; // [sp+C8h] [bp+20h]@1

  v20 = pCipherText;
  v19 = tLength;
  v18 = pText;
  v17 = this;
  v5 = &v9;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v13 = -2i64;
  if ( v17->m_byState & 1 )
  {
    v14 = v17->m_ECPEncryptor.vfptr;
    LODWORD(v8) = ((int (__fastcall *)(signed __int64, unsigned __int64))v14->CiphertextLength)(
                    (signed __int64)&v17->m_ECPEncryptor,
                    tLength);
    v15 = v8;
    v12 = v8;
    if ( v8 <= tCipherTextLength )
    {
      v16 = v17->m_ECPEncryptor.vfptr;
      v11 = &CryptoPP::g_nullNameValuePairs;
      v10 = v20;
      ((void (__fastcall *)(signed __int64, CryptoPP::AutoSeededRandomPool *, const char *, unsigned __int64))v16[1].__vecDelDtor)(
        (signed __int64)&v17->m_ECPEncryptor,
        v17->m_prng,
        v18,
        v19);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
