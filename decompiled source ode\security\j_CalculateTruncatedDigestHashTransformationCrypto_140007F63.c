/*
 * Function: j_?CalculateTruncatedDigest@HashTransformation@CryptoPP@@UEAAXPEAE_KPEBE1@Z
 * Address: 0x140007F63
 */

void __fastcall CryptoPP::HashTransformation::CalculateTruncatedDigest(CryptoPP::HashTransformation *this, char *digest, unsigned __int64 digestSize, const char *input, unsigned __int64 length)
{
  CryptoPP::HashTransformation::CalculateTruncatedDigest(this, digest, digestSize, input, length);
}
