/*
 * Function: ?PrepareCascade@?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@CryptoPP@@AEBAXAEBV?$DL_GroupPrecomputation@UECPPoint@CryptoPP@@@2@AEAV?$vector@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@AEBVInteger@2@@Z
 * Address: 0x140578D90
 */

void __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::PrepareCascade(__int64 a1, __int64 a2, __int64 a3, struct CryptoPP::Integer *a4)
{
  unsigned __int64 v4; // rax@1
  unsigned __int64 v5; // rax@6
  CryptoPP::ECPPoint *v6; // rax@9
  __int64 v7; // rax@9
  __int64 v8; // rax@9
  CryptoPP::ECPPoint *v9; // rax@10
  __int64 v10; // rax@10
  CryptoPP::ECPPoint *v11; // rax@12
  __int64 v12; // rax@12
  char v13; // [sp+20h] [bp-298h]@5
  unsigned int _Pos; // [sp+24h] [bp-294h]@5
  unsigned __int64 _Pos_4; // [sp+28h] [bp-290h]@1
  CryptoPP::Integer v16; // [sp+30h] [bp-288h]@1
  CryptoPP::Integer b; // [sp+58h] [bp-260h]@1
  CryptoPP::Integer v18; // [sp+80h] [bp-238h]@1
  char v19; // [sp+A8h] [bp-210h]@9
  CryptoPP::Integer result; // [sp+128h] [bp-190h]@9
  char v21; // [sp+150h] [bp-168h]@10
  char v22; // [sp+1D0h] [bp-E8h]@12
  __int64 v23; // [sp+250h] [bp-68h]@1
  int v24; // [sp+258h] [bp-60h]@3
  unsigned __int64 v25; // [sp+260h] [bp-58h]@6
  CryptoPP::Integer *v26; // [sp+268h] [bp-50h]@9
  CryptoPP::Integer *v27; // [sp+270h] [bp-48h]@9
  __int64 v28; // [sp+278h] [bp-40h]@9
  __int64 v29; // [sp+280h] [bp-38h]@9
  __int64 v30; // [sp+288h] [bp-30h]@9
  __int64 v31; // [sp+290h] [bp-28h]@10
  __int64 v32; // [sp+298h] [bp-20h]@10
  __int64 v33; // [sp+2A0h] [bp-18h]@12
  __int64 v34; // [sp+2A8h] [bp-10h]@12
  __int64 v35; // [sp+2C0h] [bp+8h]@1
  __int64 v36; // [sp+2D0h] [bp+18h]@1
  struct CryptoPP::Integer *v37; // [sp+2D8h] [bp+20h]@1

  v37 = a4;
  v36 = a3;
  v35 = a1;
  v23 = -2i64;
  LODWORD(v4) = (*(int (__fastcall **)(__int64))(*(_QWORD *)a2 + 24i64))(a2);
  _Pos_4 = v4;
  CryptoPP::Integer::Integer(&b);
  CryptoPP::Integer::Integer(&v16);
  CryptoPP::Integer::Integer(&v18, v37);
  v24 = (unsigned __int8)(*(int (__fastcall **)(unsigned __int64))(*(_QWORD *)_Pos_4 + 40i64))(_Pos_4)
     && *(_DWORD *)(v35 + 96) > 1u;
  v13 = v24;
  for ( _Pos = 0; ; ++_Pos )
  {
    v25 = _Pos + 1;
    v5 = std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::size((std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *)(v35 + 144));
    if ( v25 >= v5 )
      break;
    CryptoPP::Integer::DivideByPowerOf2(&b, &v16, &v18, *(_DWORD *)(v35 + 96));
    std::swap<CryptoPP::Integer>(&v16, &v18);
    if ( v13 && CryptoPP::Integer::GetBit(&b, (unsigned int)(*(_DWORD *)(v35 + 96) - 1)) )
    {
      CryptoPP::Integer::operator++(&v18);
      v26 = CryptoPP::operator-(&result, (CryptoPP::Integer *)(v35 + 104), &b);
      v27 = v26;
      v6 = std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::operator[](
             (std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *)(v35 + 144),
             _Pos);
      v28 = *(_QWORD *)_Pos_4;
      LODWORD(v7) = (*(int (__fastcall **)(unsigned __int64, CryptoPP::ECPPoint *))(v28 + 32))(_Pos_4, v6);
      LODWORD(v8) = CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(
                      &v19,
                      v7,
                      v27);
      v29 = v8;
      v30 = v8;
      std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::push_back(
        v36,
        v8);
      CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(&v19);
      CryptoPP::Integer::~Integer(&result);
    }
    else
    {
      v9 = std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::operator[](
             (std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *)(v35 + 144),
             _Pos);
      LODWORD(v10) = CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(
                       &v21,
                       v9,
                       &b);
      v31 = v10;
      v32 = v10;
      std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::push_back(
        v36,
        v10);
      CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(&v21);
    }
  }
  v11 = std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::operator[](
          (std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *)(v35 + 144),
          _Pos);
  LODWORD(v12) = CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(
                   &v22,
                   v11,
                   &v18);
  v33 = v12;
  v34 = v12;
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::push_back(
    v36,
    v12);
  CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(&v22);
  CryptoPP::Integer::~Integer(&v18);
  CryptoPP::Integer::~Integer(&v16);
  CryptoPP::Integer::~Integer(&b);
}
