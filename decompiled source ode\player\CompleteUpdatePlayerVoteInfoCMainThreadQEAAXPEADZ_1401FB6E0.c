/*
 * Function: ?CompleteUpdatePlayerVoteInfo@CMainThread@@QEAAXPEAD@Z
 * Address: 0x1401FB6E0
 */

void __fastcall CMainThread::CompleteUpdatePlayerVoteInfo(CMainThread *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  char *v5; // [sp+20h] [bp-18h]@4
  CPlayer *v6; // [sp+28h] [bp-10h]@4

  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = pData;
  v6 = GetPtrPlayerFromSerial(&g_Player, 2532, *((_DWORD *)pData + 5));
  if ( v6 )
  {
    if ( v6->m_bOper )
    {
      v6->m_pUserDB->m_AvatorData.dbSupplement.VoteEnable = v5[5];
      v6->m_pUserDB->m_AvatorData.dbSupplement.dwAccumPlayTime = *(_DWORD *)v5;
      v6->m_pUserDB->m_AvatorData.dbSupplement.byVoted = v5[4];
      v6->m_pUserDB->m_AvatorData.dbSupplement.wScanerCnt = *((_WORD *)v5 + 3);
      v6->m_pUserDB->m_AvatorData.dbSupplement.dwScanerGetDate = *((_DWORD *)v5 + 2);
    }
  }
}
