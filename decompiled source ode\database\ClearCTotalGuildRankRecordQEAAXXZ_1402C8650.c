/*
 * Function: ?Clear@CTotalGuildRankRecord@@QEAAXXZ
 * Address: 0x1402C8650
 */

void __fastcall CTotalGuildRankRecord::Clear(CTotalGuildRankRecord *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CTotalGuildRankRecord *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_dwInx = 0;
  v4->m_wRank = 0;
  v4->m_dwSerial = 0;
  memset_0(v4->m_wszGuildName, 0, 0x11ui64);
  v4->m_byGrade = 0;
  memset_0(v4->m_wszMasterName, 0, 0x11ui64);
}
