/*
 * Function: _stdext::unchecked_uninitialized_copy_std::_Vector_const_iterator_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo____CUnmannedTraderRegistItemInfo_____ptr64_std::allocator_CUnmannedTraderRegistItemInfo____::_1_::dtor$2
 * Address: 0x140369CA0
 */

void __fastcall stdext::unchecked_uninitialized_copy_std::_Vector_const_iterator_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo____CUnmannedTraderRegistItemInfo_____ptr64_std::allocator_CUnmannedTraderRegistItemInfo____::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(*(std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > **)(a2 + 88));
}
