/*
 * Function: ?CompleteTimeOutClear@CUnmannedTraderUserInfoTable@@QEAAXPEAD@Z
 * Address: 0x140364400
 */

void __fastcall CUnmannedTraderUserInfoTable::CompleteTimeOutClear(CUnmannedTraderUserInfoTable *this, char *pLoadData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-68h]@1
  char *wszName; // [sp+20h] [bp-48h]@7
  unsigned int dwRegistSerial; // [sp+28h] [bp-40h]@7
  unsigned int dwK; // [sp+30h] [bp-38h]@7
  unsigned int dwD; // [sp+38h] [bp-30h]@7
  unsigned int dwU; // [sp+40h] [bp-28h]@7
  char *v10; // [sp+50h] [bp-18h]@4
  CUnmannedTraderUserInfo *v11; // [sp+58h] [bp-10h]@4
  CUnmannedTraderUserInfoTable *v12; // [sp+70h] [bp+8h]@1

  v12 = this;
  v2 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = pLoadData;
  v11 = CUnmannedTraderUserInfoTable::Find(v12, *((_DWORD *)pLoadData + 3));
  if ( !CUnmannedTraderUserInfo::IsNull(v11)
    && *(&g_Player.m_bOper + 50856 * CUnmannedTraderUserInfo::GetIndex(v11))
    && CUnmannedTraderUserInfo::FindOwner(v11) )
  {
    CUnmannedTraderUserInfo::CompleteTimeOutClear(v11, *((_DWORD *)v10 + 1), v12->m_pkLogger);
  }
  else
  {
    dwU = *((_DWORD *)v10 + 15);
    dwD = *((_DWORD *)v10 + 14);
    dwK = *((_DWORD *)v10 + 13);
    dwRegistSerial = *((_DWORD *)v10 + 1);
    wszName = v10 + 33;
    CUnmannedTraderUserInfoTable::ClearLogLogOutState(
      v12,
      "Time Out",
      *((_DWORD *)v10 + 3),
      v10 + 20,
      v10 + 33,
      dwRegistSerial,
      dwK,
      dwD,
      dwU);
  }
}
