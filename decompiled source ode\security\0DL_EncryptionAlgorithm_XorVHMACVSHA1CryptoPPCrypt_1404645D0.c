/*
 * Function: ??0?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@CryptoPP@@QEAA@XZ
 * Address: 0x1404645D0
 */

void __fastcall CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>(CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CryptoPP::DL_SymmetricEncryptionAlgorithm::DL_SymmetricEncryptionAlgorithm((CryptoPP::DL_SymmetricEncryptionAlgorithm *)&v4->vfptr);
  v4->vfptr = (CryptoPP::DL_SymmetricEncryptionAlgorithmVtbl *)&CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>::`vftable';
}
