/*
 * Function: ?HierarcyHelpCast@CMonsterHelper@@SAXPEAVCMonster@@@Z
 * Address: 0x14015A480
 */

void __fastcall CMonsterHelper::HierarcyHelpCast(CMonster *pMon)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@8
  unsigned int v4; // eax@19
  __int64 v5; // [sp+0h] [bp-58h]@1
  int nKindIndex; // [sp+20h] [bp-38h]@5
  int nIndex; // [sp+24h] [bp-34h]@7
  CMonster *v8; // [sp+28h] [bp-30h]@4
  char v9; // [sp+30h] [bp-28h]@4
  int v10; // [sp+34h] [bp-24h]@5
  CMonster *v11; // [sp+38h] [bp-20h]@9
  CMonster *v12; // [sp+40h] [bp-18h]@20
  CMonster *lpParam; // [sp+60h] [bp+8h]@1

  lpParam = pMon;
  v1 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = CMonsterHierarchy::GetParent(&lpParam->m_MonHierarcy);
  v9 = CMonsterHierarchy::ChildKindCount(&lpParam->m_MonHierarcy);
  if ( v8 )
  {
    v10 = (unsigned __int8)CMonsterHierarchy::ChildKindCount(&v8->m_MonHierarcy);
    for ( nKindIndex = 0; nKindIndex < v10; ++nKindIndex )
    {
      for ( nIndex = 0; ; ++nIndex )
      {
        v3 = CMonsterHierarchy::GetChildCount(&v8->m_MonHierarcy, nKindIndex);
        if ( nIndex >= v3 )
          break;
        v11 = CMonsterHierarchy::GetChild(&v8->m_MonHierarcy, nKindIndex, nIndex);
        if ( v11 )
        {
          if ( v11 != lpParam )
            Us_HFSM::SendExternMsg((Us_HFSM *)&v11->m_AI.vfptr, 1u, lpParam, 0);
        }
      }
    }
    Us_HFSM::SendExternMsg((Us_HFSM *)&v8->m_AI.vfptr, 1u, lpParam, 0);
  }
  else if ( v9 )
  {
    for ( nKindIndex = 0; nKindIndex < (unsigned __int8)v9; ++nKindIndex )
    {
      for ( nIndex = 0; ; ++nIndex )
      {
        v4 = CMonsterHierarchy::GetChildCount(&lpParam->m_MonHierarcy, nKindIndex);
        if ( nIndex >= v4 )
          break;
        v12 = CMonsterHierarchy::GetChild(&lpParam->m_MonHierarcy, nKindIndex, nIndex);
        if ( v12 && v12 != lpParam )
          Us_HFSM::SendExternMsg((Us_HFSM *)&v12->m_AI.vfptr, 1u, lpParam, 0);
      }
    }
  }
}
