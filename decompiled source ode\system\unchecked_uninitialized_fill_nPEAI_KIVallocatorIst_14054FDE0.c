/*
 * Function: ??$unchecked_uninitialized_fill_n@PEAI_KIV?$allocator@I@std@@@stdext@@YAXPEAI_KAEBIAEAV?$allocator@I@std@@@Z
 * Address: 0x14054FDE0
 */

int __fastcall stdext::unchecked_uninitialized_fill_n<unsigned int *,unsigned __int64,unsigned int,std::allocator<unsigned int>>(unsigned int *a1, __int64 a2, __int64 a3, __int64 a4)
{
  char v5; // [sp+30h] [bp-18h]@1
  std::_Scalar_ptr_iterator_tag v6; // [sp+31h] [bp-17h]@1
  unsigned int *__formal; // [sp+50h] [bp+8h]@1
  __int64 v8; // [sp+58h] [bp+10h]@1
  __int64 v9; // [sp+60h] [bp+18h]@1
  __int64 v10; // [sp+68h] [bp+20h]@1

  v10 = a4;
  v9 = a3;
  v8 = a2;
  __formal = a1;
  memset(&v5, 0, sizeof(v5));
  v6 = std::_Ptr_cat<unsigned int *,unsigned int *>(&__formal, &__formal);
  return std::_Uninit_fill_n<unsigned int *,unsigned __int64,unsigned int,std::allocator<unsigned int>>(
           __formal,
           v8,
           v9,
           v10);
}
