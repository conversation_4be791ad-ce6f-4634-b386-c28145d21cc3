/*
 * Function: ?allocate@?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@QEAAPEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@_K@Z
 * Address: 0x140594F60
 */

int __fastcall std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>::allocate(__int64 a1, __int64 a2)
{
  return std::_Allocate<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>(a2, 0i64);
}
