/*
 * Function: ?ElapsedTime@TimerBase@CryptoPP@@QEAAKXZ
 * Address: 0x140660A50
 */

__int64 __fastcall CryptoPP::TimerBase::ElapsedTime(CryptoPP::TimerBase *this)
{
  double v2; // [sp+20h] [bp-18h]@1

  v2 = CryptoPP::TimerBase::ElapsedTimeAsDouble(this);
  if ( v2 > 4294967295.0 )
    _wassert(L"elapsed <= ULONG_MAX", L"D:\\RF Project\\RF_Server64\\28 Crypto++\\hrtimer.cpp", 0x39u);
  return (unsigned int)(signed int)floor(v2);
}
