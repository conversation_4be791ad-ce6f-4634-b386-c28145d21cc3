/*
 * Function: ?dev_all_kill@CPlayer@@QEAA_NXZ
 * Address: 0x1400BAF30
 */

char __fastcall CPlayer::dev_all_kill(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  int v4; // eax@15
  __int64 v6; // [sp+0h] [bp-B8h]@1
  char v7; // [sp+20h] [bp-98h]@15
  int v8; // [sp+28h] [bp-90h]@15
  int v9; // [sp+30h] [bp-88h]@15
  char v10; // [sp+38h] [bp-80h]@15
  _pnt_rect pRect; // [sp+48h] [bp-70h]@4
  _sec_info *v12; // [sp+68h] [bp-50h]@4
  int j; // [sp+70h] [bp-48h]@4
  int k; // [sp+74h] [bp-44h]@6
  unsigned int dwSecIndex; // [sp+78h] [bp-40h]@9
  CObjectList *v16; // [sp+80h] [bp-38h]@9
  CObjectList *v17; // [sp+88h] [bp-30h]@10
  CObjectListVtbl *v18; // [sp+90h] [bp-28h]@12
  CObjectListVtbl *v19; // [sp+98h] [bp-20h]@12
  unsigned int v20; // [sp+A0h] [bp-18h]@15
  void *(__cdecl *v21)(CObjectList *, unsigned int); // [sp+A8h] [bp-10h]@15
  CPlayer *v22; // [sp+C0h] [bp+8h]@1

  v22 = this;
  v1 = &v6;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = CMapData::GetSecInfo(v22->m_pCurMap);
  v3 = CGameObject::GetCurSecNum((CGameObject *)&v22->vfptr);
  CMapData::GetRectInRadius(v22->m_pCurMap, &pRect, 1, v3);
  for ( j = pRect.nStarty; j <= pRect.nEndy; ++j )
  {
    for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
    {
      dwSecIndex = v12->m_nSecNumW * j + k;
      v16 = CMapData::GetSectorListObj(v22->m_pCurMap, v22->m_wMapLayerIndex, dwSecIndex);
      if ( v16 )
      {
        v17 = (CObjectList *)v16->m_Head.m_pNext;
        while ( (_object_list_point *)v17 != &v16->m_Tail )
        {
          v18 = v17->vfptr;
          v17 = (CObjectList *)v17->m_Head.m_pItem;
          v19 = v18 + 2;
          if ( BYTE1(v18[2].__vecDelDtor) == 1 && !LOBYTE(v18[2].__vecDelDtor) )
          {
            v20 = (*((int (__fastcall **)(__int64))v18->__vecDelDtor + 11))((__int64)v18);
            v4 = ((int (__fastcall *)(CPlayer *))v22->vfptr->GetLevel)(v22);
            v21 = v18->__vecDelDtor;
            v10 = 1;
            v9 = 0;
            v8 = -1;
            v7 = 0;
            (*((void (__fastcall **)(__int64, _QWORD, CPlayer *, _QWORD))v21 + 23))(
              (__int64)v18,
              v20,
              v22,
              (unsigned int)v4);
          }
        }
      }
    }
  }
  return 1;
}
