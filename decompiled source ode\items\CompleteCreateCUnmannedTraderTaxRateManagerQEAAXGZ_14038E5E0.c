/*
 * Function: ?CompleteCreate@CUnmannedTraderTaxRateManager@@QEAAXG@Z
 * Address: 0x14038E5E0
 */

void __fastcall CUnmannedTraderTaxRateManager::CompleteCreate(CUnmannedTraderTaxRateManager *this, unsigned __int16 wInx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@6
  int v5; // eax@8
  TRC_AutoTrade **v6; // rax@8
  __int64 v7; // [sp+0h] [bp-48h]@1
  CPlayer *v8; // [sp+20h] [bp-28h]@5
  unsigned __int64 v9; // [sp+28h] [bp-20h]@6
  int n; // [sp+30h] [bp-18h]@8
  CUnmannedTraderTaxRateManager *v11; // [sp+50h] [bp+8h]@1
  unsigned __int16 v12; // [sp+58h] [bp+10h]@1

  v12 = wInx;
  v11 = this;
  v2 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( (signed int)wInx < 2532 )
  {
    v8 = &g_Player + wInx;
    if ( !std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::empty(&v11->m_vecTRC) )
    {
      v9 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::size(&v11->m_vecTRC);
      v4 = CPlayerDB::GetRaceCode(&v8->m_Param);
      if ( v9 > v4 )
      {
        n = v12;
        v5 = CPlayerDB::GetRaceCode(&v8->m_Param);
        v6 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](&v11->m_vecTRC, v5);
        TRC_AutoTrade::SendMsg_UserLogInNotifyTaxRate(*v6, n);
      }
    }
  }
}
