/*
 * Function: ?SendMsg_FixPosition@CAnimus@@UEAAXH@Z
 * Address: 0x14012A1E0
 */

void __fastcall CAnimus::SendMsg_FixPosition(CAnimus *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-98h]@1
  char szMsg[2]; // [sp+38h] [bp-60h]@4
  unsigned __int16 v6; // [sp+3Ah] [bp-5Eh]@4
  unsigned int v7; // [sp+3Ch] [bp-5Ch]@4
  __int16 pShort; // [sp+40h] [bp-58h]@4
  char v9; // [sp+46h] [bp-52h]@5
  unsigned __int16 v10; // [sp+47h] [bp-51h]@7
  unsigned int v11; // [sp+49h] [bp-4Fh]@7
  char pbyType; // [sp+64h] [bp-34h]@7
  char v13; // [sp+65h] [bp-33h]@7
  unsigned __int64 v14; // [sp+80h] [bp-18h]@4
  CAnimus *v15; // [sp+A0h] [bp+8h]@1
  int dwClientIndex; // [sp+A8h] [bp+10h]@1

  dwClientIndex = n;
  v15 = this;
  v2 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v14 = (unsigned __int64)&v4 ^ _security_cookie;
  *(_WORD *)szMsg = v15->m_pRecordSet->m_dwIndex;
  v6 = v15->m_ObjID.m_wIndex;
  v7 = v15->m_dwObjSerial;
  FloatToShort(v15->m_fCurPos, &pShort, 3);
  if ( v15->m_pRecord )
    v9 = v15->m_pRecord->m_nLevel;
  else
    v9 = 1;
  v10 = v15->m_wLastContEffect;
  v11 = v15->m_pMaster->m_dwObjSerial;
  pbyType = 4;
  v13 = 13;
  CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, szMsg, 0x15u);
}
