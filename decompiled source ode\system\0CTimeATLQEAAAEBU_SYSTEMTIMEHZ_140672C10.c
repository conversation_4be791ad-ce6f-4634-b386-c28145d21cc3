/*
 * Function: ??0CTime@ATL@@QEAA@AEBU_SYSTEMTIME@@H@Z
 * Address: 0x140672C10
 */

void __fastcall ATL::CTime::CTime(ATL::CTime *this, _SYSTEMTIME *sysTime, int nDST)
{
  int v3; // eax@3
  int v4; // ecx@3
  int v5; // edx@3
  int v6; // er8@3
  ATL::CTime v7; // [sp+48h] [bp-20h]@2
  ATL::CTime v8; // [sp+50h] [bp-18h]@3
  int nMonth; // [sp+58h] [bp-10h]@3
  ATL::CTime *v10; // [sp+70h] [bp+8h]@1
  _SYSTEMTIME *v11; // [sp+78h] [bp+10h]@1
  int v12; // [sp+80h] [bp+18h]@1

  v12 = nDST;
  v11 = sysTime;
  v10 = this;
  if ( (signed int)sysTime->wYear >= 1900 )
  {
    v3 = sysTime->wSecond;
    v4 = sysTime->wMinute;
    v5 = sysTime->wHour;
    v6 = v11->wDay;
    nMonth = v11->wMonth;
    ATL::CTime::CTime(&v8, v11->wYear, nMonth, v6, v5, v4, v3, v12);
    v10->m_time = v8.m_time;
  }
  else
  {
    ATL::CTime::CTime(&v7, 0i64);
    v10->m_time = v7.m_time;
  }
}
