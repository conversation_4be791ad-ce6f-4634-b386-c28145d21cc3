/*
 * Function: ?_UpdatePacketWin@CandidateRegister@@AEAAXEPEADK@Z
 * Address: 0x1402B7430
 */

void __fastcall CandidateRegister::_UpdatePacketWin(CandidateRegister *this, char byRace, char *wszName, unsigned int dwWinCnt)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@5
  CandidateRegister *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1
  const char *Str2; // [sp+50h] [bp+18h]@1
  unsigned int v11; // [sp+58h] [bp+20h]@1

  v11 = dwWinCnt;
  Str2 = wszName;
  v9 = byRace;
  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( (signed int)(unsigned __int8)byRace < 3 )
  {
    for ( j = 0; j < v8->_kSend[(unsigned __int8)v9].byCnt; ++j )
    {
      if ( !strcmp_0(v8->_kSend[(unsigned __int8)v9].Candidacy[j].wszAvatorName, Str2) )
      {
        v8->_kSend[(unsigned __int8)v9].Candidacy[j].dwWinCnt = v11;
        return;
      }
    }
  }
}
