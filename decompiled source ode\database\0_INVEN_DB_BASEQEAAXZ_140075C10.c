/*
 * Function: ??0_INVEN_DB_BASE@@QEAA@XZ
 * Address: 0x140075C10
 */

void __fastcall _INVEN_DB_BASE::_INVEN_DB_BASE(_INVEN_DB_BASE *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _INVEN_DB_BASE *__t; // [sp+30h] [bp+8h]@1

  __t = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  `vector constructor iterator'(__t, 0x25ui64, 100, (void *(__cdecl *)(void *))_INVEN_DB_BASE::_LIST::_LIST);
  _INVEN_DB_BASE::Init(__t);
}
