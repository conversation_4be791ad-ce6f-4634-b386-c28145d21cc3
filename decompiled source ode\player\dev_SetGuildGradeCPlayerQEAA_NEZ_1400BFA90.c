/*
 * Function: ?dev_SetGuildGrade@CPlayer@@QEAA_NE@Z
 * Address: 0x1400BFA90
 */

char __fastcall CPlayer::dev_SetGuildGrade(CPlayer *this, char byGrade)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  CGuild *v6; // [sp+20h] [bp-18h]@6
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7->m_Param.m_pGuild )
  {
    v6 = 0i64;
    v6 = v7->m_Param.m_pGuild;
    if ( (signed int)(unsigned __int8)byGrade >= 1 && (signed int)(unsigned __int8)byGrade <= 8 )
    {
      CGuild::UpdateGrade(v6, byGrade);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
