/*
 * Function: ??0?$_Ranit@VCUnmannedTraderUserInfo@@_JPEBV1@AEBV1@@std@@QEAA@XZ
 * Address: 0x140368290
 */

void __fastcall std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &>::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &>(std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &> *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::_Iterator_base::_Iterator_base((std::_Iterator_base *)&v4->_Mycont);
}
