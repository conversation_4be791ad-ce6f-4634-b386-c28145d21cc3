/*
 * Function: j_??$unchecked_uninitialized_copy@V?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@PEAPEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@2@@stdext@@YAPEAPEAVCUnmannedTraderClassInfo@@V?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@0PEAPEAV1@AEAV?$allocator@PEAVCUnmannedTraderClassInfo@@@3@@Z
 * Address: 0x14000231F
 */

CUnmannedTraderClassInfo **__fastcall stdext::unchecked_uninitialized_copy<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,CUnmannedTraderClassInfo * *,std::allocator<CUnmannedTraderClassInfo *>>(std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_First, std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_Last, CUnmannedTraderClassInfo **_Dest, std::allocator<CUnmannedTraderClassInfo *> *_Al)
{
  return stdext::unchecked_uninitialized_copy<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,CUnmannedTraderClassInfo * *,std::allocator<CUnmannedTraderClassInfo *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
