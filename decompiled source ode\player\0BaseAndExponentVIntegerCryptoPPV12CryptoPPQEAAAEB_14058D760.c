/*
 * Function: ??0?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@QEAA@AEBVInteger@1@0@Z
 * Address: 0x14058D760
 */

CryptoPP::Integer *__fastcall CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>(CryptoPP::Integer *a1, const struct CryptoPP::Integer *a2, struct CryptoPP::Integer *a3)
{
  CryptoPP::Integer *v4; // [sp+40h] [bp+8h]@1
  struct CryptoPP::Integer *v5; // [sp+50h] [bp+18h]@1

  v5 = a3;
  v4 = a1;
  CryptoPP::Integer::Integer(a1, a2);
  CryptoPP::Integer::Integer(v4 + 1, v5);
  return v4;
}
