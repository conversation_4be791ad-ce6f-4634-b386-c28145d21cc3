/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAVCUnmannedTraderDivisionInfo@@_KPEAV1@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@stdext@@YAXPEAPEAVCUnmannedTraderDivisionInfo@@_KAEBQEAV1@AEAV?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@Z
 * Address: 0x140008E2C
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CUnmannedTraderDivisionInfo * *,unsigned __int64,CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(CUnmannedTraderDivisionInfo **_First, unsigned __int64 _Count, CUnmannedTraderDivisionInfo *const *_Val, std::allocator<CUnmannedTraderDivisionInfo *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderDivisionInfo * *,unsigned __int64,CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(
    _First,
    _Count,
    _Val,
    _Al);
}
