/*
 * Function: ?UpdatePotionContEffect@CPotionMgr@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x14039F030
 */

void __fastcall CPotionMgr::UpdatePotionContEffect(CPotionMgr *this, CPlayer *pPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int v5; // [sp+20h] [bp-18h]@5
  int j; // [sp+24h] [bp-14h]@5
  unsigned int v7; // [sp+28h] [bp-10h]@8
  unsigned int v8; // [sp+2Ch] [bp-Ch]@12
  CPotionMgr *v9; // [sp+40h] [bp+8h]@1
  CPlayer *pApplyPlayer; // [sp+48h] [bp+10h]@1

  pApplyPlayer = pPlayer;
  v9 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pPlayer )
  {
    v5 = _sf_continous::GetSFContCurTime();
    for ( j = 0; j < 2; ++j )
    {
      if ( _ContPotionData::IsLive((_ContPotionData *)&pApplyPlayer->m_PotionParam + j) )
      {
        v7 = v5 - pApplyPlayer->m_PotionParam.m_ContCommonPotionData[j].m_dwStartSec;
        if ( v7 >= pApplyPlayer->m_PotionParam.m_ContCommonPotionData[j].m_wDurCapSec )
          CPotionMgr::RemovePotionContEffect(v9, pApplyPlayer, (_ContPotionData *)&pApplyPlayer->m_PotionParam + j);
      }
    }
    if ( _ContPotionData::IsLive(&pApplyPlayer->m_PotionParam.m_StoneOfMovePotionData) )
    {
      v8 = v5 - pApplyPlayer->m_PotionParam.m_StoneOfMovePotionData.m_dwStartSec;
      if ( v8 >= pApplyPlayer->m_PotionParam.m_StoneOfMovePotionData.m_wDurCapSec )
        CPotionMgr::RemovePotionContEffect(v9, pApplyPlayer, &pApplyPlayer->m_PotionParam.m_StoneOfMovePotionData);
    }
  }
}
