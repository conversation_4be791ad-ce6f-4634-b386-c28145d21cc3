/*
 * Function: j_?_Destroy@?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@IEAAXPEAVCUnmannedTraderSchedule@@0@Z
 * Address: 0x140007810
 */

void __fastcall std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Destroy(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last)
{
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Destroy(this, _First, _Last);
}
