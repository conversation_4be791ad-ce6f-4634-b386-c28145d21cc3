/*
 * Function: ?SendErrorResult@CPossibleBattleGuildListManager@GUILD_BATTLE@@AEAAXHE@Z
 * Address: 0x1403CA260
 */

void __fastcall GUILD_BATTLE::CPossibleBattleGuildListManager::SendErrorResult(GUILD_BATTLE::CPossibleBattleGuildListManager *this, int n, char byRet)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4

  v3 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szMsg = byRet;
  pbyType = 27;
  v8 = 48;
  CNetProcess::LoadSendMsg(unk_1414F2088, n, &pbyType, &szMsg, 1u);
}
