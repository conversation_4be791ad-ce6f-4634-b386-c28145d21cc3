/*
 * Function: ?CheckTakeGravityStone@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAXHHKK@Z
 * Address: 0x1403D4700
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleManager::CheckTakeGravityStone(GUILD_BATTLE::CNormalGuildBattleManager *this, int iPortalInx, int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-B8h]@1
  char v8; // [sp+30h] [bp-88h]@4
  char szMsg; // [sp+48h] [bp-70h]@5
  char v10; // [sp+49h] [bp-6Fh]@5
  char pbyType; // [sp+84h] [bp-34h]@5
  char v12; // [sp+85h] [bp-33h]@5
  unsigned __int64 v13; // [sp+A0h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleManager *v14; // [sp+C0h] [bp+8h]@1
  int dwClientIndex; // [sp+D0h] [bp+18h]@1

  dwClientIndex = n;
  v14 = this;
  v5 = &v7;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v13 = (unsigned __int64)&v7 ^ _security_cookie;
  v8 = GUILD_BATTLE::CNormalGuildBattleManager::ProcCheckTakeGravityStone(
         v14,
         iPortalInx,
         dwGuildSerial,
         dwCharacSerial);
  if ( v8 )
  {
    szMsg = v8;
    memset(&v10, 0, 0x27ui64);
    pbyType = 27;
    v12 = 72;
    CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &szMsg, 0x28u);
  }
}
