/*
 * Function: ?SendMsg_NuclearFind@CNuclearBomb@@QEAAXHE@Z
 * Address: 0x14013CAF0
 */

void __fastcall CNuclearBomb::SendMsg_NuclearFind(CNuclearBomb *this, int n, char race)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-78h]@1
  _nuclear_find_rader_result_zocl v6; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  CNuclearBomb *v9; // [sp+80h] [bp+8h]@1
  int dwClientIndex; // [sp+88h] [bp+10h]@1
  char v11; // [sp+90h] [bp+18h]@1

  v11 = race;
  dwClientIndex = n;
  v9 = this;
  v3 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  _nuclear_find_rader_result_zocl::_nuclear_find_rader_result_zocl(&v6);
  v6.byRaceCode = v11;
  v6.byUseClass = CNuclearBomb::GetMasterClass(v9);
  pbyType = 60;
  v8 = 4;
  CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &v6.byRaceCode, 2u);
}
