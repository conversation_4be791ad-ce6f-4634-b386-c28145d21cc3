/*
 * Function: ??1CMapOperation@@UEAA@XZ
 * Address: 0x1401960C0
 */

void __fastcall CMapOperation::~CMapOperation(CMapOperation *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@7
  __int64 v4; // [sp+0h] [bp-58h]@1
  CMapData *v5; // [sp+20h] [bp-38h]@5
  CMapData *v6; // [sp+28h] [bp-30h]@5
  CMapData *v7; // [sp+30h] [bp-28h]@5
  __int64 v8; // [sp+38h] [bp-20h]@4
  __int64 v9; // [sp+40h] [bp-18h]@7
  __int64 v10; // [sp+48h] [bp-10h]@9
  CMapOperation *v11; // [sp+60h] [bp+8h]@1

  v11 = this;
  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = -2i64;
  v11->vfptr = (CMapOperationVtbl *)&CMapOperation::`vftable';
  if ( v11->m_Map )
  {
    v7 = v11->m_Map;
    v6 = v7;
    v5 = v7;
    if ( v7 )
    {
      if ( v5[-1].m_nMonTotalCount )
      {
        LODWORD(v3) = ((int (__fastcall *)(CMapData *, signed __int64))v6->vfptr->__vecDelDtor)(v6, 3i64);
        v9 = v3;
      }
      else
      {
        operator delete[](&v5[-1].m_nMonTotalCount);
        v9 = 0i64;
      }
      v10 = v9;
    }
    else
    {
      v10 = 0i64;
    }
    v11->m_Map = 0i64;
  }
  ReleaseR3Engine();
  CMyTimer::~CMyTimer(&v11->m_tmrRecover);
  CMyTimer::~CMyTimer(&v11->m_tmrSystem);
  CMyTimer::~CMyTimer(&v11->m_tmrObjTerm);
  std::vector<std::pair<int,int>,std::allocator<std::pair<int,int>>>::~vector<std::pair<int,int>,std::allocator<std::pair<int,int>>>(&v11->m_vecStandardMapCodeTable);
  CMapDataTable::~CMapDataTable(&v11->m_tblMapData);
}
