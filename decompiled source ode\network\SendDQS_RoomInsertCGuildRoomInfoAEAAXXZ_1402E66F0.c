/*
 * Function: ?SendDQS_RoomInsert@CGuildRoomInfo@@AEAAXXZ
 * Address: 0x1402E66F0
 */

void __fastcall CGuildRoomInfo::SendDQS_RoomInsert(CGuildRoomInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-58h]@1
  unsigned int Dst; // [sp+38h] [bp-20h]@4
  char v5; // [sp+3Ch] [bp-1Ch]@4
  char v6; // [sp+3Dh] [bp-1Bh]@4
  CGuildRoomInfo *v7; // [sp+60h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(&Dst, 0, 8ui64);
  Dst = v7->m_dwGuildSerial;
  v5 = v7->m_byRoomType;
  v6 = v7->m_byRace;
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 71, (char *)&Dst, 8);
}
