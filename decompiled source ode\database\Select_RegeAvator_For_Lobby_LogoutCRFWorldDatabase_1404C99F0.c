/*
 * Function: ?Select_RegeAvator_For_Lobby_Logout@CRFWorldDatabase@@QEAAEKPEAU_rege_char_data@@@Z
 * Address: 0x1404C99F0
 */

char __fastcall CRFWorldDatabase::Select_RegeAvator_For_Lobby_Logout(CRFWorldDatabase *this, unsigned int dwAccountSerial, _rege_char_data *pRegeCharData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-488h]@1
  void *SQLStmt; // [sp+20h] [bp-468h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-460h]@17
  char _Dest[1024]; // [sp+40h] [bp-448h]@4
  SQLLEN v10; // [sp+458h] [bp-30h]@17
  __int16 v11; // [sp+464h] [bp-24h]@9
  int v12; // [sp+468h] [bp-20h]@14
  unsigned __int64 v13; // [sp+478h] [bp-10h]@4
  CRFWorldDatabase *v14; // [sp+490h] [bp+8h]@1
  _rege_char_data *v15; // [sp+4A0h] [bp+18h]@1

  v15 = pRegeCharData;
  v14 = this;
  v3 = &v6;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = (unsigned __int64)&v6 ^ _security_cookie;
  sprintf_s<1024>(
    (char (*)[1024])_Dest,
    "select Serial,Name,Slot,Lv,Dalant,Gold from tbl_base where AccountSerial=%d and DCK=0",
    dwAccountSerial);
  if ( v14->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v14->vfptr, _Dest);
  if ( v14->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v14->vfptr) )
  {
    v11 = SQLExecDirectA_0(v14->m_hStmtSelect, _Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v14->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v11, _Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v11, v14->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v12 = 0;
      while ( 1 )
      {
        v11 = SQLFetch_0(v14->m_hStmtSelect);
        if ( v11 )
        {
          if ( v11 != 1 )
            break;
        }
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v14->m_hStmtSelect, 1u, -18, &v15->RegeList[v12].dwCharSerial, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = (void *)17;
        v11 = SQLGetData_0(v14->m_hStmtSelect, 2u, 1, v15->RegeList[v12].szCharName, 17i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v14->m_hStmtSelect, 3u, -17, &v15->RegeList[v12], 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v14->m_hStmtSelect, 4u, 4, &v15->RegeList[v12].nLevel, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v14->m_hStmtSelect, 5u, 4, &v15->RegeList[v12].dwDalant, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v14->m_hStmtSelect, 6u, 4, &v15->RegeList[v12++].dwGold, 0i64, &v10);
        if ( v12 >= 3 )
          break;
        if ( v11 && v11 != 1 )
        {
          if ( v11 != 100 )
          {
            SQLStmt = v14->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v11, _Dest, "SQLExecDirectA", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v11, v14->m_hStmtSelect);
            if ( v14->m_hStmtSelect )
              SQLCloseCursor_0(v14->m_hStmtSelect);
            return 1;
          }
          break;
        }
      }
      v15->nCharNum = v12;
      if ( v14->m_hStmtSelect )
        SQLCloseCursor_0(v14->m_hStmtSelect);
      if ( v14->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v14->vfptr, "%s Success", _Dest);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v14->vfptr, "ReConnectDataBase Fail. Query : %s", _Dest);
    result = 1;
  }
  return result;
}
