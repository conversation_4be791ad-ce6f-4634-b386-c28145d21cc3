/*
 * Function: j_?cut_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@HPEAGKKPEAD@Z
 * Address: 0x140006C71
 */

void __fastcall CMgrAvatorItemHistory::cut_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pOreItem, int nOreNum, unsigned __int16 *pwCuttingResBuffer, unsigned int dwCostDalant, unsigned int dwNewDalant, char *pszFileName)
{
  CMgrAvatorItemHistory::cut_item(
    this,
    n,
    pOreItem,
    nOreNum,
    pwCuttingResBuffer,
    dwCostDalant,
    dwNewDalant,
    pszFileName);
}
