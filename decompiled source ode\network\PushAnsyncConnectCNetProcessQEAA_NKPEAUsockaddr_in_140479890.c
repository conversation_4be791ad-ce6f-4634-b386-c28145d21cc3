/*
 * Function: ?PushAnsyncConnect@CNetProcess@@QEAA_NKPEAUsockaddr_in@@@Z
 * Address: 0x140479890
 */

bool __fastcall CNetProcess::PushAnsyncConnect(CNetProcess *this, unsigned int dwSocketIndex, sockaddr_in *pAddr)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  _socket *v7; // [sp+20h] [bp-18h]@8
  void *Dst; // [sp+28h] [bp-10h]@10
  CNetProcess *v9; // [sp+40h] [bp+8h]@1
  unsigned int dwIndex; // [sp+48h] [bp+10h]@1
  sockaddr_in *Src; // [sp+50h] [bp+18h]@1

  Src = pAddr;
  dwIndex = dwSocketIndex;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v9->m_Type.m_bAnSyncConnect )
  {
    if ( CNetIndexList::IsInList(&v9->m_listAnsyncConnect, dwSocketIndex) )
    {
      result = 0;
    }
    else
    {
      v7 = CNetSocket::GetSocket(&v9->m_NetSocket, dwIndex);
      if ( v7->m_bAccept )
      {
        result = 0;
      }
      else
      {
        Dst = &v9->m_AnsyncConnectData[dwIndex];
        memcpy_0(Dst, Src, 0x10ui64);
        result = CNetIndexList::PushNode_Back(&v9->m_listAnsyncConnect, dwIndex);
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
