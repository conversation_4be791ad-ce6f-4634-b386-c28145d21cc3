/*
 * Function: ?Complete_db_Update_Data_For_Trade@CMainThread@@AEAAXPEAD@Z
 * Address: 0x1401B93C0
 */

void __fastcall CMainThread::Complete_db_Update_Data_For_Trade(CMainThread *this, char *pSheet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  char *v5; // [sp+20h] [bp-28h]@4
  CPlayer *v6; // [sp+28h] [bp-20h]@6
  int j; // [sp+30h] [bp-18h]@4

  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = pSheet;
  for ( j = 0; j < 2; ++j )
  {
    v6 = GetPtrPlayerFromSerial(&g_Player, 2532, *(_DWORD *)&v5[32 * j]);
    if ( v6 )
    {
      if ( v6->m_bOper )
      {
        v6->m_pUserDB->m_AvatorData_bk.dbAvator.m_dwDalant = v6->m_pUserDB->m_AvatorData.dbAvator.m_dwDalant;
        v6->m_pUserDB->m_AvatorData_bk.dbAvator.m_dwGold = v6->m_pUserDB->m_AvatorData.dbAvator.m_dwGold;
        _INVEN_DB_BASE::operator=(&v6->m_pUserDB->m_AvatorData_bk.dbInven, &v6->m_pUserDB->m_AvatorData.dbInven);
      }
    }
  }
}
