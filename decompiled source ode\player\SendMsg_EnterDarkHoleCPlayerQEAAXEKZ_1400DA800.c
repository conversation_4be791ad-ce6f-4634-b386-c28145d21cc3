/*
 * Function: ?SendMsg_EnterDarkHole@CPlayer@@QEAAXEK@Z
 * Address: 0x1400DA800
 */

void __fastcall CPlayer::SendMsg_EnterDarkHole(CPlayer *this, char byErrCode, unsigned int dwHoleSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@4
  __int64 v6; // [sp+0h] [bp-98h]@1
  _darkhole_enter_result_zocl v7; // [sp+38h] [bp-60h]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v9; // [sp+65h] [bp-33h]@4
  unsigned __int64 v10; // [sp+80h] [bp-18h]@4
  CPlayer *v11; // [sp+A0h] [bp+8h]@1

  v11 = this;
  v3 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = (unsigned __int64)&v6 ^ _security_cookie;
  v7.byRetCode = byErrCode;
  v7.dwHoleSerial = dwHoleSerial;
  v7.byMapCode = v11->m_pCurMap->m_pMapSet->m_dwIndex;
  FloatToShort(v11->m_fCurPos, v7.zPos, 3);
  pbyType = 35;
  v9 = -52;
  v5 = _darkhole_enter_result_zocl::size(&v7);
  CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, &v7.byRetCode, v5);
}
