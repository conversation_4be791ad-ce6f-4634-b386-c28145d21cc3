/*
 * Function: j_??$_Allocate@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@YAPEAVCGuildBattleRewardItem@GUILD_BATTLE@@_KPEAV12@@Z
 * Address: 0x14000FF9C
 */

GUILD_BATTLE::CGuildBattleRewardItem *__fastcall std::_Allocate<GUILD_BATTLE::CGuildBattleRewardItem>(unsigned __int64 _Count, GUILD_BATTLE::CGuildBattleRewardItem *__formal)
{
  return std::_Allocate<GUILD_BATTLE::CGuildBattleRewardItem>(_Count, __formal);
}
