/*
 * Function: ??0CUnmannedTraderScheduler@@IEAA@XZ
 * Address: 0x140393120
 */

void __fastcall CUnmannedTraderScheduler::CUnmannedTraderScheduler(CUnmannedTraderScheduler *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CUnmannedTraderScheduler *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  v5->m_bLoad = 0;
  v5->m_pkTimer = 0i64;
  v5->m_pkLogger = 0i64;
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(&v5->m_iterSchedule);
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(&v5->m_veckSchdule);
}
