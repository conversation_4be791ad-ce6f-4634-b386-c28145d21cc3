/*
 * Function: ??$_Pop_heap_0@V?$_Vector_iterator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@YAXV?$_Vector_iterator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@0@0PEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@Z
 * Address: 0x1405A6610
 */

int __fastcall std::_Pop_heap_0<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>(__int64 a1, __int64 a2)
{
  __int64 v2; // rax@1
  CryptoPP::ECPPoint *v3; // rax@1
  CryptoPP::ECPPoint *v4; // rax@1
  __int64 v5; // rax@1
  __int64 v6; // rax@1
  char v8; // [sp+30h] [bp-188h]@1
  char *v9; // [sp+48h] [bp-170h]@1
  char v10; // [sp+50h] [bp-168h]@1
  CryptoPP::ECPPoint *v11; // [sp+D0h] [bp-E8h]@1
  char v12; // [sp+D8h] [bp-E0h]@1
  char v13; // [sp+F0h] [bp-C8h]@1
  char *v14; // [sp+108h] [bp-B0h]@1
  char v15; // [sp+110h] [bp-A8h]@1
  char *v16; // [sp+128h] [bp-90h]@1
  char v17; // [sp+130h] [bp-88h]@1
  char *v18; // [sp+148h] [bp-70h]@1
  __int64 v19; // [sp+150h] [bp-68h]@1
  __int64 v20; // [sp+158h] [bp-60h]@1
  __int64 v21; // [sp+160h] [bp-58h]@1
  __int64 v22; // [sp+168h] [bp-50h]@1
  __int64 v23; // [sp+170h] [bp-48h]@1
  CryptoPP::ECPPoint *v24; // [sp+178h] [bp-40h]@1
  CryptoPP::ECPPoint *v25; // [sp+180h] [bp-38h]@1
  __int64 v26; // [sp+188h] [bp-30h]@1
  __int64 v27; // [sp+190h] [bp-28h]@1
  __int64 v28; // [sp+198h] [bp-20h]@1
  __int64 v29; // [sp+1A0h] [bp-18h]@1
  __int64 v30; // [sp+1A8h] [bp-10h]@1
  __int64 v31; // [sp+1C8h] [bp+10h]@1

  v31 = a2;
  v19 = -2i64;
  v9 = &v8;
  v11 = (CryptoPP::ECPPoint *)&v10;
  v14 = &v13;
  v16 = &v15;
  v18 = &v17;
  v20 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>((__int64)&v8);
  v21 = std::_Dist_type<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>>();
  LODWORD(v2) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator-(
                  v31,
                  &v12,
                  1i64);
  v22 = v2;
  v23 = v2;
  LODWORD(v3) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator*();
  v4 = CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(
         v11,
         v3);
  v24 = v4;
  v25 = v4;
  LODWORD(v5) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator-(
                  v31,
                  v14,
                  1i64);
  v26 = v5;
  v27 = v5;
  LODWORD(v6) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator-(
                  v31,
                  v16,
                  1i64);
  v28 = v6;
  v29 = v6;
  v30 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>((__int64)v18);
  std::_Pop_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>(
    v30,
    v29,
    v27,
    v25);
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
  return std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
}
