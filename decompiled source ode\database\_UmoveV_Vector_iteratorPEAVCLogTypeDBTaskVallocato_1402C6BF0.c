/*
 * Function: ??$_Umove@V?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@IEAAPEAPEAVCLogTypeDBTask@@V?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@1@0PEAPEAV2@@Z
 * Address: 0x1402C6BF0
 */

CLogTypeDBTask **__fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Umove<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>>(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_First, std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_Last, CLogTypeDBTask **_Ptr)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v6; // rax@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v7; // rax@4
  __int64 v9; // [sp+0h] [bp-98h]@1
  CLogTypeDBTask **v10; // [sp+20h] [bp-78h]@4
  char v11; // [sp+28h] [bp-70h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v12; // [sp+40h] [bp-58h]@4
  char v13; // [sp+48h] [bp-50h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v14; // [sp+60h] [bp-38h]@4
  __int64 v15; // [sp+68h] [bp-30h]@4
  std::allocator<CLogTypeDBTask *> *v16; // [sp+70h] [bp-28h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v17; // [sp+78h] [bp-20h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v18; // [sp+80h] [bp-18h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v19; // [sp+88h] [bp-10h]@4
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v20; // [sp+A0h] [bp+8h]@1
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v21; // [sp+A8h] [bp+10h]@1
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *__that; // [sp+B0h] [bp+18h]@1
  CLogTypeDBTask **v23; // [sp+B8h] [bp+20h]@1

  v23 = _Ptr;
  __that = _Last;
  v21 = _First;
  v20 = this;
  v4 = &v9;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v15 = -2i64;
  v12 = (std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&v11;
  v14 = (std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&v13;
  v16 = &v20->_Alval;
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(
    (std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&v11,
    _Last);
  v17 = v6;
  v18 = v6;
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(
    v14,
    v21);
  v19 = v7;
  v10 = stdext::_Unchecked_uninitialized_move<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>>(
          v7,
          v18,
          v23,
          v16);
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(v21);
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(__that);
  return v10;
}
