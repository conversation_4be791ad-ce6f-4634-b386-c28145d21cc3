/*
 * Function: ?IsValidID@CUnmannedTraderDivisionInfo@@IEAA_NK@Z
 * Address: 0x14036E660
 */

char __fastcall CUnmannedTraderDivisionInfo::IsValidID(CUnmannedTraderDivisionInfo *this, unsigned int dwID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@5
  CUnmannedTraderClassInfo **v5; // rax@10
  __int64 v6; // [sp+0h] [bp-88h]@1
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > result; // [sp+28h] [bp-60h]@8
  bool v8; // [sp+44h] [bp-44h]@9
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > v9; // [sp+48h] [bp-40h]@9
  char v10; // [sp+60h] [bp-28h]@11
  char v11; // [sp+61h] [bp-27h]@13
  __int64 v12; // [sp+68h] [bp-20h]@4
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v13; // [sp+70h] [bp-18h]@9
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_Right; // [sp+78h] [bp-10h]@9
  CUnmannedTraderDivisionInfo *v15; // [sp+90h] [bp+8h]@1
  unsigned int v16; // [sp+98h] [bp+10h]@1

  v16 = dwID;
  v15 = this;
  v2 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = -2i64;
  if ( dwID == -1 )
  {
    v4 = 0;
  }
  else if ( std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::empty(&v15->m_vecClass) )
  {
    v4 = 1;
  }
  else
  {
    std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::begin(&v15->m_vecClass, &result);
    while ( 1 )
    {
      v13 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::end(
              &v15->m_vecClass,
              &v9);
      _Right = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)v13;
      v8 = std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator!=(
             (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&result._Mycont,
             (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v13->_Mycont);
      std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&v9);
      if ( !v8 )
        break;
      v5 = std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator*(&result);
      if ( CUnmannedTraderClassInfo::GetID(*v5) == v16 )
      {
        v10 = 0;
        std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&result);
        return v10;
      }
      std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator++(&result);
    }
    v11 = 1;
    std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&result);
    v4 = v11;
  }
  return v4;
}
