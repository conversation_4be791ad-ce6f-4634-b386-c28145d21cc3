/*
 * Function: ?UpdateContUserSave@CUserDB@@QEAA_N_N@Z
 * Address: 0x140113DF0
 */

char __usercall CUserDB::UpdateContUserSave@<al>(CUserDB *this@<rcx>, bool bDirect@<dl>, signed __int64 a3@<rax>)
{
  void *v3; // rsp@1
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // rax@12
  unsigned int v8; // eax@16
  unsigned int v9; // eax@17
  __int64 v10; // rax@17
  __int64 v11; // [sp-20h] [bp-12378h]@1
  char *pQryData; // [sp+0h] [bp-12358h]@19
  int nSize; // [sp+8h] [bp-12350h]@19
  DWORD v14; // [sp+10h] [bp-12348h]@6
  _qry_case_contsave Dst; // [sp+30h] [bp-12328h]@12
  __int64 v16; // [sp+12308h] [bp-50h]@17
  __int64 v17; // [sp+12310h] [bp-48h]@18
  _DB_QRY_SYN_DATA *v18; // [sp+12318h] [bp-40h]@19
  char v19; // [sp+12328h] [bp-30h]@20
  char v20; // [sp+12329h] [bp-2Fh]@21
  __int64 v21; // [sp+12330h] [bp-28h]@4
  __int64 v22; // [sp+12338h] [bp-20h]@16
  __int64 v23; // [sp+12340h] [bp-18h]@17
  unsigned __int64 v24; // [sp+12348h] [bp-10h]@4
  CUserDB *v25; // [sp+12360h] [bp+8h]@1
  bool v26; // [sp+12368h] [bp+10h]@1

  v26 = bDirect;
  v25 = this;
  v3 = alloca(a3);
  v4 = &v11;
  for ( i = 18652i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v21 = -2i64;
  v24 = (unsigned __int64)&v11 ^ _security_cookie;
  if ( v25->m_bDBWaitState )
    return 0;
  v14 = timeGetTime();
  if ( v26 )
    goto LABEL_24;
  if ( v14 - v25->m_dwTermContSaveTime < 0x493E0 )
    return 0;
  v25->m_dwTermContSaveTime = v14;
  if ( v25->m_bDataUpdate || v14 - v25->m_dwLastContSaveTime >= 0x927C0 )
  {
LABEL_24:
    _qry_case_contsave::_qry_case_contsave(&Dst);
    v7 = v25->m_AvatorData.m_byHSKTime;
    if ( v25->m_AvatorData.m_byHSKTime <= 2
      && v25->m_AvatorData.m_byCristalBattleDBInfo != 3
      && v25->m_AvatorData.m_byCristalBattleDBInfo == 1 )
    {
      v25->m_AvatorData.m_byCristalBattleDBInfo = 3;
      v25->m_AvatorData.m_byHSKTime = -1;
    }
    Dst.dwAvatorSerial = v25->m_dwSerial;
    memcpy_0(&Dst.NewData, &v25->m_AvatorData, 0x915Fui64);
    memcpy_0(&Dst.OldData, &v25->m_AvatorData_bk, 0x915Fui64);
    Dst.bUpdateRefineCnt = 0;
    v8 = v25->m_idWorld.wIndex;
    v22 = *qword_1799C9AF0;
    if ( (unsigned __int8)(*(int (__fastcall **)(_QWORD *, _QWORD))(v22 + 40))(qword_1799C9AF0, v8) )
    {
      v9 = v25->m_idWorld.wIndex;
      v23 = *qword_1799C9AF0;
      LODWORD(v10) = (*(int (__fastcall **)(_QWORD *, _QWORD, _QWORD))(v23 + 64))(
                       qword_1799C9AF0,
                       v9,
                       v25->m_idWorld.dwSerial);
      v16 = v10;
      if ( v10 )
      {
        Dst.bUpdateRefineCnt = 1;
        v17 = v16;
        Dst.byRefinedCnt = *(_BYTE *)(v16 + 16);
        Dst.dwRefineDate = *(_DWORD *)(v16 + 12);
      }
    }
    nSize = _qry_case_contsave::size(&Dst);
    pQryData = (char *)&Dst;
    v18 = CMainThread::PushDQSData(&g_Main, v25->m_dwAccountSerial, &v25->m_idWorld, 12, (char *)&Dst, nSize);
    if ( v18 )
    {
      v25->m_bDBWaitState = 1;
      v25->m_pDBPushData = v18;
      _AVATOR_DATA::PostUpdateInit(&v25->m_AvatorData);
      v25->m_bDataUpdate = 0;
      v25->m_dwLastContSaveTime = v14;
      v20 = 1;
      _qry_case_contsave::~_qry_case_contsave(&Dst);
      result = v20;
    }
    else
    {
      v19 = 0;
      _qry_case_contsave::~_qry_case_contsave(&Dst);
      result = v19;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
