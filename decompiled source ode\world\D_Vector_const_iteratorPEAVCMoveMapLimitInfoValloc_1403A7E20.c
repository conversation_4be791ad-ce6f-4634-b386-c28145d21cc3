/*
 * Function: ??D?$_Vector_const_iterator@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEBAAEBQEAVCMoveMapLimitInfo@@XZ
 * Address: 0x1403A7E20
 */

CMoveMapLimitInfo **__fastcall std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::operator*(std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this)
{
  return this->_Myptr;
}
