/*
 * Function: ??$_Uninit_fill_n@PEAVCUnmannedTraderItemCodeInfo@@_KV1@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@YAXPEAVCUnmannedTraderItemCodeInfo@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderItemCodeInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14037B4E0
 */

void __fastcall std::_Uninit_fill_n<CUnmannedTraderItemCodeInfo *,unsigned __int64,CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(CUnmannedTraderItemCodeInfo *_First, unsigned __int64 _Count, CUnmannedTraderItemCodeInfo *_Val, std::allocator<CUnmannedTraderItemCodeInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-38h]@1
  CUnmannedTraderItemCodeInfo *v9; // [sp+20h] [bp-18h]@4
  __int64 v10; // [sp+28h] [bp-10h]@4
  CUnmannedTraderItemCodeInfo *_Ptr; // [sp+40h] [bp+8h]@1
  unsigned __int64 v12; // [sp+48h] [bp+10h]@1
  CUnmannedTraderItemCodeInfo *_Vala; // [sp+50h] [bp+18h]@1
  std::allocator<CUnmannedTraderItemCodeInfo> *v14; // [sp+58h] [bp+20h]@1

  v14 = _Al;
  _Vala = _Val;
  v12 = _Count;
  _Ptr = _First;
  v6 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10 = -2i64;
  v9 = _Ptr;
  while ( v12 )
  {
    std::allocator<CUnmannedTraderItemCodeInfo>::construct(v14, _Ptr, _Vala);
    --v12;
    ++_Ptr;
  }
}
