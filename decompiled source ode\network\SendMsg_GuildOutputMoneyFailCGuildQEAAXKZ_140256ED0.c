/*
 * Function: ?SendMsg_GuildOutputMoneyFail@CGuild@@QEAAXK@Z
 * Address: 0x140256ED0
 */

void __fastcall CGuild::SendMsg_GuildOutputMoneyFail(CGuild *this, unsigned int dwIOerSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v7; // [sp+55h] [bp-23h]@4
  int j; // [sp+64h] [bp-14h]@4
  CGuild *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  *(_DWORD *)szMsg = dwIOerSerial;
  pbyType = 27;
  v7 = 38;
  for ( j = 0; j < 50; ++j )
  {
    if ( _guild_member_info::IsFill(&v9->m_MemberData[j]) )
    {
      if ( v9->m_MemberData[j].pPlayer )
        CNetProcess::LoadSendMsg(unk_1414F2088, v9->m_MemberData[j].pPlayer->m_ObjID.m_wIndex, &pbyType, szMsg, 4u);
    }
  }
}
