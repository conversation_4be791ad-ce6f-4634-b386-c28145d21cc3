/*
 * Function: ?CreateStores@CMapItemStoreList@@QEAA_NPEAVCMapData@@@Z
 * Address: 0x14034BF40
 */

bool __fastcall CMapItemStoreList::CreateStores(CMapItemStoreList *this, CMapData *pMap)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  signed __int64 v5; // rax@6
  unsigned __int8 v6; // cf@8
  unsigned __int64 v7; // rax@8
  __int64 v8; // [sp+0h] [bp-68h]@1
  int count[2]; // [sp+30h] [bp-38h]@6
  CItemStore *v10; // [sp+38h] [bp-30h]@13
  void *v11; // [sp+40h] [bp-28h]@10
  __int64 v12; // [sp+48h] [bp-20h]@4
  CItemStore *v13; // [sp+50h] [bp-18h]@11
  CMapItemStoreList *v14; // [sp+70h] [bp+8h]@1
  CMapData *pMapa; // [sp+78h] [bp+10h]@1

  pMapa = pMap;
  v14 = this;
  v2 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = -2i64;
  if ( pMap->m_nItemStoreDumNum > 0 )
  {
    v14->m_nItemStoreNum = pMap->m_nItemStoreDumNum;
    *(_QWORD *)count = pMap->m_nItemStoreDumNum;
    v5 = 120i64 * *(_QWORD *)count;
    if ( !is_mul_ok(0x78ui64, *(unsigned __int64 *)count) )
      v5 = -1i64;
    v6 = __CFADD__(v5, 8i64);
    v7 = v5 + 8;
    if ( v6 )
      v7 = -1i64;
    v11 = operator new[](v7);
    if ( v11 )
    {
      *(_DWORD *)v11 = count[0];
      `eh vector constructor iterator'(
        (char *)v11 + 8,
        0x78ui64,
        count[0],
        (void (__cdecl *)(void *))CItemStore::CItemStore,
        (void (__cdecl *)(void *))CItemStore::~CItemStore);
      v13 = (CItemStore *)((char *)v11 + 8);
    }
    else
    {
      v13 = 0i64;
    }
    v10 = v13;
    v14->m_ItemStore = v13;
    result = CMapItemStoreList::SetItemStores(v14, pMapa);
  }
  else
  {
    v14->m_nItemStoreNum = 0;
    result = 1;
  }
  return result;
}
