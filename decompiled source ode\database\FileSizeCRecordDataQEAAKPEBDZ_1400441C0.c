/*
 * Function: ?FileSize@CRecordData@@QEAAKPEBD@Z
 * Address: 0x1400441C0
 */

__int64 __fastcall CRecordData::FileSize(CRecordData *this, const char *szFile)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  __int64 v5; // [sp+0h] [bp-58h]@1
  HANDLE hFile; // [sp+40h] [bp-18h]@4
  DWORD v7; // [sp+48h] [bp-10h]@6
  DWORD v8; // [sp+4Ch] [bp-Ch]@6

  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  hFile = CreateFileA(szFile, 0x80000000, 1u, 0i64, 3u, 0x80u, 0i64);
  if ( hFile == (HANDLE)-1 )
  {
    result = 0i64;
  }
  else
  {
    v7 = SetFilePointer(hFile, 0, 0i64, 0);
    v8 = SetFilePointer(hFile, 0, 0i64, 2u);
    CloseHandle(hFile);
    result = v8 - v7;
  }
  return result;
}
