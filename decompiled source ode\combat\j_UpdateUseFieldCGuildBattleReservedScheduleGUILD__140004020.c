/*
 * Function: j_?UpdateUseField@CGuildBattleReservedSchedule@GUILD_BATTLE@@AEAAXKK@Z
 * Address: 0x140004020
 */

void __fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::UpdateUseField(GUILD_BATTLE::CGuildBattleReservedSchedule *this, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt)
{
  GUILD_BATTLE::CGuildBattleReservedSchedule::UpdateUseField(this, dwStartTimeInx, dwElapseTimeCnt);
}
