/*
 * Function: ?SF_RecoverAllReturnStateAnimusHPFull@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 * Address: 0x1400A0790
 */

bool __fastcall CPlayer::SF_RecoverAllReturnStateAnimusHPFull(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-68h]@1
  bool bUpdate; // [sp+20h] [bp-48h]@17
  CCharacter *v8; // [sp+30h] [bp-38h]@6
  int v9; // [sp+38h] [bp-30h]@10
  _STORAGE_LIST::_db_con *v10; // [sp+40h] [bp-28h]@10
  unsigned int upg; // [sp+48h] [bp-20h]@10
  int j; // [sp+4Ch] [bp-1Ch]@10
  CPlayer *v13; // [sp+70h] [bp+8h]@1

  v13 = this;
  v3 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pDstObj->m_ObjID.m_byID )
  {
    result = 0;
  }
  else
  {
    v8 = pDstObj;
    if ( *(_QWORD *)&pDstObj[1].m_nScreenPos[0] )
    {
      if ( CPlayerDB::GetRaceCode((CPlayerDB *)&v8[1].m_fOldPos[2]) == 1 )
      {
        v9 = 0;
        v10 = 0i64;
        upg = 0;
        for ( j = 0; j < 4; ++j )
        {
          v10 = (_STORAGE_LIST::_db_con *)(*(_QWORD *)&v8[6].m_SFContAura[0][1].m_byEffectCode + 50i64 * j);
          if ( v10->m_bLoad && v13->m_pRecalledAnimusItem != v10 )
          {
            upg = GetMaxParamFromExp(v10->m_wItemIndex, v10->m_dwDur);
            if ( upg != v10->m_dwLv )
            {
              v10->m_dwLv = upg;
              bUpdate = 0;
              CUserDB::Update_ItemUpgrade(*(CUserDB **)&v8[1].m_nScreenPos[0], 4, v10->m_byStorageIndex, upg, 0);
              ++v9;
            }
          }
        }
        result = v9 > 0;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  return result;
}
