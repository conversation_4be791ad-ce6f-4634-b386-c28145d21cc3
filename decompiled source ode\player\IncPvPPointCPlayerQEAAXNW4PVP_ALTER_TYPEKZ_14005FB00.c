/*
 * Function: ?IncPvPPoint@CPlayer@@QEAAXNW4PVP_ALTER_TYPE@@K@Z
 * Address: 0x14005FB00
 */

void __fastcall CPlayer::IncPvPPoint(CPlayer *this, long double dAlter, PVP_ALTER_TYPE AlterType, unsigned int dwDstSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp-28h] [bp-28h]@1
  CPlayer *v7; // [sp+8h] [bp+8h]@1

  v7 = this;
  v4 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( dAlter >= 1.0 )
    CPlayer::AlterPvPPoint(v7, dAlter, AlterType, dwDstSerial);
}
