/*
 * Function: ?Initialize@?$DL_PrivateKey_EC@VECP@CryptoPP@@@CryptoPP@@QEAAXAEAVRandomNumberGenerator@2@AEBVECP@2@AEBUECPPoint@2@AEBVInteger@2@@Z
 * Address: 0x1405595D0
 */

void __fastcall CryptoPP::DL_PrivateKey_EC<CryptoPP::ECP>::Initialize(__int64 a1, __int64 a2, __int64 a3, __int64 a4, __int64 a5)
{
  const struct CryptoPP::Integer *v5; // rax@1
  CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> v6; // [sp+38h] [bp-1B0h]@1
  __int64 v7; // [sp+1B0h] [bp-38h]@1
  CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> > *v8; // [sp+1B8h] [bp-30h]@1
  CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> > *v9; // [sp+1C0h] [bp-28h]@1
  _BYTE *v10; // [sp+1C8h] [bp-20h]@2
  void (__fastcall **v11)(_QWORD, _QWORD, _QWORD); // [sp+1D0h] [bp-18h]@4
  __int64 v12; // [sp+1F0h] [bp+8h]@1
  __int64 v13; // [sp+1F8h] [bp+10h]@1
  __int64 v14; // [sp+200h] [bp+18h]@1
  __int64 v15; // [sp+208h] [bp+20h]@1

  v15 = a4;
  v14 = a3;
  v13 = a2;
  v12 = a1;
  v7 = -2i64;
  v5 = CryptoPP::Integer::Zero();
  v8 = CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::DL_GroupParameters_EC<CryptoPP::ECP>(
         (__int64)&v6,
         v14,
         v15,
         a5,
         (__int64)v5,
         1);
  v9 = v8;
  if ( v8 )
    v10 = &v8->gap8[*(_DWORD *)(*(_QWORD *)&v8->gap8[0] + 4i64)];
  else
    v10 = 0i64;
  v11 = *(void (__fastcall ***)(_QWORD, _QWORD, _QWORD))(v12 + 16);
  (*v11)(v12 + 16, v13, v10);
  CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::`vbase destructor(&v6);
}
