/*
 * Function: ?DTradeBetRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D3650
 */

char __fastcall CNetworkEX::DTradeBetRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  char *v6; // rax@9
  __int64 v7; // [sp+0h] [bp-38h]@1
  char *v8; // [sp+20h] [bp-18h]@4
  CPlayer *v9; // [sp+28h] [bp-10h]@4
  CNetworkEX *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = pBuf;
  v9 = &g_Player + n;
  if ( v9->m_bOper && !v9->m_bCorpse )
  {
    if ( *v8 && *v8 != 1 )
    {
      v6 = CPlayerDB::GetCharNameA(&v9->m_Param);
      CLogFile::Write(
        &v10->m_LogFile,
        "odd.. %s: DTradeBetRequest().. if(pRecv->byMoneyUnit != money_unit_dalant && pRecv->byMoneyUnit != money_unit_gold)",
        v6);
      result = 0;
    }
    else
    {
      CPlayer::pc_DTradeBetRequest(v9, *v8, *(_DWORD *)(v8 + 1));
      result = 1;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
