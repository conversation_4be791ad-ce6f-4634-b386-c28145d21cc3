/*
 * Function: ?Init@CAsyncLogBuffer<PERSON>ist@@QEAA_NIIAEAVCLogFile@@@Z
 * Address: 0x1403BD510
 */

char __fastcall CAsyncLogBufferList::Init(CAsyncLogBufferList *this, unsigned int uiMaxBufferCnt, unsigned int uiMaxBufferSize, CLogFile *logLoading)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  signed __int64 v7; // rax@8
  unsigned __int8 v8; // cf@10
  unsigned __int64 v9; // rax@10
  __int64 v10; // [sp+0h] [bp-68h]@1
  void (__cdecl *pDtor)(void *); // [sp+20h] [bp-48h]@20
  unsigned int j; // [sp+30h] [bp-38h]@17
  unsigned int dwIndex; // [sp+34h] [bp-34h]@22
  int count[2]; // [sp+38h] [bp-30h]@8
  CAsyncLogBuffer *v15; // [sp+40h] [bp-28h]@15
  void *v16; // [sp+48h] [bp-20h]@12
  __int64 v17; // [sp+50h] [bp-18h]@4
  CAsyncLogBuffer *v18; // [sp+58h] [bp-10h]@13
  CAsyncLogBufferList *v19; // [sp+70h] [bp+8h]@1
  unsigned int dwMaxBufNum; // [sp+78h] [bp+10h]@1
  unsigned int iMaxBufferSize; // [sp+80h] [bp+18h]@1
  CLogFile *v22; // [sp+88h] [bp+20h]@1

  v22 = logLoading;
  iMaxBufferSize = uiMaxBufferSize;
  dwMaxBufNum = uiMaxBufferCnt;
  v19 = this;
  v4 = &v10;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v17 = -2i64;
  if ( uiMaxBufferCnt && uiMaxBufferSize && uiMaxBufferSize >= 4 )
  {
    *(_QWORD *)count = uiMaxBufferCnt;
    v7 = 280i64 * uiMaxBufferCnt;
    if ( !is_mul_ok(0x118ui64, uiMaxBufferCnt) )
      v7 = -1i64;
    v8 = __CFADD__(v7, 8i64);
    v9 = v7 + 8;
    if ( v8 )
      v9 = -1i64;
    v16 = operator new[](v9);
    if ( v16 )
    {
      *(_DWORD *)v16 = count[0];
      `eh vector constructor iterator'(
        (char *)v16 + 8,
        0x118ui64,
        count[0],
        (void (__cdecl *)(void *))CAsyncLogBuffer::CAsyncLogBuffer,
        (void (__cdecl *)(void *))CAsyncLogBuffer::~CAsyncLogBuffer);
      v18 = (CAsyncLogBuffer *)((char *)v16 + 8);
    }
    else
    {
      v18 = 0i64;
    }
    v15 = v18;
    v19->m_pLogBuffer = v18;
    if ( v19->m_pLogBuffer )
    {
      for ( j = 0; j < dwMaxBufNum; ++j )
      {
        if ( !CAsyncLogBuffer::Init(&v19->m_pLogBuffer[j], iMaxBufferSize) )
        {
          LODWORD(pDtor) = j;
          CLogFile::Write(
            v22,
            "CAsyncLogBufferList::Init( UINT uiMaxBufferCnt(%u), UINT uiMaxBufferSize(%u) ) : m_pLogBuffer[i(%u)]->Init( "
            "uiMaxBufferSize ) Fail!",
            dwMaxBufNum,
            iMaxBufferSize);
          return 0;
        }
      }
      v19->m_uiMaxBufferCnt = dwMaxBufNum;
      v19->m_uiMaxBufferSize = iMaxBufferSize;
      CNetIndexList::SetList(&v19->m_klistEmpty, dwMaxBufNum);
      for ( dwIndex = 0; dwIndex < dwMaxBufNum; ++dwIndex )
        CNetIndexList::PushNode_Back(&v19->m_klistEmpty, dwIndex);
      CNetIndexList::SetList(&v19->m_klistProc, dwMaxBufNum);
      result = 1;
    }
    else
    {
      CLogFile::Write(
        v22,
        "CAsyncLogBufferList::Init( UINT uiMaxBufferCnt(%u), UINT uiMaxBufferSize(%u) ) : new CAsyncLogBuffer[uiMaxBufferCnt] NULL!",
        dwMaxBufNum,
        iMaxBufferSize);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
