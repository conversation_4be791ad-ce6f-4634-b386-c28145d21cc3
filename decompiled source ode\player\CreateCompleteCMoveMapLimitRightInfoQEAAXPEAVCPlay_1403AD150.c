/*
 * Function: ?CreateComplete@CMoveMapLimitRightInfo@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x1403AD150
 */

void __fastcall CMoveMapLimitRightInfo::CreateComplete(CMoveMapLimitRightInfo *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-F8h]@1
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v5; // [sp+20h] [bp-D8h]@4
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > _Right; // [sp+38h] [bp-C0h]@4
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > v7; // [sp+68h] [bp-90h]@4
  CMoveMapLimitRight *v8; // [sp+88h] [bp-70h]@6
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > result; // [sp+90h] [bp-68h]@4
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > v10; // [sp+A8h] [bp-50h]@4
  __int64 v11; // [sp+C0h] [bp-38h]@4
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v12; // [sp+C8h] [bp-30h]@4
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *__that; // [sp+D0h] [bp-28h]@4
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v14; // [sp+D8h] [bp-20h]@4
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v15; // [sp+E0h] [bp-18h]@4
  CMoveMapLimitRightInfo *v16; // [sp+100h] [bp+8h]@1
  CPlayer *v17; // [sp+108h] [bp+10h]@1

  v17 = pkPlayer;
  v16 = this;
  v2 = &v4;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = -2i64;
  v5 = (std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)v16;
  v12 = std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::end(&v16->m_vecRight, &result);
  __that = (std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)v12;
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(
    &_Right,
    (std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v12->_Mycont);
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(&result);
  v14 = std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::begin(v5, &v10);
  v15 = (std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)v14;
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(
    &v7,
    (std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v14->_Mycont);
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(&v10);
  while ( std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator!=(
            &v7,
            &_Right) )
  {
    v8 = *std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator*(&v7);
    ((void (__fastcall *)(CMoveMapLimitRight *, CPlayer *))v8->vfptr->CreateComplete)(v8, v17);
    std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator++(&v7);
  }
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(&v7);
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(&_Right);
}
