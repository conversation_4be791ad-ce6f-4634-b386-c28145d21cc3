/*
 * Function: ?RewardChangeClass@CPlayer@@QEAAXPEAU_class_fld@@E@Z
 * Address: 0x140096550
 */

void __usercall CPlayer::RewardChangeClass(CPlayer *this@<rcx>, _class_fld *pClassFld@<rdx>, char bySelectRewardItem@<r8b>, long double a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@4
  int v7; // ecx@4
  long double v8; // xmm0_8@4
  int v9; // eax@72
  int v10; // ecx@72
  __int64 v11; // [sp+0h] [bp-2C8h]@1
  char byReason[8]; // [sp+20h] [bp-2A8h]@4
  unsigned int dwExpRate[2]; // [sp+28h] [bp-2A0h]@4
  int nGrade[2]; // [sp+30h] [bp-298h]@4
  int *pnMaxPoint; // [sp+38h] [bp-290h]@4
  _MASTERY_PARAM *pData; // [sp+40h] [bp-288h]@4
  unsigned int *pdwAlter; // [sp+48h] [bp-280h]@4
  char *pszFileName; // [sp+50h] [bp-278h]@4
  char byLogType; // [sp+58h] [bp-270h]@4
  char *pszTitle; // [sp+60h] [bp-268h]@4
  int j; // [sp+70h] [bp-258h]@4
  int v22; // [sp+74h] [bp-254h]@29
  int k; // [sp+78h] [bp-250h]@29
  int v24; // [sp+7Ch] [bp-24Ch]@31
  unsigned int dwAlter; // [sp+80h] [bp-248h]@33
  int v26; // [sp+84h] [bp-244h]@37
  int v27; // [sp+88h] [bp-240h]@44
  unsigned int v28; // [sp+8Ch] [bp-23Ch]@45
  char *Str1; // [sp+90h] [bp-238h]@54
  _STORAGE_LIST::_db_con pItem; // [sp+A8h] [bp-220h]@55
  int nTableCode; // [sp+E4h] [bp-1E4h]@55
  _base_fld *v32; // [sp+E8h] [bp-1E0h]@59
  unsigned __int64 dwExp; // [sp+F0h] [bp-1D8h]@61
  char v34; // [sp+F8h] [bp-1D0h]@61
  char v35; // [sp+F9h] [bp-1CFh]@62
  char Dest; // [sp+110h] [bp-1B8h]@69
  char pszClause; // [sp+1B0h] [bp-118h]@70
  char *v38; // [sp+240h] [bp-88h]@4
  unsigned int *v39; // [sp+248h] [bp-80h]@4
  _MASTERY_PARAM *v40; // [sp+250h] [bp-78h]@4
  int *v41; // [sp+258h] [bp-70h]@4
  int v42; // [sp+260h] [bp-68h]@4
  long double v43; // [sp+268h] [bp-60h]@4
  _DWORD *v44; // [sp+270h] [bp-58h]@33
  _DWORD *v45; // [sp+278h] [bp-50h]@33
  char *v46; // [sp+280h] [bp-48h]@72
  char *v47; // [sp+288h] [bp-40h]@72
  unsigned int *v48; // [sp+290h] [bp-38h]@72
  _MASTERY_PARAM *v49; // [sp+298h] [bp-30h]@72
  int *v50; // [sp+2A0h] [bp-28h]@72
  int v51; // [sp+2A8h] [bp-20h]@72
  long double v52; // [sp+2B0h] [bp-18h]@72
  unsigned __int64 v53; // [sp+2B8h] [bp-10h]@4
  CPlayer *pOwner; // [sp+2D0h] [bp+8h]@1
  _class_fld *v55; // [sp+2D8h] [bp+10h]@1
  char v56; // [sp+2E0h] [bp+18h]@1

  v56 = bySelectRewardItem;
  v55 = pClassFld;
  pOwner = this;
  v4 = &v11;
  for ( i = 176i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v53 = (unsigned __int64)&v11 ^ _security_cookie;
  v38 = pOwner->m_szLvHistoryFileName;
  v39 = pOwner->m_Param.m_dwAlterMastery;
  v40 = &pOwner->m_pmMst;
  v41 = pOwner->m_nMaxPoint;
  v42 = pOwner->m_Param.m_byPvPGrade;
  CPlayerDB::GetExp(&pOwner->m_Param);
  v43 = a4;
  v6 = CPlayerDB::GetLevel(&pOwner->m_Param);
  v7 = pOwner->m_ObjID.m_wIndex;
  pszTitle = 0i64;
  byLogType = 0;
  pszFileName = v38;
  pdwAlter = v39;
  pData = v40;
  pnMaxPoint = v41;
  nGrade[0] = v42;
  dwExpRate[0] = pOwner->m_dwExpRate;
  v8 = v43;
  *(long double *)byReason = v43;
  CMgrAvatorLvHistory::update_mastery(
    &CPlayer::s_MgrLvHistory,
    v7,
    pOwner->m_byUserDgr,
    v6,
    v43,
    dwExpRate[0],
    v42,
    v41,
    v40,
    v39,
    v38,
    0,
    0i64);
  CPlayerDB::InitAlterMastery(&pOwner->m_Param);
  for ( j = 0; j < 2; ++j )
  {
    if ( v55->m_MasteryLim.m_nBnsMMastery[j] )
    {
      LOBYTE(nGrade[0]) = 1;
      *(_QWORD *)dwExpRate = "CPlayer::RewardChangeClass()---0";
      byReason[0] = 1;
      CPlayer::Emb_AlterStat(
        pOwner,
        0,
        j,
        v55->m_MasteryLim.m_nBnsMMastery[j],
        1,
        "CPlayer::RewardChangeClass()---0",
        1);
    }
  }
  if ( v55->m_MasteryLim.m_nBnsSMastery )
  {
    LOBYTE(nGrade[0]) = 1;
    *(_QWORD *)dwExpRate = "CPlayer::RewardChangeClass()---1";
    byReason[0] = 1;
    CPlayer::Emb_AlterStat(pOwner, 6, 0, v55->m_MasteryLim.m_nBnsSMastery, 1, "CPlayer::RewardChangeClass()---1", 1);
  }
  if ( v55->m_MasteryLim.m_nBnsDefMastery )
  {
    LOBYTE(nGrade[0]) = 1;
    *(_QWORD *)dwExpRate = "CPlayer::RewardChangeClass()---2";
    byReason[0] = 1;
    CPlayer::Emb_AlterStat(pOwner, 1, 0, v55->m_MasteryLim.m_nBnsDefMastery, 1, "CPlayer::RewardChangeClass()---2", 1);
  }
  if ( v55->m_MasteryLim.m_nBnsPryMastery )
  {
    LOBYTE(nGrade[0]) = 1;
    *(_QWORD *)dwExpRate = "CPlayer::RewardChangeClass()---3";
    byReason[0] = 1;
    CPlayer::Emb_AlterStat(pOwner, 2, 0, v55->m_MasteryLim.m_nBnsPryMastery, 1, "CPlayer::RewardChangeClass()---3", 1);
  }
  for ( j = 0; j < 3; ++j )
  {
    if ( v55->m_MasteryLim.m_nBnsMakeMastery[j] )
    {
      LOBYTE(nGrade[0]) = 1;
      *(_QWORD *)dwExpRate = "CPlayer::RewardChangeClass()---4";
      byReason[0] = 1;
      CPlayer::Emb_AlterStat(
        pOwner,
        5,
        j,
        v55->m_MasteryLim.m_nBnsMakeMastery[j],
        1,
        "CPlayer::RewardChangeClass()---4",
        1);
    }
  }
  for ( j = 0; j < 24; ++j )
  {
    if ( v55->m_MasteryLim.m_nBnsForceMastery[j] )
    {
      LOBYTE(nGrade[0]) = 1;
      *(_QWORD *)dwExpRate = "CPlayer::RewardChangeClass()---5";
      byReason[0] = 1;
      CPlayer::Emb_AlterStat(
        pOwner,
        4,
        j,
        v55->m_MasteryLim.m_nBnsForceMastery[j],
        1,
        "CPlayer::RewardChangeClass()---5",
        1);
    }
  }
  for ( j = 0; j < 8; ++j )
  {
    if ( v55->m_MasteryLim.m_nBnsSkillMastery[j] )
    {
      v22 = 0;
      for ( k = 0; k < CPlayer::s_SkillIndexPerMastery[49 * j]; ++k )
      {
        v24 = *(&CPlayer::s_SkillIndexPerMastery[49 * j + 1] + k);
        v22 += pOwner->m_pmMst.m_BaseCum.m_dwSkillCum[v24];
      }
      if ( v22 )
      {
        for ( k = 0; k < CPlayer::s_SkillIndexPerMastery[49 * j]; ++k )
        {
          v27 = *(&CPlayer::s_SkillIndexPerMastery[49 * j + 1] + k);
          if ( pOwner->m_pmMst.m_BaseCum.m_dwSkillCum[v27] )
          {
            *(float *)&v8 = (float)((float)((float)(signed int)pOwner->m_pmMst.m_BaseCum.m_dwSkillCum[v27] / (float)v22)
                                  * (float)v55->m_MasteryLim.m_nBnsSkillMastery[j])
                          + 1.0;
            v28 = (signed int)ffloor(*(float *)&v8);
            if ( (signed int)v28 > 0 )
              CPlayer::Emb_AlterStat(pOwner, 3, v27, v28, 1, "CPlayer::RewardChangeClass()---7", 1);
          }
        }
      }
      else
      {
        v44 = CPlayer::s_SkillIndexPerMastery;
        dwAlter = v55->m_MasteryLim.m_nBnsSkillMastery[j] / CPlayer::s_SkillIndexPerMastery[49 * j];
        v45 = CPlayer::s_SkillIndexPerMastery;
        if ( v55->m_MasteryLim.m_nBnsSkillMastery[j] % CPlayer::s_SkillIndexPerMastery[49 * j] )
          ++dwAlter;
        for ( k = 0; k < CPlayer::s_SkillIndexPerMastery[49 * j]; ++k )
        {
          v26 = *(&CPlayer::s_SkillIndexPerMastery[49 * j + 1] + k);
          if ( (signed int)dwAlter > 0 )
            CPlayer::Emb_AlterStat(pOwner, 3, v26, dwAlter, 1, "CPlayer::RewardChangeClass()---6", 1);
        }
      }
    }
  }
  for ( j = 0; j < 9; ++j )
  {
    if ( (unsigned __int8)v56 == 255 || (unsigned __int8)v56 == j )
    {
      Str1 = v55->m_DefaultItem[j].strDefaultItem;
      if ( strncmp(Str1, "-1", 2ui64) )
      {
        _STORAGE_LIST::_db_con::_db_con(&pItem);
        nTableCode = GetItemTableCode(Str1);
        if ( nTableCode == -1 )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "error bonus item of class change .., class: %s, error table code",
            v55->m_strCode,
            Str1);
          continue;
        }
        if ( nTableCode == 19 )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "error bonus item of class change .., class: %s, unit key",
            v55->m_strCode,
            Str1);
          continue;
        }
        v32 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + nTableCode, Str1);
        if ( !v32 )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "error bonus item of class change .., class: %s, nothing in table",
            v55->m_strCode,
            Str1);
          continue;
        }
        dwExp = *((_DWORD *)Str1 + 16);
        v34 = GetItemKindCode(nTableCode);
        if ( v34 )
        {
          if ( v34 != 1 )
            continue;
          HIDWORD(dwExp) = GetMaxParamFromExp(v32->m_dwIndex, (unsigned int)dwExp);
        }
        else
        {
          v35 = GetDefItemUpgSocketNum(nTableCode, v32->m_dwIndex);
          HIDWORD(dwExp) = GetBitAfterSetLimSocket(v35);
        }
        pItem.m_byTableCode = nTableCode;
        pItem.m_wItemIndex = v32->m_dwIndex;
        pItem.m_dwDur = (unsigned int)dwExp;
        pItem.m_dwLv = HIDWORD(dwExp);
        if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&pOwner->m_Param.m_dbInven.m_nListNum) == 255 )
        {
          LOBYTE(pdwAlter) = 0;
          pData = (_MASTERY_PARAM *)pOwner->m_fCurPos;
          LOWORD(pnMaxPoint) = pOwner->m_wMapLayerIndex;
          *(_QWORD *)nGrade = pOwner->m_pCurMap;
          CreateItemBox(
            &pItem,
            pOwner,
            0xFFFFFFFF,
            0,
            0i64,
            3,
            *(CMapData **)nGrade,
            (unsigned __int16)pnMaxPoint,
            pOwner->m_fCurPos,
            0);
          sprintf(&pszClause, "Class G (%s)", v55->m_strCode);
          CMgrAvatorItemHistory::reward_add_item(
            &CPlayer::s_MgrItemHistory,
            pOwner->m_ObjID.m_wIndex,
            &pszClause,
            &pItem,
            pOwner->m_szItemHistoryFileName);
        }
        else
        {
          pItem.m_wSerial = CPlayerDB::GetNewItemSerial(&pOwner->m_Param);
          if ( CPlayer::Emb_AddStorage(pOwner, 0, (_STORAGE_LIST::_storage_con *)&pItem.m_bLoad, 0, 1) )
          {
            CPlayer::SendMsg_RewardAddItem(pOwner, &pItem, 1);
            sprintf(&Dest, "Class (%s)", v55->m_strCode);
            CMgrAvatorItemHistory::reward_add_item(
              &CPlayer::s_MgrItemHistory,
              pOwner->m_ObjID.m_wIndex,
              &Dest,
              &pItem,
              pOwner->m_szItemHistoryFileName);
          }
          else
          {
            CMgrAvatorItemHistory::add_storage_fail(
              &CPlayer::s_MgrItemHistory,
              pOwner->m_ObjID.m_wIndex,
              &pItem,
              "RewardChangeClass - Emb_AddStorage() Fail",
              pOwner->m_szItemHistoryFileName);
          }
        }
      }
    }
  }
  v46 = v55->m_strCode;
  v47 = pOwner->m_szLvHistoryFileName;
  v48 = pOwner->m_Param.m_dwAlterMastery;
  v49 = &pOwner->m_pmMst;
  v50 = pOwner->m_nMaxPoint;
  v51 = pOwner->m_Param.m_byPvPGrade;
  CPlayerDB::GetExp(&pOwner->m_Param);
  v52 = v8;
  v9 = CPlayerDB::GetLevel(&pOwner->m_Param);
  v10 = pOwner->m_ObjID.m_wIndex;
  pszTitle = v46;
  byLogType = 3;
  pszFileName = v47;
  pdwAlter = v48;
  pData = v49;
  pnMaxPoint = v50;
  nGrade[0] = v51;
  dwExpRate[0] = pOwner->m_dwExpRate;
  *(long double *)byReason = v52;
  CMgrAvatorLvHistory::update_mastery(
    &CPlayer::s_MgrLvHistory,
    v10,
    pOwner->m_byUserDgr,
    v9,
    v52,
    dwExpRate[0],
    v51,
    v50,
    v49,
    v48,
    v47,
    3,
    v46);
  CPlayerDB::InitAlterMastery(&pOwner->m_Param);
  pOwner->m_dwUMWHLastTime = GetLoopTime();
}
