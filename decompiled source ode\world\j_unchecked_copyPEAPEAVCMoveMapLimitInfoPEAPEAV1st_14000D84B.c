/*
 * Function: j_??$unchecked_copy@PEAPEAVCMoveMapLimitInfo@@PEAPEAV1@@stdext@@YAPEAPEAVCMoveMapLimitInfo@@PEAPEAV1@00@Z
 * Address: 0x14000D84B
 */

CMoveMapLimitInfo **__fastcall stdext::unchecked_copy<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *>(CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, CMoveMapLimitInfo **_Dest)
{
  return stdext::unchecked_copy<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *>(_First, _Last, _Dest);
}
