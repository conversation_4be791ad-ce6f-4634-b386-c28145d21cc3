/*
 * Function: ?ct_report_position@@YA_NPEAVCPlayer@@@Z
 * Address: 0x14028FB00
 */

char __fastcall ct_report_position(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  signed int v4; // ecx@6
  signed int v5; // edx@6
  signed __int64 v6; // r8@6
  __int64 v7; // [sp+0h] [bp-48h]@1
  bool bFilter[4]; // [sp+20h] [bp-28h]@6
  char *pwszMessage; // [sp+28h] [bp-20h]@6
  CPlayer *v10; // [sp+50h] [bp+8h]@1

  v10 = pOne;
  v1 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v10 )
  {
    v4 = (signed int)ffloor(v10->m_fCurPos[1]);
    v5 = (signed int)ffloor(v10->m_fCurPos[0]);
    v6 = (signed __int64)v10->m_pCurMap->m_pMapSet->m_strCode;
    LODWORD(pwszMessage) = (signed int)ffloor(v10->m_fCurPos[2]);
    *(_DWORD *)bFilter = v4;
    sprintf(wszRespon, "map: %s x: %d, y: %d, z: %d", v6, (unsigned int)v5);
    CPlayer::SendData_ChatTrans(v10, 0, 0xFFFFFFFF, -1, 0, wszRespon, -1, 0i64);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
