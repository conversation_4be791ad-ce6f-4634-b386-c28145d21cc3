/*
 * Function: _CUnmannedTraderGroupDivisionVersionInfo::operator__::_1_::dtor$0
 * Address: 0x140399730
 */

void __fastcall CUnmannedTraderGroupDivisionVersionInfo::operator__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long>>::~_Vector_const_iterator<unsigned long,std::allocator<unsigned long>>(*(std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > **)(a2 + 56));
}
