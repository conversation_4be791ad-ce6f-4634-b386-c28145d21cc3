/*
 * Function: _std::vector_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64___::erase_::_1_::dtor$2
 * Address: 0x14037EF40
 */

void __fastcall std::vector_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64___::erase_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 40) & 1 )
  {
    *(_DWORD *)(a2 + 40) &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(*(std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > **)(a2 + 88));
  }
}
