/*
 * Function: ?GetLeftTime@CGuildBattleSchedule@GUILD_BATTLE@@QEAA_NAEAE00@Z
 * Address: 0x1403DA220
 */

char __fastcall GUILD_BATTLE::CGuildBattleSchedule::GetLeftTime(GUILD_BATTLE::CGuildBattleSchedule *this, char *byHour, char *byMin, char *bySec)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char v6; // al@5
  __int64 v7; // [sp+0h] [bp-88h]@1
  ATL::CTime result; // [sp+28h] [bp-60h]@4
  ATL::CTimeSpan span; // [sp+48h] [bp-40h]@4
  ATL::CTimeSpan v10; // [sp+68h] [bp-20h]@6
  GUILD_BATTLE::CGuildBattleSchedule *v11; // [sp+90h] [bp+8h]@1
  char *v12; // [sp+98h] [bp+10h]@1
  char *v13; // [sp+A0h] [bp+18h]@1
  char *v14; // [sp+A8h] [bp+20h]@1

  v14 = bySec;
  v13 = byMin;
  v12 = byHour;
  v11 = this;
  v4 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  ATL::CTime::GetTickCount(&result);
  ATL::CTime::operator-(&result, &span, v11->m_kBattleStartTime);
  if ( ATL::CTimeSpan::operator<=(&v11->m_kBattleTime, span) )
  {
    *v12 = 0;
    *v13 = 0;
    *v14 = 0;
    v6 = 1;
  }
  else
  {
    ATL::CTimeSpan::operator-(&v11->m_kBattleTime, &v10, span);
    *v12 = ATL::CTimeSpan::GetHours(&v10);
    *v13 = ATL::CTimeSpan::GetMinutes(&v10);
    *v14 = ATL::CTimeSpan::GetSeconds(&v10);
    v6 = 1;
  }
  return v6;
}
