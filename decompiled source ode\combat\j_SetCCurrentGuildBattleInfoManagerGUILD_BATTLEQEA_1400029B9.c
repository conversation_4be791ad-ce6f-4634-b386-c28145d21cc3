/*
 * Function: j_?Set@CCurrentGuildBattleInfoManager@GUILD_BATTLE@@QEAA_NIPEAVCNormalGuildBattle@2@@Z
 * Address: 0x1400029B9
 */

bool __fastcall GUILD_BATTLE::CCurrentGuildBattleInfoManager::Set(GUILD_BATTLE::CCurrentGuildBattleInfoManager *this, unsigned int uiMapID, GUILD_BATTLE::CNormalGuildBattle *pkBattle)
{
  return GUILD_BATTLE::CCurrentGuildBattleInfoManager::Set(this, uiMapID, pkBattle);
}
