/*
 * Function: j_??$?8U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@U01@@std@@YA_NAEBV?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@0@0@Z
 * Address: 0x14000CE7D
 */

bool __fastcall std::operator==<std::pair<int const,_TimeItem_fld const *>,std::pair<int const,_TimeItem_fld const *>>(std::allocator<std::pair<int const ,_TimeItem_fld const *> > *__formal, std::allocator<std::pair<int const ,_TimeItem_fld const *> > *a2)
{
  return std::operator==<std::pair<int const,_TimeItem_fld const *>,std::pair<int const,_TimeItem_fld const *>>(
           __formal,
           a2);
}
