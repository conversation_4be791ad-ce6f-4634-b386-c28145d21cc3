/*
 * Function: ??$_Umove@PEAPEAVCUnmannedTraderSortType@@@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@IEAAPEAPEAVCUnmannedTraderSortType@@PEAPEAV2@00@Z
 * Address: 0x140373BD0
 */

CUnmannedTraderSortType **__fastcall std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Umove<CUnmannedTraderSortType * *>(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, CUnmannedTraderSortType **_First, CUnmannedTraderSortType **_Last, CUnmannedTraderSortType **_Ptr)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return stdext::_Unchecked_uninitialized_move<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *,std::allocator<CUnmannedTraderSortType *>>(
           _First,
           _Last,
           _Ptr,
           &v8->_Alval);
}
