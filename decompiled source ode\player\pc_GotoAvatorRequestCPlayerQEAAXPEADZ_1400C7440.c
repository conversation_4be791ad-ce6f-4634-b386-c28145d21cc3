/*
 * Function: ?pc_GotoAvatorRequest@CPlayer@@QEAAXPEAD@Z
 * Address: 0x1400C7440
 */

void __fastcall CPlayer::pc_GotoAvatorRequest(CPlayer *this, char *pwszAvatorName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@15
  char v5; // al@21
  __int64 v6; // [sp+0h] [bp-88h]@1
  float *pfStartPos; // [sp+20h] [bp-68h]@20
  char v8; // [sp+30h] [bp-58h]@4
  CPlayer *v9; // [sp+38h] [bp-50h]@4
  int j; // [sp+40h] [bp-48h]@11
  CPlayer *v11; // [sp+48h] [bp-40h]@14
  float v12; // [sp+58h] [bp-30h]@20
  float v13; // [sp+5Ch] [bp-2Ch]@20
  float v14; // [sp+60h] [bp-28h]@20
  CPlayer *v15; // [sp+90h] [bp+8h]@1
  const char *Str2; // [sp+98h] [bp+10h]@1

  Str2 = pwszAvatorName;
  v15 = this;
  v2 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = 0;
  v9 = 0i64;
  if ( v15->m_pmTrd.bDTradeMode )
  {
    v8 = 6;
  }
  else if ( CGameObject::GetCurSecNum((CGameObject *)&v15->vfptr) == -1 || v15->m_bMapLoading )
  {
    v8 = 5;
  }
  else if ( v15->m_byStandType == 1 )
  {
    v8 = 8;
  }
  else
  {
    for ( j = 0; j < 2532; ++j )
    {
      v11 = &g_Player + j;
      if ( v11->m_bLive )
      {
        v4 = CPlayerDB::GetCharNameW(&v11->m_Param);
        if ( !strcmp_0(v4, Str2) )
        {
          v9 = v11;
          goto $RESULT_53;
        }
      }
    }
    v8 = 2;
  }
$RESULT_53:
  if ( !v8 )
  {
    v12 = v9->m_fCurPos[0];
    v14 = v9->m_fCurPos[2];
    v13 = v9->m_fCurPos[1];
    pfStartPos = &v12;
    CPlayer::OutOfMap(v15, v9->m_pCurMap, v9->m_wMapLayerIndex, 4, &v12);
  }
  v5 = CPlayerDB::GetMapCode(&v15->m_Param);
  CPlayer::SendMsg_GotoRecallResult(v15, v8, v5, &v12, 4);
}
