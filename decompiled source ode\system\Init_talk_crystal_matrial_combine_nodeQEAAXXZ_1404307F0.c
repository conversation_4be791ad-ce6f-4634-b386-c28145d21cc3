/*
 * Function: ?Init@_talk_crystal_matrial_combine_node@@QEAAXXZ
 * Address: 0x1404307F0
 */

void __fastcall _talk_crystal_matrial_combine_node::Init(_talk_crystal_matrial_combine_node *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _talk_crystal_matrial_combine_node *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->m_nMatrialCount = 0;
  v5->m_nMatrialOverlapCount = 0;
  v5->m_nMakeCount = 0;
  v5->m_bUse = 0;
  v5->m_nMixIndex = -1;
  v5->m_nNeedItemNum = 0;
  v5->m_byTableCode = -1;
  v5->m_wItemIndex = -1;
  v5->m_nRequiredSlotCount = 0;
  memset_0(&v5->m_MakeItem, 0, 0x32ui64);
  for ( j = 0; j < 24; ++j )
    _talk_crystal_matrial_combine_node::_matrialinfo::Init(&v5->m_matrialList[j]);
}
