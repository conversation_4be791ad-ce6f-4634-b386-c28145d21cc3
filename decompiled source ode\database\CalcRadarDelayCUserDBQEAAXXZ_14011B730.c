/*
 * Function: ?CalcRadarDelay@CUserDB@@QEAAXXZ
 * Address: 0x14011B730
 */

void __fastcall CUserDB::CalcRadarDelay(CUserDB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int v4; // [sp+20h] [bp-18h]@5
  unsigned int dwDelay; // [sp+24h] [bp-14h]@5
  CUserDB *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CRadarItemMgr::IsUse(&v6->m_RadarItemMgr) )
  {
    v4 = CRadarItemMgr::CalcDelay(&v6->m_RadarItemMgr);
    dwDelay = 0;
    if ( v4 )
    {
      dwDelay = v4 / 0x3E8;
      if ( v4 % 0x3E8 >= 0x1F4 )
        ++dwDelay;
    }
    CUserDB::SetRadarDelay(v6, dwDelay);
  }
}
