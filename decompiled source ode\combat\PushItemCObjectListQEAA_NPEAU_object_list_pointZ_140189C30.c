/*
 * Function: ?PushItem@CObjectList@@QEAA_NPEAU_object_list_point@@@Z
 * Address: 0x140189C30
 */

char __fastcall CObjectList::PushItem(CObjectList *this, _object_list_point *pItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v5; // [sp+0h] [bp-28h]@1
  CObjectList *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pItem->m_pNext || pItem->m_pPrev )
  {
    MyMessageBox("CObjectList", "PushItem error");
    result = 0;
  }
  else
  {
    pItem->m_pNext = &v6->m_Tail;
    pItem->m_pPrev = v6->m_Tail.m_pPrev;
    v6->m_Tail.m_pPrev->m_pNext = pItem;
    v6->m_Tail.m_pPrev = pItem;
    ++v6->m_nSize;
    result = 1;
  }
  return result;
}
