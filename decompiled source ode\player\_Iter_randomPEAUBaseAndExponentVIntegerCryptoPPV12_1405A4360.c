/*
 * Function: ??$_Iter_random@PEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@PEAU12@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@0@Z
 * Address: 0x1405A4360
 */

__int64 __fastcall std::_Iter_random<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *>(__int64 a1)
{
  return a1;
}
