/*
 * Function: ?SendMsg_RaceChat@CGoldenBoxItemMgr@@QEAAXPEAVCPlayer@@PEAD@Z
 * Address: 0x140414F60
 */

void __fastcall CGoldenBoxItemMgr::SendMsg_RaceChat(CGoldenBoxItemMgr *this, CPlayer *pOne, char *pwszChatData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@4
  int v6; // eax@9
  __int64 v7; // [sp+0h] [bp-1B8h]@1
  _announ_message_receipt_udp Dst; // [sp+40h] [bp-178h]@4
  char pbyType; // [sp+174h] [bp-44h]@4
  char v10; // [sp+175h] [bp-43h]@4
  int v11; // [sp+184h] [bp-34h]@4
  int j; // [sp+188h] [bp-30h]@4
  CPlayer *v13; // [sp+190h] [bp-28h]@7
  int v14; // [sp+1A0h] [bp-18h]@9
  unsigned __int64 v15; // [sp+1A8h] [bp-10h]@4
  CPlayer *v16; // [sp+1C8h] [bp+10h]@1
  const char *Str; // [sp+1D0h] [bp+18h]@1

  Str = pwszChatData;
  v16 = pOne;
  v3 = &v7;
  for ( i = 108i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = (unsigned __int64)&v7 ^ _security_cookie;
  _announ_message_receipt_udp::_announ_message_receipt_udp(&Dst);
  Dst.byMessageType = 4;
  Dst.bySenderRace = CPlayerDB::GetRaceCode(&v16->m_Param);
  Dst.dwSenderSerial = v16->m_dwObjSerial;
  v5 = CPlayerDB::GetCharNameW(&v16->m_Param);
  strcpy_0(Dst.wszSenderName, v5);
  Dst.bySize = strlen_0(Str);
  memcpy_0(Dst.wszChatData, Str, (unsigned __int8)Dst.bySize);
  Dst.wszChatData[(unsigned __int8)Dst.bySize] = 0;
  Dst.byPvpGrade = -1;
  pbyType = 2;
  v10 = 11;
  v11 = _announ_message_receipt_udp::size(&Dst);
  for ( j = 0; j < 2532; ++j )
  {
    v13 = &g_Player + j;
    if ( v13->m_bLive )
    {
      if ( v13->m_byUserDgr >= 2
        || (v14 = CPlayerDB::GetRaceCode(&v13->m_Param), v6 = CPlayerDB::GetRaceCode(&v16->m_Param), v14 == v6) )
      {
        CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, &Dst.byMessageType, v11);
      }
    }
  }
}
