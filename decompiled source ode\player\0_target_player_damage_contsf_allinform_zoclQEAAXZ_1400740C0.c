/*
 * Function: ??0_target_player_damage_contsf_allinform_zocl@@QEAA@XZ
 * Address: 0x1400740C0
 */

void __fastcall _target_player_damage_contsf_allinform_zocl::_target_player_damage_contsf_allinform_zocl(_target_player_damage_contsf_allinform_zocl *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _target_player_damage_contsf_allinform_zocl *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _target_player_damage_contsf_allinform_zocl::Init(v4);
}
