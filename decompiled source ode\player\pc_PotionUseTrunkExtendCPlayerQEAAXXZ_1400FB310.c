/*
 * Function: ?pc_PotionUseTrunkExtend@CPlayer@@QEAAXXZ
 * Address: 0x1400FB310
 */

void __fastcall CPlayer::pc_PotionUseTrunkExtend(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char v3; // al@10
  char v4; // al@10
  char v5; // al@11
  __int64 v6; // [sp+0h] [bp-38h]@1
  char v7; // [sp+20h] [bp-18h]@4
  char v8; // [sp+21h] [bp-17h]@4
  CPlayer *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v1 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = 0;
  v8 = 0;
  if ( (signed int)(unsigned __int8)CPlayerDB::GetTrunkSlotNum(&v9->m_Param) > 0 )
  {
    if ( (signed int)(unsigned __int8)CPlayerDB::GetExtTrunkSlotNum(&v9->m_Param) < 40 )
      v8 = 100 - CPlayerDB::GetTrunkSlotNum(&v9->m_Param);
    else
      v7 = 4;
  }
  else
  {
    v7 = 2;
  }
  if ( !v7 )
  {
    v9->m_Param.m_byTrunkSlotNum += v8;
    v9->m_Param.m_byExtTrunkSlotNum += 20;
    _STORAGE_LIST::SetUseListNum((_STORAGE_LIST *)&v9->m_Param.m_dbTrunk.m_nListNum, v9->m_Param.m_byTrunkSlotNum);
    _STORAGE_LIST::SetUseListNum((_STORAGE_LIST *)&v9->m_Param.m_dbExtTrunk.m_nListNum, v9->m_Param.m_byExtTrunkSlotNum);
    v3 = CPlayerDB::GetTrunkSlotNum(&v9->m_Param);
    CUserDB::Update_TrunkSlotNum(v9->m_pUserDB, v3);
    v4 = CPlayerDB::GetExtTrunkSlotNum(&v9->m_Param);
    CUserDB::Update_ExtTrunkSlotNum(v9->m_pUserDB, v4);
  }
  v5 = CPlayerDB::GetExtTrunkSlotNum(&v9->m_Param);
  CPlayer::SendMsg_ExtTrunkExtendResult(v9, v7, v5, v8);
}
