/*
 * Function: ?SetPatriarch<PERSON><PERSON><PERSON><PERSON>@TRC_AutoTrade@@QEAAXK@Z
 * Address: 0x1402D8120
 */

void __fastcall TRC_AutoTrade::SetPatriarchTaxMoney(TRC_AutoTrade *this, unsigned int dwTax)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v4; // rax@4
  int v5; // eax@4
  __int64 v6; // [sp+0h] [bp-78h]@1
  _qry_case_insert_patriarch_comm v7; // [sp+38h] [bp-40h]@4
  unsigned __int64 v8; // [sp+60h] [bp-18h]@4
  TRC_AutoTrade *v9; // [sp+80h] [bp+8h]@1
  signed int v10; // [sp+88h] [bp+10h]@1

  v10 = dwTax;
  v9 = this;
  v2 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = (unsigned __int64)&v6 ^ _security_cookie;
  _qry_case_insert_patriarch_comm::_qry_case_insert_patriarch_comm(&v7);
  v7.dwDalant = v10;
  v4 = CPvpUserAndGuildRankingSystem::Instance();
  v7.dwSerial = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v4, v9->m_byRace, 0);
  GetTodayStr(v7.szDepDate);
  v5 = _qry_case_insert_patriarch_comm::size(&v7);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -125, (char *)&v7, v5);
  ++v9->m_dwTrade;
  v9->m_dIncomeMoney = v9->m_dIncomeMoney + (double)v10;
}
