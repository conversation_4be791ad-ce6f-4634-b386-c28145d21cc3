/*
 * Function: ?Recv_ApexInform@CChiNetworkEX@@QEAAXKKPEAD@Z
 * Address: 0x1404103A0
 */

void __fastcall CChiNetworkEX::Recv_ApexInform(CChiNetworkEX *this, unsigned int dwSID, unsigned int dwRecvSize, char *pMsg)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CAsyncLogger *v6; // rax@5
  CAsyncLogger *v7; // rax@6
  __int64 v8; // [sp+0h] [bp-38h]@1
  CPlayer *v9; // [sp+20h] [bp-18h]@4
  unsigned int dwSerial; // [sp+48h] [bp+10h]@1
  unsigned int dwRecvSizea; // [sp+50h] [bp+18h]@1
  char *pMsga; // [sp+58h] [bp+20h]@1

  pMsga = pMsg;
  dwRecvSizea = dwRecvSize;
  dwSerial = dwSID;
  v4 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = GetPtrPlayerFromAccountSerial(&g_Player, 2532, dwSID);
  if ( v9 )
  {
    CPlayer::SendMsg_ApexInform(v9, dwRecvSizea, pMsga);
    v7 = CAsyncLogger::Instance();
    CAsyncLogger::FormatLog(v7, 12, "ApexMsg(D)..Recv_ApexInform - %d", dwSerial);
  }
  else
  {
    v6 = CAsyncLogger::Instance();
    CAsyncLogger::FormatLog(v6, 12, "ApexMsg(D)..Recv_ApexInform - %d - pOne NULL", dwSerial);
  }
}
