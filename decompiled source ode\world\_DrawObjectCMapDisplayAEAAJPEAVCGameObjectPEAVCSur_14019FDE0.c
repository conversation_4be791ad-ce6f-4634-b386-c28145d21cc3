/*
 * Function: ?_DrawObject@CMapDisplay@@AEAAJPEAVCGameObject@@PEAVCSurface@@@Z
 * Address: 0x14019FDE0
 */

__int64 __fastcall CMapDisplay::_DrawObject(CMapDisplay *this, CGameObject *pObj, CSurface *pSF)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@6
  IDirectDrawSurface7 *v6; // rax@14
  IDirectDrawSurface7 *v7; // rax@17
  __int64 v8; // [sp+0h] [bp-68h]@1
  __int64 v9; // [sp+20h] [bp-48h]@14
  int v10; // [sp+28h] [bp-40h]@14
  unsigned int v11; // [sp+30h] [bp-38h]@4
  IDirectDrawSurface7 *v12; // [sp+38h] [bp-30h]@14
  IUnknownVtbl *v13; // [sp+40h] [bp-28h]@14
  IDirectDrawSurface7 *v14; // [sp+48h] [bp-20h]@17
  IUnknownVtbl *v15; // [sp+50h] [bp-18h]@17
  CMapDisplay *v16; // [sp+70h] [bp+8h]@1
  CGameObject *v17; // [sp+78h] [bp+10h]@1
  CSurface *v18; // [sp+80h] [bp+18h]@1

  v18 = pSF;
  v17 = pObj;
  v16 = this;
  v3 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = 0;
  if ( pSF && pObj )
  {
    if ( CGameObject::GetCurSecNum(pObj) == -1 )
    {
      result = v11;
    }
    else
    {
      if ( v16->m_MapExtend.m_bExtendMode )
      {
        if ( v17->m_fAbsPos[0] <= (float)v16->m_MapExtend.m_ptStartMap.x
          || (float)v16->m_MapExtend.m_ptEndMap.x <= v17->m_fAbsPos[0]
          || v17->m_fAbsPos[2] <= (float)v16->m_MapExtend.m_ptStartMap.y
          || (float)v16->m_MapExtend.m_ptEndMap.y <= v17->m_fAbsPos[2] )
        {
          v17->m_nScreenPos[0] = -1;
          v17->m_nScreenPos[1] = -1;
        }
        else
        {
          CGameObject::CalcScrExtendPoint(v17, &v16->m_rcWnd, &v16->m_MapExtend.m_rcExtend);
          v12 = CSurface::GetDDrawSurface(v16->m_pSFMap);
          v6 = CSurface::GetDDrawSurface(v18);
          v13 = v12->vfptr;
          v10 = 1;
          v9 = 0i64;
          v11 = ((int (__fastcall *)(IDirectDrawSurface7 *, _QWORD, _QWORD, IDirectDrawSurface7 *))v13[2].AddRef)(
                  v12,
                  v17->m_nScreenPos[0],
                  v17->m_nScreenPos[1],
                  v6);
        }
      }
      else
      {
        CGameObject::CalcScrNormalPoint(v17, &v16->m_rcWnd);
        v14 = CSurface::GetDDrawSurface(v16->m_pSFMap);
        v7 = CSurface::GetDDrawSurface(v18);
        v15 = v14->vfptr;
        v10 = 1;
        v9 = 0i64;
        v11 = ((int (__fastcall *)(IDirectDrawSurface7 *, _QWORD, _QWORD, IDirectDrawSurface7 *))v15[2].AddRef)(
                v14,
                v17->m_nScreenPos[0],
                v17->m_nScreenPos[1],
                v7);
      }
      result = v11;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
