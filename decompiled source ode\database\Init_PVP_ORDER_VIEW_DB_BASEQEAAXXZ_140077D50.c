/*
 * Function: ?Init@_PVP_ORDER_VIEW_DB_BASE@@QEAAXXZ
 * Address: 0x140077D50
 */

void __fastcall _PVP_ORDER_VIEW_DB_BASE::Init(_PVP_ORDER_VIEW_DB_BASE *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _PVP_ORDER_VIEW_DB_BASE *Dst; // [sp+30h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(Dst, 0, 0x5Bui64);
}
