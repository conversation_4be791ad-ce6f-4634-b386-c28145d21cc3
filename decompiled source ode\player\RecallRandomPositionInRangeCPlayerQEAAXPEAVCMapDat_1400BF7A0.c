/*
 * Function: ?RecallRandomPositionInRange@CPlayer@@QEAAXPEAVCMapData@@GPEAMH@Z
 * Address: 0x1400BF7A0
 */

void __fastcall CPlayer::RecallRandomPositionInRange(CPlayer *this, CMapData *pIntoMap, unsigned __int16 wMapLayerIndex, float *pStartPos, int iRange)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char v7; // al@7
  __int64 v8; // [sp+0h] [bp-68h]@1
  float pNewPos; // [sp+38h] [bp-30h]@5
  int v10; // [sp+3Ch] [bp-2Ch]@5
  int v11; // [sp+40h] [bp-28h]@5
  CPlayer *v12; // [sp+70h] [bp+8h]@1
  CMapData *pIntoMapa; // [sp+78h] [bp+10h]@1
  unsigned __int16 v14; // [sp+80h] [bp+18h]@1
  float *pStdPos; // [sp+88h] [bp+20h]@1

  pStdPos = pStartPos;
  v14 = wMapLayerIndex;
  pIntoMapa = pIntoMap;
  v12 = this;
  v5 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( pStartPos )
  {
    CPlayer::pc_Resurrect(v12, 0);
    pNewPos = 0.0;
    v10 = 0;
    v11 = 0;
    if ( !CMapData::GetRandPosInRange(pIntoMapa, pStdPos, iRange, &pNewPos) )
      memcpy_0(&pNewPos, pStdPos, 0xCui64);
    CPlayer::OutOfMap(v12, pIntoMapa, v14, 4, &pNewPos);
    v7 = CPlayerDB::GetMapCode(&v12->m_Param);
    CPlayer::SendMsg_GotoRecallResult(v12, 0, v7, &pNewPos, 4);
  }
}
