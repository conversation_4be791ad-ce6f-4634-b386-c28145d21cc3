/*
 * Function: _CUnmannedTraderGroupVersionInfo::Init_::_1_::dtor$2
 * Address: 0x140397CA0
 */

void __fastcall CUnmannedTraderGroupVersionInfo::Init_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::~_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>((std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *)(a2 + 104));
}
