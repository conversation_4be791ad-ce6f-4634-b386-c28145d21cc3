/*
 * Function: j_?CancelPlayerRaceBuff@CRaceBuffManager@@QEAAHPEAVCPlayer@@W4RESULT_TYPE@CRaceBuffInfoByHolyQuestfGroup@@I@Z
 * Address: 0x1400032A6
 */

int __fastcall CRaceBuffManager::CancelPlayerRaceBuff(CRaceBuffManager *this, CPlayer *pkPlayer, CRaceBuffInfoByHolyQuestfGroup::RESULT_TYPE eReleaseType, unsigned int uiContinueCnt)
{
  return CRaceBuffManager::CancelPlayerRaceBuff(this, pkPlayer, eReleaseType, uiContinueCnt);
}
