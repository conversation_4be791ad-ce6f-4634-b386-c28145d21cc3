/*
 * Function: ?Complete_RenameChar_DB_Update@CPotionMgr@@QEAAXEPEAD@Z
 * Address: 0x14039F2A0
 */

void __fastcall CPotionMgr::Complete_RenameChar_DB_Update(CPotionMgr *this, char byRet, char *p)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@9
  char *v6; // rax@11
  unsigned int v7; // eax@16
  CPlayer::CashChangeStateFlag *v8; // rax@17
  __int64 v9; // [sp+0h] [bp-E8h]@1
  bool bUpdate; // [sp+20h] [bp-C8h]@10
  bool bSend; // [sp+28h] [bp-C0h]@10
  char v12; // [sp+30h] [bp-B8h]@4
  char *v13; // [sp+38h] [bp-B0h]@4
  CPlayer *v14; // [sp+40h] [bp-A8h]@4
  _STORAGE_LIST *v15; // [sp+48h] [bp-A0h]@7
  _STORAGE_LIST::_db_con *pUseItem; // [sp+50h] [bp-98h]@7
  _base_fld *v17; // [sp+58h] [bp-90h]@7
  unsigned __int8 v18; // [sp+60h] [bp-88h]@10
  char szMsg; // [sp+78h] [bp-70h]@17
  unsigned int v20; // [sp+7Ah] [bp-6Eh]@17
  char Dest; // [sp+7Eh] [bp-6Ah]@17
  char pbyType; // [sp+A4h] [bp-44h]@17
  char v23; // [sp+A5h] [bp-43h]@17
  _STORAGE_LIST::_storage_con *v24; // [sp+B8h] [bp-30h]@25
  CPlayer::CashChangeStateFlag v25; // [sp+C8h] [bp-20h]@17
  unsigned int dwActDelay; // [sp+CCh] [bp-1Ch]@16
  unsigned __int64 v27; // [sp+D0h] [bp-18h]@4
  CPotionMgr *v28; // [sp+F0h] [bp+8h]@1
  char v29; // [sp+F8h] [bp+10h]@1
  char *pInfo; // [sp+100h] [bp+18h]@1

  pInfo = p;
  v29 = byRet;
  v28 = this;
  v3 = &v9;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v27 = (unsigned __int64)&v9 ^ _security_cookie;
  v12 = 0;
  v13 = p;
  v14 = GetPtrPlayerFromSerial(&g_Player, 2532, *(_DWORD *)p);
  if ( v29 )
  {
    if ( v14 )
    {
      if ( v14->m_bOper )
      {
        v24 = (_STORAGE_LIST::_storage_con *)_STORAGE_LIST::GetPtrFromSerial(
                                               v14->m_Param.m_pStoragePtr[(unsigned __int8)v13[8]],
                                               *(_WORD *)(v13 + 9));
        if ( v24 )
          _STORAGE_LIST::_storage_con::lock(v24, 0);
      }
    }
    v12 = 26;
  }
  else
  {
    if ( v14 && v14->m_bOper )
    {
      v15 = v14->m_Param.m_pStoragePtr[(unsigned __int8)v13[8]];
      pUseItem = _STORAGE_LIST::GetPtrFromSerial(v15, *(_WORD *)(v13 + 9));
      v17 = 0i64;
      if ( !pUseItem )
      {
        _RENAME_POTION_USE_INFO::Init(&v14->m_ReNamePotionUseInfo);
        v6 = CPlayerDB::GetCharNameA(&v14->m_Param);
        CLogFile::Write(
          &stru_1799C8E78,
          "CPotionMgr::Complete_RenameChar_DB_Update : %u(%s) pUseItem NULL!",
          v14->m_dwObjSerial,
          v6);
        return;
      }
      v17 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 13, pUseItem->m_wItemIndex);
      if ( !v17 )
      {
        v5 = CPlayerDB::GetCharNameA(&v14->m_Param);
        CLogFile::Write(
          &stru_1799C8E78,
          "CPotionMgr::Complete_RenameChar_DB_Update : %u(%s) pPotionFld NULL!",
          v14->m_dwObjSerial,
          v5);
      }
      bSend = 0;
      bUpdate = 0;
      v18 = CPlayer::Emb_AlterDurPoint(v14, 0, pUseItem->m_byStorageIndex, -1, 0, 0);
      CPlayer::SendMsg_AdjustAmountInform(v14, 0, pUseItem->m_wSerial, v18);
      if ( v17 && *(_DWORD *)&v17[9].m_strCode[36] == 1 )
        CMgrAvatorItemHistory::cash_item_use(
          &CPlayer::s_MgrItemHistory,
          v14->m_ObjID.m_wIndex,
          pUseItem,
          v14->m_szItemHistoryFileName);
      if ( v17 )
      {
        dwActDelay = (signed int)ffloor(*(float *)&v17[6].m_strCode[0]);
        v7 = timeGetTime();
        CPlayer::SetPotionActDelay(v14, v17[6].m_strCode[4], v7 / 0x3E8, dwActDelay);
      }
      strcpy_0(v14->m_Param.m_dbChar.m_wszCharID, v13 + 12);
      strcpy_0(v14->m_pUserDB->m_wszAvatorName, v13 + 12);
      CPlayer::CashChangeStateFlag::CashChangeStateFlag(&v25, 1);
      CPlayer::UpdateVisualVer(v14, (CPlayer::CashChangeStateFlag)v8->0);
      v20 = v14->m_dwObjSerial;
      strcpy_0(&Dest, v14->m_pUserDB->m_wszAvatorName);
      pbyType = 3;
      v23 = 61;
      CGameObject::CircleReport((CGameObject *)&v14->vfptr, &pbyType, &szMsg, 23, 0);
      if ( CPartyPlayer::IsPartyMode(v14->m_pPartyMgr) )
        CPlayer::pc_PartyLeaveSelfReqeuest(v14);
      if ( v14->m_Param.m_pGuild )
        CPlayer::pc_GuildSelfLeaveRequest(v14);
      v14->m_NameChangeBuddyInfo.bNameChange = 1;
    }
    CPotionMgr::PushRenamePotionDBLog(v28, pInfo);
  }
  if ( v14 )
  {
    if ( v14->m_bOper )
    {
      _RENAME_POTION_USE_INFO::Init(&v14->m_ReNamePotionUseInfo);
      CPlayer::SendMsg_CharacterRenameCashResult(v14, 1, v29);
    }
  }
}
