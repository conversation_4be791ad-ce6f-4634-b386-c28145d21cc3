/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAVCMoveMapLimitRightInfo@@_KV1@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@stdext@@YAXPEAVCMoveMapLimitRightInfo@@_KAEBV1@AEAV?$allocator@VCMoveMapLimitRightInfo@@@std@@@Z
 * Address: 0x140005FBA
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CMoveMapLimitRightInfo *,unsigned __int64,CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(CMoveMapLimitRightInfo *_First, unsigned __int64 _Count, CMoveMapLimitRightInfo *_Val, std::allocator<CMoveMapLimitRightInfo> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CMoveMapLimitRightInfo *,unsigned __int64,CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(
    _First,
    _Count,
    _Val,
    _Al);
}
