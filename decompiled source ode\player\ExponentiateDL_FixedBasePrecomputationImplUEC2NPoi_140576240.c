/*
 * Function: ?Exponentiate@?$DL_FixedBasePrecomputationImpl@UEC2NPoint@CryptoPP@@@CryptoPP@@UEBA?AUEC2NPoint@2@AEBV?$DL_GroupPrecomputation@UEC2NPoint@CryptoPP@@@2@AEBVInteger@2@@Z
 * Address: 0x140576240
 */

__int64 __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::Exponentiate(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  signed __int64 v4; // rax@1
  __int64 v5; // rax@1
  __int64 v6; // rax@1
  __int64 v7; // rax@1
  __int64 v8; // rax@1
  char v10; // [sp+20h] [bp-E8h]@1
  char v11; // [sp+48h] [bp-C0h]@1
  char *v12; // [sp+60h] [bp-A8h]@1
  char v13; // [sp+68h] [bp-A0h]@1
  char *v14; // [sp+80h] [bp-88h]@1
  CryptoPP::EC2NPoint v15; // [sp+88h] [bp-80h]@1
  int v16; // [sp+C0h] [bp-48h]@1
  __int64 v17; // [sp+C8h] [bp-40h]@1
  __int64 v18; // [sp+D0h] [bp-38h]@1
  __int64 v19; // [sp+D8h] [bp-30h]@1
  __int64 v20; // [sp+E0h] [bp-28h]@1
  __int64 v21; // [sp+E8h] [bp-20h]@1
  __int64 v22; // [sp+F0h] [bp-18h]@1
  __int64 v23; // [sp+F8h] [bp-10h]@1
  __int64 v24; // [sp+110h] [bp+8h]@1
  __int64 v25; // [sp+118h] [bp+10h]@1
  __int64 v26; // [sp+120h] [bp+18h]@1
  __int64 v27; // [sp+128h] [bp+20h]@1

  v27 = a4;
  v26 = a3;
  v25 = a2;
  v24 = a1;
  v17 = -2i64;
  v16 = 0;
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>(&v10);
  v4 = std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::size(v24 + 112);
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::reserve(
    &v10,
    v4);
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::PrepareCascade(v24, v26, &v10, v27);
  v12 = &v11;
  v14 = &v13;
  LODWORD(v5) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::end(
                  &v10,
                  &v11);
  v18 = v5;
  v19 = v5;
  LODWORD(v6) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::begin(
                  &v10,
                  v14);
  v20 = v6;
  v21 = v6;
  LODWORD(v7) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v26 + 24i64))(v26);
  LODWORD(v8) = CryptoPP::GeneralCascadeMultiplication<CryptoPP::EC2NPoint,std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>>(
                  &v15,
                  v7,
                  v21,
                  v19);
  v22 = v8;
  v23 = v8;
  (*(void (__fastcall **)(__int64, __int64, __int64))(*(_QWORD *)v26 + 16i64))(v26, v25, v8);
  v16 |= 1u;
  CryptoPP::EC2NPoint::~EC2NPoint(&v15);
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>(&v10);
  return v25;
}
