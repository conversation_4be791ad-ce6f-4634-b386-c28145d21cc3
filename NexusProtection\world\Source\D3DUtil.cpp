/**
 * @file D3DUtil.cpp
 * @brief Implementation of DirectX utility functions
 * 
 * This file contains the implementation of DirectX utility functions for
 * matrix operations, particularly cube map view matrix generation.
 * 
 * @note Refactored from decompiled D3D utility functions
 * Original function: D3DUtil_GetCubeMapViewMatrix (Address: 0x14052B5D0)
 */

#include "D3DUtil.h"
#include <cmath>
#include <stdexcept>
#include <cstring>
#include <iostream>

// External function declarations (would be implemented elsewhere)
extern "C" {
    void Error(const char* message, const char* file);
}

// D3DXMATRIX implementation

D3DXMATRIX::D3DXMATRIX(float m00, float m01, float m02, float m03,
                       float m10, float m11, float m12, float m13,
                       float m20, float m21, float m22, float m23,
                       float m30, float m31, float m32, float m33)
{
    m[0][0] = m00; m[0][1] = m01; m[0][2] = m02; m[0][3] = m03;
    m[1][0] = m10; m[1][1] = m11; m[1][2] = m12; m[1][3] = m13;
    m[2][0] = m20; m[2][1] = m21; m[2][2] = m22; m[2][3] = m23;
    m[3][0] = m30; m[3][1] = m31; m[3][2] = m32; m[3][3] = m33;
}

// D3DUtil namespace implementation

namespace D3DUtil {

D3DXMATRIX GetCubeMapViewMatrix(uint32_t faceIndex)
{
    if (!IsValidCubeMapFace(faceIndex)) {
        throw std::invalid_argument("Invalid cube map face index");
    }

    // Eye position (origin)
    D3DXVECTOR3 eye(0.0f, 0.0f, 0.0f);
    
    // Target and up vectors based on face index
    D3DXVECTOR3 target, up;

    using namespace Constants;

    switch (faceIndex) {
        case 0: // POSITIVE_X - Right face
            target = D3DXVECTOR3(FLOAT_1_0, FLOAT_0_0, FLOAT_0_0);
            up = D3DXVECTOR3(FLOAT_0_0, FLOAT_1_0, FLOAT_0_0);
            break;

        case 1: // NEGATIVE_X - Left face  
            target = D3DXVECTOR3(FLOAT_N1_0, FLOAT_0_0, FLOAT_0_0);
            up = D3DXVECTOR3(FLOAT_0_0, FLOAT_1_0, FLOAT_0_0);
            break;

        case 2: // POSITIVE_Y - Top face
            target = D3DXVECTOR3(FLOAT_0_0, FLOAT_1_0, FLOAT_0_0);
            up = D3DXVECTOR3(FLOAT_0_0, FLOAT_0_0, FLOAT_N1_0);
            break;

        case 3: // NEGATIVE_Y - Bottom face
            target = D3DXVECTOR3(FLOAT_0_0, FLOAT_N1_0, FLOAT_0_0);
            up = D3DXVECTOR3(FLOAT_0_0, FLOAT_0_0, FLOAT_1_0);
            break;

        case 4: // POSITIVE_Z - Front face
            target = D3DXVECTOR3(FLOAT_0_0, FLOAT_0_0, FLOAT_1_0);
            up = D3DXVECTOR3(FLOAT_0_0, FLOAT_1_0, FLOAT_0_0);
            break;

        case 5: // NEGATIVE_Z - Back face
            target = D3DXVECTOR3(FLOAT_0_0, FLOAT_0_0, FLOAT_N1_0);
            up = D3DXVECTOR3(FLOAT_0_0, FLOAT_1_0, FLOAT_0_0);
            break;

        default:
            // Should never reach here due to validation above
            target = D3DXVECTOR3(FLOAT_1_0, FLOAT_0_0, FLOAT_0_0);
            up = D3DXVECTOR3(FLOAT_0_0, FLOAT_1_0, FLOAT_0_0);
            break;
    }

    return MatrixLookAtLH(eye, target, up);
}

D3DXMATRIX GetCubeMapViewMatrix(CubeMapFace face)
{
    return GetCubeMapViewMatrix(CubeMapFaceToIndex(face));
}

D3DXMATRIX MatrixLookAtLH(const D3DXVECTOR3& eye, 
                          const D3DXVECTOR3& at, 
                          const D3DXVECTOR3& up)
{
    D3DXMATRIX result;
    
    // Calculate the forward vector (z-axis)
    D3DXVECTOR3 zaxis;
    zaxis.x = at.x - eye.x;
    zaxis.y = at.y - eye.y;
    zaxis.z = at.z - eye.z;
    
    // Normalize z-axis
    float length = std::sqrt(zaxis.x * zaxis.x + zaxis.y * zaxis.y + zaxis.z * zaxis.z);
    if (length > 0.0f) {
        zaxis.x /= length;
        zaxis.y /= length;
        zaxis.z /= length;
    }
    
    // Calculate the right vector (x-axis) = up cross z
    D3DXVECTOR3 xaxis;
    xaxis.x = up.y * zaxis.z - up.z * zaxis.y;
    xaxis.y = up.z * zaxis.x - up.x * zaxis.z;
    xaxis.z = up.x * zaxis.y - up.y * zaxis.x;
    
    // Normalize x-axis
    length = std::sqrt(xaxis.x * xaxis.x + xaxis.y * xaxis.y + xaxis.z * xaxis.z);
    if (length > 0.0f) {
        xaxis.x /= length;
        xaxis.y /= length;
        xaxis.z /= length;
    }
    
    // Calculate the up vector (y-axis) = z cross x
    D3DXVECTOR3 yaxis;
    yaxis.x = zaxis.y * xaxis.z - zaxis.z * xaxis.y;
    yaxis.y = zaxis.z * xaxis.x - zaxis.x * xaxis.z;
    yaxis.z = zaxis.x * xaxis.y - zaxis.y * xaxis.x;
    
    // Build the view matrix
    result.m[0][0] = xaxis.x;
    result.m[1][0] = xaxis.y;
    result.m[2][0] = xaxis.z;
    result.m[3][0] = -(xaxis.x * eye.x + xaxis.y * eye.y + xaxis.z * eye.z);
    
    result.m[0][1] = yaxis.x;
    result.m[1][1] = yaxis.y;
    result.m[2][1] = yaxis.z;
    result.m[3][1] = -(yaxis.x * eye.x + yaxis.y * eye.y + yaxis.z * eye.z);
    
    result.m[0][2] = zaxis.x;
    result.m[1][2] = zaxis.y;
    result.m[2][2] = zaxis.z;
    result.m[3][2] = -(zaxis.x * eye.x + zaxis.y * eye.y + zaxis.z * eye.z);
    
    result.m[0][3] = 0.0f;
    result.m[1][3] = 0.0f;
    result.m[2][3] = 0.0f;
    result.m[3][3] = 1.0f;
    
    return result;
}

bool IsValidCubeMapFace(uint32_t faceIndex)
{
    return faceIndex <= 5;
}

uint32_t CubeMapFaceToIndex(CubeMapFace face)
{
    return static_cast<uint32_t>(face);
}

CubeMapFace IndexToCubeMapFace(uint32_t faceIndex)
{
    if (!IsValidCubeMapFace(faceIndex)) {
        throw std::invalid_argument("Invalid cube map face index");
    }
    return static_cast<CubeMapFace>(faceIndex);
}

const char* GetCubeMapFaceName(CubeMapFace face)
{
    switch (face) {
        case CubeMapFace::POSITIVE_X: return "POSITIVE_X";
        case CubeMapFace::NEGATIVE_X: return "NEGATIVE_X";
        case CubeMapFace::POSITIVE_Y: return "POSITIVE_Y";
        case CubeMapFace::NEGATIVE_Y: return "NEGATIVE_Y";
        case CubeMapFace::POSITIVE_Z: return "POSITIVE_Z";
        case CubeMapFace::NEGATIVE_Z: return "NEGATIVE_Z";
        default: return "UNKNOWN";
    }
}

uint32_t GetMipMapSkipSize(struct _DDSURFACEDESC2* surfaceDesc,
                           uint32_t targetMipLevel,
                           uint32_t maxWidth,
                           uint32_t maxHeight)
{
    if (!surfaceDesc) {
        throw std::invalid_argument("Surface description cannot be null");
    }

    // Validate minimum texture size (original check)
    if (maxWidth < 0x100 || maxHeight < 0x100) {
        // Original code called: Error("미립맵 텍스쳐 사이즈가 너무작아서 스킵할수가없다...", byte_140883769);
        throw std::invalid_argument("Texture size constraints are too small (minimum 256x256)");
    }

    uint32_t totalSkippedSize = 0;
    uint32_t currentMipLevel = 0;

    // Continue skipping mip levels until we reach target conditions
    while (currentMipLevel < targetMipLevel ||
           surfaceDesc->dwWidth > maxWidth ||
           surfaceDesc->dwHeight > maxHeight) {

        // Check if we've exceeded available mip levels
        if (currentMipLevel >= surfaceDesc->dwMipMapCount) {
            break;
        }

        // Add current level's size to skip total
        int32_t currentPitch = surfaceDesc->lPitch;
        uint32_t currentHeight = surfaceDesc->dwHeight;
        totalSkippedSize += currentPitch * currentHeight;

        // Check minimum size constraints
        if (currentHeight <= 4) {
            break;
        }

        // Reduce to next mip level
        surfaceDesc->dwHeight = currentHeight >> 1;  // Divide by 2

        uint32_t currentWidth = surfaceDesc->dwWidth;
        if (currentWidth <= 4) {
            break;
        }

        surfaceDesc->dwWidth = currentWidth >> 1;    // Divide by 2
        surfaceDesc->lPitch = currentPitch / 4;      // Reduce pitch accordingly

        ++currentMipLevel;
    }

    // Update remaining mip map count
    surfaceDesc->dwMipMapCount -= currentMipLevel;

    return totalSkippedSize;
}

bool ValidateMipMapParameters(uint32_t width, uint32_t height)
{
    // Check for power of 2 dimensions (typical requirement for mip maps)
    auto isPowerOfTwo = [](uint32_t value) {
        return value > 0 && (value & (value - 1)) == 0;
    };

    return isPowerOfTwo(width) && isPowerOfTwo(height) &&
           width >= 4 && height >= 4;
}

uint32_t CalculateMipMapLevels(uint32_t width, uint32_t height)
{
    if (width == 0 || height == 0) {
        return 0;
    }

    uint32_t levels = 1;
    uint32_t minDimension = std::min(width, height);

    while (minDimension > 1) {
        minDimension >>= 1;  // Divide by 2
        ++levels;
    }

    return levels;
}

} // namespace D3DUtil

// Legacy C-style interface implementation

extern "C" {

D3DXMATRIX* D3DUtil_GetCubeMapViewMatrix(D3DXMATRIX* retstr, int faceIndex)
{
    if (!retstr) {
        return nullptr;
    }
    
    try {
        *retstr = D3DUtil::GetCubeMapViewMatrix(static_cast<uint32_t>(faceIndex));
        return retstr;
    } catch (...) {
        // Return identity matrix on error
        std::memset(retstr, 0, sizeof(D3DXMATRIX));
        retstr->m[0][0] = retstr->m[1][1] = retstr->m[2][2] = retstr->m[3][3] = 1.0f;
        return retstr;
    }
}

D3DXMATRIX* D3DXMatrixLookAtLH_0(D3DXMATRIX* result, 
                                 const int* eye, 
                                 const char* at, 
                                 const float* up)
{
    if (!result || !eye || !at || !up) {
        return nullptr;
    }
    
    try {
        // Convert parameters to D3DXVECTOR3
        D3DXVECTOR3 eyeVec(static_cast<float>(eye[0]), 
                           static_cast<float>(eye[1]), 
                           static_cast<float>(eye[2]));
        
        D3DXVECTOR3 atVec(static_cast<float>(reinterpret_cast<const int*>(at)[0]),
                          static_cast<float>(reinterpret_cast<const int*>(at)[1]),
                          static_cast<float>(reinterpret_cast<const int*>(at)[2]));
        
        D3DXVECTOR3 upVec(up[0], up[1], up[2]);
        
        *result = D3DUtil::MatrixLookAtLH(eyeVec, atVec, upVec);
        return result;
    } catch (...) {
        // Return identity matrix on error
        std::memset(result, 0, sizeof(D3DXMATRIX));
        result->m[0][0] = result->m[1][1] = result->m[2][2] = result->m[3][3] = 1.0f;
        return result;
    }
}

int64_t GetMipMapSkipSize(struct _DDSURFACEDESC2* surfaceDesc,
                          unsigned int targetMipLevel,
                          unsigned int maxWidth,
                          unsigned int maxHeight)
{
    if (!surfaceDesc) {
        return -1;  // Error indicator
    }

    try {
        return static_cast<int64_t>(D3DUtil::GetMipMapSkipSize(surfaceDesc,
                                                               targetMipLevel,
                                                               maxWidth,
                                                               maxHeight));
    } catch (...) {
        return -1;  // Error indicator
    }
}

} // extern "C"
