/*
 * Function: j_??$_Uninit_copy@PEAHPEAHV?$allocator@H@std@@@std@@YAPEAHPEAH00AEAV?$allocator@H@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140012CC4
 */

int *__fastcall std::_Uninit_copy<int *,int *,std::allocator<int>>(int *_First, int *_Last, int *_Dest, std::allocator<int> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<int *,int *,std::allocator<int>>(_First, _Last, _Dest, __formal, a5, a6);
}
