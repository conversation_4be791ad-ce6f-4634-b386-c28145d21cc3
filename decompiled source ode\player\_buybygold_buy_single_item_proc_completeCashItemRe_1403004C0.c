/*
 * Function: ?_buybygold_buy_single_item_proc_complete@CashItemRemoteStore@@AEAAXPEAVCPlayer@@PEAU__item@_request_csi_buy_clzo@@PEAU_param_cashitem_dblog@@PEAU_CashShop_fld@@AEAU_db_con@_STORAGE_LIST@@AEAU_result_csi_buy_zocl@@KKPEA_N6@Z
 * Address: 0x1403004C0
 */

void __fastcall CashItemRemoteStore::_buybygold_buy_single_item_proc_complete(CashItemRemoteStore *this, CPlayer *pOne, _request_csi_buy_clzo::__item *pSrc, _param_cashitem_dblog *pSheet, _CashShop_fld *pCsFld, _STORAGE_LIST::_db_con *GiveItem, _result_csi_buy_zocl *Send, unsigned int dwPrice, unsigned int dwDiscountRate, bool *bCouponUseCheck, bool *bCouponUse)
{
  __int64 *v11; // rdi@1
  signed __int64 i; // rcx@1
  int v13; // eax@4
  int v14; // ecx@4
  __int64 v15; // [sp+0h] [bp-78h]@1
  int nDis; // [sp+20h] [bp-58h]@4
  int nNum; // [sp+28h] [bp-50h]@4
  int nBuyPrice; // [sp+30h] [bp-48h]@4
  int nAmount; // [sp+38h] [bp-40h]@4
  char *pFileName; // [sp+40h] [bp-38h]@4
  unsigned __int64 lnUID; // [sp+48h] [bp-30h]@4
  char byEventType; // [sp+50h] [bp-28h]@4
  int j; // [sp+60h] [bp-18h]@4
  char *v24; // [sp+68h] [bp-10h]@4
  CashItemRemoteStore *v25; // [sp+80h] [bp+8h]@1
  CPlayer *v26; // [sp+88h] [bp+10h]@1
  _request_csi_buy_clzo::__item *v27; // [sp+90h] [bp+18h]@1
  _param_cashitem_dblog *pSheeta; // [sp+98h] [bp+20h]@1

  pSheeta = pSheet;
  v27 = pSrc;
  v26 = pOne;
  v25 = this;
  v11 = &v15;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v11 = -858993460;
    v11 = (__int64 *)((char *)v11 + 4);
  }
  CashItemRemoteStore::_buybygold_buy_single_item_setsenddata(v25, GiveItem, Send);
  v24 = v26->m_szItemHistoryFileName;
  v13 = CPlayerDB::GetGold(&v26->m_Param);
  v14 = v27->byOverlapNum;
  byEventType = v27->byEventType;
  lnUID = GiveItem->m_lnUID;
  pFileName = v24;
  nAmount = v13;
  nBuyPrice = pCsFld->m_nCsPrice;
  nNum = v14;
  nDis = pCsFld->m_nCsDiscount;
  CMgrAvatorItemHistory::buy_to_inven_cashitem(
    &CPlayer::s_MgrItemHistory,
    GiveItem->m_byTableCode,
    GiveItem->m_wItemIndex,
    pCsFld->m_nCsPrice,
    nDis,
    v14,
    nBuyPrice,
    v13,
    v24,
    lnUID,
    byEventType);
  CashItemRemoteStore::_buybygold_buy_single_item_setbuydblog(v25, pSheeta, GiveItem, dwPrice, dwDiscountRate);
  for ( j = 0; j < 3; ++j )
  {
    if ( bCouponUseCheck[j] )
    {
      *bCouponUse = 1;
      return;
    }
  }
}
