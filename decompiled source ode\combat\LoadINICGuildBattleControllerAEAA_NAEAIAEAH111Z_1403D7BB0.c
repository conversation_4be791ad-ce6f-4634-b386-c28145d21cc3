/*
 * Function: ?<PERSON>adIN<PERSON>@CGuildBattleController@@AEAA_NAEAIAEAH111@Z
 * Address: 0x1403D7BB0
 */

char __fastcall CGuildBattleController::LoadINI(CGuildBattleController *this, unsigned int *uiMapCnt, int *iToday, int *iTodayDayID, int *iTomorrow, int *iTomorrowDayID)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v8; // rax@4
  __int64 v10; // [sp+0h] [bp-48h]@1
  int v11; // [sp+20h] [bp-28h]@4
  int v12; // [sp+28h] [bp-20h]@4
  unsigned int v13; // [sp+30h] [bp-18h]@4
  unsigned int *v14; // [sp+58h] [bp+10h]@1
  int *v15; // [sp+60h] [bp+18h]@1
  int *v16; // [sp+68h] [bp+20h]@1

  v16 = iTodayDayID;
  v15 = iToday;
  v14 = uiMapCnt;
  v6 = &v10;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  *iToday = GetPrivateProfileIntA("GuildBattle", "Today", -1, "..\\SystemSave\\ServerState.ini");
  *v16 = GetPrivateProfileIntA("GuildBattle", "TodayDayID", 0, "..\\SystemSave\\ServerState.ini");
  *iTomorrow = GetPrivateProfileIntA("GuildBattle", "Tomorrow", -1, "..\\SystemSave\\ServerState.ini");
  *iTomorrowDayID = GetPrivateProfileIntA("GuildBattle", "TomorrowDayID", 1, "..\\SystemSave\\ServerState.ini");
  *v14 = GetPrivateProfileIntA("GuildBattle", "MapCnt", 0, "..\\SystemSave\\ServerState.ini");
  v8 = GUILD_BATTLE::CGuildBattleLogger::Instance();
  v13 = *v14;
  v12 = *iTomorrowDayID;
  v11 = *iTomorrow;
  GUILD_BATTLE::CGuildBattleLogger::Log(
    v8,
    "CGuildBattleController::LoadINI(...) : iToday : %d, iTodayDayID : %d, iTommorow : %d, iTomorrowDayID : %d, uiMapCnt : %u Load!",
    (unsigned int)*v15,
    (unsigned int)*v16);
  return 1;
}
