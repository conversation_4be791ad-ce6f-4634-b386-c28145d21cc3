/*
 * Function: ??_GCGuildBattleScheduleManager@GUILD_BATTLE@@IEAAPEAXI@Z
 * Address: 0x1403DECF0
 */

GUILD_BATTLE::CGuildBattleScheduleManager *__fastcall GUILD_BATTLE::CGuildBattleScheduleManager::`scalar deleting destructor'(GUILD_BATTLE::CGuildBattleScheduleManager *this, int a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleScheduleManager *v6; // [sp+30h] [bp+8h]@1
  int v7; // [sp+38h] [bp+10h]@1

  v7 = a2;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  GUILD_BATTLE::CGuildBattleScheduleManager::~CGuildBattleScheduleManager(v6);
  if ( v7 & 1 )
    operator delete(v6);
  return v6;
}
