/*
 * Function: ?GetGenAttackProb@CMonster@@UEAAHPEAVCCharacter@@H_N@Z
 * Address: 0x140146DA0
 */

__int64 __fastcall CMonster::GetGenAttackProb(CMonster *this, CCharacter *pDst, int nPart, bool bBackAttack)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@5
  __int64 result; // rax@11
  __int64 v8; // [sp+0h] [bp-38h]@1
  float v9; // [sp+20h] [bp-18h]@5
  float v10; // [sp+24h] [bp-14h]@5
  float v11; // [sp+28h] [bp-10h]@5
  int v12; // [sp+2Ch] [bp-Ch]@5
  CMonster *v13; // [sp+40h] [bp+8h]@1
  bool v14; // [sp+58h] [bp+20h]@1

  v14 = bBackAttack;
  v13 = this;
  v4 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( CMonster::IsValidPlayer(v13) )
  {
    v9 = v13->m_pMonRec->m_fAttSklUnit;
    v10 = (float)((int (__fastcall *)(CCharacter *))v13->m_pTargetChar->vfptr->GetLevel)(v13->m_pTargetChar);
    v11 = (float)((int (__fastcall *)(CCharacter *, _QWORD))v13->m_pTargetChar->vfptr->GetDefSkill)(
                   v13->m_pTargetChar,
                   v14);
    v12 = (signed int)floor((float)(v9 - (float)(v10 + v11)) / 4.0 + 95.0);
    v6 = ((int (__fastcall *)(CCharacter *))v13->m_pTargetChar->vfptr->GetAvoidRate)(v13->m_pTargetChar);
    v12 -= v6;
    if ( v13->m_pTargetChar->m_bMove )
      v12 = (signed int)ffloor((float)v12 * 0.5);
    if ( v12 >= 5 )
    {
      if ( v12 > 95 )
        v12 = 95;
    }
    else
    {
      v12 = 5;
    }
    result = (unsigned int)v12;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
