/*
 * Function: ?GetLeftSec@CUnmannedTraderRegistItemInfo@@QEBAKXZ
 * Address: 0x14035FCA0
 */

__int64 __fastcall CUnmannedTraderRegistItemInfo::GetLeftSec(CUnmannedTraderRegistItemInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-58h]@1
  __int64 _Time; // [sp+28h] [bp-30h]@4
  __int64 v6; // [sp+38h] [bp-20h]@4
  __int64 v7; // [sp+40h] [bp-18h]@5
  CUnmannedTraderRegistItemInfo *v8; // [sp+60h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _Time = 0i64;
  time_18(&_Time);
  v6 = v8->m_tStartTime + 3600 * v8->m_bySellTurm;
  if ( v6 <= _Time )
    v7 = 0i64;
  else
    v7 = v6 - _Time;
  return (unsigned int)v7;
}
