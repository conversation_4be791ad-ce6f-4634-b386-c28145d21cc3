/*
 * Function: ?Attack@CNuc<PERSON>rBomb@@QEAAXHH@Z
 * Address: 0x14013C4F0
 */

void __fastcall CNuclearBomb::Attack(CNuclearBomb *this, int StartNum, int Obj_num)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@8
  __int64 v6; // [sp+0h] [bp-68h]@1
  char v7; // [sp+20h] [bp-48h]@8
  int v8; // [sp+28h] [bp-40h]@8
  int v9; // [sp+30h] [bp-38h]@8
  char v10; // [sp+38h] [bp-30h]@8
  int v11; // [sp+40h] [bp-28h]@4
  int j; // [sp+44h] [bp-24h]@4
  __int64 *v13; // [sp+48h] [bp-20h]@8
  __int64 v14; // [sp+50h] [bp-18h]@8
  CNuclearBomb *v15; // [sp+70h] [bp+8h]@1
  int v16; // [sp+80h] [bp+18h]@1

  v16 = Obj_num;
  v15 = this;
  v3 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = 30 * StartNum;
  for ( j = 30 * StartNum; j < v16 + v11; ++j )
  {
    if ( v15->m_DamList[j].m_pChar->m_bLive )
    {
      if ( v15->m_DamList[j].m_pChar->m_dwObjSerial == v15->m_DamList[j].m_dwDamCharSerial )
      {
        v5 = ((int (__fastcall *)(CNuclearBomb *))v15->vfptr->GetLevel)(v15);
        v13 = (__int64 *)v15->m_DamList[j].m_pChar;
        v14 = *v13;
        v10 = 1;
        v9 = 0;
        v8 = -1;
        v7 = 0;
        (*(void (__fastcall **)(__int64 *, _QWORD, CNuclearBomb *, _QWORD))(v14 + 184))(
          v13,
          v15->m_DamList[j].m_nDamage,
          v15,
          (unsigned int)v5);
      }
    }
  }
}
