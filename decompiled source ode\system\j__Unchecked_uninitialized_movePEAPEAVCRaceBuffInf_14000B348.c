/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAPEAVCRaceBuffInfoByHolyQuest@@PEAPEAV1@V?$allocator@PEAVCRaceBuffInfoByHolyQuest@@@std@@@stdext@@YAPEAPEAVCRaceBuffInfoByHolyQuest@@PEAPEAV1@00AEAV?$allocator@PEAVCRaceBuffInfoByHolyQuest@@@std@@@Z
 * Address: 0x14000B348
 */

CRaceBuffInfoByHolyQuest **__fastcall stdext::_Unchecked_uninitialized_move<CRaceBuffInfoByHolyQuest * *,CRaceBuffInfoByHolyQuest * *,std::allocator<CRaceBuffInfoByHolyQuest *>>(CRaceBuffInfoByHolyQuest **_First, CRaceBuffInfoByHolyQuest **_Last, CRaceBuffInfoByHolyQuest **_Dest, std::allocator<CRaceBuffInfoByHolyQuest *> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<CRaceBuffInfoByHolyQuest * *,CRaceBuffInfoByHolyQuest * *,std::allocator<CRaceBuffInfoByHolyQuest *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
