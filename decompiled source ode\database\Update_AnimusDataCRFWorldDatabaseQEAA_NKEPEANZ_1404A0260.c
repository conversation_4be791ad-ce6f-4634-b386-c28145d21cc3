/*
 * Function: ?Update_AnimusData@CRFWorldDatabase@@QEAA_NKEPEAN@Z
 * Address: 0x1404A0260
 */

bool __fastcall CRFWorldDatabase::Update_AnimusData(CRFWorldDatabase *this, unsigned int dwSerial, char byRace, long double *pAnimusData)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v7; // [sp+0h] [bp-478h]@1
  int v8; // [sp+20h] [bp-458h]@6
  __int64 v9; // [sp+28h] [bp-450h]@6
  unsigned int v10; // [sp+30h] [bp-448h]@6
  char Dest; // [sp+50h] [bp-428h]@6
  unsigned __int64 v12; // [sp+460h] [bp-18h]@4
  CRFWorldDatabase *v13; // [sp+480h] [bp+8h]@1

  v13 = this;
  v4 = &v7;
  for ( i = 284i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( (signed int)(unsigned __int8)byRace <= 2 )
  {
    v10 = dwSerial;
    v9 = *((_QWORD *)pAnimusData + 1);
    v8 = 2 * (unsigned __int8)byRace + 1;
    sprintf(
      &Dest,
      "update [dbo].[tbl_animusdata] set Data%u = %.0f, Data%u = %.0f where Serial = %u",
      2 * (unsigned int)(unsigned __int8)byRace,
      *(_QWORD *)pAnimusData);
    result = CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v13->vfptr, &Dest, 1);
  }
  else
  {
    result = 0;
  }
  return result;
}
