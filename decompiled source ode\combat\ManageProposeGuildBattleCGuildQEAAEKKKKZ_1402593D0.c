/*
 * Function: ?ManageProposeGuildBattle@CGuild@@QEAAEKKKK@Z
 * Address: 0x1402593D0
 */

char __fastcall CGuild::ManageProposeGuildBattle(CGuild *this, unsigned int dwDestGuild, unsigned int dwStartTimeIdx, unsigned int dwMemberCountIdx, unsigned int dwMapIdx)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  CGuildBattleController *v8; // rax@6
  __int64 v9; // [sp+0h] [bp-48h]@1
  unsigned int dwNumber; // [sp+20h] [bp-28h]@9
  char v11; // [sp+30h] [bp-18h]@6
  CGuild *v12; // [sp+38h] [bp-10h]@7
  CGuild *pSrcGuild; // [sp+50h] [bp+8h]@1
  unsigned int dwDestGuilda; // [sp+58h] [bp+10h]@1
  unsigned int dwStartTime; // [sp+60h] [bp+18h]@1
  unsigned int dwMemberCntInx; // [sp+68h] [bp+20h]@1

  dwMemberCntInx = dwMemberCountIdx;
  dwStartTime = dwStartTimeIdx;
  dwDestGuilda = dwDestGuild;
  pSrcGuild = this;
  v5 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( pSrcGuild->m_dTotalGold < 5000.0 )
    return 116;
  v8 = CGuildBattleController::Instance();
  v11 = CGuildBattleController::IsAvailableSuggest(v8, pSrcGuild, dwDestGuilda, dwStartTime, dwMemberCntInx, dwMapIdx);
  if ( !v11 )
  {
    v12 = GetGuildDataFromSerial(g_Guild, 500, dwDestGuilda);
    if ( !v12 )
      return 111;
    dwNumber = dwMapIdx;
    v11 = CGuild::CheckGuildBattleSuggestRequestToDestGuild(
            v12,
            pSrcGuild->m_dwSerial,
            dwStartTime,
            dwMemberCntInx,
            dwMapIdx);
    if ( !v11 )
    {
      pSrcGuild->m_GuildBattleSugestMatter.pkSrc = pSrcGuild;
      pSrcGuild->m_GuildBattleSugestMatter.pkDest = v12;
      pSrcGuild->m_GuildBattleSugestMatter.eState = 1;
      pSrcGuild->m_GuildBattleSugestMatter.dwStartTime = dwStartTime;
      pSrcGuild->m_GuildBattleSugestMatter.dwNumber = dwMemberCntInx;
      pSrcGuild->m_GuildBattleSugestMatter.dwMapIdx = dwMapIdx;
      CGuild::PushDQSSourceGuildOutputGuildBattleCost(pSrcGuild);
    }
  }
  return v11;
}
