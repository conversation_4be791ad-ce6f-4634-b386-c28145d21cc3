/*
 * Function: ?ct_goto_char@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140295B30
 */

bool __fastcall ct_goto_char(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  char *v4; // rax@10
  __int64 v5; // [sp+0h] [bp-38h]@1
  CUserDB *v6; // [sp+20h] [bp-18h]@7
  CPlayer *v7; // [sp+28h] [bp-10h]@8
  CPlayer *v8; // [sp+40h] [bp+8h]@1

  v8 = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v8 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      v6 = SearchAvatorWithName(g_UserDB, 2532, s_pwszDstCheat[0]);
      if ( v6 )
      {
        v7 = &g_Player + v6->m_idWorld.wIndex;
        if ( v7->m_bLive )
        {
          v4 = CPlayerDB::GetCharNameW(&v8->m_Param);
          result = CPlayer::mgr_recall_player(v7, v4);
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
