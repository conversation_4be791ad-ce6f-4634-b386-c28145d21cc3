/*
 * Function: j_?GetBase@?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@CryptoPP@@UEBAAEBUECPPoint@2@AEBV?$DL_GroupPrecomputation@UECPPoint@CryptoPP@@@2@@Z
 * Address: 0x14001034D
 */

CryptoPP::ECPPoint *__fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::GetBase(CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint> *this, CryptoPP::DL_GroupPrecomputation<CryptoPP::ECPPoint> *group)
{
  return CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::GetBase(this, group);
}
