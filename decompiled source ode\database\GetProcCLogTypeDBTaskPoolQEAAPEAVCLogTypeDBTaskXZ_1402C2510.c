/*
 * Function: ?GetProc@CLogTypeDBTaskPool@@QEAAPEAVCLogTypeDBTask@@XZ
 * Address: 0x1402C2510
 */

CLogTypeDBTask *__fastcall CLogTypeDBTaskPool::GetProc(CLogTypeDBTaskPool *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CLogTypeDBTask *result; // rax@5
  unsigned __int64 v4; // rax@8
  __int64 v5; // [sp+0h] [bp-48h]@1
  unsigned int pdwOutIndex; // [sp+24h] [bp-24h]@6
  CLogTypeDBTaskPool *v7; // [sp+50h] [bp+8h]@1

  v7 = this;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7->m_bInit )
  {
    pdwOutIndex = -1;
    if ( CNetIndexList::PopNode_Front(&v7->m_kInxProc, &pdwOutIndex) )
    {
      v4 = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::size(&v7->m_vecDat);
      if ( v4 > pdwOutIndex )
        result = *std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator[](&v7->m_vecDat, pdwOutIndex);
      else
        result = 0i64;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
