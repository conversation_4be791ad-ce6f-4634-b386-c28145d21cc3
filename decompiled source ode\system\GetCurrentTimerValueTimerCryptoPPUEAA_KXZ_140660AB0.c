/*
 * Function: ?GetCurrentTimerValue@Timer@CryptoPP@@UEAA_KXZ
 * Address: 0x140660AB0
 */

__int64 __fastcall CryptoPP::Timer::GetCurrentTimerValue(CryptoPP::Timer *this)
{
  unsigned int v1; // eax@2
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v2; // rax@2
  LARGE_INTEGER PerformanceCount; // [sp+20h] [bp-E8h]@1
  CryptoPP::Exception v5; // [sp+28h] [bp-E0h]@2
  char v6; // [sp+78h] [bp-90h]@2
  char v7; // [sp+A8h] [bp-60h]@2
  __int64 v8; // [sp+D8h] [bp-30h]@1
  __int64 v9; // [sp+E0h] [bp-28h]@2
  __int64 v10; // [sp+E8h] [bp-20h]@2
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v11; // [sp+F0h] [bp-18h]@2
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *s; // [sp+F8h] [bp-10h]@2

  v8 = -2i64;
  if ( !QueryPerformanceCounter(&PerformanceCount) )
  {
    v1 = GetLastError();
    v9 = CryptoPP::IntToString<unsigned long>((__int64)&v6, v1, 0xAu);
    v10 = v9;
    LODWORD(v2) = std::operator+<char,std::char_traits<char>,std::allocator<char>>(
                    &v7,
                    "Timer: QueryPerformanceCounter failed with error ",
                    v9);
    v11 = v2;
    s = v2;
    CryptoPP::Exception::Exception(&v5, OTHER_ERROR, v2);
    CxxThrowException_0((__int64)&v5, (__int64)&TI2_AVException_CryptoPP__);
  }
  return PerformanceCount.QuadPart;
}
