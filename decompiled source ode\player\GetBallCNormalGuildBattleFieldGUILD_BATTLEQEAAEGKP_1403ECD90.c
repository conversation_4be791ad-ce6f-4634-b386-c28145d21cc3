/*
 * Function: ?GetBall@CNormalGuildBattleField@GUILD_BATTLE@@QEAAEGKPEAVCPlayer@@@Z
 * Address: 0x1403ECD90
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleField::GetBall(GUILD_BATTLE::CNormalGuildBattleField *this, unsigned __int16 wIndex, unsigned int dwObjSerial, CPlayer *pkPlayer)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v8; // [sp+30h] [bp+8h]@1
  unsigned __int16 v9; // [sp+38h] [bp+10h]@1
  unsigned int dwObjSeriala; // [sp+40h] [bp+18h]@1
  CPlayer *pkPlayera; // [sp+48h] [bp+20h]@1

  pkPlayera = pkPlayer;
  dwObjSeriala = dwObjSerial;
  v9 = wIndex;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v8->m_bInit )
  {
    if ( CPlayer::IsRecallAnimus(pkPlayer) )
      result = -85;
    else
      result = CGravityStone::Get(v8->m_pkBall, v9, dwObjSeriala, pkPlayera);
  }
  else
  {
    result = 110;
  }
  return result;
}
