/*
 * Function: ?DeleteItem@CObjectList@@QEAA_NPEAU_object_list_point@@@Z
 * Address: 0x140189D20
 */

char __fastcall CObjectList::DeleteItem(CObjectList *this, _object_list_point *pItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  _object_list_point *j; // [sp+20h] [bp-18h]@4
  CObjectList *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = v7->m_Head.m_pNext; j != &v7->m_Tail; j = j->m_pNext )
  {
    if ( j == pItem )
    {
      j->m_pPrev->m_pNext = j->m_pNext;
      j->m_pNext->m_pPrev = j->m_pPrev;
      j->m_pNext = 0i64;
      j->m_pPrev = 0i64;
      --v7->m_nSize;
      return 1;
    }
  }
  MyMessageBox("CObjectList", "DeleteItem error");
  return 0;
}
