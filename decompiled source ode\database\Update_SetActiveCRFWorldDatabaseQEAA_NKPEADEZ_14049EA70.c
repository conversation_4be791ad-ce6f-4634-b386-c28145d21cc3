/*
 * Function: ?Update_SetActive@CRFWorldDatabase@@QEAA_NKPEADE@Z
 * Address: 0x14049EA70
 */

bool __fastcall CRFWorldDatabase::Update_SetActive(CRFWorldDatabase *this, unsigned int dwSerial, char *pwszActiveName, char bySlot)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-168h]@1
  int v8; // [sp+20h] [bp-148h]@4
  char Dest; // [sp+40h] [bp-128h]@4
  unsigned __int64 v10; // [sp+150h] [bp-18h]@4
  CRFWorldDatabase *v11; // [sp+170h] [bp+8h]@1

  v11 = this;
  v4 = &v7;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v10 = (unsigned __int64)&v7 ^ _security_cookie;
  v8 = (unsigned __int8)bySlot;
  sprintf(&Dest, "{ CALL pUpdate_SetActive( %d, '%s', %d ) }", dwSerial, pwszActiveName);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 1);
}
