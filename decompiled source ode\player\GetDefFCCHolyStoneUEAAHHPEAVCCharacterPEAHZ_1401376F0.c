/*
 * Function: ?GetDefFC@CHolyStone@@UEAAHHPEAVCCharacter@@PEAH@Z
 * Address: 0x1401376F0
 */

__int64 __fastcall CHolyStone::GetDefFC(CHolyStone *this, int nAttactPart, CCharacter *pAttChar, int *pnConvertPart)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  __int64 v7; // [sp+0h] [bp-28h]@1
  CHolyStone *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( nAttactPart == -1 )
    result = v8->m_nDefPart[rand() % 5];
  else
    result = v8->m_nDefPart[nAttactPart];
  return result;
}
