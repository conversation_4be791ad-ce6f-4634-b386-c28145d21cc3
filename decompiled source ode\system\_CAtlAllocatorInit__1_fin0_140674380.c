/*
 * Function: _CAtlAllocator::Init_::_1_::fin$0
 * Address: 0x140674380
 */

__int64 __fastcall CAtlAllocator::Init_::_1_::fin_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1
  __int64 result; // rax@6

  v2 = a2;
  if ( *(_QWORD *)(a2 + 48) )
  {
    *(_DWORD *)(a2 + 120) = SetThreadToken(0i64, *(HANDLE *)(a2 + 48));
    if ( !*(_DWORD *)(v2 + 120)
      && CrtDbgReportW_0(
           2i64,
           (__int64)L"f:\\dd\\vctools\\vc7libs\\ship\\atlmfc\\src\\atl\\atls\\allocate.cpp",
           117i64,
           0i64) == 1 )
    {
      __debugbreak();
    }
    CloseHandle(*(HANDLE *)(v2 + 48));
  }
  result = *(_BYTE *)(*(_QWORD *)(v2 + 176) + 64i64);
  if ( !*(_BYTE *)(*(_QWORD *)(v2 + 176) + 64i64) )
  {
    if ( *(_QWORD *)(*(_QWORD *)(v2 + 176) + 72i64) )
    {
      UnmapViewOfFile(*(LPCVOID *)(*(_QWORD *)(v2 + 176) + 72i64));
      *(_QWORD *)(*(_QWORD *)(v2 + 176) + 72i64) = 0i64;
    }
    result = *(_QWORD *)(v2 + 176);
    if ( *(_QWORD *)(result + 56) )
    {
      CloseHandle(*(HANDLE *)(*(_QWORD *)(v2 + 176) + 56i64));
      result = *(_QWORD *)(v2 + 176);
      *(_QWORD *)(result + 56) = 0i64;
    }
  }
  return result;
}
