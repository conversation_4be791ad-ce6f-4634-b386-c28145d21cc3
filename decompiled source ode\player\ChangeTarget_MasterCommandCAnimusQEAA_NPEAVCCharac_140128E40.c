/*
 * Function: ?ChangeTarget_MasterCommand@CAnimus@@QEAA_NPEAVCCharacter@@@Z
 * Address: 0x140128E40
 */

char __fastcall CAnimus::ChangeTarget_MasterCommand(CAnimus *this, CCharacter *pTarget)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@20
  int v6; // eax@27
  int v7; // eax@39
  __int64 v8; // [sp+0h] [bp-68h]@1
  CPlayer *v9; // [sp+20h] [bp-48h]@13
  CCharacter *v10; // [sp+28h] [bp-40h]@32
  int v11; // [sp+30h] [bp-38h]@20
  CGameObjectVtbl *v12; // [sp+38h] [bp-30h]@20
  int v13; // [sp+40h] [bp-28h]@27
  CGameObjectVtbl *v14; // [sp+48h] [bp-20h]@27
  int v15; // [sp+50h] [bp-18h]@39
  CGameObjectVtbl *v16; // [sp+58h] [bp-10h]@39
  CAnimus *v17; // [sp+70h] [bp+8h]@1
  CPlayer *v18; // [sp+78h] [bp+10h]@1

  v18 = (CPlayer *)pTarget;
  v17 = this;
  v2 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v17->m_byRoleCode == 3 )
  {
    if ( pTarget->m_ObjID.m_byID )
      return 0;
    v10 = pTarget;
    if ( BYTE2(pTarget[1].m_fCurPos[2]) && v17->m_pMaster->m_bInGuildBattle )
    {
      if ( LOBYTE(v10[1].m_fAbsPos[0]) != v17->m_pMaster->m_byGuildBattleColorInx )
        return 0;
    }
    else
    {
      if ( BYTE2(v10[1].m_fCurPos[2]) || v17->m_pMaster->m_bInGuildBattle )
        return 0;
      v15 = ((int (__fastcall *)(CAnimus *))v17->vfptr->GetObjRace)(v17);
      v16 = v18->vfptr;
      v7 = ((int (__fastcall *)(CPlayer *))v16->GetObjRace)(v18);
      if ( v15 != v7 )
        return 0;
    }
  }
  else
  {
    if ( (unsigned __int8)((int (__fastcall *)(CAnimus *))v17->vfptr->IsInTown)(v17) )
      return 0;
    if ( !(unsigned __int8)((int (__fastcall *)(CPlayer *))v18->vfptr->IsAttackableInTown)(v18)
      && (unsigned __int8)((int (__fastcall *)(CPlayer *))v18->vfptr->IsInTown)(v18) )
    {
      return 0;
    }
    if ( !(unsigned __int8)((int (__fastcall *)(CPlayer *, CAnimus *))v18->vfptr->IsBeDamagedAble)(v18, v17) )
      return 0;
    if ( v18->m_ObjID.m_byID )
    {
      v13 = ((int (__fastcall *)(CAnimus *))v17->vfptr->GetObjRace)(v17);
      v14 = v18->vfptr;
      v6 = ((int (__fastcall *)(CPlayer *))v14->GetObjRace)(v18);
      if ( v13 == v6 )
        return 0;
    }
    else
    {
      v9 = v18;
      if ( v18->m_bInGuildBattle && v17->m_pMaster->m_bInGuildBattle )
      {
        if ( v9->m_byGuildBattleColorInx == v17->m_pMaster->m_byGuildBattleColorInx )
          return 0;
      }
      else
      {
        if ( v9->m_bInGuildBattle || v17->m_pMaster->m_bInGuildBattle )
          return 0;
        v11 = ((int (__fastcall *)(CAnimus *))v17->vfptr->GetObjRace)(v17);
        v12 = v18->vfptr;
        v5 = ((int (__fastcall *)(CPlayer *))v12->GetObjRace)(v18);
        if ( v11 == v5 && !CPlayer::IsChaosMode(v17->m_pMaster) && !CPlayer::IsPunished(v9, 1, 0) )
          return 0;
      }
    }
  }
  CAnimus::ChangeMode(v17, 0);
  v17->m_pTarget = (CCharacter *)v18;
  return 1;
}
