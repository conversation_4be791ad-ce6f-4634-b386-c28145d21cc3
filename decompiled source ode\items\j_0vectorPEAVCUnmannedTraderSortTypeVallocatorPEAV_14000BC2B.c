/*
 * Function: j_??0?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEAA@XZ
 * Address: 0x14000BC2B
 */

void __fastcall std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this)
{
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(this);
}
