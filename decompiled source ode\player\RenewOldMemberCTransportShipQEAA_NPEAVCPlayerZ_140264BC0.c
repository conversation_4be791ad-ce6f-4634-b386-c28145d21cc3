/*
 * Function: ?RenewOldMember@CTransportShip@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x140264BC0
 */

char __fastcall CTransportShip::RenewOldMember(CTransportShip *this, CPlayer *pMember)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@11
  __int64 v5; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@4
  CTransportShip::__mgr_member *v7; // [sp+28h] [bp-20h]@7
  CTransportShip::__mgr_member *v8; // [sp+30h] [bp-18h]@10
  CTransportShip *v9; // [sp+50h] [bp+8h]@1
  CPlayer *v10; // [sp+58h] [bp+10h]@1

  v10 = pMember;
  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; ; ++j )
  {
    if ( j >= 2532 )
      return 0;
    v7 = &v9->m_OldMember[j];
    if ( CTransportShip::__mgr_member::is_fill(v7) && v7->pPtr == v10 && v7->dwSerial == v10->m_dwObjSerial )
      break;
  }
  v8 = CTransportShip::GetEmptyNewMember(v9);
  if ( v8 )
  {
    v8->pPtr = v10;
    v8->dwSerial = v10->m_dwObjSerial;
    CTransportShip::__mgr_member::init(v7);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
