/*
 * Function: ?MakeMipMap@@YAXGGPEAG0@Z
 * Address: 0x1405005B0
 */

void __fastcall MakeMipMap(unsigned __int16 a1, unsigned __int16 a2, unsigned __int16 *a3, unsigned __int16 *a4)
{
  __int64 v4; // rdi@1
  unsigned __int16 *v5; // r14@1
  unsigned __int16 v6; // r8@1
  unsigned __int16 *v7; // r11@1
  int v8; // er9@1
  unsigned int v9; // er12@1
  __int64 v10; // r13@2
  signed __int64 v11; // rsi@2
  signed __int64 v12; // rbx@2
  signed __int64 v13; // rbp@2
  _WORD *v14; // r15@4
  unsigned __int16 *v15; // rbx@4
  _WORD *v16; // r13@4
  __int64 v17; // rax@4
  __int64 v18; // rdi@4
  signed __int64 v19; // r14@4
  _WORD *v20; // r12@4
  unsigned int v21; // er10@5
  unsigned int v22; // er9@5
  unsigned int v23; // eax@5
  unsigned int v24; // ecx@5
  int v25; // er8@5
  int v26; // er9@5
  int v27; // er10@5
  unsigned int v28; // ecx@5
  unsigned int v29; // eax@5
  unsigned int v30; // edx@5
  __int64 v31; // [sp+0h] [bp-58h]@1
  __int64 v32; // [sp+8h] [bp-50h]@2
  signed __int64 v33; // [sp+18h] [bp-40h]@2
  unsigned __int16 v34; // [sp+60h] [bp+8h]@1
  unsigned int v35; // [sp+68h] [bp+10h]@1
  unsigned __int16 *v36; // [sp+70h] [bp+18h]@1

  v36 = a3;
  v34 = a1;
  v4 = 0i64;
  v5 = a3;
  v6 = a1;
  v7 = a4;
  v8 = 2 * a1;
  v9 = a1;
  v35 = a1;
  v31 = 0i64;
  if ( (signed int)a2 > 0 )
  {
    v10 = a2;
    v11 = (signed __int64)&v7[v8];
    v12 = 8i64 * a1;
    v33 = 8i64 * a1;
    v32 = a2;
    v13 = (signed __int64)&v7[v8 + 1];
    do
    {
      if ( (signed int)v9 > 0 )
      {
        v14 = (_WORD *)v11;
        v15 = v7;
        v16 = v7 + 1;
        v17 = v4 * v6;
        v18 = v9;
        v19 = (signed __int64)&v5[v17];
        v20 = (_WORD *)v13;
        do
        {
          v21 = *v15;
          v22 = ((unsigned int)*v16 >> 11) + (v21 >> 11);
          v23 = *v16;
          v24 = *v14;
          v25 = ((v23 >> 5) & 0x3F) + ((v21 >> 5) & 0x3F);
          v19 += 2i64;
          v15 += 2;
          v16 += 2;
          v26 = (v24 >> 11) + v22;
          v27 = (v24 & 0x1F) + (v23 & 0x1F) + (v21 & 0x1F);
          v14 += 2;
          v28 = (((v24 >> 5) & 0x3F) + v25 + (((unsigned int)*v20 >> 5) & 0x3F)) >> 2;
          v29 = (v26 + ((unsigned int)*v20 >> 11)) >> 2;
          v30 = (v27 + (*v20 & 0x1Fu)) >> 2;
          v20 += 2;
          --v18;
          *(_WORD *)(v19 - 2) = v30 | 32 * (((_WORD)v29 << 6) | v28);
        }
        while ( v18 );
        v12 = v33;
        v4 = v31;
        v9 = v35;
        v10 = v32;
        v6 = v34;
        v5 = v36;
      }
      ++v4;
      v11 += v12;
      v13 += v12;
      v7 = (unsigned __int16 *)((char *)v7 + v12);
      --v10;
      v31 = v4;
      v32 = v10;
    }
    while ( v10 );
  }
}
