/*
 * Function: _CryptoPP::Singleton_CryptoPP::DL_EncryptionAlgorithm_Xor_CryptoPP::HMAC_CryptoPP::SHA1__0__CryptoPP::NewObject_CryptoPP::DL_EncryptionAlgorithm_Xor_CryptoPP::HMAC_CryptoPP::SHA1__0____0_::Ref_::_1_::catch$0
 * Address: 0x14045FAF0
 */

void __noreturn CryptoPP::Singleton_CryptoPP::DL_EncryptionAlgorithm_Xor_CryptoPP::HMAC_CryptoPP::SHA1__0__CryptoPP::NewObject_CryptoPP::DL_EncryptionAlgorithm_Xor_CryptoPP::HMAC_CryptoPP::SHA1__0____0_::Ref_::_1_::catch_0()
{
  s_objectState_1[0] = 0;
  CxxThrowException_0(0i64, 0i64);
}
