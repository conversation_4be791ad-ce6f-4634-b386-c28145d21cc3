/*
 * Function: j_??$_Ptr_cat@PEAVCUnmannedTraderGroupDivisionVersionInfo@@PEAV1@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAPEAVCUnmannedTraderGroupDivisionVersionInfo@@0@Z
 * Address: 0x14000B299
 */

std::_Nonscalar_ptr_iterator_tag __fastcall std::_Ptr_cat<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo *>(CUnmannedTraderGroupDivisionVersionInfo **__formal, CUnmannedTraderGroupDivisionVersionInfo **a2)
{
  return std::_Ptr_cat<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo *>(
           __formal,
           a2);
}
