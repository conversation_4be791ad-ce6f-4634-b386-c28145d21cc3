/*
 * Function: j_??1?$_<PERSON>t@PEAVCLogTypeDBTask@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@XZ
 * Address: 0x140012891
 */

void __fastcall std::_<PERSON>t<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &>::~_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &>(std::_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &> *this)
{
  std::_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &>::~_<PERSON>t<CLogTypeDBTask *,__int64,<PERSON>LogTypeDBTask * const *,CLogTypeDBTask * const &>(this);
}
