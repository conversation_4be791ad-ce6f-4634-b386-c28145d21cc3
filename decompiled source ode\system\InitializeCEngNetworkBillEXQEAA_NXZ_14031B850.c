/*
 * Function: ?Initialize@CEngNetworkBillEX@@QEAA_NXZ
 * Address: 0x14031B850
 */

char __fastcall CEngNetworkBillEX::Initialize(CEngNetworkBillEX *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CEngNetworkBillEX *v4; // rax@10
  unsigned __int32 v5; // eax@13
  __int64 v6; // [sp+0h] [bp-48h]@1
  CEngNetworkBillEX *v7; // [sp+20h] [bp-28h]@12
  CEngNetworkBillEX *v8; // [sp+28h] [bp-20h]@9
  __int64 v9; // [sp+30h] [bp-18h]@4
  CEngNetworkBillEX *v10; // [sp+38h] [bp-10h]@10
  CEngNetworkBillEX *v11; // [sp+50h] [bp+8h]@1

  v11 = this;
  v1 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = -2i64;
  if ( CEngNetworkBillEX::LoadINIFile(v11) )
  {
    if ( BNetwork::LoadDll((BNetwork *)&v11->vfptr, "ENG_BILLNETD.dll") )
    {
      if ( !CEngNetworkBillEX::m_pEngNet )
      {
        v8 = (CEngNetworkBillEX *)operator new(0x88ui64);
        if ( v8 )
        {
          CEngNetworkBillEX::CEngNetworkBillEX(v8);
          v10 = v4;
        }
        else
        {
          v10 = 0i64;
        }
        v7 = v10;
        CEngNetworkBillEX::m_pEngNet = v10;
      }
      ((void (__fastcall *)(bool (__fastcall *)(unsigned int, unsigned int, _MSG_HEADER *, char *)))v11->SetDataAnalysisFunc)(CEngNetworkBillEX::s_DataAnalysis);
      BNetwork::InitNetwork((BNetwork *)&v11->vfptr);
      v5 = inet_addr(v11->m_ip);
      if ( ((int (__fastcall *)(_QWORD, _QWORD, _QWORD, _QWORD))v11->Connect)(0i64, 0i64, v5, LOWORD(v11->m_port)) )
      {
        v11->m_bConnect = 0;
        result = 0;
      }
      else
      {
        v11->m_bConnect = 1;
        result = 1;
      }
    }
    else
    {
      v11->m_bConnect = 0;
      result = 0;
    }
  }
  else
  {
    v11->m_bConnect = 0;
    result = 0;
  }
  return result;
}
