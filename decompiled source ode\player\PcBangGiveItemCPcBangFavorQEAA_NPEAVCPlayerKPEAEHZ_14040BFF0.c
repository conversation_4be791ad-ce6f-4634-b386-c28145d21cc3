/*
 * Function: ?PcBangGiveItem@CPcBangFavor@@QEAA_NPEAVCPlayer@@KPEAEH@Z
 * Address: 0x14040BFF0
 */

char __fastcall CPcBangFavor::PcBangGiveItem(CPcBangFavor *this, CPlayer *pOne, unsigned int dwRecIndex, char *bySeletItemIndex, int nSelectCount)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  int v8; // eax@40
  __int64 v9; // [sp+0h] [bp-A8h]@1
  _base_fld *v10; // [sp+30h] [bp-78h]@12
  int v11; // [sp+38h] [bp-70h]@16
  int j; // [sp+3Ch] [bp-6Ch]@16
  unsigned __int8 v13; // [sp+40h] [bp-68h]@20
  char *psItemCode; // [sp+48h] [bp-60h]@21
  int nTableCode; // [sp+50h] [bp-58h]@21
  unsigned __int16 *v16; // [sp+58h] [bp-50h]@22
  void *Src; // [sp+60h] [bp-48h]@23
  char *szRecordCode; // [sp+68h] [bp-40h]@32
  int v19; // [sp+70h] [bp-38h]@32
  unsigned __int16 *v20; // [sp+78h] [bp-30h]@33
  void *v21; // [sp+80h] [bp-28h]@34
  _STORAGE_LIST::_storage_con *pCon; // [sp+88h] [bp-20h]@43
  _STORAGE_LIST::_db_con *Item; // [sp+90h] [bp-18h]@43
  __int64 v24; // [sp+98h] [bp-10h]@4
  CPcBangFavor *v25; // [sp+B0h] [bp+8h]@1
  CPlayer *v26; // [sp+B8h] [bp+10h]@1
  char *v27; // [sp+C8h] [bp+20h]@1

  v27 = bySeletItemIndex;
  v26 = pOne;
  v25 = this;
  v5 = &v9;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v24 = -2i64;
  if ( pOne && pOne->m_bOper )
  {
    if ( nSelectCount <= 5 )
    {
      if ( dwRecIndex == pOne->m_dwPcBangGiveItemListIndex || pOne->m_dwPcBangGiveItemListIndex == -1 )
      {
        pOne->m_dwPcBangGiveItemListIndex = -1;
        v10 = CRecordData::GetRecord(&v25->m_tblPcRoomData, dwRecIndex);
        if ( v10 )
        {
          if ( !(_S1_3 & 1) )
          {
            _S1_3 |= 1u;
            `vector constructor iterator'(
              MaketItem,
              0x32ui64,
              15,
              (void *(__cdecl *)(void *))_STORAGE_LIST::_db_con::_db_con);
          }
          v11 = 0;
          for ( j = 0; j < 5; ++j )
          {
            if ( *(_DWORD *)&v10[3].m_strCode[84 * j + 8] >= 0 )
            {
              v13 = v27[j];
              if ( (signed int)v13 < 10 )
              {
                psItemCode = (char *)&v10[2] + 84 * j + 8 * v13;
                nTableCode = GetItemTableCode(psItemCode);
                if ( nTableCode != -1 )
                {
                  v16 = (unsigned __int16 *)CRecordData::GetRecordByHash(
                                              (CRecordData *)&unk_1799C6AA0 + nTableCode,
                                              psItemCode,
                                              2,
                                              5);
                  if ( v16 )
                  {
                    Src = MakeLoot(nTableCode, *v16);
                    if ( Src )
                    {
                      if ( IsOverLapItem(nTableCode) )
                        *(_QWORD *)((char *)Src + 5) = *(_DWORD *)&v10[3].m_strCode[84 * j + 8];
                      memcpy_0(&MaketItem[v11++], Src, 0x32ui64);
                    }
                  }
                }
              }
            }
          }
          for ( j = 0; j < 10; ++j )
          {
            if ( *(_DWORD *)&v10[8].m_strCode[12 * j + 16] >= 0 )
            {
              szRecordCode = &v10[8].m_strCode[12 * j + 8];
              v19 = GetItemTableCode(szRecordCode);
              if ( v19 != -1 )
              {
                v20 = (unsigned __int16 *)CRecordData::GetRecordByHash(
                                            (CRecordData *)&unk_1799C6AA0 + v19,
                                            szRecordCode,
                                            2,
                                            5);
                if ( v20 )
                {
                  v21 = MakeLoot(v19, *v20);
                  if ( v21 )
                  {
                    if ( IsOverLapItem(v19) )
                      *(_QWORD *)((char *)v21 + 5) = *(_DWORD *)&v10[8].m_strCode[12 * j + 16];
                    memcpy_0(&MaketItem[v11++], v21, 0x32ui64);
                  }
                }
              }
            }
          }
          if ( v11 <= 0 )
          {
            result = 0;
          }
          else
          {
            v8 = _STORAGE_LIST::GetNumEmptyCon((_STORAGE_LIST *)&v26->m_Param.m_dbInven.m_nListNum);
            if ( v11 >= v8 )
            {
              CPlayer::SendMsg_PcRoomError(v26, 1);
              result = 0;
            }
            else
            {
              for ( j = 0; j < v11; ++j )
              {
                pCon = (_STORAGE_LIST::_storage_con *)&MaketItem[j].m_bLoad;
                pCon->m_wSerial = CPlayerDB::GetNewItemSerial(&v26->m_Param);
                Item = CPlayer::Emb_AddStorage(v26, 0, pCon, 0, 1);
                CPlayer::SendMsg_RewardAddItem(v26, (_STORAGE_LIST::_db_con *)pCon, 9);
                if ( Item )
                  _PCBANG_FAVOR_ITEM_DB_BASE::InsertItem(&v26->m_pUserDB->m_AvatorData.dbPcBangFavorItem, Item);
              }
              CPlayer::SendMsg_PcRoomError(v26, 0);
              result = 1;
            }
          }
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
