/*
 * Function: ?GetPartyExpDistributionRate@CPlayer@@QEAAMHHH@Z
 * Address: 0x14005D6A0
 */

float __fastcall CPlayer::GetPartyExpDistributionRate(CPlayer *this, int iPartyMemberLevel, int iMaxLevel, int i2ndLevel)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  float result; // xmm0_4@6
  __int64 v7; // [sp+0h] [bp-28h]@1
  float v8; // [sp+10h] [bp-18h]@7

  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( iMaxLevel && i2ndLevel )
  {
    v8 = FLOAT_0_050000001;
    if ( iPartyMemberLevel >= iMaxLevel )
      v8 = v8 * (float)(iPartyMemberLevel - i2ndLevel);
    else
      v8 = v8 * (float)(iPartyMemberLevel - iMaxLevel);
    result = v8;
  }
  else
  {
    result = 0.0;
  }
  return result;
}
