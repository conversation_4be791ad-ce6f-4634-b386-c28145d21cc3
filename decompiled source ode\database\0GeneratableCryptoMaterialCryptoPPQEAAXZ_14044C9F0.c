/*
 * Function: ??0GeneratableCryptoMaterial@CryptoPP@@QEAA@XZ
 * Address: 0x14044C9F0
 */

void __fastcall CryptoPP::GeneratableCryptoMaterial::GeneratableCryptoMaterial(CryptoPP::GeneratableCryptoMaterial *this, int a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  CryptoPP::GeneratableCryptoMaterial *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = 0;
  if ( a2 )
  {
    *(_QWORD *)&v6->gap8[0] = &CryptoPP::GeneratableCryptoMaterial::`vbtable';
    CryptoPP::CryptoMaterial::CryptoMaterial((CryptoPP::CryptoMaterial *)&v6->gap8[8]);
  }
}
