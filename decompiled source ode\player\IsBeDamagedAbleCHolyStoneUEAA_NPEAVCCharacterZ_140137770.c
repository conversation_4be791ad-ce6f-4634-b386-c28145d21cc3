/*
 * Function: ?IsBeDamagedAble@CHolyStone@@UEAA_NPEAVCCharacter@@@Z
 * Address: 0x140137770
 */

bool __fastcall CHolyStone::IsBeDamagedAble(CHolyStone *this, CCharacter *pAtter)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  __int64 v5; // [sp+0h] [bp-68h]@1
  CCharacter *v6; // [sp+20h] [bp-48h]@7
  float v7[3]; // [sp+38h] [bp-30h]@20
  CMapData *v8; // [sp+58h] [bp-10h]@20
  CHolyStone *v9; // [sp+70h] [bp+8h]@1
  CCharacter *v10; // [sp+78h] [bp+10h]@1

  v10 = pAtter;
  v9 = this;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pAtter->m_ObjID.m_byID && pAtter->m_ObjID.m_byID != 3 )
  {
    result = 0;
  }
  else
  {
    v6 = 0i64;
    if ( pAtter->m_ObjID.m_byID )
    {
      if ( pAtter->m_ObjID.m_byID == 3 )
        v6 = *(CCharacter **)&pAtter[1].m_bLive;
    }
    else
    {
      v6 = pAtter;
      if ( CMainThread::IsReleaseServiceMode(&g_Main) && BYTE2(v6[25].m_SFCont[0][5].m_nCumulCounter) )
        v6 = 0i64;
    }
    if ( v6 )
    {
      if ( v6[25].m_SFCont[0][2].m_wszPlayerName[8] == 100 )
      {
        result = 0;
      }
      else if ( CPlayerDB::GetRaceCode((CPlayerDB *)&v6[1].m_fOldPos[2]) == v9->m_byMasterRaceCode )
      {
        result = 0;
      }
      else
      {
        v8 = v10->m_pCurMap;
        result = (unsigned int)CBsp::CanYouGoThere(v8->m_Level.mBsp, v10->m_fCurPos, v9->m_fCurPos, (float (*)[3])v7) != 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  return result;
}
