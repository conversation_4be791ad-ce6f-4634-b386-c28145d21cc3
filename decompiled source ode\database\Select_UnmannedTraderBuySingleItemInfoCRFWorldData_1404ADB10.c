/*
 * Function: ?Select_UnmannedTraderBuySingleItemInfo@CRFWorldDatabase@@QEAAEEKAEAU_unmannedtrader_buy_item_info@@@Z
 * Address: 0x1404ADB10
 */

char __fastcall CRFWorldDatabase::Select_UnmannedTraderBuySingleItemInfo(CRFWorldDatabase *this, char byType, unsigned int dwRegistSerial, _unmannedtrader_buy_item_info *kData)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v7; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@4
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@22
  SQLLEN v10; // [sp+38h] [bp-150h]@22
  __int16 v11; // [sp+44h] [bp-144h]@9
  char DstBuf; // [sp+60h] [bp-128h]@4
  char v13; // [sp+164h] [bp-24h]@16
  unsigned __int64 v14; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v15; // [sp+190h] [bp+8h]@1
  _unmannedtrader_buy_item_info *TargetValue; // [sp+1A8h] [bp+20h]@1

  TargetValue = kData;
  v15 = this;
  v4 = &v7;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v14 = (unsigned __int64)&v7 ^ _security_cookie;
  LODWORD(SQLStmt) = dwRegistSerial;
  sprintf_s(&DstBuf, 0x100ui64, "{ CALL pSelect_utbuysingleiteminfo_20061115( %u, %u ) }", (unsigned __int8)byType);
  if ( v15->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v15->vfptr, &DstBuf);
  if ( v15->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v15->vfptr) )
  {
    v11 = SQLExecDirectA_0(v15->m_hStmtSelect, &DstBuf, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v15->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &DstBuf, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v11 = SQLFetch_0(v15->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        v13 = 0;
        if ( v11 == 100 )
        {
          v13 = 2;
        }
        else
        {
          SQLStmt = v15->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &DstBuf, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
          v13 = 1;
        }
        if ( v15->m_hStmtSelect )
          SQLCloseCursor_0(v15->m_hStmtSelect);
        result = v13;
      }
      else
      {
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 1u, -6, TargetValue, 0i64, &v10);
        if ( v11 && v11 != 1 )
        {
          SQLStmt = v15->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &DstBuf, "SQLGetData", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
          if ( v15->m_hStmtSelect )
            SQLCloseCursor_0(v15->m_hStmtSelect);
          result = 1;
        }
        else
        {
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v15->m_hStmtSelect, 2u, 4, &TargetValue->dwK, 0i64, &v10);
          if ( v11 && v11 != 1 )
          {
            SQLStmt = v15->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &DstBuf, "SQLGetData", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
            if ( v15->m_hStmtSelect )
              SQLCloseCursor_0(v15->m_hStmtSelect);
            result = 1;
          }
          else
          {
            StrLen_or_IndPtr = &v10;
            SQLStmt = 0i64;
            v11 = SQLGetData_0(v15->m_hStmtSelect, 3u, -25, &TargetValue->dwD, 0i64, &v10);
            if ( v11 && v11 != 1 )
            {
              SQLStmt = v15->m_hStmtSelect;
              CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &DstBuf, "SQLGetData", SQLStmt);
              CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
              if ( v15->m_hStmtSelect )
                SQLCloseCursor_0(v15->m_hStmtSelect);
              result = 1;
            }
            else
            {
              StrLen_or_IndPtr = &v10;
              SQLStmt = 0i64;
              v11 = SQLGetData_0(v15->m_hStmtSelect, 4u, 4, &TargetValue->dwU, 0i64, &v10);
              if ( v11 && v11 != 1 )
              {
                SQLStmt = v15->m_hStmtSelect;
                CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &DstBuf, "SQLGetData", SQLStmt);
                CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
                if ( v15->m_hStmtSelect )
                  SQLCloseCursor_0(v15->m_hStmtSelect);
                result = 1;
              }
              else
              {
                StrLen_or_IndPtr = &v10;
                SQLStmt = 0i64;
                v11 = SQLGetData_0(v15->m_hStmtSelect, 5u, -18, &TargetValue->dwPrice, 0i64, &v10);
                if ( v11 && v11 != 1 )
                {
                  SQLStmt = v15->m_hStmtSelect;
                  CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &DstBuf, "SQLGetData", SQLStmt);
                  CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
                  if ( v15->m_hStmtSelect )
                    SQLCloseCursor_0(v15->m_hStmtSelect);
                  result = 1;
                }
                else
                {
                  StrLen_or_IndPtr = &v10;
                  SQLStmt = 0i64;
                  v11 = SQLGetData_0(v15->m_hStmtSelect, 6u, 4, &TargetValue->dwSeller, 0i64, &v10);
                  if ( v11 && v11 != 1 )
                  {
                    SQLStmt = v15->m_hStmtSelect;
                    CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &DstBuf, "SQLGetData", SQLStmt);
                    CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
                    if ( v15->m_hStmtSelect )
                      SQLCloseCursor_0(v15->m_hStmtSelect);
                    result = 1;
                  }
                  else
                  {
                    StrLen_or_IndPtr = &v10;
                    SQLStmt = 0i64;
                    v11 = SQLGetData_0(v15->m_hStmtSelect, 7u, -25, &TargetValue->lnUID, 0i64, &v10);
                    if ( v11 && v11 != 1 )
                    {
                      SQLStmt = v15->m_hStmtSelect;
                      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &DstBuf, "SQLGetData", SQLStmt);
                      CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
                      if ( v15->m_hStmtSelect )
                        SQLCloseCursor_0(v15->m_hStmtSelect);
                      result = 1;
                    }
                    else
                    {
                      StrLen_or_IndPtr = &v10;
                      SQLStmt = 0i64;
                      v11 = SQLGetData_0(v15->m_hStmtSelect, 8u, 4, &TargetValue->dwT, 0i64, &v10);
                      if ( v11 && v11 != 1 )
                      {
                        SQLStmt = v15->m_hStmtSelect;
                        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &DstBuf, "SQLGetData", SQLStmt);
                        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
                        if ( v15->m_hStmtSelect )
                          SQLCloseCursor_0(v15->m_hStmtSelect);
                        result = 1;
                      }
                      else
                      {
                        if ( v15->m_hStmtSelect )
                          SQLCloseCursor_0(v15->m_hStmtSelect);
                        if ( v15->m_bSaveDBLog )
                          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v15->vfptr, "%s Success", &DstBuf);
                        result = 0;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v15->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1;
  }
  return result;
}
