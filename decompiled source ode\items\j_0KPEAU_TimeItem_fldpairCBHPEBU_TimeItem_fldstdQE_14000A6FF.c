/*
 * Function: j_??$?0KPEAU_TimeItem_fld@@@?$pair@$$CBHPEBU_TimeItem_fld@@@std@@QEAA@AEBU?$pair@KPEAU_TimeItem_fld@@@1@@Z
 * Address: 0x14000A6FF
 */

void __fastcall std::pair<int const,_TimeItem_fld const *>::pair<int const,_TimeItem_fld const *>(std::pair<int const ,_TimeItem_fld const *> *this, std::pair<unsigned long,_TimeItem_fld *> *_Right)
{
  std::pair<int const,_TimeItem_fld const *>::pair<int const,_TimeItem_fld const *>(this, _Right);
}
