/*
 * Function: ??0CMapExtend@@QEAA@PEAPEAVCSurface@@@Z
 * Address: 0x1401A1500
 */

void __fastcall CMapExtend::CMapExtend(CMapExtend *this, CSurface **pSF)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMapExtend *v5; // [sp+30h] [bp+8h]@1
  CSurface **v6; // [sp+38h] [bp+10h]@1

  v6 = pSF;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CPoint::CPoint(&v5->m_ptStartMap);
  CPoint::CPoint(&v5->m_ptEndMap);
  CPoint::CPoint(&v5->m_ptCenter);
  CRect::CRect(&v5->m_rcExtend);
  CPoint::CPoint(&v5->m_ptStartScreen);
  CPoint::CPoint(&v5->m_ptEndScreen);
  CPoint::CPoint(&v5->m_ptMoveScreen);
  CSize::CSize(&v5->m_sizeExtend);
  v5->m_bSetArea = 0;
  v5->m_bExtendMode = 0;
  v5->m_hPen = CreatePen(0, 1, 0x646464u);
  v5->m_pSF = v6;
}
