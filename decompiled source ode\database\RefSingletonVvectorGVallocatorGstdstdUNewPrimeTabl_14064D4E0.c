/*
 * Function: ?Ref@?$Singleton@V?$vector@GV?$allocator@G@std@@@std@@UNewPrimeTable@CryptoPP@@$0A@@CryptoPP@@QEBAAEBV?$vector@GV?$allocator@G@std@@@std@@XZ
 * Address: 0x14064D4E0
 */

__int64 __fastcall CryptoPP::Singleton<std::vector<unsigned short,std::allocator<unsigned short>>,CryptoPP::NewPrimeTable,0>::Ref(__int64 a1)
{
  __int64 v1; // rax@6
  __int64 v3; // [sp+50h] [bp+8h]@1

  v3 = a1;
  if ( !(dword_184A8AA50 & 1) )
  {
    dword_184A8AA50 |= 1u;
    CryptoPP::simple_ptr<std::vector<unsigned short,std::allocator<unsigned short>>>::simple_ptr<std::vector<unsigned short,std::allocator<unsigned short>>>(&qword_184A8AA48);
    atexit(sub_1406E9C70);
  }
  while ( byte_184A8AA41 )
  {
    if ( byte_184A8AA41 != 1 )
      return qword_184A8AA48;
  }
  byte_184A8AA41 = 1;
  LODWORD(v1) = CryptoPP::NewPrimeTable::operator()(v3);
  qword_184A8AA48 = v1;
  byte_184A8AA41 = 2;
  return qword_184A8AA48;
}
