/*
 * Function: j_?GetFirstMapFieldByRace@CNormalGuildBattleFieldList@GUILD_BATTLE@@QEAAPEAVCNormalGuildBattleField@2@E@Z
 * Address: 0x14000D2BF
 */

GUILD_BATTLE::CNormalGuildBattleField *__fastcall GUILD_BATTLE::CNormalGuildBattleFieldList::GetFirstMapFieldByRace(GUILD_BATTLE::CNormalGuildBattleFieldList *this, char byRace)
{
  return GUILD_BATTLE::CNormalGuildBattleFieldList::GetFirstMapFieldByRace(this, byRace);
}
