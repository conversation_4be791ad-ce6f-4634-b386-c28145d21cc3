/*
 * Function: ?GetTalikFromSocket@@YAEKE@Z
 * Address: 0x14003E2E0
 */

char __fastcall GetTalikFromSocket(unsigned int dwLvBit, char bySocketIndex)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v5; // [sp+0h] [bp-18h]@1
  unsigned int v6; // [sp+20h] [bp+8h]@1

  v6 = dwLvBit;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  if ( v6 )
    result = (v6 >> 4 * bySocketIndex) & 0xF;
  else
    result = 15;
  return result;
}
