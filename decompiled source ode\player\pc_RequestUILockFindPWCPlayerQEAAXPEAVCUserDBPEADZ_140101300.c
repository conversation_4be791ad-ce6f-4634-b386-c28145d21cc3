/*
 * Function: ?pc_RequestUILockFindPW@CPlayer@@QEAAXPEAVCUserDB@@PEAD@Z
 * Address: 0x140101300
 */

void __fastcall CPlayer::pc_RequestUILockFindPW(CPlayer *this, CUserDB *pUserDB, char *uszHintAnswer)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  char v6; // [sp+30h] [bp-18h]@5
  CPlayer *v7; // [sp+50h] [bp+8h]@1
  CUserDB *v8; // [sp+58h] [bp+10h]@1
  const char *Str; // [sp+60h] [bp+18h]@1

  Str = uszHintAnswer;
  v8 = pUserDB;
  v7 = this;
  v3 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( !pUserDB->m_byUserDgr )
  {
    v6 = 0;
    if ( uszHintAnswer && strlen_0(uszHintAnswer) )
    {
      if ( v8->m_byUILock > 0 )
      {
        if ( v8->m_byUILock == 2 )
        {
          v6 = 3;
        }
        else if ( strcmp_0(v8->m_uszUILock_HintAnswer, Str) )
        {
          if ( ++v8->m_byUILockFindPassFailCount < 5 )
            v6 = 1;
          else
            v6 = 5;
        }
      }
      else
      {
        v6 = 2;
      }
    }
    else
    {
      v6 = 4;
    }
    if ( v6 )
    {
      if ( v6 == 5 )
      {
        CPlayer::SendMsg_UILock_FindPW_Result(v7, v6, 0i64, v8->m_byUILockFindPassFailCount);
        CUserDB::ForceCloseCommand(v8, 9, 0, 1, "UILOCK Find Password Fail");
      }
      else
      {
        CPlayer::SendMsg_UILock_FindPW_Result(v7, v6, 0i64, v8->m_byUILockFindPassFailCount);
      }
    }
    else
    {
      v8->m_byUILockFindPassFailCount = 0;
      CPlayer::SendMsg_UILock_FindPW_Result(v7, v6, v8->m_szUILock_PW, v8->m_byUILockFindPassFailCount);
    }
  }
}
