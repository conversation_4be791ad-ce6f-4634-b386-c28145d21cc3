/*
 * Function: j_??$unchecked_copy@PEAVCMoveMapLimitRightInfo@@PEAV1@@stdext@@YAPEAVCMoveMapLimitRightInfo@@PEAV1@00@Z
 * Address: 0x140001276
 */

CMoveMapLimitRightInfo *__fastcall stdext::unchecked_copy<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *>(CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, CMoveMapLimitRightInfo *_Dest)
{
  return stdext::unchecked_copy<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *>(_First, _Last, _Dest);
}
