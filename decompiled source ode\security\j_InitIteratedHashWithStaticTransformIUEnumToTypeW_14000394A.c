/*
 * Function: j_?Init@?$IteratedHashWithStaticTransform@IU?$EnumToType@W4ByteOrder@CryptoPP@@$00@CryptoPP@@$0EA@$0CA@VSHA256@2@$0A@@CryptoPP@@MEAAXXZ
 * Address: 0x14000394A
 */

void __fastcall CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,32,CryptoPP::SHA256,0>::Init(CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,32,CryptoPP::SHA256,0> *this)
{
  CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::Byte<PERSON><PERSON>r,1>,64,32,CryptoPP::SHA256,0>::Init(this);
}
