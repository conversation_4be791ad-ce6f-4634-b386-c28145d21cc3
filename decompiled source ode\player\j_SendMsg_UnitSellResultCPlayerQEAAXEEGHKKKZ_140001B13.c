/*
 * Function: j_?SendMsg_UnitSellResult@CPlayer@@QEAAXEEGHKKK@Z
 * Address: 0x140001B13
 */

void __fastcall CPlayer::SendMsg_UnitSellResult(CPlayer *this, char byRetCode, char bySlotIndex, unsigned __int16 wKeySerial, int nAddMoney, unsigned int dwTotalNonpay, unsigned int dwSumDalant, unsigned int dwSumGold)
{
  CPlayer::SendMsg_UnitSellResult(
    this,
    byRetCode,
    bySlotIndex,
    wKeySerial,
    nAddMoney,
    dwTotalNonpay,
    dwSumDalant,
    dwSumGold);
}
