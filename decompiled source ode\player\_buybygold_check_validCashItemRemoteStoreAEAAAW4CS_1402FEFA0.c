/*
 * Function: ?_buybygold_check_valid@CashItemRemoteStore@@AEAA?AW4CS_RCODE@@PEAVCPlayer@@PEAU_request_csi_buy_clzo@@PEAU_param_cashitem_dblog@@@Z
 * Address: 0x1402FEFA0
 */

signed __int64 __fastcall CashItemRemoteStore::_buybygold_check_valid(CashItemRemoteStore *this, CPlayer *pOne, _request_csi_buy_clzo *pRecv, _param_cashitem_dblog *pSheet)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  int v7; // eax@11
  __int64 v8; // [sp+0h] [bp-48h]@1
  int v9; // [sp+20h] [bp-28h]@6
  int j; // [sp+24h] [bp-24h]@6
  CS_RCODE v11; // [sp+28h] [bp-20h]@13
  int v12; // [sp+2Ch] [bp-1Ch]@6
  int v13; // [sp+30h] [bp-18h]@11
  CashItemRemoteStore *v14; // [sp+50h] [bp+8h]@1
  CPlayer *pOnea; // [sp+58h] [bp+10h]@1
  _request_csi_buy_clzo *pRecva; // [sp+60h] [bp+18h]@1
  _param_cashitem_dblog *pSheeta; // [sp+68h] [bp+20h]@1

  pSheeta = pSheet;
  pRecva = pRecv;
  pOnea = pOne;
  v14 = this;
  v4 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&pOne->m_Param.m_dbInven.m_nListNum) == 255 )
  {
    result = 16i64;
  }
  else
  {
    v12 = pRecva->nNum;
    _STORAGE_LIST::GetNumEmptyCon((_STORAGE_LIST *)&pOnea->m_Param.m_dbInven.m_nListNum);
    v9 = 0;
    for ( j = 0; j < pRecva->nNum; ++j )
    {
      if ( pRecva->item[j].byEventType == 3 )
        ++v9;
    }
    v13 = pRecva->nNum + v9;
    v7 = _STORAGE_LIST::GetNumEmptyCon((_STORAGE_LIST *)&pOnea->m_Param.m_dbInven.m_nListNum);
    if ( v13 <= v7 )
    {
      v11 = CashItemRemoteStore::_buybygold_check_coupon(v14, pOnea, pRecva, pSheeta);
      if ( v11 )
        result = (unsigned int)v11;
      else
        result = 0i64;
    }
    else
    {
      result = 16i64;
    }
  }
  return result;
}
