/*
 * Function: ?Init@CGravityStoneRegener@@QEAA_NIGPEAVCMapData@@@Z
 * Address: 0x14012E590
 */

char __fastcall CGravityStoneRegener::Init(CGravityStoneRegener *this, unsigned int uiMapInx, unsigned __int16 wInx, CMapData *pkMap)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // rax@5
  GUILD_BATTLE::CGuildBattleLogger *v7; // rax@8
  char result; // al@8
  GUILD_BATTLE::CGuildBattleLogger *v9; // rax@10
  GUILD_BATTLE::CGuildBattleLogger *v10; // rax@12
  GUILD_BATTLE::CGuildBattleLogger *v11; // rax@14
  __int64 v12; // [sp+0h] [bp-408h]@1
  DWORD nSize[2]; // [sp+20h] [bp-3E8h]@10
  LPCSTR lpFileName; // [sp+28h] [bp-3E0h]@10
  char *v15; // [sp+30h] [bp-3D8h]@10
  const char *v16; // [sp+38h] [bp-3D0h]@10
  _object_id pID; // [sp+44h] [bp-3C4h]@4
  char Dst; // [sp+70h] [bp-398h]@4
  char KeyName; // [sp+190h] [bp-278h]@4
  char Dest; // [sp+2B0h] [bp-158h]@4
  _dummy_position *v21; // [sp+3C0h] [bp-48h]@7
  _dummy_position *v22; // [sp+3C8h] [bp-40h]@4
  __int64 v23; // [sp+3D0h] [bp-38h]@4
  _dummy_position *v24; // [sp+3D8h] [bp-30h]@5
  unsigned int v25; // [sp+3E0h] [bp-28h]@8
  unsigned int v26; // [sp+3E4h] [bp-24h]@10
  unsigned int v27; // [sp+3E8h] [bp-20h]@12
  unsigned int v28; // [sp+3ECh] [bp-1Ch]@14
  unsigned __int64 v29; // [sp+3F0h] [bp-18h]@4
  CGravityStoneRegener *v30; // [sp+410h] [bp+8h]@1
  unsigned int v31; // [sp+418h] [bp+10h]@1
  unsigned __int16 v32; // [sp+420h] [bp+18h]@1
  CMapData *v33; // [sp+428h] [bp+20h]@1

  v33 = pkMap;
  v32 = wInx;
  v31 = uiMapInx;
  v30 = this;
  v4 = &v12;
  for ( i = 256i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v23 = -2i64;
  v29 = (unsigned __int64)&v12 ^ _security_cookie;
  _object_id::_object_id(&pID, 0, 8, wInx);
  CGameObject::Init((CGameObject *)&v30->vfptr, &pID);
  memset_0(&Dst, 0, 0xFFui64);
  memset_0(&KeyName, 0, 0xFFui64);
  sprintf(&Dest, "Map%u", v31);
  v22 = (_dummy_position *)operator new(0x9Cui64);
  if ( v22 )
  {
    _dummy_position::_dummy_position(v22);
    v24 = (_dummy_position *)v6;
  }
  else
  {
    v24 = 0i64;
  }
  v21 = v24;
  v30->m_pkRegenPos = v24;
  if ( v30->m_pkRegenPos )
  {
    Dst = 0;
    sprintf(&KeyName, "BallRegenDummyName%d", v30->m_ObjID.m_wIndex);
    GetPrivateProfileStringA(&Dest, &KeyName, "X", &Dst, 0xFFu, "./Initialize/NormalGuildBattle.ini");
    if ( Dst == 88 )
    {
      v26 = v32;
      v9 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v16 = "./Initialize/NormalGuildBattle.ini";
      v15 = &Dst;
      lpFileName = &KeyName;
      *(_QWORD *)nSize = &Dest;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v9,
        "CGravityStoneRegener::Init( %u, %d, pkMap ) : GetPrivateProfileString( %s, %s, X, %s, 255, %s ) == 'X'!",
        v31,
        v26);
      result = 0;
    }
    else if ( CMapData::LoadDummy(v33, &Dst, v30->m_pkRegenPos) )
    {
      v30->m_iPortalInx = CMapData::GetPortalInx(v33, v30->m_pkRegenPos->m_szCode);
      if ( v30->m_iPortalInx >= 0 )
      {
        v30->m_eState = 0;
        result = 1;
      }
      else
      {
        v28 = v32;
        v11 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        *(_QWORD *)nSize = v30->m_pkRegenPos;
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v11,
          "CGravityStoneRegener::Init( %u, %d, pkMap )pkMap->GetPortalInx( %s ) Fail!",
          v31,
          v28);
        result = 0;
      }
    }
    else
    {
      v27 = v32;
      v10 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      *(_QWORD *)nSize = &Dst;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v10,
        "CGravityStoneRegener::Init( %u, %d, pkMap ) : pkMap->LoadDummy( %s, m_pkRegenPos ) Fail!",
        v31,
        v27);
      result = 0;
    }
  }
  else
  {
    v25 = v32;
    v7 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v7,
      "CGravityStoneRegener::Init( %u, %d, pkMap ) : new _dummy_position NULL!",
      v31,
      v25);
    result = 0;
  }
  return result;
}
