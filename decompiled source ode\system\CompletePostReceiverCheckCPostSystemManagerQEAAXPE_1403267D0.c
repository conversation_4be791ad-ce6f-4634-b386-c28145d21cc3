/*
 * Function: ?CompletePostReceiverCheck@CPostSystemManager@@QEAAXPEAD@Z
 * Address: 0x1403267D0
 */

void __usercall CPostSystemManager::CompletePostReceiverCheck(CPostSystemManager *this@<rcx>, char *pData@<rdx>, signed __int64 a3@<rax>)
{
  void *v3; // rsp@1
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@12
  CPostData *v7; // rax@12
  int v8; // eax@12
  int v9; // eax@22
  __int64 v10; // [sp-20h] [bp-1368h]@1
  char *wszTitle; // [sp+0h] [bp-1348h]@12
  char *wszContent; // [sp+8h] [bp-1340h]@12
  _INVENKEY Key[2]; // [sp+10h] [bp-1338h]@12
  unsigned __int64 dwDur; // [sp+18h] [bp-1330h]@12
  unsigned int dwUpt[2]; // [sp+20h] [bp-1328h]@12
  unsigned int dwGold; // [sp+28h] [bp-1320h]@12
  unsigned int dwPSSerial[2]; // [sp+30h] [bp-1318h]@12
  unsigned int byState[2]; // [sp+38h] [bp-1310h]@12
  int *nNumber; // [sp+40h] [bp-1308h]@12
  unsigned __int64 lnUID; // [sp+48h] [bp-1300h]@12
  char *v21; // [sp+50h] [bp-12F8h]@4
  _qry_case_post_send v22; // [sp+70h] [bp-12D8h]@4
  char v23; // [sp+12C4h] [bp-84h]@6
  unsigned int j; // [sp+12C8h] [bp-80h]@4
  char *v25; // [sp+12D0h] [bp-78h]@6
  CPostData *v26; // [sp+12D8h] [bp-70h]@6
  CPlayer *v27; // [sp+12E0h] [bp-68h]@7
  int nIndex; // [sp+12E8h] [bp-60h]@12
  int v29; // [sp+12F4h] [bp-54h]@12
  CPostData *pPost; // [sp+1308h] [bp-40h]@12
  bool v31; // [sp+1310h] [bp-38h]@15
  char v32; // [sp+1311h] [bp-37h]@15
  CPostStorage *v33; // [sp+1320h] [bp-28h]@12
  bool v34; // [sp+1328h] [bp-20h]@15
  char v35; // [sp+1329h] [bp-1Fh]@15
  unsigned __int64 v36; // [sp+1330h] [bp-18h]@4
  CPostSystemManager *v37; // [sp+1350h] [bp+8h]@1
  char *v38; // [sp+1358h] [bp+10h]@1

  v38 = pData;
  v37 = this;
  v3 = alloca(a3);
  v4 = &v10;
  for ( i = 1240i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v36 = (unsigned __int64)&v10 ^ _security_cookie;
  v21 = pData;
  _qry_case_post_send::_qry_case_post_send(&v22);
  for ( j = 0; j < *(_DWORD *)v21; ++j )
  {
    v25 = &v21[32 * j + 4];
    v26 = &v37->m_PostData[*(_DWORD *)v25];
    v23 = 0;
    if ( !v21[32 * j + 32] )
    {
      v27 = GetPtrPlayerFromSerial(&g_Player, 2532, *((_DWORD *)v25 + 6));
      if ( v27 )
      {
        if ( v27->m_bPostLoad && v27->m_bOper )
        {
          if ( CPostStorage::GetSize(&v27->m_Param.m_PostStorage) < 50 )
          {
            nIndex = -1;
            v29 = -1;
            CPostData::SetState(v26, 2);
            v33 = &v27->m_Param.m_PostStorage;
            lnUID = v26->m_lnUID;
            nNumber = &v29;
            LOBYTE(byState[0]) = v26->m_byState;
            dwPSSerial[0] = 0;
            dwGold = v26->m_dwGold;
            dwUpt[0] = v26->m_dwUpt;
            dwDur = v26->m_dwDur;
            Key[0] = v26->m_Key;
            wszContent = v26->m_wszContent;
            wszTitle = v26->m_wszTitle;
            v6 = CPostStorage::AddNewPost(
                   &v27->m_Param.m_PostStorage,
                   v26->m_dwSenderSerial,
                   v26->m_wszSendName,
                   v26->m_wszRecvName,
                   v26->m_wszTitle,
                   v26->m_wszContent,
                   Key[0],
                   dwDur,
                   dwUpt[0],
                   dwGold,
                   0,
                   byState[0],
                   &v29,
                   lnUID);
            nIndex = v6;
            v7 = CPostStorage::GetPostDataFromInx(&v27->m_Param.m_PostStorage, v6);
            pPost = v7;
            v8 = _INVENKEY::CovDBKey(&v7->m_Key);
            lnUID = pPost->m_lnUID;
            LODWORD(nNumber) = pPost->m_dwGold;
            byState[0] = pPost->m_dwUpt;
            *(_QWORD *)dwPSSerial = pPost->m_dwDur;
            dwGold = v8;
            *(_QWORD *)dwUpt = (char *)pPost + 67;
            dwDur = (unsigned __int64)pPost->m_wszTitle;
            *(_QWORD *)&Key[0].bySlotIndex = (char *)pPost + 29;
            wszContent = pPost->m_wszSendName;
            LOBYTE(wszTitle) = pPost->m_byState;
            CUserDB::SetNewDBPostData(
              v27->m_pUserDB,
              nIndex,
              0,
              pPost->m_nNumber,
              (char)wszTitle,
              pPost->m_wszSendName,
              pPost->m_wszRecvName,
              pPost->m_wszTitle,
              pPost->m_wszContent,
              v8,
              *(unsigned __int64 *)dwPSSerial,
              byState[0],
              (unsigned int)nNumber,
              lnUID);
            if ( _INVENKEY::IsFilled(&pPost->m_Key) || pPost->m_dwGold )
              CMgrAvatorItemHistory::post_receive(&CPlayer::s_MgrItemHistory, pPost, v27->m_szItemHistoryFileName);
            v34 = pPost->m_dwGold != 0;
            v31 = v34;
            v35 = _INVENKEY::IsFilled(&pPost->m_Key) != 0;
            v32 = v35;
            CPlayer::SendMsg_PostDelivery(v27, v29, nIndex, pPost->m_wszSendName, pPost->m_wszTitle, v35, v31, 0);
            CNetIndexList::PushNode_Back(&v37->m_listEmpty, *(_DWORD *)v25);
            v23 = 1;
          }
          else
          {
            v25[28] = 10;
          }
        }
        else
        {
          v25[28] = 8;
        }
      }
    }
    if ( !v23 )
    {
      lnUID = v26->m_lnUID;
      LODWORD(nNumber) = v26->m_dwGold;
      byState[0] = v26->m_dwUpt;
      *(_QWORD *)dwPSSerial = v26->m_dwDur;
      dwGold = (unsigned int)v26->m_Key;
      *(_QWORD *)dwUpt = (char *)v26 + 67;
      dwDur = (unsigned __int64)v26->m_wszTitle;
      *(_QWORD *)&Key[0].bySlotIndex = (char *)v26 + 29;
      wszContent = v26->m_wszSendName;
      LODWORD(wszTitle) = v26->m_dwSenderSerial;
      _qry_case_post_send::pushdata(
        &v22,
        *(_DWORD *)v25,
        v25[28],
        *((_DWORD *)v25 + 6),
        (unsigned int)wszTitle,
        v26->m_wszSendName,
        v26->m_wszRecvName,
        v26->m_wszTitle,
        v26->m_wszContent,
        (_INVENKEY)dwGold,
        *(unsigned __int64 *)dwPSSerial,
        byState[0],
        (unsigned int)nNumber,
        lnUID);
    }
  }
  if ( v22.dwCount )
  {
    v9 = _qry_case_post_send::size(&v22);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 78, (char *)&v22, v9);
  }
}
