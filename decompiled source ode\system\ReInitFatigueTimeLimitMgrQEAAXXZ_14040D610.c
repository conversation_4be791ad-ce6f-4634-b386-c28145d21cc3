/*
 * Function: ?ReInitFatigue@TimeLimitMgr@@QEAAXXZ
 * Address: 0x14040D610
 */

void __fastcall TimeLimitMgr::ReInitFatigue(TimeLimitMgr *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  TimeLimitMgr *v4; // [sp+20h] [bp+8h]@1

  v4 = this;
  v1 = &j;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  for ( j = 0; j < v4->m_wPeriodCnt; ++j )
    v4->m_pwFatigue[j] = 100 * v4->m_pwTime[j] / v4->m_pwTime[v4->m_wPeriodCnt - 1];
}
