/*
 * Function: ?CompleteCreate@CUnmannedTraderController@@QEAAXG@Z
 * Address: 0x140079D90
 */

void __fastcall CUnmannedTraderController::CompleteCreate(CUnmannedTraderController *this, unsigned __int16 wInx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfoTable *v4; // rax@4
  CUnmannedTraderTaxRateManager *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-28h]@1
  unsigned __int16 v7; // [sp+38h] [bp+10h]@1

  v7 = wInx;
  v2 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = CUnmannedTraderUserInfoTable::Instance();
  CUnmannedTraderUserInfoTable::CompleteCreate(v4, v7);
  v5 = CUnmannedTraderTaxRateManager::Instance();
  CUnmannedTraderTaxRateManager::CompleteCreate(v5, v7);
}
