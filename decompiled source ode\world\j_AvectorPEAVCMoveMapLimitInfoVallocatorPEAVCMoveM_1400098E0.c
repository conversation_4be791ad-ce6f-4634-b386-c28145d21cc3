/*
 * Function: j_??A?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEAAAEAPEAVCMoveMapLimitInfo@@_K@Z
 * Address: 0x1400098E0
 */

CMoveMapLimitInfo **__fastcall std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::operator[](std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, unsigned __int64 _Pos)
{
  return std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::operator[](this, _Pos);
}
