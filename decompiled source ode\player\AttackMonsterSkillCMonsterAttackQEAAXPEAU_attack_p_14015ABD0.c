/*
 * Function: ?AttackMonsterSkill@CMonsterAttack@@QEAAXPEAU_attack_param@@@Z
 * Address: 0x14015ABD0
 */

void __usercall CMonsterAttack::AttackMonsterSkill(CMonsterAttack *this@<rcx>, _attack_param *pParam@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@11
  float v6; // xmm0_4@17
  float v7; // xmm0_4@17
  signed int v8; // eax@23
  __int64 v9; // rax@34
  __int64 v10; // rdx@34
  float *v11; // rax@35
  __int64 v12; // rcx@35
  CCharacter **v13; // rax@36
  CCharacter **v14; // rcx@36
  __int64 v15; // rdx@36
  CCharacter **v16; // rcx@38
  CCharacter **v17; // rdx@38
  CCharacter **v18; // r8@38
  __int64 v19; // [sp+0h] [bp-98h]@1
  int nEffAttPower[2]; // [sp+20h] [bp-78h]@34
  int bUseEffBullet[2]; // [sp+28h] [bp-70h]@34
  int v22; // [sp+30h] [bp-68h]@36
  bool v23; // [sp+38h] [bp-60h]@36
  _base_fld *v24; // [sp+40h] [bp-58h]@4
  char v25; // [sp+48h] [bp-50h]@4
  int v26; // [sp+4Ch] [bp-4Ch]@5
  char v27; // [sp+50h] [bp-48h]@8
  float v28; // [sp+54h] [bp-44h]@17
  float v29; // [sp+58h] [bp-40h]@17
  int v30; // [sp+5Ch] [bp-3Ch]@23
  int nAttPower; // [sp+60h] [bp-38h]@33
  float v32; // [sp+64h] [bp-34h]@11
  float v33; // [sp+68h] [bp-30h]@17
  float v34; // [sp+6Ch] [bp-2Ch]@17
  float v35; // [sp+70h] [bp-28h]@23
  int v36; // [sp+74h] [bp-24h]@24
  int v37; // [sp+78h] [bp-20h]@27
  int v38; // [sp+7Ch] [bp-1Ch]@33
  int *v39; // [sp+80h] [bp-18h]@34
  CMonsterAttack *v40; // [sp+A0h] [bp+8h]@1

  v40 = this;
  v3 = &v19;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v40->m_bActiveSucc = 0;
  v40->m_nDamagedObjNum = 0;
  v40->m_bIsCrtAtt = 0;
  v40->m_pp = pParam;
  v24 = v40->m_pp->pFld;
  v25 = 1;
  CCharacter::BreakStealth(v40->m_pAttChar);
  if ( v40->m_pp->byEffectCode )
    v26 = *(_DWORD *)&v24[11].m_strCode[0];
  else
    v26 = *(_DWORD *)&v24[11].m_strCode[4 * (v40->m_pp->nLevel - 1)];
  if ( !v40->m_pp->pDst )
    goto LABEL_42;
  v27 = 0;
  if ( _effect_parameter::GetEff_State(&v40->m_pp->pDst->m_EP, 14) )
  {
    v27 = 1;
  }
  else
  {
    _effect_parameter::GetEff_Plus(&v40->m_pp->pDst->m_EP, 27);
    if ( a3 > 0.0 )
    {
      v5 = rand();
      a3 = (float)(v5 % 100);
      v32 = (float)(v5 % 100);
      _effect_parameter::GetEff_Plus(&v40->m_pp->pDst->m_EP, 27);
      if ( a3 > v32 )
        v27 = 1;
    }
  }
  if ( v27 )
  {
    if ( !v40->m_pp->bPassCount
      && !v40->m_pp->nClass
      && !((int (__fastcall *)(CCharacter *))v40->m_pp->pDst->vfptr->GetWeaponClass)(v40->m_pp->pDst) )
    {
      ((void (__fastcall *)(CCharacter *))v40->m_pp->pDst->vfptr->GetAttackRange)(v40->m_pp->pDst);
      v33 = a3;
      ((void (__fastcall *)(CCharacter *))v40->m_pAttChar->vfptr->GetWidth)(v40->m_pAttChar);
      v6 = v33 + (float)(a3 / 2.0);
      v34 = v6;
      _effect_parameter::GetEff_Plus(&v40->m_pp->pDst->m_EP, 4);
      v7 = v34 + v6;
      v28 = v7;
      GetSqrt(v40->m_pp->pDst->m_fCurPos, v40->m_pAttChar->m_fCurPos);
      v29 = v7;
      a3 = v28;
      if ( v28 >= v29 )
      {
        v40->m_DamList[0].m_pChar = v40->m_pp->pDst;
        v40->m_DamList[0].m_nDamage = -1;
        v40->m_nDamagedObjNum = 1;
        CCharacter::SendMsg_AttackActEffect(v40->m_pAttChar, 0, v40->m_pp->pDst);
        return;
      }
    }
    _effect_parameter::GetEff_Plus(&v40->m_pp->pDst->m_EP, 27);
    if ( a3 > 0.0 )
    {
      v40->m_DamList[0].m_pChar = v40->m_pp->pDst;
      v40->m_DamList[0].m_nDamage = 0;
      v40->m_nDamagedObjNum = 1;
      return;
    }
  }
  if ( _effect_parameter::GetEff_State(&v40->m_pp->pDst->m_EP, 8) )
  {
    v25 = 0;
  }
  else
  {
    _effect_parameter::GetEff_Plus(&v40->m_pAttChar->m_EP, 30);
    v35 = a3 + 150.0;
    v8 = ((int (__fastcall *)(CCharacter *))v40->m_pp->pDst->vfptr->GetAvoidRate)(v40->m_pp->pDst);
    v30 = (signed int)ffloor(v35 - (float)v8);
    if ( v30 <= 0 )
      v36 = 0;
    else
      v36 = v30;
    v30 = v36;
    if ( v36 >= 100 )
      v37 = 100;
    else
      v37 = v30;
    v30 = v37;
    if ( rand() % 100 >= v30 )
      v25 = 0;
  }
  if ( v25 )
  {
LABEL_42:
    nAttPower = v40->m_pp->nAddAttPnt + CMonsterAttack::_CalcMonSkillAttPnt(v40);
    v38 = v26;
    switch ( v26 )
    {
      case 5:
        v9 = *(_DWORD *)&v24[4].m_strCode[60];
        v10 = *(_DWORD *)&v24[4].m_strCode[60];
        v39 = s_Mon_nLimitDist;
        LOBYTE(bUseEffBullet[0]) = 0;
        nEffAttPower[0] = 0;
        CAttack::FlashDamageProc(
          (CAttack *)&v40->m_pp,
          s_Mon_nLimitDist[v10],
          nAttPower,
          s_Mon_nLimitAngle[0][v9],
          0,
          0);
        goto LABEL_39;
      case 4:
      case 6:
        v11 = v40->m_pp->fArea;
        v12 = *(_DWORD *)&v24[4].m_strCode[60];
        LOBYTE(bUseEffBullet[0]) = 0;
        nEffAttPower[0] = 0;
        CAttack::AreaDamageProc((CAttack *)&v40->m_pp, s_Mon_nLimitRadius[v12], nAttPower, v11, 0, 0);
        goto LABEL_39;
      case 7:
        v13 = &v40->m_pp->pDst;
        v14 = &v40->m_pp->pDst;
        v15 = *(_DWORD *)&v24[4].m_strCode[60];
        v23 = 0;
        v22 = 0;
        bUseEffBullet[0] = *((_DWORD *)v13 + 9);
        nEffAttPower[0] = *((_DWORD *)v14 + 10);
        CAttack::SectorDamageProc(
          (CAttack *)&v40->m_pp,
          *(_DWORD *)&v24[4].m_strCode[60],
          nAttPower,
          s_Mon_nLimitAngle[0][v15],
          nEffAttPower[0],
          bUseEffBullet[0],
          0,
          0);
        goto LABEL_39;
      case 0:
      case 1:
      case 2:
      case 3:
        if ( v40->m_pp->pDst )
        {
          v40->m_DamList[0].m_pChar = v40->m_pp->pDst;
          v16 = &v40->m_pp->pDst;
          v17 = &v40->m_pp->pDst;
          v18 = &v40->m_pp->pDst;
          LOBYTE(bUseEffBullet[0]) = v40->m_pp->bBackAttack;
          *(_QWORD *)nEffAttPower = *v16;
          v40->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                          v40->m_pAttChar,
                                          nAttPower,
                                          *((_DWORD *)v18 + 2),
                                          *((_DWORD *)v17 + 3),
                                          *(CCharacter **)nEffAttPower,
                                          bUseEffBullet[0]);
          v40->m_nDamagedObjNum = 1;
        }
LABEL_39:
        CAttack::CalcAvgDamage((CAttack *)&v40->m_pp);
        break;
      default:
        return;
    }
  }
  else
  {
    v40->m_DamList[0].m_pChar = v40->m_pp->pDst;
    v40->m_DamList[0].m_nDamage = 0;
    v40->m_nDamagedObjNum = 1;
  }
}
