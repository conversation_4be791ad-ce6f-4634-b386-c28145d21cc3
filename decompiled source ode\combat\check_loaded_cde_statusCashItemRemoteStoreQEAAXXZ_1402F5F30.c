/*
 * Function: ?check_loaded_cde_status@CashItemRemoteStore@@QEAAXXZ
 * Address: 0x1402F5F30
 */

void __fastcall CashItemRemoteStore::check_loaded_cde_status(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  __time32_t Time; // [sp+24h] [bp-24h]@6
  int v5; // [sp+34h] [bp-14h]@10
  CashItemRemoteStore *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_cde.m_ini.m_bUseCashDiscount )
  {
    _time32(&Time);
    if ( Time <= v6->m_cde.m_ini.m_cdeTime[1] )
    {
      if ( Time < v6->m_cde.m_ini.m_cdeTime[0] || Time > v6->m_cde.m_ini.m_cdeTime[1] )
      {
        if ( Time < v6->m_cde.m_ini.m_cdeTime[0] )
          CashItemRemoteStore::set_cde_status(v6, 1);
      }
      else
      {
        v5 = v6->m_cde.m_ini.m_cdeTime[1] - Time;
        if ( v5 > 0 )
        {
          if ( v5 <= 0 || v5 > v6->m_cde.m_cde_inform_before[1] )
          {
            if ( v5 <= v6->m_cde.m_cde_inform_before[1] || v5 > v6->m_cde.m_cde_inform_before[0] )
            {
              if ( v5 <= v6->m_cde.m_cde_inform_before[0] )
                CashItemRemoteStore::set_cde_status(v6, 0);
              else
                CashItemRemoteStore::set_cde_status(v6, 2);
            }
            else
            {
              CashItemRemoteStore::set_cde_status(v6, 3);
            }
          }
          else
          {
            CashItemRemoteStore::set_cde_status(v6, 4);
          }
        }
        else
        {
          CashItemRemoteStore::set_cde_status(v6, 0);
        }
      }
    }
    else
    {
      CashItemRemoteStore::set_cde_status(v6, 0);
    }
    CashItemRemoteStore::SetNextDiscountEventTime(v6);
  }
  else
  {
    CashItemRemoteStore::set_cde_status(v6, 0);
  }
}
