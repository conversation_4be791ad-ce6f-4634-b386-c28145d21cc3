/*
 * Function: ?SendMsg_UnitFrameBuyResult@CPlayer@@QEAAXEEEGGPEAK@Z
 * Address: 0x1400DAC10
 */

void __fastcall CPlayer::SendMsg_UnitFrameBuyResult(CPlayer *this, char byRetCode, char byFrameCode, char byUnitSlotIndex, unsigned __int16 wKeyIndex, unsigned __int16 wKeySerial, unsigned int *pdwConsumMoney)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-B8h]@1
  char szMsg; // [sp+38h] [bp-80h]@4
  char v11; // [sp+39h] [bp-7Fh]@4
  char v12; // [sp+3Ah] [bp-7Eh]@4
  char v13; // [sp+3Bh] [bp-7Dh]@4
  unsigned __int16 v14; // [sp+3Ch] [bp-7Ch]@4
  unsigned int v15; // [sp+3Eh] [bp-7Ah]@4
  unsigned int v16; // [sp+42h] [bp-76h]@4
  unsigned int v17; // [sp+5Ah] [bp-5Eh]@4
  unsigned int v18; // [sp+5Eh] [bp-5Ah]@4
  char pbyType; // [sp+94h] [bp-24h]@4
  char v20; // [sp+95h] [bp-23h]@4
  CPlayer *v21; // [sp+C0h] [bp+8h]@1

  v21 = this;
  v7 = &v9;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  szMsg = byRetCode;
  v11 = byFrameCode;
  v12 = byUnitSlotIndex;
  v13 = wKeyIndex;
  v14 = wKeySerial;
  v15 = CPlayerDB::GetDalant(&v21->m_Param);
  v16 = CPlayerDB::GetGold(&v21->m_Param);
  v17 = *pdwConsumMoney;
  v18 = pdwConsumMoney[1];
  pbyType = 23;
  v20 = 2;
  CNetProcess::LoadSendMsg(unk_1414F2088, v21->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x3Eu);
}
