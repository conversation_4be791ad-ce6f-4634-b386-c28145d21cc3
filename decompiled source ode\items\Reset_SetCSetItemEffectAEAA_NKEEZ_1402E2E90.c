/*
 * Function: ?Reset_Set@CSetItemEffect@@AEAA_NKEE@Z
 * Address: 0x1402E2E90
 */

bool __fastcall CSetItemEffect::Reset_Set(CSetItemEffect *this, unsigned int dwSetItem, char bySetItemNum, char bySetEffectNum)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@11
  __int64 v7; // [sp+0h] [bp-48h]@1
  char bySetEffectNuma; // [sp+20h] [bp-28h]@8
  int j; // [sp+30h] [bp-18h]@4
  CSetItemEffect *v10; // [sp+50h] [bp+8h]@1
  unsigned int dwSetItema; // [sp+58h] [bp+10h]@1
  char v12; // [sp+60h] [bp+18h]@1
  char v13; // [sp+68h] [bp+20h]@1

  v13 = bySetEffectNum;
  v12 = bySetItemNum;
  dwSetItema = dwSetItem;
  v10 = this;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  for ( j = 0; j < 6; ++j )
  {
    if ( v10->m_setCount[j].m_bCheckSetEffect == 1 && v10->m_setCount[j].m_dwSetItem == dwSetItem )
    {
      bySetEffectNuma = v10->m_setCount[j].m_bySetEffectNum;
      CSetItemEffect::SetResetInfo(v10, 1, dwSetItem, v10->m_setCount[j].m_bySetItemNum, bySetEffectNuma);
      break;
    }
  }
  if ( CSetItemEffect::Detach_Set(v10, dwSetItema) )
    result = CSetItemEffect::Attach_Set(v10, dwSetItema, v12, v13) != 0;
  else
    result = 0;
  return result;
}
