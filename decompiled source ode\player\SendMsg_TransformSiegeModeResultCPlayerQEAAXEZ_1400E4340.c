/*
 * Function: ?SendMsg_TransformSiegeModeResult@CPlayer@@QEAAXE@Z
 * Address: 0x1400E4340
 */

void __fastcall CPlayer::SendMsg_TransformSiegeModeResult(CPlayer *this, char byRetCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _STORAGE_LIST::_db_con *v4; // rax@5
  __int64 v5; // [sp+0h] [bp-C8h]@1
  char szMsg; // [sp+34h] [bp-94h]@4
  char pbyType; // [sp+54h] [bp-74h]@4
  char v8; // [sp+55h] [bp-73h]@4
  char v9[4]; // [sp+78h] [bp-50h]@5
  unsigned __int16 v10; // [sp+7Ch] [bp-4Ch]@5
  char v11; // [sp+7Eh] [bp-4Ah]@5
  __int16 v12; // [sp+7Fh] [bp-49h]@5
  char *v13; // [sp+98h] [bp-30h]@5
  char v14; // [sp+A4h] [bp-24h]@5
  char v15; // [sp+A5h] [bp-23h]@5
  CPlayer *v16; // [sp+D0h] [bp+8h]@1
  char v17; // [sp+D8h] [bp+10h]@1

  v17 = byRetCode;
  v16 = this;
  v2 = &v5;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  szMsg = byRetCode;
  pbyType = 28;
  v8 = 2;
  CNetProcess::LoadSendMsg(unk_1414F2088, v16->m_ObjID.m_wIndex, &pbyType, &szMsg, 1u);
  if ( !v17 )
  {
    v11 = v16->m_pSiegeItem->m_wItemIndex;
    v4 = v16->m_Param.m_dbEquip.m_pStorageList;
    v13 = &v4[6].m_bLoad;
    v10 = v4[6].m_wItemIndex;
    *(_DWORD *)v9 = v16->m_dwObjSerial;
    v12 = CPlayer::GetVisualVer(v16);
    v14 = 28;
    v15 = 3;
    CGameObject::CircleReport((CGameObject *)&v16->vfptr, &v14, v9, 9, 0);
  }
}
