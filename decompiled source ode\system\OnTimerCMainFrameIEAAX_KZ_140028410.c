/*
 * Function: ?OnTimer@CMainFrame@@IEAAX_K@Z
 * Address: 0x140028410
 */

void __fastcall CMainFrame::OnTimer(CMainFrame *this, unsigned __int64 nIDEvent)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@5
  const char *v5; // rax@5
  __int64 v6; // r9@5
  const char *v7; // rax@5
  unsigned int v8; // eax@5
  const char *v9; // rax@5
  unsigned int v10; // eax@5
  const char *v11; // rax@5
  unsigned int v12; // eax@5
  const char *v13; // rax@5
  __int64 v14; // r8@5
  const char *v15; // rax@5
  const char *v16; // rax@5
  const char *v17; // rax@6
  __int64 v18; // [sp+0h] [bp-58h]@1
  char v19; // [sp+28h] [bp-30h]@4
  __int64 v20; // [sp+38h] [bp-20h]@4
  int v21; // [sp+40h] [bp-18h]@5
  int v22; // [sp+44h] [bp-14h]@5
  int v23; // [sp+48h] [bp-10h]@5
  CMainFrame *v24; // [sp+60h] [bp+8h]@1
  unsigned __int64 v25; // [sp+68h] [bp+10h]@1

  v25 = nIDEvent;
  v24 = this;
  v2 = &v18;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v20 = -2i64;
  ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>(&v19);
  if ( !CMainThread::IsExcuteService(&g_Main) )
  {
    v4 = CFrameRate::GetFPS(&stru_1415B7008);
    ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(&v19, "M: %d", v4);
    LODWORD(v5) = ATL::CSimpleStringT<char,1>::operator char const *(&v19);
    CStatusBar::SetPaneText(&v24->m_wndStatusBar, 0, v5, 1);
    v6 = unk_1414F2090->m_nTryConnectCount;
    ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(
      &v19,
      "%d/%d",
      unk_1414F2088->m_nTryConnectCount);
    LODWORD(v7) = ATL::CSimpleStringT<char,1>::operator char const *(&v19);
    CStatusBar::SetPaneText(&v24->m_wndStatusBar, 1, v7, 1);
    v21 = CNetIndexList::size(&stru_1799C5990);
    v8 = CFrameRate::GetFPS(&stru_1415B7028);
    ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(&v19, "D: %d - %d", v8);
    LODWORD(v9) = ATL::CSimpleStringT<char,1>::operator char const *(&v19);
    CStatusBar::SetPaneText(&v24->m_wndStatusBar, 2, v9, 1);
    v22 = CMgrAvatorItemHistory::GetTotalWaitSize(&CPlayer::s_MgrItemHistory);
    v10 = CFrameRate::GetFPS(&stru_140CE3298);
    ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(&v19, "I: %d - %d", v10);
    LODWORD(v11) = ATL::CSimpleStringT<char,1>::operator char const *(&v19);
    CStatusBar::SetPaneText(&v24->m_wndStatusBar, 3, v11, 1);
    v23 = CMgrAvatorLvHistory::GetTotalWaitSize(&CPlayer::s_MgrLvHistory);
    v12 = CFrameRate::GetFPS(&stru_140E4C308);
    ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(&v19, "L: %d - %d", v12);
    LODWORD(v13) = ATL::CSimpleStringT<char,1>::operator char const *(&v19);
    CStatusBar::SetPaneText(&v24->m_wndStatusBar, 4, v13, 1);
    ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(&v19, "Q: X - X", v14);
    LODWORD(v15) = ATL::CSimpleStringT<char,1>::operator char const *(&v19);
    CStatusBar::SetPaneText(&v24->m_wndStatusBar, 5, v15, 1);
    ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(
      &v19,
      "%d/%d",
      (unsigned int)CPlayer::s_dwAbnormalCloseCount);
    LODWORD(v16) = ATL::CSimpleStringT<char,1>::operator char const *(&v19);
    CStatusBar::SetPaneText(&v24->m_wndStatusBar, 7, v16, 1);
  }
  ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(&v19, "OPN: %d - %d", unk_1799C608D);
  LODWORD(v17) = ATL::CSimpleStringT<char,1>::operator char const *(&v19);
  CStatusBar::SetPaneText(&v24->m_wndStatusBar, 6, v17, 1);
  CWnd::OnTimer((CWnd *)&v24->vfptr, v25);
  ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::~CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>(&v19);
}
