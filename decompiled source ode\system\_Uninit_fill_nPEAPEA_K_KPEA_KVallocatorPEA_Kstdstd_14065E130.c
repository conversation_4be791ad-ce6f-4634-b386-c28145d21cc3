/*
 * Function: ??$_Uninit_fill_n@PEAPEA_K_KPEA_KV?$allocator@PEA_K@std@@@std@@YAXPEAPEA_K_KAEBQEA_KAEAV?$allocator@PEA_K@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14065E130
 */

int std::_Uninit_fill_n<unsigned __int64 * *,unsigned __int64,unsigned __int64 *,std::allocator<unsigned __int64 *>>()
{
  return stdext::unchecked_fill_n<unsigned __int64 * *,unsigned __int64,unsigned __int64 *>();
}
