/*
 * Function: ?OnTimer@CObjectTab@@IEAAX_K@Z
 * Address: 0x140033390
 */

void __fastcall CObjectTab::OnTimer(CObjectTab *this, unsigned __int64 nIDEvent)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  CObjectTab *v5; // [sp+40h] [bp+8h]@1
  unsigned __int64 v6; // [sp+48h] [bp+10h]@1

  v6 = nIDEvent;
  v5 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CMsgData::PackingMsg(&stru_1415B7048, 0x3EEu, 0, 0, 0);
  CWnd::OnTimer((CWnd *)&v5->vfptr, v6);
}
