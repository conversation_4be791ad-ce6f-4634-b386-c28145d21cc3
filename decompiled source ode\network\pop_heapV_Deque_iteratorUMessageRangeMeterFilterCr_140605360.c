/*
 * Function: ??$pop_heap@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@@std@@YAXV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@0@Z
 * Address: 0x140605360
 */

int std::pop_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>()
{
  signed __int64 v0; // rax@1
  char v2; // [sp+20h] [bp-B8h]@2
  char *v3; // [sp+40h] [bp-98h]@2
  char v4; // [sp+48h] [bp-90h]@2
  char *v5; // [sp+68h] [bp-70h]@2
  char v6; // [sp+70h] [bp-68h]@2
  char *v7; // [sp+90h] [bp-48h]@2
  __int64 v8; // [sp+98h] [bp-40h]@1
  __int64 v9; // [sp+A0h] [bp-38h]@2
  __int64 v10; // [sp+A8h] [bp-30h]@2
  __int64 v11; // [sp+B0h] [bp-28h]@2
  __int64 v12; // [sp+B8h] [bp-20h]@2
  __int64 v13; // [sp+C0h] [bp-18h]@2

  v8 = -2i64;
  LODWORD(v0) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-();
  if ( v0 > 1 )
  {
    v3 = &v2;
    v5 = &v4;
    v7 = &v6;
    v9 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)&v2);
    v10 = std::_Val_type<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>();
    v11 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v5);
    v12 = v11;
    v13 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v7);
    std::_Pop_heap_0<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,CryptoPP::MeterFilter::MessageRange>(
      v13,
      v12,
      v10);
  }
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
}
