/*
 * Function: ?dtor$0@?0??AddToRunnables@InternalContextBase@details@Concurrency@@MEAAXVlocation@3@@Z@4HA_8
 * Address: 0x14065A830
 */

void __fastcall `Concurrency::details::InternalContextBase::AddToRunnables'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  std::_Deque_iterator<unsigned int,std::allocator<unsigned int>,0>::~_Deque_iterator<unsigned int,std::allocator<unsigned int>,0>(*(std::_Ranit<unsigned int,__int64,unsigned int const *,unsigned int const &> **)(a2 + 264));
}
