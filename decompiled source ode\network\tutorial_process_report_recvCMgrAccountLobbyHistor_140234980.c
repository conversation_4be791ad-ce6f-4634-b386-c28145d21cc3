/*
 * Function: ?tutorial_process_report_recv@CMgrAccountLobbyHistory@@QEAAXPEAD@Z
 * Address: 0x140234980
 */

void __fastcall CMgrAccountLobbyHistory::tutorial_process_report_recv(CMgrAccountLobbyHistory *this, char *pszFileName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMgrAccountLobbyHistory *v5; // [sp+30h] [bp+8h]@1
  char *pszFileNamea; // [sp+38h] [bp+10h]@1

  pszFileNamea = pszFileName;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  sLData[0] = 0;
  sprintf_s<10240>(
    (char (*)[10240])sLBuf,
    "Tutorial Process Report Received [%s %s]\r\n",
    v5->m_szCurDate,
    v5->m_szCurTime);
  strcat_s<20000>((char (*)[20000])sLData, sLBuf);
  strcat_s<20000>((char (*)[20000])sLData, "\r\n\t============\r\n\r\n");
  CMgrAccountLobbyHistory::WriteFile(v5, pszFileNamea, sLData);
}
