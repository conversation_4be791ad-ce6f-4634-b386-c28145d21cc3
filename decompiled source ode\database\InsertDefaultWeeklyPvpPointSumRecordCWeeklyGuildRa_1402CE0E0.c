/*
 * Function: ?InsertDefaultWeeklyPvpPointSumRecord@CWeeklyGuildRankManager@@AEAA_NXZ
 * Address: 0x1402CE0E0
 */

char __fastcall CWeeklyGuildRankManager::InsertDefaultWeeklyPvpPointSumRecord(CWeeklyGuildRankManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v3; // rax@5
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1

  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CRFWorldDatabase::Insert_DefaultWeeklyGuildPvpPointSumRecord(pkDB) )
  {
    result = 1;
  }
  else
  {
    v3 = CPvpUserAndGuildRankingSystem::Instance();
    CPvpUserAndGuildRankingSystem::Log(
      v3,
      "CWeeklyGuildRankManager::InsertDefaultRecord() : g_Main.m_pWorldDB->Insert_DefaultRecordWeeklyGuildPvpPointSum() Fail!");
    result = 0;
  }
  return result;
}
