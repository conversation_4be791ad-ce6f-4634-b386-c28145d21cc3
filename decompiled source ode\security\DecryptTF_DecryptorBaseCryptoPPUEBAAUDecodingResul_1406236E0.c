/*
 * Function: ?Decrypt@TF_DecryptorBase@CryptoPP@@UEBA?AUDecodingResult@2@AEAVRandomNumberGenerator@2@PEBE_KPEAEAEBVNameValuePairs@2@@Z
 * Address: 0x1406236E0
 */

struct CryptoPP::DecodingResult *__fastcall CryptoPP::TF_DecryptorBase::Decrypt(CryptoPP::TF_DecryptorBase *this, struct CryptoPP::DecodingResult *retstr, struct CryptoPP::RandomNumberGenerator *a3, const unsigned __int8 *a4, unsigned __int64 a5, unsigned __int8 *a6, const struct CryptoPP::NameValuePairs *a7)
{
  unsigned __int64 v7; // rax@1
  __int64 v8; // rax@1
  unsigned __int64 v9; // rax@1
  unsigned __int64 v10; // rax@1
  CryptoPP::Integer *v11; // rax@2
  unsigned __int8 *v12; // rax@3
  __int64 *v13; // rax@3
  __int64 v14; // rax@3
  char *v15; // rax@3
  CryptoPP::Integer v17; // [sp+30h] [bp-C8h]@1
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v18; // [sp+58h] [bp-A0h]@1
  CryptoPP::Integer v19; // [sp+70h] [bp-88h]@1
  __int64 v20; // [sp+98h] [bp-60h]@1
  CryptoPP::TF_Base<CryptoPP::TrapdoorFunctionInverse,CryptoPP::PK_EncryptionMessageEncodingMethod>Vtbl *v21; // [sp+A0h] [bp-58h]@1
  __int64 v22; // [sp+A8h] [bp-50h]@1
  CryptoPP::Integer *v23; // [sp+B0h] [bp-48h]@1
  CryptoPP::Integer *v24; // [sp+B8h] [bp-40h]@1
  unsigned __int64 v25; // [sp+C0h] [bp-38h]@1
  unsigned __int64 v26; // [sp+C8h] [bp-30h]@3
  CryptoPP::TF_Base<CryptoPP::TrapdoorFunctionInverse,CryptoPP::PK_EncryptionMessageEncodingMethod>Vtbl *v27; // [sp+D0h] [bp-28h]@3
  __int64 *v28; // [sp+D8h] [bp-20h]@3
  __int64 v29; // [sp+E0h] [bp-18h]@3
  __int64 v30; // [sp+E8h] [bp-10h]@3
  CryptoPP::TF_DecryptorBase *v31; // [sp+100h] [bp+8h]@1
  struct CryptoPP::DecodingResult *v32; // [sp+108h] [bp+10h]@1
  struct CryptoPP::RandomNumberGenerator *v33; // [sp+110h] [bp+18h]@1
  unsigned __int8 *v34; // [sp+118h] [bp+20h]@1

  v34 = (unsigned __int8 *)a4;
  v33 = a3;
  v32 = retstr;
  v31 = this;
  v20 = -2i64;
  LODWORD(v7) = CryptoPP::TF_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::TF_Base<CryptoPP::TrapdoorFunctionInverse,CryptoPP::PK_EncryptionMessageEncodingMethod>>::PaddedBlockByteLength();
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v18,
    v7);
  v21 = v31->vfptr;
  LODWORD(v8) = ((int (__fastcall *)(signed __int64))v21->GetTrapdoorFunctionInterface)((signed __int64)&v31->vfptr);
  v22 = v8;
  LODWORD(v9) = ((int (__fastcall *)(CryptoPP::TF_DecryptorBase *))v31->vfptr->FixedCiphertextLength)(v31);
  v23 = CryptoPP::Integer::Integer(&v19, v34, v9, 0);
  v24 = v23;
  (*(void (__fastcall **)(__int64, CryptoPP::Integer *, struct CryptoPP::RandomNumberGenerator *, CryptoPP::Integer *))(*(_QWORD *)v22 + 24i64))(
    v22,
    &v17,
    v33,
    v23);
  CryptoPP::Integer::~Integer(&v19);
  v25 = (unsigned int)CryptoPP::Integer::ByteCount(&v17);
  v10 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v18);
  if ( v25 > v10 )
  {
    LODWORD(v11) = CryptoPP::Integer::Zero();
    CryptoPP::Integer::operator=(&v17, v11);
  }
  v26 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v18);
  v12 = (unsigned __int8 *)CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v18);
  CryptoPP::Integer::Encode(&v17, v12, v26, 0);
  v27 = v31->vfptr;
  LODWORD(v13) = ((int (__fastcall *)(signed __int64))v27->GetMessageEncodingInterface)((signed __int64)&v31->vfptr);
  v28 = v13;
  LODWORD(v14) = CryptoPP::TF_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::TF_Base<CryptoPP::TrapdoorFunctionInverse,CryptoPP::PK_EncryptionMessageEncodingMethod>>::PaddedBlockBitLength(v31);
  v29 = v14;
  v15 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v18);
  v30 = *v28;
  (*(void (__fastcall **)(__int64 *, struct CryptoPP::DecodingResult *, char *, __int64))(v30 + 32))(v28, v32, v15, v29);
  CryptoPP::Integer::~Integer(&v17);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v18);
  return v32;
}
