/*
 * Function: j_?SendStealMsg@CChatStealSystem@@AEAAXPEAVCPlayer@@EKPEADE1@Z
 * Address: 0x1400033B4
 */

void __fastcall CChatStealSystem::SendStealMsg(CChatStealSystem *this, CPlayer *pPlayer, char byChatType, unsigned int dwSenderSerial, char *pwszSender, char byRaceCode, char *pwszMessage)
{
  CChatStealSystem::SendStealMsg(this, pPlayer, byChatType, dwSenderSerial, pwszSender, byRaceCode, pwszMessage);
}
