/*
 * Function: ?DataFileInit@CMainThread@@AEAA_NXZ
 * Address: 0x1401E5BF0
 */

char __fastcall CMainThread::DataFileInit(CMainThread *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v4; // eax@27
  unsigned __int16 v5; // ax@29
  CPcBangFavor *v6; // rax@68
  CSUItemSystem *v7; // rax@70
  __int64 v8; // [sp+0h] [bp-2C8h]@1
  char szErrCode; // [sp+40h] [bp-288h]@4
  char *szFile; // [sp+D8h] [bp-1F0h]@6
  const char *v11; // [sp+E0h] [bp-1E8h]@6
  const char *v12; // [sp+E8h] [bp-1E0h]@6
  const char *v13; // [sp+F0h] [bp-1D8h]@6
  unsigned int dwStructSize; // [sp+118h] [bp-1B0h]@6
  int v15; // [sp+11Ch] [bp-1ACh]@6
  int v16; // [sp+120h] [bp-1A8h]@6
  int v17; // [sp+124h] [bp-1A4h]@6
  int j; // [sp+134h] [bp-194h]@6
  CRecordData v19; // [sp+150h] [bp-178h]@24
  int n; // [sp+204h] [bp-C4h]@26
  _base_fld *v21; // [sp+208h] [bp-C0h]@28
  char *v22; // [sp+218h] [bp-B0h]@49
  const char *v23; // [sp+220h] [bp-A8h]@49
  const char *v24; // [sp+228h] [bp-A0h]@49
  const char *v25; // [sp+230h] [bp-98h]@49
  const char *v26; // [sp+238h] [bp-90h]@49
  const char *v27; // [sp+240h] [bp-88h]@49
  char v28; // [sp+260h] [bp-68h]@25
  _mob_message *v29; // [sp+268h] [bp-60h]@26
  char v30; // [sp+270h] [bp-58h]@30
  char v31; // [sp+271h] [bp-57h]@32
  char v32; // [sp+272h] [bp-56h]@34
  char v33; // [sp+273h] [bp-55h]@36
  char v34; // [sp+274h] [bp-54h]@38
  char v35; // [sp+275h] [bp-53h]@40
  char v36; // [sp+276h] [bp-52h]@42
  char v37; // [sp+277h] [bp-51h]@44
  char v38; // [sp+278h] [bp-50h]@46
  char v39; // [sp+279h] [bp-4Fh]@48
  char v40; // [sp+27Ah] [bp-4Eh]@52
  char v41; // [sp+27Bh] [bp-4Dh]@55
  char v42; // [sp+27Ch] [bp-4Ch]@57
  char v43; // [sp+27Dh] [bp-4Bh]@59
  char v44; // [sp+27Eh] [bp-4Ah]@61
  char v45; // [sp+27Fh] [bp-49h]@63
  char v46; // [sp+280h] [bp-48h]@65
  char v47; // [sp+281h] [bp-47h]@67
  char v48; // [sp+282h] [bp-46h]@69
  char v49; // [sp+283h] [bp-45h]@71
  char v50; // [sp+284h] [bp-44h]@73
  char v51; // [sp+285h] [bp-43h]@75
  char v52; // [sp+286h] [bp-42h]@77
  char v53; // [sp+287h] [bp-41h]@79
  char v54; // [sp+288h] [bp-40h]@80
  __int64 v55; // [sp+290h] [bp-38h]@4
  CRecordData *v56; // [sp+298h] [bp-30h]@8
  unsigned __int64 v57; // [sp+2A0h] [bp-28h]@26
  COreCuttingTable *v58; // [sp+2A8h] [bp-20h]@39
  CRecordData *pMonsterRecordData; // [sp+2B0h] [bp-18h]@62
  unsigned __int64 v60; // [sp+2B8h] [bp-10h]@4
  CMainThread *v61; // [sp+2D0h] [bp+8h]@1

  v61 = this;
  v1 = &v8;
  for ( i = 176i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v55 = -2i64;
  v60 = (unsigned __int64)&v8 ^ _security_cookie;
  if ( WriteTableData(37, v61->m_tblItemData, 1, &szErrCode) )
  {
    szFile = ".\\script\\skill.dat";
    v11 = ".\\script\\force.dat";
    v12 = ".\\script\\ClassSkill.dat";
    v13 = ".\\script\\BulletItemEffect.dat";
    dwStructSize = 1168;
    v15 = 1088;
    v16 = 1168;
    v17 = 1168;
    for ( j = 0; j < 4; ++j )
    {
      v56 = &v61->m_tblEffectData[j];
      if ( !CRecordData::ReadRecord(v56, (&szFile)[8 * j], *(&dwStructSize + j), &szErrCode) )
      {
        MyMessageBox("DatafileInit", &szErrCode);
        return 0;
      }
    }
    if ( CPotionMgr::DatafileInit(&g_PotionMgr) )
    {
      CPlayer::ms_pXmas_Snow_Effect = (struct _skill_fld *)CRecordData::GetRecord(&v61->m_tblEffectData[3], "16");
      CPlayer::ms_pXmas_Snow_Bullet_Effect = (struct _skill_fld *)CRecordData::GetRecord(&v61->m_tblEffectData[3], "15");
      if ( CPlayer::ms_pXmas_Snow_Effect && CPlayer::ms_pXmas_Snow_Bullet_Effect )
      {
        if ( CRecordData::ReadRecord(&v61->m_tblClass, ".\\Script\\Class.dat", 0x8D0u, &szErrCode) )
        {
          if ( CRecordData::ReadRecord(&v61->m_tblGrade, ".\\Script\\Grade.dat", 0x4Cu, &szErrCode) )
          {
            if ( CRecordData::ReadRecord(&v61->m_tblPlayer, ".\\Script\\PlayerCharacter.dat", 0xA8u, &szErrCode) )
            {
              if ( CRecordData::ReadRecord(&v61->m_tblMonster, ".\\Script\\MonsterCharacter.dat", 0x9B4u, &szErrCode) )
              {
                CRecordData::CRecordData(&v19);
                if ( CRecordData::ReadRecord(&v19, ".\\Script\\MobMessage_str.dat", 0x44C48u, &szErrCode) )
                {
                  v57 = CRecordData::GetRecordNum(&v19);
                  v29 = (_mob_message *)operator new[](saturated_mul(4ui64, v57));
                  v61->m_MobMessage = v29;
                  for ( n = 0; ; ++n )
                  {
                    v4 = CRecordData::GetRecordNum(&v19);
                    if ( n >= v4 )
                      break;
                    v21 = CRecordData::GetRecord(&v19, n);
                    if ( !v21 )
                    {
                      MyMessageBox("DatafileInit", &szErrCode);
                      v30 = 0;
                      CRecordData::~CRecordData(&v19);
                      return v30;
                    }
                    v5 = atoi(v21->m_strCode);
                    v61->m_MobMessage[n].wIndex = v5;
                    v61->m_MobMessage[n].byUsingNum = v21[1].m_dwIndex;
                  }
                  if ( CRecordData::ReadRecord(&v61->m_tblNPC, ".\\Script\\NPCharacter.dat", 0x1E8u, &szErrCode) )
                  {
                    if ( CRecordData::ReadRecord(&v61->m_tblAnimus, ".\\Script\\AnimusItem.dat", 0x188u, &szErrCode) )
                    {
                      if ( CRecordData::ReadRecord(&v61->m_tblExp, ".\\Script\\Exp.dat", 0x104u, &szErrCode) )
                      {
                        if ( CItemLootTable::ReadRecord(
                               &v61->m_tblItemLoot,
                               ".\\Script\\ItemLooting.dat",
                               v61->m_tblItemData,
                               &szErrCode) )
                        {
                          v58 = &v61->m_tblOreCutting;
                          if ( COreCuttingTable::ReadRecord(
                                 &v61->m_tblOreCutting,
                                 ".\\Script\\OreCutting.dat",
                                 &v61->m_tblItemData[17],
                                 &v61->m_tblItemData[18],
                                 &szErrCode) )
                          {
                            if ( CRecordData::ReadRecord(
                                   &v61->m_tblItemMakeData,
                                   ".\\Script\\ItemMakeData.dat",
                                   0x22Cu,
                                   &szErrCode) )
                            {
                              if ( CRecordData::ReadRecord(
                                     &v61->m_tblItemCombineData,
                                     ".\\Script\\ItemCombine.dat",
                                     0xD0u,
                                     &szErrCode) )
                              {
                                if ( CRecordData::ReadRecord(
                                       &v61->m_tblItemExchangeData,
                                       ".\\Script\\BoxItemOut.dat",
                                       0x414u,
                                       &szErrCode) )
                                {
                                  if ( CItemUpgradeTable::ReadRecord(
                                         &v61->m_tblItemUpgrade,
                                         ".\\Script\\ItemUpgrade.dat",
                                         &v61->m_tblItemData[18],
                                         &szErrCode) )
                                  {
                                    v22 = ".\\script\\UnitHead.dat";
                                    v23 = ".\\script\\UnitUpper.dat";
                                    v24 = ".\\script\\UnitLower.dat";
                                    v25 = ".\\script\\UnitArms.dat";
                                    v26 = ".\\script\\UnitShoulder.dat";
                                    v27 = ".\\script\\UnitBack.dat";
                                    for ( j = 0; j < 6; ++j )
                                    {
                                      if ( !CRecordData::ReadRecord(
                                              &v61->m_tblUnitPart[j],
                                              (&v22)[8 * j],
                                              0x204u,
                                              &szErrCode) )
                                      {
                                        MyMessageBox("DatafileInit", &szErrCode);
                                        v40 = 0;
                                        CRecordData::~CRecordData(&v19);
                                        return v40;
                                      }
                                    }
                                    if ( CRecordData::ReadRecord(
                                           &v61->m_tblUnitBullet,
                                           ".\\script\\UnitBullet.dat",
                                           0x164u,
                                           &szErrCode) )
                                    {
                                      if ( CRecordData::ReadRecord(
                                             &v61->m_tblUnitFrame,
                                             ".\\script\\UnitFrame.dat",
                                             0x288u,
                                             &szErrCode) )
                                      {
                                        if ( CRecordData::ReadRecord(
                                               &v61->m_tblEditData,
                                               ".\\script\\EditData.dat",
                                               0x83Cu,
                                               &szErrCode) )
                                        {
                                          if ( CRecordData::ReadRecord(
                                                 &v61->m_MonsterBaseSPData,
                                                 ".\\script\\MonsterCharacterAI.dat",
                                                 0xB0u,
                                                 &szErrCode) )
                                          {
                                            pMonsterRecordData = &v61->m_tblMonster;
                                            if ( CMonsterSPGroupTable::Create(
                                                   &v61->m_MonsterSPGroupTable,
                                                   &v61->m_tblMonster,
                                                   &v61->m_MonsterBaseSPData,
                                                   v61->m_tblEffectData,
                                                   &v61->m_tblEffectData[1],
                                                   &v61->m_tblEffectData[2]) )
                                            {
                                              if ( CQuestMgr::LoadQuestData() )
                                              {
                                                if ( ItemCombineMgr::LoadData() )
                                                {
                                                  v6 = CPcBangFavor::Instance();
                                                  if ( CPcBangFavor::LoadPcBangData(v6) )
                                                  {
                                                    v7 = CSUItemSystem::Instance();
                                                    if ( CSUItemSystem::SUItemSystem_Init(v7) )
                                                    {
                                                      if ( AggroCaculateData::Load(
                                                             &g_AggroCaculateData,
                                                             ".\\Initialize\\AP.ini") )
                                                      {
                                                        if ( MonsterSetInfoData::Load(
                                                               &g_MonsterSetInfoData,
                                                               ".\\Initialize\\MonsterSet.ini") )
                                                        {
                                                          if ( CMainThread::SetGlobalDataName(v61) )
                                                          {
                                                            if ( CMainThread::check_loaded_data(v61) )
                                                            {
                                                              CMainThread::gm_MainThreadControl(v61);
                                                              v54 = 1;
                                                              CRecordData::~CRecordData(&v19);
                                                              result = v54;
                                                            }
                                                            else
                                                            {
                                                              v53 = 0;
                                                              CRecordData::~CRecordData(&v19);
                                                              result = v53;
                                                            }
                                                          }
                                                          else
                                                          {
                                                            v52 = 0;
                                                            CRecordData::~CRecordData(&v19);
                                                            result = v52;
                                                          }
                                                        }
                                                        else
                                                        {
                                                          MyMessageBox(
                                                            "CMainThread::DataFileInit()",
                                                            "DatafileInit->g_MonsterSetInfoData %s Loading Fail",
                                                            ".\\Initialize\\MonsterSet.ini");
                                                          v51 = 0;
                                                          CRecordData::~CRecordData(&v19);
                                                          result = v51;
                                                        }
                                                      }
                                                      else
                                                      {
                                                        MyMessageBox(
                                                          "CMainThread::DataFileInit()",
                                                          "DatafileInit->AggroSystem %s Loading Fail",
                                                          ".\\Initialize\\AP.ini");
                                                        v50 = 0;
                                                        CRecordData::~CRecordData(&v19);
                                                        result = v50;
                                                      }
                                                    }
                                                    else
                                                    {
                                                      v49 = 0;
                                                      CRecordData::~CRecordData(&v19);
                                                      result = v49;
                                                    }
                                                  }
                                                  else
                                                  {
                                                    v48 = 0;
                                                    CRecordData::~CRecordData(&v19);
                                                    result = v48;
                                                  }
                                                }
                                                else
                                                {
                                                  v47 = 0;
                                                  CRecordData::~CRecordData(&v19);
                                                  result = v47;
                                                }
                                              }
                                              else
                                              {
                                                v46 = 0;
                                                CRecordData::~CRecordData(&v19);
                                                result = v46;
                                              }
                                            }
                                            else
                                            {
                                              MyMessageBox("DatafileInit", " m_MonsterSPGroupTable.Create()... fail");
                                              v45 = 0;
                                              CRecordData::~CRecordData(&v19);
                                              result = v45;
                                            }
                                          }
                                          else
                                          {
                                            MyMessageBox("DatafileInit", &szErrCode);
                                            v44 = 0;
                                            CRecordData::~CRecordData(&v19);
                                            result = v44;
                                          }
                                        }
                                        else
                                        {
                                          MyMessageBox("DatafileInit", &szErrCode);
                                          v43 = 0;
                                          CRecordData::~CRecordData(&v19);
                                          result = v43;
                                        }
                                      }
                                      else
                                      {
                                        MyMessageBox("DatafileInit", &szErrCode);
                                        v42 = 0;
                                        CRecordData::~CRecordData(&v19);
                                        result = v42;
                                      }
                                    }
                                    else
                                    {
                                      MyMessageBox("DatafileInit", &szErrCode);
                                      v41 = 0;
                                      CRecordData::~CRecordData(&v19);
                                      result = v41;
                                    }
                                  }
                                  else
                                  {
                                    MyMessageBox("DatafileInit", &szErrCode);
                                    v39 = 0;
                                    CRecordData::~CRecordData(&v19);
                                    result = v39;
                                  }
                                }
                                else
                                {
                                  MyMessageBox("DatafileInit", &szErrCode);
                                  v38 = 0;
                                  CRecordData::~CRecordData(&v19);
                                  result = v38;
                                }
                              }
                              else
                              {
                                MyMessageBox("DatafileInit", &szErrCode);
                                v37 = 0;
                                CRecordData::~CRecordData(&v19);
                                result = v37;
                              }
                            }
                            else
                            {
                              MyMessageBox("DatafileInit", &szErrCode);
                              v36 = 0;
                              CRecordData::~CRecordData(&v19);
                              result = v36;
                            }
                          }
                          else
                          {
                            MyMessageBox("DatafileInit", &szErrCode);
                            v35 = 0;
                            CRecordData::~CRecordData(&v19);
                            result = v35;
                          }
                        }
                        else
                        {
                          MyMessageBox("DatafileInit", &szErrCode);
                          v34 = 0;
                          CRecordData::~CRecordData(&v19);
                          result = v34;
                        }
                      }
                      else
                      {
                        MyMessageBox("DatafileInit", &szErrCode);
                        v33 = 0;
                        CRecordData::~CRecordData(&v19);
                        result = v33;
                      }
                    }
                    else
                    {
                      MyMessageBox("DatafileInit", &szErrCode);
                      v32 = 0;
                      CRecordData::~CRecordData(&v19);
                      result = v32;
                    }
                  }
                  else
                  {
                    MyMessageBox("DatafileInit", &szErrCode);
                    v31 = 0;
                    CRecordData::~CRecordData(&v19);
                    result = v31;
                  }
                }
                else
                {
                  MyMessageBox("DatafileInit", &szErrCode);
                  v28 = 0;
                  CRecordData::~CRecordData(&v19);
                  result = v28;
                }
              }
              else
              {
                MyMessageBox("DatafileInit", &szErrCode);
                result = 0;
              }
            }
            else
            {
              MyMessageBox("DatafileInit", &szErrCode);
              result = 0;
            }
          }
          else
          {
            MyMessageBox("DatafileInit", &szErrCode);
            result = 0;
          }
        }
        else
        {
          MyMessageBox("DatafileInit", &szErrCode);
          result = 0;
        }
      }
      else
      {
        MyMessageBox("MILKSIK_X_MAS_2006 Error", "m_tblEffectData[effect_code_throw].GetRecord( '%s' ) == NULL", "16");
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    MyMessageBox("DatafileInit", &szErrCode);
    result = 0;
  }
  return result;
}
