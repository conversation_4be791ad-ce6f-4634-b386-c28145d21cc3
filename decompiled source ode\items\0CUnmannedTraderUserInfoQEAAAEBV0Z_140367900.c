/*
 * Function: ??0CUnmannedTraderUserInfo@@QEAA@AEBV0@@Z
 * Address: 0x140367900
 */

void __fastcall CUnmannedTraderUserInfo::CUnmannedTraderUserInfo(CUnmannedTraderUserInfo *this, CUnmannedTraderUserInfo *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  __int64 v5; // [sp+20h] [bp-18h]@4
  CUnmannedTraderUserInfo *v6; // [sp+40h] [bp+8h]@1
  CUnmannedTraderUserInfo *v7; // [sp+48h] [bp+10h]@1

  v7 = __that;
  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = -2i64;
  v6->m_eState = __that->m_eState;
  v6->m_wInx = __that->m_wInx;
  v6->m_dwUserSerial = __that->m_dwUserSerial;
  v6->m_byRegistCnt = __that->m_byRegistCnt;
  v6->m_byMaxRegistCnt = __that->m_byMaxRegistCnt;
  v6->m_kRequestState.m_eState = __that->m_kRequestState.m_eState;
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(
    &v6->m_vecRegistItemInfo,
    &__that->m_vecRegistItemInfo);
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(
    &v6->m_vecLoadItemInfo,
    &v7->m_vecLoadItemInfo);
}
