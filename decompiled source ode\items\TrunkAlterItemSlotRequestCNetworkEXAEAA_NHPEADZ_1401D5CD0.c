/*
 * Function: ?TrunkAlterItemSlotRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D5CD0
 */

char __fastcall CNetworkEX::TrunkAlterItemSlotRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v6; // rax@8
  char *v7; // rax@12
  char *v8; // rax@15
  __int64 v9; // [sp+0h] [bp-38h]@1
  char *v10; // [sp+20h] [bp-18h]@4
  CPlayer *v11; // [sp+28h] [bp-10h]@4
  CNetworkEX *v12; // [sp+40h] [bp+8h]@1

  v12 = this;
  v3 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = pBuf;
  v11 = &g_Player + n;
  if ( !v11->m_bOper )
    return 1;
  if ( v10[5] == 5 )
  {
    if ( (signed int)(unsigned __int8)v10[4] >= 100 )
    {
      v6 = CPlayerDB::GetCharNameA(&v11->m_Param);
      CLogFile::Write(
        &v12->m_LogFile,
        "odd.. %s: TrunkAlterItemSlotRequest() : if(pRecv->byClientSlotIndex >= trunk_storage_num)",
        v6);
      return 0;
    }
  }
  else if ( v10[5] == 7 )
  {
    if ( (signed int)(unsigned __int8)v10[4] >= 40 )
    {
      v7 = CPlayerDB::GetCharNameA(&v11->m_Param);
      CLogFile::Write(
        &v12->m_LogFile,
        "odd.. %s: TrunkAlterItemSlotRequest() : if(pRecv->byClientSlotIndex >= extend_trunk_storage_num)",
        v7);
      return 0;
    }
  }
  else if ( (signed int)(unsigned __int8)v10[5] >= 8 )
  {
    v8 = CPlayerDB::GetCharNameA(&v11->m_Param);
    CLogFile::Write(&v12->m_LogFile, "odd.. %s: TrunkAlterItemSlotRequest() : pRecv->byStorageIndex Is Invalid!", v8);
    return 0;
  }
  CPlayer::pc_TrunkAlterItemSlotRequest(v11, *(_DWORD *)v10, v10[4], v10[5]);
  return 1;
}
