/*
 * Function: ?insert@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEAA?AV?$_Vector_iterator@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@2@V32@AEBQEAVCUnmannedTraderSortType@@@Z
 * Address: 0x140370C80
 */

std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *__fastcall std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::insert(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *result, std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *_Where, CUnmannedTraderSortType *const *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v6; // rax@9
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v7; // rax@9
  __int64 v9; // [sp+0h] [bp-C8h]@1
  __int64 _Off; // [sp+20h] [bp-A8h]@7
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > resulta; // [sp+28h] [bp-A0h]@6
  char v12; // [sp+40h] [bp-88h]@9
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v13; // [sp+58h] [bp-70h]@9
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > v14; // [sp+60h] [bp-68h]@9
  int v15; // [sp+78h] [bp-50h]@4
  __int64 v16; // [sp+80h] [bp-48h]@4
  __int64 v17; // [sp+88h] [bp-40h]@5
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v18; // [sp+90h] [bp-38h]@6
  std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *_Right; // [sp+98h] [bp-30h]@6
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v20; // [sp+A0h] [bp-28h]@9
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v21; // [sp+A8h] [bp-20h]@9
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v22; // [sp+B0h] [bp-18h]@9
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v23; // [sp+D0h] [bp+8h]@1
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v24; // [sp+D8h] [bp+10h]@1
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *__that; // [sp+E0h] [bp+18h]@1
  CUnmannedTraderSortType *const *v26; // [sp+E8h] [bp+20h]@1

  v26 = _Val;
  __that = _Where;
  v24 = result;
  v23 = this;
  v4 = &v9;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v16 = -2i64;
  v15 = 0;
  if ( std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::size(v23) )
  {
    v18 = std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::begin(v23, &resulta);
    _Right = (std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *)v18;
    v15 |= 1u;
    v17 = std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::operator-(
            __that,
            (std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *)&v18->_Mycont);
  }
  else
  {
    v17 = 0i64;
  }
  _Off = v17;
  if ( v15 & 1 )
  {
    v15 &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::~_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(&resulta);
  }
  v13 = (std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *)&v12;
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(
    (std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *)&v12,
    __that);
  v20 = v6;
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Insert_n(v23, v6, 1ui64, v26);
  v7 = std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::begin(v23, &v14);
  v21 = v7;
  v22 = v7;
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::operator+(v7, v24, _Off);
  v15 |= 2u;
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::~_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(&v14);
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::~_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(__that);
  return v24;
}
