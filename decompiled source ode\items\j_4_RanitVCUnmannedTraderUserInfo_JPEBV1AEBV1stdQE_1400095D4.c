/*
 * Function: j_??4?$_Ranit@VCUnmannedTraderUserInfo@@_JPEBV1@AEBV1@@std@@QEAAAEAU01@AEBU01@@Z
 * Address: 0x1400095D4
 */

std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &> *__fastcall std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &>::operator=(std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &> *this, std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &> *__that)
{
  return std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &>::operator=(
           this,
           __that);
}
