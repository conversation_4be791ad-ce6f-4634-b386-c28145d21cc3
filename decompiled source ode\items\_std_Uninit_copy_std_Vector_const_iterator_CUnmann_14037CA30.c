/*
 * Function: _std::_Uninit_copy_std::_Vector_const_iterator_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo____CUnmannedTraderItemCodeInfo_____ptr64_std::allocator_CUnmannedTraderItemCodeInfo____::_1_::catch$0
 * Address: 0x14037CA30
 */

void __fastcall __noreturn std::_Uninit_copy_std::_Vector_const_iterator_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo____CUnmannedTraderItemCodeInfo_____ptr64_std::allocator_CUnmannedTraderItemCodeInfo____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 112); *(_QWORD *)(i + 32) += 72i64 )
    std::allocator<CUnmannedTraderItemCodeInfo>::destroy(
      *(std::allocator<CUnmannedTraderItemCodeInfo> **)(i + 120),
      *(CUnmannedTraderItemCodeInfo **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
