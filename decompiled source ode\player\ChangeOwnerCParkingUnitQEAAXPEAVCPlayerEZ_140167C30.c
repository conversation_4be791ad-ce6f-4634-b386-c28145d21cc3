/*
 * Function: ?ChangeOwner@CParkingUnit@@QEAAXPEAVCPlayer@@E@Z
 * Address: 0x140167C30
 */

void __fastcall CParkingUnit::ChangeOwner(CParkingUnit *this, CPlayer *pNewOwner, char byUnitSlotIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  CPlayer *pOldOwner; // [sp+20h] [bp-18h]@4
  CParkingUnit *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pOldOwner = v7->m_pOwner;
  v7->m_pOwner = pNewOwner;
  v7->m_dwOwnerSerial = pNewOwner->m_dwObjSerial;
  CParkingUnit::SendMsg_ChangeOwner(v7, byUnitSlotIndex, pOldOwner);
}
