/*
 * Function: ?GetPtrItemStorage@CPlayerDB@@QEAAPEAU_db_con@_STORAGE_LIST@@GPEAE@Z
 * Address: 0x14010C160
 */

_STORAGE_LIST::_db_con *__fastcall CPlayerDB::GetPtrItemStorage(CPlayerDB *this, unsigned __int16 wSerial, char *pbyStorageCode)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _STORAGE_LIST::_db_con *v8; // [sp+28h] [bp-10h]@6
  CPlayerDB *v9; // [sp+40h] [bp+8h]@1
  unsigned __int16 v10; // [sp+48h] [bp+10h]@1
  char *v11; // [sp+50h] [bp+18h]@1

  v11 = pbyStorageCode;
  v10 = wSerial;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; ; ++j )
  {
    if ( j >= 8 )
      return 0i64;
    v8 = _STORAGE_LIST::GetPtrFromSerial(v9->m_pStoragePtr[j], v10);
    if ( v8 )
      break;
  }
  if ( v11 )
    *v11 = j;
  return v8;
}
