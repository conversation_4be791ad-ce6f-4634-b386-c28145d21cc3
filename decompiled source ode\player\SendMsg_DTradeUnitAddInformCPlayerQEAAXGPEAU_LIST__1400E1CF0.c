/*
 * Function: ?SendMsg_DTradeUnitAddInform@CPlayer@@QEAAXGPEAU_LIST@_UNIT_DB_BASE@@@Z
 * Address: 0x1400E1CF0
 */

void __fastcall CPlayer::SendMsg_DTradeUnitAddInform(CPlayer *this, unsigned __int16 wUnitKeySerial, _UNIT_DB_BASE::_LIST *pUnitData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-C8h]@1
  char szMsg[2]; // [sp+38h] [bp-90h]@4
  char v7; // [sp+3Ah] [bp-8Eh]@4
  char v8; // [sp+3Bh] [bp-8Dh]@4
  unsigned int v9; // [sp+3Ch] [bp-8Ch]@4
  char Dst; // [sp+40h] [bp-88h]@4
  char v11; // [sp+46h] [bp-82h]@4
  char v12; // [sp+4Eh] [bp-7Ah]@4
  int v13; // [sp+6Eh] [bp-5Ah]@4
  char pbyType; // [sp+94h] [bp-34h]@4
  char v15; // [sp+95h] [bp-33h]@4
  unsigned __int64 v16; // [sp+B0h] [bp-18h]@4
  CPlayer *v17; // [sp+D0h] [bp+8h]@1
  _UNIT_DB_BASE::_LIST *v18; // [sp+E0h] [bp+18h]@1

  v18 = pUnitData;
  v17 = this;
  v3 = &v5;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v16 = (unsigned __int64)&v5 ^ _security_cookie;
  *(_WORD *)szMsg = wUnitKeySerial;
  v7 = pUnitData->bySlotIndex;
  v8 = pUnitData->byFrame;
  v9 = pUnitData->dwGauge;
  memcpy_0(&Dst, pUnitData->byPart, 6ui64);
  memcpy_0(&v11, v18->dwBullet, 8ui64);
  memcpy_0(&v12, v18->dwSpare, 0x20ui64);
  v13 = v18->nPullingFee;
  pbyType = 18;
  v15 = 28;
  CNetProcess::LoadSendMsg(unk_1414F2088, v17->m_ObjID.m_wIndex, &pbyType, szMsg, 0x3Au);
}
