/*
 * Function: j_??$_Unchecked_move_backward@PEAPEAVCMoveMapLimitInfo@@PEAPEAV1@@stdext@@YAPEAPEAVCMoveMapLimitInfo@@PEAPEAV1@00@Z
 * Address: 0x140012AA8
 */

CMoveMapLimitInfo **__fastcall stdext::_Unchecked_move_backward<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *>(CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, CMoveMapLimitInfo **_Dest)
{
  return stdext::_Unchecked_move_backward<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *>(_First, _Last, _Dest);
}
