/*
 * Function: ??$fill@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@V12@@std@@YAXPEAVCGuildBattleRewardItem@GUILD_BATTLE@@0AEBV12@@Z
 * Address: 0x1403D27F0
 */

void __fastcall std::fill<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem>(GUILD_BATTLE::CGuildBattleRewardItem *_First, GUILD_BATTLE::CGuildBattleRewardItem *_Last, GUILD_BATTLE::CGuildBattleRewardItem *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleRewardItem *_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Fill<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem>(_Firsta, _Last, _Val);
}
