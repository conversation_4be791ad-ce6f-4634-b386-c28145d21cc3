/*
 * Function: ?exchange_money@CMgrAvatorItemHistory@@QEAAXHKKKKPEAD@Z
 * Address: 0x14023AE70
 */

void __fastcall CMgrAvatorItemHistory::exchange_money(CMgrAvatorItemHistory *this, int n, unsigned int dwCurDalant, unsigned int dwCurGold, unsigned int dwNewDalant, unsigned int dwNewGold, char *pszFileName)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-48h]@1
  unsigned int v10; // [sp+20h] [bp-28h]@4
  unsigned int v11; // [sp+28h] [bp-20h]@4
  char *v12; // [sp+30h] [bp-18h]@4
  char *v13; // [sp+38h] [bp-10h]@4
  CMgrAvatorItemHistory *v14; // [sp+50h] [bp+8h]@1

  v14 = this;
  v7 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v13 = v14->m_szCurTime;
  v12 = v14->m_szCurDate;
  v11 = dwNewGold;
  v10 = dwCurGold;
  sprintf(sData, "EXCHANGE: D:%u->$D:%u / G:%u->$G:%u [%s %s]\r\n", dwCurDalant, dwNewDalant);
  CMgrAvatorItemHistory::WriteFile(v14, pszFileName, sData);
}
