/*
 * Function: ?RequestGMList@GMCallMgr@@QEAA_NPEAVCPlayer@@H@Z
 * Address: 0x1402AA7A0
 */

bool __fastcall GMCallMgr::RequestGMList(GMCallMgr *this, CPlayer *pOne, int nCurrPageIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  __int64 v6; // [sp+0h] [bp-28h]@1
  GMCallMgr *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pOne && pOne->m_byUserDgr >= 2 )
    result = GMCallMgr::SendResponseGMList(v7, pOne, nCurrPageIndex);
  else
    result = 0;
  return result;
}
