/*
 * Function: j_??$_Ucopy@V?$_Vector_const_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@IEAAPEAPEAVCMoveMapLimitRight@@V?$_Vector_const_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@1@0PEAPEAV2@@Z
 * Address: 0x14001024E
 */

CMoveMapLimitRight **__fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Ucopy<std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>>(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_First, std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_Last, CMoveMapLimitRight **_Ptr)
{
  return std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Ucopy<std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>>(
           this,
           _First,
           _Last,
           _Ptr);
}
