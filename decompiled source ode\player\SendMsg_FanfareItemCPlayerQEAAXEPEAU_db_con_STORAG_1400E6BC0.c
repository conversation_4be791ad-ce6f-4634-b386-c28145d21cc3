/*
 * Function: ?SendMsg_FanfareItem@CPlayer@@QEAAXEPEAU_db_con@_STORAGE_LIST@@PEAVCItemBox@@@Z
 * Address: 0x1400E6BC0
 */

void __fastcall CPlayer::SendMsg_FanfareItem(CPlayer *this, char byGetType, _STORAGE_LIST::_db_con *pItem, CItemBox *pItemBox)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char *v6; // rax@22
  int v7; // eax@29
  __int64 v8; // [sp+0h] [bp-C8h]@1
  _base_fld *v9; // [sp+30h] [bp-98h]@4
  char v10; // [sp+38h] [bp-90h]@5
  char v11; // [sp+39h] [bp-8Fh]@5
  char v12; // [sp+3Ah] [bp-8Eh]@5
  _item_fanfare_zocl v13; // [sp+48h] [bp-80h]@11
  char pbyType; // [sp+84h] [bp-44h]@11
  char v15; // [sp+85h] [bp-43h]@11
  bool v16; // [sp+94h] [bp-34h]@11
  int j; // [sp+98h] [bp-30h]@25
  CPlayer *v18; // [sp+A0h] [bp-28h]@28
  int v19; // [sp+B0h] [bp-18h]@29
  unsigned __int64 v20; // [sp+B8h] [bp-10h]@4
  CPlayer *v21; // [sp+D0h] [bp+8h]@1
  char v22; // [sp+D8h] [bp+10h]@1
  _STORAGE_LIST::_db_con *v23; // [sp+E0h] [bp+18h]@1
  CItemBox *v24; // [sp+E8h] [bp+20h]@1

  v24 = pItemBox;
  v23 = pItem;
  v22 = byGetType;
  v21 = this;
  v4 = &v8;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v20 = (unsigned __int64)&v8 ^ _security_cookie;
  v9 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
  if ( v9 )
  {
    v10 = 1;
    v11 = 0;
    v12 = 0;
    if ( (v22 == 1 || !v22) && GetDefItemUpgSocketNum(v23->m_byTableCode, v23->m_wItemIndex) )
      v12 = GetItemUpgedLv(v23->m_dwLv);
    if ( v22 )
      v11 = GetItemGrade(v23->m_byTableCode, v23->m_wItemIndex);
    _item_fanfare_zocl::_item_fanfare_zocl(&v13);
    pbyType = 7;
    v15 = 58;
    v13.byTakeType = v22;
    v16 = 0;
    if ( (signed int)(unsigned __int8)v12 < 5 )
    {
      if ( (signed int)(unsigned __int8)v11 < 3 )
      {
        if ( (signed int)(unsigned __int8)v12 < 1 || (signed int)(unsigned __int8)v12 > 5 )
          return;
        *(_BYTE *)&v13.bySubType &= 0xFCu;
        v13.bySubType = (_item_fanfare_zocl::_SubType)(4 * (v12 & 0x3F) | *(_BYTE *)&v13.bySubType & 3);
        v16 = 0;
      }
      else
      {
        v13.bySubType = (_item_fanfare_zocl::_SubType)(*(_BYTE *)&v13.bySubType & 0xFC | 1);
        v13.bySubType = (_item_fanfare_zocl::_SubType)(4 * (v11 & 0x3F) | *(_BYTE *)&v13.bySubType & 3);
        v16 = 1;
      }
    }
    else
    {
      *(_BYTE *)&v13.bySubType &= 0xFCu;
      v13.bySubType = (_item_fanfare_zocl::_SubType)(4 * (v12 & 0x3F) | *(_BYTE *)&v13.bySubType & 3);
      v16 = 1;
    }
    v13.byTableCode = v23->m_byTableCode;
    v13.wItemIndex = v23->m_wItemIndex;
    v13.dwCharacterSerial = v21->m_dwObjSerial;
    if ( v24 )
      v13.wMonsterIndex = v24->m_wMonRecIndex;
    else
      v13.wMonsterIndex = -1;
    v13.wMapIndex = v21->m_pCurMap->m_nMapIndex;
    v6 = CPlayerDB::GetCharNameW(&v21->m_Param);
    strcpy_0(v13.strCharacterName, v6);
    v13.bAllSend = v16;
    if ( v21->m_byUserDgr < 3 )
    {
      if ( v16 )
      {
        for ( j = 0; j < 2532; ++j )
        {
          v18 = &g_Player + j;
          if ( v18->m_bLive )
          {
            v19 = CPlayerDB::GetRaceCode(&v18->m_Param);
            v7 = CPlayerDB::GetRaceCode(&v21->m_Param);
            if ( v19 == v7 )
              CNetProcess::LoadSendMsg(unk_1414F2088, v18->m_ObjID.m_wIndex, &pbyType, &v13.byTakeType, 0x1Fu);
          }
        }
      }
      else
      {
        CGameObject::CircleReport((CGameObject *)&v21->vfptr, &pbyType, &v13.byTakeType, 31, 1);
      }
    }
    else
    {
      CNetProcess::LoadSendMsg(unk_1414F2088, v21->m_ObjID.m_wIndex, &pbyType, &v13.byTakeType, 0x1Fu);
    }
  }
}
