/*
 * Function: _std::vector_GUILD_BATTLE::CGuildBattleRewardItem_std::allocator_GUILD_BATTLE::CGuildBattleRewardItem___::erase_::_1_::dtor$2
 * Address: 0x1403D1250
 */

void __fastcall std::vector_GUILD_BATTLE::CGuildBattleRewardItem_std::allocator_GUILD_BATTLE::CGuildBattleRewardItem___::erase_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 40) & 1 )
  {
    *(_DWORD *)(a2 + 40) &= 0xFFFFFFFE;
    std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::~_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(*(std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > **)(a2 + 88));
  }
}
