/*
 * Function: ??0?$DL_EncryptorBase@VInteger@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x1406387A0
 */

__int64 __fastcall CryptoPP::DL_EncryptorBase<CryptoPP::Integer>::DL_EncryptorBase<CryptoPP::Integer>(__int64 a1)
{
  __int64 v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::Integer>>::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::Integer>>();
  return v2;
}
