/*
 * Function: ?CheatGetStone@CGuildBattleController@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x1403D75E0
 */

char __fastcall CGuildBattleController::CheatGetStone(CGuildBattleController *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleFieldList *v4; // rax@5
  char result; // al@7
  char *v6; // rax@9
  __int64 v7; // [sp+0h] [bp-C8h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v8; // [sp+30h] [bp-98h]@4
  char Dst; // [sp+48h] [bp-80h]@8
  char v10; // [sp+49h] [bp-7Fh]@9
  char Dest; // [sp+5Ah] [bp-6Eh]@9
  char pbyType; // [sp+84h] [bp-44h]@10
  char v13; // [sp+85h] [bp-43h]@10
  CMapData *v14; // [sp+A0h] [bp-28h]@5
  int v15; // [sp+A8h] [bp-20h]@5
  unsigned __int64 v16; // [sp+B0h] [bp-18h]@4
  CPlayer *pkPlayera; // [sp+D8h] [bp+10h]@1

  pkPlayera = pkPlayer;
  v2 = &v7;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v16 = (unsigned __int64)&v7 ^ _security_cookie;
  v8 = 0i64;
  if ( pkPlayer->m_pCurMap )
  {
    v14 = pkPlayer->m_pCurMap;
    v15 = CPlayerDB::GetRaceCode(&pkPlayer->m_Param);
    v4 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
    v8 = GUILD_BATTLE::CNormalGuildBattleFieldList::GetField(v4, v15, v14->m_nMapCode);
  }
  if ( v8 )
  {
    memset_0(&Dst, 0, 0x28ui64);
    Dst = GUILD_BATTLE::CNormalGuildBattleField::CheatGetStone(v8, pkPlayera);
    if ( !Dst )
    {
      v6 = CPlayerDB::GetCharNameW(&pkPlayera->m_Param);
      strcpy_0(&Dest, v6);
      strcpy_0(&v10, "Cheat");
    }
    pbyType = 27;
    v13 = 72;
    CNetProcess::LoadSendMsg(unk_1414F2088, pkPlayera->m_ObjID.m_wIndex, &pbyType, &Dst, 0x28u);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
