/*
 * Function: ??1?$CArrayEx@VCLuaLooting_Novus_Item@@U_State@1@@US@@QEAA@XZ
 * Address: 0x140405600
 */

void __fastcall US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::~CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>(US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State> *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  US::CArray<CLuaLooting_Novus_Item::_State>::~CArray<CLuaLooting_Novus_Item::_State>(&v5->m_StateAr);
  US::CArray<CLuaLooting_Novus_Item>::~CArray<CLuaLooting_Novus_Item>(&v5->m_DataAr);
}
