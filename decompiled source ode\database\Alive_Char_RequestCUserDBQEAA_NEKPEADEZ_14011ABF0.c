/*
 * Function: ?Alive_Char_Request@CUserDB@@QEAA_NEKPEADE@Z
 * Address: 0x14011ABF0
 */

bool __fastcall CUserDB::Alive_Char_Request(CUserDB *this, char byCase, unsigned int dwSerial, char *pwszName, char bySlotIndex)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  CNationSettingManager *v8; // rax@32
  __int64 v9; // [sp+0h] [bp-1B8h]@1
  _REGED *pAliveAvator; // [sp+20h] [bp-198h]@34
  int nSize; // [sp+28h] [bp-190h]@34
  char v12; // [sp+30h] [bp-188h]@7
  int j; // [sp+34h] [bp-184h]@7
  char v14; // [sp+38h] [bp-180h]@14
  int k; // [sp+3Ch] [bp-17Ch]@14
  const char *l; // [sp+40h] [bp-178h]@26
  _qry_case_alive_char v17; // [sp+60h] [bp-158h]@34
  unsigned __int64 v18; // [sp+1A0h] [bp-18h]@4
  CUserDB *v19; // [sp+1C0h] [bp+8h]@1
  char v20; // [sp+1C8h] [bp+10h]@1
  unsigned int dwSeriala; // [sp+1D0h] [bp+18h]@1
  const char *Str2; // [sp+1D8h] [bp+20h]@1

  Str2 = pwszName;
  dwSeriala = dwSerial;
  v20 = byCase;
  v19 = this;
  v5 = &v9;
  for ( i = 108i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v18 = (unsigned __int64)&v9 ^ _security_cookie;
  if ( v19->m_RegedList[(unsigned __int8)bySlotIndex].m_bySlotIndex == 255 )
  {
    if ( !byCase )
      goto LABEL_39;
    v12 = 0;
    for ( j = 0; j < 50; ++j )
    {
      if ( v19->m_dwArrangePassCase0[j] == dwSerial )
      {
        v12 = 1;
        break;
      }
    }
    if ( v12 )
    {
LABEL_39:
      v14 = 0;
      for ( k = 0; k < 50; ++k )
      {
        if ( v19->m_NotArrangedChar[k].dwSerial != -1 && v19->m_NotArrangedChar[k].dwSerial == dwSerial )
        {
          if ( byCase )
          {
            if ( !strcmp_0(v19->m_NotArrangedChar[k].wszName, pwszName) )
              break;
          }
          else if ( strcmp_0(v19->m_NotArrangedChar[k].wszName, pwszName) )
          {
            break;
          }
          v14 = 1;
          break;
        }
      }
      if ( v14 )
      {
        for ( l = Str2; ; ++l )
        {
          if ( *l == 32 || *l == 39 )
          {
            CUserDB::Alive_Char_Complete(v19, 47, v20, dwSeriala, 0i64);
            return 1;
          }
          if ( !*l )
            break;
        }
        v8 = CTSingleton<CNationSettingManager>::Instance();
        if ( CNationSettingManager::IsNormalString(v8, Str2) )
        {
          _qry_case_alive_char::_qry_case_alive_char(&v17);
          v17.in_byCase = v20;
          v17.in_dwSerial = dwSeriala;
          strcpy_0(v17.in_w_szName, Str2);
          v17.in_bySlot = bySlotIndex;
          nSize = _qry_case_alive_char::size(&v17);
          pAliveAvator = (_REGED *)&v17;
          if ( CMainThread::PushDQSData(&g_Main, v19->m_dwAccountSerial, &v19->m_idWorld, 23, &v17.in_byCase, nSize) )
          {
            v19->m_bDBWaitState = 1;
            result = 1;
          }
          else
          {
            result = 0;
          }
        }
        else
        {
          CUserDB::Insert_Char_Complete(v19, 47, 0i64);
          result = 1;
        }
      }
      else
      {
        CUserDB::Alive_Char_Complete(v19, 57, v20, dwSeriala, 0i64);
        result = 0;
      }
    }
    else
    {
      CUserDB::Alive_Char_Complete(v19, 50, byCase, dwSerial, 0i64);
      result = 0;
    }
  }
  else
  {
    CUserDB::Alive_Char_Complete(v19, 50, byCase, dwSerial, 0i64);
    result = 0;
  }
  return result;
}
