/*
 * Function: ?Copy@CUnmannedTraderSubClassInfo@@IEAAAEBV1@AEBV1@@Z
 * Address: 0x140384710
 */

CUnmannedTraderSubClassInfo *__fastcall CUnmannedTraderSubClassInfo::Copy(CUnmannedTraderSubClassInfo *this, CUnmannedTraderSubClassInfo *lhs)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderSubClassInfo *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6->m_dwID = lhs->m_dwID;
  strcpy_0(v6->m_szName, lhs->m_szName);
  return v6;
}
