/*
 * Function: ?SetEmpty@CLogTypeDBTaskPool@@QEAA_NPEAVCLogTypeDBTask@@AEAVCLogFile@@@Z
 * Address: 0x1402C2810
 */

char __fastcall CLogTypeDBTaskPool::SetEmpty(CLogTypeDBTaskPool *this, CLogTypeDBTask *pTask, CLogFile *kLogger)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  unsigned int v6; // eax@7
  int v7; // eax@8
  __int64 v8; // [sp+0h] [bp-38h]@1
  unsigned int v9; // [sp+20h] [bp-18h]@8
  CLogTypeDBTaskPool *v10; // [sp+40h] [bp+8h]@1
  CLogTypeDBTask *v11; // [sp+48h] [bp+10h]@1
  CLogFile *v12; // [sp+50h] [bp+18h]@1

  v12 = kLogger;
  v11 = pTask;
  v10 = this;
  v3 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v10->m_bInit && pTask )
  {
    v6 = CLogTypeDBTask::GetInx(pTask);
    if ( CNetIndexList::PushNode_Back(&v10->m_kInxEmpty, v6) )
    {
      CLogTypeDBTask::SetEmpty(v11);
      result = 1;
    }
    else
    {
      v9 = CLogTypeDBTask::GetInx(v11);
      v7 = CLogTypeDBTask::GetQueryType(v11);
      CLogFile::Write(
        v12,
        "CLogTypeDBTaskPool::SetEmpty(...) : QueryType(%d) m_kInxEmpty.PushNode_Back( pTask->GetInx()(%u) ) Fail!",
        (unsigned int)v7,
        v9);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
