/*
 * Function: ?_LockUnit<PERSON>ey@CPlayer@@QEAA_NE_N@Z
 * Address: 0x1401068B0
 */

char __fastcall CPlayer::_LockUnitKey(CPlayer *this, char bySlotIndex, bool bLock)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  int n; // [sp+20h] [bp-18h]@4
  char *v8; // [sp+28h] [bp-10h]@7
  CPlayer *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( n = 0; n < v9->m_Param.m_dbInven.m_nUsedNum; ++n )
  {
    v8 = &v9->m_Param.m_dbInven.m_pStorageList[n].m_bLoad;
    if ( *v8 && *(_DWORD *)(v8 + 13) == (unsigned __int8)bySlotIndex )
    {
      _STORAGE_LIST::SetLock((_STORAGE_LIST *)&v9->m_Param.m_dbInven.m_nListNum, n, bLock);
      return 1;
    }
  }
  return 0;
}
