/*
 * Function: ?Update_RaceRank_Step_6_2@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404B6B50
 */

char __fastcall CRFWorldDatabase::Update_RaceRank_Step_6_2(CRFWorldDatabase *this, char *szDate)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-458h]@1
  char DstBuf; // [sp+30h] [bp-428h]@4
  char v7; // [sp+31h] [bp-427h]@4
  unsigned __int64 v8; // [sp+440h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+460h] [bp+8h]@1
  char *v10; // [sp+468h] [bp+10h]@1

  v10 = szDate;
  v9 = this;
  v2 = &v5;
  for ( i = 276i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = (unsigned __int64)&v5 ^ _security_cookie;
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v9->vfptr,
    "CRFWorldDatabase::Update_RaceRank_Step_6_2(szDate(%s)) : Start drop #tbl_PvpRankC Table",
    szDate);
  DstBuf = 0;
  memset(&v7, 0, 0x3FFui64);
  sprintf_s(&DstBuf, 0x400ui64, "drop table #tbl_PvpRankC");
  if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 0) )
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v9->vfptr,
      "CRFWorldDatabase::Update_RaceRank_Step_6_2(szDate(%s)) : End drop #tbl_PvpRankC Table",
      v10);
    result = 1;
  }
  else
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v9->vfptr,
      "CRFWorldDatabase::Update_RaceRank_Step_6_2(szDate(%s)) : %s Fail!",
      v10,
      &DstBuf);
    result = 0;
  }
  return result;
}
