/*
 * Function: ??0CUnmannedTraderClassInfoTableCodeType@@QEAA@K@Z
 * Address: 0x140377040
 */

void __fastcall CUnmannedTraderClassInfoTableCodeType::CUnmannedTraderClassInfoTableCodeType(CUnmannedTraderClassInfoTableCodeType *this, unsigned int dwID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  __int64 v5; // [sp+20h] [bp-18h]@4
  CUnmannedTraderClassInfoTableCodeType *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = -2i64;
  CUnmannedTraderClassInfoTableType::CUnmannedTraderClassInfoTableType(
    (CUnmannedTraderClassInfoTableType *)&v6->vfptr,
    dwID);
  v6->vfptr = (CUnmannedTraderClassInfoVtbl *)&CUnmannedTraderClassInfoTableCodeType::`vftable';
  std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(&v6->m_vecCodeList);
  strcpy_0(v6->m_szTypeName, "table and code");
}
