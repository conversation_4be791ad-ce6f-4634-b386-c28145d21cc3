/*
 * Function: j_?NetClose@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAEKKPEAVCPlayer@@@Z
 * Address: 0x14000F5CE
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleManager::NetClose(GUILD_BATTLE::CNormalGuildBattleManager *this, unsigned int dwGuildSerial, unsigned int dwCharacSerial, CPlayer *pkPlayer)
{
  return GUILD_BATTLE::CNormalGuildBattleManager::NetClose(this, dwGuildSerial, dwCharacSerial, pkPlayer);
}
