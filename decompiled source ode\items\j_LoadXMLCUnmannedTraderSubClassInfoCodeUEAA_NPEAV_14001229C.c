/*
 * Function: j_?LoadXML@CUnmannedTraderSubClassInfoCode@@UEAA_NPEAVTiXmlElement@@AEAVCLogFile@@KK@Z
 * Address: 0x14001229C
 */

bool __fastcall CUnmannedTraderSubClassInfoCode::LoadXML(CUnmannedTraderSubClassInfoCode *this, TiXmlElement *elemSubClass, CLogFile *kLogger, unsigned int dwDivisionID, unsigned int dwClassID)
{
  return CUnmannedTraderSubClassInfoCode::LoadXML(this, elemSubClass, kLogger, dwDivisionID, dwClassID);
}
