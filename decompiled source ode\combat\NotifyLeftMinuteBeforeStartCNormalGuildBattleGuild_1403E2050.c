/*
 * Function: ?NotifyLeftMinuteBeforeStart@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXE@Z
 * Address: 0x1403E2050
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::NotifyLeftMinuteBeforeStart(GUILD_BATTLE::CNormalGuildBattleGuild *this, char byLeftMin)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@9
  __int64 v5; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@5
  char pbyType; // [sp+54h] [bp-24h]@5
  char v8; // [sp+55h] [bp-23h]@5
  int j; // [sp+64h] [bp-14h]@5
  GUILD_BATTLE::CNormalGuildBattleGuild *v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v10->m_pkGuild )
  {
    szMsg = byLeftMin;
    pbyType = 27;
    v8 = 65;
    for ( j = 0; j < 50; ++j )
    {
      if ( GUILD_BATTLE::CNormalGuildBattleGuildMember::IsExist(&v10->m_kMember[j]) )
      {
        v4 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetIndex(&v10->m_kMember[j]);
        CNetProcess::LoadSendMsg(unk_1414F2088, v4, &pbyType, &szMsg, 1u);
      }
    }
  }
}
