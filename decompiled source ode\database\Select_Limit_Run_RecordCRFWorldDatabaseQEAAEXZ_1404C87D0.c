/*
 * Function: ?Select_Limit_Run_Record@CRFWorldDatabase@@QEAAEXZ
 * Address: 0x1404C87D0
 */

char __fastcall CRFWorldDatabase::Select_Limit_Run_Record(CRFWorldDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-4A8h]@1
  char _Dest[1024]; // [sp+40h] [bp-468h]@4
  char v6; // [sp+444h] [bp-64h]@4
  int v7; // [sp+454h] [bp-54h]@8
  unsigned __int16 ColumnNumber; // [sp+474h] [bp-34h]@8
  unsigned __int64 v9; // [sp+490h] [bp-18h]@4
  CRFWorldDatabase *v10; // [sp+4B0h] [bp+8h]@1

  v10 = this;
  v1 = &v4;
  for ( i = 296i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = (unsigned __int64)&v4 ^ _security_cookie;
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0x3FFui64);
  sprintf_s<1024>((char (*)[1024])_Dest, "select [aserial] from [dbo].[tbl_sf_delay] where [aserial] = 0");
  v6 = CRFNewDatabase::SQLExecDirect_RetErrCode((CRFNewDatabase *)&v10->vfptr, _Dest);
  if ( v6 )
  {
    result = v6;
  }
  else
  {
    v6 = CRFNewDatabase::SQLFetch_RetErrCode((CRFNewDatabase *)&v10->vfptr, _Dest);
    if ( v6 )
    {
      result = v6;
    }
    else
    {
      v7 = 0;
      ColumnNumber = 1;
      v6 = CRFNewDatabase::SQLGetData_RetErrCode((CRFNewDatabase *)&v10->vfptr, _Dest, &ColumnNumber, 4, &v7);
      if ( v6 )
      {
        result = v6;
      }
      else
      {
        CRFNewDatabase::SelectCleanUp((CRFNewDatabase *)&v10->vfptr, _Dest);
        result = 0;
      }
    }
  }
  return result;
}
