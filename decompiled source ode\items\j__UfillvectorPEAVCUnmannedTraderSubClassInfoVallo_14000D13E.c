/*
 * Function: j_?_Ufill@?$vector@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@IEAAPEAPEAVCUnmannedTraderSubClassInfo@@PEAPEAV3@_KAEBQEAV3@@Z
 * Address: 0x14000D13E
 */

CUnmannedTraderSubClassInfo **__fastcall std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Ufill(std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *this, CUnmannedTraderSubClassInfo **_Ptr, unsigned __int64 _Count, CUnmannedTraderSubClassInfo *const *_Val)
{
  return std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Ufill(
           this,
           _Ptr,
           _Count,
           _Val);
}
