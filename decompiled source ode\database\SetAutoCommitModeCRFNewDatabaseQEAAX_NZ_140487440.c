/*
 * Function: ?SetAutoCommitMode@CRFNewDatabase@@QEAAX_N@Z
 * Address: 0x140487440
 */

void __fastcall CRFNewDatabase::SetAutoCommitMode(CRFNewDatabase *this, bool bAutoCommit)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CRFNewDatabase *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( bAutoCommit )
    SQLSetConnectAttr_0(v5->m_hDbc, 102, (SQLPOINTER)1, 0);
  else
    SQLSetConnectAttr_0(v5->m_hDbc, 102, 0i64, 0);
}
