/*
 * Function: ?AccessBasePrecomputation@?$DL_GroupParametersImpl@V?$EcPrecomputation@VEC2N@CryptoPP@@@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@UEC2NPoint@CryptoPP@@@2@V?$DL_GroupParameters@UEC2NPoint@CryptoPP@@@2@@CryptoPP@@UEAAAEAV?$DL_FixedBasePrecomputation@UEC2NPoint@CryptoPP@@@2@XZ
 * Address: 0x14055CD70
 */

signed __int64 __fastcall CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::EC2N>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>,CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>>::AccessBasePrecomputation(__int64 a1)
{
  return a1 + 152;
}
