/*
 * Function: j_?db_input_guild_money@CMainThread@@QEAAEKKKKPEAN0PEAEPEAD@Z
 * Address: 0x140003B61
 */

char __fastcall CMainThread::db_input_guild_money(CMainThread *this, unsigned int dwPusherSerial, unsigned int dwGuildSerial, unsigned int dwAddDalant, unsigned int dwAddGold, long double *dTotalDalant, long double *dTotalGold, char *byDate, char *pwszName)
{
  return CMainThread::db_input_guild_money(
           this,
           dwPusherSerial,
           dwGuildSerial,
           dwAddDalant,
           dwAddGold,
           dTotalDalant,
           dTotalGold,
           byDate,
           pwszName);
}
