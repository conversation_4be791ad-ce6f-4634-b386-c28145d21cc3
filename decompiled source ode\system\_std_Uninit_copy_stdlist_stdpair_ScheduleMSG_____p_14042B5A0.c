/*
 * Function: _std::_Uninit_copy_std::list_std::pair_ScheduleMSG_____ptr64_const_unsigned_long__std::allocator_std::pair_ScheduleMSG_____ptr64_const_unsigned_long_____::_Iterator_0______ptr64_std::list_std::pair_ScheduleMSG_____ptr64_const_unsigned_long__std::allocator_std::pair_ScheduleMSG_____ptr64_const_unsigned_long_____::_Iterator_0______ptr64_std::allocator_std::list_std::pair_ScheduleMSG_____ptr64_const_unsigned_long__std::allocator_std::pair_ScheduleMSG_____ptr64_const_unsigned_long_____::_Iterator_0______::_1_::catch$0
 * Address: 0x14042B5A0
 */

void __fastcall __noreturn std::_Uninit_copy_std::list_std::pair_ScheduleMSG_____ptr64_const_unsigned_long__std::allocator_std::pair_ScheduleMSG_____ptr64_const_unsigned_long_____::_Iterator_0______ptr64_std::list_std::pair_ScheduleMSG_____ptr64_const_unsigned_long__std::allocator_std::pair_ScheduleMSG_____ptr64_const_unsigned_long_____::_Iterator_0______ptr64_std::allocator_std::list_std::pair_ScheduleMSG_____ptr64_const_unsigned_long__std::allocator_std::pair_ScheduleMSG_____ptr64_const_unsigned_long_____::_Iterator_0______::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 80); *(_QWORD *)(i + 32) += 24i64 )
    std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>::destroy(
      *(std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> > **)(i + 88),
      *(std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
