/*
 * Function: ?LoadXML@CUnmannedTraderClassInfoTableCodeType@@UEAA_NPEAVTiXmlElement@@AEAVCLogFile@@K@Z
 * Address: 0x140377190
 */

char __fastcall CUnmannedTraderClassInfoTableCodeType::LoadXML(CUnmannedTraderClassInfoTableCodeType *this, TiXmlElement *elemClass, CLogFile *kLogger, unsigned int dwDivisionID)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CUnmannedTraderItemCodeInfo *v7; // rax@10
  __int64 v8; // [sp+0h] [bp-E8h]@1
  char *szCode; // [sp+20h] [bp-C8h]@6
  unsigned int dwStartIndex; // [sp+34h] [bp-B4h]@6
  unsigned int dwEndIndex; // [sp+54h] [bp-94h]@6
  TiXmlNode *v12; // [sp+68h] [bp-80h]@6
  int v13; // [sp+70h] [bp-78h]@6
  CUnmannedTraderItemCodeInfo v14; // [sp+74h] [bp-74h]@10
  __int64 v15; // [sp+C0h] [bp-28h]@4
  CUnmannedTraderItemCodeInfo *v16; // [sp+C8h] [bp-20h]@10
  CUnmannedTraderItemCodeInfo *_Val; // [sp+D0h] [bp-18h]@10
  CUnmannedTraderClassInfoTableCodeType *v18; // [sp+F0h] [bp+8h]@1
  TiXmlElement *elemClassa; // [sp+F8h] [bp+10h]@1
  CLogFile *kLoggera; // [sp+100h] [bp+18h]@1
  unsigned int dwDivisionIDa; // [sp+108h] [bp+20h]@1

  dwDivisionIDa = dwDivisionID;
  kLoggera = kLogger;
  elemClassa = elemClass;
  v18 = this;
  v4 = &v8;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v15 = -2i64;
  if ( CUnmannedTraderClassInfoTableType::LoadXML(
         (CUnmannedTraderClassInfoTableType *)&v18->vfptr,
         elemClass,
         kLogger,
         dwDivisionID) )
  {
    szCode = 0i64;
    dwStartIndex = -1;
    dwEndIndex = -1;
    v12 = (TiXmlNode *)TiXmlNode::FirstChildElement((TiXmlNode *)&elemClassa->vfptr, "code");
    v13 = 0;
    while ( v12 )
    {
      szCode = (char *)TiXmlElement::Attribute((TiXmlElement *)v12, "code");
      if ( !szCode )
      {
        CLogFile::Write(
          kLoggera,
          "CUnmannedTraderClassInfoTableCodeType::LoadXML( TiXmlElement * elemClass, CLogFile & kLogger, DWORD dwDivision"
          "ID )\r\n"
          "\t\tDivisionID(%u), ClassID(%u) pkElement->Attribute( code ) NULL!\r\n",
          dwDivisionIDa,
          v18->m_dwID);
        return 0;
      }
      TiXmlElement::Attribute((TiXmlElement *)v12, "startindex", (int *)&dwStartIndex);
      TiXmlElement::Attribute((TiXmlElement *)v12, "endindex", (int *)&dwEndIndex);
      CUnmannedTraderItemCodeInfo::CUnmannedTraderItemCodeInfo(&v14, szCode, dwStartIndex, dwEndIndex);
      v16 = v7;
      _Val = v7;
      std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::push_back(
        &v18->m_vecCodeList,
        v7);
      CUnmannedTraderItemCodeInfo::~CUnmannedTraderItemCodeInfo(&v14);
      v12 = (TiXmlNode *)TiXmlNode::NextSiblingElement(v12, "code");
      ++v13;
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
