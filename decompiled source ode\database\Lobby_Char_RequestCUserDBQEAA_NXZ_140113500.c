/*
 * Function: ?Lobby_Char_Request@CUserDB@@QEAA_NXZ
 * Address: 0x140113500
 */

char __usercall CUserDB::Lobby_Char_Request@<al>(CUserDB *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned int v6; // eax@14
  unsigned int v7; // eax@15
  __int64 v8; // rax@15
  __int64 v9; // [sp-20h] [bp-12368h]@1
  char *pQryData; // [sp+0h] [bp-12348h]@17
  int nSize; // [sp+8h] [bp-12340h]@17
  _qry_sheet_lobby Dst; // [sp+20h] [bp-12328h]@11
  void *Src; // [sp+122F8h] [bp-50h]@11
  __int64 v14; // [sp+12300h] [bp-48h]@15
  __int64 v15; // [sp+12308h] [bp-40h]@16
  char v16; // [sp+12318h] [bp-30h]@18
  char v17; // [sp+12319h] [bp-2Fh]@19
  __int64 v18; // [sp+12320h] [bp-28h]@4
  __int64 v19; // [sp+12328h] [bp-20h]@14
  __int64 v20; // [sp+12330h] [bp-18h]@15
  unsigned __int64 v21; // [sp+12338h] [bp-10h]@4
  CUserDB *v22; // [sp+12350h] [bp+8h]@1

  v22 = this;
  v2 = alloca(a2);
  v3 = &v9;
  for ( i = 18648i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v18 = -2i64;
  v21 = (unsigned __int64)&v9 ^ _security_cookie;
  if ( v22->m_bActive )
  {
    if ( v22->m_bField && v22->m_dwSerial != -1 )
    {
      if ( v22->m_dwSerial == -1 )
      {
        result = 0;
      }
      else
      {
        CPlayer::NetClose(&g_Player + v22->m_idWorld.wIndex, 1);
        _qry_sheet_lobby::_qry_sheet_lobby(&Dst);
        Dst.dwAvatorSerial = v22->m_dwSerial;
        memcpy_0(&Dst.NewData, &v22->m_AvatorData, 0x915Fui64);
        Src = CUserDB::IsContPushBefore(v22);
        if ( Src )
          memcpy_0(&Dst.OldData, Src, 0x915Fui64);
        else
          memcpy_0(&Dst.OldData, &v22->m_AvatorData_bk, 0x915Fui64);
        Dst.bUpdateRefineCnt = 0;
        v6 = v22->m_idWorld.wIndex;
        v19 = *qword_1799C9AF0;
        if ( (unsigned __int8)(*(int (__fastcall **)(_QWORD *, _QWORD))(v19 + 40))(qword_1799C9AF0, v6) )
        {
          v7 = v22->m_idWorld.wIndex;
          v20 = *qword_1799C9AF0;
          LODWORD(v8) = (*(int (__fastcall **)(_QWORD *, _QWORD, _QWORD))(v20 + 64))(
                          qword_1799C9AF0,
                          v7,
                          v22->m_idWorld.dwSerial);
          v14 = v8;
          if ( v8 )
          {
            Dst.bUpdateRefineCnt = 1;
            v15 = v14;
            Dst.byRefinedCnt = *(_BYTE *)(v14 + 16);
            Dst.dwRefineDate = *(_DWORD *)(v14 + 12);
          }
        }
        nSize = _qry_sheet_lobby::size(&Dst);
        pQryData = (char *)&Dst;
        if ( CMainThread::PushDQSData(&g_Main, v22->m_dwAccountSerial, &v22->m_idWorld, 6, (char *)&Dst, nSize) )
        {
          v22->m_dwOperLobbyTime = timeGetTime();
          v22->m_bDBWaitState = 1;
          v17 = 1;
          _qry_sheet_lobby::~_qry_sheet_lobby(&Dst);
          result = v17;
        }
        else
        {
          CUserDB::Exit_Account_Complete(v22, 100);
          v16 = 0;
          _qry_sheet_lobby::~_qry_sheet_lobby(&Dst);
          result = v16;
        }
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
