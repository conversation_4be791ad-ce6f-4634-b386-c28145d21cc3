/*
 * Function: j_?destroy@?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@QEAAXPEAVCGuildBattleRewardItem@GUILD_BATTLE@@@Z
 * Address: 0x140013174
 */

void __fastcall std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::destroy(std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *this, GUILD_BATTLE::CGuildBattleRewardItem *_Ptr)
{
  std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::destroy(this, _Ptr);
}
