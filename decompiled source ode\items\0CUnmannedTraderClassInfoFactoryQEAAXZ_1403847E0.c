/*
 * Function: ??0CUnmannedTraderClassInfoFactory@@QEAA@XZ
 * Address: 0x1403847E0
 */

void __fastcall CUnmannedTraderClassInfoFactory::CUnmannedTraderClassInfoFactory(CUnmannedTraderClassInfoFactory *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderClassInfo *v3; // rax@5
  CUnmannedTraderClassInfo *v4; // rax@8
  __int64 v5; // [sp+0h] [bp-68h]@1
  CUnmannedTraderClassInfo *pkType; // [sp+20h] [bp-48h]@7
  CUnmannedTraderClassInfoTableType *v7; // [sp+28h] [bp-40h]@4
  CUnmannedTraderClassInfo *v8; // [sp+30h] [bp-38h]@10
  CUnmannedTraderClassInfoTableCodeType *v9; // [sp+38h] [bp-30h]@7
  __int64 v10; // [sp+40h] [bp-28h]@4
  CUnmannedTraderClassInfo *v11; // [sp+48h] [bp-20h]@5
  CUnmannedTraderClassInfo *v12; // [sp+50h] [bp-18h]@8
  CUnmannedTraderClassInfoFactory *v13; // [sp+70h] [bp+8h]@1

  v13 = this;
  v1 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = -2i64;
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&v13->m_vecTable);
  v7 = (CUnmannedTraderClassInfoTableType *)operator new(0x140ui64);
  if ( v7 )
  {
    CUnmannedTraderClassInfoTableType::CUnmannedTraderClassInfoTableType(v7, 0xFFFFFFFF);
    v11 = v3;
  }
  else
  {
    v11 = 0i64;
  }
  pkType = v11;
  CUnmannedTraderClassInfoFactory::Regist(v13, v11);
  v9 = (CUnmannedTraderClassInfoTableCodeType *)operator new(0x168ui64);
  if ( v9 )
  {
    CUnmannedTraderClassInfoTableCodeType::CUnmannedTraderClassInfoTableCodeType(v9, 0xFFFFFFFF);
    v12 = v4;
  }
  else
  {
    v12 = 0i64;
  }
  v8 = v12;
  CUnmannedTraderClassInfoFactory::Regist(v13, v12);
}
