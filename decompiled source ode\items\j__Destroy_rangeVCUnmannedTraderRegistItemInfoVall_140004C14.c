/*
 * Function: j_??$_Destroy_range@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@YAXPEAVCUnmannedTraderRegistItemInfo@@0AEAV?$allocator@VCUnmannedTraderRegistItemInfo@@@0@@Z
 * Address: 0x140004C14
 */

void __fastcall std::_Destroy_range<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(CUnmannedTraderRegistItemInfo *_First, CUnmannedTraderRegistItemInfo *_Last, std::allocator<CUnmannedTraderRegistItemInfo> *_Al)
{
  std::_Destroy_range<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(_First, _Last, _Al);
}
