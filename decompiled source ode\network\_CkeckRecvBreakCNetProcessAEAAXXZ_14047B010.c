/*
 * Function: ?_CkeckRecvBreak@CNetProcess@@AEAAXXZ
 * Address: 0x14047B010
 */

void __fastcall CNetProcess::_CkeckRecvBreak(CNetProcess *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  DWORD v4; // [sp+20h] [bp-18h]@5
  unsigned int dwIndex; // [sp+24h] [bp-14h]@5
  _socket *v6; // [sp+28h] [bp-10h]@8
  CNetProcess *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CNetTimer::CountingTimer(&v7->m_tmrCheckRecvBreak) )
  {
    v4 = timeGetTime();
    for ( dwIndex = 0; (signed int)dwIndex < v7->m_Type.m_wSocketMaxNum; ++dwIndex )
    {
      v6 = CNetSocket::GetSocket(&v7->m_NetSocket, dwIndex);
      if ( v6->m_bAccept )
      {
        if ( v6->m_dwRecvPopMissTime )
          CNetIndexList::PushNode_Back(&v7->m_listRecvEvent, dwIndex);
      }
    }
  }
}
