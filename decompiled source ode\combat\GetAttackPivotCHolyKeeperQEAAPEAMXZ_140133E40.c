/*
 * Function: ?GetAttackPivot@CHoly<PERSON>eeper@@QEAAPEAMXZ
 * Address: 0x140133E40
 */

float *__fastcall CHolyKeeper::GetAttackPivot(CHolyKeeper *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  float v3; // xmm0_4@6
  float v4; // xmm0_4@6
  float v5; // xmm0_4@6
  __int64 v7; // [sp+0h] [bp-38h]@1
  float v8; // [sp+20h] [bp-18h]@6
  float v9; // [sp+24h] [bp-14h]@6
  CHolyKeeper *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v1 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !v10->m_pRec )
    _wassert(
      L"false",
      L"G:\\00_ZoneServer_Source\\03_Temp_Source\\2009_05_13_Source_Oversea\\zoneserver\\gamemain\\HolyKeeper.cpp",
      0x170u);
  v8 = (float)(v10->m_pRec->m_fAttExt * 4.0) / 7.0;
  v3 = 6.283185307 / (float)(rand() % -8);
  v9 = v3;
  v4 = cosf(v3);
  s_Pivot[0] = v10->m_fCurPos[0] + (float)(v4 * v8);
  v5 = sinf(v9);
  s_Pivot[2] = v10->m_fCurPos[2] + (float)(v5 * v8);
  s_Pivot[1] = v10->m_fCurPos[1];
  return s_Pivot;
}
