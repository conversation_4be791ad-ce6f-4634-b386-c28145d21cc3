/*
 * Function: ?CalcEquipMaxDP@CPlayer@@QEAAX_N@Z
 * Address: 0x140057430
 */

void __fastcall CPlayer::CalcEquipMaxDP(CPlayer *this, bool bInit)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  int v5; // [sp+20h] [bp-28h]@4
  int j; // [sp+24h] [bp-24h]@4
  char *v7; // [sp+28h] [bp-20h]@6
  _base_fld *v8; // [sp+30h] [bp-18h]@6
  CPlayer *v9; // [sp+50h] [bp+8h]@1
  bool v10; // [sp+58h] [bp+10h]@1

  v10 = bInit;
  v9 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = 1;
  for ( j = 0; j < 5; ++j )
  {
    v7 = &v9->m_Param.m_dbEquip.m_pStorageList[j].m_bLoad;
    v8 = 0i64;
    if ( *v7 )
      v8 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + j, *(_WORD *)(v7 + 3));
    else
      v8 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + j, v9->m_Param.m_dbChar.m_byDftPart[j]);
    if ( v8 )
      v5 += *(_DWORD *)&v8[5].m_strCode[52];
  }
  if ( v9->m_nMaxDP != v5 )
  {
    v9->m_nMaxDP = v5;
    if ( CPlayer::GetDP(v9) > v9->m_nMaxDP )
    {
      CPlayer::SetDP(v9, v9->m_nMaxDP, 0);
      CPlayer::SendMsg_SetDPInform(v9);
    }
    if ( !v10 )
      CPlayer::SendMsg_AlterMaxDP(v9);
  }
}
