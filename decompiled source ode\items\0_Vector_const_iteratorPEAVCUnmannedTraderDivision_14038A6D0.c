/*
 * Function: ??0?$_Vector_const_iterator@PEAVCUnmannedTraderDivisionInfo@@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@QEAA@PEAPEAVCUnmannedTraderDivisionInfo@@@Z
 * Address: 0x14038A6D0
 */

void __fastcall std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *this, CUnmannedTraderDivisionInfo **_Ptr)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *v5; // [sp+30h] [bp+8h]@1
  CUnmannedTraderDivisionInfo **v6; // [sp+38h] [bp+10h]@1

  v6 = _Ptr;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Ranit<CUnmannedTraderDivisionInfo *,__int64,CUnmannedTraderDivisionInfo * const *,CUnmannedTraderDivisionInfo * const &>::_Ranit<CUnmannedTraderDivisionInfo *,__int64,CUnmannedTraderDivisionInfo * const *,CUnmannedTraderDivisionInfo * const &>((std::_Ranit<CUnmannedTraderDivisionInfo *,__int64,CUnmannedTraderDivisionInfo * const *,CUnmannedTraderDivisionInfo * const &> *)&v5->_Mycont);
  v5->_Myptr = v6;
}
