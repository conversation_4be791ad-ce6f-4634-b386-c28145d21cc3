/*
 * Function: ?Select_Unit@CRFWorldDatabase@@QEAAEKPEAU_worlddb_unit_info_array@@@Z
 * Address: 0x14048F6F0
 */

char __fastcall CRFWorldDatabase::Select_Unit(CRFWorldDatabase *this, unsigned int dwCharacterSerial, _worlddb_unit_info_array *pUnitInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-198h]@1
  void *SQLStmt; // [sp+20h] [bp-178h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-170h]@24
  SQLLEN v9; // [sp+38h] [bp-160h]@24
  __int16 v10; // [sp+44h] [bp-154h]@9
  char Dest; // [sp+60h] [bp-138h]@4
  int j; // [sp+164h] [bp-34h]@4
  int v13; // [sp+168h] [bp-30h]@4
  char v14; // [sp+16Ch] [bp-2Ch]@16
  int k; // [sp+170h] [bp-28h]@61
  unsigned __int64 v16; // [sp+180h] [bp-18h]@4
  CRFWorldDatabase *v17; // [sp+1A0h] [bp+8h]@1
  _worlddb_unit_info_array *v18; // [sp+1B0h] [bp+18h]@1

  v18 = pUnitInfo;
  v17 = this;
  v3 = &v6;
  for ( i = 100i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v16 = (unsigned __int64)&v6 ^ _security_cookie;
  j = 0;
  v13 = 0;
  sprintf(&Dest, "{ CALL pSelect_Unit( %d ) }", dwCharacterSerial);
  if ( v17->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v17->vfptr, &Dest);
  if ( v17->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v17->vfptr) )
  {
    v10 = SQLExecDirectA_0(v17->m_hStmtSelect, &Dest, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v17->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v10, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v10, v17->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v10 = SQLFetch_0(v17->m_hStmtSelect);
      if ( v10 && v10 != 1 )
      {
        v14 = 0;
        if ( v10 == 100 )
        {
          v14 = 2;
        }
        else
        {
          SQLStmt = v17->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v10, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v10, v17->m_hStmtSelect);
          v14 = 1;
        }
        if ( v17->m_hStmtSelect )
          SQLCloseCursor_0(v17->m_hStmtSelect);
        result = v14;
      }
      else
      {
        for ( j = 0; j < 4; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v17->m_hStmtSelect, v13, 5, (char *)v18 + 68 * j, 0i64, &v9);
        }
        for ( j = 0; j < 4; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v17->m_hStmtSelect, v13, 5, v18->UnitInfo[j].byPart, 0i64, &v9);
        }
        for ( j = 0; j < 4; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v17->m_hStmtSelect, v13, 5, &v18->UnitInfo[j].byPart[1], 0i64, &v9);
        }
        for ( j = 0; j < 4; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v17->m_hStmtSelect, v13, 5, &v18->UnitInfo[j].byPart[2], 0i64, &v9);
        }
        for ( j = 0; j < 4; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v17->m_hStmtSelect, v13, 5, &v18->UnitInfo[j].byPart[3], 0i64, &v9);
        }
        for ( j = 0; j < 4; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v17->m_hStmtSelect, v13, 5, &v18->UnitInfo[j].byPart[4], 0i64, &v9);
        }
        for ( j = 0; j < 4; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v17->m_hStmtSelect, v13, 5, &v18->UnitInfo[j].byPart[5], 0i64, &v9);
        }
        for ( j = 0; j < 4; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v17->m_hStmtSelect, v13, 4, &v18->UnitInfo[j].dwGauge, 0i64, &v9);
        }
        for ( j = 0; j < 4; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v17->m_hStmtSelect, v13, 4, v18->UnitInfo[j].dwBullet, 0i64, &v9);
        }
        for ( j = 0; j < 4; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v17->m_hStmtSelect, v13, 4, &v18->UnitInfo[j].dwBullet[1], 0i64, &v9);
        }
        for ( j = 0; j < 4; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v17->m_hStmtSelect, v13, 4, &v18->UnitInfo[j].nKeepingFee, 0i64, &v9);
        }
        for ( j = 0; j < 4; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v17->m_hStmtSelect, v13, 4, &v18->UnitInfo[j].nPullingFee, 0i64, &v9);
        }
        for ( j = 0; j < 4; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v17->m_hStmtSelect, v13, 4, &v18->UnitInfo[j].dwCutTime, 0i64, &v9);
        }
        for ( k = 0; k < 8; ++k )
        {
          for ( j = 0; j < 4; ++j )
          {
            ++v13;
            StrLen_or_IndPtr = &v9;
            SQLStmt = 0i64;
            v10 = SQLGetData_0(v17->m_hStmtSelect, v13, 4, &v18->UnitInfo[j].dwSpare[k], 0i64, &v9);
          }
        }
        if ( v17->m_hStmtSelect )
          SQLCloseCursor_0(v17->m_hStmtSelect);
        if ( v17->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v17->vfptr, "%s Success", &Dest);
        result = 0;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v17->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
