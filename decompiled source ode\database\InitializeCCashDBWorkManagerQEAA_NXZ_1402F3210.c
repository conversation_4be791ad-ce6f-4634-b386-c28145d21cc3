/*
 * Function: ?Initialize@CCashDBWorkManager@@QEAA_NXZ
 * Address: 0x1402F3210
 */

char __fastcall CCashDBWorkManager::Initialize(CCashDBWorkManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager *v3; // rax@4
  char result; // al@5
  CNationSettingManager *v5; // rax@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  CashDbWorker *v7; // [sp+20h] [bp-18h]@4
  CCashDBWorkManager *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = CTSingleton<CNationSettingManager>::Instance();
  v7 = CNationSettingManager::CreateWorker(v3);
  if ( v7 )
  {
    v8->m_pWorker = v7;
    v5 = CTSingleton<CNationSettingManager>::Instance();
    if ( CNationSettingManager::IsCashDBUseExtRef(v5) )
      result = 1;
    else
      result = ((int (__fastcall *)(CashDbWorker *))v8->m_pWorker->vfptr[1].~Worker)(v8->m_pWorker);
  }
  else
  {
    result = 0;
  }
  return result;
}
