/*
 * Function: ?GetStone@CGuildBattleController@@QEAAPEAVCGameObject@@H@Z
 * Address: 0x1403D67C0
 */

CGravityStone *__fastcall CGuildBattleController::GetStone(CGuildBattleController *this, int iInx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleFieldList *v4; // rax@4
  __int64 v6; // [sp+0h] [bp-28h]@1
  int iInxa; // [sp+38h] [bp+10h]@1

  iInxa = iInx;
  v2 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
  return GUILD_BATTLE::CNormalGuildBattleFieldList::GetStone(v4, iInxa);
}
