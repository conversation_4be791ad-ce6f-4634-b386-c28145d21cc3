/*
 * Function: ?VerifyPrime@CryptoPP@@YA_NAEAVRandomNumberGenerator@1@AEBVInteger@1@I@Z
 * Address: 0x140642FA0
 */

bool __fastcall CryptoPP::VerifyPrime(CryptoPP *this, struct CryptoPP::RandomNumberGenerator *a2, const struct CryptoPP::Integer *a3)
{
  bool v4; // [sp+20h] [bp-18h]@5
  bool v5; // [sp+24h] [bp-14h]@3
  bool v6; // [sp+28h] [bp-10h]@8
  CryptoPP *v7; // [sp+40h] [bp+8h]@1
  struct CryptoPP::RandomNumberGenerator *v8; // [sp+48h] [bp+10h]@1
  unsigned int v9; // [sp+50h] [bp+18h]@1

  v9 = (unsigned int)a3;
  v8 = a2;
  v7 = this;
  v5 = CryptoPP::IsPrime((CryptoPP *)a2, (const struct CryptoPP::Integer *)a2)
    && CryptoPP::RabinMillerTest(v7, v8, (const struct CryptoPP::Integer *)1);
  v4 = v5;
  if ( v9 >= 1 )
  {
    v6 = v5 && CryptoPP::RabinMillerTest(v7, v8, (const struct CryptoPP::Integer *)0xA);
    v4 = v6;
  }
  return v4;
}
