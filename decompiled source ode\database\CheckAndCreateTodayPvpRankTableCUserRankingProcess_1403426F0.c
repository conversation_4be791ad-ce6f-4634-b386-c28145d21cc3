/*
 * Function: ?CheckAndCreateTodayPvpRankTable@CUserRankingProcess@@AEAA_NPEBD@Z
 * Address: 0x1403426F0
 */

char __fastcall CUserRankingProcess::CheckAndCreateTodayPvpRankTable(CUserRankingProcess *this, const char *szDate)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-78h]@1
  char DstBuf; // [sp+28h] [bp-50h]@4
  char v7; // [sp+29h] [bp-4Fh]@4
  unsigned __int64 v8; // [sp+60h] [bp-18h]@4
  const char *szDatea; // [sp+88h] [bp+10h]@1

  szDatea = szDate;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = (unsigned __int64)&v5 ^ _security_cookie;
  DstBuf = 0;
  memset(&v7, 0, 0x1Fui64);
  sprintf_s(&DstBuf, 0x20ui64, "tbl_PvpRank%s", szDate);
  if ( !CRFNewDatabase::TableExist((CRFNewDatabase *)&pkDB->vfptr, &DstBuf) )
  {
    if ( !CRFWorldDatabase::Update_RaceRank(pkDB, (char *)szDatea) )
    {
      MyMessageBox("DatabaseInit", "create race-rank-table fail");
      return 0;
    }
    CLogFile::Write(&stru_1799C8F30, "Today Rank Table(%s) Make Complete!!", &DstBuf);
  }
  return 1;
}
