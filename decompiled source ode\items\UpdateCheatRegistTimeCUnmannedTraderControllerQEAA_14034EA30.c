/*
 * Function: ?UpdateCheatRegistTime@CUnmannedTraderController@@QEAAEPEAD@Z
 * Address: 0x14034EA30
 */

char __fastcall CUnmannedTraderController::UpdateCheatRegistTime(CUnmannedTraderController *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  char *byProcRet; // [sp+20h] [bp-28h]@7
  char *v7; // [sp+30h] [bp-18h]@4
  char v8; // [sp+38h] [bp-10h]@4
  unsigned __int8 j; // [sp+39h] [bp-Fh]@4
  CUnmannedTraderController *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = pData;
  v8 = 0;
  for ( j = 0; j < (signed int)(unsigned __int8)v7[1]; ++j )
  {
    byProcRet = &v7[8 * j + 8];
    CUnmannedTraderController::CheckDBItemState(v10, *v7, *(_DWORD *)&v7[8 * j + 12], &v7[8 * j + 9], byProcRet);
    if ( !v7[8 * j + 8]
      && !CRFWorldDatabase::Update_UnmannedTraderCheatUpdateRegistDate(pkDB, *v7, *(_DWORD *)&v7[8 * j + 12]) )
    {
      v7[8 * j + 8] = 30;
    }
  }
  return 0;
}
