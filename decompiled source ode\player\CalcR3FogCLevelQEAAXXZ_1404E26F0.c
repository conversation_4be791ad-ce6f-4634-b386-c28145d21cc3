/*
 * Function: ?CalcR3Fog@CLevel@@QEAAXXZ
 * Address: 0x1404E26F0
 */

void __usercall CLevel::CalcR3Fog(CLevel *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  CExtDummy *v3; // rsi@1
  int v4; // ebp@2
  int v5; // xmm9_4@2
  __int64 v6; // rbx@2
  int v7; // xmm10_4@2
  unsigned __int32 *v8; // rdi@3
  struct _EXT_DUMMY *v9; // rax@7
  signed int v10; // edi@9
  signed int v11; // ebx@9
  int v12; // xmm2_4@14
  struct IDirect3DDevice8 *v13; // rax@16
  float v14; // [sp+10h] [bp-9CA8h]@2
  int v15; // [sp+14h] [bp-9CA4h]@2
  int v16; // [sp+18h] [bp-9CA0h]@2
  unsigned __int32 v17[10000]; // [sp+20h] [bp-9C98h]@1
  unsigned __int32 v18; // [sp+9CC0h] [bp+8h]@1

  v2 = alloca(a2);
  v3 = &this->mDummy;
  CExtDummy::GetDummyList(&this->mDummy, 2, &v18, v17);
  if ( v18 )
  {
    v4 = dword_184A7984C;
    v5 = dword_184A79848;
    v14 = dword_184A79B1C;
    v6 = 0i64;
    v7 = dword_184A79844;
    v15 = dword_184A79B20;
    v16 = dword_184A79B24;
    if ( v18 )
    {
      v8 = v17;
      while ( !(unsigned int)CExtDummy::IsInBBox(v3, *v8, &v14) )
      {
        v6 = (unsigned int)(v6 + 1);
        ++v8;
        if ( (unsigned int)v6 >= v18 )
          goto LABEL_8;
      }
      v9 = CExtDummy::GetDummy(v3, v17[v6]);
      v4 = v9->mdwArgv[0];
      v7 = v9->mdwArgv[1];
      v5 = v9->mdwArgv[2];
    }
LABEL_8:
    if ( dword_184A7981C != v4 )
    {
      v10 = (signed int)ffloor((float)(R3GetLoopTime()
                                     * (float)((float)(unsigned __int8)((unsigned int)v4 >> 16)
                                             - (float)BYTE2(dword_184A7981C))) + (float)BYTE2(dword_184A7981C));
      v11 = (signed int)ffloor((float)(R3GetLoopTime() * (float)((float)BYTE1(v4) - (float)BYTE1(dword_184A7981C))) + (float)BYTE1(dword_184A7981C));
      dword_184A7981C = ((v11 | ((v10 | 0xFFFFFF00) << 8)) << 8) | (signed int)ffloor((float)(R3GetLoopTime()
                                                                                            * (float)((float)(unsigned __int8)v4 - (float)(unsigned __int8)dword_184A7981C)) + (float)(unsigned __int8)dword_184A7981C);
    }
    if ( *(float *)&dword_184A79814 != *(float *)&v7 )
    {
      *(float *)&dword_184A79814 = *(float *)&dword_184A79814
                                 + (float)(R3GetLoopTime() * (float)(*(float *)&v7 - *(float *)&dword_184A79814));
      if ( (float)(*(float *)&dword_184A79814 - *(float *)&v7) < 2.0 )
        dword_184A79814 = v7;
    }
    if ( *(float *)&dword_184A79818 != *(float *)&v5 )
    {
      *(float *)&dword_184A79818 = *(float *)&dword_184A79818
                                 + (float)(R3GetLoopTime() * (float)(*(float *)&v5 - *(float *)&dword_184A79818));
      v12 = dword_184A79818;
      if ( (float)(*(float *)&dword_184A79818 - *(float *)&v5) < 2.0 )
      {
        v12 = v5;
        dword_184A79818 = v5;
      }
      dword_184A79778 = v12;
      D3DXMatrixPerspectiveFovLH_0(dword_184A79B2C);
      v13 = GetD3dDevice();
      ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, float *))v13->vfptr[12].AddRef)(
        v13,
        3i64,
        dword_184A79B2C);
    }
  }
}
