/*
 * Function: ??0_100_per_random_table@@QEAA@XZ
 * Address: 0x1400726C0
 */

void __fastcall _100_per_random_table::_100_per_random_table(_100_per_random_table *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@5
  int k; // [sp+24h] [bp-14h]@7
  _100_per_random_table *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !_100_per_random_table::s_bRecordSet )
  {
    _100_per_random_table::s_bRecordSet = 1;
    v3 = time(0i64);
    srand(v3);
    for ( j = 0; j < 10; ++j )
    {
      for ( k = 0; k < 100; ++k )
        *(&_100_per_random_table::s_wRecord[100 * j] + k) = k;
    }
    _100_per_random_table::reset(v7);
  }
  v7->m_wCurTable = rand() % 10;
  v7->m_wCurPoint = 0;
}
