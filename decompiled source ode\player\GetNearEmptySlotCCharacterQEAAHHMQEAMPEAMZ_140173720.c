/*
 * Function: ?GetNearEmptySlot@CCharacter@@QEAAHHMQEAMPEAM@Z
 * Address: 0x140173720
 */

signed __int64 __fastcall CCharacter::GetNearEmptySlot(CCharacter *this, int pos, float dist, float *cur, float *target)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  float v7; // xmm0_4@14
  float v8; // xmm0_4@14
  float v10; // xmm0_4@17
  float v11; // xmm0_4@17
  __int64 v12; // [sp+0h] [bp-98h]@1
  double v13; // [sp+20h] [bp-78h]@7
  unsigned int v14; // [sp+28h] [bp-70h]@7
  unsigned int v15; // [sp+2Ch] [bp-6Ch]@7
  float Dst; // [sp+38h] [bp-60h]@14
  float v17; // [sp+40h] [bp-58h]@14
  float v18[3]; // [sp+68h] [bp-30h]@14
  int j; // [sp+84h] [bp-14h]@7
  float v20; // [sp+88h] [bp-10h]@14
  float v21; // [sp+8Ch] [bp-Ch]@17
  CCharacter *v22; // [sp+A0h] [bp+8h]@1
  int v23; // [sp+A8h] [bp+10h]@1
  float *v24; // [sp+B8h] [bp+20h]@1

  v24 = cur;
  v23 = pos;
  v22 = this;
  v5 = &v12;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( pos < 0 || pos >= 5 )
    v23 = 4;
  v13 = DOUBLE_3_1415926535;
  v14 = v23;
  v15 = v23;
  for ( j = 0; j < 5; ++j )
  {
    if ( (signed int)v14 >= 5 )
      v14 = 0;
    if ( (v15 & 0x80000000) != 0 )
      v15 = 4;
    if ( !v22->m_AroundSlot[v14] )
    {
      memcpy_0(&Dst, target, 0xCui64);
      v7 = 2.0 * v13 * (float)(signed int)(v14 + 1) / 5.0;
      v20 = v7;
      cos(v7);
      Dst = Dst + (float)(v7 * dist);
      v8 = v20;
      sin(v20);
      v17 = v17 + (float)(v8 * dist);
      if ( (unsigned int)CBsp::CanYouGoThere(v22->m_pCurMap->m_Level.mBsp, v24, &Dst, (float (*)[3])v18) )
        return v14;
    }
    if ( !v22->m_AroundSlot[v15] )
    {
      memcpy_0(&Dst, target, 0xCui64);
      v10 = 2.0 * v13 * (float)(signed int)(v15 + 1) / 5.0;
      v21 = v10;
      cos(v10);
      Dst = Dst + (float)(v10 * dist);
      v11 = v21;
      sin(v21);
      v17 = v17 + (float)(v11 * dist);
      if ( (unsigned int)CBsp::CanYouGoThere(v22->m_pCurMap->m_Level.mBsp, v24, &Dst, (float (*)[3])v18) )
        return v15;
    }
    ++v14;
    --v15;
  }
  return 0xFFFFFFFFi64;
}
