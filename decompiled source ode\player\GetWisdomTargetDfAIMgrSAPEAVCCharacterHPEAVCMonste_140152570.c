/*
 * Function: ?GetWisdomTarget@DfAIMgr@@SAPEAVCCharacter@@HPEAVCMonsterAI@@PEAVCMonster@@@Z
 * Address: 0x140152570
 */

CCharacter *__fastcall DfAIMgr::GetWisdomTarget(int nDstCaseType, CMonsterAI *pAI, CMonster *pMon)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CCharacter *result; // rax@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  int v7; // [sp+30h] [bp+8h]@1

  v7 = nDstCaseType;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v7 )
  {
    if ( v7 == 1 )
    {
      result = (CCharacter *)pMon;
    }
    else if ( v7 == 2 )
    {
      result = (CCharacter *)&pAI->m_pAsistMonster->vfptr;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = CMonster::GetAttackTarget(pMon);
  }
  return result;
}
