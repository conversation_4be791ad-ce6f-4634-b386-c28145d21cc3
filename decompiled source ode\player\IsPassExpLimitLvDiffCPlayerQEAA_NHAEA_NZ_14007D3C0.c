/*
 * Function: ?IsPassExpLimitLvDiff@CPlayer@@QEAA_NHAEA_N@Z
 * Address: 0x14007D3C0
 */

char __usercall CPlayer::IsPassExpLimitLvDiff@<al>(CPlayer *this@<rcx>, int iDstLevel@<edx>, bool *bGetAttackExp@<r8>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  int v8; // [sp+20h] [bp-18h]@4
  float v9; // [sp+24h] [bp-14h]@4
  CPlayer *v10; // [sp+40h] [bp+8h]@1
  bool *v11; // [sp+50h] [bp+18h]@1

  v11 = bGetAttackExp;
  v10 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  *bGetAttackExp = 1;
  v8 = ((int (__fastcall *)(CPlayer *))v10->vfptr->GetLevel)(v10) - iDstLevel;
  _effect_parameter::GetEff_Have(&v10->m_EP, 52);
  v9 = a4;
  if ( (float)v8 > (float)(a4 + 3.0) )
  {
    *v11 = 0;
    return 0;
  }
  if ( (float)(-10.0 - v9) > (float)v8 )
  {
    if ( !CPartyPlayer::IsPartyMode(v10->m_pPartyMgr) )
    {
      *v11 = 0;
      return 0;
    }
    *v11 = 0;
  }
  return 1;
}
