/*
 * Function: ?_check_hp_send_party@CPlayer@@QEAAXXZ
 * Address: 0x1400FF190
 */

void __fastcall CPlayer::_check_hp_send_party(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int v4; // [sp+20h] [bp-18h]@5
  int v5; // [sp+24h] [bp-14h]@7
  int v6; // [sp+28h] [bp-10h]@9
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CPartyPlayer::IsPartyMode(v7->m_pPartyMgr) )
  {
    v4 = (unsigned __int16)(*(int (__fastcall **)(CPlayer *))&v7->vfptr->gap8[8])(v7);
    if ( abs_0(v4 - v7->m_wPointRate_PartySend[0]) > 500 )
    {
      v7->m_wPointRate_PartySend[0] = v4;
      CPlayer::SendData_PartyMemberHP(v7);
    }
    v5 = CPlayer::CalcCurFPRate(v7);
    if ( abs_0(v5 - v7->m_wPointRate_PartySend[1]) > 500 )
    {
      v7->m_wPointRate_PartySend[1] = v5;
      CPlayer::SendData_PartyMemberFP(v7);
    }
    v6 = CPlayer::CalcCurSPRate(v7);
    if ( abs_0(v6 - v7->m_wPointRate_PartySend[2]) > 500 )
    {
      v7->m_wPointRate_PartySend[2] = v6;
      CPlayer::SendData_PartyMemberSP(v7);
    }
  }
}
