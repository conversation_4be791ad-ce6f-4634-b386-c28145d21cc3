/*
 * Function: ?Init@C24Timer@@QEAAXXZ
 * Address: 0x140284E10
 */

void __fastcall C24Timer::Init(C24Timer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int v4; // [sp+20h] [bp-18h]@4
  C24Timer *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->m_dwBaseTickTime = GetLoopTime();
  v4 = 60 * GetCurrentHour();
  v5->m_dwBase24Time = GetCurrentMin() + v4;
}
