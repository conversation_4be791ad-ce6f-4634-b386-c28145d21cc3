/*
 * Function: j_?construct@?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@QEAAXPEAU?$pair@$$CBHPEAVCAsyncLogInfo@@@2@AEBU32@@Z
 * Address: 0x14000ECC8
 */

void __fastcall std::allocator<std::pair<int const,CAsyncLogInfo *>>::construct(std::allocator<std::pair<int const ,CAsyncLogInfo *> > *this, std::pair<int const ,CAsyncLogInfo *> *_Ptr, std::pair<int const ,CAsyncLogInfo *> *_Val)
{
  std::allocator<std::pair<int const,CAsyncLogInfo *>>::construct(this, _Ptr, _Val);
}
