/*
 * Function: ?Load@CReservedGuildScheduleMapGroup@GUILD_BATTLE@@QEAA_NEAEAU_worlddb_guild_battle_reserved_schedule_info@@@Z
 * Address: 0x1403CC540
 */

char __fastcall GUILD_BATTLE::CReservedGuildScheduleMapGroup::Load(GUILD_BATTLE::CReservedGuildScheduleMapGroup *this, char byDayID, _worlddb_guild_battle_reserved_schedule_info *kInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v6; // [sp+0h] [bp-48h]@1
  unsigned __int8 v7; // [sp+20h] [bp-28h]@4
  unsigned __int16 v8; // [sp+24h] [bp-24h]@9
  char *Dest; // [sp+28h] [bp-20h]@9
  _worlddb_guild_battle_reserved_schedule_info::__list *v10; // [sp+30h] [bp-18h]@9
  unsigned __int8 j; // [sp+38h] [bp-10h]@9
  unsigned __int8 v12; // [sp+39h] [bp-Fh]@11
  unsigned __int8 v13; // [sp+3Ah] [bp-Eh]@19
  GUILD_BATTLE::CReservedGuildScheduleMapGroup *v14; // [sp+50h] [bp+8h]@1
  char v15; // [sp+58h] [bp+10h]@1
  _worlddb_guild_battle_reserved_schedule_info *v16; // [sp+60h] [bp+18h]@1

  v16 = kInfo;
  v15 = byDayID;
  v14 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14->m_byMaxPage = (signed int)kInfo->wCount >> 2;
  v7 = kInfo->wCount & 3;
  if ( v14->m_byMaxPage || v7 )
  {
    if ( (unsigned int)v14->m_byMaxPage >= 6 )
    {
      v14->m_byMaxPage = 5;
      v7 = 0;
    }
    v8 = 0;
    Dest = 0i64;
    v10 = 0i64;
    for ( j = 0; j < (signed int)v14->m_byMaxPage; ++j )
    {
      v12 = 0;
      while ( (signed int)v12 < 4 )
      {
        Dest = v14->m_kList[j].m_pkList->List[v12].wsz1PName;
        v10 = &v16->list[v8];
        v14->m_kList[j].m_dw1PGuildSerial[v12] = v10->dw1PGuildSerial;
        strcpy_0(Dest, v10->wsz1PName);
        Dest[17] = v10->by1PRace;
        v14->m_kList[j].m_dw2PGuildSerial[v12] = v16->list[v8].dw2PGuildSerial;
        strcpy_0(Dest + 18, v10->wsz2PName);
        Dest[35] = v10->by2PRace;
        Dest[36] = v10->byStartHour;
        Dest[37] = v10->byStartMin;
        Dest[38] = v10->byEndHour;
        Dest[39] = v10->byEndMin;
        if ( v16->wCount <= (signed int)v8 )
          break;
        ++v12;
        ++v8;
      }
      v14->m_kList[j].m_pkList->byDate = v15;
      v14->m_kList[j].m_pkList->byCnt = 4;
    }
    if ( (signed int)v7 > 0 && (unsigned int)++v14->m_byMaxPage > 6 )
      v14->m_byMaxPage = 6;
    v13 = 0;
    while ( v13 < (signed int)v7 )
    {
      Dest = v14->m_kList[j].m_pkList->List[v13].wsz1PName;
      v10 = &v16->list[v8];
      v14->m_kList[j].m_dw1PGuildSerial[v13] = v10->dw1PGuildSerial;
      strcpy_0(Dest, v10->wsz1PName);
      Dest[17] = v10->by1PRace;
      v14->m_kList[j].m_dw2PGuildSerial[v13] = v16->list[v8].dw2PGuildSerial;
      strcpy_0(Dest + 18, v10->wsz2PName);
      Dest[35] = v10->by2PRace;
      Dest[36] = v10->byStartHour;
      Dest[37] = v10->byStartMin;
      Dest[38] = v10->byEndHour;
      Dest[39] = v10->byEndMin;
      if ( v16->wCount <= (signed int)v8 )
        break;
      ++v13;
      ++v8;
    }
    if ( (signed int)v7 > 0 )
    {
      v14->m_kList[j].m_pkList->byDate = v15;
      v14->m_kList[j].m_pkList->byCnt = v8 & 3;
    }
    for ( j = 0; j < (signed int)v14->m_byMaxPage; ++j )
    {
      v14->m_kList[j].m_pkList->byMaxPage = v14->m_byMaxPage;
      GUILD_BATTLE::CReservedGuildSchedulePage::IncVer(&v14->m_kList[j]);
    }
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
