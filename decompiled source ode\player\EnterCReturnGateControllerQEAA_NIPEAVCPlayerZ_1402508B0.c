/*
 * Function: ?Enter@CReturnGateController@@QEAA_NIPEAVCPlayer@@@Z
 * Address: 0x1402508B0
 */

char __fastcall CReturnGateController::Enter(CReturnGateController *this, unsigned int uiGateInx, CPlayer *pkObj)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  int iResult; // [sp+20h] [bp-18h]@4
  CReturnGateController *v8; // [sp+40h] [bp+8h]@1
  CPlayer *pkObja; // [sp+50h] [bp+18h]@1

  pkObja = pkObj;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  iResult = CReturnGateController::ProcessEnter(v8, uiGateInx, pkObj);
  if ( iResult )
  {
    CReturnGateController::SendEnterResult(v8, iResult, pkObja);
    result = 0;
  }
  else
  {
    result = 1;
  }
  return result;
}
