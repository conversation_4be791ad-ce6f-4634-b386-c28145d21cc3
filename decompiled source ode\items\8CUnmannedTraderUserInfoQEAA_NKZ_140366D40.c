/*
 * Function: ??8CUnmannedTraderUserInfo@@QEAA_NK@Z
 * Address: 0x140366D40
 */

bool __fastcall CUnmannedTraderUserInfo::operator==(CUnmannedTraderUserInfo *this, unsigned int dwSerial)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // [sp+0h] [bp-18h]@1
  CUnmannedTraderUserInfo *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  return v6->m_dwUserSerial && v6->m_dwUserSerial == dwSerial;
}
