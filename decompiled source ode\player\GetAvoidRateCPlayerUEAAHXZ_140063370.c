/*
 * Function: ?GetAvoidRate@CPlayer@@UEAAHXZ
 * Address: 0x140063370
 */

__int64 __fastcall CPlayer::GetAvoidRate(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  float v4; // xmm1_4@6
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@6
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7->m_fTalik_AvoidPoint > 0.0 )
  {
    CPlayer::CalcDPRate(v7);
    v4 = v7->m_fTalik_AvoidPoint * (float)(1.0 - 0.0);
    v6 = (signed int)ffloor(v4);
    _effect_parameter::GetEff_Plus(&v7->m_EP, 3);
    result = (unsigned int)(signed int)ffloor(v4 - (float)v6);
  }
  else
  {
    _effect_parameter::GetEff_Plus(&v7->m_EP, 3);
    result = (unsigned int)(signed int)ffloor(0.0);
  }
  return result;
}
