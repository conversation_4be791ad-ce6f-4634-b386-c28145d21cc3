/*
 * Function: ?_GetFlashEffectMember@CCharacter@@QEAAHPEAV1@_NHH0PEADPEAPEAV1@@Z
 * Address: 0x140177970
 */

__int64 __fastcall CCharacter::_GetFlashEffectMember(CCharacter *this, CCharacter *pOriDst, bool bBenefit, int nLimitRadius, int nLimitAngle, CCharacter *pOriTar, char *psActableDst, CCharacter **ppDsts)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  int v11; // eax@8
  _sec_info *v12; // rax@13
  int v13; // eax@23
  int v14; // eax@25
  float v15; // xmm0_4@35
  __int64 v16; // [sp+0h] [bp-C8h]@1
  unsigned int v17; // [sp+30h] [bp-98h]@6
  _pnt_rect pRect; // [sp+48h] [bp-80h]@8
  int j; // [sp+64h] [bp-64h]@8
  int k; // [sp+68h] [bp-60h]@10
  unsigned int dwSecIndex; // [sp+6Ch] [bp-5Ch]@13
  CObjectList *v22; // [sp+70h] [bp-58h]@13
  CObjectList *v23; // [sp+78h] [bp-50h]@14
  CCharacter *pDst; // [sp+80h] [bp-48h]@16
  CPlayer *v25; // [sp+88h] [bp-40h]@27
  float v26; // [sp+90h] [bp-38h]@35
  int v27; // [sp+94h] [bp-34h]@23
  CGameObjectVtbl *v28; // [sp+98h] [bp-30h]@23
  int v29; // [sp+A0h] [bp-28h]@25
  CGameObjectVtbl *v30; // [sp+A8h] [bp-20h]@25
  float *chkpos; // [sp+B0h] [bp-18h]@36
  CCharacter *v32; // [sp+D0h] [bp+8h]@1
  CCharacter *v33; // [sp+D8h] [bp+10h]@1
  bool v34; // [sp+E0h] [bp+18h]@1
  int v35; // [sp+E8h] [bp+20h]@1

  v35 = nLimitRadius;
  v34 = bBenefit;
  v33 = pOriDst;
  v32 = this;
  v8 = &v16;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  if ( !pOriTar )
    return 0i64;
  v17 = 0;
  *ppDsts = pOriTar;
  ++v17;
  if ( CGameObject::GetCurSecNum((CGameObject *)&pOriTar->vfptr) == -1 )
    return v17;
  v11 = CGameObject::GetCurSecNum((CGameObject *)&pOriTar->vfptr);
  CMapData::GetRectInRadius(v32->m_pCurMap, &pRect, 1, v11);
  for ( j = pRect.nStarty; j <= pRect.nEndy; ++j )
  {
    for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
    {
      v12 = CMapData::GetSecInfo(v32->m_pCurMap);
      dwSecIndex = v12->m_nSecNumW * j + k;
      v22 = CMapData::GetSectorListObj(v32->m_pCurMap, v32->m_wMapLayerIndex, dwSecIndex);
      if ( v22 )
      {
        v23 = (CObjectList *)v22->m_Head.m_pNext;
        while ( 1 )
        {
          while ( 1 )
          {
            while ( 1 )
            {
              if ( (_object_list_point *)v23 == &v22->m_Tail )
                goto LABEL_11;
              pDst = (CCharacter *)v23->vfptr;
              v23 = (CObjectList *)v23->m_Head.m_pItem;
              if ( v33 != pDst && !pDst->m_ObjID.m_byKind && CCharacter::IsEffectableDst(v32, psActableDst, pDst) )
              {
                if ( (signed int)v17 >= 29 )
                  return v17;
                if ( !pDst->m_bCorpse )
                  break;
              }
            }
            if ( !v34 )
              break;
            v27 = ((int (__fastcall *)(CCharacter *))pDst->vfptr->GetObjRace)(pDst);
            v28 = v32->vfptr;
            v13 = ((int (__fastcall *)(CCharacter *))v28->GetObjRace)(v32);
            if ( v27 == v13 )
              goto LABEL_35;
          }
          v29 = ((int (__fastcall *)(CCharacter *))pDst->vfptr->GetObjRace)(pDst);
          v30 = v32->vfptr;
          v14 = ((int (__fastcall *)(CCharacter *))v30->GetObjRace)(v32);
          if ( v29 == v14 && !pDst->m_ObjID.m_byID )
          {
            v25 = (CPlayer *)pDst;
            if ( !CPlayer::IsPunished((CPlayer *)pDst, 1, 0) && !CPlayer::IsChaosMode(v25) )
              continue;
          }
          if ( ((unsigned __int8)((int (__fastcall *)(CCharacter *))v32->vfptr->IsAttackableInTown)(v32)
             || (unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsAttackableInTown)(pDst)
             || !(unsigned __int8)((int (__fastcall *)(CCharacter *))v32->vfptr->IsInTown)(v32)
             && !(unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsInTown)(pDst))
            && (unsigned __int8)((int (__fastcall *)(CCharacter *, CCharacter *))pDst->vfptr->IsBeDamagedAble)(
                                  pDst,
                                  v32) )
          {
LABEL_35:
            v15 = v32->m_fCurPos[1] - pDst->m_fCurPos[1];
            abs(v15);
            v26 = v15;
            if ( v15 <= 350.0 )
            {
              chkpos = pDst->m_fCurPos;
              if ( CAttack::IsCharInSector(
                     pDst->m_fCurPos,
                     v32->m_fCurPos,
                     pOriTar->m_fCurPos,
                     (float)nLimitAngle,
                     (float)v35) )
              {
                ppDsts[v17++] = pDst;
              }
            }
          }
        }
      }
LABEL_11:
      ;
    }
  }
  return v17;
}
