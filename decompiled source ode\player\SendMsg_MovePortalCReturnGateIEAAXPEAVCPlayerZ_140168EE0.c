/*
 * Function: ?SendMsg_MovePortal@CReturnGate@@IEAAXPEAVCPlayer@@@Z
 * Address: 0x140168EE0
 */

void __fastcall CReturnGate::SendMsg_MovePortal(CReturnGate *this, CPlayer *pkObj)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  char v6; // [sp+39h] [bp-4Fh]@4
  char Dst; // [sp+3Ah] [bp-4Eh]@4
  char v8; // [sp+46h] [bp-42h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v10; // [sp+65h] [bp-23h]@4
  CReturnGate *v11; // [sp+90h] [bp+8h]@1
  CPlayer *v12; // [sp+98h] [bp+10h]@1

  v12 = pkObj;
  v11 = this;
  v2 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  szMsg = 0;
  v6 = v11->m_pDestMap->m_pMapSet->m_dwIndex;
  memcpy_0(&Dst, v11->m_fBindPos, 0xCui64);
  v8 = 2;
  pbyType = 8;
  v10 = 2;
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, &szMsg, 0xFu);
}
