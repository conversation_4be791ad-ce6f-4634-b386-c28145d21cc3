/*
 * Function: ?IsGoal@CNormalGuildBattleField@GUILD_BATTLE@@QEAAEPEAVCPlayer@@H@Z
 * Address: 0x1403ECE70
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleField::IsGoal(GUILD_BATTLE::CNormalGuildBattleField *this, CPlayer *pkPlayer, int iPortalInx)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-48h]@1
  float *pfCurPos; // [sp+20h] [bp-28h]@9
  char v8; // [sp+28h] [bp-20h]@11
  unsigned int k; // [sp+2Ch] [bp-1Ch]@12
  unsigned int j; // [sp+30h] [bp-18h]@21
  GUILD_BATTLE::CNormalGuildBattleField *v11; // [sp+50h] [bp+8h]@1
  CPlayer *pkPlayera; // [sp+58h] [bp+10h]@1
  int v13; // [sp+60h] [bp+18h]@1

  v13 = iPortalInx;
  pkPlayera = pkPlayer;
  v11 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v11->m_bInit )
  {
    if ( CGravityStone::IsValidOwner(v11->m_pkBall, pkPlayer) && pkPlayera->m_bTakeGravityStone )
    {
      pfCurPos = CGravityStone::GetOwnerCurPos(v11->m_pkBall);
      if ( pfCurPos )
      {
        v8 = 0;
        if ( pkPlayera->m_byGuildBattleColorInx )
        {
          if ( pkPlayera->m_byGuildBattleColorInx == 1 )
          {
            for ( j = 0; ; ++j )
            {
              if ( j >= v11->m_ui1PGoalPosCnt )
                return -118;
              if ( v13 == CCircleZone::GetPortalInx(&v11->m_pk1PGoalZone[j]) )
                break;
            }
            v8 = CCircleZone::Goal(&v11->m_pk1PGoalZone[j], v11->m_pkMap, pfCurPos);
            if ( v8 )
              result = v8;
            else
              result = 0;
          }
          else
          {
            result = 110;
          }
        }
        else
        {
          for ( k = 0; ; ++k )
          {
            if ( k >= v11->m_ui2PGoalPosCnt )
              return -118;
            if ( v13 == CCircleZone::GetPortalInx(&v11->m_pk2PGoalZone[k]) )
              break;
          }
          v8 = CCircleZone::Goal(&v11->m_pk2PGoalZone[k], v11->m_pkMap, pfCurPos);
          if ( v8 )
            result = v8;
          else
            result = 0;
        }
      }
      else
      {
        result = -125;
      }
    }
    else
    {
      result = -125;
    }
  }
  else
  {
    result = 110;
  }
  return result;
}
