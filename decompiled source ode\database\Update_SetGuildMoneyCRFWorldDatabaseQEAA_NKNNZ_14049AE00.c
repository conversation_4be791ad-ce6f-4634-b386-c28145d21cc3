/*
 * Function: ?Update_SetGuildMoney@CRFWorldDatabase@@QEAA_NKNN@Z
 * Address: 0x14049AE00
 */

bool __fastcall CRFWorldDatabase::Update_SetGuildMoney(CRFWorldDatabase *this, unsigned int dwGuildSerial, long double dDalant, long double dGold)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp-20h] [bp-168h]@1
  long double v8; // [sp+0h] [bp-148h]@4
  unsigned int v9; // [sp+8h] [bp-140h]@4
  char DstBuf; // [sp+20h] [bp-128h]@4
  char v11; // [sp+21h] [bp-127h]@4
  unsigned __int64 v12; // [sp+130h] [bp-18h]@4
  CRFWorldDatabase *v13; // [sp+150h] [bp+8h]@1

  v13 = this;
  v4 = &v7;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = (unsigned __int64)&v7 ^ _security_cookie;
  DstBuf = 0;
  memset(&v11, 0, 0xFFui64);
  v9 = dwGuildSerial;
  v8 = dGold;
  sprintf_s(&DstBuf, 0x100ui64, "update [dbo].[tbl_guild] set [Dalant]=%f, [Gold]=%f where [Serial]=%d", dDalant);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v13->vfptr, &DstBuf, 1);
}
