/*
 * Function: ?insert_atrade_taxrate@CRFWorldDatabase@@QEAA_NEKPEADK0EK@Z
 * Address: 0x1404A8650
 */

bool __fastcall CRFWorldDatabase::insert_atrade_taxrate(CRFWorldDatabase *this, char byRace, unsigned int dwSerial, char *wszName, unsigned int dwMatterDst, char *wszMatterDst, char byCurrTax, unsigned int dwNext)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v11; // [sp+0h] [bp-488h]@1
  char *v12; // [sp+20h] [bp-468h]@4
  int v13; // [sp+28h] [bp-460h]@4
  unsigned int v14; // [sp+30h] [bp-458h]@4
  unsigned int v15; // [sp+38h] [bp-450h]@4
  char *v16; // [sp+40h] [bp-448h]@4
  char Dest; // [sp+60h] [bp-428h]@4
  char v18; // [sp+61h] [bp-427h]@4
  unsigned __int64 v19; // [sp+470h] [bp-18h]@4
  CRFWorldDatabase *v20; // [sp+490h] [bp+8h]@1

  v20 = this;
  v8 = &v11;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v8 = -*********;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v19 = (unsigned __int64)&v11 ^ _security_cookie;
  Dest = 0;
  memset(&v18, 0, 0x3FFui64);
  v16 = wszMatterDst;
  v15 = dwMatterDst;
  v14 = dwNext;
  v13 = (unsigned __int8)byCurrTax;
  v12 = wszName;
  sprintf(
    &Dest,
    "Insert [dbo].[tbl_atradetaxrate] (Race, GSerial, GName, Tax, NextTax, suggester, suggestername) Values(%d, %d, '%s',"
    " %d, %d, %d, '%s')",
    (unsigned __int8)byRace,
    dwSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v20->vfptr, &Dest, 1);
}
