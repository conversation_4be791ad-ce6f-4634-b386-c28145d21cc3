/*
 * Function: ?GetResDummySector@CMapData@@QEAAHHPEAM@Z
 * Address: 0x140184950
 */

signed __int64 __fastcall CMapData::GetResDummySector(CMapData *this, int nDummyIndex, float *pCurPos)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-58h]@1
  _res_dummy *v7; // [sp+20h] [bp-38h]@6
  unsigned int v8; // [sp+28h] [bp-30h]@8
  _dummy_position *v9; // [sp+30h] [bp-28h]@9
  struct _EXT_DUMMY *v10; // [sp+38h] [bp-20h]@9
  unsigned int j; // [sp+40h] [bp-18h]@11
  CMapData *v12; // [sp+60h] [bp+8h]@1
  float *v13; // [sp+70h] [bp+18h]@1

  v13 = pCurPos;
  v12 = this;
  v3 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v12->m_nResDumNum <= 0 )
    return 0xFFFFFFFFi64;
  v7 = &v12->m_pResDummy[nDummyIndex];
  if ( !CExtDummy::IsInBBox(&v12->m_Dummy, v7->m_pDumPos->m_wLineIndex, pCurPos) )
    return 0xFFFFFFFFi64;
  v8 = -1;
  if ( _res_dummy::GetQualityGrade(v7) )
  {
    v8 = 100;
  }
  else
  {
    v9 = v7->m_pDumPos;
    v10 = CExtDummy::GetDummy(&v12->m_Dummy, v9->m_wLineIndex);
    if ( !v10 )
      return 0xFFFFFFFFi64;
    for ( j = 0; (signed int)j < 3; ++j )
    {
      memcpy_0(v10->mBBmin, v7->m_fMinLocal[j], 0xCui64);
      memcpy_0(v10->mBBmax, v7->m_fMaxLocal[j], 0xCui64);
      if ( CExtDummy::IsInBBox(&v12->m_Dummy, v9->m_wLineIndex, v13) )
      {
        v8 = j;
        break;
      }
    }
    memcpy_0(v10->mBBmin, v7->m_fMinLocal[2], 0xCui64);
    memcpy_0(v10->mBBmax, v7->m_fMaxLocal[2], 0xCui64);
  }
  return v8;
}
