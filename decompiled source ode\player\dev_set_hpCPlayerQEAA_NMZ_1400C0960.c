/*
 * Function: ?dev_set_hp@CPlayer@@QEAA_NM@Z
 * Address: 0x1400C0960
 */

char __fastcall CPlayer::dev_set_hp(CPlayer *this, float prob)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-68h]@1
  unsigned __int8 v6; // [sp+20h] [bp-48h]@7
  _base_fld *v7; // [sp+28h] [bp-40h]@7
  unsigned int v8; // [sp+30h] [bp-38h]@9
  unsigned int v9; // [sp+34h] [bp-34h]@9
  unsigned int v10; // [sp+38h] [bp-30h]@19
  int v11; // [sp+3Ch] [bp-2Ch]@19
  unsigned int v12; // [sp+40h] [bp-28h]@10
  unsigned int v13; // [sp+44h] [bp-24h]@13
  unsigned int v14; // [sp+48h] [bp-20h]@15
  unsigned int v15; // [sp+4Ch] [bp-1Ch]@20
  unsigned int v16; // [sp+50h] [bp-18h]@23
  unsigned int v17; // [sp+54h] [bp-14h]@25
  CPlayer *v18; // [sp+70h] [bp+8h]@1

  v18 = this;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CPlayer::IsRidingUnit(v18) )
  {
    if ( !v18->m_pUsingUnit )
      return 0;
    v6 = v18->m_pUsingUnit->byFrame;
    v7 = CRecordData::GetRecord(&stru_1799C8BA0, v6);
    if ( !v7 )
      return 0;
    v8 = *(_DWORD *)&v7[1].m_strCode[0];
    v9 = (signed int)ffloor((float)(signed int)v8 * prob);
    if ( v9 >= 1 )
      v12 = v9;
    else
      v12 = 1;
    if ( v8 >= v12 )
    {
      if ( v9 >= 1 )
        v14 = v9;
      else
        v14 = 1;
      v13 = v14;
    }
    else
    {
      v13 = v8;
    }
    v9 = v13;
    v18->m_pUsingUnit->dwGauge = v13;
    CPlayer::SendMsg_AlterUnitHPInform(v18, v18->m_pUsingUnit->bySlotIndex, v18->m_pUsingUnit->dwGauge);
  }
  else
  {
    v10 = ((int (__fastcall *)(CPlayer *))v18->vfptr->GetMaxHP)(v18);
    v11 = (signed int)ffloor((float)(signed int)v10 * prob);
    if ( (unsigned int)v11 >= 1 )
      v15 = v11;
    else
      v15 = 1;
    if ( v10 >= v15 )
    {
      if ( (unsigned int)v11 >= 1 )
        v17 = v11;
      else
        v17 = 1;
      v16 = v17;
    }
    else
    {
      v16 = v10;
    }
    v11 = v16;
    ((void (__fastcall *)(CPlayer *, _QWORD, _QWORD))v18->vfptr->SetHP)(v18, v16, 0i64);
    (*(void (__fastcall **)(CPlayer *))&v18->vfptr->gap8[72])(v18);
  }
  return 1;
}
