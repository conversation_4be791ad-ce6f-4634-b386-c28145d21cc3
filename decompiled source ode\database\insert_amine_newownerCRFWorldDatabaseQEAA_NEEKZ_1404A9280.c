/*
 * Function: ?insert_amine_newowner@CRFWorldDatabase@@QEAA_NEEK@Z
 * Address: 0x1404A9280
 */

bool __fastcall CRFWorldDatabase::insert_amine_newowner(CRFWorldDatabase *this, char byType, char byRace, unsigned int dwSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-E8h]@1
  unsigned int v8; // [sp+20h] [bp-C8h]@4
  char Dest; // [sp+40h] [bp-A8h]@4
  char v10; // [sp+41h] [bp-A7h]@4
  unsigned __int64 v11; // [sp+D0h] [bp-18h]@4
  CRFWorldDatabase *v12; // [sp+F0h] [bp+8h]@1

  v12 = this;
  v4 = &v7;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = (unsigned __int64)&v7 ^ _security_cookie;
  Dest = 0;
  memset(&v10, 0, 0x7Fui64);
  v8 = dwSerial;
  sprintf(&Dest, "{ CALL pinsert_automine_newowner(%d,%d,%d) }", (unsigned __int8)byType, (unsigned __int8)byRace);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v12->vfptr, &Dest, 1);
}
