/*
 * Function: ?ct_set_exp_rate@@YA_NPEAVCPlayer@@@Z
 * Address: 0x1402964F0
 */

char __fastcall ct_set_exp_rate(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  float v4; // xmm0_4@8
  double v5; // xmm0_8@9
  cStaticMember_Player *v6; // rax@11
  double v7; // xmm0_8@11
  __int64 v8; // [sp+0h] [bp-58h]@1
  float v9; // [sp+30h] [bp-28h]@8
  double v10; // [sp+38h] [bp-20h]@11
  int lv; // [sp+40h] [bp-18h]@11
  double v12; // [sp+48h] [bp-10h]@11
  CPlayer *v13; // [sp+60h] [bp+8h]@1

  v13 = pOne;
  v1 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v13 )
  {
    if ( s_nWordCount == 1 )
    {
      v4 = atof(s_pwszDstCheat[0]);
      v9 = v4;
      if ( v4 >= 0.0 && (*(_QWORD *)&v5 = LODWORD(v9), v9 < 100.0) )
      {
        lv = CPlayerDB::GetLevel(&v13->m_Param);
        v6 = cStaticMember_Player::Instance();
        cStaticMember_Player::GetLimitExp(v6, lv);
        v7 = v5 * (float)(v9 / 100.0);
        v12 = v7;
        CPlayerDB::GetExp(&v13->m_Param);
        v10 = v12 - v7;
        CPlayer::AlterExp(v13, v12 - v7, 1, 0, 0);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
