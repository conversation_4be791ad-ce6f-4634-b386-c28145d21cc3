/*
 * Function: ??0_worlddb_sf_delay_info@@QEAA@XZ
 * Address: 0x140077E60
 */

void __fastcall _worlddb_sf_delay_info::_worlddb_sf_delay_info(_worlddb_sf_delay_info *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _worlddb_sf_delay_info *__t; // [sp+30h] [bp+8h]@1

  __t = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  `vector constructor iterator'(__t, 7ui64, 10, (void *(__cdecl *)(void *))_worlddb_sf_delay_info::_eff_list::_eff_list);
  `vector constructor iterator'(
    __t->MAS,
    6ui64,
    10,
    (void *(__cdecl *)(void *))_worlddb_sf_delay_info::_mas_list::_mas_list);
}
