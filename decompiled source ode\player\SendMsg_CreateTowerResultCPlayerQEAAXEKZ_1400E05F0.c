/*
 * Function: ?SendMsg_CreateTowerResult@CPlayer@@QEAAXEK@Z
 * Address: 0x1400E05F0
 */

void __fastcall CPlayer::SendMsg_CreateTowerResult(CPlayer *this, char byErrCode, unsigned int dwTowerObjSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  unsigned int v7; // [sp+35h] [bp-43h]@4
  __int16 v8; // [sp+39h] [bp-3Fh]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v10; // [sp+55h] [bp-23h]@4
  CPlayer *v11; // [sp+80h] [bp+8h]@1

  v11 = this;
  v3 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szMsg = byErrCode;
  v7 = dwTowerObjSerial;
  v8 = CPlayer::GetFP(v11);
  pbyType = 17;
  v10 = 19;
  CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, &szMsg, 7u);
}
