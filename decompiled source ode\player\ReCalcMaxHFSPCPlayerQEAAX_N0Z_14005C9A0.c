/*
 * Function: ?ReCalcMaxHFSP@CPlayer@@QEAAX_N0@Z
 * Address: 0x14005C9A0
 */

void __fastcall CPlayer::ReCalcMaxHFSP(CPlayer *this, bool bSend, bool bRatio)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6->m_nMaxPoint[0] = CPlayer::_CalcMaxHP(v6);
  v6->m_nMaxPoint[1] = CPlayer::_CalcMaxFP(v6);
  v6->m_nMaxPoint[2] = CPlayer::_CalcMaxSP(v6);
}
