/*
 * Function: ?pc_TakeGroundingItem@CPlayer@@QEAAXPEAVCItemBox@@G@Z
 * Address: 0x1400B3240
 */

void __usercall CPlayer::pc_TakeGroundingItem(CPlayer *this@<rcx>, CItemBox *pBox@<rdx>, unsigned __int16 wAddSerial@<r8w>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char v6; // al@60
  CGoldenBoxItemMgr *v7; // rax@61
  unsigned int v8; // eax@67
  char *v9; // rax@74
  char *v10; // rdx@75
  int v11; // er10@75
  char *v12; // rax@77
  __int64 v13; // [sp+0h] [bp-148h]@1
  bool bAdd[8]; // [sp+20h] [bp-128h]@54
  bool bSend[8]; // [sp+28h] [bp-120h]@54
  char *pszThrowerID; // [sp+30h] [bp-118h]@74
  unsigned __int16 wMonRecIndex; // [sp+38h] [bp-110h]@75
  char *pMapCode; // [sp+40h] [bp-108h]@75
  float *pfPos; // [sp+48h] [bp-100h]@75
  char *pszFileName; // [sp+50h] [bp-F8h]@75
  char v21; // [sp+60h] [bp-E8h]@4
  __int16 v22; // [sp+64h] [bp-E4h]@4
  _STORAGE_LIST::_db_con Dst; // [sp+78h] [bp-D0h]@4
  _STORAGE_LIST::_db_con *v24; // [sp+B8h] [bp-90h]@4
  DWORD v25; // [sp+C0h] [bp-88h]@4
  int v26; // [sp+C4h] [bp-84h]@4
  _STORAGE_LIST::_db_con *pItem; // [sp+C8h] [bp-80h]@4
  bool v28; // [sp+D0h] [bp-78h]@52
  _base_fld *v29; // [sp+D8h] [bp-70h]@58
  _base_fld *v30; // [sp+E0h] [bp-68h]@64
  char v31; // [sp+E8h] [bp-60h]@68
  _base_fld *v32; // [sp+F0h] [bp-58h]@74
  _base_fld *v33; // [sp+F8h] [bp-50h]@74
  _base_fld *v34; // [sp+100h] [bp-48h]@78
  bool v35; // [sp+108h] [bp-40h]@78
  _base_fld *v36; // [sp+110h] [bp-38h]@83
  char v37; // [sp+118h] [bp-30h]@88
  char *szCharName; // [sp+120h] [bp-28h]@67
  DWORD v39; // [sp+128h] [bp-20h]@74
  char *pszTakerID; // [sp+130h] [bp-18h]@77
  CPlayer *pOne; // [sp+150h] [bp+8h]@1
  CItemBox *pItemBox; // [sp+158h] [bp+10h]@1
  signed __int16 v43; // [sp+160h] [bp+18h]@1

  v43 = wAddSerial;
  pItemBox = pBox;
  pOne = this;
  v4 = &v13;
  for ( i = 80i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v21 = 0;
  v22 = 0;
  _STORAGE_LIST::_db_con::_db_con(&Dst);
  v24 = 0i64;
  v25 = timeGetTime();
  v26 = 1;
  pItem = 0i64;
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, pOne->m_id.wIndex) == 99 )
  {
    v21 = 16;
    goto $RESULT_42;
  }
  if ( v25 - pOne->m_dwLastTakeItemTime < 0x190 )
  {
    v21 = 9;
    goto $RESULT_42;
  }
  if ( !pItemBox->m_bLive )
  {
    v21 = 4;
    goto $RESULT_42;
  }
  if ( !CItemBox::IsTakeRight(pItemBox, pOne) )
  {
    v21 = 5;
    goto $RESULT_42;
  }
  GetSqrt(pItemBox->m_fCurPos, pOne->m_fCurPos);
  if ( a4 > 100.0 )
  {
    v21 = 6;
    goto $RESULT_42;
  }
  memcpy_0(&Dst, &pItemBox->m_Item, 0x32ui64);
  v26 = IsSaveItem((unsigned __int8)Dst.m_byTableCode);
  if ( Dst.m_byTableCode == 19 )
  {
    v21 = 8;
    goto $RESULT_42;
  }
  if ( _effect_parameter::GetEff_State(&pOne->m_EP, 20) )
  {
    v21 = 13;
    goto $RESULT_42;
  }
  if ( _effect_parameter::GetEff_State(&pOne->m_EP, 28) )
  {
    v21 = 13;
    goto $RESULT_42;
  }
  if ( v26 )
  {
    if ( (unsigned __int16)v43 == 0xFFFF )
    {
      if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&pOne->m_Param.m_dbInven.m_nListNum) == 255 )
        v21 = 1;
    }
    else
    {
      if ( !IsOverLapItem((unsigned __int8)Dst.m_byTableCode) )
      {
        v21 = 7;
        goto $RESULT_42;
      }
      v24 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&pOne->m_Param.m_dbInven.m_nListNum, v43);
      if ( !v24 )
      {
        CPlayer::SendMsg_AdjustAmountInform(pOne, 0, v43, 0);
        v21 = 2;
        goto $RESULT_42;
      }
      if ( v24->m_byCsMethod && Dst.m_byCsMethod && v24->m_dwT != Dst.m_dwT )
      {
        if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&pOne->m_Param.m_dbInven.m_nListNum) == 255 )
        {
          v21 = 1;
          goto $RESULT_42;
        }
        v43 = -1;
      }
      if ( v24->m_bLock )
      {
        v21 = 12;
      }
      else if ( v24->m_byTableCode == (unsigned __int8)Dst.m_byTableCode && v24->m_wItemIndex == Dst.m_wItemIndex )
      {
        if ( Dst.m_dwDur + v24->m_dwDur <= 0x63 )
        {
          if ( v24->m_dwT != Dst.m_dwT )
            v21 = 14;
        }
        else
        {
          CPlayer::SendMsg_AdjustAmountInform(pOne, 0, v43, v24->m_dwDur);
          v21 = 3;
        }
      }
      else
      {
        v21 = 2;
      }
    }
  }
$RESULT_42:
  if ( !v21 )
  {
    pOne->m_dwLastTakeItemTime = v25;
    if ( v26 )
    {
      if ( (unsigned __int16)v43 == 0xFFFF )
      {
        Dst.m_wSerial = CPlayerDB::GetNewItemSerial(&pOne->m_Param);
        pItem = CPlayer::Emb_AddStorage(pOne, 0, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 0, 1);
        if ( !pItem )
        {
          CPlayer::SendMsg_TakeNewResult(pOne, -1, 0i64);
          return;
        }
        if ( pItemBox->m_byThrowerID == 1 )
          CPlayer::SendMsg_FanfareItem(pOne, 3, &Dst, pItemBox);
      }
      else
      {
        v28 = 1;
        if ( !IsProtectItem((unsigned __int8)Dst.m_byTableCode) )
          v28 = 0;
        bSend[0] = 0;
        bAdd[0] = v28;
        CPlayer::Emb_AlterDurPoint(pOne, 0, v24->m_byStorageIndex, Dst.m_dwDur, v28, 0);
      }
      if ( pOne->m_pUserDB && pOne->m_bCntEnable && Dst.m_byTableCode == 20 )
      {
        v29 = CRecordData::GetRecord(
                (CRecordData *)&unk_1799C6AA0 + (unsigned __int8)Dst.m_byTableCode,
                Dst.m_wItemIndex);
        if ( !v29 )
          return;
        if ( !strcmp_0(v29->m_strCode, "iyhol01") )
        {
          v6 = CHolyStoneSystem::GetNumOfTime(&g_HolySys);
          CUserDB::Update_UserGetScaner(pOne->m_pUserDB, 1u, (unsigned __int8)v6);
        }
      }
      v7 = CGoldenBoxItemMgr::Instance();
      if ( CGoldenBoxItemMgr::Get_Event_Status(v7) == 2 && pOne->m_pUserDB && Dst.m_byTableCode == 31 )
      {
        v30 = CRecordData::GetRecord(
                (CRecordData *)&unk_1799C6AA0 + (unsigned __int8)Dst.m_byTableCode,
                Dst.m_wItemIndex);
        if ( !v30 )
          return;
        if ( !strcmp_0(v30->m_strCode, "bxgol03") && pItemBox->m_dwThrowerCharSerial == -1 )
        {
          szCharName = CPlayerDB::GetCharNameA(&pOne->m_Param);
          v8 = CPlayerDB::GetCharSerial(&pOne->m_Param);
          CPlayer::SendMsg_Notify_Get_Golden_Box(pOne, 3, v8, szCharName, &Dst, 0);
        }
      }
      v31 = 1;
      if ( !IsProtectItem((unsigned __int8)Dst.m_byTableCode) && pItemBox->m_byThrowerID == 1 )
        v31 = 0;
      if ( v31 )
      {
        if ( pItemBox->m_wMonRecIndex != 0xFFFF && pItemBox->m_bBossMob )
        {
          v32 = CRecordData::GetRecord(&stru_1799C6210, pItemBox->m_wMonRecIndex);
          v33 = CRecordData::GetRecord(
                  (CRecordData *)&unk_1799C6AA0 + (unsigned __int8)Dst.m_byTableCode,
                  Dst.m_wItemIndex);
          v39 = timeGetTime() / 0x3E8;
          v9 = CPlayerDB::GetCharNameA(&pOne->m_Param);
          LODWORD(pszThrowerID) = v39;
          *(_DWORD *)bSend = pOne->m_dwObjSerial;
          *(_QWORD *)bAdd = v9;
          CLogFile::Write(
            &CMonster::s_logTrace_Boss_Looting,
            "\t Mob: %s Item: %s => %s ( %d ), Sec: %d",
            v32->m_strCode,
            v33->m_strCode);
        }
        v10 = pOne->m_pCurMap->m_pMapSet->m_strCode;
        v11 = pOne->m_ObjID.m_wIndex;
        pszFileName = pOne->m_szItemHistoryFileName;
        pfPos = pOne->m_fCurPos;
        pMapCode = v10;
        wMonRecIndex = pItemBox->m_wMonRecIndex;
        pszThrowerID = pItemBox->m_szThrowerID;
        *(_DWORD *)bSend = pItemBox->m_dwThrowerCharSerial;
        *(_QWORD *)bAdd = (char *)pItemBox + 228;
        CMgrAvatorItemHistory::take_ground_item(
          &CPlayer::s_MgrItemHistory,
          v11,
          pItemBox->m_byCreateCode,
          &Dst,
          pItemBox->m_aszThrowerName,
          *(unsigned int *)bSend,
          pItemBox->m_szThrowerID,
          wMonRecIndex,
          v10,
          pOne->m_fCurPos,
          pOne->m_szItemHistoryFileName);
        if ( pItemBox->m_dwThrowerCharSerial != -1 && pItemBox->m_dwThrowerCharSerial != pOne->m_dwObjSerial )
        {
          pszTakerID = pOne->m_pUserDB->m_szAccountID;
          v12 = CPlayerDB::GetCharNameA(&pOne->m_Param);
          *(_QWORD *)bSend = pItemBox->m_szThrowerItemHistoryFileName;
          *(_QWORD *)bAdd = pszTakerID;
          CMgrAvatorItemHistory::trans_ground_item(
            &CPlayer::s_MgrItemHistory,
            &Dst,
            v12,
            pOne->m_dwObjSerial,
            pszTakerID,
            *(char **)bSend);
        }
      }
    }
    v34 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)Dst.m_byTableCode, Dst.m_wItemIndex);
    CQuestMgr::CheckFailLoop(&pOne->m_QuestMgr, 4, v34->m_strCode);
    v35 = 0;
    if ( pItemBox->m_byCreateCode == 5 && pOne->m_pDHChannel )
    {
      *(_QWORD *)bSend = pItemBox;
      *(_DWORD *)bAdd = Dst.m_dwDur;
      v35 = CDarkHoleChannel::CheckEvent(
              pOne->m_pDHChannel,
              0,
              (unsigned __int8)Dst.m_byTableCode,
              Dst.m_wItemIndex,
              Dst.m_dwDur,
              (CGameObject *)&pItemBox->vfptr);
    }
    if ( pItemBox->m_byThrowerID == 1 )
    {
      if ( !v35 )
      {
        v36 = CRecordData::GetRecord(
                (CRecordData *)&unk_1799C6AA0 + (unsigned __int8)Dst.m_byTableCode,
                Dst.m_wItemIndex);
        CPlayer::Emb_CheckActForQuest(pOne, 4, v36->m_strCode, 1u, 0);
        if ( CPartyPlayer::IsPartyMode(pOne->m_pPartyMgr) )
          CPlayer::Emb_CheckActForQuestParty(pOne, 4, v36->m_strCode, 1u);
        CPlayer::CheckMentalTakeAndUpdateLastMetalTicket(pOne, v36->m_strCode);
      }
      if ( CPartyPlayer::IsPartyMode(pOne->m_pPartyMgr)
        && pItemBox->m_dwPartyBossSerial == pOne->m_pPartyMgr->m_pPartyBoss->m_id.dwSerial )
      {
        v37 = 1;
        if ( IsOverLapItem((unsigned __int8)Dst.m_byTableCode) )
          v37 = Dst.m_dwDur;
        bAdd[0] = v37;
        CPlayer::SendMsg_PartyLootItemInform(pOne, pOne->m_dwObjSerial, Dst.m_byTableCode, Dst.m_wItemIndex, v37);
      }
    }
    CItemBox::Destroy(pItemBox);
  }
  if ( (unsigned __int16)v43 == 0xFFFF )
  {
    if ( v21 || pItem )
    {
      CPlayer::SendMsg_TakeNewResult(pOne, v21, pItem);
    }
    else
    {
      CLogFile::Write(
        &stru_1799C8E78,
        "pc_TakeGroundingItem() : Error Take New Item Result : BoxItem TableCode(%u), ItemIndex(%u)",
        (unsigned __int8)Dst.m_byTableCode,
        Dst.m_wItemIndex);
      CPlayer::SendMsg_TakeNewResult(pOne, 15, pItem);
    }
  }
  else if ( v21 || v24 )
  {
    CPlayer::SendMsg_TakeAddResult(pOne, v21, v24);
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "pc_TakeGroundingItem() : Error Take Add Item Result : BoxItem TableCode(%u), ItemIndex(%u)",
      (unsigned __int8)Dst.m_byTableCode,
      Dst.m_wItemIndex);
    CPlayer::SendMsg_TakeNewResult(pOne, 15, v24);
  }
}
