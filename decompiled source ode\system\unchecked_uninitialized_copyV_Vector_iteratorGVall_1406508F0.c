/*
 * Function: ??$unchecked_uninitialized_copy@V?$_Vector_iterator@GV?$allocator@G@std@@@std@@PEAGV?$allocator@G@2@@stdext@@YAPEAGV?$_Vector_iterator@GV?$allocator@G@std@@@std@@0PEAGAEAV?$allocator@G@2@@Z
 * Address: 0x1406508F0
 */

__int64 __fastcall stdext::unchecked_uninitialized_copy<std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>,unsigned short *,std::allocator<unsigned short>>(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  __int64 v4; // rax@1
  __int64 v5; // rax@1
  __int64 v6; // rax@1
  __int64 v7; // ST30_8@1
  char v9; // [sp+38h] [bp-70h]@1
  char v10; // [sp+39h] [bp-6Fh]@1
  char v11; // [sp+40h] [bp-68h]@1
  char *v12; // [sp+58h] [bp-50h]@1
  char v13; // [sp+60h] [bp-48h]@1
  char *v14; // [sp+78h] [bp-30h]@1
  __int64 v15; // [sp+80h] [bp-28h]@1
  __int64 v16; // [sp+88h] [bp-20h]@1
  __int64 v17; // [sp+90h] [bp-18h]@1
  __int64 v18; // [sp+98h] [bp-10h]@1
  __int64 v19; // [sp+C0h] [bp+18h]@1
  __int64 v20; // [sp+C8h] [bp+20h]@1

  v20 = a4;
  v19 = a3;
  v15 = -2i64;
  memset(&v9, 0, sizeof(v9));
  v10 = std::_Ptr_cat<std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>,unsigned short *>(a1, &v19);
  v12 = &v11;
  v14 = &v13;
  v4 = std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>::_Vector_iterator<unsigned short,std::allocator<unsigned short>>((__int64)&v11);
  v16 = v4;
  v17 = v4;
  v5 = std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>::_Vector_iterator<unsigned short,std::allocator<unsigned short>>((__int64)v14);
  v18 = v5;
  LODWORD(v6) = std::_Uninit_copy<std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>,unsigned short *,std::allocator<unsigned short>>(
                  v5,
                  v17,
                  v19,
                  v20);
  v7 = v6;
  std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>::~_Vector_iterator<unsigned short,std::allocator<unsigned short>>();
  std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>::~_Vector_iterator<unsigned short,std::allocator<unsigned short>>();
  return v7;
}
