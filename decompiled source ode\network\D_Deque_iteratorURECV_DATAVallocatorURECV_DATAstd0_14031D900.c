/*
 * Function: ??D?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEBAAEAURECV_DATA@@XZ
 * Address: 0x14031D900
 */

RECV_DATA *__fastcall std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator*(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator*((std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v5->_Mycont);
}
