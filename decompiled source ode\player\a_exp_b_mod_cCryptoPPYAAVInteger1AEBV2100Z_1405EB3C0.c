/*
 * Function: ?a_exp_b_mod_c@CryptoPP@@YA?AVInteger@1@AEBV21@00@Z
 * Address: 0x1405EB3C0
 */

struct CryptoPP::Integer *__fastcall CryptoPP::a_exp_b_mod_c(CryptoPP *this, struct CryptoPP::Integer *retstr, const struct CryptoPP::Integer *a3, const struct CryptoPP::Integer *a4, const struct CryptoPP::Integer *a5)
{
  CryptoPP::ModularArithmetic v6; // [sp+20h] [bp-A8h]@1
  int v7; // [sp+B0h] [bp-18h]@1
  __int64 v8; // [sp+B8h] [bp-10h]@1
  CryptoPP::Integer *v9; // [sp+D0h] [bp+8h]@1
  struct CryptoPP::Integer *v10; // [sp+D8h] [bp+10h]@1
  const struct CryptoPP::Integer *v11; // [sp+E0h] [bp+18h]@1

  v11 = a3;
  v10 = retstr;
  v9 = (CryptoPP::Integer *)this;
  v8 = -2i64;
  v7 = 0;
  CryptoPP::ModularArithmetic::ModularArithmetic(&v6, (CryptoPP::Integer *)a4);
  CryptoPP::AbstractRing<CryptoPP::Integer>::Exponentiate((__int64)&v6, v9, (__int64)v10, (__int64)v11);
  v7 |= 1u;
  CryptoPP::ModularArithmetic::~ModularArithmetic(&v6);
  return v9;
}
