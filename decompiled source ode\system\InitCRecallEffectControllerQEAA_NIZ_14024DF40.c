/*
 * Function: ?Init@CRecallEffectController@@QEAA_NI@Z
 * Address: 0x14024DF40
 */

char __fastcall CRecallEffectController::Init(CRecallEffectController *this, unsigned int uiSize)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // rax@7
  __int64 v6; // rax@10
  __int64 v7; // rax@13
  __int64 v8; // rax@23
  __int64 v9; // [sp+0h] [bp-A8h]@1
  unsigned int j; // [sp+20h] [bp-88h]@20
  unsigned int dwIndex; // [sp+24h] [bp-84h]@28
  CMyTimer *v12; // [sp+28h] [bp-80h]@9
  CMyTimer *v13; // [sp+30h] [bp-78h]@6
  CNetIndexList *v14; // [sp+38h] [bp-70h]@12
  CNetIndexList *v15; // [sp+40h] [bp-68h]@9
  CNetIndexList *v16; // [sp+48h] [bp-60h]@15
  CNetIndexList *v17; // [sp+50h] [bp-58h]@12
  CRecallRequest **v18; // [sp+58h] [bp-50h]@15
  CRecallRequest *v19; // [sp+60h] [bp-48h]@25
  CRecallRequest *v20; // [sp+68h] [bp-40h]@22
  __int64 v21; // [sp+70h] [bp-38h]@4
  CMyTimer *v22; // [sp+78h] [bp-30h]@7
  CNetIndexList *v23; // [sp+80h] [bp-28h]@10
  CNetIndexList *v24; // [sp+88h] [bp-20h]@13
  __int64 v25; // [sp+90h] [bp-18h]@15
  CRecallRequest *v26; // [sp+98h] [bp-10h]@23
  CRecallEffectController *v27; // [sp+B0h] [bp+8h]@1
  unsigned int dwMaxBufNum; // [sp+B8h] [bp+10h]@1

  dwMaxBufNum = uiSize;
  v27 = this;
  v2 = &v9;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v21 = -2i64;
  if ( uiSize )
  {
    v13 = (CMyTimer *)operator new(0x18ui64);
    if ( v13 )
    {
      CMyTimer::CMyTimer(v13);
      v22 = (CMyTimer *)v5;
    }
    else
    {
      v22 = 0i64;
    }
    v12 = v22;
    v27->m_pkTimer = v22;
    v15 = (CNetIndexList *)operator new(0xA0ui64);
    if ( v15 )
    {
      CNetIndexList::CNetIndexList(v15);
      v23 = (CNetIndexList *)v6;
    }
    else
    {
      v23 = 0i64;
    }
    v14 = v23;
    v27->m_pkEmptyInxList = v23;
    v17 = (CNetIndexList *)operator new(0xA0ui64);
    if ( v17 )
    {
      CNetIndexList::CNetIndexList(v17);
      v24 = (CNetIndexList *)v7;
    }
    else
    {
      v24 = 0i64;
    }
    v16 = v24;
    v27->m_pkUseInxList = v24;
    v25 = dwMaxBufNum;
    v18 = (CRecallRequest **)operator new[](saturated_mul(8ui64, dwMaxBufNum));
    v27->m_ppkReqeust = v18;
    if ( v27->m_pkTimer && v27->m_pkEmptyInxList && v27->m_pkUseInxList && v27->m_ppkReqeust )
    {
      memset_0(v27->m_ppkReqeust, 0, 8i64 * dwMaxBufNum);
      for ( j = 0; j < dwMaxBufNum; ++j )
      {
        v20 = (CRecallRequest *)operator new(0x30ui64);
        if ( v20 )
        {
          CRecallRequest::CRecallRequest(v20, j);
          v26 = (CRecallRequest *)v8;
        }
        else
        {
          v26 = 0i64;
        }
        v19 = v26;
        v27->m_ppkReqeust[j] = v26;
        if ( !v27->m_ppkReqeust[j] )
          return 0;
      }
      CNetIndexList::SetList(v27->m_pkEmptyInxList, dwMaxBufNum);
      CNetIndexList::SetList(v27->m_pkUseInxList, dwMaxBufNum);
      for ( dwIndex = 0; dwIndex < dwMaxBufNum; ++dwIndex )
        CNetIndexList::PushNode_Back(v27->m_pkEmptyInxList, dwIndex);
      v27->m_uiInfoTotCnt = dwMaxBufNum;
      CMyTimer::BeginTimer(v27->m_pkTimer, 0x7530u);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
