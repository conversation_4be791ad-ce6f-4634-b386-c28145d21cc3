/*
 * Function: ?LoadDB_CombineResult@ItemCombineMgr@@QEAAEPEAU_combine_ex_item_result_zocl@@@Z
 * Address: 0x1402AD400
 */

char __fastcall ItemCombineMgr::LoadDB_CombineResult(ItemCombineMgr *this, _combine_ex_item_result_zocl *pLoadData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-58h]@1
  char v6; // [sp+20h] [bp-38h]@4
  _ITEMCOMBINE_DB_BASE *v7; // [sp+28h] [bp-30h]@6
  int j; // [sp+30h] [bp-28h]@8
  void *Src; // [sp+38h] [bp-20h]@13
  int v10; // [sp+40h] [bp-18h]@10
  ItemCombineMgr *v11; // [sp+60h] [bp+8h]@1
  _combine_ex_item_result_zocl *v12; // [sp+68h] [bp+10h]@1

  v12 = pLoadData;
  v11 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  if ( pLoadData )
  {
    v7 = &v11->m_pMaster->m_Param.m_ItemCombineDB;
    if ( _ITEMCOMBINE_DB_BASE::IsCombineData(v7) )
    {
      v12->dwCheckKey = v7->m_dwCheckKey;
      v12->byDlgType = v7->m_byDlgType;
      v12->dwDalant = v7->m_dwDalant;
      v12->bySelectItemCount = v7->m_bySelectItemCount;
      _combine_ex_item_result_zocl::_Result_ItemList_Buff::Init(&v12->ItemBuff);
      v12->ItemBuff.byItemListNum = v7->m_byItemListNum;
      for ( j = 0; ; ++j )
      {
        v10 = v7->m_byItemListNum >= 24 ? 24 : v7->m_byItemListNum;
        if ( j >= v10 )
          break;
        Src = &v7->m_List[j];
        memcpy_0(&v12->ItemBuff.RewardItemList[j], Src, 4ui64);
        v12->ItemBuff.RewardItemList[j].dwDur = *((_DWORD *)Src + 1);
        v12->ItemBuff.RewardItemList[j].dwUpt = *((_DWORD *)Src + 2);
      }
      v12->byErrCode = v6;
      v12->dwResultEffectType = v7->m_dwResultEffectType;
      v12->dwResultEffectMsgCode = v7->m_dwResultEffectMsgCode;
      result = v6;
    }
    else
    {
      v6 = 14;
      v12->byErrCode = 14;
      result = v6;
    }
  }
  else
  {
    v6 = 13;
    result = 13;
  }
  return result;
}
