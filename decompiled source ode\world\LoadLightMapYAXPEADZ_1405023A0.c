/*
 * Function: ?LoadLightMap@@YAXPEAD@Z
 * Address: 0x1405023A0
 */

void __fastcall LoadLightMap(char *a1)
{
  signed __int64 v1; // rdx@1
  char v2; // al@2
  char *v3; // rax@4
  int v4; // er10@4
  int v5; // edx@4
  char *v6; // r11@4
  __int64 v7; // r9@5
  __int64 v8; // r8@5
  int v9; // eax@6
  char v10; // [sp+20h] [bp-118h]@1

  v1 = &v10 - a1;
  do
  {
    v2 = *a1++;
    a1[v1 - 1] = v2;
  }
  while ( v2 );
  qword_184A79DA0 = (__int64)R3GetTexInfoR3T(&v10, 0);
  stLightmap = LoadR3TLightMap((struct R3Texture *)qword_184A79DA0, D3DFMT_R5G6B5);
  if ( stLightmap )
  {
    dword_184A79D88 = *(_DWORD *)(qword_184A79DA0 + 132);
    v3 = (char *)Dmalloc(4 * dword_184A79D88);
    v4 = dword_184A79D88;
    v5 = 0;
    v6 = v3;
    LightmapTexID = v3;
    if ( dword_184A79D88 > 0 )
    {
      v7 = qword_184A79DA0;
      v8 = 0i64;
      while ( 1 )
      {
        v8 += 4i64;
        v9 = v5++ + *(_DWORD *)(v7 + 128);
        *(_DWORD *)&v6[v8 - 4] = v9;
        if ( v5 >= v4 )
          break;
        v6 = (char *)LightmapTexID;
      }
    }
  }
}
