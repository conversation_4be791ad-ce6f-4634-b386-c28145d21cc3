/*
 * Function: ??_G?$CipherModeFinalTemplate_CipherHolder@V?$BlockCipherFinal@$0A@VBase@DES@CryptoPP@@@CryptoPP@@VCBC_Encryption@2@@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x14061B700
 */

void *__fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>,CryptoPP::CBC_Encryption>::`scalar deleting destructor'(__int64 a1, int a2)
{
  void *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = (void *)a1;
  CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>,CryptoPP::CBC_Encryption>::~CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>,CryptoPP::CBC_Encryption>(a1);
  if ( v4 & 1 )
    operator delete(v3);
  return v3;
}
