/*
 * Function: ??0?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAA@XZ
 * Address: 0x1402C47C0
 */

void __fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<CLogTypeDBTask *> v3; // al@4
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  std::allocator<CLogTypeDBTask *> *v6; // [sp+28h] [bp-10h]@4
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (std::allocator<CLogTypeDBTask *> *)&v5;
  std::allocator<CLogTypeDBTask *>::allocator<CLogTypeDBTask *>((std::allocator<CLogTypeDBTask *> *)&v5);
  std::_Vector_val<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_val<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(
    (std::_Vector_val<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&v7->_Myfirstiter,
    v3);
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Buy(v7, 0i64);
}
