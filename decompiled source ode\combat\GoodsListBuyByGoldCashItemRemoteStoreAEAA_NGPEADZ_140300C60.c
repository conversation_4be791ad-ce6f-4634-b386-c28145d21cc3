/*
 * Function: ?GoodsListBuyByGold@CashItemRemoteStore@@AEAA_NGPEAD@Z
 * Address: 0x140300C60
 */

char __fastcall CashItemRemoteStore::GoodsListBuyByGold(CashItemRemoteStore *this, unsigned __int16 wSock, char *pPacket)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned __int16 v6; // ax@19
  __int64 v7; // [sp+0h] [bp-A8h]@1
  CPlayer *v8; // [sp+30h] [bp-78h]@6
  char *v9; // [sp+38h] [bp-70h]@10
  _result_csi_goods_list_zocl v10; // [sp+48h] [bp-60h]@10
  int j; // [sp+64h] [bp-44h]@10
  char pbyType; // [sp+74h] [bp-34h]@19
  char v13; // [sp+75h] [bp-33h]@19
  unsigned __int64 v14; // [sp+90h] [bp-18h]@4
  CashItemRemoteStore *v15; // [sp+B0h] [bp+8h]@1
  unsigned __int16 v16; // [sp+B8h] [bp+10h]@1

  v16 = wSock;
  v15 = this;
  v3 = &v7;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( (signed int)wSock < 2532 )
  {
    v8 = &g_Player + wSock;
    if ( v8->m_bLive )
    {
      if ( v8->m_bOper )
      {
        v9 = pPacket;
        v10.nCashAmount = CPlayerDB::GetGold(&v8->m_Param);
        v10.bAdjustDiscountRate = CashItemRemoteStore::is_cde_time(v15);
        v10.bOneNOne = CashItemRemoteStore::IsEventTime(v15, 1);
        v10.bSetDiscount = CashItemRemoteStore::IsEventTime(v15, 0);
        for ( j = 0; j < 4; ++j )
        {
          if ( CashItemRemoteStore::IsEventTime(v15, 0) )
            v10.bySetDiscount[j] = CashItemRemoteStore::GetSetDiscout(v15, j);
          else
            v10.bySetDiscount[j] = 0;
        }
        v10.bLimSale = CashItemRemoteStore::IsEventTime(v15, 2);
        if ( v10.bLimSale )
          v10.byLimDiscount = CashItemRemoteStore::GetLimDiscout(v15);
        else
          v10.byLimDiscount = 0;
        pbyType = 57;
        v13 = 2;
        v6 = _result_csi_goods_list_zocl::size(&v10);
        CNetProcess::LoadSendMsg(unk_1414F2088, v16, &pbyType, (char *)&v10, v6);
        result = 1;
      }
      else
      {
        result = 1;
      }
    }
    else
    {
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
