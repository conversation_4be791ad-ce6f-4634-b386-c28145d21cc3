/*
 * Function: j_??$_Uninit_move@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@U_Undefined_move_tag@4@@std@@YAPEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@00AEAV?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140012D1E
 */

GUILD_BATTLE::CGuildBattleRewardItem *__fastcall std::_Uninit_move<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>,std::_Undefined_move_tag>(GUILD_BATTLE::CGuildBattleRewardItem *_First, GUILD_BATTLE::CGuildBattleRewardItem *_Last, GUILD_BATTLE::CGuildBattleRewardItem *_Dest, std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
