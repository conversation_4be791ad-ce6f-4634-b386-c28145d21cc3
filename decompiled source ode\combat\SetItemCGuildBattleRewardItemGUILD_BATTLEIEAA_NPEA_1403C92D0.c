/*
 * Function: ?SetItem@CGuildBattleRewardItem@GUILD_BATTLE@@IEAA_NPEAD@Z
 * Address: 0x1403C92D0
 */

char __fastcall GUILD_BATTLE::CGuildBattleRewardItem::SetItem(GUILD_BATTLE::CGuildBattleRewardItem *this, char *szItemCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-38h]@1
  _base_fld *v6; // [sp+20h] [bp-18h]@4
  unsigned __int8 j; // [sp+28h] [bp-10h]@4
  GUILD_BATTLE::CGuildBattleRewardItem *v8; // [sp+40h] [bp+8h]@1
  const char *szRecordCode; // [sp+48h] [bp+10h]@1

  szRecordCode = szItemCode;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0i64;
  for ( j = 0; ; ++j )
  {
    if ( (signed int)j >= 37 )
      return 0;
    v6 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + j, szRecordCode);
    if ( v6 )
      break;
  }
  if ( GetItemKindCode(j) )
  {
    result = 0;
  }
  else
  {
    v8->m_ucTableCode = j;
    v8->m_pFld = v6;
    result = 1;
  }
  return result;
}
