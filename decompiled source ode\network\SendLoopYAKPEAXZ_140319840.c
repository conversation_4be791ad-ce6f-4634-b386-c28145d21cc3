/*
 * Function: ?SendLoop@@YAKPEAX@Z
 * Address: 0x140319840
 */

void __fastcall SendLoop(void *pVoid)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CEngNetworkBillEX *v3; // rax@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  char v6; // [sp+24h] [bp-14h]@4

  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = 0;
  v6 = 0;
  while ( 1 )
  {
    v3 = CTSingleton<CEngNetworkBillEX>::Instance();
    if ( CEngNetworkBillEX::ConnectToNcash(v3) )
    {
      ++v5;
    }
    else
    {
      SuspendThread(m_hThread);
      v5 = 0;
      v6 = 0;
    }
    if ( v5 == 4 && !v6 )
    {
      MyMessageBox("Connection Fail", "Can Not Connect Billing Server!");
      v6 = 1;
    }
    Sleep(0xC8u);
  }
}
