/*
 * Function: ?_Hashval@?$_Hash@V?$_Hmap_traits@HPEAVCNationCodeStr@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@@std@@$0A@@stdext@@@stdext@@IEBA_KAEBH@Z
 * Address: 0x14020D8B0
 */

unsigned __int64 __fastcall stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationCodeStr *>>,0>>::_Hashval(stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationCodeStr *> >,0> > *this, const int *_Keyval)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned __int64 v6; // [sp+20h] [bp-18h]@4
  stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationCodeStr *> >,0> > *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = v7->_Mask & stdext::hash_compare<int,std::less<int>>::operator()(&v7->comp, _Keyval);
  if ( v7->_Maxidx <= v6 )
    v6 -= (v7->_Mask >> 1) + 1;
  return v6;
}
