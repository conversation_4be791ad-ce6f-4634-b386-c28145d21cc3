/*
 * Function: ?Select_UnmannedTraderRegister@CRFWorldDatabase@@QEAAEEKPEAK@Z
 * Address: 0x1404AF6B0
 */

char __fastcall CRFWorldDatabase::Select_UnmannedTraderRegister(CRFWorldDatabase *this, char byType, unsigned int dwRegistSerial, unsigned int *pdwRegister)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v7; // [sp+0h] [bp-488h]@1
  SQLLEN BufferLength; // [sp+20h] [bp-468h]@4
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-460h]@21
  SQLLEN v10; // [sp+38h] [bp-450h]@21
  __int16 v11; // [sp+44h] [bp-444h]@9
  char DstBuf; // [sp+60h] [bp-428h]@4
  unsigned __int64 v13; // [sp+470h] [bp-18h]@4
  CRFWorldDatabase *v14; // [sp+490h] [bp+8h]@1
  unsigned int *TargetValue; // [sp+4A8h] [bp+20h]@1

  TargetValue = pdwRegister;
  v14 = this;
  v4 = &v7;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v13 = (unsigned __int64)&v7 ^ _security_cookie;
  LODWORD(BufferLength) = dwRegistSerial;
  sprintf_s(&DstBuf, 0x400ui64, "{ CALL pSelect_utsellinfo_owner( %u, %u ) }", (unsigned __int8)byType);
  if ( v14->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v14->vfptr, &DstBuf);
  if ( v14->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v14->vfptr) )
  {
    v11 = SQLExecDirectA_0(v14->m_hStmtSelect, &DstBuf, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2;
      }
      else
      {
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v11, v14->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v11 = SQLFetch_0(v14->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        if ( v14->m_hStmtSelect )
          SQLCloseCursor_0(v14->m_hStmtSelect);
        if ( v11 == 100 )
        {
          result = 2;
        }
        else
        {
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v11, v14->m_hStmtSelect);
          result = 1;
        }
      }
      else
      {
        StrLen_or_IndPtr = &v10;
        BufferLength = 0i64;
        v11 = SQLGetData_0(v14->m_hStmtSelect, 1u, -18, TargetValue, 0i64, &v10);
        if ( v11 && v11 != 1 )
        {
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v11, v14->m_hStmtSelect);
          result = 1;
        }
        else
        {
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          if ( v14->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v14->vfptr, "%s Success", &DstBuf);
          result = 0;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v14->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1;
  }
  return result;
}
