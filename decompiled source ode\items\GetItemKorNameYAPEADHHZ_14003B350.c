/*
 * Function: ?GetItemKorName@@YAPEADHH@Z
 * Address: 0x14003B350
 */

char *__fastcall GetItemKorName(int nTableCode, int nItemIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *result; // rax@6
  __int64 v5; // [sp+0h] [bp-138h]@1
  CRecordData *v6; // [sp+20h] [bp-118h]@4
  _base_fld *v7; // [sp+28h] [bp-110h]@5
  _base_fld *v8; // [sp+30h] [bp-108h]@8
  _base_fld *v9; // [sp+38h] [bp-100h]@11
  _base_fld *v10; // [sp+40h] [bp-F8h]@14
  _base_fld *v11; // [sp+48h] [bp-F0h]@17
  _base_fld *v12; // [sp+50h] [bp-E8h]@20
  _base_fld *v13; // [sp+58h] [bp-E0h]@23
  _base_fld *v14; // [sp+60h] [bp-D8h]@26
  _base_fld *v15; // [sp+68h] [bp-D0h]@29
  _base_fld *v16; // [sp+70h] [bp-C8h]@32
  _base_fld *v17; // [sp+78h] [bp-C0h]@35
  _base_fld *v18; // [sp+80h] [bp-B8h]@38
  _base_fld *v19; // [sp+88h] [bp-B0h]@41
  _base_fld *v20; // [sp+90h] [bp-A8h]@44
  _base_fld *v21; // [sp+98h] [bp-A0h]@47
  _base_fld *v22; // [sp+A0h] [bp-98h]@50
  _base_fld *v23; // [sp+A8h] [bp-90h]@53
  _base_fld *v24; // [sp+B0h] [bp-88h]@56
  _base_fld *v25; // [sp+B8h] [bp-80h]@59
  _base_fld *v26; // [sp+C0h] [bp-78h]@62
  _base_fld *v27; // [sp+C8h] [bp-70h]@65
  _base_fld *v28; // [sp+D0h] [bp-68h]@68
  _base_fld *v29; // [sp+D8h] [bp-60h]@71
  _base_fld *v30; // [sp+E0h] [bp-58h]@74
  _base_fld *v31; // [sp+E8h] [bp-50h]@77
  _base_fld *v32; // [sp+F0h] [bp-48h]@80
  _base_fld *v33; // [sp+F8h] [bp-40h]@83
  _base_fld *v34; // [sp+100h] [bp-38h]@86
  _base_fld *v35; // [sp+108h] [bp-30h]@89
  _base_fld *v36; // [sp+110h] [bp-28h]@92
  _base_fld *v37; // [sp+118h] [bp-20h]@95
  int v38; // [sp+120h] [bp-18h]@4
  int v39; // [sp+140h] [bp+8h]@1

  v39 = nTableCode;
  v2 = &v5;
  for ( i = 76i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = &s_ptblItemData[v39];
  v38 = v39;
  switch ( v39 )
  {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 7:
      v7 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v7 )
        goto LABEL_97;
      result = v7[2].m_strCode;
      break;
    case 6:
      v8 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v8 )
        goto LABEL_97;
      result = v8[2].m_strCode;
      break;
    case 11:
      v9 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v9 )
        goto LABEL_97;
      result = v9[2].m_strCode;
      break;
    case 12:
      v10 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v10 )
        goto LABEL_97;
      result = v10[2].m_strCode;
      break;
    case 13:
      v11 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v11 )
        goto LABEL_97;
      result = v11[2].m_strCode;
      break;
    case 10:
      v12 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v12 )
        goto LABEL_97;
      result = v12[2].m_strCode;
      break;
    case 17:
      v13 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v13 )
        goto LABEL_97;
      result = v13[2].m_strCode;
      break;
    case 18:
      v14 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v14 )
        goto LABEL_97;
      result = v14[2].m_strCode;
      break;
    case 19:
      v15 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v15 )
        goto LABEL_97;
      result = v15[2].m_strCode;
      break;
    case 20:
      v16 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v16 )
        goto LABEL_97;
      result = v16[2].m_strCode;
      break;
    case 16:
      v17 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v17 )
        goto LABEL_97;
      result = v17[2].m_strCode;
      break;
    case 15:
      v18 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v18 )
        goto LABEL_97;
      result = v18[2].m_strCode;
      break;
    case 8:
      v19 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v19 )
        goto LABEL_97;
      result = v19[2].m_strCode;
      break;
    case 9:
      v20 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v20 )
        goto LABEL_97;
      result = v20[2].m_strCode;
      break;
    case 14:
      v21 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v21 )
        goto LABEL_97;
      result = (char *)&v21[2];
      break;
    case 21:
      v22 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v22 )
        goto LABEL_97;
      result = v22[2].m_strCode;
      break;
    case 22:
      v23 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v23 )
        goto LABEL_97;
      result = v23[2].m_strCode;
      break;
    case 23:
      v24 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v24 )
        goto LABEL_97;
      result = v24[2].m_strCode;
      break;
    case 24:
      v25 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v25 )
        goto LABEL_97;
      result = &v25[2].m_strCode[60];
      break;
    case 25:
      v26 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v26 )
        goto LABEL_97;
      result = &v26[2].m_strCode[56];
      break;
    case 26:
      v27 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v27 )
        goto LABEL_97;
      result = (char *)&v27[3];
      break;
    case 27:
      v28 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v28 )
        goto LABEL_97;
      result = v28[2].m_strCode;
      break;
    case 28:
      v29 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v29 )
        goto LABEL_97;
      result = v29[2].m_strCode;
      break;
    case 29:
      v30 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v30 )
        goto LABEL_97;
      result = v30[1].m_strCode;
      break;
    case 30:
      v31 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v31 )
        goto LABEL_97;
      result = v31[2].m_strCode;
      break;
    case 31:
      v32 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v32 )
        goto LABEL_97;
      result = v32[2].m_strCode;
      break;
    case 32:
      v33 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v33 )
        goto LABEL_97;
      result = v33[2].m_strCode;
      break;
    case 33:
      v34 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v34 )
        goto LABEL_97;
      result = &v34[2].m_strCode[60];
      break;
    case 34:
      v35 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v35 )
        goto LABEL_97;
      result = v35[2].m_strCode;
      break;
    case 35:
      v36 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v36 )
        goto LABEL_97;
      result = v36[2].m_strCode;
      break;
    case 36:
      v37 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v37 )
        goto LABEL_97;
      result = v37[2].m_strCode;
      break;
    default:
LABEL_97:
      result = szDefItemName;
      break;
  }
  return result;
}
