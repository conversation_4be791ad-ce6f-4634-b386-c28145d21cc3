/*
 * Function: ?SendMsg_SFDelayRequest@CPlayer@@QEAAXXZ
 * Address: 0x1400E78F0
 */

void __fastcall CPlayer::SendMsg_SFDelayRequest(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-118h]@1
  DWORD v4; // [sp+30h] [bp-E8h]@4
  _sf_delay_download_result_zocl v5; // [sp+50h] [bp-C8h]@4
  int v6; // [sp+E4h] [bp-34h]@4
  int v7; // [sp+E8h] [bp-30h]@4
  int j; // [sp+ECh] [bp-2Ch]@4
  char pbyType; // [sp+F4h] [bp-24h]@11
  char v10; // [sp+F5h] [bp-23h]@11
  CPlayer *v11; // [sp+120h] [bp+8h]@1

  v11 = this;
  v1 = &v3;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = timeGetTime();
  _sf_delay_download_result_zocl::_sf_delay_download_result_zocl(&v5);
  v6 = 0;
  v7 = 0;
  for ( j = 0; j < 10; ++j )
  {
    if ( v11->m_AttDelayChker.EFF[j].byEffectCode != 255 )
    {
      v5.EFF[v6].byEffectCode = v11->m_AttDelayChker.EFF[j].byEffectCode;
      v5.EFF[v6].wEffectIndex = v11->m_AttDelayChker.EFF[j].wEffectIndex;
      v5.EFF[v6++].dwRemainTime = v11->m_AttDelayChker.EFF[j].dwNextTime - v4;
    }
    if ( v11->m_AttDelayChker.MAS[j].byEffectCode != 255 )
    {
      v5.MAS[v7].byEffectCode = v11->m_AttDelayChker.MAS[j].byEffectCode;
      v5.MAS[v7].byMastery = v11->m_AttDelayChker.MAS[j].byMastery;
      v5.MAS[v7++].dwRemainTime = v11->m_AttDelayChker.MAS[j].dwNextTime - v4;
    }
  }
  pbyType = 3;
  v10 = 56;
  CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, (char *)&v5, 0x82u);
}
