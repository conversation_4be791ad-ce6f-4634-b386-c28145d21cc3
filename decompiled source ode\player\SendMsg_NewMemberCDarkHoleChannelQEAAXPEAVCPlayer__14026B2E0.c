/*
 * Function: ?SendMsg_NewMember@CDarkHoleChannel@@QEAAXPEAVCPlayer@@_N@Z
 * Address: 0x14026B2E0
 */

void __fastcall CDarkHoleChannel::SendMsg_NewMember(CDarkHoleChannel *this, CPlayer *pNewMember, bool bReconnect)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@4
  unsigned __int16 v6; // ax@7
  __int64 v7; // [sp+0h] [bp-98h]@1
  _darkhole_new_member_inform_zocl v8; // [sp+38h] [bp-60h]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v10; // [sp+65h] [bp-33h]@4
  int j; // [sp+74h] [bp-24h]@4
  _dh_player_mgr *v12; // [sp+78h] [bp-20h]@6
  unsigned __int64 v13; // [sp+88h] [bp-10h]@4
  CDarkHoleChannel *v14; // [sp+A0h] [bp+8h]@1
  bool v15; // [sp+B0h] [bp+18h]@1

  v15 = bReconnect;
  v14 = this;
  v3 = &v7;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = (unsigned __int64)&v7 ^ _security_cookie;
  v8.dwNewMemberSerial = pNewMember->m_dwObjSerial;
  v5 = CPlayerDB::GetCharNameW(&pNewMember->m_Param);
  strcpy_0(v8.wszNewMemberName, v5);
  v8.bReconnect = v15;
  pbyType = 35;
  v10 = 1;
  for ( j = 0; j < 32; ++j )
  {
    v12 = &v14->m_Quester[j];
    if ( _dh_player_mgr::IsFill(v12) )
    {
      v6 = _darkhole_new_member_inform_zocl::size(&v8);
      CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_Quester[j].pOne->m_ObjID.m_wIndex, &pbyType, (char *)&v8, v6);
    }
  }
}
