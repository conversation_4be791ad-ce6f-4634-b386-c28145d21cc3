/*
 * Function: _std::_Find_std::_Vector_iterator_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo____unsigned_long__::_1_::dtor$1
 * Address: 0x140369A50
 */

void __fastcall std::_Find_std::_Vector_iterator_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo____unsigned_long__::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::~_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(*(std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > **)(a2 + 72));
}
