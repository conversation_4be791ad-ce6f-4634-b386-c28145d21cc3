/*
 * Function: j_??0?$Singleton@V?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@CryptoPP@@U?$NewObject@V?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@CryptoPP@@@2@$0A@@CryptoPP@@QEAA@U?$NewObject@V?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@CryptoPP@@@1@@Z
 * Address: 0x14000B690
 */

void __fastcall CryptoPP::Singleton<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>,CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>,0>::Singleton<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>,CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>,0>(CryptoPP::Singleton<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>,CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> >,0> *this, CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> > objectFactory)
{
  CryptoPP::Singleton<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>,CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>,0>::Singleton<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>,CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>,0>(
    this,
    objectFactory);
}
