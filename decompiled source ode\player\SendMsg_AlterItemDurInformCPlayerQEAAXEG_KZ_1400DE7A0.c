/*
 * Function: ?SendMsg_AlterItemDurInform@CPlayer@@QEAAXEG_K@Z
 * Address: 0x1400DE7A0
 */

void __fastcall CPlayer::SendMsg_AlterItemDurInform(CPlayer *this, char byStorageCode, unsigned __int16 wItemSerial, unsigned __int64 dwDur)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  unsigned __int16 v8; // [sp+39h] [bp-4Fh]@4
  unsigned __int64 v9; // [sp+3Bh] [bp-4Dh]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v11; // [sp+65h] [bp-23h]@4
  CPlayer *v12; // [sp+90h] [bp+8h]@1

  v12 = this;
  v4 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = byStorageCode;
  v8 = wItemSerial;
  v9 = dwDur;
  pbyType = 7;
  v11 = 25;
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, &szMsg, 0xBu);
}
