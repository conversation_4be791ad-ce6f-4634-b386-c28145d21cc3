/*
 * Function: ?CreateTblLtd@CRFDBItemLog@@QEAA_NH@Z
 * Address: 0x140485500
 */

bool __fastcall CRFDBItemLog::CreateTblLtd(CRFDBItemLog *this, int nKorTime)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-498h]@1
  int v6; // [sp+20h] [bp-478h]@4
  int v7; // [sp+28h] [bp-470h]@4
  int v8; // [sp+30h] [bp-468h]@4
  int v9; // [sp+38h] [bp-460h]@4
  int v10; // [sp+40h] [bp-458h]@4
  int v11; // [sp+48h] [bp-450h]@4
  int v12; // [sp+50h] [bp-448h]@4
  int v13; // [sp+58h] [bp-440h]@4
  char Dest; // [sp+70h] [bp-428h]@4
  char v15; // [sp+71h] [bp-427h]@4
  unsigned __int64 v16; // [sp+480h] [bp-18h]@4
  CRFDBItemLog *v17; // [sp+4A0h] [bp+8h]@1

  v17 = this;
  v2 = &v5;
  for ( i = 292i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v16 = (unsigned __int64)&v5 ^ _security_cookie;
  Dest = 0;
  memset(&v15, 0, 0x3FFui64);
  v13 = nKorTime;
  v12 = nKorTime;
  v11 = nKorTime;
  v10 = nKorTime;
  v9 = nKorTime;
  v8 = nKorTime;
  v7 = nKorTime;
  v6 = nKorTime;
  sprintf(
    &Dest,
    "CREATE TABLE [dbo].[tbl_ltd_%d] ( [Serial] [int] IDENTITY (1, 1) NOT NULL , [LogSerial] [datetime] NOT NULL, [MainTy"
    "pe] [tinyint] NOT NULL, [SubType] [tinyint] NOT NULL, [AccountSerial] [int] NOT NULL, [CharSerial] [int] NOT NULL, )"
    " ON [PRIMARY] ALTER TABLE [dbo].[tbl_ltd_%d] WITH NOCHECK ADD CONSTRAINT [PK_tbl_ltd_%d] PRIMARY KEY  CLUSTERED ([Se"
    "rial])  ON [PRIMARY] CREATE INDEX [IX_tbl_ltd_Logserial] ON [dbo].[tbl_ltd_%d]([LogSerial]) ON [PRIMARY] CREATE INDE"
    "X [IX_tbl_ltd_MainType] ON [dbo].[tbl_ltd_%d]([MainType]) ON [PRIMARY] CREATE INDEX [IX_tbl_ltd_SubType] ON [dbo].[t"
    "bl_ltd_%d]([SubType]) ON [PRIMARY] CREATE INDEX [IX_tbl_ltd_AccountSerial] ON [dbo].[tbl_ltd_%d]([AccountSerial]) ON"
    " [PRIMARY] CREATE INDEX [IX_tbl_ltd_CharSerial] ON [dbo].[tbl_ltd_%d]([CharSerial]) ON [PRIMARY]",
    (unsigned int)nKorTime,
    (unsigned int)nKorTime);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v17->vfptr, &Dest, 1) != 0;
}
