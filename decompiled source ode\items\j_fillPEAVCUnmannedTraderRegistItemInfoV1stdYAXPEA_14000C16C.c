/*
 * Function: j_??$fill@PEAVCUnmannedTraderRegistItemInfo@@V1@@std@@YAXPEAVCUnmannedTraderRegistItemInfo@@0AEBV1@@Z
 * Address: 0x14000C16C
 */

void __fastcall std::fill<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo>(CUnmannedTraderRegistItemInfo *_First, CUnmannedTraderRegistItemInfo *_Last, CUnmannedTraderRegistItemInfo *_Val)
{
  std::fill<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo>(_First, _Last, _Val);
}
