/*
 * Function: ?Insert_SFDelayInfo@CRFWorldDatabase@@QEAA_NKPEAU_worlddb_sf_delay_info@@@Z
 * Address: 0x1404C2310
 */

bool __fastcall CRFWorldDatabase::Insert_SFDelayInfo(CRFWorldDatabase *this, unsigned int dwSerial, _worlddb_sf_delay_info *pSFDelay)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-168h]@1
  char Dest; // [sp+40h] [bp-128h]@4
  unsigned __int64 v8; // [sp+150h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+170h] [bp+8h]@1
  _worlddb_sf_delay_info *buf; // [sp+180h] [bp+18h]@1

  buf = pSFDelay;
  v9 = this;
  v3 = &v6;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = (unsigned __int64)&v6 ^ _security_cookie;
  sprintf(&Dest, "{ CALL pInsert_SFDelay( %d, ? ) }", dwSerial);
  return CRFNewDatabase::ExecUpdateBinaryQuery((CRFNewDatabase *)&v9->vfptr, &Dest, buf, 130, 0);
}
