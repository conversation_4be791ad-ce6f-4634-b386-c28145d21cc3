/*
 * Function: j_?GetGroupID@CUnmannedTraderClassInfoTableCodeType@@UEAA_NEGAEAE0@Z
 * Address: 0x14000B3D9
 */

bool __fastcall CUnmannedTraderClassInfoTableCodeType::GetGroupID(CUnmannedTraderClassInfoTableCodeType *this, char byTableCode, unsigned __int16 wItemTableIndex, char *byClass, char *bySubClass)
{
  return CUnmannedTraderClassInfoTableCodeType::GetGroupID(this, byTableCode, wItemTableIndex, byClass, bySubClass);
}
