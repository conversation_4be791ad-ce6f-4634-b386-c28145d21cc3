/*
 * Function: j_??$_Uninit_move@PEAEPEAEV?$allocator@E@std@@U_Undefined_move_tag@2@@std@@YAPEAEPEAE00AEAV?$allocator@E@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140004AB6
 */

char *__fastcall std::_Uninit_move<unsigned char *,unsigned char *,std::allocator<unsigned char>,std::_Undefined_move_tag>(char *_First, char *_Last, char *_Dest, std::allocator<unsigned char> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<unsigned char *,unsigned char *,std::allocator<unsigned char>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _<PERSON>,
           __formal,
           a6);
}
