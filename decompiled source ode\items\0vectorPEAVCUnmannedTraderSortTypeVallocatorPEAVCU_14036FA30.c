/*
 * Function: ??0?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEAA@XZ
 * Address: 0x14036FA30
 */

void __fastcall std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<CUnmannedTraderSortType *> v3; // al@4
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  std::allocator<CUnmannedTraderSortType *> *v6; // [sp+28h] [bp-10h]@4
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (std::allocator<CUnmannedTraderSortType *> *)&v5;
  std::allocator<CUnmannedTraderSortType *>::allocator<CUnmannedTraderSortType *>((std::allocator<CUnmannedTraderSortType *> *)&v5);
  std::_Vector_val<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Vector_val<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(
    (std::_Vector_val<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *)&v7->_Myfirstiter,
    v3);
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Buy(v7, 0i64);
}
