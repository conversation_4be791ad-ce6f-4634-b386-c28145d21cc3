/*
 * Function: ?update_amine_mineore@CRFWorldDatabase@@QEAA_NEEKEKEK@Z
 * Address: 0x1404A9450
 */

bool __fastcall CRFWorldDatabase::update_amine_mineore(CRFWorldDatabase *this, char byType, char byRace, unsigned int dwSerial, char bySlot, unsigned int dwK, char byNum, unsigned int dwGage)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v11; // [sp+0h] [bp-488h]@1
  unsigned int v12; // [sp+20h] [bp-468h]@4
  int v13; // [sp+28h] [bp-460h]@4
  int v14; // [sp+30h] [bp-458h]@4
  int v15; // [sp+38h] [bp-450h]@4
  int v16; // [sp+40h] [bp-448h]@4
  unsigned int v17; // [sp+48h] [bp-440h]@4
  char Dest; // [sp+60h] [bp-428h]@4
  char v19; // [sp+61h] [bp-427h]@4
  unsigned __int64 v20; // [sp+470h] [bp-18h]@4
  CRFWorldDatabase *v21; // [sp+490h] [bp+8h]@1

  v21 = this;
  v8 = &v11;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v20 = (unsigned __int64)&v11 ^ _security_cookie;
  Dest = 0;
  memset(&v19, 0, 0x3FFui64);
  v17 = dwSerial;
  v16 = (unsigned __int8)byRace;
  v15 = (unsigned __int8)byType;
  v14 = (unsigned __int8)byNum;
  v13 = (unsigned __int8)bySlot;
  v12 = dwK;
  sprintf(
    &Dest,
    "update [dbo].[tbl_automine_inven] set battery=%d, k_%d=%d, o_%d=%d where dck = 0 and collisiontype = %d and race = %"
    "d and guildserial = %d",
    dwGage,
    (unsigned __int8)bySlot);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v21->vfptr, &Dest, 1);
}
