/*
 * Function: ??$upvalue_@P8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@lua_tinker@@YAP8CMonster@@EAAPEAVCLuaSignalReActor@@XZPEAUlua_State@@@Z
 * Address: 0x14040A260
 */

CLuaSignalReActor *(__cdecl *__fastcall lua_tinker::upvalue_<CLuaSignalReActor * (CMonster::*)(void)>(struct lua_State *L, __int64 a2, int a3))(CMonster *this)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  lua_tinker::user2type<CLuaSignalReActor * (__cdecl CMonster::*)(void)> *v7; // [sp+30h] [bp+8h]@1

  v7 = (lua_tinker::user2type<CLuaSignalReActor * (__cdecl CMonster::*)(void)> *)L;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return lua_tinker::user2type<CLuaSignalReActor * (CMonster::*)(void)>::invoke(v7, (struct lua_State *)0xFFFFD8ED, a3);
}
