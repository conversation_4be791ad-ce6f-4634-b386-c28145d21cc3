/*
 * Function: ?Update_Class@CUserDB@@QEAA_NPEADEG@Z
 * Address: 0x140116000
 */

char __fastcall CUserDB::Update_Class(CUserDB *this, char *pszClassCode, char byHistoryRecordNum, unsigned __int16 wHistoryClassIndex)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-28h]@1
  CUserDB *v8; // [sp+30h] [bp+8h]@1
  char v9; // [sp+40h] [bp+18h]@1
  unsigned __int16 v10; // [sp+48h] [bp+20h]@1

  v10 = wHistoryClassIndex;
  v9 = byHistoryRecordNum;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( (signed int)(unsigned __int8)byHistoryRecordNum < 3 )
  {
    strcpy_0(v8->m_AvatorData.dbAvator.m_szClassCode, pszClassCode);
    v8->m_AvatorData.dbAvator.m_zClassHistory[(unsigned __int8)v9] = v10;
    if ( (unsigned __int8)v9 + 1 > v8->m_AvatorData.dbAvator.m_byLastClassGrade )
      v8->m_AvatorData.dbAvator.m_byLastClassGrade = v9 + 1;
    v8->m_bDataUpdate = 1;
    result = 1;
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_Class(): byHistoryRecordNum (%d) => failed ",
      v8->m_aszAvatorName,
      (unsigned __int8)byHistoryRecordNum);
    result = 0;
  }
  return result;
}
