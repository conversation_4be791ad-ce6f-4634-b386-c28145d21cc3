/*
 * Function: ?CompleteReRegistRollBack@CUnmannedTraderUserInfo@@QEAAXPEADPEAVCLogFile@@@Z
 * Address: 0x1403562E0
 */

void __fastcall CUnmannedTraderUserInfo::CompleteReRegistRollBack(CUnmannedTraderUserInfo *this, char *pData, CLogFile *pkLogger)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v5; // eax@4
  unsigned int v6; // ecx@4
  int v7; // ecx@8
  __int64 v8; // [sp+0h] [bp-48h]@1
  int v9; // [sp+20h] [bp-28h]@4
  int v10; // [sp+28h] [bp-20h]@8
  char *v11; // [sp+30h] [bp-18h]@4
  unsigned int j; // [sp+38h] [bp-10h]@4
  CUnmannedTraderUserInfo *v13; // [sp+50h] [bp+8h]@1
  CLogFile *v14; // [sp+60h] [bp+18h]@1

  v14 = pkLogger;
  v13 = this;
  v3 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = pData;
  v5 = *((_WORD *)pData + 1);
  v6 = (unsigned __int8)*pData;
  v9 = *((_DWORD *)pData + 2);
  CLogFile::Write(
    pkLogger,
    "CUnmannedTraderUserInfo::CompleteReRegistRollBack( char * pLoadData, CLogFile * pkLogger )\r\n"
    "\t\tType(%u) wInx(%u) Owner(%u)\r\n",
    v6,
    v5);
  for ( j = 0; (signed int)j < (unsigned __int8)v11[1]; ++j )
  {
    if ( !v11[8 * j + 12]
      && !CUnmannedTraderUserInfo::CompleteUpdateState(v13, *(_DWORD *)&v11[8 * j + 16], v11[8 * j + 13], 0) )
    {
      v7 = (unsigned __int8)v11[8 * j + 12];
      v10 = (unsigned __int8)v11[8 * j + 13];
      v9 = v7;
      CLogFile::Write(
        v14,
        "\t\t(%d)Nth RegistSerial(%u) CompleteUpdateState(..) State(%u) Fail!\r\n",
        j,
        *(_DWORD *)&v11[8 * j + 16]);
    }
    v9 = (unsigned __int8)v11[8 * j + 12];
    CLogFile::Write(v14, "\t\t(%d)Nth RegistSerial(%u) byProcRet(%u)\r\n", j, *(_DWORD *)&v11[8 * j + 16]);
  }
  CUnmannedTraderUserInfo::CountRegistItem(v13);
}
