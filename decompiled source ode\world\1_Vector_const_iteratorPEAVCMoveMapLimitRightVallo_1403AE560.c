/*
 * Function: ??1?$_Vector_const_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEAA@XZ
 * Address: 0x1403AE560
 */

void __fastcall std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &>::~_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &>((std::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &> *)&v4->_Mycont);
}
