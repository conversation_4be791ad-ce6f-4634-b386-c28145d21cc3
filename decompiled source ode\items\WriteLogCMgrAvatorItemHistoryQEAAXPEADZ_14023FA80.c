/*
 * Function: ?WriteLog@CMgrAvatorItemHistory@@QEAAXPEAD@Z
 * Address: 0x14023FA80
 */

void __fastcall CMgrAvatorItemHistory::WriteLog(CMgrAvatorItemHistory *this, char *pszFileName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMgrAvatorItemHistory *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( sData[0] )
    CMgrAvatorItemHistory::WriteFile(v5, pszFileName, sData);
}
