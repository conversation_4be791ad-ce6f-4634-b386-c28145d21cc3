/*
 * Function: ?CanYouEnterHole@CDarkHoleChannel@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x14026A710
 */

bool __usercall CDarkHoleChannel::CanYouEnterHole@<al>(CDarkHoleChannel *this@<rcx>, CPlayer *pEnter@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@6
  int k; // [sp+24h] [bp-14h]@14
  CDarkHoleChannel *v9; // [sp+40h] [bp+8h]@1
  CPlayer *pkObj; // [sp+48h] [bp+10h]@1

  pkObj = pEnter;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( CDarkHoleChannel::GetCurrentMemberNum(v9) >= 32 )
    return 0;
  for ( j = 0; j < 32; ++j )
  {
    if ( _dh_player_mgr::IsFill(&v9->m_Quester[j]) && v9->m_Quester[j].dwSerial == pkObj->m_dwObjSerial )
      return 0;
  }
  if ( v9->m_pQuestSetup->bPartyOnly )
  {
    for ( k = 0; k < 8; ++k )
    {
      if ( CPartyPlayer::IsPartyMember(v9->m_pPartyMng, pkObj) )
        return 1;
    }
    _effect_parameter::GetEff_Have(&pkObj->m_EP, 50);
    result = a3 > 0.0;
  }
  else
  {
    if ( v9->m_dwOpenerSerial == pkObj->m_dwObjSerial )
      return 1;
    if ( v9->m_nOpenerDegree != pkObj->m_byUserDgr )
    {
      if ( pkObj->m_byUserDgr )
      {
        if ( !v9->m_nOpenerDegree && v9->m_nOpenerDegree != 2 )
          return 0;
      }
      else if ( v9->m_nOpenerDegree != 2 )
      {
        return 0;
      }
    }
    result = 1;
  }
  return result;
}
