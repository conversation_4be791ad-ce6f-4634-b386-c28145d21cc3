/*
 * Function: ?Make_LastTimeRespawnSystemTimeString@BossSchedule@@QEAA_NPEADH@Z
 * Address: 0x14041A4B0
 */

char __fastcall BossSchedule::Make_LastTimeRespawnSystemTimeString(BossSchedule *this, char *strBuff, int nBuffSize)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v6; // eax@6
  __int64 v7; // [sp+0h] [bp-78h]@1
  int v8; // [sp+20h] [bp-58h]@6
  int v9; // [sp+28h] [bp-50h]@6
  int v10; // [sp+30h] [bp-48h]@6
  int v11; // [sp+38h] [bp-40h]@6
  int v12; // [sp+40h] [bp-38h]@6
  int v13; // [sp+48h] [bp-30h]@6
  int v14; // [sp+50h] [bp-28h]@6
  int v15; // [sp+54h] [bp-24h]@6
  int v16; // [sp+58h] [bp-20h]@6
  int v17; // [sp+5Ch] [bp-1Ch]@6
  int v18; // [sp+60h] [bp-18h]@6
  int v19; // [sp+64h] [bp-14h]@6
  BossSchedule *v20; // [sp+80h] [bp+8h]@1
  char *DstBuf; // [sp+88h] [bp+10h]@1
  int v22; // [sp+90h] [bp+18h]@1

  v22 = nBuffSize;
  DstBuf = strBuff;
  v20 = this;
  v3 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( strBuff )
  {
    v14 = ATL::CTime::GetMinute(&v20->m_LastRespawnSystemTime);
    v15 = ATL::CTime::GetSecond(&v20->m_LastRespawnSystemTime);
    v16 = ATL::CTime::GetMinute(&v20->m_LastRespawnSystemTime);
    v17 = ATL::CTime::GetHour(&v20->m_LastRespawnSystemTime);
    v18 = ATL::CTime::GetDay(&v20->m_LastRespawnSystemTime);
    v19 = ATL::CTime::GetMonth(&v20->m_LastRespawnSystemTime);
    v6 = ATL::CTime::GetYear(&v20->m_LastRespawnSystemTime);
    v13 = v14;
    v12 = v15;
    v11 = v16;
    v10 = v17;
    v9 = v18;
    v8 = v19;
    sprintf_s(DstBuf, v22, "%04d-%02d-%02d-%02d-%02d-%02d-%03d", (unsigned int)v6);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
