/*
 * Function: j_?capacity@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEBA_KXZ
 * Address: 0x140013BF1
 */

unsigned __int64 __fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::capacity(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this)
{
  return std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::capacity(this);
}
