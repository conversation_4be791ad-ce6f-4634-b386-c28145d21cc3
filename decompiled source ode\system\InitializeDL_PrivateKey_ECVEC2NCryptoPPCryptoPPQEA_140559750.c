/*
 * Function: ?Initialize@?$DL_PrivateKey_EC@VEC2N@CryptoPP@@@CryptoPP@@QEAAXAEBVEC2N@2@AEBUEC2NPoint@2@AEBVInteger@2@2@Z
 * Address: 0x140559750
 */

int __fastcall CryptoPP::DL_PrivateKey_EC<CryptoPP::EC2N>::Initialize(__int64 a1, __int64 a2, __int64 a3, __int64 a4, __int64 a5)
{
  __int64 v5; // rax@1
  __int64 v7; // [sp+50h] [bp+8h]@1
  __int64 v8; // [sp+58h] [bp+10h]@1
  __int64 v9; // [sp+60h] [bp+18h]@1

  v9 = a3;
  v8 = a2;
  v7 = a1;
  CryptoPP::Integer::Zero();
  LODWORD(v5) = CryptoPP::DL_KeyImpl<CryptoPP::PKCS8PrivateKey,CryptoPP::DL_GroupParameters_EC<CryptoPP::EC2N>,CryptoPP::OID>::AccessGroupParameters(v7 + 8);
  CryptoPP::DL_GroupParameters_EC<CryptoPP::EC2N>::Initialize(v5, v8, v9);
  return (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v7 + 24i64))(v7, a5);
}
