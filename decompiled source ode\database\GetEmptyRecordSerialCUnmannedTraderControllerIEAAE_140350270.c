/*
 * Function: ?GetEmptyRecordSerial@CUnmannedTraderController@@IEAAEAEAKPEA_N@Z
 * Address: 0x140350270
 */

char __fastcall CUnmannedTraderController::GetEmptyRecordSerial(CUnmannedTraderController *this, unsigned int *dwSerial, bool *pbRecordInserted)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v6; // [sp+0h] [bp-28h]@1
  unsigned int *dwSeriala; // [sp+38h] [bp+10h]@1
  bool *v8; // [sp+40h] [bp+18h]@1

  v8 = pbRecordInserted;
  dwSeriala = dwSerial;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  *pbRecordInserted = 0;
  if ( !CRFWorldDatabase::Select_UnmannedTraderSingleItemEmptyRecordSerial(pkDB, dwSerial) )
    goto LABEL_13;
  if ( !CRFWorldDatabase::Insert_UnmannedTraderSingleDefaultRecord(pkDB, 1u)
    && !CRFWorldDatabase::Insert_UnmannedTraderSingleDefaultRecord(pkDB, 1u) )
  {
    return 24;
  }
  *v8 = 1;
  if ( CRFWorldDatabase::Select_UnmannedTraderSingleItemEmptyRecordSerial(pkDB, dwSeriala) )
    result = 24;
  else
LABEL_13:
    result = 0;
  return result;
}
