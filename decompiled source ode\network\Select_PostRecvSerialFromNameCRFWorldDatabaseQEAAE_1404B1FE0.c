/*
 * Function: ?Select_PostRecvSerialFromName@CRFWorldDatabase@@QEAAEPEADPEAK11@Z
 * Address: 0x1404B1FE0
 */

char __fastcall CRFWorldDatabase::Select_PostRecvSerialFromName(CRFWorldDatabase *this, char *wszRecvName, unsigned int *pdwOutSerial, unsigned int *pdwAccSerial, unsigned int *pdwRace)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v8; // [sp+0h] [bp-108h]@1
  void *SQLStmt; // [sp+20h] [bp-E8h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-E0h]@22
  SQLLEN v11; // [sp+38h] [bp-D0h]@22
  __int16 v12; // [sp+44h] [bp-C4h]@9
  char DstBuf; // [sp+60h] [bp-A8h]@4
  char v14; // [sp+61h] [bp-A7h]@4
  char v15; // [sp+E4h] [bp-24h]@16
  unsigned __int64 v16; // [sp+F0h] [bp-18h]@4
  CRFWorldDatabase *v17; // [sp+110h] [bp+8h]@1
  unsigned int *TargetValue; // [sp+120h] [bp+18h]@1
  unsigned int *v19; // [sp+128h] [bp+20h]@1

  v19 = pdwAccSerial;
  TargetValue = pdwOutSerial;
  v17 = this;
  v5 = &v8;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v5 = -*********;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v16 = (unsigned __int64)&v8 ^ _security_cookie;
  DstBuf = 0;
  memset(&v14, 0, 0x7Fui64);
  sprintf_s(&DstBuf, 0x80ui64, "select Serial,AccountSerial,Race from tbl_base where Name='%s'", wszRecvName);
  if ( v17->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v17->vfptr, &DstBuf);
  if ( v17->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v17->vfptr) )
  {
    v12 = SQLExecDirectA_0(v17->m_hStmtSelect, &DstBuf, -3);
    if ( v12 && v12 != 1 )
    {
      if ( v12 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v17->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v12, &DstBuf, "_SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v12, v17->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v12 = SQLFetch_0(v17->m_hStmtSelect);
      if ( v12 && v12 != 1 )
      {
        v15 = 0;
        if ( v12 == 100 )
        {
          v15 = 2;
        }
        else
        {
          SQLStmt = v17->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v12, &DstBuf, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v12, v17->m_hStmtSelect);
          v15 = 1;
        }
        if ( v17->m_hStmtSelect )
          SQLCloseCursor_0(v17->m_hStmtSelect);
        result = v15;
      }
      else
      {
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v17->m_hStmtSelect, 1u, -18, TargetValue, 0i64, &v11);
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v17->m_hStmtSelect, 2u, -18, v19, 0i64, &v11);
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v17->m_hStmtSelect, 3u, -18, pdwRace, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          SQLStmt = v17->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v12, &DstBuf, "SQLGetData", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v12, v17->m_hStmtSelect);
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          result = 1;
        }
        else
        {
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          if ( v17->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v17->vfptr, "%s Success", &DstBuf);
          result = 0;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v17->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1;
  }
  return result;
}
