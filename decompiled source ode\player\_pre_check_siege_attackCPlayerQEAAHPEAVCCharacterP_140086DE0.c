/*
 * Function: ?_pre_check_siege_attack@CPlayer@@QEAAHPEAVCCharacter@@PEAMGPEAPEAU_db_con@_STORAGE_LIST@@PEAPEAU_BulletItem_fld@@G23@Z
 * Address: 0x140086DE0
 */

signed __int64 __fastcall CPlayer::_pre_check_siege_attack(CPlayer *this, CCharacter *pDst, float *pfAttackPos, unsigned __int16 wBulletSerial, _STORAGE_LIST::_db_con **ppBulletProp, _BulletItem_fld **ppfldBullet, unsigned __int16 wEffBtSerial, _STORAGE_LIST::_db_con **ppEffBulletProp, _BulletItem_fld **ppfldEffBullet)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v12; // rdx@51
  float v13; // xmm0_4@56
  float v14; // xmm0_4@56
  float v15; // xmm0_4@56
  float v16; // xmm0_4@57
  float v17; // xmm1_4@57
  float v18; // xmm0_4@77
  float v19; // xmm0_4@77
  float v20; // xmm0_4@78
  __int64 v21; // [sp+0h] [bp-88h]@1
  _STORAGE_LIST::_db_con *v22; // [sp+20h] [bp-68h]@4
  _BulletItem_fld *v23; // [sp+28h] [bp-60h]@4
  _STORAGE_LIST::_db_con *v24; // [sp+30h] [bp-58h]@4
  _BulletItem_fld *v25; // [sp+38h] [bp-50h]@4
  _base_fld *v26; // [sp+40h] [bp-48h]@14
  char v27; // [sp+48h] [bp-40h]@18
  int j; // [sp+4Ch] [bp-3Ch]@18
  __int64 v29; // [sp+50h] [bp-38h]@20
  int v30; // [sp+58h] [bp-30h]@51
  float v31; // [sp+5Ch] [bp-2Ch]@55
  float v32; // [sp+60h] [bp-28h]@58
  float v33; // [sp+64h] [bp-24h]@76
  float v34; // [sp+68h] [bp-20h]@79
  float v35; // [sp+6Ch] [bp-1Ch]@56
  float v36; // [sp+70h] [bp-18h]@56
  float v37; // [sp+74h] [bp-14h]@57
  float v38; // [sp+78h] [bp-10h]@77
  float v39; // [sp+7Ch] [bp-Ch]@78
  CPlayer *v40; // [sp+90h] [bp+8h]@1
  CCharacter *v41; // [sp+98h] [bp+10h]@1
  float *fPos; // [sp+A0h] [bp+18h]@1
  unsigned __int16 v43; // [sp+A8h] [bp+20h]@1

  v43 = wBulletSerial;
  fPos = pfAttackPos;
  v41 = pDst;
  v40 = this;
  v9 = &v21;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v22 = 0i64;
  v23 = 0i64;
  v24 = 0i64;
  v25 = 0i64;
  if ( !v40->m_bSFDelayNotCheck && !_ATTACK_DELAY_CHECKER::IsDelay(&v40->m_AttDelayChker, -1, 0xFFu, -1) )
    return 4294967291i64;
  if ( !CPlayer::IsSiegeMode(v40) || CPlayer::IsActingSiegeMode(v40) )
    return 4294967262i64;
  if ( v40->m_byMoveType == 2 )
    return 4294967255i64;
  if ( !v40->m_pSiegeItem->m_dwDur )
    return 4294967261i64;
  v26 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 27, v40->m_pSiegeItem->m_wItemIndex);
  if ( *(_DWORD *)&v26[3].m_strCode[4] != v40->m_pmWpn.byWpType )
    return 4294967263i64;
  if ( v40->m_pmWpn.byWpType == 7 )
  {
    if ( !v40->m_bFreeSFByClass )
    {
      v27 = 0;
      for ( j = 0; j < 4; ++j )
      {
        v29 = (__int64)*v40->m_Param.m_ppHistoryEffect[j];
        if ( !v29 )
          break;
        if ( *(_DWORD *)(v29 + 1444) )
        {
          v27 = 1;
          break;
        }
      }
      if ( !v27 )
        return 4294967269i64;
    }
  }
  else if ( v40->m_pmWpn.byWpType == 11 || v40->m_pmWpn.byWpType == 10 )
  {
    return 4294967287i64;
  }
  if ( v43 == 0xFFFF && v40->m_pmWpn.byWpClass )
    return 4294967270i64;
  if ( v43 != 0xFFFF )
  {
    v22 = CPlayer::IsBulletValidity(v40, v43);
    if ( !v22 )
    {
      CPlayer::SendMsg_AdjustAmountInform(v40, 2, v43, 0);
      return 4294967279i64;
    }
    v23 = (_BulletItem_fld *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 10, v22->m_wItemIndex);
    if ( v22->m_bLock )
      return 4294967267i64;
  }
  if ( wEffBtSerial != 0xFFFF )
  {
    v24 = CPlayer::IsEffBulletValidity(v40, wEffBtSerial);
    if ( !v24 )
    {
      CPlayer::SendMsg_AdjustAmountInform(v40, 2, wEffBtSerial, 0);
      return 4294967233i64;
    }
    v25 = (_BulletItem_fld *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 10, v24->m_wItemIndex);
    if ( v24->m_bLock )
      return 4294967267i64;
  }
  if ( v41 )
  {
    if ( (CPlayer *)v41 == v40 )
      return 4294967290i64;
    if ( !v41->m_bLive
      || v41->m_bCorpse
      || v41->m_pCurMap != v40->m_pCurMap
      || CGameObject::GetCurSecNum((CGameObject *)&v41->vfptr) == -1 )
    {
      return 4294967290i64;
    }
    v30 = CPlayer::_pre_check_in_guild_battle(v40, v41);
    if ( v30 )
      return (unsigned int)v30;
    LOBYTE(v12) = 1;
    if ( !(unsigned __int8)((int (__fastcall *)(CCharacter *, __int64))v41->vfptr->IsBeAttackedAble)(v41, v12) )
      return 4294967290i64;
    v31 = 0.0;
    if ( v40->m_pmWpn.byWpType == 7 )
    {
      v37 = (float)v40->m_pmWpn.wGaAttRange;
      v16 = v37;
      ((void (__fastcall *)(CCharacter *))v41->vfptr->GetWidth)(v41);
      v17 = v37 + (float)(v16 / 2.0);
      v15 = v17 + *(float *)&v26[5].m_strCode[16];
      v31 = v17 + *(float *)&v26[5].m_strCode[16];
    }
    else
    {
      v35 = (float)v40->m_pmWpn.wGaAttRange;
      v13 = v35;
      ((void (__fastcall *)(CCharacter *))v41->vfptr->GetWidth)(v41);
      v36 = (float)(v35 + (float)(v13 / 2.0)) + *(float *)&v26[5].m_strCode[16];
      v14 = v36;
      _effect_parameter::GetEff_Plus(&v40->m_EP, v40->m_pmWpn.byWpClass + 4);
      v15 = v36 + v14;
      v31 = v15;
    }
    GetSqrt(v41->m_fCurPos, v40->m_fCurPos);
    v32 = v15;
    if ( v15 > v31 )
    {
      GetSqrt(v41->m_fOldPos, v40->m_fCurPos);
      v32 = v15;
      if ( v15 > v31 )
        return 4294967293i64;
    }
    if ( *(float *)&v26[5].m_strCode[12] > v32 )
      return 4294967293i64;
    if ( !(unsigned __int8)((int (__fastcall *)(CPlayer *))v40->vfptr->IsAttackableInTown)(v40)
      && !(unsigned __int8)((int (__fastcall *)(CCharacter *))v41->vfptr->IsAttackableInTown)(v41)
      && ((unsigned __int8)((int (__fastcall *)(CPlayer *))v40->vfptr->IsInTown)(v40)
       || (unsigned __int8)((int (__fastcall *)(CCharacter *))v41->vfptr->IsInTown)(v41)) )
    {
      return 4294967265i64;
    }
    if ( !(unsigned __int8)((int (__fastcall *)(CCharacter *, CPlayer *))v41->vfptr->IsBeDamagedAble)(v41, v40) )
      return 4294967290i64;
  }
  else
  {
    if ( v23->m_nEffectGroup != 4 && v23->m_nEffectGroup != 6 )
      return 4294967290i64;
    if ( !CMapData::IsMapIn(v40->m_pCurMap, fPos) )
      return 4294967260i64;
    v33 = 0.0;
    if ( v40->m_pmWpn.byWpType == 7 )
    {
      v39 = (float)v40->m_pmWpn.wGaAttRange + *(float *)&v26[5].m_strCode[16];
      v20 = v39;
      _effect_parameter::GetEff_Plus(&v40->m_EP, 36);
      v19 = v39 + v20;
      v33 = v19;
    }
    else
    {
      v38 = (float)v40->m_pmWpn.wGaAttRange + *(float *)&v26[5].m_strCode[16];
      v18 = v38;
      _effect_parameter::GetEff_Plus(&v40->m_EP, v40->m_pmWpn.byWpClass + 4);
      v19 = v38 + v18;
      v33 = v19;
    }
    GetSqrt(fPos, v40->m_fCurPos);
    v34 = v19;
    if ( v19 > v33 )
      return 4294967293i64;
    if ( *(float *)&v26[5].m_strCode[12] > v34 )
      return 4294967293i64;
  }
  *ppBulletProp = v22;
  *ppfldBullet = v23;
  *ppEffBulletProp = v24;
  *ppfldEffBullet = v25;
  return 0i64;
}
