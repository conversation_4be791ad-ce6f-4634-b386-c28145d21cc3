/*
 * Function: j_??$_Uninit_fill_n@PEAPEAU_guild_member_refresh_data@@_KPEAU1@V?$allocator@PEAU_guild_member_refresh_data@@@std@@@std@@YAXPEAPEAU_guild_member_refresh_data@@_KAEBQEAU1@AEAV?$allocator@PEAU_guild_member_refresh_data@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140004548
 */

void __fastcall std::_Uninit_fill_n<_guild_member_refresh_data * *,unsigned __int64,_guild_member_refresh_data *,std::allocator<_guild_member_refresh_data *>>(_guild_member_refresh_data **_First, unsigned __int64 _Count, _guild_member_refresh_data *const *_Val, std::allocator<_guild_member_refresh_data *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<_guild_member_refresh_data * *,unsigned __int64,_guild_member_refresh_data *,std::allocator<_guild_member_refresh_data *>>(
    _First,
    _Count,
    _Val,
    __formal,
    a5,
    a6);
}
