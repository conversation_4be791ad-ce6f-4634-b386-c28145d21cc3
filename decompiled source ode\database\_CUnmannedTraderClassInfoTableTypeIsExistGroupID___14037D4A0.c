/*
 * Function: _CUnmannedTraderClassInfoTableType::IsExistGroupID_::_1_::dtor$1
 * Address: 0x14037D4A0
 */

void __fastcall CUnmannedTraderClassInfoTableType::IsExistGroupID_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>((std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)(a2 + 72));
}
