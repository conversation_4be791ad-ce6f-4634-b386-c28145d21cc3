/*
 * Function: ?LeaveGuild@CGuildBattleController@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x1403D66D0
 */

void __fastcall CGuildBattleController::LeaveGuild(CGuildBattleController *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleManager *v4; // rax@9
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *pkPlayera; // [sp+38h] [bp+10h]@1

  pkPlayera = pkPlayer;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pkPlayer && pkPlayer->m_bOper && pkPlayer->m_Param.m_pGuild && pkPlayer->m_Param.m_pGuild->m_dwSerial != -1 )
  {
    v4 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
    GUILD_BATTLE::CNormalGuildBattleManager::LeaveGuild(v4, pkPlayera);
  }
}
