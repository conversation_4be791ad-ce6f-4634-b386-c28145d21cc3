/*
 * Function: ?IncreaseVersion@CUnmannedTraderGroupVersionInfo@@QEAA_NEE@Z
 * Address: 0x14036BD90
 */

bool __fastcall CUnmannedTraderGroupVersionInfo::IncreaseVersion(CUnmannedTraderGroupVersionInfo *this, char byDivision, char byClass)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  CUnmannedTraderGroupDivisionVersionInfo *v6; // rax@6
  __int64 v7; // [sp+0h] [bp-28h]@1
  CUnmannedTraderGroupVersionInfo *v8; // [sp+30h] [bp+8h]@1
  char v9; // [sp+38h] [bp+10h]@1
  char v10; // [sp+40h] [bp+18h]@1

  v10 = byClass;
  v9 = byDivision;
  v8 = this;
  v3 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::size(&v8->m_vecVerInfo) > (unsigned __int8)byDivision )
  {
    v6 = std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::operator[](
           &v8->m_vecVerInfo,
           (unsigned __int8)v9);
    result = CUnmannedTraderGroupDivisionVersionInfo::IncreaseVersion(v6, v10);
  }
  else
  {
    result = 0;
  }
  return result;
}
