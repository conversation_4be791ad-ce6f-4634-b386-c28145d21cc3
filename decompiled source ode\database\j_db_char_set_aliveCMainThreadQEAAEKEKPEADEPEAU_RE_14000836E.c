/*
 * Function: j_?db_char_set_alive@CMainThread@@QEAAEKEKPEADEPEAU_REGED@@@Z
 * Address: 0x14000836E
 */

char __fastcall CMainThread::db_char_set_alive(CMainThread *this, unsigned int dwAccountSerial, char byCase, unsigned int dwSerial, char *pwszName, char bySlot, _REGED *pAliveAvator)
{
  return CMainThread::db_char_set_alive(this, dwAccountSerial, byCase, dwSerial, pwszName, bySlot, pAliveAvator);
}
