/*
 * Function: ?SendMsg_UnitPartTuningResult@CPlayer@@QEAAXEEPEAH@Z
 * Address: 0x1400DAE30
 */

void __fastcall CPlayer::SendMsg_UnitPartTuningResult(CPlayer *this, char byRetCode, char bySlotIndex, int *pnCost)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-D8h]@1
  char szMsg; // [sp+40h] [bp-98h]@4
  char v8; // [sp+41h] [bp-97h]@4
  char Dst; // [sp+42h] [bp-96h]@4
  char v10; // [sp+48h] [bp-90h]@4
  int v11; // [sp+50h] [bp-88h]@4
  int v12; // [sp+54h] [bp-84h]@4
  unsigned int v13; // [sp+6Ch] [bp-6Ch]@4
  unsigned int v14; // [sp+70h] [bp-68h]@4
  char pbyType; // [sp+A4h] [bp-34h]@4
  char v16; // [sp+A5h] [bp-33h]@4
  unsigned __int64 v17; // [sp+C0h] [bp-18h]@4
  CPlayer *v18; // [sp+E0h] [bp+8h]@1
  char v19; // [sp+F0h] [bp+18h]@1
  int *v20; // [sp+F8h] [bp+20h]@1

  v20 = pnCost;
  v19 = bySlotIndex;
  v18 = this;
  v4 = &v6;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v17 = (unsigned __int64)&v6 ^ _security_cookie;
  szMsg = byRetCode;
  v8 = bySlotIndex;
  memcpy_0(&Dst, v18->m_Param.m_UnitDB.m_List[(unsigned __int8)bySlotIndex].byPart, 6ui64);
  memcpy_0(&v10, v18->m_Param.m_UnitDB.m_List[(unsigned __int8)v19].dwBullet, 8ui64);
  v11 = *v20;
  v12 = v20[1];
  v13 = CPlayerDB::GetDalant(&v18->m_Param);
  v14 = CPlayerDB::GetGold(&v18->m_Param);
  pbyType = 23;
  v16 = 6;
  CNetProcess::LoadSendMsg(unk_1414F2088, v18->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x48u);
}
