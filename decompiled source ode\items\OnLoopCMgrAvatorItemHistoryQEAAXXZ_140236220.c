/*
 * Function: ?OnLoop@CMgrAvatorItemHistory@@QEAAXXZ
 * Address: 0x140236220
 */

void __fastcall CMgrAvatorItemHistory::OnLoop(CMgrAvatorItemHistory *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CMgrAvatorItemHistory *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CMyTimer::CountingTimer(&v4->m_tmrUpdateTime) )
  {
    _strdate(v4->m_szCurDate);
    v4->m_szCurDate[5] = 0;
    _strtime(v4->m_szCurTime);
    v4->m_szCurTime[5] = 0;
  }
}
