/*
 * Function: ?Init@CLogTypeDBTaskPool@@QEAA_NIIAEAVCLogFile@@@Z
 * Address: 0x1402C2110
 */

char __fastcall CLogTypeDBTaskPool::Init(CLogTypeDBTaskPool *this, unsigned int uiBuffSize, unsigned int uiMaxCnt, CLogFile *kLogger)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  CLogTypeDBTask *v7; // rax@21
  CLogTypeDBTask **v8; // rax@25
  __int64 v9; // [sp+0h] [bp-68h]@1
  int v10; // [sp+20h] [bp-48h]@26
  unsigned int dwIndex; // [sp+30h] [bp-38h]@11
  unsigned int dwInx; // [sp+34h] [bp-34h]@18
  CLogTypeDBTask *_Val; // [sp+38h] [bp-30h]@23
  CLogTypeDBTask *v14; // [sp+40h] [bp-28h]@23
  CLogTypeDBTask *v15; // [sp+48h] [bp-20h]@20
  __int64 v16; // [sp+50h] [bp-18h]@4
  CLogTypeDBTask *v17; // [sp+58h] [bp-10h]@21
  CLogTypeDBTaskPool *v18; // [sp+70h] [bp+8h]@1
  int uiSize; // [sp+78h] [bp+10h]@1
  unsigned int dwMaxBufNum; // [sp+80h] [bp+18h]@1
  CLogFile *v21; // [sp+88h] [bp+20h]@1

  v21 = kLogger;
  dwMaxBufNum = uiMaxCnt;
  uiSize = uiBuffSize;
  v18 = this;
  v4 = &v9;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v16 = -2i64;
  if ( uiBuffSize && uiMaxCnt )
  {
    if ( !CNetIndexList::SetList(&v18->m_kInxProc, uiMaxCnt) )
      CLogFile::Write(v21, "CLogTypeDBTaskPool::Init(...) : m_kInxProc.SetList( uiMaxCnt(%u) ) Fail!", dwMaxBufNum);
    if ( !CNetIndexList::SetList(&v18->m_kInxEmpty, dwMaxBufNum) )
      CLogFile::Write(v21, "CLogTypeDBTaskPool::Init(...) : m_kInxEmpty.SetList( uiMaxCnt(%u) ) Fail!", dwMaxBufNum);
    for ( dwIndex = 0; dwIndex < dwMaxBufNum; ++dwIndex )
      CNetIndexList::PushNode_Back(&v18->m_kInxEmpty, dwIndex);
    if ( dwMaxBufNum == v18->m_kInxEmpty.m_dwCount )
    {
      if ( !CNetIndexList::SetList(&v18->m_kInxComplete, dwMaxBufNum) )
        CLogFile::Write(
          v21,
          "CLogTypeDBTaskPool::Init(...) : m_kInxComplete.SetList( uiMaxCnt(%u) ) Fail!",
          dwMaxBufNum);
      std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::reserve(&v18->m_vecDat, dwMaxBufNum);
      for ( dwInx = 0; dwInx < dwMaxBufNum; ++dwInx )
      {
        v15 = (CLogTypeDBTask *)operator new(0x18ui64);
        if ( v15 )
        {
          CLogTypeDBTask::CLogTypeDBTask(v15);
          v17 = v7;
        }
        else
        {
          v17 = 0i64;
        }
        v14 = v17;
        _Val = v17;
        std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::push_back(&v18->m_vecDat, &_Val);
        if ( !*std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::back(&v18->m_vecDat) )
        {
          CLogFile::Write(
            v21,
            "CLogTypeDBTaskPool::Init(...) : MaxCnt(%u) BuffSize(%u) m_vecDat.back() NULL!",
            dwMaxBufNum,
            (unsigned int)uiSize);
          return 0;
        }
        v8 = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::back(&v18->m_vecDat);
        if ( !CLogTypeDBTask::Init(*v8, dwInx, uiSize) )
        {
          v10 = uiSize;
          CLogFile::Write(
            v21,
            "CLogTypeDBTaskPool::Init(...) : MaxCnt(%u) m_vecDat.back()->Init( i(%u), uiBuffSize(%u) ) Fail!",
            dwMaxBufNum,
            dwInx);
          return 0;
        }
      }
      v18->m_bInit = 1;
      result = 1;
    }
    else
    {
      CLogFile::Write(
        v21,
        "CLogTypeDBTaskPool::Init(...) : ( uiMaxCnt(%u) != m_kInxEmpty.m_dwCount(%u) ) Invalid!",
        dwMaxBufNum,
        v18->m_kInxEmpty.m_dwCount);
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(
      kLogger,
      "CLogTypeDBTaskPool::Init(...) : 0 == uiBuffSize(%u) || 0 == uiMaxCnt(%u) Invalid!",
      uiBuffSize,
      uiMaxCnt);
    result = 0;
  }
  return result;
}
