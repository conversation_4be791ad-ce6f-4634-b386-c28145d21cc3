/*
 * Function: ?GetRandPosVirtualDum@CMapData@@QEAA_NPEAMH0@Z
 * Address: 0x140185C70
 */

char __fastcall CMapData::GetRandPosVirtualDum(CMapData *this, float *pStdPos, int nRange, float *pNewPos)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@6
  __int64 v8; // [sp+0h] [bp-88h]@1
  float v9; // [sp+28h] [bp-60h]@4
  float v10; // [sp+2Ch] [bp-5Ch]@4
  float v11; // [sp+30h] [bp-58h]@4
  float v12; // [sp+58h] [bp-30h]@4
  float v13; // [sp+5Ch] [bp-2Ch]@4
  float v14; // [sp+60h] [bp-28h]@4
  int v15; // [sp+74h] [bp-14h]@4
  float v16; // [sp+78h] [bp-10h]@5
  float v17; // [sp+7Ch] [bp-Ch]@5
  CMapData *v18; // [sp+90h] [bp+8h]@1
  float *v19; // [sp+98h] [bp+10h]@1
  int v20; // [sp+A0h] [bp+18h]@1
  float *v21; // [sp+A8h] [bp+20h]@1

  v21 = pNewPos;
  v20 = nRange;
  v19 = pStdPos;
  v18 = this;
  v4 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = *pStdPos - (float)(nRange / 2);
  v10 = pStdPos[1] - 100.0;
  v11 = pStdPos[2] - (float)(nRange / 2);
  v12 = *pStdPos + (float)(nRange / 2);
  v13 = pStdPos[1] + 100.0;
  v14 = pStdPos[2] + (float)(nRange / 2);
  v15 = 0;
  while ( 1 )
  {
    v16 = *v19 - (float)(v20 / 2);
    *v21 = v16 + (float)(rand() % v20);
    v17 = v19[2] - (float)(v20 / 2);
    v21[2] = v17 + (float)(rand() % v20);
    v21[1] = v19[1];
    if ( CBsp::GetFirstYpos(v18->m_Level.mBsp, v21, &v9, &v12) != 0.0 )
      break;
    v6 = v15++;
    if ( v6 > 50 )
      return 0;
  }
  return 1;
}
