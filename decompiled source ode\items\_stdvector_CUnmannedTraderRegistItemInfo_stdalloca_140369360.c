/*
 * Function: _std::vector_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo___::_Ucopy_std::_Vector_const_iterator_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo______::_1_::dtor$2
 * Address: 0x140369360
 */

void __fastcall std::vector_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo___::_Ucopy_std::_Vector_const_iterator_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo______::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(*(std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > **)(a2 + 64));
}
