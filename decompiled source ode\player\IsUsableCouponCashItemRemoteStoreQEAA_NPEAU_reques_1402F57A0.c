/*
 * Function: ?IsUsableCoupon@CashItemRemoteStore@@QEAA_NPEAU_request_csi_buy_clzo@@U_STORAGE_POS_INDIV@@PEAVCPlayer@@PEA_N@Z
 * Address: 0x1402F57A0
 */

bool __fastcall CashItemRemoteStore::IsUsableCoupon(CashItemRemoteStore *this, _request_csi_buy_clzo *pBuyList, _STORAGE_POS_INDIV pCoupon, CPlayer *pOne, bool *bCheck)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v8; // [sp+0h] [bp-58h]@1
  _STORAGE_LIST *v9; // [sp+20h] [bp-38h]@4
  _STORAGE_LIST::_db_con *v10; // [sp+28h] [bp-30h]@6
  _base_fld *v11; // [sp+30h] [bp-28h]@8
  int v12; // [sp+38h] [bp-20h]@10
  int j; // [sp+3Ch] [bp-1Ch]@10
  int v14; // [sp+40h] [bp-18h]@13
  int k; // [sp+44h] [bp-14h]@15
  int v16; // [sp+48h] [bp-10h]@17
  _request_csi_buy_clzo *v17; // [sp+68h] [bp+10h]@1
  _STORAGE_POS_INDIV v18; // [sp+70h] [bp+18h]@1

  v18 = pCoupon;
  v17 = pBuyList;
  v5 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v9 = pOne->m_Param.m_pStoragePtr[(unsigned __int8)pCoupon.byStorageCode];
  if ( v9 )
  {
    v10 = _STORAGE_LIST::GetPtrFromSerial(v9, v18.wItemSerial);
    if ( v10 )
    {
      v11 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v10->m_byTableCode, v10->m_wItemIndex);
      if ( v11 )
      {
        v12 = 0;
        for ( j = 0; j < v17->nNum; ++j )
          v12 += v17->item[j].byOverlapNum * v17->item[j].nPrice;
        v14 = 10 * *(_DWORD *)&v11[4].m_strCode[8];
        if ( *(_DWORD *)&v11[4].m_strCode[4] )
        {
          result = 1;
        }
        else if ( v14 >= v12 )
        {
          result = 0;
        }
        else
        {
          for ( k = 0; k < v17->nNum; ++k )
          {
            v16 = v17->item[k].byOverlapNum * v17->item[k].nPrice;
            if ( v16 > v14 && !bCheck[k] )
            {
              bCheck[k] = 1;
              return 1;
            }
          }
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
