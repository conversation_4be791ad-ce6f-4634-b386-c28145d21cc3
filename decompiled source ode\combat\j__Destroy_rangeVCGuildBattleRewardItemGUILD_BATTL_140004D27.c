/*
 * Function: j_??$_Destroy_range@VCGuildBattleRewardItem@GUILD_BATTLE@@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@std@@YAXPEAVCGuildBattleRewardItem@GUILD_BATTLE@@0AEAV?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@0@@Z
 * Address: 0x140004D27
 */

void __fastcall std::_Destroy_range<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(GUILD_BATTLE::CGuildBattleRewardItem *_First, GUILD_BATTLE::CGuildBattleRewardItem *_Last, std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *_Al)
{
  std::_Destroy_range<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(
    _First,
    _Last,
    _Al);
}
