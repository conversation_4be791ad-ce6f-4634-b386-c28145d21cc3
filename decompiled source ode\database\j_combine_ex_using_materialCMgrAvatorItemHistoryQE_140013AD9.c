/*
 * Function: j_?combine_ex_using_material@CMgrAvatorItemHistory@@QEAAXHKEPEAPEAU_db_con@_STORAGE_LIST@@PEAEKPEADHK@Z
 * Address: 0x140013AD9
 */

void __fastcall CMgrAvatorItemHistory::combine_ex_using_material(CMgrAvatorItemHistory *this, int n, unsigned int dwCheckKey, char bySlot<PERSON><PERSON>, _STORAGE_LIST::_db_con **ppMaterial, char *pbyMtrNum, unsigned int dwFee, char *strFileName, int bSucc, unsigned int dwFailCount)
{
  CMgrAvatorItemHistory::combine_ex_using_material(
    this,
    n,
    dwCheckKey,
    bySlotNum,
    ppMaterial,
    pbyMtrNum,
    dwFee,
    strFileName,
    bSucc,
    dwFailCount);
}
