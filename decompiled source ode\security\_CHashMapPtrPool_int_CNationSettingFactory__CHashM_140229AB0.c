/*
 * Function: _CHashMapPtrPool_int_CNationSettingFactory_::_CHashMapPtrPool_int_CNationSettingFactory__::_1_::dtor$0
 * Address: 0x140229AB0
 */

void __fastcall CHashMapPtrPool_int_CNationSettingFactory_::_CHashMapPtrPool_int_CNationSettingFactory__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  stdext::hash_map<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::~hash_map<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>>((stdext::hash_map<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationSettingFactory *> > > *)(*(_QWORD *)(a2 + 64) + 8i64));
}
