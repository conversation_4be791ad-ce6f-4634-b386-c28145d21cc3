/*
 * Function: _CUnmannedTraderDivisionInfo::Copy_::_1_::dtor$1
 * Address: 0x14036E630
 */

void __fastcall CUnmannedTraderDivisionInfo::Copy_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(*(std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > **)(a2 + 88));
}
