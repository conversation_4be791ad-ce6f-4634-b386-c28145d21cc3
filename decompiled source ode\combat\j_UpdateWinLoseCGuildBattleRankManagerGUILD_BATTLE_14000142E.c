/*
 * Function: j_?UpdateWinLose@CGuildBattleRankManager@GUILD_BATTLE@@QEAA_NEKEK@Z
 * Address: 0x14000142E
 */

bool __fastcall GUILD_BATTLE::CGuildBattleRankManager::UpdateWinLose(GUILD_BATTLE::CGuildBattleRankManager *this, char byWin<PERSON><PERSON>, unsigned int dwWinGuildSerial, char by<PERSON>oseRace, unsigned int dwLoseGuildSerial)
{
  return GUILD_BATTLE::CGuildBattleRankManager::UpdateWinLose(
           this,
           byWinRace,
           dwWinGuildSerial,
           byLoseRace,
           dwLoseGuildSerial);
}
