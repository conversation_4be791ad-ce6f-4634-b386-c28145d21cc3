/*
 * Function: ?ReleaseAll@LendItemMng@@QEAAXXZ
 * Address: 0x14030DF50
 */

void __fastcall LendItemMng::ReleaseAll(LendItemMng *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-68h]@1
  int j; // [sp+20h] [bp-48h]@5
  LendItemSheet *v5; // [sp+28h] [bp-40h]@8
  LendItemSheet *v6; // [sp+30h] [bp-38h]@8
  void *v7; // [sp+38h] [bp-30h]@13
  LendItemMng *v8; // [sp+40h] [bp-28h]@15
  struct LendItemMng *v9; // [sp+48h] [bp-20h]@15
  void *v10; // [sp+50h] [bp-18h]@9
  void *v11; // [sp+58h] [bp-10h]@16
  LendItemMng *v12; // [sp+70h] [bp+8h]@1

  v12 = this;
  v1 = &v3;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v12->_ppkLendItem )
  {
    for ( j = 0; j < 2532; ++j )
    {
      if ( v12->_ppkLendItem[j] )
      {
        v6 = v12->_ppkLendItem[j];
        v5 = v6;
        if ( v6 )
          v10 = LendItemSheet::`scalar deleting destructor'(v5, 1u);
        else
          v10 = 0i64;
        v12->_ppkLendItem[j] = 0i64;
      }
    }
    v7 = v12->_ppkLendItem;
    operator delete[](v7);
    v12->_ppkLendItem = 0i64;
  }
  if ( LendItemMng::_pkInstance )
  {
    v9 = LendItemMng::_pkInstance;
    v8 = LendItemMng::_pkInstance;
    if ( LendItemMng::_pkInstance )
      v11 = LendItemMng::`scalar deleting destructor'(v8, 1u);
    else
      v11 = 0i64;
    LendItemMng::_pkInstance = 0i64;
  }
}
