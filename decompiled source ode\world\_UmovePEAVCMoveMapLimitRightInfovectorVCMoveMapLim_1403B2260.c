/*
 * Function: ??$_Umove@PEAVCMoveMapLimitRightInfo@@@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@IEAAPEAVCMoveMapLimitRightInfo@@PEAV2@00@Z
 * Address: 0x1403B2260
 */

CMoveMapLimitRightInfo *__fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Umove<CMoveMapLimitRightInfo *>(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, CMoveMapLimitRightInfo *_Ptr)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return stdext::_Unchecked_uninitialized_move<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *,std::allocator<CMoveMapLimitRightInfo>>(
           _First,
           _Last,
           _Ptr,
           &v8->_Alval);
}
