/*
 * Function: ?ct_loot_dungeon@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140291F90
 */

bool __fastcall ct_loot_dungeon(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  int v4; // eax@7
  __int64 v5; // [sp+0h] [bp-B8h]@1
  CMapData *pMap; // [sp+30h] [bp-88h]@9
  unsigned __int16 wLayerIndex; // [sp+38h] [bp-80h]@9
  float *pStdPos; // [sp+40h] [bp-78h]@9
  bool bHide; // [sp+48h] [bp-70h]@9
  int n; // [sp+50h] [bp-68h]@6
  unsigned __int16 *v11; // [sp+58h] [bp-60h]@8
  _STORAGE_LIST::_db_con pItem; // [sp+68h] [bp-50h]@9
  CPlayer *pOwner; // [sp+C0h] [bp+8h]@1

  pOwner = pOne;
  v1 = &v5;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( pOwner )
  {
    for ( n = 0; ; ++n )
    {
      v4 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 23);
      if ( n >= v4 )
        break;
      v11 = (unsigned __int16 *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 23, n);
      if ( !v11 )
        break;
      _STORAGE_LIST::_db_con::_db_con(&pItem);
      pItem.m_byTableCode = 23;
      pItem.m_wItemIndex = *v11;
      pItem.m_dwDur = 1i64;
      pItem.m_dwLv = 0xFFFFFFF;
      bHide = 1;
      pStdPos = pOwner->m_fCurPos;
      wLayerIndex = pOwner->m_wMapLayerIndex;
      pMap = pOwner->m_pCurMap;
      if ( !CreateItemBox(&pItem, pOwner, 0xFFFFFFFF, 0, 0i64, 2, pMap, wLayerIndex, pOwner->m_fCurPos, 1) )
        return 1;
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
