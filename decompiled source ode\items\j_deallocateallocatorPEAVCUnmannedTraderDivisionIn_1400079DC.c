/*
 * Function: j_?deallocate@?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@QEAAXPEAPEAVCUnmannedTraderDivisionInfo@@_K@Z
 * Address: 0x1400079DC
 */

void __fastcall std::allocator<CUnmannedTraderDivisionInfo *>::deallocate(std::allocator<CUnmannedTraderDivisionInfo *> *this, CUnmannedTraderDivisionInfo **_Ptr, unsigned __int64 __formal)
{
  std::allocator<CUnmannedTraderDivisionInfo *>::deallocate(this, _Ptr, __formal);
}
