/*
 * Function: j_??$_Uninit_copy@PEAVCUnmannedTraderItemCodeInfo@@PEAV1@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@YAPEAVCUnmannedTraderItemCodeInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderItemCodeInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000F3CB
 */

CUnmannedTraderItemCodeInfo *__fastcall std::_Uninit_copy<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *,std::allocator<CUnmannedTraderItemCodeInfo>>(CUnmannedTraderItemCodeInfo *_First, CUnmannedTraderItemCodeInfo *_Last, CUnmannedTraderItemCodeInfo *_Dest, std::allocator<CUnmannedTraderItemCodeInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *,std::allocator<CUnmannedTraderItemCodeInfo>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
