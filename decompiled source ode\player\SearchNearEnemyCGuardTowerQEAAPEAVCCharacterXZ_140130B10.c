/*
 * Function: ?SearchNearEnemy@CGuardTower@@QEAAPEAVCCharacter@@XZ
 * Address: 0x140130B10
 */

CMonster *__fastcall CGuardTower::SearchNearEnemy(CGuardTower *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CMonster *result; // rax@5
  int v4; // eax@6
  _sec_info *v5; // rax@11
  int v6; // eax@16
  int v7; // eax@28
  float v8; // xmm0_4@34
  float v9; // xmm0_4@35
  float v10; // xmm0_4@35
  float *v11; // rax@58
  float *v12; // rax@64
  __int64 v13; // [sp+0h] [bp-1F8h]@1
  CMonster *v14; // [sp+20h] [bp-1D8h]@6
  _pnt_rect pRect; // [sp+38h] [bp-1C0h]@6
  __int64 v16; // [sp+58h] [bp-1A0h]@6
  float v17[3]; // [sp+68h] [bp-190h]@58
  int v18; // [sp+84h] [bp-174h]@6
  int v19; // [sp+88h] [bp-170h]@6
  int v20; // [sp+8Ch] [bp-16Ch]@6
  __int64 v21[14]; // [sp+A0h] [bp-158h]@49
  __int64 v22[10]; // [sp+110h] [bp-E8h]@41
  char v23; // [sp+164h] [bp-94h]@6
  int j; // [sp+168h] [bp-90h]@6
  int k; // [sp+16Ch] [bp-8Ch]@8
  unsigned int dwSecIndex; // [sp+170h] [bp-88h]@11
  CObjectList *v27; // [sp+178h] [bp-80h]@11
  CObjectList *v28; // [sp+180h] [bp-78h]@12
  CMonster *v29; // [sp+188h] [bp-70h]@14
  char *v30; // [sp+190h] [bp-68h]@14
  CCharacter *v31; // [sp+198h] [bp-60h]@25
  int v32; // [sp+1A0h] [bp-58h]@56
  int l; // [sp+1A4h] [bp-54h]@56
  int v34; // [sp+1A8h] [bp-50h]@58
  int v35; // [sp+1ACh] [bp-4Ch]@62
  int m; // [sp+1B0h] [bp-48h]@62
  int v37; // [sp+1B4h] [bp-44h]@64
  int v38; // [sp+1B8h] [bp-40h]@16
  CGameObjectVtbl *v39; // [sp+1C0h] [bp-38h]@16
  int v40; // [sp+1C8h] [bp-30h]@28
  CGameObjectVtbl *v41; // [sp+1D0h] [bp-28h]@28
  float v42; // [sp+1D8h] [bp-20h]@35
  float v43; // [sp+1DCh] [bp-1Ch]@35
  CMapData *v44; // [sp+1E0h] [bp-18h]@58
  CMapData *v45; // [sp+1E8h] [bp-10h]@64
  CGuardTower *v46; // [sp+200h] [bp+8h]@1

  v46 = this;
  v1 = &v13;
  for ( i = 124i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v46->m_pMasterSetTarget )
    return (CMonster *)v46->m_pMasterSetTarget;
  v14 = 0i64;
  v16 = 0i64;
  v18 = *(_DWORD *)&v46->m_pRecordSet[5].m_strCode[24];
  v19 = 0;
  v20 = 0;
  v23 = 0;
  v4 = CGameObject::GetCurSecNum((CGameObject *)&v46->vfptr);
  CMapData::GetRectInRadius(v46->m_pCurMap, &pRect, 4, v4);
  for ( j = pRect.nStarty; j <= pRect.nEndy; ++j )
  {
    for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
    {
      v5 = CMapData::GetSecInfo(v46->m_pCurMap);
      dwSecIndex = v5->m_nSecNumW * j + k;
      v27 = CMapData::GetSectorListObj(v46->m_pCurMap, v46->m_wMapLayerIndex, dwSecIndex);
      if ( v27 )
      {
        v28 = (CObjectList *)v27->m_Head.m_pNext;
        while ( 1 )
        {
          while ( 1 )
          {
            while ( 1 )
            {
              while ( 1 )
              {
                if ( (_object_list_point *)v28 == &v27->m_Tail )
                  goto LABEL_9;
                v29 = (CMonster *)v28->vfptr;
                v28 = (CObjectList *)v28->m_Head.m_pItem;
                v30 = &v29->m_ObjID.m_byKind;
                if ( !v29->m_ObjID.m_byKind && !v29->m_bCorpse )
                {
                  v38 = ((int (__fastcall *)(CMonster *))v29->vfptr->GetObjRace)(v29);
                  v39 = v46->vfptr;
                  v6 = ((int (__fastcall *)(CGuardTower *))v39->GetObjRace)(v46);
                  if ( v38 != v6
                    && ((unsigned __int8)((int (__fastcall *)(CMonster *))v29->vfptr->IsAttackableInTown)(v29)
                     || !(unsigned __int8)((int (__fastcall *)(CMonster *))v29->vfptr->IsInTown)(v29)) )
                  {
                    break;
                  }
                }
              }
              if ( v30[1] && v30[1] != 3 )
                break;
              if ( v20 < 10 )
                goto LABEL_33;
            }
            if ( v30[1] == 1 && v19 < 10 )
            {
              v31 = CMonster::GetAttackTarget(v29);
              if ( v31 )
                break;
            }
          }
          if ( v31->m_ObjID.m_byID )
          {
LABEL_33:
            if ( !CCharacter::GetStealth((CCharacter *)&v29->vfptr, 1) )
            {
              v8 = v29->m_fCurPos[1] - v46->m_fCurPos[1];
              abs(v8);
              if ( v8 <= 400.0 )
              {
                v9 = (float)v18;
                v42 = (float)v18;
                ((void (__fastcall *)(CMonster *))v29->vfptr->GetWidth)(v29);
                v10 = v42 + (float)(v9 / 2.0);
                v43 = v10;
                GetSqrt(v29->m_fCurPos, v46->m_fCurPos);
                if ( v10 <= v43 )
                {
                  if ( v23 )
                  {
                    v14 = v29;
                    break;
                  }
                  if ( v30[1] )
                  {
                    if ( v30[1] == 3 )
                    {
                      if ( v20 < 10 )
                        v22[v20++] = (__int64)v29;
                    }
                    else if ( v30[1] == 1 && v19 < 10 )
                    {
                      v21[v19++] = (__int64)v29;
                    }
                  }
                  else if ( !v29->m_bObserver && v20 < 10 )
                  {
                    v22[v20++] = (__int64)v29;
                  }
                }
              }
            }
          }
          else if ( v46->m_pMasterTwr )
          {
            if ( v31 == (CCharacter *)v46->m_pMasterTwr )
              goto LABEL_33;
          }
          else
          {
            v40 = ((int (__fastcall *)(CCharacter *))v31->vfptr->GetObjRace)(v31);
            v41 = v46->vfptr;
            v7 = ((int (__fastcall *)(CGuardTower *))v41->GetObjRace)(v46);
            if ( v40 == v7 )
              goto LABEL_33;
          }
        }
      }
LABEL_9:
      ;
    }
  }
  if ( v14 )
  {
    result = v14;
  }
  else
  {
    if ( v20 > 0 )
    {
      v32 = rand() % v20;
      for ( l = 0; l < v20; ++l )
      {
        v34 = (l + v32) % v20;
        v11 = (float *)(v22[v34] + 40);
        v44 = v46->m_pCurMap;
        if ( (unsigned int)CBsp::CanYouGoThere(v44->m_Level.mBsp, v46->m_fCurPos, v11, (float (*)[3])v17) )
          return (CMonster *)v22[v34];
      }
    }
    if ( v19 > 0 )
    {
      v35 = rand() % v19;
      for ( m = 0; m < v19; ++m )
      {
        v37 = (m + v35) % v19;
        v12 = (float *)(v21[v37] + 40);
        v45 = v46->m_pCurMap;
        if ( (unsigned int)CBsp::CanYouGoThere(v45->m_Level.mBsp, v46->m_fCurPos, v12, (float (*)[3])v17) )
          return (CMonster *)v21[v37];
      }
    }
    result = 0i64;
  }
  return result;
}
