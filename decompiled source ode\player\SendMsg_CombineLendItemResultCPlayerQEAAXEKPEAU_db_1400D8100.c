/*
 * Function: ?SendMsg_CombineLendItemResult@CPlayer@@QEAAXEKPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1400D8100
 */

void __fastcall CPlayer::SendMsg_CombineLendItemResult(CPlayer *this, char byErrCode, unsigned int dwFee, _STORAGE_LIST::_db_con *pNewItem)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v6; // ax@6
  __int64 v7; // [sp+0h] [bp-98h]@1
  _combine_lend_item_result_zocl v8; // [sp+38h] [bp-60h]@4
  char pbyType; // [sp+74h] [bp-24h]@6
  char v10; // [sp+75h] [bp-23h]@6
  CPlayer *v11; // [sp+A0h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v12; // [sp+B8h] [bp+20h]@1

  v12 = pNewItem;
  v11 = this;
  v4 = &v7;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8.byErrorCode = byErrCode;
  if ( !byErrCode )
  {
    v8.dwFee = dwFee;
    v8.dwLeftDalant = CPlayerDB::GetDalant(&v11->m_Param);
    v8.Item.byTblCode = v12->m_byTableCode;
    v8.Item.wItemIdx = v12->m_wItemIndex;
    v8.Item.dwDur = v12->m_dwDur;
    v8.Item.dwUp = v12->m_dwLv;
    v8.Item.dwItemSerial = v12->m_wSerial;
    v8.Item.byCsMethod = v12->m_byCsMethod;
    v8.Item.dwT = v12->m_dwT;
  }
  pbyType = 7;
  v10 = 61;
  v6 = _combine_lend_item_result_zocl::size(&v8);
  CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, &v8.byErrorCode, v6);
}
