/*
 * Function: j_?SendReservedScheduleList@CGuildBattleController@@QEAAXHIKEEK@Z
 * Address: 0x1400105B9
 */

void __fastcall CGuildBattleController::SendReservedScheduleList(CGuildBattleController *this, int n, unsigned int uiMapID, unsigned int dwVer, char byDay, char byPage, unsigned int dwGuildSerial)
{
  CGuildBattleController::SendReservedScheduleList(this, n, uiMapID, dwVer, byDay, byPage, dwGuildSerial);
}
