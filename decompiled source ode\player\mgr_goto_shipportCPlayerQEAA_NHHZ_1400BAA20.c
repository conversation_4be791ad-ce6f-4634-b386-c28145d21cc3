/*
 * Function: ?mgr_goto_shipport@CPlayer@@QEAA_NHH@Z
 * Address: 0x1400BAA20
 */

char __fastcall CPlayer::mgr_goto_shipport(CPlayer *this, int nRaceCode, int nPort)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char v6; // al@19
  __int64 v7; // [sp+0h] [bp-88h]@1
  char *v8; // [sp+30h] [bp-58h]@9
  _portal_dummy *v9; // [sp+38h] [bp-50h]@9
  CMapData *pIntoMap; // [sp+40h] [bp-48h]@13
  _portal_dummy *v11; // [sp+48h] [bp-40h]@15
  float pNewPos; // [sp+58h] [bp-30h]@17
  CPlayer *v13; // [sp+90h] [bp+8h]@1
  int v14; // [sp+98h] [bp+10h]@1
  int v15; // [sp+A0h] [bp+18h]@1

  v15 = nPort;
  v14 = nRaceCode;
  v13 = this;
  v3 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( nRaceCode < 3 )
  {
    if ( CGameObject::GetCurSecNum((CGameObject *)&v13->vfptr) == -1 || v13->m_bMapLoading )
    {
      result = 0;
    }
    else
    {
      v8 = (char *)&g_TransportShip + 81296 * v14;
      v9 = CMapData::GetPortal(*((CMapData **)v8 + 2), 3 * v15 + v14);
      if ( v9 )
      {
        if ( v9->m_pPortalRec )
        {
          pIntoMap = CMapOperation::GetMap(&g_MapOper, v9->m_pPortalRec->m_strLinkMapCode);
          if ( pIntoMap )
          {
            v11 = CMapData::GetPortal(pIntoMap, v9->m_pPortalRec->m_strLinkPortalCode);
            if ( v11 )
            {
              if ( !CMapData::GetRandPosInDummy(pIntoMap, v11->m_pDumPos, &pNewPos, 1) )
                memcpy_0(&pNewPos, v11->m_pDumPos->m_fCenterPos, 0xCui64);
              CPlayer::OutOfMap(v13, pIntoMap, 0, 4, &pNewPos);
              v6 = CPlayerDB::GetMapCode(&v13->m_Param);
              CPlayer::SendMsg_GotoRecallResult(v13, 0, v6, &pNewPos, 4);
              result = 1;
            }
            else
            {
              result = 0;
            }
          }
          else
          {
            result = 0;
          }
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
