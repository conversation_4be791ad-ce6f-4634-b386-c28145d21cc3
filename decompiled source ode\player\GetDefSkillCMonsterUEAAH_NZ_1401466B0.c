/*
 * Function: ?GetDefSkill@CMonster@@UEAAH_N@Z
 * Address: 0x1401466B0
 */

__int64 __fastcall CMonster::GetDefSkill(CMonster *this, bool bBackAttackDamage)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-58h]@1
  float v6; // [sp+20h] [bp-38h]@4
  int nOutValue; // [sp+34h] [bp-24h]@5
  CMonster *v8; // [sp+60h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = *(float *)&v8->m_pRecordSet[4].m_strCode[56];
  if ( bBackAttackDamage )
  {
    nOutValue = -1;
    if ( CMonster::GetViewAngleCap(v8, 3, &nOutValue) )
    {
      if ( nOutValue > 100 )
        nOutValue = 100;
      if ( nOutValue < 0 )
        nOutValue = 0;
      v6 = v6 * (float)((float)(100 - nOutValue) / 100.0);
    }
  }
  return (unsigned int)(signed int)ffloor(v6);
}
