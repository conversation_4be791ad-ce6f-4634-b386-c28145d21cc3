/*
 * Function: ?Initialize@?$ListHeap@UWaitCell@TimeLimitJade@@@@QEAA_N_K@Z
 * Address: 0x1403FC2D0
 */

char __fastcall ListHeap<TimeLimitJade::WaitCell>::Initialize(ListHeap<TimeLimitJade::WaitCell> *this, unsigned __int64 nMaxBuf)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-58h]@1
  unsigned int dwIndex[2]; // [sp+20h] [bp-38h]@9
  int __n[2]; // [sp+28h] [bp-30h]@4
  void *v8; // [sp+30h] [bp-28h]@7
  void *__t; // [sp+38h] [bp-20h]@4
  __int64 v10; // [sp+40h] [bp-18h]@4
  void *v11; // [sp+48h] [bp-10h]@5
  ListHeap<TimeLimitJade::WaitCell> *v12; // [sp+60h] [bp+8h]@1
  unsigned __int64 dwMaxBufNum; // [sp+68h] [bp+10h]@1

  dwMaxBufNum = nMaxBuf;
  v12 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = -2i64;
  *(_QWORD *)__n = nMaxBuf;
  __t = operator new[](saturated_mul(0x10ui64, nMaxBuf));
  if ( __t )
  {
    `vector constructor iterator'(__t, 0x10ui64, __n[0], (void *(__cdecl *)(void *))TimeLimitJade::WaitCell::WaitCell);
    v11 = __t;
  }
  else
  {
    v11 = 0i64;
  }
  v8 = v11;
  v12->_pBuf = (TimeLimitJade::WaitCell *)v11;
  if ( v12->_pBuf )
  {
    memset_0(v12->_pBuf, 0, 16 * dwMaxBufNum);
    CNetIndexList::SetList((CNetIndexList *)&v12->_listData.m_Head, dwMaxBufNum);
    CNetIndexList::SetList((CNetIndexList *)&v12->_listEmpty.m_Head, dwMaxBufNum);
    for ( *(_QWORD *)dwIndex = 0i64; *(_QWORD *)dwIndex < dwMaxBufNum; ++*(_QWORD *)dwIndex )
      CNetIndexList::PushNode_Back((CNetIndexList *)&v12->_listEmpty.m_Head, dwIndex[0]);
    v12->_nMaxSize = dwMaxBufNum;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
