/*
 * Function: j_?make_siege_attack_param@CPlayer@@QEAAXPEAVCCharacter@@PEAMEPEAU_BulletItem_fld@@MPEAU_attack_param@@2M@Z
 * Address: 0x14000307B
 */

void __fastcall CPlayer::make_siege_attack_param(CPlayer *this, CCharacter *pDst, float *pfAttackPos, char by<PERSON><PERSON>, _BulletItem_fld *pBulletFld, float fAddBulletFc, _attack_param *pAP, _BulletItem_fld *pEffBulletFld, float fAddEffBtFc)
{
  CPlayer::make_siege_attack_param(
    this,
    pDst,
    pfAttackPos,
    byPart,
    pBulletFld,
    fAddBulletFc,
    pAP,
    pEffBulletFld,
    fAddEffBtFc);
}
