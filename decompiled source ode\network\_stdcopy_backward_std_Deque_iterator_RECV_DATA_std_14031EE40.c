/*
 * Function: _std::copy_backward_std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0____::_1_::dtor$2
 * Address: 0x14031EE40
 */

void __fastcall std::copy_backward_std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0____::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(*(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> **)(a2 + 280));
}
