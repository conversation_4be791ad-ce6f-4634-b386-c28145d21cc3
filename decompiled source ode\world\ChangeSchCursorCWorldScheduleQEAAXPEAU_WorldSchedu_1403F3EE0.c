/*
 * Function: ?ChangeSchCursor@CWorldSchedule@@QEAAXPEAU_WorldSchedule_fld@@H@Z
 * Address: 0x1403F3EE0
 */

void __fastcall CWorldSchedule::ChangeSchCursor(CWorldSchedule *this, _WorldSchedule_fld *pFld, int nPassMin)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  int nNextSubEventTerm; // [sp+20h] [bp-28h]@9
  char v7; // [sp+30h] [bp-18h]@7
  bool v8; // [sp+31h] [bp-17h]@7
  int j; // [sp+34h] [bp-14h]@7
  int v10; // [sp+38h] [bp-10h]@4
  bool v11; // [sp+3Ch] [bp-Ch]@7
  _WorldSchedule_fld *v12; // [sp+58h] [bp+10h]@1
  int nPassMina; // [sp+60h] [bp+18h]@1

  nPassMina = nPassMin;
  v12 = pFld;
  v3 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = pFld->m_nEventCode;
  if ( v10 )
  {
    if ( v10 == 1 && pFld->m_nEventInfo1 != -1 )
      CHolyStoneSystem::AlterSchedule(&g_HolySys, pFld->m_nEventInfo1, pFld->m_nEventInfo2);
  }
  else
  {
    v7 = pFld->m_nEventInfo1;
    v11 = pFld->m_nEventInfo2 != 0;
    v8 = v11;
    for ( j = 0; j < 3; ++j )
    {
      nNextSubEventTerm = v12->m_nEventInfo3;
      CTransportShip::AlterState((CTransportShip *)&g_TransportShip[10162 * j], v8, v7, nPassMina, nNextSubEventTerm);
    }
  }
}
