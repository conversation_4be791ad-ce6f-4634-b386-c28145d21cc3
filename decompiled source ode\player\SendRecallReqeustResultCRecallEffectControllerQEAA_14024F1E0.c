/*
 * Function: ?SendRecallReqeustResult@CRecallEffectController@@QEAAXEPEAVCPlayer@@@Z
 * Address: 0x14024F1E0
 */

void __fastcall CRecallEffectController::SendRecallReqeustResult(CRecallEffectController *this, char byRet, CPlayer *pkObj)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-98h]@1
  char szMsg; // [sp+38h] [bp-60h]@4
  char Dest; // [sp+39h] [bp-5Fh]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v10; // [sp+65h] [bp-33h]@4
  unsigned __int64 v11; // [sp+80h] [bp-18h]@4
  CPlayer *v12; // [sp+B0h] [bp+18h]@1

  v12 = pkObj;
  v3 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = (unsigned __int64)&v6 ^ _security_cookie;
  szMsg = byRet;
  v5 = CPlayerDB::GetCharNameW(&pkObj->m_Param);
  strcpy_0(&Dest, v5);
  pbyType = 17;
  v10 = 32;
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x12u);
}
