/*
 * Function: ?SubActPoint@CPlayer@@QEAAXEK@Z
 * Address: 0x140056140
 */

void __fastcall CPlayer::SubActPoint(CPlayer *this, char byCode, unsigned int dwSub)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v5; // eax@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int dwPoint; // [sp+20h] [bp-18h]@4
  CPlayer *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1
  unsigned int v10; // [sp+50h] [bp+18h]@1

  v10 = dwSub;
  v9 = byCode;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  dwPoint = CUserDB::GetActPoint(v8->m_pUserDB, byCode) - dwSub;
  if ( v10 > CUserDB::GetActPoint(v8->m_pUserDB, v9) )
    dwPoint = 0;
  v5 = CUserDB::GetActPoint(v8->m_pUserDB, v9);
  if ( dwPoint != v5 )
    CUserDB::Update_User_Action_Point(v8->m_pUserDB, v9, dwPoint);
}
