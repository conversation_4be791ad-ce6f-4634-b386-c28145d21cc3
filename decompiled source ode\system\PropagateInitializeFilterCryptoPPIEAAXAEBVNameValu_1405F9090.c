/*
 * Function: ?PropagateInitialize@Filter@CryptoPP@@IEAAXAEBVNameValuePairs@2@H@Z
 * Address: 0x1405F9090
 */

void __fastcall CryptoPP::Filter::PropagateInitialize(__int64 a1, __int64 a2, int a3)
{
  __int64 v3; // rax@2
  __int64 v4; // [sp+48h] [bp+10h]@1
  int v5; // [sp+50h] [bp+18h]@1

  v5 = a3;
  v4 = a2;
  if ( a3 )
  {
    LODWORD(v3) = (*(int (**)(void))(*(_QWORD *)a1 + 328i64))();
    (*(void (__fastcall **)(__int64, __int64, _QWORD))(*(_QWORD *)v3 + 80i64))(v3, v4, (unsigned int)(v5 - 1));
  }
}
