/*
 * Function: j_??$_Uninit_copy@PEAPEAVCMoveMapLimitRight@@PEAPEAV1@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@YAPEAPEAVCMoveMapLimitRight@@PEAPEAV1@00AEAV?$allocator@PEAVCMoveMapLimitRight@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000196A
 */

CMoveMapLimitRight **__fastcall std::_Uninit_copy<CMoveMapLimitRight * *,CMoveMapLimitRight * *,std::allocator<CMoveMapLimitRight *>>(CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, CMoveMapLimitRight **_Dest, std::allocator<CMoveMapLimitRight *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CMoveMapLimitRight * *,CMoveMapLimitRight * *,std::allocator<CMoveMapLimitRight *>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
