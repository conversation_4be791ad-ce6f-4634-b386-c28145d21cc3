/*
 * Function: ?pc_ChatFarRequest@CPlayer@@QEAAXPEAD0@Z
 * Address: 0x140091500
 */

void __usercall CPlayer::pc_ChatFarRequest(CPlayer *this@<rcx>, char *pwszName@<rdx>, char *pwszChatData@<r8>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@13
  CPvpUserAndGuildRankingSystem *v7; // rax@16
  unsigned int v8; // eax@16
  char v9; // al@20
  CPlayer *v10; // rcx@20
  char v11; // al@20
  char *v12; // rax@20
  CChatStealSystem *v13; // rax@20
  __int64 v14; // [sp+0h] [bp-1A8h]@1
  bool bFilter; // [sp+20h] [bp-188h]@20
  char *pwszMessage; // [sp+28h] [bp-180h]@20
  char byPvpGrade; // [sp+30h] [bp-178h]@20
  char *pwszSender; // [sp+38h] [bp-170h]@20
  CPlayer *v19; // [sp+40h] [bp-168h]@7
  bool v20; // [sp+48h] [bp-160h]@12
  char v21; // [sp+49h] [bp-15Fh]@12
  char DstBuf; // [sp+60h] [bp-148h]@20
  int v23; // [sp+180h] [bp-28h]@13
  int v24; // [sp+184h] [bp-24h]@16
  char *v25; // [sp+188h] [bp-20h]@20
  char *v26; // [sp+190h] [bp-18h]@20
  unsigned __int64 v27; // [sp+198h] [bp-10h]@4
  CPlayer *pPlayer; // [sp+1B0h] [bp+8h]@1
  char *pwszNamea; // [sp+1B8h] [bp+10h]@1
  char *szChatMsg; // [sp+1C0h] [bp+18h]@1

  szChatMsg = pwszChatData;
  pwszNamea = pwszName;
  pPlayer = this;
  v4 = &v14;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v27 = (unsigned __int64)&v14 ^ _security_cookie;
  if ( !pPlayer->m_pUserDB || !pPlayer->m_pUserDB->m_bChatLock && !CPlayer::IsPunished(pPlayer, 0, 1) )
  {
    v19 = CPlayer::FindFarChatPlayerWithTemp(pPlayer, pwszNamea);
    if ( v19 )
    {
      if ( pPlayer->m_byUserDgr != 2 && v19->m_bBlockWhisper )
      {
        CPlayer::SendMsg_ChatFarFailure(pPlayer, 1);
      }
      else
      {
        v20 = 0;
        v21 = 2;
        if ( pPlayer->m_byUserDgr == 2 )
        {
          v21 = 12;
        }
        else
        {
          v23 = CPlayerDB::GetRaceCode(&v19->m_Param);
          v6 = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
          if ( v23 != v6 && v19->m_byUserDgr < 2 )
          {
            _effect_parameter::GetEff_Have(&v19->m_EP, 3);
            if ( a4 == 0.0 )
            {
              v24 = CPlayerDB::GetRaceCode(&v19->m_Param);
              v7 = CPvpUserAndGuildRankingSystem::Instance();
              v8 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v7, v24, 0);
              if ( v8 != v19->m_dwObjSerial )
                v20 = 1;
            }
          }
        }
        v25 = CPlayerDB::GetCharNameW(&pPlayer->m_Param);
        v9 = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
        v10 = &g_Player + v19->m_ObjID.m_wIndex;
        pwszSender = v25;
        byPvpGrade = pPlayer->m_Param.m_byPvPGrade;
        pwszMessage = szChatMsg;
        bFilter = v20;
        CPlayer::SendData_ChatTrans(v10, v21, pPlayer->m_dwObjSerial, v9, v20, szChatMsg, byPvpGrade, v25);
        v26 = CPlayerDB::GetCharNameW(&v19->m_Param);
        v11 = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
        pwszSender = v26;
        byPvpGrade = pPlayer->m_Param.m_byPvPGrade;
        pwszMessage = szChatMsg;
        bFilter = 0;
        CPlayer::SendData_ChatTrans(pPlayer, v21, pPlayer->m_dwObjSerial, v11, 0, szChatMsg, byPvpGrade, v26);
        v12 = CPlayerDB::GetCharNameW(&v19->m_Param);
        sprintf_s(&DstBuf, 0x110ui64, v12);
        v13 = CChatStealSystem::Instance();
        CChatStealSystem::StealChatMsg(v13, pPlayer, v21, szChatMsg);
      }
    }
    else
    {
      CPlayer::SendMsg_ChatFarFailure(pPlayer, 0);
    }
  }
}
