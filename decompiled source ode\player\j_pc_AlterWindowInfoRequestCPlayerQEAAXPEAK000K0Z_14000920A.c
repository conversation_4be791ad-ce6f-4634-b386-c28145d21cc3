/*
 * Function: j_?pc_AlterWindowInfoRequest@CPlayer@@QEAAXPEAK000K0@Z
 * Address: 0x14000920A
 */

void __fastcall CPlayer::pc_AlterWindowInfoRequest(CPlayer *this, unsigned int *pdwSkill, unsigned int *pdwForce, unsigned int *pdwChar, unsigned int *pdwAnimus, unsigned int dwInven, unsigned int *pdwInvenBag)
{
  CPlayer::pc_AlterWindowInfoRequest(this, pdwSkill, pdwForce, pdwChar, pdwAnimus, dwInven, pdwInvenBag);
}
