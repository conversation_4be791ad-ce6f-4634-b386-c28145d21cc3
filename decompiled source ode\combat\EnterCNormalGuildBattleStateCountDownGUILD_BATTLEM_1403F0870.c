/*
 * Function: ?Enter@CNormalGuildBattleStateCountDown@GUILD_BATTLE@@MEAAHPEAVCNormalGuildBattle@2@@Z
 * Address: 0x1403F0870
 */

__int64 __fastcall GUILD_BATTLE::CNormalGuildBattleStateCountDown::Enter(GUILD_BATTLE::CNormalGuildBattleStateCountDown *this, GUILD_BATTLE::CNormalGuildBattle *pkBattle)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  ATL::CTimeSpan *v4; // rax@4
  char v5; // al@4
  ATL::CTimeSpan *v6; // rax@4
  char v7; // al@4
  ATL::CTimeSpan *v8; // rax@4
  int v9; // eax@4
  __int64 v11; // [sp+0h] [bp-58h]@1
  GUILD_BATTLE::CNormalGuildBattleGuild *v12; // [sp+20h] [bp-38h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v13; // [sp+28h] [bp-30h]@4
  char v14; // [sp+30h] [bp-28h]@4
  char v15; // [sp+38h] [bp-20h]@4
  char v16; // [sp+40h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleStateCountDown *v17; // [sp+60h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattle *pkBattlea; // [sp+68h] [bp+10h]@1

  pkBattlea = pkBattle;
  v17 = this;
  v2 = &v11;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = GUILD_BATTLE::CNormalGuildBattle::GetRed(pkBattle);
  v13 = GUILD_BATTLE::CNormalGuildBattle::GetBlue(pkBattlea);
  LODWORD(v4) = ((int (__fastcall *)(GUILD_BATTLE::CNormalGuildBattleStateCountDown *, char *))v17->vfptr->GetTerm)(
                  v17,
                  &v14);
  v5 = ATL::CTimeSpan::GetMinutes(v4);
  GUILD_BATTLE::CNormalGuildBattleGuild::NotifyLeftMinuteBeforeStart(v12, v5);
  LODWORD(v6) = ((int (__fastcall *)(GUILD_BATTLE::CNormalGuildBattleStateCountDown *, char *))v17->vfptr->GetTerm)(
                  v17,
                  &v15);
  v7 = ATL::CTimeSpan::GetMinutes(v6);
  GUILD_BATTLE::CNormalGuildBattleGuild::NotifyLeftMinuteBeforeStart(v13, v7);
  LODWORD(v8) = ((int (__fastcall *)(GUILD_BATTLE::CNormalGuildBattleStateCountDown *, char *))v17->vfptr->GetTerm)(
                  v17,
                  &v16);
  v9 = ATL::CTimeSpan::GetMinutes(v8);
  GUILD_BATTLE::CNormalGuildBattleState::Log(
    (GUILD_BATTLE::CNormalGuildBattleState *)&v17->vfptr,
    pkBattlea,
    "Enter : Notify%uMinuteBeforeStart",
    (unsigned int)v9);
  return 0i64;
}
