/*
 * Function: ?OnUsStateTBLInit@DfAIMgr@@SAHXZ
 * Address: 0x14014FD10
 */

signed __int64 __cdecl DfAIMgr::OnUsStateTBLInit()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  UsStateTBL *v2; // rax@5
  UsPoint<UsStateTBL> *v3; // rax@7
  int v4; // eax@7
  __int64 v6; // [sp+0h] [bp-78h]@1
  unsigned int j; // [sp+30h] [bp-48h]@4
  UsPoint<UsStateTBL> *v8; // [sp+38h] [bp-40h]@4
  UsStateTBL *v9; // [sp+40h] [bp-38h]@4
  UsStateTBL *pObject; // [sp+48h] [bp-30h]@7
  DfAIMgr *v11; // [sp+50h] [bp-28h]@4
  __int64 v12; // [sp+58h] [bp-20h]@4
  UsStateTBL *v13; // [sp+60h] [bp-18h]@5

  v0 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  v12 = -2i64;
  j = 0;
  v8 = (UsPoint<UsStateTBL> *)CRFMonsterAIMgr::Instance();
  v9 = 0i64;
  v11 = (DfAIMgr *)operator new(0x48ui64);
  if ( v11 )
  {
    DfAIMgr::DfAIMgr(v11);
    v13 = v2;
  }
  else
  {
    v13 = 0i64;
  }
  pObject = v13;
  v3 = UsPoint<UsStateTBL>::operator=(v8, v13);
  v9 = UsPoint<UsStateTBL>::operator UsStateTBL *(v3);
  UsStateTBL::SetInitFunction(v9, (int (__cdecl *)(UsStateTBL *, Us_HFSM *))DfAIMgr::OnDFInitHFSM);
  UsStateTBL::SetExternCallFunction(
    v9,
    (void (__cdecl *)(Us_HFSM *, unsigned int, void *, int))DfAIMgr::OnDfExternCallFun);
  UsStateTBL::Alloc(v9, 8, 25, 48);
  UsStateTBL::SetHFSMNode(v9, 1, 2u, 0x7D0u, -1, 1);
  UsStateTBL::SetHFSMNode(v9, 2, 4u, 0x1F4u, -1, 1);
  UsStateTBL::SetHFSMNode(v9, 7, 5u, 0x3E8u, -1, 1);
  v4 = rand();
  UsStateTBL::SetHFSMNode(v9, 6, 7u, 100 * (v4 % 50) + 2500, -1, 1);
  UsStateTBL::SetHFSMNode(v9, 3, 0xBu, 0x7530u, -1, 1);
  UsStateTBL::SetHFSMNode(v9, 4, 0x10u, 0x2710u, -1, 1);
  UsStateTBL::SetHFSMNode(v9, 5, 0x17u, 0x7530u, -1, 1);
  UsStateTBL::Add(v9, 1, 1, 0, 0, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Atp_SearchStart_OnLoop);
  UsStateTBL::Add(v9, 1, 1, 27, 2, 0i64);
  UsStateTBL::Add(v9, 1, 2, 26, 1, 0i64);
  UsStateTBL::Add(v9, 1, 1, 29, 0, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Atp_Lost_Handler);
  UsStateTBL::Add(v9, 1, 2, 29, 0, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Atp_Lost_Handler);
  UsStateTBL::Add(v9, 1, 1, 28, 0, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Atp_Searched_Handler);
  UsStateTBL::Add(v9, 1, 2, 28, 0, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Atp_Searched_Handler);
  UsStateTBL::Add(v9, 2, 3, 0, 0, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Mon_SearchStart_OnLoop);
  UsStateTBL::Add(v9, 2, 3, 31, 4, 0i64);
  UsStateTBL::Add(v9, 2, 4, 30, 3, 0i64);
  UsStateTBL::Add(v9, 2, 3, 32, 0, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Mon_Searched_Handler);
  UsStateTBL::Add(v9, 2, 4, 32, 0, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Mon_Searched_Handler);
  UsStateTBL::Add(v9, 7, 5, 34, 6, 0i64);
  UsStateTBL::Add(v9, 7, 6, 33, 5, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Mv_Stop_Handler);
  UsStateTBL::Add(v9, 7, 6, 0, 0, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Mv_Go_OnLoop);
  UsStateTBL::Add(v9, 6, 7, 28, 9, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Action_Change_Handler);
  UsStateTBL::Add(v9, 6, 8, 28, 9, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Action_Change_Handler);
  UsStateTBL::Add(v9, 6, 9, 29, 7, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Action_Change_Handler);
  UsStateTBL::Add(v9, 6, 9, 38, 8, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Action_Change_Handler);
  UsStateTBL::Add(v9, 6, 7, 38, 8, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Action_Change_Handler);
  UsStateTBL::Add(v9, 6, 7, 0, 0, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Action_Wait_OnLoop);
  UsStateTBL::Add(v9, 6, 8, 0, 0, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Action_Patrol_OnLoop);
  UsStateTBL::Add(v9, 6, 9, 0, 0, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Action_Attack_OnLoop);
  UsStateTBL::Add(v9, 6, 10, 0, 0, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Action_Runaway_OnLoop);
  for ( j = 11; j <= 0xF; ++j )
    UsStateTBL::Add(v9, 3, j, 0, 0, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Emotion_OnLoop);
  UsStateTBL::Add(v9, 3, 11, 35, 12, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Emotion_OnChange);
  UsStateTBL::Add(v9, 3, 12, 35, 13, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Emotion_OnChange);
  UsStateTBL::Add(v9, 3, 13, 35, 14, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Emotion_OnChange);
  UsStateTBL::Add(v9, 3, 14, 35, 15, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Emotion_OnChange);
  UsStateTBL::Add(v9, 3, 15, 36, 14, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Emotion_OnChange);
  UsStateTBL::Add(v9, 3, 14, 36, 13, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Emotion_OnChange);
  UsStateTBL::Add(v9, 3, 13, 36, 12, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Emotion_OnChange);
  UsStateTBL::Add(v9, 3, 12, 36, 11, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Emotion_OnChange);
  for ( j = 16; j <= 0x15; ++j )
    UsStateTBL::Add(v9, 4, j, 0, 0, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Condition_OnLoop);
  for ( j = 16; j <= 0x15; ++j )
    UsStateTBL::Add(v9, 4, j, 39, 16, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Condition_OnChange);
  for ( j = 16; j <= 0x15; ++j )
    UsStateTBL::Add(v9, 4, j, 40, 17, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Condition_OnChange);
  for ( j = 16; j <= 0x15; ++j )
    UsStateTBL::Add(v9, 4, j, 41, 18, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Condition_OnChange);
  for ( j = 16; j <= 0x15; ++j )
    UsStateTBL::Add(v9, 4, j, 42, 19, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Condition_OnChange);
  for ( j = 16; j <= 0x15; ++j )
    UsStateTBL::Add(v9, 4, j, 43, 20, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Condition_OnChange);
  for ( j = 16; j <= 0x15; ++j )
    UsStateTBL::Add(v9, 4, j, 44, 21, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Condition_OnChange);
  UsStateTBL::Add(v9, 5, 23, 45, 22, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Assist_OnChange);
  UsStateTBL::Add(v9, 5, 24, 46, 23, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Assist_OnChange);
  UsStateTBL::Add(v9, 5, 22, 46, 23, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Assist_OnChange);
  UsStateTBL::Add(v9, 5, 22, 47, 24, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Assist_OnChange);
  UsStateTBL::Add(v9, 5, 23, 47, 24, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Assist_OnChange);
  UsStateTBL::Add(v9, 5, 22, 0, 0, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Assist_OnLoop);
  UsStateTBL::Add(v9, 5, 24, 0, 0, (void (__cdecl *)(Us_HFSM *, unsigned int, void *))DfAIMgr::Assist_OnLoop);
  return 1i64;
}
