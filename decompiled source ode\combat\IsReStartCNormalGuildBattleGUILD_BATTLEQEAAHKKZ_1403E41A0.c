/*
 * Function: ?IsReStart@CNormalGuildBattle@GUILD_BATTLE@@QEAAHKK@Z
 * Address: 0x1403E41A0
 */

signed __int64 __fastcall GUILD_BATTLE::CNormalGuildBattle::IsReStart(GUILD_BATTLE::CNormalGuildBattle *this, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattle *v8; // [sp+40h] [bp+8h]@1
  unsigned int v9; // [sp+48h] [bp+10h]@1
  unsigned int dwSerial; // [sp+50h] [bp+18h]@1

  dwSerial = dwCharacSerial;
  v9 = dwGuildSerial;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = 0;
  if ( dwGuildSerial == GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v8->m_k1P) )
  {
    v7 = GUILD_BATTLE::CNormalGuildBattleGuild::IsReStart(&v8->m_k1P, dwSerial);
    if ( v7 >= 0 )
    {
      if ( v7 <= 0 )
        result = 0i64;
      else
        result = 0xFFFFFFFFi64;
    }
    else
    {
      result = 145i64;
    }
  }
  else if ( v9 == GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v8->m_k2P) )
  {
    v7 = GUILD_BATTLE::CNormalGuildBattleGuild::IsReStart(&v8->m_k2P, dwSerial);
    if ( v7 >= 0 )
    {
      if ( v7 <= 0 )
        result = 0i64;
      else
        result = 0xFFFFFFFFi64;
    }
    else
    {
      result = 145i64;
    }
  }
  else
  {
    result = 141i64;
  }
  return result;
}
