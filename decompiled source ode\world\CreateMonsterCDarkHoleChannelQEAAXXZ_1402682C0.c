/*
 * Function: ?<PERSON><PERSON><PERSON><PERSON><PERSON>@CDarkHoleChannel@@QEAAXXZ
 * Address: 0x1402682C0
 */

void __fastcall CDarkHoleChannel::CreateMonster(CDarkHoleChannel *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // ecx@20
  __int64 v4; // [sp+0h] [bp-A8h]@1
  _dummy_position *pDumPosition; // [sp+20h] [bp-88h]@20
  bool bRobExp; // [sp+28h] [bp-80h]@20
  bool bRewardExp; // [sp+30h] [bp-78h]@20
  bool bDungeon; // [sp+38h] [bp-70h]@20
  bool bWithoutFail; // [sp+40h] [bp-68h]@20
  bool bApplyRopExpField; // [sp+48h] [bp-60h]@20
  CMapData *pMap; // [sp+50h] [bp-58h]@5
  int *v12; // [sp+58h] [bp-50h]@5
  int j; // [sp+60h] [bp-48h]@5
  _mon_block *v14; // [sp+68h] [bp-40h]@7
  int v15; // [sp+70h] [bp-38h]@7
  int k; // [sp+74h] [bp-34h]@7
  _mon_active *pActiveRec; // [sp+78h] [bp-30h]@10
  _mon_active_fld *v18; // [sp+80h] [bp-28h]@10
  unsigned int v19; // [sp+88h] [bp-20h]@10
  unsigned int v20; // [sp+8Ch] [bp-1Ch]@13
  unsigned int l; // [sp+90h] [bp-18h]@15
  int v22; // [sp+94h] [bp-14h]@18
  CMonster *v23; // [sp+98h] [bp-10h]@20
  CDarkHoleChannel *v24; // [sp+B0h] [bp+8h]@1

  v24 = this;
  v1 = &v4;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( _LAYER_SET::IsActiveLayer(v24->m_pLayerSet) )
  {
    pMap = v24->m_pQuestSetup->pUseMap;
    v12 = &v24->m_pLayerSet->m_pMB->m_nBlockNum;
    for ( j = 0; j < pMap->m_nMonBlockNum; ++j )
    {
      v14 = &pMap->m_pMonBlock[j];
      v15 = CRecordData::GetRecordNum((CRecordData *)(*((_QWORD *)v12 + 1) + 176i64 * j));
      for ( k = 0; k < v15; ++k )
      {
        pActiveRec = &v24->m_pLayerSet->m_MonAct[j][k];
        v18 = pActiveRec->m_pActRec;
        v19 = v18->m_dwRegenLimNum;
        if ( pActiveRec->m_dwCumMonNum < v19
          && pActiveRec->m_wMonRecIndex != 0xFFFF
          && v18->m_dwRegenProp > rand() % 100 )
        {
          v20 = v19 - pActiveRec->m_dwCumMonNum;
          if ( (v20 & 0x80000000) != 0 )
            v20 = 0;
          for ( l = 0; (signed int)l < (signed int)v20; ++l )
          {
            v22 = _mon_block::SelectDummyIndex(v14);
            if ( v22 != -1 )
            {
              if ( v14->m_pDumPos[v22]->m_bPosAble )
              {
                v3 = pActiveRec->m_wMonRecIndex;
                bApplyRopExpField = 0;
                bWithoutFail = 1;
                bDungeon = 1;
                bRewardExp = 1;
                bRobExp = 0;
                pDumPosition = v14->m_pDumPos[v22];
                v23 = CreateRespawnMonster(pMap, v24->m_wLayerIndex, v3, pActiveRec, pDumPosition, 0, 1, 1, 1, 0);
              }
            }
          }
        }
      }
    }
  }
}
