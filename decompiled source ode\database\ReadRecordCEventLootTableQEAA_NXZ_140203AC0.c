/*
 * Function: ?ReadRecord@CEventLootTable@@QEAA_NXZ
 * Address: 0x140203AC0
 */

char __fastcall CEventLootTable::ReadRecord(CEventLootTable *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v3; // rax@10
  __int16 v4; // ax@12
  __int16 v5; // ax@12
  __int16 v6; // ax@12
  __int16 v7; // ax@12
  char result; // al@14
  __int64 v9; // [sp+0h] [bp-438h]@1
  FILE *File; // [sp+28h] [bp-410h]@4
  char Buf; // [sp+50h] [bp-3E8h]@6
  int v12; // [sp+254h] [bp-1E4h]@4
  int v13; // [sp+258h] [bp-1E0h]@8
  char Dst; // [sp+270h] [bp-1C8h]@5
  char v15; // [sp+2B0h] [bp-188h]@5
  char v16; // [sp+2F0h] [bp-148h]@5
  char v17; // [sp+330h] [bp-108h]@5
  char v18; // [sp+370h] [bp-C8h]@5
  char *ppszDst; // [sp+3C8h] [bp-70h]@5
  char *Str; // [sp+3D0h] [bp-68h]@5
  char *v21; // [sp+3D8h] [bp-60h]@5
  char *v22; // [sp+3E0h] [bp-58h]@5
  char *v23; // [sp+3E8h] [bp-50h]@5
  char *Dest; // [sp+3F8h] [bp-40h]@12
  char *v25; // [sp+408h] [bp-30h]@12
  CEventLootTable::_event_drop *v26; // [sp+410h] [bp-28h]@9
  __int64 v27; // [sp+418h] [bp-20h]@4
  char *v28; // [sp+420h] [bp-18h]@10
  unsigned __int64 v29; // [sp+428h] [bp-10h]@4
  CEventLootTable *v30; // [sp+440h] [bp+8h]@1

  v30 = this;
  v1 = &v9;
  for ( i = 268i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v27 = -2i64;
  v29 = (unsigned __int64)&v9 ^ _security_cookie;
  v12 = 0;
  if ( fopen_s(&File, ".\\Initialize\\EventLooting.ini", "r+t") )
  {
    result = 0;
  }
  else
  {
    ppszDst = &Dst;
    Str = &v15;
    v21 = &v16;
    v22 = &v17;
    v23 = &v18;
    while ( fgets(&Buf, 512, File) )
    {
      if ( Buf != 59 )
      {
        memset_0(&Dst, 0, 0x140ui64);
        v13 = ParsingCommandA(&Buf, 5, &ppszDst, 64);
        if ( v13 == 5 )
        {
          v26 = (CEventLootTable::_event_drop *)operator new(0x50ui64);
          if ( v26 )
          {
            CEventLootTable::_event_drop::_event_drop(v26);
            v28 = v3;
          }
          else
          {
            v28 = 0i64;
          }
          v25 = v28;
          Dest = v28;
          strcpy_0(v28, ppszDst);
          v4 = atoi(Str);
          *((_WORD *)Dest + 32) = v4;
          v5 = atoi(v21);
          *((_WORD *)Dest + 33) = v5;
          v6 = atoi(v22);
          *((_WORD *)Dest + 34) = v6;
          v7 = atoi(v23);
          *((_WORD *)Dest + 35) = v7;
          *((_QWORD *)Dest + 9) = 0i64;
          CEventLootTable::AddRecord(v30, (CEventLootTable::_event_drop *)Dest);
        }
      }
    }
    fclose(File);
    result = 1;
  }
  return result;
}
