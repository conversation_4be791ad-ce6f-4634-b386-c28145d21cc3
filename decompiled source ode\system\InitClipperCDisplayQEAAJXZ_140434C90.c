/*
 * Function: ?InitClipper@CDisplay@@QEAAJXZ
 * Address: 0x140434C90
 */

__int64 __fastcall CDisplay::InitClipper(CDisplay *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  __int64 v5; // [sp+28h] [bp-20h]@4
  unsigned int v6; // [sp+34h] [bp-14h]@4
  CDisplay *v7; // [sp+50h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = ((int (__fastcall *)(IDirectDraw7 *, _QWORD, __int64 *, _QWORD))v7->m_pDD->vfptr[1].AddRef)(
         v7->m_pDD,
         0i64,
         &v5,
         0i64);
  if ( (v6 & 0x80000000) == 0 )
  {
    (*(void (__fastcall **)(__int64, _QWORD, HWND__ *))(*(_QWORD *)v5 + 64i64))(v5, 0i64, v7->m_hWnd);
    v6 = ((int (__fastcall *)(IDirectDrawSurface7 *, __int64))v7->m_pddsFrontBuffer->vfptr[9].AddRef)(
           v7->m_pddsFrontBuffer,
           v5);
    if ( (v6 & 0x80000000) == 0 )
    {
      if ( v5 )
      {
        (*(void (__fastcall **)(__int64))(*(_QWORD *)v5 + 16i64))(v5);
        v5 = 0i64;
      }
      result = 0i64;
    }
    else
    {
      result = v6;
    }
  }
  else
  {
    result = v6;
  }
  return result;
}
