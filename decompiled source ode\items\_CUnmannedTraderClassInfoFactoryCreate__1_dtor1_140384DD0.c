/*
 * Function: _CUnmannedTraderClassInfoFactory::Create_::_1_::dtor$1
 * Address: 0x140384DD0
 */

void __fastcall CUnmannedTraderClassInfoFactory::Create_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>((std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)(a2 + 72));
}
