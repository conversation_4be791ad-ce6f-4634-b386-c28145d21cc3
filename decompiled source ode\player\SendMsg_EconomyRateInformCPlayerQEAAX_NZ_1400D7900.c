/*
 * Function: ?SendMsg_EconomyRateInform@CPlayer@@QEAAX_N@Z
 * Address: 0x1400D7900
 */

void __fastcall CPlayer::SendMsg_EconomyRateInform(CPlayer *this, bool bStart)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@4
  int v5; // eax@4
  int v6; // xmm0_4@4
  int v7; // eax@4
  int v8; // eax@4
  __int64 v9; // [sp+0h] [bp-98h]@1
  char szMsg; // [sp+38h] [bp-60h]@4
  float v11; // [sp+39h] [bp-5Fh]@4
  int v12; // [sp+3Dh] [bp-5Bh]@4
  __int16 v13; // [sp+41h] [bp-57h]@4
  __int16 v14[3]; // [sp+43h] [bp-55h]@7
  int v15; // [sp+49h] [bp-4Fh]@4
  int nRaceCode; // [sp+54h] [bp-44h]@5
  char pbyType; // [sp+64h] [bp-34h]@8
  char v18; // [sp+65h] [bp-33h]@8
  unsigned __int64 v19; // [sp+80h] [bp-18h]@4
  CPlayer *v20; // [sp+A0h] [bp+8h]@1
  bool v21; // [sp+A8h] [bp+10h]@1

  v21 = bStart;
  v20 = this;
  v2 = &v9;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v19 = (unsigned __int64)&v9 ^ _security_cookie;
  szMsg = bStart;
  v4 = CPlayerDB::GetRaceCode(&v20->m_Param);
  v5 = eGetRate(v4);
  *(float *)&v6 = (float)v5;
  v11 = (float)v5;
  v7 = CPlayerDB::GetRaceCode(&v20->m_Param);
  eGetTex(v7);
  v12 = v6;
  v13 = eGetMgrValue();
  v8 = CPlayerDB::GetRaceCode(&v20->m_Param);
  eGetOreRate(v8);
  v15 = v6;
  if ( !v21 )
  {
    for ( nRaceCode = 0; nRaceCode < 3; ++nRaceCode )
      v14[nRaceCode] = eGetGuide(nRaceCode);
  }
  pbyType = 12;
  v18 = 15;
  CNetProcess::LoadSendMsg(unk_1414F2088, v20->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x15u);
}
