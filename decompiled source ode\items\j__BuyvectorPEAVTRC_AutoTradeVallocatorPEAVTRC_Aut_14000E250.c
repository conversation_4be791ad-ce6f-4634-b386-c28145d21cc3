/*
 * Function: j_?_Buy@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@IEAA_N_K@Z
 * Address: 0x14000E250
 */

bool __fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Buy(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, unsigned __int64 _Capacity)
{
  return std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Buy(this, _Capacity);
}
