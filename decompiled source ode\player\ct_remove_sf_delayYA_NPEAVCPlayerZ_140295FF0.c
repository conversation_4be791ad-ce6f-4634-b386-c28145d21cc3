/*
 * Function: ?ct_remove_sf_delay@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140295FF0
 */

char __fastcall ct_remove_sf_delay(CPlayer *pOne)
{
  char *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  bool v4; // [sp+0h] [bp-18h]@1
  CPlayer *v5; // [sp+20h] [bp+8h]@1

  v5 = pOne;
  v1 = (char *)&v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 += 4;
  }
  if ( v5 && v5->m_bOper )
  {
    v4 = v5->m_bSFDelayNotCheck == 0;
    v5->m_bSFDelayNotCheck = v4;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
