/*
 * Function: ?SendMsg_GroupTargetInform@CPlayer@@QEAAXEPEAD@Z
 * Address: 0x1400E2C30
 */

void __fastcall CPlayer::SendMsg_GroupTargetInform(CPlayer *this, char byGroupType, char *pwszName)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-D8h]@1
  char szMsg; // [sp+40h] [bp-98h]@4
  char Dest; // [sp+41h] [bp-97h]@4
  char pbyType; // [sp+A4h] [bp-34h]@4
  char v9; // [sp+A5h] [bp-33h]@4
  unsigned __int64 v10; // [sp+C0h] [bp-18h]@4
  CPlayer *v11; // [sp+E0h] [bp+8h]@1

  v11 = this;
  v3 = &v5;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  szMsg = byGroupType;
  strcpy_0(&Dest, pwszName);
  pbyType = 13;
  v9 = 111;
  CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x42u);
}
