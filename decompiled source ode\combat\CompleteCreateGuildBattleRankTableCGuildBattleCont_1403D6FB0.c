/*
 * Function: ?CompleteCreateGuildBattleRankTable@CGuildBattleController@@QEAAXE@Z
 * Address: 0x1403D6FB0
 */

void __fastcall CGuildBattleController::CompleteCreateGuildBattleRankTable(CGuildBattleController *this, char byResult)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v4; // rax@7
  __int64 v5; // [sp+0h] [bp-38h]@1
  const char *v6; // [sp+20h] [bp-18h]@5
  unsigned int v7; // [sp+28h] [bp-10h]@7

  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( byResult )
    v6 = "Fail";
  else
    v6 = "Success";
  v7 = (unsigned __int8)byResult;
  v4 = CPvpUserAndGuildRankingSystem::Instance();
  CPvpUserAndGuildRankingSystem::Log(
    v4,
    "CGuildBattleController::CompleteCreateGuildBattleRankTable( BYTE byResult(%u) ) : Create %s!",
    v7,
    v6);
}
