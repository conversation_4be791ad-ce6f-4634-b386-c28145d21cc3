/*
 * Function: ?_ResponseAppoint@ClassOrderProcessor@@AEAAHPEAVCPlayer@@PEAD@Z
 * Address: 0x1402B87C0
 */

__int64 __fastcall ClassOrderProcessor::_ResponseAppoint(ClassOrderProcessor *this, CPlayer *pOne, char *pData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v5; // rax@4
  unsigned int v6; // eax@4
  __int64 result; // rax@5
  char v8; // al@7
  CandidateMgr *v9; // rax@12
  CPvpUserAndGuildRankingSystem *v10; // rax@17
  PatriarchElectProcessor *v11; // rax@17
  unsigned int v12; // eax@17
  __int64 v13; // [sp+0h] [bp-68h]@1
  char *pwszAvatorName; // [sp+20h] [bp-48h]@10
  unsigned int v15; // [sp+28h] [bp-40h]@17
  char *v16; // [sp+30h] [bp-38h]@4
  CPlayer *v17; // [sp+38h] [bp-30h]@4
  int v18; // [sp+40h] [bp-28h]@7
  int v19; // [sp+44h] [bp-24h]@4
  _candidate_info::ClassType eClassType; // [sp+48h] [bp-20h]@12
  unsigned int v21; // [sp+4Ch] [bp-1Ch]@17
  int v22; // [sp+50h] [bp-18h]@17
  int v23; // [sp+54h] [bp-14h]@17
  unsigned int v24; // [sp+58h] [bp-10h]@17
  ClassOrderProcessor *v25; // [sp+70h] [bp+8h]@1
  CPlayer *pUser; // [sp+78h] [bp+10h]@1

  pUser = pOne;
  v25 = this;
  v3 = &v13;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v16 = pData;
  v19 = CPlayerDB::GetRaceCode(&pOne->m_Param);
  v5 = CPvpUserAndGuildRankingSystem::Instance();
  v6 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v5, v19, 0);
  v17 = GetPtrPlayerFromSerial(&g_Player, 2532, v6);
  if ( pUser->m_byPatriarchAppointPropose == 255 )
  {
    result = 0i64;
  }
  else
  {
    if ( *v16 )
    {
      if ( *v16 == 1 && v17 && v17->m_bOper )
      {
        pwszAvatorName = CPlayerDB::GetCharNameW(&pUser->m_Param);
        ClassOrderProcessor::SendMsg_QueryAppointResult(
          v25,
          v17->m_id.wIndex,
          5,
          pUser->m_byPatriarchAppointPropose,
          pwszAvatorName);
      }
    }
    else
    {
      v8 = CPlayerDB::GetRaceCode(&pUser->m_Param);
      v18 = ClassOrderProcessor::_CheckUserInfo(v25, v8, pUser->m_byPatriarchAppointPropose, pUser);
      if ( v18 )
      {
        if ( v17 )
        {
          if ( v17->m_bOper )
          {
            pwszAvatorName = CPlayerDB::GetCharNameW(&pUser->m_Param);
            ClassOrderProcessor::SendMsg_QueryAppointResult(
              v25,
              v17->m_id.wIndex,
              v18,
              pUser->m_byPatriarchAppointPropose,
              pwszAvatorName);
          }
        }
      }
      else
      {
        eClassType = pUser->m_byPatriarchAppointPropose;
        v9 = CandidateMgr::Instance();
        if ( CandidateMgr::AppointPatriarchGroup(v9, pUser, eClassType) )
        {
          v21 = CPlayerDB::GetCharSerial(&pUser->m_Param);
          v22 = pUser->m_byPatriarchAppointPropose;
          v23 = CPlayerDB::GetRaceCode(&pUser->m_Param);
          v10 = CPvpUserAndGuildRankingSystem::Instance();
          v24 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v10, v23, 0);
          v11 = PatriarchElectProcessor::Instance();
          v12 = PatriarchElectProcessor::GetElectSerial(v11);
          v15 = v21;
          LODWORD(pwszAvatorName) = v22;
          CLogFile::Write(&v25->_kSysLog, "Patriarch Appoint (ES:%d, PS:%d) >> Class(%d) Avator(%d)", v12, v24);
        }
        else if ( v17 && v17->m_bOper )
        {
          pwszAvatorName = CPlayerDB::GetCharNameW(&pUser->m_Param);
          ClassOrderProcessor::SendMsg_QueryAppointResult(
            v25,
            v17->m_id.wIndex,
            2,
            pUser->m_byPatriarchAppointPropose,
            pwszAvatorName);
        }
      }
    }
    pUser->m_byPatriarchAppointPropose = -1;
    pUser->m_dwPatriarchAppointTime = -1;
    result = 0i64;
  }
  return result;
}
