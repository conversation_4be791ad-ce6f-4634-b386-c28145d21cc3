/*
 * Function: j_??$_Uninit_copy@PEAPEAVCUnmannedTraderSubClassInfo@@PEAPEAV1@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@YAPEAPEAVCUnmannedTraderSubClassInfo@@PEAPEAV1@00AEAV?$allocator@PEAVCUnmannedTraderSubClassInfo@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400133E0
 */

CUnmannedTraderSubClassInfo **__fastcall std::_Uninit_copy<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo * *,std::allocator<CUnmannedTraderSubClassInfo *>>(CUnmannedTraderSubClassInfo **_First, CUnmannedTraderSubClassInfo **_Last, CUnmannedTraderSubClassInfo **_Dest, std::allocator<CUnmannedTraderSubClassInfo *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo * *,std::allocator<CUnmannedTraderSubClassInfo *>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
