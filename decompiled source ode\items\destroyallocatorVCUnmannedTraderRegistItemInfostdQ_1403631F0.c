/*
 * Function: ?destroy@?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@QEAAXPEAVCUnmannedTraderRegistItemInfo@@@Z
 * Address: 0x1403631F0
 */

void __fastcall std::allocator<CUnmannedTraderRegistItemInfo>::destroy(std::allocator<CUnmannedTraderRegistItemInfo> *this, CUnmannedTraderRegistItemInfo *_Ptr)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1

  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Destroy<CUnmannedTraderRegistItemInfo>(_Ptr);
}
