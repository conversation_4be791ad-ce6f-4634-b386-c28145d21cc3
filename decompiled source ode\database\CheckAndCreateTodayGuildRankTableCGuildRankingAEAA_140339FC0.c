/*
 * Function: ?CheckAndCreateTodayGuildRankTable@CGuildRanking@@AEAA_NPEAD@Z
 * Address: 0x140339FC0
 */

char __fastcall CGuildRanking::CheckAndCreateTodayGuildRankTable(CGuildRanking *this, char *szDate)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v5; // [sp+0h] [bp-78h]@1
  char DstBuf; // [sp+28h] [bp-50h]@4
  char v7; // [sp+29h] [bp-4Fh]@4
  unsigned __int64 v8; // [sp+60h] [bp-18h]@4
  char *szDatea; // [sp+88h] [bp+10h]@1

  szDatea = szDate;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = (unsigned __int64)&v5 ^ _security_cookie;
  DstBuf = 0;
  memset(&v7, 0, 0x1Fui64);
  CLogFile::Write(&stru_1799C8F30, "Guild Rank Table Make Start!!");
  sprintf_s(&DstBuf, 0x20ui64, "tbl_GuildRank%s", szDatea);
  if ( CRFNewDatabase::TableExist((CRFNewDatabase *)&pkDB->vfptr, &DstBuf) )
    goto LABEL_12;
  if ( !CRFWorldDatabase::Update_GuildRank(pkDB, szDatea) )
  {
    MyMessageBox("DatabaseInit", "create guild-rank-table fail");
    return 0;
  }
  if ( CRFWorldDatabase::Update_GuildGrade(pkDB) )
  {
LABEL_12:
    CLogFile::Write(&stru_1799C8F30, "Guild Rank Table Make Complete!!");
    result = 1;
  }
  else
  {
    MyMessageBox("DatabaseInit", "update guild-grade fail");
    result = 0;
  }
  return result;
}
