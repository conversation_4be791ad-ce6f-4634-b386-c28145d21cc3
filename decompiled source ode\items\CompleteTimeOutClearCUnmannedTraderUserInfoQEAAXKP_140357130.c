/*
 * Function: ?CompleteTimeOutClear@CUnmannedTraderUserInfo@@QEAAXKPEAVCLogFile@@@Z
 * Address: 0x140357130
 */

void __usercall CUnmannedTraderUserInfo::CompleteTimeOutClear(CUnmannedTraderUserInfo *this@<rcx>, unsigned int dwRegistSerial@<edx>, CLogFile *pkLogger@<r8>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderRegistItemInfo *v6; // rax@6
  CUnmannedTraderRegistItemInfo *v7; // rax@6
  char *v8; // rax@13
  CUnmannedTraderTaxRateManager *v9; // rax@14
  CUnmannedTraderRegistItemInfo *v10; // rax@14
  unsigned int v11; // eax@14
  __int64 v12; // [sp+0h] [bp-B8h]@1
  char *pszFileName; // [sp+20h] [bp-98h]@13
  _DWORD byTax[4]; // [sp+28h] [bp-90h]@13
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > result; // [sp+38h] [bp-80h]@4
  unsigned __int16 v16; // [sp+54h] [bp-64h]@6
  CPlayer *v17; // [sp+58h] [bp-60h]@6
  _STORAGE_LIST::_db_con *pRegItem; // [sp+60h] [bp-58h]@12
  bool v19; // [sp+68h] [bp-50h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v20; // [sp+70h] [bp-48h]@4
  __int64 v21; // [sp+88h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v22; // [sp+90h] [bp-28h]@4
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *_Right; // [sp+98h] [bp-20h]@4
  unsigned int v24; // [sp+A0h] [bp-18h]@9
  int v25; // [sp+A4h] [bp-14h]@13
  int v26; // [sp+A8h] [bp-10h]@14
  char v27; // [sp+ACh] [bp-Ch]@14
  CUnmannedTraderUserInfo *v28; // [sp+C0h] [bp+8h]@1
  unsigned int dwRegistSeriala; // [sp+C8h] [bp+10h]@1
  CLogFile *v30; // [sp+D0h] [bp+18h]@1

  v30 = pkLogger;
  dwRegistSeriala = dwRegistSerial;
  v28 = this;
  v4 = &v12;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v21 = -2i64;
  CUnmannedTraderUserInfo::Find(v28, &result, dwRegistSerial);
  v22 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
          &v28->m_vecRegistItemInfo,
          &v20);
  _Right = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)v22;
  v19 = std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator==(
          (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&result._Mycont,
          (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v22->_Mycont);
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v20);
  if ( v19 )
  {
    CLogFile::Write(
      v30,
      "CUnmannedTraderUserInfo::CompleteTimeOutClear(...)\r\n\t\tFind( dwRegistSerial(%u) ) Item Fail!\r\n",
      dwRegistSeriala);
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
  }
  else
  {
    v6 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(&result);
    v16 = CUnmannedTraderRegistItemInfo::GetItemSerial(v6);
    v7 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(&result);
    CUnmannedTraderRegistItemInfo::SetState(v7, 13);
    CUnmannedTraderUserInfo::CountRegistItem(v28);
    v17 = CUnmannedTraderUserInfo::FindOwner(v28);
    if ( v17 && v17->m_bOper )
    {
      pRegItem = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v17->m_Param.m_dbInven.m_nListNum, v16);
      if ( pRegItem )
      {
        CMgrAvatorItemHistory::time_out_cancel_auto_trade(
          &CPlayer::s_MgrItemHistory,
          v17->m_ObjID.m_wIndex,
          dwRegistSeriala,
          pRegItem,
          v17->m_szItemHistoryFileName);
        v26 = CPlayerDB::GetRaceCode(&v17->m_Param);
        v9 = CUnmannedTraderTaxRateManager::Instance();
        CUnmannedTraderTaxRateManager::GetTaxRate(v9, v26);
        v27 = (signed int)ffloor(a4 * 100.0);
        v10 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(&result);
        v11 = CUnmannedTraderRegistItemInfo::GetPrice(v10);
        LOBYTE(byTax[0]) = v27;
        LODWORD(pszFileName) = v11;
        CUnmannedTraderUserInfo::SendNotifyCloseItem(v28, v17->m_ObjID.m_wIndex, v16, dwRegistSeriala, v11, v27);
        std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
      }
      else
      {
        v25 = v16;
        v8 = CPlayerDB::GetCharNameA(&v17->m_Param);
        byTax[0] = v25;
        LODWORD(pszFileName) = dwRegistSeriala;
        CLogFile::Write(
          v30,
          "CUnmannedTraderUserInfo::CompleteTimeOutClear(...)\r\n"
          "\t\tOnwer : Serial(%u) Name(%s)\r\n"
          "\t\tdwRegistSerial(%u)\r\n"
          "\t\tpkOwner->m_Param.m_dbInven.GetPtrFromSerial( (*i).GetItemSerial(%u) ) NULL!\r\n",
          v17->m_dwObjSerial,
          v8);
        CUnmannedTraderUserInfo::NotifyRegistItem(v28);
        std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
      }
    }
    else
    {
      if ( v17 )
        v24 = v17->m_bOper;
      else
        v24 = -1;
      CLogFile::Write(
        v30,
        "CUnmannedTraderUserInfo::CompleteTimeOutClear( dwRegistSerial(%u) )\r\n"
        "\t\t( 0 == pkOwner || !pkOwner->m_bOper(%d) )\r\n",
        dwRegistSeriala,
        v24);
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    }
  }
}
