/*
 * Function: ?SendMsg_EndBattle@CHolyStoneSystem@@QEAAXE@Z
 * Address: 0x14027EC30
 */

void __fastcall CHolyStoneSystem::SendMsg_EndBattle(CHolyStoneSystem *this, char byLoseRace)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char v6; // [sp+35h] [bp-43h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  unsigned int dwClientIndex; // [sp+64h] [bp-14h]@4
  CHolyStoneSystem *v10; // [sp+80h] [bp+8h]@1
  char v11; // [sp+88h] [bp+10h]@1

  v11 = byLoseRace;
  v10 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  szMsg = CHolyStoneSystem::GetHolyMasterRace(v10);
  v6 = v11;
  pbyType = 25;
  v8 = 19;
  for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
  {
    if ( *(&g_Player.m_bLive + 50856 * (signed int)dwClientIndex) )
    {
      CPlayer::SetCntEnable(&g_Player + (signed int)dwClientIndex, 0);
      CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &szMsg, 2u);
    }
  }
}
