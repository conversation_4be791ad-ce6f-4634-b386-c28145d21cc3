/*
 * Function: ??0?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEAA@_KPEBV_Container_base@1@@Z
 * Address: 0x14031E370
 */

void __fastcall std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this, unsigned __int64 _Off, std::_Container_base *_Pdeque)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
    (std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v6->_Mycont,
    _Off,
    _Pdeque);
}
