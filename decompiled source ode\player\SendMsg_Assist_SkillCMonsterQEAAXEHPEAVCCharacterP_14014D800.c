/*
 * Function: ?SendMsg_Assist_Skill@CMonster@@QEAAXEHPEAVCCharacter@@PEAU_skill_fld@@H@Z
 * Address: 0x14014D800
 */

void __fastcall CMonster::SendMsg_Assist_Skill(CMonster *this, char byErrCode, int nEffectCode, CCharacter *pDst, _skill_fld *pSkill_fld, int nSFLv)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-D8h]@1
  char szMsg; // [sp+38h] [bp-A0h]@12
  char v10; // [sp+39h] [bp-9Fh]@12
  unsigned __int16 v11; // [sp+3Ah] [bp-9Eh]@12
  unsigned int v12; // [sp+3Ch] [bp-9Ch]@12
  char v13; // [sp+40h] [bp-98h]@12
  unsigned __int16 v14; // [sp+41h] [bp-97h]@12
  unsigned int v15; // [sp+43h] [bp-95h]@12
  char v16; // [sp+47h] [bp-91h]@12
  char v17; // [sp+48h] [bp-90h]@12
  char pbyType; // [sp+64h] [bp-74h]@12
  char v19; // [sp+65h] [bp-73h]@12
  char v20; // [sp+88h] [bp-50h]@14
  char v21; // [sp+89h] [bp-4Fh]@14
  unsigned __int16 v22; // [sp+8Ah] [bp-4Eh]@14
  unsigned int v23; // [sp+8Ch] [bp-4Ch]@14
  char v24; // [sp+90h] [bp-48h]@14
  unsigned __int16 v25; // [sp+91h] [bp-47h]@14
  unsigned int v26; // [sp+93h] [bp-45h]@14
  __int16 v27; // [sp+97h] [bp-41h]@14
  char v28; // [sp+B4h] [bp-24h]@14
  char v29; // [sp+B5h] [bp-23h]@14
  CMonster *v30; // [sp+E0h] [bp+8h]@1

  v30 = this;
  v6 = &v8;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( pDst
    && pSkill_fld
    && (v30->m_nCirclePlayerNum <= 500 || (CMonster *)pDst != v30)
    && (!byErrCode || byErrCode == 100) )
  {
    if ( nEffectCode )
    {
      if ( nEffectCode == 2 )
      {
        v20 = byErrCode;
        v24 = pDst->m_ObjID.m_byID;
        v25 = pDst->m_ObjID.m_wIndex;
        v26 = pDst->m_dwObjSerial;
        v21 = v30->m_ObjID.m_byID;
        v22 = v30->m_ObjID.m_wIndex;
        v23 = v30->m_dwObjSerial;
        v27 = pSkill_fld->m_dwIndex;
        v28 = 17;
        v29 = 9;
        CGameObject::CircleReport((CGameObject *)&v30->vfptr, &v28, &v20, 18, 0);
      }
    }
    else
    {
      szMsg = byErrCode;
      v13 = pDst->m_ObjID.m_byID;
      v14 = pDst->m_ObjID.m_wIndex;
      v15 = pDst->m_dwObjSerial;
      v10 = v30->m_ObjID.m_byID;
      v11 = v30->m_ObjID.m_wIndex;
      v12 = v30->m_dwObjSerial;
      v16 = pSkill_fld->m_dwIndex;
      v17 = nSFLv;
      pbyType = 17;
      v19 = 6;
      CGameObject::CircleReport((CGameObject *)&v30->vfptr, &pbyType, &szMsg, 18, 0);
    }
  }
}
