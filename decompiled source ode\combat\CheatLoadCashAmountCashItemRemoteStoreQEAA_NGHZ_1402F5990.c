/*
 * Function: ?CheatLoadCashAmount@CashItemRemoteStore@@QEAA_NGH@Z
 * Address: 0x1402F5990
 */

char __fastcall CashItemRemoteStore::CheatLoadCashAmount(CashItemRemoteStore *this, unsigned __int16 wSock, int nNum)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned int v6; // eax@8
  CCashDBWorkManager *v7; // rax@8
  __int64 v8; // [sp+0h] [bp-A8h]@1
  CPlayer *v9; // [sp+20h] [bp-88h]@6
  _param_cash_select v10; // [sp+38h] [bp-70h]@8
  char v11; // [sp+80h] [bp-28h]@8
  __int64 v12; // [sp+88h] [bp-20h]@4
  unsigned __int64 size; // [sp+90h] [bp-18h]@8
  unsigned __int64 v14; // [sp+98h] [bp-10h]@4
  CashItemRemoteStore *v15; // [sp+B0h] [bp+8h]@1
  unsigned __int16 v16; // [sp+B8h] [bp+10h]@1

  v16 = wSock;
  v15 = this;
  v3 = &v8;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = -2i64;
  v14 = (unsigned __int64)&v8 ^ _security_cookie;
  if ( CRecordData::GetRecordNum(&v15->_kRecGoods) > nNum )
  {
    v9 = &g_Player + v16;
    if ( v9->m_bOper )
    {
      v6 = CPlayerDB::GetCharSerial(&v9->m_Param);
      _param_cash_select::_param_cash_select(&v10, v9->m_pUserDB->m_dwAccountSerial, v6, v16);
      strcpy_s(v10.in_szAcc, 0xDui64, v9->m_pUserDB->m_szAccountID);
      size = _param_cash_select::size(&v10);
      v7 = CTSingleton<CCashDBWorkManager>::Instance();
      CCashDBWorkManager::PushTask(v7, 0, (char *)&v10, size);
      v11 = 1;
      _param_cash_select::~_param_cash_select(&v10);
      result = v11;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
