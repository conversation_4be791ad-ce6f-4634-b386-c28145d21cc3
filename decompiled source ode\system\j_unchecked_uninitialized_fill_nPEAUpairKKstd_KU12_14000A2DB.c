/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAU?$pair@K<PERSON>@std@@_KU12@V?$allocator@U?$pair@K<PERSON>@std@@@2@@stdext@@YAXPEAU?$pair@KK@std@@_KAEBU12@AEAV?$allocator@U?$pair@KK@std@@@2@@Z
 * Address: 0x14000A2DB
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<std::pair<unsigned long,unsigned long> *,unsigned __int64,std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>(std::pair<unsigned long,unsigned long> *_First, unsigned __int64 _Count, std::pair<unsigned long,unsigned long> *_Val, std::allocator<std::pair<unsigned long,unsigned long> > *_Al)
{
  stdext::unchecked_uninitialized_fill_n<std::pair<unsigned long,unsigned long> *,unsigned __int64,std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>(
    _First,
    _Count,
    _Val,
    _Al);
}
