/*
 * Function: ?OutputMessageSeriesEnd@Filter@CryptoPP@@IEAA_NHH_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
 * Address: 0x1405F9330
 */

char __fastcall CryptoPP::Filter::OutputMessageSeriesEnd(__int64 a1, int a2, int a3, unsigned __int8 a4, __int64 a5)
{
  __int64 v5; // rax@2
  char result; // al@3
  __int64 v7; // [sp+40h] [bp+8h]@1
  int v8; // [sp+48h] [bp+10h]@1
  int v9; // [sp+50h] [bp+18h]@1
  unsigned __int8 v10; // [sp+58h] [bp+20h]@1

  v10 = a4;
  v9 = a3;
  v8 = a2;
  v7 = a1;
  if ( a3
    && (LODWORD(v5) = (*(int (**)(void))(*(_QWORD *)a1 + 328i64))(),
        (unsigned __int8)(*(int (__fastcall **)(__int64, __int64, _QWORD, _QWORD))(*(_QWORD *)v5 + 296i64))(
                           v5,
                           a5,
                           (unsigned int)(v9 - 1),
                           v10)) )
  {
    *(_DWORD *)(v7 + 40) = v8;
    result = 1;
  }
  else
  {
    *(_DWORD *)(v7 + 40) = 0;
    result = 0;
  }
  return result;
}
