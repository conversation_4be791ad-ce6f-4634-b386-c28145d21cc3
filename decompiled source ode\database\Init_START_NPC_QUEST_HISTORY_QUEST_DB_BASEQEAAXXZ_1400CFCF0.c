/*
 * Function: ?Init@_START_NPC_QUEST_HISTORY@_QUEST_DB_BASE@@QEAAXXZ
 * Address: 0x1400CFCF0
 */

void __fastcall _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY::Init(_QUEST_DB_BASE::_START_NPC_QUEST_HISTORY *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY *Dest; // [sp+30h] [bp+8h]@1

  Dest = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  strcpy_0(Dest->szQuestCode, "*");
  Dest->byLevel = -1;
  Dest->nEndTime = 0i64;
  memset_0(&Dest->tmStartTime, 0, 0x10ui64);
}
