/*
 * Function: ?ct_query_remain_ore@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140297020
 */

char __fastcall ct_query_remain_ore(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  COreAmountMgr *v4; // rax@7
  COreAmountMgr *v5; // rax@16
  unsigned int v6; // eax@16
  __int64 v7; // [sp+0h] [bp-108h]@1
  bool bFilter[8]; // [sp+20h] [bp-E8h]@13
  char Dest; // [sp+50h] [bp-B8h]@7
  char v10; // [sp+51h] [bp-B7h]@7
  const float *v11; // [sp+D8h] [bp-30h]@7
  int j; // [sp+E0h] [bp-28h]@8
  int k; // [sp+E4h] [bp-24h]@10
  unsigned __int64 v14; // [sp+F0h] [bp-18h]@4
  CPlayer *v15; // [sp+110h] [bp+8h]@1

  v15 = pOne;
  v1 = &v7;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v14 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( v15 && v15->m_bOper )
  {
    Dest = 0;
    memset(&v10, 0, 0x7Fui64);
    v4 = COreAmountMgr::Instance();
    v11 = COreAmountMgr::GetMultipleRate(v4);
    if ( v11 )
    {
      for ( j = 0; j < 7; ++j )
      {
        for ( k = 0; k < 3; ++k )
        {
          if ( *v11 != 1.0 )
          {
            *(double *)bFilter = (float)(*v11 * 100.0);
            sprintf(&Dest, "%s_%d : %0.f", dayofweek[j], (unsigned int)(k + 1));
            CPlayer::SendData_ChatTrans(v15, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
          }
          ++v11;
        }
      }
    }
    v5 = COreAmountMgr::Instance();
    v6 = COreAmountMgr::GetRemainOre(v5);
    sprintf(&Dest, "Remain Ore : %d", v6);
    CPlayer::SendData_ChatTrans(v15, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
