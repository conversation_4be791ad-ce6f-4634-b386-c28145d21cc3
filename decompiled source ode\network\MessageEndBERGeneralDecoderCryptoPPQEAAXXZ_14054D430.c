/*
 * Function: ?MessageEnd@BERGeneralDecoder@CryptoPP@@QEAAXXZ
 * Address: 0x14054D430
 */

void __fastcall CryptoPP::BERGeneralDecoder::MessageEnd(CryptoPP::BERGeneralDecoder *this)
{
  CryptoPP *v1; // rcx@5
  unsigned __int16 v2; // [sp+20h] [bp-18h]@5

  this->m_finished = 1;
  if ( this->m_definiteLength )
  {
    if ( this->m_length )
      CryptoPP::BERDecodeError((CryptoPP *)this);
  }
  else if ( CryptoPP::BufferedTransformation::GetWord16(this->m_inQueue, &v2, BIG_ENDIAN_ORDER) != 2 || v2 )
  {
    CryptoPP::BERDecodeError(v1);
  }
}
