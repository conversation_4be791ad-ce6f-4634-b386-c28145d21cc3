/*
 * Function: ?SetPortalInx@CNormalGuildBattleField@GUILD_BATTLE@@AEAA_NPEAPEAU_dummy_position@@AEAPEAHI@Z
 * Address: 0x1403EDD60
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleField::SetPortalInx(GUILD_BATTLE::CNormalGuildBattleField *this, _dummy_position **ppkDummy, int **iPortalInx, unsigned int uiCnt)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  GUILD_BATTLE::CGuildBattleLogger *v7; // rax@7
  GUILD_BATTLE::CGuildBattleLogger *v8; // rax@11
  __int64 v9; // [sp+0h] [bp-48h]@1
  unsigned int j; // [sp+20h] [bp-28h]@8
  void *v11; // [sp+28h] [bp-20h]@6
  __int64 v12; // [sp+30h] [bp-18h]@6
  __int64 v13; // [sp+38h] [bp-10h]@11
  GUILD_BATTLE::CNormalGuildBattleField *v14; // [sp+50h] [bp+8h]@1
  _dummy_position **v15; // [sp+58h] [bp+10h]@1
  void **v16; // [sp+60h] [bp+18h]@1
  unsigned int v17; // [sp+68h] [bp+20h]@1

  v17 = uiCnt;
  v16 = (void **)iPortalInx;
  v15 = ppkDummy;
  v14 = this;
  v4 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v14->m_pkMap )
  {
    v12 = uiCnt;
    v11 = operator new[](saturated_mul(4ui64, uiCnt));
    *v16 = v11;
    if ( *v16 )
    {
      memset_0(*v16, -1, 4i64 * v17);
      for ( j = 0; j < v17; ++j )
      {
        *((_DWORD *)*v16 + (signed int)j) = CMapData::GetPortalInx(v14->m_pkMap, v15[j]->m_szCode);
        if ( *((_DWORD *)*v16 + (signed int)j) < 0 )
        {
          v13 = (signed int)j;
          v8 = GUILD_BATTLE::CGuildBattleLogger::Instance();
          GUILD_BATTLE::CGuildBattleLogger::Log(
            v8,
            "CNormalGuildBattleField::SetPortalInx()[%d] m_pkMap->GetPortalInx( %s ) Fail!",
            j,
            v15[v13]);
          return 0;
        }
      }
      result = 1;
    }
    else
    {
      v7 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      GUILD_BATTLE::CGuildBattleLogger::Log(v7, "CNormalGuildBattleField::SetPortalInx()NULL == new int[%u]", v17);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
