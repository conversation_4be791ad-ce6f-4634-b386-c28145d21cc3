/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAPEAUINI_Key@@PEAPEAU1@V?$allocator@PEAUI<PERSON>_Key@@@std@@@stdext@@YAPEAPEAUINI_Key@@PEAPEAU1@00AEAV?$allocator@PEAUI<PERSON>_Key@@@std@@@Z
 * Address: 0x140008968
 */

INI_Key **__fastcall stdext::_Unchecked_uninitialized_move<INI_Key * *,INI_Key * *,std::allocator<INI_Key *>>(INI_Key **_First, INI_Key **_Last, INI_Key **_Dest, std::allocator<INI_Key *> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<INI_Key * *,INI_Key * *,std::allocator<INI_Key *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
