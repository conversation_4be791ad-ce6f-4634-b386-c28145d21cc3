/*
 * Function: ?dtor$0@?0??AddToRunnables@InternalContextBase@details@Concurrency@@MEAAXVlocation@3@@Z@4HA_5
 * Address: 0x1405AA1C0
 */

void __fastcall `Concurrency::details::InternalContextBase::AddToRunnables'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>(*(_QWORD *)(a2 + 264));
}
