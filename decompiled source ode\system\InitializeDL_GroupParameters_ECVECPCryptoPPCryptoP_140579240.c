/*
 * Function: ?Initialize@?$DL_GroupParameters_EC@VECP@CryptoPP@@@CryptoPP@@QEAAXAEBVOID@2@@Z
 * Address: 0x140579240
 */

int __fastcall CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::Initialize(CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *a1, CryptoPP::OID *a2)
{
  __int64 v2; // rax@1
  __int64 v3; // rax@5
  struct CryptoPP::ECP *v4; // rax@5
  CryptoPP::ECP *v5; // rax@8
  unsigned __int64 v6; // rax@13
  __int64 v8; // [sp+20h] [bp-258h]@1
  CryptoPP::StringSource v9; // [sp+30h] [bp-248h]@8
  __int64 v10; // [sp+A0h] [bp-1D8h]@1
  char v11; // [sp+A8h] [bp-1D0h]@5
  __int64 v12; // [sp+B0h] [bp-1C8h]@1
  bool v13; // [sp+B8h] [bp-1C0h]@8
  CryptoPP::ECPPoint v14; // [sp+C0h] [bp-1B8h]@8
  __int64 v15; // [sp+120h] [bp-158h]@5
  CryptoPP::StringSource v16; // [sp+130h] [bp-148h]@13
  unsigned __int8 v17; // [sp+1A0h] [bp-D8h]@1
  CryptoPP::UnknownOID v18; // [sp+1A8h] [bp-D0h]@3
  struct CryptoPP::BufferedTransformation *v19; // [sp+1F8h] [bp-80h]@8
  CryptoPP::HexDecoder *v20; // [sp+200h] [bp-78h]@5
  struct CryptoPP::BufferedTransformation *v21; // [sp+208h] [bp-70h]@13
  CryptoPP::HexDecoder *v22; // [sp+210h] [bp-68h]@10
  CryptoPP::Integer v23; // [sp+218h] [bp-60h]@13
  __int64 v24; // [sp+240h] [bp-38h]@1
  struct CryptoPP::BufferedTransformation *v25; // [sp+248h] [bp-30h]@6
  unsigned __int64 v26; // [sp+250h] [bp-28h]@8
  struct CryptoPP::BufferedTransformation *v27; // [sp+258h] [bp-20h]@11
  CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *v28; // [sp+280h] [bp+8h]@1
  CryptoPP::OID *__that; // [sp+288h] [bp+10h]@1

  __that = a2;
  v28 = a1;
  v24 = -2i64;
  unknown_libname_1365(&v10, &v12);
  memset(&v17, 0, sizeof(v17));
  LODWORD(v2) = std::lower_bound<CryptoPP::EcRecommendedParameters<CryptoPP::ECP> const *,CryptoPP::OID,CryptoPP::OIDLessThan>(
                  v10,
                  v12,
                  __that,
                  v17);
  v8 = v2;
  if ( v2 == v12 || (unsigned __int8)CryptoPP::operator!=() )
  {
    CryptoPP::UnknownOID::UnknownOID(&v18);
    CxxThrowException_0((__int64)&v18, (__int64)&TI5_AVUnknownOID_CryptoPP__);
  }
  v15 = v8;
  CryptoPP::OID::operator=((CryptoPP::OID *)v28->gapE8, __that);
  LODWORD(v3) = CryptoPP::EcRecommendedParameters<CryptoPP::ECP>::NewEC(v15);
  std::auto_ptr<CryptoPP::ECP>::auto_ptr<CryptoPP::ECP>(&v11, v3);
  LODWORD(v4) = std::auto_ptr<CryptoPP::ECP>::operator*(&v11);
  CryptoPP::EcPrecomputation<CryptoPP::ECP>::SetCurve((__int64)v28->gap18, v4);
  v20 = (CryptoPP::HexDecoder *)operator new(0x68ui64);
  if ( v20 )
    v25 = (struct CryptoPP::BufferedTransformation *)CryptoPP::HexDecoder::HexDecoder(v20, 0i64);
  else
    v25 = 0i64;
  v19 = v25;
  CryptoPP::StringSource::StringSource(&v9, *(const char **)(v15 + 64), 1, v25);
  CryptoPP::ECPPoint::ECPPoint(&v14);
  v26 = CryptoPP::BufferedTransformation::MaxRetrievable((CryptoPP::BufferedTransformation *)&v9.vfptr);
  v5 = CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::GetCurve(v28);
  v13 = CryptoPP::ECP::DecodePoint(v5, &v14, (struct CryptoPP::BufferedTransformation *)&v9.vfptr, v26);
  ((void (__fastcall *)(CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *, CryptoPP::ECPPoint *))v28->vfptr[2].__vecDelDtor)(
    v28,
    &v14);
  if ( !v13 )
    _wassert(L"result", L"d:\\rf project\\rf_server64\\28 crypto++\\eccrypto.cpp", 0x189u);
  v22 = (CryptoPP::HexDecoder *)operator new(0x68ui64);
  if ( v22 )
    v27 = (struct CryptoPP::BufferedTransformation *)CryptoPP::HexDecoder::HexDecoder(v22, 0i64);
  else
    v27 = 0i64;
  v21 = v27;
  CryptoPP::StringSource::StringSource(&v16, *(const char **)(v15 + 72), 1, v27);
  v6 = CryptoPP::BufferedTransformation::MaxRetrievable((CryptoPP::BufferedTransformation *)&v16.vfptr);
  CryptoPP::Integer::Decode(&v28->m_n, (struct CryptoPP::BufferedTransformation *)&v16.vfptr, v6, 0);
  CryptoPP::Integer::Integer(&v23, *(_DWORD *)(v15 + 80));
  CryptoPP::Integer::operator=(&v28->m_k);
  CryptoPP::Integer::~Integer(&v23);
  CryptoPP::StringSource::~StringSource(&v16);
  CryptoPP::ECPPoint::~ECPPoint(&v14);
  CryptoPP::StringSource::~StringSource(&v9);
  return std::auto_ptr<CryptoPP::ECP>::~auto_ptr<CryptoPP::ECP>(&v11);
}
