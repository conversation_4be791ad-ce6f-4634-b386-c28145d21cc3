/*
 * Function: ??1CPlayerDB@@QEAA@XZ
 * Address: 0x140108AA0
 */

void __fastcall CPlayerDB::~CPlayerDB(CPlayerDB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  void *v4; // [sp+20h] [bp-18h]@5
  __int64 v5; // [sp+28h] [bp-10h]@4
  CPlayerDB *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = -2i64;
  if ( v6->m_wCuttingResBuffer )
  {
    v4 = v6->m_wCuttingResBuffer;
    operator delete[](v4);
  }
  CPostReturnStorage::~CPostReturnStorage(&v6->m_ReturnPostStorage);
  CPostStorage::~CPostStorage(&v6->m_PostStorage);
}
