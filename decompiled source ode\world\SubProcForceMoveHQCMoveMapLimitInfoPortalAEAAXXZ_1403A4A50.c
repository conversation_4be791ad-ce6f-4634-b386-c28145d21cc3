/*
 * Function: ?SubProcForceMoveHQ@CMoveMapLimitInfoPortal@@AEAAXXZ
 * Address: 0x1403A4A50
 */

void __fastcall CMoveMapLimitInfoPortal::SubProcForceMoveHQ(CMoveMapLimitInfoPortal *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char v3; // al@10
  __int64 v4; // [sp+0h] [bp-B8h]@1
  int v5; // [sp+30h] [bp-88h]@4
  CMapData *pIntoMap; // [sp+38h] [bp-80h]@4
  float pfoutPos; // [sp+48h] [bp-70h]@4
  char v8; // [sp+4Ch] [bp-6Ch]@4
  char pbyType; // [sp+74h] [bp-44h]@4
  char v10; // [sp+75h] [bp-43h]@4
  _notice_move_limit_map_msg_zocl v11; // [sp+94h] [bp-24h]@4
  unsigned int j; // [sp+A4h] [bp-14h]@4
  CMoveMapLimitInfoPortal *v13; // [sp+C0h] [bp+8h]@1

  v13 = this;
  v1 = &v4;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = 0;
  pIntoMap = 0i64;
  pfoutPos = 0.0;
  memset(&v8, 0, 8ui64);
  pbyType = 59;
  v10 = 1;
  _notice_move_limit_map_msg_zocl::_notice_move_limit_map_msg_zocl(&v11);
  v11.byType = 1;
  for ( j = v13->m_uiProcNotifyInx; j < 0x9E4; ++j )
  {
    if ( *(&g_Player.m_bLive + 50856 * j) )
    {
      if ( v13->m_iMapInx == *(_DWORD *)(*((_QWORD *)&g_Player.m_pCurMap + 6357 * j) + 584i64) )
      {
        v3 = CPlayerDB::GetRaceCode((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * j));
        pIntoMap = CMapOperation::GetPosStartMap(&g_MapOper, v3, 0, &pfoutPos);
        if ( pIntoMap )
        {
          CPlayer::pc_Resurrect(&g_Player + j, 0);
          CPlayer::ForcePullUnit(&g_Player + j, 0);
          CPlayer::OutOfMap(&g_Player + j, pIntoMap, 0, 3, &pfoutPos);
          CPlayer::SendMsg_GotoBasePortalResult(&g_Player + j, 0);
          CNetProcess::LoadSendMsg(unk_1414F2088, *(&g_Player.m_ObjID.m_wIndex + 25428 * j), &pbyType, &v11.byType, 1u);
          if ( (unsigned int)++v5 >= 0x64 )
          {
            v13->m_uiProcNotifyInx = j + 1;
            break;
          }
        }
      }
    }
  }
  if ( j >= 0x9E4 || v13->m_uiProcNotifyInx >= 0x9E4 )
  {
    v13->m_uiProcNotifyInx = 0;
    CMyTimer::StopTimer(v13->m_pkNotifyForceMoveHQTimer);
    v13->m_eNotifyForceMoveHQState = 0;
  }
}
