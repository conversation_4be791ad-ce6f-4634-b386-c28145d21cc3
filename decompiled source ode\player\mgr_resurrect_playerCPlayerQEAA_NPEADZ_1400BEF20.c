/*
 * Function: ?mgr_resurrect_player@CPlayer@@QEAA_NPEAD@Z
 * Address: 0x1400BEF20
 */

bool __fastcall CPlayer::mgr_resurrect_player(CPlayer *this, char *pwszCharName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  __int64 v5; // [sp+0h] [bp-38h]@1
  CUserDB *v6; // [sp+20h] [bp-18h]@4
  CPlayer *v7; // [sp+28h] [bp-10h]@5

  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = SearchAvatorWithName(g_UserDB, 2532, pwszCharName);
  if ( v6 )
  {
    v7 = &g_Player + v6->m_idWorld.wIndex;
    if ( v7->m_bLive )
      result = CPlayer::pc_Resurrect(v7, 0);
    else
      result = 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
