/*
 * Function: ?SelectGuildBattleRankList@CRFWorldDatabase@@QEAA_NEPEAU_worlddb_guild_battle_rank_list@@@Z
 * Address: 0x1404A2B70
 */

char __fastcall CRFWorldDatabase::SelectGuildBattleRankList(CRFWorldDatabase *this, char byR<PERSON>, _worlddb_guild_battle_rank_list *pkInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned int *v6; // rax@19
  char *v7; // rax@19
  char *v8; // rax@19
  unsigned int *v9; // rax@19
  unsigned int *v10; // rax@19
  unsigned int *v11; // rax@19
  unsigned int *v12; // rax@19
  __int64 v13; // [sp+0h] [bp-498h]@1
  void *SQLStmt; // [sp+20h] [bp-478h]@15
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-470h]@19
  char Dest; // [sp+40h] [bp-458h]@6
  SQLLEN v17; // [sp+458h] [bp-40h]@19
  __int16 v18; // [sp+464h] [bp-34h]@11
  int v19; // [sp+468h] [bp-30h]@6
  int v20; // [sp+46Ch] [bp-2Ch]@6
  unsigned __int16 v21; // [sp+470h] [bp-28h]@16
  unsigned __int16 j; // [sp+474h] [bp-24h]@16
  unsigned __int64 v23; // [sp+480h] [bp-18h]@4
  CRFWorldDatabase *v24; // [sp+4A0h] [bp+8h]@1
  _worlddb_guild_battle_rank_list *v25; // [sp+4B0h] [bp+18h]@1

  v25 = pkInfo;
  v24 = this;
  v3 = &v13;
  for ( i = 292i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v23 = (unsigned __int64)&v13 ^ _security_cookie;
  if ( pkInfo )
  {
    v19 = 0;
    v20 = 0;
    sprintf(&Dest, "{ CALL pSelect_GuildBattleRankList(%u) }", (unsigned __int8)byRace);
    if ( v24->m_bSaveDBLog )
      CRFNewDatabase::Log((CRFNewDatabase *)&v24->vfptr, &Dest);
    if ( v24->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v24->vfptr) )
    {
      v18 = SQLExecDirect_0(v24->m_hStmtSelect, &Dest, -3);
      if ( v18 && v18 != 1 )
      {
        if ( v18 == 100 )
        {
          result = 1;
        }
        else
        {
          SQLStmt = v24->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v24->vfptr, v18, &Dest, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v24->vfptr, v18, v24->m_hStmtSelect);
          result = 0;
        }
      }
      else
      {
        v25->wCount = 0;
        v21 = 0;
        for ( j = 1; ; v25->list[v25->wCount++].nRank = j )
        {
          v18 = SQLFetch_0(v24->m_hStmtSelect);
          if ( v18 )
          {
            if ( v18 != 1 )
              break;
          }
          v6 = &v25->list[v25->wCount].dwSerial;
          StrLen_or_IndPtr = &v17;
          SQLStmt = 0i64;
          v18 = SQLGetData_0(v24->m_hStmtSelect, 1u, -18, v6, 0i64, &v17);
          v7 = &v25->list[v25->wCount].byGrade;
          StrLen_or_IndPtr = &v17;
          SQLStmt = 0i64;
          v18 = SQLGetData_0(v24->m_hStmtSelect, 2u, -6, v7, 0i64, &v17);
          v8 = v25->list[v25->wCount].wszName;
          StrLen_or_IndPtr = &v17;
          SQLStmt = (void *)17;
          v18 = SQLGetData_0(v24->m_hStmtSelect, 3u, 1, v8, 17i64, &v17);
          v9 = &v25->list[v25->wCount].dwWin;
          StrLen_or_IndPtr = &v17;
          SQLStmt = 0i64;
          v18 = SQLGetData_0(v24->m_hStmtSelect, 4u, -18, v9, 0i64, &v17);
          v10 = &v25->list[v25->wCount].dwDraw;
          StrLen_or_IndPtr = &v17;
          SQLStmt = 0i64;
          v18 = SQLGetData_0(v24->m_hStmtSelect, 5u, -18, v10, 0i64, &v17);
          v11 = &v25->list[v25->wCount].dwLose;
          StrLen_or_IndPtr = &v17;
          SQLStmt = 0i64;
          v18 = SQLGetData_0(v24->m_hStmtSelect, 6u, -18, v11, 0i64, &v17);
          v12 = &v25->list[v25->wCount].dwScore;
          StrLen_or_IndPtr = &v17;
          SQLStmt = 0i64;
          v18 = SQLGetData_0(v24->m_hStmtSelect, 7u, -18, v12, 0i64, &v17);
          if ( (signed int)v25->wCount > 0 )
          {
            v21 = v25->wCount - 1;
            if ( v25->list[v21].dwScore != v25->list[v25->wCount].dwScore
              || v25->list[v21].dwWin != v25->list[v25->wCount].dwWin
              || v25->list[v21].dwDraw != v25->list[v25->wCount].dwDraw
              || v25->list[v21].dwLose != v25->list[v25->wCount].dwLose )
            {
              j = v25->wCount + 1;
            }
          }
        }
        if ( v24->m_hStmtSelect )
          SQLCloseCursor_0(v24->m_hStmtSelect);
        if ( v24->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v24->vfptr, "%s Success", &Dest);
        result = 1;
      }
    }
    else
    {
      CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v24->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
