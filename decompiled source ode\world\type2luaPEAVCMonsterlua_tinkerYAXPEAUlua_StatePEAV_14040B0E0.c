/*
 * Function: ??$type2lua@PEAVCMonster@@@lua_tinker@@YAXPEAUlua_State@@PEAVCMonster@@@Z
 * Address: 0x14040B0E0
 */

void __fastcall lua_tinker::type2lua<CMonster *>(struct lua_State *L, CMonster *val, CMonster *a3)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  lua_tinker::object2lua<CMonster *> *v6; // [sp+30h] [bp+8h]@1

  v6 = (lua_tinker::object2lua<CMonster *> *)L;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  lua_tinker::object2lua<CMonster *>::invoke(v6, (struct lua_State *)val, a3);
}
