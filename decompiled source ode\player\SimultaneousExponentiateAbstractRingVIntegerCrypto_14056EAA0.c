/*
 * Function: ?SimultaneousExponentiate@?$AbstractRing@VInteger@CryptoPP@@@CryptoPP@@UEBAXPEAVInteger@2@AEBV32@PEBV32@I@Z
 * Address: 0x14056EAA0
 */

int __fastcall CryptoPP::AbstractRing<CryptoPP::Integer>::SimultaneousExponentiate(__int64 a1, __int64 a2, struct CryptoPP::Integer *a3, CryptoPP::Integer *a4, unsigned int a5)
{
  __int64 v5; // rax@1
  __int64 v7; // [sp+48h] [bp+10h]@1
  struct CryptoPP::Integer *v8; // [sp+50h] [bp+18h]@1
  CryptoPP::Integer *v9; // [sp+58h] [bp+20h]@1

  v9 = a4;
  v8 = a3;
  v7 = a2;
  LODWORD(v5) = (*(int (**)(void))(*(_QWORD *)a1 + 176i64))();
  return CryptoPP::AbstractGroup<CryptoPP::Integer>::SimultaneousMultiply(v5, v7, v8, v9, a5);
}
