/*
 * Function: ?_Init@_candidate_info@@QEAAXXZ
 * Address: 0x1402B62B0
 */

void __fastcall _candidate_info::_Init(_candidate_info *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _candidate_info *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->bLoad = 0;
  v4->bUpdateClassType = 0;
  v4->eStatus = 0;
  v4->byRace = -1;
  v4->byLevel = -1;
  v4->dwRank = -1;
  *(_QWORD *)&v4->dPvpPoint = 0i64;
  v4->dwGuildSerial = -1;
  v4->dwAvatorSerial = -1;
  memset_0(v4->wszGuildName, 0, 0x11ui64);
  memset_0(v4->wszName, 0, 0x11ui64);
  v4->dwScore = 0;
  v4->dwWinCnt = 0;
  v4->eClassType = 255;
  v4->bValidChar = 1;
}
