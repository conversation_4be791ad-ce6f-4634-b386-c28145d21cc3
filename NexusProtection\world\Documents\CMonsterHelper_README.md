# CMonsterHelper Class Documentation

## Overview

The `CMonsterHelper` class is a utility class that provides static helper functions for monster-related operations in the NexusProtection game engine. This class was refactored from decompiled C source files to modern C++17/C++20 standards compatible with Visual Studio 2022.

## Features

### Core Functionality
- **Angle Calculations**: Compute angles between monster and target positions
- **Direction Computations**: Calculate movement directions with distance and angle parameters
- **Sector Checks**: Determine if positions are within specified sectors (angle and radius)
- **Monster Searching**: Find nearby monsters within specified criteria
- **Transportation**: Handle monster position changes and teleportation
- **Hierarchy Management**: Manage parent-child relationships between monsters

### Design Principles
- **Static Utility Class**: All methods are static, no instantiation required
- **Thread-Safe**: Read-only operations are thread-safe
- **Error Handling**: Comprehensive parameter validation and exception handling
- **Modern C++**: Uses C++17/C++20 features and STL containers
- **Logging**: Built-in operation and error logging

## Class Structure

### Public Methods

#### `GetAngle(float* mon, float* plr)`
Calculates the angle between two 3D positions.
- **Parameters**: 
  - `mon`: Monster position (3D coordinates)
  - `plr`: Player/target position (3D coordinates)
- **Returns**: Angle in radians between the positions
- **Usage**: Used for monster AI targeting and orientation

#### `GetDirection(float (*cur)[3], float (*tar)[3], float (*out)[3], float deg, float distance)`
Calculates a direction vector from current to target position with angle and distance modifiers.
- **Parameters**:
  - `cur`: Current position (3D coordinates)
  - `tar`: Target position (3D coordinates)
  - `out`: Output direction vector (3D coordinates)
  - `deg`: Degree offset for calculation
  - `distance`: Distance scaling factor
- **Usage**: Used for monster movement and pathfinding

#### `IsInSector(float* chkpos, float* src, float* dest, float angle, float radius, float* pfDist)`
Checks if a position is within a sector defined by angle and radius.
- **Parameters**:
  - `chkpos`: Position to check (3D coordinates)
  - `src`: Source position (3D coordinates)
  - `dest`: Destination position (3D coordinates)
  - `angle`: Sector angle in degrees
  - `radius`: Maximum distance from source
  - `pfDist`: Optional output parameter for calculated distance
- **Returns**: `true` if position is within the sector
- **Usage**: Used for monster vision and attack range checks

#### `SearchNearMonster(CMonster* pMon, _NEAR_DATA* NearChar, uint32_t dwArSize, int bTargetIgnore)`
Searches for nearby monsters within specified parameters.
- **Parameters**:
  - `pMon`: Source monster for the search
  - `NearChar`: Array to store found nearby monsters
  - `dwArSize`: Size of the NearChar array
  - `bTargetIgnore`: Flag to ignore certain targets
- **Returns**: Number of monsters found
- **Usage**: Used for monster AI awareness and group behavior

#### `SearchNearMonsterByDistance(CMonster* pMon, uint32_t dwDist)`
Finds the nearest monster within a specified distance.
- **Parameters**:
  - `pMon`: Source monster for the search
  - `dwDist`: Maximum search distance
- **Returns**: Pointer to the nearest monster found, or `nullptr`
- **Usage**: Used for monster targeting and assistance

#### `TransPort(CMonster* mon, float* tarPos)`
Transports a monster to a new position.
- **Parameters**:
  - `mon`: Monster to transport
  - `tarPos`: Target position (3D coordinates)
- **Usage**: Used for monster teleportation and repositioning

#### `HierarcyHelpCast(CMonster* pMon)`
Performs hierarchy help cast operations for monster relationships.
- **Parameters**:
  - `pMon`: Monster to perform hierarchy operation on
- **Usage**: Used for parent-child monster assistance and group behavior

### Private Utility Methods

#### `Normalize(float* vec)`
Normalizes a 3D vector to unit length.

#### `CalculateDistance(const float* pos1, const float* pos2)`
Calculates the Euclidean distance between two 3D points.

#### `DotProduct(const float* vec1, const float* vec2)`
Calculates the dot product of two 3D vectors.

#### `ValidatePosition(const float* pos)`
Validates that a position contains finite, valid values.

#### `ValidateMonster(const CMonster* pMon)`
Validates that a monster pointer is valid and usable.

## CMonsterHelperUtils Namespace

Provides additional utility functions for the CMonsterHelper class:

- `LogOperation(const char* operation, const char* details)`: Logs operations
- `LogError(const char* errorMessage, const char* context)`: Logs errors
- `ValidateAngle(float angle)`: Validates angle values
- `ValidateDistance(float distance)`: Validates distance values
- `DegreesToRadians(float degrees)`: Converts degrees to radians
- `RadiansToDegrees(float radians)`: Converts radians to degrees

## Constants

- `PI`: Mathematical constant π
- `TWO_PI`: 2π for full circle calculations
- `RAD_TO_DEG`: Conversion factor from radians to degrees
- `DEG_TO_RAD`: Conversion factor from degrees to radians
- `EPSILON`: Small value for floating-point comparisons

## Usage Examples

```cpp
// Calculate angle between monster and player
float angle = CMonsterHelper::GetAngle(monsterPos, playerPos);

// Check if player is in monster's vision sector
bool inVision = CMonsterHelper::IsInSector(
    playerPos, monsterPos, targetPos, 
    90.0f,    // 90-degree vision cone
    100.0f    // 100 unit vision range
);

// Find nearest monster within 50 units
CMonster* nearestMonster = CMonsterHelper::SearchNearMonsterByDistance(
    currentMonster, 50
);

// Transport monster to new location
float newPos[3] = {100.0f, 0.0f, 200.0f};
CMonsterHelper::TransPort(monster, newPos);
```

## Error Handling

The class includes comprehensive error handling:
- Parameter validation for all public methods
- Exception handling with logging
- Graceful degradation for invalid inputs
- Detailed error messages with context information

## Thread Safety

- All static methods are thread-safe for read-only operations
- No shared mutable state within the class
- External dependencies (like monster objects) may require synchronization

## Dependencies

- Standard C++ libraries: `<cmath>`, `<algorithm>`, `<iostream>`
- Game engine headers: `CMonster.h`
- Platform: Visual Studio 2022, C++17/C++20 standard

## Refactoring Notes

This class was refactored from the following decompiled source files:
- `GetAngleCMonsterHelperSAMQEAM0Z_1401597A0.c`
- `GetDirectionCMonsterHelperSAXAEAY02M00MZ_1401598E0.c`
- `IsInSectorCMonsterHelperSAHQEAM00MMPEAMZ_140158160.c`
- `SearchNearMonsterCMonsterHelperSAKPEAVCMonsterPEAU_140158DE0.c`
- `SearchNearMonsterByDistanceCMonsterHelperSAPEAVCMo_140159540.c`
- `TransPortCMonsterHelperSAXPEAVCMonsterQEAMZ_14015A310.c`
- `HierarcyHelpCastCMonsterHelperSAXPEAVCMonsterZ_14015A480.c`

The refactoring process involved:
1. Converting C-style functions to C++ static methods
2. Adding proper parameter validation and error handling
3. Implementing modern C++ memory management
4. Adding comprehensive documentation
5. Creating a clean, maintainable class structure
