/*
 * Function: ?trunk_swap_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@0KKPEAD@Z
 * Address: 0x14023CF40
 */

void __fastcall CMgrAvatorItemHistory::trunk_swap_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pInputItem, _STORAGE_LIST::_db_con *pOutputItem, unsigned int dwFeeDalant, unsigned int dwNewDalant, char *pszFileName)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char *v9; // rax@4
  __int64 v10; // [sp+0h] [bp-B8h]@1
  char *v11; // [sp+20h] [bp-98h]@4
  unsigned __int64 v12; // [sp+28h] [bp-90h]@4
  char *v13; // [sp+30h] [bp-88h]@4
  unsigned __int64 v14; // [sp+38h] [bp-80h]@4
  char *v15; // [sp+40h] [bp-78h]@4
  unsigned __int64 v16; // [sp+48h] [bp-70h]@4
  unsigned int v17; // [sp+50h] [bp-68h]@4
  unsigned int v18; // [sp+58h] [bp-60h]@4
  char *v19; // [sp+60h] [bp-58h]@4
  char *v20; // [sp+68h] [bp-50h]@4
  _base_fld *v21; // [sp+70h] [bp-48h]@4
  _base_fld *v22; // [sp+78h] [bp-40h]@4
  char *v23; // [sp+80h] [bp-38h]@4
  char *v24; // [sp+88h] [bp-30h]@4
  int nTableCode; // [sp+90h] [bp-28h]@4
  char *v26; // [sp+98h] [bp-20h]@4
  char *v27; // [sp+A0h] [bp-18h]@4
  int v28; // [sp+A8h] [bp-10h]@4
  CMgrAvatorItemHistory *v29; // [sp+C0h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v30; // [sp+D0h] [bp+18h]@1
  _STORAGE_LIST::_db_con *v31; // [sp+D8h] [bp+20h]@1

  v31 = pOutputItem;
  v30 = pInputItem;
  v29 = this;
  v7 = &v10;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v21 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pInputItem->m_byTableCode, pInputItem->m_wItemIndex);
  v22 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v31->m_byTableCode, v31->m_wItemIndex);
  v23 = v29->m_szCurTime;
  v24 = v29->m_szCurDate;
  nTableCode = v31->m_byTableCode;
  v26 = DisplayItemUpgInfo(nTableCode, v31->m_dwLv);
  v27 = v22->m_strCode;
  v28 = v30->m_byTableCode;
  v9 = DisplayItemUpgInfo(v28, v30->m_dwLv);
  v20 = v23;
  v19 = v24;
  v18 = dwNewDalant;
  v17 = dwFeeDalant;
  v16 = v31->m_lnUID;
  v15 = v26;
  v14 = v31->m_dwDur;
  v13 = v27;
  v12 = v30->m_lnUID;
  v11 = v9;
  sprintf(
    sData,
    "TRUNK ITEM SWAP: IN> %s_%u_@%s[%I64u] OUT> %s_%d_@%s[%I64u], pay(%u) $D:%u [%s %s]\r\n",
    v21->m_strCode,
    v30->m_dwDur);
  CMgrAvatorItemHistory::WriteFile(v29, pszFileName, sData);
}
