/*
 * Function: ?Select_PostStorageList@CRFWorldDatabase@@QEAAEKPEAU_post_storage_list@@@Z
 * Address: 0x1404B3490
 */

char __fastcall CRFWorldDatabase::Select_PostStorageList(CRFWorldDatabase *this, unsigned int dwOwner, _post_storage_list *pListData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  char *v6; // rax@16
  _post_storage_list::__list *v7; // rax@16
  char *v8; // rax@16
  char *v9; // rax@16
  char *v10; // rax@16
  int *v11; // rax@16
  unsigned __int64 *v12; // rax@16
  unsigned int *v13; // rax@16
  unsigned int *v14; // rax@16
  unsigned __int64 *v15; // rax@16
  unsigned __int16 *v16; // rax@16
  __int64 v17; // [sp+0h] [bp-288h]@1
  void *SQLStmt; // [sp+20h] [bp-268h]@4
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-260h]@4
  SQLLEN v20; // [sp+38h] [bp-250h]@16
  __int16 v21; // [sp+44h] [bp-244h]@9
  char DstBuf; // [sp+60h] [bp-228h]@4
  char v23; // [sp+61h] [bp-227h]@4
  unsigned __int64 v24; // [sp+270h] [bp-18h]@4
  CRFWorldDatabase *v25; // [sp+290h] [bp+8h]@1
  _post_storage_list *v26; // [sp+2A0h] [bp+18h]@1

  v26 = pListData;
  v25 = this;
  v3 = &v17;
  for ( i = 160i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v24 = (unsigned __int64)&v17 ^ _security_cookie;
  DstBuf = 0;
  memset(&v23, 0, 0x1FFui64);
  pListData->dwCount = 0;
  LODWORD(StrLen_or_IndPtr) = 100;
  LODWORD(SQLStmt) = dwOwner;
  sprintf_s(
    &DstBuf,
    0x200ui64,
    "select top %d postinx,serial,poststate,sendname,title,k,d,u,gold,uid,sindex from tbl_PostStorage where owner=%d and "
    "poststate<%d and dck=0",
    50i64);
  if ( v25->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v25->vfptr, &DstBuf);
  if ( v25->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v25->vfptr) )
  {
    v21 = SQLExecDirectA_0(v25->m_hStmtSelect, &DstBuf, -3);
    if ( v21 && v21 != 1 )
    {
      if ( v21 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v25->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v25->vfptr, v21, &DstBuf, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v25->vfptr, v21, v25->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      while ( 1 )
      {
        v21 = SQLFetch_0(v25->m_hStmtSelect);
        if ( v21 )
        {
          if ( v21 != 1 )
            break;
        }
        v6 = &v26->List[v26->dwCount].byIndex;
        StrLen_or_IndPtr = &v20;
        SQLStmt = 0i64;
        v21 = SQLGetData_0(v25->m_hStmtSelect, 1u, -6, v6, 0i64, &v20);
        v7 = &v26->List[v26->dwCount];
        StrLen_or_IndPtr = &v20;
        SQLStmt = 0i64;
        v21 = SQLGetData_0(v25->m_hStmtSelect, 2u, -18, v7, 0i64, &v20);
        v8 = &v26->List[v26->dwCount].byState;
        StrLen_or_IndPtr = &v20;
        SQLStmt = 0i64;
        v21 = SQLGetData_0(v25->m_hStmtSelect, 3u, -6, v8, 0i64, &v20);
        v9 = v26->List[v26->dwCount].wszSendName;
        StrLen_or_IndPtr = &v20;
        SQLStmt = (void *)17;
        v21 = SQLGetData_0(v25->m_hStmtSelect, 4u, 1, v9, 17i64, &v20);
        v10 = v26->List[v26->dwCount].wszTitle;
        StrLen_or_IndPtr = &v20;
        SQLStmt = (void *)21;
        v21 = SQLGetData_0(v25->m_hStmtSelect, 5u, 1, v10, 21i64, &v20);
        v11 = &v26->List[v26->dwCount].nK;
        StrLen_or_IndPtr = &v20;
        SQLStmt = 0i64;
        v21 = SQLGetData_0(v25->m_hStmtSelect, 6u, 4, v11, 0i64, &v20);
        v12 = &v26->List[v26->dwCount].dwDur;
        StrLen_or_IndPtr = &v20;
        SQLStmt = 0i64;
        v21 = SQLGetData_0(v25->m_hStmtSelect, 7u, -25, v12, 0i64, &v20);
        v13 = &v26->List[v26->dwCount].dwUpt;
        StrLen_or_IndPtr = &v20;
        SQLStmt = 0i64;
        v21 = SQLGetData_0(v25->m_hStmtSelect, 8u, 4, v13, 0i64, &v20);
        v14 = &v26->List[v26->dwCount].dwGold;
        StrLen_or_IndPtr = &v20;
        SQLStmt = 0i64;
        v21 = SQLGetData_0(v25->m_hStmtSelect, 9u, -18, v14, 0i64, &v20);
        v15 = &v26->List[v26->dwCount].lnUID;
        StrLen_or_IndPtr = &v20;
        SQLStmt = 0i64;
        v21 = SQLGetData_0(v25->m_hStmtSelect, 0xAu, -25, v15, 0i64, &v20);
        v16 = &v26->List[v26->dwCount].wStorageIndex;
        StrLen_or_IndPtr = &v20;
        SQLStmt = 0i64;
        v21 = SQLGetData_0(v25->m_hStmtSelect, 0xBu, 5, v16, 0i64, &v20);
        ++v26->dwCount;
      }
      if ( v25->m_hStmtSelect )
        SQLCloseCursor_0(v25->m_hStmtSelect);
      if ( v25->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v25->vfptr, "%s Success", &DstBuf);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v25->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1;
  }
  return result;
}
