/*
 * Function: ?Destroy@CHolyStone@@QEAA_NEPEAVCCharacter@@@Z
 * Address: 0x140137140
 */

bool __fastcall CHolyStone::Destroy(CHolyStone *this, char byDestroyCode, CCharacter *pAtter)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@14
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int dwDestroySerial; // [sp+20h] [bp-18h]@4
  CPlayer *pAttera; // [sp+28h] [bp-10h]@6
  CHolyStone *v9; // [sp+40h] [bp+8h]@1
  char v10; // [sp+48h] [bp+10h]@1
  CCharacter *v11; // [sp+50h] [bp+18h]@1

  v11 = pAtter;
  v10 = byDestroyCode;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9->m_dwLastDestroyTime = timeGetTime();
  dwDestroySerial = -1;
  if ( v10 || !v11 )
  {
    --CHolyStone::s_nLiveNum;
    result = CCharacter::Destroy((CCharacter *)&v9->vfptr);
  }
  else
  {
    pAttera = 0i64;
    if ( !v11->m_ObjID.m_byID )
      pAttera = (CPlayer *)v11;
    if ( v11->m_ObjID.m_byID == 3 && *(_QWORD *)&v11[1].m_bLive )
      pAttera = *(CPlayer **)&v11[1].m_bLive;
    CHolyStone::SetDropItem(v9);
    CHolyStone::DropItem(v9);
    if ( pAttera )
    {
      if ( CHolyStoneSystem::CheckHolyMaster(&g_HolySys, pAttera, v9->m_byMasterRaceCode) )
        dwDestroySerial = pAttera->m_dwObjSerial;
    }
    v9->m_bOper = 0;
    CHolyStone::SendMsg_Destroy(v9, 0, dwDestroySerial);
    result = 1;
  }
  return result;
}
