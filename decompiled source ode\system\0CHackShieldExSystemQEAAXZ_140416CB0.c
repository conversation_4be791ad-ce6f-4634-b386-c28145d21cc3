/*
 * Function: ??0CHackShieldExSystem@@QEAA@XZ
 * Address: 0x140416CB0
 */

void __fastcall CHackShieldExSystem::CHackShieldExSystem(CHackShieldExSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CHackShieldExSystem *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  INationGameGuardSystem::INationGameGuardSystem((INationGameGuardSystem *)&v5->vfptr);
  v5->vfptr = (INationGameGuardSystemVtbl *)&CHackShieldExSystem::`vftable';
  v5->m_ppNodeArray = 0i64;
  v5->m_bInit = 0;
  v5->m_bActive = 0;
  CMyTimer::CMyTimer(&v5->m_tmLoopTime);
  v5->m_dwCurrentCheckIndex = 0;
}
