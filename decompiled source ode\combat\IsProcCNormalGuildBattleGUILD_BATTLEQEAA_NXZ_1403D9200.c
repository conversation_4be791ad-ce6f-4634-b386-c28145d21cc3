/*
 * Function: ?Is<PERSON>roc@CNormalGuildBattle@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403D9200
 */

bool __fastcall GUILD_BATTLE::CNormalGuildBattle::IsProc(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattle *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return GUILD_BATTLE::CGuildBattleStateList::IsProc((GUILD_BATTLE::CGuildBattleStateList *)&v5->m_pkStateList->vfptr);
}
