/*
 * Function: ?IsTimeItem@@YAHEK@Z
 * Address: 0x1400409C0
 */

__int64 __fastcall IsTimeItem(char byTblCode, unsigned int dwIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@6
  __int64 v5; // [sp+0h] [bp-F8h]@1
  CRecordData *v6; // [sp+20h] [bp-D8h]@4
  _base_fld *v7; // [sp+28h] [bp-D0h]@5
  _base_fld *v8; // [sp+30h] [bp-C8h]@8
  _base_fld *v9; // [sp+38h] [bp-C0h]@11
  _base_fld *v10; // [sp+40h] [bp-B8h]@14
  _base_fld *v11; // [sp+48h] [bp-B0h]@17
  _base_fld *v12; // [sp+50h] [bp-A8h]@20
  _base_fld *v13; // [sp+58h] [bp-A0h]@23
  _base_fld *v14; // [sp+60h] [bp-98h]@26
  _base_fld *v15; // [sp+68h] [bp-90h]@29
  _base_fld *v16; // [sp+70h] [bp-88h]@32
  _base_fld *v17; // [sp+78h] [bp-80h]@35
  _base_fld *v18; // [sp+80h] [bp-78h]@38
  _base_fld *v19; // [sp+88h] [bp-70h]@41
  _base_fld *v20; // [sp+90h] [bp-68h]@44
  _base_fld *v21; // [sp+98h] [bp-60h]@47
  _base_fld *v22; // [sp+A0h] [bp-58h]@50
  _base_fld *v23; // [sp+A8h] [bp-50h]@53
  _base_fld *v24; // [sp+B0h] [bp-48h]@56
  _base_fld *v25; // [sp+B8h] [bp-40h]@59
  _base_fld *v26; // [sp+C0h] [bp-38h]@62
  _base_fld *v27; // [sp+C8h] [bp-30h]@65
  _base_fld *v28; // [sp+D0h] [bp-28h]@68
  _base_fld *v29; // [sp+D8h] [bp-20h]@71
  _base_fld *v30; // [sp+E0h] [bp-18h]@74
  int v31; // [sp+E8h] [bp-10h]@4
  char v32; // [sp+100h] [bp+8h]@1

  v32 = byTblCode;
  v2 = &v5;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = &s_ptblItemData[(unsigned __int8)v32];
  v31 = (unsigned __int8)v32;
  switch ( v32 )
  {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      v7 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v7 )
        goto LABEL_76;
      result = *(_DWORD *)&v7[7].m_strCode[56];
      break;
    case 6:
      v8 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v8 )
        goto LABEL_76;
      result = *(_DWORD *)&v8[12].m_strCode[20];
      break;
    case 7:
      v9 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v9 )
        goto LABEL_76;
      result = *(_DWORD *)&v9[7].m_strCode[56];
      break;
    case 8:
      v10 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v10 )
        goto LABEL_76;
      result = *(_DWORD *)&v10[6].m_strCode[52];
      break;
    case 9:
      v11 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v11 )
        goto LABEL_76;
      result = *(_DWORD *)&v11[6].m_strCode[52];
      break;
    case 0xA:
      v12 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v12 )
        goto LABEL_76;
      result = *(_DWORD *)&v12[9].m_strCode[20];
      break;
    case 0xB:
      v13 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v13 )
        goto LABEL_76;
      result = *(_DWORD *)&v13[6].m_strCode[8];
      break;
    case 0xC:
      v14 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v14 )
        goto LABEL_76;
      result = *(_DWORD *)&v14[5].m_strCode[44];
      break;
    case 0xD:
      v15 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v15 )
        goto LABEL_76;
      result = *(_DWORD *)&v15[9].m_strCode[44];
      break;
    case 0x10:
      v16 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v16 )
        goto LABEL_76;
      result = *(_DWORD *)&v16[5].m_strCode[52];
      break;
    case 0x11:
      v17 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v17 )
        goto LABEL_76;
      result = *(_DWORD *)&v17[5].m_strCode[4];
      break;
    case 0x12:
      v18 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v18 )
        goto LABEL_76;
      result = *(_DWORD *)&v18[8].m_strCode[16];
      break;
    case 0x14:
      v19 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v19 )
        goto LABEL_76;
      result = *(_DWORD *)&v19[5].m_strCode[48];
      break;
    case 0x15:
      v20 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v20 )
        goto LABEL_76;
      result = *(_DWORD *)&v20[6].m_strCode[40];
      break;
    case 0x16:
      v21 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v21 )
        goto LABEL_76;
      result = *(_DWORD *)&v21[7].m_strCode[52];
      break;
    case 0x17:
      v22 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v22 )
        goto LABEL_76;
      result = *(_DWORD *)&v22[10].m_strCode[0];
      break;
    case 0x19:
      v23 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v23 )
        goto LABEL_76;
      result = *(_DWORD *)&v23[11].m_strCode[32];
      break;
    case 0x1A:
      v24 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v24 )
        goto LABEL_76;
      result = *(_DWORD *)&v24[9].m_strCode[40];
      break;
    case 0x1B:
      v25 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v25 )
        goto LABEL_76;
      result = *(_DWORD *)&v25[7].m_strCode[20];
      break;
    case 0x1C:
      v26 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v26 )
        goto LABEL_76;
      result = *(_DWORD *)&v26[7].m_strCode[44];
      break;
    case 0x1F:
      v27 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v27 )
        goto LABEL_76;
      result = v27[6].m_dwIndex;
      break;
    case 0x21:
      v28 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v28 )
        goto LABEL_76;
      result = *(_DWORD *)&v28[7].m_strCode[36];
      break;
    case 0x22:
      v29 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v29 )
        goto LABEL_76;
      result = *(_DWORD *)&v29[7].m_strCode[40];
      break;
    case 0x24:
      v30 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v30 )
        goto LABEL_76;
      result = *(_DWORD *)&v30[6].m_strCode[4];
      break;
    default:
LABEL_76:
      result = 0i64;
      break;
  }
  return result;
}
