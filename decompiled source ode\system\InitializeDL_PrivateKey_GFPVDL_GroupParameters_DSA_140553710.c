/*
 * Function: ?Initialize@?$DL_PrivateKey_GFP@VDL_GroupParameters_DSA@CryptoPP@@@CryptoPP@@QEAAXAEAVRandomNumberGenerator@2@AEBVInteger@2@11@Z
 * Address: 0x140553710
 */

int __fastcall CryptoPP::DL_PrivateKey_GFP<CryptoPP::DL_GroupParameters_DSA>::Initialize(__int64 a1, __int64 a2, __int64 a3, __int64 a4, __int64 a5)
{
  __int64 v5; // rax@1
  __int64 v6; // rax@1
  __int64 v7; // rax@1
  char v9; // [sp+20h] [bp-1E8h]@1
  char v10; // [sp+68h] [bp-1A0h]@1
  char v11; // [sp+F0h] [bp-118h]@1
  __int64 v12; // [sp+1B8h] [bp-50h]@1
  __int64 v13; // [sp+1C0h] [bp-48h]@1
  __int64 v14; // [sp+1C8h] [bp-40h]@1
  __int64 v15; // [sp+1D0h] [bp-38h]@1
  __int64 v16; // [sp+1D8h] [bp-30h]@1
  __int64 v17; // [sp+1E0h] [bp-28h]@1
  __int64 v18; // [sp+1E8h] [bp-20h]@1
  void (__fastcall **v19)(_QWORD, _QWORD, _QWORD); // [sp+1F0h] [bp-18h]@1
  __int64 v20; // [sp+210h] [bp+8h]@1
  __int64 v21; // [sp+218h] [bp+10h]@1
  __int64 v22; // [sp+228h] [bp+20h]@1

  v22 = a4;
  v21 = a2;
  v20 = a1;
  v12 = -2i64;
  LOBYTE(a4) = 1;
  LODWORD(v5) = CryptoPP::MakeParameters<CryptoPP::Integer>(&v9, "Modulus", a3, a4);
  v13 = v5;
  v14 = v5;
  LODWORD(v6) = CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,CryptoPP::Integer>::operator()<CryptoPP::Integer>(
                  v5,
                  &v10,
                  "SubgroupOrder",
                  v22);
  v15 = v6;
  v16 = v6;
  LODWORD(v7) = CryptoPP::AlgorithmParameters<CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,CryptoPP::Integer>,CryptoPP::Integer>::operator()<CryptoPP::Integer>(
                  v6,
                  &v11,
                  "SubgroupGenerator",
                  a5);
  v17 = v7;
  v18 = v7;
  v19 = *(void (__fastcall ***)(_QWORD, _QWORD, _QWORD))(v20 + 16);
  (*v19)(v20 + 16, v21, v7);
  CryptoPP::AlgorithmParameters<CryptoPP::AlgorithmParameters<CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,CryptoPP::Integer>,CryptoPP::Integer>,CryptoPP::Integer>::~AlgorithmParameters<CryptoPP::AlgorithmParameters<CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,CryptoPP::Integer>,CryptoPP::Integer>,CryptoPP::Integer>(&v11);
  CryptoPP::AlgorithmParameters<CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,CryptoPP::Integer>,CryptoPP::Integer>::~AlgorithmParameters<CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,CryptoPP::Integer>,CryptoPP::Integer>(&v10);
  return CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,CryptoPP::Integer>::~AlgorithmParameters<CryptoPP::NullNameValuePairs,CryptoPP::Integer>(&v9);
}
