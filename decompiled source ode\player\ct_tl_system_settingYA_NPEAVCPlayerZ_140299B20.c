/*
 * Function: ?ct_tl_system_setting@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140299B20
 */

char __fastcall ct_tl_system_setting(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-148h]@1
  char DstBuf; // [sp+50h] [bp-F8h]@4
  char v6; // [sp+51h] [bp-F7h]@4
  char v7; // [sp+E8h] [bp-60h]@4
  char v8; // [sp+E9h] [bp-5Fh]@4
  unsigned __int16 v9; // [sp+124h] [bp-24h]@8
  int v10; // [sp+130h] [bp-18h]@11
  unsigned __int64 v11; // [sp+138h] [bp-10h]@4
  CPlayer *v12; // [sp+150h] [bp+8h]@1

  v12 = pOne;
  v1 = &v4;
  for ( i = 80i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11 = (unsigned __int64)&v4 ^ _security_cookie;
  DstBuf = 0;
  memset(&v6, 0, 0x7Cui64);
  v7 = 0;
  memset(&v8, 0, 0x31ui64);
  if ( s_nWordCount > 0 )
  {
    if ( !strcmp_0(s_pwszDstCheat[0], "?") )
    {
      sprintf_s(&DstBuf, 0x7Dui64, "Cheat Help : Syntax = tlsysset 0 ~ 2,   0 = TL Disable, 1 = TL Enable, 2 = Suspend");
      CPlayer::SendData_ChatTrans(v12, 0, 0xFFFFFFFF, -1, 0, &DstBuf, -1, 0i64);
      result = 1;
    }
    else
    {
      v9 = atoi(s_pwszDstCheat[0]);
      if ( (signed int)v9 >= 0 || (signed int)v9 <= 3 )
      {
        v10 = v9;
        if ( v9 )
        {
          if ( v10 == 1 )
          {
            TimeLimitMgr::SetTLEnable(qword_1799CA2D0, 1u);
            sprintf_s(&v7, 0x32ui64, "Time Limit System Enable");
          }
          else if ( v10 == 2 )
          {
            TimeLimitMgr::SetTLEnable(qword_1799CA2D0, 2u);
            sprintf_s(&v7, 0x32ui64, "Time Limit System Suspend");
          }
        }
        else
        {
          TimeLimitMgr::SetTLEnable(qword_1799CA2D0, 0);
          sprintf_s(&v7, 0x32ui64, "Time Limit System Disable");
        }
        sprintf_s(&DstBuf, 0x7Dui64, "Time Limit Info : %s", &v7);
        CPlayer::SendData_ChatTrans(v12, 0, 0xFFFFFFFF, -1, 0, &DstBuf, -1, 0i64);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
