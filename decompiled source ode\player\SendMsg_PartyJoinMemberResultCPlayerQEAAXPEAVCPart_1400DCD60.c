/*
 * Function: ?SendMsg_PartyJoinMemberResult@CPlayer@@QEAAXPEAVCPartyPlayer@@E@Z
 * Address: 0x1400DCD60
 */

void __fastcall CPlayer::SendMsg_PartyJoinMemberResult(CPlayer *this, CPartyPlayer *pJoiner, char byLootShareMode)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-98h]@1
  char szMsg[4]; // [sp+38h] [bp-60h]@4
  char Dest; // [sp+3Ch] [bp-5Ch]@4
  char v8; // [sp+4Dh] [bp-4Bh]@4
  unsigned __int16 v9; // [sp+4Eh] [bp-4Ah]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v11; // [sp+65h] [bp-33h]@4
  unsigned __int64 v12; // [sp+80h] [bp-18h]@4
  CPlayer *v13; // [sp+A0h] [bp+8h]@1
  char v14; // [sp+B0h] [bp+18h]@1

  v14 = byLootShareMode;
  v13 = this;
  v3 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = (unsigned __int64)&v5 ^ _security_cookie;
  v9 = pJoiner->m_id.wIndex;
  *(_DWORD *)szMsg = pJoiner->m_id.dwSerial;
  strcpy_0(&Dest, pJoiner->m_wszName);
  v8 = v14;
  pbyType = 16;
  v11 = 8;
  CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, szMsg, 0x18u);
}
