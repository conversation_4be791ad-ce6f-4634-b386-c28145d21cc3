/*
 * Function: ??1?$AlgorithmImpl@V?$DL_DecryptorBase@VInteger@CryptoPP@@@CryptoPP@@U?$DLIES@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@CryptoPP@@$00@2@@CryptoPP@@UEAA@XZ
 * Address: 0x140632C70
 */

int CryptoPP::AlgorithmImpl<CryptoPP::DL_DecryptorBase<CryptoPP::Integer>,CryptoPP::DLIES<CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,1>>::~AlgorithmImpl<CryptoPP::DL_DecryptorBase<CryptoPP::Integer>,CryptoPP::DLIES<CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,1>>()
{
  return CryptoPP::DL_DecryptorBase<CryptoPP::Integer>::~DL_DecryptorBase<CryptoPP::Integer>();
}
