/*
 * Function: ?VerifyAndRestart@?$DL_VerifierBase@UECPPoint@CryptoPP@@@CryptoPP@@UEBA_NAEAVPK_MessageAccumulator@2@@Z
 * Address: 0x140565460
 */

char __fastcall CryptoPP::DL_VerifierBase<CryptoPP::ECPPoint>::VerifyAndRestart(__int64 a1, __int64 a2)
{
  CryptoPP::CryptoMaterial *v2; // rax@1
  __int64 *v3; // rax@1
  __int64 v4; // rax@1
  unsigned __int64 v5; // rax@1
  __int64 *v6; // rax@1
  __int64 v7; // rax@1
  const void *v8; // rax@1
  __int64 v9; // rax@1
  CryptoPP *v10; // rcx@1
  struct CryptoPP::RandomNumberGenerator *v11; // rax@1
  char v12; // ST30_1@1
  char *v13; // rax@1
  char *v14; // rax@1
  CryptoPP::Sec<PERSON>lock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v16; // [sp+50h] [bp-138h]@1
  __int64 v17; // [sp+68h] [bp-120h]@1
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *v18; // [sp+70h] [bp-118h]@1
  __int64 v19; // [sp+78h] [bp-110h]@1
  __int64 *v20; // [sp+80h] [bp-108h]@1
  CryptoPP::Integer v21; // [sp+88h] [bp-100h]@1
  CryptoPP::Integer v22; // [sp+B0h] [bp-D8h]@1
  char v23; // [sp+D8h] [bp-B0h]@1
  char v24; // [sp+E8h] [bp-A0h]@1
  char v25; // [sp+F0h] [bp-98h]@1
  __int64 v26; // [sp+100h] [bp-88h]@1
  __int64 v27; // [sp+108h] [bp-80h]@1
  __int64 v28; // [sp+110h] [bp-78h]@1
  __int64 *v29; // [sp+118h] [bp-70h]@1
  __int64 v30; // [sp+120h] [bp-68h]@1
  char *v31; // [sp+128h] [bp-60h]@1
  __int64 v32; // [sp+130h] [bp-58h]@1
  __int64 v33; // [sp+138h] [bp-50h]@1
  unsigned __int64 v34; // [sp+140h] [bp-48h]@1
  char *v35; // [sp+148h] [bp-40h]@1
  __int64 v36; // [sp+150h] [bp-38h]@1
  unsigned __int64 v37; // [sp+158h] [bp-30h]@1
  unsigned __int64 v38; // [sp+160h] [bp-28h]@1
  __int64 v39; // [sp+168h] [bp-20h]@1
  __int64 v40; // [sp+190h] [bp+8h]@1
  __int64 v41; // [sp+198h] [bp+10h]@1

  v41 = a2;
  v40 = a1;
  v26 = -2i64;
  v27 = *(_QWORD *)(a1 + 8);
  LODWORD(v2) = (*(int (__fastcall **)(signed __int64))(v27 + 32))(a1 + 8);
  CryptoPP::CryptoMaterial::DoQuickSanityCheck(v2);
  v17 = v41;
  LODWORD(v3) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v40 + 136i64))(v40);
  v20 = v3;
  v18 = CryptoPP::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>>::GetAbstractGroupParameters((CryptoPP::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> > *)(v40 + 16));
  v28 = *(_QWORD *)(v40 + 16);
  LODWORD(v4) = (*(int (__fastcall **)(signed __int64))(v28 + 8))(v40 + 16);
  v19 = v4;
  LODWORD(v5) = CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>>::MessageRepresentativeLength(v40);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v16,
    v5);
  LODWORD(v6) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v40 + 144i64))(v40);
  v29 = v6;
  LODWORD(v7) = CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>>::MessageRepresentativeBitLength(v40);
  v30 = v7;
  v31 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v16);
  v32 = *(_QWORD *)v40;
  LODWORD(v8) = (*(int (__fastcall **)(__int64, char *))(v32 + 152))(v40, &v23);
  qmemcpy(&v25, v8, 0x10ui64);
  LODWORD(v9) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v17 + 144i64))(v17);
  v33 = v9;
  v34 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v17 + 8));
  v35 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v17 + 8));
  v11 = CryptoPP::NullRNG(v10);
  v36 = *v29;
  v12 = *(_BYTE *)(v17 + 184);
  (*(void (__fastcall **)(__int64 *, struct CryptoPP::RandomNumberGenerator *, char *, unsigned __int64))(v36 + 48))(
    v29,
    v11,
    v35,
    v34);
  *(_BYTE *)(v17 + 184) = 1;
  v37 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v16);
  v13 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v16);
  CryptoPP::Integer::Integer(&v22, (const unsigned __int8 *)v13, v37, 0);
  v38 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v17 + 80));
  v14 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v17 + 80));
  CryptoPP::Integer::Integer(&v21, (const unsigned __int8 *)v14, v38, 0);
  v39 = *v20;
  v24 = (*(int (__fastcall **)(__int64 *, CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *, __int64, CryptoPP::Integer *))(v39 + 8))(
          v20,
          v18,
          v19,
          &v22);
  CryptoPP::Integer::~Integer(&v21);
  CryptoPP::Integer::~Integer(&v22);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v16);
  return v24;
}
