/*
 * Function: ?_Insert_n@?$vector@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@IEAAXV?$_Vector_iterator@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@2@_KAEBVCUnmannedTraderGroupDivisionVersionInfo@@@Z
 * Address: 0x140398CF0
 */

void __fastcall std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Insert_n(std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *this, std::_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *_Where, unsigned __int64 _Count, CUnmannedTraderGroupDivisionVersionInfo *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v6; // rax@5
  unsigned __int64 v7; // rax@7
  unsigned __int64 v8; // rax@8
  unsigned __int64 v9; // rax@11
  __int64 v10; // [sp+0h] [bp-C8h]@1
  CUnmannedTraderGroupDivisionVersionInfo _Vala; // [sp+28h] [bp-A0h]@4
  unsigned __int64 _Counta; // [sp+68h] [bp-60h]@4
  CUnmannedTraderGroupDivisionVersionInfo *_Ptr; // [sp+70h] [bp-58h]@13
  CUnmannedTraderGroupDivisionVersionInfo *v14; // [sp+78h] [bp-50h]@13
  CUnmannedTraderGroupDivisionVersionInfo *_Last; // [sp+80h] [bp-48h]@18
  __int64 v16; // [sp+88h] [bp-40h]@4
  unsigned __int64 v17; // [sp+90h] [bp-38h]@5
  unsigned __int64 v18; // [sp+98h] [bp-30h]@8
  unsigned __int64 v19; // [sp+A0h] [bp-28h]@9
  CUnmannedTraderGroupDivisionVersionInfo *v20; // [sp+A8h] [bp-20h]@13
  CUnmannedTraderGroupDivisionVersionInfo *v21; // [sp+B0h] [bp-18h]@13
  std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *v22; // [sp+D0h] [bp+8h]@1
  std::_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *v23; // [sp+D8h] [bp+10h]@1
  unsigned __int64 v24; // [sp+E0h] [bp+18h]@1
  unsigned __int64 v25; // [sp+E0h] [bp+18h]@13

  v24 = _Count;
  v23 = _Where;
  v22 = this;
  v4 = &v10;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v16 = -2i64;
  CUnmannedTraderGroupDivisionVersionInfo::CUnmannedTraderGroupDivisionVersionInfo(&_Vala, _Val);
  _Counta = std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::capacity(v22);
  if ( v24 )
  {
    v17 = std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::size(v22);
    v6 = std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::max_size(v22);
    if ( v6 - v17 < v24 )
      std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Xlen();
    v7 = std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::size(v22);
    if ( _Counta >= v24 + v7 )
    {
      if ( (unsigned int)((char *)v22->_Mylast - (char *)v23->_Myptr) / 48i64 >= v24 )
      {
        _Last = v22->_Mylast;
        v22->_Mylast = std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Umove<CUnmannedTraderGroupDivisionVersionInfo *>(
                         v22,
                         &_Last[-v24],
                         _Last,
                         v22->_Mylast);
        stdext::_Unchecked_move_backward<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo *>(
          v23->_Myptr,
          &_Last[-v24],
          _Last);
        std::fill<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo>(
          v23->_Myptr,
          &v23->_Myptr[v24],
          &_Vala);
      }
      else
      {
        std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Umove<CUnmannedTraderGroupDivisionVersionInfo *>(
          v22,
          v23->_Myptr,
          v22->_Mylast,
          &v23->_Myptr[v24]);
        std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Ufill(
          v22,
          v22->_Mylast,
          v24 - (unsigned int)((char *)v22->_Mylast - (char *)v23->_Myptr) / 48i64,
          &_Vala);
        v22->_Mylast += v24;
        std::fill<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo>(
          v23->_Myptr,
          &v22->_Mylast[-v24],
          &_Vala);
      }
    }
    else
    {
      v18 = _Counta / 2;
      v8 = std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::max_size(v22);
      if ( v8 - v18 >= _Counta )
        v19 = _Counta / 2 + _Counta;
      else
        v19 = 0i64;
      _Counta = v19;
      v9 = std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::size(v22);
      if ( _Counta < v24 + v9 )
        _Counta = v24
                + std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::size(v22);
      _Ptr = std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::allocate(&v22->_Alval, _Counta);
      v14 = _Ptr;
      v20 = std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Umove<CUnmannedTraderGroupDivisionVersionInfo *>(
              v22,
              v22->_Myfirst,
              v23->_Myptr,
              _Ptr);
      v14 = v20;
      v21 = std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Ufill(
              v22,
              v20,
              v24,
              &_Vala);
      v14 = v21;
      std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Umove<CUnmannedTraderGroupDivisionVersionInfo *>(
        v22,
        v23->_Myptr,
        v22->_Mylast,
        v21);
      v25 = std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::size(v22)
          + v24;
      if ( v22->_Myfirst )
      {
        std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Destroy(
          v22,
          v22->_Myfirst,
          v22->_Mylast);
        std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::deallocate(
          &v22->_Alval,
          v22->_Myfirst,
          (unsigned int)((char *)v22->_Myend - (char *)v22->_Myfirst) / 48i64);
      }
      v22->_Myend = &_Ptr[_Counta];
      v22->_Mylast = &_Ptr[v25];
      v22->_Myfirst = _Ptr;
    }
  }
  CUnmannedTraderGroupDivisionVersionInfo::~CUnmannedTraderGroupDivisionVersionInfo(&_Vala);
  std::_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::~_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(v23);
}
