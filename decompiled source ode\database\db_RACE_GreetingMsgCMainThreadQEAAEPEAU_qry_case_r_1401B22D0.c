/*
 * Function: ?db_RACE_GreetingMsg@CMainThread@@QEAAEPEAU_qry_case_race_greetingmsg@@@Z
 * Address: 0x1401B22D0
 */

char __fastcall CMainThread::db_RACE_GreetingMsg(CMainThread *this, _qry_case_race_greetingmsg *pSheet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMainThread *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CRFWorldDatabase::Update_RaceGreet(v6->m_pWorldDB, pSheet) )
    result = 0;
  else
    result = 24;
  return result;
}
