/*
 * Function: ??$_Uninit_move@PEAVCUnmannedTraderSchedule@@PEAV1@V?$allocator@VCUnmannedTraderSchedule@@@std@@U_Undefined_move_tag@3@@std@@YAPEAVCUnmannedTraderSchedule@@PEAV1@00AEAV?$allocator@VCUnmannedTraderSchedule@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140397190
 */

CUnmannedTraderSchedule *__fastcall std::_Uninit_move<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *,std::allocator<CUnmannedTraderSchedule>,std::_Undefined_move_tag>(CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last, CUnmannedTraderSchedule *_Dest, std::allocator<CUnmannedTraderSchedule> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-28h]@1
  CUnmannedTraderSchedule *_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  return stdext::unchecked_uninitialized_copy<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *,std::allocator<CUnmannedTraderSchedule>>(
           _Firsta,
           _Last,
           _Dest,
           _Al);
}
