/*
 * Function: ?ExpulsionSocket@CNetworkEX@@EEAA_NKKEPEAX@Z
 * Address: 0x1401DAFA0
 */

char __fastcall CNetworkEX::ExpulsionSocket(CNetworkEX *this, unsigned int dwProID, unsigned int dwIndex, char byReason, void *pvInfo)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  _socket *v7; // rax@8
  char *v8; // rax@8
  _socket *v9; // rax@12
  char *v10; // rax@12
  char *v12; // rax@19
  int v13; // ecx@19
  unsigned int v14; // edx@19
  unsigned int v15; // edi@19
  char *v16; // rax@23
  CAsyncLogger *v17; // rax@31
  unsigned __int16 v18; // ax@33
  CAsyncLogger *v19; // rax@34
  __int64 v20; // [sp+0h] [bp-3B8h]@1
  char *pszLog; // [sp+20h] [bp-398h]@19
  char *v22; // [sp+28h] [bp-390h]@19
  int v23; // [sp+30h] [bp-388h]@31
  CUserDB *v24; // [sp+40h] [bp-378h]@6
  char Dest; // [sp+58h] [bp-360h]@6
  char v26; // [sp+5Dh] [bp-35Bh]@6
  _socket *v27; // [sp+88h] [bp-330h]@8
  char v28; // [sp+A0h] [bp-318h]@8
  CUserDB *v29; // [sp+128h] [bp-290h]@10
  char v30; // [sp+138h] [bp-280h]@10
  char v31; // [sp+13Dh] [bp-27Bh]@10
  _socket *v32; // [sp+168h] [bp-250h]@12
  char v33; // [sp+180h] [bp-238h]@12
  _socket *v34; // [sp+208h] [bp-1B0h]@14
  _BYTE *v35; // [sp+210h] [bp-1A8h]@14
  char v36; // [sp+230h] [bp-188h]@19
  _socket *v37; // [sp+2B8h] [bp-100h]@21
  char v38; // [sp+2D0h] [bp-E8h]@23
  _socket *v39; // [sp+358h] [bp-60h]@27
  CUserDB *v40; // [sp+360h] [bp-58h]@29
  int v41; // [sp+368h] [bp-50h]@29
  _DWORD *v42; // [sp+370h] [bp-48h]@31
  char *v43; // [sp+380h] [bp-38h]@31
  char *v44; // [sp+388h] [bp-30h]@31
  int v45; // [sp+390h] [bp-28h]@31
  unsigned __int64 v46; // [sp+398h] [bp-20h]@4
  CNetworkEX *v47; // [sp+3C0h] [bp+8h]@1
  unsigned int dwSocketIndex; // [sp+3D0h] [bp+18h]@1

  dwSocketIndex = dwIndex;
  v47 = this;
  v5 = &v20;
  for ( i = 234i64; i; --i )
  {
    *(_DWORD *)v5 = -*********;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v46 = (unsigned __int64)&v20 ^ _security_cookie;
  if ( !dwProID )
  {
    if ( byReason == 3 )
    {
      v24 = &g_UserDB[dwIndex];
      qmemcpy(&Dest, "null", 5ui64);
      memset(&v26, 0, 0x1Bui64);
      if ( v24->m_bActive )
        strcpy_0(&Dest, v24->m_szAccountID);
      v7 = CNetWorking::GetSocket((CNetWorking *)&v47->vfptr, 0, dwSocketIndex);
      v27 = v7;
      v8 = inet_ntoa(v7->m_Addr.sin_addr);
      sprintf(&v28, "CLOSE>> ExpulsionSocket[Buffer: Send Buffer Full] ID: %s, IP: %s", &Dest, v8);
      CNetworkEX::Close(v47, 0, dwSocketIndex, 0, &v28);
    }
    else if ( byReason )
    {
      switch ( byReason )
      {
        case 1:
          v34 = CNetWorking::GetSocket((CNetWorking *)&v47->vfptr, 0, dwIndex);
          v35 = pvInfo;
          if ( !v34->m_bAccept )
            return 0;
          if ( v35[2] == 1 && v35[3] == 1 )
          {
            v34->m_bEnterCheck = 1;
            return 0;
          }
          v12 = inet_ntoa(v34->m_Addr.sin_addr);
          v13 = *(_WORD *)v35;
          v14 = v35[3];
          v15 = v35[2];
          v22 = v12;
          LODWORD(pszLog) = v13;
          sprintf(&v36, "CLOSE>> ExpulsionSocket[Certify None: %d,%d, size:%d] IP: %s", v15, v14);
          CNetworkEX::Close(v47, 0, dwSocketIndex, 0, &v36);
          break;
        case 2:
          v37 = CNetWorking::GetSocket((CNetWorking *)&v47->vfptr, 0, dwIndex);
          if ( !v37->m_bAccept )
            return 0;
          v16 = inet_ntoa(v37->m_Addr.sin_addr);
          sprintf(&v38, "CLOSE>> ExpulsionSocket[Certify Delay] IP: %s", v16);
          CNetworkEX::Close(v47, 0, dwSocketIndex, 0, &v38);
          break;
        case 5:
          if ( !v47->m_bUseFG )
            return 1;
          v39 = CNetWorking::GetSocket((CNetWorking *)&v47->vfptr, 0, dwIndex);
          if ( !v39->m_bAccept )
            return 0;
          v40 = &g_UserDB[dwSocketIndex];
          v41 = *(_DWORD *)pvInfo;
          if ( !v40->m_bActive )
          {
            CNetworkEX::Close(v47, 0, dwSocketIndex, 1, "FireGuard Detect !pUser->m_bActive !!");
            return 1;
          }
          v43 = v40->m_szAccountID;
          v44 = inet_ntoa(v39->m_Addr.sin_addr);
          v17 = CAsyncLogger::Instance();
          v23 = v41;
          LODWORD(v22) = v40->m_dwAccountSerial;
          pszLog = v43;
          CAsyncLogger::FormatLog(v17, 7, "IP(%s) : Account(%s) : AccountSerial(%u) : FGStatus(%#x)", v44);
          v42 = pvInfo;
          v45 = *(_DWORD *)pvInfo;
          if ( v45 < 11 || v45 > 14 )
          {
            v19 = CAsyncLogger::Instance();
            CAsyncLogger::FormatLog(v19, 7, "Fire Guard Punishment type Invaild : Type(%#x)", *v42);
          }
          else
          {
            v18 = _ccrfg_detect_alret::size(&sCcrFgBlock);
            CNetProcess::LoadSendMsg(unk_1414F2088, dwSocketIndex, sbyCcrFgBlock, (char *)&sCcrFgBlock, v18);
            CNetworkEX::Close(v47, 0, dwSocketIndex, 1, 0i64);
          }
          break;
      }
    }
    else
    {
      v29 = &g_UserDB[dwIndex];
      qmemcpy(&v30, "null", 5ui64);
      memset(&v31, 0, 0x1Bui64);
      if ( v29->m_bActive )
        strcpy_0(&v30, v29->m_szAccountID);
      v9 = CNetWorking::GetSocket((CNetWorking *)&v47->vfptr, 0, dwSocketIndex);
      v32 = v9;
      v10 = inet_ntoa(v9->m_Addr.sin_addr);
      sprintf(&v33, "CLOSE>> ExpulsionSocket[SpeedHack: No Respon] ID: %s, IP: %s", &v30, v10);
      CNetworkEX::Close(v47, 0, dwSocketIndex, 0, &v33);
    }
  }
  return 1;
}
