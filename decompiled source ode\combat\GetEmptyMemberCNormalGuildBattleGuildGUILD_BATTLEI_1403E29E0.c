/*
 * Function: ?GetEmptyMember@CNormalGuildBattleGuild@GUILD_BATTLE@@IEAAHXZ
 * Address: 0x1403E29E0
 */

signed __int64 __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::GetEmptyMember(GUILD_BATTLE::CNormalGuildBattleGuild *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; (signed int)j < 50; ++j )
  {
    if ( GUILD_BATTLE::CNormalGuildBattleGuildMember::IsEmpty(&v6->m_kMember[j]) )
      return j;
  }
  return 0xFFFFFFFFi64;
}
