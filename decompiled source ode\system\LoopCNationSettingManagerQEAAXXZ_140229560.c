/*
 * Function: ?Loop@CNationSettingManager@@QEAAXXZ
 * Address: 0x140229560
 */

void __fastcall CNationSettingManager::Loop(CNationSettingManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  INationGameGuardSystem *v4; // [sp+20h] [bp-18h]@5
  CNationSettingManager *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  (*(void (__fastcall **)(CNationSettingData *))&v5->m_pData->vfptr->gap8[0])(v5->m_pData);
  if ( CNationSettingData::GetGameGuardSystem(v5->m_pData) )
  {
    v4 = CNationSettingData::GetGameGuardSystem(v5->m_pData);
    ((void (__fastcall *)(INationGameGuardSystem *))v4->vfptr->OnLoop)(v4);
  }
}
