/*
 * Function: ?Use@CMonsterSkill@@QEAAHK_N@Z
 * Address: 0x140156920
 */

__int64 __fastcall CMonsterSkill::Use(CMonsterSkill *this, unsigned int dwUsedTime, bool bCount)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CMonsterSkill *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7->m_BefTime = dwUsedTime;
  if ( bCount )
    CMonsterSkill::SetAccumulationCountAdd(v7, 1);
  return 0i64;
}
