/*
 * Function: ?Update_Level@CUserDB@@QEAA_NEN@Z
 * Address: 0x140115D60
 */

char __fastcall CUserDB::Update_Level(CUserDB *this, char lv, long double exp)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  unsigned int v6; // ecx@14
  __int64 v7; // [sp-20h] [bp-38h]@1
  int v8; // [sp+0h] [bp-18h]@11
  CUserDB *v9; // [sp+20h] [bp+8h]@1

  v9 = this;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v9->m_AvatorData.dbAvator.m_byMaxLevel )
  {
    if ( (unsigned __int8)lv > (signed int)v9->m_AvatorData.dbAvator.m_byMaxLevel
      && (!v9->m_byUserDgr || v9->m_byUserDgr == 1) )
    {
      v8 = v9->m_AvatorData.dbAvator.m_byMaxLevel;
      CLogFile::Write(
        &stru_1799C8E78,
        "%s : Update_Level(): pRecv->byLv (%d) > max level (%d) => failed ",
        v9->m_aszAvatorName,
        (unsigned __int8)lv);
      return 0;
    }
  }
  else if ( (signed int)(unsigned __int8)lv > 50 )
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_Level(): pRecv->byLv (%d) => failed ",
      v9->m_aszAvatorName,
      (unsigned __int8)lv);
    return 0;
  }
  if ( (unsigned __int8)lv > (signed int)v9->m_AvatorData.dbAvator.m_byLevel || v9->m_byUserDgr )
  {
    v9->m_AvatorData.dbAvator.m_byLevel = lv;
    v9->m_AvatorData.dbAvator.m_dExp = exp;
    if ( !v9->m_byUserDgr )
      CUserDB::WriteLog_Level(v9, lv);
    v9->m_bDataUpdate = 1;
    result = 1;
  }
  else
  {
    v6 = v9->m_AvatorData.dbAvator.m_byLevel;
    v8 = (unsigned __int8)lv;
    CLogFile::Write(&stru_1799C8E78, "%s : Update_Level(): OLD(%d) >= NEW(%d) => failed ", v9->m_aszAvatorName, v6);
    result = 0;
  }
  return result;
}
