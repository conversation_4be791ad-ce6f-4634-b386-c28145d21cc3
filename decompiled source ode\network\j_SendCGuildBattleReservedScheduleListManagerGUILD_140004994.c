/*
 * Function: j_?Send@CGuildBattleReservedScheduleListManager@GUILD_BATTLE@@QEAAXIHKEEK@Z
 * Address: 0x140004994
 */

void __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Send(GUILD_BATTLE::CGuildBattleReservedScheduleListManager *this, unsigned int uiMapID, int n, unsigned int dwVer, char byDay, char byPage, unsigned int dwGuildSerial)
{
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Send(this, uiMapID, n, dwVer, byDay, byPage, dwGuildSerial);
}
