/*
 * Function: ??$_Max_element@PEAVCNormalGuildBattleGuildMember@GUILD_BATTLE@@VCTopGoalPrediCate@CNormalGuildBattleGuild@2@@std@@YAPEAVCNormalGuildBattleGuildMember@GUILD_BATTLE@@PEAV12@0VCTopGoalPrediCate@CNormalGuildBattleGuild@2@@Z
 * Address: 0x1403EB660
 */

GUILD_BATTLE::CNormalGuildBattleGuildMember *__fastcall std::_Max_element<GUILD_BATTLE::CNormalGuildBattleGuildMember *,GUILD_BATTLE::CNormalGuildBattleGuild::CTopGoalPrediCate>(GUILD_BATTLE::CNormalGuildBattleGuildMember *_First, GUILD_BATTLE::CNormalGuildBattleGuildMember *_Last, GUILD_BATTLE::CNormalGuildBattleGuild::CTopGoalPrediCate _Pred)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CNormalGuildBattleGuildMember *lhs; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuildMember *rhs; // [sp+40h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v9; // [sp+48h] [bp+10h]@1
  GUILD_BATTLE::CNormalGuildBattleGuild::CTopGoalPrediCate v10; // [sp+50h] [bp+18h]@1

  v10 = _Pred;
  v9 = _Last;
  rhs = _First;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  lhs = rhs;
  if ( rhs != _Last )
  {
    while ( 1 )
    {
      ++rhs;
      if ( rhs == v9 )
        break;
      if ( GUILD_BATTLE::CNormalGuildBattleGuild::CTopGoalPrediCate::operator()(&v10, lhs, rhs) )
        lhs = rhs;
    }
  }
  return lhs;
}
