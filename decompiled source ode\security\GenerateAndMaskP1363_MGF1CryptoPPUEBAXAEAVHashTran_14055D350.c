/*
 * Function: ?GenerateAndMask@P1363_MGF1@CryptoPP@@UEBAXAEAVHashTransformation@2@PEAE_KPEBE2_N@Z
 * Address: 0x14055D350
 */

void __fastcall CryptoPP::P1363_MGF1::GenerateAndMask(CryptoPP::P1363_MGF1 *this, struct CryptoPP::HashTransformation *a2, unsigned __int8 *a3, unsigned __int8 *a4, const unsigned __int8 *a5, unsigned __int64 a6, bool a7)
{
  unsigned __int64 v7; // [sp+38h] [bp-20h]@0
  unsigned int v8; // [sp+48h] [bp-10h]@0

  LOBYTE(v7) = a7;
  CryptoPP::P1363_MGF1KDF2_Common(
    (CryptoPP *)a2,
    (struct CryptoPP::HashTransformation *)a3,
    a4,
    (char)a5,
    (const unsigned __int8 *)a6,
    0i64,
    0i64,
    v7,
    0,
    v8);
}
