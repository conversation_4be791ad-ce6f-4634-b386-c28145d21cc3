/*
 * Function: ?InsertSFContEffect@CMonster@@UEAAEEEKGEPEA_NPEAVCCharacter@@@Z
 * Address: 0x1401475B0
 */

char __usercall CMonster::InsertSFContEffect@<al>(CMonster *this@<rcx>, char byContCode@<dl>, char byEffectCode@<r8b>, unsigned int dwEffectIndex@<r9d>, float a5@<xmm0>, unsigned __int16 wDurSec, char byLv, bool *pbUpMty, CCharacter *pActChar)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  int v11; // eax@4
  char result; // al@5
  CGameObjectVtbl *v13; // rax@53
  __int64 v14; // [sp+0h] [bp-A8h]@1
  bool bAura[8]; // [sp+20h] [bp-88h]@53
  float v16; // [sp+40h] [bp-68h]@10
  unsigned int v17; // [sp+44h] [bp-64h]@17
  int j; // [sp+48h] [bp-60h]@17
  bool *v19; // [sp+50h] [bp-58h]@20
  unsigned int v20; // [sp+58h] [bp-50h]@25
  unsigned int v21; // [sp+5Ch] [bp-4Ch]@25
  _sf_continous *pCont; // [sp+60h] [bp-48h]@30
  unsigned __int8 v23; // [sp+68h] [bp-40h]@30
  _sf_continous *v24; // [sp+70h] [bp-38h]@32
  _sf_continous *v25; // [sp+78h] [bp-30h]@38
  unsigned int v26; // [sp+80h] [bp-28h]@41
  unsigned int v27; // [sp+84h] [bp-24h]@41
  float v28; // [sp+88h] [bp-20h]@45
  unsigned __int16 v29; // [sp+8Ch] [bp-1Ch]@48
  int v30; // [sp+90h] [bp-18h]@49
  int v31; // [sp+94h] [bp-14h]@4
  CMonster *v32; // [sp+B0h] [bp+8h]@1
  char v33; // [sp+B8h] [bp+10h]@1
  char v34; // [sp+C0h] [bp+18h]@1
  unsigned int v35; // [sp+C8h] [bp+20h]@1
  unsigned __int16 wDurSeca; // [sp+D0h] [bp+28h]@17

  v35 = dwEffectIndex;
  v34 = byEffectCode;
  v33 = byContCode;
  v32 = this;
  v9 = &v14;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v9 = -*********;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  *pbUpMty = 1;
  v31 = CMonster::GetMyDMGSFContCount(v32);
  v11 = CMonster::GetMaxDMGSFContCount(v32);
  if ( v31 < v11 )
  {
    if ( (signed int)(unsigned __int8)v33 < 2 )
    {
      if ( !MonsterSFContDamageToleracne::IsSFContDamage(&v32->m_SFContDamageTolerance) || v33 )
      {
        wDurSeca = _GetMonsterContTime(v34, byLv);
        v17 = _sf_continous::GetSFContCurTime();
        for ( j = 0; j < 8; ++j )
        {
          v19 = &v32->m_SFCont[(unsigned __int8)v33][j].m_bExist;
          if ( *v19 && v19[1] == v34 && *((_WORD *)v19 + 1) == v35 )
          {
            if ( v19[4] > (signed int)(unsigned __int8)byLv )
              return 11;
            v20 = v17 - *((_DWORD *)v19 + 2);
            v21 = *((_WORD *)v19 + 6) - v20;
            if ( (signed int)*((_WORD *)v19 + 6) > 0 && (float)((float)(signed int)v21 / (float)wDurSeca) > 0.1 )
              return 12;
            CCharacter::RemoveSFContEffect((CCharacter *)&v32->vfptr, v33, j, 0, 0);
            break;
          }
        }
        pCont = 0i64;
        v23 = -1;
        for ( j = 0; j < 8; ++j )
        {
          v24 = &v32->m_SFCont[(unsigned __int8)v33][j];
          if ( !v24->m_bExist )
          {
            pCont = v24;
            v23 = j;
            break;
          }
        }
        if ( !pCont )
        {
          pCont = v32->m_SFCont[(unsigned __int8)v33];
          v23 = 0;
          for ( j = 1; j < 8; ++j )
          {
            v25 = &v32->m_SFCont[(unsigned __int8)v33][j];
            if ( v25->m_dwEffSerial < pCont->m_dwEffSerial )
            {
              pCont = v25;
              v23 = j;
            }
          }
          v26 = v17 - pCont->m_dwStartSec;
          v27 = pCont->m_wDurSec - v26;
          if ( (signed int)pCont->m_wDurSec > 0 && (float)((float)(signed int)v27 / (float)wDurSeca) > 0.1 )
            *pbUpMty = 0;
          CCharacter::RemoveSFContEffect((CCharacter *)&v32->vfptr, v33, v23, 0, 0);
        }
        v28 = FLOAT_1_0;
        if ( !v33 && v34 == 1 )
        {
          _effect_parameter::GetEff_Rate(&v32->m_EP, 8);
          v28 = FLOAT_1_0;
        }
        v29 = wDurSeca;
        if ( v32->m_nContEffectSec == -1 )
        {
          v29 = (signed int)ffloor((float)v29 * v28);
          v30 = 2 * wDurSeca;
          if ( v29 > v30 )
            v29 = v30;
        }
        else
        {
          v29 = v32->m_nContEffectSec;
        }
        CCharacter::_set_sf_cont((CCharacter *)&v32->vfptr, pCont, v34, v35, byLv, v17, v29, 0);
        v13 = v32->vfptr;
        *(_QWORD *)bAura = 0i64;
        ((void (__fastcall *)(CMonster *, _QWORD, _QWORD, _QWORD))v13->SFContInsertMessage)(
          v32,
          (unsigned __int8)v33,
          v23,
          0i64);
        v32->m_bLastContEffectUpdate = 1;
        MonsterSFContDamageToleracne::SetSFDamageToleracne_Variation(&v32->m_SFContDamageTolerance, -0.*********);
        CMonster::CheckEventEmotionPresentation(v32, 5, 0i64);
        result = 0;
      }
      else
      {
        MonsterSFContDamageToleracne::GetToleranceProb(&v32->m_SFContDamageTolerance);
        v16 = a5;
        if ( a5 > 1.0 || v16 <= 0.69999999 )
        {
          if ( v16 > 0.69999999 || v16 <= 0.34999999 )
            result = 31;
          else
            result = 30;
        }
        else
        {
          result = 29;
        }
      }
    }
    else
    {
      result = 13;
    }
  }
  else
  {
    result = 7;
  }
  return result;
}
