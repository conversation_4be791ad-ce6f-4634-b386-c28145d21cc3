/*
 * Function: ?history_used_cheet_changetaxrate@TRC_AutoTrade@@QEAAXKPEAD@Z
 * Address: 0x14038EFC0
 */

void __fastcall TRC_AutoTrade::history_used_cheet_changetaxrate(TRC_AutoTrade *this, unsigned int dwProb, char *pName)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  TRC_AutoTrade *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CLogFile::Write(&v6->m_serviceLog, "[ChangeTaxRate]:Used cheat code(changetaxrate) :: %s(%d)", pName);
}
