/*
 * Function: ??$_Fill@PEAPEAVCUnmannedTraderDivisionInfo@@PEAV1@@std@@YAXPEAPEAVCUnmannedTraderDivisionInfo@@0AEBQEAV1@@Z
 * Address: 0x14038C490
 */

void __fastcall std::_Fill<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo *>(CUnmannedTraderDivisionInfo **_First, CUnmannedTraderDivisionInfo **_Last, CUnmannedTraderDivisionInfo *const *_Val)
{
  CUnmannedTraderDivisionInfo **i; // [sp+10h] [bp+8h]@1

  for ( i = _First; i != _Last; ++i )
    *i = *_Val;
}
