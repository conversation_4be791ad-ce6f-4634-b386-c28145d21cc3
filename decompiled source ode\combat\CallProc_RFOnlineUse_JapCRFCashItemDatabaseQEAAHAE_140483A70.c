/*
 * Function: ?CallProc_RFOnlineUse_Jap@CRFCashItemDatabase@@QEAAHAEAU_param_cash_update@@H@Z
 * Address: 0x140483A70
 */

signed __int64 __usercall CRFCashItemDatabase::CallProc_RFOnlineUse_Jap@<rax>(CRFCashItemDatabase *this@<rcx>, _param_cash_update *rParam@<rdx>, int nIdx@<r8d>, signed __int64 a4@<rax>)
{
  void *v4; // rsp@1
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char *v7; // rax@4
  signed __int64 result; // rax@8
  __int64 v9; // [sp-20h] [bp-1508h]@1
  void *SQLStmt; // [sp+0h] [bp-14E8h]@4
  SQLLEN *StrLen_or_IndPtr; // [sp+8h] [bp-14E0h]@4
  char *v12; // [sp+10h] [bp-14D8h]@4
  char *v13; // [sp+18h] [bp-14D0h]@4
  SQLLEN v14; // [sp+28h] [bp-14C0h]@22
  __int16 v15; // [sp+34h] [bp-14B4h]@9
  char _Dest[5120]; // [sp+50h] [bp-1498h]@4
  char Dest; // [sp+1468h] [bp-80h]@4
  char v18; // [sp+1469h] [bp-7Fh]@4
  int v19; // [sp+1484h] [bp-64h]@4
  unsigned __int8 v20; // [sp+1488h] [bp-60h]@16
  int TargetValue; // [sp+1494h] [bp-54h]@22
  unsigned __int8 v22; // [sp+14A4h] [bp-44h]@24
  int v23; // [sp+14B4h] [bp-34h]@33
  unsigned __int8 v24; // [sp+14C4h] [bp-24h]@35
  unsigned __int64 v25; // [sp+14D0h] [bp-18h]@4
  CRFCashItemDatabase *v26; // [sp+14F0h] [bp+8h]@1
  _param_cash_update *v27; // [sp+14F8h] [bp+10h]@1
  int v28; // [sp+1500h] [bp+18h]@1

  v28 = nIdx;
  v27 = rParam;
  v26 = this;
  v4 = alloca(a4);
  v5 = &v9;
  for ( i = 1344i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v25 = (unsigned __int64)&v9 ^ _security_cookie;
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0x13FFui64);
  Dest = 0;
  memset(&v18, 0, 0x13ui64);
  v7 = inet_ntoa((struct in_addr)rParam->in_dwIP);
  strcpy_0(&Dest, v7);
  v19 = v27->in_item[(signed __int64)v28].in_byOverlapNum * v27->in_item[(signed __int64)v28].in_nPrice;
  v13 = &Dest;
  v12 = v27->in_szSvrName;
  StrLen_or_IndPtr = (SQLLEN *)v27->in_item[(signed __int64)v28].in_strItemCode;
  LODWORD(SQLStmt) = v19;
  sprintf_s<5120>(
    (char (*)[5120])_Dest,
    "declare @out_status int  declare @out_amount_s int  exec SP_RF_CHG_ITEM_GAMEON @uid = '%s', @charname = '%s', @item_"
    "price = %d, @ccr_itemcode = %s, @world = '%s', @ip_addr = '%s', @s_status = @out_status output, @s_amount = @out_amo"
    "unt_s output select @out_status, @out_amount_s",
    v27->in_szAcc,
    v27->in_szAvatorName);
  if ( v26->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v26->vfptr, _Dest);
  if ( v26->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v26->vfptr) )
  {
    v15 = SQLExecDirectA_0(v26->m_hStmtSelect, _Dest, -3);
    if ( v15 && v15 != 1 )
    {
      if ( v15 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v26->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v26->vfptr, v15, _Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v26->vfptr, v15, v26->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v15 = SQLFetch_0(v26->m_hStmtSelect);
      if ( v15 && v15 != 1 )
      {
        v20 = 0;
        if ( v15 == 100 )
        {
          v20 = 2;
        }
        else
        {
          SQLStmt = v26->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v26->vfptr, v15, _Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v26->vfptr, v15, v26->m_hStmtSelect);
          v20 = 1;
        }
        if ( v26->m_hStmtSelect )
          SQLCloseCursor_0(v26->m_hStmtSelect);
        result = v20;
      }
      else
      {
        TargetValue = 0;
        StrLen_or_IndPtr = &v14;
        SQLStmt = 0i64;
        v15 = SQLGetData_0(v26->m_hStmtSelect, 1u, 4, &TargetValue, 0i64, &v14);
        if ( v15 && v15 != 1 )
        {
          v22 = 0;
          if ( v15 == 100 )
          {
            v22 = 2;
          }
          else
          {
            SQLStmt = v26->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v26->vfptr, v15, _Dest, "SQLFetch", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v26->vfptr, v15, v26->m_hStmtSelect);
            v22 = 1;
          }
          if ( v26->m_hStmtSelect )
            SQLCloseCursor_0(v26->m_hStmtSelect);
          result = v22;
        }
        else
        {
          if ( TargetValue == 1 )
            v27->in_item[(signed __int64)v28].out_cState = 84;
          else
            v27->in_item[(signed __int64)v28].out_cState = 70;
          v23 = 0;
          StrLen_or_IndPtr = &v14;
          SQLStmt = 0i64;
          v15 = SQLGetData_0(v26->m_hStmtSelect, 2u, 4, &v23, 0i64, &v14);
          if ( v15 && v15 != 1 )
          {
            v24 = 0;
            if ( v15 == 100 )
            {
              v24 = 2;
            }
            else
            {
              SQLStmt = v26->m_hStmtSelect;
              CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v26->vfptr, v15, _Dest, "SQLFetch", SQLStmt);
              CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v26->vfptr, v15, v26->m_hStmtSelect);
              v24 = 1;
            }
            if ( v26->m_hStmtSelect )
              SQLCloseCursor_0(v26->m_hStmtSelect);
            result = v24;
          }
          else
          {
            v27->in_item[(signed __int64)v28].out_nCashAmount = v23;
            v27->out_nCashAmount = v23;
            if ( v26->m_hStmtSelect )
              SQLCloseCursor_0(v26->m_hStmtSelect);
            if ( v26->m_bSaveDBLog )
              CRFNewDatabase::FmtLog((CRFNewDatabase *)&v26->vfptr, "%s Success", _Dest);
            result = 0i64;
          }
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v26->vfptr, "ReConnectDataBase Fail. Query : %s", _Dest);
    result = 1i64;
  }
  return result;
}
