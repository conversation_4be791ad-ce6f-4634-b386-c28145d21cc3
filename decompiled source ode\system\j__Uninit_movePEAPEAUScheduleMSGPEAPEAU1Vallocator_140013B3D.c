/*
 * Function: j_??$_Uninit_move@PEAPEAUScheduleMSG@@PEAPEAU1@V?$allocator@PEAUScheduleMSG@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAUSchedule<PERSON>G@@PEAPEAU1@00AEAV?$allocator@PEAUScheduleMSG@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140013B3D
 */

ScheduleMSG **__fastcall std::_Uninit_move<ScheduleMSG * *,ScheduleMSG * *,std::allocator<ScheduleMSG *>,std::_Undefined_move_tag>(ScheduleMSG **_First, ScheduleMSG **_Last, ScheduleMSG **_Dest, std::allocator<ScheduleMSG *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<ScheduleMSG * *,ScheduleMSG * *,std::allocator<ScheduleMSG *>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
