/*
 * Function: ?SendMsg_FixPosition@CTrap@@UEAAXH@Z
 * Address: 0x14013FB80
 */

void __fastcall CTrap::SendMsg_FixPosition(CTrap *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-98h]@1
  char szMsg[2]; // [sp+38h] [bp-60h]@4
  unsigned __int16 v6; // [sp+3Ah] [bp-5Eh]@4
  unsigned int v7; // [sp+3Ch] [bp-5Ch]@4
  __int16 pShort; // [sp+40h] [bp-58h]@4
  __int16 v9; // [sp+46h] [bp-52h]@5
  unsigned int v10; // [sp+48h] [bp-50h]@4
  char v11; // [sp+4Ch] [bp-4Ch]@4
  char v12; // [sp+4Dh] [bp-4Bh]@4
  unsigned int v13; // [sp+54h] [bp-44h]@6
  char pbyType; // [sp+64h] [bp-34h]@7
  char v15; // [sp+65h] [bp-33h]@7
  int v16; // [sp+80h] [bp-18h]@4
  unsigned __int64 v17; // [sp+88h] [bp-10h]@4
  CTrap *v18; // [sp+A0h] [bp+8h]@1
  int dwClientIndex; // [sp+A8h] [bp+10h]@1

  dwClientIndex = n;
  v18 = this;
  v2 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v17 = (unsigned __int64)&v4 ^ _security_cookie;
  *(_WORD *)szMsg = v18->m_pRecordSet->m_dwIndex;
  v6 = v18->m_ObjID.m_wIndex;
  v7 = v18->m_dwObjSerial;
  FloatToShort(v18->m_fCurPos, &pShort, 3);
  v10 = v18->m_dwMasterSerial;
  v16 = v18->m_bBreakTransparBuffer == 0;
  v11 = v16;
  v12 = v18->m_byRaceCode;
  if ( v18->m_bComplete )
  {
    v9 = 0;
  }
  else
  {
    v13 = timeGetTime() - v18->m_dwStartMakeTime;
    v9 = v13 / 0x3E8;
  }
  pbyType = 4;
  v15 = -88;
  CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, szMsg, 0x16u);
}
