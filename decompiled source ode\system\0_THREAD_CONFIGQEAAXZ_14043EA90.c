/*
 * Function: ??0_THREAD_CONFIG@@QEAA@XZ
 * Address: 0x14043EA90
 */

void __fastcall _THREAD_CONFIG::_THREAD_CONFIG(_THREAD_CONFIG *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _THREAD_CONFIG *Dest; // [sp+30h] [bp+8h]@1

  Dest = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  Dest->bProgramExitWhenLackData = 0;
  Dest->nSleepCount = 100;
  sprintf(Dest->szThreadID, "NONAME");
  sprintf(Dest->szL<PERSON><PERSON><PERSON>Name, "NONAME");
}
