/*
 * Function: ?SetGauge@CPlayer@@QEAAXHH_N@Z
 * Address: 0x14005F430
 */

void __fastcall CPlayer::SetGauge(CPlayer *this, int nParamCode, int nValue, bool bOver)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@4
  CPlayer *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7 = nParamCode;
  if ( nParamCode )
  {
    if ( v7 == 1 )
    {
      CPlayer::SetFP(v8, nValue, bOver);
    }
    else if ( v7 == 2 )
    {
      CPlayer::SetSP(v8, nValue, bOver);
    }
  }
  else
  {
    ((void (__fastcall *)(CPlayer *, _QWORD, _QWORD))v8->vfptr->SetHP)(v8, (unsigned int)nValue, bOver);
  }
}
