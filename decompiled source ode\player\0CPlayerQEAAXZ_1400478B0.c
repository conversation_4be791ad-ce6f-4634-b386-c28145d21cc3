/*
 * Function: ??0CPlayer@@QEAA@XZ
 * Address: 0x1400478B0
 */

void __fastcall CPlayer::CPlayer(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CPlayer *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CCharacter::CCharacter((CCharacter *)&v5->vfptr);
  v5->vfptr = (CGameObjectVtbl *)&CPlayer::`vftable';
  CPlayerDB::CPlayerDB(&v5->m_Param);
  CRealMoveRequestDelayChecker::CRealMoveRequestDelayChecker(&v5->m_kMoveDelayChecker);
  _DTRADE_PARAM::_DTRADE_PARAM(&v5->m_pmTrd);
  _MASTERY_PARAM::_MASTERY_PARAM(&v5->m_pmMst);
  _TRAP_PARAM::_TRAP_PARAM(&v5->m_pmTrp);
  _BUDDY_LIST::_BUDDY_LIST(&v5->m_pmBuddy);
  CQuestMgr::CQuestMgr(&v5->m_QuestMgr);
  ItemCombineMgr::ItemCombineMgr(&v5->m_ItemCombineMgr);
  MiningTicket::MiningTicket(&v5->m_MinigTicket);
  CSetItemEffect::CSetItemEffect(&v5->m_clsSetItem);
  CEquipItemSFAgent::CEquipItemSFAgent(&v5->EquipItemSFAgent);
  _CRYMSG_LIST::_CRYMSG_LIST(&v5->m_pmCryMsg);
  _RENAME_POTION_USE_INFO::_RENAME_POTION_USE_INFO(&v5->m_ReNamePotionUseInfo);
  CPlayer::CashChangeStateFlag::CashChangeStateFlag(&v5->m_CashChangeStateFlag, 0);
  _NPCQuestIndexTempData::_NPCQuestIndexTempData(&v5->m_NPCQuestIndexTempData);
  _100_per_random_table::_100_per_random_table(&v5->m_MakeRandTable);
  CMyTimer::CMyTimer(&v5->m_tmrSiegeTime);
  CMyTimer::CMyTimer(&v5->m_tmrIntervalSec);
  CMyTimer::CMyTimer(&v5->m_tmrBilling);
  CPotionParam::CPotionParam(&v5->m_PotionParam);
  CExtPotionBuf::CExtPotionBuf(&v5->m_PotionBufUse);
  CMyTimer::CMyTimer(&v5->m_tmrAccumPlayingTime);
  CPlayer::__target::__target(&v5->m_TargetObject);
  `vector constructor iterator'(
    v5->m_GroupTargetObject,
    0x48ui64,
    3,
    (void *(__cdecl *)(void *))CPlayer::__target::__target);
  CMyTimer::CMyTimer(&v5->m_tmrGroupTargeting);
  _ATTACK_DELAY_CHECKER::_ATTACK_DELAY_CHECKER(&v5->m_AttDelayChker);
  CPvpPointLimiter::CPvpPointLimiter(&v5->m_kPvpPointLimiter);
  CMyTimer::CMyTimer(&v5->m_tmrAuraSkill);
  CPvpCashPoint::CPvpCashPoint(&v5->m_kPvpCashPoint);
  CCouponMgr::CCouponMgr(&v5->m_kPcBangCoupon);
  CMyTimer::CMyTimer(&v5->m_tmrEffectStartTime);
  CMyTimer::CMyTimer(&v5->m_tmrEffectEndTime);
  _NameChangeBuddyInfo::_NameChangeBuddyInfo(&v5->m_NameChangeBuddyInfo);
  CMyTimer::CMyTimer(&v5->m_tmrPremiumPVPInform);
  _other_shape_all_zocl::_other_shape_all_zocl(&v5->m_bufShapeAll);
  _other_shape_part_zocl::_other_shape_part_zocl(&v5->m_bufSpapePart);
  v5->m_pParkingUnit = 0i64;
}
