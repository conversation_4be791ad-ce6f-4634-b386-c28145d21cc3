/*
 * Function: ??4?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@CryptoPP@@QEAAAEAV01@AEBV01@@Z
 * Address: 0x14055E6C0
 */

__int64 __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::operator=(__int64 a1, __int64 a2)
{
  __int64 v3; // [sp+30h] [bp+8h]@1
  __int64 v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::DL_FixedBasePrecomputation<CryptoPP::Integer>::operator=();
  CryptoPP::Integer::operator=(v3 + 8);
  *(_DWORD *)(v3 + 48) = *(_DWORD *)(v4 + 48);
  CryptoPP::Integer::operator=(v3 + 56);
  std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::operator=(v3 + 96, v4 + 96);
  return v3;
}
