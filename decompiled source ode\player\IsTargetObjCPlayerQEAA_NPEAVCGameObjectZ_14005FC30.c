/*
 * Function: ?IsTargetObj@CPlayer@@QEAA_NPEAVCGameObject@@@Z
 * Address: 0x14005FC30
 */

bool __fastcall CPlayer::IsTargetObj(CPlayer *this, CGameObject *pkObj)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  __int64 v5; // [sp+0h] [bp-38h]@1
  CGameObject *v6; // [sp+20h] [bp-18h]@7
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pkObj && v7->m_TargetObject.pObject )
  {
    v6 = v7->m_TargetObject.pObject;
    if ( v6->m_bLive )
    {
      if ( v7->m_TargetObject.byKind == v6->m_ObjID.m_byKind
        && v7->m_TargetObject.byID == v6->m_ObjID.m_byID
        && v7->m_TargetObject.dwSerial == v6->m_dwObjSerial )
      {
        if ( v7->m_TargetObject.pObject->m_pCurMap == v7->m_pCurMap )
        {
          result = v7->m_TargetObject.byKind == pkObj->m_ObjID.m_byKind
                && v7->m_TargetObject.byID == pkObj->m_ObjID.m_byID
                && v7->m_TargetObject.dwSerial == pkObj->m_dwObjSerial;
        }
        else
        {
          CPlayer::__target::init(&v7->m_TargetObject);
          result = 0;
        }
      }
      else
      {
        CPlayer::__target::init(&v7->m_TargetObject);
        result = 0;
      }
    }
    else
    {
      CPlayer::__target::init(&v7->m_TargetObject);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
