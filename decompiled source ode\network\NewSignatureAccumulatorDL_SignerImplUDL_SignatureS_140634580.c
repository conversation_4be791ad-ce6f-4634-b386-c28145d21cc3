/*
 * Function: ?NewSignatureAccumulator@?$DL_SignerImpl@U?$DL_SignatureSchemeOptions@V?$DL_SS@UDL_SignatureKeys_GFP@CryptoPP@@V?$DL_Algorithm_NR@VInteger@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_NR@2@VSHA1@2@H@CryptoPP@@UDL_SignatureKeys_GFP@2@V?$DL_Algorithm_NR@VInteger@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_NR@2@VSHA1@2@@CryptoPP@@@CryptoPP@@UEBAPEAVPK_MessageAccumulator@2@AEAVRandomNumberGenerator@2@@Z
 * Address: 0x140634580
 */

__int64 CryptoPP::DL_SignerImpl<CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DL_SS<CryptoPP::DL_SignatureKeys_GFP,CryptoPP::DL_Algorithm_NR<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_NR,CryptoPP::SHA1,int>,CryptoPP::DL_SignatureKeys_GFP,CryptoPP::DL_Algorithm_NR<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_NR,CryptoPP::SHA1>>::NewSignatureAccumulator()
{
  __int64 v1; // [sp+20h] [bp-38h]@4
  CryptoPP::PK_MessageAccumulatorBase *v2; // [sp+28h] [bp-30h]@4
  CryptoPP::PK_MessageAccumulatorBase *v3; // [sp+30h] [bp-28h]@1
  __int64 v4; // [sp+38h] [bp-20h]@4
  __int64 v5; // [sp+40h] [bp-18h]@1
  CryptoPP::PK_MessageAccumulatorBase *v6; // [sp+48h] [bp-10h]@2

  v5 = -2i64;
  v3 = (CryptoPP::PK_MessageAccumulatorBase *)operator new(0x180ui64);
  if ( v3 )
    v6 = CryptoPP::PK_MessageAccumulatorImpl<CryptoPP::SHA1>::PK_MessageAccumulatorImpl<CryptoPP::SHA1>(v3);
  else
    v6 = 0i64;
  v2 = v6;
  std::auto_ptr<CryptoPP::PK_MessageAccumulatorBase>::auto_ptr<CryptoPP::PK_MessageAccumulatorBase>(&v1, (__int64)v6);
  std::auto_ptr<CryptoPP::PK_MessageAccumulatorBase>::operator*((__int64)&v1);
  CryptoPP::DL_SignerBase<CryptoPP::Integer>::RestartMessageAccumulator();
  v4 = std::auto_ptr<CryptoPP::PK_MessageAccumulatorBase>::release(&v1);
  std::auto_ptr<CryptoPP::PK_MessageAccumulatorBase>::~auto_ptr<CryptoPP::PK_MessageAccumulatorBase>(&v1);
  return v4;
}
