/*
 * Function: ?SendMsg_RefeshGroupTargetPosition@CPlayer@@QEAAXE@Z
 * Address: 0x1400E2B00
 */

void __fastcall CPlayer::SendMsg_RefeshGroupTargetPosition(CPlayer *this, char byGroupType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  char v6; // [sp+39h] [bp-4Fh]@4
  char v7; // [sp+3Ah] [bp-4Eh]@4
  unsigned int v8; // [sp+3Bh] [bp-4Dh]@4
  char Dst; // [sp+3Fh] [bp-49h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v11; // [sp+65h] [bp-23h]@4
  CPlayer *v12; // [sp+90h] [bp+8h]@1

  v12 = this;
  v2 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  szMsg = byGroupType;
  v6 = v12->m_GroupTargetObject[(unsigned __int8)byGroupType].pObject->m_pCurMap->m_pMapSet->m_dwIndex;
  v7 = v12->m_GroupTargetObject[(unsigned __int8)byGroupType].byID;
  v8 = v12->m_GroupTargetObject[(unsigned __int8)byGroupType].dwSerial;
  memcpy_0(&Dst, v12->m_GroupTargetObject[(unsigned __int8)byGroupType].pObject->m_fCurPos, 0xCui64);
  pbyType = 13;
  v11 = 110;
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x13u);
}
