/*
 * Function: ?GetDigestSize@?$DL_ObjectImplBase@V?$DL_VerifierBase@VInteger@CryptoPP@@@CryptoPP@@U?$DL_SignatureSchemeOptions@V?$DL_SS@UDL_SignatureKeys_GFP@CryptoPP@@V?$DL_Algorithm_NR@VInteger@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_NR@2@VSHA1@2@H@CryptoPP@@UDL_SignatureKeys_GFP@2@V?$DL_Algorithm_NR@VInteger@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_NR@2@VSHA1@2@@2@V?$DL_PublicKey_GFP@VDL_GroupParameters_GFP@CryptoPP@@@2@@CryptoPP@@MEBA_KXZ
 * Address: 0x1406349B0
 */

signed __int64 CryptoPP::DL_ObjectImplBase<CryptoPP::DL_VerifierBase<CryptoPP::Integer>,CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DL_SS<CryptoPP::DL_SignatureKeys_GFP,CryptoPP::DL_Algorithm_NR<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_NR,CryptoPP::SHA1,int>,CryptoPP::DL_SignatureKeys_GFP,CryptoPP::DL_Algorithm_NR<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_NR,CryptoPP::SHA1>,CryptoPP::DL_PublicKey_GFP<CryptoPP::DL_GroupParameters_GFP>>::GetDigestSize()
{
  return 20i64;
}
