/*
 * Function: ?SearchNearEnemy@CAnimus@@QEAAPEAVCCharacter@@XZ
 * Address: 0x140127E90
 */

CCharacter *__fastcall CAnimus::SearchNearEnemy(CAnimus *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CAnimus *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &j;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  for ( j = 0; j < 5; ++j )
  {
    if ( v5->m_AroundSlot[j] )
      return v5->m_AroundSlot[j];
  }
  return 0i64;
}
