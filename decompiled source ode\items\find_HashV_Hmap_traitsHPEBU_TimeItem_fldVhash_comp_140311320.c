/*
 * Function: ?find@?$_Hash@V?$_Hmap_traits@HPEBU_TimeItem_fld@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@std@@$0A@@stdext@@@stdext@@QEAA?AV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@2@@std@@AEBH@Z
 * Address: 0x140311320
 */

std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *__fastcall stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>>::find(stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_TimeItem_fld const *> >,0> > *this, std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *result, const int *_Keyval)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@4
  stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_TimeItem_fld const *> >,0> > *v8; // [sp+40h] [bp+8h]@1
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *resulta; // [sp+48h] [bp+10h]@1

  resulta = result;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = 0;
  stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>>::lower_bound(
    v8,
    result,
    _Keyval);
  return resulta;
}
