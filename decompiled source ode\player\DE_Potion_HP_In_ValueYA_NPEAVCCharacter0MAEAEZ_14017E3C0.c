/*
 * Function: ?DE_Potion_HP_In_Value@@YA_NPEAVCCharacter@@0MAEAE@Z
 * Address: 0x14017E3C0
 */

bool __fastcall DE_Potion_HP_In_Value(CCharacter *pActChar, CCharacter *pTargetChar, float fEffectValue, char *byRet)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  signed int v7; // eax@8
  float v8; // xmm0_4@8
  __int64 v9; // [sp+0h] [bp-38h]@1
  int v10; // [sp+20h] [bp-18h]@8
  float v11; // [sp+24h] [bp-14h]@8
  CCharacter *v12; // [sp+40h] [bp+8h]@1
  CCharacter *v13; // [sp+48h] [bp+10h]@1

  v13 = pTargetChar;
  v12 = pActChar;
  v4 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pTargetChar )
  {
    if ( v12->m_ObjID.m_byID )
    {
      result = 0;
    }
    else
    {
      v7 = ((int (__fastcall *)(CCharacter *))pTargetChar->vfptr->GetHP)(pTargetChar);
      v8 = (float)v7;
      v11 = (float)v7;
      _effect_parameter::GetEff_Rate(&v13->m_EP, 18);
      v10 = (signed int)ffloor(v11 + (float)(fEffectValue * v8));
      result = (unsigned __int8)((int (__fastcall *)(CCharacter *, _QWORD, _QWORD))v13->vfptr->SetHP)(
                                  v13,
                                  (unsigned int)v10,
                                  0i64) != 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
