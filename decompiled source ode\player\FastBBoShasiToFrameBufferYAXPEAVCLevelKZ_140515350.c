/*
 * Function: ?FastBBoShasiToFrameBuffer@@YAXPEAVCLevel@@K@Z
 * Address: 0x140515350
 */

void __usercall FastBBoShasiToFrameBuffer(struct CLevel *a1@<rcx>, unsigned __int32 a2@<edx>, __int64 a3@<r14>)
{
  unsigned __int32 v3; // esi@1
  struct IDirect3DDevice8 *v4; // rax@2
  struct IDirect3DDevice8 *v5; // rax@2
  __int64 v6; // rdx@2
  struct CTextureRender *v7; // rbx@2
  struct CTextureRender *v8; // rdi@3
  struct IDirect3DDevice8 *v9; // rbx@5
  struct CTextureRender *v10; // rax@6
  struct IDirect3DDevice8 *v11; // rax@7
  struct IDirect3DDevice8 *v12; // rax@7
  float v13; // [sp+80h] [bp+18h]@5
  float v14; // [sp+84h] [bp+1Ch]@5
  float v15; // [sp+88h] [bp+20h]@5
  float v16; // [sp+8Ch] [bp+24h]@5

  v3 = a2;
  if ( dword_184A89370 )
  {
    R3EndScene();
    v4 = GetD3dDevice();
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64))v4->vfptr[16].Release)(v4, 7i64);
    v5 = GetD3dDevice();
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64))v5->vfptr[16].Release)(v5, 14i64);
    v7 = Get1st256x256TexRender();
    TextureCopy(v7, *(void **)(v6 + 8), a3);
    if ( (unsigned int)IsEnableBBoShasiShader() )
    {
      v8 = Get2nd256x256TexRender();
      BlurFilterShader(dword_140978DA0, v8, v7);
    }
    else
    {
      v8 = Get2nd256x256TexRender();
      BlurFilterSprite(v8, *((void **)v7 + 1), 2 * dword_140978DA0 - 1, a3);
    }
    dword_184A89274 = 1;
    R3BeginScene();
    v15 = 0.0;
    v13 = 0.0;
    v16 = FLOAT_1_0;
    v14 = FLOAT_1_0;
    v9 = GetD3dDevice();
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, _QWORD))v9->vfptr[16].Release)(v9, 137i64, 0i64);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64))v9->vfptr[16].Release)(
      v9,
      19i64,
      4i64);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64))v9->vfptr[16].Release)(
      v9,
      20i64,
      2i64);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64))v9->vfptr[16].Release)(v9, 27i64);
    if ( !(dword_184A89278 & 0xC0000000) )
    {
      ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, _QWORD, signed __int64))v9->vfptr[12].QueryInterface)(
        v9,
        0i64,
        0i64,
        2i64);
      v10 = Get1st1024x1024TexRender();
      DrawRect(0.0, 0.0, (float)dword_184A79BB0, (float)dword_184A79BB4, &v15, &v13, *((void **)v10 + 1), 0xFFFFFFFF);
    }
    ((void (__fastcall *)(_QWORD, signed __int64, signed __int64))v9->vfptr[16].Release)(v9, 27i64, 1i64);
    v15 = (float)((float)(signed int)dword_140978DA0 * 0.5) / (float)*((signed int *)v8 + 14);
    v13 = (float)((float)(signed int)dword_140978DA0 * 0.5) / (float)*((signed int *)v8 + 15);
    DrawRect(0.0, 0.0, (float)dword_184A79BB0, (float)dword_184A79BB4, &v15, &v13, *((void **)v8 + 1), v3);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64))v9->vfptr[16].Release)(v9, 27i64);
    v11 = GetD3dDevice();
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64))v11->vfptr[16].Release)(
      v11,
      7i64,
      1i64);
    v12 = GetD3dDevice();
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64))v12->vfptr[16].Release)(
      v12,
      14i64,
      1i64);
  }
}
