/*
 * Function: ?FixTalikItemIndex@@YAHE@Z
 * Address: 0x14003F280
 */

signed __int64 __fastcall FixTalikItemIndex(char byTalikEffectNum)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  int v4; // [sp+0h] [bp-18h]@1
  char v5; // [sp+20h] [bp+8h]@1

  v5 = byTalikEffectNum;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  if ( (signed int)(unsigned __int8)v5 < 13 )
    result = (unsigned int)(unsigned __int8)v5 + 20;
  else
    result = 0xFFFFFFFFi64;
  return result;
}
