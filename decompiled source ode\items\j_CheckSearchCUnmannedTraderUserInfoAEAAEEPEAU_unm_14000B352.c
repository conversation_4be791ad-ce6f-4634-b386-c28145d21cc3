/*
 * Function: j_?CheckSearch@CUnmannedTraderUserInfo@@AEAAEEPEAU_unmannedtrader_search_list_request_clzo@@AEAK1PEAVCLogFile@@@Z
 * Address: 0x14000B352
 */

char __fastcall CUnmannedTraderUserInfo::CheckSearch(CUnmannedTraderUserInfo *this, char byType, _unmannedtrader_search_list_request_clzo *pRequest, unsigned int *dwListIndex, unsigned int *dwCurVer, CLogFile *pkLogger)
{
  return CUnmannedTraderUserInfo::CheckSearch(this, byType, pRequest, dwListIndex, dwCurVer, pkLogger);
}
