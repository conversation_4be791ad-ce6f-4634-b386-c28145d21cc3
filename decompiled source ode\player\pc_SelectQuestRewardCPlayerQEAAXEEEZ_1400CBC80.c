/*
 * Function: ?pc_SelectQuestReward@CPlayer@@QEAAXEEE@Z
 * Address: 0x1400CBC80
 */

void __fastcall CPlayer::pc_SelectQuestReward(CPlayer *this, char byQuestDBSlot, char bySelectItemSlotIndex, char bySelectLinkQuestIndex)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  _QUEST_DB_BASE *v7; // [sp+20h] [bp-28h]@4
  int j; // [sp+28h] [bp-20h]@5
  _base_fld *v9; // [sp+30h] [bp-18h]@9
  char *Str1; // [sp+38h] [bp-10h]@14
  CPlayer *v11; // [sp+50h] [bp+8h]@1
  char v12; // [sp+58h] [bp+10h]@1
  char v13; // [sp+60h] [bp+18h]@1
  char v14; // [sp+68h] [bp+20h]@1

  v14 = bySelectLinkQuestIndex;
  v13 = bySelectItemSlotIndex;
  v12 = byQuestDBSlot;
  v11 = this;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7 = (_QUEST_DB_BASE *)((char *)&v11->m_Param.m_QuestDB + 13 * (unsigned __int8)byQuestDBSlot);
  if ( v7->m_List[0].byQuestType != 255 )
  {
    for ( j = 0; j < 3; ++j )
    {
      if ( v7->m_List[0].wNum[j] != 0xFFFF )
        return;
    }
    v9 = CRecordData::GetRecord(CQuestMgr::s_tblQuest, v7->m_List[0].wIndex);
    if ( (unsigned __int8)v13 == 255 )
    {
      if ( *(_DWORD *)&v9[13].m_strCode[60] )
        return;
    }
    else if ( !*(_DWORD *)&v9[13].m_strCode[60] )
    {
      return;
    }
    if ( (unsigned __int8)v13 == 255 || (Str1 = (char *)&v9[14] + 72 * (unsigned __int8)v13, strncmp(Str1, "-1", 2ui64)) )
    {
      if ( (unsigned __int8)v14 == 255
        || strncmp(&v9[22].m_strCode[64 * (unsigned __int64)(unsigned __int8)v13 + 44], "-1", 2ui64) )
      {
        CPlayer::Emb_CompleteQuest(v11, v12, v13, v14);
      }
    }
  }
}
