/*
 * Function: ?player_money_fix@CMgrAccountLobbyHistory@@QEAAXKKPEAU_AVATOR_DATA@@PEAD@Z
 * Address: 0x1402348A0
 */

void __fastcall CMgrAccountLobbyHistory::player_money_fix(CMgrAccountLobbyHistory *this, unsigned int dwOldDalant, unsigned int dwOldGold, _AVATOR_DATA *pAvator, char *pszFileName)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  unsigned int v8; // [sp+20h] [bp-28h]@4
  unsigned int v9; // [sp+28h] [bp-20h]@4
  char *v10; // [sp+30h] [bp-18h]@4
  char *v11; // [sp+38h] [bp-10h]@4
  CMgrAccountLobbyHistory *v12; // [sp+50h] [bp+8h]@1

  v12 = this;
  v5 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -*********;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  sLData[0] = 0;
  v11 = v12->m_szCurTime;
  v10 = v12->m_szCurDate;
  v9 = pAvator->dbAvator.m_dwGold;
  v8 = dwOldGold;
  sprintf_s<10240>(
    (char (*)[10240])sLBuf,
    "Player Money FIX: $D(%d -> %d), $G(%d -> %d) [%s %s]\r\n",
    dwOldDalant,
    pAvator->dbAvator.m_dwDalant);
  strcat_s<20000>((char (*)[20000])sLData, sLBuf);
  strcat_s<20000>((char (*)[20000])sLData, "\r\n\t============\r\n\r\n");
  CMgrAccountLobbyHistory::WriteFile(v12, pszFileName, sLData);
}
