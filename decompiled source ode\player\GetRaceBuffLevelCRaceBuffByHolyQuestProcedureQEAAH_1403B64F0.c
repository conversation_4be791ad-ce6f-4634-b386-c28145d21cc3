/*
 * Function: ?GetRaceBuffLevel@CRaceBuffByHolyQuestProcedure@@QEAAHPEAVCPlayer@@@Z
 * Address: 0x1403B64F0
 */

unsigned int __fastcall CRaceBuffByHolyQuestProcedure::GetRaceBuffLevel(CRaceBuffByHolyQuestProcedure *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@4
  unsigned int result; // eax@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  int iType; // [sp+20h] [bp-18h]@4
  bool v8; // [sp+24h] [bp-14h]@4
  CRaceBuffByHolyQuestProcedure *v9; // [sp+40h] [bp+8h]@1
  CPlayer *v10; // [sp+48h] [bp+10h]@1

  v10 = pOne;
  v9 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = CPlayer::IsHaveMentalTicket(pOne);
  v4 = CPlayerDB::GetRaceCode(&v10->m_Param);
  iType = CRaceBuffHolyQuestResultInfo::GetResultType(&v9->m_kBuffHolyQestResultInfo, v4, v8);
  if ( iType >= 0 )
    result = CRaceBuffHolyQuestResultInfo::GetContinueCnt(&v9->m_kBuffHolyQestResultInfo, iType);
  else
    result = -1;
  return result;
}
