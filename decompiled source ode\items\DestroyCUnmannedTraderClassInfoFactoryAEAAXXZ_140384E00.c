/*
 * Function: ?Destroy@CUnmannedTraderClassInfoFactory@@AEAAXXZ
 * Address: 0x140384E00
 */

void __fastcall CUnmannedTraderClassInfoFactory::Destroy(CUnmannedTraderClassInfoFactory *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-98h]@1
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > result; // [sp+28h] [bp-70h]@5
  bool v5; // [sp+44h] [bp-54h]@6
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > v6; // [sp+48h] [bp-50h]@6
  CUnmannedTraderClassInfo *v7; // [sp+60h] [bp-38h]@8
  CUnmannedTraderClassInfo *v8; // [sp+68h] [bp-30h]@8
  __int64 v9; // [sp+70h] [bp-28h]@4
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v10; // [sp+78h] [bp-20h]@6
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_Right; // [sp+80h] [bp-18h]@6
  void *v12; // [sp+88h] [bp-10h]@9
  CUnmannedTraderClassInfoFactory *v13; // [sp+A0h] [bp+8h]@1

  v13 = this;
  v1 = &v3;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = -2i64;
  if ( !std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::empty(&v13->m_vecTable) )
  {
    std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::begin(&v13->m_vecTable, &result);
    while ( 1 )
    {
      v10 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::end(
              &v13->m_vecTable,
              &v6);
      _Right = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)v10;
      v5 = std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator!=(
             (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&result._Mycont,
             (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v10->_Mycont);
      std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&v6);
      if ( !v5 )
        break;
      if ( *std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator*(&result) )
      {
        v8 = *std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator*(&result);
        v7 = v8;
        if ( v8 )
          v12 = CUnmannedTraderClassInfo::`scalar deleting destructor'(v7, 1u);
        else
          v12 = 0i64;
      }
      std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator++(&result);
    }
    std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&result);
  }
}
