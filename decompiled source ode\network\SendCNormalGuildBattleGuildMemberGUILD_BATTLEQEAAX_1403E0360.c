/*
 * Function: ?Send@CNormalGuildBattleGuildMember@GUILD_BATTLE@@QEAAXPEAEPEADI@Z
 * Address: 0x1403E0360
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuildMember::Send(GUILD_BATTLE::CNormalGuildBattleGuildMember *this, char *byType, char *pSend, unsigned int uiSize)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  unsigned int dwClientIndex; // [sp+30h] [bp-18h]@5
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v8; // [sp+50h] [bp+8h]@1
  char *pbyType; // [sp+58h] [bp+10h]@1
  char *szMsg; // [sp+60h] [bp+18h]@1
  unsigned int v11; // [sp+68h] [bp+20h]@1

  v11 = uiSize;
  szMsg = pSend;
  pbyType = byType;
  v8 = this;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( GUILD_BATTLE::CNormalGuildBattleGuildMember::IsExist(v8) )
  {
    dwClientIndex = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetIndex(v8);
    CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, pbyType, szMsg, v11);
  }
}
