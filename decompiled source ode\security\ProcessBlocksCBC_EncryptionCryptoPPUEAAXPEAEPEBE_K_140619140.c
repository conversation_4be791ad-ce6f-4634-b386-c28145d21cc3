/*
 * Function: ?ProcessBlocks@CBC_Encryption@CryptoPP@@UEAAXPEAEPEBE_K@Z
 * Address: 0x140619140
 */

void __fastcall CryptoPP::CBC_Encryption::ProcessBlocks(CryptoPP::CBC_Encryption *this, unsigned __int8 *a2, const unsigned __int8 *a3, __int64 a4)
{
  char *v4; // rax@1
  unsigned __int64 v5; // r9@1
  char *v6; // rax@3
  char *v7; // rax@3
  unsigned __int8 *v8; // rax@4
  unsigned __int64 v9; // [sp+20h] [bp-38h]@0
  CryptoPP::CBC_Encryption *v10; // [sp+60h] [bp+8h]@1
  unsigned __int8 *v11; // [sp+68h] [bp+10h]@1
  const unsigned __int8 *v12; // [sp+70h] [bp+18h]@1
  __int64 v13; // [sp+78h] [bp+20h]@1

  v13 = a4;
  v12 = a3;
  v11 = a2;
  v10 = this;
  LODWORD(v9) = CryptoPP::CipherModeBase::BlockSize((CryptoPP::CipherModeBase *)&this->vfptr);
  v4 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v10->m_register);
  CryptoPP::xorbuf((CryptoPP *)v4, (unsigned __int8 *)v12, (const unsigned __int8 *)(unsigned int)v9, v5);
  while ( --v13 )
  {
    v6 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v10->m_register);
    CryptoPP::BlockTransformation::ProcessBlock(
      (CryptoPP::BlockTransformation *)&v10->m_cipher->vfptr,
      (const unsigned __int8 *)v6,
      v11);
    v12 += (unsigned int)v9;
    v7 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v10->m_register);
    CryptoPP::xorbuf((CryptoPP *)v7, (unsigned __int8 *)v12, v11, (const unsigned __int8 *)(unsigned int)v9, v9);
    v11 += (unsigned int)v9;
  }
  v8 = (unsigned __int8 *)CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v10->m_register);
  CryptoPP::BlockTransformation::ProcessBlock((CryptoPP::BlockTransformation *)&v10->m_cipher->vfptr, v8);
  qmemcpy(
    v11,
    CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator void *(&v10->m_register),
    (unsigned int)v9);
}
