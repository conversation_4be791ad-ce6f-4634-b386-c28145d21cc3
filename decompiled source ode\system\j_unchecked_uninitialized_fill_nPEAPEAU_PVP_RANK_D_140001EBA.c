/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAU_PVP_RANK_DATA@@_KPEAU1@V?$allocator@PEAU_PVP_RANK_DATA@@@std@@@stdext@@YAXPEAPEAU_PVP_RANK_DATA@@_KAEBQEAU1@AEAV?$allocator@PEAU_PVP_RANK_DATA@@@std@@@Z
 * Address: 0x140001EBA
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<_PVP_RANK_DATA * *,unsigned __int64,_PVP_RANK_DATA *,std::allocator<_PVP_RANK_DATA *>>(_PVP_RANK_DATA **_First, unsigned __int64 _Count, _PVP_RANK_DATA *const *_Val, std::allocator<_PVP_RANK_DATA *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<_PVP_RANK_DATA * *,unsigned __int64,_PVP_RANK_DATA *,std::allocator<_PVP_RANK_DATA *>>(
    _First,
    _Count,
    _Val,
    _Al);
}
