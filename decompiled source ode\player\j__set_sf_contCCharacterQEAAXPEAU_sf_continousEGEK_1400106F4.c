/*
 * Function: j_?_set_sf_cont@CCharacter@@QEAAXPEAU_sf_continous@@EGEKGH@Z
 * Address: 0x1400106F4
 */

void __fastcall CCharacter::_set_sf_cont(CCharacter *this, _sf_continous *pCont, char byEffectC<PERSON>, unsigned __int16 wEffectIndex, char byLv, unsigned int dwStartSec, unsigned __int16 wDurSec, int nCumulCount)
{
  CCharacter::_set_sf_cont(this, pCont, byEffectCode, wEffectIndex, byLv, dwStartSec, wDurSec, nCumulCount);
}
