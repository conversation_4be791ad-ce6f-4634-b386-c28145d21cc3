/*
 * Function: ?InitPen@CCollLineDraw@@SAXXZ
 * Address: 0x14019C080
 */

void CCollLineDraw::InitPen(void)
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-38h]@1
  COLORREF color; // [sp+20h] [bp-18h]@4
  HPEN v4; // [sp+28h] [bp-10h]@4

  v0 = &v2;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  color = 7895160;
  v4 = CreatePen(0, 1, 0x787878u);
  if ( v4 )
    CCollLineDraw::s_hPen = v4;
}
