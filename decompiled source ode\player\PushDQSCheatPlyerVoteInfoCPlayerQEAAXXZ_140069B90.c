/*
 * Function: ?PushDQSCheatPlyerVoteInfo@CPlayer@@QEAAXXZ
 * Address: 0x140069B90
 */

void __fastcall CPlayer::PushDQSCheatPlyerVoteInfo(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v3; // rax@4
  int v4; // eax@4
  __int64 v5; // [sp+0h] [bp-88h]@1
  _qry_case_cheat_player_vote_info v6; // [sp+38h] [bp-50h]@4
  unsigned __int64 v7; // [sp+70h] [bp-18h]@4
  CPlayer *v8; // [sp+90h] [bp+8h]@1

  v8 = this;
  v1 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = (unsigned __int64)&v5 ^ _security_cookie;
  v6.dwAccountSerial = v8->m_pUserDB->m_dwAccountSerial;
  v6.dwCharSerial = CPlayerDB::GetCharSerial(&v8->m_Param);
  v3 = CPlayerDB::GetCharNameW(&v8->m_Param);
  strcpy_0(v6.wszCharName, v3);
  v4 = _qry_case_cheat_player_vote_info::size(&v6);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -105, (char *)&v6, v4);
}
