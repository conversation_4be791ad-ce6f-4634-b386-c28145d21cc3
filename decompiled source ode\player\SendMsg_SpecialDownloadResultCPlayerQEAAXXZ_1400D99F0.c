/*
 * Function: ?SendMsg_SpecialDownloadResult@CPlayer@@QEAAXXZ
 * Address: 0x1400D99F0
 */

void __fastcall CPlayer::SendMsg_SpecialDownloadResult(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@10
  unsigned __int16 v4; // ax@17
  unsigned __int16 v5; // ax@18
  __int64 v6; // [sp+0h] [bp-2B8h]@1
  _unit_download_result_zocl Dst; // [sp+40h] [bp-278h]@5
  int v8; // [sp+144h] [bp-174h]@5
  int k; // [sp+148h] [bp-170h]@5
  char pbyType; // [sp+154h] [bp-164h]@10
  char v11; // [sp+155h] [bp-163h]@10
  _animus_download_result_zocl v12; // [sp+180h] [bp-138h]@12
  int v13; // [sp+1D4h] [bp-E4h]@12
  int j; // [sp+1D8h] [bp-E0h]@12
  char *v15; // [sp+1E0h] [bp-D8h]@14
  char v16; // [sp+1F4h] [bp-C4h]@17
  char v17; // [sp+1F5h] [bp-C3h]@17
  _animus_download_result_zocl v18; // [sp+220h] [bp-98h]@18
  char v19; // [sp+284h] [bp-34h]@18
  char v20; // [sp+285h] [bp-33h]@18
  unsigned __int64 v21; // [sp+2A0h] [bp-18h]@4
  CPlayer *v22; // [sp+2C0h] [bp+8h]@1

  v22 = this;
  v1 = &v6;
  for ( i = 172i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v21 = (unsigned __int64)&v6 ^ _security_cookie;
  v22->m_bSpecialDownload = 1;
  if ( CPlayerDB::GetRaceCode(&v22->m_Param) )
  {
    if ( CPlayerDB::GetRaceCode(&v22->m_Param) == 1 )
    {
      _animus_download_result_zocl::_animus_download_result_zocl(&v12);
      v13 = 0;
      for ( j = 0; j < 4; ++j )
      {
        v15 = &v22->m_Param.m_dbAnimus.m_pStorageList[j].m_bLoad;
        if ( *v15 )
        {
          v12.AnimusList[v13].sItemIndex = v15[3];
          v12.AnimusList[v13].dwExp = *(_QWORD *)(v15 + 5);
          v12.AnimusList[v13].dwParam = *(_DWORD *)(v15 + 13);
          v12.AnimusList[v13].byCsMethod = v15[32];
          v12.AnimusList[v13++].dwT = *(_DWORD *)(v15 + 33);
        }
      }
      v12.byAnimusNum = v13;
      v16 = 3;
      v17 = 15;
      v4 = _animus_download_result_zocl::size(&v12);
      CNetProcess::LoadSendMsg(unk_1414F2088, v22->m_ObjID.m_wIndex, &v16, &v12.byAnimusNum, v4);
    }
    else
    {
      _animus_download_result_zocl::_animus_download_result_zocl(&v18);
      v19 = 3;
      v20 = 15;
      v5 = _animus_download_result_zocl::size(&v18);
      CNetProcess::LoadSendMsg(unk_1414F2088, v22->m_ObjID.m_wIndex, &v19, &v18.byAnimusNum, v5);
    }
  }
  else
  {
    _unit_download_result_zocl::_unit_download_result_zocl(&Dst);
    v8 = 0;
    for ( k = 0; k < 4; ++k )
    {
      if ( v22->m_Param.m_UnitDB.m_List[k].byFrame != 255 )
      {
        Dst.UnitList[v8].bySlotIndex = k;
        memcpy_0(&Dst.UnitList[v8++].UnitData, (char *)&v22->m_Param.m_UnitDB + 62 * k, 0x3Eui64);
      }
    }
    Dst.byUnitNum = v8;
    pbyType = 3;
    v11 = 14;
    v3 = _unit_download_result_zocl::size(&Dst);
    CNetProcess::LoadSendMsg(unk_1414F2088, v22->m_ObjID.m_wIndex, &pbyType, &Dst.byUnitNum, v3);
  }
}
