/*
 * Function: ??0?$_Vector_const_iterator@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEAA@AEBV01@@Z
 * Address: 0x1403A7350
 */

void __fastcall std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v5; // [sp+30h] [bp+8h]@1
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *__thata; // [sp+38h] [bp+10h]@1

  __thata = __that;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Ranit<CMoveMapLimitInfo *,__int64,CMoveMapLimitInfo * const *,CMoveMapLimitInfo * const &>::_Ranit<CMoveMapLimitInfo *,__int64,CMoveMapLimitInfo * const *,CMoveMapLimitInfo * const &>(
    (std::_Ranit<CMoveMapLimitInfo *,__int64,CMoveMapLimitInfo * const *,CMoveMapLimitInfo * const &> *)&v5->_Mycont,
    (std::_Ranit<CMoveMapLimitInfo *,__int64,CMoveMapLimitInfo * const *,CMoveMapLimitInfo * const &> *)&__that->_Mycont);
  v5->_Myptr = __thata->_Myptr;
}
