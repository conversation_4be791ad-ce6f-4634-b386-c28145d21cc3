/*
 * Function: ?CompleteBuy@CUnmannedTraderController@@QEAAXEPEAD@Z
 * Address: 0x140207450
 */

void __fastcall CUnmannedTraderController::CompleteBuy(CUnmannedTraderController *this, char byRet, char *pLoadData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfoTable *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-38h]@1
  CUnmannedTraderTradeInfo *pkTaradInfo; // [sp+20h] [bp-18h]@4
  CUnmannedTraderController *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1
  char *pLoadDataa; // [sp+50h] [bp+18h]@1

  pLoadDataa = pLoadData;
  v9 = byRet;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pkTaradInfo = &v8->m_kTradeInfo;
  v5 = CUnmannedTraderUserInfoTable::Instance();
  CUnmannedTraderUserInfoTable::CompleteBuy(v5, v9, pLoadDataa, pkTaradInfo);
}
