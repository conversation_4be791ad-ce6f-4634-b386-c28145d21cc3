/*
 * Function: ??G?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@QEBA?AV01@_J@Z
 * Address: 0x1406039B0
 */

__int64 __fastcall std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-(__int64 a1, __int64 a2, __int64 a3)
{
  char v4; // [sp+20h] [bp-38h]@1
  int v5; // [sp+40h] [bp-18h]@1
  __int64 v6; // [sp+48h] [bp-10h]@1
  __int64 v7; // [sp+68h] [bp+10h]@1
  __int64 v8; // [sp+70h] [bp+18h]@1

  v8 = a3;
  v7 = a2;
  v6 = -2i64;
  v5 = 0;
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)&v4);
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-=(
    &v4,
    v8);
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>(v7);
  v5 |= 1u;
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return v7;
}
