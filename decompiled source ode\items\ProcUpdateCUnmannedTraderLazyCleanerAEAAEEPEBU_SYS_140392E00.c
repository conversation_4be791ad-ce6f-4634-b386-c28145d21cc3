/*
 * Function: ?ProcUpdate@CUnmannedTraderLazyCleaner@@AEAAEEPEBU_SYSTEMTIME@@PEA_N@Z
 * Address: 0x140392E00
 */

char __fastcall CUnmannedTraderLazyCleaner::ProcUpdate(CUnmannedTraderLazyCleaner *this, char byState, _SYSTEMTIME *pCurTime, bool *pbRemain)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v7; // [sp+0h] [bp-158h]@1
  unsigned int pdwSerial; // [sp+40h] [bp-118h]@7
  char v9; // [sp+44h] [bp-114h]@7
  unsigned __int16 v10; // [sp+124h] [bp-34h]@7
  unsigned __int16 v11; // [sp+134h] [bp-24h]@7
  char v12; // [sp+138h] [bp-20h]@7
  int j; // [sp+13Ch] [bp-1Ch]@8
  int v14; // [sp+140h] [bp-18h]@13
  _SYSTEMTIME *pkCurTime; // [sp+170h] [bp+18h]@1
  bool *v16; // [sp+178h] [bp+20h]@1

  v16 = pbRemain;
  pkCurTime = pCurTime;
  v4 = &v7;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( (signed int)(unsigned __int8)byState < 14 && pkDB )
  {
    pdwSerial = 0;
    memset(&v9, 0, 0xC4ui64);
    v10 = 0;
    v11 = 0;
    v12 = CRFWorldDatabase::Select_UnmannedTraderItemRecordCntByState(pkDB, 0, byState, &pdwSerial, 0x32u, &v10);
    if ( !v12 )
    {
      for ( j = 0; j < v10; ++j )
      {
        if ( CRFWorldDatabase::Update_UnmannedTraderSingleTypeClearUseCompleteRecord(pkDB, *(&pdwSerial + j), pkCurTime) )
          ++v11;
      }
    }
    v14 = v10 > (signed int)v11;
    *v16 = v14;
    result = v12;
  }
  else
  {
    result = 0;
  }
  return result;
}
