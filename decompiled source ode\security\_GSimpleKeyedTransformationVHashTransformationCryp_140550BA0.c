/*
 * Function: ??_G?$SimpleKeyedTransformation@VHashTransformation@CryptoPP@@@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x140550BA0
 */

CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation> *__fastcall CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>::`scalar deleting destructor'(CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation> *a1, int a2)
{
  CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation> *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>::~SimpleKeyedTransformation<CryptoPP::HashTransformation>(a1);
  if ( v4 & 1 )
    operator delete(v3);
  return v3;
}
