/*
 * Function: _CBossMonsterScheduleSystem::CBossMonsterScheduleSystem_::_1_::dtor$0
 * Address: 0x1404186A0
 */

void __fastcall CBossMonsterScheduleSystem::CBossMonsterScheduleSystem_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::~CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>(*(US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> > **)(a2 + 64));
}
