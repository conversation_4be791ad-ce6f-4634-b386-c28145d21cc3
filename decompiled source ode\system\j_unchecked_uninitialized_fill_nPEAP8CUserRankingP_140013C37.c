/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAP8CUserRankingProcess@@EAAXXZ_KP81@EAAXXZV?$allocator@P8CUserRankingProcess@@EAAXXZ@std@@@stdext@@YAXPEAP8CUserRankingProcess@@EAAXXZ_KAEBQ81@EAAXXZAEAV?$allocator@P8CUserRankingProcess@@EAAXXZ@std@@@Z
 * Address: 0x140013C37
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<void (CUserRankingProcess::**)(void),unsigned __int64,void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>(void (__cdecl **_First)(CUserRankingProcess *this), unsigned __int64 _Count, void (__cdecl *const *_Val)(CUserRankingProcess *this), std::allocator<void (__cdecl CUserRankingProcess::*)(void)> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<void (CUserRankingProcess::**)(void),unsigned __int64,void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>(
    _First,
    _Count,
    _Val,
    _Al);
}
