/*
 * Function: ?LastPut@HashVerificationFilter@CryptoPP@@MEAAXPEBE_K@Z
 * Address: 0x1405FD290
 */

void __fastcall CryptoPP::HashVerificationFilter::LastPut(CryptoPP::HashVerificationFilter *this, const unsigned __int8 *a2, __int64 a3)
{
  char *v3; // rax@4
  CryptoPP::BufferedTransformation *v4; // rax@10
  CryptoPP::BufferedTransformation *v5; // rax@12
  CryptoPP::HashVerificationFilter::HashVerificationFailed v6; // [sp+20h] [bp-78h]@15
  __int64 *v7; // [sp+70h] [bp-28h]@4
  __int64 v8; // [sp+78h] [bp-20h]@4
  int v9; // [sp+80h] [bp-18h]@7
  CryptoPP::HashVerificationFilter *v10; // [sp+A0h] [bp+8h]@1
  unsigned __int8 *v11; // [sp+A8h] [bp+10h]@1
  __int64 v12; // [sp+B0h] [bp+18h]@1

  v12 = a3;
  v11 = (unsigned __int8 *)a2;
  v10 = this;
  if ( this->m_flags & 1 )
  {
    if ( a3 )
      _wassert(L"length == 0", L"D:\\RF Project\\RF_Server64\\28 Crypto++\\filters.cpp", 0x30Au);
    v3 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&this->m_expectedHash);
    v7 = (__int64 *)v10->m_hashModule;
    v8 = *v7;
    v10->m_verified = (*(int (__fastcall **)(__int64 *, char *))(v8 + 96))(v7, v3);
  }
  else
  {
    v9 = a3 == ((int (__fastcall *)(CryptoPP::HashTransformation *))this->m_hashModule->vfptr[3].Clone)(this->m_hashModule)
      && (unsigned __int8)((int (__fastcall *)(CryptoPP::HashTransformation *, unsigned __int8 *))v10->m_hashModule->vfptr[6].__vecDelDtor)(
                            v10->m_hashModule,
                            v11);
    v10->m_verified = v9;
    if ( v10->m_flags & 4 )
    {
      LODWORD(v4) = ((int (__fastcall *)(CryptoPP::HashVerificationFilter *))v10->vfptr[20].Clone)(v10);
      CryptoPP::BufferedTransformation::Put(v4, v11, v12);
    }
  }
  if ( v10->m_flags & 8 )
  {
    LODWORD(v5) = ((int (__fastcall *)(CryptoPP::HashVerificationFilter *))v10->vfptr[20].Clone)(v10);
    CryptoPP::BufferedTransformation::Put(v5, v10->m_verified, 1);
  }
  if ( v10->m_flags & 0x10 && !v10->m_verified )
  {
    CryptoPP::HashVerificationFilter::HashVerificationFailed::HashVerificationFailed(&v6);
    CxxThrowException_0((__int64)&v6, (__int64)&TI3_AVHashVerificationFailed_HashVerificationFilter_CryptoPP__);
  }
}
