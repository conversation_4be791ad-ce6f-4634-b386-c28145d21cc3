/*
 * Function: ?AddRecord@CEventLootTable@@QEAAXPEAU_event_drop@1@@Z
 * Address: 0x140203E70
 */

void __fastcall CEventLootTable::AddRecord(CEventLootTable *this, CEventLootTable::_event_drop *pEventDrop)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CEventLootTable::_event_drop *j; // [sp+0h] [bp-18h]@1
  CEventLootTable *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v2 = (__int64 *)&j;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v5->m_pEventDropList )
  {
    for ( j = v5->m_pEventDropList; j; j = j->pNext )
    {
      if ( !j->pNext )
      {
        j->pNext = pEventDrop;
        return;
      }
    }
  }
  else
  {
    v5->m_pEventDropList = pEventDrop;
  }
}
