/*
 * Function: ?CalcSellPrice@CItemStore@@AEAAHHPEAE@Z
 * Address: 0x1402624B0
 */

__int64 __fastcall CItemStore::CalcSellPrice(CItemStore *this, int nGoodIndex, char *pbyMoneyUnit)
{
  __int64 result; // rax@3

  *pbyMoneyUnit = this->m_pStorageItem[nGoodIndex].byMoneyUnit;
  if ( this->m_pStorageItem[nGoodIndex].byMoneyUnit != 2 && this->m_pStorageItem[nGoodIndex].byMoneyUnit != 3 )
  {
    switch ( this->m_pStorageItem[nGoodIndex].byMoneyUnit )
    {
      case 4:
        result = this->m_pStorageItem[nGoodIndex].nResPoint;
        break;
      case 5:
        result = this->m_pStorageItem[nGoodIndex].nKillPoint;
        break;
      case 6:
        result = this->m_pStorageItem[nGoodIndex].nGoldPoint;
        break;
      default:
        result = this->m_pStorageItem[nGoodIndex].nStdPrice;
        break;
    }
  }
  else
  {
    result = this->m_pStorageItem[nGoodIndex].nStdPoint;
  }
  return result;
}
