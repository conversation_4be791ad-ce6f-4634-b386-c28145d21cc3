/*
 * Function: ?GetMapPos@CGuildRoomSystem@@QEAA_NKPEAMPEAVCMapData@@AEAGAEAE@Z
 * Address: 0x1402EA790
 */

bool __fastcall CGuildRoomSystem::GetMapPos(CGuildRoomSystem *this, unsigned int dwGuildSerial, float *pPos, CMapData *pMap, unsigned __int16 *wMapLayer, char *byRoomType)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomInfo *v8; // rax@7
  CGuildRoomInfo *v9; // rax@8
  CGuildRoomInfo *v10; // rax@9
  CGuildRoomInfo *v11; // rax@9
  CGuildRoomInfo *v12; // rax@9
  CGuildRoomInfo *v13; // rax@9
  __int64 v15; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CGuildRoomSystem *v17; // [sp+40h] [bp+8h]@1
  unsigned int v18; // [sp+48h] [bp+10h]@1
  float *pPosa; // [sp+50h] [bp+18h]@1

  pPosa = pPos;
  v18 = dwGuildSerial;
  v17 = this;
  v6 = &v15;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  for ( j = 0; j < 90; ++j )
  {
    v8 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v17->m_vecGuildRoom, j);
    if ( CGuildRoomInfo::IsRent(v8) )
    {
      v9 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v17->m_vecGuildRoom, j);
      if ( CGuildRoomInfo::GetGuildSerial(v9) == v18 )
      {
        v10 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v17->m_vecGuildRoom, j);
        CGuildRoomInfo::GetMapData(v10);
        v11 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v17->m_vecGuildRoom, j);
        *wMapLayer = CGuildRoomInfo::GetMapLayer(v11);
        v12 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v17->m_vecGuildRoom, j);
        *byRoomType = CGuildRoomInfo::GetRoomType(v12);
        v13 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v17->m_vecGuildRoom, j);
        return CGuildRoomInfo::GetMapPos(v13, pPosa);
      }
    }
  }
  return 0;
}
