/*
 * Function: ??$_Construct@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@U12@@std@@YAXPEAU?$pair@$$CBHPEAVCNationSettingFactory@@@0@AEBU10@@Z
 * Address: 0x140221A40
 */

void __fastcall std::_Construct<std::pair<int const,CNationSettingFactory *>,std::pair<int const,CNationSettingFactory *>>(std::pair<int const ,CNationSettingFactory *> *_Ptr, std::pair<int const ,CNationSettingFactory *> *_Val)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-68h]@1
  void *_Where; // [sp+20h] [bp-48h]@4
  char *v6; // [sp+28h] [bp-40h]@4
  char v7; // [sp+30h] [bp-38h]@5
  std::pair<int const ,CNationSettingFactory *> *v8; // [sp+70h] [bp+8h]@1
  std::pair<int const ,CNationSettingFactory *> *v9; // [sp+78h] [bp+10h]@1

  v9 = _Val;
  v8 = _Ptr;
  v2 = &v4;
  for ( i = 22i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _Where = v8;
  v6 = (char *)operator new(0x10ui64, v8);
  if ( v6 )
  {
    qmemcpy(&v7, v9, 0x10ui64);
    qmemcpy(v6, &v7, 0x10ui64);
  }
}
