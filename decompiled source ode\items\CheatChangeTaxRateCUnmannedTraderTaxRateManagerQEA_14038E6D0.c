/*
 * Function: ?CheatChangeTaxRate@CUnmannedTraderTaxRateManager@@QEAA_NEKPEAD@Z
 * Address: 0x14038E6D0
 */

char __fastcall CUnmannedTraderTaxRateManager::CheatChangeTaxRate(CUnmannedTraderTaxRateManager *this, char byRace, unsigned int dwNewTaxRate, char *pCheaterName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  TRC_AutoTrade **v7; // rax@7
  TRC_AutoTrade **v8; // rax@9
  __int64 v9; // [sp+0h] [bp-38h]@1
  float v10; // [sp+20h] [bp-18h]@7
  CUnmannedTraderTaxRateManager *v11; // [sp+40h] [bp+8h]@1
  char v12; // [sp+48h] [bp+10h]@1
  signed int dwProb; // [sp+50h] [bp+18h]@1
  char *pName; // [sp+58h] [bp+20h]@1

  pName = pCheaterName;
  dwProb = dwNewTaxRate;
  v12 = byRace;
  v11 = this;
  v4 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::empty(&v11->m_vecTRC)
    || std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::size(&v11->m_vecTRC) <= (unsigned __int8)v12 )
  {
    result = 0;
  }
  else
  {
    v10 = (float)dwProb / 100.0;
    v7 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](&v11->m_vecTRC, (unsigned __int8)v12);
    if ( TRC_AutoTrade::ChangeTaxRate(*v7, v10) )
    {
      result = 0;
    }
    else
    {
      v8 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](
             &v11->m_vecTRC,
             (unsigned __int8)v12);
      TRC_AutoTrade::history_used_cheet_changetaxrate(*v8, dwProb, pName);
      result = 1;
    }
  }
  return result;
}
