/*
 * Function: ?DigestSize@PK_MessageAccumulator@CryptoPP@@UEBAIXZ
 * Address: 0x140562E10
 */

void __fastcall __noreturn CryptoPP::PK_MessageAccumulator::DigestSize(CryptoPP::PK_MessageAccumulator *this)
{
  CryptoPP::NotImplemented v1; // [sp+20h] [bp-98h]@1
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+70h] [bp-48h]@1
  unsigned __int8 v3; // [sp+A0h] [bp-18h]@1
  __int64 v4; // [sp+A8h] [bp-10h]@1

  v4 = -2i64;
  memset(&v3, 0, sizeof(v3));
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
    &s,
    "PK_MessageAccumulator: DigestSize() should not be called",
    v3);
  CryptoPP::NotImplemented::NotImplemented(&v1, &s);
  CxxThrowException_0((__int64)&v1, (__int64)&TI3_AVNotImplemented_CryptoPP__);
}
