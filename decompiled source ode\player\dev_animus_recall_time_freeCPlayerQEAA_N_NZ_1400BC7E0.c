/*
 * Function: ?dev_animus_recall_time_free@CPlayer@@QEAA_N_N@Z
 * Address: 0x1400BC7E0
 */

char __fastcall CPlayer::dev_animus_recall_time_free(CPlayer *this, bool bFree)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@6
  char pbyType; // [sp+54h] [bp-24h]@6
  char v8; // [sp+55h] [bp-23h]@6
  CPlayer *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v9->m_bFreeRecallWaitTime == bFree )
  {
    result = 0;
  }
  else
  {
    v9->m_bFreeRecallWaitTime = bFree;
    szMsg = bFree;
    pbyType = 22;
    v8 = 13;
    CNetProcess::LoadSendMsg(unk_1414F2088, v9->m_ObjID.m_wIndex, &pbyType, &szMsg, 1u);
    result = 1;
  }
  return result;
}
