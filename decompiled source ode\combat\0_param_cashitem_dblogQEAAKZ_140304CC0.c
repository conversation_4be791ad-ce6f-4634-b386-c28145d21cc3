/*
 * Function: ??0_param_cashitem_dblog@@QEAA@K@Z
 * Address: 0x140304CC0
 */

void __fastcall _param_cashitem_dblog::_param_cashitem_dblog(_param_cashitem_dblog *this, unsigned int dwAv)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  _param_cashitem_dblog *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _param_cash::_param_cash((_param_cash *)&v5->in_dwAccountSerial, 0xFFFFFFFF, dwAv, 0xFFFFu);
  v5->nBuyNum = 0;
  memset_0(v5->data, 0, 0x140ui64);
}
