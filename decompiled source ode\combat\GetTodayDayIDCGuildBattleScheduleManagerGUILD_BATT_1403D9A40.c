/*
 * Function: ?GetTodayDayID@CGuildBattleScheduleManager@GUILD_BATTLE@@QEAAIXZ
 * Address: 0x1403D9A40
 */

unsigned int __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::GetTodayDayID(GUILD_BATTLE::CGuildBattleScheduleManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleScheduleManager *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::GetDayID(v5->m_pkTodaySchedule);
}
