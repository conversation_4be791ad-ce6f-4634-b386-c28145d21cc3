/*
 * Function: _CryptoPP::DL_EncryptorBase_CryptoPP::ECPPoint_::Encrypt_::_1_::dtor$4
 * Address: 0x140455640
 */

void __fastcall CryptoPP::DL_EncryptorBase_CryptoPP::ECPPoint_::Encrypt_::_1_::dtor_4(__int64 a1, __int64 a2)
{
  CryptoPP::Sec<PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~<PERSON><PERSON><PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>((CryptoPP::Se<PERSON><PERSON><PERSON><unsigned char,CryptoPP::Allocator<PERSON>ith<PERSON>leanup<unsigned char,0> > *)(a2 + 440));
}
