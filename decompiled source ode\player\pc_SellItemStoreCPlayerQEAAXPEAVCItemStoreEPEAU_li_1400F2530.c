/*
 * Function: ?pc_SellItemStore@CPlayer@@QEAAXPEAVCItemStore@@EPEAU_list@_sell_store_request_clzo@@H@Z
 * Address: 0x1400F2530
 */

void __usercall CPlayer::pc_SellItemStore(CPlayer *this@<rcx>, CItemStore *pStore@<rdx>, char by<PERSON><PERSON><PERSON><PERSON>@<r8b>, _sell_store_request_clzo::_list *pList@<r9>, float a5@<xmm0>, int bUseNPCLinkIntem)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  int v8; // eax@9
  char v9; // al@36
  int v10; // eax@38
  int v11; // eax@41
  int v12; // eax@44
  int v13; // eax@44
  int v14; // eax@44
  int v15; // eax@48
  int v16; // eax@48
  int v17; // eax@50
  int v18; // eax@54
  int v19; // eax@56
  CMoneySupplyMgr *v20; // rax@61
  __int64 v21; // [sp+0h] [bp-728h]@1
  char byRace[4]; // [sp+20h] [bp-708h]@47
  unsigned int dwIncomeGold[2]; // [sp+28h] [bp-700h]@47
  int j; // [sp+50h] [bp-6D8h]@15
  _sell_offer pOffer[100]; // [sp+70h] [bp-6B8h]@17
  char v26; // [sp+6B4h] [bp-74h]@4
  int k; // [sp+6B8h] [bp-70h]@23
  float v28; // [sp+6BCh] [bp-6Ch]@36
  int l; // [sp+6C0h] [bp-68h]@44
  char *v30; // [sp+6C8h] [bp-60h]@46
  int v31; // [sp+6D0h] [bp-58h]@57
  int v32; // [sp+6D4h] [bp-54h]@9
  float v33; // [sp+6D8h] [bp-50h]@36
  unsigned __int64 ui64HasMoney; // [sp+6E0h] [bp-48h]@38
  unsigned __int64 ui64HasGold; // [sp+6E8h] [bp-40h]@41
  char *v36; // [sp+6F0h] [bp-38h]@44
  unsigned int v37; // [sp+6F8h] [bp-30h]@44
  unsigned int v38; // [sp+6FCh] [bp-2Ch]@44
  unsigned int v39; // [sp+700h] [bp-28h]@44
  int nAdd; // [sp+704h] [bp-24h]@54
  int v41; // [sp+708h] [bp-20h]@56
  unsigned int nAmount; // [sp+70Ch] [bp-1Ch]@61
  char *szClass; // [sp+710h] [bp-18h]@61
  int nLv; // [sp+718h] [bp-10h]@61
  int v45; // [sp+71Ch] [bp-Ch]@61
  CPlayer *v46; // [sp+730h] [bp+8h]@1
  CItemStore *pStorea; // [sp+738h] [bp+10h]@1
  char v48; // [sp+740h] [bp+18h]@1
  _sell_store_request_clzo::_list *v49; // [sp+748h] [bp+20h]@1

  v49 = pList;
  v48 = byOfferNum;
  pStorea = pStore;
  v46 = this;
  v6 = &v21;
  for ( i = 456i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v26 = 0;
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v46->m_id.wIndex) == 99 )
  {
    v26 = 23;
  }
  else if ( bUseNPCLinkIntem || pStorea->m_pExistMap == v46->m_pCurMap )
  {
    v32 = pStorea->m_byNpcRaceCode;
    v8 = CPlayerDB::GetRaceCode(&v46->m_Param);
    if ( v32 == v8 || pStorea->m_byNpcRaceCode == 255 )
    {
      if ( bUseNPCLinkIntem || (GetSqrt(pStorea->m_pDum->m_pDumPos->m_fCenterPos, v46->m_fCurPos), a5 <= 100.0) )
      {
        for ( j = 0; j < (unsigned __int8)v48; ++j )
        {
          pOffer[j].pItem = _STORAGE_LIST::GetPtrFromSerial(
                              v46->m_Param.m_pStoragePtr[v49[j].byStorageCode],
                              v49[j].wSerial);
          if ( !pOffer[j].pItem )
          {
            v26 = 8;
            goto $RESULT_68;
          }
          if ( pOffer[j].pItem->m_byTableCode == 19 )
          {
            v26 = 9;
            goto $RESULT_68;
          }
          if ( pOffer[j].pItem->m_bLock )
          {
            v26 = 10;
            goto $RESULT_68;
          }
          for ( k = 0; k < j; ++k )
          {
            if ( pOffer[k].pItem == pOffer[j].pItem )
            {
              v26 = 11;
              goto $RESULT_68;
            }
          }
          if ( !IsSellItem(pOffer[j].pItem->m_byTableCode, pOffer[j].pItem->m_wItemIndex) )
          {
            v26 = 15;
            goto $RESULT_68;
          }
          pOffer[j].bySlotIndex = pOffer[j].pItem->m_byStorageIndex;
          pOffer[j].byStorageCode = v49[j].byStorageCode;
          if ( IsOverLapItem(pOffer[j].pItem->m_byTableCode) )
          {
            if ( v49[j].byAmount > pOffer[j].pItem->m_dwDur )
            {
              v26 = 12;
              goto $RESULT_68;
            }
            pOffer[j].byAmount = v49[j].byAmount;
          }
          else
          {
            pOffer[j].byAmount = 1;
          }
        }
        _effect_parameter::GetEff_Have(&v46->m_EP, 1);
        v33 = a5;
        _effect_parameter::GetEff_Have(&v46->m_EP, 10);
        v28 = v33 + a5;
        v9 = CPlayerDB::GetRaceCode(&v46->m_Param);
        v26 = CItemStore::IsBuy(pStorea, v48, pOffer, v28, v9);
        if ( !v26 )
        {
          if ( CItemStore::GetLastTradeDalant(pStorea) <= 0
            || (ui64HasMoney = CPlayerDB::GetDalant(&v46->m_Param),
                v10 = CItemStore::GetLastTradeDalant(pStorea),
                CanAddMoneyForMaxLimMoney(v10, ui64HasMoney)) )
          {
            if ( CItemStore::GetLastTradeGold(pStorea) > 0 )
            {
              ui64HasGold = CPlayerDB::GetGold(&v46->m_Param);
              v11 = CItemStore::GetLastTradeGold(pStorea);
              if ( !CanAddMoneyForMaxLimGold(v11, ui64HasGold) )
                v26 = 18;
            }
          }
          else
          {
            v26 = 18;
          }
        }
      }
      else
      {
        v26 = 3;
      }
    }
    else
    {
      v26 = 2;
    }
  }
  else
  {
    v26 = 1;
  }
$RESULT_68:
  if ( !v26 )
  {
    v12 = CItemStore::GetLastTradeDalant(pStorea);
    CPlayer::AddDalant(v46, v12, 1);
    v13 = CItemStore::GetLastTradeGold(pStorea);
    CPlayer::AddGold(v46, v13, 1);
    v36 = v46->m_szItemHistoryFileName;
    v37 = CPlayerDB::GetGold(&v46->m_Param);
    v38 = CPlayerDB::GetDalant(&v46->m_Param);
    v39 = CItemStore::GetLastTradeGold(pStorea);
    v14 = CItemStore::GetLastTradeDalant(pStorea);
    CMgrAvatorItemHistory::sell_item(
      &CPlayer::s_MgrItemHistory,
      v46->m_ObjID.m_wIndex,
      pOffer,
      v48,
      v14,
      v39,
      v38,
      v37,
      v36);
    for ( l = 0; l < (unsigned __int8)v48; ++l )
    {
      v30 = &v46->m_Param.m_pStoragePtr[pOffer[l].byStorageCode]->m_pStorageList[pOffer[l].bySlotIndex].m_bLoad;
      if ( IsOverLapItem(pOffer[l].pItem->m_byTableCode) )
      {
        v17 = -pOffer[l].byAmount;
        LOBYTE(dwIncomeGold[0]) = 0;
        byRace[0] = 1;
        CPlayer::Emb_AlterDurPoint(v46, pOffer[l].byStorageCode, pOffer[l].bySlotIndex, v17, 1, 0);
      }
      else
      {
        *(_QWORD *)dwIncomeGold = "CPlayer::pc_SellItemStore()";
        byRace[0] = 1;
        if ( !CPlayer::Emb_DelStorage(
                v46,
                pOffer[l].byStorageCode,
                pOffer[l].bySlotIndex,
                0,
                1,
                "CPlayer::pc_SellItemStore()") )
        {
          v15 = CItemStore::GetLastTradeDalant(pStorea);
          CPlayer::AddGold(v46, -v15, 1);
          v16 = CItemStore::GetLastTradeGold(pStorea);
          CPlayer::AddGold(v46, -v16, 1);
          CPlayer::SendMsg_SellItemStoreResult(v46, pStorea, -1);
          return;
        }
      }
    }
    if ( !v46->m_byUserDgr )
    {
      if ( CItemStore::GetLastTradeDalant(pStorea) > 0 )
      {
        nAdd = CItemStore::GetLastTradeDalant(pStorea);
        v18 = CPlayerDB::GetRaceCode(&v46->m_Param);
        eAddDalant(v18, nAdd);
      }
      if ( CItemStore::GetLastTradeGold(pStorea) > 0 )
      {
        v41 = CItemStore::GetLastTradeGold(pStorea);
        v19 = CPlayerDB::GetRaceCode(&v46->m_Param);
        eAddGold(v19, v41);
      }
    }
    v31 = CPlayerDB::GetLevel(&v46->m_Param);
    if ( v31 == 30 || v31 == 40 || v31 == 50 || v31 == 60 )
    {
      nAmount = CItemStore::GetLastTradeDalant(pStorea);
      szClass = CPlayerDB::GetPtrCurClass(&v46->m_Param)->m_strCode;
      nLv = CPlayerDB::GetLevel(&v46->m_Param);
      v45 = CPlayerDB::GetRaceCode(&v46->m_Param);
      v20 = CMoneySupplyMgr::Instance();
      CMoneySupplyMgr::UpdateSellData(v20, v45, nLv, szClass, nAmount);
    }
  }
  CPlayer::SendMsg_SellItemStoreResult(v46, pStorea, v26);
}
