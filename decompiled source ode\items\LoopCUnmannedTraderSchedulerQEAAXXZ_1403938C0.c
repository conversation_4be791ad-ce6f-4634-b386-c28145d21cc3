/*
 * Function: ?Loop@CUnmannedTraderScheduler@@QEAAXXZ
 * Address: 0x1403938C0
 */

void __fastcall CUnmannedTraderScheduler::Loop(CUnmannedTraderScheduler *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderSchedule *v3; // rax@11
  CUnmannedTraderSchedule *v4; // rax@12
  unsigned int v5; // eax@12
  CUnmannedTraderSchedule *v6; // rax@13
  unsigned int v7; // eax@13
  Player_TL_Status *v8; // rax@13
  CUnmannedTraderSchedule *v9; // rax@14
  unsigned int v10; // eax@14
  CUnmannedTraderSchedule *v11; // rax@15
  unsigned int v12; // eax@15
  Player_TL_Status *v13; // rax@15
  CUnmannedTraderSchedule *v14; // rax@16
  CUnmannedTraderSchedule *v15; // rax@17
  __int64 v16; // [sp+0h] [bp-68h]@1
  bool v17; // [sp+20h] [bp-48h]@8
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > result; // [sp+28h] [bp-40h]@8
  __int64 v19; // [sp+40h] [bp-28h]@4
  std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *_Right; // [sp+48h] [bp-20h]@8
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v21; // [sp+50h] [bp-18h]@8
  std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v22; // [sp+58h] [bp-10h]@8
  CUnmannedTraderScheduler *v23; // [sp+70h] [bp+8h]@1

  v23 = this;
  v1 = &v16;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v19 = -2i64;
  if ( v23->m_bLoad && v23->m_pkTimer && CMyTimer::CountingTimer(v23->m_pkTimer) )
  {
    CUnmannedTraderScheduler::DoDayChangedWork(v23);
    _Right = (std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)&v23->m_iterSchedule._Mycont;
    v21 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::end(
            &v23->m_veckSchdule,
            &result);
    v22 = (std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)v21;
    v17 = std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator==(
            (std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)&v21->_Mycont,
            _Right);
    std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(&result);
    if ( v17 )
    {
      CUnmannedTraderScheduler::PushLoad(v23);
    }
    else if ( CUnmannedTraderScheduler::FindWaitItem(v23) )
    {
      v3 = std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator*(&v23->m_iterSchedule);
      if ( CUnmannedTraderSchedule::IsDone(v3)
        || (v4 = std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator*(&v23->m_iterSchedule),
            v5 = CUnmannedTraderSchedule::GetOwnerSerial(v4),
            TimeLimitMgr::Find_Data(qword_1799CA2D0, v5))
        && (v6 = std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator*(&v23->m_iterSchedule),
            v7 = CUnmannedTraderSchedule::GetOwnerSerial(v6),
            v8 = TimeLimitMgr::Find_Data(qword_1799CA2D0, v7),
            Player_TL_Status::GetTLStatus(v8) == 99) )
      {
        v9 = std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator*(&v23->m_iterSchedule);
        v10 = CUnmannedTraderSchedule::GetOwnerSerial(v9);
        if ( TimeLimitMgr::Find_Data(qword_1799CA2D0, v10)
          && (v11 = std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator*(&v23->m_iterSchedule),
              v12 = CUnmannedTraderSchedule::GetOwnerSerial(v11),
              v13 = TimeLimitMgr::Find_Data(qword_1799CA2D0, v12),
              Player_TL_Status::GetTLStatus(v13) == 99) )
        {
          v14 = std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator*(&v23->m_iterSchedule);
          CUnmannedTraderSchedule::PushClear(v14, 1);
        }
        else
        {
          v15 = std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator*(&v23->m_iterSchedule);
          CUnmannedTraderSchedule::PushClear(v15, 0);
        }
        std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator++(&v23->m_iterSchedule);
      }
    }
  }
}
