/*
 * Function: ?CallProc_InsertCashItemLog@CRFCashItemDatabase@@QEAA_NKEPEAD0EK@Z
 * Address: 0x140483530
 */

bool __fastcall CRFCashItemDatabase::CallProc_InsertCashItemLog(CRFCashItemDatabase *this, unsigned int dwSerial, char byLv, char *szItemCode, char *szItemName, char byNum, unsigned int dwCost)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v10; // [sp+0h] [bp-4D8h]@1
  char *v11; // [sp+20h] [bp-4B8h]@4
  char *v12; // [sp+28h] [bp-4B0h]@4
  int v13; // [sp+30h] [bp-4A8h]@4
  unsigned int v14; // [sp+38h] [bp-4A0h]@4
  int v15; // [sp+40h] [bp-498h]@4
  int v16; // [sp+48h] [bp-490h]@4
  int v17; // [sp+50h] [bp-488h]@4
  int v18; // [sp+58h] [bp-480h]@4
  int v19; // [sp+60h] [bp-478h]@4
  int v20; // [sp+68h] [bp-470h]@4
  char _Dest[1024]; // [sp+80h] [bp-458h]@4
  _SYSTEMTIME SystemTime; // [sp+498h] [bp-40h]@4
  unsigned __int64 v23; // [sp+4C0h] [bp-18h]@4
  CRFCashItemDatabase *v24; // [sp+4E0h] [bp+8h]@1
  unsigned int v25; // [sp+4E8h] [bp+10h]@1
  char v26; // [sp+4F0h] [bp+18h]@1
  char *v27; // [sp+4F8h] [bp+20h]@1

  v27 = szItemCode;
  v26 = byLv;
  v25 = dwSerial;
  v24 = this;
  v7 = &v10;
  for ( i = 308i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v23 = (unsigned __int64)&v10 ^ _security_cookie;
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0x3FFui64);
  GetLocalTime(&SystemTime);
  v20 = SystemTime.wSecond;
  v19 = SystemTime.wMinute;
  v18 = SystemTime.wHour;
  v17 = SystemTime.wDay;
  v16 = SystemTime.wMonth;
  v15 = SystemTime.wYear;
  v14 = dwCost;
  v13 = (unsigned __int8)byNum;
  v12 = szItemName;
  v11 = v27;
  sprintf_s<1024>(
    (char (*)[1024])_Dest,
    "{ CALL Prc_RFONLINE_TestServer_Buy_Log(%u, %d, '%s', '%s', %d, %d, '%04d-%02d-%02d %02d:%02d:%02d') }",
    v25,
    (unsigned __int8)v26);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v24->vfptr, _Dest, 1);
}
