/*
 * Function: j_??A?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEAAAEAPEAVCMoveMapLimitRight@@_K@Z
 * Address: 0x140014029
 */

CMoveMapLimitRight **__fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator[](std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, unsigned __int64 _Pos)
{
  return std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator[](this, _Pos);
}
