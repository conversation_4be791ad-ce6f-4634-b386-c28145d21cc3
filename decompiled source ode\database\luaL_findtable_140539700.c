/*
 * Function: luaL_findtable
 * Address: 0x140539700
 */

const char *__fastcall luaL_findtable(__int64 a1, signed int a2, const char *a3, unsigned int a4)
{
  unsigned int v4; // er12@1
  const char *v5; // rbx@1
  __int64 v6; // rsi@1
  char *v7; // rdi@2
  unsigned int v8; // er8@5

  v4 = a4;
  v5 = a3;
  v6 = a1;
  lua_pushvalue(a1, a2);
  while ( 1 )
  {
    v7 = strchr(v5, 46);
    if ( !v7 )
      v7 = (char *)&v5[strlen(v5)];
    lua_pushlstring(v6, (__int64)v5, v7 - v5);
    lua_rawget(v6, -2);
    if ( !(unsigned int)lua_type(v6, -1) )
    {
      lua_settop(v6, -2);
      v8 = v4;
      if ( *v7 == 46 )
        v8 = 1;
      lua_createtable(v6, 0, v8);
      lua_pushlstring(v6, (__int64)v5, v7 - v5);
      lua_pushvalue(v6, -2);
      lua_settable(v6, -4);
      goto LABEL_9;
    }
    if ( (unsigned int)lua_type(v6, -1) != 5 )
      break;
LABEL_9:
    lua_remove(v6, -2);
    v5 = v7 + 1;
    if ( *v7 != 46 )
      return 0i64;
  }
  lua_settop(v6, -3);
  return v5;
}
