/*
 * Function: ?IsSQLValidString@@YA_NPEBD@Z
 * Address: 0x1400408A0
 */

bool __fastcall IsSQLValidString(const char *wszStr)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  char *v5; // [sp+20h] [bp-18h]@6
  char *Str; // [sp+40h] [bp+8h]@1

  Str = (char *)wszStr;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( Str )
  {
    v5 = 0i64;
    v5 = strchr(Str, 59);
    if ( v5 )
    {
      result = 0;
    }
    else
    {
      v5 = strchr(Str, 39);
      if ( v5 )
      {
        result = 0;
      }
      else
      {
        v5 = strstr(Str, "--");
        if ( v5 )
        {
          result = 0;
        }
        else
        {
          v5 = strstr(Str, "/*");
          if ( v5 )
          {
            result = 0;
          }
          else
          {
            v5 = strstr(Str, "*/");
            if ( v5 )
            {
              result = 0;
            }
            else
            {
              v5 = strstr(Str, "xp_");
              result = v5 == 0i64;
            }
          }
        }
      }
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
