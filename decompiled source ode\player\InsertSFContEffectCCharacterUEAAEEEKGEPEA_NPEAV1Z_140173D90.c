/*
 * Function: ?InsertSFContEffect@CCharacter@@UEAAEEEKGEPEA_NPEAV1@@Z
 * Address: 0x140173D90
 */

char __usercall CCharacter::InsertSFContEffect@<al>(CCharacter *this@<rcx>, char byContCode@<dl>, char byEffectCode@<r8b>, unsigned int dwEffectIndex@<r9d>, float a5@<xmm0>, unsigned __int16 wDurSec, char byLv, bool *pbUpMty, CCharacter *pActChar)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@9
  float v12; // xmm0_4@67
  CGameObjectVtbl *v13; // rax@84
  CGameObjectVtbl *v14; // rax@85
  CGameObjectVtbl *v15; // rax@89
  CGameObjectVtbl *v16; // rax@90
  __int64 v17; // [sp+0h] [bp-138h]@1
  char **pstrLinkCode; // [sp+20h] [bp-118h]@82
  unsigned int dwStartSec; // [sp+28h] [bp-110h]@82
  unsigned __int16 v20; // [sp+30h] [bp-108h]@82
  int nCumulCount; // [sp+38h] [bp-100h]@82
  int v22; // [sp+40h] [bp-F8h]@4
  int nEffectCount; // [sp+54h] [bp-E4h]@4
  int nCumulMax; // [sp+74h] [bp-C4h]@4
  char *szRecordCode; // [sp+98h] [bp-A0h]@4
  _base_fld *v26; // [sp+A8h] [bp-90h]@4
  bool v27; // [sp+B0h] [bp-88h]@4
  _base_fld *v28; // [sp+B8h] [bp-80h]@5
  unsigned int v29; // [sp+C0h] [bp-78h]@14
  int j; // [sp+C4h] [bp-74h]@14
  bool *v31; // [sp+C8h] [bp-70h]@17
  unsigned int v32; // [sp+D0h] [bp-68h]@25
  unsigned int v33; // [sp+D4h] [bp-64h]@25
  float v34; // [sp+D8h] [bp-60h]@25
  _sf_continous *pCont; // [sp+E0h] [bp-58h]@39
  unsigned __int8 v36; // [sp+E8h] [bp-50h]@39
  _sf_continous *v37; // [sp+F0h] [bp-48h]@41
  _sf_continous *v38; // [sp+F8h] [bp-40h]@53
  _base_fld *v39; // [sp+100h] [bp-38h]@56
  unsigned int v40; // [sp+108h] [bp-30h]@63
  unsigned int v41; // [sp+10Ch] [bp-2Ch]@63
  float v42; // [sp+110h] [bp-28h]@67
  unsigned __int16 v43; // [sp+114h] [bp-24h]@70
  int v44; // [sp+118h] [bp-20h]@71
  float v45; // [sp+11Ch] [bp-1Ch]@76
  _base_fld *v46; // [sp+120h] [bp-18h]@78
  int v47; // [sp+128h] [bp-10h]@79
  CCharacter *v48; // [sp+140h] [bp+8h]@1
  char v49; // [sp+148h] [bp+10h]@1
  char v50; // [sp+150h] [bp+18h]@1
  int n; // [sp+158h] [bp+20h]@1

  n = dwEffectIndex;
  v50 = byEffectCode;
  v49 = byContCode;
  v48 = this;
  v9 = &v17;
  for ( i = 76i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  *pbUpMty = 1;
  v22 = 1;
  nEffectCount = -1;
  nCumulMax = 1;
  szRecordCode = 0i64;
  v26 = 0i64;
  v27 = 0;
  if ( byContCode
    || (v28 = CRecordData::GetRecord(&stru_1799C8410 + 3, "17")) == 0i64
    || (_effect_parameter::GetEff_Plus(&v48->m_EP, 23), a5 <= 0.0)
    || v50 == 3
    || n == v28->m_dwIndex )
  {
    if ( !v50 )
    {
      v26 = CRecordData::GetRecord(&stru_1799C8410, n);
      if ( v26 )
      {
        if ( v26[1].m_dwIndex == 4 )
          v27 = 1;
      }
    }
    v29 = _sf_continous::GetSFContCurTime();
    for ( j = 0; j < 8; ++j )
    {
      v31 = 0i64;
      if ( v27 )
        v31 = &v48->m_SFContAura[(unsigned __int8)v49][j].m_bExist;
      else
        v31 = &v48->m_SFCont[(unsigned __int8)v49][j].m_bExist;
      if ( *v31 && v31[1] == v50 && *((_WORD *)v31 + 1) == n )
      {
        if ( v31[4] > (signed int)(unsigned __int8)byLv )
          return 11;
        v32 = v29 - *((_DWORD *)v31 + 2);
        v33 = *((_WORD *)v31 + 6) - v32;
        v34 = (float)(signed int)v33 / (float)wDurSec;
        if ( !v27 && v31[4] == byLv && v34 > 0.1 )
          return -13;
        if ( v27 && v26[1].m_dwIndex == 4 && !strncmp(v26[12].m_strCode, "-1", 2ui64) )
        {
          *((_DWORD *)v31 + 2) = v29;
          return 0;
        }
        if ( !_CheckCumulativeSF(v50, n, &nCumulMax, &nEffectCount, &szRecordCode)
          && (signed int)*((_WORD *)v31 + 6) > 0
          && (float)((float)(signed int)v33 / (float)wDurSec) > 0.1 )
        {
          return 12;
        }
        v22 = *((_DWORD *)v31 + 5) + 1;
        CCharacter::RemoveSFContEffect(v48, v49, j, 0, v27);
        break;
      }
    }
    pCont = 0i64;
    v36 = -1;
    for ( j = 0; j < 8; ++j )
    {
      v37 = 0i64;
      if ( v27 )
        v37 = &v48->m_SFContAura[(unsigned __int8)v49][j];
      else
        v37 = &v48->m_SFCont[(unsigned __int8)v49][j];
      if ( !v37->m_bExist )
      {
        pCont = v37;
        v36 = j;
        break;
      }
    }
    if ( !pCont )
    {
      if ( v27 )
        pCont = v48->m_SFContAura[(unsigned __int8)v49];
      else
        pCont = v48->m_SFCont[(unsigned __int8)v49];
      v36 = 0;
      for ( j = 1; j < 8; ++j )
      {
        v38 = 0i64;
        if ( v27 )
          v38 = &v48->m_SFContAura[(unsigned __int8)v49][j];
        else
          v38 = &v48->m_SFCont[(unsigned __int8)v49][j];
        v39 = CRecordData::GetRecord(&stru_1799C8410 + 3, "17");
        if ( v39 && pCont->m_byEffectCode == 3 && pCont->m_wEffectIndex == v39->m_dwIndex )
        {
          pCont = &v48->m_SFCont[(unsigned __int8)v49][j];
          v36 = j;
        }
        if ( v38->m_dwEffSerial < pCont->m_dwEffSerial )
        {
          pCont = v38;
          v36 = j;
        }
      }
      v40 = v29 - pCont->m_dwStartSec;
      v41 = pCont->m_wDurSec - v40;
      if ( (signed int)pCont->m_wDurSec > 0 && (float)((float)(signed int)v41 / (float)wDurSec) > 0.1 )
        *pbUpMty = 0;
      CCharacter::RemoveSFContEffect(v48, v49, v36, 0, v27);
    }
    v12 = FLOAT_1_0;
    v42 = FLOAT_1_0;
    if ( !v49 && v50 == 1 )
    {
      _effect_parameter::GetEff_Rate(&v48->m_EP, 8);
      v42 = FLOAT_1_0;
    }
    v43 = wDurSec;
    if ( v48->m_nContEffectSec == -1 )
    {
      v12 = (float)v43 * v42;
      v43 = (signed int)ffloor(v12);
      v44 = 2 * wDurSec;
      if ( v43 > v44 )
        v43 = v44;
    }
    else
    {
      v43 = v48->m_nContEffectSec;
    }
    if ( !v49 )
    {
      _effect_parameter::GetEff_Have(&v48->m_EP, 77);
      v45 = v12;
      v43 = (signed int)ffloor((float)v43 * (float)(1.0 - v12));
    }
    if ( nEffectCount == v22 )
    {
      v46 = CRecordData::GetRecord(&stru_1799C8410 + (unsigned __int8)v50, szRecordCode);
      if ( v46 )
      {
        v47 = 1;
        if ( v50 == 1 )
          v43 = *(_WORD *)&v46[15].m_strCode[32];
        else
          v43 = *(_WORD *)&v46[16].m_strCode[24];
        nCumulCount = 0;
        v20 = v43;
        dwStartSec = v29;
        LOBYTE(pstrLinkCode) = 1;
        CCharacter::_set_sf_cont(v48, pCont, v50, v46->m_dwIndex, 1, v29, v43, 0);
        if ( !pActChar || pActChar->m_ObjID.m_byID )
        {
          v14 = v48->vfptr;
          pstrLinkCode = 0i64;
          ((void (__fastcall *)(CCharacter *, _QWORD, _QWORD, _QWORD))v14->SFContInsertMessage)(
            v48,
            (unsigned __int8)v49,
            v36,
            v27);
        }
        else
        {
          v13 = v48->vfptr;
          pstrLinkCode = (char **)pActChar;
          ((void (__fastcall *)(CCharacter *, _QWORD, _QWORD, _QWORD))v13->SFContInsertMessage)(
            v48,
            (unsigned __int8)v49,
            v36,
            v27);
        }
      }
    }
    else
    {
      CCharacter::_set_sf_cont(v48, pCont, v50, n, byLv, v29, v43, v22);
      if ( !pActChar || pActChar->m_ObjID.m_byID )
      {
        v16 = v48->vfptr;
        pstrLinkCode = 0i64;
        ((void (__fastcall *)(CCharacter *, _QWORD, _QWORD, _QWORD))v16->SFContInsertMessage)(
          v48,
          (unsigned __int8)v49,
          v36,
          v27);
      }
      else
      {
        v15 = v48->vfptr;
        pstrLinkCode = (char **)pActChar;
        ((void (__fastcall *)(CCharacter *, _QWORD, _QWORD, _QWORD))v15->SFContInsertMessage)(
          v48,
          (unsigned __int8)v49,
          v36,
          v27);
      }
    }
    v48->m_bLastContEffectUpdate = 1;
    result = 0;
  }
  else
  {
    result = 10;
  }
  return result;
}
