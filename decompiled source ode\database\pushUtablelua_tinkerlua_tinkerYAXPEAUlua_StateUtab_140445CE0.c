/*
 * Function: ??$push@Utable@lua_tinker@@@lua_tinker@@YAXPEAUlua_State@@Utable@0@@Z
 * Address: 0x140445CE0
 */

void __fastcall lua_tinker::push<lua_tinker::table>(struct lua_State *L, lua_tinker::table ret)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  struct lua_State *v5; // [sp+30h] [bp+8h]@1
  lua_tinker::table *v6; // [sp+38h] [bp+10h]@1

  v6 = (lua_tinker::table *)ret.m_obj;
  v5 = L;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  lua_pushvalue(v5, *((_DWORD *)ret.m_obj->m_L + 2));
  lua_tinker::table::~table(v6);
}
