/*
 * Function: ?SendTargetPlayerDamageContInfo@CPlayer@@QEAAXXZ
 * Address: 0x1400E5740
 */

void __fastcall CPlayer::SendTargetPlayerDamageContInfo(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@14
  __int64 v4; // [sp+0h] [bp-B8h]@1
  CGameObject *v5; // [sp+30h] [bp-88h]@4
  CGameObject *v6; // [sp+38h] [bp-80h]@8
  _target_player_damage_contsf_allinform_zocl src1; // [sp+48h] [bp-70h]@8
  char v8; // [sp+74h] [bp-44h]@8
  int j; // [sp+78h] [bp-40h]@8
  CGameObject *v10; // [sp+80h] [bp-38h]@10
  char pbyType; // [sp+94h] [bp-24h]@14
  char v12; // [sp+95h] [bp-23h]@14
  CPlayer *v13; // [sp+C0h] [bp+8h]@1

  v13 = this;
  v1 = &v4;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = CPlayer::GetTargetObj(v13);
  if ( v5 && !v5->m_ObjID.m_byKind && !v5->m_ObjID.m_byID )
  {
    v6 = v5;
    _target_player_damage_contsf_allinform_zocl::_target_player_damage_contsf_allinform_zocl(&src1);
    src1.dwSerial = v6->m_dwObjSerial;
    v8 = 0;
    for ( j = 0; j < 8; ++j )
    {
      v10 = (CGameObject *)((char *)v6 + 48 * j + 252);
      if ( LOBYTE(v10->vfptr) )
      {
        src1.m_PlayerContSf[(unsigned __int8)v8].wSfcode = CCharacter::CalcEffectBit(
                                                             (CCharacter *)&v13->vfptr,
                                                             BYTE1(v10->vfptr),
                                                             WORD1(v10->vfptr));
        src1.m_PlayerContSf[(unsigned __int8)v8++].byContCount = v10->m_dwObjSerial;
        src1.byContCount = v8;
      }
    }
    if ( !_target_player_damage_contsf_allinform_zocl::IsSame(
            &src1,
            &v13->m_TargetObject.m_PrevTargetPlayerDamageContInfo) )
    {
      pbyType = 13;
      v12 = 112;
      v3 = _target_player_damage_contsf_allinform_zocl::size(&src1);
      CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, (char *)&src1, v3);
      memcpy_0(&v13->m_TargetObject.m_PrevTargetPlayerDamageContInfo, &src1, 0x1Dui64);
    }
  }
}
