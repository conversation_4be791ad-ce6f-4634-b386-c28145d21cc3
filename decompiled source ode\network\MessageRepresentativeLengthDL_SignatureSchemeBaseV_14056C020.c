/*
 * Function: ?MessageRepresentativeLength@?$DL_SignatureSchemeBase@VPK_Signer@CryptoPP@@V?$DL_PrivateKey@UECPPoint@CryptoPP@@@2@@CryptoPP@@IEBA_KXZ
 * Address: 0x14056C020
 */

unsigned __int64 __fastcall CryptoPP::DL_SignatureSchemeBase<CryptoPP::P<PERSON>_Signer,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::MessageRepresentativeLength(__int64 a1)
{
  CryptoPP *v1; // rax@1

  LODWORD(v1) = CryptoPP::DL_SignatureSchemeBase<CryptoPP::P<PERSON>_Signer,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::MessageRepresentativeBitLength(a1);
  return CryptoPP::BitsToBytes(v1);
}
