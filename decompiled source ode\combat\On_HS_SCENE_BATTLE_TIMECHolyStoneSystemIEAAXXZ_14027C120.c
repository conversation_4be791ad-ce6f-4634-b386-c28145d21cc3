/*
 * Function: ?On_HS_SCENE_BATTLE_TIME@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x14027C120
 */

void __fastcall CHolyStoneSystem::On_HS_SCENE_BATTLE_TIME(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CMoveMapLimitManager *v3; // rax@4
  CGoldenBoxItemMgr *v4; // rax@4
  unsigned __int16 v5; // ax@6
  __int64 v6; // [sp+0h] [bp-108h]@1
  bool bDate[4]; // [sp+20h] [bp-E8h]@6
  bool bAddCount[4]; // [sp+28h] [bp-E0h]@6
  int v9; // [sp+30h] [bp-D8h]@6
  int v10; // [sp+38h] [bp-D0h]@6
  char _Dest[128]; // [sp+50h] [bp-B8h]@6
  int v12; // [sp+E0h] [bp-28h]@6
  int v13; // [sp+E4h] [bp-24h]@6
  int v14; // [sp+E8h] [bp-20h]@6
  int v15; // [sp+ECh] [bp-1Ch]@6
  unsigned __int64 v16; // [sp+F0h] [bp-18h]@4
  CHolyStoneSystem *v17; // [sp+110h] [bp+8h]@1

  v17 = this;
  v1 = &v6;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v16 = (unsigned __int64)&v6 ^ _security_cookie;
  CHolyKeeper::Destroy(g_Keeper, 1, 0i64);
  v17->m_dwNextStartTime = 0;
  v17->m_pkDestroyer = 0i64;
  v17->m_SaveData.m_nHolyMasterRace = -1;
  v17->m_SaveData.m_nDestroyStoneRace = -1;
  v17->m_SaveData.m_dwDestroyerSerial = -1;
  v17->m_SaveData.m_eDestroyerState = 3;
  v17->m_SaveData.m_wStartYear = GetCurrentYear();
  v17->m_SaveData.m_byStartMonth = GetCurrentMonth();
  v17->m_SaveData.m_byStartDay = GetCurrentDay();
  v17->m_SaveData.m_byStartHour = GetCurrentHour();
  v17->m_SaveData.m_byStartMin = GetCurrentMin();
  CHolyStoneSystem::ReleaseLastAttBuff(v17);
  v17->m_SaveData.m_dwDestroyerGuildSerial = -1;
  CHolyStoneSystem::CreateHolyStone(v17);
  CHolyStoneSystem::SendMsg_EnterStone(v17, -1);
  CHolyStoneSystem::SendMsg_StartBattle(v17);
  CHolyStoneSystem::GiveHSKQuest(v17);
  CHolyStoneSystem::SendHolyStoneHPToRaceBoss(v17);
  v3 = CMoveMapLimitManager::Instance();
  CMoveMapLimitManager::RequestElanMapUserForceMoveHQ(v3);
  v4 = CGoldenBoxItemMgr::Instance();
  if ( CGoldenBoxItemMgr::Get_Event_Status(v4) == 2 )
    CHolyStoneSystem::SetGoldBoxConsumable(v17, 1);
  v12 = (unsigned __int8)CHolyStoneSystem::GetStartMin(v17);
  v13 = (unsigned __int8)CHolyStoneSystem::GetStartHour(v17);
  v14 = (unsigned __int8)CHolyStoneSystem::GetStartDay(v17);
  v15 = (unsigned __int8)CHolyStoneSystem::GetStartMonth(v17);
  v5 = CHolyStoneSystem::GetStartYear(v17);
  v10 = v12;
  v9 = v13;
  *(_DWORD *)bAddCount = v14;
  *(_DWORD *)bDate = v15;
  sprintf_s<128>(
    (char (*)[128])_Dest,
    "..\\ZoneServerLog\\ServiceLog\\%s_HolyInfo_%d_%d_%d_%d_%d.txt",
    byte_1799C5B78,
    v5);
  CLogFile::SetWriteLogFile(&v17->m_logPer10Min, _Dest, 1, 0, 0, 0);
}
