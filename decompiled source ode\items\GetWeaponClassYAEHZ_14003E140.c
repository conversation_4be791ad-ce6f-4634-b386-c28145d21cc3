/*
 * Function: ?GetWeaponClass@@YAEH@Z
 * Address: 0x14003E140
 */

char __fastcall GetWeaponClass(int nItemIndex)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  CRecordData *v5; // [sp+20h] [bp-28h]@4
  _base_fld *v6; // [sp+28h] [bp-20h]@4
  int v7; // [sp+30h] [bp-18h]@4
  int n; // [sp+50h] [bp+8h]@1

  n = nItemIndex;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = s_ptblItemData + 6;
  v6 = CRecordData::GetRecord(s_ptblItemData + 6, n);
  v7 = *(_DWORD *)&v6[6].m_strCode[8];
  switch ( v7 )
  {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 9:
      result = 0;
      break;
    case 5:
    case 6:
    case 7:
    case 8:
    case 11:
      result = 1;
      break;
    default:
      result = 0;
      break;
  }
  return result;
}
