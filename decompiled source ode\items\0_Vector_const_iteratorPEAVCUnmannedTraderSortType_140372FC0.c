/*
 * Function: ??0?$_Vector_const_iterator@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEAA@PEAPEAVCUnmannedTraderSortType@@@Z
 * Address: 0x140372FC0
 */

void __fastcall std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, CUnmannedTraderSortType **_Ptr)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v5; // [sp+30h] [bp+8h]@1
  CUnmannedTraderSortType **v6; // [sp+38h] [bp+10h]@1

  v6 = _Ptr;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Ranit<CUnmannedTraderSortType *,__int64,CUnmannedTraderSortType * const *,CUnmannedTraderSortType * const &>::_Ranit<CUnmannedTraderSortType *,__int64,CUnmannedTraderSortType * const *,CUnmannedTraderSortType * const &>((std::_Ranit<CUnmannedTraderSortType *,__int64,CUnmannedTraderSortType * const *,CUnmannedTraderSortType * const &> *)&v5->_Mycont);
  v5->_Myptr = v6;
}
