/*
 * Function: ?GetConnectTime_AddBySec@@YAKH@Z
 * Address: 0x14043CB80
 */

__int64 __fastcall GetConnectTime_AddBySec(int iSec)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-F8h]@1
  int v5; // [sp+20h] [bp-D8h]@4
  int v6; // [sp+28h] [bp-D0h]@4
  int v7; // [sp+30h] [bp-C8h]@4
  int Dst; // [sp+44h] [bp-B4h]@4
  struct tm Tm; // [sp+68h] [bp-90h]@4
  char Dest; // [sp+A8h] [bp-50h]@4
  int v11; // [sp+D4h] [bp-24h]@4
  unsigned __int64 v12; // [sp+E0h] [bp-18h]@4
  int v13; // [sp+100h] [bp+8h]@1

  v13 = iSec;
  v1 = &v4;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = (unsigned __int64)&v4 ^ _security_cookie;
  memset_0(&Dst, 0, 4ui64);
  memset_0(&Tm, 0, 0x24ui64);
  _time32(&Dst);
  Dst += v13;
  _localtime32_s(&Tm, &Dst);
  memset_0(&Dest, 0, 0x20ui64);
  v7 = Tm.tm_min;
  v6 = Tm.tm_hour;
  v5 = Tm.tm_mday;
  sprintf(&Dest, "%01d%02d%02d%02d%02d", (unsigned int)(Tm.tm_year - 100), (unsigned int)(Tm.tm_mon + 1));
  v11 = 0;
  return (unsigned int)atol(&Dest);
}
