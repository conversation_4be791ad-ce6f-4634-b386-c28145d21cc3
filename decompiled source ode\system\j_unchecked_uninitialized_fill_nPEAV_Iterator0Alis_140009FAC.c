/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAV?$_Iterator@$0A@@?$list@U?$pair@QEAUScheduleMSG@@K@std@@V?$allocator@U?$pair@QEAUSchedule<PERSON><PERSON>@@K@std@@@2@@std@@_KV123@V?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@QEAUScheduleMSG@@K@std@@V?$allocator@U?$pair@QEAUScheduleMSG@@K@std@@@2@@std@@@3@@stdext@@YAXPEAV?$_Iterator@$0A@@?$list@U?$pair@QEAUScheduleMSG@@K@std@@V?$allocator@U?$pair@QEAUScheduleMSG@@K@std@@@2@@std@@_KAEBV123@AEAV?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@QEAUSchedule<PERSON>G@@K@std@@V?$allocator@U?$pair@QEAUScheduleMSG@@K@std@@@2@@std@@@3@@Z
 * Address: 0x140009FAC
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0> *,unsigned __int64,std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>(std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *_First, unsigned __int64 _Count, std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *_Val, std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> > *_Al)
{
  stdext::unchecked_uninitialized_fill_n<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0> *,unsigned __int64,std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>(
    _First,
    _Count,
    _Val,
    _Al);
}
