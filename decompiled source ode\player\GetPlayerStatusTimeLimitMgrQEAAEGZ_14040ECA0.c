/*
 * Function: ?GetPlayerStatus@TimeLimitMgr@@QEAAEG@Z
 * Address: 0x14040ECA0
 */

char __fastcall TimeLimitMgr::GetPlayerStatus(TimeLimitMgr *this, unsigned __int16 wIndex)
{
  char result; // al@2

  if ( this->m_wEnable )
  {
    if ( this->m_lstTLStaus[wIndex].m_bUse )
    {
      if ( this->m_lstTLStaus[wIndex].m_bAgeLimit )
        result = this->m_lstTLStaus[wIndex].m_byTL_Status;
      else
        result = 0;
    }
    else
    {
      result = 99;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
