/*
 * Function: ?GetGuild@CNormalGuildBattle@GUILD_BATTLE@@QEAAPEAVCNormalGuildBattleGuild@2@K@Z
 * Address: 0x1403E3AB0
 */

GUILD_BATTLE::CNormalGuildBattleGuild *__fastcall GUILD_BATTLE::CNormalGuildBattle::GetGuild(GUILD_BATTLE::CNormalGuildBattle *this, unsigned int dwGuildSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleGuild *result; // rax@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattle *v6; // [sp+30h] [bp+8h]@1
  unsigned int v7; // [sp+38h] [bp+10h]@1

  v7 = dwGuildSerial;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( dwGuildSerial == GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v6->m_k1P) )
  {
    result = &v6->m_k1P;
  }
  else if ( v7 == GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v6->m_k2P) )
  {
    result = &v6->m_k2P;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
