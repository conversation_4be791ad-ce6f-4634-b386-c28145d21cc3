/*
 * Function: ?D3DUtil_InitLight@@YAXAEAU_D3DLIGHT8@@W4_D3DLIGHTTYPE@@MMM@Z
 * Address: 0x14052B290
 */

void __fastcall D3DUtil_InitLight(struct _D3DLIGHT8 *a1, enum _D3DLIGHTTYPE a2, float a3, float a4, float a5)
{
  enum _D3DLIGHTTYPE v5; // ebx@1
  struct _D3DLIGHT8 *v6; // rdi@1
  float v7; // [sp+20h] [bp-48h]@1
  float v8; // [sp+24h] [bp-44h]@1
  float v9; // [sp+28h] [bp-40h]@1

  v5 = a2;
  v6 = a1;
  memset_0(a1, 0, 0x68ui64);
  v7 = a3;
  v8 = a4;
  v6->Type = v5;
  LODWORD(v6->Diffuse.r) = 1065353216;
  LODWORD(v6->Diffuse.g) = 1065353216;
  v9 = a5;
  LODWORD(v6->Diffuse.b) = 1065353216;
  D3DXVec3Normalize_0(&v6->Direction, &v7);
  v6->Position.x = a3;
  v6->Position.y = a4;
  v6->Position.z = a5;
  LODWORD(v6->Range) = 1148846080;
}
