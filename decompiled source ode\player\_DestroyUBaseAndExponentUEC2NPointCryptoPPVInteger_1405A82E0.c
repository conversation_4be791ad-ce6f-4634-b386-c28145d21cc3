/*
 * Function: ??$_Destroy@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@YAXPEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@Z
 * Address: 0x1405A82E0
 */

int __fastcall std::_Destroy<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>(__int64 a1)
{
  return CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::`scalar deleting destructor'(a1, 0i64);
}
