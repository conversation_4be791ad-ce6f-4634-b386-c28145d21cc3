/*
 * Function: ?Select_PvpRate@CRFWorldDatabase@@QEAAEKPEADPEAKPEAG@Z
 * Address: 0x1404971A0
 */

char __fastcall CRFWorldDatabase::Select_PvpRate(CRFWorldDatabase *this, unsigned int dwSerial, char *szDate, unsigned int *pdwRank, unsigned __int16 *pwRankRate)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v8; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@22
  SQLLEN v11; // [sp+38h] [bp-150h]@22
  __int16 v12; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  char v14; // [sp+61h] [bp-127h]@4
  char v15; // [sp+164h] [bp-24h]@16
  unsigned __int64 v16; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v17; // [sp+190h] [bp+8h]@1
  unsigned int *TargetValue; // [sp+1A8h] [bp+20h]@1

  TargetValue = pdwRank;
  v17 = this;
  v5 = &v8;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v16 = (unsigned __int64)&v8 ^ _security_cookie;
  Dest = 0;
  memset(&v14, 0, 0xFFui64);
  sprintf(&Dest, "select Rank, Rate from tbl_PvpRank%s where serial=%d", szDate);
  if ( v17->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v17->vfptr, &Dest);
  if ( v17->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v17->vfptr) )
  {
    v12 = SQLExecDirectA_0(v17->m_hStmtSelect, &Dest, -3);
    if ( v12 && v12 != 1 )
    {
      if ( v12 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v17->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v12, &Dest, "SQLExecDirectA", SQLStmt);
        result = 1;
      }
    }
    else
    {
      v12 = SQLFetch_0(v17->m_hStmtSelect);
      if ( v12 && v12 != 1 )
      {
        v15 = 0;
        if ( v12 == 100 )
        {
          v15 = 2;
        }
        else
        {
          SQLStmt = v17->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v12, &Dest, "SQLFetch", SQLStmt);
          v15 = 1;
        }
        if ( v17->m_hStmtSelect )
          SQLCloseCursor_0(v17->m_hStmtSelect);
        result = v15;
      }
      else
      {
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v17->m_hStmtSelect, 1u, 4, TargetValue, 0i64, &v11);
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v17->m_hStmtSelect, 2u, -17, pwRankRate, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          SQLStmt = v17->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v12, &Dest, "SQLGetData", SQLStmt);
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          result = 1;
        }
        else
        {
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          if ( v17->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v17->vfptr, "%s Success", &Dest);
          result = 0;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v17->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
