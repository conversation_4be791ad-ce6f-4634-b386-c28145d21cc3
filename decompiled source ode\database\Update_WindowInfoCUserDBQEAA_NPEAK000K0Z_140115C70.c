/*
 * Function: ?Update_WindowInfo@CUserDB@@QEAA_NPEAK000K0@Z
 * Address: 0x140115C70
 */

char __fastcall CUserDB::Update_WindowInfo(CUserDB *this, unsigned int *pdwSkill, unsigned int *pdwForce, unsigned int *pdwChar, unsigned int *pdwAnimus, unsigned int dwInven, unsigned int *pdwInvenBag)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v10; // [sp+0h] [bp-28h]@1
  CUserDB *v11; // [sp+30h] [bp+8h]@1
  unsigned int *v12; // [sp+40h] [bp+18h]@1
  unsigned int *v13; // [sp+48h] [bp+20h]@1

  v13 = pdwChar;
  v12 = pdwForce;
  v11 = this;
  v7 = &v10;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  memcpy_0(v11->m_AvatorData.dbLink.m_dwSkill, pdwSkill, 8ui64);
  memcpy_0(v11->m_AvatorData.dbLink.m_dwForce, v12, 8ui64);
  memcpy_0(v11->m_AvatorData.dbLink.m_dwCharacter, v13, 8ui64);
  memcpy_0(v11->m_AvatorData.dbLink.m_dwAnimus, pdwAnimus, 8ui64);
  v11->m_AvatorData.dbLink.m_dwInven = dwInven;
  memcpy_0(v11->m_AvatorData.dbLink.m_dwInvenBag, pdwInvenBag, 0x14ui64);
  return 1;
}
