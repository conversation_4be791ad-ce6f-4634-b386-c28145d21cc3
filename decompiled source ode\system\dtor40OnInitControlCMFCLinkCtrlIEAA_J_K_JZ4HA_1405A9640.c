/*
 * Function: ?dtor$4@?0??OnInitControl@CMFCLinkCtrl@@IEAA_J_K_J@Z@4HA
 * Address: 0x1405A9640
 */

int `CMFCLinkCtrl::OnInitControl'::`1'::dtor$4()
{
  return std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>();
}
