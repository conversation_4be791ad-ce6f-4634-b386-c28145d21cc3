/*
 * Function: ?SumMinuteOne@TimeLimitMgr@@QEAAKPEAU_SYSTEMTIME@@@Z
 * Address: 0x14040DF50
 */

__int64 __fastcall TimeLimitMgr::SumMinuteOne(TimeLimitMgr *this, _SYSTEMTIME *tm)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // [sp+0h] [bp-18h]@1
  unsigned int v6; // [sp+4h] [bp-14h]@4
  int j; // [sp+8h] [bp-10h]@4

  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  v5 = 0;
  v6 = 0;
  for ( j = 0; j < tm->wMonth - 1; ++j )
  {
    if ( tm->wMonth == 2 )
    {
      if ( tm->wYear & 3 && tm->wYear % 100 || tm->wYear % 400 )
        v5 = 29;
      else
        v5 = 28;
    }
    else if ( tm->wMonth != 4 && tm->wMonth != 6 && tm->wMonth != 9 && tm->wMonth != 11 )
    {
      v5 = 31;
    }
    else
    {
      v5 = 30;
    }
    v6 += v5;
  }
  v6 += tm->wDay;
  v6 *= 1440;
  v6 += 60 * tm->wHour + tm->wMinute;
  return v6;
}
