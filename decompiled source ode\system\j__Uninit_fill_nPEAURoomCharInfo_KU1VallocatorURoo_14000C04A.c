/*
 * Function: j_??$_Uninit_fill_n@PEAURoomCharInfo@@_KU1@V?$allocator@URoomCharInfo@@@std@@@std@@YAXPEAURoomCharInfo@@_KAEBU1@AEAV?$allocator@URoomCharInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000C04A
 */

void __fastcall std::_Uninit_fill_n<RoomCharInfo *,unsigned __int64,RoomCharInfo,std::allocator<RoomCharInfo>>(RoomCharInfo *_First, unsigned __int64 _Count, RoomCharInfo *_Val, std::allocator<RoomCharInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<RoomCharInfo *,unsigned __int64,RoomCharInfo,std::allocator<RoomCharInfo>>(
    _First,
    _Count,
    _<PERSON>,
    _<PERSON>,
    __formal,
    a6);
}
