/*
 * Function: _std::_Uninit_copy_CUnmannedTraderGroupDivisionVersionInfo_____ptr64_CUnmannedTraderGroupDivisionVersionInfo_____ptr64_std::allocator_CUnmannedTraderGroupDivisionVersionInfo____::_1_::catch$0
 * Address: 0x14039C5C0
 */

void __fastcall __noreturn std::_Uninit_copy_CUnmannedTraderGroupDivisionVersionInfo_____ptr64_CUnmannedTraderGroupDivisionVersionInfo_____ptr64_std::allocator_CUnmannedTraderGroupDivisionVersionInfo____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 80); *(_QWORD *)(i + 32) += 48i64 )
    std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::destroy(
      *(std::allocator<CUnmannedTraderGroupDivisionVersionInfo> **)(i + 88),
      *(CUnmannedTraderGroupDivisionVersionInfo **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
