/*
 * Function: ?ConnectionStatusRequest@CNetworkEX@@AEAA_NH@Z
 * Address: 0x1401C7490
 */

char __fastcall CNetworkEX::ConnectionStatusRequest(CNetworkEX *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CLogTypeDBTaskManager *v4; // rax@10
  CLogTypeDBTaskManager *v5; // rax@11
  CCashDBWorkManager *v6; // rax@13
  unsigned __int16 v7; // ax@14
  __int64 v9; // [sp+0h] [bp-88h]@1
  char Dst; // [sp+34h] [bp-54h]@4
  char v11; // [sp+35h] [bp-53h]@11
  bool v12; // [sp+36h] [bp-52h]@13
  char v13; // [sp+37h] [bp-51h]@13
  char v14; // [sp+38h] [bp-50h]@13
  char v15; // [sp+39h] [bp-4Fh]@13
  unsigned __int8 j; // [sp+44h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-34h]@13
  char v18; // [sp+55h] [bp-33h]@13
  unsigned __int64 v19; // [sp+70h] [bp-18h]@4
  CNetworkEX *v20; // [sp+90h] [bp+8h]@1

  v20 = this;
  v2 = &v9;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v19 = (unsigned __int64)&v9 ^ _security_cookie;
  memset_0(&Dst, 0, 6ui64);
  for ( j = 0; (signed int)j < 6; ++j )
    *(&Dst + j) = 0;
  if ( pkDB )
    Dst = CRFNewDatabase::IsConectionActive((CRFNewDatabase *)&pkDB->vfptr) != 0;
  else
    Dst = 2;
  v4 = CLogTypeDBTaskManager::Instance();
  if ( CLogTypeDBTaskManager::GetDBTaskDataStatus(v4) )
  {
    v5 = CLogTypeDBTaskManager::Instance();
    v11 = CLogTypeDBTaskManager::GetDBTaskConnectionStatus(v5) != 0;
  }
  else
  {
    v11 = 2;
  }
  v6 = CTSingleton<CCashDBWorkManager>::Instance();
  v12 = CCashDBWorkManager::GetBillingDBConnectionStatus(v6) != 0;
  v13 = v20->m_byStatus[1];
  v14 = v20->m_byStatus[2];
  v15 = v20->m_byStatus[3];
  pbyType = 54;
  v18 = 6;
  if ( unk_1799C9AE0 )
  {
    v7 = _connection_status_result_zoct::size((_connection_status_result_zoct *)&Dst);
    CNetProcess::LoadSendMsg(unk_1414F2098, unk_1799C9ADF, &pbyType, &Dst, v7);
  }
  return 1;
}
