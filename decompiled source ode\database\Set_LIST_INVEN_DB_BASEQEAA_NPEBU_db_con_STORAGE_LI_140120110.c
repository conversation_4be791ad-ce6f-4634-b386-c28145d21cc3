/*
 * Function: ?Set@_LIST@_INVEN_DB_BASE@@QEAA_NPEBU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x140120110
 */

char __fastcall _INVEN_DB_BASE::_LIST::Set(_INVEN_DB_BASE::_LIST *this, _STORAGE_LIST::_db_con *pItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  _INVEN_DB_BASE::_LIST *v6; // [sp+30h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v7; // [sp+38h] [bp+10h]@1

  v7 = pItem;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( _INVENKEY::IsFilled(&v6->Key) )
  {
    result = 0;
  }
  else
  {
    v6->Key.byTableCode = v7->m_byTableCode;
    v6->Key.wItemIndex = v7->m_wItemIndex;
    v6->dwDur = v7->m_dwDur;
    v6->dwUpt = v7->m_dwLv;
    v6->lnUID = v7->m_lnUID;
    v6->dwT = v7->m_dwT;
    v6->byCsMethod = v7->m_byCsMethod;
    v6->dwLendRegdTime = v7->m_dwLendRegdTime;
    result = 1;
  }
  return result;
}
