/*
 * Function: j_?GetPublicPrecomputation@?$DL_PublicKeyImpl@V?$DL_GroupParameters_EC@VECP@CryptoPP@@@CryptoPP@@@CryptoPP@@UEBAAEBV?$DL_FixedBasePrecomputation@UECPPoint@CryptoPP@@@2@XZ
 * Address: 0x14000348B
 */

CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint> *__fastcall CryptoPP::DL_PublicKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>>::GetPublicPrecomputation(CryptoPP::DL_PublicKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> > *this)
{
  return CryptoPP::DL_PublicKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>>::GetPublicPrecomputation(this);
}
