/*
 * Function: ?CleanUp@CUnmannedTraderDivisionInfo@@IEAAXXZ
 * Address: 0x14036E2A0
 */

void __fastcall CUnmannedTraderDivisionInfo::CleanUp(CUnmannedTraderDivisionInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v3; // rax@6
  unsigned __int64 v4; // rax@14
  __int64 v5; // [sp+0h] [bp-78h]@1
  int j; // [sp+20h] [bp-58h]@5
  int k; // [sp+24h] [bp-54h]@13
  CUnmannedTraderClassInfo *v8; // [sp+28h] [bp-50h]@7
  CUnmannedTraderClassInfo *v9; // [sp+30h] [bp-48h]@7
  CUnmannedTraderSortType *v10; // [sp+38h] [bp-40h]@15
  CUnmannedTraderSortType *v11; // [sp+40h] [bp-38h]@15
  unsigned __int64 v12; // [sp+48h] [bp-30h]@6
  void *v13; // [sp+50h] [bp-28h]@8
  unsigned __int64 v14; // [sp+58h] [bp-20h]@14
  void *v15; // [sp+60h] [bp-18h]@16
  CUnmannedTraderDivisionInfo *v16; // [sp+80h] [bp+8h]@1

  v16 = this;
  v1 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::empty(&v16->m_vecClass) )
  {
    for ( j = 0; ; ++j )
    {
      v12 = j;
      v3 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::size(&v16->m_vecClass);
      if ( v12 >= v3 )
        break;
      v9 = *std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator[](
              &v16->m_vecClass,
              j);
      v8 = v9;
      if ( v9 )
        v13 = CUnmannedTraderClassInfo::`scalar deleting destructor'(v8, 1u);
      else
        v13 = 0i64;
    }
    std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::clear(&v16->m_vecClass);
  }
  if ( !std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::empty(&v16->m_vecSortType) )
  {
    for ( k = 0; ; ++k )
    {
      v14 = k;
      v4 = std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::size(&v16->m_vecSortType);
      if ( v14 >= v4 )
        break;
      v11 = *std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::operator[](
               &v16->m_vecSortType,
               k);
      v10 = v11;
      if ( v11 )
        v15 = CUnmannedTraderSortType::`scalar deleting destructor'(v10, 1u);
      else
        v15 = 0i64;
    }
    std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::clear(&v16->m_vecSortType);
  }
}
