/*
 * Function: ?InsertCharacterSelectLog@CRFWorldDatabase@@QEAA_NKPEADK0GEEEEE@Z
 * Address: 0x1404A0D30
 */

bool __fastcall CRFWorldDatabase::InsertCharacterSelectLog(CRFWorldDatabase *this, unsigned int dwAccountSerial, char *wszAccount, unsigned int dwCharacSerial, char *pwszCharacName, unsigned __int16 dwYear, char byMonth, char byDay, char byHour, char byMin, char bySec)
{
  __int64 *v11; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v14; // [sp+0h] [bp-4B8h]@1
  unsigned int v15; // [sp+20h] [bp-498h]@4
  char *v16; // [sp+28h] [bp-490h]@4
  unsigned int v17; // [sp+30h] [bp-488h]@4
  char *v18; // [sp+38h] [bp-480h]@4
  int v19; // [sp+40h] [bp-478h]@4
  int v20; // [sp+48h] [bp-470h]@4
  int v21; // [sp+50h] [bp-468h]@4
  int v22; // [sp+58h] [bp-460h]@4
  int v23; // [sp+60h] [bp-458h]@4
  int v24; // [sp+68h] [bp-450h]@4
  char Dst; // [sp+80h] [bp-438h]@4
  unsigned __int64 v26; // [sp+490h] [bp-28h]@4
  CRFWorldDatabase *v27; // [sp+4C0h] [bp+8h]@1
  unsigned int v28; // [sp+4C8h] [bp+10h]@1
  char *v29; // [sp+4D0h] [bp+18h]@1
  unsigned int v30; // [sp+4D8h] [bp+20h]@1

  v30 = dwCharacSerial;
  v29 = wszAccount;
  v28 = dwAccountSerial;
  v27 = this;
  v11 = &v14;
  for ( i = 298i64; i; --i )
  {
    *(_DWORD *)v11 = -*********;
    v11 = (__int64 *)((char *)v11 + 4);
  }
  v26 = (unsigned __int64)&v14 ^ _security_cookie;
  memset_0(&Dst, 0, 0x400ui64);
  v24 = (unsigned __int8)bySec;
  v23 = (unsigned __int8)byMin;
  v22 = (unsigned __int8)byHour;
  v21 = (unsigned __int8)byDay;
  v20 = (unsigned __int8)byMonth;
  v19 = dwYear;
  v18 = pwszCharacName;
  v17 = v30;
  v16 = v29;
  v15 = v28;
  sprintf(
    &Dst,
    "insert into tbl_characterselect_log_%04d%02d ( AccountSerial, Account, CharacSerial, CharacName, LogDate ) values ( "
    "%d, '%s', %d, '%s', '%04d-%02d-%02d %02d:%02d:%02d' )",
    dwYear,
    (unsigned __int8)byMonth);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v27->vfptr, &Dst, 1);
}
