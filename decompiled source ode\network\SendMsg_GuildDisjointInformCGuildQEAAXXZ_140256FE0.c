/*
 * Function: ?SendMsg_GuildDisjointInform@CGuild@@QEAAXXZ
 * Address: 0x140256FE0
 */

void __fastcall CGuild::SendMsg_GuildDisjointInform(CGuild *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@5
  char pbyType; // [sp+54h] [bp-24h]@5
  char v6; // [sp+55h] [bp-23h]@5
  int j; // [sp+64h] [bp-14h]@5
  _guild_applier_info *v8; // [sp+68h] [bp-10h]@8
  CGuild *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v9->m_nApplierNum > 0 )
  {
    *(_DWORD *)szMsg = v9->m_dwSerial;
    pbyType = 27;
    v6 = 42;
    for ( j = 0; j < 32; ++j )
    {
      v8 = &v9->m_ApplierData[j];
      if ( v8 )
      {
        if ( _guild_applier_info::IsFill(v8) )
        {
          CNetProcess::LoadSendMsg(unk_1414F2088, v8->pPlayer->m_ObjID.m_wIndex, &pbyType, szMsg, 4u);
          v8->pPlayer->m_Param.m_pApplyGuild = 0i64;
        }
      }
    }
  }
}
