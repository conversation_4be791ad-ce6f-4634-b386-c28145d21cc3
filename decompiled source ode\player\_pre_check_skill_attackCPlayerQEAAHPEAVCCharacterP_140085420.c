/*
 * Function: ?_pre_check_skill_attack@CPlayer@@QEAAHPEAVC<PERSON>haracter@@PEAMEPEAU_skill_fld@@GPEAPEAU_db_con@_STORAGE_LIST@@PEAPEAU_BulletItem_fld@@HPEAGG34@Z
 * Address: 0x140085420
 */

signed __int64 __fastcall CPlayer::_pre_check_skill_attack(CPlayer *this, CCharacter *pDst, float *pfAttackPos, char byEffectCode, _skill_fld *pSkillFld, unsigned __int16 wBulletSerial, _STORAGE_LIST::_db_con **ppBulletProp, _BulletItem_fld **ppfldBullet, int nEffectGroup, unsigned __int16 *pdwDecPoint, unsigned __int16 wEffBtSerial, _STORAGE_LIST::_db_con **ppEffBtProp, _BulletItem_fld **ppfldEffBt)
{
  __int64 *v13; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v16; // rdx@21
  float v17; // xmm0_4@103
  float v18; // xmm0_4@103
  float v19; // xmm0_4@103
  float v20; // xmm0_4@103
  float v21; // xmm0_4@104
  float v22; // xmm0_4@104
  float v23; // xmm0_4@104
  float v24; // xmm0_4@116
  float v25; // xmm0_4@116
  float v26; // xmm0_4@116
  float v27; // xmm0_4@117
  float v28; // xmm0_4@117
  __int64 v29; // [sp+0h] [bp-D8h]@1
  _STORAGE_LIST::_db_con *v30; // [sp+20h] [bp-B8h]@4
  _BulletItem_fld *v31; // [sp+28h] [bp-B0h]@4
  _STORAGE_LIST::_db_con *v32; // [sp+30h] [bp-A8h]@4
  _BulletItem_fld *v33; // [sp+38h] [bp-A0h]@4
  int v34; // [sp+40h] [bp-98h]@21
  int pnClassGrade; // [sp+54h] [bp-84h]@55
  char v36; // [sp+64h] [bp-74h]@72
  char v37; // [sp+65h] [bp-73h]@93
  float v38; // [sp+68h] [bp-70h]@102
  _base_fld *v39; // [sp+70h] [bp-68h]@106
  float v40; // [sp+78h] [bp-60h]@107
  _base_fld *v41; // [sp+80h] [bp-58h]@111
  float v42; // [sp+88h] [bp-50h]@115
  _base_fld *v43; // [sp+90h] [bp-48h]@119
  float v44; // [sp+98h] [bp-40h]@120
  _base_fld *v45; // [sp+A0h] [bp-38h]@123
  float v46; // [sp+A8h] [bp-30h]@103
  float v47; // [sp+ACh] [bp-2Ch]@103
  float v48; // [sp+B0h] [bp-28h]@103
  float v49; // [sp+B4h] [bp-24h]@104
  float v50; // [sp+B8h] [bp-20h]@104
  float v51; // [sp+BCh] [bp-1Ch]@104
  float v52; // [sp+C0h] [bp-18h]@116
  float v53; // [sp+C4h] [bp-14h]@116
  float v54; // [sp+C8h] [bp-10h]@117
  float v55; // [sp+CCh] [bp-Ch]@117
  CPlayer *v56; // [sp+E0h] [bp+8h]@1
  CCharacter *v57; // [sp+E8h] [bp+10h]@1
  float *fPos; // [sp+F0h] [bp+18h]@1
  char v59; // [sp+F8h] [bp+20h]@1

  v59 = byEffectCode;
  fPos = pfAttackPos;
  v57 = pDst;
  v56 = this;
  v13 = &v29;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v13 = -858993460;
    v13 = (__int64 *)((char *)v13 + 4);
  }
  v30 = 0i64;
  v31 = 0i64;
  v32 = 0i64;
  v33 = 0i64;
  if ( !pSkillFld->m_nAttackable )
    return 4294967246i64;
  if ( v56->m_pmWpn.byWpType == 10 )
    return 4294967287i64;
  if ( CPlayerDB::GetRaceCode(&v56->m_Param) == 2 && CPlayer::IsActingSiegeMode(v56) )
    return 4294967236i64;
  if ( v57 )
  {
    if ( (CPlayer *)v57 == v56 )
      return 4294967290i64;
    if ( !v57->m_bLive
      || v57->m_bCorpse
      || v57->m_pCurMap != v56->m_pCurMap
      || CGameObject::GetCurSecNum((CGameObject *)&v57->vfptr) == -1 )
    {
      return 4294967290i64;
    }
    if ( v57 == (CCharacter *)v56->m_pRecalledAnimusChar )
      return 4294967254i64;
    v34 = CPlayer::_pre_check_in_guild_battle(v56, v57);
    if ( v34 )
      return (unsigned int)v34;
    LOBYTE(v16) = 1;
    if ( !(unsigned __int8)((int (__fastcall *)(CCharacter *, __int64))v57->vfptr->IsBeAttackedAble)(v57, v16) )
      return 4294967290i64;
    if ( !(unsigned __int8)((int (__fastcall *)(CPlayer *))v56->vfptr->IsAttackableInTown)(v56)
      && !(unsigned __int8)((int (__fastcall *)(CCharacter *))v57->vfptr->IsAttackableInTown)(v57)
      && ((unsigned __int8)((int (__fastcall *)(CPlayer *))v56->vfptr->IsInTown)(v56)
       || (unsigned __int8)((int (__fastcall *)(CCharacter *))v57->vfptr->IsInTown)(v57)) )
    {
      return 4294967265i64;
    }
    if ( !(unsigned __int8)((int (__fastcall *)(CCharacter *, CPlayer *))v57->vfptr->IsBeDamagedAble)(v57, v56) )
      return 4294967290i64;
  }
  else if ( nEffectGroup != 4 && nEffectGroup != 6 )
  {
    return 4294967290i64;
  }
  if ( !pSkillFld )
    return 4294967236i64;
  if ( !CPlayer::IsSFUsableGauge(v56, v59, pSkillFld->m_dwIndex, pdwDecPoint) )
    return 4294967271i64;
  if ( wBulletSerial != 0xFFFF )
  {
    v30 = CPlayer::IsBulletValidity(v56, wBulletSerial);
    if ( !v30 )
    {
      CPlayer::SendMsg_AdjustAmountInform(v56, 2, wBulletSerial, 0);
      return 4294967279i64;
    }
    v31 = (_BulletItem_fld *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 10, v30->m_wItemIndex);
    if ( v30->m_bLock )
      return 4294967267i64;
  }
  if ( (v56->m_pmWpn.byWpType == 5
     || v56->m_pmWpn.byWpType == 6
     || v56->m_pmWpn.byWpType == 7
     || v56->m_pmWpn.byWpType == 11)
    && !v31 )
  {
    return 4294967279i64;
  }
  if ( wEffBtSerial != 0xFFFF )
  {
    v32 = CPlayer::IsEffBulletValidity(v56, wEffBtSerial);
    if ( !v32 )
    {
      CPlayer::SendMsg_AdjustAmountInform(v56, 2, wEffBtSerial, 0);
      return 4294967233i64;
    }
    v33 = (_BulletItem_fld *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 10, v32->m_wItemIndex);
    *ppEffBtProp = v32;
    *ppfldEffBt = v33;
  }
  pnClassGrade = -1;
  if ( v59 )
  {
    if ( v59 == 2 )
    {
      if ( !CPlayerDB::IsActableClassSkill(&v56->m_Param, pSkillFld->m_strCode, &pnClassGrade) )
        return 4294967281i64;
    }
    else if ( v59 == 3 )
    {
      if ( !v31 )
        return 4294967279i64;
      if ( strcmp_0(v31->m_strEffectIndex, pSkillFld->m_strCode) )
        return 4294967281i64;
    }
  }
  else
  {
    if ( pSkillFld->m_nMastIndex > 8u )
      return 4294967236i64;
    if ( !CPlayer::IsSFUsableSFMastery(v56, 3, pSkillFld->m_nMastIndex) )
      return 4294967282i64;
    if ( !CPlayer::IsSFActableByClass(v56, 0, (_base_fld *)&pSkillFld->m_dwIndex) )
      return 4294967272i64;
  }
  v36 = -1;
  if ( v59 )
  {
    if ( v59 == 2 )
      v36 = pnClassGrade;
  }
  else if ( pSkillFld->m_nMastIndex < 8u )
  {
    v36 = pSkillFld->m_nMastIndex;
  }
  if ( !v56->m_bSFDelayNotCheck
    && !_ATTACK_DELAY_CHECKER::IsDelay(&v56->m_AttDelayChker, v59, pSkillFld->m_dwIndex, v36) )
  {
    return 4294967291i64;
  }
  if ( CPlayer::IsRidingUnit(v56) )
    return 4294967275i64;
  if ( _effect_parameter::GetEff_State(&v56->m_EP, 0) )
    return 4294967246i64;
  if ( _effect_parameter::GetEff_State(&v56->m_EP, 20) )
    return 4294967259i64;
  if ( _effect_parameter::GetEff_State(&v56->m_EP, 28) )
    return 4294967259i64;
  if ( _effect_parameter::GetEff_State(&v56->m_EP, 21) )
    return 4294967258i64;
  if ( v56->m_byMoveType == 2 )
    return 4294967255i64;
  v37 = 0;
  if ( _WEAPON_PARAM::GetAttackToolType(&v56->m_pmWpn) == 1 )
  {
    if ( pSkillFld->m_strFixWeapon[v56->m_pmWpn.byWpType] == 49 )
      v37 = 1;
  }
  else if ( !strstr(pSkillFld->m_strFixWeapon, "1") )
  {
    v37 = 1;
  }
  if ( !v37 )
    return 4294967287i64;
  if ( v57 )
  {
    v38 = 0.0;
    if ( v56->m_pmWpn.byWpType == 7 )
    {
      v49 = (float)v56->m_pmWpn.wGaAttRange;
      v21 = v49;
      ((void (__fastcall *)(CCharacter *))v57->vfptr->GetWidth)(v57);
      v22 = v49 + (float)(v21 / 2.0);
      v50 = v22;
      _effect_parameter::GetEff_Plus(&v56->m_EP, 36);
      v23 = v50 + v22;
      v51 = v23;
      _effect_parameter::GetEff_Plus(&v56->m_EP, v56->m_pmWpn.byWpClass + 6);
      v20 = (float)(v51 + v23) + (float)pSkillFld->m_nBonusDistance;
      v38 = v20;
    }
    else
    {
      v46 = (float)v56->m_pmWpn.wGaAttRange;
      v17 = v46;
      ((void (__fastcall *)(CCharacter *))v57->vfptr->GetWidth)(v57);
      v18 = v46 + (float)(v17 / 2.0);
      v47 = v18;
      _effect_parameter::GetEff_Plus(&v56->m_EP, v56->m_pmWpn.byWpClass + 4);
      v19 = v47 + v18;
      v48 = v19;
      _effect_parameter::GetEff_Plus(&v56->m_EP, v56->m_pmWpn.byWpClass + 6);
      v20 = (float)(v48 + v19) + (float)pSkillFld->m_nBonusDistance;
      v38 = v20;
    }
    if ( CPlayer::IsSiegeMode(v56) )
    {
      v39 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 27, v56->m_pSiegeItem->m_wItemIndex);
      v20 = v38 + *(float *)&v39[5].m_strCode[16];
      v38 = v38 + *(float *)&v39[5].m_strCode[16];
    }
    GetSqrt(v57->m_fCurPos, v56->m_fCurPos);
    v40 = v20;
    if ( v20 > v38 )
    {
      GetSqrt(v57->m_fOldPos, v56->m_fCurPos);
      v40 = v20;
      if ( v20 > v38 )
        return 4294967293i64;
    }
    if ( CPlayer::IsSiegeMode(v56) )
    {
      v41 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 27, v56->m_pSiegeItem->m_wItemIndex);
      if ( *(float *)&v41[5].m_strCode[12] > v40 )
        return 4294967293i64;
    }
  }
  else if ( nEffectGroup != 4 )
  {
    v42 = 0.0;
    if ( v56->m_pmWpn.byWpType == 7 )
    {
      v54 = (float)v56->m_pmWpn.wGaAttRange;
      v27 = v54;
      _effect_parameter::GetEff_Plus(&v56->m_EP, 36);
      v28 = v54 + v27;
      v55 = v28;
      _effect_parameter::GetEff_Plus(&v56->m_EP, v56->m_pmWpn.byWpClass + 6);
      v26 = (float)(v55 + v28) + (float)pSkillFld->m_nBonusDistance;
      v42 = v26;
    }
    else
    {
      v52 = (float)v56->m_pmWpn.wGaAttRange;
      v24 = v52;
      _effect_parameter::GetEff_Plus(&v56->m_EP, v56->m_pmWpn.byWpClass + 4);
      v25 = v52 + v24;
      v53 = v25;
      _effect_parameter::GetEff_Plus(&v56->m_EP, v56->m_pmWpn.byWpClass + 6);
      v26 = (float)(v53 + v25) + (float)pSkillFld->m_nBonusDistance;
      v42 = v26;
    }
    if ( CPlayer::IsSiegeMode(v56) )
    {
      v43 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 27, v56->m_pSiegeItem->m_wItemIndex);
      v26 = v42 + *(float *)&v43[5].m_strCode[16];
      v42 = v42 + *(float *)&v43[5].m_strCode[16];
    }
    GetSqrt(fPos, v56->m_fCurPos);
    v44 = v26;
    if ( v26 > v42 )
      return 4294967293i64;
    if ( CPlayer::IsSiegeMode(v56) )
    {
      v45 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 27, v56->m_pSiegeItem->m_wItemIndex);
      if ( *(float *)&v45[5].m_strCode[12] > v44 )
        return 4294967293i64;
    }
  }
  *ppBulletProp = v30;
  *ppfldBullet = v31;
  return 0i64;
}
