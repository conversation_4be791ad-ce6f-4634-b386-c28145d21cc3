/*
 * Function: ?Init@CNationSettingDataCN@@UEAAHXZ
 * Address: 0x140230570
 */

signed __int64 __fastcall CNationSettingDataCN::Init(CNationSettingDataCN *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@7
  CChiNetworkEX *v4; // rax@8
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@8
  CNationSettingDataCN *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CNationSettingData::GetFireGuardEnableSetting((CNationSettingData *)&v7->vfptr) )
  {
    MyMessageBox("CNationSettingDataCN::Init()", "FireGuard Setting Enabled Wanning!");
    CLogFile::Write(&stru_1799C8F30, "CNationSettingDataCN::Init() : FireGuard Setting Enabled Invalid!");
  }
  if ( CNationSettingData::GetTimeLimitEnableSetting((CNationSettingData *)&v7->vfptr) )
  {
    v4 = CChiNetworkEX::Instance();
    v6 = CChiNetworkEX::Initialize(v4);
    if ( v6 )
    {
      CLogFile::Write(
        &stru_1799C8F30,
        "CNationSettingDataCN::Init() : CChiNetworkEX::Instance()->Initialize() iRet(%d) Fail!",
        (unsigned int)v6);
      result = 4294967294i64;
    }
    else if ( (unsigned __int8)((int (__fastcall *)(CNationSettingDataCN *))v7->vfptr->ReadSystemPass)(v7) )
    {
      result = 0i64;
    }
    else
    {
      MyMessageBox("CNationSettingDataCN::Init()", "All Event Error!");
      CLogFile::Write(&stru_1799C8F30, "CNationSettingDataCN::Init() : All Event Error!");
      result = 0xFFFFFFFFi64;
    }
  }
  else
  {
    MyMessageBox("CNationSettingDataCN::Init()", "Time Limit System Setting Disabled Invalid!");
    CLogFile::Write(&stru_1799C8F30, "CNationSettingDataCN::Init() : Time Limit System Setting Disabled Invalid!");
    result = 0xFFFFFFFFi64;
  }
  return result;
}
