/*
 * Function: ?Load@CReservedGuildScheduleDayGroup@GUILD_BATTLE@@QEAA_NEIAEAU_worlddb_guild_battle_reserved_schedule_info@@@Z
 * Address: 0x1403CCF20
 */

bool __fastcall GUILD_BATTLE::CReservedGuildScheduleDayGroup::Load(GUILD_BATTLE::CReservedGuildScheduleDayGroup *this, char byDayID, unsigned int uiMapInx, _worlddb_guild_battle_reserved_schedule_info *kInfo)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  __int64 v7; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CReservedGuildScheduleDayGroup *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v8->m_uiMapCnt > uiMapInx && v8->m_pkList )
    result = GUILD_BATTLE::CReservedGuildScheduleMapGroup::Load(&v8->m_pkList[uiMapInx], byDayID, kInfo);
  else
    result = 0;
  return result;
}
