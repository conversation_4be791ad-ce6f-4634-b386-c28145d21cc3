/*
 * Function: j_?Load@CGuildBattleScheduleManager@GUILD_BATTLE@@QEAA_NHIHHHH@Z
 * Address: 0x1400014A6
 */

bool __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::Load(GUILD_BATTLE::CGuildBattleScheduleManager *this, int iCurDay, unsigned int uiOldMapCnt, int iToday, int iTodayDayID, int iTomorrow, int iTomorrowDayID)
{
  return GUILD_BATTLE::CGuildBattleScheduleManager::Load(
           this,
           iCurDay,
           uiOldMapCnt,
           iToday,
           iTodayDayID,
           iTomorrow,
           iTomorrowDayID);
}
