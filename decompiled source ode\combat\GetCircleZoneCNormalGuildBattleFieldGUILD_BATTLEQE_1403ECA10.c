/*
 * Function: ?GetCircleZone@CNormalGuildBattleField@GUILD_BATTLE@@QEAAPEAVCCircleZone@@H@Z
 * Address: 0x1403ECA10
 */

CCircleZone *__fastcall GUILD_BATTLE::CNormalGuildBattleField::GetCircleZone(GUILD_BATTLE::CNormalGuildBattleField *this, int iInx)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int j; // [sp+0h] [bp-18h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = (int *)&j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  for ( j = 0; j < v6->m_ui1PGoalPosCnt; ++j )
  {
    if ( v6->m_pk1PGoalZone[j].m_ObjID.m_wIndex == iInx )
      return &v6->m_pk1PGoalZone[j];
  }
  for ( j = 0; j < v6->m_ui2PGoalPosCnt; ++j )
  {
    if ( v6->m_pk2PGoalZone[j].m_ObjID.m_wIndex == iInx )
      return &v6->m_pk2PGoalZone[j];
  }
  return 0i64;
}
