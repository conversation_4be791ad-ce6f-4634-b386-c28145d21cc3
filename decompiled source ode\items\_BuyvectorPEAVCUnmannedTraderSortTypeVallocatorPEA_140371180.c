/*
 * Function: ?_Buy@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@IEAA_N_K@Z
 * Address: 0x140371180
 */

char __fastcall std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Buy(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, unsigned __int64 _Capacity)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v5; // rcx@6
  __int64 v6; // [sp+0h] [bp-28h]@1
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v7; // [sp+30h] [bp+8h]@1
  unsigned __int64 _Count; // [sp+38h] [bp+10h]@1

  _Count = _Capacity;
  v7 = this;
  v2 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7->_Myfirst = 0i64;
  v7->_Mylast = 0i64;
  v7->_Myend = 0i64;
  if ( _Capacity )
  {
    if ( std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::max_size(v7) < _Capacity )
      std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Xlen(v5);
    v7->_Myfirst = std::allocator<CUnmannedTraderSortType *>::allocate(&v7->_Alval, _Count);
    v7->_Mylast = v7->_Myfirst;
    v7->_Myend = &v7->_Myfirst[_Count];
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
