/*
 * Function: ??$_Umove@PEAPEAVTRC_AutoTrade@@@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@IEAAPEAPEAVTRC_AutoTrade@@PEAPEAV2@00@Z
 * Address: 0x140391400
 */

TRC_AutoTrade **__fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Umove<TRC_AutoTrade * *>(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, TRC_AutoTrade **_First, TRC_AutoTrade **_Last, TRC_AutoTrade **_Ptr)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return stdext::_Unchecked_uninitialized_move<TRC_AutoTrade * *,TRC_AutoTrade * *,std::allocator<TRC_AutoTrade *>>(
           _First,
           _Last,
           _Ptr,
           &v8->_Alval);
}
