/*
 * Function: j_?_<PERSON><PERSON>@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@KAXXZ
 * Address: 0x140008D91
 */

void __fastcall __noreturn std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_<PERSON>len(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this)
{
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_<PERSON>len(this);
}
