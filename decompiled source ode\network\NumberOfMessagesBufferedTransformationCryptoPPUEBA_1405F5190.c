/*
 * Function: ?NumberOfMessages@BufferedTransformation@CryptoPP@@UEBAIXZ
 * Address: 0x1405F5190
 */

int __fastcall CryptoPP::BufferedTransformation::NumberOfMessages(CryptoPP::BufferedTransformation *this)
{
  __int64 v1; // rax@1
  CryptoPP *v2; // rcx@1
  __int64 v3; // rax@2
  int result; // eax@2
  CryptoPP::BitBucket *v5; // rax@3
  CryptoPP::BufferedTransformation *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  LODWORD(v1) = ((int (*)(void))this->vfptr[20].__vecDelDtor)();
  if ( v1 )
  {
    LODWORD(v3) = ((int (__fastcall *)(CryptoPP::BufferedTransformation *))v6->vfptr[20].__vecDelDtor)(v6);
    result = (*(int (__fastcall **)(__int64))(*(_QWORD *)v3 + 184i64))(v3);
  }
  else
  {
    v5 = CryptoPP::TheBitBucket(v2);
    result = CryptoPP::BufferedTransformation::CopyMessagesTo(
               v6,
               v5,
               0xFFFFFFFFi64,
               &CryptoPP::BufferedTransformation::NULL_CHANNEL);
  }
  return result;
}
