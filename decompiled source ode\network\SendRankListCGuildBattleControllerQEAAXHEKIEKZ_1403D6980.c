/*
 * Function: ?SendRankList@CGuildBattleController@@QEAAXHEKIEK@Z
 * Address: 0x1403D6980
 */

void __fastcall CGuildBattleController::SendRankList(CGuildBattleController *this, int n, char bySelfRace, unsigned int dwCurVer, unsigned int uiMapID, char byPage, unsigned int dwGuildSerial)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleRankManager *v9; // rax@4
  __int64 v10; // [sp+0h] [bp-48h]@1
  int na; // [sp+58h] [bp+10h]@1
  char v12; // [sp+60h] [bp+18h]@1
  unsigned int dwCurVera; // [sp+68h] [bp+20h]@1

  dwCurVera = dwCurVer;
  v12 = bySelfRace;
  na = n;
  v7 = &v10;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v9 = GUILD_BATTLE::CGuildBattleRankManager::Instance();
  GUILD_BATTLE::CGuildBattleRankManager::Send(v9, na, v12, dwCurVera, uiMapID, byPage, dwGuildSerial);
}
