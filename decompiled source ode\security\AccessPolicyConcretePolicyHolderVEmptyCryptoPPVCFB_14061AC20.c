/*
 * Function: ?AccessPolicy@?$ConcretePolicyHolder@VEmpty@CryptoPP@@V?$CFB_DecryptionTemplate@V?$AbstractPolicyHolder@VCFB_CipherAbstractPolicy@CryptoPP@@VCFB_ModePolicy@2@@CryptoPP@@@2@VCFB_CipherAbstractPolicy@2@@CryptoPP@@MEAAAEAVCFB_CipherAbstractPolicy@2@XZ
 * Address: 0x14061AC20
 */

signed __int64 __fastcall CryptoPP::ConcretePolicyHolder<CryptoPP::Empty,CryptoPP::CFB_DecryptionTemplate<CryptoPP::AbstractPolicyHolder<CryptoPP::CFB_CipherAbstractPolicy,CryptoPP::CFB_ModePolicy>>,CryptoPP::CFB_CipherAbstractPolicy>::AccessPolicy(__int64 a1)
{
  signed __int64 v2; // [sp+0h] [bp-18h]@2

  if ( a1 )
    v2 = a1 + 48;
  else
    v2 = 0i64;
  return v2;
}
