/*
 * Function: ?ct_user_num@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140290980
 */

char __fastcall ct_user_num(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-F8h]@1
  bool bFilter[4]; // [sp+20h] [bp-D8h]@6
  char *pwszMessage; // [sp+28h] [bp-D0h]@6
  char byPvpGrade[4]; // [sp+30h] [bp-C8h]@6
  char Dest; // [sp+50h] [bp-A8h]@6
  char v9; // [sp+51h] [bp-A7h]@6
  unsigned __int64 v10; // [sp+E0h] [bp-18h]@4
  CPlayer *v11; // [sp+100h] [bp+8h]@1

  v11 = pOne;
  v1 = &v4;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( v11 )
  {
    Dest = 0;
    memset(&v9, 0, 0x7Fui64);
    *(_DWORD *)byPvpGrade = dword_140988F90;
    LODWORD(pwszMessage) = dword_140988F8C;
    *(_DWORD *)bFilter = CPlayer::s_nRaceNum;
    sprintf(
      &Dest,
      "ConnectUserNum : %d (Field: %d) (B:%d, C:%d, A:%d)",
      (unsigned int)CUserDB::s_nLoginNum,
      (unsigned int)CPlayer::s_nLiveNum);
    CPlayer::SendData_ChatTrans(v11, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
