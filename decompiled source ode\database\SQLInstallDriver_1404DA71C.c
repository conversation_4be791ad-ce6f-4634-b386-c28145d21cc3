/*
 * Function: SQLInstallDriver
 * Address: 0x1404DA71C
 */

int __fastcall SQLInstallDriver(const char *lpszInfFile, const char *lpszDriver, char *lpszPath, unsigned __int16 cbPathMax, unsigned __int16 *pcbPathOut)
{
  const char *v5; // rbp@1
  unsigned __int16 v6; // bx@1
  char *v7; // rdi@1
  const char *v8; // rsi@1
  __int64 (__cdecl *v9)(); // rax@1
  int result; // eax@2

  v5 = lpszInfFile;
  v6 = cbPathMax;
  v7 = lpszPath;
  v8 = lpszDriver;
  v9 = ODBC___GetSetupProc("SQLInstallDriver");
  if ( v9 )
    result = ((int (__fastcall *)(const char *, const char *, char *, _QWORD))v9)(v5, v8, v7, v6);
  else
    result = 0;
  return result;
}
