/*
 * Function: ?SendMsg_GuildBattleProposed@CGuild@@QEAAHPEAD@Z
 * Address: 0x14025A120
 */

signed __int64 __fastcall CGuild::SendMsg_GuildBattleProposed(CGuild *this, char *pwszName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  __int64 v5; // [sp+0h] [bp-98h]@1
  char Dest; // [sp+38h] [bp-60h]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v8; // [sp+65h] [bp-33h]@4
  CPlayer *v9; // [sp+78h] [bp-20h]@5
  unsigned __int64 v10; // [sp+88h] [bp-10h]@4
  CGuild *v11; // [sp+A0h] [bp+8h]@1

  v11 = this;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  strcpy_0(&Dest, pwszName);
  pbyType = 27;
  v8 = 92;
  if ( v11->m_MasterData.pMember && (v9 = v11->m_MasterData.pMember->pPlayer) != 0i64 )
  {
    CNetProcess::LoadSendMsg(unk_1414F2088, v9->m_ObjID.m_wIndex, &pbyType, &Dest, 0x11u);
    result = 1i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
