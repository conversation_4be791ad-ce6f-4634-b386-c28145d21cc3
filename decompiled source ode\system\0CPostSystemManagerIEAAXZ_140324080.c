/*
 * Function: ??0CPostSystemManager@@IEAA@XZ
 * Address: 0x140324080
 */

void __fastcall CPostSystemManager::CPostSystemManager(CPostSystemManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CPostSystemManager *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  v5->m_byRegiNum = 20;
  v5->m_byProcNum = 15;
  CMyTimer::CMyTimer(&v5->m_tmrRegiTime);
  CMyTimer::CMyTimer(&v5->m_tmrProcTime);
  CNetIndexList::CNetIndexList(&v5->m_listEmpty);
  CNetIndexList::CNetIndexList(&v5->m_listRegist);
  CNetIndexList::CNetIndexList(&v5->m_listProc);
  v5->m_nPostProcCountPerDay = 0;
  v5->m_nPostReturnCountPerDay = 0;
}
