/*
 * Function: ?allocate@?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@QEAAPEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@_K@Z
 * Address: 0x140594780
 */

int __fastcall std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>::allocate(__int64 a1, __int64 a2)
{
  return std::_Allocate<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>(a2, 0i64);
}
