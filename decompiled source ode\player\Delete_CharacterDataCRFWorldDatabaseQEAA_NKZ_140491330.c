/*
 * Function: ?Delete_CharacterData@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x140491330
 */

char __fastcall CRFWorldDatabase::Delete_CharacterData(CRFWorldDatabase *this, unsigned int dwCharacterSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-168h]@1
  void *SQLStmt; // [sp+20h] [bp-148h]@11
  char Dest; // [sp+40h] [bp-128h]@4
  __int16 v8; // [sp+144h] [bp-24h]@9
  unsigned __int64 v9; // [sp+150h] [bp-18h]@4
  CRFWorldDatabase *v10; // [sp+170h] [bp+8h]@1
  unsigned int v11; // [sp+178h] [bp+10h]@1

  v11 = dwCharacterSerial;
  v10 = this;
  v2 = &v5;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  sprintf(&Dest, "{ CALL pDelete_Base( %d ) }", dwCharacterSerial);
  if ( v10->m_bSaveDBLog )
    CRFNewDatabase::FmtLog((CRFNewDatabase *)&v10->vfptr, "Query : %s", &Dest);
  if ( v10->m_hStmtUpdate || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v10->vfptr) )
  {
    v8 = SQLExecDirectA_0(v10->m_hStmtUpdate, &Dest, -3);
    if ( v8 && v8 != 1 )
    {
      SQLStmt = v10->m_hStmtUpdate;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v10->vfptr, v8, &Dest, "SQLExecDirectA", SQLStmt);
      CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v10->vfptr, v8, v10->m_hStmtUpdate);
      result = 0;
    }
    else
    {
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v10->vfptr, 0);
      sprintf(&Dest, "{ CALL pUpdate_DeleteName_Step1( %d ) }", v11);
      v8 = SQLExecDirectA_0(v10->m_hStmtUpdate, &Dest, -3);
      if ( v8 && v8 != 1 )
      {
        SQLStmt = v10->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v10->vfptr, v8, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v10->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v10->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v10->vfptr, v8, v10->m_hStmtUpdate);
        result = 0;
      }
      else
      {
        sprintf(&Dest, "{ CALL pUpdate_DeleteName_Step2( %d ) }", v11);
        v8 = SQLExecDirectA_0(v10->m_hStmtUpdate, &Dest, -3);
        if ( v8 && v8 != 1 )
        {
          SQLStmt = v10->m_hStmtUpdate;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v10->vfptr, v8, &Dest, "SQLExecDirectA", SQLStmt);
          CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v10->vfptr);
          CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v10->vfptr, 1);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v10->vfptr, v8, v10->m_hStmtUpdate);
          result = 0;
        }
        else
        {
          CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&v10->vfptr);
          CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v10->vfptr, 1);
          if ( v10->m_bSaveDBLog )
            CRFNewDatabase::Log((CRFNewDatabase *)&v10->vfptr, "Query Success");
          result = 1;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v10->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
