/*
 * Function: j_??$_Unchecked_move_backward@PEAVCMoveMapLimitRightInfo@@PEAV1@@stdext@@YAPEAVCMoveMapLimitRightInfo@@PEAV1@00@Z
 * Address: 0x14000C856
 */

CMoveMapLimitRightInfo *__fastcall stdext::_Unchecked_move_backward<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *>(CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, CMoveMapLimitRightInfo *_Dest)
{
  return stdext::_Unchecked_move_backward<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *>(_First, _Last, _Dest);
}
