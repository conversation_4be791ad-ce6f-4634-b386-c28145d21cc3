/*
 * Function: _cfltcvt_init
 * Address: 0x1405E4070
 */

void (__fastcall *cfltcvt_init())(CryptoPP *__hidden this, unsigned __int64 *, const unsigned __int64 *, const unsigned __int64 *, unsigned __int64)
{
  void (__fastcall *result)(CryptoPP *__hidden, unsigned __int64 *, const unsigned __int64 *, const unsigned __int64 *, unsigned __int64); // rax@1

  qword_184A8A480 = (int (__fastcall *)(_QWORD, _QWORD, _QWORD))CryptoPP::Baseline_Multiply2;
  qword_184A8A430 = (int (__fastcall *)(_QWORD, _QWORD, _QWORD))CryptoPP::Baseline_MultiplyBottom2;
  qword_184A8A3E0[0] = (__int64)CryptoPP::Baseline_Square2;
  qword_184A8A390[0] = (__int64)CryptoPP::Baseline_MultiplyTop2;
  qword_184A8A398 = (__int64)CryptoPP::Baseline_MultiplyTop4;
  qword_184A8A488 = (__int64)CryptoPP::Baseline_Multiply4;
  qword_184A8A490 = (__int64)CryptoPP::Baseline_Multiply8;
  qword_184A8A438 = (__int64)CryptoPP::Baseline_MultiplyBottom4;
  qword_184A8A440 = (__int64)CryptoPP::Baseline_MultiplyBottom8;
  qword_184A8A3E8 = (__int64)CryptoPP::Baseline_Square4;
  qword_184A8A3F0 = (__int64)CryptoPP::Baseline_Square8;
  qword_184A8A3A0 = (__int64)CryptoPP::Baseline_MultiplyTop8;
  qword_184A8A4A0 = (__int64)CryptoPP::Baseline_Multiply16;
  qword_184A8A450 = (__int64)CryptoPP::Baseline_MultiplyBottom16;
  qword_184A8A400 = (__int64)CryptoPP::Baseline_Square16;
  result = CryptoPP::Baseline_MultiplyTop16;
  qword_184A8A3B0 = (__int64)CryptoPP::Baseline_MultiplyTop16;
  return result;
}
