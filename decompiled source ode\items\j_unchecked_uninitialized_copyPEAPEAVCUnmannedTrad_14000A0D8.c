/*
 * Function: j_??$unchecked_uninitialized_copy@PEAPEAVCUnmannedTraderSortType@@PEAPEAV1@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@stdext@@YAPEAPEAVCUnmannedTraderSortType@@PEAPEAV1@00AEAV?$allocator@PEAVCUnmannedTraderSortType@@@std@@@Z
 * Address: 0x14000A0D8
 */

CUnmannedTraderSortType **__fastcall stdext::unchecked_uninitialized_copy<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *,std::allocator<CUnmannedTraderSortType *>>(CUnmannedTraderSortType **_First, CUnmannedTraderSortType **_Last, CUnmannedTraderSortType **_Dest, std::allocator<CUnmannedTraderSortType *> *_Al)
{
  return stdext::unchecked_uninitialized_copy<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *,std::allocator<CUnmannedTraderSortType *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
