/*
 * Function: ??0CNationSettingFactoryBR@@QEAA@XZ
 * Address: 0x140219700
 */

void __fastcall CNationSettingFactoryBR::CNationSettingFactoryBR(CNationSettingFactoryBR *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CNationSettingFactoryBR *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CNationSettingFactory::CNationSettingFactory((CNationSettingFactory *)&v4->vfptr, 76);
  v4->vfptr = (CNationSettingFactoryVtbl *)&CNationSettingFactoryBR::`vftable';
}
