/*
 * Function: ?ct_server_rate@@YA_NPEAVCPlayer@@@Z
 * Address: 0x1402972F0
 */

char __fastcall ct_server_rate(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v4; // [sp+0h] [bp-198h]@1
  bool bFilter[8]; // [sp+20h] [bp-178h]@7
  char *pwszMessage; // [sp+28h] [bp-170h]@7
  char byPvpGrade[8]; // [sp+30h] [bp-168h]@7
  char *pwszSender; // [sp+38h] [bp-160h]@7
  double v9; // [sp+40h] [bp-158h]@7
  double v10; // [sp+48h] [bp-150h]@7
  char DstBuf; // [sp+60h] [bp-138h]@7
  char v12; // [sp+61h] [bp-137h]@7
  unsigned __int64 v13; // [sp+170h] [bp-28h]@4
  CPlayer *v14; // [sp+1A0h] [bp+8h]@1

  v14 = pOne;
  v1 = &v4;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v13 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( v14 && v14->m_bOper )
  {
    DstBuf = 0;
    memset(&v12, 0, 0xFEui64);
    v10 = (float)(TSVR_ADD_DARKHOLE_REWARD_RATE * 100.0);
    v9 = (float)(PLAYER_EXP_RATE * 100.0);
    *(double *)&pwszSender = (float)(ANIMUS_EXP_RATE * 100.0);
    *(double *)byPvpGrade = (float)(MASTERY_GET_RATE * 100.0);
    *(double *)&pwszMessage = (float)(FORCE_LIVER_ACCUM_RATE * 100.0);
    *(double *)bFilter = (float)(MINE_SPEED_RATE * 100.0);
    sprintf_s(
      &DstBuf,
      0xFFui64,
      "Item Looting: %f Percent\n"
      "Mine Speed: %f Percent\n"
      "Force Liver: %f Percent\n"
      "Mastery: %f Percent\n"
      "Animus Exp: %f Percent\n"
      "Character Exp: %f Percent\n"
      "Darkhole Reward: %f Percent",
      (float)(ITEM_ROOT_RATE * 100.0));
    CPlayer::SendData_ChatTrans(v14, 0, 0xFFFFFFFF, -1, 0, &DstBuf, -1, 0i64);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
