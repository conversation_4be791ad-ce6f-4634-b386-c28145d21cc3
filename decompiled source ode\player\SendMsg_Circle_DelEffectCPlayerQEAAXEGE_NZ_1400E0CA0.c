/*
 * Function: ?SendMsg_Circle_DelEffect@CPlayer@@QEAAXEGE_N@Z
 * Address: 0x1400E0CA0
 */

void __fastcall CPlayer::SendMsg_Circle_DelEffect(CPlayer *this, char byEffectCode, unsigned __int16 wEffectIndex, char byLv, bool bToOne)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-88h]@1
  unsigned __int16 v8; // [sp+30h] [bp-58h]@4
  char szMsg[2]; // [sp+44h] [bp-44h]@4
  unsigned int v10; // [sp+46h] [bp-42h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v12; // [sp+65h] [bp-23h]@4
  CPlayer *v13; // [sp+90h] [bp+8h]@1

  v13 = this;
  v5 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v8 = CCharacter::CalcEffectBit((CCharacter *)&v13->vfptr, (unsigned __int8)byEffectCode, wEffectIndex);
  *(_WORD *)szMsg = v8;
  v10 = v13->m_dwObjSerial;
  pbyType = 17;
  v12 = 11;
  CGameObject::CircleReport((CGameObject *)&v13->vfptr, &pbyType, szMsg, 6, bToOne);
}
