/*
 * Function: ?Recv<PERSON>KQuest@CPlayer@@QEAAXEEHGGE@Z
 * Address: 0x1400CC840
 */

void __fastcall CPlayer::RecvHSKQuest(CPlayer *this, char byHSKQuestCode, char byCristalBattleDBInfo, int nPvpPoint, unsigned __int16 wKillPoint, unsigned __int16 wDieCount, char byHSKTime)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v9; // ax@8
  char v10; // al@9
  char v11; // al@10
  __int64 v12; // [sp+0h] [bp-58h]@1
  _SYSTEMTIME tm; // [sp+28h] [bp-30h]@8
  CPlayer *v14; // [sp+60h] [bp+8h]@1

  v14 = this;
  v7 = &v12;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  if ( byCristalBattleDBInfo == 1 )
  {
    if ( v14->m_byCristalBattleDBInfo != 3 )
    {
      v14->m_byCristalBattleDBInfo = byCristalBattleDBInfo;
      v14->m_pUserDB->m_AvatorData.m_byCristalBattleDBInfo = v14->m_byCristalBattleDBInfo;
    }
  }
  else
  {
    v14->m_byHSKQuestCode = byHSKQuestCode;
    v14->m_nHSKPvpPoint = 0;
    v14->m_wKillPoint = 0;
    v14->m_wDiePoint = 0;
    v14->m_byCristalBattleDBInfo = 3;
    v14->m_byHSKTime = -1;
    v14->m_pUserDB->m_AvatorData.m_byHSKTime = v14->m_byHSKTime;
    v14->m_pUserDB->m_AvatorData.m_byPvpGrade = 0;
    v14->m_pUserDB->m_AvatorData.m_iPvpPoint = v14->m_nHSKPvpPoint;
    v14->m_pUserDB->m_AvatorData.m_wKillPoint = v14->m_wKillPoint;
    v14->m_pUserDB->m_AvatorData.m_wDiePoint = v14->m_wDiePoint;
    v14->m_pUserDB->m_AvatorData.m_byCristalBattleDBInfo = v14->m_byCristalBattleDBInfo;
    v14->m_pUserDB->m_AvatorData.m_bCristalBattleDateUpdate = 0;
    if ( v14->m_byHSKQuestCode != 100 )
    {
      v14->m_nHSKPvpPoint = nPvpPoint;
      v14->m_wKillPoint = wKillPoint;
      v14->m_wDiePoint = wDieCount;
      v14->m_byHSKTime = byHSKTime - 1;
      v14->m_byCristalBattleDBInfo = byCristalBattleDBInfo;
      v14->m_pUserDB->m_AvatorData.m_byHSKTime = v14->m_byHSKTime;
      v14->m_pUserDB->m_AvatorData.m_byPvpGrade = v14->m_Param.m_byPvPGrade;
      v14->m_pUserDB->m_AvatorData.m_iPvpPoint = v14->m_nHSKPvpPoint;
      v14->m_pUserDB->m_AvatorData.m_wKillPoint = v14->m_wKillPoint;
      v14->m_pUserDB->m_AvatorData.m_wDiePoint = v14->m_wDiePoint;
      v14->m_pUserDB->m_AvatorData.m_byCristalBattleDBInfo = v14->m_byCristalBattleDBInfo;
      CPlayer::ExtractStringToTime(v14, v14->m_pUserDB->m_AvatorData.dbSupplement.dwScanerGetDate, &tm);
      v9 = CHolyStoneSystem::GetStartYear(&g_HolySys);
      if ( v9 != tm.wYear
        || (v10 = CHolyStoneSystem::GetStartMonth(&g_HolySys), (unsigned __int8)v10 != tm.wMonth)
        || (v11 = CHolyStoneSystem::GetStartDay(&g_HolySys), (unsigned __int8)v11 != tm.wDay)
        || (unsigned __int8)byHSKTime != tm.wHour )
      {
        CPlayer::SetCntEnable(v14, 1);
      }
      else
      {
        CPlayer::SetCntEnable(v14, 0);
      }
      CPlayer::SendMsg_RecvHSKQuest(v14);
    }
  }
}
