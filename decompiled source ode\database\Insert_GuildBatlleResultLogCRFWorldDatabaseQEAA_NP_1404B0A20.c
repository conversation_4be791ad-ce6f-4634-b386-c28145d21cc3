/*
 * Function: ?Insert_GuildBatlleResultLog@CRFWorldDatabase@@QEAA_NPEAD0K0K0KKKKKKKKEK0K0EK0@Z
 * Address: 0x1404B0A20
 */

bool __fastcall CRFWorldDatabase::Insert_GuildBatlleResultLog(CRFWorldDatabase *this, char *szStartTime, char *szEndTime, unsigned int dwRedSerial, char *wszRedName, unsigned int dwBlueSerial, char *wszBlueName, unsigned int dwRedScore, unsigned int dwBlueScore, unsigned int dwRedMaxJoinCnt, unsigned int dwBlueMaxJoinCnt, unsigned int dwRedGoalCntSum, unsigned int dwBlueGoalCntSum, unsigned int dwRedKillCntSum, unsigned int dwBlueKillCntSum, char by<PERSON>attle<PERSON><PERSON>ult, unsigned int dwMaxGoalCharacSerial, char *wszMaxGoalCharacName, unsigned int dwMaxKillCharacSerial, char *wszMaxKillCharacName, char byJoinLimit, unsigned int dwGuildBattleCostGold, char *szBattleMapCode)
{
  __int64 *v23; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v26; // [sp+0h] [bp-E8h]@1
  unsigned int pkLogSerial; // [sp+C4h] [bp-24h]@6
  CRFWorldDatabase *v28; // [sp+F0h] [bp+8h]@1
  unsigned int dwRedSeriala; // [sp+108h] [bp+20h]@1

  dwRedSeriala = dwRedSerial;
  v28 = this;
  v23 = &v26;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v23 = -*********;
    v23 = (__int64 *)((char *)v23 + 4);
  }
  if ( CRFWorldDatabase::Insert_GuildBatlleResultLogBattelInfo(
         v28,
         szStartTime,
         szEndTime,
         dwRedSerial,
         wszRedName,
         dwBlueSerial,
         wszBlueName,
         dwRedScore,
         dwBlueScore,
         dwRedMaxJoinCnt,
         dwBlueMaxJoinCnt,
         dwRedGoalCntSum,
         dwBlueGoalCntSum,
         dwRedKillCntSum,
         dwBlueKillCntSum,
         byBattleResult,
         dwMaxGoalCharacSerial,
         wszMaxGoalCharacName,
         dwMaxKillCharacSerial,
         wszMaxKillCharacName,
         byJoinLimit,
         dwGuildBattleCostGold,
         szBattleMapCode) )
  {
    pkLogSerial = 0;
    if ( CRFWorldDatabase::Select_BattleResultLogLatest(v28, &pkLogSerial) && pkLogSerial )
      result = CRFWorldDatabase::Update_BattleResultLogBattleResultAndPvpPoint(
                 v28,
                 pkLogSerial,
                 dwRedSeriala,
                 dwBlueSerial) != 0;
    else
      result = 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
