/*
 * Function: ?VerifyMessageRepresentative@PK_RecoverableSignatureMessageEncodingMethod@CryptoPP@@UEBA_NAEAVHashTransformation@2@U?$pair@PEBE_K@std@@_NPEAE_K@Z
 * Address: 0x1406226A0
 */

char __fastcall CryptoPP::PK_RecoverableSignatureMessageEncodingMethod::VerifyMessageRepresentative(__int64 *a1, __int64 a2, __int64 a3, __int64 a4, __int64 a5, __int64 a6)
{
  int v6; // eax@1
  unsigned __int64 v7; // rax@1
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v9; // [sp+40h] [bp-78h]@1
  char v10; // [sp+58h] [bp-60h]@1
  __int64 v11; // [sp+60h] [bp-58h]@2
  char v12; // [sp+68h] [bp-50h]@5
  char v13; // [sp+70h] [bp-48h]@1
  __int64 v14; // [sp+80h] [bp-38h]@1
  __int64 v15; // [sp+88h] [bp-30h]@1
  __int64 v16; // [sp+90h] [bp-28h]@1
  int v17; // [sp+98h] [bp-20h]@3
  __int64 *v18; // [sp+C0h] [bp+8h]@1
  __int64 v19; // [sp+C8h] [bp+10h]@1
  __int64 v20; // [sp+D0h] [bp+18h]@1

  v20 = a3;
  v19 = a2;
  v18 = a1;
  v14 = -2i64;
  v6 = (*(int (__fastcall **)(__int64))(*(_QWORD *)a2 + 56i64))(a2);
  v15 = *v18;
  LODWORD(v7) = (*(int (__fastcall **)(__int64 *, __int64, _QWORD, _QWORD))(v15 + 16))(
                  v18,
                  a6,
                  *(_QWORD *)(v20 + 8),
                  (unsigned int)v6);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v9,
    v7);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v9);
  qmemcpy(&v13, (const void *)v20, 0x10ui64);
  v16 = *v18;
  (*(void (__fastcall **)(__int64 *, char *, __int64, char *))(v16 + 64))(v18, &v10, v19, &v13);
  v17 = v10 && !v11;
  v12 = v17;
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v9);
  return v12;
}
