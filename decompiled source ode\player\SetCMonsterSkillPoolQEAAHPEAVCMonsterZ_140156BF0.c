/*
 * Function: ?Set@CMonsterSkillPool@@QEAAHPEAVCMonster@@@Z
 * Address: 0x140156BF0
 */

__int64 __fastcall CMonsterSkillPool::Set(CMonsterSkillPool *this, CMonster *pMyMonster)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  unsigned int v5; // eax@24
  unsigned int v6; // eax@29
  unsigned int v7; // eax@34
  unsigned int v8; // ecx@36
  __int64 v9; // [sp+0h] [bp-148h]@1
  int nEffectType[2]; // [sp+20h] [bp-128h]@24
  _skill_fld *pSkillFld; // [sp+28h] [bp-120h]@24
  unsigned int dwDelayTime; // [sp+30h] [bp-118h]@24
  unsigned int v13; // [sp+38h] [bp-110h]@24
  unsigned int dwCastDelay; // [sp+40h] [bp-108h]@24
  int nMotive; // [sp+48h] [bp-100h]@24
  int nMotiveValue; // [sp+50h] [bp-F8h]@24
  int skillDestType; // [sp+58h] [bp-F0h]@24
  CMonsterSkill skill; // [sp+70h] [bp-D8h]@6
  _monster_fld *pMonsterFld; // [sp+D8h] [bp-70h]@6
  _monster_sp_group *v20; // [sp+E0h] [bp-68h]@8
  unsigned __int16 j; // [sp+E8h] [bp-60h]@12
  _monster_sp_fld *pSPCont; // [sp+F0h] [bp-58h]@14
  _skill_fld **v23; // [sp+F8h] [bp-50h]@14
  float v24; // [sp+100h] [bp-48h]@18
  unsigned int v25; // [sp+104h] [bp-44h]@7
  unsigned int v26; // [sp+108h] [bp-40h]@9
  unsigned int v27; // [sp+10Ch] [bp-3Ch]@11
  unsigned int v28; // [sp+110h] [bp-38h]@15
  unsigned int v29; // [sp+114h] [bp-34h]@17
  unsigned int v30; // [sp+118h] [bp-30h]@36
  __int64 v31; // [sp+120h] [bp-28h]@4
  float v32; // [sp+128h] [bp-20h]@22
  float v33; // [sp+12Ch] [bp-1Ch]@27
  float v34; // [sp+130h] [bp-18h]@32
  CMonsterSkillPool *v35; // [sp+150h] [bp+8h]@1
  CMonster *v36; // [sp+158h] [bp+10h]@1

  v36 = pMyMonster;
  v35 = this;
  v2 = &v9;
  for ( i = 80i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v31 = -2i64;
  CMonsterSkillPool::Init(v35);
  if ( v36 )
  {
    v35->m_pMyMon = v36;
    CMonsterSkill::CMonsterSkill(&skill);
    pMonsterFld = v35->m_pMyMon->m_pMonRec;
    if ( pMonsterFld )
    {
      v20 = CMonsterSPGroupTable::GetRecord(&stru_1799C8DB0, pMonsterFld->m_dwIndex);
      if ( v20 )
      {
        if ( (signed int)v20->m_wCount <= 15 )
        {
          for ( j = 0; j < (signed int)v20->m_wCount; ++j )
          {
            pSPCont = v20->m_pSPData[j];
            v23 = &v20->m_SPDataFld[j].m_SkillFld;
            if ( !pSPCont )
            {
              v28 = 0;
              CMonsterSkill::~CMonsterSkill(&skill);
              return v28;
            }
            if ( !v23 )
            {
              v29 = 0;
              CMonsterSkill::~CMonsterSkill(&skill);
              return v29;
            }
            v24 = pMonsterFld->m_fAttExt;
            if ( pSPCont->m_fSpecialRange != -1.0 )
              v24 = pSPCont->m_fSpecialRange;
            if ( pSPCont->m_nSpecialAttType )
            {
              if ( pSPCont->m_nSpecialAttType == 1 )
              {
                CMonsterSkill::Init(&skill);
                if ( (*v23)->m_fActDelay <= (float)(1000 * pSPCont->m_nMotiveTime) )
                  v33 = (float)(1000 * pSPCont->m_nMotiveTime);
                else
                  v33 = (*v23)->m_fActDelay;
                v6 = (signed int)ffloor(pMonsterFld->m_fAttMoTime1);
                nMotiveValue = pSPCont->m_nDstType;
                nMotive = pSPCont->m_nMotiveValue;
                dwCastDelay = pSPCont->m_nMotiveCondition;
                v13 = v6;
                dwDelayTime = LODWORD(v24);
                LODWORD(pSkillFld) = (signed int)ffloor(v33);
                *(_QWORD *)nEffectType = *v23;
                CMonsterSkill::SetForce(
                  &skill,
                  pMonsterFld,
                  pSPCont,
                  pSPCont->m_nAttLv,
                  *(_force_fld **)nEffectType,
                  (unsigned int)pSkillFld,
                  v24,
                  v6,
                  dwCastDelay,
                  nMotive,
                  nMotiveValue);
                CMonsterSkillPool::InsertSkill(v35, &skill);
              }
              else if ( pSPCont->m_nSpecialAttType == 2 )
              {
                CMonsterSkill::Init(&skill);
                if ( (*v23)->m_fActDelay <= (float)(1000 * pSPCont->m_nMotiveTime) )
                  v34 = (float)(1000 * pSPCont->m_nMotiveTime);
                else
                  v34 = (*v23)->m_fActDelay;
                v7 = (signed int)ffloor(pMonsterFld->m_fAttMoTime1);
                skillDestType = pSPCont->m_nDstType;
                nMotiveValue = pSPCont->m_nMotiveValue;
                nMotive = pSPCont->m_nMotiveCondition;
                dwCastDelay = v7;
                v13 = LODWORD(v24);
                dwDelayTime = (signed int)ffloor(v34);
                pSkillFld = *v23;
                nEffectType[0] = 2;
                CMonsterSkill::SetSkill(
                  &skill,
                  pMonsterFld,
                  pSPCont,
                  pSPCont->m_nAttLv,
                  2,
                  pSkillFld,
                  dwDelayTime,
                  v24,
                  v7,
                  nMotive,
                  nMotiveValue,
                  skillDestType);
                CMonsterSkillPool::InsertSkill(v35, &skill);
              }
            }
            else
            {
              CMonsterSkill::Init(&skill);
              if ( (*v23)->m_fActDelay <= (float)(1000 * pSPCont->m_nMotiveTime) )
                v32 = (float)(1000 * pSPCont->m_nMotiveTime);
              else
                v32 = (*v23)->m_fActDelay;
              v5 = (signed int)ffloor(pMonsterFld->m_fAttMoTime1);
              skillDestType = pSPCont->m_nDstType;
              nMotiveValue = pSPCont->m_nMotiveValue;
              nMotive = pSPCont->m_nMotiveCondition;
              dwCastDelay = v5;
              v13 = LODWORD(v24);
              dwDelayTime = (signed int)ffloor(v32);
              pSkillFld = *v23;
              nEffectType[0] = 0;
              CMonsterSkill::SetSkill(
                &skill,
                pMonsterFld,
                pSPCont,
                pSPCont->m_nAttLv,
                0,
                pSkillFld,
                dwDelayTime,
                v24,
                v5,
                nMotive,
                nMotiveValue,
                skillDestType);
              CMonsterSkillPool::InsertSkill(v35, &skill);
            }
          }
          CMonsterSkill::Init(&skill);
          v8 = (signed int)ffloor(pMonsterFld->m_fAttSpd);
          LODWORD(pSkillFld) = (signed int)ffloor(pMonsterFld->m_fAttMoTime1);
          nEffectType[0] = LODWORD(pMonsterFld->m_fAttExt);
          CMonsterSkill::SetGen(&skill, pMonsterFld, 1, v8, *(float *)nEffectType, (unsigned int)pSkillFld);
          CMonsterSkillPool::InsertSkill(v35, &skill);
          v30 = 1;
          CMonsterSkill::~CMonsterSkill(&skill);
          result = v30;
        }
        else
        {
          v27 = 0;
          CMonsterSkill::~CMonsterSkill(&skill);
          result = v27;
        }
      }
      else
      {
        v26 = 0;
        CMonsterSkill::~CMonsterSkill(&skill);
        result = v26;
      }
    }
    else
    {
      v25 = 0;
      CMonsterSkill::~CMonsterSkill(&skill);
      result = v25;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
