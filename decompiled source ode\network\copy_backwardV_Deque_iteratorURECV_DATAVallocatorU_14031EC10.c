/*
 * Function: ??$copy_backward@V?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@V12@@std@@YA?AV?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@0@V10@00@Z
 * Address: 0x14031EC10
 */

std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::copy_backward<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>>(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *result, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_First, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Last, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Dest)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  std::random_access_iterator_tag *v6; // rax@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v7; // rax@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v8; // rax@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v9; // rax@4
  __int64 v11; // [sp+0h] [bp-108h]@1
  std::_Nonscalar_ptr_iterator_tag v12; // [sp+28h] [bp-E0h]@4
  std::_Range_checked_iterator_tag v13; // [sp+30h] [bp-D8h]@4
  std::_Range_checked_iterator_tag v14; // [sp+40h] [bp-C8h]@4
  std::_Nonscalar_ptr_iterator_tag v15; // [sp+41h] [bp-C7h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> v16; // [sp+42h] [bp-C6h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v17; // [sp+68h] [bp-A0h]@4
  char v18; // [sp+70h] [bp-98h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v19; // [sp+90h] [bp-78h]@4
  char v20; // [sp+98h] [bp-70h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v21; // [sp+B8h] [bp-50h]@4
  int v22; // [sp+C0h] [bp-48h]@4
  __int64 v23; // [sp+C8h] [bp-40h]@4
  std::random_access_iterator_tag *v24; // [sp+D0h] [bp-38h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v25; // [sp+D8h] [bp-30h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v26; // [sp+E0h] [bp-28h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v27; // [sp+E8h] [bp-20h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v28; // [sp+F0h] [bp-18h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v29; // [sp+F8h] [bp-10h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v30; // [sp+110h] [bp+8h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__formal; // [sp+118h] [bp+10h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v32; // [sp+120h] [bp+18h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__that; // [sp+128h] [bp+20h]@1

  __that = _Dest;
  v32 = _Last;
  __formal = _First;
  v30 = result;
  v4 = &v11;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v23 = -2i64;
  v22 = 0;
  memset(&v14, 0, sizeof(v14));
  v15 = std::_Ptr_cat<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>>(
          _First,
          _Dest);
  v17 = (std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)((char *)&v16 + 6);
  v19 = (std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v18;
  v21 = (std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v20;
  LOBYTE(v6) = std::_Iter_random<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>>(
                 &v16,
                 __formal);
  v24 = v6;
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
    v17,
    __that);
  v25 = v7;
  v26 = v7;
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
    v19,
    v32);
  v27 = v8;
  v28 = v8;
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
    v21,
    __formal);
  v29 = v9;
  v13 = v14;
  v12 = v15;
  std::_Copy_backward_opt<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::random_access_iterator_tag>(
    v30,
    v9,
    v28,
    v26,
    (std::random_access_iterator_tag)v24->0,
    v15,
    v14);
  v22 |= 1u;
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(__formal);
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(v32);
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(__that);
  return v30;
}
