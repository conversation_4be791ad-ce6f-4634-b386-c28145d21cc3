/*
 * Function: ?Regist@CRecallRequest@@QEAAEPEAVCPlayer@@PEAVCCharacter@@_N22@Z
 * Address: 0x14024D530
 */

char __fastcall CRecallRequest::Regist(CRecallRequest *this, CPlayer *pkObj, CCharacter *pkDest, bool bRecallParty, bool bStone, bool bBattleModeUse)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleManager *v9; // rax@48
  GUILD_BATTLE::CNormalGuildBattle *v10; // rax@48
  GUILD_BATTLE::CNormalGuildBattleField *v11; // rax@48
  GUILD_BATTLE::CNormalGuildBattleManager *v12; // rax@48
  GUILD_BATTLE::CNormalGuildBattle *v13; // rax@48
  GUILD_BATTLE::CNormalGuildBattleField *v14; // rax@48
  unsigned int v15; // eax@53
  int v16; // eax@56
  int v17; // eax@59
  CGuildRoomSystem *v18; // rax@67
  unsigned int v19; // eax@69
  __int64 v20; // [sp+0h] [bp-68h]@1
  CCharacter *v21; // [sp+20h] [bp-48h]@11
  CPlayer *pkObja; // [sp+28h] [bp-40h]@14
  CMapData *v23; // [sp+30h] [bp-38h]@48
  CMapData *v24; // [sp+38h] [bp-30h]@48
  int v25; // [sp+40h] [bp-28h]@56
  int v26; // [sp+44h] [bp-24h]@58
  int j; // [sp+48h] [bp-20h]@65
  int v28; // [sp+4Ch] [bp-1Ch]@65
  unsigned int dwGuildSerial; // [sp+50h] [bp-18h]@48
  unsigned int v30; // [sp+54h] [bp-14h]@48
  unsigned int v31; // [sp+58h] [bp-10h]@69
  CRecallRequest *v32; // [sp+70h] [bp+8h]@1
  CPlayer *v33; // [sp+78h] [bp+10h]@1
  CCharacter *v34; // [sp+80h] [bp+18h]@1
  bool v35; // [sp+88h] [bp+20h]@1

  v35 = bRecallParty;
  v34 = pkDest;
  v33 = pkObj;
  v32 = this;
  v6 = &v20;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( !pkObj || pkObj->m_ObjID.m_byID || !pkDest || pkDest->m_ObjID.m_byID || (CCharacter *)pkObj == pkDest )
    return 3;
  if ( !pkDest->m_ObjID.m_byID )
  {
    v21 = pkDest;
    if ( BYTE2(pkDest[1].m_fCurPos[2]) )
    {
      if ( LOBYTE(v21[1].m_fCurPos[2]) )
        return 10;
    }
  }
  pkObja = (CPlayer *)pkDest;
  if ( bRecallParty )
  {
    if ( !pkObja->m_bOper || !CPartyPlayer::IsPartyMember(pkObj->m_pPartyMgr, pkObja) )
      return 3;
  }
  else
  {
    if ( !bBattleModeUse && (unsigned __int8)((int (__fastcall *)(CPlayer *))pkObja->vfptr->Is_Battle_Mode)(pkObja) )
      return 18;
    if ( CGameObject::GetCurSecNum((CGameObject *)&pkObja->vfptr) == -1 || pkObja->m_bMapLoading )
      return 21;
    if ( pkObja->m_pDHChannel || v33->m_pDHChannel )
    {
      if ( v33->m_pDHChannel && pkObja->m_pDHChannel )
      {
        if ( pkObja->m_pDHChannel != v33->m_pDHChannel )
          return 22;
      }
      else
      {
        if ( !v33->m_pDHChannel && pkObja->m_pDHChannel )
          return 22;
        if ( v33->m_pDHChannel && !pkObja->m_pDHChannel )
          return 23;
      }
    }
    if ( v33->m_bInGuildBattle || pkObja->m_bInGuildBattle )
    {
      if ( v33->m_bInGuildBattle && !pkObja->m_bInGuildBattle )
        return 25;
      if ( !v33->m_bInGuildBattle || pkObja->m_bInGuildBattle )
        return 9;
      if ( v33->m_bInGuildBattle && pkObja->m_bInGuildBattle )
      {
        dwGuildSerial = CPlayerDB::GetGuildSerial(&v33->m_Param);
        v9 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
        v10 = GUILD_BATTLE::CNormalGuildBattleManager::GetBattleByGuildSerial(v9, dwGuildSerial);
        v11 = GUILD_BATTLE::CNormalGuildBattle::GetField(v10);
        v23 = GUILD_BATTLE::CNormalGuildBattleField::GetMap(v11);
        v30 = CPlayerDB::GetGuildSerial(&pkObja->m_Param);
        v12 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
        v13 = GUILD_BATTLE::CNormalGuildBattleManager::GetBattleByGuildSerial(v12, v30);
        v14 = GUILD_BATTLE::CNormalGuildBattle::GetField(v13);
        v24 = GUILD_BATTLE::CNormalGuildBattleField::GetMap(v14);
        if ( v23 != v24 )
          return 25;
        if ( pkObja->m_bTakeGravityStone )
          return 10;
      }
    }
  }
  if ( CHolyStoneSystem::GetDestroyerState(&g_HolySys) == 2 )
  {
    v15 = CHolyStoneSystem::GetDestroyerSerial(&g_HolySys);
    if ( v15 == pkObja->m_dwObjSerial )
      return 20;
  }
  if ( v35 )
  {
    if ( v33->m_pCurMap != pkObja->m_pCurMap )
      return 11;
  }
  else
  {
    v25 = CMapData::GetLevelLimit(v33->m_pCurMap);
    v16 = ((int (__fastcall *)(CCharacter *))v34->vfptr->GetLevel)(v34);
    if ( v16 < v25 )
      return 19;
    v26 = v33->m_pCurMap->m_pMapSet->m_nUpLevelLim;
    if ( v26 != -1 )
    {
      v17 = ((int (__fastcall *)(CCharacter *))v34->vfptr->GetLevel)(v34);
      if ( v17 > v26 )
        return 19;
    }
  }
  if ( v33->m_Param.m_pGuild )
  {
    v28 = CPlayerDB::GetRaceCode(&v33->m_Param);
    for ( j = 0; j < 2; ++j )
    {
      v18 = CGuildRoomSystem::GetInstance();
      if ( CGuildRoomSystem::GetMapData(v18, v28, j) == v33->m_pCurMap )
      {
        if ( !pkObja->m_Param.m_pGuild )
          return 3;
        v31 = CPlayerDB::GetGuildSerial(&pkObja->m_Param);
        v19 = CPlayerDB::GetGuildSerial(&v33->m_Param);
        if ( v31 != v19 )
          return 3;
        break;
      }
    }
  }
  v32->m_pkOwner = v33;
  v32->m_dwOwnerSerial = v32->m_pkOwner->m_dwObjSerial;
  v32->m_pkDest = pkObja;
  v32->m_dwDestSerial = pkObja->m_dwObjSerial;
  v32->m_dwCloseTime = GetLoopTime() + 60000;
  v32->m_eState = 1;
  v32->m_bRecallParty = v35;
  v32->m_bStone = bStone;
  v32->m_bBattleModeUse = bBattleModeUse;
  return 0;
}
