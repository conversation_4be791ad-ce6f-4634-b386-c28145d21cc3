/*
 * Function: ??$_Uninit_copy@V?$_Vector_iterator@U?$pair@KK@std@@V?$allocator@U?$pair@KK@std@@@2@@std@@PEAU?$pair@KK@2@V?$allocator@U?$pair@KK@std@@@2@@std@@YAPEAU?$pair@KK@0@V?$_Vector_iterator@U?$pair@KK@std@@V?$allocator@U?$pair@KK@std@@@2@@0@0PEAU10@AEAV?$allocator@U?$pair@KK@std@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14038D240
 */

std::pair<unsigned long,unsigned long> *__fastcall std::_Uninit_copy<std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>,std::pair<unsigned long,unsigned long> *,std::allocator<std::pair<unsigned long,unsigned long>>>(std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *_First, std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *_Last, std::pair<unsigned long,unsigned long> *_Dest, std::allocator<std::pair<unsigned long,unsigned long> > *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-58h]@1
  std::pair<unsigned long,unsigned long> *v10; // [sp+20h] [bp-38h]@4
  std::pair<unsigned long,unsigned long> *v11; // [sp+28h] [bp-30h]@7
  __int64 v12; // [sp+30h] [bp-28h]@4
  bool v13; // [sp+38h] [bp-20h]@5
  std::pair<unsigned long,unsigned long> *_Val; // [sp+40h] [bp-18h]@6
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *v15; // [sp+60h] [bp+8h]@1
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *_Right; // [sp+68h] [bp+10h]@1
  std::pair<unsigned long,unsigned long> *_Ptr; // [sp+70h] [bp+18h]@1
  std::allocator<std::pair<unsigned long,unsigned long> > *v18; // [sp+78h] [bp+20h]@1

  v18 = _Al;
  _Ptr = _Dest;
  _Right = _Last;
  v15 = _First;
  v6 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v12 = -2i64;
  v10 = _Dest;
  while ( 1 )
  {
    v13 = std::_Vector_const_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::operator!=(
            (std::_Vector_const_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *)&v15->_Mycont,
            (std::_Vector_const_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *)&_Right->_Mycont);
    if ( !v13 )
      break;
    _Val = std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::operator*(v15);
    std::allocator<std::pair<unsigned long,unsigned long>>::construct(v18, _Ptr, _Val);
    ++_Ptr;
    std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::operator++(v15);
  }
  v11 = _Ptr;
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::~_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>(v15);
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::~_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>(_Right);
  return v11;
}
