/*
 * Function: ?ct_SetGuildGradeByGuildSerial@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140294FA0
 */

bool __fastcall ct_SetGuildGradeByGuildSerial(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int dwGuildSerial; // [sp+20h] [bp-18h]@8
  char v6; // [sp+24h] [bp-14h]@8
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7 )
  {
    if ( s_nWordCount >= 2 )
    {
      dwGuildSerial = 0;
      dwGuildSerial = atoi(s_pwszDstCheat[0]);
      v6 = 0;
      v6 = atoi(s_pwszDstCheat[1]);
      result = CPlayer::dev_SetGuildGradeByGuildSerial(v7, dwGuildSerial, v6);
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
