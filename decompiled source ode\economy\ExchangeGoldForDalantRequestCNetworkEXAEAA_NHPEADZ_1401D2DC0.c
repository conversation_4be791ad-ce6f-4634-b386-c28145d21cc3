/*
 * Function: ?ExchangeGoldForDalantRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D2DC0
 */

char __fastcall CNetworkEX::ExchangeGoldForDalantRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int *v7; // [sp+20h] [bp-18h]@4
  CPlayer *v8; // [sp+28h] [bp-10h]@4

  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = (unsigned int *)pBuf;
  v8 = &g_Player + n;
  if ( !v8->m_bOper || v8->m_pmTrd.bDTradeMode || v8->m_bCorpse )
  {
    result = 1;
  }
  else
  {
    CPlayer::pc_ExchangeGoldForDalant(v8, *v7);
    result = 1;
  }
  return result;
}
