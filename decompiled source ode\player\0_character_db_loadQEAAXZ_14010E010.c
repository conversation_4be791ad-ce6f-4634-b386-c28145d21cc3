/*
 * Function: ??0_character_db_load@@QEAA@XZ
 * Address: 0x14010E010
 */

void __fastcall _character_db_load::_character_db_load(_character_db_load *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _character_db_load *Dst; // [sp+30h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(Dst, 0, 0x6Aui64);
}
