/*
 * Function: ?lower_bound@?$_Hash@V?$_Hmap_traits@HPEBU_TimeItem_fld@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@std@@$0A@@stdext@@@stdext@@QEAA?AV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@2@@std@@AEBH@Z
 * Address: 0x140311AC0
 */

std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *__fastcall stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>>::lower_bound(stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_TimeItem_fld const *> >,0> > *this, std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *result, const int *_Keyval)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *v5; // rax@4
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *v6; // rax@5
  stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_TimeItem_fld const *> >,0> *v7; // rax@6
  std::pair<int const ,_TimeItem_fld const *> *v8; // rdx@6
  const int *v9; // rax@6
  stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_TimeItem_fld const *> >,0> *v10; // rax@7
  std::pair<int const ,_TimeItem_fld const *> *v11; // rdx@7
  const int *v12; // rax@7
  __int64 v14; // [sp+0h] [bp-D8h]@1
  unsigned __int64 _Pos; // [sp+20h] [bp-B8h]@4
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> v16; // [sp+38h] [bp-A0h]@4
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> resulta; // [sp+58h] [bp-80h]@4
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *v18; // [sp+70h] [bp-68h]@10
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> v19; // [sp+78h] [bp-60h]@8
  int v20; // [sp+90h] [bp-48h]@4
  __int64 v21; // [sp+98h] [bp-40h]@4
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *v22; // [sp+A0h] [bp-38h]@4
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *__that; // [sp+A8h] [bp-30h]@4
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *v24; // [sp+B0h] [bp-28h]@8
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *v25; // [sp+B8h] [bp-20h]@8
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *v26; // [sp+C0h] [bp-18h]@8
  stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_TimeItem_fld const *> >,0> > *v27; // [sp+E0h] [bp+8h]@1
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *v28; // [sp+E8h] [bp+10h]@1
  int *_Keyvala; // [sp+F0h] [bp+18h]@1

  _Keyvala = (int *)_Keyval;
  v28 = result;
  v27 = this;
  v3 = &v14;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v21 = -2i64;
  v20 = 0;
  _Pos = stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>>::_Hashval(
           v27,
           _Keyval);
  std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>::_Iterator<0>(&v16);
  v5 = std::vector<std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>,std::allocator<std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>>>::operator[](
         &v27->_Vec,
         _Pos);
  v22 = stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>>::_Get_iter_from_vec(
          v27,
          &resulta,
          v5);
  __that = v22;
  std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>::operator=(
    &v16,
    v22);
  std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>::~_Iterator<0>(&resulta);
  while ( 1 )
  {
    v6 = std::vector<std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>,std::allocator<std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>>>::operator[](
           &v27->_Vec,
           _Pos + 1);
    if ( !std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Const_iterator<0>::operator!=(
            (std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Const_iterator<0> *)&v6->_Mycont,
            (std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Const_iterator<0> *)&v16._Mycont) )
    {
      stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>>::end(
        v27,
        v28);
      v20 |= 2u;
      std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>::~_Iterator<0>(&v16);
      return v28;
    }
    v7 = (stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_TimeItem_fld const *> >,0> *)std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>::operator*(&v16);
    v9 = stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>::_Kfn(
           v7,
           v8);
    if ( !stdext::hash_compare<int,std::less<int>>::operator()(&v27->comp, v9, _Keyvala) )
      break;
    std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>::operator++(&v16);
  }
  v10 = (stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_TimeItem_fld const *> >,0> *)std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>::operator*(&v16);
  v12 = stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>::_Kfn(
          v10,
          v11);
  if ( stdext::hash_compare<int,std::less<int>>::operator()(&v27->comp, _Keyvala, v12) )
  {
    v24 = stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>>::end(
            v27,
            &v19);
    v25 = v24;
    v20 |= 1u;
    v26 = v24;
  }
  else
  {
    v26 = &v16;
  }
  v18 = v26;
  std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>::_Iterator<0>(
    v28,
    v26);
  v20 |= 2u;
  if ( v20 & 1 )
  {
    v20 &= 0xFFFFFFFE;
    std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>::~_Iterator<0>(&v19);
  }
  std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>::~_Iterator<0>(&v16);
  return v28;
}
