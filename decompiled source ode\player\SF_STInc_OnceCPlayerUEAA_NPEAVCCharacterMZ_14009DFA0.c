/*
 * Function: ?SF_STInc_Once@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 * Address: 0x14009DFA0
 */

char __usercall CPlayer::SF_STInc_Once@<al>(CPlayer *this@<rcx>, CCharacter *pDstObj@<rdx>, float fEffectValue@<xmm2>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@7
  char result; // al@8
  __int64 v8; // [sp+0h] [bp-48h]@1
  char v9; // [sp+20h] [bp-28h]@4
  int v10; // [sp+24h] [bp-24h]@7
  int v11; // [sp+28h] [bp-20h]@7
  float v12; // [sp+2Ch] [bp-1Ch]@7
  int v13; // [sp+30h] [bp-18h]@7
  CPlayer *v14; // [sp+58h] [bp+10h]@1

  v14 = (CPlayer *)pDstObj;
  v4 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = 0;
  if ( !pDstObj->m_ObjID.m_byID )
    v9 = 1;
  if ( v9 )
  {
    v10 = CPlayer::GetSP((CPlayer *)pDstObj);
    v11 = CPlayer::GetMaxSP(v14);
    _effect_parameter::GetEff_Rate(&v14->m_EP, 20);
    v12 = fEffectValue * a4;
    v13 = (signed int)ffloor((float)v11 * (float)(fEffectValue * a4));
    CPlayer::SetSP(v14, v13 + v10, 0);
    v6 = CPlayer::GetSP(v14);
    if ( v6 - v10 >= 20 )
    {
      CPlayer::SendMsg_SetSPInform(v14);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
