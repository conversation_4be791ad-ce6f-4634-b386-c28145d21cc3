/*
 * Function: ?SendMsg_UnitReturnResult@CPlayer@@QEAAXEK@Z
 * Address: 0x1400DB3F0
 */

void __fastcall CPlayer::SendMsg_UnitReturnResult(CPlayer *this, char byRetCode, unsigned int dwPayDalant)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  unsigned int v7; // [sp+39h] [bp-4Fh]@4
  unsigned int v8; // [sp+3Dh] [bp-4Bh]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v10; // [sp+65h] [bp-23h]@4
  CPlayer *v11; // [sp+90h] [bp+8h]@1

  v11 = this;
  v3 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szMsg = byRetCode;
  v7 = dwPayDalant;
  v8 = CPlayerDB::GetDalant(&v11->m_Param);
  pbyType = 23;
  v10 = 16;
  CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, &szMsg, 9u);
}
