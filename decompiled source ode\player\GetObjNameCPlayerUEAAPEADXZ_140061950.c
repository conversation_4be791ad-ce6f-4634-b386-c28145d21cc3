/*
 * Function: ?GetObjName@CPlayer@@UEAAPEADXZ
 * Address: 0x140061950
 */

char *__fastcall CPlayer::GetObjName(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  __int64 v5; // [sp+0h] [bp-78h]@1
  char *v6; // [sp+20h] [bp-58h]@4
  int v7; // [sp+28h] [bp-50h]@4
  int v8; // [sp+30h] [bp-48h]@4
  int v9; // [sp+38h] [bp-40h]@4
  int v10; // [sp+40h] [bp-38h]@4
  int v11; // [sp+44h] [bp-34h]@4
  int v12; // [sp+48h] [bp-30h]@4
  char *v13; // [sp+50h] [bp-28h]@4
  char *v14; // [sp+58h] [bp-20h]@4
  CGameObjectVtbl *v15; // [sp+60h] [bp-18h]@4
  CPlayer *v16; // [sp+80h] [bp+8h]@1

  v16 = this;
  v1 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = (signed int)ffloor(v16->m_fCurPos[2]);
  v11 = (signed int)ffloor(v16->m_fCurPos[1]);
  v12 = (signed int)ffloor(v16->m_fCurPos[0]);
  v13 = v16->m_pCurMap->m_pMapSet->m_strCode;
  v14 = CPlayerDB::GetCharNameA(&v16->m_Param);
  v15 = v16->vfptr;
  v3 = ((int (__fastcall *)(CPlayer *))v15->GetObjRace)(v16);
  v9 = v10;
  v8 = v11;
  v7 = v12;
  v6 = v13;
  sprintf(szName, "[PLAYER][%d] >> %s (pos: %s {%d, %d, %d})", (unsigned int)v3, v14);
  return szName;
}
