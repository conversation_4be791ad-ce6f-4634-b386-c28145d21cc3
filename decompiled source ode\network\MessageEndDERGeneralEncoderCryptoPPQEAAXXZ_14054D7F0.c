/*
 * Function: ?MessageEnd@DERGeneralEncoder@CryptoPP@@QEAAXXZ
 * Address: 0x14054D7F0
 */

void __fastcall CryptoPP::DERGeneralEncoder::MessageEnd(CryptoPP::DERGeneralEncoder *this)
{
  struct CryptoPP::BufferedTransformation *v1; // ST20_8@1
  CryptoPP::DERGeneralEncoder *v2; // [sp+40h] [bp+8h]@1

  v2 = this;
  this->m_finished = 1;
  v1 = (struct CryptoPP::BufferedTransformation *)CryptoPP::ByteQueue::CurrentSize((CryptoPP::ByteQueue *)&this->vfptr);
  CryptoPP::BufferedTransformation::Put(v2->m_outQueue, v2->m_asnTag, 1);
  CryptoPP::DERLengthEncode((CryptoPP *)v2->m_outQueue, v1);
  CryptoPP::BufferedTransformation::TransferTo(
    (__int64)v2,
    (__int64)v2->m_outQueue,
    -1i64,
    (__int64)&CryptoPP::BufferedTransformation::NULL_CHANNEL);
}
