/*
 * Function: ??$?0U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@?$allocator@U_Node@?$_List_nod@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@2@@std@@@std@@QEAA@AEBV?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@1@@Z
 * Address: 0x140316B50
 */

void __fastcall std::allocator<std::_List_nod<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Node>::allocator<std::_List_nod<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Node>(std::allocator<std::_List_nod<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Node> *this, std::allocator<std::pair<int const ,_TimeItem_fld const *> > *__formal)
{
  ;
}
