/*
 * Function: ??1CUnmannedTraderUserInfo@@QEAA@XZ
 * Address: 0x140353160
 */

void __fastcall CUnmannedTraderUserInfo::~CUnmannedTraderUserInfo(CUnmannedTraderUserInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CUnmannedTraderUserInfo *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  v5->m_wInx = -1;
  v5->m_byMaxRegistCnt = 0;
  CUnmannedTraderUserInfo::Clear(v5);
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v5->m_vecLoadItemInfo);
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v5->m_vecRegistItemInfo);
  CUnmannedTraderRequestLimiter::~CUnmannedTraderRequestLimiter(&v5->m_kRequestState);
}
