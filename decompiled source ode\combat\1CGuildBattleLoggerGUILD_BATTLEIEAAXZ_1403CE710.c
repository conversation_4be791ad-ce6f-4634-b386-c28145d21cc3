/*
 * Function: ??1CGuildBattleLogger@GUILD_BATTLE@@IEAA@XZ
 * Address: 0x1403CE710
 */

void __fastcall GUILD_BATTLE::CGuildBattleLogger::~CGuildBattleLogger(GUILD_BATTLE::CGuildBattleLogger *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  CLogFile *v4; // [sp+20h] [bp-28h]@5
  CLogFile *v5; // [sp+28h] [bp-20h]@5
  GUILD_BATTLE::CGuildBattleLogger *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_pkLogger )
  {
    v5 = v6->m_pkLogger;
    v4 = v5;
    if ( v5 )
      CLogFile::`scalar deleting destructor'(v4, 1u);
  }
}
