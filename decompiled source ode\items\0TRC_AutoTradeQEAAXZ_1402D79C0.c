/*
 * Function: ??0TRC_AutoTrade@@QEAA@XZ
 * Address: 0x1402D79C0
 */

void __fastcall TRC_AutoTrade::TRC_AutoTrade(TRC_AutoTrade *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-58h]@1
  _SYSTEMTIME SystemTime; // [sp+28h] [bp-30h]@4
  __int64 v5; // [sp+48h] [bp-10h]@4
  TRC_AutoTrade *v6; // [sp+60h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = -2i64;
  v6->m_bInit = 0;
  v6->m_bChangeTaxRate = 0;
  v6->m_fCommonTaxRate = FLOAT_0_050000001;
  v6->m_pOwnerGuild = 0i64;
  ControllerTaxRate::ControllerTaxRate(&v6->m_Controller);
  CLogFile::CLogFile(&v6->m_sysLog);
  CLogFile::CLogFile(&v6->m_serviceLog);
  _suggested_matter_change_taxrate::_suggested_matter_change_taxrate(&v6->m_suggested);
  GetLocalTime(&SystemTime);
  v6->m_byCurDay = SystemTime.wDay;
  v6->m_wCurMonth = SystemTime.wMonth;
  v6->m_wCurYear = SystemTime.wYear;
  *(_QWORD *)&v6->m_dIncomeMoney = 0i64;
  v6->m_dwTrade = 0;
}
