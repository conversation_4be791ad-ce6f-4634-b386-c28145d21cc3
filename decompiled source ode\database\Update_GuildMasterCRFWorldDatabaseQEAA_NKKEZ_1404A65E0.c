/*
 * Function: ?Update_GuildMaster@CRFWorldDatabase@@QEAA_NKKE@Z
 * Address: 0x1404A65E0
 */

bool __fastcall CRFWorldDatabase::Update_GuildMaster(CRFWorldDatabase *this, unsigned int dwGuild_Serial, unsigned int dwGuildMaster_Serial, char byGuildMaster_PrevGrade)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-168h]@1
  int v8; // [sp+20h] [bp-148h]@4
  char Dest; // [sp+40h] [bp-128h]@4
  unsigned __int64 v10; // [sp+150h] [bp-18h]@4
  CRFWorldDatabase *v11; // [sp+170h] [bp+8h]@1

  v11 = this;
  v4 = &v7;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v10 = (unsigned __int64)&v7 ^ _security_cookie;
  v8 = (unsigned __int8)byGuildMaster_PrevGrade;
  sprintf(&Dest, "{ CALL pUpdate_GuildMaster(%d, %d, %d) }", dwGuild_Serial, dwGuildMaster_Serial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 1);
}
