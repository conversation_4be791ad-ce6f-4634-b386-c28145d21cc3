/*
 * Function: ??0Hash<PERSON><PERSON><PERSON>@CryptoPP@@QEAA@AEAVHashTransformation@1@PEAVBufferedTransformation@1@_NH@Z
 * Address: 0x140623D40
 */

CryptoPP::HashFilter *__fastcall CryptoPP::HashFilter::HashFilter(CryptoPP::HashFilter *this, struct CryptoPP::HashTransformation *a2, struct CryptoPP::BufferedTransformation *a3, bool a4, int a5)
{
  CryptoPP::HashFilter *v6; // [sp+40h] [bp+8h]@1
  struct CryptoPP::HashTransformation *v7; // [sp+48h] [bp+10h]@1
  struct CryptoPP::BufferedTransformation *v8; // [sp+50h] [bp+18h]@1
  bool v9; // [sp+58h] [bp+20h]@1

  v9 = a4;
  v8 = a3;
  v7 = a2;
  v6 = this;
  CryptoPP::Bufferless<CryptoPP::Filter>::B<PERSON>erless<CryptoPP::Filter>();
  CryptoPP::FilterPutSpaceHelper::FilterPutSpaceHelper((CryptoPP::FilterPutSpaceHelper *)&v6->m_tempSpace);
  v6->vfptr = (CryptoPP::ClonableVtbl *)&CryptoPP::HashFilter::`vftable'{for `CryptoPP::Algorithm'};
  v6->vfptr = (CryptoPP::WaitableVtbl *)&CryptoPP::HashFilter::`vftable'{for `CryptoPP::Waitable'};
  v6->m_hashModule = v7;
  v6->m_putMessage = v9;
  v6->m_truncatedDigestSize = a5;
  CryptoPP::Filter::Detach((__int64)v6, (__int64)v8);
  return v6;
}
