/*
 * Function: j_?size@?$vector@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@QEBA_KXZ
 * Address: 0x140010C49
 */

unsigned __int64 __fastcall std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::size(std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *this)
{
  return std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::size(this);
}
