/*
 * Function: ?Initialize@Redirector@CryptoPP@@UEAAXAEBVNameValuePairs@2@H@Z
 * Address: 0x1405FB130
 */

void __fastcall CryptoPP::Redirector::Initialize(CryptoPP::Redirector *this, const struct CryptoPP::NameValuePairs *a2, unsigned int a3)
{
  CryptoPP::BufferedTransformation *v3; // rax@1
  CryptoPP::Redirector *v4; // [sp+30h] [bp+8h]@1
  const struct CryptoPP::NameValuePairs *v5; // [sp+38h] [bp+10h]@1
  unsigned int v6; // [sp+40h] [bp+18h]@1

  v6 = a3;
  v5 = a2;
  v4 = this;
  LODWORD(v3) = CryptoPP::NameValuePairs::GetValueWithDefault<CryptoPP::BufferedTransformation *>(
                  a2,
                  "RedirectionTargetPointer",
                  0i64);
  v4->m_target = v3;
  v4->m_behavior = CryptoPP::NameValuePairs::GetIntValueWithDefault(
                     (CryptoPP::NameValuePairs *)v5,
                     "RedirectionBehavior",
                     3u);
  if ( v4->m_target )
  {
    if ( CryptoPP::Redirector::GetPassSignals(v4) )
      ((void (__fastcall *)(_QWORD, const struct CryptoPP::NameValuePairs *, _QWORD))v4->m_target->vfptr[5].__vecDelDtor)(
        v4->m_target,
        v5,
        v6);
  }
}
