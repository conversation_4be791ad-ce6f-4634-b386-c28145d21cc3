/*
 * Function: ?CheckGoal@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAXHKKH@Z
 * Address: 0x1403D4910
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleManager::CheckGoal(GUILD_BATTLE::CNormalGuildBattleManager *this, int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial, int iPortalInx)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-D8h]@1
  char v8; // [sp+30h] [bp-A8h]@4
  char szMsg; // [sp+48h] [bp-90h]@5
  char v10; // [sp+49h] [bp-8Fh]@5
  char pbyType; // [sp+A4h] [bp-34h]@5
  char v12; // [sp+A5h] [bp-33h]@5
  unsigned __int64 v13; // [sp+C0h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleManager *v14; // [sp+E0h] [bp+8h]@1
  int dwClientIndex; // [sp+E8h] [bp+10h]@1

  dwClientIndex = n;
  v14 = this;
  v5 = &v7;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v13 = (unsigned __int64)&v7 ^ _security_cookie;
  v8 = GUILD_BATTLE::CNormalGuildBattleManager::ProcCheckGoal(v14, dwGuildSerial, dwCharacSerial, iPortalInx);
  if ( v8 )
  {
    szMsg = v8;
    memset(&v10, 0, 0x39ui64);
    pbyType = 27;
    v12 = 74;
    CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &szMsg, 0x3Au);
  }
}
