/*
 * Function: ?IncreaseVersion@CUnmannedTraderGroupItemInfoTable@@QEAA_NEGEE@Z
 * Address: 0x14036B580
 */

char __fastcall CUnmannedTraderGroupItemInfoTable::IncreaseVersion(CUnmannedTraderGroupItemInfoTable *this, char byTableCode, unsigned __int16 wItemTableIndex, char byRegistDivision, char byRegistClass)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v8; // [sp+0h] [bp-B8h]@1
  char *byClass; // [sp+20h] [bp-98h]@8
  int v10; // [sp+28h] [bp-90h]@8
  char *v11; // [sp+30h] [bp-88h]@10
  int v12; // [sp+38h] [bp-80h]@15
  int v13; // [sp+40h] [bp-78h]@15
  int v14; // [sp+48h] [bp-70h]@15
  _base_fld *v15; // [sp+50h] [bp-68h]@9
  char byDivision; // [sp+64h] [bp-54h]@11
  char v17; // [sp+84h] [bp-34h]@11
  int v18; // [sp+94h] [bp-24h]@10
  int v19; // [sp+98h] [bp-20h]@12
  int v20; // [sp+9Ch] [bp-1Ch]@17
  CUnmannedTraderGroupItemInfoTable *v21; // [sp+C0h] [bp+8h]@1
  char v22; // [sp+C8h] [bp+10h]@1
  unsigned __int16 v23; // [sp+D0h] [bp+18h]@1
  char v24; // [sp+D8h] [bp+20h]@1

  v24 = byRegistDivision;
  v23 = wItemTableIndex;
  v22 = byTableCode;
  v21 = this;
  v5 = &v8;
  for ( i = 42i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( (unsigned __int8)byTableCode != 255 && wItemTableIndex != 0xFFFF )
  {
    if ( (signed int)(unsigned __int8)byTableCode < 37 )
    {
      v15 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)byTableCode, wItemTableIndex);
      if ( v15 )
      {
        byDivision = -1;
        v17 = -1;
        if ( CUnmannedTraderGroupIDInfo::GetGroupID(&v21->m_kGroupIDInfo, v22, v23, &byDivision, &v17) )
        {
          if ( (unsigned __int8)v24 == (unsigned __int8)byDivision
            && (unsigned __int8)byRegistClass == (unsigned __int8)v17 )
          {
            if ( CUnmannedTraderGroupVersionInfo::IncreaseVersion(&v21->m_kVerInfo, byDivision, v17) )
            {
              result = 1;
            }
            else
            {
              v20 = (unsigned __int8)v22;
              v12 = (unsigned __int8)v17;
              LODWORD(v11) = (unsigned __int8)byDivision;
              v10 = (unsigned __int8)byRegistClass;
              LODWORD(byClass) = (unsigned __int8)v24;
              CUnmannedTraderGroupItemInfoTable::Log(
                v21,
                "UnmannedTraderGroupItemInfoTable::IncreaseVersion( BYTE byTableCode(%u), WORD wItemTableIndex(%u), BYTE "
                "byRegistDivision(%u), BYTE byRegistClass(%u) )\r\n"
                "\t\tm_kVerInfo.IncreaseVersion( byDivision(%u), byClass(%u) ) Fail!\r\n",
                (unsigned __int8)v22,
                v23);
              result = 0;
            }
          }
          else
          {
            v14 = (unsigned __int8)v17;
            v13 = (unsigned __int8)byRegistClass;
            v12 = (unsigned __int8)byDivision;
            LODWORD(v11) = (unsigned __int8)v24;
            v10 = (unsigned __int8)byRegistClass;
            LODWORD(byClass) = (unsigned __int8)v24;
            CUnmannedTraderGroupItemInfoTable::Log(
              v21,
              "UnmannedTraderGroupItemInfoTable::IncreaseVersion( BYTE byTableCode(%u), WORD wItemTableIndex(%u), BYTE by"
              "RegistDivision(%u), BYTE byRegistClass(%u) )\r\n"
              "\t\t( byRegistDivision(%u) != byDivision(%u) || byRegistClass(%u) != byClass(%u) ) Invalid!\r\n",
              (unsigned __int8)v22,
              v23);
            result = 0;
          }
        }
        else
        {
          v19 = (unsigned __int8)v22;
          v11 = v15->m_strCode;
          v10 = (unsigned __int8)byRegistClass;
          LODWORD(byClass) = (unsigned __int8)v24;
          CUnmannedTraderGroupItemInfoTable::Log(
            v21,
            "UnmannedTraderGroupItemInfoTable::IncreaseVersion( BYTE byTableCode(%u), WORD wItemTableIndex(%u), BYTE byRe"
            "gistDivision(%u), BYTE byRegistClass(%u) )\r\n"
            "\t\tm_kGroupIDInfo.GetDivsionAndClassID( byTableCode, pFld->m_strCode(%s) Invalid!\r\n",
            (unsigned __int8)v22,
            v23);
          result = 0;
        }
      }
      else
      {
        v18 = (unsigned __int8)v22;
        v11 = (char *)4;
        v10 = (unsigned __int8)byRegistClass;
        LODWORD(byClass) = (unsigned __int8)v24;
        CUnmannedTraderGroupItemInfoTable::Log(
          v21,
          "UnmannedTraderGroupItemInfoTable::IncreaseVersion( BYTE byTableCode(%u), WORD wItemTableIndex(%u), BYTE byRegi"
          "stDivision(%u), BYTE byRegistClass(%u) )\r\n"
          "\t\tg_Main.m_tblItemData[byTableCode].GetRecord( wItemTableIndex ) NULL!\r\n",
          (unsigned __int8)v22,
          v23);
        result = 0;
      }
    }
    else
    {
      v10 = (unsigned __int8)byRegistClass;
      LODWORD(byClass) = (unsigned __int8)byRegistDivision;
      CUnmannedTraderGroupItemInfoTable::Log(
        v21,
        "UnmannedTraderGroupItemInfoTable::IncreaseVersion( BYTE byTableCode(%u), WORD wItemTableIndex(%u), BYTE byRegist"
        "Division(%u), BYTE byRegistClass(%u) )\r\n"
        "\t\titem_tbl_num <= byTableCode!\r\n",
        (unsigned __int8)byTableCode,
        wItemTableIndex);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
