/*
 * Function: ?IsPreAttackAbleMon@CMonster@@QEAAHXZ
 * Address: 0x140155460
 */

__int64 __fastcall CMonster::IsPreAttackAbleMon(CMonster *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // [sp+0h] [bp-18h]@1
  CMonster *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  return *(float *)&v5->m_pRecordSet[29].m_strCode[28] > 5.0;
}
