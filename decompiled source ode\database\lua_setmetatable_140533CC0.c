/*
 * Function: lua_setmetatable
 * Address: 0x140533CC0
 */

signed __int64 __fastcall lua_setmetatable(__int64 a1, signed int a2)
{
  __int64 v2; // rbx@1
  _DWORD *v3; // rax@1
  __int64 v4; // rdx@1
  _DWORD *v5; // r10@1
  __int64 v6; // rdx@2
  __int64 v7; // rax@4

  v2 = a1;
  v3 = sub_140532CD0(a1, a2);
  v4 = *(_QWORD *)(v2 + 16);
  v5 = v3;
  if ( *(_DWORD *)(v4 - 8) )
    v6 = *(_QWORD *)(v4 - 16);
  else
    v6 = 0i64;
  v7 = v3[2];
  if ( (_DWORD)v7 == 5 )
  {
    *(_QWORD *)(*(_QWORD *)v5 + 16i64) = v6;
    if ( v6 && *(_BYTE *)(v6 + 9) & 3 && *(_BYTE *)(*(_QWORD *)v5 + 9i64) & 4 )
      luaC_barrierback(v2);
  }
  else
  {
    if ( (_DWORD)v7 != 7 )
    {
      *(_QWORD *)(*(_QWORD *)(v2 + 32) + 8 * v7 + 224) = v6;
      *(_QWORD *)(v2 + 16) -= 16i64;
      return 1i64;
    }
    *(_QWORD *)(*(_QWORD *)v5 + 16i64) = v6;
    if ( v6 && *(_BYTE *)(v6 + 9) & 3 && *(_BYTE *)(*(_QWORD *)v5 + 9i64) & 4 )
    {
      luaC_barrierf(v2);
      *(_QWORD *)(v2 + 16) -= 16i64;
      return 1i64;
    }
  }
  *(_QWORD *)(v2 + 16) -= 16i64;
  return 1i64;
}
