/*
 * Function: j_??0?$_Ranit@PEAVCUnmannedTraderSubClassInfo@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@AEBU01@@Z
 * Address: 0x1400138D1
 */

void __fastcall std::_Ranit<CUnmannedTraderSubClassInfo *,__int64,CUnmannedTraderSubClassInfo * const *,CUnmannedTraderSubClassInfo * const &>::_Ranit<CUnmannedTraderSubClassInfo *,__int64,CUnmannedTraderSubClassInfo * const *,CUnmannedTraderSubClassInfo * const &>(std::_Ranit<CUnmannedTraderSubClassInfo *,__int64,CUnmannedTraderSubClassInfo * const *,CUnmannedTraderSubClassInfo * const &> *this, std::_Ranit<CUnmannedTraderSubClassInfo *,__int64,CUnmannedTraderSubClassInfo * const *,CUnmannedTraderSubClassInfo * const &> *__that)
{
  std::_Ranit<CUnmannedTraderSubClassInfo *,__int64,CUnmannedTraderSubClassInfo * const *,CUnmannedTraderSubClassInfo * const &>::_Ranit<CUnmannedTraderSubClassInfo *,__int64,CUnmannedTraderSubClassInfo * const *,CUnmannedTraderSubClassInfo * const &>(
    this,
    __that);
}
