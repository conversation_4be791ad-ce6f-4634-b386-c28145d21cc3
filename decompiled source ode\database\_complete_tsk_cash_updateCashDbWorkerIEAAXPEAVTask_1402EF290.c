/*
 * Function: ?_complete_tsk_cash_update@CashDbWorker@@IEAAXPEAVTask@@@Z
 * Address: 0x1402EF290
 */

void __fastcall CashDbWorker::_complete_tsk_cash_update(CashDbWorker *this, Task *pkTsk)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@11
  unsigned int v5; // eax@12
  char *v6; // rax@12
  char *v7; // rax@16
  unsigned int v8; // edx@16
  CashItemRemoteStore *v9; // rax@21
  CashItemRemoteStore *v10; // rax@21
  CashItemRemoteStore *v11; // rax@21
  CAsyncLogger *v12; // rax@22
  char *v13; // rdi@26
  CAsyncLogger *v14; // rax@26
  char *v15; // rax@29
  __int64 v16; // [sp+0h] [bp-748h]@1
  char *v17; // [sp+20h] [bp-728h]@8
  char *v18; // [sp+28h] [bp-720h]@8
  unsigned int v19; // [sp+30h] [bp-718h]@16
  char *v20; // [sp+38h] [bp-710h]@22
  char *v21; // [sp+40h] [bp-708h]@22
  unsigned __int64 v22; // [sp+48h] [bp-700h]@22
  int v23; // [sp+50h] [bp-6F8h]@22
  int v24; // [sp+58h] [bp-6F0h]@22
  int v25; // [sp+60h] [bp-6E8h]@22
  _param_cash_update *psheet; // [sp+70h] [bp-6D8h]@4
  CPlayer *pOne; // [sp+78h] [bp-6D0h]@4
  int v28; // [sp+80h] [bp-6C8h]@12
  _param_cash_update sheetplus; // [sp+A0h] [bp-6A8h]@12
  char v30; // [sp+614h] [bp-134h]@12
  int nNum; // [sp+618h] [bp-130h]@12
  _param_cash_update::__item *pGII; // [sp+620h] [bp-128h]@15
  bool v33; // [sp+628h] [bp-120h]@17
  _param_cash_update::__item *v34; // [sp+630h] [bp-118h]@24
  char v35; // [sp+640h] [bp-108h]@24
  __int64 v36; // [sp+680h] [bp-C8h]@4
  CLogFile *v37; // [sp+688h] [bp-C0h]@8
  int v38; // [sp+690h] [bp-B8h]@16
  char *v39; // [sp+698h] [bp-B0h]@16
  CLogFile *v40; // [sp+6A0h] [bp-A8h]@16
  unsigned int dwIndex; // [sp+6A8h] [bp-A0h]@21
  unsigned int v42; // [sp+6ACh] [bp-9Ch]@21
  int v43; // [sp+6B0h] [bp-98h]@22
  int v44; // [sp+6B4h] [bp-94h]@22
  char *v45; // [sp+6B8h] [bp-90h]@22
  char *v46; // [sp+6C0h] [bp-88h]@22
  CUserDB *v47; // [sp+6C8h] [bp-80h]@22
  char *v48; // [sp+6D0h] [bp-78h]@22
  unsigned int v49; // [sp+6D8h] [bp-70h]@22
  char *v50; // [sp+6E0h] [bp-68h]@22
  int v51; // [sp+6E8h] [bp-60h]@26
  int v52; // [sp+6ECh] [bp-5Ch]@26
  char *v53; // [sp+6F0h] [bp-58h]@26
  char *v54; // [sp+6F8h] [bp-50h]@26
  CUserDB *v55; // [sp+700h] [bp-48h]@26
  unsigned int v56; // [sp+708h] [bp-40h]@26
  char *v57; // [sp+710h] [bp-38h]@26
  int v58; // [sp+718h] [bp-30h]@29
  CLogFile *v59; // [sp+720h] [bp-28h]@29
  unsigned __int64 v60; // [sp+728h] [bp-20h]@4
  CashDbWorker *v61; // [sp+750h] [bp+8h]@1
  Task *v62; // [sp+758h] [bp+10h]@1

  v62 = pkTsk;
  v61 = this;
  v2 = &v16;
  for ( i = 462i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v36 = -2i64;
  v60 = (unsigned __int64)&v16 ^ _security_cookie;
  psheet = (_param_cash_update *)Task::GetTaskBuf(pkTsk);
  pOne = CashDbWorker::_get_player(v61, psheet->in_wSockIndex, psheet->in_dwAvatorSerial);
  if ( pOne && pOne->m_bOper && pOne->m_pUserDB )
  {
    if ( Task::GetRetCode(v62) )
    {
      v4 = Task::GetRetCode(v62);
      ICsSendInterface::SendMsg_Error(psheet->in_wSockIndex, v4);
    }
    else
    {
      v28 = 0;
      v5 = CPlayerDB::GetCharSerial(&pOne->m_Param);
      _param_cash_update::_param_cash_update(&sheetplus, pOne->m_pUserDB->m_dwAccountSerial, v5, pOne->m_ObjID.m_wIndex);
      strcpy_s(sheetplus.in_szAcc, 0xDui64, pOne->m_pUserDB->m_szAccountID);
      strcpy_s(sheetplus.in_szSvrName, 0x21ui64, byte_1799C5B78);
      v6 = CPlayerDB::GetCharNameW(&pOne->m_Param);
      strcpy_s(sheetplus.in_szAvatorName, 0x11ui64, v6);
      v30 = 0;
      for ( nNum = 0; nNum < psheet->in_nNum10; ++nNum )
      {
        pGII = &psheet->in_item[(signed __int64)nNum];
        if ( psheet->in_item[(signed __int64)nNum].out_cState )
        {
          ICsSendInterface::SendMsg_Error(psheet->in_wSockIndex, pGII->out_cState);
          v38 = CPlayer::GetCashAmount(pOne);
          v39 = pGII->in_strItemCode;
          v7 = CPlayerDB::GetCharNameA(&pOne->m_Param);
          v8 = pGII->out_cState;
          v40 = v61->_kLogger;
          v19 = v38;
          v18 = v39;
          v17 = v7;
          CLogFile::Write(
            v61->_kLogger,
            "Failed use cash(%d) : id:%s name:%s pscode:%s (remain:%d)",
            v8,
            psheet->in_szAcc);
        }
        else
        {
          v33 = CashDbWorker::_insert_to_inven(v61, pOne, pGII);
          if ( !v33 )
          {
            ICsSendInterface::SendMsg_Error(psheet->in_wSockIndex, 15);
            CashDbWorker::_delete_from_inven(v61, pOne, psheet, nNum);
            ((void (__fastcall *)(CashDbWorker *, _param_cash_update *))v61->vfptr[4].~Worker)(v61, psheet);
            _param_cash_update::~_param_cash_update(&sheetplus);
            return;
          }
          if ( pGII->in_nEventType == 5 && psheet->in_bLimited_Sale == 1 )
          {
            dwIndex = pGII->in_wItemIdx;
            v9 = CashItemRemoteStore::Instance();
            CashItemRemoteStore::Set_LimitedSale_count(v9, pGII->in_byTblCode, dwIndex);
            v10 = CashItemRemoteStore::Instance();
            CashItemRemoteStore::Set_DB_LimitedSale_Event(v10);
            v42 = pGII->in_wItemIdx;
            v11 = CashItemRemoteStore::Instance();
            CashItemRemoteStore::LimitedSale_check_count(v11, pGII->in_byTblCode, v42);
          }
          v43 = pGII->in_nEventType;
          v44 = pGII->in_byOverlapNum;
          v45 = GetItemKorName(pGII->in_byTblCode, pGII->in_wItemIdx);
          v46 = pGII->in_strItemCode;
          v47 = pOne->m_pUserDB;
          v48 = pOne->m_pUserDB->m_szAccountID;
          v49 = CPlayerDB::GetCharSerial(&pOne->m_Param);
          v50 = CPlayerDB::GetCharNameA(&pOne->m_Param);
          v12 = CAsyncLogger::Instance();
          v25 = v43;
          v24 = v44;
          v23 = pGII->in_nPrice;
          v22 = pGII->in_lnUID;
          v21 = v45;
          v20 = v46;
          v19 = v47->m_dwAccountSerial;
          v18 = v48;
          LODWORD(v17) = v49;
          CAsyncLogger::FormatLog(
            v12,
            10,
            "[Name: %s(%d) AccountID: %s(%d)] : %s(%s) [UID: %I64u] [Price:%d Num:%d Event: %d]",
            v50);
          if ( pGII->in_nEventType == 3 && psheet->in_bOneN_One == 1 )
          {
            v34 = &psheet->in_item[(signed __int64)nNum];
            v34->in_lnUID = UIDGenerator::getuid(unk_1799C608C);
            v34->in_nPrice = 0;
            qmemcpy(&v35, &psheet->in_item[(signed __int64)nNum], 0x40ui64);
            qmemcpy(
              &sheetplus.in_item[(unsigned __int64)(unsigned __int8)v30],
              &v35,
              sizeof(sheetplus.in_item[(unsigned __int64)(unsigned __int8)v30]));
            sheetplus.in_item[(unsigned __int64)(unsigned __int8)v30].in_lnUID = v34->in_lnUID;
            sheetplus.in_item[(unsigned __int64)(unsigned __int8)v30].in_nPrice = v34->in_nPrice;
            sheetplus.in_item[(unsigned __int64)(unsigned __int8)v30].in_nLendType = v34->in_nEventType;
            sheetplus.in_item[(unsigned __int64)(unsigned __int8)v30++].in_dwLendTime = v34->in_dwLendTime;
            if ( !CashDbWorker::_insert_to_inven(v61, pOne, v34) )
            {
              ICsSendInterface::SendMsg_Error(psheet->in_wSockIndex, 15);
              CashDbWorker::_delete_from_inven(v61, pOne, psheet, nNum);
              ((void (__fastcall *)(CashDbWorker *, _param_cash_update *))v61->vfptr[4].~Worker)(v61, psheet);
              _param_cash_update::~_param_cash_update(&sheetplus);
              return;
            }
            v51 = v34->in_nEventType;
            v52 = v34->in_byOverlapNum;
            v53 = GetItemKorName(v34->in_byTblCode, v34->in_wItemIdx);
            v54 = v34->in_strItemCode;
            v55 = pOne->m_pUserDB;
            v13 = pOne->m_pUserDB->m_szAccountID;
            v56 = CPlayerDB::GetCharSerial(&pOne->m_Param);
            v57 = CPlayerDB::GetCharNameA(&pOne->m_Param);
            v14 = CAsyncLogger::Instance();
            v25 = v51;
            v24 = v52;
            v23 = v34->in_nPrice;
            v22 = v34->in_lnUID;
            v21 = v53;
            v20 = v54;
            v19 = v55->m_dwAccountSerial;
            v18 = v13;
            LODWORD(v17) = v56;
            CAsyncLogger::FormatLog(
              v14,
              10,
              "[Name: %s(%d) AccountID: %s(%d)] [Insert 1+1 Event CASTITEM] : %s(%s) [UID: %I64u] [Price:%d Num:%d Event: %d]",
              v57);
          }
          ++v28;
        }
      }
      sheetplus.in_nNum10 = v30;
      if ( psheet->out_nCashAmount < 0 )
      {
        psheet->out_nCashAmount = 0;
        v58 = CPlayer::GetCashAmount(pOne);
        v15 = CPlayerDB::GetCharNameA(&pOne->m_Param);
        v59 = v61->_kLogger;
        LODWORD(v17) = v58;
        CLogFile::Write(v61->_kLogger, "Wrong CashAmount : id:%s name:%s (Local:%d)", psheet->in_szAcc, v15);
      }
      if ( v28 > 0 )
      {
        ICsSendInterface::SendMsg_BuyCashItem(pOne->m_ObjID.m_wIndex, psheet, &sheetplus);
        CPlayer::SetCashAmount(pOne, psheet->out_nCashAmount);
        CPlayer::DeleteCouponItem(pOne, psheet->in_CouponItem, psheet->in_nCouponCnt);
      }
      _param_cash_update::~_param_cash_update(&sheetplus);
    }
  }
  else
  {
    CLogFile::Write(
      v61->_kLogger,
      "CashDbWorker::_complete_tsk_cash_update : Invalid Character!\r\n"
      "( !pOne || !pOne->m_bOper || NULL == pOne->m_pUserDB ) Invalid!");
    if ( !Task::GetRetCode(v62) )
    {
      ((void (__fastcall *)(CashDbWorker *, _param_cash_update *))v61->vfptr[4].~Worker)(v61, psheet);
      v37 = v61->_kLogger;
      v18 = psheet->in_szAvatorName;
      LODWORD(v17) = psheet->in_dwAvatorSerial;
      CLogFile::Write(
        v61->_kLogger,
        "CashDbWorker::_complete_tsk_cash_update : Account:%u(%s) Character:%u(%s) -> _all_rollback!",
        psheet->in_dwAccountSerial,
        psheet->in_szAcc);
    }
  }
}
