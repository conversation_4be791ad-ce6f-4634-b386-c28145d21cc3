/*
 * Function: ?ParameterSupported@?$DL_CryptoSystemBase@VPK_Encryptor@CryptoPP@@V?$DL_PublicKey@VInteger@CryptoPP@@@2@@CryptoPP@@UEBA_NPEBD@Z
 * Address: 0x140635130
 */

bool __fastcall CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::Integer>>::ParameterSupported(__int64 a1, __int64 a2)
{
  int (__fastcall ***v2)(_QWORD, _QWORD); // rax@1
  int (__fastcall ***v3)(_QWORD, _QWORD); // rax@2
  bool v5; // [sp+30h] [bp-18h]@3
  __int64 v6; // [sp+50h] [bp+8h]@1
  __int64 v7; // [sp+58h] [bp+10h]@1

  v7 = a2;
  v6 = a1;
  LODWORD(v2) = (*(int (**)(void))(*(_QWORD *)a1 + 72i64))();
  v5 = (unsigned __int8)(**v2)(v2, v7)
    || (LODWORD(v3) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v6 + 80i64))(v6), (unsigned __int8)(**v3)(v3, v7));
  return v5;
}
