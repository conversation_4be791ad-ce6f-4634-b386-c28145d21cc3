/*
 * Function: j_??$_Umove@V?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@IEAAPEAPEAVCLogTypeDBTask@@V?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@1@0PEAPEAV2@@Z
 * Address: 0x140007F40
 */

CLogTypeDBTask **__fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Umove<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>>(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_First, std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_Last, CLogTypeDBTask **_Ptr)
{
  return std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Umove<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>>(
           this,
           _First,
           _Last,
           _Ptr);
}
