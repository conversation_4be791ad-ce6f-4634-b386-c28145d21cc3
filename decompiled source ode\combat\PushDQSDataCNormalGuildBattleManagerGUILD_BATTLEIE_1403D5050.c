/*
 * Function: ?PushDQSData@CNormalGuildBattleManager@GUILD_BATTLE@@IEAA_NIIPEAVCNormalGuildBattle@2@PEAVCGuildBattleSchedule@2@@Z
 * Address: 0x1403D5050
 */

bool __fastcall GUILD_BATTLE::CNormalGuildBattleManager::PushDQSData(GUILD_BATTLE::CNormalGuildBattleManager *this, unsigned int uiFieldInx, unsigned int uiSLID, GUILD_BATTLE::CNormalGuildBattle *pkBattle, GUILD_BATTLE::CGuildBattleSchedule *pkSchedule)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleGuild *v7; // rax@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v8; // rax@4
  int v9; // eax@4
  __int64 v11; // [sp+0h] [bp-88h]@1
  _qry_case_addguildbattleschedule v12; // [sp+38h] [bp-50h]@4
  unsigned int v13; // [sp+98h] [bp+10h]@1
  unsigned int v14; // [sp+A0h] [bp+18h]@1
  GUILD_BATTLE::CNormalGuildBattle *v15; // [sp+A8h] [bp+20h]@1

  v15 = pkBattle;
  v14 = uiSLID;
  v13 = uiFieldInx;
  v5 = &v11;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v12.dwID = GUILD_BATTLE::CNormalGuildBattle::GetID(pkBattle);
  v7 = GUILD_BATTLE::CNormalGuildBattle::Get1P(v15);
  v12.dw1PGuildSerial = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(v7);
  v8 = GUILD_BATTLE::CNormalGuildBattle::Get2P(v15);
  v12.dw2PGuildSerial = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(v8);
  v12.dwMapID = v13;
  v12.byBattleNumber = GUILD_BATTLE::CNormalGuildBattle::GetGuildBattleNumber(v15);
  v12.dwSLID = v14;
  v12.byState = GUILD_BATTLE::CGuildBattleSchedule::GetState(pkSchedule);
  v12.tStartTime = GUILD_BATTLE::CGuildBattleSchedule::GetRealStartTime(pkSchedule);
  v12.wTurmMin = GUILD_BATTLE::CGuildBattleSchedule::GetBattleTurm(pkSchedule);
  v9 = _qry_case_addguildbattleschedule::size(&v12);
  return CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 30, (char *)&v12, v9) != 0i64;
}
