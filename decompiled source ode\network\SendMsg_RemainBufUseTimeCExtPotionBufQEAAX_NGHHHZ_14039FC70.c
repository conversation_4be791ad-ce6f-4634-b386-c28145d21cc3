/*
 * Function: ?SendMsg_RemainBufUseTime@CExtPotionBuf@@QEAAX_NGHHH@Z
 * Address: 0x14039FC70
 */

void __fastcall CExtPotionBuf::SendMsg_RemainBufUseTime(CExtPotionBuf *this, bool bUse, unsigned __int16 wIndex, int nEndDay, int nEndHour, int nEndMin)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char v10; // [sp+35h] [bp-43h]@4
  char v11; // [sp+36h] [bp-42h]@4
  char v12; // [sp+37h] [bp-41h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v14; // [sp+55h] [bp-23h]@4

  v6 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  szMsg = bUse;
  v10 = nEndDay;
  v11 = nEndHour;
  v12 = nEndMin;
  pbyType = 7;
  v14 = 110;
  CNetProcess::LoadSendMsg(unk_1414F2088, wIndex, &pbyType, &szMsg, 4u);
}
