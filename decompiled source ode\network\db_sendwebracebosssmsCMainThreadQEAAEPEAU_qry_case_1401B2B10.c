/*
 * Function: ?db_sendwebracebosssms@CMainThread@@QEAAEPEAU_qry_case_sendwebracebosssms@@@Z
 * Address: 0x1401B2B10
 */

char __fastcall CMainThread::db_sendwebracebosssms(CMainThread *this, _qry_case_sendwebracebosssms *pSheet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMainThread *v6; // [sp+30h] [bp+8h]@1
  _qry_case_sendwebracebosssms *v7; // [sp+38h] [bp+10h]@1

  v7 = pSheet;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CRFWorldDatabase::Select_ChracterSerialRace(
         v6->m_pWorldDB,
         pSheet->szCharacterName,
         &pSheet->dwCharactSerial,
         &pSheet->byRace) )
  {
    v7->byRace >>= 1;
    result = 0;
  }
  else
  {
    result = 24;
  }
  return result;
}
