/*
 * Function: ?MessageSeriesEnd@BufferedTransformation@CryptoPP@@UEAA_NH_N@Z
 * Address: 0x1405F48F0
 */

int __fastcall CryptoPP::BufferedTransformation::MessageSeriesEnd(CryptoPP::BufferedTransformation *this, __int64 a2, unsigned __int8 a3)
{
  __int64 v3; // rax@1
  CryptoPP::BufferedTransformation *v5; // [sp+30h] [bp+8h]@1
  unsigned __int8 v6; // [sp+40h] [bp+18h]@1

  v6 = a3;
  v5 = this;
  LODWORD(v3) = ((int (*)(void))this->vfptr[20].Clone)();
  if ( v3 )
    _wassert(L"!AttachedTransformation()", L"D:\\RF Project\\RF_Server64\\28 Crypto++\\cryptlib.cpp", 0xEBu);
  return ((int (__fastcall *)(CryptoPP::BufferedTransformation *, _QWORD))v5->vfptr[4].Clone)(v5, v6);
}
