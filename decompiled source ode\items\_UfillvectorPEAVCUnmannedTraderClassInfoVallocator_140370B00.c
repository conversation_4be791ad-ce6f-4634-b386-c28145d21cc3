/*
 * Function: ?_Ufill@?$vector@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@IEAAPEAPEAVCUnmannedTraderClassInfo@@PEAPEAV3@_KAEBQEAV3@@Z
 * Address: 0x140370B00
 */

CUnmannedTraderClassInfo **__fastcall std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Ufill(std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this, CUnmannedTraderClassInfo **_Ptr, unsigned __int64 _Count, CUnmannedTraderClassInfo *const *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v8; // [sp+30h] [bp+8h]@1
  CUnmannedTraderClassInfo **_First; // [sp+38h] [bp+10h]@1
  unsigned __int64 _Counta; // [sp+40h] [bp+18h]@1

  _Counta = _Count;
  _First = _Ptr;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderClassInfo * *,unsigned __int64,CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
    _Ptr,
    _Count,
    _Val,
    &v8->_Alval);
  return &_First[_Counta];
}
