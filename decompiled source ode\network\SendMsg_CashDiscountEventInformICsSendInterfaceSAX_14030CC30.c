/*
 * Function: ?SendMsg_CashDiscountEventInform@ICsSendInterface@@SAXGEPEAU_cash_discount_ini_@@@Z
 * Address: 0x14030CC30
 */

void __fastcall ICsSendInterface::SendMsg_CashDiscountEventInform(unsigned __int16 wSock, char byEventType, _cash_discount_ini_ *pIni)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@15
  __int64 v6; // [sp+0h] [bp-98h]@1
  char Dst; // [sp+38h] [bp-60h]@4
  unsigned __int16 v8; // [sp+39h] [bp-5Fh]@10
  __int16 v9[2]; // [sp+3Bh] [bp-5Dh]@12
  char v10[2]; // [sp+3Fh] [bp-59h]@12
  char v11[2]; // [sp+41h] [bp-57h]@12
  char v12[2]; // [sp+43h] [bp-55h]@12
  char v13[15]; // [sp+45h] [bp-53h]@12
  unsigned __int8 j; // [sp+54h] [bp-44h]@10
  char pbyType; // [sp+64h] [bp-34h]@15
  char v16; // [sp+65h] [bp-33h]@15
  unsigned __int64 v17; // [sp+80h] [bp-18h]@4
  unsigned __int16 v18; // [sp+A0h] [bp+8h]@1
  char v19; // [sp+A8h] [bp+10h]@1
  _cash_discount_ini_ *v20; // [sp+B0h] [bp+18h]@1

  v20 = pIni;
  v19 = byEventType;
  v18 = wSock;
  v3 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v17 = (unsigned __int64)&v6 ^ _security_cookie;
  memset_0(&Dst, 0, 0xFui64);
  if ( (signed int)(unsigned __int8)v19 < 8 )
  {
    if ( v20->m_bCoEvent )
      Dst = 2;
    else
      Dst = v19;
  }
  else
  {
    Dst = 6;
  }
  if ( v20 )
  {
    v8 = v20->m_wCsDiscount;
    for ( j = 0; (signed int)j < 2; ++j )
    {
      v9[j] = v20->m_wYear[j];
      v10[(unsigned __int64)j] = v20->m_byMonth[j];
      v11[(unsigned __int64)j] = v20->m_byDay[j];
      v12[(unsigned __int64)j] = v20->m_byHour[j];
      v13[j] = v20->m_byMinute[j];
    }
  }
  else
  {
    Dst = 6;
  }
  pbyType = 57;
  v16 = 7;
  v5 = _cash_discount_event_inform_zocl::size((_cash_discount_event_inform_zocl *)&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2088, v18, &pbyType, &Dst, v5);
}
