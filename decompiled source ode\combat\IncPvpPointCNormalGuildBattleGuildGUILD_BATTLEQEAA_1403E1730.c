/*
 * Function: ?IncPvpPoint@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXNEAEAVCNormalGuildBattleLogger@2@@Z
 * Address: 0x1403E1730
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::IncPvpPoint(GUILD_BATTLE::CNormalGuildBattleGuild *this, long double dTotalInc, char byWin, GUILD_BATTLE::CNormalGuildBattleLogger *kLogger)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp-28h] [bp-78h]@1
  char pSend; // [sp+0h] [bp-50h]@4
  long double v8; // [sp+1h] [bp-4Fh]@4
  char byType; // [sp+2Ch] [bp-24h]@4
  char v10; // [sp+2Dh] [bp-23h]@4
  int j; // [sp+3Ch] [bp-14h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v12; // [sp+58h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattleLogger *kLoggera; // [sp+70h] [bp+20h]@1

  kLoggera = kLogger;
  v12 = this;
  v4 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  byType = 27;
  v10 = 79;
  v8 = dTotalInc;
  pSend = byWin;
  for ( j = 0; j < 50; ++j )
  {
    if ( !GUILD_BATTLE::CNormalGuildBattleGuildMember::IsEmpty(&v12->m_kMember[j]) )
    {
      GUILD_BATTLE::CNormalGuildBattleGuildMember::IncPvpPoint(&v12->m_kMember[j], dTotalInc, kLoggera);
      GUILD_BATTLE::CNormalGuildBattleGuildMember::Send(&v12->m_kMember[j], &byType, &pSend, 9u);
    }
  }
}
