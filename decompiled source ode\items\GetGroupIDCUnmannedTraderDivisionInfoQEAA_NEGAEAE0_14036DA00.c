/*
 * Function: ?GetGroupID@CUnmannedTraderDivisionInfo@@QEAA_NEGAEAE0@Z
 * Address: 0x14036DA00
 */

char __fastcall CUnmannedTraderDivisionInfo::GetGroupID(CUnmannedTraderDivisionInfo *this, char byTableCode, unsigned __int16 wItemTableIndex, char *byDivision, char *byClass)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char v7; // al@5
  __int64 v8; // [sp+0h] [bp-B8h]@1
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > result; // [sp+28h] [bp-90h]@6
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > v10; // [sp+48h] [bp-70h]@10
  bool v11; // [sp+60h] [bp-58h]@7
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > v12; // [sp+68h] [bp-50h]@7
  char v13; // [sp+80h] [bp-38h]@9
  bool v14; // [sp+81h] [bp-37h]@11
  __int64 v15; // [sp+88h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v16; // [sp+90h] [bp-28h]@7
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_Right; // [sp+98h] [bp-20h]@7
  CUnmannedTraderClassInfo *v18; // [sp+A0h] [bp-18h]@8
  CUnmannedTraderDivisionInfo *v19; // [sp+C0h] [bp+8h]@1
  char v20; // [sp+C8h] [bp+10h]@1
  unsigned __int16 v21; // [sp+D0h] [bp+18h]@1
  char *v22; // [sp+D8h] [bp+20h]@1

  v22 = byDivision;
  v21 = wItemTableIndex;
  v20 = byTableCode;
  v19 = this;
  v5 = &v8;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v15 = -2i64;
  if ( std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::empty(&v19->m_vecClass) )
  {
    v7 = 0;
  }
  else
  {
    std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::begin(&v19->m_vecClass, &result);
    while ( 1 )
    {
      v16 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::end(
              &v19->m_vecClass,
              &v12);
      _Right = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)v16;
      v11 = std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator!=(
              (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&result._Mycont,
              (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v16->_Mycont);
      std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&v12);
      if ( !v11 )
        break;
      v18 = *std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator*(&result);
      if ( (unsigned __int8)((int (__fastcall *)(CUnmannedTraderClassInfo *, _QWORD, _QWORD, char *))v18->vfptr->GetGroupID)(
                              v18,
                              (unsigned __int8)v20,
                              v21,
                              byClass) )
      {
        *v22 = v19->m_dwID;
        v13 = 1;
        std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&result);
        return v13;
      }
      std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator++(
        &result,
        &v10,
        0);
      std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&v10);
    }
    v14 = 0;
    std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&result);
    v7 = v14;
  }
  return v7;
}
