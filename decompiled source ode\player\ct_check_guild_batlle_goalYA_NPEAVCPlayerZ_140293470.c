/*
 * Function: ?ct_check_guild_batlle_goal@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140293470
 */

char __fastcall ct_check_guild_batlle_goal(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CGuildBattleController *v4; // rax@8
  __int64 v5; // [sp+0h] [bp-38h]@1
  int iPortalInx; // [sp+20h] [bp-18h]@8
  CPlayer *pkPlayer; // [sp+40h] [bp+8h]@1

  pkPlayer = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( pkPlayer )
  {
    if ( s_nWordCount == 1 )
    {
      iPortalInx = atoi(s_pwszDstCheat[0]);
      v4 = CGuildBattleController::Instance();
      CGuildBattleController::CheckGoal(v4, pkPlayer, iPortalInx);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
