/*
 * Function: j_??0?$_Bidit@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@_JPEBU12@AEBU12@@std@@QEAA@AEBU01@@Z
 * Address: 0x14000D24C
 */

void __fastcall std::_Bidit<std::pair<int const,CNationSettingFactory *>,__int64,std::pair<int const,CNationSettingFactory *> const *,std::pair<int const,CNationSettingFactory *> const &>::_Bidit<std::pair<int const,CNationSettingFactory *>,__int64,std::pair<int const,CNationSettingFactory *> const *,std::pair<int const,CNationSettingFactory *> const &>(std::_Bidit<std::pair<int const ,CNationSettingFactory *>,__int64,std::pair<int const ,CNationSettingFactory *> const *,std::pair<int const ,CNationSettingFactory *> const &> *this, std::_Bidit<std::pair<int const ,CNationSettingFactory *>,__int64,std::pair<int const ,CNationSettingFactory *> const *,std::pair<int const ,CNationSettingFactory *> const &> *__that)
{
  std::_Bidit<std::pair<int const,CNationSettingFactory *>,__int64,std::pair<int const,CNationSettingFactory *> const *,std::pair<int const,CNationSettingFactory *> const &>::_Bidit<std::pair<int const,CNationSettingFactory *>,__int64,std::pair<int const,CNationSettingFactory *> const *,std::pair<int const,CNationSettingFactory *> const &>(
    this,
    __that);
}
