/*
 * Function: ??0?$CipherModeFinalTemplate_CipherHolder@V?$BlockCipherFinal@$00VDec@Rijndael@CryptoPP@@@CryptoPP@@VCBC_Decryption@2@@CryptoPP@@QEAA@XZ
 * Address: 0x1404525C0
 */

void __fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption>::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption>(CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption> *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CryptoPP::ObjectHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>>::ObjectHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>>((CryptoPP::ObjectHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec> > *)&v5->m_object);
  CryptoPP::AlgorithmImpl<CryptoPP::CBC_Decryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption>>::AlgorithmImpl<CryptoPP::CBC_Decryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption>>((CryptoPP::AlgorithmImpl<CryptoPP::CBC_Decryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption> > *)&v5->vfptr);
  v5->vfptr = (CryptoPP::ClonableVtbl *)&CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption>::`vftable'{for `CryptoPP::StreamTransformation'};
  v5->vfptr = (CryptoPP::SimpleKeyingInterfaceVtbl *)&CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption>::`vftable'{for `CryptoPP::SimpleKeyingInterface'};
  v5->m_cipher = (CryptoPP::SimpleKeyedTransformation<CryptoPP::BlockTransformation> *)&v5->m_object.vfptr;
  CryptoPP::CBC_Decryption::ResizeBuffers((CryptoPP::CBC_Decryption *)&v5->vfptr);
}
