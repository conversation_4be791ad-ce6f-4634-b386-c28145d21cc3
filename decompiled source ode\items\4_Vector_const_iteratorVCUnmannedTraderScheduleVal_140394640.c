/*
 * Function: ??4?$_Vector_const_iterator@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@QEAAAEAV01@AEBV01@@Z
 * Address: 0x140394640
 */

std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *__fastcall std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator=(std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v6; // [sp+30h] [bp+8h]@1
  std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *__thata; // [sp+38h] [bp+10h]@1

  __thata = __that;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Ranit<CUnmannedTraderSchedule,__int64,CUnmannedTraderSchedule const *,CUnmannedTraderSchedule const &>::operator=(
    (std::_Ranit<CUnmannedTraderSchedule,__int64,CUnmannedTraderSchedule const *,CUnmannedTraderSchedule const &> *)&v6->_Mycont,
    (std::_Ranit<CUnmannedTraderSchedule,__int64,CUnmannedTraderSchedule const *,CUnmannedTraderSchedule const &> *)&__that->_Mycont);
  v6->_Myptr = __thata->_Myptr;
  return v6;
}
