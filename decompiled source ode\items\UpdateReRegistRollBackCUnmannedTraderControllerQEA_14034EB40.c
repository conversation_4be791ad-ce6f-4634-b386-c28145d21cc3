/*
 * Function: ?UpdateReRegistRollBack@CUnmannedTraderController@@QEAAEPEAD@Z
 * Address: 0x14034EB40
 */

char __fastcall CUnmannedTraderController::UpdateReRegistRollBack(CUnmannedTraderController *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-B8h]@1
  char *byProcRet; // [sp+20h] [bp-98h]@7
  char *v7; // [sp+30h] [bp-88h]@4
  char byState; // [sp+44h] [bp-74h]@4
  char v9; // [sp+64h] [bp-54h]@4
  _SYSTEMTIME SystemTime; // [sp+88h] [bp-30h]@4
  unsigned __int8 j; // [sp+A4h] [bp-14h]@4
  CUnmannedTraderController *v12; // [sp+C0h] [bp+8h]@1

  v12 = this;
  v2 = &v5;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = pData;
  byState = 0;
  v9 = 0;
  SystemTime.wYear = 0;
  memset(&SystemTime.wMonth, 0, 0xEui64);
  GetLocalTime(&SystemTime);
  v7[4] = 1;
  for ( j = 0; j < (signed int)(unsigned __int8)v7[1]; ++j )
  {
    byState = 0;
    v9 = 0;
    byProcRet = &v9;
    CUnmannedTraderController::CheckDBItemState(v12, *v7, *(_DWORD *)&v7[8 * j + 16], &byState, &v9);
    if ( v9 )
    {
      v7[8 * j + 12] = v9;
      v7[4] = 0;
    }
    else
    {
      byProcRet = (char *)&SystemTime;
      if ( !CRFWorldDatabase::Update_UnmannedTraderItemState(
              pkDB,
              *v7,
              *(_DWORD *)&v7[8 * j + 16],
              v7[8 * j + 13],
              &SystemTime) )
      {
        v7[8 * j + 12] = 30;
        v7[4] = 0;
      }
    }
  }
  return 0;
}
