/*
 * Function: ??_D?$DL_GroupParametersImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@QEAAXXZ
 * Address: 0x14055F890
 */

void __fastcall CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::`vbase destructor(CryptoPP::DL_GroupParameters_IntegerBased *a1)
{
  CryptoPP::DL_GroupParameters_IntegerBased *v1; // [sp+30h] [bp+8h]@1

  v1 = a1;
  CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::~DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>(a1);
  CryptoPP::CryptoMaterial::~CryptoMaterial((CryptoPP::CryptoMaterial *)&v1[2].m_q[8]);
}
