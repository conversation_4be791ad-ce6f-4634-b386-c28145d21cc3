/*
 * Function: ?IsRepairableItem@@YAHHH@Z
 * Address: 0x14003BF20
 */

__int64 __fastcall IsRepairableItem(int nTableCode, int nItemIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@6
  __int64 v5; // [sp+0h] [bp-58h]@1
  CRecordData *v6; // [sp+20h] [bp-38h]@4
  _base_fld *v7; // [sp+28h] [bp-30h]@5
  _base_fld *v8; // [sp+30h] [bp-28h]@8
  _base_fld *v9; // [sp+38h] [bp-20h]@11
  _base_fld *v10; // [sp+40h] [bp-18h]@14
  int v11; // [sp+48h] [bp-10h]@4
  int v12; // [sp+60h] [bp+8h]@1

  v12 = nTableCode;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = &s_ptblItemData[v12];
  v11 = v12;
  switch ( v12 )
  {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 7:
      v7 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v7 )
        goto LABEL_16;
      result = *(_DWORD *)&v7[5].m_strCode[8];
      break;
    case 6:
      v8 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v8 )
        goto LABEL_16;
      result = *(_DWORD *)&v8[9].m_strCode[8];
      break;
    case 11:
      v9 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v9 )
        goto LABEL_16;
      result = *(_DWORD *)&v9[4].m_strCode[48];
      break;
    case 27:
      v10 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v10 )
        goto LABEL_16;
      result = *(_DWORD *)&v10[5].m_strCode[0];
      break;
    default:
LABEL_16:
      result = 0i64;
      break;
  }
  return result;
}
