/*
 * Function: ?SendMsg_AttackResult_Unit@CPlayer@@QEAAXPEAVCAttack@@EG@Z
 * Address: 0x1400D49E0
 */

void __fastcall CPlayer::SendMsg_AttackResult_Unit(CPlayer *this, CAttack *pAt, char byWeaponPart, unsigned __int16 wBulletIndex)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@7
  __int64 v7; // [sp+0h] [bp-1C8h]@1
  _attack_unit_result_zocl v8; // [sp+40h] [bp-188h]@4
  int j; // [sp+194h] [bp-34h]@4
  char pbyType; // [sp+1A4h] [bp-24h]@7
  char v11; // [sp+1A5h] [bp-23h]@7
  CPlayer *v12; // [sp+1D0h] [bp+8h]@1
  CAttack *v13; // [sp+1D8h] [bp+10h]@1
  char v14; // [sp+1E0h] [bp+18h]@1
  unsigned __int16 v15; // [sp+1E8h] [bp+20h]@1

  v15 = wBulletIndex;
  v14 = byWeaponPart;
  v13 = pAt;
  v12 = this;
  v4 = &v7;
  for ( i = 112i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  _attack_unit_result_zocl::_attack_unit_result_zocl(&v8);
  v8.dwAtterSerial = v12->m_dwObjSerial;
  v8.byWeaponPart = v14;
  v8.wWeaponIndex = v13->m_pp->pFld->m_dwIndex;
  v8.wBulletIndex = v15;
  v8.byAttackPart = v13->m_pp->nPart;
  v8.bCritical = v13->m_bIsCrtAtt;
  v8.byListNum = v13->m_nDamagedObjNum;
  for ( j = 0; j < v13->m_nDamagedObjNum; ++j )
  {
    v8.DamList[j].byDstID = v13->m_DamList[j].m_pChar->m_ObjID.m_byID;
    v8.DamList[j].dwDstSerial = v13->m_DamList[j].m_pChar->m_dwObjSerial;
    v8.DamList[j].wDamage = v13->m_DamList[j].m_nDamage;
  }
  pbyType = 5;
  v11 = 10;
  v6 = _attack_unit_result_zocl::size(&v8);
  CGameObject::CircleReport((CGameObject *)&v12->vfptr, &pbyType, (char *)&v8, v6, 1);
}
