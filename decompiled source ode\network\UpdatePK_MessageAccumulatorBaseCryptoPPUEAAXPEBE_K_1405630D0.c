/*
 * Function: ?Update@PK_MessageAccumulatorBase@CryptoPP@@UEAAXPEBE_K@Z
 * Address: 0x1405630D0
 */

void __fastcall CryptoPP::PK_MessageAccumulatorBase::Update(CryptoPP::PK_MessageAccumulatorBase *this, const unsigned __int8 *a2, __int64 a3)
{
  __int64 v3; // rax@1
  bool v4; // [sp+28h] [bp-10h]@3
  CryptoPP::PK_MessageAccumulatorBase *v5; // [sp+40h] [bp+8h]@1
  const unsigned __int8 *v6; // [sp+48h] [bp+10h]@1
  __int64 v7; // [sp+50h] [bp+18h]@1

  v7 = a3;
  v6 = a2;
  v5 = this;
  LODWORD(v3) = ((int (*)(void))this->vfptr[9].__vecDelDtor)();
  (*(void (__fastcall **)(__int64, const unsigned __int8 *, __int64))(*(_QWORD *)v3 + 24i64))(v3, v6, v7);
  v4 = v5->m_empty && !v7;
  v5->m_empty = v4;
}
