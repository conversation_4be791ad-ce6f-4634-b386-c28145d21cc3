/*
 * Function: j_?UpdateClearGuildBattleScheduleDayInfo@CGuildBattleScheduler@GUILD_BATTLE@@QEAA_NKK@Z
 * Address: 0x1400013ED
 */

bool __fastcall GUILD_BATTLE::CGuildBattleScheduler::UpdateClearGuildBattleScheduleDayInfo(GUILD_BATTLE::CGuildBattleScheduler *this, unsigned int dwStartSLID, unsigned int dwEndSLID)
{
  return GUILD_BATTLE::CGuildBattleScheduler::UpdateClearGuildBattleScheduleDayInfo(this, dwStartSLID, dwEndSLID);
}
