/*
 * Function: ?SubPoint@CPlayer@@QEAAXK@Z
 * Address: 0x140055F60
 */

void __usercall CPlayer::SubPoint(CPlayer *this@<rcx>, unsigned int dwSub@<edx>, double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  double v5; // xmm0_8@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  double v7; // [sp+20h] [bp-18h]@5
  double v8; // [sp+28h] [bp-10h]@5
  CPlayer *v9; // [sp+40h] [bp+8h]@1
  unsigned int v10; // [sp+48h] [bp+10h]@1

  v10 = dwSub;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( dwSub )
  {
    CPvpOrderView::GetPvpCash(&v9->m_kPvpOrderView);
    v7 = a3 - (double)(signed int)v10;
    v5 = (double)(signed int)v10;
    v8 = (double)(signed int)v10;
    CPvpOrderView::GetPvpCash(&v9->m_kPvpOrderView);
    if ( v8 > (double)(signed int)v10 )
    {
      CPvpOrderView::GetPvpCash(&v9->m_kPvpOrderView);
      if ( v5 > 0.0 )
      {
        v5 = 0.0;
        v7 = 0.0;
      }
    }
    CPvpOrderView::GetPvpCash(&v9->m_kPvpOrderView);
    if ( v7 != v5 )
    {
      CPvpOrderView::SetPvpCash(&v9->m_kPvpOrderView, v7);
      CPvpOrderView::UpdatePvpCash(&v9->m_kPvpOrderView, v7);
    }
  }
}
