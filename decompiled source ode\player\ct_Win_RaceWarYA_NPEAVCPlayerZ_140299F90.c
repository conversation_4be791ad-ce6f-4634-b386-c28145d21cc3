/*
 * Function: ?ct_Win_RaceWar@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140299F90
 */

char __fastcall ct_Win_RaceWar(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  int v4; // eax@7
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = pOne;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6 && v6->m_bOper )
  {
    v4 = CPlayerDB::GetRaceCode(&v6->m_Param);
    CHolyStoneSystem::SetHolyMasterRace(&g_HolySys, v4);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
