/*
 * Function: ?GetInitClassCost@CPlayer@@QEAAKXZ
 * Address: 0x140060110
 */

__int64 __fastcall CPlayer::GetInitClassCost(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-68h]@1
  unsigned int v5; // [sp+20h] [bp-48h]@4
  int v6; // [sp+38h] [bp-30h]@4
  int v7; // [sp+3Ch] [bp-2Ch]@4
  int v8; // [sp+40h] [bp-28h]@4
  int v9; // [sp+44h] [bp-24h]@4
  int v10; // [sp+48h] [bp-20h]@4
  unsigned int v11; // [sp+54h] [bp-14h]@5
  CPlayer *v12; // [sp+70h] [bp+8h]@1

  v12 = this;
  v1 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = v12->m_pUserDB->m_AvatorData.dbAvator.m_dwClassInitCnt;
  v6 = 1000;
  v7 = 10000;
  v8 = 100000;
  v9 = 1000000;
  v10 = 1000000;
  if ( v5 > 4 )
    v11 = 1000000;
  else
    v11 = *(&v6 + v5);
  return v11;
}
