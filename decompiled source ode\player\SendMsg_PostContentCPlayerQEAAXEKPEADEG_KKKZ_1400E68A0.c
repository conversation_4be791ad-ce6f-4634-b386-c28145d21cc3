/*
 * Function: ?SendMsg_PostContent@CPlayer@@QEAAXEKPEADEG_KKK@Z
 * Address: 0x1400E68A0
 */

void __fastcall CPlayer::SendMsg_PostContent(CPlayer *this, char byErrCode, unsigned int dwPostSerial, char *wszContent, char byTableCode, unsigned __int16 wItemIndex, unsigned __int64 dwDur, unsigned int dwLv, unsigned int dwGold)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v11; // [sp+0h] [bp-178h]@1
  _post_content_result_zocl v12; // [sp+40h] [bp-138h]@4
  char pbyType; // [sp+144h] [bp-34h]@6
  char v14; // [sp+145h] [bp-33h]@6
  unsigned __int64 v15; // [sp+160h] [bp-18h]@4
  CPlayer *v16; // [sp+180h] [bp+8h]@1
  char v17; // [sp+188h] [bp+10h]@1
  unsigned int v18; // [sp+190h] [bp+18h]@1
  const char *Src; // [sp+198h] [bp+20h]@1

  Src = wszContent;
  v18 = dwPostSerial;
  v17 = byErrCode;
  v16 = this;
  v9 = &v11;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v15 = (unsigned __int64)&v11 ^ _security_cookie;
  _post_content_result_zocl::_post_content_result_zocl(&v12);
  v12.byErrCode = v17;
  v12.dwPostSerial = v18;
  if ( !v17 )
  {
    strcpy_s(v12.wszContent, 0xC9ui64, Src);
    v12.Item.byTableCode = byTableCode;
    v12.Item.wItemIndex = wItemIndex;
    v12.Item.dwDur = dwDur;
    v12.Item.dwLv = dwLv;
    v12.dwGold = dwGold;
  }
  pbyType = 58;
  v14 = 6;
  CNetProcess::LoadSendMsg(unk_1414F2088, v16->m_ObjID.m_wIndex, &pbyType, &v12.byErrCode, 0xE1u);
}
