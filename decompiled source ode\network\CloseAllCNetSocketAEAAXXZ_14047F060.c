/*
 * Function: ?CloseAll@CNetSocket@@AEAAXXZ
 * Address: 0x14047F060
 */

void __fastcall CNetSocket::CloseAll(CNetSocket *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int n; // [sp+20h] [bp-18h]@4
  CNetSocket *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( n = 0; n < v5->m_SockType.m_wSocketMaxNum; ++n )
  {
    if ( v5->m_Socket[n].m_bAccept )
    {
      CNetSocket::CloseSocket(v5, n);
      Sleep(1u);
    }
  }
  if ( v5->m_SockType.m_bServer )
    closesocket(v5->m_sAccept);
}
