/*
 * Function: ?construct@?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@QEAAXPEAVCGuildBattleRewardItem@GUILD_BATTLE@@AEBV34@@Z
 * Address: 0x1403D2F40
 */

void __fastcall std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::construct(std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *this, GUILD_BATTLE::CGuildBattleRewardItem *_Ptr, GUILD_BATTLE::CGuildBattleRewardItem *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1

  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Construct<GUILD_BATTLE::CGuildBattleRewardItem,GUILD_BATTLE::CGuildBattleRewardItem>(_Ptr, _Val);
}
