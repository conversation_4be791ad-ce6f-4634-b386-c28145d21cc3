/*
 * Function: ?_buybygold_complete@CashItemRemoteStore@@AEAAXPEAVCPlayer@@AEAU_result_csi_buy_zocl@@PEAU_request_csi_buy_clzo@@PEAU__item@4@PEAU_param_cashitem_dblog@@_N@Z
 * Address: 0x140300840
 */

void __fastcall CashItemRemoteStore::_buybygold_complete(CashItemRemoteStore *this, CPlayer *pOne, _result_csi_buy_zocl *Send, _request_csi_buy_clzo *pRecv, _request_csi_buy_clzo::__item *pSrc, _param_cashitem_dblog *pSheet, bool bCouponUse)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v9; // ax@4
  CCashDBWorkManager *v10; // rax@4
  __int64 v11; // [sp+0h] [bp-58h]@1
  char pbyType; // [sp+34h] [bp-24h]@4
  char v13; // [sp+35h] [bp-23h]@4
  unsigned __int64 size; // [sp+48h] [bp-10h]@4
  CashItemRemoteStore *v15; // [sp+60h] [bp+8h]@1
  CPlayer *v16; // [sp+68h] [bp+10h]@1
  _result_csi_buy_zocl *v17; // [sp+70h] [bp+18h]@1
  _request_csi_buy_clzo *v18; // [sp+78h] [bp+20h]@1

  v18 = pRecv;
  v17 = Send;
  v16 = pOne;
  v15 = this;
  v7 = &v11;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  Send->nCashAmount = CPlayerDB::GetGold(&pOne->m_Param);
  pbyType = 57;
  v13 = 4;
  v9 = _result_csi_buy_zocl::size(v17);
  CNetProcess::LoadSendMsg(unk_1414F2088, v16->m_ObjID.m_wIndex, &pbyType, (char *)v17, v9);
  size = _param_cashitem_dblog::size(pSheet);
  v10 = CTSingleton<CCashDBWorkManager>::Instance();
  CCashDBWorkManager::PushTask(v10, 3, (char *)pSheet, size);
  if ( pSheet->in_bLimited_Sale && pSrc->byEventType == 5 )
  {
    CashItemRemoteStore::Set_LimitedSale_count(v15, pSrc->byTblCode, pSrc->wItemIdx);
    CashItemRemoteStore::Set_DB_LimitedSale_Event(v15);
    CashItemRemoteStore::LimitedSale_check_count(v15, pSrc->byTblCode, pSrc->wItemIdx);
  }
  if ( bCouponUse )
    CPlayer::DeleteCouponItem(v16, v18->CouponItem, v18->byCouponNum);
}
