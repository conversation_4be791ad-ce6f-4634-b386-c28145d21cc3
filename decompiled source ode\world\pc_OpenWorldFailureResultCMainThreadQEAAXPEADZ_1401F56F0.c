/*
 * Function: ?pc_OpenWorldFailureResult@CMainThread@@QEAAXPEAD@Z
 * Address: 0x1401F56F0
 */

void __fastcall CMainThread::pc_OpenWorldFailureResult(CMainThread *this, char *szMsg)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1

  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  MyMessageBox("pc_OpenWorldFailureResult", "request world-open fail");
  CWnd::SendMessageA(g_pFrame, 0x10u, 0i64, 0i64);
}
