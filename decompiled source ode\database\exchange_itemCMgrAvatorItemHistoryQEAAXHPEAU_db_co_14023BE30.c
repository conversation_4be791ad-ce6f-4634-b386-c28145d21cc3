/*
 * Function: ?exchange_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@0PEAD@Z
 * Address: 0x14023BE30
 */

void __fastcall CMgrAvatorItemHistory::exchange_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pUseItem, _STORAGE_LIST::_db_con *pOutItem, char *pszFileName)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char *v7; // rax@4
  __int64 v8; // [sp+0h] [bp-78h]@1
  char *v9; // [sp+20h] [bp-58h]@4
  unsigned __int64 v10; // [sp+28h] [bp-50h]@4
  char *v11; // [sp+30h] [bp-48h]@4
  char *v12; // [sp+38h] [bp-40h]@4
  _base_fld *v13; // [sp+40h] [bp-38h]@4
  _base_fld *v14; // [sp+48h] [bp-30h]@4
  char *v15; // [sp+50h] [bp-28h]@4
  char *v16; // [sp+58h] [bp-20h]@4
  int nTableCode; // [sp+60h] [bp-18h]@4
  CMgrAvatorItemHistory *v18; // [sp+80h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v19; // [sp+90h] [bp+18h]@1
  _STORAGE_LIST::_db_con *v20; // [sp+98h] [bp+20h]@1

  v20 = pOutItem;
  v19 = pUseItem;
  v18 = this;
  v5 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  sData[0] = 0;
  v13 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pOutItem->m_byTableCode, pOutItem->m_wItemIndex);
  v15 = v18->m_szCurTime;
  v16 = v18->m_szCurDate;
  nTableCode = v20->m_byTableCode;
  v7 = DisplayItemUpgInfo(nTableCode, v20->m_dwLv);
  v12 = v15;
  v11 = v16;
  v10 = v20->m_lnUID;
  v9 = v7;
  sprintf(sBuf, "EXCHANGE: %s_%u_@%s[%I64u] [%s %s]\r\n", v13->m_strCode, v20->m_dwDur);
  strcat_0(sData, sBuf);
  v14 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v19->m_byTableCode, v19->m_wItemIndex);
  v9 = DisplayItemUpgInfo(v19->m_byTableCode, v19->m_dwLv);
  sprintf(sBuf, "\t- %s_%u_%s\r\n", v14->m_strCode, v19->m_dwDur);
  strcat_0(sData, sBuf);
  CMgrAvatorItemHistory::WriteFile(v18, pszFileName, sData);
}
