/*
 * Function: ?Select_LimitItemUsedRecord@CRFWorldDatabase@@QEAAEEKKPEAK@Z
 * Address: 0x1404BA4F0
 */

char __fastcall CRFWorldDatabase::Select_LimitItemUsedRecord(CRFWorldDatabase *this, char byType, unsigned int dwTypeSerial, unsigned int dwStoreInx, unsigned int *pdwSerial)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v8; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@4
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@4
  SQLLEN v11; // [sp+38h] [bp-150h]@22
  __int16 v12; // [sp+44h] [bp-144h]@9
  char DstBuf; // [sp+60h] [bp-128h]@4
  char v14; // [sp+61h] [bp-127h]@4
  char v15; // [sp+164h] [bp-24h]@16
  unsigned __int64 v16; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v17; // [sp+190h] [bp+8h]@1

  v17 = this;
  v5 = &v8;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v16 = (unsigned __int64)&v8 ^ _security_cookie;
  DstBuf = 0;
  memset(&v14, 0, 0xFFui64);
  LODWORD(StrLen_or_IndPtr) = dwStoreInx;
  LODWORD(SQLStmt) = dwTypeSerial;
  sprintf_s(
    &DstBuf,
    0x100ui64,
    "select serial from tbl_StoreLimitItem_061212 where dck=0 and type=%d and typeserial=%d and storeinx=%d",
    (unsigned __int8)byType);
  if ( v17->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v17->vfptr, &DstBuf);
  if ( v17->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v17->vfptr) )
  {
    v12 = SQLExecDirect_0(v17->m_hStmtSelect, &DstBuf, -3);
    if ( v12 && v12 != 1 )
    {
      if ( v12 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v17->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v12, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v12, v17->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v12 = SQLFetch_0(v17->m_hStmtSelect);
      if ( v12 && v12 != 1 )
      {
        v15 = 0;
        if ( v12 == 100 )
        {
          v15 = 2;
        }
        else
        {
          SQLStmt = v17->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v12, &DstBuf, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v12, v17->m_hStmtSelect);
          v15 = 1;
        }
        if ( v17->m_hStmtSelect )
          SQLCloseCursor_0(v17->m_hStmtSelect);
        result = v15;
      }
      else
      {
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v17->m_hStmtSelect, 1u, -18, pdwSerial, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          SQLStmt = v17->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v12, &DstBuf, "SQLGetData", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v12, v17->m_hStmtSelect);
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          result = 1;
        }
        else
        {
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          if ( v17->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v17->vfptr, "%s Success", &DstBuf);
          result = 0;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v17->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1;
  }
  return result;
}
