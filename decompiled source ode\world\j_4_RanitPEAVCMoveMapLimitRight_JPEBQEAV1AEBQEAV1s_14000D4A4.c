/*
 * Function: j_??4?$_Ranit@PEAVCMoveMapLimitRight@@_JPEBQEAV1@AEBQEAV1@@std@@QEAAAEAU01@AEBU01@@Z
 * Address: 0x14000D4A4
 */

std::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &> *__fastcall std::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &>::operator=(std::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &> *this, std::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &> *__that)
{
  return std::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &>::operator=(
           this,
           __that);
}
