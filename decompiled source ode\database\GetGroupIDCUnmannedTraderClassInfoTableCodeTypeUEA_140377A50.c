/*
 * Function: ?GetGroupID@CUnmannedTraderClassInfoTableCodeType@@UEAA_NEGAEAE0@Z
 * Address: 0x140377A50
 */

bool __fastcall CUnmannedTraderClassInfoTableCodeType::GetGroupID(CUnmannedTraderClassInfoTableCodeType *this, char byTableCode, unsigned __int16 wItemTableIndex, char *byClass, char *bySubClass)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v8; // [sp+0h] [bp-38h]@1
  CUnmannedTraderClassInfoTableCodeType *v9; // [sp+40h] [bp+8h]@1
  char v10; // [sp+48h] [bp+10h]@1
  unsigned __int16 v11; // [sp+50h] [bp+18h]@1
  char *byClassa; // [sp+58h] [bp+20h]@1

  byClassa = byClass;
  v11 = wItemTableIndex;
  v10 = byTableCode;
  v9 = this;
  v5 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  *bySubClass = 0;
  if ( CUnmannedTraderClassInfoTableCodeType::GetGroupID(v9, byTableCode, wItemTableIndex, byClass) )
  {
    if ( std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::empty(&v9->m_vecSubClass) )
      result = 1;
    else
      result = CUnmannedTraderClassInfoTableType::GetGroupID(
                 (CUnmannedTraderClassInfoTableType *)&v9->vfptr,
                 v10,
                 v11,
                 byClassa,
                 bySubClass);
  }
  else
  {
    result = 0;
  }
  return result;
}
