/*
 * Function: ?UpdateKillerList@CPvpCashPoint@@QEAAXPEAU_PVP_ORDER_VIEW_DB_BASE@@@Z
 * Address: 0x1403F56E0
 */

void __fastcall CPvpCashPoint::UpdateKillerList(CPvpCashPoint *this, _PVP_ORDER_VIEW_DB_BASE *pkInfo)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  int v5; // [sp+20h] [bp-28h]@4
  bool *v6; // [sp+28h] [bp-20h]@4
  int v7; // [sp+30h] [bp-18h]@4
  CPvpCashPoint *v8; // [sp+50h] [bp+8h]@1
  _PVP_ORDER_VIEW_DB_BASE *v9; // [sp+58h] [bp+10h]@1

  v9 = pkInfo;
  v8 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = CIndexList::GetSize(&v8->m_KillerList);
  CSyncCS::Lock(&v8->m_KillerList.m_csList);
  v6 = &v8->m_KillerList.m_Head.m_pNext->m_bLoad;
  v7 = 0;
  while ( (CIndexList::_index_node *)v6 != &v8->m_KillerList.m_Tail )
  {
    v9->dwKillerSerial[v7] = *((_DWORD *)v6 + 1);
    v6 = (bool *)*((_QWORD *)v6 + 4);
    ++v7;
  }
  CSyncCS::Unlock(&v8->m_KillerList.m_csList);
}
