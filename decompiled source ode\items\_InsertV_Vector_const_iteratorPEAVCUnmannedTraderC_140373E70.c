/*
 * Function: ??$_Insert@V?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@@?$vector@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@QEAAXV?$_Vector_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@1@V?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@1@1Uforward_iterator_tag@1@@Z
 * Address: 0x140373E70
 */

void __fastcall std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Insert<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>>(std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this, std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_Where, std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_First, std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_Last, std::forward_iterator_tag __formal)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v7; // rax@4
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v8; // rax@4
  unsigned __int64 v9; // rax@5
  unsigned __int64 v10; // rax@7
  unsigned __int64 v11; // rax@8
  unsigned __int64 v12; // rax@11
  unsigned __int64 v13; // rax@12
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v14; // rax@13
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v15; // rax@13
  unsigned __int64 v16; // rax@13
  unsigned __int64 v17; // rax@16
  __int64 v18; // rax@17
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v19; // rax@17
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v20; // rax@17
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v21; // rax@17
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v22; // rax@17
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v23; // rax@18
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v24; // rax@18
  __int64 v25; // [sp+0h] [bp-2D8h]@1
  unsigned __int64 v26; // [sp+28h] [bp-2B0h]@4
  unsigned __int64 _Count; // [sp+38h] [bp-2A0h]@4
  CUnmannedTraderClassInfo **_Ptr; // [sp+40h] [bp-298h]@13
  CUnmannedTraderClassInfo **v29; // [sp+48h] [bp-290h]@13
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > _Wherea; // [sp+58h] [bp-280h]@17
  CUnmannedTraderClassInfo **_Lasta; // [sp+78h] [bp-260h]@18
  char v32; // [sp+80h] [bp-258h]@4
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v33; // [sp+98h] [bp-240h]@4
  char v34; // [sp+A0h] [bp-238h]@4
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v35; // [sp+B8h] [bp-220h]@4
  char v36; // [sp+C0h] [bp-218h]@13
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v37; // [sp+D8h] [bp-200h]@13
  char v38; // [sp+E0h] [bp-1F8h]@13
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v39; // [sp+F8h] [bp-1E0h]@13
  bool v40; // [sp+100h] [bp-1D8h]@16
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > result; // [sp+108h] [bp-1D0h]@16
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > v42; // [sp+120h] [bp-1B8h]@17
  char v43; // [sp+138h] [bp-1A0h]@17
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v44; // [sp+150h] [bp-188h]@17
  char v45; // [sp+158h] [bp-180h]@17
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v46; // [sp+170h] [bp-168h]@17
  char v47; // [sp+178h] [bp-160h]@17
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v48; // [sp+190h] [bp-148h]@17
  char v49; // [sp+198h] [bp-140h]@17
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v50; // [sp+1B0h] [bp-128h]@17
  char v51; // [sp+1B8h] [bp-120h]@18
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v52; // [sp+1D0h] [bp-108h]@18
  char v53; // [sp+1D8h] [bp-100h]@18
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v54; // [sp+1F0h] [bp-E8h]@18
  __int64 v55; // [sp+1F8h] [bp-E0h]@4
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v56; // [sp+200h] [bp-D8h]@4
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v57; // [sp+208h] [bp-D0h]@4
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v58; // [sp+210h] [bp-C8h]@4
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v59; // [sp+218h] [bp-C0h]@5
  unsigned __int64 v60; // [sp+220h] [bp-B8h]@8
  unsigned __int64 v61; // [sp+228h] [bp-B0h]@9
  CUnmannedTraderClassInfo **v62; // [sp+230h] [bp-A8h]@13
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v63; // [sp+238h] [bp-A0h]@13
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v64; // [sp+240h] [bp-98h]@13
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v65; // [sp+248h] [bp-90h]@13
  CUnmannedTraderClassInfo **v66; // [sp+250h] [bp-88h]@13
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v67; // [sp+258h] [bp-80h]@16
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v68; // [sp+260h] [bp-78h]@16
  int v69; // [sp+268h] [bp-70h]@16
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v70; // [sp+270h] [bp-68h]@17
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v71; // [sp+278h] [bp-60h]@17
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v72; // [sp+280h] [bp-58h]@17
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v73; // [sp+288h] [bp-50h]@17
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v74; // [sp+290h] [bp-48h]@17
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v75; // [sp+298h] [bp-40h]@17
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v76; // [sp+2A0h] [bp-38h]@17
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v77; // [sp+2A8h] [bp-30h]@17
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v78; // [sp+2B0h] [bp-28h]@18
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v79; // [sp+2B8h] [bp-20h]@18
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v80; // [sp+2C0h] [bp-18h]@18
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v81; // [sp+2E0h] [bp+8h]@1
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_Right; // [sp+2E8h] [bp+10h]@1
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v83; // [sp+2F0h] [bp+18h]@1
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *__that; // [sp+2F8h] [bp+20h]@1

  __that = _Last;
  v83 = _First;
  _Right = _Where;
  v81 = this;
  v5 = &v25;
  for ( i = 180i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v55 = -2i64;
  v26 = 0i64;
  v33 = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v32;
  v35 = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v34;
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
    (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v32,
    _Last);
  v56 = v7;
  v57 = v7;
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
    v35,
    v83);
  v58 = v8;
  std::_Distance<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,unsigned __int64>(
    v8,
    v57,
    &v26);
  _Count = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::capacity(v81);
  if ( v26 )
  {
    v59 = (std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::size(v81);
    v9 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::max_size(v81);
    if ( v9 - (unsigned __int64)v59 < v26 )
      std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Xlen(v59);
    v10 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::size(v81);
    if ( _Count >= v26 + v10 )
    {
      v67 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::end(v81, &result);
      v68 = v67;
      v17 = std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator-(
              v67,
              (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&_Right->_Mycont);
      v69 = v17 < v26;
      v40 = v17 < v26;
      std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&result);
      if ( v40 )
      {
        std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Umove<CUnmannedTraderClassInfo * *>(
          v81,
          _Right->_Myptr,
          v81->_Mylast,
          &_Right->_Myptr[v26]);
        std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
          &_Wherea,
          v83);
        v70 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::end(v81, &v42);
        v71 = v70;
        v18 = std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator-(
                v70,
                (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&_Right->_Mycont);
        std::advance<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,__int64>(
          &_Wherea,
          v18);
        std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&v42);
        v44 = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v43;
        v46 = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v45;
        std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
          (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v43,
          __that);
        v72 = v19;
        v73 = v19;
        std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
          v46,
          &_Wherea);
        v74 = v20;
        std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Ucopy<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>>(
          v81,
          v20,
          v73,
          v81->_Mylast);
        v81->_Mylast += v26;
        v48 = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v47;
        v50 = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v49;
        std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
          (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v47,
          &_Wherea);
        v75 = v21;
        v76 = v21;
        std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
          v50,
          v83);
        v77 = v22;
        stdext::unchecked_copy<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,CUnmannedTraderClassInfo * *>(
          v22,
          v76,
          _Right->_Myptr);
        std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&_Wherea);
      }
      else
      {
        _Lasta = v81->_Mylast;
        v81->_Mylast = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Umove<CUnmannedTraderClassInfo * *>(
                         v81,
                         &_Lasta[-v26],
                         _Lasta,
                         v81->_Mylast);
        stdext::_Unchecked_move_backward<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *>(
          _Right->_Myptr,
          &_Lasta[-v26],
          _Lasta);
        v52 = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v51;
        v54 = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v53;
        std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
          (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v51,
          __that);
        v78 = v23;
        v79 = v23;
        std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
          v54,
          v83);
        v80 = v24;
        stdext::unchecked_copy<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,CUnmannedTraderClassInfo * *>(
          v24,
          v79,
          _Right->_Myptr);
      }
    }
    else
    {
      v60 = _Count / 2;
      v11 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::max_size(v81);
      if ( v11 - v60 >= _Count )
        v61 = _Count / 2 + _Count;
      else
        v61 = 0i64;
      _Count = v61;
      v12 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::size(v81);
      if ( _Count < v26 + v12 )
      {
        v13 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::size(v81);
        _Count = v26 + v13;
      }
      _Ptr = std::allocator<CUnmannedTraderClassInfo *>::allocate(&v81->_Alval, _Count);
      v29 = _Ptr;
      v62 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Umove<CUnmannedTraderClassInfo * *>(
              v81,
              v81->_Myfirst,
              _Right->_Myptr,
              _Ptr);
      v29 = v62;
      v37 = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v36;
      v39 = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v38;
      std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
        (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v36,
        __that);
      v63 = v14;
      v64 = v14;
      std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
        v39,
        v83);
      v65 = v15;
      v66 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Ucopy<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>>(
              v81,
              v15,
              v64,
              v29);
      v29 = v66;
      std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Umove<CUnmannedTraderClassInfo * *>(
        v81,
        _Right->_Myptr,
        v81->_Mylast,
        v66);
      v16 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::size(v81);
      v26 += v16;
      if ( v81->_Myfirst )
      {
        std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Destroy(
          v81,
          v81->_Myfirst,
          v81->_Mylast);
        std::allocator<CUnmannedTraderClassInfo *>::deallocate(&v81->_Alval, v81->_Myfirst, v81->_Myend - v81->_Myfirst);
      }
      v81->_Myend = &_Ptr[_Count];
      v81->_Mylast = &_Ptr[v26];
      v81->_Myfirst = _Ptr;
    }
  }
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(_Right);
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(v83);
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(__that);
}
