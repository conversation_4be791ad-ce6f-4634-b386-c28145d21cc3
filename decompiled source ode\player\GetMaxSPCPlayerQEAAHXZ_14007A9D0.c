/*
 * Function: ?Get<PERSON>axSP@CPlayer@@QEAAHXZ
 * Address: 0x14007A9D0
 */

__int64 __fastcall CPlayer::GetMaxSP(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  float v3; // xmm0_4@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  float v7; // [sp+24h] [bp-14h]@4
  CPlayer *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = (float)v8->m_nMaxPoint[2];
  v3 = v7;
  _effect_parameter::GetEff_Rate(&v8->m_EP, 11);
  v6 = (signed int)ffloor(v7 * v3);
  if ( v6 <= 0 )
    v6 = 1;
  return (unsigned int)v6;
}
