/*
 * Function: j_?SendMsg_UnitFrameBuyResult@CPlayer@@QEAAXEEEGGPEAK@Z
 * Address: 0x1400018CF
 */

void __fastcall CPlayer::SendMsg_UnitFrameBuyResult(CPlayer *this, char byRetCode, char by<PERSON>rameC<PERSON>, char byUnitSlotIndex, unsigned __int16 wKeyIndex, unsigned __int16 wKeySerial, unsigned int *pdwConsumMoney)
{
  CPlayer::SendMsg_UnitFrameBuyResult(
    this,
    byRetCode,
    byFrameCode,
    byUnitSlotIndex,
    wKeyIndex,
    wKeySerial,
    pdwConsumMoney);
}
