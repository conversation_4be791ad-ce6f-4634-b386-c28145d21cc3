/*
 * Function: luaX_init
 * Address: 0x1405428F0
 */

_QWORD *__fastcall luaX_init(__int64 a1)
{
  __int64 v1; // rbp@1
  signed int v2; // ebx@1
  const void **v3; // rsi@1
  _QWORD *result; // rax@2

  v1 = a1;
  v2 = 0;
  v3 = (const void **)&luaX_tokens;
  do
  {
    result = luaS_newlstr(v1, *v3, strlen((const char *)*v3));
    ++v2;
    *((_BYTE *)result + 9) |= 0x20u;
    ++v3;
    *((_BYTE *)result + 10) = v2;
  }
  while ( v2 < 21 );
  return result;
}
