/*
 * Function: ?accHitTest@CFormView@@UEAAJJJPEAUtagVARIANT@@@Z
 * Address: 0x14002BB60
 */

__int32 __fastcall CFormView::accHitTest(CFormView *this, __int32 a2, __int32 a3, struct tagVARIANT *a4)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  CFormView *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return CWnd::GetAccessibilityHitTest((CWnd *)&v8->vfptr, a2, a3, a4);
}
