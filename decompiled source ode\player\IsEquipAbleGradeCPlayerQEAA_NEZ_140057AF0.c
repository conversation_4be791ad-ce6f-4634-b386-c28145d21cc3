/*
 * Function: ?IsEquipAbleGrade@CPlayer@@QEAA_NE@Z
 * Address: 0x140057AF0
 */

char __fastcall CPlayer::IsEquipAbleGrade(CPlayer *this, char byGradeLv)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v5; // eax@14
  CPvpUserAndGuildRankingSystem *v6; // rax@17
  CPvpUserAndGuildRankingSystem *v7; // rax@20
  unsigned int v8; // eax@20
  __int64 v9; // [sp+0h] [bp-48h]@1
  char v10; // [sp+20h] [bp-28h]@8
  unsigned int v11; // [sp+24h] [bp-24h]@14
  unsigned int dwSerial; // [sp+28h] [bp-20h]@17
  int v13; // [sp+2Ch] [bp-1Ch]@17
  unsigned int v14; // [sp+30h] [bp-18h]@20
  int v15; // [sp+34h] [bp-14h]@20
  CPlayer *v16; // [sp+50h] [bp+8h]@1

  v16 = this;
  v2 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( (signed int)(unsigned __int8)byGradeLv >= 8 )
  {
    v10 = byGradeLv;
    if ( byGradeLv == 8 )
    {
      if ( !v16->m_Param.m_pGuild )
        return 0;
      v11 = CGuild::GetGuildMasterSerial(v16->m_Param.m_pGuild);
      v5 = CPlayerDB::GetCharSerial(&v16->m_Param);
      if ( v11 != v5 )
        return 0;
    }
    else if ( v10 == 9 )
    {
      dwSerial = CPlayerDB::GetCharSerial(&v16->m_Param);
      v13 = CPlayerDB::GetRaceCode(&v16->m_Param);
      v6 = CPvpUserAndGuildRankingSystem::Instance();
      if ( !CPvpUserAndGuildRankingSystem::IsCurrentRaceBossGroup(v6, v13, dwSerial) )
        return 0;
    }
    else
    {
      if ( v10 != 10 )
        return 0;
      v14 = CPlayerDB::GetCharSerial(&v16->m_Param);
      v15 = CPlayerDB::GetRaceCode(&v16->m_Param);
      v7 = CPvpUserAndGuildRankingSystem::Instance();
      v8 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v7, v15, 0);
      if ( v14 != v8 )
        return 0;
    }
  }
  else if ( (unsigned __int8)byGradeLv > (signed int)v16->m_Param.m_byPvPGrade )
  {
    return 0;
  }
  return 1;
}
