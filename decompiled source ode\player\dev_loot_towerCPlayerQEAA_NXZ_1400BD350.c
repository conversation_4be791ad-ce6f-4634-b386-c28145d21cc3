/*
 * Function: ?dev_loot_tower@CPlayer@@QEAA_NXZ
 * Address: 0x1400BD350
 */

char __fastcall CPlayer::dev_loot_tower(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-B8h]@1
  CMapData *pMap; // [sp+30h] [bp-88h]@9
  unsigned __int16 wLayerIndex; // [sp+38h] [bp-80h]@9
  float *pStdPos; // [sp+40h] [bp-78h]@9
  bool bHide; // [sp+48h] [bp-70h]@9
  int v9; // [sp+50h] [bp-68h]@4
  int n; // [sp+54h] [bp-64h]@4
  _base_fld *v11; // [sp+58h] [bp-60h]@7
  _STORAGE_LIST::_db_con pItem; // [sp+68h] [bp-50h]@9
  int j; // [sp+A4h] [bp-14h]@10
  CPlayer *pOwner; // [sp+C0h] [bp+8h]@1

  pOwner = this;
  v1 = &v4;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 25);
  for ( n = 0; n < v9; ++n )
  {
    v11 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 25, n);
    if ( IsExistItem(25, n) && IsGroundableItem(25, n) )
    {
      _STORAGE_LIST::_db_con::_db_con(&pItem);
      pItem.m_byTableCode = 25;
      pItem.m_wItemIndex = n;
      pItem.m_dwDur = GetItemDurPoint(25, n);
      pItem.m_dwLv = 0xFFFFFFF;
      bHide = 1;
      pStdPos = pOwner->m_fCurPos;
      wLayerIndex = pOwner->m_wMapLayerIndex;
      pMap = pOwner->m_pCurMap;
      if ( !CreateItemBox(&pItem, pOwner, 0xFFFFFFFF, 0, 0i64, 2, pMap, wLayerIndex, pOwner->m_fCurPos, 1) )
        return 1;
      for ( j = 0; j < 3 && loot_item(pOwner, &v11[j + 7].m_strCode[4], 1, 0i64, 0); ++j )
        ;
    }
  }
  return 1;
}
