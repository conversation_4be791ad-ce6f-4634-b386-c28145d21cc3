/*
 * Function: ?LoadItemConsumeINI@CMainThread@@AEAAXXZ
 * Address: 0x1401E7120
 */

void __fastcall CMainThread::LoadItemConsumeINI(CMainThread *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-D8h]@1
  char ReturnedString; // [sp+38h] [bp-A0h]@4
  char v5; // [sp+39h] [bp-9Fh]@4
  char Str1; // [sp+68h] [bp-70h]@4
  char v7; // [sp+69h] [bp-6Fh]@4
  char v8; // [sp+98h] [bp-40h]@4
  char v9; // [sp+99h] [bp-3Fh]@4
  bool v10; // [sp+C0h] [bp-18h]@4
  bool v11; // [sp+C1h] [bp-17h]@4
  bool v12; // [sp+C2h] [bp-16h]@4
  bool v13; // [sp+C3h] [bp-15h]@4
  unsigned __int64 v14; // [sp+C8h] [bp-10h]@4
  CMainThread *v15; // [sp+E0h] [bp+8h]@1

  v15 = this;
  v1 = &v3;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v14 = (unsigned __int64)&v3 ^ _security_cookie;
  ReturnedString = 0;
  memset(&v5, 0, 9ui64);
  GetPrivateProfileStringA("AWAY_PARTY", "ItemConsume", "TRUE", &ReturnedString, 0xAu, ".\\Initialize\\ItemConsume.ini");
  v10 = strcmp_0(&ReturnedString, "TRUE") == 0;
  v15->m_bAwayPartyConsumeItem = v10;
  GetPrivateProfileStringA(
    "AWAY_PARTY",
    "ItemCode",
    "ircht01",
    v15->m_strAwayPartyItemCode,
    0x40u,
    ".\\Initialize\\ItemConsume.ini");
  Str1 = 0;
  memset(&v7, 0, 9ui64);
  GetPrivateProfileStringA("AWAY_PARTY", "MoneyConsume", "FALSE", &Str1, 0xAu, ".\\Initialize\\ItemConsume.ini");
  v11 = strcmp_0(&Str1, "TRUE") == 0;
  v15->m_bAwayPartyConsumeMoney = v11;
  v15->m_dwAwayPartyMoney = GetPrivateProfileIntA("AWAY_PARTY", "Money", 0, ".\\Initialize\\ItemConsume.ini");
  GetPrivateProfileStringA(
    "AllRaceChat",
    "ItemCode",
    &byte_14076B5BF,
    v15->m_strAllRaceChatItemCode,
    0x40u,
    ".\\Initialize\\ItemConsume.ini");
  v8 = 0;
  memset(&v9, 0, 9ui64);
  GetPrivateProfileStringA("AllRaceChat", "ItemConsume", "TRUE", &v8, 0xAu, ".\\Initialize\\ItemConsume.ini");
  v12 = strcmp_0(&v8, "TRUE") == 0;
  v15->m_bAllRaceChatItemConsume = v12;
  GetPrivateProfileStringA("AllRaceChat", "MoneyConsume", "FALSE", &v8, 0xAu, ".\\Initialize\\ItemConsume.ini");
  v13 = strcmp_0(&v8, "TRUE") == 0;
  v15->m_bAllRaceChatMoneyConsume = v13;
  v15->m_dwAllRaceChatMoney = GetPrivateProfileIntA("AllRaceChat", "Money", 0, ".\\Initialize\\ItemConsume.ini");
}
