/*
 * Function: ?db_Update_PostStorage@CMainThread@@QEAAEKPEAU_AVATOR_DATA@@0@Z
 * Address: 0x1401B4110
 */

char __fastcall CMainThread::db_Update_PostStorage(CMainThread *this, unsigned int dwAvatorSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v6; // eax@24
  __int64 v8; // [sp+0h] [bp-9C8h]@1
  char *wszSendName; // [sp+20h] [bp-9A8h]@9
  char *wszRecvName; // [sp+28h] [bp-9A0h]@21
  char *wszTitle; // [sp+30h] [bp-998h]@21
  char *wszContent; // [sp+38h] [bp-990h]@21
  int nK; // [sp+40h] [bp-988h]@21
  unsigned __int64 dwD; // [sp+48h] [bp-980h]@21
  unsigned int dwU; // [sp+50h] [bp-978h]@21
  unsigned int dwGold; // [sp+58h] [bp-970h]@21
  char byErr; // [sp+60h] [bp-968h]@21
  unsigned __int16 wStorageIndex; // [sp+68h] [bp-960h]@21
  char *pbyNumber; // [sp+70h] [bp-958h]@21
  bool bGetNumber; // [sp+78h] [bp-950h]@21
  unsigned __int64 lnUID; // [sp+80h] [bp-948h]@21
  unsigned int j; // [sp+90h] [bp-938h]@5
  char v23; // [sp+AFh] [bp-919h]@41
  char DstBuf; // [sp+B0h] [bp-918h]@4
  char v25; // [sp+B1h] [bp-917h]@4
  char Src; // [sp+8D0h] [bp-F8h]@4
  char v27; // [sp+8D1h] [bp-F7h]@4
  _POSTDATA_DB_BASE *v28; // [sp+958h] [bp-70h]@4
  _POSTDATA_DB_BASE *v29; // [sp+960h] [bp-68h]@4
  bool *v30; // [sp+968h] [bp-60h]@4
  bool *v31; // [sp+970h] [bp-58h]@4
  char v32; // [sp+978h] [bp-50h]@16
  unsigned int *pdwStorageSerial; // [sp+980h] [bp-48h]@15
  _POSTDATA_DB_BASE *v34; // [sp+988h] [bp-40h]@15
  char v35; // [sp+994h] [bp-34h]@21
  unsigned __int64 v36; // [sp+9B0h] [bp-18h]@4
  CMainThread *v37; // [sp+9D0h] [bp+8h]@1
  unsigned int dwOwner; // [sp+9D8h] [bp+10h]@1

  dwOwner = dwAvatorSerial;
  v37 = this;
  v4 = &v8;
  for ( i = 624i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v36 = (unsigned __int64)&v8 ^ _security_cookie;
  DstBuf = 0;
  memset(&v25, 0, 0x7FFui64);
  Src = 0;
  memset(&v27, 0, 0x7Fui64);
  v28 = &pNewData->dbPostData;
  v29 = &pOldData->dbPostData;
  v30 = &pNewData->dbPostData.dbRetPost.m_bUpdate;
  v31 = &pNewData->dbPostData.dbDelPost.m_bUpdate;
  if ( pNewData->dbPostData.dbDelPost.m_bUpdate )
  {
    for ( j = 0; (signed int)j < *(_DWORD *)(v31 + 5); ++j )
    {
      sprintf_s(&DstBuf, 0x800ui64, "update tbl_PostStorage set dck=1 where ");
      if ( *(_DWORD *)&v31[8 * j + 9] )
      {
        sprintf_s(&Src, 0x80ui64, "serial=%d", *(_DWORD *)&v31[8 * j + 9]);
      }
      else
      {
        LODWORD(wszSendName) = dwOwner;
        sprintf_s(&Src, 0x80ui64, "sindex=%d and owner=%d", *(_DWORD *)&v31[8 * j + 13]);
      }
      strcat_s(&DstBuf, 0x800ui64, &Src);
      CRFWorldDatabase::Update_Post(v37->m_pWorldDB, &DstBuf);
    }
  }
  if ( v28->dbPost.m_bUpdate )
  {
    for ( j = 0; (signed int)j < 50; ++j )
    {
      pdwStorageSerial = (unsigned int *)((char *)v28 + 297 * (signed int)j);
      v34 = (_POSTDATA_DB_BASE *)((char *)v29 + 297 * (signed int)j);
      if ( *((_BYTE *)pdwStorageSerial + 29) )
      {
        v32 = 0;
        if ( *((_BYTE *)pdwStorageSerial + 8) != 255 )
        {
          if ( *((_BYTE *)pdwStorageSerial + 31) )
          {
            if ( !CRFWorldDatabase::Select_PostStorageRecordCheck(v37->m_pWorldDB) )
              CRFWorldDatabase::Insert_PostStorageRecord(v37->m_pWorldDB);
            if ( CRFWorldDatabase::Select_PostStorageEmptyRecordSerial(v37->m_pWorldDB, pdwStorageSerial) )
            {
              v35 = *((_BYTE *)pdwStorageSerial + 4);
              lnUID = *(_QWORD *)((char *)pdwStorageSerial + 289);
              bGetNumber = 0;
              pbyNumber = &v35;
              wStorageIndex = j;
              byErr = 0;
              dwGold = *(unsigned int *)((char *)pdwStorageSerial + 25);
              dwU = *(unsigned int *)((char *)pdwStorageSerial + 21);
              dwD = *(_QWORD *)((char *)pdwStorageSerial + 13);
              nK = *(unsigned int *)((char *)pdwStorageSerial + 9);
              wszContent = (char *)(pdwStorageSerial + 22);
              wszTitle = (char *)pdwStorageSerial + 67;
              wszRecvName = (char *)pdwStorageSerial + 50;
              wszSendName = (char *)pdwStorageSerial + 33;
              *((_BYTE *)pdwStorageSerial + 30) = CRFWorldDatabase::Update_PostStorageSendToRecver(
                                                    pkDB,
                                                    dwOwner,
                                                    *pdwStorageSerial,
                                                    *((_BYTE *)pdwStorageSerial + 8),
                                                    (char *)pdwStorageSerial + 33,
                                                    (char *)pdwStorageSerial + 50,
                                                    (char *)pdwStorageSerial + 67,
                                                    (char *)pdwStorageSerial + 88,
                                                    nK,
                                                    dwD,
                                                    dwU,
                                                    dwGold,
                                                    0,
                                                    j,
                                                    &v35,
                                                    0,
                                                    lnUID);
            }
          }
          else
          {
            if ( *pdwStorageSerial )
            {
              sprintf_s(&DstBuf, 0x800ui64, "update tbl_PostStorage set ");
              if ( *((_BYTE *)pdwStorageSerial + 8) != v34->dbPost.m_PostList[0].byState )
              {
                sprintf_s(&Src, 0x80ui64, "poststate=%d,", *((_BYTE *)pdwStorageSerial + 8));
                strcat_s(&DstBuf, 0x800ui64, &Src);
                v32 = 1;
              }
              if ( pdwStorageSerial[1] != v34->dbPost.m_PostList[0].nNumber )
              {
                sprintf_s(&Src, 0x80ui64, "postinx=%d,", pdwStorageSerial[1]);
                strcat_s(&DstBuf, 0x800ui64, &Src);
                v32 = 1;
              }
              if ( *(unsigned int *)((char *)pdwStorageSerial + 9) != v34->dbPost.m_PostList[0].nKey )
              {
                sprintf_s(&Src, 0x80ui64, "k=%d,", *(unsigned int *)((char *)pdwStorageSerial + 9));
                strcat_s(&DstBuf, 0x800ui64, &Src);
                v32 = 1;
              }
              if ( *(_QWORD *)((char *)pdwStorageSerial + 13) != v34->dbPost.m_PostList[0].dwDur )
              {
                sprintf_s(&Src, 0x80ui64, "d=%d,", *(_QWORD *)((char *)pdwStorageSerial + 13));
                strcat_s(&DstBuf, 0x800ui64, &Src);
                v32 = 1;
              }
              if ( *(unsigned int *)((char *)pdwStorageSerial + 21) != v34->dbPost.m_PostList[0].dwUpt )
              {
                sprintf_s(&Src, 0x80ui64, "u=%d,", *(unsigned int *)((char *)pdwStorageSerial + 21));
                strcat_s(&DstBuf, 0x800ui64, &Src);
                v32 = 1;
              }
              if ( *(_QWORD *)((char *)pdwStorageSerial + 289) != v34->dbPost.m_PostList[0].lnUID )
              {
                sprintf_s(&Src, 0x80ui64, "uid=%I64d,", *(_QWORD *)((char *)pdwStorageSerial + 289));
                strcat_s(&DstBuf, 0x800ui64, &Src);
                v32 = 1;
              }
              if ( *(unsigned int *)((char *)pdwStorageSerial + 25) != v34->dbPost.m_PostList[0].dwGold )
              {
                sprintf_s(&Src, 0x80ui64, "gold=%d,", *(unsigned int *)((char *)pdwStorageSerial + 25));
                strcat_s(&DstBuf, 0x800ui64, &Src);
                v32 = 1;
              }
              if ( *((_BYTE *)pdwStorageSerial + 32) )
              {
                sprintf_s(&Src, 0x80ui64, "sindex=%d,", j);
                strcat_s(&DstBuf, 0x800ui64, &Src);
                v32 = 1;
              }
              sprintf_s(&Src, 0x80ui64, "where serial=%d", *pdwStorageSerial);
              *(&v23 + strlen_0(&DstBuf)) = 32;
              strcat_s(&DstBuf, 0x800ui64, &Src);
            }
            else
            {
              v6 = *((_BYTE *)pdwStorageSerial + 8);
              nK = *(unsigned int *)((char *)pdwStorageSerial + 25);
              LODWORD(wszContent) = *(unsigned int *)((char *)pdwStorageSerial + 21);
              wszTitle = *(char **)((char *)pdwStorageSerial + 13);
              LODWORD(wszRecvName) = *(unsigned int *)((char *)pdwStorageSerial + 9);
              LODWORD(wszSendName) = pdwStorageSerial[1];
              sprintf_s(
                &DstBuf,
                0x800ui64,
                "update tbl_PostStorage set poststate=%d,postinx=%d,k=%d,d=%d,u=%d,gold=%d",
                v6);
              sprintf_s(&Src, 0x80ui64, ",uid=%I64d", *(_QWORD *)((char *)pdwStorageSerial + 289));
              strcat_s(&DstBuf, 0x800ui64, &Src);
              LODWORD(wszSendName) = dwOwner;
              sprintf_s(&Src, 0x80ui64, " where dck=0 and sindex=%d and owner=%u", j);
              strcat_s(&DstBuf, 0x800ui64, &Src);
              v32 = 1;
            }
            if ( v32 )
              *((_BYTE *)pdwStorageSerial + 30) = CRFWorldDatabase::Update_Post(v37->m_pWorldDB, &DstBuf);
          }
        }
      }
    }
  }
  if ( *v30 )
  {
    for ( j = 0; (signed int)j < *(_DWORD *)(v30 + 5); ++j )
    {
      sprintf_s(&DstBuf, 0x800ui64, "update tbl_PostStorage set dck=1 where serial=%d", *(_DWORD *)&v30[4 * j + 9]);
      CRFWorldDatabase::Update_Post(v37->m_pWorldDB, &DstBuf);
    }
  }
  return 0;
}
