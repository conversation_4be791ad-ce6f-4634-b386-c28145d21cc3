/*
 * Function: ?Init@CUnmannedTraderUserInfo@@QEAA_NG@Z
 * Address: 0x140353280
 */

char __fastcall CUnmannedTraderUserInfo::Init(CUnmannedTraderUserInfo *this, unsigned __int16 wInx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderRegistItemInfo *v4; // rax@4
  char result; // al@5
  CUnmannedTraderRegistItemInfo *v6; // rax@6
  __int64 v7; // [sp+0h] [bp-128h]@1
  CUnmannedTraderRegistItemInfo v8; // [sp+20h] [bp-108h]@4
  CUnmannedTraderRegistItemInfo v9; // [sp+88h] [bp-A0h]@6
  __int64 v10; // [sp+F0h] [bp-38h]@4
  CUnmannedTraderRegistItemInfo *v11; // [sp+F8h] [bp-30h]@4
  CUnmannedTraderRegistItemInfo *_Val; // [sp+100h] [bp-28h]@4
  CUnmannedTraderRegistItemInfo *v13; // [sp+108h] [bp-20h]@6
  CUnmannedTraderRegistItemInfo *v14; // [sp+110h] [bp-18h]@6
  CUnmannedTraderUserInfo *v15; // [sp+130h] [bp+8h]@1

  v15 = this;
  v2 = &v7;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = -2i64;
  v15->m_wInx = wInx;
  CUnmannedTraderRegistItemInfo::CUnmannedTraderRegistItemInfo(&v8);
  v11 = v4;
  _Val = v4;
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::assign(
    &v15->m_vecRegistItemInfo,
    0xAui64,
    v4);
  CUnmannedTraderRegistItemInfo::~CUnmannedTraderRegistItemInfo(&v8);
  if ( std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(&v15->m_vecRegistItemInfo) == 10 )
  {
    CUnmannedTraderRegistItemInfo::CUnmannedTraderRegistItemInfo(&v9);
    v13 = v6;
    v14 = v6;
    std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::assign(
      &v15->m_vecLoadItemInfo,
      0x14ui64,
      v6);
    CUnmannedTraderRegistItemInfo::~CUnmannedTraderRegistItemInfo(&v9);
    if ( std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(&v15->m_vecLoadItemInfo) == 20 )
    {
      CUnmannedTraderRequestLimiter::ClearRequset(&v15->m_kRequestState);
      v15->m_eState = 0;
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
