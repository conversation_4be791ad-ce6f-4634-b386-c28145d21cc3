/*
 * Function: ?VerifyDigest@HashTransformation@CryptoPP@@UEAA_NPEBE0_K@Z
 * Address: 0x14044DCF0
 */

int __fastcall CryptoPP::HashTransformation::VerifyDigest(CryptoPP::HashTransformation *this, const char *digest, const char *input, unsigned __int64 length)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  CryptoPP::HashTransformation *v8; // [sp+30h] [bp+8h]@1
  const char *v9; // [sp+38h] [bp+10h]@1

  v9 = digest;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  ((void (__fastcall *)(CryptoPP::HashTransformation *, const char *, unsigned __int64))v8->vfptr[1].Clone)(
    v8,
    input,
    length);
  return ((int (__fastcall *)(CryptoPP::HashTransformation *, const char *))v8->vfptr[6].__vecDelDtor)(v8, v9);
}
