/*
 * Function: ?GetMyThreadPool@?$ThreadParamInterface@VCBossMonsterScheduleSystem@@VAbstractThreadPool@US@@@US@@QEAAPEAVAbstractThreadPool@2@XZ
 * Address: 0x14041FA60
 */

US::AbstractThreadPool *__fastcall US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>::GetMyThreadPool(US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> *this)
{
  return this->m_pMyThreadPool;
}
