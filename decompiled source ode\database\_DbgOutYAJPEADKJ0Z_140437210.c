/*
 * Function: ?_DbgOut@@YAJPEADKJ0@Z
 * Address: 0x140437210
 */

__int64 __fastcall _DbgOut(char *strFile, unsigned int dwLine, HRESULT hr, char *strMsg)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-158h]@1
  char OutputString; // [sp+30h] [bp-128h]@4
  unsigned __int64 v9; // [sp+140h] [bp-18h]@4
  char *v10; // [sp+160h] [bp+8h]@1
  HRESULT v11; // [sp+170h] [bp+18h]@1
  char *lpOutputString; // [sp+178h] [bp+20h]@1

  lpOutputString = strMsg;
  v11 = hr;
  v10 = strFile;
  v4 = &v7;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = (unsigned __int64)&v7 ^ _security_cookie;
  wsprintfA(&OutputString, "%s(%ld): ", v10, dwLine);
  OutputDebugStringA(&OutputString);
  OutputDebugStringA(lpOutputString);
  if ( v11 )
  {
    wsprintfA(&OutputString, "(hr=%08lx)\n", (unsigned int)v11);
    OutputDebugStringA(&OutputString);
  }
  OutputDebugStringA("\n");
  return (unsigned int)v11;
}
