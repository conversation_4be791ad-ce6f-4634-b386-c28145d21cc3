/*
 * Function: ?SetSFCont@CEquipItemSFAgent@@IEAAXHPEAU_sf_continous@@@Z
 * Address: 0x1401212E0
 */

void __fastcall CEquipItemSFAgent::SetSFCont(CEquipItemSFAgent *this, int nEquipTblIndex, _sf_continous *pSF)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CPlayer::CashChangeStateFlag *v5; // rax@8
  CPlayer::CashChangeStateFlag *v6; // rax@11
  float *v7; // rax@11
  float *v8; // rcx@11
  __int64 v9; // [sp+0h] [bp-58h]@1
  char byDirect; // [sp+20h] [bp-38h]@11
  bool *v11; // [sp+30h] [bp-28h]@6
  CPlayer::CashChangeStateFlag v12; // [sp+38h] [bp-20h]@8
  CPlayer::CashChangeStateFlag v13; // [sp+3Ch] [bp-1Ch]@11
  int v14; // [sp+40h] [bp-18h]@7
  int v15; // [sp+44h] [bp-14h]@10
  bool v16; // [sp+48h] [bp-10h]@11
  CEquipItemSFAgent *v17; // [sp+60h] [bp+8h]@1

  v17 = this;
  v3 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( nEquipTblIndex < 8 && nEquipTblIndex >= 0 )
  {
    v11 = &v17->m_pContSF[nEquipTblIndex]->m_bExist;
    v17->m_pContSF[nEquipTblIndex] = pSF;
    if ( pSF )
    {
      v14 = nEquipTblIndex;
      if ( nEquipTblIndex == 7 )
      {
        v17->m_pMaster->m_byMoveType = 2;
        CPlayer::SenseState(v17->m_pMaster);
        CPlayer::CashChangeStateFlag::CashChangeStateFlag(&v12, 0);
        CPlayer::UpdateVisualVer(v17->m_pMaster, (CPlayer::CashChangeStateFlag)v5->0);
      }
    }
    else
    {
      v15 = nEquipTblIndex;
      if ( nEquipTblIndex == 7 )
      {
        v17->m_pMaster->m_byMoveType = 1;
        CPlayer::SenseState(v17->m_pMaster);
        CPlayer::CashChangeStateFlag::CashChangeStateFlag(&v13, 0);
        CPlayer::UpdateVisualVer(v17->m_pMaster, (CPlayer::CashChangeStateFlag)v6->0);
        v16 = v17->m_pMaster->m_byMoveDirect == 0;
        v7 = v17->m_pMaster->m_fTarPos;
        v8 = v17->m_pMaster->m_fCurPos;
        byDirect = v16;
        CPlayer::pc_MoveNext(v17->m_pMaster, 1, v8, v7, v16);
        if ( v11 )
        {
          byDirect = 0;
          CPlayer::SendMsg_Circle_DelEffect(v17->m_pMaster, v11[1], *((_WORD *)v11 + 1), v11[4], 0);
        }
      }
    }
  }
}
