/*
 * Function: ?_db_Update_General@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD_N@Z
 * Address: 0x1401AA500
 */

char __fastcall CMainThread::_db_Update_General(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *pSzQuery, bool bCheck<PERSON>owHigh)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  int v8; // eax@39
  int v9; // eax@42
  int v10; // eax@43
  int v11; // eax@64
  int v12; // eax@65
  int v13; // eax@72
  int v14; // eax@75
  int v15; // eax@76
  int v16; // eax@80
  char v17; // al@87
  char v18; // al@90
  char v19; // al@91
  char v20; // al@97
  size_t v21; // rax@143
  __int64 v23; // [sp+0h] [bp-1C8h]@1
  int v24; // [sp+20h] [bp-1A8h]@64
  unsigned __int64 v25; // [sp+28h] [bp-1A0h]@64
  unsigned int v26; // [sp+30h] [bp-198h]@64
  unsigned int v27; // [sp+38h] [bp-190h]@64
  unsigned __int64 v28; // [sp+40h] [bp-188h]@64
  unsigned __int64 v29; // [sp+48h] [bp-180h]@65
  char Source; // [sp+60h] [bp-168h]@4
  char v31; // [sp+61h] [bp-167h]@4
  char *Dest; // [sp+138h] [bp-90h]@4
  size_t Size; // [sp+140h] [bp-88h]@4
  unsigned int v34; // [sp+148h] [bp-80h]@50
  unsigned int v35; // [sp+14Ch] [bp-7Ch]@50
  unsigned int v36; // [sp+150h] [bp-78h]@59
  int v37; // [sp+160h] [bp-68h]@42
  __int64 v38; // [sp+168h] [bp-60h]@64
  __int64 v39; // [sp+170h] [bp-58h]@65
  int v40; // [sp+178h] [bp-50h]@65
  int v41; // [sp+17Ch] [bp-4Ch]@75
  __int64 v42; // [sp+180h] [bp-48h]@80
  int v43; // [sp+188h] [bp-40h]@90
  __int64 v44; // [sp+190h] [bp-38h]@91
  __int64 v45; // [sp+198h] [bp-30h]@97
  __int64 v46; // [sp+1A0h] [bp-28h]@97
  __int64 v47; // [sp+1A8h] [bp-20h]@97
  unsigned __int64 v48; // [sp+1B0h] [bp-18h]@4
  unsigned int v49; // [sp+1D8h] [bp+10h]@1
  _AVATOR_DATA *v50; // [sp+1E0h] [bp+18h]@1
  _AVATOR_DATA *v51; // [sp+1E8h] [bp+20h]@1

  v51 = pOldData;
  v50 = pNewData;
  v49 = dwSerial;
  v6 = &v23;
  for ( i = 112i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v48 = (unsigned __int64)&v23 ^ _security_cookie;
  Source = 0;
  memset(&v31, 0, 0xC7ui64);
  Dest = pSzQuery;
  sprintf(pSzQuery, "UPDATE tbl_general SET ");
  LODWORD(Size) = strlen_0(Dest);
  if ( v51->dbAvator.m_dExp != v50->dbAvator.m_dExp )
  {
    sprintf(&Source, "Exp=%f,", v50->dbAvator.m_dExp);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbAvator.m_dLossExp != v50->dbAvator.m_dLossExp )
  {
    sprintf(&Source, "LossExp=%f,", v50->dbAvator.m_dLossExp);
    strcat_0(Dest, &Source);
  }
  for ( HIDWORD(Size) = 0; SHIDWORD(Size) < 3; ++HIDWORD(Size) )
  {
    if ( v51->dbAvator.m_zClassHistory[SHIDWORD(Size)] != v50->dbAvator.m_zClassHistory[SHIDWORD(Size)] )
    {
      sprintf(&Source, "Class%d=%d,", HIDWORD(Size), v50->dbAvator.m_zClassHistory[SHIDWORD(Size)]);
      strcat_0(Dest, &Source);
    }
  }
  if ( v51->dbAvator.m_dwClassInitCnt != v50->dbAvator.m_dwClassInitCnt )
  {
    sprintf(&Source, "ClassInitCnt=%d,", v50->dbAvator.m_dwClassInitCnt);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbAvator.m_byLastClassGrade != v50->dbAvator.m_byLastClassGrade )
  {
    sprintf(&Source, "LastClassGrade=%d,", v50->dbAvator.m_byLastClassGrade);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbAvator.m_dwHP != v50->dbAvator.m_dwHP )
  {
    sprintf(&Source, "HP=%d,", v50->dbAvator.m_dwHP);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbAvator.m_dwFP != v50->dbAvator.m_dwFP )
  {
    sprintf(&Source, "FP=%d,", v50->dbAvator.m_dwFP);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbAvator.m_dwSP != v50->dbAvator.m_dwSP )
  {
    sprintf(&Source, "SP=%d,", v50->dbAvator.m_dwSP);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbAvator.m_dwDP != v50->dbAvator.m_dwDP )
  {
    sprintf(&Source, "DP=%d,", v50->dbAvator.m_dwDP);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbAvator.m_byMapCode != v50->dbAvator.m_byMapCode )
  {
    sprintf(&Source, "Map=%d,", v50->dbAvator.m_byMapCode);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbAvator.m_fStartPos[0] != v50->dbAvator.m_fStartPos[0] )
  {
    sprintf(&Source, "X=%f,", v50->dbAvator.m_fStartPos[0]);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbAvator.m_fStartPos[1] != v50->dbAvator.m_fStartPos[1] )
  {
    sprintf(&Source, "Y=%f,", v50->dbAvator.m_fStartPos[1]);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbAvator.m_fStartPos[2] != v50->dbAvator.m_fStartPos[2] )
  {
    sprintf(&Source, "Z=%f,", v50->dbAvator.m_fStartPos[2]);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbAvator.m_byBagNum != v50->dbAvator.m_byBagNum )
  {
    sprintf(&Source, "BagNum=%d,", v50->dbAvator.m_byBagNum);
    strcat_0(Dest, &Source);
  }
  for ( HIDWORD(Size) = 0; SHIDWORD(Size) < 6; ++HIDWORD(Size) )
  {
    if ( _EMBELLKEY::IsFilled((_EMBELLKEY *)((char *)&v50->dbEquip + 27 * SHIDWORD(Size))) )
    {
      if ( _EMBELLKEY::IsFilled((_EMBELLKEY *)((char *)&v51->dbEquip + 27 * SHIDWORD(Size))) )
      {
        v37 = _EMBELLKEY::CovDBKey((_EMBELLKEY *)((char *)&v50->dbEquip + 27 * SHIDWORD(Size)));
        v9 = _EMBELLKEY::CovDBKey((_EMBELLKEY *)((char *)&v51->dbEquip + 27 * SHIDWORD(Size)));
        if ( v37 != v9 )
        {
          v10 = _EMBELLKEY::CovDBKey((_EMBELLKEY *)((char *)&v50->dbEquip + 27 * SHIDWORD(Size)));
          sprintf(&Source, "EK%d=%d,", HIDWORD(Size), (unsigned int)v10);
          strcat_0(Dest, &Source);
        }
        if ( v50->dbEquip.m_EmbellishList[SHIDWORD(Size)].wAmount != v51->dbEquip.m_EmbellishList[SHIDWORD(Size)].wAmount )
        {
          if ( (signed int)v50->dbEquip.m_EmbellishList[SHIDWORD(Size)].wAmount < 0xFFFF )
            sprintf(&Source, "ED%d=%d,", HIDWORD(Size), v50->dbEquip.m_EmbellishList[SHIDWORD(Size)].wAmount);
          else
            sprintf(&Source, "ED%d=-1,", HIDWORD(Size));
          strcat_0(Dest, &Source);
        }
        if ( v50->dbEquip.m_EmbellishList[SHIDWORD(Size)].byCsMethod )
        {
          v34 = 0;
          v35 = 0;
          if ( v50->dbEquip.m_EmbellishList[SHIDWORD(Size)].byCsMethod == 1 )
          {
            v34 = v50->dbEquip.m_EmbellishList[SHIDWORD(Size)].dwT - Time;
            v35 = v51->dbEquip.m_EmbellishList[SHIDWORD(Size)].dwT - Time;
          }
          else if ( v50->dbEquip.m_EmbellishList[SHIDWORD(Size)].byCsMethod == 2 )
          {
            v34 = v50->dbEquip.m_EmbellishList[SHIDWORD(Size)].dwT;
            v35 = v51->dbEquip.m_EmbellishList[SHIDWORD(Size)].dwT;
          }
          if ( v34 != v35 )
          {
            sprintf(&Source, "ET%d=%d,", HIDWORD(Size), v34);
            strcat_0(Dest, &Source);
          }
        }
        if ( v50->dbEquip.m_EmbellishList[SHIDWORD(Size)].lnUID != v51->dbEquip.m_EmbellishList[SHIDWORD(Size)].lnUID )
        {
          sprintf(&Source, "ES%d=%I64d,", HIDWORD(Size), v50->dbEquip.m_EmbellishList[SHIDWORD(Size)].lnUID);
          strcat_0(Dest, &Source);
        }
      }
      else
      {
        v36 = 0;
        if ( v50->dbEquip.m_EmbellishList[SHIDWORD(Size)].byCsMethod == 1 )
        {
          v36 = v50->dbEquip.m_EmbellishList[SHIDWORD(Size)].dwT - Time;
        }
        else if ( v50->dbEquip.m_EmbellishList[SHIDWORD(Size)].byCsMethod == 2 )
        {
          v36 = v50->dbEquip.m_EmbellishList[SHIDWORD(Size)].dwT;
        }
        if ( (signed int)v50->dbEquip.m_EmbellishList[SHIDWORD(Size)].wAmount < 0xFFFF )
        {
          v39 = 27i64 * SHIDWORD(Size);
          v40 = v50->dbEquip.m_EmbellishList[SHIDWORD(Size)].wAmount;
          v12 = _EMBELLKEY::CovDBKey((_EMBELLKEY *)((char *)&v50->dbEquip + 27 * SHIDWORD(Size)));
          v29 = v50->dbEquip.m_EmbellishList[(unsigned __int64)v39 / 0x1B].lnUID;
          LODWORD(v28) = HIDWORD(Size);
          v27 = v36;
          v26 = HIDWORD(Size);
          LODWORD(v25) = v40;
          v24 = HIDWORD(Size);
          sprintf(&Source, "EK%d=%d,ED%d=%d,ET%d=%d,ES%d=%I64d,", HIDWORD(Size), (unsigned int)v12);
        }
        else
        {
          v38 = 27i64 * SHIDWORD(Size);
          v11 = _EMBELLKEY::CovDBKey((_EMBELLKEY *)((char *)&v50->dbEquip + 27 * SHIDWORD(Size)));
          v28 = v50->dbEquip.m_EmbellishList[(unsigned __int64)v38 / 0x1B].lnUID;
          v27 = HIDWORD(Size);
          v26 = v36;
          LODWORD(v25) = HIDWORD(Size);
          v24 = HIDWORD(Size);
          sprintf(&Source, "EK%d=%d,ED%d=-1,ET%d=%d,ES%d=%I64d,", HIDWORD(Size), (unsigned int)v11);
        }
        strcat_0(Dest, &Source);
      }
    }
    else if ( _EMBELLKEY::IsFilled((_EMBELLKEY *)((char *)&v51->dbEquip + 27 * SHIDWORD(Size))) )
    {
      v8 = _EMBELLKEY::CovDBKey((_EMBELLKEY *)((char *)&v50->dbEquip + 27 * SHIDWORD(Size)));
      sprintf(&Source, "EK%d=%d,", HIDWORD(Size), (unsigned int)v8);
      strcat_0(Dest, &Source);
    }
  }
  for ( HIDWORD(Size) = 0; SHIDWORD(Size) < 88; ++HIDWORD(Size) )
  {
    if ( _FORCEKEY::IsFilled((_FORCEKEY *)((char *)&v50->dbForce + 25 * SHIDWORD(Size))) )
    {
      if ( _FORCEKEY::IsFilled((_FORCEKEY *)((char *)&v51->dbForce + 25 * SHIDWORD(Size))) )
      {
        v41 = _FORCEKEY::CovDBKey((_FORCEKEY *)((char *)&v50->dbForce + 25 * SHIDWORD(Size)));
        v14 = _FORCEKEY::CovDBKey((_FORCEKEY *)((char *)&v51->dbForce + 25 * SHIDWORD(Size)));
        if ( v41 != v14 )
        {
          v15 = _FORCEKEY::CovDBKey((_FORCEKEY *)((char *)&v50->dbForce + 25 * SHIDWORD(Size)));
          sprintf(&Source, "F%d=%d,", HIDWORD(Size), (unsigned int)v15);
          strcat_0(Dest, &Source);
        }
        if ( v50->dbForce.m_List[SHIDWORD(Size)].lnUID != v51->dbForce.m_List[SHIDWORD(Size)].lnUID )
        {
          sprintf(&Source, "FS%d=%I64d,", HIDWORD(Size), v50->dbForce.m_List[SHIDWORD(Size)].lnUID);
          strcat_0(Dest, &Source);
        }
      }
      else
      {
        v42 = 25i64 * SHIDWORD(Size);
        v16 = _FORCEKEY::CovDBKey((_FORCEKEY *)((char *)&v50->dbForce + 25 * SHIDWORD(Size)));
        v25 = v50->dbForce.m_List[(unsigned __int64)v42 / 0x19].lnUID;
        v24 = HIDWORD(Size);
        sprintf(&Source, "F%d=%d,FS%d=%I64d,", HIDWORD(Size), (unsigned int)v16);
        strcat_0(Dest, &Source);
      }
    }
    else if ( _FORCEKEY::IsFilled((_FORCEKEY *)((char *)&v51->dbForce + 25 * SHIDWORD(Size))) )
    {
      v13 = _FORCEKEY::CovDBKey((_FORCEKEY *)((char *)&v50->dbForce + 25 * SHIDWORD(Size)));
      sprintf(&Source, "F%d=%d,", HIDWORD(Size), (unsigned int)v13);
      strcat_0(Dest, &Source);
    }
  }
  if ( v51->dbAvator.m_byRaceSexCode >> 1 == 1 )
  {
    for ( HIDWORD(Size) = 0; SHIDWORD(Size) < 4; ++HIDWORD(Size) )
    {
      if ( _ANIMUSKEY::IsFilled((_ANIMUSKEY *)&v50->dbAnimus + 34 * SHIDWORD(Size)) )
      {
        if ( _ANIMUSKEY::IsFilled((_ANIMUSKEY *)&v51->dbAnimus + 34 * SHIDWORD(Size)) )
        {
          v43 = (unsigned __int8)_ANIMUSKEY::CovDBKey((_ANIMUSKEY *)&v50->dbAnimus + 34 * SHIDWORD(Size));
          v18 = _ANIMUSKEY::CovDBKey((_ANIMUSKEY *)&v51->dbAnimus + 34 * SHIDWORD(Size));
          if ( v43 != (unsigned __int8)v18 )
          {
            v44 = 34i64 * SHIDWORD(Size);
            v19 = _ANIMUSKEY::CovDBKey((_ANIMUSKEY *)&v50->dbAnimus + 34 * SHIDWORD(Size));
            v25 = v50->dbAnimus.m_List[(unsigned __int64)v44 / 0x22].lnUID;
            v24 = HIDWORD(Size);
            sprintf(&Source, "AK%d=%d,AS%d=%I64d,", HIDWORD(Size), (unsigned __int8)v19);
            strcat_0(Dest, &Source);
          }
          if ( v50->dbAnimus.m_List[SHIDWORD(Size)].dwExp != v51->dbAnimus.m_List[SHIDWORD(Size)].dwExp )
          {
            sprintf(&Source, "AD%d=%I64d,", HIDWORD(Size), v50->dbAnimus.m_List[SHIDWORD(Size)].dwExp);
            strcat_0(Dest, &Source);
          }
          if ( v50->dbAnimus.m_List[SHIDWORD(Size)].dwParam != v51->dbAnimus.m_List[SHIDWORD(Size)].dwParam )
          {
            sprintf(&Source, "AP%d=%d,", HIDWORD(Size), v50->dbAnimus.m_List[SHIDWORD(Size)].dwParam);
            strcat_0(Dest, &Source);
          }
        }
        else
        {
          v45 = 34i64 * SHIDWORD(Size);
          v46 = 34i64 * SHIDWORD(Size);
          v47 = 34i64 * SHIDWORD(Size);
          v20 = _ANIMUSKEY::CovDBKey((_ANIMUSKEY *)&v50->dbAnimus + 34 * SHIDWORD(Size));
          v29 = v50->dbAnimus.m_List[(unsigned __int64)v45 / 0x22].lnUID;
          LODWORD(v28) = HIDWORD(Size);
          v27 = v50->dbAnimus.m_List[(unsigned __int64)v46 / 0x22].dwParam;
          v26 = HIDWORD(Size);
          v25 = v50->dbAnimus.m_List[(unsigned __int64)v47 / 0x22].dwExp;
          v24 = HIDWORD(Size);
          sprintf(&Source, "AK%d=%d,AD%d=%I64d,AP%d=%d,AS%d=%I64d,", HIDWORD(Size), (unsigned __int8)v20);
          strcat_0(Dest, &Source);
        }
      }
      else if ( _ANIMUSKEY::IsFilled((_ANIMUSKEY *)&v51->dbAnimus + 34 * SHIDWORD(Size)) )
      {
        v17 = _ANIMUSKEY::CovDBKey((_ANIMUSKEY *)&v50->dbAnimus + 34 * SHIDWORD(Size));
        sprintf(&Source, "AK%d=%d,", HIDWORD(Size), (unsigned __int8)v17);
        strcat_0(Dest, &Source);
      }
    }
  }
  for ( HIDWORD(Size) = 0; SHIDWORD(Size) < 2; ++HIDWORD(Size) )
  {
    if ( v51->dbStat.m_dwDamWpCnt[SHIDWORD(Size)] != v50->dbStat.m_dwDamWpCnt[SHIDWORD(Size)] )
    {
      sprintf(&Source, "WM%d=%d,", HIDWORD(Size), v50->dbStat.m_dwDamWpCnt[SHIDWORD(Size)]);
      strcat_0(Dest, &Source);
    }
  }
  for ( HIDWORD(Size) = 0; SHIDWORD(Size) < 24; ++HIDWORD(Size) )
  {
    if ( v51->dbStat.m_dwForceCum[SHIDWORD(Size)] != v50->dbStat.m_dwForceCum[SHIDWORD(Size)] )
    {
      sprintf(&Source, "FM%d=%d,", HIDWORD(Size), v50->dbStat.m_dwForceCum[SHIDWORD(Size)]);
      strcat_0(Dest, &Source);
    }
  }
  for ( HIDWORD(Size) = 0; SHIDWORD(Size) < 48; ++HIDWORD(Size) )
  {
    if ( v51->dbStat.m_dwSkillCum[SHIDWORD(Size)] != v50->dbStat.m_dwSkillCum[SHIDWORD(Size)] )
    {
      sprintf(&Source, "SM%d=%d,", HIDWORD(Size), v50->dbStat.m_dwSkillCum[SHIDWORD(Size)]);
      strcat_0(Dest, &Source);
    }
  }
  for ( HIDWORD(Size) = 0; SHIDWORD(Size) < 3; ++HIDWORD(Size) )
  {
    if ( v51->dbStat.m_dwMakeCum[SHIDWORD(Size)] != v50->dbStat.m_dwMakeCum[SHIDWORD(Size)] )
    {
      sprintf(&Source, "MI%d=%d,", HIDWORD(Size), v50->dbStat.m_dwMakeCum[SHIDWORD(Size)]);
      strcat_0(Dest, &Source);
    }
  }
  if ( v51->dbStat.m_dwSpecialCum != v50->dbStat.m_dwSpecialCum )
  {
    sprintf(&Source, "SR=%d,", v50->dbStat.m_dwSpecialCum);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbStat.m_dwDefenceCnt != v50->dbStat.m_dwDefenceCnt )
  {
    sprintf(&Source, "DM=%d,", v50->dbStat.m_dwDefenceCnt);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbStat.m_dwShieldCnt != v50->dbStat.m_dwShieldCnt )
  {
    sprintf(&Source, "PM=%d,", v50->dbStat.m_dwShieldCnt);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbAvator.m_dPvPPoint != v50->dbAvator.m_dPvPPoint )
  {
    sprintf(&Source, "PvpPoint=%f,", v50->dbAvator.m_dPvPPoint);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbAvator.m_dPvPCashBag != v50->dbAvator.m_dPvPCashBag )
  {
    sprintf(&Source, "PvpCashBag=%f,", v50->dbAvator.m_dPvPCashBag);
    strcat_0(Dest, &Source);
  }
  if ( strcmp_0(v51->dbAvator.m_szBindMapCode, v50->dbAvator.m_szBindMapCode) )
  {
    v50->dbAvator.m_szBindMapCode[11] = 0;
    sprintf(&Source, "BindMapCode='%s',", v50->dbAvator.m_szBindMapCode);
    strcat_0(Dest, &Source);
  }
  if ( strcmp_0(v51->dbAvator.m_szBindDummy, v50->dbAvator.m_szBindDummy) )
  {
    v50->dbAvator.m_szBindDummy[11] = 0;
    sprintf(&Source, "BindDummy='%s',", v50->dbAvator.m_szBindDummy);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbAvator.m_dwRadarDelayTime != v50->dbAvator.m_dwRadarDelayTime )
  {
    sprintf_s<200>((char (*)[200])&Source, "RDDelay=%d,", v50->dbAvator.m_dwRadarDelayTime);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbAvator.m_dwTakeLastMentalTicket != v50->dbAvator.m_dwTakeLastMentalTicket )
  {
    sprintf_s<200>((char (*)[200])&Source, "TakeLastMentalTicket=%d,", v50->dbAvator.m_dwTakeLastMentalTicket);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbAvator.m_dwTakeLastCriTicket != v50->dbAvator.m_dwTakeLastCriTicket )
  {
    sprintf_s<200>((char (*)[200])&Source, "TakeLastCriTicket=%d,", v50->dbAvator.m_dwTakeLastCriTicket);
    strcat_0(Dest, &Source);
  }
  if ( v51->dbAvator.m_byMaxLevel != v50->dbAvator.m_byMaxLevel )
  {
    sprintf(&Source, "MaxLevel=%d,", v50->dbAvator.m_byMaxLevel);
    strcat_0(Dest, &Source);
  }
  if ( v50->dbCutting.m_bOldDataLoad )
    strcat_0(Dest, "LeftResList='*',");
  sprintf(&Source, "TotalPlayMin=%d,", v50->dbAvator.m_dwTotalPlayMin);
  strcat_0(Dest, &Source);
  v21 = strlen_0(Dest);
  if ( v21 <= (unsigned int)Size )
  {
    memset_0(Dest, 0, (unsigned int)Size);
  }
  else
  {
    sprintf(&Source, "WHERE Serial = %d", v49);
    Dest[strlen_0(Dest) - 1] = 32;
    strcat_0(Dest, &Source);
  }
  return 1;
}
