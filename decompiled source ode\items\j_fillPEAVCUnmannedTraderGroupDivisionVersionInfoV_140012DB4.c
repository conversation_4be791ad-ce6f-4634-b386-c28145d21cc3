/*
 * Function: j_??$fill@PEAVCUnmannedTraderGroupDivisionVersionInfo@@V1@@std@@YAXPEAVCUnmannedTraderGroupDivisionVersionInfo@@0AEBV1@@Z
 * Address: 0x140012DB4
 */

void __fastcall std::fill<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo>(CUnmannedTraderGroupDivisionVersionInfo *_First, CUnmannedTraderGroupDivisionVersionInfo *_Last, CUnmannedTraderGroupDivisionVersionInfo *_Val)
{
  std::fill<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo>(_First, _Last, _Val);
}
