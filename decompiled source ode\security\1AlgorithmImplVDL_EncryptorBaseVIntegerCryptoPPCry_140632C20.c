/*
 * Function: ??1?$AlgorithmImpl@V?$DL_EncryptorBase@VInteger@CryptoPP@@@CryptoPP@@U?$DLIES@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@CryptoPP@@$00@2@@CryptoPP@@UEAA@XZ
 * Address: 0x140632C20
 */

int CryptoPP::AlgorithmImpl<CryptoPP::DL_EncryptorBase<CryptoPP::Integer>,CryptoPP::DLIES<CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,1>>::~AlgorithmImpl<CryptoPP::DL_EncryptorBase<CryptoPP::Integer>,CryptoPP::DLIES<CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,1>>()
{
  return CryptoPP::DL_EncryptorBase<CryptoPP::Integer>::~DL_EncryptorBase<CryptoPP::Integer>();
}
