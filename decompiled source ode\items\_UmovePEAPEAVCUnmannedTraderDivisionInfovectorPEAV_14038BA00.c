/*
 * Function: ??$_Umove@PEAPEAVCUnmannedTraderDivisionInfo@@@?$vector@PEAVCUnmannedTraderDivisionInfo@@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@IEAAPEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV2@00@Z
 * Address: 0x14038BA00
 */

CUnmannedTraderDivisionInfo **__fastcall std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_Umove<CUnmannedTraderDivisionInfo * *>(std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *this, CUnmannedTraderDivisionInfo **_First, CUnmannedTraderDivisionInfo **_Last, CUnmannedTraderDivisionInfo **_Ptr)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return stdext::_Unchecked_uninitialized_move<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo * *,std::allocator<CUnmannedTraderDivisionInfo *>>(
           _First,
           _Last,
           _Ptr,
           &v8->_Alval);
}
