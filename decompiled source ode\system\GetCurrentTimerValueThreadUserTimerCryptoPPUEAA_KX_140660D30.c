/*
 * Function: ?GetCurrentTimerValue@ThreadUserTimer@CryptoPP@@UEAA_KXZ
 * Address: 0x140660D30
 */

unsigned __int64 __fastcall CryptoPP::ThreadUserTimer::GetCurrentTimerValue(CryptoPP::ThreadUserTimer *this)
{
  HANDLE v1; // rax@2
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v2; // rax@5
  _FILETIME UserTime; // [sp+30h] [bp-F8h]@2
  _FILETIME CreationTime; // [sp+38h] [bp-F0h]@2
  unsigned int v6; // [sp+40h] [bp-E8h]@3
  CryptoPP::Exception v7; // [sp+48h] [bp-E0h]@5
  char v8; // [sp+98h] [bp-90h]@5
  char v9; // [sp+C8h] [bp-60h]@5
  __int64 v10; // [sp+F8h] [bp-30h]@1
  __int64 v11; // [sp+100h] [bp-28h]@5
  __int64 v12; // [sp+108h] [bp-20h]@5
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v13; // [sp+110h] [bp-18h]@5
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *s; // [sp+118h] [bp-10h]@5

  v10 = -2i64;
  if ( byte_1409850E0 )
  {
    v1 = GetCurrentThread();
    if ( GetThreadTimes(v1, &CreationTime, &CreationTime, &CreationTime, &UserTime) )
      return ((unsigned __int64)UserTime.dwHighDateTime << 32) + UserTime.dwLowDateTime;
    v6 = GetLastError();
    if ( v6 != 120 )
    {
      v11 = CryptoPP::IntToString<unsigned long>((__int64)&v8, v6, 0xAu);
      v12 = v11;
      LODWORD(v2) = std::operator+<char,std::char_traits<char>,std::allocator<char>>(
                      &v9,
                      "ThreadUserTimer: GetThreadTimes failed with error ",
                      v11);
      v13 = v2;
      s = v2;
      CryptoPP::Exception::Exception(&v7, OTHER_ERROR, v2);
      CxxThrowException_0((__int64)&v7, (__int64)&TI2_AVException_CryptoPP__);
    }
    byte_1409850E0 = 0;
  }
  return 10000i64 * clock();
}
