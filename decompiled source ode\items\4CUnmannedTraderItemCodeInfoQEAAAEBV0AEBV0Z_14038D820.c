/*
 * Function: ??4CUnmannedTraderItemCodeInfo@@QEAAAEBV0@AEBV0@@Z
 * Address: 0x14038D820
 */

CUnmannedTraderItemCodeInfo *__fastcall CUnmannedTraderItemCodeInfo::operator=(CUnmannedTraderItemCodeInfo *this, CUnmannedTraderItemCodeInfo *lhs)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderItemCodeInfo *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6->m_dwStartInx = lhs->m_dwStartInx;
  v6->m_dwEndInx = lhs->m_dwEndInx;
  v6->m_szCode[0] = 0;
  strcpy_0(v6->m_szCode, lhs->m_szCode);
  v6->m_szCode[63] = 0;
  return v6;
}
