/*
 * Function: ?Set@CCurrentGuildBattleInfoManager@GUILD_BATTLE@@QEAA_NIPEAVCNormalGuildBattle@2@@Z
 * Address: 0x1403CE160
 */

char __fastcall GUILD_BATTLE::CCurrentGuildBattleInfoManager::Set(GUILD_BATTLE::CCurrentGuildBattleInfoManager *this, unsigned int uiMapID, GUILD_BATTLE::CNormalGuildBattle *pkBattle)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  GUILD_BATTLE::CGuildBattleLogger *v6; // rax@8
  __int64 v7; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CCurrentGuildBattleInfoManager *v8; // [sp+30h] [bp+8h]@1
  unsigned int uiMapIDa; // [sp+38h] [bp+10h]@1
  GUILD_BATTLE::CNormalGuildBattle *v10; // [sp+40h] [bp+18h]@1

  v10 = pkBattle;
  uiMapIDa = uiMapID;
  v8 = this;
  v3 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v8->m_bInit && pkBattle )
  {
    GUILD_BATTLE::CCurrentGuildBattleInfoManager::Clear(v8, uiMapID);
    if ( GUILD_BATTLE::CNormalGuildBattle::GetInfo(v10, &v8->m_pkInfo[uiMapIDa]) )
    {
      v8->m_pbUpdate[uiMapIDa] = 1;
      result = 1;
    }
    else
    {
      v6 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v6,
        "CCurrentGuildBattleInfoManager::Set( %u, pkBattle )pkBattle->GetInfo( m_pkInfo[%u] ) Fail!",
        uiMapIDa,
        uiMapIDa);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
