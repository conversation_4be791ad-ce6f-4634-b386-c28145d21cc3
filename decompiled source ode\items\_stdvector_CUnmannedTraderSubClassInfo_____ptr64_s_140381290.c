/*
 * Function: _std::vector_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64___::_Insert_std::_Vector_const_iterator_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64______::_1_::catch$1
 * Address: 0x140381290
 */

void __fastcall __noreturn std::vector_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64___::_Insert_std::_Vector_const_iterator_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64______::_1_::catch_1(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Destroy(
    *(std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > **)(a2 + 736),
    (CUnmannedTraderSubClassInfo **)(*(_QWORD *)(*(_QWORD *)(a2 + 744) + 16i64) + 8i64 * *(_QWORD *)(a2 + 40)),
    (CUnmannedTraderSubClassInfo **)(*(_QWORD *)(*(_QWORD *)(a2 + 736) + 24i64) + 8i64 * *(_QWORD *)(a2 + 40)));
  CxxThrowException_0(0i64, 0i64);
}
