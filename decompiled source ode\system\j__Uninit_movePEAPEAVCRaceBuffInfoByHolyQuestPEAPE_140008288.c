/*
 * Function: j_??$_Uninit_move@PEAPEAVCRaceBuffInfoByHolyQuest@@PEAPEAV1@V?$allocator@PEAVCRaceBuffInfoByHolyQuest@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAVCRaceBuffInfoByHolyQuest@@PEAPEAV1@00AEAV?$allocator@PEAVCRaceBuffInfoByHolyQuest@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140008288
 */

CRaceBuffInfoByHolyQuest **__fastcall std::_Uninit_move<CRaceBuffInfoByHolyQuest * *,CRaceBuffInfoByHolyQuest * *,std::allocator<CRaceBuffInfoByHolyQuest *>,std::_Undefined_move_tag>(CRaceBuffInfoByHolyQuest **_First, CRaceBuffInfoByHolyQuest **_Last, CRaceBuffInfoByHolyQuest **_Dest, std::allocator<CRaceBuffInfoByHolyQuest *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<CRaceBuffInfoByHolyQuest * *,CRaceBuffInfoByHolyQuest * *,std::allocator<CRaceBuffInfoByHolyQuest *>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
