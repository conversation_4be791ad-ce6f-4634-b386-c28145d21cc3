/*
 * Function: ?CreateComplete@CRaceBuffInfoByHolyQuest@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x1403B4010
 */

char __fastcall CRaceBuffInfoByHolyQuest::CreateComplete(CRaceBuffInfoByHolyQuest *this, CPlayer *pkDest)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CRaceBuffInfoByHolyQuest *v6; // [sp+30h] [bp+8h]@1
  CPlayer *pkDesta; // [sp+38h] [bp+10h]@1

  pkDesta = pkDest;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CRaceBuffInfoByHolyQuest::ApplyEffect(v6, pkDest, 1) )
  {
    CRaceBuffInfoByHolyQuest::NotifyLogInSetBuff(v6, pkDesta->m_ObjID.m_wIndex);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
