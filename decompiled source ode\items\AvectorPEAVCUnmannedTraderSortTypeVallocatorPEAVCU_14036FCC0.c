/*
 * Function: ??A?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEAAAEAPEAVCUnmannedTraderSortType@@_K@Z
 * Address: 0x14036FCC0
 */

CUnmannedTraderSortType **__fastcall std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::operator[](std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, unsigned __int64 _Pos)
{
  return &this->_Myfirst[_Pos];
}
