/*
 * Function: j_??$_Uninit_fill_n@PEAPEAUINI_Section@@_KPEAU1@V?$allocator@PEAUINI_Section@@@std@@@std@@YAXPEAPEAUINI_Section@@_KAEBQEAU1@AEAV?$allocator@PEAUINI_Section@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000F867
 */

void __fastcall std::_Uninit_fill_n<INI_Section * *,unsigned __int64,INI_Section *,std::allocator<INI_Section *>>(INI_Section **_First, unsigned __int64 _Count, INI_Section *const *_Val, std::allocator<INI_Section *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<INI_Section * *,unsigned __int64,INI_Section *,std::allocator<INI_Section *>>(
    _First,
    _Count,
    _Val,
    __formal,
    a5,
    a6);
}
