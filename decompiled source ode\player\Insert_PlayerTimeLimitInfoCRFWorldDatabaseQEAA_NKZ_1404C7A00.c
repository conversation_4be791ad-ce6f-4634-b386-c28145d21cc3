/*
 * Function: ?Insert_PlayerTimeLimitInfo@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x1404C7A00
 */

bool __fastcall CRFWorldDatabase::Insert_PlayerTimeLimitInfo(CRFWorldDatabase *this, unsigned int dwAccountSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-158h]@1
  char DstBuf; // [sp+30h] [bp-128h]@4
  unsigned __int64 v7; // [sp+140h] [bp-18h]@4
  CRFWorldDatabase *v8; // [sp+160h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = (unsigned __int64)&v5 ^ _security_cookie;
  sprintf_s(&DstBuf, 0x100ui64, "Insert into [dbo].[tbl_TimeLimitInfo] ( AccountSerial) values (%d)", dwAccountSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v8->vfptr, &DstBuf, 1);
}
