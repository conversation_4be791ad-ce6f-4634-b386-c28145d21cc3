/*
 * Function: ?SendMsg_NotifyHolyKeeperAttackTimeBe<PERSON>eep<PERSON>eeper@CHolyStoneSystem@@QEAAX_N@Z
 * Address: 0x14027C7E0
 */

void __fastcall CHolyStoneSystem::SendMsg_NotifyHolyKeeperAttackTimeBeKeepKeeper(CHolyStoneSystem *this, bool b<PERSON>eep<PERSON>eeper)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v7; // [sp+55h] [bp-23h]@4
  unsigned int dwClientIndex; // [sp+64h] [bp-14h]@4

  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  szMsg = bKeepKeeper;
  pbyType = 25;
  v7 = 27;
  for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
  {
    if ( *(&g_Player.m_bLive + 50856 * (signed int)dwClientIndex) )
      CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &szMsg, 1u);
  }
}
