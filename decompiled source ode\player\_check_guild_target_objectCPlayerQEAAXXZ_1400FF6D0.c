/*
 * Function: ?_check_guild_target_object@CPlayer@@QEAAXXZ
 * Address: 0x1400FF6D0
 */

void __fastcall CPlayer::_check_guild_target_object(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  CGameObject *pObject; // [sp+20h] [bp-18h]@4
  unsigned int v6; // [sp+28h] [bp-10h]@5
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  pObject = 0i64;
  if ( v7->m_Param.m_pGuild )
  {
    v6 = CGuild::GetGuildMasterSerial(v7->m_Param.m_pGuild);
    v3 = CPlayerDB::GetCharSerial(&v7->m_Param);
    if ( v6 == v3 )
    {
      pObject = v7->m_GroupTargetObject[1].pObject;
      if ( pObject )
      {
        if ( pObject->m_bLive )
        {
          if ( v7->m_GroupTargetObject[1].byKind == pObject->m_ObjID.m_byKind
            && v7->m_GroupTargetObject[1].byID == pObject->m_ObjID.m_byID
            && v7->m_GroupTargetObject[1].dwSerial == pObject->m_dwObjSerial )
          {
            CPlayer::pc_RefreshGroupTargetPosition(v7, 1, pObject);
          }
          else
          {
            CPlayer::pc_ReleaseGroupTargetObjectRequest(v7, 1);
          }
        }
        else
        {
          CPlayer::pc_ReleaseGroupTargetObjectRequest(v7, 1);
        }
      }
    }
  }
}
