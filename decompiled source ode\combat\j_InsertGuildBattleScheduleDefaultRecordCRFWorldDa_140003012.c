/*
 * Function: j_?InsertGuildBattleScheduleDefaultRecord@CRFWorldDatabase@@QEAA_NIIEE@Z
 * Address: 0x140003012
 */

bool __fastcall CRFWorldDatabase::InsertGuildBattleScheduleDefaultRecord(CRFWorldDatabase *this, unsigned int uiDayCnt, unsigned int uiMapCnt, char byMaxHour, char byUnitTimeCntPerTime)
{
  return CRFWorldDatabase::InsertGuildBattleScheduleDefaultRecord(
           this,
           uiDayCnt,
           uiMapCnt,
           byMaxHour,
           byUnitTimeCntPerTime);
}
