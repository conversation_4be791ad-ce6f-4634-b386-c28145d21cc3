/*
 * Function: ?Set_StarterBox_Count@CGoldenBoxItemMgr@@QEAAXK_N@Z
 * Address: 0x140413FB0
 */

void __fastcall CGoldenBoxItemMgr::Set_StarterBox_Count(CGoldenBoxItemMgr *this, unsigned int dwNum, bool bAdd)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CGoldenBoxItemMgr *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( bAdd )
  {
    v6->m_golden_box_item.m_dwStarterBoxCnt += dwNum;
  }
  else
  {
    if ( !v6->m_golden_box_item.m_dwStarterBoxCnt )
      return;
    if ( v6->m_golden_box_item.m_dwStarterBoxCnt == dwNum )
      v6->m_golden_box_item.m_dwStarterBoxCnt = 0;
    else
      v6->m_golden_box_item.m_dwStarterBoxCnt -= dwNum;
  }
  CGoldenBoxItemMgr::Set_ToStruct(v6);
}
