/*
 * Function: j_??$_Allocate@U_Node@?$_List_nod@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@std@@@std@@YAPEAU_Node@?$_List_nod@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@0@_KPEAU120@@Z
 * Address: 0x140006B1D
 */

std::_List_nod<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Node *__fastcall std::_Allocate<std::_List_nod<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Node>(unsigned __int64 _Count, std::_List_nod<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Node *__formal)
{
  return std::_Allocate<std::_List_nod<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Node>(
           _Count,
           __formal);
}
