/*
 * Function: ?Action_Attack_OnLoop@DfAIMgr@@SAXPEAVUs_HFSM@@KPEAX@Z
 * Address: 0x1401518E0
 */

void __usercall DfAIMgr::Action_Attack_OnLoop(Us_HFSM *pHFS@<rcx>, unsigned int dwEvent@<edx>, void *lpParam@<r8>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@14
  Us_FSM_Node *v7; // rax@18
  __int64 v8; // [sp+0h] [bp-58h]@1
  Us_HFSM *pHFSa; // [sp+20h] [bp-38h]@5
  CMonster *pMon; // [sp+28h] [bp-30h]@5
  CCharacter *pTarget; // [sp+30h] [bp-28h]@8
  double v12; // [sp+38h] [bp-20h]@14
  unsigned int dwLoopTime; // [sp+40h] [bp-18h]@20
  Us_HFSM *v14; // [sp+60h] [bp+8h]@1

  v14 = pHFS;
  v4 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v14 )
  {
    pHFSa = v14;
    pMon = (CMonster *)Us_HFSM::GetObjectA(v14);
    if ( pMon )
    {
      if ( pMon->m_bLive )
      {
        pTarget = CMonster::GetAttackTarget(pMon);
        if ( !pTarget
          || !pTarget->m_bLive
          || CCharacter::GetStealth(pTarget, 1)
          || pTarget->m_bCorpse
          || pMon->m_pCurMap != pTarget->m_pCurMap )
        {
          Us_HFSM::SendMsg(pHFSa, 1u, 0x1Du, 0i64);
          Us_HFSM::SetLoopTime(pHFSa, 6, 0x1F4u);
        }
        else if ( !DfAIMgr::CheckMonArea_N_ChangeState((CMonsterAI *)pHFSa, pMon, 1) )
        {
          Get3DSqrt(pMon->m_fCurPos, pTarget->m_fCurPos);
          v12 = a4;
          v6 = MonsterSetInfoData::GetLostMonsterTargetDistance(&g_MonsterSetInfoData);
          if ( v12 <= (float)v6 || CMonsterAggroMgr::GetTopAggroCharacter(&pMon->m_AggroMgr) )
          {
            if ( CMonster::GetHelpMeCase(pMon) )
            {
              v7 = Us_HFSM::GetNode(v14, 5u);
              if ( Us_FSM_Node::GetState(v7) == 23 )
                Us_HFSM::SendMsg(pHFSa, 2u, 0x1Eu, 0i64);
            }
            dwLoopTime = GetLoopTime();
            if ( DfAIMgr::CheckSPFDelayTime((CMonsterAI *)pHFSa, 1, dwLoopTime)
              && DfAIMgr::CheckSPF((CMonsterAI *)pHFSa, pMon) )
            {
              Us_HFSM::SendMsg(v14, 7u, 0x21u, 0i64);
              Us_HFSM::SetLoopTime(
                pHFSa,
                6,
                (signed int)ffloor(*(float *)&pMon->m_pRecordSet[25].m_strCode[20] + 500.0));
            }
            else if ( DfAIMgr::CheckSPFDelayTime((CMonsterAI *)pHFSa, 0, dwLoopTime)
                   && DfAIMgr::CheckGen((CMonsterAI *)pHFSa, pMon) )
            {
              Us_HFSM::SendMsg(v14, 7u, 0x21u, 0i64);
              Us_HFSM::SetLoopTime(
                pHFSa,
                6,
                (signed int)ffloor(*(float *)&pMon->m_pRecordSet[25].m_strCode[20] + 500.0));
            }
            else
            {
              if ( DfAIMgr::SearchCharacterPath((CMonsterAI *)pHFSa, pMon, pTarget) )
                HIDWORD(pHFSa[1].m_ArNode[9].m_pParent) = 0;
              else
                ++HIDWORD(pHFSa[1].m_ArNode[9].m_pParent);
              Us_HFSM::SetLoopTime(pHFSa, 6, 0x1F4u);
            }
          }
          else
          {
            Us_HFSM::SendMsg(pHFSa, 1u, 0x1Du, 0i64);
            Us_HFSM::SetLoopTime(pHFSa, 6, 0x1F4u);
          }
        }
      }
    }
  }
}
