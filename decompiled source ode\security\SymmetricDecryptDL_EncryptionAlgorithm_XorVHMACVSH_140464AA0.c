/*
 * Function: ?SymmetricDecrypt@?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@CryptoPP@@UEBA?AUDecodingResult@2@PEBE0_KPEAEAEBVNameValuePairs@2@@Z
 * Address: 0x140464AA0
 */

CryptoPP::DecodingResult *__fastcall CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>::SymmetricDecrypt(CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *this, CryptoPP::DecodingResult *result, const char *key, const char *ciphertext, unsigned __int64 ciphertextLength, char *plaintext, CryptoPP::NameValuePairs *parameters)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v9; // rax@4
  const char *v10; // rax@4
  const unsigned __int8 *v11; // rax@4
  CryptoPP::DecodingResult *v12; // rax@5
  __int64 v13; // [sp+0h] [bp-1F8h]@1
  char *xorBlock; // [sp+20h] [bp-1D8h]@6
  unsigned __int64 len; // [sp+30h] [bp-1C8h]@4
  unsigned __int8 *v16; // [sp+38h] [bp-1C0h]@4
  char *keya; // [sp+40h] [bp-1B8h]@4
  CryptoPP::ConstByteArrayParameter value; // [sp+58h] [bp-1A0h]@4
  CryptoPP::HMAC<CryptoPP::SHA1> v19; // [sp+A8h] [bp-150h]@4
  __int64 v20; // [sp+1D0h] [bp-28h]@4
  unsigned __int64 v21; // [sp+1D8h] [bp-20h]@4
  unsigned __int64 v22; // [sp+1E0h] [bp-18h]@4
  CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *v23; // [sp+200h] [bp+8h]@1
  CryptoPP::DecodingResult *v24; // [sp+208h] [bp+10h]@1
  unsigned __int8 *v25; // [sp+210h] [bp+18h]@1
  unsigned __int8 *v26; // [sp+218h] [bp+20h]@1

  v26 = (unsigned __int8 *)ciphertext;
  v25 = (unsigned __int8 *)key;
  v24 = result;
  v23 = this;
  v7 = &v13;
  for ( i = 124i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v20 = -2i64;
  v22 = (unsigned __int64)&v13 ^ _security_cookie;
  LODWORD(v9) = ((int (__fastcall *)(CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *, unsigned __int64))v23->vfptr->GetMaxSymmetricPlaintextLength)(
                  v23,
                  ciphertextLength);
  len = v9;
  v16 = v25;
  keya = (char *)&v25[v9];
  CryptoPP::ConstByteArrayParameter::ConstByteArrayParameter(&value, 0i64, 0);
  v10 = CryptoPP::Name::EncodingParameters();
  CryptoPP::NameValuePairs::GetValue<CryptoPP::ConstByteArrayParameter>(parameters, v10, &value);
  CryptoPP::HMAC<CryptoPP::SHA1>::HMAC<CryptoPP::SHA1>(&v19, keya, 0x10ui64);
  CryptoPP::HMAC_Base::Update((CryptoPP::HMAC_Base *)&v19.vfptr, v26, len);
  v21 = CryptoPP::ConstByteArrayParameter::size(&value);
  v11 = (const unsigned __int8 *)CryptoPP::ConstByteArrayParameter::begin(&value);
  CryptoPP::HMAC_Base::Update((CryptoPP::HMAC_Base *)&v19.vfptr, v11, v21);
  if ( CryptoPP::HashTransformation::Verify((CryptoPP::HashTransformation *)&v19.vfptr, (const char *)&v26[len]) )
  {
    CryptoPP::xorbuf((CryptoPP *)plaintext, v26, v16, (const unsigned __int8 *)len, (unsigned __int64)xorBlock);
    CryptoPP::DecodingResult::DecodingResult(v24, len);
    CryptoPP::HMAC<CryptoPP::SHA1>::~HMAC<CryptoPP::SHA1>(&v19);
    CryptoPP::ConstByteArrayParameter::~ConstByteArrayParameter(&value);
    v12 = v24;
  }
  else
  {
    CryptoPP::DecodingResult::DecodingResult(v24);
    CryptoPP::HMAC<CryptoPP::SHA1>::~HMAC<CryptoPP::SHA1>(&v19);
    CryptoPP::ConstByteArrayParameter::~ConstByteArrayParameter(&value);
    v12 = v24;
  }
  return v12;
}
