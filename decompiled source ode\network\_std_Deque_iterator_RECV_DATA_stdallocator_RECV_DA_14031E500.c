/*
 * Function: _std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0_::operator__::_1_::dtor$1
 * Address: 0x14031E500
 */

void __fastcall std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0_::operator__::_1_::dtor_1(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 84) & 1 )
  {
    *(_DWORD *)(a2 + 84) &= 0xFFFFFFFE;
    std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(*(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> **)(a2 + 120));
  }
}
