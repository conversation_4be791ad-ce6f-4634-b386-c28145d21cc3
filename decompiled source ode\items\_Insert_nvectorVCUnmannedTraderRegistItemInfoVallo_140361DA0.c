/*
 * Function: ?_Insert_n@?$vector@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@IEAAXV?$_Vector_iterator@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@2@_KAEBVCUnmannedTraderRegistItemInfo@@@Z
 * Address: 0x140361DA0
 */

void __fastcall std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Insert_n(std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *this, std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *_Where, unsigned __int64 _Count, CUnmannedTraderRegistItemInfo *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v6; // rax@5
  unsigned __int64 v7; // rax@7
  unsigned __int64 v8; // rax@8
  unsigned __int64 v9; // rax@11
  __int64 v10; // [sp+0h] [bp-188h]@1
  CUnmannedTraderRegistItemInfo _Vala; // [sp+30h] [bp-158h]@4
  unsigned __int64 _Counta; // [sp+A8h] [bp-E0h]@4
  CUnmannedTraderRegistItemInfo *_Ptr; // [sp+B0h] [bp-D8h]@13
  CUnmannedTraderRegistItemInfo *v14; // [sp+B8h] [bp-D0h]@13
  CUnmannedTraderRegistItemInfo *_Last; // [sp+C0h] [bp-C8h]@18
  char v16; // [sp+D0h] [bp-B8h]@4
  __int64 v17; // [sp+138h] [bp-50h]@4
  unsigned __int64 v18; // [sp+140h] [bp-48h]@5
  unsigned __int64 v19; // [sp+148h] [bp-40h]@8
  unsigned __int64 v20; // [sp+150h] [bp-38h]@9
  CUnmannedTraderRegistItemInfo *v21; // [sp+158h] [bp-30h]@13
  CUnmannedTraderRegistItemInfo *v22; // [sp+160h] [bp-28h]@13
  unsigned __int64 v23; // [sp+168h] [bp-20h]@4
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v24; // [sp+190h] [bp+8h]@1
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v25; // [sp+198h] [bp+10h]@1
  unsigned __int64 v26; // [sp+1A0h] [bp+18h]@1
  unsigned __int64 v27; // [sp+1A0h] [bp+18h]@13

  v26 = _Count;
  v25 = _Where;
  v24 = this;
  v4 = &v10;
  for ( i = 94i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v17 = -2i64;
  v23 = (unsigned __int64)&v10 ^ _security_cookie;
  qmemcpy(&v16, _Val, 0x68ui64);
  qmemcpy(&_Vala, &v16, sizeof(_Vala));
  _Counta = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::capacity(v24);
  if ( v26 )
  {
    v18 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(v24);
    v6 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::max_size(v24);
    if ( v6 - v18 < v26 )
      std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Xlen();
    v7 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(v24);
    if ( _Counta >= v26 + v7 )
    {
      if ( (unsigned int)((char *)v24->_Mylast - (char *)v25->_Myptr) / 104i64 >= v26 )
      {
        _Last = v24->_Mylast;
        v24->_Mylast = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Umove<CUnmannedTraderRegistItemInfo *>(
                         v24,
                         &_Last[-v26],
                         _Last,
                         v24->_Mylast);
        stdext::_Unchecked_move_backward<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *>(
          v25->_Myptr,
          &_Last[-v26],
          _Last);
        std::fill<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo>(v25->_Myptr, &v25->_Myptr[v26], &_Vala);
      }
      else
      {
        std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Umove<CUnmannedTraderRegistItemInfo *>(
          v24,
          v25->_Myptr,
          v24->_Mylast,
          &v25->_Myptr[v26]);
        std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Ufill(
          v24,
          v24->_Mylast,
          v26 - (unsigned int)((char *)v24->_Mylast - (char *)v25->_Myptr) / 104i64,
          &_Vala);
        v24->_Mylast += v26;
        std::fill<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo>(
          v25->_Myptr,
          &v24->_Mylast[-v26],
          &_Vala);
      }
    }
    else
    {
      v19 = _Counta / 2;
      v8 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::max_size(v24);
      if ( v8 - v19 >= _Counta )
        v20 = _Counta / 2 + _Counta;
      else
        v20 = 0i64;
      _Counta = v20;
      v9 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(v24);
      if ( _Counta < v26 + v9 )
        _Counta = v26
                + std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(v24);
      _Ptr = std::allocator<CUnmannedTraderRegistItemInfo>::allocate(&v24->_Alval, _Counta);
      v14 = _Ptr;
      v21 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Umove<CUnmannedTraderRegistItemInfo *>(
              v24,
              v24->_Myfirst,
              v25->_Myptr,
              _Ptr);
      v14 = v21;
      v22 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Ufill(
              v24,
              v21,
              v26,
              &_Vala);
      v14 = v22;
      std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Umove<CUnmannedTraderRegistItemInfo *>(
        v24,
        v25->_Myptr,
        v24->_Mylast,
        v22);
      v27 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(v24) + v26;
      if ( v24->_Myfirst )
      {
        std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Destroy(
          v24,
          v24->_Myfirst,
          v24->_Mylast);
        std::allocator<CUnmannedTraderRegistItemInfo>::deallocate(
          &v24->_Alval,
          v24->_Myfirst,
          (unsigned int)((char *)v24->_Myend - (char *)v24->_Myfirst) / 104i64);
      }
      v24->_Myend = &_Ptr[_Counta];
      v24->_Mylast = &_Ptr[v27];
      v24->_Myfirst = _Ptr;
    }
  }
  CUnmannedTraderRegistItemInfo::~CUnmannedTraderRegistItemInfo(&_Vala);
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(v25);
}
