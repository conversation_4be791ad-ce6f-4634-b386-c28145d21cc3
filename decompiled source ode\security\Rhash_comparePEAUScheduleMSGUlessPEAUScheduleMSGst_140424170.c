/*
 * Function: ??R?$hash_compare@PEAUScheduleMSG@@U?$less@PEAUScheduleMSG@@@std@@@stdext@@QEBA_KAEBQEAUScheduleMSG@@@Z
 * Address: 0x140424170
 */

unsigned __int64 __fastcall stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>::operator()(stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> > *this, ScheduleMSG *const *_Keyval)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int32 v4; // eax@4
  __int64 v6; // [sp+0h] [bp-48h]@1
  ldiv_t v7; // [sp+28h] [bp-20h]@4
  ldiv_t v8; // [sp+34h] [bp-14h]@4

  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = stdext::hash_value<ScheduleMSG *>(_Keyval);
  v8 = ldiv(v4, 127773);
  v7 = v8;
  v7.rem = 16807 * v8.rem - 2836 * v8.quot;
  if ( v7.rem < 0 )
    v7.rem += 0x7FFFFFFF;
  return v7.rem;
}
