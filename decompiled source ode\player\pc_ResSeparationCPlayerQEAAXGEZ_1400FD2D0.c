/*
 * Function: ?pc_ResSeparation@CPlayer@@QEAAXGE@Z
 * Address: 0x1400FD2D0
 */

void __fastcall CPlayer::pc_ResSeparation(CPlayer *this, unsigned __int16 wStartSerial, char byMoveAmount)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CPlayer::SendMsg_ResSeparation(v6, 1, 0i64, 0i64);
}
