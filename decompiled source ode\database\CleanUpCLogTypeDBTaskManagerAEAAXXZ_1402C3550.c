/*
 * Function: ?CleanUp@CLogTypeDBTaskManager@@AEAAXXZ
 * Address: 0x1402C3550
 */

void __fastcall CLogTypeDBTaskManager::CleanUp(CLogTypeDBTaskManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@6
  __int64 v4; // [sp+0h] [bp-58h]@1
  CRFWorldDatabase *v5; // [sp+20h] [bp-38h]@5
  CRFWorldDatabase *v6; // [sp+28h] [bp-30h]@5
  CLogFile *v7; // [sp+30h] [bp-28h]@10
  CLogFile *v8; // [sp+38h] [bp-20h]@10
  __int64 v9; // [sp+40h] [bp-18h]@6
  void *v10; // [sp+48h] [bp-10h]@11
  CLogTypeDBTaskManager *v11; // [sp+60h] [bp+8h]@1

  v11 = this;
  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v11->m_pkWorldDB )
  {
    v6 = v11->m_pkWorldDB;
    v5 = v6;
    if ( v6 )
    {
      LODWORD(v3) = ((int (__fastcall *)(CRFWorldDatabase *, signed __int64))v5->vfptr->__vecDelDtor)(v5, 1i64);
      v9 = v3;
    }
    else
    {
      v9 = 0i64;
    }
    v11->m_pkWorldDB = 0i64;
  }
  if ( v11->m_pkLogger )
  {
    v8 = v11->m_pkLogger;
    v7 = v8;
    if ( v8 )
      v10 = CLogFile::`scalar deleting destructor'(v7, 1u);
    else
      v10 = 0i64;
    v11->m_pkLogger = 0i64;
  }
  v11->m_bDBProc = 0;
}
