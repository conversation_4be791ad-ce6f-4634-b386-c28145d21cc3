/*
 * Function: ?Loop@CGuildBattleScheduleManager@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403DCD30
 */

void __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::Loop(GUILD_BATTLE::CGuildBattleScheduleManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleScheduleManager *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v4->m_bLoad && (!v4->m_pkTimer || CMyTimer::CountingTimer(v4->m_pkTimer)) )
  {
    GUILD_BATTLE::CGuildBattleScheduleManager::UpdateDayChangedWork(v4);
    if ( !GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::IsDone(v4->m_pkTodaySchedule)
      && GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Loop(v4->m_pkTodaySchedule) )
    {
      GUILD_BATTLE::CGuildBattleScheduleManager::SetNextEvnet(v4);
    }
  }
}
