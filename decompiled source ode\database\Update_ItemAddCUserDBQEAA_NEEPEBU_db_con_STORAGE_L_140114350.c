/*
 * Function: ?Update_ItemAdd@CUserDB@@QEAA_NEEPEBU_db_con@_STORAGE_LIST@@_N@Z
 * Address: 0x140114350
 */

char __fastcall CUserDB::Update_ItemAdd(CUserDB *this, char storage, char slot, _STORAGE_LIST::_db_con *pItem, bool bUpdate)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v8; // ecx@31
  __int64 v9; // [sp+0h] [bp-B8h]@1
  int v10; // [sp+20h] [bp-98h]@5
  int v11; // [sp+28h] [bp-90h]@31
  int v12; // [sp+30h] [bp-88h]@31
  int v13; // [sp+38h] [bp-80h]@31
  int v14; // [sp+40h] [bp-78h]@31
  int v15; // [sp+48h] [bp-70h]@31
  char v16; // [sp+50h] [bp-68h]@6
  char v17; // [sp+51h] [bp-67h]@6
  unsigned __int16 v18; // [sp+54h] [bp-64h]@6
  _INVEN_DB_BASE::_LIST *v19; // [sp+58h] [bp-60h]@7
  _EQUIP_DB_BASE::_EMBELLISH_LIST *v20; // [sp+60h] [bp-58h]@13
  _FORCE_DB_BASE::_LIST *v21; // [sp+68h] [bp-50h]@16
  _ANIMUS_DB_BASE::_LIST *v22; // [sp+70h] [bp-48h]@19
  _TRUNK_DB_BASE::_LIST *v23; // [sp+78h] [bp-40h]@22
  _PERSONALAMINE_INVEN_DB_BASE::_LIST *v24; // [sp+80h] [bp-38h]@25
  _TRUNK_DB_BASE::_LIST *v25; // [sp+88h] [bp-30h]@28
  int v26; // [sp+90h] [bp-28h]@6
  CUserDB *v27; // [sp+C0h] [bp+8h]@1
  char v28; // [sp+C8h] [bp+10h]@1
  char v29; // [sp+D0h] [bp+18h]@1
  _STORAGE_LIST::_db_con *pItema; // [sp+D8h] [bp+20h]@1

  pItema = pItem;
  v29 = slot;
  v28 = storage;
  v27 = this;
  v5 = &v9;
  for ( i = 42i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( IsStorageRange(storage, slot) )
  {
    v16 = -1;
    v17 = -1;
    v18 = -1;
    v26 = (unsigned __int8)v28;
    switch ( v28 )
    {
      case 0:
        v19 = &v27->m_AvatorData.dbInven.m_List[(unsigned __int8)v29];
        if ( !_INVEN_DB_BASE::_LIST::Set(v19, pItema) )
        {
          v16 = v19->Key.bySlotIndex;
          v17 = v19->Key.byTableCode;
          v18 = v19->Key.wItemIndex;
        }
        break;
      case 1:
        if ( !_REGED::Set((_REGED *)v27->m_AvatorData.dbAvator.m_wszAvatorName, v29, pItema) )
        {
          v17 = v29;
          v18 = v27->m_AvatorData.dbAvator.m_EquipKey[(unsigned __int8)v29].zItemIndex;
        }
        break;
      case 2:
        v20 = &v27->m_AvatorData.dbEquip.m_EmbellishList[(unsigned __int8)v29];
        if ( !_EQUIP_DB_BASE::_EMBELLISH_LIST::Set(v20, pItema) )
        {
          v16 = v20->Key.bySlotIndex;
          v17 = v20->Key.byTableCode;
          v18 = v20->Key.wItemIndex;
        }
        break;
      case 3:
        v21 = &v27->m_AvatorData.dbForce.m_List[(unsigned __int8)v29];
        if ( !_FORCE_DB_BASE::_LIST::Set(v21, pItema) )
        {
          v17 = 15;
          v18 = (unsigned __int8)_FORCEKEY::GetIndex(&v21->Key);
        }
        break;
      case 4:
        v22 = &v27->m_AvatorData.dbAnimus.m_List[(unsigned __int8)v29];
        if ( !_ANIMUS_DB_BASE::_LIST::Set(v22, pItema) )
        {
          v17 = 24;
          v18 = v22->Key.byItemIndex;
        }
        break;
      case 5:
        v23 = &v27->m_AvatorData.dbTrunk.m_List[(unsigned __int8)v29];
        if ( !_TRUNK_DB_BASE::_LIST::Set(v23, pItema, v27->m_AvatorData.dbAvator.m_byRaceSexCode >> 1) )
        {
          v16 = v23->Key.bySlotIndex;
          v17 = v23->Key.byTableCode;
          v18 = v23->Key.wItemIndex;
        }
        break;
      case 6:
        v24 = &v27->m_AvatorData.dbPersonalAmineInven.m_List[(unsigned __int8)v29];
        if ( !_PERSONALAMINE_INVEN_DB_BASE::_LIST::Set(v24, pItema) )
        {
          v16 = v24->Key.bySlotIndex;
          v17 = v24->Key.byTableCode;
          v18 = v24->Key.wItemIndex;
        }
        break;
      case 7:
        v25 = &v27->m_AvatorData.dbTrunk.m_ExtList[(unsigned __int8)v29];
        if ( !_TRUNK_DB_BASE::_LIST::Set(v25, pItema, v27->m_AvatorData.dbAvator.m_byRaceSexCode >> 1) )
        {
          v16 = v25->Key.bySlotIndex;
          v17 = v25->Key.byTableCode;
          v18 = v25->Key.wItemIndex;
        }
        break;
      default:
        break;
    }
    if ( (unsigned __int8)v17 == 255 )
    {
      v27->m_bDataUpdate = 1;
      result = 1;
    }
    else
    {
      v8 = pItema->m_byTableCode;
      v15 = pItema->m_wItemIndex;
      v14 = v8;
      v13 = v18;
      v12 = (unsigned __int8)v17;
      v11 = (unsigned __int8)v16;
      v10 = (unsigned __int8)v29;
      CLogFile::Write(
        &stru_1799C8E78,
        "%s:Update_Add(%s, Idx:%d) CUR..(item:%d,%d,%d) ADD..(item:%d,%d)",
        v27->m_aszAvatorName,
        *(_QWORD *)&s_szStorage[8 * (unsigned __int8)v28]);
      result = 0;
    }
  }
  else
  {
    v10 = (unsigned __int8)v29;
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_ItemAdd(CODE) : storage : %d, slot : %d  ",
      v27->m_aszAvatorName,
      (unsigned __int8)v28);
    result = 0;
  }
  return result;
}
