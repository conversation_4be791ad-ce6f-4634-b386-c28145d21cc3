/*
 * Function: ?_CalcMonSkillAttPnt@CMonsterAttack@@IEAAHXZ
 * Address: 0x14015A6A0
 */

__int64 __fastcall CMonsterAttack::_CalcMonSkillAttPnt(CMonsterAttack *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  _attack_param *v3; // rcx@5
  _attack_param *v4; // rdx@5
  __int64 result; // rax@7
  float v6; // xmm0_4@10
  float v7; // xmm1_4@10
  float v8; // xmm0_4@10
  __int64 v9; // [sp+0h] [bp-A8h]@1
  double v10; // [sp+20h] [bp-88h]@5
  int v11; // [sp+28h] [bp-80h]@5
  int v12; // [sp+30h] [bp-78h]@5
  int v13; // [sp+38h] [bp-70h]@5
  int v14; // [sp+40h] [bp-68h]@5
  _base_fld *v15; // [sp+50h] [bp-58h]@4
  float v16; // [sp+58h] [bp-50h]@4
  float v17; // [sp+5Ch] [bp-4Ch]@4
  int v18; // [sp+60h] [bp-48h]@4
  int v19; // [sp+64h] [bp-44h]@4
  int v20; // [sp+68h] [bp-40h]@4
  int v21; // [sp+6Ch] [bp-3Ch]@6
  int v22; // [sp+70h] [bp-38h]@10
  int v23; // [sp+74h] [bp-34h]@10
  int v24; // [sp+88h] [bp-20h]@10
  int v25; // [sp+8Ch] [bp-1Ch]@15
  float v26; // [sp+94h] [bp-14h]@10
  CMonsterAttack *v27; // [sp+B0h] [bp+8h]@1

  v27 = this;
  v1 = &v9;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v15 = v27->m_pp->pFld;
  v16 = (float)v27->m_pp->nLevel + (float)((float)(7.0 - (float)v27->m_pp->nLevel) * 0.5);
  v17 = *(float *)&v15[11].m_strCode[56];
  v18 = *(_DWORD *)&v15[11].m_strCode[4 * (v27->m_pp->nLevel - 1) + 28];
  v19 = (signed int)floor((float)((float)((float)v18 / 788.0)
                                * (float)((float)((float)v27->m_pp->nMinAF
                                                * (float)((float)(fR + (float)((float)(v16 / 7.0) * fRLf))
                                                        + (float)((float)((float)v27->m_pp->nMastery / 99.0) * fRMf)))
                                        * v17)) + 0.5);
  v20 = (signed int)floor((float)((float)((float)v18 / 788.0)
                                * (float)((float)((float)v27->m_pp->nMaxAF
                                                * (float)((float)(fR + (float)((float)(v16 / 7.0) * fRLf))
                                                        + (float)((float)((float)v27->m_pp->nMastery / 99.0) * fRMf)))
                                        * v17)) + 0.5);
  if ( v20 < 0 )
  {
    v3 = v27->m_pp;
    v4 = v27->m_pp;
    v14 = v27->m_pp->nMinAF;
    v13 = v3->nMaxAF;
    v12 = v4->nMastery;
    v11 = v18;
    v10 = v17;
    CLogFile::Write(
      &stru_1799C8E78,
      "Skill Attack Error : Skill(%s), SIndex(%d), l_fConst(%f), l_nLvConst(%d), nMastery(%d), nMaxAF(%d), nMinAF(%d)",
      v15->m_strCode,
      v15->m_dwIndex);
    v19 = 1;
    v20 = 1;
  }
  v21 = (signed int)floor((double)((v20 + 125) / (v20 + 50) * v20) + 0.5);
  if ( v27->m_pp->nMaxAttackPnt <= 0 )
  {
    if ( v27->m_pp->nMaxAttackPnt >= 0 )
    {
      v22 = (signed int)floor((float)((float)(v20 + v19) / 2.0) + 0.5);
      v23 = _100_per_random_table::GetRand(&v27->m_pAttChar->m_rtPer100);
      v26 = (float)v27->m_pp->nMinSel;
      v6 = v26;
      _effect_parameter::GetEff_Plus(&v27->m_pAttChar->m_EP, 14);
      v7 = v26 - v6;
      v8 = v7;
      v24 = (signed int)ffloor(v7);
      if ( v27->m_pp->pDst && v27->m_pp->pDst != v27->m_pAttChar )
      {
        _effect_parameter::GetEff_Plus(&v27->m_pp->pDst->m_EP, 37);
        v8 = (float)v24 + v7;
        v24 = (signed int)ffloor(v8);
      }
      if ( v24 < 0 )
        v24 = 0;
      v25 = v27->m_pp->nMaxSel + v27->m_pp->nMinSel;
      if ( v27->m_pp->pDst && v27->m_pp->pDst != v27->m_pAttChar )
      {
        _effect_parameter::GetEff_Plus(&v27->m_pp->pDst->m_EP, 37);
        v25 = (signed int)ffloor((float)v25 + v8);
      }
      if ( v25 < 0 )
        v25 = 0;
      if ( v23 >= v24 )
      {
        if ( v23 >= v25 )
        {
          v27->m_bIsCrtAtt = 1;
          result = (unsigned int)v21;
        }
        else if ( v20 - v22 <= 0 )
        {
          result = (unsigned int)v22;
        }
        else
        {
          result = (unsigned int)(rand() % (v20 - v22) + v22);
        }
      }
      else if ( v22 - v19 <= 0 )
      {
        result = (unsigned int)v19;
      }
      else
      {
        result = (unsigned int)(rand() % (v22 - v19) + v19);
      }
    }
    else
    {
      result = (unsigned int)v19;
    }
  }
  else
  {
    result = (unsigned int)v21;
  }
  return result;
}
