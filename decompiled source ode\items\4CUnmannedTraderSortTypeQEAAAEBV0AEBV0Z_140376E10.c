/*
 * Function: ??4CUnmannedTraderSortType@@QEAAAEBV0@AEBV0@@Z
 * Address: 0x140376E10
 */

CUnmannedTraderSortType *__fastcall CUnmannedTraderSortType::operator=(CUnmannedTraderSortType *this, CUnmannedTraderSortType *lhs)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderSortType *v6; // [sp+30h] [bp+8h]@1
  CUnmannedTraderSortType *v7; // [sp+38h] [bp+10h]@1

  v7 = lhs;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6->m_dwID = lhs->m_dwID;
  strcpy_0(v6->m_szTypeName, lhs->m_szTypeName);
  strcpy_0(v6->m_szQuery, v7->m_szQuery);
  return v6;
}
