/*
 * Function: ?Update_UnitDelete@CUserDB@@QEAA_NE@Z
 * Address: 0x140115360
 */

char __fastcall CUserDB::Update_UnitDelete(CUserDB *this, char bySlotIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUserDB *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( (signed int)(unsigned __int8)bySlotIndex < 4 )
  {
    if ( v6->m_AvatorData.dbUnit.m_List[(unsigned __int8)bySlotIndex].byFrame == 255 )
    {
      CLogFile::Write(
        &stru_1799C8E78,
        "%s : Update_UnitDelete(EXIST) : slot : %d",
        v6->m_aszAvatorName,
        (unsigned __int8)bySlotIndex);
      result = 0;
    }
    else
    {
      _UNIT_DB_BASE::_LIST::DelUnit((_UNIT_DB_BASE::_LIST *)&v6->m_AvatorData.dbUnit + (unsigned __int8)bySlotIndex);
      v6->m_bDataUpdate = 1;
      result = 1;
    }
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_UnitDelete(SlotIndex OVER) : slot : %d",
      v6->m_aszAvatorName,
      (unsigned __int8)bySlotIndex);
    result = 0;
  }
  return result;
}
