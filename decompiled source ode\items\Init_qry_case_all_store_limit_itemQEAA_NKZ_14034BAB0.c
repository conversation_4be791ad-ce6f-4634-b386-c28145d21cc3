/*
 * Function: ?Init@_qry_case_all_store_limit_item@@QEAA_NK@Z
 * Address: 0x14034BAB0
 */

char __fastcall _qry_case_all_store_limit_item::Init(_qry_case_all_store_limit_item *this, unsigned int dwStoreNum)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-58h]@1
  void *v6; // [sp+20h] [bp-38h]@7
  int __n[2]; // [sp+28h] [bp-30h]@8
  void *__t; // [sp+38h] [bp-20h]@8
  __int64 v9; // [sp+40h] [bp-18h]@4
  _qry_case_all_store_limit_item::__list *v10; // [sp+48h] [bp-10h]@9
  _qry_case_all_store_limit_item *v11; // [sp+60h] [bp+8h]@1
  unsigned int v12; // [sp+68h] [bp+10h]@1

  v12 = dwStoreNum;
  v11 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = -2i64;
  if ( dwStoreNum )
  {
    if ( v11->pStoreList )
    {
      v6 = v11->pStoreList;
      operator delete[](v6);
    }
    *(_QWORD *)__n = v12;
    __t = operator new[](saturated_mul(0xA0ui64, v12));
    if ( __t )
    {
      `vector constructor iterator'(
        __t,
        0xA0ui64,
        __n[0],
        (void *(__cdecl *)(void *))_qry_case_all_store_limit_item::__list::__list);
      v10 = (_qry_case_all_store_limit_item::__list *)__t;
    }
    else
    {
      v10 = 0i64;
    }
    v11->pStoreList = v10;
    v11->dwMax = v12;
    v11->dwCount = 0;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
