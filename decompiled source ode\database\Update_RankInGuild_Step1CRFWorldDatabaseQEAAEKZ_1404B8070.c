/*
 * Function: ?Update_RankInGuild_Step1@CRFWorldDatabase@@QEAAEK@Z
 * Address: 0x1404B8070
 */

char __fastcall CRFWorldDatabase::Update_RankInGuild_Step1(CRFWorldDatabase *this, unsigned int dwGuildSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v5; // [sp+0h] [bp-468h]@1
  __int16 v6; // [sp+20h] [bp-448h]@7
  char Dest; // [sp+40h] [bp-428h]@4
  char v8; // [sp+41h] [bp-427h]@4
  unsigned __int64 v9; // [sp+450h] [bp-18h]@4
  CRFWorldDatabase *v10; // [sp+470h] [bp+8h]@1
  unsigned int v11; // [sp+478h] [bp+10h]@1

  v11 = dwGuildSerial;
  v10 = this;
  v2 = &v5;
  for ( i = 280i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  Dest = 0;
  memset(&v8, 0, 0x3FFui64);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v10->vfptr,
    "CRFWorldDatabase::Update_RankInGuild_Step1( DWORD dwGuildSerial(%u) ) Start",
    dwGuildSerial);
  if ( v10->m_hStmtUpdate || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v10->vfptr) )
  {
    sprintf(
      &Dest,
      "select top %u IDENTITY(int, 1, 1) AS Rank, -1 as Rate, -1 as NewGrade, b.serial, b.lv, g.Pvppoint, g.GuildGrade as"
      " CurGrade into #tbl_RankInGuild from tbl_general as g join tbl_base as b on g.serial = b.serial where g.guildseria"
      "l=%d and b.dck=0 order by g.Pvppoint desc",
      50i64,
      v11);
    v6 = SQLExecDirect_0(v10->m_hStmtUpdate, &Dest, -3);
    if ( v6 && v6 != 1 )
    {
      if ( v6 == 100 )
      {
        CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, "drop table #tbl_RankInGuild", 0);
        CRFNewDatabase::FmtLog(
          (CRFNewDatabase *)&v10->vfptr,
          "CRFWorldDatabase::Update_RankInGuild_Step1( DWORD dwGuildSerial(%u) ) No Data!",
          v11);
        result = 2;
      }
      else
      {
        CRFNewDatabase::FmtLog(
          (CRFNewDatabase *)&v10->vfptr,
          "CRFWorldDatabase::Update_RankInGuild_Step1( DWORD dwGuildSerial(%u) ) Sql Error!",
          v11);
        result = 1;
      }
    }
    else
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v10->vfptr,
        "CRFWorldDatabase::Update_RankInGuild_Step1( DWORD dwGuildSerial(%u) ) End",
        v11);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v10->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
