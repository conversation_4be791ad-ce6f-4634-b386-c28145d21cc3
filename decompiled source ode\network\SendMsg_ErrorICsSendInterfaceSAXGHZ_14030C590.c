/*
 * Function: ?SendMsg_Error@ICsSendInterface@@SAXGH@Z
 * Address: 0x14030C590
 */

void __fastcall ICsSendInterface::SendMsg_Error(unsigned __int16 wSock, int eCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@4
  __int64 v5; // [sp+0h] [bp-78h]@1
  _result_csi_error_zocl v6; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  unsigned __int16 v9; // [sp+80h] [bp+8h]@1

  v9 = wSock;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6.byRetCode = eCode;
  pbyType = 57;
  v8 = 0;
  v4 = _result_csi_error_zocl::size(&v6);
  CNetProcess::LoadSendMsg(unk_1414F2088, v9, &pbyType, &v6.byRetCode, v4);
}
