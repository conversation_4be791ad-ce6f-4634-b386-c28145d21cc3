/*
 * Function: j_??0?$_Ranit@PEAVCMoveMapLimitInfo@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@AEBU01@@Z
 * Address: 0x1400035E9
 */

void __fastcall std::_Ranit<CMoveMapLimitInfo *,__int64,CMoveMapLimitInfo * const *,CMoveMapLimitInfo * const &>::_Ranit<CMoveMapLimitInfo *,__int64,CMoveMapLimitInfo * const *,CMoveMapLimitInfo * const &>(std::_Ranit<CMoveMapLimitInfo *,__int64,CMoveMapLimitInfo * const *,CMoveMapLimitInfo * const &> *this, std::_Ranit<CMoveMapLimitInfo *,__int64,CMoveMapLimitInfo * const *,CMoveMapLimitInfo * const &> *__that)
{
  std::_Ranit<CMoveMapLimitInfo *,__int64,CMoveMapLimitInfo * const *,CMoveMapLimitInfo * const &>::_Ranit<CMoveMapLimitInfo *,__int64,CMoveMapLimitInfo * const *,CMoveMapLimitInfo * const &>(
    this,
    __that);
}
