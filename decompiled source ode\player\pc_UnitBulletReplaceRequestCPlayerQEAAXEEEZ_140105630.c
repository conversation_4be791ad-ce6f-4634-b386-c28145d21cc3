/*
 * Function: ?pc_UnitBulletReplaceRequest@CPlayer@@QEAAXEEE@Z
 * Address: 0x140105630
 */

void __fastcall CPlayer::pc_UnitBulletReplaceRequest(CPlayer *this, char bySlotIndex, char byPackIndex, char byBulletPart)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  char v7; // [sp+20h] [bp-28h]@4
  _UNIT_DB_BASE::_LIST *pData; // [sp+28h] [bp-20h]@4
  void *Dst; // [sp+30h] [bp-18h]@4
  _unit_bullet_param *v10; // [sp+38h] [bp-10h]@4
  CPlayer *v11; // [sp+50h] [bp+8h]@1
  char v12; // [sp+58h] [bp+10h]@1

  v12 = bySlotIndex;
  v11 = this;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7 = 0;
  pData = &v11->m_Param.m_UnitDB.m_List[(unsigned __int8)bySlotIndex];
  Dst = &pData->dwBullet[(unsigned __int8)byBulletPart];
  v10 = (_unit_bullet_param *)&pData->dwSpare[(unsigned __int8)byPackIndex];
  if ( v11->m_pUserDB )
  {
    if ( pData->byFrame == 255 )
    {
      v7 = 5;
    }
    else if ( !_unit_bullet_param::IsFill(v10) )
    {
      v7 = 25;
    }
    if ( !v7 )
    {
      memcpy_0(Dst, v10, 4ui64);
      *v10 = (_unit_bullet_param)-1;
      CUserDB::Update_UnitData(v11->m_pUserDB, v12, pData);
    }
    CPlayer::SendMsg_UnitBulletReplaceResult(v11, v7);
  }
}
