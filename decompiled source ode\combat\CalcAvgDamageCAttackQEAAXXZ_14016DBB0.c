/*
 * Function: ?CalcAvgDamage@CAttack@@QEAAXXZ
 * Address: 0x14016DBB0
 */

void __fastcall CAttack::CalcAvgDamage(CAttack *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  float v3; // [sp+0h] [bp-28h]@1
  float v4; // [sp+4h] [bp-24h]@10
  int j; // [sp+8h] [bp-20h]@10
  int v6; // [sp+Ch] [bp-1Ch]@6
  int v7; // [sp+10h] [bp-18h]@13
  CAttack *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v1 = (int *)&v3;
  for ( i = 8i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  v3 = FLOAT_1_0;
  if ( v8->m_nDamagedObjNum == 1 )
  {
    if ( v8->m_DamList[0].m_nDamage == -2 )
      v6 = 2;
    else
      v6 = v8->m_DamList[0].m_nDamage;
    v3 = (float)v6;
  }
  else if ( v8->m_nDamagedObjNum > 1 )
  {
    v4 = 0.0;
    for ( j = 0; j < v8->m_nDamagedObjNum; ++j )
    {
      if ( v8->m_DamList[j].m_nDamage == -2 )
        v7 = 0;
      else
        v7 = v8->m_DamList[j].m_nDamage;
      v4 = v4 + (float)v7;
    }
    v3 = v4 / (float)v8->m_nDamagedObjNum;
  }
  if ( v3 <= 1.0 )
  {
    if ( v8->m_bIsCrtAtt )
      v8->m_bIsCrtAtt = 0;
    v8->m_bFailure = 1;
  }
}
