/*
 * Function: ?SkipWhiteSpace@TiXmlBase@@KAPEBDPEBDW4TiXmlEncoding@@@Z
 * Address: 0x140530B60
 */

const char *__fastcall TiXmlBase::SkipWhiteSpace(const char *a1, enum TiXmlEncoding a2)
{
  const char *v2; // rbx@1
  char v3; // di@2
  char v4; // al@5
  const char *result; // rax@20
  char v6; // di@21

  v2 = a1;
  if ( a1 && (v3 = *a1) != 0 )
  {
    if ( a2 == 1 )
    {
      while ( 1 )
      {
        if ( v3 == -17 )
        {
          v4 = v2[1];
          if ( v4 == -69 && v2[2] == -65 )
          {
            v2 += 3;
            goto LABEL_19;
          }
          if ( v4 == -65 )
          {
            if ( v2[2] == -66 )
            {
              v2 += 3;
              goto LABEL_19;
            }
            if ( v2[2] == -65 )
            {
              v2 += 3;
              goto LABEL_19;
            }
          }
        }
        if ( !isspace((unsigned __int8)v3) && v3 != 10 && v3 != 13 && *v2 != 10 && *v2 != 13 )
          goto LABEL_20;
        ++v2;
LABEL_19:
        v3 = *v2;
        if ( !*v2 )
          goto LABEL_20;
      }
    }
    while ( 1 )
    {
      v6 = *v2;
      if ( (!*v2 || !isspace((unsigned __int8)v6) && v6 != 10 && v6 != 13) && *v2 != 10 && *v2 != 13 )
        break;
      ++v2;
    }
LABEL_20:
    result = v2;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
