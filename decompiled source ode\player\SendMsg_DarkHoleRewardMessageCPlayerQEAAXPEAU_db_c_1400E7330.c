/*
 * Function: ?SendMsg_DarkHoleRewardMessage@CPlayer@@QEAAXPEAU_db_con@_STORAGE_LIST@@KH@Z
 * Address: 0x1400E7330
 */

void __fastcall CPlayer::SendMsg_DarkHoleRewardMessage(CPlayer *this, _STORAGE_LIST::_db_con *pItem, unsigned int dwMemberIndex, int isRewarded)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-88h]@1
  char szMsg[4]; // [sp+38h] [bp-50h]@4
  unsigned int v8; // [sp+3Ch] [bp-4Ch]@4
  char v9; // [sp+40h] [bp-48h]@4
  unsigned __int16 v10; // [sp+41h] [bp-47h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v12; // [sp+65h] [bp-23h]@4
  CPlayer *v13; // [sp+90h] [bp+8h]@1

  v13 = this;
  v4 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  *(_DWORD *)szMsg = isRewarded;
  v8 = dwMemberIndex;
  v9 = pItem->m_byTableCode;
  v10 = pItem->m_wItemIndex;
  pbyType = 35;
  v12 = -44;
  CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, szMsg, 0xBu);
}
