/*
 * Function: ??$invoke@PEAVCLuaSignalReActor@@@?$mem_functor@VCMonster@@XXXXX@lua_tinker@@SAHPEAUlua_State@@@Z
 * Address: 0x140409390
 */

signed __int64 __fastcall lua_tinker::mem_functor<CMonster,void,void,void,void,void>::invoke<CLuaSignalReActor *>(struct lua_State *a1)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CMonster *v3; // rax@4
  CLuaSignalReActor *v4; // rax@4
  __int64 v6; // [sp+0h] [bp-38h]@1
  CLuaSignalReActor *(__cdecl *v7)(CMonster *); // [sp+20h] [bp-18h]@4
  struct lua_State *L; // [sp+40h] [bp+8h]@1

  L = a1;
  v1 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = lua_tinker::upvalue_<CLuaSignalReActor * (CMonster::*)(void)>(L);
  v3 = lua_tinker::read<CMonster *>(L, 1);
  LODWORD(v4) = ((int (__fastcall *)(CMonster *))v7)(v3);
  lua_tinker::push<CLuaSignalReActor *>(L, v4);
  return 1i64;
}
