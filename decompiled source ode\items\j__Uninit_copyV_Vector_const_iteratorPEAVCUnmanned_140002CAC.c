/*
 * Function: j_??$_Uninit_copy@V?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@PEAPEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@2@@std@@YAPEAPEAVCUnmannedTraderClassInfo@@V?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@0@0PEAPEAV1@AEAV?$allocator@PEAVCUnmannedTraderClassInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140002CAC
 */

CUnmannedTraderClassInfo **__fastcall std::_Uninit_copy<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,CUnmannedTraderClassInfo * *,std::allocator<CUnmannedTraderClassInfo *>>(std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_First, std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_Last, CUnmannedTraderClassInfo **_Dest, std::allocator<CUnmannedTraderClassInfo *> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,CUnmannedTraderClassInfo * *,std::allocator<CUnmannedTraderClassInfo *>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
