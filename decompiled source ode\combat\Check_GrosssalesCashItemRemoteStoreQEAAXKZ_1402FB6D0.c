/*
 * Function: ?Check_Grosssales@CashItemRemoteStore@@QEAAXK@Z
 * Address: 0x1402FB6D0
 */

void __fastcall CashItemRemoteStore::Check_Grosssales(CashItemRemoteStore *this, unsigned int dwTotalSellCash)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  __time32_t Time; // [sp+24h] [bp-24h]@4
  int j; // [sp+34h] [bp-14h]@6
  struct tm *Tm; // [sp+38h] [bp-10h]@24
  CashItemRemoteStore *v8; // [sp+50h] [bp+8h]@1
  unsigned int v9; // [sp+58h] [bp+10h]@1

  v9 = dwTotalSellCash;
  v8 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _time32(&Time);
  if ( v8->m_cde.m_ini.m_cdeTime[0] > Time || v8->m_cde.m_ini.m_cdeTime[1] < Time )
  {
    for ( j = 0; j < 3; ++j )
    {
      if ( v8->m_cash_event[j].m_ini.m_EventTime[0] <= Time && v8->m_cash_event[j].m_ini.m_EventTime[1] >= Time )
        return;
    }
    if ( (v8->m_con_event.m_eventtime.m_EventTime[0] < Time || v8->m_con_event.m_eventtime.m_EventTime[1] < Time)
      && !v8->m_con_event.m_bConEvent
      && (Time <= v8->m_cde.m_ini.m_cdeTime[1] || (unsigned int)(Time - v8->m_cde.m_ini.m_cdeTime[1]) > 0x1C20) )
    {
      for ( j = 0; j < 3; ++j )
      {
        if ( Time > v8->m_cash_event[j].m_ini.m_EventTime[1]
          && (unsigned int)(Time - v8->m_cash_event[j].m_ini.m_EventTime[1]) <= 0x1C20 )
        {
          return;
        }
      }
      if ( (Time <= v8->m_con_event.m_eventtime.m_EventTime[1]
         || (unsigned int)(Time - v8->m_con_event.m_eventtime.m_EventTime[1]) > 0x1C20)
        && v9 < v8->m_con_event.m_ini.m_dwCashMin )
      {
        Tm = _localtime32(&Time);
        ++Tm->tm_min;
        v8->m_con_event.m_eventtime.m_EventTime[0] = _mktime32(Tm);
        v8->m_con_event.m_eventtime.m_nYear[0] = Tm->tm_year;
        v8->m_con_event.m_eventtime.m_nMonth[0] = Tm->tm_mon;
        v8->m_con_event.m_eventtime.m_nDay[0] = Tm->tm_mday;
        v8->m_con_event.m_eventtime.m_nHour[0] = Tm->tm_hour;
        v8->m_con_event.m_eventtime.m_nMinute[0] = Tm->tm_min;
        Tm->tm_min += v8->m_con_event.m_ini.m_iEventTime;
        v8->m_con_event.m_eventtime.m_EventTime[1] = _mktime32(Tm);
        v8->m_con_event.m_eventtime.m_nYear[1] = Tm->tm_year;
        v8->m_con_event.m_eventtime.m_nMonth[1] = Tm->tm_mon;
        v8->m_con_event.m_eventtime.m_nDay[1] = Tm->tm_mday;
        v8->m_con_event.m_eventtime.m_nHour[1] = Tm->tm_hour;
        v8->m_con_event.m_eventtime.m_nMinute[1] = Tm->tm_min;
        v8->m_con_event.m_bConEvent = 1;
        CashItemRemoteStore::Set_Conditional_Evnet_Status(v8, 1);
      }
    }
  }
}
