/*
 * Function: ?dev_full_animus_gauge@CPlayer@@QEAA_NXZ
 * Address: 0x1400C00C0
 */

char __fastcall CPlayer::dev_full_animus_gauge(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  _STORAGE_LIST::_db_con *v3; // rcx@9
  _STORAGE_LIST::_db_con *v5; // rcx@12
  _STORAGE_LIST::_db_con *v6; // r8@12
  __int64 v7; // [sp+0h] [bp-D8h]@1
  bool bUpdate[2]; // [sp+20h] [bp-B8h]@12
  int j; // [sp+30h] [bp-A8h]@4
  _animus_fld *v10; // [sp+38h] [bp-A0h]@9
  unsigned int *v11; // [sp+40h] [bp-98h]@11
  char szMsg[2]; // [sp+54h] [bp-84h]@13
  __int16 v13; // [sp+56h] [bp-82h]@13
  char pbyType; // [sp+74h] [bp-64h]@13
  char v15; // [sp+75h] [bp-63h]@13
  char v16[2]; // [sp+94h] [bp-44h]@13
  __int16 v17; // [sp+96h] [bp-42h]@13
  char v18; // [sp+B4h] [bp-24h]@13
  char v19; // [sp+B5h] [bp-23h]@13
  int nAnimusClass; // [sp+C4h] [bp-14h]@9
  CPlayer *v21; // [sp+E0h] [bp+8h]@1

  v21 = this;
  v1 = &v7;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 4; ++j )
  {
    if ( v21->m_Param.m_dbAnimus.m_pStorageList[j].m_bLoad && !v21->m_Param.m_dbAnimus.m_pStorageList[j].m_bLock )
    {
      v3 = v21->m_Param.m_dbAnimus.m_pStorageList;
      nAnimusClass = v21->m_Param.m_dbAnimus.m_pStorageList[j].m_wItemIndex;
      v10 = GetAnimusFldFromExp(nAnimusClass, v3[j].m_dwDur);
      if ( !v10 )
        return 0;
      v11 = &v21->m_Param.m_dbAnimus.m_pStorageList[j].m_dwLv;
      *(_WORD *)v11 = v10->m_nMaxHP;
      *((_WORD *)v11 + 1) = v10->m_nMaxFP;
      if ( v21->m_pUserDB )
      {
        v5 = v21->m_Param.m_dbAnimus.m_pStorageList;
        v6 = v21->m_Param.m_dbAnimus.m_pStorageList;
        bUpdate[0] = 0;
        CUserDB::Update_ItemUpgrade(v21->m_pUserDB, 4, v6[j].m_byStorageIndex, v5[j].m_dwLv, 0);
      }
      *(_WORD *)szMsg = v21->m_Param.m_dbAnimus.m_pStorageList[j].m_wSerial;
      v13 = *(_WORD *)v11;
      pbyType = 22;
      v15 = 9;
      CNetProcess::LoadSendMsg(unk_1414F2088, v21->m_ObjID.m_wIndex, &pbyType, szMsg, 4u);
      *(_WORD *)v16 = v21->m_Param.m_dbAnimus.m_pStorageList[j].m_wSerial;
      v17 = *((_WORD *)v11 + 1);
      v18 = 22;
      v19 = 10;
      CNetProcess::LoadSendMsg(unk_1414F2088, v21->m_ObjID.m_wIndex, &v18, v16, 4u);
    }
  }
  return 1;
}
