/*
 * Function: _CUnmannedTraderUserInfo::CheckCancelRegist_::_1_::dtor$1
 * Address: 0x14035B590
 */

void __fastcall CUnmannedTraderUserInfo::CheckCancelRegist_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>((std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)(a2 + 88));
}
