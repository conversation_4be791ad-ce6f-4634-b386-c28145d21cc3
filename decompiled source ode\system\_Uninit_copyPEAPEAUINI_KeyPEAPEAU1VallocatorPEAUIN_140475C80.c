/*
 * Function: ??$_Uninit_copy@PEAPEAUINI_Key@@PEAPEAU1@V?$allocator@PEAUI<PERSON>_Key@@@std@@@std@@YAPEAPEAUINI_Key@@PEAPEAU1@00AEAV?$allocator@PEAUI<PERSON>_Key@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140475C80
 */

INI_Key **__fastcall std::_Uninit_copy<INI_Key * *,INI_Key * *,std::allocator<INI_Key *>>(INI_Key **_First, INI_Key **_Last, INI_Key **_Dest, std::allocator<INI_Key *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-38h]@1
  __int64 v10; // [sp+20h] [bp-18h]@4
  INI_Key **v11; // [sp+28h] [bp-10h]@4
  INI_Key **Src; // [sp+40h] [bp+8h]@1

  Src = _First;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10 = _Last - Src;
  v11 = &_Dest[v10];
  if ( v10 )
    memmove_s(_Dest, 8 * v10, Src, 8 * v10);
  return v11;
}
