/*
 * Function: _std::vector_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo___::erase_::_1_::dtor$1
 * Address: 0x140378A60
 */

void __fastcall std::vector_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo___::erase_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(*(std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > **)(a2 + 96));
}
