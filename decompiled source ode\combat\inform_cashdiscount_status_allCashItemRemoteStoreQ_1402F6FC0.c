/*
 * Function: ?inform_cashdiscount_status_all@CashItemRemoteStore@@QEAAXEPEAU_cash_discount_ini_@@@Z
 * Address: 0x1402F6FC0
 */

void __fastcall CashItemRemoteStore::inform_cashdiscount_status_all(CashItemRemoteStore *this, char byType, _cash_discount_ini_ *pIni)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned __int16 j; // [sp+20h] [bp-18h]@4
  CPlayer *v7; // [sp+28h] [bp-10h]@7
  CashItemRemoteStore *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1

  v9 = byType;
  v8 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; (signed int)j < 2532; ++j )
  {
    v7 = &g_Player + j;
    if ( v7->m_bLive )
      ICsSendInterface::SendMsg_CashDiscountEventInform(j, v9, &v8->m_cde.m_ini);
  }
}
