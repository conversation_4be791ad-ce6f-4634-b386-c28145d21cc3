/*
 * Function: ?CalcDistForSec@CCharacter@@QEAAMMM@Z
 * Address: 0x140173360
 */

float __fastcall CCharacter::CalcDistForSec(CCharacter *this, float fSec, float fSpeed)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-18h]@1

  v3 = &v6;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return (float)(fSec * 15.0) * fSpeed;
}
