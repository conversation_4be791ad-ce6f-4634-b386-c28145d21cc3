/*
 * Function: ?InsertDefaultPSRecord@CPostSystemManager@@QEAA_NXZ
 * Address: 0x140325190
 */

char __fastcall CPostSystemManager::InsertDefaultPSRecord(CPostSystemManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  int v6; // [sp+24h] [bp-14h]@4
  CPostSystemManager *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = 5000;
  v6 = CRFWorldDatabase::Select_PostStorageEmptyRecord(pkDB);
  if ( v6 >= 0 )
  {
    if ( v6 >= 5000 || CRFWorldDatabase::Insert_PSDefaultRecord(pkDB, 0x1388u) )
    {
      result = 1;
    }
    else
    {
      CPostSystemManager::Log(
        v7,
        "CPostSystemManager::InsertDefaultPSRecord\r\n\t\tg_Main.m_pWorldDB->Insert_PSDefaultRecord( %d ) Fail!\r\n",
        5000i64);
      result = 0;
    }
  }
  else
  {
    CPostSystemManager::Log(
      v7,
      "CPostSystemManager::InsertDefaultPSRecord\r\n"
      "\t\tnCount(%d) = g_Main.m_pWorldDB->Select_PostStorageEmptyRecord() Fail!\r\n",
      (unsigned int)v6);
    result = 0;
  }
  return result;
}
