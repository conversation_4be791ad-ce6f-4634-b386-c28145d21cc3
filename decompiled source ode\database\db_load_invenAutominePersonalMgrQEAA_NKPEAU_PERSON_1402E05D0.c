/*
 * Function: ?db_load_inven@AutominePersonalMgr@@QEAA_NKPEAU_PERSONALAMINE_INVEN_DB_BASE@@@Z
 * Address: 0x1402E05D0
 */

char __fastcall AutominePersonalMgr::db_load_inven(AutominePersonalMgr *this, unsigned int dwSerial, _PERSONALAMINE_INVEN_DB_BASE *pCon)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-188h]@1
  _personal_amine_inven pInven; // [sp+30h] [bp-158h]@4
  int v8; // [sp+174h] [bp-14h]@4
  int j; // [sp+178h] [bp-10h]@6
  _PERSONALAMINE_INVEN_DB_BASE *v10; // [sp+1A0h] [bp+18h]@1

  v10 = pCon;
  v3 = &v6;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = CRFWorldDatabase::select_amine_personal(pkDB, dwSerial, &pInven);
  if ( v8 )
  {
    result = 0;
  }
  else
  {
    for ( j = 0; j < 40; ++j )
    {
      if ( pInven.list[j].lK == -1 )
      {
        _INVENKEY::SetRelease((_INVENKEY *)&v10->m_List[j]);
        v10->m_List[j].dwDur = 0;
      }
      else
      {
        memcpy_0(&v10->m_List[j], (char *)&pInven + 8 * j, 4ui64);
        v10->m_List[j].dwDur = pInven.list[j].byNum;
      }
    }
    result = 1;
  }
  return result;
}
