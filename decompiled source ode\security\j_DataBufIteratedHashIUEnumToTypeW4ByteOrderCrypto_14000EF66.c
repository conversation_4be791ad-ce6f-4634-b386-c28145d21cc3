/*
 * Function: j_?DataBuf@?$IteratedHash@IU?$EnumToType@W4ByteOrder@CryptoPP@@$00@CryptoPP@@$0EA@VHashTransformation@2@@CryptoPP@@MEAAPEAIXZ
 * Address: 0x14000EF66
 */

unsigned int *__fastcall CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>::DataBuf(CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation> *this)
{
  return CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>::DataBuf(this);
}
