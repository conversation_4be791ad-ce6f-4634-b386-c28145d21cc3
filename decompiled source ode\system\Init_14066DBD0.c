/*
 * Function: Init
 * Address: 0x14066DBD0
 */

char __cdecl Init()
{
  int *v0; // rax@2
  int *v1; // rax@6
  int *v2; // rax@9
  int v4; // [sp+30h] [bp-148h]@1
  char DstBuf; // [sp+40h] [bp-138h]@1
  int v6; // [sp+150h] [bp-28h]@1
  int v7; // [sp+154h] [bp-24h]@7
  ATL::CAtlException v8; // [sp+158h] [bp-20h]@6
  int v9; // [sp+15Ch] [bp-1Ch]@10
  ATL::CAtlException v10; // [sp+160h] [bp-18h]@9

  v6 = *errno_0();
  *errno_0() = 0;
  GetCurrentProcessId();
  v4 = snprintf_s_0(&DstBuf, 0x104ui64, 0x103ui64, g_pszKernelObjFmt);
  if ( *errno_0() )
  {
    v0 = errno_0();
    ATL::AtlCrtErrorCheck(*v0);
  }
  else
  {
    *errno_0() = v6;
  }
  if ( v4 == -1 || v4 >= 260 )
  {
    ATL::CAtlException::CAtlException(&v8, -2147467259);
    v7 = *v1;
    CxxThrowException_0((__int64)&v7, (__int64)&TI1_AVCAtlException_ATL__);
  }
  if ( !CAtlAllocator::Init(&g_Allocator, &DstBuf, 0x400000u) )
  {
    ATL::CAtlException::CAtlException(&v10, -2147024882);
    v9 = *v2;
    CxxThrowException_0((__int64)&v9, (__int64)&TI1_AVCAtlException_ATL__);
  }
  return 1;
}
