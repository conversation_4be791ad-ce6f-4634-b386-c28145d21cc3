/*
 * Function: ?Loop@CNormalGuildBattleStateInBattle@GUILD_BATTLE@@UEAAHPEAVCGuildBattle@2@@Z
 * Address: 0x140080070
 */

__int64 __fastcall GUILD_BATTLE::CNormalGuildBattleStateInBattle::Loop(GUILD_BATTLE::CNormalGuildBattleStateInBattle *this, GUILD_BATTLE::CGuildBattle *pkBattle)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleStateInBattle *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  GUILD_BATTLE::CGuildBattleStateList::Process(
    (GUILD_BATTLE::CGuildBattleStateList *)&v6->m_kRountStateList.vfptr,
    pkBattle);
  return 0i64;
}
