/*
 * Function: ?SendMsg_OreIntoBagResult@CPlayer@@QEAAXEGEK@Z
 * Address: 0x1400DC950
 */

void __fastcall CPlayer::SendMsg_OreIntoBagResult(CPlayer *this, char byErrCode, unsigned __int16 wNewSerial, char byLendType, unsigned int dwLendTime)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+38h] [bp-40h]@4
  unsigned __int16 v9; // [sp+39h] [bp-3Fh]@4
  char v10; // [sp+3Bh] [bp-3Dh]@4
  unsigned int v11; // [sp+3Ch] [bp-3Ch]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v13; // [sp+55h] [bp-23h]@4
  CPlayer *v14; // [sp+80h] [bp+8h]@1

  v14 = this;
  v5 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  szMsg = byErrCode;
  v9 = wNewSerial;
  v10 = byLendType;
  v11 = dwLendTime;
  pbyType = 14;
  v13 = 12;
  CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_ObjID.m_wIndex, &pbyType, &szMsg, 8u);
}
