/*
 * Function: ?IsProcLinkNpcQuest@CQuestMgr@@QEAA_NPEADH@Z
 * Address: 0x14028AA30
 */

char __fastcall CQuestMgr::IsProcLinkNpcQuest(CQuestMgr *this, char *pszCode, int nLinkQuestGroupID)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@4
  _QUEST_DB_BASE *v8; // [sp+28h] [bp-20h]@7
  _base_fld *v9; // [sp+30h] [bp-18h]@8
  CQuestMgr *v10; // [sp+50h] [bp+8h]@1
  int v11; // [sp+60h] [bp+18h]@1

  v11 = nLinkQuestGroupID;
  v10 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; j < 30; ++j )
  {
    v8 = (_QUEST_DB_BASE *)((char *)v10->m_pQuestData + 13 * j);
    if ( v8->m_List[0].byQuestType == 1 )
    {
      v9 = 0i64;
      v9 = CRecordData::GetRecord(CQuestMgr::s_tblQuest, v8->m_List[0].wIndex);
      if ( v9 )
      {
        if ( *(_DWORD *)&v9[27].m_strCode[24] == v11 )
          return 0;
      }
    }
  }
  return 1;
}
