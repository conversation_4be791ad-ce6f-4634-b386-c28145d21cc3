/*
 * Function: ??$_Uninit_copy@PEAUEC2NPoint@CryptoPP@@PEAU12@V?$allocator@UEC2NPoint@CryptoPP@@@std@@@std@@YAPEAUEC2NPoint@CryptoPP@@PEAU12@00AEAV?$allocator@UEC2NPoint@CryptoPP@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405A22F0
 */

__int64 __fastcall std::_Uninit_copy<CryptoPP::EC2NPoint *,CryptoPP::EC2NPoint *,std::allocator<CryptoPP::EC2NPoint>>(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  __int64 i; // [sp+40h] [bp+8h]@1
  __int64 v6; // [sp+48h] [bp+10h]@1
  __int64 v7; // [sp+50h] [bp+18h]@1
  __int64 v8; // [sp+58h] [bp+20h]@1

  v8 = a4;
  v7 = a3;
  v6 = a2;
  for ( i = a1; i != v6; i += 56i64 )
  {
    std::allocator<CryptoPP::EC2NPoint>::construct(v8, v7, i);
    v7 += 56i64;
  }
  return v7;
}
