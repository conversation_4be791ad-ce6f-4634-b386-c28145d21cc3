/*
 * Function: ?PotionSocketDivisionRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D6F90
 */

char __fastcall CNetworkEX::PotionSocketDivisionRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  char *v6; // rax@8
  __int64 v7; // [sp+0h] [bp-48h]@1
  char *v8; // [sp+20h] [bp-28h]@4
  CPlayer *v9; // [sp+28h] [bp-20h]@4
  unsigned int v10; // [sp+30h] [bp-18h]@8
  CNetworkEX *v11; // [sp+50h] [bp+8h]@1

  v11 = this;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = pBuf;
  v9 = &g_Player + n;
  if ( v9->m_bOper && !v9->m_pmTrd.bDTradeMode )
  {
    if ( (signed int)(unsigned __int8)v8[4] <= 99 )
    {
      CPlayer::pc_PotionDivision(v9, *(_WORD *)v8, *((_WORD *)v8 + 1), v8[4]);
      result = 1;
    }
    else
    {
      v10 = (unsigned __int8)v8[4];
      v6 = CPlayerDB::GetCharNameA(&v9->m_Param);
      CLogFile::Write(&v11->m_LogFile, "odd.. %s: PotionSocketDivisionRequest() : pRecv->byMoveAmount(%d)", v6, v10);
      result = 0;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
