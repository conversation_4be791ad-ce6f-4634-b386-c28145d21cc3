/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAPEAVCMoveMapLimitInfo@@PEAPEAV1@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@stdext@@YAPEAPEAVCMoveMapLimitInfo@@PEAPEAV1@00AEAV?$allocator@PEAVCMoveMapLimitInfo@@@std@@@Z
 * Address: 0x1400139F3
 */

CMoveMapLimitInfo **__fastcall stdext::_Unchecked_uninitialized_move<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *,std::allocator<CMoveMapLimitInfo *>>(CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, CMoveMapLimitInfo **_Dest, std::allocator<CMoveMapLimitInfo *> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *,std::allocator<CMoveMapLimitInfo *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
