/*
 * Function: luaT_init
 * Address: 0x14053E5E0
 */

__int64 __fastcall luaT_init(__int64 a1)
{
  __int64 v1; // rsi@1
  signed __int64 v2; // rbx@1
  __int64 v3; // rax@2
  __int64 result; // rax@2

  v1 = a1;
  v2 = 296i64;
  do
  {
    LODWORD(v3) = luaS_newlstr(
                    v1,
                    *(_QWORD *)((char *)&unk_1408880B0 + v2 - 296),
                    strlen(*(const char **)((char *)&unk_1408880B0 + v2 - 296)));
    v2 += 8i64;
    *(_QWORD *)(v2 + *(_QWORD *)(v1 + 32) - 8) = v3;
    result = *(_QWORD *)(v1 + 32);
    *(_BYTE *)(*(_QWORD *)(result + v2 - 8) + 9i64) |= 0x20u;
  }
  while ( v2 < 432 );
  return result;
}
