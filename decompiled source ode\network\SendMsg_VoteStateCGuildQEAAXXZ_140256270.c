/*
 * Function: ?SendMsg_VoteState@CGuild@@QEAAXXZ
 * Address: 0x140256270
 */

void __fastcall CGuild::SendMsg_VoteState(CGuild *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-88h]@1
  int v4; // [sp+30h] [bp-58h]@5
  int j; // [sp+34h] [bp-54h]@5
  char szMsg[4]; // [sp+48h] [bp-40h]@10
  char v7; // [sp+4Ch] [bp-3Ch]@10
  char v8; // [sp+4Dh] [bp-3Bh]@10
  char v9; // [sp+4Eh] [bp-3Ah]@10
  char v10; // [sp+4Fh] [bp-39h]@10
  char pbyType; // [sp+64h] [bp-24h]@10
  char v12; // [sp+65h] [bp-23h]@10
  _guild_member_info *v13; // [sp+78h] [bp-10h]@13
  CGuild *v14; // [sp+90h] [bp+8h]@1

  v14 = this;
  v1 = &v3;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v14->m_bNowProcessSgtMter )
  {
    v4 = 0;
    for ( j = 0; j < v14->m_SuggestedMatter.nTotal_VotableMemNum; ++j )
    {
      if ( v14->m_SuggestedMatter.VotableMem[j]->pPlayer )
        ++v4;
    }
    *(_DWORD *)szMsg = v14->m_SuggestedMatter.dwMatterVoteSynKey;
    v7 = v14->m_SuggestedMatter.byVoteState[0];
    v8 = v14->m_SuggestedMatter.byVoteState[1];
    v10 = v14->m_SuggestedMatter.nTotal_VotableMemNum;
    v9 = v4;
    pbyType = 27;
    v12 = 27;
    for ( j = 0; j < 50; ++j )
    {
      v13 = &v14->m_MemberData[j];
      if ( _guild_member_info::IsFill(v13) && v13->pPlayer )
        CNetProcess::LoadSendMsg(unk_1414F2088, v13->pPlayer->m_ObjID.m_wIndex, &pbyType, szMsg, 8u);
    }
  }
}
