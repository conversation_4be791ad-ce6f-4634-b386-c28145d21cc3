/*
 * Function: ?Update_SFContUpdate@CUserDB@@QEAA_NEEG_N@Z
 * Address: 0x140116D80
 */

char __fastcall CUserDB::Update_SFContUpdate(CUserDB *this, char byContCode, char bySlotIndex, unsigned __int16 wTime, bool bUpdate)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-38h]@1
  int v9; // [sp+20h] [bp-18h]@9
  CUserDB *v10; // [sp+40h] [bp+8h]@1
  char v11; // [sp+48h] [bp+10h]@1
  char v12; // [sp+50h] [bp+18h]@1
  unsigned __int16 v13; // [sp+58h] [bp+20h]@1

  v13 = wTime;
  v12 = bySlotIndex;
  v11 = byContCode;
  v10 = this;
  v5 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( (signed int)(unsigned __int8)byContCode < 2 )
  {
    if ( (signed int)(unsigned __int8)bySlotIndex < 8 )
    {
      if ( _SFCONT_DB_BASE::_LIST::IsFilled((_SFCONT_DB_BASE::_LIST *)&v10->m_AvatorData.dbSfcont + 8 * (unsigned __int8)byContCode + (unsigned __int8)bySlotIndex) )
      {
        _SFCONT_DB_BASE::_LIST::SetLeftTime(
          (_SFCONT_DB_BASE::_LIST *)&v10->m_AvatorData.dbSfcont + 8 * (unsigned __int8)v11 + (unsigned __int8)v12,
          v13);
        result = 1;
      }
      else
      {
        v9 = (unsigned __int8)v12;
        CLogFile::Write(
          &stru_1799C8E78,
          "%s : Update_SFContUpdate(NOTHING) : code : %d, slot : %d",
          v10->m_aszAvatorName,
          (unsigned __int8)v11);
        result = 0;
      }
    }
    else
    {
      CLogFile::Write(
        &stru_1799C8E78,
        "%s : Update_SFContUpdate(SlotIndex OVER) : slot : %d",
        v10->m_aszAvatorName,
        (unsigned __int8)bySlotIndex);
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_SFContUpdate(byContCode OVER) : code : %d",
      v10->m_aszAvatorName,
      (unsigned __int8)byContCode);
    result = 0;
  }
  return result;
}
