/*
 * Function: ?SetLogFile@CRFNewDatabase@@QEAAXPEBD0@Z
 * Address: 0x140488640
 */

void __fastcall CRFNewDatabase::SetLogFile(CRFNewDatabase *this, const char *szUpperLogPath, const char *szOdbcName)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-488h]@1
  bool bDate[4]; // [sp+20h] [bp-468h]@4
  char Dst; // [sp+40h] [bp-448h]@4
  char _Dest[512]; // [sp+260h] [bp-228h]@4
  unsigned __int64 v9; // [sp+470h] [bp-18h]@4
  CRFNewDatabase *v10; // [sp+490h] [bp+8h]@1
  const char *v11; // [sp+4A0h] [bp+18h]@1

  v11 = szOdbcName;
  v10 = this;
  v3 = &v5;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  sprintf_s<128>((char (*)[128])v10->m_szLogUpperPath, szUpperLogPath);
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0x1FFui64);
  sprintf_s<512>((char (*)[512])_Dest, "%sDBLog", v10->m_szLogUpperPath);
  CreateDirectoryA(_Dest, 0i64);
  memset_0(&Dst, 0, 0x200ui64);
  *(_DWORD *)bDate = GetKorLocalTime();
  sprintf(&Dst, "%sDBLog\\%DBProcess_%s_%d_U.log", v10->m_szLogUpperPath, v11);
  if ( !v10->m_ProcessLogW.m_bInit )
    CLogFile::SetWriteLogFile(&v10->m_ProcessLogW, &Dst, 1, 1, 1, 1);
  memset_0(&Dst, 0, 0x200ui64);
  *(_DWORD *)bDate = GetKorLocalTime();
  sprintf(&Dst, "%sDBLog\\DBProcess_%s_%d_A.log", v10->m_szLogUpperPath, v11);
  if ( !v10->m_ProcessLogA.m_bInit )
    CLogFile::SetWriteLogFile(&v10->m_ProcessLogA, &Dst, 1, 1, 1, 1);
  memset_0(&Dst, 0, 0x200ui64);
  *(_DWORD *)bDate = GetKorLocalTime();
  sprintf(&Dst, "%sDBLog\\DBError_%s_%d_U.log", v10->m_szLogUpperPath, v11);
  if ( !v10->m_ErrorLogW.m_bInit )
    CLogFile::SetWriteLogFile(&v10->m_ErrorLogW, &Dst, 1, 1, 1, 1);
  memset_0(&Dst, 0, 0x200ui64);
  *(_DWORD *)bDate = GetKorLocalTime();
  sprintf(&Dst, "%sDBLog\\DBError_%s_%d_A.log", v10->m_szLogUpperPath, v11);
  if ( !v10->m_ErrorLogA.m_bInit )
    CLogFile::SetWriteLogFile(&v10->m_ErrorLogA, &Dst, 1, 1, 1, 1);
  v10->m_byLogFileHour = CRFNewDatabase::GetLocalHour(v10);
  v10->m_bSaveDBLog = 0;
}
