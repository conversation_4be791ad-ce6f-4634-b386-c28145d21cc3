/*
 * Function: ?AttackTestRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C1D60
 */

char __fastcall CNetworkEX::AttackTestRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@10
  char *v7; // rax@13
  char *v8; // rax@16
  __int64 v9; // [sp+0h] [bp-48h]@1
  char *v10; // [sp+20h] [bp-28h]@4
  CPlayer *v11; // [sp+28h] [bp-20h]@4
  unsigned int v12; // [sp+30h] [bp-18h]@10
  CNetworkEX *v13; // [sp+50h] [bp+8h]@1

  v13 = this;
  v3 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = pBuf;
  v11 = &g_Player + n;
  if ( !v11->m_bOper || v11->m_pmTrd.bDTradeMode || v11->m_bCorpse )
  {
    result = 1;
  }
  else if ( (unsigned __int8)*v10 == 255 || (signed int)(unsigned __int8)*v10 < 4 )
  {
    if ( (signed int)(unsigned __int8)*v10 >= 4
      || CRecordData::GetRecord(&stru_1799C8410 + (unsigned __int8)*v10, (unsigned __int8)v10[1]) )
    {
      if ( (unsigned __int8)v10[4] == 255 || (signed int)(unsigned __int8)v10[4] < 2 )
      {
        result = 1;
      }
      else
      {
        v8 = CPlayerDB::GetCharNameA(&v11->m_Param);
        CLogFile::Write(
          &v13->m_LogFile,
          "odd.. %s: AttackTestRequest()..  if(pRecv->byWeaponPart >= UNIT_BULLET_NUM)",
          v8);
        result = 1;
      }
    }
    else
    {
      v7 = CPlayerDB::GetCharNameA(&v11->m_Param);
      CLogFile::Write(
        &v13->m_LogFile,
        "odd.. %s: AttackTestRequest()..  if(!g_Main.m_tblEffectData[pRecv->byEffectCode].GetRecord(pRecv->byEffectIndex))",
        v7);
      result = 1;
    }
  }
  else
  {
    v12 = (unsigned __int8)*v10;
    v6 = CPlayerDB::GetCharNameA(&v11->m_Param);
    CLogFile::Write(
      &v13->m_LogFile,
      "odd.. %s: AttackTestRequest()..  if(pRecv->byEffectCode != 0xFF && pRecv->byEffectCode >= EFFECT_CODE_NUM) : %d",
      v6,
      v12);
    result = 1;
  }
  return result;
}
