/*
 * Function: j_?_Insert_n@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@IEAAXV?$_Vector_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@2@_KAEBQEAVCMoveMapLimitRight@@@Z
 * Address: 0x140001749
 */

void __fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Insert_n(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_Where, unsigned __int64 _Count, CMoveMapLimitRight *const *_Val)
{
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Insert_n(this, _Where, _Count, _Val);
}
