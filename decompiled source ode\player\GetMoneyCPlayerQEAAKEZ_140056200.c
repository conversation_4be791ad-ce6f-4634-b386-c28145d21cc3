/*
 * Function: ?<PERSON><PERSON><PERSON>@CPlayer@@QEAAKE@Z
 * Address: 0x140056200
 */

unsigned int __fastcall CPlayer::<PERSON><PERSON><PERSON>(CPlayer *this, char byMoneyCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int result; // eax@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( byMoneyCode )
    result = CPlayerDB::GetGold(&v6->m_Param);
  else
    result = CPlayerDB::GetDalant(&v6->m_Param);
  return result;
}
