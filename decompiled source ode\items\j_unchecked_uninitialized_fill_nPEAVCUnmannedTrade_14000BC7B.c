/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAVCUnmannedTraderSchedule@@_KV1@V?$allocator@VCUnmannedTraderSchedule@@@std@@@stdext@@YAXPEAVCUnmannedTraderSchedule@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderSchedule@@@std@@@Z
 * Address: 0x14000BC7B
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CUnmannedTraderSchedule *,unsigned __int64,CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(CUnmannedTraderSchedule *_First, unsigned __int64 _Count, CUnmannedTraderSchedule *_Val, std::allocator<CUnmannedTraderSchedule> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderSchedule *,unsigned __int64,CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(
    _First,
    _Count,
    _<PERSON>,
    _<PERSON>);
}
