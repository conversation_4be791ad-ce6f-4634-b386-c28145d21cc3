/*
 * Function: ??0_DB_LOAD_AUTOMINE_MACHINE@@QEAA@XZ
 * Address: 0x1402D4100
 */

void __fastcall _DB_LOAD_AUTOMINE_MACHINE::_DB_LOAD_AUTOMINE_MACHINE(_DB_LOAD_AUTOMINE_MACHINE *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _DB_LOAD_AUTOMINE_MACHINE *Dst; // [sp+30h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  `vector constructor iterator'(Dst->slot, 0xCui64, 80, (void *(__cdecl *)(void *))_AUTOMINE_SLOT::_AUTOMINE_SLOT);
  memset_0(Dst, 0, 0x3D1ui64);
}
