/*
 * Function: _std::_Uninit_copy_std::_Vector_iterator_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64____CLogTypeDBTask_____ptr64_____ptr64_std::allocator_CLogTypeDBTask_____ptr64____::_1_::dtor$1
 * Address: 0x1402C80F0
 */

void __fastcall std::_Uninit_copy_std::_Vector_iterator_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64____CLogTypeDBTask_____ptr64_____ptr64_std::allocator_CLogTypeDBTask_____ptr64____::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(*(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > **)(a2 + 96));
}
