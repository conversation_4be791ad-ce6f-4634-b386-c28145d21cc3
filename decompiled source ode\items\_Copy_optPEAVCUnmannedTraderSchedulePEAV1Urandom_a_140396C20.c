/*
 * Function: ??$_Copy_opt@PEAVCUnmannedTraderSchedule@@PEAV1@Urandom_access_iterator_tag@std@@@std@@YAPEAVCUnmannedTraderSchedule@@PEAV1@00Urandom_access_iterator_tag@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140396C20
 */

CUnmannedTraderSchedule *__fastcall std::_Copy_opt<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *,std::random_access_iterator_tag>(CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last, CUnmannedTraderSchedule *_Dest, std::random_access_iterator_tag __formal, std::_Nonscalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  char *v6; // rdi@1
  signed __int64 i; // rcx@1
  char v9; // [sp+0h] [bp-38h]@1
  CUnmannedTraderSchedule *v10; // [sp+40h] [bp+8h]@1
  CUnmannedTraderSchedule *v11; // [sp+50h] [bp+18h]@1

  v11 = _Dest;
  v10 = _First;
  v6 = &v9;
  for ( i = 10i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 += 4;
  }
  while ( v10 != _Last )
  {
    qmemcpy(&v9, v10, 0x20ui64);
    qmemcpy(v11, &v9, sizeof(CUnmannedTraderSchedule));
    ++v11;
    ++v10;
  }
  return v11;
}
