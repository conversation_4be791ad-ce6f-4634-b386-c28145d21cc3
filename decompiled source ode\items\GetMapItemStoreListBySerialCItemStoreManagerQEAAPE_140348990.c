/*
 * Function: ?GetMapItemStoreListBySerial@CItemStoreManager@@QEAAPEAVCMapItemStoreList@@H@Z
 * Address: 0x140348990
 */

CMapItemStoreList *__fastcall CItemStoreManager::GetMapItemStoreListBySerial(CItemStoreManager *this, int nSerial)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  bool *v6; // [sp+8h] [bp-10h]@6
  CItemStoreManager *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  for ( j = 0; j < v7->m_nMapItemStoreListNum; ++j )
  {
    v6 = &v7->m_MapItemStoreList[j].m_bUse;
    if ( *((_DWORD *)v6 + 1) == nSerial )
      return (CMapItemStoreList *)v6;
  }
  return 0i64;
}
