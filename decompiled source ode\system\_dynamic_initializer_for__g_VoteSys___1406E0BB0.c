/*
 * Function: _dynamic_initializer_for__g_VoteSys__
 * Address: 0x1406E0BB0
 */

__int64 dynamic_initializer_for__g_VoteSys__()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1

  v0 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  `eh vector constructor iterator'(
    g_VoteSys,
    0x5F0ui64,
    3,
    (void (__cdecl *)(void *))CVoteSystem::CVoteSystem,
    (void (__cdecl *)(void *))CVoteSystem::~CVoteSystem);
  return atexit(dynamic_atexit_destructor_for__g_VoteSys__);
}
