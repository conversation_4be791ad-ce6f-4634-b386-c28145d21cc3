/*
 * Function: ?SendMsg_InsertItemInform@CPlayer@@QEAAXEPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1400E0440
 */

void __fastcall CPlayer::SendMsg_InsertItemInform(CPlayer *this, char byStorageCode, _STORAGE_LIST::_db_con *pItem)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  char v7; // [sp+39h] [bp-4Fh]@4
  unsigned __int16 v8; // [sp+3Ah] [bp-4Eh]@4
  int v9; // [sp+3Ch] [bp-4Ch]@4
  unsigned __int16 v10; // [sp+40h] [bp-48h]@4
  unsigned int v11; // [sp+42h] [bp-46h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v13; // [sp+65h] [bp-23h]@4
  CPlayer *v14; // [sp+90h] [bp+8h]@1

  v14 = this;
  v3 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szMsg = byStorageCode;
  v7 = pItem->m_byTableCode;
  v8 = pItem->m_wItemIndex;
  v9 = pItem->m_dwDur;
  v10 = pItem->m_wSerial;
  v11 = pItem->m_dwLv;
  pbyType = 17;
  v13 = 15;
  CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_ObjID.m_wIndex, &pbyType, &szMsg, 0xEu);
}
