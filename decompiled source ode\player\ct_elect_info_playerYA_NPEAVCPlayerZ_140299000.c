/*
 * Function: ?ct_elect_info_player@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140299000
 */

char __fastcall ct_elect_info_player(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@8
  char *v5; // rax@12
  __int64 v6; // [sp+0h] [bp-2E8h]@1
  bool bFilter[4]; // [sp+20h] [bp-2C8h]@8
  char *pwszMessage; // [sp+28h] [bp-2C0h]@8
  char byPvpGrade[4]; // [sp+30h] [bp-2B8h]@8
  char *pwszSender; // [sp+38h] [bp-2B0h]@8
  int v11; // [sp+40h] [bp-2A8h]@8
  unsigned int v12; // [sp+50h] [bp-298h]@4
  unsigned int v13; // [sp+54h] [bp-294h]@4
  unsigned __int16 v14; // [sp+58h] [bp-290h]@4
  unsigned __int8 v15; // [sp+5Ch] [bp-28Ch]@4
  int v16; // [sp+60h] [bp-288h]@4
  char Dest; // [sp+78h] [bp-270h]@4
  char v18; // [sp+79h] [bp-26Fh]@4
  char DstBuf; // [sp+B0h] [bp-238h]@4
  char v20; // [sp+B1h] [bp-237h]@4
  CPlayer *v21; // [sp+2B8h] [bp-30h]@10
  int j; // [sp+2C0h] [bp-28h]@10
  unsigned __int64 v23; // [sp+2D0h] [bp-18h]@4
  CPlayer *v24; // [sp+2F0h] [bp+8h]@1

  v24 = pOne;
  v1 = &v6;
  for ( i = 184i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v23 = (unsigned __int64)&v6 ^ _security_cookie;
  v12 = 0;
  v13 = 0;
  v14 = 0;
  v15 = 0;
  v16 = 0;
  Dest = 0;
  memset(&v18, 0, 0x10ui64);
  DstBuf = 0;
  memset(&v20, 0, 0x1FFui64);
  if ( s_nWordCount > 0 )
  {
    if ( s_nWordCount >= 1 )
    {
      v21 = 0i64;
      for ( j = 0; j < 2532; ++j )
      {
        v21 = &g_Player + j;
        v5 = CPlayerDB::GetCharNameW(&v21->m_Param);
        if ( !strcmp_0(v5, s_pwszDstCheat[0]) )
        {
          v12 = v21->m_pUserDB->m_AvatorData.dbSupplement.dwAccumPlayTime;
          v14 = v21->m_pUserDB->m_AvatorData.dbSupplement.wScanerCnt;
          v15 = v21->m_pUserDB->m_AvatorData.dbSupplement.VoteEnable;
          v16 = v21->m_pUserDB->m_AvatorData.dbAvator.m_bOverlapVote;
          v13 = v24->m_pUserDB->m_AvatorData.dbSupplement.dwLastResetDate;
          strcpy_0(&Dest, s_pwszDstCheat[0]);
          v11 = v16;
          LODWORD(pwszSender) = v15;
          *(_DWORD *)byPvpGrade = v14;
          LODWORD(pwszMessage) = v12;
          *(_DWORD *)bFilter = v13;
          sprintf_s(
            &DstBuf,
            0x200ui64,
            "Player Vote Info : Name = %s, ResetTime = %d, AccTime = %d, Scaner = %d, Enable = %d, Over = %d",
            &Dest);
          goto LABEL_16;
        }
      }
    }
    sprintf_s(&DstBuf, 0x200ui64, "Player Search Fail!! -> Name = %s", s_pwszDstCheat[0]);
  }
  else
  {
    if ( !v24 || !v24->m_bOper )
      return 0;
    v12 = v24->m_pUserDB->m_AvatorData.dbSupplement.dwAccumPlayTime;
    v14 = v24->m_pUserDB->m_AvatorData.dbSupplement.wScanerCnt;
    v15 = v24->m_pUserDB->m_AvatorData.dbSupplement.VoteEnable;
    v16 = v24->m_pUserDB->m_AvatorData.dbAvator.m_bOverlapVote;
    v13 = v24->m_pUserDB->m_AvatorData.dbSupplement.dwLastResetDate;
    v4 = CPlayerDB::GetCharNameW(&v24->m_Param);
    strcpy_0(&Dest, v4);
    v11 = v16;
    LODWORD(pwszSender) = v15;
    *(_DWORD *)byPvpGrade = v14;
    LODWORD(pwszMessage) = v12;
    *(_DWORD *)bFilter = v13;
    sprintf_s(
      &DstBuf,
      0x200ui64,
      "Player Vote Info : Name = %s, Token = %d, AccTime = %d, Scaner = %d, Enable = %d, Over = %d",
      &Dest);
  }
LABEL_16:
  CLogFile::Write(&s_logCheat, &DstBuf);
  CPlayer::SendData_ChatTrans(v24, 0, 0xFFFFFFFF, -1, 0, &DstBuf, -1, 0i64);
  return 1;
}
