/*
 * Function: ?CheckAutoRecoverHP@CMonster@@QEAAXXZ
 * Address: 0x140143370
 */

void __fastcall CMonster::CheckAutoRecoverHP(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@7
  float v4; // xmm0_4@9
  __int64 v5; // [sp+0h] [bp-48h]@1
  int v6; // [sp+20h] [bp-28h]@7
  CGameObjectVtbl *v7; // [sp+28h] [bp-20h]@7
  CGameObjectVtbl *v8; // [sp+30h] [bp-18h]@9
  CMonster *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v9->m_pMonRec->m_fHPRecDelay > 0.0
    && v9->m_pMonRec->m_fHPRecUnit > 0.0
    && v9->m_pMonRec->m_bMonsterCondition == 1 )
  {
    v6 = ((int (__fastcall *)(CMonster *))v9->vfptr->GetHP)(v9);
    v7 = v9->vfptr;
    v3 = ((int (__fastcall *)(CMonster *))v7->GetMaxHP)(v9);
    if ( v6 < v3
      && (float)(signed int)(GetLoopTime() - v9->m_dwLastRecoverTime) >= (float)(v9->m_pMonRec->m_fHPRecDelay * 1000.0) )
    {
      v9->m_dwLastRecoverTime = GetLoopTime();
      v4 = (float)((int (__fastcall *)(CMonster *))v9->vfptr->GetHP)(v9) + v9->m_pMonRec->m_fHPRecUnit;
      v8 = v9->vfptr;
      ((void (__fastcall *)(CMonster *, _QWORD, _QWORD))v8->SetHP)(v9, (unsigned int)(signed int)ffloor(v4), 0i64);
    }
  }
  CMonster::AutoRecover(v9);
}
