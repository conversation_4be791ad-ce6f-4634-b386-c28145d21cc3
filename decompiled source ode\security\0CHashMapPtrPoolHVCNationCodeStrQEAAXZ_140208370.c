/*
 * Function: ??0?$CHashMapPtrPool@HVCNationCodeStr@@@@QEAA@XZ
 * Address: 0x140208370
 */

void __fastcall CHashMapPtrPool<int,CNationCodeStr>::CHashMapPtrPool<int,CNationCodeStr>(CHashMapPtrPool<int,CNationCodeStr> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CHashMapPtrPool<int,CNationCodeStr> *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_bCleanUp = 1;
  stdext::hash_map<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationCodeStr *>>>::hash_map<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationCodeStr *>>>(&v4->m_mapData);
}
