/*
 * Function: ??OCell@LendItemSheet@@QEBA_NAEBU01@@Z
 * Address: 0x14030FCD0
 */

bool __fastcall LendItemSheet::Cell::operator>(LendItemSheet::Cell *this, LendItemSheet::Cell *rhs)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // [sp+0h] [bp-18h]@1
  LendItemSheet::Cell *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  return v6->_pkItem->m_dwT > rhs->_pkItem->m_dwT;
}
