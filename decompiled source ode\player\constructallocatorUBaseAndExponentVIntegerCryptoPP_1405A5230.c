/*
 * Function: ?construct@?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@QEAAXPEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@AEBU34@@Z
 * Address: 0x1405A5230
 */

int __fastcall std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>::construct(__int64 a1, __int64 a2, __int64 a3)
{
  return std::_Construct<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>(
           a2,
           a3);
}
