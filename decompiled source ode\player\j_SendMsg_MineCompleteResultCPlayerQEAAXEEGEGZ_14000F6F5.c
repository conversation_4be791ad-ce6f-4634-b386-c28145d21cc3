/*
 * Function: j_?SendMsg_MineCompleteResult@CPlayer@@QEAAXEEGEG@Z
 * Address: 0x14000F6F5
 */

void __fastcall CPlayer::SendMsg_MineCompleteResult(CPlayer *this, char byErrCode, char byNewOreIndex, unsigned __int16 dwOreSerial, char byOreDur, unsigned __int16 dwBatteryLeftDurPoint)
{
  CPlayer::SendMsg_MineCompleteResult(this, byErrCode, byNewOreIndex, dwOreSerial, byOreDur, dwBatteryLeftDurPoint);
}
