/*
 * Function: ?DrawMapAlpha<PERSON><PERSON>@CLevel@@QEAAXQEAM@Z
 * Address: 0x1404E08B0
 */

void __fastcall CLevel::DrawMapAlphaRender(CLevel *this, float *const a2)
{
  float *v2; // rdi@1
  CLevel *v3; // rbx@1

  v2 = a2;
  v3 = this;
  if ( this->mIsLoadedBsp )
  {
    ResetVertexBufferCache();
    *v2 = dword_184A79B1C;
    *((_DWORD *)v2 + 1) = dword_184A79B20;
    *((_DWORD *)v2 + 2) = dword_184A79B24;
    CBsp::DrawAlphaRender(v3->mBsp, v2);
  }
}
