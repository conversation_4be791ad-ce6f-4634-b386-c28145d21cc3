/*
 * Function: ?pc_GuildHonorListRequest@CPlayer@@QEAAXE@Z
 * Address: 0x1400AB9E0
 */

void __fastcall CPlayer::pc_GuildHonorListRequest(CPlayer *this, char byUI)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CHonorGuild *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  CPlayer *v7; // [sp+40h] [bp+8h]@1
  char v8; // [sp+48h] [bp+10h]@1

  v8 = byUI;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = CPlayerDB::GetRaceCode(&v7->m_Param);
  v4 = CHonorGuild::Instance();
  CHonorGuild::SendCurrHonorGuildList(v4, v7->m_ObjID.m_wIndex, v6, v8);
}
