/*
 * Function: ?db_Insert_guild@CMainThread@@QEAAEPEAKPEADE0@Z
 * Address: 0x1401B0C80
 */

char __fastcall CMainThread::db_Insert_guild(CMainThread *this, unsigned int *dwSerial, char *pwszGuildName, char byRace, unsigned int *dwGuildSerial)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-68h]@1
  CCheckSumGuildData v9; // [sp+28h] [bp-40h]@8
  int j; // [sp+44h] [bp-24h]@12
  char v11; // [sp+48h] [bp-20h]@9
  char v12; // [sp+49h] [bp-1Fh]@11
  char v13; // [sp+4Ah] [bp-1Eh]@16
  char v14; // [sp+4Bh] [bp-1Dh]@18
  __int64 v15; // [sp+50h] [bp-18h]@4
  CMainThread *v16; // [sp+70h] [bp+8h]@1
  unsigned int *v17; // [sp+78h] [bp+10h]@1
  char *pwszGuildNamea; // [sp+80h] [bp+18h]@1

  pwszGuildNamea = pwszGuildName;
  v17 = dwSerial;
  v16 = this;
  v5 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v15 = -2i64;
  if ( CRFWorldDatabase::Insert_Guild(v16->m_pWorldDB, pwszGuildName, byRace) )
  {
    if ( CRFWorldDatabase::Select_GuildSerial(v16->m_pWorldDB, pwszGuildNamea, dwGuildSerial) )
    {
      CCheckSumGuildData::CCheckSumGuildData(&v9, *dwGuildSerial);
      CCheckSumGuildData::Encode(&v9, 0.0, 0.0);
      if ( CCheckSumGuildData::Insert(&v9, v16->m_pWorldDB) )
      {
        if ( CRFWorldDatabase::Insert_WeeklyGuildPvpPointSum(v16->m_pWorldDB, *dwGuildSerial) )
        {
          for ( j = 0; j < 8 && v17[j] != -1; ++j )
          {
            if ( !CRFWorldDatabase::Update_UserGuildData(v16->m_pWorldDB, v17[j], *dwGuildSerial, 0) )
            {
              v13 = 24;
              CCheckSumGuildData::~CCheckSumGuildData(&v9);
              return v13;
            }
          }
          v14 = 0;
          CCheckSumGuildData::~CCheckSumGuildData(&v9);
          result = v14;
        }
        else
        {
          v12 = 24;
          CCheckSumGuildData::~CCheckSumGuildData(&v9);
          result = v12;
        }
      }
      else
      {
        v11 = 24;
        CCheckSumGuildData::~CCheckSumGuildData(&v9);
        result = v11;
      }
    }
    else
    {
      result = 24;
    }
  }
  else
  {
    result = 24;
  }
  return result;
}
