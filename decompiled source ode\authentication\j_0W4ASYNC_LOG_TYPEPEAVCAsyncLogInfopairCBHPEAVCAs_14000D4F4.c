/*
 * Function: j_??$?0W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@QEAA@AEBU?$pair@W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@1@@Z
 * Address: 0x14000D4F4
 */

void __fastcall std::pair<int const,CAsyncLogInfo *>::pair<int const,CAsyncLogInfo *>(std::pair<int const ,CAsyncLogInfo *> *this, std::pair<enum ASYNC_LOG_TYPE,CAsyncLogInfo *> *_Right)
{
  std::pair<int const,CAsyncLogInfo *>::pair<int const,CAsyncLogInfo *>(this, _Right);
}
