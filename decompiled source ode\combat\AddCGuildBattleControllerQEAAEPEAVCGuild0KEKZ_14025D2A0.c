/*
 * Function: ?Add@CGuildBattleController@@QEAAEPEAVCGuild@@0KEK@Z
 * Address: 0x14025D2A0
 */

char __fastcall CGuildBattleController::Add(CGuildBattleController *this, CGuild *pSrcGuild, CGuild *pDestGuild, unsigned int dwStartTime, char by<PERSON><PERSON><PERSON>, unsigned int dwMapInx)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-48h]@1
  CGuildBattleController *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v6 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  return CGuildBattleController::Add(v10, pSrcGuild, pDestGuild, dwStartTime, 1u, byN<PERSON>ber, dwMapInx);
}
