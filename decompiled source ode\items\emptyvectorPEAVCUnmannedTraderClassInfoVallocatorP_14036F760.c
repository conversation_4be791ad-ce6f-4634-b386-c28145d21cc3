/*
 * Function: ?empty@?$vector@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@QEBA_NXZ
 * Address: 0x14036F760
 */

bool __fastcall std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::empty(std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::size(v5) == 0;
}
