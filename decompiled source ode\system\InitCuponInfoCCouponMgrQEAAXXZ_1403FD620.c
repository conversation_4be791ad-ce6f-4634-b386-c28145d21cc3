/*
 * Function: ?InitCuponInfo@CCouponMgr@@QEAAXXZ
 * Address: 0x1403FD620
 */

void __fastcall CCouponMgr::InitCuponInfo(CCouponMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int n; // [sp+20h] [bp-18h]@4
  _base_fld *v5; // [sp+28h] [bp-10h]@7
  CCouponMgr *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( n = 0; n < 5; ++n )
  {
    v5 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 36, n);
    if ( v5 )
    {
      v6->m_Coupon[n].byTableCode = 36;
      v6->m_Coupon[n].dwIndex = v5->m_dwIndex;
    }
  }
}
