/*
 * Function: ?Instance@?$CTS<PERSON>leton@VCNationSettingManager@@@@SAPEAVCNationSettingManager@@XZ
 * Address: 0x140028FB0
 */

CNationSettingManager *__cdecl CTSingleton<CNationSettingManager>::Instance()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // rax@6
  __int64 v4; // [sp+0h] [bp-58h]@1
  __int64 v5; // [sp+20h] [bp-38h]@10
  __int64 v6; // [sp+30h] [bp-28h]@8
  CNationSettingManager *v7; // [sp+38h] [bp-20h]@5
  __int64 v8; // [sp+40h] [bp-18h]@4
  __int64 v9; // [sp+48h] [bp-10h]@6

  v0 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  v8 = -2i64;
  if ( !CTSingleton<CNationSettingManager>::ms_instance )
  {
    v7 = (CNationSettingManager *)operator new(0x10ui64);
    if ( v7 )
    {
      CNationSettingManager::CNationSettingManager(v7);
      v9 = v2;
    }
    else
    {
      v9 = 0i64;
    }
    v6 = v9;
    CTSingleton<CNationSettingManager>::ms_instance = v9;
  }
  if ( !CTSingleton<CNationSettingManager>::ms_instance )
  {
    v5 = 0i64;
    if ( _CrtDbgReportW(2i64, L"../Common\\TSingleton.h", 18i64, 0i64) == 1 )
      __debugbreak();
  }
  return (CNationSettingManager *)CTSingleton<CNationSettingManager>::ms_instance;
}
