/*
 * Function: ?Change_BillingType@CBilling@@QEAAXPEAD0FJPEAU_SYSTEMTIME@@E@Z
 * Address: 0x14028D170
 */

void __fastcall CBilling::Change_BillingType(CBilling *this, char *szID, char *szCMSCode, __int16 iType, int lRemainTime, _SYSTEMTIME *pstEndDate, char byReason)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-48h]@1
  _SYSTEMTIME *v10; // [sp+20h] [bp-28h]@10
  CPlayer *v11; // [sp+30h] [bp-18h]@5
  CBilling *v12; // [sp+50h] [bp+8h]@1
  char *szCMSCodea; // [sp+60h] [bp+18h]@1
  __int16 v14; // [sp+68h] [bp+20h]@1

  v14 = iType;
  szCMSCodea = szCMSCode;
  v12 = this;
  v7 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v7 = -*********;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  if ( v12->m_bOper )
  {
    v11 = GetPtrPlayerFromAccount(&g_Player, 2532, szID);
    if ( v11 )
    {
      if ( v11->m_pUserDB->m_BillingInfo.iType == 6 || v11->m_pUserDB->m_BillingInfo.iType == 7 )
      {
        CPlayer::SendMsg_BillingTypeChangeInform(v11, v14, lRemainTime, pstEndDate, byReason);
        if ( v14 != 6 && v14 != 7 )
        {
          v10 = pstEndDate;
          CUserDB::SetBillingData(v11->m_pUserDB, 0i64, v14, lRemainTime, pstEndDate);
        }
        else
        {
          v10 = pstEndDate;
          CUserDB::SetBillingData(v11->m_pUserDB, szCMSCodea, v14, lRemainTime, pstEndDate);
        }
      }
    }
  }
}
