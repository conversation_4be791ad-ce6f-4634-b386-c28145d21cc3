/*
 * Function: ??0CCheckSumCharacAccountTrunkData@@QEAA@KKE@Z
 * Address: 0x1402C06A0
 */

void __fastcall CCheckSumCharacAccountTrunkData::CCheckSumCharacAccountTrunkData(CCheckSumCharacAccountTrunkData *this, unsigned int dwSerial, unsigned int dwAccountSerial, char byRace)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CCheckSumCharacAccountTrunkData *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v4 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7->m_dwSerial = dwSerial;
  v7->m_dwAccountSerial = dwAccountSerial;
  v7->m_byRace = byRace;
  memset_0(v7->m_dwValues, 0, 0x18ui64);
  memset_0(v7->m_dValues, 0, 0x10ui64);
}
