/*
 * Function: ?Select_CashLimSale@CRFWorldDatabase@@QEAAHPEAU_worlddb_cash_limited_sale@@@Z
 * Address: 0x1404C6780
 */

signed __int64 __fastcall CRFWorldDatabase::Select_CashLimSale(CRFWorldDatabase *this, _worlddb_cash_limited_sale *pcashlimitedsale)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v5; // [sp+0h] [bp-498h]@1
  void *SQLStmt; // [sp+20h] [bp-478h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-470h]@22
  char _Dest[1024]; // [sp+40h] [bp-458h]@4
  SQLLEN v9; // [sp+458h] [bp-40h]@22
  __int16 v10; // [sp+464h] [bp-34h]@9
  unsigned __int8 v11; // [sp+468h] [bp-30h]@16
  int j; // [sp+46Ch] [bp-2Ch]@22
  int v13; // [sp+470h] [bp-28h]@22
  unsigned __int64 v14; // [sp+480h] [bp-18h]@4
  CRFWorldDatabase *v15; // [sp+4A0h] [bp+8h]@1
  _worlddb_cash_limited_sale *TargetValue; // [sp+4A8h] [bp+10h]@1

  TargetValue = pcashlimitedsale;
  v15 = this;
  v2 = &v5;
  for ( i = 292i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v14 = (unsigned __int64)&v5 ^ _security_cookie;
  sprintf_s<1024>((char (*)[1024])_Dest, "{ CALL pSelect_Cash_LimSale }");
  if ( v15->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v15->vfptr, _Dest);
  if ( v15->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v15->vfptr) )
  {
    v10 = SQLExecDirect_0(v15->m_hStmtSelect, _Dest, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v15->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v10, _Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v10, v15->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v10 = SQLFetch_0(v15->m_hStmtSelect);
      if ( v10 && v10 != 1 )
      {
        v11 = 0;
        if ( v10 == 100 )
        {
          v11 = 2;
        }
        else
        {
          SQLStmt = v15->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v10, _Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v10, v15->m_hStmtSelect);
          v11 = 1;
        }
        if ( v15->m_hStmtSelect )
          SQLCloseCursor_0(v15->m_hStmtSelect);
        result = v11;
      }
      else
      {
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 1u, -6, TargetValue, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 2u, -6, &TargetValue->byLimited_sale_num, 0i64, &v9);
        v13 = 2;
        for ( j = 0; j < TargetValue->byLimited_sale_num; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v15->m_hStmtSelect, v13++, 4, &TargetValue->List[j], 0i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v15->m_hStmtSelect, v13, 4, &TargetValue->List[j].nLimcount, 0i64, &v9);
        }
        if ( v15->m_hStmtSelect )
          SQLCloseCursor_0(v15->m_hStmtSelect);
        if ( v15->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v15->vfptr, "%s Success", _Dest);
        result = 0i64;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v15->vfptr, "ReConnectDataBase Fail. Query : %s", _Dest);
    result = 1i64;
  }
  return result;
}
