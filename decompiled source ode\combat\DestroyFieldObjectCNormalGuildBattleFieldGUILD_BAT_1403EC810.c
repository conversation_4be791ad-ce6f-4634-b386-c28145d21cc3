/*
 * Function: ?DestroyFieldObject@CNormalGuildBattleField@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403EC810
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleField::DestroyFieldObject(GUILD_BATTLE::CNormalGuildBattleField *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleField *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < v5->m_ui1PGoalPosCnt; ++j )
    CCircleZone::Destroy(&v5->m_pk1PGoalZone[j]);
  for ( j = 0; j < v5->m_ui2PGoalPosCnt; ++j )
    CCircleZone::Destroy(&v5->m_pk2PGoalZone[j]);
  for ( j = 0; j < v5->m_uiRegenPosCnt; ++j )
    CGravityStoneRegener::Destroy(&v5->m_pkRegenPos[j]);
}
