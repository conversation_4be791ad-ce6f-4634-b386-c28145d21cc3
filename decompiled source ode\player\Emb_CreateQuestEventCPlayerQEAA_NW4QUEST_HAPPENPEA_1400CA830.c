/*
 * Function: ?Emb_CreateQuestEvent@CPlayer@@QEAA_NW4QUEST_HAPPEN@@PEAD@Z
 * Address: 0x1400CA830
 */

char __fastcall CPlayer::Emb_CreateQuestEvent(CPlayer *this, QUEST_HAPPEN HappenType, char *pszEventCode)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // al@4
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-68h]@1
  void *Src; // [sp+20h] [bp-48h]@4
  _happen_event_cont Dst; // [sp+38h] [bp-30h]@6
  int j; // [sp+54h] [bp-14h]@8
  CPlayer *v11; // [sp+70h] [bp+8h]@1
  QUEST_HAPPEN HappenTypea; // [sp+78h] [bp+10h]@1
  char *pszEventCodea; // [sp+80h] [bp+18h]@1

  pszEventCodea = pszEventCode;
  HappenTypea = HappenType;
  v11 = this;
  v3 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = CPlayerDB::GetRaceCode(&v11->m_Param);
  Src = CQuestMgr::CheckQuestHappenEvent(&v11->m_QuestMgr, HappenTypea, pszEventCodea, v5);
  if ( Src )
  {
    _happen_event_cont::_happen_event_cont(&Dst);
    memcpy_0(&Dst, Src, 0x18ui64);
    if ( Dst.m_pEvent->m_bSelectQuestManual )
    {
      CPlayer::SendMsg_SelectWaitedQuest(v11, HappenTypea, Dst.m_nIndexInType, Dst.m_nRaceCode);
      result = 1;
    }
    else if ( CPlayer::Emb_StartQuest(v11, -1, &Dst) )
    {
      result = 1;
    }
    else
    {
      for ( j = 0; j < 3; ++j )
      {
        if ( !_happen_event_cont::isset(&v11->m_QuestMgr.m_pTempHappenEvent[j]) )
        {
          memcpy_0(&v11->m_QuestMgr.m_pTempHappenEvent[j], &Dst, 0x18ui64);
          break;
        }
      }
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
