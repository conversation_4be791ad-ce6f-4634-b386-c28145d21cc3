/*
 * Function: ?Delete_PatriarchComm@CRFWorldDatabase@@QEAAEKPEAD@Z
 * Address: 0x1404C0120
 */

bool __fastcall CRFWorldDatabase::Delete_PatriarchComm(CRFWorldDatabase *this, unsigned int dwSerial, char *pszDepDate)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-D8h]@1
  char Dest; // [sp+30h] [bp-A8h]@4
  unsigned __int64 v8; // [sp+C0h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+E0h] [bp+8h]@1

  v9 = this;
  v3 = &v6;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = (unsigned __int64)&v6 ^ _security_cookie;
  sprintf(&Dest, "{ CALL pDelete_PatriarchComm( %d, '%s') }", dwSerial, pszDepDate);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &Dest, 1);
}
