/*
 * Function: ?UpdateServerResetToken@CRFWorldDatabase@@QEAA_NKGK@Z
 * Address: 0x1404C7510
 */

bool __fastcall CRFWorldDatabase::UpdateServerResetToken(CRFWorldDatabase *this, unsigned int dwToken, unsigned __int16 wProcType, unsigned int dwESerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-468h]@1
  int v8; // [sp+20h] [bp-448h]@4
  unsigned int v9; // [sp+28h] [bp-440h]@4
  char DstBuf; // [sp+40h] [bp-428h]@4
  char v11; // [sp+41h] [bp-427h]@4
  unsigned __int64 v12; // [sp+450h] [bp-18h]@4
  CRFWorldDatabase *v13; // [sp+470h] [bp+8h]@1

  v13 = this;
  v4 = &v7;
  for ( i = 280i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = (unsigned __int64)&v7 ^ _security_cookie;
  DstBuf = 0;
  memset(&v11, 0, 0x3FFui64);
  v9 = dwESerial;
  v8 = wProcType;
  sprintf_s(&DstBuf, 0x400ui64, "{ CALL pUpdate_Patriarch_Elect_ResetTime( %d, %d, %d ) }", dwToken);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v13->vfptr, &DstBuf, 1);
}
