/*
 * Function: ?pc_GuildCancelSuggestRequest@CPlayer@@QEAAXK@Z
 * Address: 0x1400A8350
 */

void __fastcall CPlayer::pc_GuildCancelSuggestRequest(CPlayer *this, unsigned int dwMatterVoteSynKey)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  CGuild *v6; // [sp+28h] [bp-10h]@4
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = 0;
  v6 = v7->m_Param.m_pGuild;
  if ( v6 )
  {
    if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v7->m_id.wIndex) == 99 )
    {
      v5 = 106;
    }
    else if ( v6->m_dwSuggesterSerial != v7->m_dwObjSerial )
    {
      v5 = -53;
    }
  }
  else
  {
    v5 = -54;
  }
  if ( !v5 )
    CGuild::CancelSuggestedMatter(v6);
  CPlayer::SendMsg_CancelSuggestResult(v7, v5);
}
