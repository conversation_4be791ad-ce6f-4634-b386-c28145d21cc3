/*
 * Function: ??0<PERSON><PERSON><PERSON>@@QEAA@XZ
 * Address: 0x140172230
 */

void __fastcall CCharacter::CCharacter(CCharacter *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CCharacter *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CGameObject::CGameObject((CGameObject *)&v5->vfptr);
  v5->vfptr = (CGameObjectVtbl *)&CCharacter::`vftable';
  `vector constructor iterator'(v5->m_SFCont, 0x30ui64, 16, (void *(__cdecl *)(void *))_sf_continous::_sf_continous);
  `vector constructor iterator'(v5->m_SFContAura, 0x30ui64, 16, (void *(__cdecl *)(void *))_sf_continous::_sf_continous);
  _effect_parameter::_effect_parameter(&v5->m_EP);
  CMyTimer::CMyTimer(&v5->m_tmrSFCont);
}
