/*
 * Function: ?CompleteOutGuildbattleCost@CGuild@@QEAAXKKKK@Z
 * Address: 0x140257B00
 */

void __fastcall CGuild::CompleteOutGuildbattleCost(CGuild *this, unsigned int dwSrcGuildSerial, unsigned int dwStartTimeInx, unsigned int dwMemberCntInx, unsigned int dwMapInx)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  unsigned int dwMapIdx; // [sp+20h] [bp-28h]@4
  char v9; // [sp+30h] [bp-18h]@4
  CGuild *v10; // [sp+50h] [bp+8h]@1
  unsigned int dwSrcGuildSeriala; // [sp+58h] [bp+10h]@1
  unsigned int dwStartTimeInxa; // [sp+60h] [bp+18h]@1
  unsigned int dwMemberCntInxa; // [sp+68h] [bp+20h]@1

  dwMemberCntInxa = dwMemberCntInx;
  dwStartTimeInxa = dwStartTimeInx;
  dwSrcGuildSeriala = dwSrcGuildSerial;
  v10 = this;
  v5 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  dwMapIdx = dwMapInx;
  v9 = CGuild::CheckGuildBattleSuggestRequestToDestGuild(
         v10->m_GuildBattleSugestMatter.pkDest,
         v10->m_dwSerial,
         dwStartTimeInx,
         dwMemberCntInx,
         dwMapInx);
  if ( v9 )
  {
    CGuild::PushDQSInGuildBattleCost(v10->m_GuildBattleSugestMatter.pkSrc);
  }
  else if ( CGuild::SendMsg_GuildBattleProposed(v10->m_GuildBattleSugestMatter.pkDest, v10->m_wszName) )
  {
    dwMapIdx = dwMapInx;
    CGuild::SetGuildBattleMatter(
      v10->m_GuildBattleSugestMatter.pkDest,
      dwSrcGuildSeriala,
      dwStartTimeInxa,
      dwMemberCntInxa,
      dwMapInx);
    v10->m_GuildBattleSugestMatter.eState = 2;
  }
  else
  {
    v9 = -77;
    CGuild::PushDQSInGuildBattleCost(v10->m_GuildBattleSugestMatter.pkSrc);
  }
  CGuild::SendMsg_GuildBattleSuggestResult(v10, v9, v10->m_GuildBattleSugestMatter.pkDest->m_wszName);
  _guild_battle_suggest_matter::Clear(&v10->m_GuildBattleSugestMatter);
}
