/*
 * Function: ?Load_Golden_Box_Item_Event@CGoldenBoxItemMgr@@QEAA_NXZ
 * Address: 0x1404121A0
 */

bool __fastcall CGoldenBoxItemMgr::Load_Golden_Box_Item_Event(CGoldenBoxItemMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CGoldenBoxItemMgr *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CNetTimer::BeginTimer(&v5->m_golden_box_event.m_event_timer, 0x1D4C0u);
  CLogFile::Write(&v5->m_golden_box_event.m_event_log, "Server Start and LogFile Loading");
  CGoldenBoxItemMgr::Set_Event_Status(v5, 0);
  if ( CGoldenBoxItemMgr::Load_Event_INI(v5, &v5->m_golden_box_event.m_ini) )
  {
    CGoldenBoxItemMgr::Check_Loaded_Event_Status(v5);
    result = !CGoldenBoxItemMgr::Get_Event_Status(v5) || CGoldenBoxItemMgr::SetGoldBoxItemIndex(v5);
  }
  else
  {
    MyMessageBox("CGoldenBoxItemMgr::Load_Event_INI() : ", "Load_Golden_Box_Item_Event() Fail!");
    result = 0;
  }
  return result;
}
