/*
 * Function: j_??0?$_Ranit@VCUnmannedTraderRegistItemInfo@@_JPEBV1@AEBV1@@std@@QEAA@XZ
 * Address: 0x14000D3AA
 */

void __fastcall std::_<PERSON>t<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &>::_<PERSON>t<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &>(std::_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &> *this)
{
  std::_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &>::_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &>(this);
}
