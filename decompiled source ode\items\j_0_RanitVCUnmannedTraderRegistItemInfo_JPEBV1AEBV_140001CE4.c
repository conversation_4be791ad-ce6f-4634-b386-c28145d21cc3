/*
 * Function: j_??0?$_Ranit@VCUnmannedTraderRegistItemInfo@@_JPEBV1@AEBV1@@std@@QEAA@AEBU01@@Z
 * Address: 0x140001CE4
 */

void __fastcall std::_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &>::_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &>(std::_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &> *this, std::_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &> *__that)
{
  std::_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &>::_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &>(
    this,
    __that);
}
