/*
 * Function: j_?allocate@?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@QEAAPEAPEAVCUnmannedTraderDivisionInfo@@_K@Z
 * Address: 0x14000359E
 */

CUnmannedTraderDivisionInfo **__fastcall std::allocator<CUnmannedTraderDivisionInfo *>::allocate(std::allocator<CUnmannedTraderDivisionInfo *> *this, unsigned __int64 _Count)
{
  return std::allocator<CUnmannedTraderDivisionInfo *>::allocate(this, _Count);
}
