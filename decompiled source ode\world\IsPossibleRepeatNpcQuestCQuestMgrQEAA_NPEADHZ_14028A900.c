/*
 * Function: ?IsPossibleRepeatNpcQuest@CQuestMgr@@QEAA_NPEADH@Z
 * Address: 0x14028A900
 */

bool __fastcall CQuestMgr::IsPossibleRepeatNpcQuest(CQuestMgr *this, char *pszCode, int nLinkQuestGroupID)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  int v7; // [sp+20h] [bp-28h]@4
  int j; // [sp+24h] [bp-24h]@4
  char *szRecordCode; // [sp+28h] [bp-20h]@7
  _base_fld *v10; // [sp+30h] [bp-18h]@8
  unsigned int v11; // [sp+38h] [bp-10h]@12
  CQuestMgr *v12; // [sp+50h] [bp+8h]@1
  int v13; // [sp+60h] [bp+18h]@1

  v13 = nLinkQuestGroupID;
  v12 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = 0;
  for ( j = 0; j < 70; ++j )
  {
    szRecordCode = v12->m_pQuestData->m_History[j].szQuestCode;
    if ( (unsigned __int8)szRecordCode[12] != 255 )
    {
      v10 = 0i64;
      v10 = CRecordData::GetRecord(CQuestMgr::s_tblQuest, szRecordCode);
      if ( (!v10 || *(_DWORD *)&v10[27].m_strCode[24] == v13) && v10 && *(_DWORD *)&v10[27].m_strCode[24] == v13 )
      {
        v11 = GetConnectTime_AddBySec(0);
        if ( *(_DWORD *)(szRecordCode + 13) >= v11 )
          ++v7;
      }
    }
  }
  return v7 == 0;
}
