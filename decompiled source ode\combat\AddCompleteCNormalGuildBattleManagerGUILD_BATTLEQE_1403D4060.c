/*
 * Function: ?AddComplete@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAXEK@Z
 * Address: 0x1403D4060
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleManager::AddComplete(GUILD_BATTLE::CNormalGuildBattleManager *this, char byRet, unsigned int dwBattleID)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleManager *v6; // [sp+30h] [bp+8h]@1
  char v7; // [sp+38h] [bp+10h]@1
  unsigned int v8; // [sp+40h] [bp+18h]@1

  v8 = dwBattleID;
  v7 = byRet;
  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v6->m_ppkNormalBattle && v6->m_uiMaxBattleCnt > dwBattleID && v6->m_ppkNormalBattle[dwBattleID] )
  {
    if ( byRet )
      GUILD_BATTLE::CNormalGuildBattle::Clear(v6->m_ppkNormalBattle[dwBattleID]);
    GUILD_BATTLE::CNormalGuildBattle::AddComplete(v6->m_ppkNormalBattle[v8], v7);
  }
}
