/*
 * Function: j_??1?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEAA@XZ
 * Address: 0x14000987C
 */

void __fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::~vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this)
{
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::~vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(this);
}
