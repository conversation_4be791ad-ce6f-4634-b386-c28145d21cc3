/*
 * Function: pre_c_init
 * Address: 0x1404DD210
 */

// write access to const memory has been detected, the output may be wrong!
__int64 __cdecl pre_c_init()
{
  __int64 v0; // rax@1

  managedapp = check_managed_app();
  __set_app_type(2i64);
  LODWORD(v0) = _encode_pointer(-1i64);
  _onexitend = v0;
  _onexitbegin = v0;
  _fmode = fmode_0;
  _commode = commode_0;
  RTC_Initialize();
  setargv();
  if ( !_defaultmatherr )
    __setusermatherr(matherr);
  if ( _globallocalestatus == -1 )
    _configthreadlocale(-1);
  return 0i64;
}
