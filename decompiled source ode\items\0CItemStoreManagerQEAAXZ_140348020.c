/*
 * Function: ??0CItemStoreManager@@QEAA@XZ
 * Address: 0x140348020
 */

void __fastcall CItemStoreManager::CItemStoreManager(CItemStoreManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CItemStoreManager *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CMyTimer::CMyTimer(&v5->m_tmrCheckTime);
  CMyTimer::CMyTimer(&v5->m_tmrSaveTime);
  _qry_case_all_store_limit_item::_qry_case_all_store_limit_item(&v5->m_Sheet);
  v5->m_nInstanceItemStoreListNum = 0;
  v5->m_InstanceItemStoreList = 0i64;
  CRecordData::CRecordData(&v5->m_tblItemStore);
  v5->m_nMapItemStoreListNum = 0;
  v5->m_MapItemStoreList = 0i64;
}
