/*
 * Function: j_??$unchecked_uninitialized_copy@PEAPEAUScheduleMSG@@PEAPEAU1@V?$allocator@PEAUScheduleMSG@@@std@@@stdext@@YAPEAPEAUSchedule<PERSON>G@@PEAPEAU1@00AEAV?$allocator@PEAUScheduleMSG@@@std@@@Z
 * Address: 0x140004B97
 */

ScheduleMSG **__fastcall stdext::unchecked_uninitialized_copy<ScheduleMSG * *,ScheduleMSG * *,std::allocator<ScheduleMSG *>>(ScheduleMSG **_First, ScheduleMSG **_Last, ScheduleMSG **_Dest, std::allocator<ScheduleMSG *> *_Al)
{
  return stdext::unchecked_uninitialized_copy<ScheduleMSG * *,ScheduleMSG * *,std::allocator<ScheduleMSG *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
