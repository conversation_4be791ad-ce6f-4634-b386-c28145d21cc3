/*
 * Function: j_??$unchecked_fill_n@PEAPEAVCUnmannedTraderSubClassInfo@@_KPEAV1@@stdext@@YAXPEAPEAVCUnmannedTraderSubClassInfo@@_KAEBQEAV1@@Z
 * Address: 0x140010E15
 */

void __fastcall stdext::unchecked_fill_n<CUnmannedTraderSubClassInfo * *,unsigned __int64,CUnmannedTraderSubClassInfo *>(CUnmannedTraderSubClassInfo **_First, unsigned __int64 _Count, CUnmannedTraderSubClassInfo *const *_Val)
{
  stdext::unchecked_fill_n<CUnmannedTraderSubClassInfo * *,unsigned __int64,CUnmannedTraderSubClassInfo *>(
    _First,
    _Count,
    _Val);
}
