/*
 * Function: _CUnmannedTraderUserInfo::CheatCancelRegistSingle_::_1_::dtor$3
 * Address: 0x14035C5C0
 */

void __fastcall CUnmannedTraderUserInfo::CheatCancelRegistSingle_::_1_::dtor_3(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>((std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)(a2 + 880));
}
