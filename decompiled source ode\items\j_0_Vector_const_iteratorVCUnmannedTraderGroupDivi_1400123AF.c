/*
 * Function: j_??0?$_Vector_const_iterator@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@QEAA@PEAVCUnmannedTraderGroupDivisionVersionInfo@@@Z
 * Address: 0x1400123AF
 */

void __fastcall std::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(std::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *this, CUnmannedTraderGroupDivisionVersionInfo *_Ptr)
{
  std::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(
    this,
    _Ptr);
}
