/*
 * Function: ?_TowerReturn@CPlayer@@QEAAGPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1400A1A50
 */

__int16 __fastcall CPlayer::_TowerReturn(CPlayer *this, _STORAGE_LIST::_db_con *pTowerItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int16 result; // ax@5
  _STORAGE_LIST::_db_con *v5; // rax@10
  __int64 v6; // [sp+0h] [bp-48h]@1
  bool bUpdate; // [sp+20h] [bp-28h]@10
  bool bSend; // [sp+28h] [bp-20h]@10
  int j; // [sp+30h] [bp-18h]@6
  int nAlter; // [sp+34h] [bp-14h]@10
  __int16 v11; // [sp+38h] [bp-10h]@10
  CPlayer *v12; // [sp+50h] [bp+8h]@1

  v12 = this;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pTowerItem )
  {
    for ( j = 0; j < 6; ++j )
    {
      if ( v12->m_pmTwr.m_List[j].m_pTowerItem == pTowerItem )
      {
        v12->m_pmTwr.m_List[j].m_pTowerItem->m_bLock = 0;
        nAlter = v12->m_pmTwr.m_List[j].m_pTowerObj->m_nHP - LODWORD(v12->m_pmTwr.m_List[j].m_pTowerItem->m_dwDur);
        v5 = v12->m_pmTwr.m_List[j].m_pTowerItem;
        bSend = 0;
        bUpdate = 0;
        v11 = CPlayer::Emb_AlterDurPoint(v12, 0, v5->m_byStorageIndex, nAlter, 0, 0);
        CGuardTower::Destroy(v12->m_pmTwr.m_List[j].m_pTowerObj, 0, 0);
        _TOWER_PARAM::_list::init(&v12->m_pmTwr.m_List[j]);
        --v12->m_pmTwr.m_nCount;
        return v11;
      }
    }
    result = 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
