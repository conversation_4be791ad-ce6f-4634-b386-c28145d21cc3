/*
 * Function: ?dev_full_force@CPlayer@@QEAA_NXZ
 * Address: 0x1400BCD30
 */

bool __usercall CPlayer::dev_full_force@<al>(CPlayer *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp-20h] [bp-11F8h]@1
  char __t[4404]; // [sp+20h] [bp-11B8h]@4
  char v8; // [sp+1154h] [bp-84h]@4
  int v9; // [sp+1158h] [bp-80h]@4
  int n; // [sp+115Ch] [bp-7Ch]@4
  _base_fld *v11; // [sp+1160h] [bp-78h]@7
  char v12; // [sp+1168h] [bp-70h]@8
  int j; // [sp+116Ch] [bp-6Ch]@8
  char *v14; // [sp+1170h] [bp-68h]@11
  _STORAGE_LIST::_db_con Src; // [sp+1188h] [bp-50h]@16
  CPlayer *v16; // [sp+11E0h] [bp+8h]@1

  v16 = this;
  v2 = alloca(a2);
  v3 = &v6;
  for ( i = 1148i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  `vector constructor iterator'(__t, 0x32ui64, 88, (void *(__cdecl *)(void *))_STORAGE_LIST::_db_con::_db_con);
  v8 = 0;
  v9 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 15);
  for ( n = 0; n < v9; ++n )
  {
    v11 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 15, n);
    if ( !_STORAGE_LIST::GetNumEmptyCon((_STORAGE_LIST *)&v16->m_Param.m_dbForce.m_nListNum) )
      break;
    v12 = 0;
    for ( j = 0; j < 88; ++j )
    {
      v14 = &v16->m_Param.m_dbForce.m_pStorageList[j].m_bLoad;
      if ( *v14
        && *((_DWORD *)CPlayer::s_pnLinkForceItemToEffect + *(_WORD *)(v14 + 3)) == *((_DWORD *)CPlayer::s_pnLinkForceItemToEffect
                                                                                    + v11->m_dwIndex) )
      {
        v12 = 1;
        break;
      }
    }
    if ( !v12 )
    {
      _STORAGE_LIST::_db_con::_db_con(&Src);
      Src.m_byTableCode = 15;
      Src.m_wItemIndex = n;
      Src.m_dwDur = 0xFFFFFFi64;
      Src.m_wSerial = CPlayerDB::GetNewItemSerial(&v16->m_Param);
      Src.m_dwLv = 0xFFFFFFF;
      if ( CPlayer::Emb_AddStorage(v16, 3, (_STORAGE_LIST::_storage_con *)&Src.m_bLoad, 1, 1) )
        memcpy_0(&__t[50 * (unsigned __int8)v8++], &Src, 0x32ui64);
    }
  }
  CMgrAvatorItemHistory::cheat_add_item(
    &CPlayer::s_MgrItemHistory,
    v16->m_ObjID.m_wIndex,
    (_STORAGE_LIST::_db_con *)__t,
    v8,
    v16->m_szItemHistoryFileName);
  return v8 != 0;
}
