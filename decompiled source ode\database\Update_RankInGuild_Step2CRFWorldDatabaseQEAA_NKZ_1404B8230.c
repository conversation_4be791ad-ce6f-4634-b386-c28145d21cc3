/*
 * Function: ?Update_RankInGuild_Step2@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x1404B8230
 */

char __fastcall CRFWorldDatabase::Update_RankInGuild_Step2(CRFWorldDatabase *this, unsigned int dwGuildSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v5; // [sp+0h] [bp-28h]@1
  CRFWorldDatabase *v6; // [sp+30h] [bp+8h]@1
  unsigned int v7; // [sp+38h] [bp+10h]@1

  v7 = dwGuildSerial;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v6->vfptr,
    "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) Start!",
    dwGuildSerial);
  if ( CRFNewDatabase::ExecUpdateQuery(
         (CRFNewDatabase *)&v6->vfptr,
         "update #tbl_RankInGuild set Rate = ( (Rank*10000)/(select count(*) from #tbl_RankInGuild) )",
         0) )
  {
    if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v6->vfptr, "update #tbl_RankInGuild set NewGrade=0", 0) )
    {
      if ( CRFNewDatabase::ExecUpdateQuery(
             (CRFNewDatabase *)&v6->vfptr,
             "update #tbl_RankInGuild set NewGrade=3 where lv >= 30 and lv <= 34 and rate <= 6500",
             0) )
      {
        if ( CRFNewDatabase::ExecUpdateQuery(
               (CRFNewDatabase *)&v6->vfptr,
               "update #tbl_RankInGuild set NewGrade=2 where lv >= 30 and lv <= 34 and rate > 6500 and rate <= 8500",
               0) )
        {
          if ( CRFNewDatabase::ExecUpdateQuery(
                 (CRFNewDatabase *)&v6->vfptr,
                 "update #tbl_RankInGuild set NewGrade=1 where lv >= 30 and lv <= 34 and rate > 8500 and rate <= 9500",
                 0) )
          {
            if ( CRFNewDatabase::ExecUpdateQuery(
                   (CRFNewDatabase *)&v6->vfptr,
                   "update #tbl_RankInGuild set NewGrade=4 where lv >= 35 and lv <= 39 and rate <= 3500",
                   0) )
            {
              if ( CRFNewDatabase::ExecUpdateQuery(
                     (CRFNewDatabase *)&v6->vfptr,
                     "update #tbl_RankInGuild set NewGrade=3 where lv >= 35 and lv <= 39 and rate > 3500 and rate <= 6500",
                     0) )
              {
                if ( CRFNewDatabase::ExecUpdateQuery(
                       (CRFNewDatabase *)&v6->vfptr,
                       "update #tbl_RankInGuild set NewGrade=2 where lv >= 35 and lv <= 39 and rate > 6500 and rate <= 8500",
                       0) )
                {
                  if ( CRFNewDatabase::ExecUpdateQuery(
                         (CRFNewDatabase *)&v6->vfptr,
                         "update #tbl_RankInGuild set NewGrade=1 where lv >= 35 and lv <= 39 and rate > 8500 and rate <= 9500",
                         0) )
                  {
                    if ( CRFNewDatabase::ExecUpdateQuery(
                           (CRFNewDatabase *)&v6->vfptr,
                           "update #tbl_RankInGuild set NewGrade=5 where lv >= 40 and lv <= 44 and rate <= 1500",
                           0) )
                    {
                      if ( CRFNewDatabase::ExecUpdateQuery(
                             (CRFNewDatabase *)&v6->vfptr,
                             "update #tbl_RankInGuild set NewGrade=4 where lv >= 40 and lv <= 44 and rate > 1500 and rate <= 3500",
                             0) )
                      {
                        if ( CRFNewDatabase::ExecUpdateQuery(
                               (CRFNewDatabase *)&v6->vfptr,
                               "update #tbl_RankInGuild set NewGrade=3 where lv >= 40 and lv <= 44 and rate > 3500 and rate <= 6500",
                               0) )
                        {
                          if ( CRFNewDatabase::ExecUpdateQuery(
                                 (CRFNewDatabase *)&v6->vfptr,
                                 "update #tbl_RankInGuild set NewGrade=2 where lv >= 40 and lv <= 44 and rate > 6500 and rate <= 8500",
                                 0) )
                          {
                            if ( CRFNewDatabase::ExecUpdateQuery(
                                   (CRFNewDatabase *)&v6->vfptr,
                                   "update #tbl_RankInGuild set NewGrade=1 where lv >= 40 and lv <= 44 and rate > 8500 and rate <= 9500",
                                   0) )
                            {
                              if ( CRFNewDatabase::ExecUpdateQuery(
                                     (CRFNewDatabase *)&v6->vfptr,
                                     "update #tbl_RankInGuild set NewGrade=6 where lv >= 45 and lv <= 49 and rate <= 500",
                                     0) )
                              {
                                if ( CRFNewDatabase::ExecUpdateQuery(
                                       (CRFNewDatabase *)&v6->vfptr,
                                       "update #tbl_RankInGuild set NewGrade=5 where lv >= 45 and lv <= 49 and rate > 500"
                                       " and rate <= 1500",
                                       0) )
                                {
                                  if ( CRFNewDatabase::ExecUpdateQuery(
                                         (CRFNewDatabase *)&v6->vfptr,
                                         "update #tbl_RankInGuild set NewGrade=4 where lv >= 45 and lv <= 49 and rate > 1"
                                         "500 and rate <= 3500",
                                         0) )
                                  {
                                    if ( CRFNewDatabase::ExecUpdateQuery(
                                           (CRFNewDatabase *)&v6->vfptr,
                                           "update #tbl_RankInGuild set NewGrade=3 where lv >= 45 and lv <= 49 and rate >"
                                           " 3500 and rate <= 6500",
                                           0) )
                                    {
                                      if ( CRFNewDatabase::ExecUpdateQuery(
                                             (CRFNewDatabase *)&v6->vfptr,
                                             "update #tbl_RankInGuild set NewGrade=2 where lv >= 45 and lv <= 49 and rate"
                                             " > 6500 and rate <= 8500",
                                             0) )
                                      {
                                        if ( CRFNewDatabase::ExecUpdateQuery(
                                               (CRFNewDatabase *)&v6->vfptr,
                                               "update #tbl_RankInGuild set NewGrade=1 where lv >= 45 and lv <= 49 and ra"
                                               "te > 8500 and rate <= 9500",
                                               0) )
                                        {
                                          if ( CRFNewDatabase::ExecUpdateQuery(
                                                 (CRFNewDatabase *)&v6->vfptr,
                                                 "update #tbl_RankInGuild set NewGrade=7 where lv >= 50 and rate <= 100",
                                                 0) )
                                          {
                                            if ( CRFNewDatabase::ExecUpdateQuery(
                                                   (CRFNewDatabase *)&v6->vfptr,
                                                   "update #tbl_RankInGuild set NewGrade=6 where lv >= 50 and rate > 100 and rate <= 500",
                                                   0) )
                                            {
                                              if ( CRFNewDatabase::ExecUpdateQuery(
                                                     (CRFNewDatabase *)&v6->vfptr,
                                                     "update #tbl_RankInGuild set NewGrade=5 where lv >= 50 and rate > 50"
                                                     "0 and rate <= 1500",
                                                     0) )
                                              {
                                                if ( CRFNewDatabase::ExecUpdateQuery(
                                                       (CRFNewDatabase *)&v6->vfptr,
                                                       "update #tbl_RankInGuild set NewGrade=4 where lv >= 50 and rate > "
                                                       "1500 and rate <= 3500",
                                                       0) )
                                                {
                                                  if ( CRFNewDatabase::ExecUpdateQuery(
                                                         (CRFNewDatabase *)&v6->vfptr,
                                                         "update #tbl_RankInGuild set NewGrade=3 where lv >= 50 and rate "
                                                         "> 3500 and rate <= 6500",
                                                         0) )
                                                  {
                                                    if ( CRFNewDatabase::ExecUpdateQuery(
                                                           (CRFNewDatabase *)&v6->vfptr,
                                                           "update #tbl_RankInGuild set NewGrade=2 where lv >= 50 and rat"
                                                           "e > 6500 and rate <= 8500",
                                                           0) )
                                                    {
                                                      if ( CRFNewDatabase::ExecUpdateQuery(
                                                             (CRFNewDatabase *)&v6->vfptr,
                                                             "update #tbl_RankInGuild set NewGrade=1 where lv >= 50 and r"
                                                             "ate > 8500 and rate <= 9500",
                                                             0) )
                                                      {
                                                        CRFNewDatabase::FmtLog(
                                                          (CRFNewDatabase *)&v6->vfptr,
                                                          "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) End!",
                                                          v7);
                                                        result = 1;
                                                      }
                                                      else
                                                      {
                                                        CRFNewDatabase::FmtLog(
                                                          (CRFNewDatabase *)&v6->vfptr,
                                                          "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) "
                                                          ") : update #tbl_RankInGuild set NewGrade=1 where lv >= 50 and "
                                                          "rate > 8500 and rate <= 9500 Fail!",
                                                          v7);
                                                        if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                                                          CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                                                        result = 0;
                                                      }
                                                    }
                                                    else
                                                    {
                                                      CRFNewDatabase::FmtLog(
                                                        (CRFNewDatabase *)&v6->vfptr,
                                                        "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) "
                                                        ": update #tbl_RankInGuild set NewGrade=2 where lv >= 50 and rate"
                                                        " > 6500 and rate <= 8500 Fail!",
                                                        v7);
                                                      if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                                                        CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                                                      result = 0;
                                                    }
                                                  }
                                                  else
                                                  {
                                                    CRFNewDatabase::FmtLog(
                                                      (CRFNewDatabase *)&v6->vfptr,
                                                      "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : "
                                                      "update #tbl_RankInGuild set NewGrade=3 where lv >= 50 and rate > 3"
                                                      "500 and rate <= 6500 Fail!",
                                                      v7);
                                                    if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                                                      CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                                                    result = 0;
                                                  }
                                                }
                                                else
                                                {
                                                  CRFNewDatabase::FmtLog(
                                                    (CRFNewDatabase *)&v6->vfptr,
                                                    "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : up"
                                                    "date #tbl_RankInGuild set NewGrade=4 where lv >= 50 and rate > 1500 "
                                                    "and rate <= 3500 Fail!",
                                                    v7);
                                                  if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                                                    CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                                                  result = 0;
                                                }
                                              }
                                              else
                                              {
                                                CRFNewDatabase::FmtLog(
                                                  (CRFNewDatabase *)&v6->vfptr,
                                                  "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : upda"
                                                  "te #tbl_RankInGuild set NewGrade=5 where lv >= 50 and rate > 500 and r"
                                                  "ate <= 1500 Fail!",
                                                  v7);
                                                if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                                                  CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                                                result = 0;
                                              }
                                            }
                                            else
                                            {
                                              CRFNewDatabase::FmtLog(
                                                (CRFNewDatabase *)&v6->vfptr,
                                                "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update"
                                                " #tbl_RankInGuild set NewGrade=6 where lv >= 50 and rate > 100 and rate <= 500 Fail!",
                                                v7);
                                              if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                                                CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                                              result = 0;
                                            }
                                          }
                                          else
                                          {
                                            CRFNewDatabase::FmtLog(
                                              (CRFNewDatabase *)&v6->vfptr,
                                              "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #"
                                              "tbl_RankInGuild set NewGrade=7 where lv >= 50 and rate <= 100 Fail!",
                                              v7);
                                            if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                                              CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                                            result = 0;
                                          }
                                        }
                                        else
                                        {
                                          CRFNewDatabase::FmtLog(
                                            (CRFNewDatabase *)&v6->vfptr,
                                            "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tb"
                                            "l_RankInGuild set NewGrade=1 where lv >= 45 and lv <= 49 and rate > 8500 and"
                                            " rate <= 9500 Fail!",
                                            v7);
                                          if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                                            CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                                          result = 0;
                                        }
                                      }
                                      else
                                      {
                                        CRFNewDatabase::FmtLog(
                                          (CRFNewDatabase *)&v6->vfptr,
                                          "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_"
                                          "RankInGuild set NewGrade=2 where lv >= 45 and lv <= 49 and rate > 6500 and rate <= 8500 Fail!",
                                          v7);
                                        if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                                          CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                                        result = 0;
                                      }
                                    }
                                    else
                                    {
                                      CRFNewDatabase::FmtLog(
                                        (CRFNewDatabase *)&v6->vfptr,
                                        "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_Ra"
                                        "nkInGuild set NewGrade=3 where lv >= 45 and lv <= 49 and rate > 3500 and rate <= 6500 Fail!",
                                        v7);
                                      if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                                        CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                                      result = 0;
                                    }
                                  }
                                  else
                                  {
                                    CRFNewDatabase::FmtLog(
                                      (CRFNewDatabase *)&v6->vfptr,
                                      "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_Rank"
                                      "InGuild set NewGrade=4 where lv >= 45 and lv <= 49 and rate > 1500 and rate <= 3500 Fail!",
                                      v7);
                                    if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                                      CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                                    result = 0;
                                  }
                                }
                                else
                                {
                                  CRFNewDatabase::FmtLog(
                                    (CRFNewDatabase *)&v6->vfptr,
                                    "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_RankIn"
                                    "Guild set NewGrade=5 where lv >= 45 and lv <= 49 and rate > 500 and rate <= 1500 Fail!",
                                    v7);
                                  if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                                    CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                                  result = 0;
                                }
                              }
                              else
                              {
                                CRFNewDatabase::FmtLog(
                                  (CRFNewDatabase *)&v6->vfptr,
                                  "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_RankInGu"
                                  "ild set NewGrade=6 where lv >= 45 and lv <= 49 and rate <= 500 Fail!",
                                  v7);
                                if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                                  CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                                result = 0;
                              }
                            }
                            else
                            {
                              CRFNewDatabase::FmtLog(
                                (CRFNewDatabase *)&v6->vfptr,
                                "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_RankInGuil"
                                "d set NewGrade=1 where lv >= 40 and lv <= 44 and rate > 8500 and rate <= 9500 Fail!",
                                v7);
                              if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                                CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                              result = 0;
                            }
                          }
                          else
                          {
                            CRFNewDatabase::FmtLog(
                              (CRFNewDatabase *)&v6->vfptr,
                              "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_RankInGuild "
                              "set NewGrade=2 where lv >= 40 and lv <= 44 and rate > 6500 and rate <= 8500 Fail!",
                              v7);
                            if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                              CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                            result = 0;
                          }
                        }
                        else
                        {
                          CRFNewDatabase::FmtLog(
                            (CRFNewDatabase *)&v6->vfptr,
                            "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_RankInGuild se"
                            "t NewGrade=3 where lv >= 40 and lv <= 44 and rate > 3500 and rate <= 6500 Fail!",
                            v7);
                          if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                            CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                          result = 0;
                        }
                      }
                      else
                      {
                        CRFNewDatabase::FmtLog(
                          (CRFNewDatabase *)&v6->vfptr,
                          "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_RankInGuild set "
                          "NewGrade=4 where lv >= 40 and lv <= 44 and rate > 1500 and rate <= 3500 Fail!",
                          v7);
                        if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                          CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                        result = 0;
                      }
                    }
                    else
                    {
                      CRFNewDatabase::FmtLog(
                        (CRFNewDatabase *)&v6->vfptr,
                        "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_RankInGuild set Ne"
                        "wGrade=5 where lv >= 40 and lv <= 44 and rate <= 1500 Fail!",
                        v7);
                      if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                        CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                      result = 0;
                    }
                  }
                  else
                  {
                    CRFNewDatabase::FmtLog(
                      (CRFNewDatabase *)&v6->vfptr,
                      "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_RankInGuild set NewG"
                      "rade=1 where lv >= 35 and lv <= 39 and rate > 8500 and rate <= 9500 Fail!",
                      v7);
                    if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                      CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                    result = 0;
                  }
                }
                else
                {
                  CRFNewDatabase::FmtLog(
                    (CRFNewDatabase *)&v6->vfptr,
                    "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_RankInGuild set NewGra"
                    "de=2 where lv >= 35 and lv <= 39 and rate > 6500 and rate <= 8500 Fail!",
                    v7);
                  if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                    CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                  result = 0;
                }
              }
              else
              {
                CRFNewDatabase::FmtLog(
                  (CRFNewDatabase *)&v6->vfptr,
                  "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_RankInGuild set NewGrade"
                  "=3 where lv >= 35 and lv <= 39 and rate > 3500 and rate <= 6500 Fail!",
                  v7);
                if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                  CRFWorldDatabase::Update_RankInGuild_Step7(v6);
                result = 0;
              }
            }
            else
            {
              CRFNewDatabase::FmtLog(
                (CRFNewDatabase *)&v6->vfptr,
                "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_RankInGuild set NewGrade=4"
                " where lv >= 35 and lv <= 39 and rate <= 3500 Fail!",
                v7);
              if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
                CRFWorldDatabase::Update_RankInGuild_Step7(v6);
              result = 0;
            }
          }
          else
          {
            CRFNewDatabase::FmtLog(
              (CRFNewDatabase *)&v6->vfptr,
              "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_RankInGuild set NewGrade=1 w"
              "here lv >= 30 and lv <= 34 and rate > 8500 and rate <= 9500 Fail!",
              v7);
            if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
              CRFWorldDatabase::Update_RankInGuild_Step7(v6);
            result = 0;
          }
        }
        else
        {
          CRFNewDatabase::FmtLog(
            (CRFNewDatabase *)&v6->vfptr,
            "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_RankInGuild set NewGrade=2 whe"
            "re lv >= 30 and lv <= 34 and rate > 6500 and rate <= 8500",
            v7);
          if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
            CRFWorldDatabase::Update_RankInGuild_Step7(v6);
          result = 0;
        }
      }
      else
      {
        CRFNewDatabase::FmtLog(
          (CRFNewDatabase *)&v6->vfptr,
          "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_RankInGuild set NewGrade=3 where"
          " lv >= 30 and lv <= 34 and rate <= 6500 Fail!",
          v7);
        if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
          CRFWorldDatabase::Update_RankInGuild_Step7(v6);
        result = 0;
      }
    }
    else
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v6->vfptr,
        "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_RankInGuild set NewGrade=0 Fail!",
        v7);
      if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
        CRFWorldDatabase::Update_RankInGuild_Step7(v6);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v6->vfptr,
      "CRFWorldDatabase::Update_RankInGuild_Step2( dwGuildSerial(%u) ) : update #tbl_RankInGuild set Rate = ( (Rank*10000"
      ")/(select count(*) from #tbl_RankInGuild) ) Fail!",
      v7);
    if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
      CRFWorldDatabase::Update_RankInGuild_Step7(v6);
    result = 0;
  }
  return result;
}
