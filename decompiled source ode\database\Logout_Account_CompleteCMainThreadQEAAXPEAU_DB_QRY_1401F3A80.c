/*
 * Function: ?Logout_Account_Complete@CMainThread@@QEAAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1401F3A80
 */

void __fastcall CMainThread::Logout_Account_Complete(CMainThread *this, _DB_QRY_SYN_DATA *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  CUserDB *v6; // [sp+28h] [bp-10h]@4

  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = 0;
  v6 = &g_UserDB[pData->m_idWorld.wIndex];
  if ( v6->m_bActive )
  {
    if ( v6->m_idWorld.dwSerial == pData->m_idWorld.dwSerial )
      CUserDB::Exit_Account_Complete(v6, pData->m_byResult);
  }
}
