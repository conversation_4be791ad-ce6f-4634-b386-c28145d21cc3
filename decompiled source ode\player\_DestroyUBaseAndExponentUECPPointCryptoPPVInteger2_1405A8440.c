/*
 * Function: ??$_Destroy@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@YAXPEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@Z
 * Address: 0x1405A8440
 */

int __fastcall std::_Destroy<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>(__int64 a1)
{
  return CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::`scalar deleting destructor'(a1, 0i64);
}
