/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAU?$pair@KK@std@@PEAU12@V?$allocator@U?$pair@KK@std@@@2@@stdext@@YAPEAU?$pair@K<PERSON>@std@@PEAU12@00AEAV?$allocator@U?$pair@KK@std@@@2@@Z
 * Address: 0x1400075E0
 */

std::pair<unsigned long,unsigned long> *__fastcall stdext::_Unchecked_uninitialized_move<std::pair<unsigned long,unsigned long> *,std::pair<unsigned long,unsigned long> *,std::allocator<std::pair<unsigned long,unsigned long>>>(std::pair<unsigned long,unsigned long> *_First, std::pair<unsigned long,unsigned long> *_Last, std::pair<unsigned long,unsigned long> *_Dest, std::allocator<std::pair<unsigned long,unsigned long> > *_Al)
{
  return stdext::_Unchecked_uninitialized_move<std::pair<unsigned long,unsigned long> *,std::pair<unsigned long,unsigned long> *,std::allocator<std::pair<unsigned long,unsigned long>>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
