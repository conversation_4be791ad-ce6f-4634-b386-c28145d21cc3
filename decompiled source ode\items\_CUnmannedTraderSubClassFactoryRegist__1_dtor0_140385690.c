/*
 * Function: _CUnmannedTraderSubClassFactory::Regist_::_1_::dtor$0
 * Address: 0x140385690
 */

void __fastcall CUnmannedTraderSubClassFactory::Regist_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>((std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)(a2 + 40));
}
