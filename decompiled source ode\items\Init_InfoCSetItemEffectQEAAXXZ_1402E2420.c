/*
 * Function: ?Init_Info@CSetItemEffect@@QEAAXXZ
 * Address: 0x1402E2420
 */

void __fastcall CSetItemEffect::Init_Info(CSetItemEffect *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CSetItemEffect *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 6; ++j )
    CSetItemEffect::Init_Data(v5, j);
  v5->m_byTotalSetCount = 0;
}
