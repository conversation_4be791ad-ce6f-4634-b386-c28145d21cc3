/*
 * Function: ?NetClose@CNormalGuildBattle@GUILD_BATTLE@@QEAAEKPEAVCPlayer@@@Z
 * Address: 0x1403E5940
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattle::NetClose(GUILD_BATTLE::CNormalGuildBattle *this, unsigned int dwCharacSerial, CPlayer *pkPlayer)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  GUILD_BATTLE::CGuildBattleLogger *v6; // rax@13
  char *v7; // rax@29
  __int64 v8; // [sp+0h] [bp-78h]@1
  char *v9; // [sp+20h] [bp-58h]@13
  char *v10; // [sp+28h] [bp-50h]@13
  const char *v11; // [sp+30h] [bp-48h]@29
  char v12; // [sp+40h] [bp-38h]@4
  char v13; // [sp+41h] [bp-37h]@4
  char v14; // [sp+42h] [bp-36h]@11
  char v15; // [sp+43h] [bp-35h]@14
  int v16; // [sp+44h] [bp-34h]@13
  char *v17; // [sp+48h] [bp-30h]@13
  const char *v18; // [sp+50h] [bp-28h]@21
  char *v19; // [sp+58h] [bp-20h]@24
  const char *v20; // [sp+60h] [bp-18h]@27
  GUILD_BATTLE::CNormalGuildBattle *v21; // [sp+80h] [bp+8h]@1
  unsigned int dwSerial; // [sp+88h] [bp+10h]@1
  CPlayer *pkPlayera; // [sp+90h] [bp+18h]@1

  pkPlayera = pkPlayer;
  dwSerial = dwCharacSerial;
  v21 = this;
  v3 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = 1;
  v13 = 0;
  if ( !GUILD_BATTLE::CNormalGuildBattleGuild::NetClose(
          &v21->m_k1P,
          pkPlayer->m_bInGuildBattle,
          dwCharacSerial,
          &v21->m_kLogger) )
  {
    if ( !GUILD_BATTLE::CNormalGuildBattleGuild::NetClose(
            &v21->m_k2P,
            pkPlayera->m_bInGuildBattle,
            dwSerial,
            &v21->m_kLogger) )
      return -111;
    v12 = 0;
  }
  if ( pkPlayera->m_bInGuildBattle )
  {
    if ( pkPlayera->m_bTakeGravityStone )
    {
      v14 = GUILD_BATTLE::CNormalGuildBattleField::DropBall(v21->m_pkField, pkPlayera);
      if ( v14 )
      {
        v16 = (unsigned __int8)v14;
        v17 = CPlayerDB::GetCharNameW(&pkPlayera->m_Param);
        v6 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        LODWORD(v10) = v16;
        v9 = v17;
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v6,
          "CNormalGuildBattle::NetClose( %u ) : (%u) m_pkField->DropBall( %s ) Fail(%u)!",
          dwSerial,
          dwSerial);
      }
      else
      {
        GUILD_BATTLE::CNormalGuildBattle::NotifyDestoryBall(v21, pkPlayera->m_dwObjSerial);
        v13 = 1;
      }
    }
    CPlayer::pc_SetInGuildBattle(pkPlayera, 0, -1);
    v15 = 1;
    if ( v12 )
    {
      if ( GUILD_BATTLE::CNormalGuildBattleGuild::GetColorInx(&v21->m_k1P) == 1 )
        v15 = 0;
    }
    else if ( GUILD_BATTLE::CNormalGuildBattleGuild::GetColorInx(&v21->m_k2P) == 1 )
    {
      v15 = 0;
    }
    if ( v13 )
      v18 = "Drop Ball";
    else
      v18 = "None";
    if ( v12 )
      v19 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v21->m_k1P);
    else
      v19 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v21->m_k2P);
    if ( v15 )
      v20 = "Left";
    else
      v20 = "Right";
    v7 = CPlayerDB::GetCharNameW(&pkPlayera->m_Param);
    v11 = v18;
    v10 = v7;
    v9 = v19;
    GUILD_BATTLE::CNormalGuildBattleLogger::Log(
      &v21->m_kLogger,
      "CNormalGuildBattle::NetClose( %u ) : %s %s %s %s",
      dwSerial,
      v20);
    result = 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
