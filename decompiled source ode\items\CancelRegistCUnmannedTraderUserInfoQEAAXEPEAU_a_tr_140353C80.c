/*
 * Function: ?CancelRegist@CUnmannedTraderUserInfo@@QEAAXEPEAU_a_trade_clear_item_request_clzo@@PEAVCLogFile@@@Z
 * Address: 0x140353C80
 */

void __fastcall CUnmannedTraderUserInfo::CancelRegist(CUnmannedTraderUserInfo *this, char byType, _a_trade_clear_item_request_clzo *pRequest, CLogFile *pkLogger)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char *v6; // rax@7
  __int64 v7; // [sp+0h] [bp-B8h]@1
  char v8; // [sp+30h] [bp-88h]@5
  unsigned __int16 Dst; // [sp+48h] [bp-70h]@7
  unsigned __int16 v10; // [sp+4Ah] [bp-6Eh]@7
  unsigned int v11; // [sp+4Ch] [bp-6Ch]@7
  char v12; // [sp+50h] [bp-68h]@7
  char v13; // [sp+5Dh] [bp-5Bh]@7
  char v14; // [sp+6Eh] [bp-4Ah]@7
  unsigned int v15; // [sp+70h] [bp-48h]@7
  char v16; // [sp+74h] [bp-44h]@7
  char v17; // [sp+75h] [bp-43h]@7
  unsigned __int64 v18; // [sp+A0h] [bp-18h]@4
  CUnmannedTraderUserInfo *v19; // [sp+C0h] [bp+8h]@1
  char v20; // [sp+C8h] [bp+10h]@1
  _a_trade_clear_item_request_clzo *pRequesta; // [sp+D0h] [bp+18h]@1

  pRequesta = pRequest;
  v20 = byType;
  v19 = this;
  v4 = &v7;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v18 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( (signed int)(unsigned __int8)byType < 2 )
  {
    v8 = CUnmannedTraderUserInfo::CheckCancelRegist(v19, byType, pRequest, pkLogger);
    if ( v8 )
    {
      CUnmannedTraderUserInfo::SendCancelRegistErrorResult(v19, v19->m_wInx, v8);
    }
    else
    {
      memset_0(&Dst, 0, 0x3Cui64);
      Dst = v19->m_wInx;
      v10 = pRequesta->wItemSerial;
      v11 = v19->m_dwUserSerial;
      strcpy_s(&v12, 0xDui64, (const char *)(*((_QWORD *)&g_Player.m_pUserDB + 6357 * v19->m_wInx) + 32i64));
      v6 = CPlayerDB::GetCharNameW((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * v19->m_wInx));
      strcpy_s(&v13, 0x11ui64, v6);
      v17 = 0;
      v14 = v20;
      v15 = pRequesta->dwRegistSerial;
      v16 = 5;
      CUnmannedTraderRequestLimiter::SetRequest(&v19->m_kRequestState, 2);
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 61, (char *)&Dst, 60);
    }
  }
}
