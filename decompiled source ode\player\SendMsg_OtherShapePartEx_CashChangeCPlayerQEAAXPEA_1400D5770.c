/*
 * Function: ?SendMsg_OtherShapePartEx_CashChange@CPlayer@@QEAAXPEAV1@TCashChangeStateFlag@1@1@Z
 * Address: 0x1400D5770
 */

void __fastcall CPlayer::SendMsg_OtherShapePartEx_CashChange(CPlayer *this, CPlayer *pDst, CPlayer::CashChangeStateFlag ServerData, CPlayer::CashChangeStateFlag ClinetData)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char *v6; // rax@7
  __int64 v7; // [sp+0h] [bp-98h]@1
  char szMsg[2]; // [sp+38h] [bp-60h]@7
  unsigned int v9; // [sp+3Ah] [bp-5Eh]@7
  char Dest; // [sp+3Eh] [bp-5Ah]@7
  char pbyType; // [sp+64h] [bp-34h]@7
  char v12; // [sp+65h] [bp-33h]@7
  unsigned __int64 v13; // [sp+80h] [bp-18h]@4
  CPlayer *v14; // [sp+A0h] [bp+8h]@1
  CPlayer *v15; // [sp+A8h] [bp+10h]@1

  v15 = pDst;
  v14 = this;
  v4 = &v7;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v13 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( (!v14->m_bObserver || pDst->m_byUserDgr) && (ServerData.m_byStateFlag & 7) != (ClinetData.m_byStateFlag & 7) )
  {
    *(_WORD *)szMsg = v14->m_ObjID.m_wIndex;
    v9 = v14->m_dwObjSerial;
    v6 = CPlayerDB::GetCharNameW(&v14->m_Param);
    strcpy_0(&Dest, v6);
    pbyType = 3;
    v12 = 61;
    CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, szMsg, 0x17u);
  }
}
