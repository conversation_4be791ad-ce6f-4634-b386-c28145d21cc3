/*
 * Function: ?GetModulus@ModExpPrecomputation@CryptoPP@@QEBAAEBVInteger@2@XZ
 * Address: 0x140552480
 */

const struct CryptoPP::Integer *__fastcall CryptoPP::ModExpPrecomputation::GetModulus(CryptoPP::ModExpPrecomputation *this)
{
  CryptoPP::ModularArithmetic *v1; // rax@1

  LODWORD(v1) = CryptoPP::member_ptr<CryptoPP::MontgomeryRepresentation>::operator->(&this->m_mr);
  return CryptoPP::ModularArithmetic::GetModulus(v1);
}
