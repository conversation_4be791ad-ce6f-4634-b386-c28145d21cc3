/*
 * Function: ?Initialize@?$DL_PrivateKey_GFP@VDL_GroupParameters_DSA@CryptoPP@@@CryptoPP@@QEAAXAEBVInteger@2@00@Z
 * Address: 0x140553900
 */

int __fastcall CryptoPP::DL_PrivateKey_GFP<CryptoPP::DL_GroupParameters_DSA>::Initialize(__int64 a1, struct CryptoPP::Integer *a2, struct CryptoPP::Integer *a3, __int64 a4)
{
  CryptoPP::DL_GroupParameters_IntegerBased *v4; // rax@1
  __int64 v6; // [sp+30h] [bp+8h]@1
  struct CryptoPP::Integer *v7; // [sp+38h] [bp+10h]@1
  struct CryptoPP::Integer *v8; // [sp+40h] [bp+18h]@1
  __int64 v9; // [sp+48h] [bp+20h]@1

  v9 = a4;
  v8 = a3;
  v7 = a2;
  v6 = a1;
  LODWORD(v4) = CryptoPP::DL_KeyImpl<CryptoPP::PKCS8PrivateKey,CryptoPP::DL_GroupParameters_DSA,CryptoPP::OID>::AccessGroupParameters(a1 + 8);
  CryptoPP::DL_GroupParameters_IntegerBased::Initialize(v4, v7, v8);
  return (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v6 + 24i64))(v6, v9);
}
