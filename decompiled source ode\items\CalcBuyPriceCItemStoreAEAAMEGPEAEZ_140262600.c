/*
 * Function: ?CalcBuyPrice@CItemStore@@AEAAMEGPEAE@Z
 * Address: 0x140262600
 */

float __fastcall CItemStore::CalcBuyPrice(CItemStore *this, char byTableCode, unsigned __int16 wItemIndex, char *pbyMoneyUnit)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char v6; // al@5
  int v7; // eax@7
  __int128 v8; // xmm1@7
  __int64 v10; // [sp+0h] [bp-48h]@1
  int v11; // [sp+30h] [bp-18h]@4
  int nTableCode; // [sp+38h] [bp-10h]@5
  int v13; // [sp+3Ch] [bp-Ch]@7
  CItemStore *v14; // [sp+50h] [bp+8h]@1
  char v15; // [sp+58h] [bp+10h]@1
  unsigned __int16 v16; // [sp+60h] [bp+18h]@1
  char *pbyMoneyKind; // [sp+68h] [bp+20h]@1

  pbyMoneyKind = pbyMoneyUnit;
  v16 = wItemIndex;
  v15 = byTableCode;
  v14 = this;
  v4 = &v10;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = 2;
  if ( v14->m_byNpcRaceCode != 255 )
  {
    v6 = 2 * v14->m_byNpcRaceCode;
    nTableCode = (unsigned __int8)byTableCode;
    if ( !IsItemEquipCivil((unsigned __int8)byTableCode, wItemIndex, v6) )
      v11 = 2;
  }
  v7 = v14->m_byNpcRaceCode;
  v13 = (unsigned __int8)v15;
  v8 = COERCE_UNSIGNED_INT((float)GetItemStdPrice((unsigned __int8)v15, v16, v7, pbyMoneyKind));
  return *(float *)&v8 / (float)v11;
}
