/*
 * Function: ?ProcThread@CLogTypeDBTaskManager@@CAXPEAX@Z
 * Address: 0x1402C3480
 */

void __fastcall CLogTypeDBTaskManager::ProcThread(void *pParam)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-58h]@1
  CLogTypeDBTaskManager *v4; // [sp+20h] [bp-38h]@4
  int v5; // [sp+34h] [bp-24h]@4
  CLogTypeDBTaskManager *v6; // [sp+60h] [bp+8h]@1

  v6 = (CLogTypeDBTaskManager *)pParam;
  v1 = &v3;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = v6;
  v5 = 0;
  while ( CLogTypeDBTaskManager::GetDBProc(v4) )
  {
    CLogTypeDBTaskManager::DBProcess(v4);
    if ( ++v5 > 100 )
      Sleep(1u);
  }
  _endthread();
}
