/*
 * Function: SQLRemoveDriverW
 * Address: 0x1404DB254
 */

int __fastcall SQLRemoveDriverW(const unsigned __int16 *lpszDriver, int fRemoveDSN, unsigned int *lpdwUsageCount)
{
  const unsigned __int16 *v3; // rsi@1
  unsigned int *v4; // rbx@1
  int v5; // edi@1
  __int64 (__cdecl *v6)(); // rax@1
  int result; // eax@2

  v3 = lpszDriver;
  v4 = lpdwUsageCount;
  v5 = fRemoveDSN;
  v6 = ODBC___GetSetupProc("SQLRemoveDriverW");
  if ( v6 )
    result = ((int (__fastcall *)(const unsigned __int16 *, _QWORD, unsigned int *))v6)(v3, (unsigned int)v5, v4);
  else
    result = 0;
  return result;
}
