/*
 * Function: ?ConvertErrorCode@CashDbWorker@@MEAAHD@Z
 * Address: 0x1402F0CD0
 */

signed __int64 __fastcall CashDbWorker::ConvertErrorCode(CashDbWorker *this, char state)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  int v5; // [sp+0h] [bp-18h]@1

  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  v5 = state;
  if ( state > 12592 )
  {
LABEL_20:
    result = 1i64;
  }
  else if ( v5 == 12592 )
  {
    result = 18i64;
  }
  else
  {
    v5 -= 49;
    switch ( v5 )
    {
      case 35:
        result = 0i64;
        break;
      case 21:
        result = 13i64;
        break;
      case 0:
        result = 2i64;
        break;
      case 1:
        result = 3i64;
        break;
      case 2:
        result = 4i64;
        break;
      case 3:
        result = 5i64;
        break;
      case 4:
        result = 6i64;
        break;
      case 5:
        result = 7i64;
        break;
      case 6:
        result = 8i64;
        break;
      case 7:
        result = 9i64;
        break;
      case 8:
        result = 10i64;
        break;
      default:
        goto LABEL_20;
    }
  }
  return result;
}
