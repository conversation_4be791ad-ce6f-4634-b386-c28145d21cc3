/*
 * Function: j_??$_Uninit_move@PEAUAreaData@@PEAU1@V?$allocator@UAreaData@@@std@@U_Undefined_move_tag@3@@std@@YAPEAUAreaData@@PEAU1@00AEAV?$allocator@UAreaData@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400138F9
 */

AreaData *__fastcall std::_Uninit_move<AreaData *,AreaData *,std::allocator<AreaData>,std::_Undefined_move_tag>(AreaData *_First, AreaData *_Last, AreaData *_Dest, std::allocator<AreaData> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<AreaData *,AreaData *,std::allocator<AreaData>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
