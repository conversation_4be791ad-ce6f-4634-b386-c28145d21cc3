/*
 * Function: ?SelectClass@CPlayerDB@@QEAAXEPEAU_class_fld@@@Z
 * Address: 0x14010B700
 */

void __fastcall CPlayerDB::SelectClass(CPlayerDB *this, char byHistoryRecordNum, _class_fld *pSelectClass)
{
  int *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // [sp+0h] [bp-18h]@1
  CPlayerDB *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v3 = -858993460;
    ++v3;
  }
  v6->m_pClassHistory[(unsigned __int8)byHistoryRecordNum] = v6->m_pClassData;
  v6->m_pClassData = pSelectClass;
  if ( v6->m_nMakeTrapMaxNum <= pSelectClass->m_nMakeTrapMaxNum )
    v5 = pSelectClass->m_nMakeTrapMaxNum;
  else
    v5 = v6->m_nMakeTrapMaxNum;
  v6->m_nMakeTrapMaxNum = v5;
}
