/*
 * Function: j_?have_item_close@CMgrAvatorItemHistory@@QEAAXHPEADPEAU_AVATOR_DATA@@10KEKKPEBVCUnmannedTraderRegistItemInfo@@E0@Z
 * Address: 0x14000883C
 */

void __fastcall CMgrAvatorItemHistory::have_item_close(CMgrAvatorItemHistory *this, int n, char *pszName, _AVATOR_DATA *pLoadData, _AVATOR_DATA *pBackupData, char *pszID, unsigned int dwIDSerial, char byDgr, unsigned int dwIP, unsigned int dwExpRate, CUnmannedTraderRegistItemInfo *pkInfo, char byMaxCnt, char *pszFileName)
{
  CMgrAvatorItemHistory::have_item_close(
    this,
    n,
    pszName,
    pLoadData,
    pBackupData,
    pszID,
    dwIDSerial,
    byDgr,
    dwIP,
    dwExpRate,
    pkInfo,
    byMaxCnt,
    pszFileName);
}
