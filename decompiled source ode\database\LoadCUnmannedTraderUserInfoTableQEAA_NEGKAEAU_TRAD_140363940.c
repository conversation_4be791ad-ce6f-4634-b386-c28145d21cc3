/*
 * Function: ?Load@CUnmannedTraderUserInfoTable@@QEAA_NEGKAEAU_TRADE_DB_BASE@@@Z
 * Address: 0x140363940
 */

bool __fastcall CUnmannedTraderUserInfoTable::Load(CUnmannedTraderUserInfoTable *this, char byType, unsigned __int16 wInx, unsigned int dwSerial, _TRADE_DB_BASE *kInfo)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  CUnmannedTraderUserInfo *v8; // rax@7
  __int64 v9; // [sp+0h] [bp-38h]@1
  CLogFile *pkLogger; // [sp+28h] [bp-10h]@7
  CUnmannedTraderUserInfoTable *v11; // [sp+40h] [bp+8h]@1
  char v12; // [sp+48h] [bp+10h]@1
  unsigned __int16 v13; // [sp+50h] [bp+18h]@1
  unsigned int dwSeriala; // [sp+58h] [bp+20h]@1

  dwSeriala = dwSerial;
  v13 = wInx;
  v12 = byType;
  v11 = this;
  v5 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::empty(&v11->m_veckInfo)
    && std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::size(&v11->m_veckInfo) <= v13 )
  {
    result = 0;
  }
  else
  {
    v8 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator[](&v11->m_veckInfo, v13);
    pkLogger = v11->m_pkLogger;
    result = CUnmannedTraderUserInfo::Load(v8, v12, v13, dwSeriala, kInfo, pkLogger);
  }
  return result;
}
