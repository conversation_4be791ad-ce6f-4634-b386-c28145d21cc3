/*
 * Function: ?Create@CMoveMapLimitInfo@@SAPEAV1@IH@Z
 * Address: 0x1403A3DB0
 */

CMoveMapLimitInfo *__fastcall CMoveMapLimitInfo::Create(unsigned int uiInx, int iType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CMoveMapLimitInfo *v4; // rax@6
  CMoveMapLimitInfo *result; // rax@8
  __int64 v6; // [sp+0h] [bp-58h]@1
  CMoveMapLimitInfo *v7; // [sp+20h] [bp-38h]@8
  CMoveMapLimitInfoPortal *v8; // [sp+28h] [bp-30h]@5
  __int64 v9; // [sp+30h] [bp-28h]@4
  int v10; // [sp+38h] [bp-20h]@4
  CMoveMapLimitInfo *v11; // [sp+40h] [bp-18h]@6
  unsigned int uiInxa; // [sp+60h] [bp+8h]@1

  uiInxa = uiInx;
  v2 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = -2i64;
  v10 = iType;
  if ( iType )
  {
    result = 0i64;
  }
  else
  {
    v8 = (CMoveMapLimitInfoPortal *)operator new(0x78ui64);
    if ( v8 )
    {
      CMoveMapLimitInfoPortal::CMoveMapLimitInfoPortal(v8, uiInxa, 0);
      v11 = v4;
    }
    else
    {
      v11 = 0i64;
    }
    v7 = v11;
    result = v11;
  }
  return result;
}
