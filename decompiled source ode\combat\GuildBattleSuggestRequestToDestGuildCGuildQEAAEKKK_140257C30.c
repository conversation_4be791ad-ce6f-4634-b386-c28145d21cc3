/*
 * Function: ?GuildBattleSuggestRequestToDestGuild@CGuild@@QEAAEKKKK@Z
 * Address: 0x140257C30
 */

char __fastcall CGuild::GuildBattleSuggestRequestToDestGuild(CGuild *this, unsigned int dwSrcGuildSerial, unsigned int dwStartTimeInx, unsigned int dwMemberCntInx, unsigned int dwMapInx)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-78h]@1
  char v9; // [sp+40h] [bp-38h]@14
  char v10; // [sp+54h] [bp-24h]@16
  char v11; // [sp+55h] [bp-23h]@16
  CGuild *v12; // [sp+80h] [bp+8h]@1
  unsigned int dwSerial; // [sp+88h] [bp+10h]@1
  unsigned int v14; // [sp+90h] [bp+18h]@1
  unsigned int v15; // [sp+98h] [bp+20h]@1

  v15 = dwMemberCntInx;
  v14 = dwStartTimeInx;
  dwSerial = dwSrcGuildSerial;
  v12 = this;
  v5 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( v12->m_bRankWait )
  {
    result = -92;
  }
  else if ( v12->m_bNowProcessSgtMter )
  {
    result = -91;
  }
  else if ( v12->m_dTotalGold >= 5000.0 )
  {
    if ( GetGuildDataFromSerial(g_Guild, 500, dwSrcGuildSerial) )
    {
      if ( GetGuildDataFromSerial(g_Guild, 500, v12->m_dwSerial) )
      {
        v9 = CGuild::DestGuildIsAvailableBattleRequestState(v12);
        if ( v9 )
        {
          result = v9;
        }
        else
        {
          v10 = 0;
          v11 = 0;
          if ( CGuild::RegSuggestedMatter(v12, 0xFFFFFFFF, 5, dwSerial, &v10, v14, v15, dwMapInx) )
            result = 0;
          else
            result = -89;
        }
      }
      else
      {
        result = 111;
      }
    }
    else
    {
      result = 111;
    }
  }
  else
  {
    result = -90;
  }
  return result;
}
