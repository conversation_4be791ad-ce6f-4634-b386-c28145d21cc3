/*
 * Function: j_??$_Fill_n@PEAPEAVCMoveMapLimitRight@@_KPEAV1@Urandom_access_iterator_tag@std@@@std@@YAXPEAPEAVCMoveMapLimitRight@@_KAEBQEAV1@Urandom_access_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140012F85
 */

void __fastcall std::_Fill_n<CMoveMapLimitRight * *,unsigned __int64,CMoveMapLimitRight *,std::random_access_iterator_tag>(CMoveMapLimitRight **_First, unsigned __int64 _Count, CMoveMapLimitRight *const *_Val, std::random_access_iterator_tag __formal, std::_Range_checked_iterator_tag a5)
{
  std::_Fill_n<CMoveMapLimitRight * *,unsigned __int64,CMoveMapLimitRight *,std::random_access_iterator_tag>(
    _First,
    _Count,
    _Val,
    __formal,
    a5);
}
