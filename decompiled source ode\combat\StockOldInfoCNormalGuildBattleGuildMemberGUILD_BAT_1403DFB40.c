/*
 * Function: ?StockOldInfo@CNormalGuildBattleGuildMember@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403DFB40
 */

void __usercall GUILD_BATTLE::CNormalGuildBattleGuildMember::StockOldInfo(GUILD_BATTLE::CNormalGuildBattleGuildMember *this@<rcx>, long double a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-68h]@1
  int v5; // [sp+28h] [bp-40h]@7
  int v6; // [sp+2Ch] [bp-3Ch]@7
  int v7; // [sp+30h] [bp-38h]@7
  char *Source; // [sp+48h] [bp-20h]@7
  char *v9; // [sp+50h] [bp-18h]@7
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v10; // [sp+70h] [bp+8h]@1

  v10 = this;
  v2 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v10->m_pkMember )
  {
    if ( v10->m_pkMember->pPlayer )
    {
      CPlayerDB::GetPvPPoint(&v10->m_pkMember->pPlayer->m_Param);
      v10->m_dPvpPoint = a2;
      v5 = 0;
      v6 = 0;
      v7 = 0;
      v10->m_pOldBindMapData = CPlayer::GetBindMapData(v10->m_pkMember->pPlayer);
      v10->m_pOldBindDummyData = CPlayer::GetBindDummy(v10->m_pkMember->pPlayer);
      Source = v10->m_pkMember->pPlayer->m_pUserDB->m_AvatorData.dbAvator.m_szBindMapCode;
      v9 = v10->m_pkMember->pPlayer->m_pUserDB->m_AvatorData.dbAvator.m_szBindDummy;
      strcpy_0(v10->m_szOldBindMapCode, Source);
      strcpy_0(v10->m_szOldBindDummy, v9);
    }
  }
}
