/*
 * Function: j_??$_Uninit_copy@PEAUECPPoint@CryptoPP@@PEAU12@V?$allocator@UECPPoint@CryptoPP@@@std@@@std@@YAPEAUECPPoint@CryptoPP@@PEAU12@00AEAV?$allocator@UECPPoint@CryptoPP@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140001EA1
 */

CryptoPP::ECPPoint *__fastcall std::_Uninit_copy<CryptoPP::ECPPoint *,CryptoPP::ECPPoint *,std::allocator<CryptoPP::ECPPoint>>(CryptoPP::ECPPoint *_First, CryptoPP::ECPPoint *_Last, CryptoPP::ECPPoint *_Dest, std::allocator<CryptoPP::ECPPoint> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CryptoPP::ECPPoint *,CryptoPP::ECPPoint *,std::allocator<CryptoPP::ECPPoint>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
