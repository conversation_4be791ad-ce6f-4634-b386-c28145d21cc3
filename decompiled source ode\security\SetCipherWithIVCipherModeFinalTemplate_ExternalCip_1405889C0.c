/*
 * Function: ?SetCipherWithIV@?$CipherModeFinalTemplate_ExternalCipher@VCBC_CTS_Encryption@CryptoPP@@@CryptoPP@@QEAAXAEAV?$SimpleKeyedTransformation@VBlockTransformation@CryptoPP@@@2@PEBEH@Z
 * Address: 0x1405889C0
 */

int __fastcall CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Encryption>::SetCipherWithIV(__int64 a1, __int64 a2, const unsigned __int8 *a3, unsigned int a4)
{
  __int64 v4; // rax@1
  __int64 v6; // [sp+40h] [bp+8h]@1
  __int64 v7; // [sp+48h] [bp+10h]@1
  unsigned __int8 *v8; // [sp+50h] [bp+18h]@1
  unsigned int v9; // [sp+58h] [bp+20h]@1

  v9 = a4;
  v8 = (unsigned __int8 *)a3;
  v7 = a2;
  v6 = a1;
  CryptoPP::SimpleKeyingInterface::ThrowIfInvalidIV((CryptoPP::SimpleKeyingInterface *)(a1 + 8), a3);
  *(_QWORD *)(v6 + 16) = v7;
  (*(void (__fastcall **)(__int64))(*(_QWORD *)v6 + 120i64))(v6);
  (*(void (__fastcall **)(__int64, _QWORD))(*(_QWORD *)v6 + 112i64))(v6, v9);
  v4 = CryptoPP::SimpleKeyingInterface::IsResynchronizable((CryptoPP::SimpleKeyingInterface *)(v6 + 8));
  if ( (_BYTE)v4 )
    LODWORD(v4) = (*(int (__fastcall **)(signed __int64, unsigned __int8 *))(*(_QWORD *)(v6 + 8) + 72i64))(v6 + 8, v8);
  return v4;
}
