/*
 * Function: j_?WorkerThread@?$CWinThread@U?$ThreadParamInterface@VCBossMonsterScheduleSystem@@VAbstractThreadPool@US@@@US@@@US@@SAKPEAX@Z
 * Address: 0x140005A2E
 */

unsigned int __fastcall US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::WorkerThread(void *pParam)
{
  return US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::WorkerThread(pParam);
}
