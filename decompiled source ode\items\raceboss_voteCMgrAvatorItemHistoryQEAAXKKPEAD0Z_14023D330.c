/*
 * Function: ?raceboss_vote@CMgrAvatorItemHistory@@QEAAXKKPEAD0@Z
 * Address: 0x14023D330
 */

void __fastcall CMgrAvatorItemHistory::raceboss_vote(CMgrAvatorItemHistory *this, unsigned int dwSerial, unsigned int dwAvatorSerial, char *pAvatorName, char *pszFileName)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  unsigned int v8; // [sp+20h] [bp-18h]@4
  unsigned int v9; // [sp+28h] [bp-10h]@4
  CMgrAvatorItemHistory *v10; // [sp+40h] [bp+8h]@1
  unsigned int v11; // [sp+48h] [bp+10h]@1
  unsigned int v12; // [sp+50h] [bp+18h]@1
  char *v13; // [sp+58h] [bp+20h]@1

  v13 = pAvatorName;
  v12 = dwAvatorSerial;
  v11 = dwSerial;
  v10 = this;
  v5 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v9 = GetKorLocalTime();
  v8 = v12;
  sprintf(sData, "[RACE BOSS]vote: Serial:%d\tTarget:%s(%d)\tTime:%d\n", v11, v13);
  CMgrAvatorItemHistory::WriteFile(v10, pszFileName, sData);
}
