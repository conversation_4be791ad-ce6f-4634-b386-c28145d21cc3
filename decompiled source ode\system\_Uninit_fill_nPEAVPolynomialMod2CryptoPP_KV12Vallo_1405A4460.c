/*
 * Function: ??$_Uninit_fill_n@PEAVPolynomialMod2@CryptoPP@@_KV12@V?$allocator@VPolynomialMod2@CryptoPP@@@std@@@std@@YAXPEAVPolynomialMod2@CryptoPP@@_KAEBV12@AEAV?$allocator@VPolynomialMod2@CryptoPP@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405A4460
 */

signed __int64 __fastcall std::_Uninit_fill_n<CryptoPP::PolynomialMod2 *,unsigned __int64,CryptoPP::PolynomialMod2,std::allocator<CryptoPP::PolynomialMod2>>(signed __int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  signed __int64 result; // rax@1
  signed __int64 v5; // [sp+40h] [bp+8h]@1
  __int64 v6; // [sp+48h] [bp+10h]@1
  __int64 v7; // [sp+50h] [bp+18h]@1
  __int64 v8; // [sp+58h] [bp+20h]@1

  v8 = a4;
  v7 = a3;
  v6 = a2;
  v5 = a1;
  result = a1;
  while ( v6 )
  {
    std::allocator<CryptoPP::PolynomialMod2>::construct(v8, v5, v7);
    --v6;
    result = v5 + 24;
    v5 += 24i64;
  }
  return result;
}
