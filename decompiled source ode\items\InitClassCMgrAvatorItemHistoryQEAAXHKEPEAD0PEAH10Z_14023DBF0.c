/*
 * Function: ?InitClass@CMgrAvatorItemHistory@@QEAAXHKEPEAD0PEAH10@Z
 * Address: 0x14023DBF0
 */

void __fastcall CMgrAvatorItemHistory::InitClass(CMgrAvatorItemHistory *this, int iCostGold, unsigned int dwInitClassCnt, char byLastClassGrade, char *szOldClass, char *szCurClass, int *piOldMaxPoint, int *piAlterMaxPoint, char *pszFileName)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v11; // [sp+0h] [bp-78h]@1
  int v12; // [sp+20h] [bp-58h]@4
  char *v13; // [sp+28h] [bp-50h]@4
  char *v14; // [sp+30h] [bp-48h]@4
  int v15; // [sp+38h] [bp-40h]@4
  int v16; // [sp+40h] [bp-38h]@4
  int v17; // [sp+48h] [bp-30h]@4
  int v18; // [sp+50h] [bp-28h]@4
  int v19; // [sp+58h] [bp-20h]@4
  int v20; // [sp+60h] [bp-18h]@4
  CMgrAvatorItemHistory *v21; // [sp+80h] [bp+8h]@1

  v21 = this;
  v9 = &v11;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v20 = piAlterMaxPoint[2];
  v19 = piAlterMaxPoint[1];
  v18 = *piAlterMaxPoint;
  v17 = piOldMaxPoint[2];
  v16 = piOldMaxPoint[1];
  v15 = *piOldMaxPoint;
  v14 = szCurClass;
  v13 = szOldClass;
  v12 = (unsigned __int8)byLastClassGrade;
  sprintf(
    sData,
    "INIT CLASS: G:%d Cnt:%d Gd:%d %s -> %s H:%d F:%d S:%d -> H:%d F:%d S:%d\r\n",
    (unsigned int)iCostGold,
    dwInitClassCnt);
  CMgrAvatorItemHistory::WriteFile(v21, pszFileName, sData);
}
