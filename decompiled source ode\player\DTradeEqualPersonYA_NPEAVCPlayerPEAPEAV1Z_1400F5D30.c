/*
 * Function: ?DTradeEqual<PERSON>erson@@YA_NPEAVCPlayer@@PEAPEAV1@@Z
 * Address: 0x1400F5D30
 */

char __fastcall DTradeEqualPerson(CPlayer *lp_pOne, CPlayer **lpp_pDst)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  int v5; // eax@30
  __int64 v6; // [sp+0h] [bp-38h]@1
  CPlayer *v7; // [sp+20h] [bp-18h]@14
  int v8; // [sp+28h] [bp-10h]@30
  CPlayer *v9; // [sp+40h] [bp+8h]@1
  CPlayer **v10; // [sp+48h] [bp+10h]@1

  v10 = lpp_pDst;
  v9 = lp_pOne;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v9->m_bLive && !v9->m_bCorpse && CGameObject::GetCurSecNum((CGameObject *)&v9->vfptr) != -1 )
  {
    if ( v9->m_bOper && v9->m_pUserDB )
    {
      if ( v9->m_pmTrd.bDTradeMode && v9->m_pmTrd.wDTradeDstIndex != 0xFFFF )
      {
        v7 = &g_Player + v9->m_pmTrd.wDTradeDstIndex;
        if ( v7->m_bLive )
        {
          if ( v7->m_pmTrd.bDTradeMode )
          {
            if ( v9->m_pmTrd.dwDTradeDstSerial == v7->m_dwObjSerial )
            {
              if ( v7->m_bCorpse
                || CGameObject::GetCurSecNum((CGameObject *)&v7->vfptr) == -1
                || v7->m_pCurMap != v9->m_pCurMap )
              {
                _DTRADE_PARAM::Init(&v7->m_pmTrd);
                CPlayer::SendMsg_DTradeCloseInform(v7, 0);
                result = 0;
              }
              else if ( v7->m_bOper && v7->m_pUserDB )
              {
                if ( v7->m_pmTrd.wDTradeDstIndex == v9->m_ObjID.m_wIndex
                  && v7->m_pmTrd.dwDTradeDstSerial == v9->m_dwObjSerial )
                {
                  v8 = CPlayerDB::GetRaceCode(&v7->m_Param);
                  v5 = CPlayerDB::GetRaceCode(&v9->m_Param);
                  if ( v8 == v5 )
                  {
                    if ( v10 )
                      *v10 = v7;
                    result = 1;
                  }
                  else
                  {
                    _DTRADE_PARAM::Init(&v7->m_pmTrd);
                    CPlayer::SendMsg_DTradeCloseInform(v7, 0);
                    result = 0;
                  }
                }
                else
                {
                  _DTRADE_PARAM::Init(&v7->m_pmTrd);
                  CPlayer::SendMsg_DTradeCloseInform(v7, 0);
                  result = 0;
                }
              }
              else
              {
                _DTRADE_PARAM::Init(&v7->m_pmTrd);
                CPlayer::SendMsg_DTradeCloseInform(v7, 0);
                result = 0;
              }
            }
            else
            {
              _DTRADE_PARAM::Init(&v7->m_pmTrd);
              CPlayer::SendMsg_DTradeCloseInform(v7, 0);
              result = 0;
            }
          }
          else
          {
            result = 0;
          }
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
