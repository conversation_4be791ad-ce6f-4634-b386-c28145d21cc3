/*
 * Function: j_?ProcessRequestRecall@CRecallEffectController@@IEAAEPEAVCPlayer@@PEAVCCharacter@@AEAPEAVCRecallRequest@@_N33@Z
 * Address: 0x14000B550
 */

char __fastcall CRecallEffectController::ProcessRequestRecall(CRecallEffectController *this, CPlayer *pkPerformer, CCharacter *pkDest, CRecallRequest **pkRequest, bool bRecallParty, bool bStone, bool bBattleModeUse)
{
  return CRecallEffectController::ProcessRequestRecall(
           this,
           pkPerformer,
           pkDest,
           pkRequest,
           bRecallParty,
           bStone,
           bBattleModeUse);
}
