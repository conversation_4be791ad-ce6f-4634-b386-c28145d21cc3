/*
 * Function: ?CalcDefExp@CAnimus@@QEAAXPEAVCCharacter@@H@Z
 * Address: 0x140126DA0
 */

void __fastcall CAnimus::CalcDefExp(CAnimus *this, CCharacter *pAttackObj, int nDamage)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@8
  int v6; // eax@13
  __int64 v7; // [sp+0h] [bp-48h]@1
  int v8; // [sp+20h] [bp-28h]@10
  int v9; // [sp+24h] [bp-24h]@14
  int v10; // [sp+28h] [bp-20h]@8
  CGameObjectVtbl *v11; // [sp+30h] [bp-18h]@8
  CAnimus *v12; // [sp+50h] [bp+8h]@1
  CCharacter *v13; // [sp+58h] [bp+10h]@1
  int v14; // [sp+60h] [bp+18h]@1

  v14 = nDamage;
  v13 = pAttackObj;
  v12 = this;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pAttackObj )
  {
    if ( pAttackObj->m_ObjID.m_byID == 1
      && nDamage > 1
      && !(unsigned __int8)((int (__fastcall *)(CAnimus *))v12->vfptr->IsInTown)(v12) )
    {
      v10 = ((int (__fastcall *)(CAnimus *))v12->vfptr->GetLevel)(v12);
      v11 = v13->vfptr;
      v5 = ((int (__fastcall *)(CCharacter *))v11->GetLevel)(v13);
      if ( abs_0(v10 - v5) <= 10 )
      {
        if ( v12->m_pMaster )
        {
          if ( (v8 = ((int (__fastcall *)(CAnimus *))v12->vfptr->GetLevel)(v12),
                ((int (__fastcall *)(CPlayer *))v12->m_pMaster->vfptr->GetLevel)(v12->m_pMaster) < 50)
            && v8 <= 50
            || ((int (__fastcall *)(CPlayer *))v12->m_pMaster->vfptr->GetLevel)(v12->m_pMaster) >= 50
            && (v6 = ((int (__fastcall *)(_QWORD))v12->m_pMaster->vfptr->GetLevel)(v12->m_pMaster), v8 <= v6 + 1) )
          {
            v9 = 4 * v14;
            if ( 4 * v14 )
              CAnimus::AlterExp(v12, v9);
          }
        }
      }
    }
  }
}
