/*
 * Function: ?IsApplyPcbangPrimium@CNationSettingData@@UEAA_NQEBVCPlayer@@@Z
 * Address: 0x140211D00
 */

bool __fastcall CNationSettingData::IsApplyPcbangPrimium(CNationSettingData *this, CPlayer *const pUser)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  bool v6; // [sp+20h] [bp-18h]@7

  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pUser )
  {
    if ( pUser->m_pUserDB )
      v6 = _BILLING_INFO::IsPcBangType(&pUser->m_pUserDB->m_BillingInfo);
    else
      v6 = 0;
    result = v6;
  }
  else
  {
    result = 0;
  }
  return result;
}
