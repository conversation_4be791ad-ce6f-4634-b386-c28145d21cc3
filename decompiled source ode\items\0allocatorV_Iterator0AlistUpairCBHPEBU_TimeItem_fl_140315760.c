/*
 * Function: ??0?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@2@@std@@@std@@QEAA@AEBV01@@Z
 * Address: 0x140315760
 */

void __fastcall std::allocator<std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>>::allocator<std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>>(std::allocator<std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> > *this, std::allocator<std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> > *__formal)
{
  ;
}
