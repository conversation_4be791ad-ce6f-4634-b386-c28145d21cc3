/*
 * Function: j_?ChannelMessageSeriesEnd@?$InputRejecting@VBufferedTransformation@CryptoPP@@@CryptoPP@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H_N@Z
 * Address: 0x14000A191
 */

void __fastcall __noreturn CryptoPP::InputRejecting<CryptoPP::BufferedTransformation>::ChannelMessageSeriesEnd(CryptoPP::InputRejecting<CryptoPP::BufferedTransformation> *this, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *__formal, int a3, bool a4)
{
  CryptoPP::InputRejecting<CryptoPP::BufferedTransformation>::ChannelMessageSeriesEnd(this, __formal, a3, a4);
}
