/*
 * Function: ??0?$IteratedHashBase@IVHashTransformation@CryptoPP@@@CryptoPP@@QEAA@AEBV01@@Z
 * Address: 0x140458130
 */

void __fastcall CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation>::IteratedHashBase<unsigned int,CryptoPP::HashTransformation>(CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation> *this, CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation> *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation> *v5; // [sp+30h] [bp+8h]@1
  CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation> *__thata; // [sp+38h] [bp+10h]@1

  __thata = __that;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CryptoPP::HashTransformation::HashTransformation(
    (CryptoPP::HashTransformation *)&v5->vfptr,
    (CryptoPP::HashTransformation *)&__that->vfptr);
  v5->m_countLo = __thata->m_countLo;
  v5->m_countHi = __thata->m_countHi;
}
