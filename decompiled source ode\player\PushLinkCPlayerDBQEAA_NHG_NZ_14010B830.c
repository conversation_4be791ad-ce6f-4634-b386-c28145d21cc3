/*
 * Function: ?PushLink@CPlayerDB@@QEAA_NHG_N@Z
 * Address: 0x14010B830
 */

char __fastcall CPlayerDB::PushLink(CPlayerDB *this, int nLinkIndex, unsigned __int16 wSerail, bool bInit)
{
  int *v4; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CPlayerDB *v8; // [sp+20h] [bp+8h]@1

  v8 = this;
  v4 = &j;
  for ( i = 4i64; i; --i )
  {
    *v4 = -858993460;
    ++v4;
  }
  if ( bInit )
  {
    for ( j = 0; j < 50; ++j )
    {
      if ( v8->m_QLink[j].byLinkIndex != 255 && v8->m_QLink[j].wSerial == wSerail )
        return 0;
    }
  }
  v8->m_QLink[nLinkIndex].byLinkIndex = nLinkIndex;
  v8->m_QLink[nLinkIndex].wSerial = wSerail;
  return 1;
}
