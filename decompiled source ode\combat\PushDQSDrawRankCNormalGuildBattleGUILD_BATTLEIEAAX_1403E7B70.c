/*
 * Function: ?PushDQSDrawRank@CNormalGuildBattle@GUILD_BATTLE@@IEAAXXZ
 * Address: 0x1403E7B70
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::PushDQSDrawRank(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  __int64 v4; // [sp+0h] [bp-68h]@1
  _qry_case_updatedrawguildbattlerank v5; // [sp+38h] [bp-30h]@4
  GUILD_BATTLE::CNormalGuildBattle *v6; // [sp+70h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5.by1PRace = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuild(&v6->m_k1P)->m_byRace;
  v5.dw1PGuildSerial = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v6->m_k1P);
  v5.by2PRace = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuild(&v6->m_k2P)->m_byRace;
  v5.dw2PGuildSerial = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v6->m_k2P);
  v3 = _qry_case_updatedrawguildbattlerank::size(&v5);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 32, &v5.by1PRace, v3);
}
