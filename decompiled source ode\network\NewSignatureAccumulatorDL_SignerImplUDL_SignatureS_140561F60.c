/*
 * Function: ?NewSignatureAccumulator@?$DL_SignerImpl@U?$DL_SignatureSchemeOptions@UDSA@CryptoPP@@UDL_Keys_DSA@2@V?$DL_Algorithm_GDSA@VInteger@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@@CryptoPP@@@CryptoPP@@UEBAPEAVPK_MessageAccumulator@2@AEAVRandomNumberGenerator@2@@Z
 * Address: 0x140561F60
 */

__int64 __fastcall CryptoPP::DL_SignerImpl<CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DSA,CryptoPP::DL_Keys_DSA,CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>>::NewSignatureAccumulator(__int64 a1, __int64 a2)
{
  __int64 v2; // rax@2
  __int64 v3; // rax@4
  __int64 v4; // rax@4
  char v6; // [sp+20h] [bp-38h]@4
  __int64 v7; // [sp+28h] [bp-30h]@4
  void *v8; // [sp+30h] [bp-28h]@1
  __int64 v9; // [sp+38h] [bp-20h]@4
  __int64 v10; // [sp+40h] [bp-18h]@1
  __int64 v11; // [sp+48h] [bp-10h]@2
  __int64 v12; // [sp+60h] [bp+8h]@1
  __int64 v13; // [sp+68h] [bp+10h]@1

  v13 = a2;
  v12 = a1;
  v10 = -2i64;
  v8 = operator new(0x180ui64);
  if ( v8 )
  {
    LODWORD(v2) = CryptoPP::PK_MessageAccumulatorImpl<CryptoPP::SHA1>::PK_MessageAccumulatorImpl<CryptoPP::SHA1>(v8);
    v11 = v2;
  }
  else
  {
    v11 = 0i64;
  }
  v7 = v11;
  std::auto_ptr<CryptoPP::PK_MessageAccumulatorBase>::auto_ptr<CryptoPP::PK_MessageAccumulatorBase>(&v6, v11);
  LODWORD(v3) = std::auto_ptr<CryptoPP::PK_MessageAccumulatorBase>::operator*(&v6);
  CryptoPP::DL_SignerBase<CryptoPP::Integer>::RestartMessageAccumulator(v12, v13, v3);
  LODWORD(v4) = std::auto_ptr<CryptoPP::PK_MessageAccumulatorBase>::release(&v6);
  v9 = v4;
  std::auto_ptr<CryptoPP::PK_MessageAccumulatorBase>::~auto_ptr<CryptoPP::PK_MessageAccumulatorBase>(&v6);
  return v9;
}
