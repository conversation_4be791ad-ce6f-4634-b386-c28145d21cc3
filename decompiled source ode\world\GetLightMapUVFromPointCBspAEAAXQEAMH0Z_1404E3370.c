/*
 * Function: ?GetLightMapUVFromPoint@CBsp@@AEAAXQEAMH0@Z
 * Address: 0x1404E3370
 */

void __fastcall CBsp::GetLightMapUVFromPoint(CBsp *this, float *const a2, int a3, float *const a4)
{
  _BSP_C_FACE *v4; // r10@1
  float *v5; // r14@1
  CBsp *v6; // rbp@1
  signed __int64 v7; // r8@1
  __int64 v8; // rdi@1
  int v9; // er13@1
  float (*v10)[3]; // r9@2
  unsigned int *v11; // rdx@2
  float v12; // xmm7_4@2
  float v13; // xmm8_4@2
  float v14; // xmm9_4@2
  __int64 v15; // rax@2
  __int64 v16; // rbx@2
  int v17; // er11@2
  signed __int64 v18; // r10@2
  float v19; // xmm11_4@2
  float v20; // xmm12_4@2
  float v21; // xmm13_4@2
  __int64 v22; // rax@3
  int v23; // esi@3
  int v24; // er12@3
  float v25; // xmm6_4@3
  float v26; // xmm3_4@3
  float v27; // xmm5_4@3
  __int64 v28; // r10@3
  int v29; // er11@3
  float v30; // xmm5_4@3
  unsigned int *v31; // rcx@4
  __int64 v32; // rax@4
  float v33; // xmm6_4@4
  float v34; // xmm3_4@4
  float v35; // xmm5_4@4
  int v36; // xmm1_4@4
  float v37; // xmm0_4@4
  float v38; // xmm5_4@4
  unsigned int *v39; // rcx@5
  __int64 v40; // rax@5
  float v41; // xmm6_4@5
  float v42; // xmm3_4@5
  float v43; // xmm5_4@5
  int v44; // xmm1_4@5
  float v45; // xmm0_4@5
  float v46; // xmm5_4@5
  float (*v47)[3]; // r13@8
  float v48; // xmm15_4@8
  float v49; // xmm14_4@8
  float v50; // xmm6_4@8
  __int64 v51; // rcx@8
  float v52; // xmm0_4@8
  float v53; // xmm1_4@8
  float v54; // xmm0_4@8
  __int64 v55; // rcx@8
  float v56; // xmm7_4@8
  float v57; // xmm0_4@8
  __int64 v58; // rcx@8
  float v59; // xmm8_4@8
  float v60; // xmm0_4@8
  int v61; // er8@10
  int v62; // eax@10
  unsigned int *v63; // rcx@15
  __int64 v64; // rsi@15
  __int64 v65; // rdi@15
  signed __int64 v66; // rdx@15
  __int64 v67; // rax@15
  float v68; // xmm11_4@15
  float v69; // xmm13_4@15
  float v70; // xmm10_4@15
  float v71; // xmm0_4@15
  unsigned int *v72; // rcx@15
  float v73; // xmm12_4@15
  __int64 v74; // rax@15
  float v75; // xmm7_4@15
  float v76; // xmm9_4@15
  float v77; // xmm6_4@15
  float v78; // xmm0_4@15
  __int64 v79; // rcx@15
  float v80; // xmm8_4@15
  float v81; // xmm15_4@15
  float v82; // xmm1_4@15
  float v83; // xmm15_4@15
  float v84; // xmm13_4@15
  float v85; // xmm9_4@15
  float v86; // xmm7_4@15
  float v87; // xmm0_4@15
  unsigned int *v88; // r8@15
  float v89; // xmm7_4@15
  __int64 v90; // rax@15
  float v91; // xmm5_4@15
  float v92; // xmm4_4@15
  float v93; // xmm3_4@15
  float v94; // xmm0_4@15
  __int16 (*v95)[2]; // rcx@15
  float v96; // xmm14_4@15
  float v97; // xmm5_4@15
  float v98; // xmm6_4@15
  float v99; // xmm1_4@15
  float v100; // xmm4_4@15
  float v101; // xmm3_4@15
  float v102; // [sp+20h] [bp-138h]@3
  float v103; // [sp+24h] [bp-134h]@3
  float v104; // [sp+28h] [bp-130h]@3
  float v105; // [sp+30h] [bp-128h]@3
  float v106; // [sp+34h] [bp-124h]@3
  float v107; // [sp+38h] [bp-120h]@3
  float v108; // [sp+40h] [bp-118h]@4
  int v109; // [sp+44h] [bp-114h]@4
  float v110; // [sp+48h] [bp-110h]@4
  float v111; // [sp+50h] [bp-108h]@4
  float v112; // [sp+54h] [bp-104h]@4
  float v113; // [sp+58h] [bp-100h]@4
  float v114; // [sp+60h] [bp-F8h]@5
  int v115; // [sp+64h] [bp-F4h]@5
  float v116; // [sp+68h] [bp-F0h]@5
  float v117; // [sp+70h] [bp-E8h]@5
  float v118; // [sp+74h] [bp-E4h]@5
  float v119; // [sp+78h] [bp-E0h]@5
  float *v120; // [sp+168h] [bp+10h]@1
  int v121; // [sp+170h] [bp+18h]@0
  float v122; // [sp+170h] [bp+18h]@15
  float *v123; // [sp+178h] [bp+20h]@1
  float v124; // [sp+178h] [bp+20h]@15

  v123 = a4;
  v120 = a2;
  v4 = this->mCFace;
  v5 = a4;
  v6 = this;
  v7 = 3i64 * a3;
  v8 = v4[(unsigned __int64)(8 * v7) / 0x18].VStartId;
  v9 = v4[(unsigned __int64)(8 * v7) / 0x18].VNum - 2;
  if ( v9 <= 0 )
  {
    LODWORD(v8) = v121;
    v23 = v121;
    v24 = v121;
  }
  else
  {
    v10 = this->mCVertex;
    v11 = this->mCVertexId;
    v12 = v5[1];
    v13 = *v5;
    v14 = v5[2];
    v15 = v11[v8];
    v16 = (__int64)v4[(unsigned __int64)(8 * v7) / 0x18].Normal;
    v17 = v8 + 2;
    v18 = 4 * v8 + 8;
    v19 = this->mCVertex[v15][0];
    v20 = this->mCVertex[v15][1];
    v21 = this->mCVertex[v15][2];
    do
    {
      v22 = *(unsigned int *)((char *)v11 + v18 - 4);
      v23 = v17 - 1;
      v24 = v17;
      v25 = v10[v22][0];
      v26 = v10[v22][1];
      v27 = v10[v22][2];
      v102 = v19 - v25;
      v103 = v20 - v26;
      v104 = v21 - v27;
      sub_1404E2FB0(v16, (__int64)&v102, (__int64)&v105);
      if ( (float)((float)((float)((float)(v12 - v26) * v106) + (float)((float)(v13 - v25) * v105))
                 + (float)((float)(v14 - v30) * v107)) <= 0.0 )
      {
        v31 = v6->mCVertexId;
        v32 = *(unsigned int *)((char *)v31 + v28 - 4);
        v33 = v10[*(unsigned int *)((char *)v31 + v28)][0];
        v34 = v10[*(unsigned int *)((char *)v31 + v28)][1];
        v35 = v10[*(unsigned int *)((char *)v31 + v28)][2];
        *(float *)&v36 = v10[v32][1] - v34;
        v108 = v10[v32][0] - v33;
        v37 = v10[v32][2];
        v109 = v36;
        v110 = v37 - v35;
        sub_1404E2FB0(v16, (__int64)&v108, (__int64)&v111);
        if ( (float)((float)((float)((float)(v12 - v34) * v112) + (float)((float)(v13 - v33) * v111))
                   + (float)((float)(v14 - v38) * v113)) <= 0.0 )
        {
          v39 = v6->mCVertexId;
          v40 = *(unsigned int *)((char *)v39 + v28);
          v41 = v10[v39[v8]][0];
          v42 = v10[v39[v8]][1];
          v43 = v10[v39[v8]][2];
          *(float *)&v44 = v10[v40][1] - v42;
          v114 = v10[v40][0] - v41;
          v45 = v10[v40][2];
          v115 = v44;
          v116 = v45 - v43;
          sub_1404E2FB0(v16, (__int64)&v114, (__int64)&v117);
          if ( (float)((float)((float)((float)(v12 - v42) * v118) + (float)((float)(v13 - v41) * v117))
                     + (float)((float)(v14 - v46) * v119)) <= 0.0 )
            break;
        }
      }
      v11 = v6->mCVertexId;
      v17 = v29 + 1;
      v18 = v28 + 4;
    }
    while ( -2 - (signed int)v8 + v17 < v9 );
    v5 = v123;
  }
  v47 = v6->mCVertex;
  v48 = *v5;
  v49 = v5[1];
  v50 = v5[2];
  v51 = v6->mCVertexId[(signed int)v8];
  v52 = v6->mCVertex[v51][1];
  v53 = v6->mCVertex[v51][2];
  v54 = sqrtf_0((float)((float)((float)(v52 - v49) * (float)(v52 - v49))
                      + (float)((float)(v6->mCVertex[v51][0] - v48) * (float)(v6->mCVertex[v51][0] - v48))) + (float)((float)(v53 - v50) * (float)(v53 - v50)));
  v55 = v6->mCVertexId[v23];
  v56 = v54;
  v57 = sqrtf_0((float)((float)((float)(v47[v55][1] - v49) * (float)(v47[v55][1] - v49))
                      + (float)((float)(v47[v55][0] - v48) * (float)(v47[v55][0] - v48))) + (float)((float)(v47[v55][2] - v50)
                                                                                                  * (float)(v47[v55][2] - v50)));
  v58 = v6->mCVertexId[v24];
  v59 = v57;
  v60 = sqrtf_0((float)((float)((float)(v47[v58][1] - v49) * (float)(v47[v58][1] - v49))
                      + (float)((float)(v47[v58][0] - v48) * (float)(v47[v58][0] - v48))) + (float)((float)(v47[v58][2] - v50)
                                                                                                  * (float)(v47[v58][2] - v50)));
  if ( v56 <= v59 )
  {
    if ( v59 > v60 )
    {
      v62 = v24;
      v61 = v23;
      v24 = v8;
      goto LABEL_15;
    }
LABEL_14:
    v61 = v24;
    v62 = v8;
    v24 = v23;
    goto LABEL_15;
  }
  if ( v56 <= v60 )
    goto LABEL_14;
  v61 = v8;
  v62 = v23;
LABEL_15:
  v63 = v6->mCVertexId;
  v64 = v62;
  v65 = v61;
  v66 = 3i64 * v63[v62];
  v67 = v63[v61];
  v68 = v47[(unsigned __int64)(4 * v66) / 0xC][0] - v47[v67][0];
  v69 = v47[(unsigned __int64)(4 * v66) / 0xC][1] - v47[v67][1];
  v70 = v47[(unsigned __int64)(4 * v66) / 0xC][2] - v47[v67][2];
  v71 = sqrtf_0((float)((float)(v69 * v69) + (float)(v68 * v68)) + (float)(v70 * v70));
  v72 = v6->mCVertexId;
  v73 = v71;
  v74 = v72[v65];
  v75 = v47[v72[v24]][0] - v47[v74][0];
  v76 = v47[v72[v24]][1] - v47[v74][1];
  v77 = v47[v72[v24]][2] - v47[v74][2];
  v78 = sqrtf_0((float)((float)(v76 * v76) + (float)(v75 * v75)) + (float)(v77 * v77));
  v79 = v6->mCVertexId[v65];
  v80 = v78;
  v81 = v48 - v47[v79][0];
  v122 = v49 - v47[v79][1];
  v124 = v5[2] - v47[v79][2];
  v82 = sqrtf_0((float)((float)(v122 * v122) + (float)(v81 * v81)) + (float)(v124 * v124));
  v83 = v81 / v82;
  v84 = (float)((float)((float)(v69 / v73) * (float)(v122 / v82)) + (float)((float)(v68 / v73) * v83))
      + (float)((float)(v70 / v73) * (float)(v124 / v82));
  v85 = (float)((float)((float)(v76 / v80) * (float)(v122 / v82)) + (float)((float)(v75 / v80) * v83))
      + (float)((float)(v77 / v80) * (float)(v124 / v82));
  v86 = sqrtf_0(1.0 - (float)(v84 * v84)) * v73;
  v87 = sqrtf_0(1.0 - (float)(v85 * v85));
  v88 = v6->mCVertexId;
  v89 = v86 / (float)((float)(v87 * v80) + v86);
  v90 = v88[v65];
  v91 = (float)((float)((float)(v47[v88[v24]][1] - v47[v88[v64]][1]) * v89) + v47[v88[v64]][1]) - v47[v90][1];
  v92 = (float)((float)((float)(v47[v88[v24]][0] - v47[v88[v64]][0]) * v89) + v47[v88[v64]][0]) - v47[v90][0];
  v93 = (float)((float)((float)(v47[v88[v24]][2] - v47[v88[v64]][2]) * v89) + v47[v88[v64]][2]) - v47[v90][2];
  v94 = sqrtf_0((float)((float)(v91 * v91) + (float)(v92 * v92)) + (float)(v93 * v93));
  v95 = v6->mLgtUV;
  v96 = v82 / v94;
  v97 = (float)v95[v65][0] / 32767.0;
  v98 = (float)v95[v65][1] / 32767.0;
  v99 = (float)v95[v64][0] / 32767.0;
  v100 = (float)v95[v64][1] / 32767.0;
  v101 = (float)((float)v95[v24][1] / 32767.0) - v100;
  *v120 = (float)((float)((float)((float)((float)((float)((float)v95[v24][0] / 32767.0) - v99) * v89) + v99) - v97) * v96)
        + v97;
  v120[1] = (float)((float)((float)((float)(v101 * v89) + v100) - v98) * v96) + v98;
}
