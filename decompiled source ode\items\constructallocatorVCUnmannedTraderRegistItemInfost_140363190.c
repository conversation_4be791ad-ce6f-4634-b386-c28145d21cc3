/*
 * Function: ?construct@?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@QEAAXPEAVCUnmannedTraderRegistItemInfo@@AEBV3@@Z
 * Address: 0x140363190
 */

void __fastcall std::allocator<CUnmannedTraderRegistItemInfo>::construct(std::allocator<CUnmannedTraderRegistItemInfo> *this, CUnmannedTraderRegistItemInfo *_Ptr, CUnmannedTraderRegistItemInfo *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1

  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Construct<CUnmannedTraderRegistItemInfo,CUnmannedTraderRegistItemInfo>(_Ptr, _Val);
}
