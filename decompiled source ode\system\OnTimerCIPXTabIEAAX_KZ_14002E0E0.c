/*
 * Function: ?OnTimer@CIPXTab@@IEAAX_K@Z
 * Address: 0x14002E0E0
 */

void __fastcall CIPXTab::OnTimer(CIPXTab *this, unsigned __int64 nIDEvent)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CIPXTab *v5; // [sp+30h] [bp+8h]@1
  unsigned __int64 v6; // [sp+38h] [bp+10h]@1

  v6 = nIDEvent;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CIPXTab::UpdateTab(v5);
  CWnd::OnTimer((CWnd *)&v5->vfptr, v6);
}
