/*
 * Function: ?FindEffectDst@CCharacter@@QEAAHHHH_NPEAV1@PEADPEAPEAV1@@Z
 * Address: 0x140177010
 */

__int64 __fastcall CCharacter::FindEffectDst(CCharacter *this, int nEffectCode, int nAreaType, int nLv, bool bBenefit, CCharacter *pOriDst, char *psActableDst, CCharacter **ppDsts)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  int v10; // eax@11
  int v11; // eax@12
  int v12; // eax@13
  int v13; // eax@14
  __int64 result; // rax@9
  __int64 v15; // [sp+0h] [bp-58h]@1
  float *pTar; // [sp+20h] [bp-38h]@11
  CCharacter *pOriTar; // [sp+28h] [bp-30h]@11
  CCharacter **v18; // [sp+30h] [bp-28h]@11
  CCharacter **v19; // [sp+38h] [bp-20h]@12
  unsigned int v20; // [sp+40h] [bp-18h]@4
  int v21; // [sp+44h] [bp-14h]@4
  CCharacter *v22; // [sp+60h] [bp+8h]@1

  v22 = this;
  v8 = &v15;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v20 = 0;
  *ppDsts = pOriDst;
  ++v20;
  v21 = nAreaType;
  if ( nAreaType )
  {
    switch ( v21 )
    {
      case 1:
        v18 = &ppDsts[v20];
        pOriTar = (CCharacter *)psActableDst;
        pTar = pOriDst->m_fCurPos;
        v10 = CCharacter::_GetAreaEffectMember(
                v22,
                pOriDst,
                bBenefit,
                s_nLimitRadius_0[nLv],
                pOriDst->m_fCurPos,
                psActableDst,
                v18);
        result = v10 + v20;
        break;
      case 2:
        v12 = CCharacter::_GetPartyEffectMember(v22, pOriDst, 0, &ppDsts[v20]);
        result = v12 + v20;
        break;
      case 3:
        v13 = CCharacter::_GetPartyEffectMember(v22, pOriDst, 1, &ppDsts[v20]);
        result = v13 + v20;
        break;
      case 10:
        v19 = &ppDsts[v20];
        v18 = (CCharacter **)psActableDst;
        pOriTar = pOriDst;
        LODWORD(pTar) = s_nLimitAngle_0;
        v11 = CCharacter::_GetFlashEffectMember(
                v22,
                pOriDst,
                bBenefit,
                s_nLimitRadius_0[nLv],
                s_nLimitAngle_0,
                pOriDst,
                psActableDst,
                v19);
        result = v11 + v20;
        break;
      default:
        result = v20;
        break;
    }
  }
  else
  {
    result = v20;
  }
  return result;
}
