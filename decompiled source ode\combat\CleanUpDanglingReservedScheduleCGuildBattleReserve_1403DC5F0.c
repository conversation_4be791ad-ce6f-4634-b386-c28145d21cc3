/*
 * Function: ?CleanUpDanglingReservedSchedule@CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403DC5F0
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::CleanUpDanglingReservedSchedule(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < v6->m_uiMapCnt; ++j )
  {
    if ( v6->m_ppkReservedSchedule[j]
      && !GUILD_BATTLE::CGuildBattleReservedSchedule::CleanUpDanglingReservedSchedule(v6->m_ppkReservedSchedule[j]) )
    {
      return 0;
    }
  }
  return 1;
}
