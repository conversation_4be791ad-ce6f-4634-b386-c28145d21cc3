/*
 * Function: ?Put2@PK_DefaultDecryptionFilter@CryptoPP@@UEAA_KPEBE_KH_N@Z
 * Address: 0x1405F7110
 */

unsigned __int64 __fastcall CryptoPP::PK_DefaultDecryptionFilter::Put2(CryptoPP::PK_DefaultDecryptionFilter *this, const unsigned __int8 *a2, __int64 a3, int a4, bool a5)
{
  __int64 v5; // rax@5
  unsigned __int64 v6; // rax@8
  char *v7; // rax@8
  char *v8; // rax@8
  __int64 v9; // ST30_8@8
  const void *v10; // rax@8
  __int64 v11; // rax@9
  __int64 v12; // rax@9
  char v13; // al@12
  __int64 v14; // r9@12
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v16; // [sp+40h] [bp-1F8h]@8
  unsigned __int64 size; // [sp+58h] [bp-1E0h]@5
  unsigned __int64 v18; // [sp+60h] [bp-1D8h]@8
  CryptoPP::InvalidArgument v19; // [sp+68h] [bp-1D0h]@6
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+B8h] [bp-180h]@6
  unsigned __int8 v21; // [sp+E8h] [bp-150h]@6
  char v22; // [sp+F0h] [bp-148h]@8
  char v23; // [sp+100h] [bp-138h]@9
  char v24; // [sp+150h] [bp-E8h]@9
  char v25; // [sp+180h] [bp-B8h]@9
  __int64 v26; // [sp+1B0h] [bp-88h]@13
  __int64 v27; // [sp+1B8h] [bp-80h]@13
  char v28; // [sp+1C0h] [bp-78h]@8
  __int64 v29; // [sp+1D0h] [bp-68h]@1
  int v30; // [sp+1D8h] [bp-60h]@1
  __int64 v31; // [sp+1E0h] [bp-58h]@8
  char *v32; // [sp+1E8h] [bp-50h]@8
  __int64 *v33; // [sp+1F0h] [bp-48h]@8
  __int64 v34; // [sp+1F8h] [bp-40h]@8
  __int64 v35; // [sp+200h] [bp-38h]@9
  __int64 v36; // [sp+208h] [bp-30h]@9
  __int64 v37; // [sp+210h] [bp-28h]@9
  __int64 v38; // [sp+218h] [bp-20h]@9
  CryptoPP::PK_DefaultDecryptionFilter *v39; // [sp+240h] [bp+8h]@1
  int v40; // [sp+258h] [bp+20h]@1

  v40 = a4;
  v39 = this;
  v29 = -2i64;
  v30 = *((_DWORD *)this + 10);
  if ( v30 )
  {
    if ( v30 != 1 )
      _wassert(L"false", L"D:\\RF Project\\RF_Server64\\28 Crypto++\\cryptlib.cpp", 0x279u);
  }
  else
  {
    *((_QWORD *)this + 4) = 0i64;
    CryptoPP::BufferedTransformation::Put((CryptoPP::BufferedTransformation *)this + 3, a2, a3);
    if ( !v40 )
      return 0i64;
    v5 = CryptoPP::ByteQueue::CurrentSize((CryptoPP::ByteQueue *)((char *)v39 + 72));
    if ( !CryptoPP::SafeConvert<unsigned __int64,unsigned __int64>(v5, &size) )
    {
      memset(&v21, 0, sizeof(v21));
      std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
        &s,
        "PK_DefaultDecryptionFilter: ciphertext too long",
        v21);
      CryptoPP::InvalidArgument::InvalidArgument(&v19, &s);
      CxxThrowException_0((__int64)&v19, (__int64)&TI3_AVInvalidArgument_CryptoPP__);
    }
    LODWORD(v6) = (*(int (__fastcall **)(_QWORD, unsigned __int64))(**((_QWORD **)v39 + 7) + 8i64))(
                    *((_QWORD *)v39 + 7),
                    size);
    v18 = v6;
    CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
      &v16,
      size);
    v7 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v16);
    v31 = *((_QWORD *)v39 + 9);
    (*(void (__fastcall **)(signed __int64, char *, unsigned __int64))(v31 + 136))((signed __int64)v39 + 72, v7, size);
    CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::resize((__int64)v39 + 152, v18);
    v32 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)((char *)v39 + 152));
    v8 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v16);
    v33 = (__int64 *)*((_QWORD *)v39 + 7);
    v34 = *v33;
    v9 = *((_QWORD *)v39 + 8);
    LODWORD(v10) = (*(int (__fastcall **)(__int64 *, char *, _QWORD, char *))(v34 + 48))(
                     v33,
                     &v22,
                     *((_QWORD *)v39 + 6),
                     v8);
    qmemcpy(&v28, v10, 0x10ui64);
    qmemcpy((char *)v39 + 176, &v28, 0x10ui64);
    if ( !*((_BYTE *)v39 + 176) )
    {
      LODWORD(v11) = (*(int (__fastcall **)(signed __int64, char *))(*(_QWORD *)(*((_QWORD *)v39 + 7) + 8i64) + 16i64))(
                       *((_QWORD *)v39 + 7) + 8i64,
                       &v24);
      v35 = v11;
      v36 = v11;
      LODWORD(v12) = std::operator+<char,std::char_traits<char>,std::allocator<char>>(&v25, v11, ": invalid ciphertext");
      v37 = v12;
      v38 = v12;
      CryptoPP::InvalidCiphertext::InvalidCiphertext(&v23, v12);
      CxxThrowException_0((__int64)&v23, (__int64)&TI4_AVInvalidCiphertext_CryptoPP__);
    }
    CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v16);
  }
  v13 = (unsigned __int64)CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)((char *)v39 + 152));
  v14 = *((_QWORD *)v39 + 23);
  if ( CryptoPP::Filter::Output((_DWORD)v39, 1, v13) )
  {
    v26 = *((_QWORD *)v39 + 23) - *((_QWORD *)v39 + 4);
    v27 = 1i64;
    return *CryptoPP::STDMAX<unsigned __int64>(&v27, &v26);
  }
  return 0i64;
}
