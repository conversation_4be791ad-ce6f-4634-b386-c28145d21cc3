/*
 * Function: ?GetAngle@CMonsterHelper@@SAMQEAM0@Z
 * Address: 0x1401597A0
 */

float __fastcall CMonsterHelper::GetAngle(float *mon, float *plr)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float result; // xmm0_4@8
  __int64 v5; // [sp+0h] [bp-78h]@1
  __int128 v6; // [sp+20h] [bp-58h]@8
  D3DXVECTOR2 pV1; // [sp+38h] [bp-40h]@4
  D3DXVECTOR2 pV2; // [sp+58h] [bp-20h]@4
  char v9; // [sp+64h] [bp-14h]@4
  int v10; // [sp+68h] [bp-10h]@6
  float v11; // [sp+6Ch] [bp-Ch]@6
  float *v12; // [sp+80h] [bp+8h]@1
  float *v13; // [sp+88h] [bp+10h]@1

  v13 = plr;
  v12 = mon;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  D3DXVECTOR2::D3DXVECTOR2(&pV1);
  D3DXVECTOR2::D3DXVECTOR2(&pV2);
  pV1.x = *v12 - *v13;
  pV1.y = v12[2] - v13[2];
  pV2.x = FLOAT_1_0;
  LODWORD(pV2.y) = 0;
  D3DXVec2Normalize_0(&pV1, &pV1);
  D3DXVec2Normalize_0(&pV2, &pV2);
  v9 = 1;
  if ( pV1.y <= 0.0 )
    v9 = 0;
  D3DXVec2Dot(&pV1, &pV2);
  v10 = 0;
  acos(0.0);
  v11 = 0.0;
  if ( !v9 )
    v11 = -0.0 - v11;
  _mm_storeu_si128((__m128i *)&v6, (__m128i)LODWORD(v11));
  LODWORD(result) = (unsigned __int128)_mm_loadu_si128((const __m128i *)&v6);
  return result;
}
