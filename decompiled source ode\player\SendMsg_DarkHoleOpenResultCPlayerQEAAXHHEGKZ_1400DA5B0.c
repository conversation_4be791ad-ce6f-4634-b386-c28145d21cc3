/*
 * Function: ?SendMsg_DarkHoleOpenResult@CPlayer@@QEAAXHHEGK@Z
 * Address: 0x1400DA5B0
 */

void __fastcall CPlayer::SendMsg_DarkHoleOpenResult(CPlayer *this, int n, int bPartyOnly, char byErrCode, unsigned __int16 wHoleIndex, unsigned int dwHoleSerial)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v8; // ax@9
  unsigned __int16 v9; // ax@12
  __int64 v10; // [sp+0h] [bp-D8h]@1
  _darkhole_open_result_zocl v11; // [sp+34h] [bp-A4h]@4
  char pbyType; // [sp+54h] [bp-84h]@4
  char v13; // [sp+55h] [bp-83h]@4
  CPlayer *out_ppMember; // [sp+80h] [bp-58h]@5
  char v15; // [sp+C4h] [bp-14h]@5
  int j; // [sp+C8h] [bp-10h]@5
  CPlayer *v17; // [sp+E0h] [bp+8h]@1
  int dwClientIndex; // [sp+E8h] [bp+10h]@1

  dwClientIndex = n;
  v17 = this;
  v6 = &v10;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v11.byRetCode = byErrCode;
  v11.wHoleIndex = wHoleIndex;
  v11.dwHoleSerial = dwHoleSerial;
  pbyType = 35;
  v13 = -54;
  if ( bPartyOnly )
  {
    v15 = CPlayer::_GetPartyMemberInCircle(v17, &out_ppMember, 8, 1);
    for ( j = 0; j < (unsigned __int8)v15; ++j )
    {
      if ( *(&out_ppMember + j) )
      {
        if ( (*(&out_ppMember + j))->m_bLive )
        {
          v8 = _darkhole_open_result_zocl::size(&v11);
          CNetProcess::LoadSendMsg(unk_1414F2088, (*(&out_ppMember + j))->m_id.wIndex, &pbyType, &v11.byRetCode, v8);
        }
      }
    }
  }
  else
  {
    v9 = _darkhole_open_result_zocl::size(&v11);
    CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &v11.byRetCode, v9);
    CGameObject::CircleReport((CGameObject *)&v17->vfptr, &pbyType, &v11.byRetCode, 7, 0);
  }
}
