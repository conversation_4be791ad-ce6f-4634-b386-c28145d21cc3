/*
 * Function: ??0CRFNewDatabase@@QEAA@XZ
 * Address: 0x140485F80
 */

void __fastcall CRFNewDatabase::CRFNewDatabase(CRFNewDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CRFNewDatabase *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  v5->vfptr = (CRFNewDatabaseVtbl *)&CRFNewDatabase::`vftable';
  v5->m_hEnv = 0i64;
  v5->m_hDbc = 0i64;
  v5->m_hStmtSelect = 0i64;
  v5->m_hStmtUpdate = 0i64;
  v5->m_bConectionActive = 0;
  CLogFile::CLogFile(&v5->m_ProcessLogW);
  CLogFile::CLogFile(&v5->m_ErrorLogW);
  CLogFile::CLogFile(&v5->m_ProcessLogA);
  CLogFile::CLogFile(&v5->m_ErrorLogA);
  v5->m_bReconnectFailExit = 1;
  memset_0(v5->m_szOdbcName, 0, 0x20ui64);
  memset_0(v5->m_szAccountName, 0, 0x20ui64);
  memset_0(v5->m_szPassword, 0, 0x20ui64);
  v5->m_szLogUpperPath[0] = 0;
}
