/*
 * Function: ?_db_Load_PvpPointLimitData@CMainThread@@AEAAEKAEAV_PVPPOINT_LIMIT_DB_BASE@@@Z
 * Address: 0x1401A96B0
 */

char __fastcall CMainThread::_db_Load_PvpPointLimitData(CMainThread *this, unsigned int dwSerial, _PVPPOINT_LIMIT_DB_BASE *kData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-68h]@1
  __int64 Dst; // [sp+28h] [bp-40h]@4
  bool v8; // [sp+30h] [bp-38h]@10
  char v9; // [sp+31h] [bp-37h]@10
  long double v10; // [sp+38h] [bp-30h]@10
  long double v11; // [sp+40h] [bp-28h]@10
  long double v12; // [sp+48h] [bp-20h]@10
  char v13; // [sp+54h] [bp-14h]@4
  CMainThread *v14; // [sp+70h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+78h] [bp+10h]@1
  _PVPPOINT_LIMIT_DB_BASE *v16; // [sp+80h] [bp+18h]@1

  v16 = kData;
  dwSeriala = dwSerial;
  v14 = this;
  v3 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  memset_0(&Dst, 0, 0x28ui64);
  v13 = CRFWorldDatabase::Select_PvpPointLimitInfo(v14->m_pWorldDB, dwSeriala, (_pvppointlimit_info *)&Dst);
  if ( v13 == 1 )
  {
    result = 24;
  }
  else if ( v13 == 2 )
  {
    if ( CRFWorldDatabase::Insert_PvpPointLimitInfoRecord(v14->m_pWorldDB, dwSeriala) )
      result = 0;
    else
      result = 24;
  }
  else
  {
    v16->tUpdatedate = Dst;
    v16->bUseUp = v8;
    v16->byLimitRate = v9;
    v16->dOriginalPoint = v10;
    v16->dLimitPoint = v11;
    v16->dUsePoint = v12;
    result = 0;
  }
  return result;
}
