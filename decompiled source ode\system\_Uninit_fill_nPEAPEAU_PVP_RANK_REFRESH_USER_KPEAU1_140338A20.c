/*
 * Function: ??$_Uninit_fill_n@PEAPEAU_PVP_RANK_REFRESH_USER@@_KPEAU1@V?$allocator@PEAU_PVP_RANK_REFRESH_USER@@@std@@@std@@YAXPEAPEAU_PVP_RANK_REFRESH_USER@@_KAEBQEAU1@AEAV?$allocator@PEAU_PVP_RANK_REFRESH_USER@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140338A20
 */

void __fastcall std::_Uninit_fill_n<_PVP_RANK_REFRESH_USER * *,unsigned __int64,_PVP_RANK_REFRESH_USER *,std::allocator<_PVP_RANK_REFRESH_USER *>>(_PVP_RANK_REFRESH_USER **_First, unsigned __int64 _Count, _PVP_RANK_REFRESH_USER *const *_Val, std::allocator<_PVP_RANK_REFRESH_USER *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-28h]@1
  _PVP_RANK_REFRESH_USER **_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  stdext::unchecked_fill_n<_PVP_RANK_REFRESH_USER * *,unsigned __int64,_PVP_RANK_REFRESH_USER *>(_Firsta, _Count, _Val);
}
