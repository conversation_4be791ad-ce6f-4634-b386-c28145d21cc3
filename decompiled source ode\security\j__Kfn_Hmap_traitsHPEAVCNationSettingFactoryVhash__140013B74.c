/*
 * Function: j_?_Kfn@?$_Hmap_traits@HPEAVCNationSettingFactory@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@std@@$0A@@stdext@@SAAEBHAEBU?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@Z
 * Address: 0x140013B74
 */

const int *__fastcall stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>,0>::_Kfn(stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationSettingFactory *> >,0> *this, std::pair<int const ,CNationSettingFactory *> *_Val)
{
  return stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>,0>::_Kfn(
           this,
           _Val);
}
