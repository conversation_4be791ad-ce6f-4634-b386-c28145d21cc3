/*
 * Function: ??$fill@PEAVCMoveMapLimitRightInfo@@V1@@std@@YAXPEAVCMoveMapLimitRightInfo@@0AEBV1@@Z
 * Address: 0x1403B22D0
 */

void __fastcall std::fill<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo>(CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, CMoveMapLimitRightInfo *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMoveMapLimitRightInfo *_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Fill<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo>(_Firsta, _Last, _Val);
}
