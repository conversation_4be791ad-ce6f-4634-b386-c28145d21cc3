/*
 * Function: ?SendMsg_ConnectNewUser@PatriarchElectProcessor@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x1402BB050
 */

void __fastcall PatriarchElectProcessor::SendMsg_ConnectNewUser(PatriarchElectProcessor *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@5
  char v5; // al@6
  int v6; // eax@6
  __int64 v7; // [sp+0h] [bp-78h]@1
  unsigned __int64 dwRef; // [sp+20h] [bp-58h]@6
  _AVATOR_DATA *v9; // [sp+30h] [bp-48h]@4
  unsigned __int64 ui64AddMoney; // [sp+38h] [bp-40h]@5
  _qry_case_request_refund v11; // [sp+48h] [bp-30h]@6
  unsigned int dwS; // [sp+64h] [bp-14h]@6
  ElectProcessor::ProcessorType v13; // [sp+68h] [bp-10h]@8
  PatriarchElectProcessor *v14; // [sp+80h] [bp+8h]@1
  CPlayer *v15; // [sp+88h] [bp+10h]@1

  v15 = pOne;
  v14 = this;
  v2 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = &pOne->m_pUserDB->m_AvatorData;
  if ( v9->dbAvator.m_dwGivebackCount )
  {
    ui64AddMoney = 10000000 * v9->dbAvator.m_dwGivebackCount;
    v4 = CPlayerDB::GetDalant(&pOne->m_Param);
    if ( CanAddMoneyForMaxLimMoney(ui64AddMoney, v4) )
    {
      dwS = CPlayerDB::GetCharSerial(&v15->m_Param);
      v5 = CPlayerDB::GetRaceCode(&v15->m_Param);
      dwRef = ui64AddMoney;
      _qry_case_request_refund::_qry_case_request_refund(&v11, v5, v15->m_id.wIndex, dwS, ui64AddMoney);
      v6 = _qry_case_request_refund::size(&v11);
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 123, &v11.byRace, v6);
    }
    else
    {
      PatriarchElectProcessor::SendMsg_ResultCode(v14, v15->m_id.wIndex, 15);
    }
  }
  v13 = v14->_eProcessType;
  if ( v13 == 2 )
  {
    if ( v14->_kRunningProcessor )
    {
      ((void (__fastcall *)(ElectProcessor *, signed __int64, CPlayer *, _QWORD))v14->_kRunningProcessor->vfptr->Doit)(
        v14->_kRunningProcessor,
        6i64,
        v15,
        0i64);
      ((void (__fastcall *)(ElectProcessor *, signed __int64, CPlayer *, _QWORD))v14->_kRunningProcessor->vfptr->Doit)(
        v14->_kRunningProcessor,
        8i64,
        v15,
        0i64);
    }
  }
  else if ( v13 == 3 )
  {
    if ( v14->_kRunningProcessor )
      ((void (__fastcall *)(ElectProcessor *, signed __int64, CPlayer *, _QWORD))v14->_kRunningProcessor->vfptr->Doit)(
        v14->_kRunningProcessor,
        13i64,
        v15,
        0i64);
  }
}
