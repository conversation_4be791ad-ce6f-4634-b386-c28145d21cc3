/*
 * Function: ?eGetDalant@@YANH@Z
 * Address: 0x1402A4390
 */

long double __fastcall eGetDalant(int nRaceCode)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-18h]@1
  int v5; // [sp+20h] [bp+8h]@1

  v5 = nRaceCode;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return e_EconomySystem.m_dCurTradeDalant[v5] + e_EconomySystem.m_dBufTradeDalant[v5];
}
