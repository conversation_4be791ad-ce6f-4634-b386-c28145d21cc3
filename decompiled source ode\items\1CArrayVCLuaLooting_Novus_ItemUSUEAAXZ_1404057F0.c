/*
 * Function: ??1?$CArray@VCLuaLooting_Novus_Item@@@US@@UEAA@XZ
 * Address: 0x1404057F0
 */

void __fastcall US::CArray<CLuaLooting_Novus_Item>::~CArray<CLuaLooting_Novus_Item>(US::CArray<CLuaLooting_Novus_Item> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  CLuaLooting_Novus_Item *v4; // [sp+20h] [bp-28h]@5
  CLuaLooting_Novus_Item *v5; // [sp+28h] [bp-20h]@5
  US::CArray<CLuaLooting_Novus_Item> *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6->vfptr = (US::CArray<CLuaLooting_Novus_Item>Vtbl *)&US::CArray<CLuaLooting_Novus_Item>::`vftable';
  if ( v6->m_bAlloc )
  {
    v5 = v6->m_pBuffer;
    v4 = v5;
    if ( v5 )
      CLuaLooting_Novus_Item::`vector deleting destructor'(v4, 3u);
  }
}
