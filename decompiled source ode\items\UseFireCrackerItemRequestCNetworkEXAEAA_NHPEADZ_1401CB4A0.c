/*
 * Function: ?UseFireCrackerItemRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CB4A0
 */

char __fastcall CNetworkEX::UseFireCrackerItemRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-D8h]@1
  unsigned __int16 *v7; // [sp+30h] [bp-A8h]@4
  CPlayer *v8; // [sp+38h] [bp-A0h]@4
  int v9; // [sp+40h] [bp-98h]@6
  char szMsg; // [sp+54h] [bp-84h]@9
  unsigned __int16 v11; // [sp+55h] [bp-83h]@9
  char pbyType; // [sp+74h] [bp-64h]@9
  char v13; // [sp+75h] [bp-63h]@9
  char v14[4]; // [sp+94h] [bp-44h]@10
  __int16 v15; // [sp+98h] [bp-40h]@10
  char v16; // [sp+B4h] [bp-24h]@10
  char v17; // [sp+B5h] [bp-23h]@10
  int v18; // [sp+C4h] [bp-14h]@7

  v3 = &v6;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = (unsigned __int16 *)pBuf;
  v8 = &g_Player + n;
  if ( v8->m_bOper )
  {
    v9 = CPlayer::pc_UseFireCracker(v8, *v7);
    if ( v9 < 0 )
      v18 = v9;
    else
      v18 = 0;
    szMsg = v18;
    v11 = *v7;
    pbyType = 7;
    v13 = 38;
    CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, &szMsg, 3u);
    if ( v9 >= 0 )
    {
      *(_DWORD *)v14 = v8->m_dwObjSerial;
      v15 = v9;
      v16 = 7;
      v17 = 39;
      CGameObject::CircleReport((CGameObject *)&v8->vfptr, &v16, v14, 6, 0);
    }
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
