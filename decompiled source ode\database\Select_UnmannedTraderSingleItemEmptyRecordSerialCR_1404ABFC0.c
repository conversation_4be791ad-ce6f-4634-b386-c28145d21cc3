/*
 * Function: ?Select_UnmannedTraderSingleItemEmptyRecordSerial@CRFWorldDatabase@@QEAAHAEAK@Z
 * Address: 0x1404ABFC0
 */

signed __int64 __fastcall CRFWorldDatabase::Select_UnmannedTraderSingleItemEmptyRecordSerial(CRFWorldDatabase *this, unsigned int *dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v5; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@22
  SQLLEN v8; // [sp+38h] [bp-150h]@22
  __int16 v9; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  int v11; // [sp+164h] [bp-24h]@4
  unsigned int v12; // [sp+168h] [bp-20h]@16
  unsigned __int64 v13; // [sp+178h] [bp-10h]@4
  CRFWorldDatabase *v14; // [sp+190h] [bp+8h]@1
  unsigned int *TargetValue; // [sp+198h] [bp+10h]@1

  TargetValue = dwSerial;
  v14 = this;
  v2 = &v5;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  v11 = 0;
  sprintf(&Dest, "select top 1 serial from tbl_utresultinfo where type=0 and state=0 order by serial");
  if ( v14->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v14->vfptr, &Dest);
  if ( v14->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v14->vfptr) )
  {
    v9 = SQLExecDirectA_0(v14->m_hStmtSelect, &Dest, -3);
    if ( v9 && v9 != 1 )
    {
      if ( v9 == 100 )
      {
        result = 4294967294i64;
      }
      else
      {
        SQLStmt = v14->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v9, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v9, v14->m_hStmtSelect);
        result = 0i64;
      }
    }
    else
    {
      v9 = SQLFetch_0(v14->m_hStmtSelect);
      if ( v9 && v9 != 1 )
      {
        v12 = 0;
        if ( v9 == 100 )
        {
          v12 = -3;
        }
        else
        {
          SQLStmt = v14->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v9, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v9, v14->m_hStmtSelect);
          v12 = -4;
        }
        if ( v14->m_hStmtSelect )
          SQLCloseCursor_0(v14->m_hStmtSelect);
        result = v12;
      }
      else
      {
        *TargetValue = 0;
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v14->m_hStmtSelect, 1u, -18, TargetValue, 0i64, &v8);
        if ( v9 == 100 )
        {
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          result = 4294967291i64;
        }
        else
        {
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          if ( v14->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v14->vfptr, "%s Success", &Dest);
          result = 0i64;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v14->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0xFFFFFFFFi64;
  }
  return result;
}
