/*
 * Function: ??0?$PK_MessageAccumulatorImpl@VSHA1@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x140562D60
 */

CryptoPP::PK_MessageAccumulatorBase *__fastcall CryptoPP::PK_MessageAccumulatorImpl<CryptoPP::SHA1>::PK_MessageAccumulatorImpl<CryptoPP::SHA1>(CryptoPP::PK_MessageAccumulatorBase *a1)
{
  CryptoPP::PK_MessageAccumulatorBase *v2; // [sp+40h] [bp+8h]@1

  v2 = a1;
  CryptoPP::PK_MessageAccumulatorBase::PK_MessageAccumulatorBase(a1);
  CryptoPP::ObjectHolder<CryptoPP::SHA1>::ObjectHolder<CryptoPP::SHA1>(&v2[1]);
  v2->vfptr = (CryptoPP::ClonableVtbl *)&CryptoPP::PK_MessageAccumulatorImpl<CryptoPP::SHA1>::`vftable';
  return v2;
}
