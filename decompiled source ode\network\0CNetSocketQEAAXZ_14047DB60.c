/*
 * Function: ??0CNetSocket@@QEAA@XZ
 * Address: 0x14047DB60
 */

void __fastcall CNetSocket::CNetSocket(CNetSocket *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CNetSocket *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  v5->vfptr = (CNetSocketVtbl *)&CNetSocket::`vftable';
  _SOCK_TYPE_PARAM::_SOCK_TYPE_PARAM(&v5->m_SockType);
  _total_count::_total_count(&v5->m_TotalCount);
  CNetIndexList::CNetIndexList(&v5->m_listIPCheck);
  CNetIndexList::CNetIndexList(&v5->m_listIPCheck_Empty);
  CNetTimer::CNetTimer(&v5->m_tmrListCheckerIPCheck);
  v5->m_dwSerialCounter = 0;
  v5->m_bSetSocket = 0;
  v5->m_ndIPCheck = 0i64;
  v5->m_dwIPCheckBufferList = 0i64;
  v5->m_Socket = 0i64;
  v5->m_Event = 0i64;
}
