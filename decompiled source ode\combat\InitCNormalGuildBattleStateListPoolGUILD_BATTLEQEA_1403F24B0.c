/*
 * Function: ?Init@CNormalGuildBattleStateListPool@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403F24B0
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleStateListPool::Init(GUILD_BATTLE::CNormalGuildBattleStateListPool *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleFieldList *v3; // rax@4
  char result; // al@5
  signed __int64 v5; // rax@6
  unsigned __int8 v6; // cf@8
  unsigned __int64 v7; // rax@8
  __int64 v8; // [sp+0h] [bp-78h]@1
  unsigned int v9; // [sp+30h] [bp-48h]@4
  GUILD_BATTLE::CGuildBattleLogger *v10; // [sp+38h] [bp-40h]@6
  int count[2]; // [sp+40h] [bp-38h]@6
  GUILD_BATTLE::CNormalGuildBattleStateList *v12; // [sp+48h] [bp-30h]@13
  void *v13; // [sp+50h] [bp-28h]@10
  __int64 v14; // [sp+58h] [bp-20h]@4
  GUILD_BATTLE::CNormalGuildBattleStateList *v15; // [sp+60h] [bp-18h]@11
  GUILD_BATTLE::CNormalGuildBattleStateListPool *v16; // [sp+80h] [bp+8h]@1

  v16 = this;
  v1 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v14 = -2i64;
  v3 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
  v9 = GUILD_BATTLE::CNormalGuildBattleFieldList::GetMapCnt(v3);
  if ( v9 )
  {
    v16->m_dwMaxCount = 46 * v9;
    v10 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    *(_QWORD *)count = v16->m_dwMaxCount;
    v5 = 304i64 * *(_QWORD *)count;
    if ( !is_mul_ok(0x130ui64, *(unsigned __int64 *)count) )
      v5 = -1i64;
    v6 = __CFADD__(v5, 8i64);
    v7 = v5 + 8;
    if ( v6 )
      v7 = -1i64;
    v13 = operator new[](v7);
    if ( v13 )
    {
      *(_DWORD *)v13 = count[0];
      `eh vector constructor iterator'(
        (char *)v13 + 8,
        0x130ui64,
        count[0],
        (void (__cdecl *)(void *))GUILD_BATTLE::CNormalGuildBattleStateList::CNormalGuildBattleStateList,
        (void (__cdecl *)(void *))GUILD_BATTLE::CNormalGuildBattleStateList::~CNormalGuildBattleStateList);
      v15 = (GUILD_BATTLE::CNormalGuildBattleStateList *)((char *)v13 + 8);
    }
    else
    {
      v15 = 0i64;
    }
    v12 = v15;
    v16->m_pkStateList = v15;
    if ( v16->m_pkStateList )
    {
      result = 1;
    }
    else
    {
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v10,
        "CNormalGuildBattleStateListPool::Init()NULL == new CNormalGuildBattleStateList[%u]",
        v16->m_dwMaxCount);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
