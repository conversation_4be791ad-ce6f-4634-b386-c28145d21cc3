/*
 * Function: ?IsEmpty@_LIST@_TRADE_DB_BASE@@QEAA_NXZ
 * Address: 0x14035FDF0
 */

bool __fastcall _TRADE_DB_BASE::_LIST::IsEmpty(_TRADE_DB_BASE::_LIST *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // [sp+0h] [bp-18h]@1
  _TRADE_DB_BASE::_LIST *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  return v5->dwRegistSerial == 0;
}
