/*
 * Function: ?_buybygold_buy_single_item_calc_price_one_n_one@CashItemRemoteStore@@AEAAKEHE@Z
 * Address: 0x1402FFDB0
 */

__int64 __fastcall CashItemRemoteStore::_buybygold_buy_single_item_calc_price_one_n_one(CashItemRemoteStore *this, char bySet<PERSON><PERSON>, int nCsPrice, char by<PERSON>verlapNum)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  CashItemRemoteStore *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return (unsigned int)(unsigned __int8)byOverlapNum
       * (nCsPrice
        - 10
        * (signed int)floor((double)((unsigned int)(unsigned __int8)CashItemRemoteStore::GetSetDiscout(
                                                                      v8,
                                                                      bySetKind - 1)
                                   * nCsPrice
                                   / 0x64)
                          * 0.1 + 0.5));
}
