/*
 * Function: ?UpdateChaosModeState@CPlayer@@AEAAXK@Z
 * Address: 0x14007BE90
 */

void __fastcall CPlayer::UpdateChaosModeState(CPlayer *this, unsigned int dwCurTime)
{
  if ( this->m_nChaosMode )
  {
    if ( dwCurTime <= this->m_dwChaosModeEndTime )
    {
      if ( this->m_nChaosMode != 1 || this->m_dwChaosModeEndTime - dwCurTime >= this->m_dwChaosModeTime10Per )
      {
        if ( this->m_nChaosMode == 2 && this->m_dwChaosModeEndTime - dwCurTime >= this->m_dwChaosModeTime10Per )
          this->m_nChaosMode = 1;
      }
      else
      {
        this->m_nChaosMode = 2;
      }
    }
    else
    {
      this->m_nChaosMode = 0;
    }
  }
}
