/*
 * Function: ?SF_TransMonsterHP@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 * Address: 0x14009F260
 */

bool __usercall CPlayer::SF_TransMonsterHP@<al>(CPlayer *this@<rcx>, CCharacter *pDstObj@<rdx>, float fEffectValue@<xmm2>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@4
  bool result; // al@5
  int v8; // eax@6
  _sec_info *v9; // rax@11
  __int64 v10; // [sp+0h] [bp-B8h]@1
  int v11; // [sp+20h] [bp-98h]@6
  _pnt_rect pRect; // [sp+38h] [bp-80h]@6
  int j; // [sp+54h] [bp-64h]@6
  int k; // [sp+58h] [bp-60h]@8
  unsigned int dwSecIndex; // [sp+5Ch] [bp-5Ch]@11
  CObjectList *v16; // [sp+60h] [bp-58h]@12
  CPlayer *v17; // [sp+68h] [bp-50h]@14
  CObjectList *v18; // [sp+70h] [bp-48h]@11
  int v19; // [sp+78h] [bp-40h]@18
  int v20; // [sp+7Ch] [bp-3Ch]@19
  int v21; // [sp+80h] [bp-38h]@19
  int v22; // [sp+84h] [bp-34h]@20
  int v23; // [sp+88h] [bp-30h]@21
  int v24; // [sp+8Ch] [bp-2Ch]@21
  int v25; // [sp+90h] [bp-28h]@4
  CGameObjectVtbl *v26; // [sp+98h] [bp-20h]@4
  CGameObjectVtbl *v27; // [sp+A0h] [bp-18h]@23
  CPlayer *v28; // [sp+C0h] [bp+8h]@1

  v28 = this;
  v4 = &v10;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v25 = ((int (__fastcall *)(CPlayer *))v28->vfptr->GetHP)(v28);
  v26 = v28->vfptr;
  v6 = ((int (__fastcall *)(CPlayer *))v26->GetMaxHP)(v28);
  if ( v25 < v6 )
  {
    v11 = 0;
    v8 = CGameObject::GetCurSecNum((CGameObject *)&v28->vfptr);
    CMapData::GetRectInRadius(v28->m_pCurMap, &pRect, 1, v8);
    for ( j = pRect.nStarty; j < pRect.nEndy; ++j )
    {
      for ( k = pRect.nStartx; k < pRect.nEndx; ++k )
      {
        v9 = CMapData::GetSecInfo(v28->m_pCurMap);
        dwSecIndex = v9->m_nSecNumW * j + k;
        v18 = CMapData::GetSectorListObj(v28->m_pCurMap, v28->m_wMapLayerIndex, dwSecIndex);
        if ( v18 )
        {
          v16 = (CObjectList *)v18->m_Head.m_pNext;
          while ( (_object_list_point *)v16 != &v18->m_Tail )
          {
            v17 = (CPlayer *)v16->vfptr;
            v16 = (CObjectList *)v16->m_Head.m_pItem;
            if ( v17 != v28
              && !v17->m_ObjID.m_byKind
              && v17->m_ObjID.m_byID == 1
              && *(unsigned int *)((char *)&v17->m_pSoccerItem[5].m_dwETSerialNumber + 2) != 1 )
            {
              GetSqrt(v28->m_fCurPos, v17->m_fCurPos);
              v19 = (signed int)ffloor(a4);
              if ( v19 <= 200 )
              {
                v20 = ((int (__fastcall *)(__int64))v17->vfptr->GetHP)((__int64)v17);
                v21 = ((int (__fastcall *)(__int64))v17->vfptr->GetMaxHP)((__int64)v17);
                a4 = (float)v20 / (float)v21;
                if ( a4 > 0.30000001 )
                {
                  a4 = (float)v20 * fEffectValue;
                  v22 = (signed int)ffloor(a4);
                  if ( v22 > 0 )
                  {
                    v23 = ((int (__fastcall *)(CPlayer *))v28->vfptr->GetHP)(v28);
                    v24 = ((int (__fastcall *)(CPlayer *))v28->vfptr->GetMaxHP)(v28);
                    if ( v23 >= v24 )
                      return v11 > 0;
                    v27 = v28->vfptr;
                    ((void (__fastcall *)(CPlayer *, _QWORD, _QWORD))v27->SetHP)(v28, (unsigned int)(v22 + v23), 0i64);
                    (*(void (__fastcall **)(CPlayer *))&v28->vfptr->gap8[72])(v28);
                    ((void (__fastcall *)(__int64, CPlayer *, _QWORD))v17->vfptr->RobbedHP)(
                      (__int64)v17,
                      v28,
                      (unsigned int)v22);
                    ++v11;
                  }
                }
              }
            }
          }
        }
      }
    }
    result = v11 > 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
