/*
 * Function: ?SendMsg_AwayPartyInvitationQuestion@CPlayer@@QEAAXG@Z
 * Address: 0x1400E7160
 */

void __fastcall CPlayer::SendMsg_AwayPartyInvitationQuestion(CPlayer *this, unsigned __int16 wJoinerIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-98h]@1
  char szMsg[2]; // [sp+38h] [bp-60h]@4
  unsigned int v7; // [sp+3Ah] [bp-5Eh]@4
  char Dest; // [sp+3Eh] [bp-5Ah]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v10; // [sp+65h] [bp-33h]@4
  unsigned __int64 v11; // [sp+80h] [bp-18h]@4
  CPlayer *v12; // [sp+A0h] [bp+8h]@1
  unsigned __int16 v13; // [sp+A8h] [bp+10h]@1

  v13 = wJoinerIndex;
  v12 = this;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = (unsigned __int64)&v5 ^ _security_cookie;
  v7 = v12->m_dwObjSerial;
  *(_WORD *)szMsg = v12->m_ObjID.m_wIndex;
  v4 = CPlayerDB::GetCharNameA(&v12->m_Param);
  strcpy_0(&Dest, v4);
  pbyType = 16;
  v10 = 33;
  CNetProcess::LoadSendMsg(unk_1414F2088, v13, &pbyType, szMsg, 0x17u);
}
