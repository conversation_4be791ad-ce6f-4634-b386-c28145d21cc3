/*
 * Function: ?Load@CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAA_N_N@Z
 * Address: 0x1403DBDC0
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Load(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this, bool bToday)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  GUILD_BATTLE::CGuildBattleLogger *v5; // rax@9
  unsigned int v6; // eax@13
  GUILD_BATTLE::CGuildBattleLogger *v7; // rax@14
  GUILD_BATTLE::CGuildBattleLogger *v8; // rax@18
  __int64 v9; // [sp+0h] [bp-68h]@1
  unsigned int j; // [sp+20h] [bp-48h]@10
  char v11; // [sp+24h] [bp-44h]@13
  void *v12; // [sp+28h] [bp-40h]@8
  unsigned __int64 v13; // [sp+30h] [bp-38h]@8
  _worlddb_guild_battle_schedule_list *pkInfo; // [sp+38h] [bp-30h]@13
  unsigned int v15; // [sp+40h] [bp-28h]@14
  GUILD_BATTLE::CGuildBattleReservedSchedule **v16; // [sp+48h] [bp-20h]@17
  unsigned int v17; // [sp+50h] [bp-18h]@18
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *v18; // [sp+70h] [bp+8h]@1
  bool v19; // [sp+78h] [bp+10h]@1

  v19 = bToday;
  v18 = this;
  v2 = &v9;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v18->m_uiMapCnt && v18->m_ppkReservedSchedule )
  {
    if ( GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::ms_pkDBScheduleInfo
      || (v13 = v18->m_uiMapCnt,
          v12 = operator new[](saturated_mul(0x2E8ui64, v13)),
          (GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::ms_pkDBScheduleInfo = v12) != 0i64) )
    {
      memset_0(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::ms_pkDBScheduleInfo, 0, 744i64 * v18->m_uiMapCnt);
      for ( j = 0; j < v18->m_uiMapCnt; ++j )
      {
        pkInfo = (_worlddb_guild_battle_schedule_list *)((char *)GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::ms_pkDBScheduleInfo
                                                       + 744 * j);
        v6 = GUILD_BATTLE::CGuildBattleReservedSchedule::GetID(v18->m_ppkReservedSchedule[j]);
        v11 = CRFWorldDatabase::LoadGuildBattleScheduleInfo(pkDB, v6, 0x17u, pkInfo);
        if ( v11 == 1 )
        {
          v15 = GUILD_BATTLE::CGuildBattleReservedSchedule::GetID(v18->m_ppkReservedSchedule[j]);
          v7 = GUILD_BATTLE::CGuildBattleLogger::Instance();
          GUILD_BATTLE::CGuildBattleLogger::Log(
            v7,
            "CGuildBattleReservedScheduleMapGroup::Load() g_Main.m_pWorldDB->LoadGuildBattleScheduleInfo( %u, %u, ms_pkDBInfo )",
            v15,
            23i64);
          return 0;
        }
        if ( v11 != 2 )
        {
          if ( *((_WORD *)GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::ms_pkDBScheduleInfo + 372 * j) )
          {
            v16 = v18->m_ppkReservedSchedule;
            if ( !GUILD_BATTLE::CGuildBattleReservedSchedule::Load(
                    v16[j],
                    v19,
                    (_worlddb_guild_battle_schedule_list *)GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::ms_pkDBScheduleInfo
                  + j) )
            {
              v17 = v19;
              v8 = GUILD_BATTLE::CGuildBattleLogger::Instance();
              GUILD_BATTLE::CGuildBattleLogger::Log(
                v8,
                "CGuildBattleReservedScheduleMapGroup::Load( %d ) m_ppkReservedSchedule[%u]->Load( ms_pkDBScheduleInfo )",
                v17,
                j);
              return 0;
            }
          }
        }
      }
      result = 1;
    }
    else
    {
      v5 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v5,
        "CGuildBattleReservedScheduleMapGroup::Load() new _worlddb_guild_battle_schedule_list[%u] Fail!",
        v18->m_uiMapCnt);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
