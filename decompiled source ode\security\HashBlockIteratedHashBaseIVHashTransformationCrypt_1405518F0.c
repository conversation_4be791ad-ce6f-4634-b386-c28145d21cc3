/*
 * Function: ?HashBlock@?$IteratedHashBase@IVHashTransformation@CryptoPP@@@CryptoPP@@IEAAXPEBI@Z
 * Address: 0x1405518F0
 */

int __fastcall CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation>::HashBlock(__int64 a1, __int64 a2)
{
  int v2; // eax@1
  __int64 v4; // [sp+40h] [bp+8h]@1
  __int64 v5; // [sp+48h] [bp+10h]@1

  v5 = a2;
  v4 = a1;
  v2 = (*(int (**)(void))(*(_QWORD *)a1 + 64i64))();
  return (*(int (__fastcall **)(__int64, __int64, _QWORD))(*(_QWORD *)v4 + 168i64))(v4, v5, (unsigned int)v2);
}
