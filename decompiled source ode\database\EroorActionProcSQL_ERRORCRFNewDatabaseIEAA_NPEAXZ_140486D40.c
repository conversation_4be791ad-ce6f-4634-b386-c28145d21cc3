/*
 * Function: ?EroorActionProcSQL_ERROR@CRFNewDatabase@@IEAA_NPEAX@Z
 * Address: 0x140486D40
 */

char __fastcall CRFNewDatabase::EroorActionProcSQL_ERROR(CRFNewDatabase *this, void *SQLStmt)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-2D8h]@1
  SQLCHAR szSqlState; // [sp+44h] [bp-294h]@6
  SQLCHAR v7; // [sp+70h] [bp-268h]@6
  SQLINTEGER v8; // [sp+284h] [bp-54h]@6
  SQLSMALLINT v9; // [sp+2A4h] [bp-34h]@4
  int j; // [sp+2B4h] [bp-24h]@4
  unsigned __int64 v11; // [sp+2C0h] [bp-18h]@4
  CRFNewDatabase *v12; // [sp+2E0h] [bp+8h]@1
  SQLHANDLE handle; // [sp+2E8h] [bp+10h]@1

  handle = SQLStmt;
  v12 = this;
  v2 = &v5;
  for ( i = 180i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = (unsigned __int64)&v5 ^ _security_cookie;
  v9 = 0;
  for ( j = 1; j < 11 && !SQLGetDiagRecA_0(3, handle, j, &szSqlState, &v8, &v7, 512, &v9); ++j )
  {
    if ( !strncmp("08S01", &szSqlState, 5ui64) || !strncmp("24000", &szSqlState, 5ui64) || v8 == 10054 )
    {
      CRFNewDatabase::ErrFmtLog(v12, "EroorActionProcSQL_ERROR : DB Error!(%s) -> ReConnectDataBase()", &szSqlState);
      if ( !CRFNewDatabase::ReConnectDataBase(v12) )
        return 0;
    }
  }
  return 1;
}
