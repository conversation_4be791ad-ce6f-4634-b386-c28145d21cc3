/*
 * Function: j_?begin@?$vector@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@QEAA?AV?$_Vector_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@2@XZ
 * Address: 0x140010203
 */

std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *__fastcall std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::begin(std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *this, std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *result)
{
  return std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::begin(this, result);
}
