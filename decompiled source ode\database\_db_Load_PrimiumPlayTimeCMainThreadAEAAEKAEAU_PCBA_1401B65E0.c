/*
 * Function: ?_db_Load_PrimiumPlayTime@CMainThread@@AEAAEKAEAU_PCBANG_PLAY_TIME@@@Z
 * Address: 0x1401B65E0
 */

char __fastcall CMainThread::_db_Load_PrimiumPlayTime(CMainThread *this, unsigned int dwAccSerial, _PCBANG_PLAY_TIME *kData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  char v7; // [sp+20h] [bp-18h]@4
  CMainThread *v8; // [sp+40h] [bp+8h]@1
  unsigned int dwAccSeriala; // [sp+48h] [bp+10h]@1
  _PCBANG_PLAY_TIME *kInfo; // [sp+50h] [bp+18h]@1

  kInfo = kData;
  dwAccSeriala = dwAccSerial;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = CRFWorldDatabase::Select_PrimiumPlayTime(v8->m_pWorldDB, dwAccSerial, kData);
  if ( v7 == 1 )
    return 24;
  if ( v7 == 2 )
  {
    if ( !CRFWorldDatabase::Insert_PrimiumPlayTime(v8->m_pWorldDB, dwAccSeriala) )
      return 24;
    kInfo->bForcedClose = 1;
    kInfo->dwContPlayTime = 0;
    kInfo->dwLastConnTime = 0;
  }
  kInfo->dwAccSerial = dwAccSeriala;
  return 0;
}
