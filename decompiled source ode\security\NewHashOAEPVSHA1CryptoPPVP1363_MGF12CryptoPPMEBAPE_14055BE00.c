/*
 * Function: ?NewHash@?$O<PERSON>EP@VSHA1@CryptoPP@@VP1363_MGF1@2@@CryptoPP@@MEBAPEAVHashTransformation@2@XZ
 * Address: 0x14055BE00
 */

__int64 CryptoPP::OAEP<CryptoPP::SHA1,CryptoPP::P1363_MGF1>::NewHash()
{
  __int64 v0; // rax@2
  CryptoPP::SHA1 *v2; // [sp+28h] [bp-20h]@1
  __int64 v3; // [sp+38h] [bp-10h]@2

  v2 = (CryptoPP::SHA1 *)operator new(0xC0ui64);
  if ( v2 )
  {
    CryptoPP::SHA1::SHA1(v2);
    v3 = v0;
  }
  else
  {
    v3 = 0i64;
  }
  return v3;
}
