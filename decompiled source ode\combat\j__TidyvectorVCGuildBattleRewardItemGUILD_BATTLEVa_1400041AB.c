/*
 * Function: j_?_Tidy@?$vector@VCGuildBattleRewardItem@GUILD_BATTLE@@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@std@@IEAAXXZ
 * Address: 0x1400041AB
 */

void __fastcall std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Tidy(std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *this)
{
  std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Tidy(this);
}
