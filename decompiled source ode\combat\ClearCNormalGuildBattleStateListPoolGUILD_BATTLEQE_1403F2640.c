/*
 * Function: ?Clear@CNormalGuildBattleStateListPool@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403F2640
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleStateListPool::Clear(GUILD_BATTLE::CNormalGuildBattleStateListPool *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@7
  GUILD_BATTLE::CNormalGuildBattleStateListPool *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5->m_pkStateList && v5->m_dwMaxCount )
  {
    for ( j = 0; j < v5->m_dwMaxCount; ++j )
      GUILD_BATTLE::CGuildBattleStateList::Clear((GUILD_BATTLE::CGuildBattleStateList *)&v5->m_pkStateList[j].vfptr);
  }
}
