/*
 * Function: j_??$_Uninit_copy@PEAVCGuildRoomInfo@@PEAV1@V?$allocator@VCGuildRoomInfo@@@std@@@std@@YAPEAVCGuildRoomInfo@@PEAV1@00AEAV?$allocator@VCGuildRoomInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000E3C2
 */

CGuildRoomInfo *__fastcall std::_Uninit_copy<CGuildRoomInfo *,CGuildRoomInfo *,std::allocator<CGuildRoomInfo>>(CGuildRoomInfo *_First, CGuildRoomInfo *_Last, CGuildRoomInfo *_Dest, std::allocator<CGuildRoomInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CGuildRoomInfo *,CGuildRoomInfo *,std::allocator<CGuildRoomInfo>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
