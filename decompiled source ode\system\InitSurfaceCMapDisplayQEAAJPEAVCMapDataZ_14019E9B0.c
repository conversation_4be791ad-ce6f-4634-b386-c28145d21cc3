/*
 * Function: ?InitSurface@CMapDisplay@@QEAAJPEAVCMapData@@@Z
 * Address: 0x14019E9B0
 */

__int64 __fastcall CMapDisplay::InitSurface(CMapDisplay *this, CMapData *pMap)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@6
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@5
  CDisplayView *v7; // [sp+28h] [bp-10h]@7
  CMapDisplay *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v8->m_bDisplayMode && (v6 = CMapDisplay::ReleaseDisplay(v8)) != 0 )
  {
    MyMessageBox("CMapDisplay Error", "Release Failure");
    result = (unsigned int)v6;
  }
  else
  {
    v7 = &g_pDoc->m_DisplayView;
    CDisplayView::GetDrawableRect(&g_pDoc->m_DisplayView, &v8->m_rcWnd);
    v6 = CDisplay::CreateWindowedDisplay((CDisplay *)&v8->vfptr, v7->m_hWnd, v8->m_rcWnd.right, v8->m_rcWnd.bottom);
    if ( v6 >= 0 )
    {
      v6 = CDisplay::CreateSurface((CDisplay *)&v8->vfptr, &v8->m_pSFMap, v8->m_rcWnd.right, v8->m_rcWnd.bottom);
      if ( v6 >= 0 )
      {
        v6 = CDisplay::CreateSurface((CDisplay *)&v8->vfptr, &v8->m_pSFBuf, v8->m_rcWnd.right, v8->m_rcWnd.bottom);
        if ( v6 >= 0 )
        {
          v6 = CSurface::FillColor(v8->m_pSFBuf, 0x9BBD52u);
          if ( v6 >= 0 )
          {
            v6 = CMapDisplay::CreateObjSurface(v8);
            if ( v6 >= 0 )
            {
              result = (unsigned int)v6;
            }
            else
            {
              MyMessageBox("CMapDisplay Error", "CreateObjSurface Failure");
              result = (unsigned int)v6;
            }
          }
          else
          {
            MyMessageBox("CMapDisplay Error", "FillColor Failure");
            result = (unsigned int)v6;
          }
        }
        else
        {
          MyMessageBox("CMapDisplay Error", "CreateSurface Failure");
          result = (unsigned int)v6;
        }
      }
      else
      {
        MyMessageBox("CMapDisplay Error", "CreateSurface Failure");
        result = (unsigned int)v6;
      }
    }
    else
    {
      MyMessageBox("CMapDisplay Error", "CreateWindowedDisplay Failure");
      result = (unsigned int)v6;
    }
  }
  return result;
}
