/*
 * Function: ?_complete_tsk_cash_select@CashDbWorker@@IEAAXPEAVTask@@@Z
 * Address: 0x1402EF070
 */

void __fastcall CashDbWorker::_complete_tsk_cash_select(CashDbWorker *this, Task *pkTsk)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@7
  __int64 v5; // [sp+0h] [bp-58h]@1
  char *pFileName; // [sp+20h] [bp-38h]@5
  _param_cash_select *psheet; // [sp+30h] [bp-28h]@4
  CPlayer *v8; // [sp+38h] [bp-20h]@6
  char *v9; // [sp+40h] [bp-18h]@7
  CashDbWorker *v10; // [sp+60h] [bp+8h]@1
  Task *v11; // [sp+68h] [bp+10h]@1

  v11 = pkTsk;
  v10 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  psheet = (_param_cash_select *)Task::GetTaskBuf(pkTsk);
  if ( Task::GetRetCode(v11) == 1 )
  {
    LODWORD(pFileName) = psheet->in_dwAvatorSerial;
    CLogFile::Write(
      v10->_kLogger,
      "[ERR_QRY] cash select >> AC:%u(%s) AV:%u",
      psheet->in_dwAccountSerial,
      psheet->in_szAcc);
  }
  else
  {
    v8 = CashDbWorker::_get_player(v10, psheet->in_wSockIndex, psheet->in_dwAvatorSerial);
    if ( v8 )
    {
      ICsSendInterface::SendMsg_GoodsList(psheet->in_wSockIndex, psheet);
      CPlayer::SetCashAmount(v8, psheet->out_dwCashAmount);
      v9 = v8->m_szItemHistoryFileName;
      v4 = CPlayer::GetCashAmount(v8);
      pFileName = v9;
      CMgrAvatorItemHistory::read_cashamount(
        &CPlayer::s_MgrItemHistory,
        psheet->in_dwAccountSerial,
        psheet->in_dwAvatorSerial,
        v4,
        v9);
    }
  }
}
