/*
 * Function: ?Set@CUnmannedTraderRegistItemInfo@@QEAA_NGEIAEAU_TRADE_DB_BASE@@PEAVCLogFile@@@Z
 * Address: 0x140351EB0
 */

char __fastcall CUnmannedTraderRegistItemInfo::Set(CUnmannedTraderRegistItemInfo *this, unsigned __int16 wInx, char byInvenIndex, unsigned int uiInx, _TRADE_DB_BASE *kInfo, CLogFile *pkLogger)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char v8; // al@7
  char result; // al@8
  __int64 v10; // [sp+0h] [bp-58h]@1
  int v11; // [sp+20h] [bp-38h]@7
  unsigned int v12; // [sp+28h] [bp-30h]@7
  _STORAGE_LIST::_storage_con *v13; // [sp+30h] [bp-28h]@7
  _STORAGE_LIST::_storage_con *v14; // [sp+40h] [bp-18h]@4
  int v15; // [sp+48h] [bp-10h]@7
  CUnmannedTraderRegistItemInfo *v16; // [sp+60h] [bp+8h]@1
  unsigned __int16 v17; // [sp+68h] [bp+10h]@1
  char v18; // [sp+70h] [bp+18h]@1
  unsigned int v19; // [sp+78h] [bp+20h]@1

  v19 = uiInx;
  v18 = byInvenIndex;
  v17 = wInx;
  v16 = this;
  v6 = &v10;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v14 = (_STORAGE_LIST::_storage_con *)CPlayerDB::GetItem(
                                         (CPlayerDB *)((char *)&g_Player.m_Param + 50856 * wInx),
                                         byInvenIndex);
  if ( v19 < 0xA && v14 )
  {
    if ( CUnmannedTraderItemState::Set(&v16->m_kState, kInfo->m_List[v19].byState) )
    {
      v16->m_dwRegistSerial = kInfo->m_List[v19].dwRegistSerial;
      v16->m_wItemSerial = v14->m_wSerial;
      v16->m_dwETSerialNumber = v14->m_dwETSerialNumber;
      v16->m_dwPrice = kInfo->m_List[v19].dwPrice;
      v16->m_tStartTime = kInfo->m_List[v19].tStartTime;
      v16->m_bySellTurm = kInfo->m_List[v19].bySellTurm;
      v16->m_dwTax = kInfo->m_List[v19].dwTax;
      v16->m_tResultTime = kInfo->m_List[v19].tResultTime;
      if ( CUnmannedTraderRegistItemInfo::IsSellWait(v16) )
      {
        v16->m_dwBuyerSerial = kInfo->m_List[v19].dwBuyerSerial;
        strcpy_s(v16->m_wszBuyerName, 0x11ui64, kInfo->m_List[v19].wszBuyerName);
        strcpy_s(v16->m_szBuyerAccount, 0xDui64, kInfo->m_List[v19].szBuyerAccount);
      }
      v16->m_byTableCode = v14->m_byTableCode;
      v16->m_wItemIndex = v14->m_wItemIndex;
      v16->m_byStorageIndex = BYTE3(v14[1].m_dwDur);
      v16->m_dwD = v14->m_dwDur;
      v16->m_dwU = v14->m_dwLv;
      if ( CUnmannedTraderRegistItemInfo::IsRegist(v16) )
        _STORAGE_LIST::_storage_con::lock(v14, 1);
      result = 1;
    }
    else
    {
      if ( pkLogger )
      {
        v11 = kInfo->m_List[v19].byState;
        CLogFile::Write(
          pkLogger,
          "CUnmannedTraderRegistItemInfo::Set(...)\r\n"
          "\t\tdwRegistSerial(%u)\r\n"
          "\t\tm_kState.Set( kInfo.m_List[uiInx(%u)].byState(%u) ) Faili!\r\n",
          kInfo->m_List[v19].dwRegistSerial,
          v19);
      }
      result = 0;
    }
  }
  else
  {
    if ( pkLogger )
    {
      v15 = (unsigned __int8)v18;
      v8 = CPlayerDB::GetUseSlot((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * v17));
      v13 = v14;
      v12 = v19;
      v11 = v15;
      CLogFile::Write(
        pkLogger,
        "CUnmannedTraderRegistItemInfo::Set(...)\r\n"
        "\t\tdwRegistSerial(%u) MaxSlot(%u) byInvenIndex(%u)\r\n"
        "\t\t( max_used_trade_num <= uiInx(%u) || 0 == pkItem(%p) ) == false!\r\n",
        kInfo->m_List[v19].dwRegistSerial,
        (unsigned __int8)v8);
    }
    result = 0;
  }
  return result;
}
