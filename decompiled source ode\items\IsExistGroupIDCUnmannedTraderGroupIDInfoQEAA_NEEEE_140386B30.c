/*
 * Function: ?IsExistGroupID@CUnmannedTraderGroupIDInfo@@QEAA_NEEEEAEAK@Z
 * Address: 0x140386B30
 */

char __fastcall CUnmannedTraderGroupIDInfo::IsExistGroupID(CUnmannedTraderGroupIDInfo *this, char byDivision, char byClass, char bySubClass, char bySortType, unsigned int *dwListIndex)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char v8; // al@5
  CUnmannedTraderDivisionInfo **v9; // rax@8
  CUnmannedTraderDivisionInfo **v10; // rax@10
  unsigned int v11; // eax@10
  __int64 v12; // [sp+0h] [bp-E8h]@1
  char v13; // [sp+20h] [bp-C8h]@8
  unsigned int *v14; // [sp+28h] [bp-C0h]@8
  int v15; // [sp+30h] [bp-B8h]@6
  unsigned int v16; // [sp+44h] [bp-A4h]@6
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > result; // [sp+68h] [bp-80h]@6
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > v18; // [sp+88h] [bp-60h]@10
  bool v19; // [sp+A0h] [bp-48h]@7
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > v20; // [sp+A8h] [bp-40h]@7
  char v21; // [sp+C0h] [bp-28h]@9
  bool v22; // [sp+C1h] [bp-27h]@11
  __int64 v23; // [sp+C8h] [bp-20h]@4
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *v24; // [sp+D0h] [bp-18h]@7
  std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *_Right; // [sp+D8h] [bp-10h]@7
  CUnmannedTraderGroupIDInfo *v26; // [sp+F0h] [bp+8h]@1
  char v27; // [sp+F8h] [bp+10h]@1
  char v28; // [sp+100h] [bp+18h]@1
  char v29; // [sp+108h] [bp+20h]@1

  v29 = bySubClass;
  v28 = byClass;
  v27 = byDivision;
  v26 = this;
  v6 = &v12;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v23 = -2i64;
  if ( (unsigned __int8)byDivision == 255 )
  {
    v8 = 0;
  }
  else
  {
    v15 = 0;
    v16 = 0;
    std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::begin(
      &v26->m_vecDivisionInfo,
      &result);
    while ( 1 )
    {
      v24 = std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::end(
              &v26->m_vecDivisionInfo,
              &v20);
      _Right = (std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)v24;
      v19 = std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator!=(
              (std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)&result._Mycont,
              (std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)&v24->_Mycont);
      std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&v20);
      if ( !v19 )
        break;
      v9 = std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator*(&result);
      v14 = &v16;
      v13 = bySortType;
      if ( CUnmannedTraderDivisionInfo::IsExistGroupID(*v9, v27, v28, v29, bySortType, &v16) )
      {
        *dwListIndex = v16 + v15;
        v21 = 1;
        std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&result);
        return v21;
      }
      v10 = std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator*(&result);
      v11 = CUnmannedTraderDivisionInfo::GetMaxClassCnt(*v10);
      v15 += v11;
      std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator++(
        &result,
        &v18,
        0);
      std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&v18);
    }
    v22 = 0;
    std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&result);
    v8 = v22;
  }
  return v8;
}
