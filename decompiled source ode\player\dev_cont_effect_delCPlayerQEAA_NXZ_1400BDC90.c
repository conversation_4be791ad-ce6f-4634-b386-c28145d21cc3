/*
 * Function: ?dev_cont_effect_del@CPlayer@@QEAA_NXZ
 * Address: 0x1400BDC90
 */

char __fastcall CPlayer::dev_cont_effect_del(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-58h]@1
  int j; // [sp+30h] [bp-28h]@4
  int k; // [sp+34h] [bp-24h]@6
  bool *v7; // [sp+38h] [bp-20h]@9
  bool *v8; // [sp+40h] [bp-18h]@11
  CPlayer *v9; // [sp+60h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 2; ++j )
  {
    for ( k = 0; k < 8; ++k )
    {
      v7 = &v9->m_SFContAura[j][k].m_bExist;
      if ( *v7 )
        CCharacter::RemoveSFContEffect((CCharacter *)&v9->vfptr, j, k, 0, 1);
      v8 = &v9->m_SFCont[j][k].m_bExist;
      if ( *v8 )
        CCharacter::RemoveSFContEffect((CCharacter *)&v9->vfptr, j, k, 0, 0);
    }
  }
  return 1;
}
