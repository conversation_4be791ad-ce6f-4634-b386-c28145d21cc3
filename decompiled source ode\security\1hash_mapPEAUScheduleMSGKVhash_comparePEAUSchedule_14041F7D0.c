/*
 * Function: ??1?$hash_map@PEAUScheduleMSG@@KV?$hash_compare@PEAUScheduleMSG@@U?$less@PEAUScheduleMSG@@@std@@@stdext@@V?$allocator@U?$pair@QEAUScheduleMSG@@K@std@@@std@@@stdext@@QEAA@XZ
 * Address: 0x14041F7D0
 */

void __fastcall stdext::hash_map<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::~hash_map<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>(stdext::hash_map<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  stdext::hash_map<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > > *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>::~_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>((stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> > *)&v4->_Myfirstiter);
}
