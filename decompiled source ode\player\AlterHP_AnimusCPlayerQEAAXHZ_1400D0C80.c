/*
 * Function: ?AlterHP_Animus@CPlayer@@QEAAXH@Z
 * Address: 0x1400D0C80
 */

void __fastcall CPlayer::AlterHP_Animus(CPlayer *this, int nNewHP)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _STORAGE_LIST::_db_con *v4; // rax@6
  _STORAGE_LIST::_db_con *v5; // rcx@6
  __int64 v6; // [sp+0h] [bp-48h]@1
  bool bUpdate; // [sp+20h] [bp-28h]@6
  unsigned int *v8; // [sp+30h] [bp-18h]@5
  CPlayer *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v9->m_pRecalledAnimusItem )
  {
    v8 = &v9->m_pRecalledAnimusItem->m_dwLv;
    *(_WORD *)v8 = nNewHP;
    CPlayer::SendMsg_AnimusHPInform(v9);
    if ( v9->m_pUserDB )
    {
      v4 = v9->m_pRecalledAnimusItem;
      v5 = v9->m_pRecalledAnimusItem;
      bUpdate = 0;
      CUserDB::Update_ItemUpgrade(v9->m_pUserDB, 4, v5->m_byStorageIndex, v4->m_dwLv, 0);
    }
  }
}
