/*
 * Function: _CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_0_CryptoPP::Rijndael::Enc__CryptoPP::CBC_Encryption_::StaticAlgorithmName_::_1_::dtor$1
 * Address: 0x14045A590
 */

int __fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_0_CryptoPP::Rijndael::Enc__CryptoPP::CBC_Encryption_::StaticAlgorithmName_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  return std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(a2 + 80);
}
