/*
 * Function: ?QueryIntAttribute@TiXmlElement@@QEBAHPEBDPEAH@Z
 * Address: 0x14052FB40
 */

signed __int64 __fastcall TiXmlElement::QueryIntAttribute(TiXmlElement *this, const char *a2, int *a3)
{
  TiXmlElement *v3; // r12@1
  int *v4; // rbp@1
  const char *v5; // rsi@1
  unsigned __int64 v6; // kr08_8@1
  signed __int64 v7; // rbx@1
  TiXmlString::Rep *v8; // rdi@2
  TiXmlAttribute *v9; // rax@4
  signed __int64 result; // rax@7
  unsigned int v11; // ebx@8
  struct TiXmlString v12; // [sp+40h] [bp+8h]@2

  v3 = this;
  v4 = a3;
  v5 = a2;
  v6 = strlen(a2) + 1;
  v7 = v6 - 1;
  if ( v6 == 1 )
  {
    v8 = (TiXmlString::Rep *)&TiXmlString::nullrep_;
    v12.rep_ = (TiXmlString::Rep *)&TiXmlString::nullrep_;
  }
  else
  {
    v8 = (TiXmlString::Rep *)operator new(saturated_mul(4ui64, (v6 - 1 + 27) >> 2));
    v12.rep_ = v8;
    v8->size = v7;
    v8->str[v7] = 0;
    v8->capacity = v7;
  }
  memcpy_0(v8->str, v5, v8->size);
  v9 = TiXmlAttributeSet::Find(&v3->attributeSet, &v12);
  if ( v9 )
  {
    v11 = TiXmlAttribute::QueryIntValue(v9, v4);
    if ( (_UNKNOWN *)v8 != &TiXmlString::nullrep_ )
      operator delete(v8);
    result = v11;
  }
  else
  {
    if ( (_UNKNOWN *)v8 != &TiXmlString::nullrep_ )
      operator delete(v8);
    result = 1i64;
  }
  return result;
}
