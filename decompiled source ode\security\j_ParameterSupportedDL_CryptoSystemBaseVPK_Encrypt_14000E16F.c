/*
 * Function: j_?ParameterSupported@?$DL_CryptoSystemBase@VPK_Encryptor@CryptoPP@@V?$DL_PublicKey@UECPPoint@CryptoPP@@@2@@CryptoPP@@UEBA_NPEBD@Z
 * Address: 0x14000E16F
 */

bool __fastcall CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>>::ParameterSupported(CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> > *this, const char *name)
{
  return CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>>::ParameterSupported(
           this,
           name);
}
