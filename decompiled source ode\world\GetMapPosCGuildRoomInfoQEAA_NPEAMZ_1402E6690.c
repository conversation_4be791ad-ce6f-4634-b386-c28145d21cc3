/*
 * Function: ?GetMapPos@CGuildRoomInfo@@QEAA_NPEAM@Z
 * Address: 0x1402E6690
 */

bool __fastcall CGuildRoomInfo::GetMapPos(CGuildRoomInfo *this, float *pPos)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CGuildRoomInfo *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return CMapData::GetRandPosInDummy(v6->m_pRoomMap, v6->m_pRoomStartDummy, pPos, 1) != 0;
}
