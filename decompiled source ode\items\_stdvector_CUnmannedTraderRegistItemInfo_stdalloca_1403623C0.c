/*
 * Function: _std::vector_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo___::_Insert_n_::_1_::dtor$1
 * Address: 0x1403623C0
 */

void __fastcall std::vector_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo___::_Insert_n_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  CUnmannedTraderRegistItemInfo::~CUnmannedTraderRegistItemInfo((CUnmannedTraderRegistItemInfo *)(a2 + 48));
}
