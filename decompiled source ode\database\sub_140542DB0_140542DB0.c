/*
 * Function: sub_140542DB0
 * Address: 0x140542DB0
 */

__int64 __fastcall sub_140542DB0(__int64 a1, double *a2, __int64 a3, __int64 a4)
{
  double *v4; // rdi@1
  __int64 v5; // rbx@1
  _QWORD *v6; // r11@2
  bool v7; // zf@2
  __int64 v8; // rcx@2
  int v9; // edx@3
  __int64 v10; // r8@9
  __int64 v11; // r9@9
  _QWORD *v12; // r11@11
  __int64 v13; // rcx@11
  int v14; // edx@12
  __int64 result; // rax@14

  v4 = a2;
  v5 = a1;
  do
  {
    sub_140542840(v5, *(_DWORD *)v5, a3, a4);
    v6 = *(_QWORD **)(v5 + 64);
    v7 = (*v6)-- == 0i64;
    v8 = *(_QWORD *)(v5 + 64);
    if ( v7 )
      v9 = luaZ_fill(v8);
    else
      v9 = *(_BYTE *)(*(_QWORD *)(v8 + 8))++;
    *(_DWORD *)v5 = v9;
  }
  while ( isdigit(v9) || *(_DWORD *)v5 == 46 );
  if ( (unsigned int)sub_140542C80((int *)v5, "Ee") )
    sub_140542C80((int *)v5, "+-");
  while ( isalnum(*(_DWORD *)v5) || *(_DWORD *)v5 == 95 )
  {
    sub_140542840(v5, *(_DWORD *)v5, v10, v11);
    v12 = *(_QWORD **)(v5 + 64);
    v7 = (*v12)-- == 0i64;
    v13 = *(_QWORD *)(v5 + 64);
    if ( v7 )
    {
      *(_DWORD *)v5 = luaZ_fill(v13);
    }
    else
    {
      v14 = *(_BYTE *)(*(_QWORD *)(v13 + 8))++;
      *(_DWORD *)v5 = v14;
    }
  }
  sub_140542840(v5, 0i64, v10, v11);
  sub_140542D00(v5, 46, *(_BYTE *)(v5 + 88));
  result = luaO_str2d(**(char ***)(v5 + 72), v4);
  if ( !(_DWORD)result )
    result = sub_140542D30(v5, v4);
  return result;
}
