/*
 * Function: j_??$unchecked_copy@PEAPEAVCMoveMapLimitRight@@PEAPEAV1@@stdext@@YAPEAPEAVCMoveMapLimitRight@@PEAPEAV1@00@Z
 * Address: 0x14000F281
 */

CMoveMapLimitRight **__fastcall stdext::unchecked_copy<CMoveMapLimitRight * *,CMoveMapLimitRight * *>(CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, CMoveMapLimitRight **_Dest)
{
  return stdext::unchecked_copy<CMoveMapLimitRight * *,CMoveMapLimitRight * *>(_First, _Last, _Dest);
}
