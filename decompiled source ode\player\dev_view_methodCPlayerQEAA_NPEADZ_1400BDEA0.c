/*
 * Function: ?dev_view_method@CPlayer@@QEAA_NPEAD@Z
 * Address: 0x1400BDEA0
 */

char __usercall CPlayer::dev_view_method@<al>(CPlayer *this@<rcx>, char *pwszDstName@<rdx>, double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@11
  char result; // al@14
  char *v7; // rax@15
  int v8; // eax@15
  int v9; // eax@15
  int v10; // eax@15
  signed int v11; // ecx@15
  signed int v12; // edx@15
  __int64 v13; // rcx@15
  __int64 v14; // rdx@15
  bool v15; // al@23
  __int64 v16; // [sp+0h] [bp-2A8h]@1
  bool bDate[8]; // [sp+20h] [bp-288h]@15
  CPlayer *v18; // [sp+30h] [bp-278h]@4
  unsigned __int8 v19; // [sp+38h] [bp-270h]@6
  int j; // [sp+3Ch] [bp-26Ch]@6
  CPlayer *v21; // [sp+40h] [bp-268h]@9
  CLogFile v22; // [sp+60h] [bp-248h]@15
  char Dest; // [sp+140h] [bp-168h]@15
  const char *v24; // [sp+1D8h] [bp-D0h]@15
  const char *v25; // [sp+1E0h] [bp-C8h]@15
  const char *v26; // [sp+208h] [bp-A0h]@15
  const char *v27; // [sp+210h] [bp-98h]@15
  const char *v28; // [sp+238h] [bp-70h]@15
  const char *v29; // [sp+240h] [bp-68h]@15
  int nParamIndex; // [sp+254h] [bp-54h]@15
  __int64 v31; // [sp+260h] [bp-48h]@4
  size_t MaxCount; // [sp+268h] [bp-40h]@11
  int v33; // [sp+270h] [bp-38h]@15
  unsigned int v34; // [sp+274h] [bp-34h]@15
  int v35; // [sp+278h] [bp-30h]@15
  CGameObjectVtbl *v36; // [sp+280h] [bp-28h]@15
  int v37; // [sp+288h] [bp-20h]@15
  int v38; // [sp+28Ch] [bp-1Ch]@15
  unsigned __int64 v39; // [sp+290h] [bp-18h]@4
  CPlayer *v40; // [sp+2B0h] [bp+8h]@1
  const char *Str; // [sp+2B8h] [bp+10h]@1

  Str = pwszDstName;
  v40 = this;
  v3 = &v16;
  for ( i = 168i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v31 = -2i64;
  v39 = (unsigned __int64)&v16 ^ _security_cookie;
  v18 = 0i64;
  if ( pwszDstName )
  {
    v19 = strlen_0(pwszDstName);
    for ( j = 0; j < 2532; ++j )
    {
      v21 = &g_Player + j;
      if ( v21->m_bLive )
      {
        if ( v21->m_Param.m_byNameLen == v19 )
        {
          MaxCount = v19;
          v5 = CPlayerDB::GetCharNameW(&v21->m_Param);
          if ( !strncmp(v5, Str, MaxCount) )
          {
            v18 = v21;
            break;
          }
        }
      }
    }
  }
  else
  {
    v18 = v40;
  }
  if ( v18 )
  {
    CLogFile::CLogFile(&v22);
    v33 = GetCurrentSec();
    v34 = GetKorLocalTime();
    v7 = CPlayerDB::GetCharNameA(&v18->m_Param);
    *(_DWORD *)bDate = v33;
    sprintf(&Dest, "..\\ZoneServerLog\\CharLog\\%s_Method_%d_%d.log", v7, v34);
    CLogFile::SetWriteLogFile(&v22, &Dest, 1, 0, 0, 0);
    CLogFile::Write(&v22, ">> PARAM ###########");
    v35 = ((int (__fastcall *)(CPlayer *))v18->vfptr->GetMaxHP)(v18);
    v36 = v18->vfptr;
    v8 = ((int (__fastcall *)(CPlayer *))v36->GetHP)(v18);
    *(_DWORD *)bDate = v35;
    CLogFile::Write(&v22, "HP cur: %d, max: %d, lim: %d", (unsigned int)v8, v18->m_nMaxPoint[0]);
    v37 = CPlayer::GetMaxFP(v18);
    v9 = CPlayer::GetFP(v18);
    *(_DWORD *)bDate = v37;
    CLogFile::Write(&v22, "FP cur: %d, max: %d, lim: %d", (unsigned int)v9, v18->m_nMaxPoint[1]);
    v38 = CPlayer::GetMaxSP(v18);
    v10 = CPlayer::GetSP(v18);
    *(_DWORD *)bDate = v38;
    CLogFile::Write(&v22, "SP cur: %d, max: %d, lim: %d", (unsigned int)v10, v18->m_nMaxPoint[2]);
    v24 = "stop";
    v25 = "move";
    v26 = "walk";
    v27 = "run";
    v28 = "stand";
    v29 = "sit";
    CLogFile::Write(&v22, ">> POSTION #########");
    CLogFile::Write(&v22, "MAP: %s", v18->m_pCurMap->m_pMapSet->m_strCode);
    v11 = (signed int)ffloor(v18->m_fCurPos[1]);
    v12 = (signed int)ffloor(v18->m_fCurPos[0]);
    *(_DWORD *)bDate = (signed int)ffloor(v18->m_fCurPos[2]);
    CLogFile::Write(&v22, "POS: %d, %d, %d", (unsigned int)v12, (unsigned int)v11);
    CLogFile::Write(&v22, "TOWN: %d", v18->m_byPosRaceTown);
    v13 = v18->m_byMoveType;
    v14 = v18->m_bMove;
    *(_QWORD *)bDate = (&v28)[8 * v18->m_byStandType];
    CLogFile::Write(&v22, "STATE: %s, MODE: %s, STAND: %s", (&v24)[8 * v14], (&v26)[8 * v13]);
    CLogFile::Write(&v22, ">> RATE ############");
    for ( nParamIndex = 0; nParamIndex < 62; ++nParamIndex )
    {
      _effect_parameter::GetEff_Rate(&v18->m_EP, nParamIndex);
      a3 = *(float *)&a3;
      CLogFile::Write(&v22, "%d\t : %f", (unsigned int)nParamIndex, a3);
    }
    CLogFile::Write(&v22, ">> PLUS ############");
    for ( nParamIndex = 0; nParamIndex < 42; ++nParamIndex )
    {
      _effect_parameter::GetEff_Plus(&v18->m_EP, nParamIndex);
      a3 = *(float *)&a3;
      CLogFile::Write(&v22, "%d\t : %f", (unsigned int)nParamIndex, a3);
    }
    CLogFile::Write(&v22, ">> STATE ###########");
    for ( nParamIndex = 0; nParamIndex < 29; ++nParamIndex )
    {
      v15 = _effect_parameter::GetEff_State(&v18->m_EP, nParamIndex);
      CLogFile::Write(&v22, "%d\t : %d", (unsigned int)nParamIndex, v15);
    }
    CLogFile::Write(&v22, ">> HAVE ############");
    for ( nParamIndex = 0; nParamIndex < 83; ++nParamIndex )
    {
      _effect_parameter::GetEff_Have(&v18->m_EP, nParamIndex);
      a3 = *(float *)&a3;
      CLogFile::Write(&v22, "%d\t : %f", (unsigned int)nParamIndex, a3);
    }
    CLogFile::~CLogFile(&v22);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
