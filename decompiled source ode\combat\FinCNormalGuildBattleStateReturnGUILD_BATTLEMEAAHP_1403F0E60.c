/*
 * Function: ?Fin@CNormalGuildBattleStateReturn@GUILD_BATTLE@@MEAAHPEAVCNormalGuildBattle@2@@Z
 * Address: 0x1403F0E60
 */

__int64 __fastcall GUILD_BATTLE::CNormalGuildBattleStateReturn::Fin(GUILD_BATTLE::CNormalGuildBattleStateReturn *this, GUILD_BATTLE::CNormalGuildBattle *pkBattle)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CNormalGuildBattleGuild *v6; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v7; // [sp+28h] [bp-10h]@4
  GUILD_BATTLE::CNormalGuildBattleStateReturn *v8; // [sp+40h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattle *pkBattlea; // [sp+48h] [bp+10h]@1

  pkBattlea = pkBattle;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = GUILD_BATTLE::CNormalGuildBattle::GetRed(pkBattle);
  v7 = GUILD_BATTLE::CNormalGuildBattle::GetBlue(pkBattlea);
  GUILD_BATTLE::CNormalGuildBattle::NotifyAllProcessEnd(pkBattlea);
  GUILD_BATTLE::CNormalGuildBattleGuild::ReturnHQPosAll(v6);
  GUILD_BATTLE::CNormalGuildBattleGuild::ReturnHQPosAll(v7);
  GUILD_BATTLE::CNormalGuildBattleGuild::ReturnBindPosAll(v6);
  GUILD_BATTLE::CNormalGuildBattleGuild::ReturnBindPosAll(v7);
  GUILD_BATTLE::CNormalGuildBattleState::Log(
    (GUILD_BATTLE::CNormalGuildBattleState *)&v8->vfptr,
    pkBattlea,
    "Fin : Return HQ");
  GUILD_BATTLE::CNormalGuildBattle::RewardItem(pkBattlea);
  GUILD_BATTLE::CNormalGuildBattleState::Log(
    (GUILD_BATTLE::CNormalGuildBattleState *)&v8->vfptr,
    pkBattlea,
    "Fin : RewardItem");
  return 0i64;
}
