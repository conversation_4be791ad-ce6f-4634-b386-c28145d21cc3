/*
 * Function: ?OnButtonMapchange@CMapTab@@IEAAXXZ
 * Address: 0x14002EC30
 */

void __fastcall CMapTab::OnButtonMapchange(CMapTab *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  struct _TREEITEM *v4; // [sp+30h] [bp-18h]@7
  unsigned int dwKey1; // [sp+38h] [bp-10h]@9
  CMapTab *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !CMainThread::IsExcuteService(&g_Main) )
  {
    if ( unk_1414736F8 )
    {
      v4 = CTreeCtrl::GetSelectedItem(&v6->m_trMap);
      if ( v4 && (unsigned int)CTreeCtrl::ItemHasChildren(&v6->m_trMap, v4) )
      {
        for ( dwKey1 = 0; (signed int)dwKey1 < dword_141470B98; ++dwKey1 )
        {
          if ( v4 == v6->m_hMap[dwKey1] )
          {
            CMsgData::PackingMsg(&stru_1415B7048, 0x3EAu, dwKey1, 0, 0);
            return;
          }
        }
      }
    }
    else
    {
      MyMessageBox("Display Error", "Not Display Mode");
    }
  }
}
