/*
 * Function: ??0?$AssignFromHelperClass@V?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@QEAA@PEAV?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@1@AEBVNameValuePairs@1@@Z
 * Address: 0x14058BD20
 */

__int64 __fastcall CryptoPP::AssignFromHelperClass<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>,CryptoPP::DL_GroupParameters_IntegerBased>::AssignFromHelperClass<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>,CryptoPP::DL_GroupParameters_IntegerBased>(__int64 a1, __int64 a2, struct CryptoPP::NameValuePairs *a3)
{
  __int64 v4; // [sp+30h] [bp+8h]@1
  __int64 v5; // [sp+38h] [bp+10h]@1
  struct CryptoPP::NameValuePairs *v6; // [sp+40h] [bp+18h]@1

  v6 = a3;
  v5 = a2;
  v4 = a1;
  *(_QWORD *)a1 = a2;
  *(_QWORD *)(a1 + 8) = a3;
  *(_BYTE *)(a1 + 16) = 0;
  if ( (unsigned __int8)CryptoPP::NameValuePairs::GetThisObject<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>>() )
  {
    *(_BYTE *)(v4 + 16) = 1;
  }
  else if ( (unsigned __int8)type_info::operator!=(
                               &CryptoPP::DL_GroupParameters_IntegerBased `RTTI Type Descriptor',
                               &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor') )
  {
    CryptoPP::DL_GroupParameters_IntegerBased::AssignFrom((CryptoPP::DL_GroupParameters_IntegerBased *)(v5 + 80), v6);
  }
  return v4;
}
