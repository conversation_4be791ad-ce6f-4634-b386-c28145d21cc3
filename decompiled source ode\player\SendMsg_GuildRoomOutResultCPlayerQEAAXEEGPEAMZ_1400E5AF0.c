/*
 * Function: ?SendMsg_GuildRoomOutResult@CPlayer@@QEAAXEEGPEAM@Z
 * Address: 0x1400E5AF0
 */

void __fastcall CPlayer::SendMsg_GuildRoomOutResult(CPlayer *this, char byRetCode, char byMapIndex, unsigned __int16 wMapLayer, float *pPos)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v7; // ax@6
  __int64 v8; // [sp+0h] [bp-98h]@1
  _guildroom_out_result_zocl v9; // [sp+38h] [bp-60h]@4
  char pbyType; // [sp+64h] [bp-34h]@6
  char v11; // [sp+65h] [bp-33h]@6
  unsigned __int64 v12; // [sp+80h] [bp-18h]@4
  CPlayer *v13; // [sp+A0h] [bp+8h]@1

  v13 = this;
  v5 = &v8;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v12 = (unsigned __int64)&v8 ^ _security_cookie;
  v9.byRetCode = byRetCode;
  v9.byMapIndex = byMapIndex;
  v9.wMapLayerIndex = wMapLayer;
  if ( pPos )
    FloatToShort(pPos, v9.sNewPos, 3);
  pbyType = 27;
  v11 = 107;
  v7 = _guildroom_out_result_zocl::size(&v9);
  CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, &v9.byRetCode, v7);
}
