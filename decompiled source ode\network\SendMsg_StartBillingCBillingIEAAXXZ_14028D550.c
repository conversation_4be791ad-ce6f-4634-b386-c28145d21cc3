/*
 * Function: ?SendMsg_StartBilling@CBilling@@IEAAXXZ
 * Address: 0x14028D550
 */

void __fastcall CBilling::SendMsg_StartBilling(CBilling *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-A8h]@1
  char Dst; // [sp+38h] [bp-70h]@5
  char pbyType; // [sp+74h] [bp-34h]@5
  char v6; // [sp+75h] [bp-33h]@5
  unsigned __int64 v7; // [sp+90h] [bp-18h]@4
  CBilling *v8; // [sp+B0h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = (unsigned __int64)&v3 ^ _security_cookie;
  if ( v8->m_bOper )
  {
    memcpy_0(&Dst, byte_1799C5B78, 0x21ui64);
    pbyType = 29;
    v6 = 1;
    CNetProcess::LoadSendMsg(qword_1414F20A0, 0, &pbyType, &Dst, 0x21u);
  }
}
