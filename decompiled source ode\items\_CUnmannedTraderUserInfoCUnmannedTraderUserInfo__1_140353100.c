/*
 * Function: _CUnmannedTraderUserInfo::CUnmannedTraderUserInfo_::_1_::dtor$1
 * Address: 0x140353100
 */

void __fastcall CUnmannedTraderUserInfo::CUnmannedTraderUserInfo_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>((std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)(*(_QWORD *)(a2 + 64) + 24i64));
}
