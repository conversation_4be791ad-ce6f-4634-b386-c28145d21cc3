/*
 * Function: ?Goto@CGuildBattleStateList@GUILD_BATTLE@@QEAAHXZ
 * Address: 0x1403DF2C0
 */

signed __int64 __fastcall GUILD_BATTLE::CGuildBattleStateList::Goto(GUILD_BATTLE::CGuildBattleStateList *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@6
  GUILD_BATTLE::CGuildBattleStateList *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( GUILD_BATTLE::CGuildBattleStateList::IsEmpty(v6) )
  {
    result = 0i64;
  }
  else
  {
    v5 = GUILD_BATTLE::CGuildBattleStateList::CheckLoop(v6);
    if ( v5 == 2 )
    {
      ((void (__fastcall *)(GUILD_BATTLE::CGuildBattleStateList *))v6->vfptr->SetNextState)(v6);
      result = 1i64;
    }
    else
    {
      result = (unsigned int)v5;
    }
  }
  return result;
}
