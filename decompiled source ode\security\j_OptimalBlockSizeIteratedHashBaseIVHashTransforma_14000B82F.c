/*
 * Function: j_?OptimalBlockSize@?$IteratedHashBase@IVHashTransformation@CryptoPP@@@CryptoPP@@UEBAIXZ
 * Address: 0x14000B82F
 */

unsigned int __fastcall CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation>::OptimalBlockSize(CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation> *this)
{
  return CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation>::OptimalBlockSize(this);
}
