/*
 * Function: ?IsEmptyTime@CGuildBattleReservedSchedule@GUILD_BATTLE@@QEAAEKK@Z
 * Address: 0x1403DABC0
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::IsEmptyTime(GUILD_BATTLE::CGuildBattleReservedSchedule *this, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt)
{
  int *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  unsigned int v6; // [sp+0h] [bp-18h]@1
  GUILD_BATTLE::CGuildBattleReservedSchedule *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v3 = (int *)&v6;
  for ( i = 4i64; i; --i )
  {
    *v3 = -858993460;
    ++v3;
  }
  v6 = dwStartTimeInx;
  if ( dwStartTimeInx || !dwElapseTimeCnt )
  {
    if ( v6 < 0x17 )
    {
      if ( v7->m_bUseField[v6] )
        result = 112;
      else
        result = 0;
    }
    else
    {
      result = 122;
    }
  }
  else
  {
    result = 125;
  }
  return result;
}
