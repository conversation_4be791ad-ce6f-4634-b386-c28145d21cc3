/*
 * Function: ?GetMapInxList@CNormalGuildBattleFieldList@GUILD_BATTLE@@QEAAKEPEAE@Z
 * Address: 0x1403EEB70
 */

__int64 __fastcall GUILD_BATTLE::CNormalGuildBattleFieldList::GetMapInxList(GUILD_BATTLE::CNormalGuildBattleFieldList *this, char byRace, char *pbyInxList)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  char v6; // al@11
  __int64 v7; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@9
  GUILD_BATTLE::CNormalGuildBattleFieldList *v9; // [sp+40h] [bp+8h]@1
  char v10; // [sp+48h] [bp+10h]@1
  char *v11; // [sp+50h] [bp+18h]@1

  v11 = pbyInxList;
  v10 = byRace;
  v9 = this;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( (signed int)(unsigned __int8)byRace < 3 )
  {
    if ( v9->m_byUseFieldCnt[(unsigned __int8)byRace] && v9->m_ppkUseFieldByRace[(unsigned __int8)byRace] )
    {
      for ( j = 0; j < v9->m_byUseFieldCnt[(unsigned __int8)v10]; ++j )
      {
        v6 = GUILD_BATTLE::CNormalGuildBattleField::GetMapCode(v9->m_ppkUseFieldByRace[(unsigned __int8)v10][j]);
        v11[j] = v6;
      }
      result = v9->m_byUseFieldCnt[(unsigned __int8)v10];
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
