/*
 * Function: ?SetMstHaveEffect@CPlayer@@QEAAXPEBU_ResourceItem_fld@@PEBU_db_con@_STORAGE_LIST@@_NH@Z
 * Address: 0x140052970
 */

void __fastcall CPlayer::SetMstHaveEffect(CPlayer *this, _ResourceItem_fld *pFld, _STORAGE_LIST::_db_con *pItem, bool bAdd, int nAlter)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 v7; // rcx@11
  float v8; // xmm0_4@11
  __int64 v9; // [sp+0h] [bp-48h]@1
  int nDiffCnt; // [sp+20h] [bp-28h]@17
  float v11; // [sp+30h] [bp-18h]@4
  float v12; // [sp+34h] [bp-14h]@4
  int j; // [sp+38h] [bp-10h]@4
  CPlayer *v14; // [sp+50h] [bp+8h]@1
  _ResourceItem_fld *v15; // [sp+58h] [bp+10h]@1
  _STORAGE_LIST::_db_con *v16; // [sp+60h] [bp+18h]@1
  bool v17; // [sp+68h] [bp+20h]@1

  v17 = bAdd;
  v16 = pItem;
  v15 = pFld;
  v14 = this;
  v5 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v11 = 0.0;
  v12 = 0.0;
  for ( j = 0; j < v15->m_nEffectDataNum; ++j )
  {
    if ( v15->m_EffectData[j].nEffCode > -1
      && v15->m_EffectData[j].nEffCode >= 59
      && v15->m_EffectData[j].nEffCode <= 65 )
    {
      if ( nAlter )
      {
        v11 = v15->m_EffectData[j].fEffUnit * (float)nAlter;
      }
      else
      {
        v7 = v16->m_dwDur;
        v8 = (float)(signed int)v7;
        if ( v7 < 0 )
          v8 = v8 + 1.8446744e19;
        v11 = v15->m_EffectData[j].fEffUnit * v8;
      }
      v12 = v14->m_EP.m_pDataParam->m_fEff_Have[v15->m_EffectData[j].nEffCode];
      v14->m_EP.m_pDataParam->m_fEff_Have[v15->m_EffectData[j].nEffCode] = v14->m_EP.m_pDataParam->m_fEff_Have[v15->m_EffectData[j].nEffCode]
                                                                         + v11;
      if ( v14->m_EP.m_pDataParam->m_fEff_Have[v15->m_EffectData[j].nEffCode] > v15->m_EffectData[j].fEffUnitMax )
      {
        v14->m_EP.m_pDataParam->m_fEff_Have[v15->m_EffectData[j].nEffCode] = v15->m_EffectData[j].fEffUnitMax;
        v11 = v15->m_EffectData[j].fEffUnitMax - v12;
      }
      nDiffCnt = 0;
      CPlayer::apply_have_item_std_effect(v14, v15->m_EffectData[j].nEffCode, v11, v17, 0);
    }
  }
}
