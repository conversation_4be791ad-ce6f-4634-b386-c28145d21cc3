/*
 * Function: ?SetEventRespawn@CMonsterEventRespawn@@QEAA_NXZ
 * Address: 0x1402A5DE0
 */

char __fastcall CMonsterEventRespawn::SetEventRespawn(CMonsterEventRespawn *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed int v4; // ecx@35
  signed int v5; // edx@35
  __int64 v6; // [sp+0h] [bp-DC8h]@1
  DWORD nSize[2]; // [sp+20h] [bp-DA8h]@15
  LPCSTR lpFileName; // [sp+28h] [bp-DA0h]@23
  char Dest[2080]; // [sp+40h] [bp-D88h]@7
  char KeyName; // [sp+860h] [bp-568h]@18
  int v11; // [sp+8E4h] [bp-4E4h]@6
  _WIN32_FIND_DATAA FindFileData; // [sp+900h] [bp-4C8h]@6
  HANDLE hFindFile; // [sp+A48h] [bp-380h]@6
  int v14; // [sp+A50h] [bp-378h]@7
  int v15; // [sp+A54h] [bp-374h]@10
  int j; // [sp+A58h] [bp-370h]@10
  char FileName; // [sp+A70h] [bp-358h]@12
  bool *v18; // [sp+AF8h] [bp-2D0h]@12
  UINT v19; // [sp+B00h] [bp-2C8h]@12
  unsigned int v20; // [sp+B04h] [bp-2C4h]@16
  unsigned int k; // [sp+B08h] [bp-2C0h]@16
  char ReturnedString; // [sp+B20h] [bp-2A8h]@18
  _base_fld *v23; // [sp+B68h] [bp-260h]@20
  UINT v24; // [sp+B70h] [bp-258h]@22
  char szMapCode; // [sp+B90h] [bp-238h]@27
  CMapData *v26; // [sp+C18h] [bp-1B0h]@27
  int l; // [sp+C20h] [bp-1A8h]@29
  UINT v28; // [sp+C24h] [bp-1A4h]@31
  UINT v29; // [sp+C28h] [bp-1A0h]@36
  UINT v30; // [sp+C2Ch] [bp-19Ch]@36
  UINT v31; // [sp+C30h] [bp-198h]@36
  UINT v32; // [sp+C34h] [bp-194h]@36
  UINT v33; // [sp+C38h] [bp-190h]@36
  UINT v34; // [sp+C3Ch] [bp-18Ch]@36
  UINT v35; // [sp+C40h] [bp-188h]@36
  int m; // [sp+C44h] [bp-184h]@36
  char psItemCode; // [sp+C60h] [bp-168h]@39
  unsigned __int8 v38; // [sp+CE4h] [bp-E4h]@40
  _base_fld *v39; // [sp+CE8h] [bp-E0h]@42
  char Str1; // [sp+D00h] [bp-C8h]@44
  _base_fld *v41; // [sp+D88h] [bp-40h]@44
  char v42; // [sp+D90h] [bp-38h]@47
  UINT n; // [sp+D94h] [bp-34h]@47
  UINT v44; // [sp+D98h] [bp-30h]@54
  bool *v45; // [sp+DA0h] [bp-28h]@54
  bool v46; // [sp+DB0h] [bp-18h]@55
  bool v47; // [sp+DB1h] [bp-17h]@55
  bool v48; // [sp+DB2h] [bp-16h]@55
  bool v49; // [sp+DB3h] [bp-15h]@55
  unsigned __int64 v50; // [sp+DB8h] [bp-10h]@4
  CMonsterEventRespawn *v51; // [sp+DD0h] [bp+8h]@1

  v51 = this;
  v1 = &v6;
  for ( i = 880i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v50 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( !(_S1_2 & 1) )
  {
    _S1_2 |= 1u;
    nExtLen = strlen_0(".ini");
  }
  v11 = 0;
  hFindFile = FindFirstFileA(".\\EventRespawn\\*.ini", &FindFileData);
  if ( hFindFile != (HANDLE)-1 )
  {
    do
    {
      v14 = strlen_0(FindFileData.cFileName) - nExtLen;
      strncpy(&Dest[64 * (signed __int64)v11], FindFileData.cFileName, v14);
      *(&Dest[64 * (signed __int64)v11++] + v14) = 0;
    }
    while ( v11 < 32 && FindNextFileA(hFindFile, &FindFileData) );
    FindClose(hFindFile);
  }
  v15 = 0;
  for ( j = 0; j < v11; ++j )
  {
    sprintf(&FileName, ".\\EventRespawn\\%s.ini", &Dest[64 * (signed __int64)j]);
    v18 = &v51->m_EventRespawn[v15].bLoad;
    strcpy_0(v51->m_EventRespawn[v15].szScriptName, &Dest[64 * (signed __int64)j]);
    v19 = GetPrivateProfileIntA("MONSTER", "set", -1, &FileName);
    if ( v19 == -1 )
    {
      CLogFile::Write(
        &stru_1799C8F30,
        "Monster Respawn Load Error : %s >> mon set num error",
        &Dest[64 * (signed __int64)j]);
      return 0;
    }
    if ( (signed int)v19 > 640 )
    {
      nSize[0] = 640;
      CLogFile::Write(
        &stru_1799C8F30,
        "Monster Respawn Load Error : %s >> mon set num error : %d > %d",
        &Dest[64 * (signed __int64)j],
        v19);
      return 0;
    }
    *((_WORD *)v18 + 1) = v19;
    v20 = 0;
    for ( k = 0; (signed int)k < (signed int)v19; ++k )
    {
      sprintf(&KeyName, "code%d", k);
      GetPrivateProfileStringA("MONSTER", &KeyName, "X", &ReturnedString, 0x40u, &FileName);
      if ( !strcmp_0(&ReturnedString, "X") )
      {
        *(_QWORD *)nSize = &ReturnedString;
        CLogFile::Write(
          &stru_1799C8F30,
          "Monster Respawn Load Error : %s >> mon code error : %d) %s",
          &Dest[64 * (signed __int64)j],
          k);
        return 0;
      }
      v23 = CRecordData::GetRecord(&stru_1799C6210, &ReturnedString);
      if ( !v23 )
      {
        *(_QWORD *)nSize = &ReturnedString;
        CLogFile::Write(
          &stru_1799C8F30,
          "Monster Respawn Load Error : %s >> mon code error : %d) %s",
          &Dest[64 * (signed __int64)j],
          k);
        return 0;
      }
      *(_QWORD *)&v18[16 * k + 8] = v23;
      sprintf(&KeyName, "num%d", k);
      v24 = GetPrivateProfileIntA("MONSTER", &KeyName, -1, &FileName);
      if ( v24 == -1 )
      {
        LODWORD(lpFileName) = -1;
        *(_QWORD *)nSize = &ReturnedString;
        CLogFile::Write(
          &stru_1799C8F30,
          "Monster Respawn Load Error : %s >> mon num error : %d) %s num: %d",
          &Dest[64 * (signed __int64)j],
          k);
        return 0;
      }
      *(_WORD *)&v18[16 * k + 16] = v24;
      v20 += v24;
    }
    if ( (signed int)v20 > 640 )
    {
      nSize[0] = 640;
      CLogFile::Write(
        &stru_1799C8F30,
        "Monster Respawn Load Error : %s >> total mon num error : %d > %d",
        &Dest[64 * (signed __int64)j],
        v20);
      return 0;
    }
    GetPrivateProfileStringA("POSITION", "map", "X", &szMapCode, 0x80u, &FileName);
    v26 = CMapOperation::GetMap(&g_MapOper, &szMapCode);
    if ( !v26 )
    {
      CLogFile::Write(
        &stru_1799C8F30,
        "Monster Respawn Load Error : %s >> map code error : %s",
        &Dest[64 * (signed __int64)j],
        &szMapCode);
      return 0;
    }
    *((_QWORD *)v18 + 33) = v26;
    for ( l = 0; l < 3; ++l )
    {
      v28 = GetPrivateProfileIntA("POSITION", *((LPCSTR *)&EqSukList[23].nCode + l), 0xFFFF, &FileName);
      if ( v28 == 0xFFFF )
      {
        CLogFile::Write(
          &stru_1799C8F30,
          "Monster Respawn Load Error : %s >> xyz error : %d",
          &Dest[64 * (signed __int64)j],
          0xFFFFi64);
        return 0;
      }
      *(float *)&v18[4 * l + 272] = (float)(signed int)v28;
    }
    if ( !CMapData::IsMapIn(v26, (float *)v18 + 68) )
    {
      v4 = (signed int)ffloor(*((float *)v18 + 69));
      v5 = (signed int)ffloor(*((float *)v18 + 68));
      LODWORD(lpFileName) = (signed int)ffloor(*((float *)v18 + 70));
      nSize[0] = v4;
      CLogFile::Write(
        &stru_1799C8F30,
        "Monster Respawn Load Error : %s >> xyz range error : %d %d %d",
        &Dest[64 * (signed __int64)j],
        (unsigned int)v5);
      return 0;
    }
    v29 = GetPrivateProfileIntA("TERM", "hour", 0, &FileName);
    v30 = GetPrivateProfileIntA("TERM", "min", 0, &FileName);
    v31 = GetPrivateProfileIntA("TERM", "sec", 0, &FileName);
    *((_DWORD *)v18 + 71) = 1000 * (3600 * v29 + v31 + 60 * v30);
    v32 = GetPrivateProfileIntA("OPTION", "kill after stop", 0, &FileName);
    v33 = GetPrivateProfileIntA("OPTION", "exp penalty", 0, &FileName);
    v34 = GetPrivateProfileIntA("OPTION", "exp reward", 1, &FileName);
    v35 = GetPrivateProfileIntA("OPTION", "item loot", 1, &FileName);
    *((_DWORD *)v18 + 73) = 0;
    for ( m = 0; m < 128; ++m )
    {
      sprintf(&KeyName, "item code %d", (unsigned int)(m + 1));
      GetPrivateProfileStringA("REWARD ITEM", &KeyName, "X", &psItemCode, 0x80u, &FileName);
      if ( psItemCode != 88 )
      {
        v38 = GetItemTableCode(&psItemCode);
        if ( v38 == 255 )
        {
          CLogFile::Write(
            &stru_1799C8F30,
            "Monster Respawn Load Error : %s >> reward item : item code error : %s",
            &psItemCode);
          return 0;
        }
        v39 = CRecordData::GetRecordByHash((CRecordData *)&unk_1799C6AA0 + v38, &psItemCode, 2, 5);
        if ( !v39 )
        {
          CLogFile::Write(
            &stru_1799C8F30,
            "Monster Respawn Load Error : %s >> reward item : item code error : %s",
            &psItemCode);
          return 0;
        }
        sprintf(&KeyName, "monster %d", (unsigned int)(m + 1));
        GetPrivateProfileStringA("REWARD ITEM", &KeyName, "ALL", &Str1, 0x80u, &FileName);
        v41 = 0i64;
        if ( strcmp_0(&Str1, "ALL") )
        {
          v41 = CRecordData::GetRecord(&stru_1799C6210, &Str1);
          if ( !v41 )
          {
            CLogFile::Write(
              &stru_1799C8F30,
              "Monster Respawn Load Error : %s >> reward item : monster code error : %s",
              &Str1);
            return 0;
          }
          v42 = 0;
          for ( n = 0; (signed int)n < (signed int)v19; ++n )
          {
            if ( *(_base_fld **)&v18[16 * n + 8] == v41 )
            {
              v42 = 1;
              break;
            }
          }
          if ( !v42 )
          {
            CLogFile::Write(
              &stru_1799C8F30,
              "Monster Respawn Load Error : %s >> reward item : monster code match error : %s",
              &Str1);
            return 0;
          }
        }
        sprintf(&KeyName, "item %% %d", (unsigned int)(m + 1));
        v44 = GetPrivateProfileIntA("REWARD ITEM", &KeyName, 0, &FileName);
        v45 = &v18[32 * *((_DWORD *)v18 + 73) + 296];
        *v45 = v38;
        *((_QWORD *)v45 + 1) = v39;
        *((_DWORD *)v45 + 6) = v44;
        *((_QWORD *)v45 + 2) = v41;
        ++*((_DWORD *)v18 + 73);
      }
    }
    v46 = v32 != 0;
    v18[288] = v32 != 0;
    v47 = v33 != 0;
    v18[289] = v33 != 0;
    v48 = v34 != 0;
    v18[290] = v34 != 0;
    v49 = v35 != 0;
    v18[291] = v35 != 0;
    *v18 = 1;
    ++v15;
    CLogFile::Write(&stru_1799C8F30, "Monster Respawn Load >> %s", &Dest[64 * (signed __int64)j]);
  }
  v51->m_nLoadEventRespawn = v15;
  return 1;
}
