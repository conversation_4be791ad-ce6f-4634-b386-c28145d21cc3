/*
 * Function: j_??$_Uninit_copy@PEAPEAVTRC_AutoTrade@@PEAPEAV1@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@YAPEAPEAVTRC_AutoTrade@@PEAPEAV1@00AEAV?$allocator@PEAVTRC_AutoTrade@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140006023
 */

TRC_AutoTrade **__fastcall std::_Uninit_copy<TRC_AutoTrade * *,TRC_AutoTrade * *,std::allocator<TRC_AutoTrade *>>(TRC_AutoTrade **_First, TRC_AutoTrade **_Last, TRC_AutoTrade **_Dest, std::allocator<TRC_AutoTrade *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<TRC_AutoTrade * *,TRC_AutoTrade * *,std::allocator<TRC_AutoTrade *>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
