/*
 * Function: ?pc_GuildListRequest@CPlayer@@QEAAXE@Z
 * Address: 0x1400AB850
 */

void __fastcall CPlayer::pc_GuildListRequest(CPlayer *this, char byPage)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v4; // rax@4
  unsigned int v5; // eax@4
  char v6; // al@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  unsigned int v8; // [sp+20h] [bp-18h]@4
  int v9; // [sp+24h] [bp-14h]@4
  CPlayer *v10; // [sp+40h] [bp+8h]@1
  char v11; // [sp+48h] [bp+10h]@1

  v11 = byPage;
  v10 = this;
  v2 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = CPlayerDB::GetCharSerial(&v10->m_Param);
  v9 = CPlayerDB::GetRaceCode(&v10->m_Param);
  v4 = CPvpUserAndGuildRankingSystem::Instance();
  v5 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v4, v9, 0);
  if ( v8 == v5 )
  {
    v6 = CPlayerDB::GetRaceCode(&v10->m_Param);
    CGuildList::SendList(&CGuild::s_GuildList, v10->m_ObjID.m_wIndex, v6, v11);
  }
}
