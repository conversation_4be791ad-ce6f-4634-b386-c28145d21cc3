/*
 * Function: ?D3DUtil_GetCubeMapViewMatrix@@YA?AUD3DXMATRIX@@K@Z
 * Address: 0x14052B5D0
 */

struct D3DXMATRIX *__fastcall D3DUtil_GetCubeMapViewMatrix(struct D3DXMATRIX *retstr, int a2)
{
  struct D3DXMATRIX *v2; // rbx@1
  float v3; // xmm0_4@2
  int *v4; // rcx@2
  char *v5; // rdx@4
  int v6; // eax@4
  float v8; // [sp+20h] [bp-48h]@2
  float v9; // [sp+24h] [bp-44h]@2
  float v10; // [sp+28h] [bp-40h]@2
  char v11; // [sp+30h] [bp-38h]@4
  float v12; // [sp+34h] [bp-34h]@4
  float v13; // [sp+40h] [bp-28h]@3
  float v14; // [sp+44h] [bp-24h]@4
  float v15; // [sp+48h] [bp-20h]@3
  int v16; // [sp+50h] [bp-18h]@1
  int v17; // [sp+54h] [bp-14h]@1
  int v18; // [sp+58h] [bp-10h]@1

  v2 = retstr;
  v16 = 0;
  v17 = 0;
  v18 = 0;
  switch ( a2 )
  {
    case 0:
      v3 = FLOAT_1_0;
      v9 = 0.0;
      v10 = 0.0;
      v4 = (int *)&v8;
      v8 = FLOAT_1_0;
      goto LABEL_10;
    case 1:
      v15 = 0.0;
      v13 = FLOAT_N1_0;
      goto LABEL_8;
    case 2:
      v5 = &v11;
      v13 = 0.0;
      *(_DWORD *)&v11 = 0;
      v12 = FLOAT_1_0;
      v6 = 0;
      v14 = 0.0;
      v15 = FLOAT_N1_0;
      goto LABEL_11;
    case 3:
      v5 = &v11;
      v13 = 0.0;
      *(_DWORD *)&v11 = 0;
      v12 = FLOAT_N1_0;
      v6 = 0;
      v14 = 0.0;
      v15 = FLOAT_1_0;
      goto LABEL_11;
    case 4:
      v3 = FLOAT_1_0;
      v13 = 0.0;
      v15 = FLOAT_1_0;
      goto LABEL_9;
    case 5:
      v13 = 0.0;
      v15 = FLOAT_N1_0;
LABEL_8:
      v3 = FLOAT_1_0;
LABEL_9:
      v14 = 0.0;
      v4 = (int *)&v13;
LABEL_10:
      v5 = &v11;
      *(_DWORD *)&v11 = *v4;
      v12 = *((float *)v4 + 1);
      v6 = v4[2];
      v15 = 0.0;
      v14 = v3;
LABEL_11:
      *((_DWORD *)v5 + 2) = v6;
      v13 = 0.0;
      v8 = 0.0;
      v9 = v14;
      v10 = v15;
      break;
    default:
      break;
  }
  D3DXMatrixLookAtLH_0(v2, &v16, &v11, &v8);
  return v2;
}
