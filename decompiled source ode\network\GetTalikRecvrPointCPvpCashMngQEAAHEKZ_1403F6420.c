/*
 * Function: ?GetTalikRecvrPoint@CPvpCashMng@@QEAAHEK@Z
 * Address: 0x1403F6420
 */

__int64 __fastcall CPvpCashMng::GetTalikRecvrPoint(CPvpCashMng *this, char byTblCode, unsigned int dwIndex)
{
  int *v3; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CPvpCashMng *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v3 = &j;
  for ( i = 4i64; i; --i )
  {
    *v3 = -858993460;
    ++v3;
  }
  for ( j = 0; j < v7->m_TalikList.byTalikNum; ++j )
  {
    if ( v7->m_TalikList.TalikInfo[j].byTableCode == (unsigned __int8)byTblCode
      && v7->m_TalikList.TalikInfo[j].m_pFld->m_dwIndex == dwIndex )
    {
      return v7->m_TalikList.TalikInfo[j].nRecvrPoint;
    }
  }
  return 0i64;
}
