/*
 * Function: ?close@CMgrAvatorItemHistory@@QEAAXHPEAD0@Z
 * Address: 0x140238090
 */

void __fastcall CMgrAvatorItemHistory::close(CMgrAvatorItemHistory *this, int n, char *pCloseCode, char *pszFileName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  char *v7; // [sp+20h] [bp-18h]@4
  CMgrAvatorItemHistory *v8; // [sp+40h] [bp+8h]@1
  char *pszFileNamea; // [sp+58h] [bp+20h]@1

  pszFileNamea = pszFileName;
  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7 = v8->m_szCurTime;
  sprintf(sData, "\r\nCLOSE %s [%s %s]\r\n\r\n", pCloseCode);
  CMgrAvatorItemHistory::WriteFile(v8, pszFileNamea, sData);
}
