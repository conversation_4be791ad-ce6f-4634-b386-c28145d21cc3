/*
 * Function: ??G?$_Vector_const_iterator@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEBA_JAEBV01@@Z
 * Address: 0x140373240
 */

__int64 __fastcall std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::operator-(std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *_Right)
{
  return this->_Myptr - _Right->_Myptr;
}
