/*
 * Function: _CUnmannedTraderDivisionInfo::GetSortType_::_1_::dtor$1
 * Address: 0x14036E270
 */

void __fastcall CUnmannedTraderDivisionInfo::GetSortType_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::~_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>((std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *)(a2 + 72));
}
