/*
 * Function: ?size@?$vector@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@QEBA_KXZ
 * Address: 0x140592EB0
 */

signed __int64 __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::size(__int64 a1)
{
  signed __int64 v2; // [sp+0h] [bp-18h]@2

  if ( *(_QWORD *)(a1 + 16) )
    v2 = (*(_QWORD *)(a1 + 24) - *(_QWORD *)(a1 + 16)) / 80i64;
  else
    v2 = 0i64;
  return v2;
}
