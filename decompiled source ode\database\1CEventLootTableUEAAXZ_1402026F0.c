/*
 * Function: ??1CEventLootTable@@UEAA@XZ
 * Address: 0x1402026F0
 */

void __fastcall CEventLootTable::~CEventLootTable(CEventLootTable *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  CEventLootTable::_event_drop *j; // [sp+20h] [bp-28h]@4
  CEventLootTable::_event_drop *v5; // [sp+28h] [bp-20h]@6
  void *v6; // [sp+30h] [bp-18h]@6
  CEventLootTable *v7; // [sp+50h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7->vfptr = (CEventLootTableVtbl *)&CEventLootTable::`vftable';
  for ( j = v7->m_pEventDropList; j; j = v5 )
  {
    v5 = j->pNext;
    v6 = j;
    operator delete(j);
  }
  v7->m_pEventDropList = 0i64;
}
