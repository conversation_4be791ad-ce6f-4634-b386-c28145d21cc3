/*
 * Function: ?buy_to_inven_cashitem@CMgrAvatorItemHistory@@QEAAXEGHHHHHPEAD_KE@Z
 * Address: 0x14023DF10
 */

void __fastcall CMgrAvatorItemHistory::buy_to_inven_cashitem(CMgrAvatorItemHistory *this, char byTbl, unsigned __int16 wIndex, int nPrice, int nDis, int nNum, int nBuyPrice, int nAmount, char *pFileName, unsigned __int64 lnUID, char byEventType)
{
  __int64 *v11; // rdi@1
  signed __int64 i; // rcx@1
  char *v13; // rax@5
  unsigned int v14; // eax@6
  __int64 v15; // [sp+0h] [bp-A8h]@1
  unsigned __int64 v16; // [sp+20h] [bp-88h]@5
  int v17; // [sp+28h] [bp-80h]@5
  int v18; // [sp+30h] [bp-78h]@5
  int v19; // [sp+38h] [bp-70h]@5
  int v20; // [sp+40h] [bp-68h]@5
  int v21; // [sp+48h] [bp-60h]@5
  unsigned int v22; // [sp+50h] [bp-58h]@5
  int v23; // [sp+58h] [bp-50h]@5
  _base_fld *v24; // [sp+60h] [bp-48h]@4
  __time32_t Time; // [sp+74h] [bp-34h]@4
  struct tm *v26; // [sp+88h] [bp-20h]@4
  int v27; // [sp+90h] [bp-18h]@5
  unsigned int v28; // [sp+94h] [bp-14h]@5
  int v29; // [sp+98h] [bp-10h]@5
  CMgrAvatorItemHistory *v30; // [sp+B0h] [bp+8h]@1
  char v31; // [sp+B8h] [bp+10h]@1
  unsigned __int16 v32; // [sp+C0h] [bp+18h]@1
  int v33; // [sp+C8h] [bp+20h]@1

  v33 = nPrice;
  v32 = wIndex;
  v31 = byTbl;
  v30 = this;
  v11 = &v15;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v11 = -858993460;
    v11 = (__int64 *)((char *)v11 + 4);
  }
  sData[0] = 0;
  v24 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)byTbl, wIndex);
  _time32(&Time);
  v26 = _localtime32(&Time);
  if ( v24 )
  {
    v27 = GetItemTableCode(v24->m_strCode);
    v28 = GetKorLocalTime();
    v29 = (unsigned __int8)byEventType;
    v13 = GetItemKorName((unsigned __int8)v31, v32);
    v23 = v26->tm_sec;
    v22 = v28;
    v21 = nAmount;
    v20 = v29;
    v19 = nNum;
    v18 = nDis;
    v17 = v33;
    v16 = lnUID;
    sprintf(
      sData,
      "[BUY_CASHITEM] : %s(%s) [UID: %I64u] [Price:%d Discount:%d Num:%d Event: %d] [Remain:%d][T:%u/%d]\r\n",
      v24->m_strCode,
      v13);
  }
  else
  {
    v14 = GetKorLocalTime();
    sprintf(sData, "[BUY_CASHITEM] - [Report Server Developer][T:%u]\r\n", v14);
  }
  CMgrAvatorItemHistory::WriteFile(v30, pFileName, sData);
}
