/*
 * Function: j_?GuildBattleSuggestRequestToDestGuild@CGuild@@QEAAEKKKK@Z
 * Address: 0x140011F59
 */

char __fastcall CGuild::GuildBattleSuggestRequestToDestGuild(CGuild *this, unsigned int dwSrcGuildSerial, unsigned int dwStartTimeInx, unsigned int dwMemberCntInx, unsigned int dwMapInx)
{
  return CGuild::GuildBattleSuggestRequestToDestGuild(this, dwSrcGuildSerial, dwStartTimeInx, dwMemberCntInx, dwMapInx);
}
