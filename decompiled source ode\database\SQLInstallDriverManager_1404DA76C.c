/*
 * Function: SQLInstallDriverManager
 * Address: 0x1404DA76C
 */

int __fastcall SQLInstallDriverManager(char *lpszPath, unsigned __int16 cbPathMax, unsigned __int16 *pcbPathOut)
{
  char *v3; // rsi@1
  unsigned __int16 *v4; // rbx@1
  unsigned __int16 v5; // di@1
  __int64 (__cdecl *v6)(); // rax@1
  int result; // eax@2

  v3 = lpszPath;
  v4 = pcbPathOut;
  v5 = cbPathMax;
  v6 = ODBC___GetSetupProc("SQLInstallDriverManager");
  if ( v6 )
    result = ((int (__fastcall *)(char *, _QWORD, unsigned __int16 *))v6)(v3, v5, v4);
  else
    result = 0;
  return result;
}
