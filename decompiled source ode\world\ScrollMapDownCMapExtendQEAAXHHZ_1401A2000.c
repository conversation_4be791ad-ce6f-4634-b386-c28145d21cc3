/*
 * Function: ?ScrollMapDown@CMapExtend@@QEAAXHH@Z
 * Address: 0x1401A2000
 */

void __fastcall CMapExtend::ScrollMapDown(CMapExtend *this, int nMapY, int nInterval)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  tagPOINT bottomRight; // [sp+20h] [bp-18h]@8
  tagPOINT topLeft; // [sp+28h] [bp-10h]@8
  CMapExtend *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v8->m_bExtendMode )
  {
    if ( nInterval + v8->m_ptEndMap.y <= nMapY )
    {
      v8->m_ptStartMap.y += nInterval;
      v8->m_ptEndMap.y += nInterval;
      v8->m_ptCenter.y += nInterval;
    }
    else
    {
      v8->m_ptEndMap.y = nMapY;
      v8->m_ptStartMap.y = v8->m_ptEndMap.y - v8->m_sizeExtend.cy;
      v8->m_ptCenter.y = v8->m_ptEndMap.y - v8->m_sizeExtend.cy / 2;
    }
    bottomRight = (tagPOINT)v8->m_ptEndMap;
    topLeft = (tagPOINT)v8->m_ptStartMap;
    CRect::SetRect(&v8->m_rcExtend, topLeft, bottomRight);
  }
}
