/*
 * Function: ?FindOwner@CUnmannedTraderUserInfo@@QEAAPEAVCPlayer@@XZ
 * Address: 0x140357930
 */

CPlayer *__fastcall CUnmannedTraderUserInfo::FindOwner(CUnmannedTraderUserInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  CPlayer *v5; // [sp+20h] [bp-28h]@4
  CPlayer *v6; // [sp+28h] [bp-20h]@9
  CPlayer *v7; // [sp+30h] [bp-18h]@13
  CUnmannedTraderUserInfo *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = 0i64;
  if ( *(&g_Player.m_id.dwSerial + 12714 * v8->m_wInx) == v8->m_dwUserSerial
    && *(&g_Player.m_bLive + 50856 * v8->m_wInx) )
  {
    v5 = &g_Player + v8->m_wInx;
  }
  else
  {
    v5 = GetPtrPlayerFromSerial(&g_Player, 2532, v8->m_dwUserSerial);
    if ( v5 && v5->m_bOper )
      v6 = v5;
    else
      v6 = 0i64;
    v5 = v6;
  }
  if ( v5 )
    v7 = v5;
  else
    v7 = 0i64;
  return v7;
}
