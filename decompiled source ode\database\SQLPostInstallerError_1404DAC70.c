/*
 * Function: SQLPostInstallerError
 * Address: 0x1404DAC70
 */

__int16 __fastcall SQLPostInstallerError(unsigned int dwErrorCode, const char *lpszErrMsg)
{
  unsigned int v2; // edi@1
  const char *v3; // rbx@1
  __int64 (__cdecl *v4)(); // rax@1
  __int16 result; // ax@2

  v2 = dwErrorCode;
  v3 = lpszErrMsg;
  v4 = ODBC___GetSetupProc("SQLPostInstallerError");
  if ( v4 )
    result = ((int (__fastcall *)(_QWORD, const char *))v4)(v2, v3);
  else
    result = 0;
  return result;
}
