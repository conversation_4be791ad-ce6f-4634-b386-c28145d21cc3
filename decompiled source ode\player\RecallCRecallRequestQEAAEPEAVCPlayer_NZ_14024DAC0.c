/*
 * Function: ?Recall@CRecallRequest@@QEAAEPEAVCPlayer@@_N@Z
 * Address: 0x14024DAC0
 */

char __fastcall CRecallRequest::Recall(CRecallRequest *this, CPlayer *pkDest, bool bStone)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CPlayer *v6; // rax@8
  __int64 v7; // [sp+0h] [bp-78h]@1
  float *pfStartPos; // [sp+20h] [bp-58h]@8
  CMapData *pIntoMap; // [sp+30h] [bp-48h]@8
  float pNewPos; // [sp+48h] [bp-30h]@8
  CRecallRequest *v11; // [sp+80h] [bp+8h]@1
  CPlayer *pApplyPlayer; // [sp+88h] [bp+10h]@1
  bool v13; // [sp+90h] [bp+18h]@1

  v13 = bStone;
  pApplyPlayer = pkDest;
  v11 = this;
  v3 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pkDest->m_bInGuildBattle )
  {
    result = 10;
  }
  else if ( v11->m_pkOwner->m_pCurMap )
  {
    pIntoMap = 0i64;
    pIntoMap = v11->m_pkOwner->m_pCurMap;
    CMapData::GetRandPosInRange(pIntoMap, v11->m_pkOwner->m_fCurPos, 30, &pNewPos);
    v6 = v11->m_pkOwner;
    pfStartPos = &pNewPos;
    CPlayer::OutOfMap(pApplyPlayer, pIntoMap, v6->m_wMapLayerIndex, 3, &pNewPos);
    CPlayer::SendMsg_MovePortal(pApplyPlayer, pIntoMap->m_pMapSet->m_dwIndex, &pNewPos, 2);
    if ( v13 )
    {
      CPotionMgr::InsertMovePotionStoneEffect(&g_PotionMgr, pApplyPlayer);
      CPlayer::SenseState(pApplyPlayer);
      CPlayer::SendMsg_NewMovePotionResult(pApplyPlayer);
    }
    result = 0;
  }
  else
  {
    result = 7;
  }
  return result;
}
