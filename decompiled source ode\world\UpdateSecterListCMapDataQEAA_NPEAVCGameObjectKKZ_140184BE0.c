/*
 * Function: ?UpdateSecterList@CMapData@@QEAA_NPEAVCGameObject@@KK@Z
 * Address: 0x140184BE0
 */

char __fastcall CMapData::UpdateSecterList(CMapData *this, CGameObject *pObj, unsigned int dwOldSec, unsigned int dwNewSec)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CObjectList *v6; // rax@5
  CObjectList *v7; // rax@6
  CObjectList *v8; // rax@9
  CObjectList *v9; // rax@10
  __int64 v11; // [sp+0h] [bp-48h]@1
  _object_list_point *pItem; // [sp+20h] [bp-28h]@5
  _object_list_point *v13; // [sp+28h] [bp-20h]@6
  _object_list_point *v14; // [sp+30h] [bp-18h]@9
  _object_list_point *v15; // [sp+38h] [bp-10h]@10
  CMapData *v16; // [sp+50h] [bp+8h]@1
  CGameObject *v17; // [sp+58h] [bp+10h]@1
  unsigned int dwSecIndex; // [sp+60h] [bp+18h]@1
  unsigned int v19; // [sp+68h] [bp+20h]@1

  v19 = dwNewSec;
  dwSecIndex = dwOldSec;
  v17 = pObj;
  v16 = this;
  v4 = &v11;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( dwOldSec != -1 )
  {
    pItem = &pObj->m_SectorPoint;
    v6 = CMapData::GetSectorListObj(v16, pObj->m_wMapLayerIndex, dwOldSec);
    CObjectList::DeleteItem(v6, pItem);
  }
  v13 = &v17->m_SectorPoint;
  v7 = CMapData::GetSectorListObj(v16, v17->m_wMapLayerIndex, v19);
  CObjectList::PushItem(v7, v13);
  if ( !v17->m_ObjID.m_byKind && !v17->m_ObjID.m_byID )
  {
    if ( dwSecIndex != -1 )
    {
      v14 = &v17->m_SectorNetPoint;
      v8 = CMapData::GetSectorListPlayer(v16, v17->m_wMapLayerIndex, dwSecIndex);
      CObjectList::DeleteItem(v8, v14);
    }
    v15 = &v17->m_SectorNetPoint;
    v9 = CMapData::GetSectorListPlayer(v16, v17->m_wMapLayerIndex, v19);
    CObjectList::PushItem(v9, v15);
  }
  return 1;
}
