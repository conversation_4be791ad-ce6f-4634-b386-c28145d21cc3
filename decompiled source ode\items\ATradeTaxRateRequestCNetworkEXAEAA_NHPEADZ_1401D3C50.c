/*
 * Function: ?ATradeTaxRateRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D3C50
 */

char __fastcall CNetworkEX::ATradeTaxRateRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  CUnmannedTraderTaxRateManager *v6; // rax@8
  __int64 v7; // [sp+0h] [bp-38h]@1
  CPlayer *v8; // [sp+20h] [bp-18h]@4
  int v9; // [sp+28h] [bp-10h]@8
  int na; // [sp+48h] [bp+10h]@1

  na = n;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = &g_Player + n;
  if ( !v8->m_bOper || v8->m_pmTrd.bDTradeMode || v8->m_bCorpse )
  {
    result = 1;
  }
  else
  {
    v9 = CPlayerDB::GetRaceCode(&v8->m_Param);
    v6 = CUnmannedTraderTaxRateManager::Instance();
    CUnmannedTraderTaxRateManager::SendTaxRate(v6, na, v9);
    result = 1;
  }
  return result;
}
