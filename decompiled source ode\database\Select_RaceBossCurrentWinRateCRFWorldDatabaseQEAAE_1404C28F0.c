/*
 * Function: ?Select_RaceBossCurrentWinRate@CRFWorldDatabase@@QEAAEEPEADPEAK1@Z
 * Address: 0x1404C28F0
 */

char __fastcall CRFWorldDatabase::Select_RaceBossCurrentWinRate(CRFWorldDatabase *this, char byRace, char *szDate, unsigned int *dwTotalCnt, unsigned int *dwWinCnt)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v8; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@4
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@22
  SQLLEN v11; // [sp+38h] [bp-150h]@22
  __int16 v12; // [sp+44h] [bp-144h]@9
  char _Dest[256]; // [sp+60h] [bp-128h]@4
  char v14; // [sp+164h] [bp-24h]@16
  char v15; // [sp+165h] [bp-23h]@24
  char v16; // [sp+166h] [bp-22h]@32
  unsigned __int64 v17; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v18; // [sp+190h] [bp+8h]@1
  unsigned int *TargetValue; // [sp+1A8h] [bp+20h]@1

  TargetValue = dwTotalCnt;
  v18 = this;
  v5 = &v8;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v17 = (unsigned __int64)&v8 ^ _security_cookie;
  SQLStmt = szDate;
  sprintf_s<256>(
    (char (*)[256])_Dest,
    "{ CALL pSelect_BossWinRate(%d, N'%s', '%s') }",
    (unsigned __int8)byRace,
    &unk_1799C5B99);
  if ( v18->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v18->vfptr, _Dest);
  if ( v18->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v18->vfptr) )
  {
    v12 = SQLExecDirectA_0(v18->m_hStmtSelect, _Dest, -3);
    if ( v12 && v12 != 1 )
    {
      if ( v12 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v18->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v12, _Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v12, v18->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v12 = SQLFetch_0(v18->m_hStmtSelect);
      if ( v12 && v12 != 1 )
      {
        v14 = 0;
        if ( v12 == 100 )
        {
          v14 = 2;
        }
        else
        {
          SQLStmt = v18->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v12, _Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v12, v18->m_hStmtSelect);
          v14 = 1;
        }
        if ( v18->m_hStmtSelect )
          SQLCloseCursor_0(v18->m_hStmtSelect);
        result = v14;
      }
      else
      {
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v18->m_hStmtSelect, 1u, -18, TargetValue, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          v15 = 0;
          if ( v12 == 100 )
          {
            v15 = 2;
          }
          else
          {
            SQLStmt = v18->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v12, _Dest, "SQLGetData", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v12, v18->m_hStmtSelect);
            v15 = 1;
          }
          if ( v18->m_hStmtSelect )
            SQLCloseCursor_0(v18->m_hStmtSelect);
          result = v15;
        }
        else
        {
          StrLen_or_IndPtr = &v11;
          SQLStmt = 0i64;
          v12 = SQLGetData_0(v18->m_hStmtSelect, 2u, -18, dwWinCnt, 0i64, &v11);
          if ( v12 && v12 != 1 )
          {
            v16 = 0;
            if ( v12 == 100 )
            {
              v16 = 2;
            }
            else
            {
              SQLStmt = v18->m_hStmtSelect;
              CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v12, _Dest, "SQLGetData", SQLStmt);
              CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v12, v18->m_hStmtSelect);
              v16 = 1;
            }
            if ( v18->m_hStmtSelect )
              SQLCloseCursor_0(v18->m_hStmtSelect);
            result = v16;
          }
          else
          {
            if ( v18->m_hStmtSelect )
              SQLCloseCursor_0(v18->m_hStmtSelect);
            if ( v18->m_bSaveDBLog )
              CRFNewDatabase::FmtLog((CRFNewDatabase *)&v18->vfptr, "%s Success", _Dest);
            result = 0;
          }
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v18->vfptr, "ReConnectDataBase Fail. Query : %s", _Dest);
    result = 1;
  }
  return result;
}
