/*
 * Function: ?RewardItem@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXAEAVCNormalGuildBattleLogger@2@@Z
 * Address: 0x1403E19B0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::RewardItem(GUILD_BATTLE::CNormalGuildBattleGuild *this, GUILD_BATTLE::CNormalGuildBattleLogger *kLogger)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@13
  __int64 v5; // [sp+0h] [bp-88h]@1
  char *v6; // [sp+20h] [bp-68h]@13
  int v7; // [sp+28h] [bp-60h]@13
  const char *v8; // [sp+30h] [bp-58h]@13
  GUILD_BATTLE::CGuildBattleRewardItem *v9; // [sp+40h] [bp-48h]@5
  CPlayer *pkPlayer; // [sp+48h] [bp-40h]@5
  GUILD_BATTLE::CGuildBattleRewardItemManager *v11; // [sp+50h] [bp-38h]@5
  int j; // [sp+58h] [bp-30h]@5
  const char *v13; // [sp+60h] [bp-28h]@11
  int v14; // [sp+68h] [bp-20h]@13
  char *v15; // [sp+70h] [bp-18h]@13
  GUILD_BATTLE::CNormalGuildBattleGuild *v16; // [sp+90h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattleLogger *v17; // [sp+98h] [bp+10h]@1

  v17 = kLogger;
  v16 = this;
  v2 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v16->m_pkGuild )
  {
    v9 = 0i64;
    pkPlayer = 0i64;
    v11 = GUILD_BATTLE::CGuildBattleRewardItemManager::Instance();
    for ( j = 0; j < 50; ++j )
    {
      if ( GUILD_BATTLE::CNormalGuildBattleGuildMember::IsExist(&v16->m_kMember[j]) )
      {
        pkPlayer = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(&v16->m_kMember[j]);
        if ( pkPlayer )
        {
          v9 = GUILD_BATTLE::CGuildBattleRewardItemManager::Give(v11, pkPlayer);
          if ( GUILD_BATTLE::CGuildBattleRewardItem::IsNull(v9) )
            v13 = "Fail";
          else
            v13 = "Success";
          v14 = (unsigned __int8)GUILD_BATTLE::CGuildBattleRewardItem::GetAmount(v9);
          v15 = GUILD_BATTLE::CGuildBattleRewardItem::GetItemCode(v9);
          v4 = CPlayerDB::GetCharNameW(&pkPlayer->m_Param);
          v8 = v13;
          v7 = v14;
          v6 = v15;
          GUILD_BATTLE::CNormalGuildBattleLogger::Log(
            v17,
            "CNormalGuildBattleGuild::RewardItem() : Give %s(%u) : %s(%u) %s",
            v4,
            pkPlayer->m_dwObjSerial);
        }
      }
    }
  }
}
