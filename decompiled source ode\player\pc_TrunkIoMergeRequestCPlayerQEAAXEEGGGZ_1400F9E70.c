/*
 * Function: ?pc_TrunkIoMergeRequest@CPlayer@@QEAAXEEGGG@Z
 * Address: 0x1400F9E70
 */

void __fastcall CPlayer::pc_TrunkIoMergeRequest(CPlayer *this, char byStartStorageIndex, char byTarStorageIndex, unsigned __int16 wStartItemSerial, unsigned __int16 wTarItemSerial, unsigned __int16 wMoveAmount)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  int v8; // eax@50
  int v9; // ecx@50
  int v10; // eax@52
  unsigned int v11; // eax@53
  CMoneySupplyMgr *v12; // rax@61
  unsigned int v13; // eax@65
  __int64 v14; // [sp+0h] [bp-E8h]@1
  bool bUpdate[4]; // [sp+20h] [bp-C8h]@56
  bool bSend[4]; // [sp+28h] [bp-C0h]@56
  char v17; // [sp+40h] [bp-A8h]@4
  unsigned int dwSub; // [sp+44h] [bp-A4h]@4
  void *Src; // [sp+48h] [bp-A0h]@4
  _STORAGE_LIST::_db_con *v20; // [sp+50h] [bp-98h]@4
  unsigned int v21; // [sp+58h] [bp-90h]@52
  unsigned __int64 v22; // [sp+60h] [bp-88h]@52
  _STORAGE_LIST::_db_con Dst; // [sp+78h] [bp-70h]@56
  int v24; // [sp+B4h] [bp-34h]@57
  bool v25; // [sp+B8h] [bp-30h]@62
  unsigned int dwLeftDalant; // [sp+BCh] [bp-2Ch]@66
  int nTableCode; // [sp+C0h] [bp-28h]@50
  int nLv; // [sp+C4h] [bp-24h]@61
  int v29; // [sp+C8h] [bp-20h]@61
  char *v30; // [sp+D0h] [bp-18h]@65
  CPlayer *p; // [sp+F0h] [bp+8h]@1
  char v32; // [sp+F8h] [bp+10h]@1
  char v33; // [sp+100h] [bp+18h]@1
  unsigned __int16 v34; // [sp+108h] [bp+20h]@1

  v34 = wStartItemSerial;
  v33 = byTarStorageIndex;
  v32 = byStartStorageIndex;
  p = this;
  v6 = &v14;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v17 = 0;
  dwSub = 0;
  Src = 0i64;
  v20 = 0i64;
  if ( !IsBeNearStore(p, 10) )
  {
    v17 = 13;
    goto $RESULT_87;
  }
  if ( !p->m_Param.m_bTrunkOpen )
  {
    v17 = 14;
    goto $RESULT_87;
  }
  if ( (signed int)(unsigned __int8)CPlayerDB::GetTrunkSlotNum(&p->m_Param) <= 0 )
  {
    v17 = 2;
    goto $RESULT_87;
  }
  Src = _STORAGE_LIST::GetPtrFromSerial(p->m_Param.m_pStoragePtr[(unsigned __int8)v32], v34);
  if ( !Src )
  {
    v17 = 9;
    goto $RESULT_87;
  }
  if ( *((_BYTE *)Src + 19) )
  {
    v17 = 10;
    goto $RESULT_87;
  }
  v20 = _STORAGE_LIST::GetPtrFromSerial(p->m_Param.m_pStoragePtr[(unsigned __int8)v33], wTarItemSerial);
  if ( !v20 )
  {
    v17 = 9;
    goto $RESULT_87;
  }
  if ( v20->m_bLock )
  {
    v17 = 10;
    goto $RESULT_87;
  }
  if ( !IsTrunkIOAble(*((_BYTE *)Src + 1), *(_WORD *)((char *)Src + 3)) )
  {
    v17 = 18;
    goto $RESULT_87;
  }
  if ( !IsTrunkIOAble(v20->m_byTableCode, v20->m_wItemIndex) )
  {
    v17 = 18;
    goto $RESULT_87;
  }
  if ( !IsOverLapItem(*((_BYTE *)Src + 1)) || !IsOverLapItem(v20->m_byTableCode) )
  {
    v17 = 11;
    goto $RESULT_87;
  }
  if ( *((_BYTE *)Src + 32) && v20->m_byCsMethod && *(_DWORD *)((char *)Src + 33) != v20->m_dwT )
  {
    v17 = 23;
    goto $RESULT_87;
  }
  if ( *((_BYTE *)Src + 1) != v20->m_byTableCode )
  {
    v17 = 12;
    goto $RESULT_87;
  }
  if ( *(_WORD *)((char *)Src + 3) != v20->m_wItemIndex )
  {
    v17 = 12;
    goto $RESULT_87;
  }
  if ( *(_QWORD *)((char *)Src + 5) < (unsigned __int64)wMoveAmount )
  {
    v17 = 7;
    goto $RESULT_87;
  }
  if ( v33 )
  {
    if ( v33 == 5 )
    {
      if ( v20->m_dwDur + wMoveAmount > 0x63 )
      {
        v17 = 7;
        goto $RESULT_87;
      }
    }
    else if ( v33 == 7 && v20->m_dwDur + wMoveAmount > 0x63 )
    {
      v17 = 7;
      goto $RESULT_87;
    }
  }
  else if ( v20->m_dwDur + wMoveAmount > 0x63 )
  {
    v17 = 7;
    goto $RESULT_87;
  }
  if ( v32 == 5 && v33 != 7 || v32 == 7 && v33 != 5 )
  {
    v8 = CPlayerDB::GetRaceCode(&p->m_Param);
    v9 = *(_WORD *)((char *)Src + 3);
    nTableCode = *((_BYTE *)Src + 1);
    dwSub = GetItemStoragePrice(nTableCode, v9, v8);
    if ( IsOverLapItem(*((_BYTE *)Src + 1)) )
      dwSub *= wMoveAmount;
    v10 = CPlayerDB::GetRaceCode(&p->m_Param);
    v21 = eGetTexRate(v10) + 10000;
    v22 = v21 * (unsigned __int64)dwSub;
    dwSub = v22 / 0x2710;
  }
  v11 = CPlayerDB::GetDalant(&p->m_Param);
  if ( v11 < dwSub )
    v17 = 6;
$RESULT_87:
  if ( !v17 )
  {
    _STORAGE_LIST::_db_con::_db_con(&Dst);
    memcpy_0(&Dst, Src, 0x32ui64);
    Dst.m_dwDur -= wMoveAmount;
    bSend[0] = 0;
    bUpdate[0] = 0;
    CPlayer::Emb_AlterDurPoint(p, v32, *((_BYTE *)Src + 49), -wMoveAmount, 0, 0);
    bSend[0] = 0;
    bUpdate[0] = 0;
    CPlayer::Emb_AlterDurPoint(p, v33, v20->m_byStorageIndex, wMoveAmount, 0, 0);
    if ( dwSub )
    {
      CPlayer::SubDalant(p, dwSub);
      v24 = CPlayerDB::GetLevel(&p->m_Param);
      if ( v24 == 30 || v24 == 40 || v24 == 50 || v24 == 60 )
      {
        nLv = CPlayerDB::GetLevel(&p->m_Param);
        v29 = CPlayerDB::GetRaceCode(&p->m_Param);
        v12 = CMoneySupplyMgr::Instance();
        CMoneySupplyMgr::UpdateFeeMoneyData(v12, v29, nLv, dwSub);
      }
    }
    v25 = 1;
    if ( v32 == 5 || v32 == 7 )
      v25 = 0;
    v30 = p->m_szItemHistoryFileName;
    v13 = CPlayerDB::GetDalant(&p->m_Param);
    CMgrAvatorItemHistory::trunk_io_item(&CPlayer::s_MgrItemHistory, p->m_ObjID.m_wIndex, &Dst, v25, dwSub, v13, v30);
  }
  dwLeftDalant = CPlayerDB::GetDalant(&p->m_Param);
  CPlayer::SendMsg_TrunkIoResult(p, 2, v17, dwLeftDalant, dwSub);
}
