/*
 * Function: ?Select_LimitInfo@CRFWorldDatabase@@QEAAEPEAE_K@Z
 * Address: 0x1404C8AE0
 */

char __fastcall CRFWorldDatabase::Select_LimitInfo(CRFWorldDatabase *this, char *pData, unsigned __int64 tDataSize)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-488h]@1
  char _Dest[1024]; // [sp+40h] [bp-448h]@4
  char v8; // [sp+444h] [bp-44h]@4
  int v9; // [sp+448h] [bp-40h]@8
  unsigned __int16 ColumnNumber; // [sp+454h] [bp-34h]@8
  unsigned __int64 v11; // [sp+470h] [bp-18h]@4
  CRFWorldDatabase *v12; // [sp+490h] [bp+8h]@1
  char *pDataa; // [sp+498h] [bp+10h]@1
  unsigned __int64 v14; // [sp+4A0h] [bp+18h]@1

  v14 = tDataSize;
  pDataa = pData;
  v12 = this;
  v3 = &v6;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = (unsigned __int64)&v6 ^ _security_cookie;
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0x3FFui64);
  sprintf_s<1024>((char (*)[1024])_Dest, "select [effect] from [dbo].[tbl_sf_delay] where [aserial] = 0");
  v8 = CRFNewDatabase::SQLExecDirect_RetErrCode((CRFNewDatabase *)&v12->vfptr, _Dest);
  if ( v8 )
  {
    result = v8;
  }
  else
  {
    v8 = CRFNewDatabase::SQLFetch_RetErrCode((CRFNewDatabase *)&v12->vfptr, _Dest);
    if ( v8 )
    {
      result = v8;
    }
    else
    {
      v9 = 0;
      ColumnNumber = 1;
      v8 = CRFNewDatabase::SQLGetData_Binary_RetErrCode(
             (CRFNewDatabase *)&v12->vfptr,
             _Dest,
             &ColumnNumber,
             pDataa,
             v14);
      if ( v8 )
      {
        result = v8;
      }
      else
      {
        CRFNewDatabase::SelectCleanUp((CRFNewDatabase *)&v12->vfptr, _Dest);
        result = 0;
      }
    }
  }
  return result;
}
