/*
 * Function: ?pc_ChatGmNoticeRequest@CPlayer@@QEAAXPEAD@Z
 * Address: 0x140092410
 */

void __fastcall CPlayer::pc_ChatGmNoticeRequest(CPlayer *this, char *pwszChatData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v5->m_pUserDB )
  {
    if ( v5->m_byUserDgr == 2 )
      CMainThread::pc_AllUserGMNoticeInform(&g_Main, pwszChatData);
  }
}
