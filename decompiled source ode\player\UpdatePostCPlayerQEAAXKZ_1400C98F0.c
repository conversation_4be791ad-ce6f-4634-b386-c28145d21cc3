/*
 * Function: ?UpdatePost@CPlayer@@QEAAXK@Z
 * Address: 0x1400C98F0
 */

void __fastcall CPlayer::UpdatePost(CPlayer *this, unsigned int dwIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPostSystemManager *v4; // rax@6
  int v5; // eax@7
  __int64 v6; // [sp+0h] [bp-68h]@1
  char byState; // [sp+20h] [bp-48h]@7
  int nKey; // [sp+28h] [bp-40h]@7
  unsigned __int64 dwDur; // [sp+30h] [bp-38h]@7
  unsigned int dwUpt; // [sp+38h] [bp-30h]@7
  unsigned int dwGold; // [sp+40h] [bp-28h]@7
  unsigned __int64 lnUID; // [sp+48h] [bp-20h]@7
  CPostData *v13; // [sp+50h] [bp-18h]@5
  CPlayer *v14; // [sp+70h] [bp+8h]@1
  unsigned int nIndex; // [sp+78h] [bp+10h]@1

  nIndex = dwIndex;
  v14 = this;
  v2 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( dwIndex < 0x32 )
  {
    v13 = CPostStorage::GetPostDataFromInx(&v14->m_Param.m_PostStorage, dwIndex);
    if ( v13 )
    {
      v5 = _INVENKEY::CovDBKey(&v13->m_Key);
      lnUID = v13->m_lnUID;
      dwGold = v13->m_dwGold;
      dwUpt = v13->m_dwUpt;
      dwDur = v13->m_dwDur;
      nKey = v5;
      byState = v13->m_byState;
      CUserDB::Update_Post(
        v14->m_pUserDB,
        nIndex,
        v13->m_dwPSSerial,
        v13->m_nNumber,
        byState,
        v5,
        dwDur,
        dwUpt,
        dwGold,
        lnUID);
    }
    else
    {
      v4 = CPostSystemManager::Instace();
      CPostSystemManager::Log(v4, "CPlayer::UpdatePost() : pPost Is Null : Index(%u)", nIndex);
    }
  }
}
