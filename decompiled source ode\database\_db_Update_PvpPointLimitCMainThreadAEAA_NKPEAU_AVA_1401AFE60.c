/*
 * Function: ?_db_Update_PvpPointLimit@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD1@Z
 * Address: 0x1401AFE60
 */

char __fastcall CMainThread::_db_Update_PvpPointLimit(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *szQuery, char *szError)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  int v9; // eax@7
  int v10; // ecx@7
  size_t v11; // rax@18
  __int64 v12; // [sp+0h] [bp-F8h]@1
  int v13; // [sp+20h] [bp-D8h]@7
  int v14; // [sp+28h] [bp-D0h]@7
  int v15; // [sp+30h] [bp-C8h]@7
  int v16; // [sp+38h] [bp-C0h]@7
  char Dest; // [sp+50h] [bp-A8h]@4
  char v18; // [sp+51h] [bp-A7h]@4
  size_t Size; // [sp+D4h] [bp-24h]@4
  unsigned __int64 v20; // [sp+E8h] [bp-10h]@4
  unsigned int v21; // [sp+108h] [bp+10h]@1
  _AVATOR_DATA *v22; // [sp+110h] [bp+18h]@1
  _AVATOR_DATA *v23; // [sp+118h] [bp+20h]@1

  v23 = pOldData;
  v22 = pNewData;
  v21 = dwSerial;
  v6 = &v12;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v20 = (unsigned __int64)&v12 ^ _security_cookie;
  Dest = 0;
  memset(&v18, 0, 0x7Fui64);
  sprintf(szQuery, "update [dbo].[tbl_pvppointlimitinfo] set ");
  LODWORD(Size) = strlen_0(szQuery);
  if ( v22->dbPvpPointLimit.tUpdatedate != v23->dbPvpPointLimit.tUpdatedate )
  {
    *(size_t *)((char *)&Size + 4) = (size_t)localtime_2(&v22->dbPvpPointLimit.tUpdatedate);
    if ( !*(size_t *)((char *)&Size + 4) )
    {
      sprintf(szError, "localtime( dbPvpPointLimit.tUpdatedate(%d) ) NULL!", v22->dbPvpPointLimit.tUpdatedate);
      return 0;
    }
    v9 = *(_DWORD *)(*(size_t *)((char *)&Size + 4) + 16) + 1;
    v10 = *(_DWORD *)(*(size_t *)((char *)&Size + 4) + 20);
    v16 = **(_DWORD **)((char *)&Size + 4);
    v15 = *(_DWORD *)(*(size_t *)((char *)&Size + 4) + 4);
    v14 = *(_DWORD *)(*(size_t *)((char *)&Size + 4) + 8);
    v13 = *(_DWORD *)(*(size_t *)((char *)&Size + 4) + 12);
    sprintf(&Dest, "[updatedate]='%04u-%02u-%02u %02u:%02u:%02u',", (unsigned int)(v10 + 1900), (unsigned int)v9);
    strcat_0(szQuery, &Dest);
  }
  if ( v22->dbPvpPointLimit.bUseUp != v23->dbPvpPointLimit.bUseUp )
  {
    sprintf(&Dest, "[useup]=%u,", v22->dbPvpPointLimit.bUseUp);
    strcat_0(szQuery, &Dest);
  }
  if ( v22->dbPvpPointLimit.byLimitRate != v23->dbPvpPointLimit.byLimitRate )
  {
    sprintf(&Dest, "[limitrate]=%u,", v22->dbPvpPointLimit.byLimitRate);
    strcat_0(szQuery, &Dest);
  }
  if ( v22->dbPvpPointLimit.dOriginalPoint != v23->dbPvpPointLimit.dOriginalPoint )
  {
    sprintf(&Dest, "[originalpoint]=%.10f,", v22->dbPvpPointLimit.dOriginalPoint);
    strcat_0(szQuery, &Dest);
  }
  if ( v22->dbPvpPointLimit.dLimitPoint != v23->dbPvpPointLimit.dLimitPoint )
  {
    sprintf(&Dest, "[limitpoint]=%.10f,", v22->dbPvpPointLimit.dLimitPoint);
    strcat_0(szQuery, &Dest);
  }
  if ( v22->dbPvpPointLimit.dUsePoint != v23->dbPvpPointLimit.dUsePoint )
  {
    sprintf(&Dest, "[usepoint]=%.10f ", v22->dbPvpPointLimit.dUsePoint);
    strcat_0(szQuery, &Dest);
  }
  v11 = strlen_0(szQuery);
  if ( v11 <= (unsigned int)Size )
  {
    memset_0(szQuery, 0, (unsigned int)Size);
  }
  else
  {
    sprintf(&Dest, "where serial=%d", v21);
    szQuery[strlen_0(szQuery) - 1] = 32;
    strcat_0(szQuery, &Dest);
  }
  return 1;
}
