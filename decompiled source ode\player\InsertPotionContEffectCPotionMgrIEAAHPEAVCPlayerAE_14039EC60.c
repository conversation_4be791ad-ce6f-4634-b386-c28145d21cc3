/*
 * Function: ?InsertPotionContEffect@CPotionMgr@@IEAAHPEAVCPlayer@@AEAV_ContPotionData@@PEAU_skill_fld@@K@Z
 * Address: 0x14039EC60
 */

signed __int64 __fastcall CPotionMgr::InsertPotionContEffect(CPotionMgr *this, CPlayer *pApplyPlayer, _ContPotionData *ContPotionData, _skill_fld *pEffecFld, unsigned int dwDurTime)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  unsigned int v8; // eax@19
  __int64 v9; // [sp+0h] [bp-48h]@1
  _cont_param_list *v10; // [sp+20h] [bp-28h]@7
  int j; // [sp+28h] [bp-20h]@8
  int *v12; // [sp+30h] [bp-18h]@10
  int v13; // [sp+38h] [bp-10h]@11
  CPotionMgr *v14; // [sp+50h] [bp+8h]@1
  CPlayer *pApplyPlayera; // [sp+58h] [bp+10h]@1
  _ContPotionData *ContPotionDataa; // [sp+60h] [bp+18h]@1
  _skill_fld *v17; // [sp+68h] [bp+20h]@1

  v17 = pEffecFld;
  ContPotionDataa = ContPotionData;
  pApplyPlayera = pApplyPlayer;
  v14 = this;
  v5 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( pApplyPlayer && pEffecFld )
  {
    CPotionMgr::RemovePotionContEffect(v14, pApplyPlayer, ContPotionData);
    v10 = v17->m_ContParamList;
    if ( v17 != (_skill_fld *)-936 )
    {
      for ( j = 0; j < 5; ++j )
      {
        v12 = &v10[j].m_nContParamCode;
        if ( *v12 == -1 )
          break;
        v13 = *v12;
        if ( v13 )
        {
          if ( v13 == 1 )
          {
            _effect_parameter::SetEff_Plus(&pApplyPlayera->m_EP, v12[1], *((float *)v12 + 2), 1);
          }
          else if ( v13 == 2 )
          {
            _effect_parameter::SetEff_State(&pApplyPlayera->m_EP, v12[1], 1);
          }
        }
        else
        {
          _effect_parameter::SetEff_Rate(&pApplyPlayera->m_EP, v12[1], *((float *)v12 + 2), 1);
        }
      }
      v8 = _sf_continous::GetSFContCurTime();
      _ContPotionData::Set(ContPotionDataa, v17->m_dwIndex, v8, dwDurTime);
    }
    result = 0i64;
  }
  else
  {
    result = 0xFFFFFFFFi64;
  }
  return result;
}
