/*
 * Function: ?SendMsg_MasterElectPossible@CGuild@@QEAAX_N@Z
 * Address: 0x140259D50
 */

void __fastcall CGuild::SendMsg_MasterElectPossible(CGuild *this, bool bPossible)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v7; // [sp+55h] [bp-23h]@4
  int j; // [sp+64h] [bp-14h]@4
  _guild_member_info *v9; // [sp+68h] [bp-10h]@7
  CGuild *v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  szMsg = bPossible;
  pbyType = 27;
  v7 = 126;
  for ( j = 0; j < 50; ++j )
  {
    v9 = &v10->m_MemberData[j];
    if ( _guild_member_info::IsFill(v9) )
    {
      if ( v9->pPlayer )
        CNetProcess::LoadSendMsg(unk_1414F2088, v9->pPlayer->m_ObjID.m_wIndex, &pbyType, &szMsg, 1u);
    }
  }
}
