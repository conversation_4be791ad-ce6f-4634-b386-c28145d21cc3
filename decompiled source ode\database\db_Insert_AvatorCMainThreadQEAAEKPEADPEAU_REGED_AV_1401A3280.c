/*
 * Function: ?db_Insert_Avator@CMainThread@@QEAAEKPEADPEAU_REGED_AVATOR_DB@@PEAK@Z
 * Address: 0x1401A3280
 */

char __fastcall CMainThread::db_Insert_Avator(CMainThread *this, unsigned int dwAccountSerial, char *pszAccount, _REGED_AVATOR_DB *pCharDB, unsigned int *pdwAvatorSerial)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CMapData *v8; // rax@8
  __int64 v9; // [sp+0h] [bp-5A8h]@1
  char *wszAccount; // [sp+20h] [bp-588h]@8
  char bySlotIndex; // [sp+28h] [bp-580h]@8
  char byRaceSexCode; // [sp+30h] [bp-578h]@8
  unsigned int dwBaseShape; // [sp+38h] [bp-570h]@8
  int nMapIndex; // [sp+40h] [bp-568h]@8
  unsigned int *pDwSerial; // [sp+48h] [bp-560h]@8
  char v16; // [sp+60h] [bp-548h]@4
  char v17; // [sp+61h] [bp-547h]@4
  unsigned int dwSerial; // [sp+574h] [bp-34h]@4
  unsigned __int64 v19; // [sp+590h] [bp-18h]@4
  CMainThread *v20; // [sp+5B0h] [bp+8h]@1
  unsigned int dwAccountSeriala; // [sp+5B8h] [bp+10h]@1
  char *v22; // [sp+5C0h] [bp+18h]@1
  _REGED_AVATOR_DB *pwszCharacterName; // [sp+5C8h] [bp+20h]@1

  pwszCharacterName = pCharDB;
  v22 = pszAccount;
  dwAccountSeriala = dwAccountSerial;
  v20 = this;
  v5 = &v9;
  for ( i = 360i64; i; --i )
  {
    *(_DWORD *)v5 = -*********;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v19 = (unsigned __int64)&v9 ^ _security_cookie;
  v16 = 0;
  memset(&v17, 0, 0x4FFui64);
  dwSerial = -1;
  *pdwAvatorSerial = -1;
  if ( CRFWorldDatabase::Select_Equal_Name(v20->m_pWorldDB, pCharDB->m_wszAvatorName) )
  {
    if ( CRFWorldDatabase::Select_Equal_DeleteName_NoArranged(v20->m_pWorldDB, pwszCharacterName->m_wszAvatorName) )
    {
      v8 = CMapOperation::GetStartMap(&g_MapOper, pwszCharacterName->m_byRaceSexCode >> 1);
      pDwSerial = &dwSerial;
      nMapIndex = v8->m_nMapIndex;
      dwBaseShape = pwszCharacterName->m_dwBaseShape;
      byRaceSexCode = pwszCharacterName->m_byRaceSexCode;
      bySlotIndex = pwszCharacterName->m_bySlotIndex;
      wszAccount = v22;
      if ( CRFWorldDatabase::Insert_CharacterData(
             v20->m_pWorldDB,
             pwszCharacterName->m_wszAvatorName,
             pwszCharacterName->m_szClassCode,
             dwAccountSeriala,
             v22,
             bySlotIndex,
             byRaceSexCode,
             dwBaseShape,
             nMapIndex,
             &dwSerial) )
      {
        if ( CRFWorldDatabase::Insert_MacroData(v20->m_pWorldDB, dwSerial) )
        {
          *pdwAvatorSerial = dwSerial;
          result = 0;
        }
        else
        {
          result = 24;
        }
      }
      else
      {
        result = 24;
      }
    }
    else
    {
      result = 25;
    }
  }
  else
  {
    result = 25;
  }
  return result;
}
