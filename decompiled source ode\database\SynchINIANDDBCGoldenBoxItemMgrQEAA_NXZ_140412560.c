/*
 * Function: ?SynchINIANDDB@CGoldenBoxItemMgr@@QEAA_NXZ
 * Address: 0x140412560
 */

char __fastcall CGoldenBoxItemMgr::SynchINIANDDB(CGoldenBoxItemMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v4; // [sp+0h] [bp-6C8h]@1
  int j; // [sp+20h] [bp-6A8h]@4
  int k; // [sp+24h] [bp-6A4h]@9
  int l; // [sp+28h] [bp-6A0h]@14
  int m; // [sp+2Ch] [bp-69Ch]@16
  int n; // [sp+30h] [bp-698h]@26
  int ii; // [sp+34h] [bp-694h]@29
  int *v11; // [sp+38h] [bp-690h]@31
  int jj; // [sp+40h] [bp-688h]@31
  _db_golden_box_item::_db_golden_box_item_List *v13; // [sp+48h] [bp-680h]@33
  char v14; // [sp+50h] [bp-678h]@35
  CGoldenBoxItemMgr *v15; // [sp+6D0h] [bp+8h]@1

  v15 = this;
  v1 = &v4;
  for ( i = 430i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 2; ++j )
  {
    if ( v15->m_temp_db.bygolden_item_num[j] <= 0 )
      return 0;
  }
  for ( k = 0; k < 2; ++k )
  {
    if ( v15->m_temp_db.nBoxcode[k] <= 0 )
      return 0;
  }
  for ( l = 0; l < 2; ++l )
  {
    for ( m = 0; m < v15->m_temp_db.bygolden_item_num[l]; ++m )
    {
      if ( v15->m_temp_db.List[l][m].ncode <= 0 )
        return 0;
      if ( (signed int)v15->m_temp_db.List[l][m].wcount < 0 )
        return 0;
    }
  }
  if ( v15->m_temp_db.bydck )
  {
    v15->m_golden_box_item.m_bydck = v15->m_temp_db.bydck;
    v15->m_golden_box_item.m_dwStarterBoxCnt = v15->m_temp_db.dwStarterBoxCnt;
    for ( n = 0; n < 2; ++n )
      v15->m_golden_box_item.m_wBoxMax[n] = v15->m_temp_db.wBoxMax[n];
    for ( ii = 0; ii < 2; ++ii )
    {
      v11 = &v15->m_temp_db.nBoxcode[ii];
      v15->m_golden_box_item.m_byBoxTableCode[ii] = BYTE1(v15->m_temp_db.nBoxcode[ii]);
      v15->m_golden_box_item.m_dwBoxIndex[ii] = *((_WORD *)v11 + 1);
      v15->m_golden_box_item.m_wBoxMax[ii] = v15->m_temp_db.wBoxMax[ii];
      v15->m_golden_box_item.m_bygolden_item_num[ii] = v15->m_temp_db.bygolden_item_num[ii];
      for ( jj = 0; jj < v15->m_temp_db.bygolden_item_num[ii]; ++jj )
      {
        v13 = &v15->m_temp_db.List[ii][jj];
        v15->m_golden_box_item.m_golden_box_item_info[ii][jj].m_byTableCode = BYTE1(v13->ncode);
        v15->m_golden_box_item.m_golden_box_item_info[ii][jj].m_dwIndex = HIWORD(v13->ncode);
        v15->m_golden_box_item.m_golden_box_item_info[ii][jj].m_wNum = v15->m_temp_db.List[ii][jj].wcount;
      }
    }
    CGoldenBoxItemMgr::Set_ToStruct(v15);
    qmemcpy(&v14, &v15->m_golden_box_item_New, 0x658ui64);
    qmemcpy(&v15->m_golden_box_item_Old, &v14, sizeof(v15->m_golden_box_item_Old));
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
