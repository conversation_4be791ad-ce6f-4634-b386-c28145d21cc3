/*
 * Function: ?Reward_DarkDungeon@CPlayer@@QEAAXPEAV_dh_reward_sub_setup@@PEADHPEAU_db_con@_STORAGE_LIST@@AEAH@Z
 * Address: 0x1400CDC00
 */

void __usercall CPlayer::Reward_DarkDungeon(CPlayer *this@<rcx>, _dh_reward_sub_setup *pSetup@<rdx>, char *pszTitle@<r8>, int b<PERSON><PERSON><PERSON><PERSON>@<r9d>, signed __int64 a5@<rax>, double a6@<xmm0>, _STORAGE_LIST::_db_con *pItem, int *bIsRewarded)
{
  void *v8; // rsp@1
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  int v11; // eax@6
  unsigned int v12; // eax@19
  CMoneySupplyMgr *v13; // rax@23
  __int64 v14; // [sp-20h] [bp-1C08h]@1
  bool bAdd[8]; // [sp+0h] [bp-1BE8h]@23
  CMapData *pMap; // [sp+10h] [bp-1BD8h]@13
  unsigned __int16 wLayerIndex[4]; // [sp+18h] [bp-1BD0h]@13
  float *pStdPos; // [sp+20h] [bp-1BC8h]@13
  bool bHide; // [sp+28h] [bp-1BC0h]@13
  char __t[6408]; // [sp+40h] [bp-1BA8h]@4
  double v21; // [sp+1948h] [bp-2A0h]@4
  int v22; // [sp+1950h] [bp-298h]@4
  _STORAGE_LIST::_db_con Dst; // [sp+1968h] [bp-280h]@6
  _TimeItem_fld *v24; // [sp+19A8h] [bp-240h]@7
  __time32_t Time; // [sp+19B4h] [bp-234h]@9
  char Dest; // [sp+19E0h] [bp-208h]@12
  char pszClause; // [sp+1A80h] [bp-168h]@13
  unsigned int dwAddDalant; // [sp+1B04h] [bp-E4h]@19
  char v29; // [sp+1B20h] [bp-C8h]@19
  int v30; // [sp+1BA4h] [bp-44h]@19
  char *pszFileName; // [sp+1BB0h] [bp-38h]@19
  unsigned int dwSumGold; // [sp+1BB8h] [bp-30h]@19
  char *szClass; // [sp+1BC0h] [bp-28h]@23
  int nLv; // [sp+1BC8h] [bp-20h]@23
  int v35; // [sp+1BCCh] [bp-1Ch]@23
  unsigned __int64 v36; // [sp+1BD0h] [bp-18h]@4
  CPlayer *pOwner; // [sp+1BF0h] [bp+8h]@1
  _dh_reward_sub_setup *pSetupa; // [sp+1BF8h] [bp+10h]@1
  char *v39; // [sp+1C00h] [bp+18h]@1
  int bRealBossa; // [sp+1C08h] [bp+20h]@1

  bRealBossa = bRealBoss;
  v39 = pszTitle;
  pSetupa = pSetup;
  pOwner = this;
  v8 = alloca(a5);
  v9 = &v14;
  for ( i = 1792i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v36 = (unsigned __int64)&v14 ^ _security_cookie;
  `vector constructor iterator'(__t, 0x32ui64, 128, (void *(__cdecl *)(void *))_STORAGE_LIST::_db_con::_db_con);
  memset_0(__t, 0, 0x1900ui64);
  TimeLimitMgr::GetPlayerPenalty(qword_1799CA2D0, pOwner->m_id.wIndex);
  v21 = a6;
  v22 = CPlayer::GetRewardItems_DarkDungeon(pOwner, pSetupa, (_STORAGE_LIST::_db_con *)__t, bRealBossa);
  if ( v22 > 0 && TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, pOwner->m_id.wIndex) != 99 )
  {
    _STORAGE_LIST::_db_con::_db_con(&Dst);
    v11 = rand();
    memcpy_0(&Dst, &__t[50 * (v11 % v22)], 0x32ui64);
    if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&pOwner->m_Param.m_dbInven.m_nListNum) == 255 )
    {
      bHide = 0;
      pStdPos = pOwner->m_fCurPos;
      wLayerIndex[0] = pOwner->m_wMapLayerIndex;
      pMap = pOwner->m_pCurMap;
      CreateItemBox(&Dst, pOwner, 0xFFFFFFFF, 0, 0i64, 3, pMap, wLayerIndex[0], pOwner->m_fCurPos, 0);
      sprintf(&pszClause, "Quest G (%s)", v39);
      CMgrAvatorItemHistory::reward_add_item(
        &CPlayer::s_MgrItemHistory,
        pOwner->m_ObjID.m_wIndex,
        &pszClause,
        &Dst,
        pOwner->m_szItemHistoryFileName);
    }
    else
    {
      Dst.m_wSerial = CPlayerDB::GetNewItemSerial(&pOwner->m_Param);
      v24 = TimeItem::FindTimeRec((unsigned __int8)Dst.m_byTableCode, Dst.m_wItemIndex);
      if ( v24 && v24->m_nCheckType )
      {
        _time32(&Time);
        Dst.m_byCsMethod = v24->m_nCheckType;
        Dst.m_dwT = v24->m_nUseTime + Time;
        Dst.m_dwLendRegdTime = Time;
      }
      if ( !CPlayer::Emb_AddStorage(pOwner, 0, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 0, 1) )
        CMgrAvatorItemHistory::add_storage_fail(
          &CPlayer::s_MgrItemHistory,
          pOwner->m_ObjID.m_wIndex,
          &Dst,
          "CPlayer::Reward_DarkDungeon - Emb_AddStorage() Fail",
          pOwner->m_szItemHistoryFileName);
      CPlayer::SendMsg_RewardAddItem(pOwner, &Dst, 2);
      sprintf(&Dest, "Quest (%s)", v39);
      CMgrAvatorItemHistory::reward_add_item(
        &CPlayer::s_MgrItemHistory,
        pOwner->m_ObjID.m_wIndex,
        &Dest,
        &Dst,
        pOwner->m_szItemHistoryFileName);
    }
    CPlayer::SendMsg_FanfareItem(pOwner, 4, &Dst, 0i64);
    if ( pItem )
    {
      *bIsRewarded = 1;
      pItem->m_byTableCode = Dst.m_byTableCode;
      pItem->m_wItemIndex = Dst.m_wItemIndex;
    }
  }
  if ( v22 <= 0 )
    *bIsRewarded = 0;
  if ( pSetupa->dwDalant )
  {
    CPlayer::AddDalant(pOwner, pSetupa->dwDalant, 1);
    CPlayer::SendMsg_ExchangeMoneyResult(pOwner, 0);
    dwAddDalant = (signed int)floor((double)(signed int)pSetupa->dwDalant * v21);
    sprintf(&v29, "Quest (%s)", v39);
    pszFileName = pOwner->m_szItemHistoryFileName;
    dwSumGold = CPlayerDB::GetGold(&pOwner->m_Param);
    v12 = CPlayerDB::GetDalant(&pOwner->m_Param);
    CMgrAvatorItemHistory::reward_add_money(
      &CPlayer::s_MgrItemHistory,
      pOwner->m_ObjID.m_wIndex,
      &v29,
      dwAddDalant,
      0,
      v12,
      dwSumGold,
      pszFileName);
    v30 = CPlayerDB::GetLevel(&pOwner->m_Param);
    if ( v30 == 30 || v30 == 40 || v30 == 50 || v30 == 60 )
    {
      szClass = CPlayerDB::GetPtrCurClass(&pOwner->m_Param)->m_strCode;
      nLv = CPlayerDB::GetLevel(&pOwner->m_Param);
      v35 = CPlayerDB::GetRaceCode(&pOwner->m_Param);
      v13 = CMoneySupplyMgr::Instance();
      *(_DWORD *)bAdd = pSetupa->dwDalant;
      CMoneySupplyMgr::UpdateGateRewardMoneyData(v13, v35, nLv, szClass, *(unsigned int *)bAdd);
    }
  }
  if ( pSetupa->dExp > 0.0 )
  {
    bAdd[0] = 1;
    CPlayer::AlterExp(pOwner, pSetupa->dExp, 1, 0, 1);
  }
  if ( pSetupa->dwPvp )
    CPlayer::AlterPvPPoint(pOwner, (double)(signed int)pSetupa->dwPvp, quest_inc, 0xFFFFFFFF);
}
