/*
 * Function: ?TotalPlayMinCheck@CUserDB@@QEAAXXZ
 * Address: 0x140113AC0
 */

void __fastcall CUserDB::TotalPlayMinCheck(CUserDB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int dwTotalTimeMin; // [sp+20h] [bp-18h]@6
  CUserDB *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5->m_dwSerial != -1 && CMyTimer::CountingTimer(&v5->m_tmrCheckPlayMin) )
  {
    dwTotalTimeMin = v5->m_AvatorData.dbAvator.m_dwTotalPlayMin + 1;
    CUserDB::Update_PlayTime(v5, dwTotalTimeMin);
  }
}
