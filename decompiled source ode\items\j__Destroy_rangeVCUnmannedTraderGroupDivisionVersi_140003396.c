/*
 * Function: j_??$_Destroy_range@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@YAXPEAVCUnmannedTraderGroupDivisionVersionInfo@@0AEAV?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@0@@Z
 * Address: 0x140003396
 */

void __fastcall std::_Destroy_range<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(CUnmannedTraderGroupDivisionVersionInfo *_First, CUnmannedTraderGroupDivisionVersionInfo *_Last, std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *_Al)
{
  std::_Destroy_range<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(
    _First,
    _Last,
    _Al);
}
