/*
 * Function: ?insert@AP_BatterySlot@@QEAAHPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1402D9D70
 */

signed __int64 __fastcall AP_BatterySlot::insert(AP_BatterySlot *this, _STORAGE_LIST::_db_con *pItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  AP_BatterySlot *v6; // [sp+30h] [bp+8h]@1
  _STORAGE_LIST::_db_con *pItema; // [sp+38h] [bp+10h]@1

  pItema = pItem;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v6->m_bFill )
  {
    result = 0xFFFFFFFFi64;
  }
  else if ( AP_BatterySlot::is_private_item(v6, pItem) )
  {
    if ( pItema->m_dwDur )
    {
      v6->m_bFill = 1;
      v6->battery_.m_bLoad = 1;
      memcpy_0(&v6->battery_, pItema, 0x32ui64);
      result = 0i64;
    }
    else
    {
      result = 4294967293i64;
    }
  }
  else
  {
    result = 4294967294i64;
  }
  return result;
}
