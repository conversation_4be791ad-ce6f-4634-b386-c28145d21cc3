/*
 * Function: ?ExecUpdateBinaryQuery@CRFNewDatabase@@QEAA_NPEADPEAXH_N@Z
 * Address: 0x140488250
 */

char __fastcall CRFNewDatabase::ExecUpdateBinaryQuery(CRFNewDatabase *this, char *strQuery, void *buf, int size, bool bNoDataError)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v8; // [sp+0h] [bp-4D8h]@1
  SQLSMALLINT fSqlType[4]; // [sp+20h] [bp-4B8h]@9
  SQLULEN cbColDef; // [sp+28h] [bp-4B0h]@9
  SQLSMALLINT ibScale; // [sp+30h] [bp-4A8h]@9
  SQLPOINTER rgbValue; // [sp+38h] [bp-4A0h]@9
  SQLLEN cbValueMax; // [sp+40h] [bp-498h]@9
  SQLLEN *pcbValue; // [sp+48h] [bp-490h]@9
  __int16 v15; // [sp+50h] [bp-488h]@9
  __int16 v16; // [sp+54h] [bp-484h]@23
  SQLLEN v17; // [sp+68h] [bp-470h]@9
  char Dst; // [sp+90h] [bp-448h]@23
  void *Src; // [sp+498h] [bp-40h]@18
  SQLPOINTER Value; // [sp+4A8h] [bp-30h]@16
  int v21; // [sp+4B4h] [bp-24h]@23
  __int64 v22; // [sp+4C0h] [bp-18h]@21
  unsigned __int64 v23; // [sp+4C8h] [bp-10h]@4
  CRFNewDatabase *v24; // [sp+4E0h] [bp+8h]@1
  char *StatementText; // [sp+4E8h] [bp+10h]@1
  void *v26; // [sp+4F0h] [bp+18h]@1
  int v27; // [sp+4F8h] [bp+20h]@1

  v27 = size;
  v26 = buf;
  StatementText = strQuery;
  v24 = this;
  v5 = &v8;
  for ( i = 308i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v23 = (unsigned __int64)&v8 ^ _security_cookie;
  if ( v24->m_bSaveDBLog )
    CRFNewDatabase::FmtLog(v24, "Query : %s", strQuery);
  if ( v24->m_hStmtUpdate || CRFNewDatabase::ReConnectDataBase(v24) )
  {
    v17 = -v27 - 100;
    pcbValue = &v17;
    cbValueMax = 0i64;
    rgbValue = (SQLPOINTER)1;
    ibScale = 0;
    cbColDef = v27;
    fSqlType[0] = -4;
    v15 = SQLBindParameter_0(v24->m_hStmtUpdate, 1u, 1, -2, -4, v27, 0, (SQLPOINTER)1, 0i64, &v17);
    v15 = SQLExecDirect_0(v24->m_hStmtUpdate, StatementText, -3);
    if ( v15 != -1 && v15 != 100 )
    {
      while ( v15 == 99 )
      {
        v15 = SQLParamData_0(v24->m_hStmtUpdate, &Value);
        if ( v15 == 99 && (_DWORD)Value == 1 )
        {
          for ( Src = v26; Src < (char *)v26 + v27; Src = (char *)Src + 1024 )
          {
            if ( (_BYTE *)v26 + v27 - (_BYTE *)Src <= 1024 )
              v22 = (_BYTE *)v26 + v27 - (_BYTE *)Src;
            else
              v22 = 1024i64;
            v21 = v22;
            memcpy_0(&Dst, Src, (signed int)v22);
            v16 = SQLPutData_0(v24->m_hStmtUpdate, &Dst, v21);
            if ( v16 == -1 || v16 == -2 )
            {
              *(_QWORD *)fSqlType = v24->m_hStmtUpdate;
              CRFNewDatabase::ErrorMsgLog(v24, v15, StatementText, "SQLExecDirect", *(void **)fSqlType);
              CRFNewDatabase::ErrorAction(v24, v15, v24->m_hStmtUpdate);
              return 0;
            }
          }
        }
      }
      if ( v24->m_bSaveDBLog )
        CRFNewDatabase::FmtLog(v24, "ExecUpdateQuery : %s Query Success", StatementText);
      result = 1;
    }
    else if ( v15 == -1 || bNoDataError )
    {
      *(_QWORD *)fSqlType = v24->m_hStmtUpdate;
      CRFNewDatabase::ErrorMsgLog(v24, v15, StatementText, "SQLExecDirect", *(void **)fSqlType);
      CRFNewDatabase::ErrorAction(v24, v15, v24->m_hStmtUpdate);
      result = 0;
    }
    else
    {
      result = 1;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog(v24, "ReConnectDataBase Fail. Query : %s", StatementText);
    result = 0;
  }
  return result;
}
