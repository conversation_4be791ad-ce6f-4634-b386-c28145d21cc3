/*
 * Function: ?CascadeExponentiateBaseAndPublicElement@?$DL_PublicKey@UECPPoint@CryptoPP@@@CryptoPP@@UEBA?AUECPPoint@2@AEBVInteger@2@0@Z
 * Address: 0x140451380
 */

CryptoPP::ECPPoint *__fastcall CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>::CascadeExponentiateBaseAndPublicElement(CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> *this, CryptoPP::ECPPoint *result, CryptoPP::Integer *baseExp, CryptoPP::Integer *publicExp)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // rax@4
  __int64 *v7; // rax@4
  __int64 v8; // rax@4
  __int64 v9; // rax@4
  __int64 v11; // [sp+0h] [bp-68h]@1
  __int64 v12; // [sp+20h] [bp-48h]@4
  CryptoPP::Integer *v13; // [sp+28h] [bp-40h]@4
  __int64 *v14; // [sp+30h] [bp-38h]@4
  int v15; // [sp+38h] [bp-30h]@4
  __int64 *v16; // [sp+40h] [bp-28h]@4
  __int64 v17; // [sp+48h] [bp-20h]@4
  __int64 v18; // [sp+50h] [bp-18h]@4
  __int64 v19; // [sp+58h] [bp-10h]@4
  CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> *v20; // [sp+70h] [bp+8h]@1
  CryptoPP::ECPPoint *v21; // [sp+78h] [bp+10h]@1
  CryptoPP::Integer *v22; // [sp+80h] [bp+18h]@1
  CryptoPP::Integer *v23; // [sp+88h] [bp+20h]@1

  v23 = publicExp;
  v22 = baseExp;
  v21 = result;
  v20 = this;
  v4 = &v11;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v15 = 0;
  LODWORD(v6) = ((int (__fastcall *)(CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> *))v20->vfptr->GetAbstractGroupParameters)(v20);
  v14 = (__int64 *)v6;
  LODWORD(v7) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v6 + 48i64))(v6);
  v16 = v7;
  LODWORD(v8) = ((int (__fastcall *)(CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> *))v20->vfptr[3].GetAbstractGroupParameters)(v20);
  v17 = v8;
  v18 = *v14;
  LODWORD(v9) = (*(int (__fastcall **)(__int64 *))(v18 + 40))(v14);
  v19 = *v16;
  v13 = v23;
  v12 = v17;
  (*(void (__fastcall **)(__int64 *, CryptoPP::ECPPoint *, __int64, CryptoPP::Integer *))(v19 + 56))(v16, v21, v9, v22);
  return v21;
}
