/*
 * Function: ?Cont_UserSave_Complete@CMainThread@@QEAAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1401F3C90
 */

void __fastcall CMainThread::Cont_UserSave_Complete(CMainThread *this, _DB_QRY_SYN_DATA *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // rax@22
  unsigned int v5; // edx@26
  __int64 v6; // [sp+0h] [bp-128h]@1
  char *pszLog; // [sp+20h] [bp-108h]@26
  char *v8; // [sp+28h] [bp-100h]@26
  unsigned int v9; // [sp+30h] [bp-F8h]@26
  CUserDB *v10; // [sp+40h] [bp-E8h]@4
  char *v11; // [sp+48h] [bp-E0h]@7
  CPlayer *v12; // [sp+50h] [bp-D8h]@8
  CPostData *v13; // [sp+58h] [bp-D0h]@13
  int nIndex; // [sp+60h] [bp-C8h]@10
  char *v15; // [sp+68h] [bp-C0h]@13
  char Dest; // [sp+80h] [bp-A8h]@26
  unsigned __int64 v17; // [sp+110h] [bp-18h]@4
  CMainThread *v18; // [sp+130h] [bp+8h]@1
  _DB_QRY_SYN_DATA *v19; // [sp+138h] [bp+10h]@1

  v19 = pData;
  v18 = this;
  v2 = &v6;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v17 = (unsigned __int64)&v6 ^ _security_cookie;
  v10 = &g_UserDB[pData->m_idWorld.wIndex];
  if ( v10->m_bActive && v10->m_idWorld.dwSerial == pData->m_idWorld.dwSerial )
  {
    v11 = pData->m_sData;
    if ( pData->m_sData[34814] )
    {
      v12 = GetPtrPlayerFromSerial(&g_Player, 2532, pData->m_dwAccountSerial);
      if ( v12 )
      {
        if ( v12->m_bOper )
        {
          for ( nIndex = 0; nIndex < 50; ++nIndex )
          {
            v13 = 0i64;
            v15 = &v11[297 * nIndex + 19964];
            if ( v11[297 * nIndex + 19993] )
            {
              if ( v15[31] )
              {
                if ( v15[30] )
                {
                  v13 = CPostStorage::GetPostDataFromInx(&v12->m_Param.m_PostStorage, nIndex);
                  if ( v13 )
                  {
                    if ( !v10->m_AvatorData.dbPostData.dbPost.m_PostList[nIndex].bNew )
                      v13->m_dwPSSerial = *(_DWORD *)v15;
                  }
                }
              }
            }
          }
        }
      }
    }
    if ( v10->m_AvatorData.m_byHSKTime <= 2 )
    {
      v4 = v10->m_AvatorData.m_byHSKTime;
      if ( v10->m_AvatorData.m_byCristalBattleDBInfo != 3 && !v10->m_AvatorData.m_bCristalBattleDateUpdate )
      {
        v10->m_AvatorData.m_bCristalBattleDateUpdate = 1;
        v10->m_AvatorData_bk.m_bCristalBattleDateUpdate = 1;
      }
    }
    _CUTTING_DB_BASE::ReSetOldDataLoad(&v10->m_AvatorData.dbCutting);
    if ( v19->m_byResult )
    {
      v5 = v19->m_byResult;
      v9 = v10->m_dwSerial;
      v8 = v10->m_aszAvatorName;
      LODWORD(pszLog) = v10->m_dwAccountSerial;
      sprintf(&Dest, "Cont_UserSave_Complete Database Error(%d) account(%s , %d), char(%s, %d)", v5, v10->m_szAccountID);
      CNetworkEX::Close(&g_Network, 0, v19->m_idWorld.wIndex, 0, &Dest);
    }
    else
    {
      CUserDB::Cont_UserSave_Complete(v10, v19->m_byResult, (_AVATOR_DATA *)(v11 + 4));
      if ( v11[74434] )
        CMainThread::_db_complete_update_event_classrefine(v18, v19->m_idWorld.wIndex, v19->m_idWorld.dwSerial);
    }
  }
}
