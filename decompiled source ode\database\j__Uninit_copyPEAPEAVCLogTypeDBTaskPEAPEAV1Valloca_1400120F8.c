/*
 * Function: j_??$_Uninit_copy@PEAPEAVCLogTypeDBTask@@PEAPEAV1@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@YAPEAPEAVCLogTypeDBTask@@PEAPEAV1@00AEAV?$allocator@PEAVCLogTypeDBTask@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400120F8
 */

CLogTypeDBTask **__fastcall std::_Uninit_copy<CLogTypeDBTask * *,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>>(CLogTypeDBTask **_First, CLogTypeDBTask **_Last, CLogTypeDBTask **_Dest, std::allocator<CLogTypeDBTask *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CLogTypeDBTask * *,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
