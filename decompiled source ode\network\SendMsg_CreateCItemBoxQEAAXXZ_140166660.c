/*
 * Function: ?SendMsg_Create@CItemBox@@QEAAXXZ
 * Address: 0x140166660
 */

void __fastcall CItemBox::SendMsg_Create(CItemBox *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-A8h]@1
  char szMsg; // [sp+38h] [bp-70h]@4
  __int16 v5; // [sp+39h] [bp-6Fh]@4
  char v6; // [sp+3Bh] [bp-6Dh]@4
  unsigned __int16 v7; // [sp+3Ch] [bp-6Ch]@4
  unsigned int v8; // [sp+3Eh] [bp-6Ah]@4
  char v9; // [sp+42h] [bp-66h]@4
  unsigned __int16 v10; // [sp+43h] [bp-65h]@4
  unsigned int v11; // [sp+45h] [bp-63h]@4
  char v12; // [sp+49h] [bp-5Fh]@4
  __int16 pShort; // [sp+4Ah] [bp-5Eh]@4
  char v14; // [sp+50h] [bp-58h]@4
  char pbyType; // [sp+74h] [bp-34h]@4
  char v16; // [sp+75h] [bp-33h]@4
  unsigned __int64 v17; // [sp+90h] [bp-18h]@4
  CItemBox *v18; // [sp+B0h] [bp+8h]@1

  v18 = this;
  v1 = &v3;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v17 = (unsigned __int64)&v3 ^ _security_cookie;
  szMsg = v18->m_Item.m_byTableCode;
  v5 = v18->m_pRecordSet->m_dwIndex;
  v6 = v18->m_Item.m_dwDur;
  v7 = v18->m_ObjID.m_wIndex;
  v12 = v18->m_nStateCode;
  v8 = v18->m_dwOwnerSerial;
  v9 = v18->m_byThrowerID;
  v10 = v18->m_wThrowerIndex;
  v11 = v18->m_dwThrowerSerial;
  v14 = v18->m_byThrowerRaceCode;
  FloatToShort(v18->m_fCurPos, &pShort, 3);
  pbyType = 3;
  v16 = 20;
  if ( v18->m_bHide )
  {
    if ( v18->m_wOwnerIndex != 0xFFFF )
      CNetProcess::LoadSendMsg(unk_1414F2088, v18->m_wOwnerIndex, &pbyType, &szMsg, 0x19u);
  }
  else
  {
    CGameObject::CircleReport((CGameObject *)&v18->vfptr, &pbyType, &szMsg, 25, 0);
  }
}
