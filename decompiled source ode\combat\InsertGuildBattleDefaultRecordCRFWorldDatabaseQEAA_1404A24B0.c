/*
 * Function: ?InsertGuildBattleDefaultRecord@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x1404A24B0
 */

char __fastcall CRFWorldDatabase::InsertGuildBattleDefaultRecord(CRFWorldDatabase *this, unsigned int dwRowCnt)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-458h]@1
  char Dest; // [sp+30h] [bp-428h]@10
  unsigned int j; // [sp+434h] [bp-24h]@8
  unsigned __int64 v8; // [sp+440h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+460h] [bp+8h]@1
  unsigned int v10; // [sp+468h] [bp+10h]@1

  v10 = dwRowCnt;
  v9 = this;
  v2 = &v5;
  for ( i = 276i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( dwRowCnt )
  {
    if ( CRFWorldDatabase::DeleteGuildBattleInfo(v9) )
    {
      for ( j = 0; j < v10; ++j )
      {
        sprintf(&Dest, "{ CALL pInsert_GuildBattleInfo( %u, 0, 0, 0, 0 ) }", j);
        if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &Dest, 1) )
        {
          CRFWorldDatabase::DeleteGuildBattleInfo(v9);
          return 0;
        }
      }
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
