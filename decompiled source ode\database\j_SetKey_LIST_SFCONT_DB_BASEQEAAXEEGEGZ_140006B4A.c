/*
 * Function: j_?<PERSON><PERSON><PERSON>@_LIST@_SFCONT_DB_BASE@@QEAAXEEGEG@Z
 * Address: 0x140006B4A
 */

void __fastcall _SFCONT_DB_BASE::_LIST::SetKey(_SFCONT_DB_BASE::_LIST *this, char pl_byOrder, char pl_byEffectCode, unsigned __int16 pl_wEffectIndex, char pl_byLv, unsigned __int16 pl_wLeftTime)
{
  _SFCONT_DB_BASE::_LIST::SetKey(this, pl_byOrder, pl_byEffectCode, pl_wEffectIndex, pl_byLv, pl_wLeftTime);
}
