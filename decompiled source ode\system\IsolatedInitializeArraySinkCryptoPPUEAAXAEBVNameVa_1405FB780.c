/*
 * Function: ?IsolatedInitialize@ArraySink@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x1405FB780
 */

void __fastcall CryptoPP::ArraySink::IsolatedInitialize(CryptoPP::ArraySink *this, const struct CryptoPP::NameValuePairs *a2)
{
  CryptoPP::Name *v2; // rcx@1
  const char *v3; // rax@1
  CryptoPP::ByteArrayParameter v4; // [sp+20h] [bp-A8h]@1
  CryptoPP::InvalidArgument v5; // [sp+30h] [bp-98h]@2
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+80h] [bp-48h]@2
  unsigned __int8 v7; // [sp+B0h] [bp-18h]@2
  __int64 v8; // [sp+B8h] [bp-10h]@1
  CryptoPP::ArraySink *v9; // [sp+D0h] [bp+8h]@1
  const struct CryptoPP::NameValuePairs *v10; // [sp+D8h] [bp+10h]@1

  v10 = a2;
  v9 = this;
  v8 = -2i64;
  CryptoPP::ByteArrayParameter::ByteArrayParameter(&v4, 0i64, 0);
  v3 = CryptoPP::Name::OutputBuffer(v2);
  if ( !(unsigned __int8)CryptoPP::NameValuePairs::GetValue<CryptoPP::ByteArrayParameter>(v10, v3, &v4) )
  {
    memset(&v7, 0, sizeof(v7));
    std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
      &s,
      "ArraySink: missing OutputBuffer argument",
      v7);
    CryptoPP::InvalidArgument::InvalidArgument(&v5, &s);
    CxxThrowException_0((__int64)&v5, (__int64)&TI3_AVInvalidArgument_CryptoPP__);
  }
  v9->m_buf = (char *)CryptoPP::ByteArrayParameter::begin(&v4);
  v9->m_size = CryptoPP::ByteArrayParameter::size(&v4);
  v9->m_total = 0i64;
}
