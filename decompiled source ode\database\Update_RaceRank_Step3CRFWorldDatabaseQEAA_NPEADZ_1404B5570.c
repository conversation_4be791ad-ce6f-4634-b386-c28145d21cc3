/*
 * Function: ?Update_RaceRank_Step3@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404B5570
 */

char __fastcall CRFWorldDatabase::Update_RaceRank_Step3(CRFWorldDatabase *this, char *szDate)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-478h]@1
  void *SQLStmt; // [sp+20h] [bp-458h]@11
  __int16 v7; // [sp+30h] [bp-448h]@7
  char DstBuf; // [sp+50h] [bp-428h]@4
  char v9; // [sp+51h] [bp-427h]@4
  unsigned int v10; // [sp+454h] [bp-24h]@7
  unsigned __int64 v11; // [sp+460h] [bp-18h]@4
  CRFWorldDatabase *v12; // [sp+480h] [bp+8h]@1
  char *szDatea; // [sp+488h] [bp+10h]@1

  szDatea = szDate;
  v12 = this;
  v2 = &v5;
  for ( i = 284i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = (unsigned __int64)&v5 ^ _security_cookie;
  DstBuf = 0;
  memset(&v9, 0, 0x3FFui64);
  if ( !v12->m_hStmtUpdate && !CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v12->vfptr) )
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v12->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    return 0;
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v12->vfptr,
    "CRFWorldDatabase::Update_RaceRank_Step2(szDate(%s)) : Start Create #tbl_PvpRankC Table",
    szDatea);
  CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v12->vfptr, 0);
  v10 = GetConnectTime_AddBySec(-2592000);
  sprintf_s(
    &DstBuf,
    0x400ui64,
    "select IDENTITY(int, 1, 1) AS Rank, -1 as Rate, tbl_base.serial, 2 as Race,  tbl_base.name, tbl_base.lv, tbl_general"
    ".PvpPoint, tbl_general.GuildSerial into #tbl_PvpRankA from tbl_general, tbl_base where tbl_base.serial=tbl_general.s"
    "erial and tbl_base.dck=0 and tbl_base.AccountSerial<********* and tbl_general.class0 <> -1 and tbl_base.race in (4) "
    "and tbl_base.LastConnTime > %d order by tbl_general.PvpPoint desc",
    v10);
  v7 = SQLExecDirectA_0(v12->m_hStmtUpdate, &DstBuf, -3);
  if ( v7 && v7 != 1 )
  {
    if ( v7 != 100 )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v12->vfptr,
        "CRFWorldDatabase::Update_RaceRank_Step2(szDate(%s)) : Create #tbl_PvpRankB Table Fail SQL_ERROR!",
        szDatea);
      SQLStmt = v12->m_hStmtUpdate;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v12->vfptr, v7, &DstBuf, "SQLExecDirect", SQLStmt);
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v12->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v12->vfptr, 1);
      if ( !CRFWorldDatabase::Update_RaceRank_Step_6_1(v12, szDatea) )
        CRFWorldDatabase::Update_RaceRank_Step_6_1(v12, szDatea);
      if ( !CRFWorldDatabase::Update_RaceRank_Step_6_2(v12, szDatea) )
        CRFWorldDatabase::Update_RaceRank_Step_6_2(v12, szDatea);
      return 0;
    }
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v12->vfptr,
      "CRFWorldDatabase::Update_RaceRank_Step2(szDate(%s)) : Create #tbl_PvpRankB Table Fail NO_DATA!",
      szDatea);
  }
  CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v12->vfptr, 1);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v12->vfptr,
    "CRFWorldDatabase::Update_RaceRank_Step3(szDate(%s)) : End Create #tbl_PvpRankA Table",
    szDatea);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v12->vfptr,
    "CRFWorldDatabase::Update_RaceRank_Step3(szDate(%s)) : Start Set Rate #tbl_PvpRankA Table",
    szDatea);
  sprintf(&DstBuf, "update #tbl_PvpRankA set Rate = ( (Rank*10000)/(select count(*) from #tbl_PvpRankA) )");
  v7 = SQLExecDirectA_0(v12->m_hStmtUpdate, &DstBuf, -3);
  if ( !v7 || v7 == 1 )
    goto LABEL_25;
  if ( v7 == 100 )
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v12->vfptr,
      "CRFWorldDatabase::Update_RaceRank_Step1(szDate(%s)) : Set Rate #tbl_PvpRankA Table Fail NO_DATA!",
      szDatea);
LABEL_25:
    CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&v12->vfptr);
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v12->vfptr,
      "CRFWorldDatabase::Update_RaceRank_Step3(szDate(%s)) : End Set Rate #tbl_PvpRankA Table",
      szDatea);
    return 1;
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v12->vfptr,
    "CRFWorldDatabase::Update_RaceRank_Step1(szDate(%s)) : Set Rate #tbl_PvpRankA Table Fail SQL_ERROR!",
    szDatea);
  SQLStmt = v12->m_hStmtUpdate;
  CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v12->vfptr, v7, &DstBuf, "SQLExecDirect", SQLStmt);
  CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v12->vfptr);
  CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v12->vfptr, 1);
  if ( !CRFWorldDatabase::Update_RaceRank_Step_6_1(v12, szDatea) )
    CRFWorldDatabase::Update_RaceRank_Step_6_1(v12, szDatea);
  if ( !CRFWorldDatabase::Update_RaceRank_Step_6_2(v12, szDatea) )
    CRFWorldDatabase::Update_RaceRank_Step_6_2(v12, szDatea);
  return 0;
}
