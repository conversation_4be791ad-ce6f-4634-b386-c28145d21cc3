/*
 * Function: ?pc_PlayAttack_Test@CPlayer@@QEAAXEEGEPEAF@Z
 * Address: 0x1400835A0
 */

void __fastcall CPlayer::pc_PlayAttack_Test(CPlayer *this, char byEffectCode, char byEffectIndex, unsigned __int16 wBulletItemSerial, char byWeaponPart, __int16 *pzTar)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-A8h]@1
  bool bUpdate; // [sp+20h] [bp-88h]@7
  bool bSend; // [sp+28h] [bp-80h]@7
  char v11; // [sp+40h] [bp-68h]@4
  _STORAGE_LIST::_db_con *pItem; // [sp+48h] [bp-60h]@4
  unsigned __int16 v13; // [sp+50h] [bp-58h]@4
  __int16 v14; // [sp+54h] [bp-54h]@4
  char v15; // [sp+58h] [bp-50h]@4
  char *v16; // [sp+60h] [bp-48h]@11
  _base_fld *v17; // [sp+68h] [bp-40h]@13
  char *v18; // [sp+70h] [bp-38h]@13
  int j; // [sp+78h] [bp-30h]@13
  char *v20; // [sp+80h] [bp-28h]@16
  unsigned int *v21; // [sp+88h] [bp-20h]@23
  _base_fld *v22; // [sp+90h] [bp-18h]@26
  int v23; // [sp+98h] [bp-10h]@33
  CPlayer *v24; // [sp+B0h] [bp+8h]@1
  char v25; // [sp+B8h] [bp+10h]@1
  char v26; // [sp+C0h] [bp+18h]@1
  unsigned __int16 v27; // [sp+C8h] [bp+20h]@1

  v27 = wBulletItemSerial;
  v26 = byEffectIndex;
  v25 = byEffectCode;
  v24 = this;
  v6 = &v8;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v11 = 0;
  pItem = 0i64;
  v13 = -1;
  v14 = 0;
  v15 = 1;
  if ( !CPlayer::IsRidingUnit(v24) )
  {
    if ( v27 != 0xFFFF )
    {
      pItem = CPlayer::IsBulletValidity(v24, v27);
      if ( pItem )
      {
        bSend = 1;
        bUpdate = 0;
        v14 = CPlayer::Emb_AlterDurPoint(v24, 2, pItem->m_byStorageIndex, -1, 0, 1);
        if ( v14 )
          CPlayer::SendMsg_AlterWeaponBulletInform(v24, pItem->m_wSerial, pItem->m_dwDur);
        else
          CMgrAvatorItemHistory::consume_del_item(
            &CPlayer::s_MgrItemHistory,
            v24->m_ObjID.m_wIndex,
            pItem,
            v24->m_szItemHistoryFileName);
      }
    }
    if ( v25 )
    {
      if ( v25 == 1 )
      {
        v17 = CRecordData::GetRecord(&stru_1799C8410 + 1, (unsigned __int8)v26);
        v18 = 0i64;
        for ( j = 0; j < 88; ++j )
        {
          v20 = &v24->m_Param.m_dbForce.m_pStorageList[j].m_bLoad;
          if ( *v20 && *((_DWORD *)CPlayer::s_pnLinkForceItemToEffect + *(_WORD *)(v20 + 3)) == (unsigned __int8)v26 )
          {
            v18 = v20;
            break;
          }
        }
        if ( v18 )
          v15 = GetSFLevel(*(_DWORD *)&v17[4].m_strCode[60], *(_DWORD *)(v18 + 5));
      }
    }
    else
    {
      v16 = (char *)CRecordData::GetRecord(&stru_1799C8410, (unsigned __int8)v26);
      v15 = _MASTERY_PARAM::GetSkillLv(&v24->m_pmMst, *v16);
    }
    goto LABEL_35;
  }
  v21 = &v24->m_pUsingUnit->dwBullet[(unsigned __int8)byWeaponPart];
  if ( *((_WORD *)v21 + 1) && *((_WORD *)v21 + 1) != 0xFFFF )
  {
    v13 = *(_WORD *)v21;
    v22 = 0i64;
    v22 = byWeaponPart ? CRecordData::GetRecord(&stru_1799C86D0 + 4, v24->m_pUsingUnit->byPart[4]) : CRecordData::GetRecord(&stru_1799C86D0 + 3, v24->m_pUsingUnit->byPart[3]);
    if ( v22 )
    {
      if ( (signed int)*((_WORD *)v21 + 1) >= *(_DWORD *)&v22[4].m_strCode[0] )
        *((_WORD *)v21 + 1) -= *(_WORD *)&v22[4].m_strCode[0];
      else
        *((_WORD *)v21 + 1) = 0;
      v23 = *((_WORD *)v21 + 1);
      CPlayer::SendMsg_AlterUnitBulletInform(v24, byWeaponPart, v23);
      if ( !*((_WORD *)v21 + 1) )
        *v21 = -1;
LABEL_35:
      if ( _effect_parameter::GetEff_State(&v24->m_EP, 14) )
        CCharacter::RemoveSFContHelpByEffect((CCharacter *)&v24->vfptr, 2, 14);
      CPlayer::SendMsg_TestAttackResult(v24, v25, v26, v13, v15, byWeaponPart, pzTar);
      return;
    }
  }
}
