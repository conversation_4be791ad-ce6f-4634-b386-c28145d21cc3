/*
 * Function: ?Select_UnmannedTraderSellInfo@CRFWorldDatabase@@QEAAEEKEAEAU_unmannedtrader_seller_info@@@Z
 * Address: 0x1404AD210
 */

char __fastcall CRFWorldDatabase::Select_UnmannedTraderSellInfo(CRFWorldDatabase *this, char byType, unsigned int dwRegistSerial, char byRace, _unmannedtrader_seller_info *kData)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v8; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@4
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@22
  SQLLEN v11; // [sp+38h] [bp-150h]@22
  __int16 v12; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  char v14; // [sp+164h] [bp-24h]@16
  unsigned __int64 v15; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v16; // [sp+190h] [bp+8h]@1

  v16 = this;
  v5 = &v8;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v15 = (unsigned __int64)&v8 ^ _security_cookie;
  LODWORD(SQLStmt) = (unsigned __int8)byRace;
  sprintf(&Dest, "{ CALL pSelect_utsellinfo( %u, %u, %u ) }", (unsigned __int8)byType, dwRegistSerial);
  if ( v16->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v16->vfptr, &Dest);
  if ( v16->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v16->vfptr) )
  {
    v12 = SQLExecDirectA_0(v16->m_hStmtSelect, &Dest, -3);
    if ( v12 && v12 != 1 )
    {
      if ( v12 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v16->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v12, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v12, v16->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v12 = SQLFetch_0(v16->m_hStmtSelect);
      if ( v12 && v12 != 1 )
      {
        v14 = 0;
        if ( v12 == 100 )
        {
          v14 = 2;
        }
        else
        {
          SQLStmt = v16->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v12, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v12, v16->m_hStmtSelect);
          v14 = 1;
        }
        if ( v16->m_hStmtSelect )
          SQLCloseCursor_0(v16->m_hStmtSelect);
        result = v14;
      }
      else
      {
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v16->m_hStmtSelect, 1u, -18, kData, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          SQLStmt = v16->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v12, &Dest, "SQLGetData", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v12, v16->m_hStmtSelect);
          if ( v16->m_hStmtSelect )
            SQLCloseCursor_0(v16->m_hStmtSelect);
          result = 1;
        }
        else
        {
          StrLen_or_IndPtr = &v11;
          SQLStmt = 0i64;
          v12 = SQLGetData_0(v16->m_hStmtSelect, 2u, -18, &kData->dwSeller, 0i64, &v11);
          if ( v12 && v12 != 1 )
          {
            SQLStmt = v16->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v12, &Dest, "SQLGetData", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v12, v16->m_hStmtSelect);
            if ( v16->m_hStmtSelect )
              SQLCloseCursor_0(v16->m_hStmtSelect);
            result = 1;
          }
          else
          {
            StrLen_or_IndPtr = &v11;
            SQLStmt = 0i64;
            v12 = SQLGetData_0(v16->m_hStmtSelect, 3u, -6, &kData->byRaceSexCode, 0i64, &v11);
            if ( v12 && v12 != 1 )
            {
              SQLStmt = v16->m_hStmtSelect;
              CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v12, &Dest, "SQLGetData", SQLStmt);
              CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v12, v16->m_hStmtSelect);
              if ( v16->m_hStmtSelect )
                SQLCloseCursor_0(v16->m_hStmtSelect);
              result = 1;
            }
            else
            {
              StrLen_or_IndPtr = &v11;
              SQLStmt = 0i64;
              v12 = SQLGetData_0(v16->m_hStmtSelect, 4u, 4, &kData->dwDalant, 0i64, &v11);
              if ( v12 && v12 != 1 )
              {
                SQLStmt = v16->m_hStmtSelect;
                CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v12, &Dest, "SQLGetData", SQLStmt);
                CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v12, v16->m_hStmtSelect);
                if ( v16->m_hStmtSelect )
                  SQLCloseCursor_0(v16->m_hStmtSelect);
                result = 1;
              }
              else
              {
                StrLen_or_IndPtr = &v11;
                SQLStmt = 0i64;
                v12 = SQLGetData_0(v16->m_hStmtSelect, 5u, 4, &kData->dwGuildSerial, 0i64, &v11);
                if ( v12 && v12 != 1 )
                {
                  SQLStmt = v16->m_hStmtSelect;
                  CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v12, &Dest, "SQLGetData", SQLStmt);
                  CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v12, v16->m_hStmtSelect);
                  if ( v16->m_hStmtSelect )
                    SQLCloseCursor_0(v16->m_hStmtSelect);
                  result = 1;
                }
                else
                {
                  StrLen_or_IndPtr = &v11;
                  SQLStmt = 0i64;
                  v12 = SQLGetData_0(v16->m_hStmtSelect, 6u, 4, &kData->dwAccountSerial, 0i64, &v11);
                  if ( v12 && v12 != 1 )
                  {
                    SQLStmt = v16->m_hStmtSelect;
                    CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v12, &Dest, "SQLGetData", SQLStmt);
                    CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v12, v16->m_hStmtSelect);
                    if ( v16->m_hStmtSelect )
                      SQLCloseCursor_0(v16->m_hStmtSelect);
                    result = 1;
                  }
                  else
                  {
                    StrLen_or_IndPtr = &v11;
                    SQLStmt = (void *)17;
                    v12 = SQLGetData_0(v16->m_hStmtSelect, 7u, 1, kData->szAccountID, 17i64, &v11);
                    if ( v12 && v12 != 1 )
                    {
                      SQLStmt = v16->m_hStmtSelect;
                      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v12, &Dest, "SQLGetData", SQLStmt);
                      CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v12, v16->m_hStmtSelect);
                      if ( v16->m_hStmtSelect )
                        SQLCloseCursor_0(v16->m_hStmtSelect);
                      result = 1;
                    }
                    else
                    {
                      StrLen_or_IndPtr = &v11;
                      SQLStmt = (void *)17;
                      v12 = SQLGetData_0(v16->m_hStmtSelect, 8u, 1, kData->wszName, 17i64, &v11);
                      if ( v12 && v12 != 1 )
                      {
                        SQLStmt = v16->m_hStmtSelect;
                        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v12, &Dest, "SQLGetData", SQLStmt);
                        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v12, v16->m_hStmtSelect);
                        if ( v16->m_hStmtSelect )
                          SQLCloseCursor_0(v16->m_hStmtSelect);
                        result = 1;
                      }
                      else
                      {
                        if ( v16->m_hStmtSelect )
                          SQLCloseCursor_0(v16->m_hStmtSelect);
                        if ( v16->m_bSaveDBLog )
                          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v16->vfptr, "%s Success", &Dest);
                        result = 0;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v16->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
