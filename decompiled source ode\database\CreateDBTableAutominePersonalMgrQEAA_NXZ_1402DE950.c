/*
 * Function: ?CreateDBTable@AutominePersonalMgr@@QEAA_NXZ
 * Address: 0x1402DE950
 */

bool __fastcall AutominePersonalMgr::CreateDBTable(AutominePersonalMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  AutominePersonalMgr *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( pkDB )
  {
    if ( CRFNewDatabase::TableExist((CRFNewDatabase *)&pkDB->vfptr, "tbl_aminepersonal_inven") )
      result = 1;
    else
      result = CRFWorldDatabase::create_amine_personal(pkDB);
  }
  else
  {
    CLogFile::Write(&v5->m_logError, "AutominePersonalMgr::CreateDBTable() >> g_Main.m_pWorldDB is NULL.");
    result = 0;
  }
  return result;
}
