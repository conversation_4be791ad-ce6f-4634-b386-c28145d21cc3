/*
 * Function: ?GetPortalIndexInfo@CNormalGuildBattleField@GUILD_BATTLE@@QEAAXAEAH0PEAH@Z
 * Address: 0x1403ECAF0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleField::GetPortalIndexInfo(GUILD_BATTLE::CNormalGuildBattleField *this, int *iRedPortalInx, int *iBluePortalInx, int *piRegenPortalInx)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v7; // [sp+30h] [bp+8h]@1
  int *v8; // [sp+40h] [bp+18h]@1
  int *v9; // [sp+48h] [bp+20h]@1

  v9 = piRegenPortalInx;
  v8 = iBluePortalInx;
  v7 = this;
  v4 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v7->m_ui1PGoalPosCnt >= 1 )
    *iRedPortalInx = CCircleZone::GetPortalInx(v7->m_pk1PGoalZone);
  if ( v7->m_ui2PGoalPosCnt >= 1 )
    *v8 = CCircleZone::GetPortalInx(v7->m_pk2PGoalZone);
  if ( v9 )
  {
    if ( v7->m_uiRegenPosCnt >= 3 )
    {
      *v9 = CGravityStoneRegener::GetPortalInx(v7->m_pkRegenPos);
      v9[1] = CGravityStoneRegener::GetPortalInx(v7->m_pkRegenPos + 1);
      v9[2] = CGravityStoneRegener::GetPortalInx(v7->m_pkRegenPos + 2);
    }
  }
}
