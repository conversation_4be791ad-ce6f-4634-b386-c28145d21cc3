/*
 * Function: ?SendData_ChatTrans@CPlayer@@QEAAXEKE_NPEADE1@Z
 * Address: 0x1400DCAB0
 */

void __fastcall CPlayer::SendData_ChatTrans(CPlayer *this, char byChatType, unsigned int dwSenderSerial, char byRaceCode, bool bFilter, char *pwszMessage, char byPvpGrade, char *pwszSender)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v10; // ax@10
  __int64 v11; // [sp+0h] [bp-1A8h]@1
  _chat_message_receipt_udp Dst; // [sp+40h] [bp-168h]@4
  char pbyType; // [sp+174h] [bp-34h]@10
  char v14; // [sp+175h] [bp-33h]@10
  unsigned __int64 v15; // [sp+190h] [bp-18h]@4
  CPlayer *v16; // [sp+1B0h] [bp+8h]@1
  char v17; // [sp+1B8h] [bp+10h]@1
  unsigned int v18; // [sp+1C0h] [bp+18h]@1
  char v19; // [sp+1C8h] [bp+20h]@1

  v19 = byRaceCode;
  v18 = dwSenderSerial;
  v17 = byChatType;
  v16 = this;
  v8 = &v11;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v15 = (unsigned __int64)&v11 ^ _security_cookie;
  _chat_message_receipt_udp::_chat_message_receipt_udp(&Dst);
  Dst.byMessageType = v17;
  Dst.dwSenderSerial = v18;
  if ( pwszSender )
    strcpy_s<17>((char (*)[17])Dst.wszSenderName, pwszSender);
  Dst.byPvpGrade = byPvpGrade;
  Dst.byRaceCode = v19;
  Dst.bFiltering = bFilter;
  if ( bFilter )
  {
    Dst.wszChatData[0] = 0;
    Dst.bySize = 0;
  }
  else
  {
    Dst.bySize = strlen_0(pwszMessage);
    if ( (signed int)(unsigned __int8)Dst.bySize > 255 )
      return;
    memcpy_0(Dst.wszChatData, pwszMessage, (unsigned __int8)Dst.bySize);
    Dst.wszChatData[(unsigned __int8)Dst.bySize] = 0;
  }
  pbyType = 2;
  v14 = 10;
  v10 = _chat_message_receipt_udp::size(&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2088, v16->m_ObjID.m_wIndex, &pbyType, &Dst.byMessageType, v10);
}
