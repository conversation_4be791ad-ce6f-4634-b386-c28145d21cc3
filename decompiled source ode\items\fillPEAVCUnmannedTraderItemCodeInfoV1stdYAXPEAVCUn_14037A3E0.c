/*
 * Function: ??$fill@PEAVCUnmannedTraderItemCodeInfo@@V1@@std@@YAXPEAVCUnmannedTraderItemCodeInfo@@0AEBV1@@Z
 * Address: 0x14037A3E0
 */

void __fastcall std::fill<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo>(CUnmannedTraderItemCodeInfo *_First, CUnmannedTraderItemCodeInfo *_Last, CUnmannedTraderItemCodeInfo *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderItemCodeInfo *_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Fill<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo>(_Firsta, _Last, _Val);
}
