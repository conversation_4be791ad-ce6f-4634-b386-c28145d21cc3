/*
 * Function: ??4?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@CryptoPP@@QEAAAEAV01@AEBV01@@Z
 * Address: 0x14045BC80
 */

CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint> *__fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::operator=(CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint> *this, CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint> *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint> *v6; // [sp+30h] [bp+8h]@1
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint> *__thata; // [sp+38h] [bp+10h]@1

  __thata = __that;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint>::operator=(
    (CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint> *)&v6->vfptr,
    (CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint> *)&__that->vfptr);
  CryptoPP::ECPPoint::operator=(&v6->m_base, &__thata->m_base);
  v6->m_windowSize = __thata->m_windowSize;
  CryptoPP::Integer::operator=(&v6->m_exponentBase);
  std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::operator=(&v6->m_bases, &__thata->m_bases);
  return v6;
}
