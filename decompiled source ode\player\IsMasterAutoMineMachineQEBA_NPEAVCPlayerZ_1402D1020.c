/*
 * Function: ?IsMaster@AutoMineMachine@@QEBA_NPEAVCPlayer@@@Z
 * Address: 0x1402D1020
 */

bool __fastcall AutoMineMachine::IsMaster(AutoMineMachine *this, CPlayer *pUser)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  unsigned int v5; // eax@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  CGuild *v7; // [sp+20h] [bp-18h]@6
  int v8; // [sp+28h] [bp-10h]@6
  AutoMineMachine *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v9->m_pOwnerGuild )
  {
    v7 = v9->m_pOwnerGuild;
    v5 = CPlayerDB::GetCharSerial(&pUser->m_Param);
    v8 = v7->m_MasterData.dwSerial == v5;
    result = v8;
  }
  else
  {
    result = 0;
  }
  return result;
}
