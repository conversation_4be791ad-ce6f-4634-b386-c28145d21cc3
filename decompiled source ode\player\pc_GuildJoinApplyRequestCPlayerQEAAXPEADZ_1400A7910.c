/*
 * Function: ?pc_GuildJoinApplyRequest@CPlayer@@QEAAXPEAD@Z
 * Address: 0x1400A7910
 */

void __fastcall CPlayer::pc_GuildJoinApplyRequest(CPlayer *this, char *pwszGuildName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@19
  __int64 v5; // [sp+0h] [bp-48h]@1
  char v6; // [sp+20h] [bp-28h]@4
  CGuild *pApplyGuild; // [sp+28h] [bp-20h]@4
  int v8; // [sp+30h] [bp-18h]@19
  CPlayer *pApplier; // [sp+50h] [bp+8h]@1

  pApplier = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  pApplyGuild = GetGuildPtrFromName(g_Guild, 500, pwszGuildName);
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, pApplier->m_id.wIndex) == 99 )
  {
    v6 = 106;
  }
  else
  {
    if ( CPlayer::IsPunished(pApplier, 1, 1) )
      return;
    if ( pApplyGuild )
    {
      if ( pApplier->m_Param.m_bGuildLock )
      {
        v6 = -51;
      }
      else if ( pApplier->m_Param.m_pGuild )
      {
        v6 = 22;
      }
      else if ( pApplier->m_Param.m_pApplyGuild )
      {
        v6 = 23;
      }
      else if ( pApplyGuild->m_nMemberNum < 50 )
      {
        if ( pApplyGuild->m_nApplierNum < 32 )
        {
          v8 = CPlayerDB::GetRaceCode(&pApplier->m_Param);
          v4 = CGuild::GetRace(pApplyGuild);
          if ( v8 == (unsigned __int8)v4 )
          {
            if ( CMainThread::IsReleaseServiceMode(&g_Main) && pApplier->m_byUserDgr )
              v6 = -1;
          }
          else
          {
            v6 = 70;
          }
        }
        else
        {
          v6 = 25;
        }
      }
      else
      {
        v6 = 24;
      }
    }
    else
    {
      v6 = -56;
    }
  }
  if ( !v6 )
  {
    CGuild::PushApplier(pApplyGuild, pApplier);
    pApplier->m_Param.m_pApplyGuild = pApplyGuild;
  }
  CPlayer::SendMsg_GuildJoinApplyResult(pApplier, v6, pApplyGuild);
}
