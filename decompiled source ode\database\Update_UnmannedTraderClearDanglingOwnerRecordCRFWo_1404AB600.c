/*
 * Function: ?Update_UnmannedTraderClearDanglingOwnerRecord@CRFWorldDatabase@@QEAA_NXZ
 * Address: 0x1404AB600
 */

bool __fastcall CRFWorldDatabase::Update_UnmannedTraderClearDanglingOwnerRecord(CRFWorldDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-458h]@1
  char Dest; // [sp+30h] [bp-428h]@4
  unsigned __int64 v6; // [sp+440h] [bp-18h]@4
  CRFWorldDatabase *v7; // [sp+460h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 276i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (unsigned __int64)&v4 ^ _security_cookie;
  sprintf(
    &Dest,
    "update [dbo].[tbl_utresultinfo] set [dbo].[tbl_utresultinfo].state = 0 from ( select s.type, s.serial, s.owner, r.st"
    "ate from [dbo].[tbl_utsellinfo] as s join [dbo].[tbl_utresultinfo] as r on s.owner <> 0 and r.type = s.type and s.se"
    "rial = r.serial join [dbo].[tbl_base] as b on s.owner = b.serial and b.dck = 1 ) as d where [dbo].[tbl_utresultinfo]"
    ".type = d.type and [dbo].[tbl_utresultinfo].serial = d.serial");
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v7->vfptr, &Dest, 0);
}
