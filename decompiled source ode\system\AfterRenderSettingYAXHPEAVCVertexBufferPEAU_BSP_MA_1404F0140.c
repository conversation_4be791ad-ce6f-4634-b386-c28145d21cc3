/*
 * Function: ?AfterRenderSetting@@YAXHPEAVCVertexBuffer@@PEAU_BSP_MAT_GROUP@@@Z
 * Address: 0x1404F0140
 */

void __fastcall AfterRenderSetting(int a1, struct CVertexBuffer *a2, struct _BSP_MAT_GROUP *a3)
{
  struct _BSP_MAT_GROUP *v3; // rbx@1
  __int64 v4; // rbp@1
  struct IDirect3DDevice8 *v5; // rdi@1
  struct _R3MATERIAL *v6; // rax@1
  int v7; // ecx@1
  signed __int64 v8; // rsi@1
  signed __int64 v9; // rdx@5
  signed __int64 v10; // rbx@9

  v3 = a3;
  v4 = a1;
  v5 = GetD3dDevice();
  v6 = GetMainMaterial();
  v8 = (signed __int64)&v6[v3->MtlId].m_iMatNum;
  if ( dword_184A79C28 )
  {
    LOWORD(v7) = *(_WORD *)(v8 + 208);
    if ( _bittest(&v7, 0xFu) && dword_184A797A8 )
    {
      LODWORD(v6) = ((int (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64))v5->vfptr[21].QueryInterface)(
                      v5,
                      1i64,
                      24i64);
      if ( !(*(_BYTE *)(46 * v4 + v8 + 162) & 0xA) )
        goto LABEL_9;
      v9 = 1i64;
    }
    else
    {
      LODWORD(v6) = ((int (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64))v5->vfptr[21].QueryInterface)(
                      v5,
                      0i64,
                      24i64);
      if ( !(*(_BYTE *)(46 * v4 + v8 + 162) & 0xA) )
        goto LABEL_9;
      v9 = 0i64;
    }
    LODWORD(v6) = ((int (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64))v5->vfptr[21].QueryInterface)(
                    v5,
                    v9,
                    11i64);
  }
LABEL_9:
  v10 = 46 * v4;
  LOWORD(v6) = *(_WORD *)(46 * v4 + v8 + 162);
  if ( _bittest((const signed int *)&v6, 0xAu) )
  {
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, _QWORD))v5->vfptr[16].Release)(v5, 137i64, 0i64);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64))v5->vfptr[16].Release)(
      v5,
      145i64,
      1i64);
    BlendOff();
  }
  if ( _bittest((const signed __int32 *)(v10 + v8 + 162), 0x10u) )
  {
    ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64, signed __int64))v5->vfptr[21].QueryInterface)(
      v5,
      0i64,
      13i64,
      1i64);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64, signed __int64))v5->vfptr[21].QueryInterface)(
      v5,
      0i64,
      14i64,
      1i64);
  }
  if ( *(_DWORD *)(v10 + v8 + 154) )
    BlendOff();
  if ( *(_BYTE *)(v10 + v8 + 162) & 1 )
  {
    ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64))v5->vfptr[21].QueryInterface)(
      v5,
      0i64,
      11i64);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64))v5->vfptr[21].QueryInterface)(
      v5,
      0i64,
      24i64);
  }
}
