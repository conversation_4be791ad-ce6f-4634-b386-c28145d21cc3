/*
 * Function: ?Process@CGuildBattleSchedule@GUILD_BATTLE@@IEAAHXZ
 * Address: 0x1403DA3B0
 */

__int64 __fastcall GUILD_BATTLE::CGuildBattleSchedule::Process(GUILD_BATTLE::CGuildBattleSchedule *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@5
  ATL::CTimeSpan *v4; // rax@6
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  ATL::CTimeSpan result; // [sp+28h] [bp-10h]@6
  GUILD_BATTLE::CGuildBattleSchedule *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = GUILD_BATTLE::CGuildBattleStateList::Next(v8->m_pkStateList, 0);
  if ( v6 )
  {
    v4 = GUILD_BATTLE::CGuildBattleStateList::GetTerm(v8->m_pkStateList, &result);
    ATL::CTime::operator+=(&v8->m_kNextStartTime, (ATL::CTimeSpan)v4->m_timeSpan);
    v3 = (unsigned int)v6;
  }
  else
  {
    v3 = 0i64;
  }
  return v3;
}
