/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAVCRaceBuffInfoByHolyQuestfGroup@@_KPEAV1@V?$allocator@PEAVCRaceBuffInfoByHolyQuestfGroup@@@std@@@stdext@@YAXPEAPEAVCRaceBuffInfoByHolyQuestfGroup@@_KAEBQEAV1@AEAV?$allocator@PEAVCRaceBuffInfoByHolyQuestfGroup@@@std@@@Z
 * Address: 0x14000C4FF
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CRaceBuffInfoByHolyQuestfGroup * *,unsigned __int64,CRaceBuffInfoByHolyQuestfGroup *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>(CRaceBuffInfoByHolyQuestfGroup **_First, unsigned __int64 _Count, CRaceBuffInfoByHolyQuestfGroup *const *_Val, std::allocator<CRaceBuffInfoByHolyQuestfGroup *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CRaceBuffInfoByHolyQuestfGroup * *,unsigned __int64,CRaceBuffInfoByHolyQuestfGroup *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>(
    _First,
    _Count,
    _Val,
    _Al);
}
