/*
 * Function: ?SelectSearchList@CUnmannedTraderController@@QEAAEPEADPEAVCRFWorldDatabase@@AEAE@Z
 * Address: 0x14034DEA0
 */

char __fastcall CUnmannedTraderController::SelectSearchList(CUnmannedTraderController *this, char *pData, CRFWorldDatabase *pkWorldDB, char *byProcRet)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-88h]@1
  char byClass2; // [sp+20h] [bp-68h]@6
  char byClass3; // [sp+28h] [bp-60h]@6
  unsigned int *dwCount; // [sp+30h] [bp-58h]@6
  unsigned int dwExcludeRowCount; // [sp+38h] [bp-50h]@16
  char *szSortQuery; // [sp+40h] [bp-48h]@16
  _unmannedtrader_page_info *pkInfo; // [sp+48h] [bp-40h]@16
  char *v14; // [sp+50h] [bp-38h]@4
  unsigned int v15; // [sp+64h] [bp-24h]@6
  char v16; // [sp+74h] [bp-14h]@6
  unsigned int v17; // [sp+78h] [bp-10h]@10
  unsigned int v18; // [sp+7Ch] [bp-Ch]@16
  CRFWorldDatabase *v19; // [sp+A0h] [bp+18h]@1
  char *v20; // [sp+A8h] [bp+20h]@1

  v20 = byProcRet;
  v19 = pkWorldDB;
  v4 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v14 = pData;
  if ( pkWorldDB )
  {
    dwCount = &v15;
    byClass3 = v14[18];
    byClass2 = v14[17];
    v16 = CRFWorldDatabase::Select_UnmannedTraderSearchGroupTotalRowCount(
            pkWorldDB,
            v14[8],
            v14[9],
            v14[16],
            byClass2,
            byClass3,
            &v15);
    if ( v16 == 1 )
    {
      *v20 = 61;
      result = 2;
    }
    else if ( v16 == 2 )
    {
      *v20 = 62;
      result = 2;
    }
    else
    {
      v17 = v15 / CUnmannedTraderEnvironmentValue::Unmanned_Trader_Max_Row_Count_Search;
      if ( v15 % CUnmannedTraderEnvironmentValue::Unmanned_Trader_Max_Row_Count_Search )
        ++v17;
      *((_DWORD *)v14 + 39) = v17;
      if ( v17 )
      {
        if ( (unsigned __int8)v14[24] < v17 )
        {
          v18 = CUnmannedTraderEnvironmentValue::Unmanned_Trader_Max_Row_Count_Search * (unsigned __int8)v14[24];
          pkInfo = (_unmannedtrader_page_info *)(v14 + 160);
          szSortQuery = v14 + 25;
          dwExcludeRowCount = v18;
          LODWORD(dwCount) = CUnmannedTraderEnvironmentValue::Unmanned_Trader_Max_Row_Count_Search;
          byClass3 = v14[18];
          byClass2 = v14[17];
          v16 = CRFWorldDatabase::Select_UnmannedTraderSearchPageInfo(
                  v19,
                  v14[8],
                  v14[9],
                  v14[16],
                  byClass2,
                  byClass3,
                  CUnmannedTraderEnvironmentValue::Unmanned_Trader_Max_Row_Count_Search,
                  v18,
                  v14 + 25,
                  (_unmannedtrader_page_info *)(v14 + 160));
          if ( v16 == 1 )
          {
            *v20 = 64;
            result = 2;
          }
          else if ( v16 == 2 )
          {
            *v20 = 65;
            result = 2;
          }
          else
          {
            *v20 = 0;
            result = 0;
          }
        }
        else
        {
          *v20 = 63;
          result = 0;
        }
      }
      else
      {
        *v20 = 62;
        result = 0;
      }
    }
  }
  else
  {
    *byProcRet = 60;
    result = 1;
  }
  return result;
}
