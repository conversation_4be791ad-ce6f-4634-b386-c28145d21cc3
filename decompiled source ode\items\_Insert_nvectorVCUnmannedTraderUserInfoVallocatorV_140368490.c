/*
 * Function: ?_Insert_n@?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@IEAAXV?$_Vector_iterator@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@2@_KAEBVCUnmannedTraderUserInfo@@@Z
 * Address: 0x140368490
 */

void __fastcall std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Insert_n(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this, std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *_Where, unsigned __int64 _Count, CUnmannedTraderUserInfo *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v6; // rax@5
  unsigned __int64 v7; // rax@7
  unsigned __int64 v8; // rax@8
  unsigned __int64 v9; // rax@11
  __int64 v10; // [sp+0h] [bp-108h]@1
  CUnmannedTraderUserInfo _Vala; // [sp+30h] [bp-D8h]@4
  unsigned __int64 _Counta; // [sp+A8h] [bp-60h]@4
  CUnmannedTraderUserInfo *_Ptr; // [sp+B0h] [bp-58h]@13
  CUnmannedTraderUserInfo *v14; // [sp+B8h] [bp-50h]@13
  CUnmannedTraderUserInfo *_Last; // [sp+C0h] [bp-48h]@18
  __int64 v16; // [sp+C8h] [bp-40h]@4
  unsigned __int64 v17; // [sp+D0h] [bp-38h]@5
  unsigned __int64 v18; // [sp+D8h] [bp-30h]@8
  unsigned __int64 v19; // [sp+E0h] [bp-28h]@9
  CUnmannedTraderUserInfo *v20; // [sp+E8h] [bp-20h]@13
  CUnmannedTraderUserInfo *v21; // [sp+F0h] [bp-18h]@13
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v22; // [sp+110h] [bp+8h]@1
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v23; // [sp+118h] [bp+10h]@1
  unsigned __int64 v24; // [sp+120h] [bp+18h]@1
  unsigned __int64 v25; // [sp+120h] [bp+18h]@13

  v24 = _Count;
  v23 = _Where;
  v22 = this;
  v4 = &v10;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v16 = -2i64;
  CUnmannedTraderUserInfo::CUnmannedTraderUserInfo(&_Vala, _Val);
  _Counta = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::capacity(v22);
  if ( v24 )
  {
    v17 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::size(v22);
    v6 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::max_size(v22);
    if ( v6 - v17 < v24 )
      std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Xlen();
    v7 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::size(v22);
    if ( _Counta >= v24 + v7 )
    {
      if ( (unsigned int)((char *)v22->_Mylast - (char *)v23->_Myptr) / 104i64 >= v24 )
      {
        _Last = v22->_Mylast;
        v22->_Mylast = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Umove<CUnmannedTraderUserInfo *>(
                         v22,
                         &_Last[-v24],
                         _Last,
                         v22->_Mylast);
        stdext::_Unchecked_move_backward<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo *>(
          v23->_Myptr,
          &_Last[-v24],
          _Last);
        std::fill<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo>(v23->_Myptr, &v23->_Myptr[v24], &_Vala);
      }
      else
      {
        std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Umove<CUnmannedTraderUserInfo *>(
          v22,
          v23->_Myptr,
          v22->_Mylast,
          &v23->_Myptr[v24]);
        std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Ufill(
          v22,
          v22->_Mylast,
          v24 - (unsigned int)((char *)v22->_Mylast - (char *)v23->_Myptr) / 104i64,
          &_Vala);
        v22->_Mylast += v24;
        std::fill<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo>(v23->_Myptr, &v22->_Mylast[-v24], &_Vala);
      }
    }
    else
    {
      v18 = _Counta / 2;
      v8 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::max_size(v22);
      if ( v8 - v18 >= _Counta )
        v19 = _Counta / 2 + _Counta;
      else
        v19 = 0i64;
      _Counta = v19;
      v9 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::size(v22);
      if ( _Counta < v24 + v9 )
        _Counta = v24 + std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::size(v22);
      _Ptr = std::allocator<CUnmannedTraderUserInfo>::allocate(&v22->_Alval, _Counta);
      v14 = _Ptr;
      v20 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Umove<CUnmannedTraderUserInfo *>(
              v22,
              v22->_Myfirst,
              v23->_Myptr,
              _Ptr);
      v14 = v20;
      v21 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Ufill(v22, v20, v24, &_Vala);
      v14 = v21;
      std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Umove<CUnmannedTraderUserInfo *>(
        v22,
        v23->_Myptr,
        v22->_Mylast,
        v21);
      v25 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::size(v22) + v24;
      if ( v22->_Myfirst )
      {
        std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Destroy(
          v22,
          v22->_Myfirst,
          v22->_Mylast);
        std::allocator<CUnmannedTraderUserInfo>::deallocate(
          &v22->_Alval,
          v22->_Myfirst,
          (unsigned int)((char *)v22->_Myend - (char *)v22->_Myfirst) / 104i64);
      }
      v22->_Myend = &_Ptr[_Counta];
      v22->_Mylast = &_Ptr[v25];
      v22->_Myfirst = _Ptr;
    }
  }
  CUnmannedTraderUserInfo::~CUnmannedTraderUserInfo(&_Vala);
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::~_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(v23);
}
