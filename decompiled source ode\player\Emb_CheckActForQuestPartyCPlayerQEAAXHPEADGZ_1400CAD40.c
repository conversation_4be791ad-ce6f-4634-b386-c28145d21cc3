/*
 * Function: ?Emb_CheckActForQuestParty@CPlayer@@QEAAXHPEADG@Z
 * Address: 0x1400CAD40
 */

void __fastcall CPlayer::Emb_CheckActForQuestParty(CPlayer *this, int nActCode, char *pszReqCode, unsigned __int16 wAddCount)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-98h]@1
  bool bParty; // [sp+20h] [bp-78h]@8
  CPlayer *out_ppMember; // [sp+40h] [bp-58h]@5
  char v9; // [sp+84h] [bp-14h]@5
  int j; // [sp+88h] [bp-10h]@5
  CPlayer *v11; // [sp+A0h] [bp+8h]@1
  int nActCodea; // [sp+A8h] [bp+10h]@1
  char *pszReqCodea; // [sp+B0h] [bp+18h]@1
  unsigned __int16 v14; // [sp+B8h] [bp+20h]@1

  v14 = wAddCount;
  pszReqCodea = pszReqCode;
  nActCodea = nActCode;
  v11 = this;
  v4 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( CPartyPlayer::IsPartyMode(v11->m_pPartyMgr) )
  {
    v9 = CPlayer::_GetPartyMemberInCircle(v11, &out_ppMember, 8, 1);
    for ( j = 0; j < (unsigned __int8)v9; ++j )
    {
      if ( *(&out_ppMember + j) != v11 )
      {
        bParty = 1;
        CPlayer::Emb_CheckActForQuest(*(&out_ppMember + j), nActCodea, pszReqCodea, v14, 1);
      }
    }
  }
}
