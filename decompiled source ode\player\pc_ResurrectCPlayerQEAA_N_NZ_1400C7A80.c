/*
 * Function: ?pc_Resurrect@CPlayer@@QEAA_N_N@Z
 * Address: 0x1400C7A80
 */

bool __fastcall CPlayer::pc_Resurrect(CPlayer *this, bool bQuickPotion)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@9
  int v5; // eax@9
  int v6; // eax@9
  CNuclearBombMgr *v7; // rax@11
  __int64 v9; // [sp+0h] [bp-48h]@1
  char v10; // [sp+20h] [bp-28h]@4
  CGameObjectVtbl *v11; // [sp+28h] [bp-20h]@9
  CPlayer *pOne; // [sp+50h] [bp+8h]@1
  bool v13; // [sp+58h] [bp+10h]@1

  v13 = bQuickPotion;
  pOne = this;
  v2 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = 0;
  if ( pOne->m_bCorpse )
  {
    if ( pOne->m_pCurMap->m_pMapSet->m_nMapType == 1 )
      v10 = 2;
  }
  else
  {
    v10 = 1;
  }
  if ( !v10 )
  {
    pOne->m_bCorpse = 0;
    pOne->m_byModeType = 0;
    pOne->m_byMoveType = 1;
    v4 = ((int (__fastcall *)(CPlayer *))pOne->vfptr->GetMaxHP)(pOne);
    v11 = pOne->vfptr;
    ((void (__fastcall *)(CPlayer *, _QWORD, _QWORD))v11->SetHP)(pOne, (unsigned int)v4, 0i64);
    v5 = CPlayer::GetMaxFP(pOne);
    CPlayer::SetFP(pOne, v5, 0);
    v6 = CPlayer::GetMaxSP(pOne);
    CPlayer::SetSP(pOne, v6, 0);
    CPlayer::SendMsg_ResurrectInform(pOne);
    CPlayer::SendMsg_Resurrect(pOne, 0, v13);
    if ( pOne->m_bAfterEffect )
      CPlayer::pc_NuclearAfterEffect(pOne);
    v7 = CNuclearBombMgr::Instance();
    CNuclearBombMgr::CheckNuclearState(v7, pOne);
  }
  return v10 == 0;
}
