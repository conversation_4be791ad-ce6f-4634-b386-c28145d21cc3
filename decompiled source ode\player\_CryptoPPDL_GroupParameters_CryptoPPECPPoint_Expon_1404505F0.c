/*
 * Function: _CryptoPP::DL_GroupParameters_CryptoPP::ECPPoint_::ExponentiateElement_::_1_::dtor$1
 * Address: 0x1404505F0
 */

void __fastcall CryptoPP::DL_GroupParameters_CryptoPP::ECPPoint_::ExponentiateElement_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 164) & 1 )
  {
    *(_DWORD *)(a2 + 164) &= 0xFFFFFFFE;
    CryptoPP::ECPPoint::~ECPPoint(*(CryptoPP::ECPPoint **)(a2 + 200));
  }
}
