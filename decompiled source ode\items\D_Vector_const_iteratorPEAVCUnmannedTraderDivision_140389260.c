/*
 * Function: ??D?$_Vector_const_iterator@PEAVCUnmannedTraderDivisionInfo@@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@QEBAAEBQEAVCUnmannedTraderDivisionInfo@@XZ
 * Address: 0x140389260
 */

CUnmannedTraderDivisionInfo **__fastcall std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator*(std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *this)
{
  return this->_Myptr;
}
