/*
 * Function: ??8Cell@LendItemSheet@@QEBA_NAEBU01@@Z
 * Address: 0x14030F950
 */

bool __fastcall LendItemSheet::Cell::operator==(LendItemSheet::Cell *this, LendItemSheet::Cell *rhs)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // [sp+0h] [bp-18h]@1
  LendItemSheet::Cell *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  return v6->_nStorageCode == rhs->_nStorageCode && v6->_pkItem == rhs->_pkItem;
}
