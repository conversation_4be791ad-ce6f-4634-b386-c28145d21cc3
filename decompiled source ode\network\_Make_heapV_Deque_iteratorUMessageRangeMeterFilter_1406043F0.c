/*
 * Function: ??$_Make_heap@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@_JUMessageRange@MeterFilter@CryptoPP@@@std@@YAXV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@0PEA_JPEAUMessageRange@MeterFilter@CryptoPP@@@Z
 * Address: 0x1406043F0
 */

int __fastcall std::_Make_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,__int64,CryptoPP::MeterFilter::MessageRange>(__int64 a1)
{
  signed __int64 v1; // rax@1
  const void *v2; // rax@3
  signed __int64 v4; // [sp+20h] [bp-E8h]@1
  __int64 v5; // [sp+28h] [bp-E0h]@1
  char v6; // [sp+30h] [bp-D8h]@3
  char v7; // [sp+48h] [bp-C0h]@3
  char v8; // [sp+68h] [bp-A0h]@3
  char *v9; // [sp+88h] [bp-80h]@3
  char v10; // [sp+90h] [bp-78h]@3
  char v11; // [sp+B0h] [bp-58h]@3
  __int64 v12; // [sp+C8h] [bp-40h]@1
  __int64 v13; // [sp+D0h] [bp-38h]@3
  __int64 v14; // [sp+D8h] [bp-30h]@3
  __int64 v15; // [sp+E0h] [bp-28h]@3
  __int64 v16; // [sp+110h] [bp+8h]@1

  v16 = a1;
  v12 = -2i64;
  LODWORD(v1) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-();
  v4 = v1;
  v5 = v1 / 2;
  while ( v5 > 0 )
  {
    --v5;
    v9 = &v8;
    v13 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
            v16,
            (__int64)&v7,
            v5);
    v14 = v13;
    LODWORD(v2) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
    qmemcpy(&v11, v2, 0x18ui64);
    qmemcpy(&v6, &v11, 0x18ui64);
    qmemcpy(&v10, &v6, 0x18ui64);
    v15 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v9);
    std::_Adjust_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,__int64,CryptoPP::MeterFilter::MessageRange>(
      v15,
      v5,
      v4,
      &v10);
    std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  }
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
}
