/*
 * Function: ?start_cashevent@CashItemRemoteStore@@QEAA_NHHHHE@Z
 * Address: 0x1402FB0D0
 */

char __fastcall CashItemRemoteStore::start_cashevent(CashItemRemoteStore *this, int iBegin_TT, int iB30_TT, int iB5_TT, int iEnd_TT, char byEventType)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-108h]@1
  __time32_t Time; // [sp+24h] [bp-E4h]@4
  __time32_t v11; // [sp+44h] [bp-C4h]@4
  __time32_t v12; // [sp+64h] [bp-A4h]@4
  int j; // [sp+74h] [bp-94h]@4
  char Dst; // [sp+88h] [bp-80h]@10
  int v15; // [sp+8Ch] [bp-7Ch]@10
  int v16; // [sp+90h] [bp-78h]@10
  int v17; // [sp+94h] [bp-74h]@10
  int v18; // [sp+98h] [bp-70h]@10
  int v19; // [sp+9Ch] [bp-6Ch]@10
  struct tm Tm; // [sp+C8h] [bp-40h]@10
  int k; // [sp+F4h] [bp-14h]@10
  char v22; // [sp+F8h] [bp-10h]@15
  CashItemRemoteStore *v23; // [sp+110h] [bp+8h]@1
  int v24; // [sp+118h] [bp+10h]@1
  int v25; // [sp+120h] [bp+18h]@1
  int v26; // [sp+128h] [bp+20h]@1

  v26 = iB5_TT;
  v25 = iB30_TT;
  v24 = iBegin_TT;
  v23 = this;
  v6 = &v9;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  _time32(&Time);
  v11 = v24 + Time;
  v12 = iEnd_TT + v26 + v25 + v24 + Time;
  for ( j = 0; j < 3; ++j )
    v23->m_cash_event[j].m_ini.m_bUseCashEvent = j == (unsigned __int8)byEventType;
  v23->m_cash_event[(unsigned __int8)byEventType].m_ini.m_EventTime[0] = v11;
  v23->m_cash_event[(unsigned __int8)byEventType].m_ini.m_EventTime[1] = v12;
  v23->m_cash_event[(unsigned __int8)byEventType].m_event_inform_before[0] = v26 + v25;
  v23->m_cash_event[(unsigned __int8)byEventType].m_event_inform_before[1] = v25;
  memset_0(&Dst, 0, 0x24ui64);
  memset_0(&Tm, 0, 0x24ui64);
  _localtime32_s((struct tm *)&Dst, &v11);
  _localtime32_s(&Tm, &v12);
  v23->m_cash_event[(unsigned __int8)byEventType].m_ini.m_wYear[0] = v19 + 1900;
  v23->m_cash_event[(unsigned __int8)byEventType].m_ini.m_byMonth[0] = v18 + 1;
  v23->m_cash_event[(unsigned __int8)byEventType].m_ini.m_byDay[0] = v17;
  v23->m_cash_event[(unsigned __int8)byEventType].m_ini.m_byHour[0] = v16;
  v23->m_cash_event[(unsigned __int8)byEventType].m_ini.m_byMinute[0] = v15;
  v23->m_cash_event[(unsigned __int8)byEventType].m_ini.m_wYear[1] = Tm.tm_year + 1900;
  v23->m_cash_event[(unsigned __int8)byEventType].m_ini.m_byMonth[1] = Tm.tm_mon + 1;
  v23->m_cash_event[(unsigned __int8)byEventType].m_ini.m_byDay[1] = Tm.tm_mday;
  v23->m_cash_event[(unsigned __int8)byEventType].m_ini.m_byHour[1] = Tm.tm_hour;
  v23->m_cash_event[(unsigned __int8)byEventType].m_ini.m_byMinute[1] = Tm.tm_min;
  for ( k = 0; k < 3; ++k )
  {
    if ( k != (unsigned __int8)byEventType )
    {
      CashItemRemoteStore::Inform_CashEvent_Status_All(v23, k, 5, &v23->m_cash_event[k].m_ini);
      CashItemRemoteStore::Set_CashEvent_Status(v23, k, 1);
    }
  }
  v22 = CashItemRemoteStore::Get_CashEvent_Status(v23, byEventType);
  if ( (unsigned __int8)v22 >= 2u && (unsigned __int8)v22 <= 4u )
    CashItemRemoteStore::Inform_CashEvent_Status_All(
      v23,
      byEventType,
      5,
      &v23->m_cash_event[(unsigned __int8)byEventType].m_ini);
  CashItemRemoteStore::Set_CashEvent_Status(v23, byEventType, 1);
  return 1;
}
