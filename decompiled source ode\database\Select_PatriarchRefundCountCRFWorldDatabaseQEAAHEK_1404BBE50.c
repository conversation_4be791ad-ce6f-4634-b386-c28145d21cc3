/*
 * Function: ?Select_PatriarchRefundCount@CRFWorldDatabase@@QEAAHEKAEAK@Z
 * Address: 0x1404BBE50
 */

signed __int64 __fastcall CRFWorldDatabase::Select_PatriarchRefundCount(CRFWorldDatabase *this, char by<PERSON><PERSON><PERSON><PERSON>, unsigned int dwAvatorSerial, unsigned int *dwCnt)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v7; // [sp+0h] [bp-488h]@1
  void *SQLStmt; // [sp+20h] [bp-468h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-460h]@22
  SQLLEN v10; // [sp+38h] [bp-450h]@22
  __int16 v11; // [sp+44h] [bp-444h]@9
  char Dest; // [sp+60h] [bp-428h]@4
  unsigned __int8 v13; // [sp+464h] [bp-24h]@16
  unsigned __int8 v14; // [sp+465h] [bp-23h]@24
  unsigned __int64 v15; // [sp+470h] [bp-18h]@4
  CRFWorldDatabase *v16; // [sp+490h] [bp+8h]@1
  unsigned int *TargetValue; // [sp+4A8h] [bp+20h]@1

  TargetValue = dwCnt;
  v16 = this;
  v4 = &v7;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v15 = (unsigned __int64)&v7 ^ _security_cookie;
  sprintf(
    &Dest,
    "select\tsum(refund) from\t[dbo].[tbl_patriarch_candidate] where\trace = %d and\taserial = %d ",
    (unsigned __int8)byRaceCode,
    dwAvatorSerial);
  if ( v16->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v16->vfptr, &Dest);
  if ( v16->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v16->vfptr) )
  {
    v11 = SQLExecDirectA_0(v16->m_hStmtSelect, &Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v16->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v11 = SQLFetch_0(v16->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        v13 = 0;
        if ( v11 == 100 )
        {
          v13 = 2;
        }
        else
        {
          SQLStmt = v16->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
          v13 = 1;
        }
        if ( v16->m_hStmtSelect )
          SQLCloseCursor_0(v16->m_hStmtSelect);
        result = v13;
      }
      else
      {
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 1u, -6, TargetValue, 0i64, &v10);
        if ( v11 && v11 != 1 )
        {
          v14 = 0;
          if ( v11 == 100 )
          {
            v14 = 2;
          }
          else
          {
            SQLStmt = v16->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &Dest, "SQLFetch", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
            v14 = 1;
          }
          if ( v16->m_hStmtSelect )
            SQLCloseCursor_0(v16->m_hStmtSelect);
          result = v14;
        }
        else
        {
          if ( v16->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v16->vfptr, "%s Success", &Dest);
          if ( v16->m_hStmtSelect )
            SQLCloseCursor_0(v16->m_hStmtSelect);
          result = 0i64;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v16->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1i64;
  }
  return result;
}
