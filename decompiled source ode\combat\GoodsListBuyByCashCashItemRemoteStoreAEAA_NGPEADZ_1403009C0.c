/*
 * Function: ?GoodsListBuyByCash@CashItemRemoteStore@@AEAA_NGPEAD@Z
 * Address: 0x1403009C0
 */

char __fastcall CashItemRemoteStore::GoodsListBuyByCash(CashItemRemoteStore *this, unsigned __int16 wSock, char *pPacket)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned int v6; // eax@10
  CCashDBWorkManager *v7; // rax@19
  __int64 v8; // [sp+0h] [bp-B8h]@1
  CPlayer *v9; // [sp+20h] [bp-98h]@6
  _param_cash_select v10; // [sp+38h] [bp-80h]@10
  char *v11; // [sp+78h] [bp-40h]@10
  int j; // [sp+80h] [bp-38h]@10
  char v13; // [sp+90h] [bp-28h]@19
  __int64 v14; // [sp+98h] [bp-20h]@4
  unsigned __int64 size; // [sp+A0h] [bp-18h]@19
  unsigned __int64 v16; // [sp+A8h] [bp-10h]@4
  CashItemRemoteStore *v17; // [sp+C0h] [bp+8h]@1
  unsigned __int16 v18; // [sp+C8h] [bp+10h]@1
  char *v19; // [sp+D0h] [bp+18h]@1

  v19 = pPacket;
  v18 = wSock;
  v17 = this;
  v3 = &v8;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = -2i64;
  v16 = (unsigned __int64)&v8 ^ _security_cookie;
  if ( (signed int)wSock < 2532 )
  {
    v9 = &g_Player + wSock;
    if ( v9->m_bLive )
    {
      if ( v9->m_bOper )
      {
        v6 = CPlayerDB::GetCharSerial(&v9->m_Param);
        _param_cash_select::_param_cash_select(&v10, v9->m_pUserDB->m_dwAccountSerial, v6, v18);
        sprintf_s(v10.in_szAcc, 0xDui64, v9->m_pUserDB->m_szAccountID);
        v11 = v19;
        v10.in_bAdjustDiscount = CashItemRemoteStore::is_cde_time(v17);
        v10.in_bOneN_One = CashItemRemoteStore::IsEventTime(v17, 1);
        v10.in_bSetDiscount = CashItemRemoteStore::IsEventTime(v17, 0);
        for ( j = 0; j < 4; ++j )
        {
          if ( v10.in_bSetDiscount )
            v10.in_bySetDiscount[j] = CashItemRemoteStore::GetSetDiscout(v17, j);
          else
            v10.in_bySetDiscount[j] = 0;
        }
        v10.in_bLimited_Sale = CashItemRemoteStore::IsEventTime(v17, 2);
        if ( v10.in_bLimited_Sale )
          v10.in_byLimDiscount = CashItemRemoteStore::GetLimDiscout(v17);
        else
          v10.in_byLimDiscount = 0;
        size = _param_cash_select::size(&v10);
        v7 = CTSingleton<CCashDBWorkManager>::Instance();
        CCashDBWorkManager::PushTask(v7, 0, (char *)&v10, size);
        v13 = 1;
        _param_cash_select::~_param_cash_select(&v10);
        result = v13;
      }
      else
      {
        result = 1;
      }
    }
    else
    {
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
