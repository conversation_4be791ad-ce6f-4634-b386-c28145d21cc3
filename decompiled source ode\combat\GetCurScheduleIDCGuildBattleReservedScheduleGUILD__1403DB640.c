/*
 * Function: ?GetCurScheduleID@CGuildBattleReservedSchedule@GUILD_BATTLE@@QEAAIXZ
 * Address: 0x1403DB640
 */

unsigned int __fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::GetCurScheduleID(GUILD_BATTLE::CGuildBattleReservedSchedule *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int result; // eax@5
  GUILD_BATTLE::CGuildBattleLogger *v4; // rax@7
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleReservedSchedule *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_bDone )
  {
    result = -1;
  }
  else if ( GUILD_BATTLE::CGuildBattleSchedule::IsProc(v6->m_pkSchedule[v6->m_uiCurScheduleInx]) )
  {
    result = GUILD_BATTLE::CGuildBattleSchedule::GetSID(v6->m_pkSchedule[v6->m_uiCurScheduleInx]);
  }
  else
  {
    v4 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v4,
      "CGuildBattleReservedSchedule::GetCurScheduleID() : m_pkSchedule[%u]->IsProc() Invalid State!",
      v6->m_uiCurScheduleInx);
    result = -1;
  }
  return result;
}
