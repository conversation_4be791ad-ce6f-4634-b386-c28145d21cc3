/*
 * Function: ??0CUserDB@@QEAA@XZ
 * Address: 0x14010FB90
 */

void __fastcall CUserDB::CUserDB(CUserDB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@5
  __int64 v4; // [sp+0h] [bp-A8h]@1
  char Dest; // [sp+40h] [bp-68h]@5
  __int64 v6; // [sp+90h] [bp-18h]@4
  unsigned __int64 v7; // [sp+98h] [bp-10h]@4
  CUserDB *v8; // [sp+B0h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = -2i64;
  v7 = (unsigned __int64)&v4 ^ _security_cookie;
  v8->vfptr = (CUserDBVtbl *)&CUserDB::`vftable';
  `vector constructor iterator'(v8->m_RegedList, 0x10Dui64, 3, (void *(__cdecl *)(void *))_REGED::_REGED);
  _AVATOR_DATA::_AVATOR_DATA(&v8->m_AvatorData);
  _AVATOR_DATA::_AVATOR_DATA(&v8->m_AvatorData_bk);
  `vector constructor iterator'(
    v8->m_NotArrangedChar,
    0x45ui64,
    50,
    (void *(__cdecl *)(void *))_NOT_ARRANGED_AVATOR_DB::_NOT_ARRANGED_AVATOR_DB);
  _SYNC_STATE::_SYNC_STATE(&v8->m_ss);
  CMyTimer::CMyTimer(&v8->m_tmrCheckPlayMin);
  _BILLING_INFO::_BILLING_INFO(&v8->m_BillingInfo);
  CRadarItemMgr::CRadarItemMgr(&v8->m_RadarItemMgr);
  v8->m_dwAccountSerial = -1;
  _AVATOR_DATA::InitData(&v8->m_AvatorData);
  v8->m_bActive = 0;
  v8->m_bField = 0;
  v8->m_bChatLock = 0;
  v8->m_bNoneUpdateData = 0;
  v8->m_dwSerial = -1;
  v8->m_idWorld.dwSerial = -1;
  v8->m_gidGlobal.dwSerial = -1;
  v8->m_bCreateTrunkFree = 0;
  if ( !CUserDB::s_logAvatorDB.m_bInit )
  {
    v3 = GetKorLocalTime();
    sprintf(&Dest, "..\\ZoneServerLog\\Systemlog\\AvatorDBError%d.log", v3);
    CLogFile::SetWriteLogFile(&CUserDB::s_logAvatorDB, &Dest, 1, 0, 1, 1);
  }
  _AVATOR_DATA::InitData(&v8->m_AvatorData_bk);
}
