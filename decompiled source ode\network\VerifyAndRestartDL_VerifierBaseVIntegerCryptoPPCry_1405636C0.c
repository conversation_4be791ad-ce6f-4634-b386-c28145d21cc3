/*
 * Function: ?VerifyAndRestart@?$DL_VerifierBase@VInteger@CryptoPP@@@CryptoPP@@UEBA_NAEAVPK_MessageAccumulator@2@@Z
 * Address: 0x1405636C0
 */

char __fastcall CryptoPP::DL_VerifierBase<CryptoPP::Integer>::VerifyAndRestart(__int64 a1, __int64 a2)
{
  CryptoPP::CryptoMaterial *v2; // rax@1
  __int64 *v3; // rax@1
  __int64 v4; // rax@1
  __int64 v5; // rax@1
  unsigned __int64 v6; // rax@1
  __int64 *v7; // rax@1
  __int64 v8; // rax@1
  const void *v9; // rax@1
  __int64 v10; // rax@1
  CryptoPP *v11; // rcx@1
  struct CryptoPP::RandomNumberGenerator *v12; // rax@1
  char v13; // ST30_1@1
  char *v14; // rax@1
  char *v15; // rax@1
  CryptoPP::Sec<PERSON>lock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v17; // [sp+50h] [bp-138h]@1
  __int64 v18; // [sp+68h] [bp-120h]@1
  __int64 v19; // [sp+70h] [bp-118h]@1
  __int64 v20; // [sp+78h] [bp-110h]@1
  __int64 *v21; // [sp+80h] [bp-108h]@1
  CryptoPP::Integer v22; // [sp+88h] [bp-100h]@1
  CryptoPP::Integer v23; // [sp+B0h] [bp-D8h]@1
  char v24; // [sp+D8h] [bp-B0h]@1
  char v25; // [sp+E8h] [bp-A0h]@1
  char v26; // [sp+F0h] [bp-98h]@1
  __int64 v27; // [sp+100h] [bp-88h]@1
  __int64 v28; // [sp+108h] [bp-80h]@1
  __int64 v29; // [sp+110h] [bp-78h]@1
  __int64 *v30; // [sp+118h] [bp-70h]@1
  __int64 v31; // [sp+120h] [bp-68h]@1
  char *v32; // [sp+128h] [bp-60h]@1
  __int64 v33; // [sp+130h] [bp-58h]@1
  __int64 v34; // [sp+138h] [bp-50h]@1
  unsigned __int64 v35; // [sp+140h] [bp-48h]@1
  char *v36; // [sp+148h] [bp-40h]@1
  __int64 v37; // [sp+150h] [bp-38h]@1
  unsigned __int64 v38; // [sp+158h] [bp-30h]@1
  unsigned __int64 v39; // [sp+160h] [bp-28h]@1
  __int64 v40; // [sp+168h] [bp-20h]@1
  __int64 v41; // [sp+190h] [bp+8h]@1
  __int64 v42; // [sp+198h] [bp+10h]@1

  v42 = a2;
  v41 = a1;
  v27 = -2i64;
  v28 = *(_QWORD *)(a1 + 8);
  LODWORD(v2) = (*(int (__fastcall **)(signed __int64))(v28 + 32))(a1 + 8);
  CryptoPP::CryptoMaterial::DoQuickSanityCheck(v2);
  v18 = v42;
  LODWORD(v3) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v41 + 136i64))(v41);
  v21 = v3;
  LODWORD(v4) = CryptoPP::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::Integer>>::GetAbstractGroupParameters(v41 + 16);
  v19 = v4;
  v29 = *(_QWORD *)(v41 + 16);
  LODWORD(v5) = (*(int (__fastcall **)(signed __int64))(v29 + 8))(v41 + 16);
  v20 = v5;
  LODWORD(v6) = CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::DL_PublicKey<CryptoPP::Integer>>::MessageRepresentativeLength(v41);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v17,
    v6);
  LODWORD(v7) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v41 + 144i64))(v41);
  v30 = v7;
  LODWORD(v8) = CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::DL_PublicKey<CryptoPP::Integer>>::MessageRepresentativeBitLength(v41);
  v31 = v8;
  v32 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v17);
  v33 = *(_QWORD *)v41;
  LODWORD(v9) = (*(int (__fastcall **)(__int64, char *))(v33 + 152))(v41, &v24);
  qmemcpy(&v26, v9, 0x10ui64);
  LODWORD(v10) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v18 + 144i64))(v18);
  v34 = v10;
  v35 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v18 + 8));
  v36 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v18 + 8));
  v12 = CryptoPP::NullRNG(v11);
  v37 = *v30;
  v13 = *(_BYTE *)(v18 + 184);
  (*(void (__fastcall **)(__int64 *, struct CryptoPP::RandomNumberGenerator *, char *, unsigned __int64))(v37 + 48))(
    v30,
    v12,
    v36,
    v35);
  *(_BYTE *)(v18 + 184) = 1;
  v38 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v17);
  v14 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v17);
  CryptoPP::Integer::Integer(&v23, (const unsigned __int8 *)v14, v38, 0);
  v39 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v18 + 80));
  v15 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v18 + 80));
  CryptoPP::Integer::Integer(&v22, (const unsigned __int8 *)v15, v39, 0);
  v40 = *v21;
  v25 = (*(int (__fastcall **)(__int64 *, __int64, __int64, CryptoPP::Integer *))(v40 + 8))(v21, v19, v20, &v23);
  CryptoPP::Integer::~Integer(&v22);
  CryptoPP::Integer::~Integer(&v23);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v17);
  return v25;
}
