/*
 * Function: ?cut_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@HPEAGKKPEAD@Z
 * Address: 0x14023B400
 */

void __fastcall CMgrAvatorItemHistory::cut_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pOreItem, int nOreNum, unsigned __int16 *pwCuttingResBuffer, unsigned int dwCostDalant, unsigned int dwNewDalant, char *pszFileName)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v10; // [sp+0h] [bp-68h]@1
  unsigned int v11; // [sp+20h] [bp-48h]@4
  unsigned int v12; // [sp+28h] [bp-40h]@4
  char *v13; // [sp+30h] [bp-38h]@4
  char *v14; // [sp+38h] [bp-30h]@4
  _base_fld *v15; // [sp+40h] [bp-28h]@4
  int na; // [sp+48h] [bp-20h]@4
  _base_fld *v17; // [sp+50h] [bp-18h]@7
  CMgrAvatorItemHistory *v18; // [sp+70h] [bp+8h]@1
  int v19; // [sp+88h] [bp+20h]@1

  v19 = nOreNum;
  v18 = this;
  v8 = &v10;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  sData[0] = 0;
  v15 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pOreItem->m_byTableCode, pOreItem->m_wItemIndex);
  v14 = v18->m_szCurTime;
  v13 = v18->m_szCurDate;
  v12 = dwNewDalant;
  v11 = dwCostDalant;
  sprintf(sBuf, "CUT: %s * %d pay(D:%u) $D:%u [%s %s]\r\n", v15->m_strCode, (unsigned int)v19);
  strcat_0(sData, sBuf);
  for ( na = 0; na < GetMaxResKind(); ++na )
  {
    if ( (signed int)pwCuttingResBuffer[na] > 0 )
    {
      v17 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 18, na);
      sprintf(sBuf, "\t+ %s_%d\r\n", v17->m_strCode, pwCuttingResBuffer[na]);
      strcat_0(sData, sBuf);
    }
  }
  CMgrAvatorItemHistory::WriteFile(v18, pszFileName, sData);
}
