/*
 * Function: ?OpenDungeon@CDarkHoleChannel@@QEAAXPEAV_dh_quest_setup@@HPEAVCPlayer@@PEAVCDarkHole@@@Z
 * Address: 0x1402678F0
 */

void __fastcall CDarkHoleChannel::OpenDungeon(CDarkHoleChannel *this, _dh_quest_setup *pQuestSetup, int nLayerIndex, CPlayer *pOpener, CDarkHole *pHoleObj)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char *v7; // rax@4
  __int64 v8; // [sp+0h] [bp-88h]@1
  CPlayer *out_ppMember; // [sp+30h] [bp-58h]@5
  char v10; // [sp+74h] [bp-14h]@5
  _MULTI_BLOCK *pMB; // [sp+78h] [bp-10h]@6
  CDarkHoleChannel *v12; // [sp+90h] [bp+8h]@1
  _dh_quest_setup *v13; // [sp+98h] [bp+10h]@1
  int v14; // [sp+A0h] [bp+18h]@1
  CPlayer *v15; // [sp+A8h] [bp+20h]@1

  v15 = pOpener;
  v14 = nLayerIndex;
  v13 = pQuestSetup;
  v12 = this;
  v5 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  CDarkHoleChannel::Init(v12);
  v12->m_pQuestSetup = v13;
  v12->m_wLayerIndex = v14;
  v12->m_dwOpenerSerial = v15->m_dwObjSerial;
  v7 = CPlayerDB::GetCharNameW(&v15->m_Param);
  strcpy_0(v12->m_wszOpenerName, v7);
  W2M(v12->m_wszOpenerName, v12->m_aszOpenerName, 0x11u);
  v12->m_nOpenerDegree = v15->m_pUserDB->m_byUserDgr;
  v12->m_nOpenerSubDegree = v15->m_pUserDB->m_bySubDgr;
  v12->m_MissionMgr.pCurMssionPtr = v13->pStartMissionSetup;
  v12->m_dwChannelSerial = CDarkHoleChannel::s_dwChannelSerialCounter++;
  v12->m_pHoleObj = pHoleObj;
  v12->m_dwHoleSerial = pHoleObj->m_dwObjSerial;
  if ( v13->bPartyOnly )
  {
    v10 = CPlayer::_GetPartyMemberInCircle(v15, &out_ppMember, 8, 1);
    v12->m_pPartyMng = v15->m_pPartyMgr;
  }
  v12->m_pLayerSet = &v13->pUseMap->m_ls[v14];
  pMB = &v13->pUseMap->m_mb[v13->dwMonRepIndex];
  _LAYER_SET::ActiveLayer(v12->m_pLayerSet, pMB);
  CDarkHoleChannel::CreateMonster(v12);
  CDarkHoleChannel::AddMonster(v12);
  CDarkHoleChannel::ChangeMonster(v12);
  CDarkHoleChannel::ShareItemToMonster(v12);
  v12->m_dwQuestStartTime = timeGetTime();
}
