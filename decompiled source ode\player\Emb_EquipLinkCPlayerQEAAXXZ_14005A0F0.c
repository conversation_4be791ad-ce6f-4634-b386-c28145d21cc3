/*
 * Function: ?Emb_EquipLink@CPlayer@@QEAAXXZ
 * Address: 0x14005A0F0
 */

void __fastcall CPlayer::Emb_EquipLink(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-68h]@1
  int nLinkIndex; // [sp+20h] [bp-48h]@4
  char *v5; // [sp+28h] [bp-40h]@7
  char pbyStorageCode; // [sp+34h] [bp-34h]@8
  _STORAGE_LIST::_db_con *v7; // [sp+48h] [bp-20h]@8
  unsigned __int16 v8; // [sp+50h] [bp-18h]@10
  CPlayer *v9; // [sp+70h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( nLinkIndex = 0; nLinkIndex < 50; ++nLinkIndex )
  {
    v5 = &v9->m_Param.m_QLink[nLinkIndex].byLinkIndex;
    if ( (unsigned __int8)*v5 != 255 )
    {
      v7 = CPlayerDB::GetPtrItemStorage(&v9->m_Param, *((_WORD *)v5 + 1), &pbyStorageCode);
      if ( v7 )
      {
        v8 = 0;
        v8 = v7->m_byStorageIndex | (unsigned __int16)((unsigned __int8)pbyStorageCode << 8);
        CUserDB::Update_LinkBoardSlot(v9->m_pUserDB, nLinkIndex, 4, v8);
      }
      else
      {
        CPlayerDB::PopLink(&v9->m_Param, nLinkIndex);
        CUserDB::Update_LinkBoardSlot(v9->m_pUserDB, nLinkIndex, -1, 0xFFFFu);
      }
    }
  }
}
