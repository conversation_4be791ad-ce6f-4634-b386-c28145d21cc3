/*
 * Function: ?GetSymmetricEncryptionAlgorithm@?$DL_ObjectImpl@V?$DL_DecryptorBase@UECPPoint@CryptoPP@@@CryptoPP@@U?$DL_CryptoSchemeOptions@U?$ECIES@VECP@CryptoPP@@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@2@$0A@@CryptoPP@@U?$DL_Keys_EC@VECP@CryptoPP@@@2@V?$DL_KeyAgreementAlgorithm_DH@UECPPoint@CryptoPP@@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@2@@2@V?$DL_KeyDerivationAlgorithm_P1363@UECPPoint@CryptoPP@@$0A@V?$P1363_KDF2@VSHA1@CryptoPP@@@2@@2@V?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@2@@2@V?$DL_PrivateKey_EC@VECP@CryptoPP@@@2@@CryptoPP@@MEBAAEBVDL_SymmetricEncryptionAlgorithm@2@XZ
 * Address: 0x140455D20
 */

CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *__fastcall CryptoPP::DL_ObjectImpl<CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>,CryptoPP::DL_CryptoSchemeOptions<CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,0>,CryptoPP::DL_Keys_EC<CryptoPP::ECP>,CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::ECPPoint,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>>,CryptoPP::DL_KeyDerivationAlgorithm_P1363<CryptoPP::ECPPoint,0,CryptoPP::P1363_KDF2<CryptoPP::SHA1>>,CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>,CryptoPP::DL_PrivateKey_EC<CryptoPP::ECP>>::GetSymmetricEncryptionAlgorithm(CryptoPP::DL_ObjectImpl<CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>,CryptoPP::DL_CryptoSchemeOptions<CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0>,0>,CryptoPP::DL_Keys_EC<CryptoPP::ECP>,CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::ECPPoint,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0> >,CryptoPP::DL_KeyDerivationAlgorithm_P1363<CryptoPP::ECPPoint,0,CryptoPP::P1363_KDF2<CryptoPP::SHA1> >,CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> >,CryptoPP::DL_PrivateKey_EC<CryptoPP::ECP> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CryptoPP::Singleton<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>,CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> >,0> *v3; // rax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> > v7; // [sp+21h] [bp-17h]@4

  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset(&v7, 0, sizeof(v7));
  CryptoPP::Singleton<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>,CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>,0>::Singleton<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>,CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>,0>(
    (CryptoPP::Singleton<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>,CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> >,0> *)&v6,
    v7);
  return CryptoPP::Singleton<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>,CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>,0>::Ref(v3);
}
