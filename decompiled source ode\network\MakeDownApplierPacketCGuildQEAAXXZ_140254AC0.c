/*
 * Function: ?MakeDownApplierPacket@CGuild@@QEAAXXZ
 * Address: 0x140254AC0
 */

void __usercall CGuild::MakeDownApplierPacket(CGuild *this@<rcx>, double a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@8
  char *v5; // rax@8
  __int64 v6; // [sp+0h] [bp-B8h]@1
  int v7; // [sp+20h] [bp-98h]@4
  void *Dst; // [sp+28h] [bp-90h]@4
  int j; // [sp+30h] [bp-88h]@4
  _guild_applier_info *v10; // [sp+38h] [bp-80h]@7
  CPlayer *v11; // [sp+40h] [bp-78h]@8
  unsigned __int8 Src; // [sp+54h] [bp-64h]@8
  char v13; // [sp+74h] [bp-44h]@8
  int v14; // [sp+94h] [bp-24h]@8
  size_t Size; // [sp+A8h] [bp-10h]@8
  CGuild *v16; // [sp+C0h] [bp+8h]@1

  v16 = this;
  v2 = &v6;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v16->m_DownPacket_Applier->wDataSize = 0;
  v7 = 0;
  Dst = v16->m_DownPacket_Applier->sData;
  memcpy_0(Dst, &v16->m_nApplierNum, 1ui64);
  Dst = (char *)Dst + 1;
  ++v7;
  for ( j = 0; j < 32; ++j )
  {
    v10 = &v16->m_ApplierData[j];
    if ( _guild_applier_info::IsFill(v10) )
    {
      v11 = v10->pPlayer;
      v4 = CPlayerDB::GetCharNameW(&v11->m_Param);
      Src = strlen_0(v4);
      memcpy_0(Dst, &Src, 1ui64);
      Dst = (char *)Dst + 1;
      ++v7;
      Size = Src;
      v5 = CPlayerDB::GetCharNameW(&v11->m_Param);
      memcpy_0(Dst, v5, Size);
      Dst = (char *)Dst + Src;
      v7 += Src;
      memcpy_0(Dst, &v11->m_dwObjSerial, 4ui64);
      Dst = (char *)Dst + 4;
      v7 += 4;
      v13 = ((int (__fastcall *)(CPlayer *))v11->vfptr->GetLevel)(v11);
      memcpy_0(Dst, &v13, 1ui64);
      Dst = (char *)Dst + 1;
      ++v7;
      CPlayerDB::GetPvPPoint(&v11->m_Param);
      v14 = (signed int)floor(a2);
      memcpy_0(Dst, &v14, 4ui64);
      Dst = (char *)Dst + 4;
      v7 += 4;
      memcpy_0(Dst, &v10->dwApplyTime, 4ui64);
      Dst = (char *)Dst + 4;
      v7 += 4;
    }
  }
  v16->m_DownPacket_Applier->wDataSize = v7;
}
