/*
 * Function: ?ct_loot_upgrade@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140291D10
 */

bool __fastcall ct_loot_upgrade(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  int v4; // eax@8
  __int64 v5; // [sp+0h] [bp-118h]@1
  CMapData *pMap; // [sp+30h] [bp-E8h]@10
  unsigned __int16 wLayerIndex; // [sp+38h] [bp-E0h]@10
  float *pStdPos; // [sp+40h] [bp-D8h]@10
  bool bHide; // [sp+48h] [bp-D0h]@10
  unsigned int dwIndex; // [sp+50h] [bp-C8h]@6
  _ItemUpgrade_fld *v11; // [sp+58h] [bp-C0h]@9
  unsigned __int16 *v12; // [sp+60h] [bp-B8h]@9
  _STORAGE_LIST::_db_con pItem; // [sp+78h] [bp-A0h]@10
  unsigned __int16 *v14; // [sp+B8h] [bp-60h]@13
  _STORAGE_LIST::_db_con v15; // [sp+C8h] [bp-50h]@15
  CPlayer *pOwner; // [sp+120h] [bp+8h]@1

  pOwner = pOne;
  v1 = &v5;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( pOwner )
  {
    for ( dwIndex = 0; ; ++dwIndex )
    {
      v4 = CItemUpgradeTable::GetSize(&stru_1799C69D8);
      if ( (signed int)dwIndex >= v4 )
        break;
      v11 = CItemUpgradeTable::GetRecord(&stru_1799C69D8, dwIndex);
      v12 = (unsigned __int16 *)CRecordData::GetRecordByHash((CRecordData *)&unk_1799C6AA0 + 18, v11->m_strCode, 2, 5);
      if ( v12 )
      {
        _STORAGE_LIST::_db_con::_db_con(&pItem);
        pItem.m_byTableCode = 18;
        pItem.m_wItemIndex = *v12;
        pItem.m_dwDur = 99i64;
        pItem.m_dwLv = 0xFFFFFFF;
        bHide = 1;
        pStdPos = pOwner->m_fCurPos;
        wLayerIndex = pOwner->m_wMapLayerIndex;
        pMap = pOwner->m_pCurMap;
        if ( !CreateItemBox(&pItem, pOwner, 0xFFFFFFFF, 0, 0i64, 2, pMap, wLayerIndex, pOwner->m_fCurPos, 1) )
          return 1;
      }
    }
    v14 = (unsigned __int16 *)CRecordData::GetRecordByHash((CRecordData *)&unk_1799C6AA0 + 11, "immmm04", 2, 5);
    if ( v14 )
    {
      _STORAGE_LIST::_db_con::_db_con(&v15);
      v15.m_byTableCode = 11;
      v15.m_wItemIndex = *v14;
      v15.m_dwDur = GetItemDurPoint(11, dwIndex);
      v15.m_dwLv = 0xFFFFFFF;
      bHide = 1;
      pStdPos = pOwner->m_fCurPos;
      wLayerIndex = pOwner->m_wMapLayerIndex;
      pMap = pOwner->m_pCurMap;
      if ( CreateItemBox(&v15, pOwner, 0xFFFFFFFF, 0, 0i64, 2, pMap, wLayerIndex, pOwner->m_fCurPos, 1) )
        result = 1;
      else
        result = 1;
    }
    else
    {
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
