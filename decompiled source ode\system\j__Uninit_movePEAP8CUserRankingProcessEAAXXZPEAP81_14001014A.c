/*
 * Function: j_??$_Uninit_move@PEAP8CUserRankingProcess@@EAAXXZPEAP81@EAAXXZV?$allocator@P8CUserRankingProcess@@EAAXXZ@std@@U_Undefined_move_tag@3@@std@@YAPEAP8CUserRankingProcess@@EAAXXZPEAP81@EAAXXZ00AEAV?$allocator@P8CUserRankingProcess@@EAAXXZ@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14001014A
 */

void (__cdecl **__fastcall std::_Uninit_move<void (CUserRankingProcess::**)(void),void (CUserRankingProcess::**)(void),std::allocator<void (CUserRankingProcess::*)(void)>,std::_Undefined_move_tag>(void (__cdecl **_First)(CUserRankingProcess *this), void (__cdecl **_Last)(CUserRankingProcess *this), void (__cdecl **_Dest)(CUserRankingProcess *this), std::allocator<void (__cdecl CUserRankingProcess::*)(void)> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6))(CUserRankingProcess *this)
{
  return std::_Uninit_move<void (CUserRankingProcess::**)(void),void (CUserRankingProcess::**)(void),std::allocator<void (CUserRankingProcess::*)(void)>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
