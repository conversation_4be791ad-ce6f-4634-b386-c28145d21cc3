/*
 * Function: ?GetMachineInfo@AutoMineMachine@@QEAAXPEAU_DB_LOAD_AUTOMINE_MACHINE@@@Z
 * Address: 0x1402D1250
 */

void __fastcall AutoMineMachine::GetMachineInfo(AutoMineMachine *this, _DB_LOAD_AUTOMINE_MACHINE *pInfo)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  int nP; // [sp+20h] [bp-28h]@4
  int nS; // [sp+24h] [bp-24h]@6
  TInvenSlot<_INVENKEY> *v7; // [sp+28h] [bp-20h]@9
  void *Src; // [sp+30h] [bp-18h]@11
  AutoMineMachine *v9; // [sp+50h] [bp+8h]@1
  _DB_LOAD_AUTOMINE_MACHINE *v10; // [sp+58h] [bp+10h]@1

  v10 = pInfo;
  v9 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  pInfo->byCollisionType = v9->m_byCollisionType;
  pInfo->byRace = v9->m_byRace;
  pInfo->dwGuildSerial = v9->m_pOwnerGuild->m_dwSerial;
  pInfo->dwGMasterSerial = v9->m_pOwnerGuild->m_MasterData.dwSerial;
  pInfo->bWorking = v9->m_bRunning;
  pInfo->bySelectedOre = v9->m_bySelectedOre;
  pInfo->dwBatteryGage = v9->m_Battery.m_nCurGage;
  pInfo->bySlotCnt = 0;
  for ( nP = 0; nP < 2; ++nP )
  {
    for ( nS = 0; nS < 40; ++nS )
    {
      v7 = TInventory<_INVENKEY>::get_slot(&v9->m_Inven, nP, nS);
      if ( v7 && !TInvenSlot<_INVENKEY>::is_empty(v7) )
      {
        Src = TInvenSlot<_INVENKEY>::get_pitem(v7);
        if ( Src )
        {
          v10->slot[v10->bySlotCnt].nLumpIndex = nP;
          memcpy_0(&v10->slot[v10->bySlotCnt].item, Src, 4ui64);
          v10->slot[v10->bySlotCnt++].nOverlapNum = TInvenSlot<_INVENKEY>::get_overlapnum(v7);
        }
      }
    }
  }
}
