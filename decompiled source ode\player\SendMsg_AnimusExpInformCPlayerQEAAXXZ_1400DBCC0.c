/*
 * Function: ?SendMsg_AnimusExpInform@CPlayer@@QEAAXXZ
 * Address: 0x1400DBCC0
 */

void __fastcall CPlayer::SendMsg_AnimusExpInform(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-88h]@1
  char szMsg[2]; // [sp+38h] [bp-50h]@7
  unsigned __int64 v5; // [sp+3Ah] [bp-4Eh]@7
  char pbyType; // [sp+64h] [bp-24h]@7
  char v7; // [sp+65h] [bp-23h]@7
  CPlayer *v8; // [sp+90h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v8->m_pRecalledAnimusChar )
  {
    if ( v8->m_pRecalledAnimusItem )
    {
      *(_WORD *)szMsg = v8->m_pRecalledAnimusItem->m_wSerial;
      v5 = v8->m_pRecalledAnimusChar->m_dwExp;
      pbyType = 22;
      v7 = 11;
      CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, szMsg, 0xAu);
    }
  }
}
