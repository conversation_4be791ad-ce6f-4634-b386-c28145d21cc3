/*
 * Function: ?CombineMessageAndShiftRegister@?$CFB_EncryptionTemplate@V?$AbstractPolicyHolder@VCFB_CipherAbstractPolicy@CryptoPP@@V?$SimpleKeyedTransformation@VStreamTransformation@CryptoPP@@@2@@CryptoPP@@@CryptoPP@@EEAAXPEAE0PEBE_K@Z
 * Address: 0x1405868D0
 */

void __fastcall CryptoPP::CFB_EncryptionTemplate<CryptoPP::AbstractPolicyHolder<CryptoPP::CFB_CipherAbstractPolicy,CryptoPP::SimpleKeyedTransformation<CryptoPP::StreamTransformation>>>::CombineMessageAndShiftRegister(unsigned __int8 *a1, void *a2, CryptoPP *a3, unsigned __int8 *a4, unsigned __int8 *a5)
{
  void *v5; // [sp+48h] [bp+10h]@1
  CryptoPP *v6; // [sp+50h] [bp+18h]@1

  v6 = a3;
  v5 = a2;
  CryptoPP::xorbuf(a3, a4, a5, (unsigned __int64)a4);
  qmemcpy(v5, (const void *)v6, (unsigned __int64)a5);
}
