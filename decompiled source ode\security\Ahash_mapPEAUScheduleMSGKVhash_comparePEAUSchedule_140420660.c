/*
 * Function: ??A?$hash_map@PEAUScheduleMSG@@KV?$hash_compare@PEAUScheduleMSG@@U?$less@PEAUScheduleMSG@@@std@@@stdext@@V?$allocator@U?$pair@QEAUScheduleMSG@@K@std@@@std@@@stdext@@QEAAAEAKAEBQEAUScheduleMSG@@@Z
 * Address: 0x140420660
 */

unsigned int *__fastcall stdext::hash_map<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::operator[](stdext::hash_map<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > > *this, ScheduleMSG *const *_Keyval)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  std::pair<ScheduleMSG * const,unsigned long> *v4; // rax@5
  __int64 v6; // [sp+0h] [bp-D8h]@1
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> result; // [sp+28h] [bp-B0h]@4
  bool v8; // [sp+44h] [bp-94h]@4
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> v9; // [sp+48h] [bp-90h]@4
  std::pair<ScheduleMSG * const,unsigned long> v10; // [sp+60h] [bp-78h]@5
  unsigned int _Val2; // [sp+70h] [bp-68h]@5
  std::pair<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0>,bool> v12; // [sp+78h] [bp-60h]@5
  unsigned int *v13; // [sp+98h] [bp-40h]@6
  __int64 v14; // [sp+A0h] [bp-38h]@4
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v15; // [sp+A8h] [bp-30h]@4
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *_Right; // [sp+B0h] [bp-28h]@4
  std::pair<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0>,bool> *v17; // [sp+B8h] [bp-20h]@5
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *__that; // [sp+C0h] [bp-18h]@5
  stdext::hash_map<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > > *v19; // [sp+E0h] [bp+8h]@1
  ScheduleMSG **_Keyvala; // [sp+E8h] [bp+10h]@1

  _Keyvala = (ScheduleMSG **)_Keyval;
  v19 = this;
  v2 = &v6;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v14 = -2i64;
  stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>::lower_bound(
    (stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> > *)&v19->_Myfirstiter,
    &result,
    _Keyval);
  v15 = stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>::end(
          (stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> > *)&v19->_Myfirstiter,
          &v9);
  _Right = (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)v15;
  v8 = std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Const_iterator<0>::operator==(
         (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)&result._Mycont,
         (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)&v15->_Mycont);
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::~_Iterator<0>(&v9);
  if ( v8 )
  {
    _Val2 = 0;
    std::pair<ScheduleMSG * const,unsigned long>::pair<ScheduleMSG * const,unsigned long>(&v10, _Keyvala, &_Val2);
    v17 = stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>::insert(
            (stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> > *)&v19->_Myfirstiter,
            &v12,
            v4);
    __that = (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *)v17;
    std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator=(
      &result,
      &v17->first);
    std::pair<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,bool>::~pair<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,bool>(&v12);
  }
  v13 = &std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator*(&result)->second;
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::~_Iterator<0>(&result);
  return v13;
}
