/*
 * Function: ?send_changed_packet@AutominePersonal@@QEAAXH@Z
 * Address: 0x1402DC930
 */

void __fastcall AutominePersonal::send_changed_packet(AutominePersonal *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@5
  __int64 v5; // [sp+0h] [bp-58h]@1
  char pbyType; // [sp+34h] [bp-24h]@5
  char v7; // [sp+35h] [bp-23h]@5
  AutominePersonal *v8; // [sp+60h] [bp+8h]@1
  int dwClientIndex; // [sp+68h] [bp+10h]@1

  dwClientIndex = n;
  v8 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v8->m_bChanged )
  {
    v8->m_bChanged = 0;
    pbyType = 14;
    v7 = 55;
    v4 = _personal_amine_mineore_zocl::size(&v8->m_changed_packet);
    CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, (char *)&v8->m_changed_packet, v4);
    _personal_amine_mineore_zocl::clear(&v8->m_changed_packet);
  }
}
