/*
 * Function: ?SetBillingInfo@_WAIT_ENTER_ACCOUNT@@QEAAXFPEADJPEAU_SYSTEMTIME@@@Z
 * Address: 0x140207AC0
 */

void __fastcall _WAIT_ENTER_ACCOUNT::SetBillingInfo(_WAIT_ENTER_ACCOUNT *this, __int16 iType, char *szCMS, int lRemainTime, _SYSTEMTIME *pstEndDate)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  _WAIT_ENTER_ACCOUNT *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v5 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -*********;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v8->m_BillingInfo.iType = iType;
  v8->m_BillingInfo.lRemainTime = lRemainTime;
  memcpy_0(v8->m_BillingInfo.szCMS, szCMS, 7ui64);
  memcpy_0(&v8->m_BillingInfo.stEndDate, pstEndDate, 0x10ui64);
}
