/*
 * Function: luaV_settable
 * Address: 0x14053EB20
 */

int __fastcall luaV_settable(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  __int64 v4; // r14@1
  __int64 v5; // r15@1
  __int64 v6; // rbx@1
  __int64 v7; // rsi@1
  signed int v8; // er12@1
  __int64 v9; // rbp@3
  __int64 v10; // rax@3
  __int64 v11; // r13@3
  __int64 v12; // rcx@4
  __int64 v13; // rax@6
  __int64 v14; // r8@6
  __int64 v15; // r9@6
  __int64 v16; // rdi@6
  __int64 v17; // rax@7
  __int64 v18; // rax@11

  v4 = a4;
  v5 = a3;
  v6 = a2;
  v7 = a1;
  v8 = 0;
  while ( *(_DWORD *)(v6 + 8) != 5 )
  {
    LODWORD(v18) = luaT_gettmbyobj(v7, v6, 1);
    v16 = v18;
    if ( !*(_DWORD *)(v18 + 8) )
      luaG_typeerror(v7, v6, (__int64)"index");
LABEL_13:
    if ( *(_DWORD *)(v16 + 8) == 6 )
    {
      LODWORD(v17) = sub_14053E980(v7, v16, v6, v5, v4);
      return v17;
    }
    ++v8;
    v6 = v16;
    if ( v8 >= 100 )
      luaG_runerror(v7, (__int64)"loop in settable", v14, v15);
  }
  v9 = *(_QWORD *)v6;
  LODWORD(v10) = luaH_set(v7, *(_QWORD *)v6, v5);
  v11 = v10;
  if ( !*(_DWORD *)(v10 + 8) )
  {
    v12 = *(_QWORD *)(v9 + 16);
    if ( v12 )
    {
      if ( !(*(_BYTE *)(v12 + 10) & 2) )
      {
        LODWORD(v13) = luaT_gettm(v12, 1, *(_QWORD *)(*(_QWORD *)(v7 + 32) + 304i64));
        v16 = v13;
        if ( v13 )
          goto LABEL_13;
      }
    }
  }
  *(_QWORD *)v11 = *(_QWORD *)v4;
  LODWORD(v17) = *(_DWORD *)(v4 + 8);
  *(_DWORD *)(v11 + 8) = v17;
  if ( *(_DWORD *)(v4 + 8) >= 4 )
  {
    v17 = *(_QWORD *)v4;
    if ( *(_BYTE *)(*(_QWORD *)v4 + 9i64) & 3 )
    {
      if ( *(_BYTE *)(v9 + 9) & 4 )
        LODWORD(v17) = luaC_barrierback(v7, v9);
    }
  }
  return v17;
}
