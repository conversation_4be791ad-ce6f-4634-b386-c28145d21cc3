/*
 * Function: _std::vector_CMoveMapLimitInfo_____ptr64_std::allocator_CMoveMapLimitInfo_____ptr64___::_Insert_n_::_1_::catch$1
 * Address: 0x1403AA3E0
 */

void __fastcall __noreturn std::vector_CMoveMapLimitInfo_____ptr64_std::allocator_CMoveMapLimitInfo_____ptr64___::_Insert_n_::_1_::catch_1(__int64 a1, __int64 a2)
{
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Destroy(
    *(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > **)(a2 + 160),
    (CMoveMapLimitInfo **)(*(_QWORD *)(*(_QWORD *)(a2 + 168) + 16i64) + 8i64 * *(_QWORD *)(a2 + 176)),
    (CMoveMapLimitInfo **)(*(_QWORD *)(*(_QWORD *)(a2 + 160) + 24i64) + 8i64 * *(_QWORD *)(a2 + 176)));
  CxxThrowException_0(0i64, 0i64);
}
