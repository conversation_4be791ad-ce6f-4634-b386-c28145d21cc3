/*
 * Function: ??0?$_List_val@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@std@@QEAA@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@1@@Z
 * Address: 0x14022A440
 */

void __fastcall std::_List_val<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_List_val<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>(std::_List_val<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > > *this, __int64 _Al)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<std::pair<int const ,CNationSettingFactory *> > v4; // al@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  std::allocator<std::pair<int const ,CNationSettingFactory *> > *v7; // [sp+28h] [bp-10h]@4
  std::_List_val<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > > *v8; // [sp+40h] [bp+8h]@1
  std::allocator<std::pair<int const ,CNationSettingFactory *> > *__formal; // [sp+48h] [bp+10h]@1

  __formal = (std::allocator<std::pair<int const ,CNationSettingFactory *> > *)_Al;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = (std::allocator<std::pair<int const ,CNationSettingFactory *> > *)&v6;
  std::allocator<std::pair<int const,CNationSettingFactory *>>::allocator<std::pair<int const,CNationSettingFactory *>>(
    (std::allocator<std::pair<int const ,CNationSettingFactory *> > *)&v6,
    (std::allocator<std::pair<int const ,CNationSettingFactory *> > *)_Al);
  std::_List_ptr<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_List_ptr<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>(
    (std::_List_ptr<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > > *)&v8->_Myfirstiter,
    v4);
  std::allocator<std::pair<int const,CNationSettingFactory *>>::allocator<std::pair<int const,CNationSettingFactory *>>(
    &v8->_Alval,
    __formal);
}
