/*
 * Function: j_??0?$_Ranit@VCMoveMapLimitRightInfo@@_JPEBV1@AEBV1@@std@@QEAA@XZ
 * Address: 0x1400109F6
 */

void __fastcall std::_Ranit<CMoveMapLimitRightInfo,__int64,CMoveMapLimitRightInfo const *,CMoveMapLimitRightInfo const &>::_Ranit<CMoveMapLimitRightInfo,__int64,CMoveMapLimitRightInfo const *,CMoveMapLimitRightInfo const &>(std::_Ranit<CMoveMapLimitRightInfo,__int64,CMoveMapLimitRightInfo const *,CMoveMapLimitRightInfo const &> *this)
{
  std::_Ranit<CMoveMapLimitRightInfo,__int64,CMoveMapLimitRightInfo const *,CMoveMapLimitRightInfo const &>::_Ranit<CMoveMapLimitRightInfo,__int64,CMoveMapLimitRightInfo const *,CMoveMapLimitRightInfo const &>(this);
}
