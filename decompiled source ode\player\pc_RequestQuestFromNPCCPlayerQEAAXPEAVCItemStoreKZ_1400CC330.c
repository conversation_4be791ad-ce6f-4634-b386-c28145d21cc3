/*
 * Function: ?pc_RequestQuestFromNPC@CPlayer@@QEAAXPEAVCItemStore@@K@Z
 * Address: 0x1400CC330
 */

void __usercall CPlayer::pc_RequestQuestFromNPC(CPlayer *this@<rcx>, CItemStore *pStore@<rdx>, unsigned int dwNPCQuestIndex@<r8d>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  float *v6; // rax@4
  int v7; // eax@5
  __int64 v8; // [sp+0h] [bp-38h]@1
  char *pszEventCode; // [sp+20h] [bp-18h]@7
  bool v10; // [sp+28h] [bp-10h]@8
  int v11; // [sp+2Ch] [bp-Ch]@5
  CPlayer *v12; // [sp+40h] [bp+8h]@1
  CItemStore *v13; // [sp+48h] [bp+10h]@1
  unsigned int dwNPCQuestIndexa; // [sp+50h] [bp+18h]@1

  dwNPCQuestIndexa = dwNPCQuestIndex;
  v13 = pStore;
  v12 = this;
  v4 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v6 = CItemStore::GetStorePos(pStore);
  GetSqrt(v12->m_fCurPos, v6);
  if ( a4 <= 80.0 )
  {
    v11 = v13->m_byNpcRaceCode;
    v7 = CPlayerDB::GetRaceCode(&v12->m_Param);
    if ( v11 == v7 || v13->m_byNpcRaceCode == 255 )
    {
      pszEventCode = CItemStore::GetNpcCode(v13);
      if ( pszEventCode )
      {
        v10 = CPlayer::Emb_CreateNPCQuest(v12, pszEventCode, dwNPCQuestIndexa);
        CPlayer::SendMsg_ResultNpcQuest(v12, v10);
      }
    }
  }
}
