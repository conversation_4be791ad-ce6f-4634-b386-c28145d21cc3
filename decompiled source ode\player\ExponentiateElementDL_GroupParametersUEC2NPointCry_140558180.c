/*
 * Function: ?ExponentiateElement@?$DL_GroupParameters@UEC2NPoint@CryptoPP@@@CryptoPP@@UEBA?AUEC2NPoint@2@AEBU32@AEBVInteger@2@@Z
 * Address: 0x140558180
 */

CryptoPP::EC2NPoint *__fastcall CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>::ExponentiateElement(__int64 a1, CryptoPP::EC2NPoint *a2, __int64 a3, __int64 a4)
{
  CryptoPP::EC2NPoint v5; // [sp+30h] [bp-58h]@1
  int v6; // [sp+68h] [bp-20h]@1
  __int64 v7; // [sp+70h] [bp-18h]@1
  __int64 v8; // [sp+90h] [bp+8h]@1
  CryptoPP::EC2NPoint *v9; // [sp+98h] [bp+10h]@1
  __int64 v10; // [sp+A0h] [bp+18h]@1
  __int64 v11; // [sp+A8h] [bp+20h]@1

  v11 = a4;
  v10 = a3;
  v9 = a2;
  v8 = a1;
  v7 = -2i64;
  v6 = 0;
  CryptoPP::EC2NPoint::EC2NPoint(&v5);
  (*(void (__fastcall **)(__int64, CryptoPP::EC2NPoint *, __int64, __int64))(*(_QWORD *)v8 + 160i64))(v8, &v5, v10, v11);
  CryptoPP::EC2NPoint::EC2NPoint(v9, &v5);
  v6 |= 1u;
  CryptoPP::EC2NPoint::~EC2NPoint(&v5);
  return v9;
}
