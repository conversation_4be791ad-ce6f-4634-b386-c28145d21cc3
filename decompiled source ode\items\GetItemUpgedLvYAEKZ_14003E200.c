/*
 * Function: ?GetItemUpgedLv@@YAEK@Z
 * Address: 0x14003E200
 */

char __fastcall GetItemUpgedLv(unsigned int dwLvBit)
{
  char *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char v4; // [sp+0h] [bp-18h]@1
  int j; // [sp+4h] [bp-14h]@6
  char v6; // [sp+8h] [bp-10h]@8
  unsigned int v7; // [sp+20h] [bp+8h]@1

  v7 = dwLvBit;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 += 4;
  }
  if ( v7 )
  {
    v4 = 0;
    for ( j = 0; j < 7; ++j )
    {
      v6 = (v7 >> 4 * j) & 0xF;
      if ( ((v7 >> 4 * j) & 0xF) == 15 )
        break;
      ++v4;
    }
    result = v4;
  }
  else
  {
    result = 0;
  }
  return result;
}
