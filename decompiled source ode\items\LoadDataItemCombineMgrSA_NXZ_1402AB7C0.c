/*
 * Function: ?LoadData@ItemCombineMgr@@SA_NXZ
 * Address: 0x1402AB7C0
 */

char __cdecl ItemCombineMgr::LoadData()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v3; // [sp+0h] [bp-E8h]@1
  char szMessage; // [sp+40h] [bp-A8h]@4
  unsigned __int64 v5; // [sp+D0h] [bp-18h]@4

  v0 = &v3;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  v5 = (unsigned __int64)&v3 ^ _security_cookie;
  if ( CRecordData::ReadRecord_Ex(
         &ItemCombineMgr::ms_tbl_ItemCombine,
         ".\\script\\CombineTable.dat",
         ".\\script\\CombineTable2.dat",
         0x3C8u,
         &szMessage) )
  {
    if ( CRecordData::ReadRecord(
           &ItemCombineMgr::ms_tbl_ItemCombine_Link_Stuff,
           ".\\script\\LinkedStuff.dat",
           0x1944u,
           &szMessage) )
    {
      if ( CRecordData::ReadRecord(
             &ItemCombineMgr::ms_tbl_ItemCombine_Link_Result,
             ".\\script\\LinkedResult.dat",
             0x1944u,
             &szMessage) )
      {
        result = 1;
      }
      else
      {
        MyMessageBox("DatafileInit", &szMessage);
        result = 0;
      }
    }
    else
    {
      MyMessageBox("DatafileInit", &szMessage);
      result = 0;
    }
  }
  else
  {
    MyMessageBox("DatafileInit", &szMessage);
    result = 0;
  }
  return result;
}
