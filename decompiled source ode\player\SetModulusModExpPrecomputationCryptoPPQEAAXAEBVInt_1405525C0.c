/*
 * Function: ?SetModulus@ModExpPrecomputation@CryptoPP@@QEAAXAEBVInteger@2@@Z
 * Address: 0x1405525C0
 */

void __fastcall CryptoPP::ModExpPrecomputation::SetModulus(CryptoPP::ModExpPrecomputation *this, const struct CryptoPP::Integer *a2)
{
  CryptoPP::MontgomeryRepresentation *v2; // [sp+28h] [bp-20h]@1
  __int64 v3; // [sp+38h] [bp-10h]@2
  CryptoPP::ModExpPrecomputation *v4; // [sp+50h] [bp+8h]@1
  struct CryptoPP::Integer *v5; // [sp+58h] [bp+10h]@1

  v5 = (struct CryptoPP::Integer *)a2;
  v4 = this;
  v2 = (CryptoPP::MontgomeryRepresentation *)operator new(0xD0ui64);
  if ( v2 )
    v3 = CryptoPP::MontgomeryRepresentation::MontgomeryRepresentation(v2, v5);
  else
    v3 = 0i64;
  CryptoPP::member_ptr<CryptoPP::MontgomeryRepresentation>::reset(&v4->m_mr, v3);
}
