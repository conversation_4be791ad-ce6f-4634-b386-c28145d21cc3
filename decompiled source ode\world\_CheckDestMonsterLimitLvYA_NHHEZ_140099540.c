/*
 * Function: ?_CheckDestMonsterLimitLv@@YA_NHHE@Z
 * Address: 0x140099540
 */

bool __fastcall _CheckDestMonsterLimitLv(int MyLevel, int iDstLevel, char byID)
{
  int *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  int v6; // [sp+0h] [bp-18h]@1
  int v7; // [sp+20h] [bp+8h]@1

  v7 = MyLevel;
  v3 = &v6;
  for ( i = 4i64; i; --i )
  {
    *v3 = -858993460;
    ++v3;
  }
  if ( byID == 1 )
  {
    v6 = v7 - iDstLevel;
    if ( v7 - iDstLevel <= 3 )
      result = v6 >= -10;
    else
      result = 0;
  }
  else
  {
    result = 1;
  }
  return result;
}
