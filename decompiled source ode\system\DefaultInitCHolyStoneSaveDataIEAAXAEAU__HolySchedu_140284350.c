/*
 * Function: ?DefaultInit@CHolyStoneSaveData@@IEAAXAEAU__HolyScheduleNode@CHolyScheduleData@@@Z
 * Address: 0x140284350
 */

void __fastcall CHolyStoneSaveData::DefaultInit(CHolyStoneSaveData *this, CHolyScheduleData::__HolyScheduleNode *ScheduleNode)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CHolyStoneSaveData *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  v5->m_nSceneCode = 0;
  v5->m_dwPassTimeInScene = 0;
  v5->m_nStartStoneHP = 1000000;
  v5->m_nHolyMasterRace = -1;
  v5->m_nDestroyStoneRace = -1;
  v5->m_byNumOfTime = 1;
  v5->m_dwCumPlayerNum = 0;
  v5->m_dwCumCount = 0;
  v5->m_wStartYear = 0;
  v5->m_byStartMonth = 0;
  v5->m_byStartDay = 0;
  v5->m_byStartHour = 0;
  v5->m_byStartMin = 0;
  v5->m_dwDestroyerSerial = -1;
  v5->m_eDestroyerState = 3;
  for ( j = 0; j < 3; ++j )
    v5->m_nStoneHP_Buffer[j] = 1000000;
  v5->m_dwTerm[0] = ScheduleNode->m_nSceneTime[5]
                  - (ScheduleNode->m_nSceneTime[2]
                   + ScheduleNode->m_nSceneTime[0]
                   + ScheduleNode->m_nSceneTime[1]);
  v5->m_dwTerm[1] = ScheduleNode->m_nSceneTime[3];
  v5->m_dwOreRemainAmount = -1;
  v5->m_dwOreTotalAmount = -1;
  v5->m_dwDestroyerGuildSerial = -1;
  v5->m_byOreTransferCount = 0;
  v5->m_dwOreTransferAmount = 0;
}
