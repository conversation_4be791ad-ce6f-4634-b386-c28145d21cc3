/*
 * Function: ?GetItemDurPoint@@YAHHH@Z
 * Address: 0x140036B40
 */

signed __int64 __fastcall GetItemDurPoint(int nTableCode, int nIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  __int64 v5; // [sp+0h] [bp-88h]@1
  CRecordData *v6; // [sp+20h] [bp-68h]@4
  _base_fld *v7; // [sp+28h] [bp-60h]@5
  _base_fld *v8; // [sp+30h] [bp-58h]@8
  _base_fld *v9; // [sp+38h] [bp-50h]@11
  _base_fld *v10; // [sp+40h] [bp-48h]@14
  _base_fld *v11; // [sp+48h] [bp-40h]@17
  _base_fld *v12; // [sp+50h] [bp-38h]@20
  _base_fld *v13; // [sp+58h] [bp-30h]@23
  _base_fld *v14; // [sp+60h] [bp-28h]@26
  _base_fld *v15; // [sp+68h] [bp-20h]@31
  int v16; // [sp+70h] [bp-18h]@4
  int v17; // [sp+90h] [bp+8h]@1

  v17 = nTableCode;
  v2 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = &s_ptblItemData[v17];
  v16 = v17;
  switch ( v17 )
  {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      v7 = CRecordData::GetRecord(v6, nIndex);
      if ( !v7 )
        goto LABEL_33;
      result = *(_DWORD *)&v7[5].m_strCode[0];
      break;
    case 6:
      v8 = CRecordData::GetRecord(v6, nIndex);
      if ( !v8 )
        goto LABEL_33;
      result = *(_DWORD *)&v8[9].m_strCode[0];
      break;
    case 11:
      v9 = CRecordData::GetRecord(v6, nIndex);
      if ( !v9 )
        goto LABEL_33;
      result = *(_DWORD *)&v9[4].m_strCode[40];
      break;
    case 10:
      v10 = CRecordData::GetRecord(v6, nIndex);
      if ( !v10 )
        goto LABEL_33;
      result = *(_DWORD *)&v10[6].m_strCode[28];
      break;
    case 16:
      v11 = CRecordData::GetRecord(v6, nIndex);
      if ( !v11 )
        goto LABEL_33;
      result = *(_DWORD *)&v11[4].m_strCode[28];
      break;
    case 25:
      v12 = CRecordData::GetRecord(v6, nIndex);
      if ( !v12 )
        goto LABEL_33;
      result = *(_DWORD *)&v12[6].m_strCode[40];
      break;
    case 26:
      v13 = CRecordData::GetRecord(v6, nIndex);
      if ( !v13 )
        goto LABEL_33;
      result = (unsigned int)(signed int)ffloor(*(float *)&v13[5].m_strCode[60]);
      break;
    case 27:
      v14 = CRecordData::GetRecord(v6, nIndex);
      if ( !v14 )
        goto LABEL_33;
      result = v14[5].m_dwIndex;
      break;
    case 24:
      result = 0i64;
      break;
    case 15:
      result = 0i64;
      break;
    case 33:
      v15 = CRecordData::GetRecord(v6, nIndex);
      if ( !v15 )
        goto LABEL_33;
      result = *(_DWORD *)&v15[5].m_strCode[44];
      break;
    default:
LABEL_33:
      result = 1i64;
      break;
  }
  return result;
}
