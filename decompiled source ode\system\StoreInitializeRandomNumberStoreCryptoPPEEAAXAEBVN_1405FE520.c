/*
 * Function: ?StoreInitialize@RandomNumberStore@CryptoPP@@EEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x1405FE520
 */

void __fastcall CryptoPP::RandomNumberStore::StoreInitialize(CryptoPP::RandomNumberStore *this, const struct CryptoPP::NameValuePairs *a2)
{
  int v2; // [sp+20h] [bp-18h]@1
  CryptoPP::RandomNumberStore *v3; // [sp+40h] [bp+8h]@1
  const struct CryptoPP::NameValuePairs *v4; // [sp+48h] [bp+10h]@1

  v4 = a2;
  v3 = this;
  CryptoPP::NameValuePairs::GetRequiredParameter<CryptoPP::RandomNumberGenerator *>(
    a2,
    "RandomNumberStore",
    "RandomNumberGeneratorPointer",
    &this->m_rng);
  CryptoPP::NameValuePairs::GetRequiredIntParameter(
    (CryptoPP::NameValuePairs *)v4,
    "RandomNumberStore",
    "RandomNumberStoreSize",
    &v2);
  v3->m_length = v2;
}
