/*
 * Function: ?Init@_PCBANG_FAVOR_ITEM_DB_BASE@@QEAAXXZ
 * Address: 0x140077DF0
 */

void __fastcall _PCBANG_FAVOR_ITEM_DB_BASE::Init(_PCBANG_FAVOR_ITEM_DB_BASE *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  _PCBANG_FAVOR_ITEM_DB_BASE *v4; // [sp+20h] [bp+8h]@1

  v4 = this;
  v1 = &j;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  for ( j = 0; j < 50; ++j )
    v4->lnUID[j] = -1i64;
}
