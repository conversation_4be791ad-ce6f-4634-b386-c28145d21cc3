/*
 * Function: j_?reward_add_money@CMgrAvatorItemHistory@@QEAAXHPEADKKKK0@Z
 * Address: 0x14000C4CD
 */

void __fastcall CMgrAvatorItemHistory::reward_add_money(CMgrAvatorItemHistory *this, int n, char *psz<PERSON><PERSON>e, unsigned int dwAddDalant, unsigned int dwAddGold, unsigned int dwSumDalant, unsigned int dwSumGold, char *pszFileName)
{
  CMgrAvatorItemHistory::reward_add_money(
    this,
    n,
    pszClause,
    dwAddDalant,
    dwAddGold,
    dwSumDalant,
    dwSumGold,
    pszFileName);
}
