/*
 * Function: ?Flip@CGuildBattleReservedScheduleListManager@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403CD8F0
 */

void __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Flip(GUILD_BATTLE::CGuildBattleReservedScheduleListManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CReservedGuildScheduleDayGroup *v4; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = v5->m_pkToday;
  v5->m_pkToday = v5->m_pkTomorrow;
  v5->m_pkTomorrow = v4;
  GUILD_BATTLE::CReservedGuildScheduleDayGroup::Flip(v5->m_pkToday);
  GUILD_BATTLE::CReservedGuildScheduleDayGroup::Clear(v5->m_pkTomorrow);
}
