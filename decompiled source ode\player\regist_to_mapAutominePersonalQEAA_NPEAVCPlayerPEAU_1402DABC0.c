/*
 * Function: ?regist_to_map@AutominePersonal@@QEAA_NPEAVCPlayer@@PEAU_db_con@_STORAGE_LIST@@EKM@Z
 * Address: 0x1402DABC0
 */

char __fastcall AutominePersonal::regist_to_map(AutominePersonal *this, CPlayer *pOne, _STORAGE_LIST::_db_con *pDstItem, char byDummyIndex, unsigned int dwObjSerial, float fDelayProf)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  bool v9; // al@13
  double v10; // xmm0_8@13
  int v11; // eax@19
  char *v12; // rax@23
  CPlayer *v13; // rcx@23
  __int64 v14; // [sp+0h] [bp-B8h]@1
  char *szFileName; // [sp+20h] [bp-98h]@23
  _res_dummy *v16; // [sp+30h] [bp-88h]@8
  int v17; // [sp+38h] [bp-80h]@8
  double v18; // [sp+40h] [bp-78h]@13
  int v19; // [sp+48h] [bp-70h]@13
  _object_create_setdata pData; // [sp+58h] [bp-60h]@17
  _base_fld *v21; // [sp+88h] [bp-30h]@17
  _base_fld *v22; // [sp+90h] [bp-28h]@17
  int *v23; // [sp+98h] [bp-20h]@23
  char *v24; // [sp+A0h] [bp-18h]@23
  AutominePersonal *v25; // [sp+C0h] [bp+8h]@1
  CPlayer *v26; // [sp+C8h] [bp+10h]@1
  _STORAGE_LIST::_db_con *v27; // [sp+D0h] [bp+18h]@1
  char v28; // [sp+D8h] [bp+20h]@1

  v28 = byDummyIndex;
  v27 = pDstItem;
  v26 = pOne;
  v25 = this;
  v6 = &v14;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( v25->m_bInstalled )
  {
    result = 0;
  }
  else if ( (unsigned __int8)byDummyIndex < pOne->m_pCurMap->m_nResDumNum )
  {
    v16 = &pOne->m_pCurMap->m_pResDummy[(unsigned __int8)byDummyIndex];
    v17 = CMapData::GetResDummySector(pOne->m_pCurMap, (unsigned __int8)byDummyIndex, pOne->m_fCurPos);
    if ( v17 == -1 )
    {
      result = 0;
    }
    else if ( v16 && _res_dummy::GetQualityGrade(v16) != 2 )
    {
      v9 = _BILLING_INFO::IsPcBangType(&v26->m_pUserDB->m_BillingInfo);
      v10 = (double)_res_dummy::GetDelay(&v26->m_pCurMap->m_pResDummy[(unsigned __int8)v28], v17, v9) * 1.2;
      v25->m_dwDelay = (signed int)floor(v10);
      TimeLimitMgr::GetPlayerPenalty(qword_1799CA2D0, v26->m_id.wIndex);
      v18 = v10;
      v19 = 0;
      if ( v10 != 0.0 )
        v19 = (signed int)floor(1.0 / v18);
      if ( fDelayProf > 1.0 )
        v25->m_dwDelay = (signed int)ffloor((float)(signed int)v25->m_dwDelay / fDelayProf);
      v25->m_dwDelaySec = v25->m_dwDelay / 0x3E8;
      v25->m_dwDelay *= v19;
      _object_create_setdata::_object_create_setdata(&pData);
      v21 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v27->m_byTableCode, v27->m_wItemIndex);
      v22 = v21;
      if ( v21 )
      {
        v11 = CPlayerDB::GetLevel(&v26->m_Param);
        if ( v11 >= *(_DWORD *)&v22[4].m_strCode[52] )
        {
          pData.m_pRecordSet = v21;
          pData.m_pMap = v26->m_pCurMap;
          pData.m_nLayerIndex = v26->m_wMapLayerIndex;
          CMapData::GetRandPosInRange(v26->m_pCurMap, v26->m_fCurPos, 10, pData.m_fStartPos);
          if ( CGameObject::Create((CGameObject *)&v25->vfptr, &pData) )
          {
            v27->m_bLock = 1;
            v25->m_bMove = 0;
            v25->m_dwObjSerial = dwObjSerial;
            v25->m_pItem = v27;
            v25->m_pOwner = v26;
            v25->m_dwNextMineTime = -1;
            v25->m_dwChangeSendTime = timeGetTime() + v25->m_dwDelay + 3000;
            v25->m_nMaxHP = *(_DWORD *)&v22[5].m_strCode[44];
            v25->m_wItemSerial = v27->m_wSerial;
            v25->m_byFilledSlotCnt = _STORAGE_LIST::GetNumUseCon((_STORAGE_LIST *)&v26->m_Param.m_dbPersonalAmineInven.m_nListNum);
            AutominePersonal::send_installed(v25);
            v25->m_bInstalled = 1;
            memset_0(v25->m_dwMineCount, 0, 0x3Cui64);
            v23 = &v25->m_pOwner->m_Param.m_dbPersonalAmineInven.m_nListNum;
            v12 = v25->m_pOwner->m_szItemHistoryFileName;
            v13 = v25->m_pOwner;
            v24 = &v25->m_pItem->m_bLoad;
            szFileName = v12;
            CMgrAvatorItemHistory::personal_amine_install(
              &CPlayer::s_MgrItemHistory,
              v24[1],
              *(_WORD *)(v24 + 3),
              &v13->m_Param.m_dbPersonalAmineInven,
              v12);
            result = 1;
          }
          else
          {
            CLogFile::Write(&v25->m_logSysErr, "regist_to_map()::Failed CGameObject::Create(...)");
            result = 0;
          }
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
