/*
 * Function: j_??$unchecked_fill_n@PEAPEAVCUnmannedTraderDivisionInfo@@_KPEAV1@@stdext@@YAXPEAPEAVCUnmannedTraderDivisionInfo@@_KAEBQEAV1@@Z
 * Address: 0x14000C35B
 */

void __fastcall stdext::unchecked_fill_n<CUnmannedTraderDivisionInfo * *,unsigned __int64,CUnmannedTraderDivisionInfo *>(CUnmannedTraderDivisionInfo **_First, unsigned __int64 _Count, CUnmannedTraderDivisionInfo *const *_Val)
{
  stdext::unchecked_fill_n<CUnmannedTraderDivisionInfo * *,unsigned __int64,CUnmannedTraderDivisionInfo *>(
    _First,
    _Count,
    _Val);
}
