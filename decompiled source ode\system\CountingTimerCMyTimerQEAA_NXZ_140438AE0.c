/*
 * Function: ?CountingTimer@CMyTimer@@QEAA_NXZ
 * Address: 0x140438AE0
 */

char __fastcall CMyTimer::CountingTimer(CMyTimer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  DWORD v5; // [sp+20h] [bp-18h]@6
  int v6; // [sp+24h] [bp-14h]@6
  CMyTimer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7->m_bOper )
  {
    v5 = timeGetTime();
    v6 = v5 - v7->m_dwTickOld;
    if ( v6 <= v7->m_nTickTerm )
    {
      if ( v6 < 0 )
        v7->m_dwTickOld = 0;
      result = 0;
    }
    else
    {
      v7->m_dwTickOld = v5;
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
