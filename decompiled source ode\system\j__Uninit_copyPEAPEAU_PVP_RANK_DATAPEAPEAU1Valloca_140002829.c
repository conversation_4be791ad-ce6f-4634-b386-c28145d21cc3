/*
 * Function: j_??$_Uninit_copy@PEAPEAU_PVP_RANK_DATA@@PEAPEAU1@V?$allocator@PEAU_PVP_RANK_DATA@@@std@@@std@@YAPEAPEAU_PVP_RANK_DATA@@PEAPEAU1@00AEAV?$allocator@PEAU_PVP_RANK_DATA@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140002829
 */

_PVP_RANK_DATA **__fastcall std::_Uninit_copy<_PVP_RANK_DATA * *,_PVP_RANK_DATA * *,std::allocator<_PVP_RANK_DATA *>>(_PVP_RANK_DATA **_First, _PVP_RANK_DATA **_Last, _PVP_RANK_DATA **_Dest, std::allocator<_PVP_RANK_DATA *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<_PVP_RANK_DATA * *,_PVP_RANK_DATA * *,std::allocator<_PVP_RANK_DATA *>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
