/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAVCLogTypeDBTask@@_KPEAV1@V?$allocator@PEAVCLogTypeDBTask@@@std@@@stdext@@YAXPEAPEAVCLogTypeDBTask@@_KAEBQEAV1@AEAV?$allocator@PEAVCLogTypeDBTask@@@std@@@Z
 * Address: 0x140002F40
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CLogTypeDBTask * *,unsigned __int64,CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(CLogTypeDBTask **_First, unsigned __int64 _Count, CLogTypeDBTask *const *_Val, std::allocator<CLogTypeDBTask *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CLogTypeDBTask * *,unsigned __int64,CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(
    _First,
    _Count,
    _<PERSON>,
    _<PERSON>);
}
