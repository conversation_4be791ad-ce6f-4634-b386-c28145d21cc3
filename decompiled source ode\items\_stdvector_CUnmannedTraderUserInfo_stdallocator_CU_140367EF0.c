/*
 * Function: _std::vector_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo___::erase_::_1_::dtor$1
 * Address: 0x140367EF0
 */

void __fastcall std::vector_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo___::erase_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::~_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(*(std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > **)(a2 + 96));
}
