/*
 * Function: ?insert@?$_Hash@V?$_Hmap_traits@PEAUScheduleMSG@@KV?$hash_compare@PEAUScheduleMSG@@U?$less@PEAUScheduleMSG@@@std@@@stdext@@V?$allocator@U?$pair@QEAUScheduleMSG@@K@std@@@std@@$0A@@stdext@@@stdext@@QEAA?AU?$pair@V?$_Iterator@$0A@@?$list@U?$pair@QEAUScheduleMSG@@K@std@@V?$allocator@U?$pair@QEAUScheduleMSG@@K@std@@@2@@std@@_N@std@@AEBU?$pair@QEAUScheduleMSG@@K@4@@Z
 * Address: 0x140421AD0
 */

std::pair<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0>,bool> *__fastcall stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>::insert(stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> > *this, std::pair<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0>,bool> *result, std::pair<ScheduleMSG * const,unsigned long> *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v5; // rax@4
  std::pair<ScheduleMSG * const,unsigned long> *v6; // rdx@4
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v7; // rax@9
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v8; // rax@10
  stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> *v9; // rax@11
  std::pair<ScheduleMSG * const,unsigned long> *v10; // rdx@11
  ScheduleMSG *const *v11; // rax@11
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v12; // rax@13
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v13; // rax@15
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v14; // rax@16
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v15; // rax@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v16; // rax@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v17; // rax@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v18; // rax@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v19; // rax@21
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v20; // rax@22
  ScheduleMSG *const *v21; // rax@28
  unsigned __int64 v22; // rax@28
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v23; // rax@28
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v24; // rax@29
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v25; // rax@30
  stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> *v26; // rax@30
  std::pair<ScheduleMSG * const,unsigned long> *v27; // rdx@30
  std::pair<ScheduleMSG * const,unsigned long> *v28; // rdx@30
  ScheduleMSG *const *v29; // rax@30
  std::pair<ScheduleMSG * const,unsigned long> *v30; // rdx@30
  stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> *v31; // rax@31
  std::pair<ScheduleMSG * const,unsigned long> *v32; // rdx@31
  ScheduleMSG *const *v33; // rax@31
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v35; // rax@35
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v36; // rax@36
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v37; // rax@37
  __int64 v38; // [sp+0h] [bp-348h]@1
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> _Val1; // [sp+48h] [bp-300h]@4
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> v40; // [sp+78h] [bp-2D0h]@4
  unsigned __int64 _Pos; // [sp+98h] [bp-2B0h]@9
  unsigned __int64 v42; // [sp+A0h] [bp-2A8h]@11
  unsigned __int64 j; // [sp+A8h] [bp-2A0h]@14
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> v44; // [sp+B8h] [bp-290h]@13
  unsigned __int64 v45; // [sp+D8h] [bp-270h]@28
  char v46; // [sp+E0h] [bp-268h]@6
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *resulta; // [sp+F8h] [bp-250h]@6
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> v48; // [sp+100h] [bp-248h]@9
  bool v49; // [sp+118h] [bp-230h]@13
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> v50; // [sp+120h] [bp-228h]@13
  char v51; // [sp+138h] [bp-210h]@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v52; // [sp+150h] [bp-1F8h]@18
  char v53; // [sp+158h] [bp-1F0h]@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v54; // [sp+170h] [bp-1D8h]@18
  char v55; // [sp+178h] [bp-1D0h]@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v56; // [sp+190h] [bp-1B8h]@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> v57; // [sp+198h] [bp-1B0h]@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> v58; // [sp+1B0h] [bp-198h]@18
  bool v59; // [sp+1C8h] [bp-180h]@21
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> v60; // [sp+1D0h] [bp-178h]@21
  bool v61; // [sp+1E8h] [bp-160h]@23
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> v62; // [sp+1F0h] [bp-158h]@23
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> v63; // [sp+208h] [bp-140h]@28
  bool _Val2; // [sp+220h] [bp-128h]@33
  char v65; // [sp+228h] [bp-120h]@35
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v66; // [sp+240h] [bp-108h]@35
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> v67; // [sp+248h] [bp-100h]@35
  bool v68; // [sp+260h] [bp-E8h]@39
  int v69; // [sp+264h] [bp-E4h]@4
  __int64 v70; // [sp+268h] [bp-E0h]@4
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v71; // [sp+270h] [bp-D8h]@6
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v72; // [sp+278h] [bp-D0h]@9
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *__that; // [sp+280h] [bp-C8h]@9
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v74; // [sp+288h] [bp-C0h]@13
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *_Right; // [sp+290h] [bp-B8h]@13
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v76; // [sp+298h] [bp-B0h]@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v77; // [sp+2A0h] [bp-A8h]@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v78; // [sp+2A8h] [bp-A0h]@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v79; // [sp+2B0h] [bp-98h]@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > > *v80; // [sp+2B8h] [bp-90h]@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v81; // [sp+2C0h] [bp-88h]@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v82; // [sp+2C8h] [bp-80h]@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v83; // [sp+2D0h] [bp-78h]@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v84; // [sp+2D8h] [bp-70h]@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v85; // [sp+2E0h] [bp-68h]@18
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v86; // [sp+2E8h] [bp-60h]@21
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *v87; // [sp+2F0h] [bp-58h]@21
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v88; // [sp+2F8h] [bp-50h]@23
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *v89; // [sp+300h] [bp-48h]@23
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v90; // [sp+308h] [bp-40h]@28
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v91; // [sp+310h] [bp-38h]@28
  ScheduleMSG **_Keyval2; // [sp+318h] [bp-30h]@30
  ScheduleMSG **v93; // [sp+320h] [bp-28h]@31
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v94; // [sp+328h] [bp-20h]@35
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v95; // [sp+330h] [bp-18h]@35
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *v96; // [sp+338h] [bp-10h]@35
  stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> > *v97; // [sp+350h] [bp+8h]@1
  std::pair<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0>,bool> *v98; // [sp+358h] [bp+10h]@1
  stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> *v99; // [sp+360h] [bp+18h]@1

  v99 = (stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> *)_Val;
  v98 = result;
  v97 = this;
  v3 = &v38;
  for ( i = 208i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v70 = -2i64;
  v69 = 0;
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::_Iterator<0>(&_Val1);
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::_Iterator<0>(&v40);
  v5 = stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>::size(v97);
  v6 = (std::pair<ScheduleMSG * const,unsigned long> *)(v5 % 4);
  if ( v97->_Maxidx <= v5 / 4 )
  {
    if ( std::vector<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>::size(&v97->_Vec)
       - 1 > v97->_Maxidx )
    {
      if ( v97->_Mask < v97->_Maxidx )
        v97->_Mask = 2 * v97->_Mask + 1;
    }
    else
    {
      v97->_Mask = 2
                 * std::vector<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>::size(&v97->_Vec)
                 - 3;
      resulta = (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *)&v46;
      v71 = stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>::end(
              v97,
              (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *)&v46);
      std::vector<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>::resize(
        &v97->_Vec,
        v97->_Mask + 2,
        v71);
    }
    _Pos = v97->_Maxidx - (v97->_Mask >> 1) - 1;
    v7 = std::vector<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>::operator[](
           &v97->_Vec,
           _Pos);
    v72 = stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>::_Get_iter_from_vec(
            v97,
            &v48,
            v7);
    __that = v72;
    std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator=(
      &_Val1,
      v72);
    std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::~_Iterator<0>(&v48);
    while ( 1 )
    {
      v8 = std::vector<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>::operator[](
             &v97->_Vec,
             _Pos + 1);
      if ( !std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Const_iterator<0>::operator!=(
              (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)&v8->_Mycont,
              (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)&_Val1._Mycont) )
        break;
      v9 = (stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> *)std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator*(&_Val1);
      v11 = stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>::_Kfn(
              v9,
              v10);
      v42 = v97->_Mask & stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>::operator()(&v97->comp, v11);
      if ( v42 == _Pos )
      {
        std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator++(&_Val1);
      }
      else
      {
        std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::_Iterator<0>(
          &v44,
          &_Val1);
        v74 = stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>::end(
                v97,
                &v50);
        _Right = (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)v74;
        v12 = std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator++(&v44);
        v49 = std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Const_iterator<0>::operator!=(
                (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)&v12->_Mycont,
                _Right);
        std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::~_Iterator<0>(&v50);
        if ( v49 )
        {
          for ( j = _Pos; ; --j )
          {
            v13 = std::vector<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>::operator[](
                    &v97->_Vec,
                    j);
            if ( !std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Const_iterator<0>::operator==(
                    (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)&v13->_Mycont,
                    (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)&_Val1._Mycont) )
              break;
            v14 = std::vector<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>::operator[](
                    &v97->_Vec,
                    j);
            std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator=(
              v14,
              &v44);
            if ( !j )
              break;
          }
          v52 = (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *)&v51;
          v54 = (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *)&v53;
          v56 = (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *)&v55;
          std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::_Iterator<0>(
            (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *)&v51,
            &v44);
          v76 = v15;
          v77 = v15;
          std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::_Iterator<0>(
            v54,
            &_Val1);
          v78 = v16;
          v79 = v16;
          v80 = &v97->_List;
          v81 = stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>::end(
                  v97,
                  v56);
          std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Splice(
            &v97->_List,
            v81,
            v80,
            v79,
            v77,
            0i64,
            0);
          v82 = stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>::end(
                  v97,
                  &v57);
          v83 = v82;
          v17 = std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator--(v82);
          std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator=(
            &_Val1,
            v17);
          std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::~_Iterator<0>(&v57);
          v84 = stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>::end(
                  v97,
                  &v58);
          v85 = v84;
          v18 = std::vector<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>::operator[](
                  &v97->_Vec,
                  v97->_Maxidx + 1);
          std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator=(
            v18,
            v85);
          std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::~_Iterator<0>(&v58);
        }
        for ( j = v97->_Maxidx; _Pos < j; --j )
        {
          v86 = stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>::end(
                  v97,
                  &v60);
          v87 = (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)v86;
          v19 = std::vector<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>::operator[](
                  &v97->_Vec,
                  j);
          v59 = std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Const_iterator<0>::operator!=(
                  (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)&v19->_Mycont,
                  v87);
          std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::~_Iterator<0>(&v60);
          if ( v59 )
            break;
          v20 = std::vector<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>::operator[](
                  &v97->_Vec,
                  j);
          std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator=(
            v20,
            &_Val1);
        }
        v88 = stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>::end(
                v97,
                &v62);
        v89 = (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)v88;
        v61 = std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Const_iterator<0>::operator==(
                (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)&v44._Mycont,
                (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)&v88->_Mycont);
        std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::~_Iterator<0>(&v62);
        if ( v61 )
        {
          std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::~_Iterator<0>(&v44);
          break;
        }
        std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator=(
          &_Val1,
          &v44);
        std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::~_Iterator<0>(&v44);
      }
    }
    ++v97->_Maxidx;
  }
  v21 = stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>::_Kfn(
          v99,
          v6);
  v22 = stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>::_Hashval(
          v97,
          v21);
  v45 = v22;
  v23 = std::vector<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>::operator[](
          &v97->_Vec,
          v22 + 1);
  v90 = stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>::_Get_iter_from_vec(
          v97,
          &v63,
          v23);
  v91 = v90;
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator=(
    &_Val1,
    v90);
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::~_Iterator<0>(&v63);
  while ( 1 )
  {
    v24 = std::vector<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>::operator[](
            &v97->_Vec,
            v45);
    if ( !std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Const_iterator<0>::operator!=(
            (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)&v24->_Mycont,
            (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)&_Val1._Mycont) )
      break;
    v25 = std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator--(&_Val1);
    v26 = (stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> *)std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator*(v25);
    _Keyval2 = (ScheduleMSG **)stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>::_Kfn(
                                 v26,
                                 v27);
    v29 = stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>::_Kfn(
            v99,
            v28);
    if ( !stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>::operator()(&v97->comp, v29, _Keyval2) )
    {
      v93 = (ScheduleMSG **)stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>::_Kfn(
                              v99,
                              v30);
      v31 = (stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> *)std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator*(&_Val1);
      v33 = stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>::_Kfn(
              v31,
              v32);
      if ( !stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>::operator()(&v97->comp, v33, v93) )
      {
        _Val2 = 0;
        std::pair<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,bool>::pair<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,bool>(
          v98,
          &_Val1,
          &_Val2);
        v69 |= 1u;
        std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::~_Iterator<0>(&v40);
        std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::~_Iterator<0>(&_Val1);
        return v98;
      }
      std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator++(&_Val1);
      break;
    }
  }
  v66 = (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *)&v65;
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::_Iterator<0>(
    (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *)&v65,
    &_Val1);
  v94 = v35;
  v95 = std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::insert(
          &v97->_List,
          &v67,
          v35,
          (std::pair<ScheduleMSG * const,unsigned long> *)v99);
  v96 = v95;
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator=(
    &v40,
    v95);
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::~_Iterator<0>(&v67);
  while ( 1 )
  {
    v36 = std::vector<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>::operator[](
            &v97->_Vec,
            v45);
    if ( !std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Const_iterator<0>::operator==(
            (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)&v36->_Mycont,
            (std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Const_iterator<0> *)&_Val1._Mycont) )
      break;
    v37 = std::vector<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>::operator[](
            &v97->_Vec,
            v45);
    std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::operator=(
      v37,
      &v40);
    if ( !v45 )
      break;
    --v45;
  }
  v68 = 1;
  std::pair<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,bool>::pair<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,bool>(
    v98,
    &v40,
    &v68);
  v69 |= 1u;
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::~_Iterator<0>(&v40);
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::~_Iterator<0>(&_Val1);
  return v98;
}
