/*
 * Function: ?GetMoveTarget@CAnimus@@QEAA_NPEAVCCharacter@@ME@Z
 * Address: 0x140127FE0
 */

char __usercall CAnimus::GetMoveTarget@<al>(CAnimus *this@<rcx>, CCharacter *target@<rdx>, float fMoveSpeed@<xmm2>, char byMoveMode@<r9b>, float a5@<xmm0>)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  float v8; // xmm0_4@15
  float v9; // xmm0_4@17
  float v10; // xmm1_4@17
  __int64 v11; // [sp+0h] [bp-98h]@1
  char Dst; // [sp+28h] [bp-70h]@8
  int v13; // [sp+44h] [bp-54h]@12
  int v14; // [sp+48h] [bp-50h]@12
  float Tar; // [sp+58h] [bp-40h]@15
  float v16; // [sp+60h] [bp-38h]@17
  float v17; // [sp+74h] [bp-24h]@15
  float v18; // [sp+78h] [bp-20h]@17
  int v19; // [sp+7Ch] [bp-1Ch]@20
  int j; // [sp+80h] [bp-18h]@22
  bool v21; // [sp+84h] [bp-14h]@35
  float v22; // [sp+88h] [bp-10h]@18
  CAnimus *v23; // [sp+A0h] [bp+8h]@1
  CCharacter *v24; // [sp+A8h] [bp+10h]@1
  char v25; // [sp+B8h] [bp+20h]@1

  v25 = byMoveMode;
  v24 = target;
  v23 = this;
  v5 = &v11;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( !CAITimer::Check(&v23->m_AITimer[1]) )
    return 0;
  if ( !v24 )
    return 0;
  memcpy_0(&Dst, v24->m_fCurPos, 0xCui64);
  if ( v24->m_bMove )
  {
    if ( v25 || v24 != (CCharacter *)v23->m_pMaster )
    {
      GetYAngle(v23->m_fCurPos, v24->m_fCurPos);
      v13 = (signed int)ffloor(a5);
      GetYAngle(v24->m_fCurPos, v24->m_fTarPos);
      v14 = (signed int)ffloor(a5);
      if ( abs_0(v13 - v14) >= 0x4000 )
        memcpy_0(&Dst, v24->m_fCurPos, 0xCui64);
      else
        memcpy_0(&Dst, v24->m_fTarPos, 0xCui64);
    }
    else
    {
      memcpy_0(&Dst, v24->m_fTarPos, 0xCui64);
    }
  }
  memcpy_0(&Tar, &Dst, 0xCui64);
  v8 = FLOAT_25_0;
  v17 = FLOAT_25_0;
  if ( v24 != (CCharacter *)v23->m_pMaster )
  {
    ((void (__fastcall *)(CAnimus *))v23->vfptr->GetAttackRange)(v23);
    v8 = 25.0 * 0.8;
    v17 = v8;
  }
  GetAngle(v23->m_fCurPos, (float *)&Dst);
  v18 = v8;
  cos(v8);
  Tar = Tar + (float)(v8 * v17);
  v9 = v18;
  sin(v18);
  v10 = v16 + (float)(v9 * v17);
  v16 = v16 + (float)(v9 * v17);
  if ( v25 != 1 )
  {
    Get3DSqrt(v23->m_fCurPos, (float *)&Dst);
    v22 = v10;
    Get3DSqrt(v23->m_fCurPos, &Tar);
    if ( v10 >= v22 )
      return 0;
    v19 = 0;
    if ( !v23->m_bMove )
      v19 = 25;
    for ( j = 0; j < 2; ++j )
    {
      if ( v23->m_Skill[j].m_bLoad )
      {
        if ( v24 != (CCharacter *)v23->m_pMaster )
        {
          Get3DSqrt(v23->m_fCurPos, v24->m_fCurPos);
          if ( (float)(v19 + v23->m_Skill[j].m_Len) >= v10 )
            return 0;
        }
      }
    }
  }
  Get3DSqrt(v23->m_fTarPos, &Tar);
  if ( v10 >= 25.0 )
  {
    v21 = 1;
    if ( !v25 )
      v21 = 0;
    if ( CCharacter::SetTarPos((CCharacter *)&v23->vfptr, &Tar, v21) )
    {
      v23->m_fMoveSpeed = fMoveSpeed;
      CAnimus::SendMsg_Move(v23);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    if ( v23->m_fMoveSpeed != fMoveSpeed && v23->m_bMove )
    {
      v23->m_fMoveSpeed = fMoveSpeed;
      CAnimus::SendMsg_Move(v23);
    }
    result = 0;
  }
  return result;
}
