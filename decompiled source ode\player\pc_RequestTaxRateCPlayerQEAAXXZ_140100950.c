/*
 * Function: ?pc_RequestTaxRate@CPlayer@@QEAAXXZ
 * Address: 0x140100950
 */

void __fastcall CPlayer::pc_RequestTaxRate(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v3; // rax@4
  CUnmannedTraderTaxRateManager *v4; // rax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  int v7; // [sp+24h] [bp-14h]@5
  int n; // [sp+28h] [bp-10h]@5
  CPlayer *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = CPlayerDB::GetRaceCode(&v9->m_Param);
  v3 = CPvpUserAndGuildRankingSystem::Instance();
  if ( CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v3, v6, 5) == v9->m_dwObjSerial )
  {
    v7 = CPlayerDB::GetRaceCode(&v9->m_Param);
    n = v9->m_ObjID.m_wIndex;
    v4 = CUnmannedTraderTaxRateManager::Instance();
    CUnmannedTraderTaxRateManager::SendTaxRatePatriarch(v4, n, v7);
  }
}
