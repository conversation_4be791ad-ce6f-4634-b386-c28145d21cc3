/*
 * Function: ?SF_AttHPtoDstFP_Once@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 * Address: 0x14009DAB0
 */

char __usercall CPlayer::SF_AttHPtoDstFP_Once@<al>(CPlayer *this@<rcx>, CCharacter *pDstObj@<rdx>, float fEffectValue@<xmm2>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@9
  __int64 v7; // [sp+0h] [bp-48h]@1
  char v8; // [sp+20h] [bp-28h]@4
  int v9; // [sp+24h] [bp-24h]@8
  int v10; // [sp+28h] [bp-20h]@8
  float v11; // [sp+2Ch] [bp-1Ch]@8
  int v12; // [sp+30h] [bp-18h]@8
  CPlayer *v13; // [sp+50h] [bp+8h]@1
  CPlayer *v14; // [sp+58h] [bp+10h]@1

  v14 = (CPlayer *)pDstObj;
  v13 = this;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = 0;
  if ( ((int (__fastcall *)(CPlayer *))v13->vfptr->GetHP)(v13) && !v14->m_ObjID.m_byID )
    v8 = 1;
  if ( v8 )
  {
    v9 = CPlayer::GetFP(v14);
    v10 = CPlayer::GetMaxFP(v14);
    _effect_parameter::GetEff_Rate(&v14->m_EP, 19);
    v11 = fEffectValue * a4;
    v12 = (signed int)ffloor((float)v10 * (float)(fEffectValue * a4));
    CPlayer::SetFP(v14, v12 + v9, 0);
    if ( (float)((float)v9 / (float)v10) <= 0.80000001 )
    {
      CPlayer::SendMsg_SetFPInform(v14);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
