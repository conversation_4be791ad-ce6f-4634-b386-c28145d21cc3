/*
 * Function: ?_db_GuildRoom_Update@CMainThread@@AEAA_NPEAU_qry_case_guildroom_update@@@Z
 * Address: 0x1401B2D30
 */

bool __fastcall CMainThread::_db_GuildRoom_Update(CMainThread *this, _qry_case_guildroom_update *pSheet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMainThread *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return CRFWorldDatabase::Update_GuildRoom(v6->m_pWorldDB, pSheet->dwGuildSerial);
}
