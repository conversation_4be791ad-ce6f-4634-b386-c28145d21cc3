/*
 * Function: ?SF_RemoveAllContHelp_Once@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 * Address: 0x14009FF50
 */

bool __fastcall CPlayer::SF_RemoveAllContHelp_Once(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-68h]@1
  int v7; // [sp+30h] [bp-38h]@4
  int j; // [sp+34h] [bp-34h]@4
  int k; // [sp+38h] [bp-30h]@6
  bool *v10; // [sp+40h] [bp-28h]@9
  _base_fld *v11; // [sp+48h] [bp-20h]@10
  CCharacter *v12; // [sp+50h] [bp-18h]@13
  CCharacter *v13; // [sp+78h] [bp+10h]@1

  v13 = pDstObj;
  v3 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = 0;
  for ( j = 0; j < 2; ++j )
  {
    for ( k = 0; k < 8; ++k )
    {
      v10 = &v13->m_SFCont[j][k].m_bExist;
      if ( *v10 )
      {
        v11 = CRecordData::GetRecord(&stru_1799C8410 + 3, "17");
        if ( !v11 )
          goto LABEL_21;
        if ( v13->m_ObjID.m_byID )
          return 0;
        v12 = v13;
        if ( !HIBYTE(v13[25].m_SFContAura[0][5].m_wDurSec) || v10[1] != 3 || *((_WORD *)v10 + 1) != v11->m_dwIndex )
        {
LABEL_21:
          CCharacter::RemoveSFContEffect(v13, j, k, 0, 0);
          ++v7;
        }
      }
    }
  }
  return v7 > 0;
}
