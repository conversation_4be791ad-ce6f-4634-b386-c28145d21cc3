/*
 * Function: ?Select_HonorGuild@CRFWorldDatabase@@QEAAHEPEAU_guild_honor_list_result_zocl@@_N@Z
 * Address: 0x1404C01D0
 */

signed __int64 __fastcall CRFWorldDatabase::Select_HonorGuild(CRFWorldDatabase *this, char by<PERSON><PERSON>, _guild_honor_list_result_zocl *pOutList, bool bNext)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  _guild_honor_list_result_zocl::__list *v7; // rax@22
  unsigned int *v8; // rax@22
  unsigned int *v9; // rax@22
  char *v10; // rax@22
  char *v11; // rax@22
  char *v12; // rax@22
  __int64 v13; // [sp+0h] [bp-108h]@1
  void *SQLStmt; // [sp+20h] [bp-E8h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-E0h]@22
  SQLLEN v16; // [sp+38h] [bp-D0h]@22
  __int16 v17; // [sp+44h] [bp-C4h]@9
  char _Dest[128]; // [sp+60h] [bp-A8h]@4
  unsigned __int8 v19; // [sp+E4h] [bp-24h]@16
  unsigned __int8 v20; // [sp+E5h] [bp-23h]@24
  int v21; // [sp+F0h] [bp-18h]@4
  unsigned __int64 v22; // [sp+F8h] [bp-10h]@4
  CRFWorldDatabase *v23; // [sp+110h] [bp+8h]@1
  _guild_honor_list_result_zocl *v24; // [sp+120h] [bp+18h]@1

  v24 = pOutList;
  v23 = this;
  v4 = &v13;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v22 = (unsigned __int64)&v13 ^ _security_cookie;
  v21 = bNext != 0;
  sprintf_s<128>((char (*)[128])_Dest, "{ CALL pSelect_HonorGuild( %d, %d ) }", (unsigned __int8)byRace, bNext != 0);
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v23->vfptr, _Dest);
  if ( v23->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v23->vfptr) )
  {
    v17 = SQLExecDirectA_0(v23->m_hStmtSelect, _Dest, -3);
    if ( v17 && v17 != 1 )
    {
      if ( v17 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v23->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v17, _Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v17, v23->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      while ( 1 )
      {
        v17 = SQLFetch_0(v23->m_hStmtSelect);
        if ( v17 && v17 != 1 )
        {
          v19 = 0;
          if ( v17 == 100 )
          {
            v19 = 2;
          }
          else
          {
            SQLStmt = v23->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v17, _Dest, "SQLFetch", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v17, v23->m_hStmtSelect);
            v19 = 1;
          }
          if ( v23->m_hStmtSelect )
            SQLCloseCursor_0(v23->m_hStmtSelect);
          return v19;
        }
        v7 = &v24->GuildList[v24->byListNum];
        StrLen_or_IndPtr = &v16;
        SQLStmt = 0i64;
        v17 = SQLGetData_0(v23->m_hStmtSelect, 1u, 4, v7, 0i64, &v16);
        v8 = &v24->GuildList[v24->byListNum].dwEmblemBack;
        StrLen_or_IndPtr = &v16;
        SQLStmt = 0i64;
        v17 = SQLGetData_0(v23->m_hStmtSelect, 2u, 4, v8, 0i64, &v16);
        v9 = &v24->GuildList[v24->byListNum].dwEmblemMark;
        StrLen_or_IndPtr = &v16;
        SQLStmt = 0i64;
        v17 = SQLGetData_0(v23->m_hStmtSelect, 3u, 4, v9, 0i64, &v16);
        v10 = v24->GuildList[v24->byListNum].wszGuildName;
        StrLen_or_IndPtr = &v16;
        SQLStmt = (void *)17;
        v17 = SQLGetData_0(v23->m_hStmtSelect, 4u, 1, v10, 17i64, &v16);
        v11 = v24->GuildList[v24->byListNum].wszMasterName;
        StrLen_or_IndPtr = &v16;
        SQLStmt = (void *)17;
        v17 = SQLGetData_0(v23->m_hStmtSelect, 5u, 1, v11, 17i64, &v16);
        v12 = &v24->GuildList[v24->byListNum].byTaxRate;
        StrLen_or_IndPtr = &v16;
        SQLStmt = 0i64;
        v17 = SQLGetData_0(v23->m_hStmtSelect, 6u, -6, v12, 0i64, &v16);
        if ( v17 )
        {
          if ( v17 != 1 )
            break;
        }
        ++v24->byListNum;
      }
      v20 = 0;
      if ( v17 == 100 )
      {
        v20 = 2;
      }
      else
      {
        SQLStmt = v23->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v17, _Dest, "SQLGetData", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v17, v23->m_hStmtSelect);
        v20 = 1;
      }
      if ( v23->m_hStmtSelect )
        SQLCloseCursor_0(v23->m_hStmtSelect);
      result = v20;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v23->vfptr, "ReConnectDataBase Fail. Query : %s", _Dest);
    result = 1i64;
  }
  return result;
}
