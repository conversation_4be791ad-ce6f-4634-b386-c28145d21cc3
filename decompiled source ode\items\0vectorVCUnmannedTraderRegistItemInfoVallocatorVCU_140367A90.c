/*
 * Function: ??0?$vector@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@QEAA@AEBV01@@Z
 * Address: 0x140367A90
 */

void __fastcall std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *this, std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *_Right)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<CUnmannedTraderRegistItemInfo> v4; // al@4
  unsigned __int64 v5; // rax@4
  __int64 v6; // [sp+0h] [bp-A8h]@1
  char v7; // [sp+20h] [bp-88h]@4
  std::allocator<CUnmannedTraderRegistItemInfo> *v8; // [sp+28h] [bp-80h]@4
  char v9; // [sp+30h] [bp-78h]@5
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *result; // [sp+48h] [bp-60h]@5
  char v11; // [sp+50h] [bp-58h]@5
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v12; // [sp+68h] [bp-40h]@5
  __int64 v13; // [sp+70h] [bp-38h]@4
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v14; // [sp+78h] [bp-30h]@5
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v15; // [sp+80h] [bp-28h]@5
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v16; // [sp+88h] [bp-20h]@5
  CUnmannedTraderRegistItemInfo *v17; // [sp+90h] [bp-18h]@5
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v18; // [sp+B0h] [bp+8h]@1
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v19; // [sp+B8h] [bp+10h]@1

  v19 = _Right;
  v18 = this;
  v2 = &v6;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = -2i64;
  v8 = (std::allocator<CUnmannedTraderRegistItemInfo> *)&v7;
  std::allocator<CUnmannedTraderRegistItemInfo>::allocator<CUnmannedTraderRegistItemInfo>(
    (std::allocator<CUnmannedTraderRegistItemInfo> *)&v7,
    &_Right->_Alval);
  std::_Vector_val<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Vector_val<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(
    (std::_Vector_val<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v18->_Myfirstiter,
    v4);
  v5 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(v19);
  if ( std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Buy(v18, v5) )
  {
    result = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v9;
    v12 = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v11;
    v14 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
            v19,
            (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v9);
    v15 = v14;
    v16 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::begin(v19, v12);
    v17 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Ucopy<std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>>(
            v18,
            v16,
            v15,
            v18->_Myfirst);
    v18->_Mylast = v17;
  }
}
