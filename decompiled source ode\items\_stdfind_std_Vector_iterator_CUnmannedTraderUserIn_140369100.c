/*
 * Function: _std::find_std::_Vector_iterator_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo____unsigned_long__::_1_::dtor$4
 * Address: 0x140369100
 */

void __fastcall std::find_std::_Vector_iterator_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo____unsigned_long__::_1_::dtor_4(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::~_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>((std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)(a2 + 96));
}
