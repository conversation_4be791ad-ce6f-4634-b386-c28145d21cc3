/*
 * Function: j_?GetCloseItemForPassTimeUpdateInfo@CUnmannedTraderUserInfoTable@@QEAA?AW4STATE@CUnmannedTraderItemState@@KKAEAPEAVCPlayer@@@Z
 * Address: 0x14000438B
 */

CUnmannedTraderItemState::STATE __fastcall CUnmannedTraderUserInfoTable::GetCloseItemForPassTimeUpdateInfo(CUnmannedTraderUserInfoTable *this, unsigned int dwOwnerSerial, unsigned int dwRegistSerial, CPlayer **pkOwner)
{
  return CUnmannedTraderUserInfoTable::GetCloseItemForPassTimeUpdateInfo(this, dwOwnerSerial, dwRegistSerial, pkOwner);
}
