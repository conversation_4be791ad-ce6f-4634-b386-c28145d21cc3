/*
 * Function: ?DividedBy@Integer@CryptoPP@@QEBA?AV12@AEBV12@@Z
 * Address: 0x1405EA480
 */

struct CryptoPP::Integer *__fastcall CryptoPP::Integer::DividedBy(CryptoPP::Integer *this, struct CryptoPP::Integer *retstr, const struct CryptoPP::Integer *a3)
{
  CryptoPP::Integer v4; // [sp+20h] [bp-68h]@1
  CryptoPP::Integer v5; // [sp+48h] [bp-40h]@1
  int v6; // [sp+70h] [bp-18h]@1
  __int64 v7; // [sp+78h] [bp-10h]@1
  const struct CryptoPP::Integer *v8; // [sp+90h] [bp+8h]@1
  CryptoPP::Integer *v9; // [sp+98h] [bp+10h]@1
  struct CryptoPP::Integer *v10; // [sp+A0h] [bp+18h]@1

  v10 = (struct CryptoPP::Integer *)a3;
  v9 = retstr;
  v8 = this;
  v7 = -2i64;
  v6 = 0;
  CryptoPP::Integer::Integer(&v4);
  CryptoPP::Integer::Integer(&v5);
  CryptoPP::Integer::Divide(&v4, &v5, v8, v10);
  CryptoPP::Integer::Integer(v9, &v5);
  v6 |= 1u;
  CryptoPP::Integer::~Integer(&v5);
  CryptoPP::Integer::~Integer(&v4);
  return v9;
}
