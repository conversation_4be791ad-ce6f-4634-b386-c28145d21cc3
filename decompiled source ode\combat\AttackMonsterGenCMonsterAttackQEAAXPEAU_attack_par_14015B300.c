/*
 * Function: ?AttackMonsterGen@CMonsterAttack@@QEAAXPEAU_attack_param@@_N@Z
 * Address: 0x14015B300
 */

void __usercall CMonsterAttack::AttackMonsterGen(CMonsterAttack *this@<rcx>, _attack_param *pParam@<rdx>, bool bMustMiss@<r8b>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@8
  float v7; // xmm0_4@14
  float v8; // xmm0_4@14
  CCharacter *v9; // r10@21
  int v10; // eax@21
  CCharacter **v11; // rax@38
  CCharacter **v12; // rcx@38
  CCharacter **v13; // rdx@38
  CCharacter **v14; // r8@38
  CCharacter **v15; // rcx@40
  float *v16; // rax@43
  CCharacter **v17; // rdx@43
  __int64 v18; // [sp+0h] [bp-78h]@1
  CCharacter *pDst; // [sp+20h] [bp-58h]@38
  bool bBackAttack; // [sp+28h] [bp-50h]@38
  char v21; // [sp+30h] [bp-48h]@4
  char v22; // [sp+31h] [bp-47h]@5
  float v23; // [sp+34h] [bp-44h]@14
  float v24; // [sp+38h] [bp-40h]@14
  float v25; // [sp+3Ch] [bp-3Ch]@30
  float v26; // [sp+40h] [bp-38h]@30
  float v27; // [sp+44h] [bp-34h]@8
  float v28; // [sp+48h] [bp-30h]@14
  float v29; // [sp+4Ch] [bp-2Ch]@14
  int v30; // [sp+50h] [bp-28h]@21
  CCharacter **v31; // [sp+58h] [bp-20h]@21
  CGameObjectVtbl *v32; // [sp+60h] [bp-18h]@21
  int v33; // [sp+68h] [bp-10h]@33
  int nAttPnt; // [sp+6Ch] [bp-Ch]@38
  CMonsterAttack *v35; // [sp+80h] [bp+8h]@1
  _attack_param *v36; // [sp+88h] [bp+10h]@1
  bool v37; // [sp+90h] [bp+18h]@1

  v37 = bMustMiss;
  v36 = pParam;
  v35 = this;
  v4 = &v18;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v35->m_nDamagedObjNum = 0;
  v35->m_bIsCrtAtt = 0;
  v35->m_pp = pParam;
  v21 = 1;
  v35->m_nDamagedObjNum = 0;
  CCharacter::BreakStealth(v35->m_pAttChar);
  if ( v35->m_pp->pDst )
  {
    v22 = 0;
    if ( _effect_parameter::GetEff_State(&v35->m_pp->pDst->m_EP, 14) )
    {
      v22 = 1;
    }
    else
    {
      _effect_parameter::GetEff_Plus(&v35->m_pp->pDst->m_EP, 27);
      if ( a4 > 0.0 )
      {
        v6 = rand();
        a4 = (float)(v6 % 100);
        v27 = (float)(v6 % 100);
        _effect_parameter::GetEff_Plus(&v35->m_pp->pDst->m_EP, 27);
        if ( a4 > v27 )
          v22 = 1;
      }
    }
    if ( v22 )
    {
      if ( !v35->m_pp->bPassCount
        && !v35->m_pp->nClass
        && !((int (__fastcall *)(CCharacter *))v35->m_pp->pDst->vfptr->GetWeaponClass)(v35->m_pp->pDst) )
      {
        ((void (__fastcall *)(CCharacter *))v35->m_pp->pDst->vfptr->GetAttackRange)(v35->m_pp->pDst);
        v28 = a4;
        ((void (__fastcall *)(CCharacter *))v35->m_pAttChar->vfptr->GetWidth)(v35->m_pAttChar);
        v7 = v28 + (float)(a4 / 2.0);
        v29 = v7;
        _effect_parameter::GetEff_Plus(&v35->m_pp->pDst->m_EP, 4);
        v8 = v29 + v7;
        v23 = v8;
        GetSqrt(v35->m_pp->pDst->m_fCurPos, v35->m_pAttChar->m_fCurPos);
        v24 = v8;
        a4 = v23;
        if ( v23 >= v24 )
        {
          v35->m_DamList[0].m_pChar = v35->m_pp->pDst;
          v35->m_DamList[0].m_nDamage = -1;
          v35->m_nDamagedObjNum = 1;
          CCharacter::SendMsg_AttackActEffect(v35->m_pAttChar, 0, v35->m_pp->pDst);
          return;
        }
      }
      _effect_parameter::GetEff_Plus(&v35->m_pp->pDst->m_EP, 27);
      if ( a4 > 0.0 )
      {
        v35->m_DamList[0].m_pChar = v35->m_pp->pDst;
        v35->m_DamList[0].m_nDamage = 0;
        v35->m_nDamagedObjNum = 1;
        return;
      }
    }
    if ( v35->m_pp->bMatchless )
    {
      v35->m_DamList[0].m_pChar = v35->m_pp->pDst;
      v35->m_DamList[0].m_nDamage = ((int (__fastcall *)(CCharacter *))v35->m_pp->pDst->vfptr->GetHP)(v35->m_pp->pDst);
      v35->m_nDamagedObjNum = 1;
      return;
    }
    if ( _effect_parameter::GetEff_State(&v35->m_pp->pDst->m_EP, 8) )
    {
      v21 = 0;
    }
    else
    {
      v30 = rand() % 100;
      v31 = &v35->m_pp->pDst;
      v9 = v35->m_pAttChar;
      v32 = v35->m_pAttChar->vfptr;
      v10 = ((int (__fastcall *)(CCharacter *, CCharacter *, _QWORD, _QWORD))v32->GetGenAttackProb)(
              v9,
              *v31,
              *((_DWORD *)v31 + 2),
              *((_BYTE *)v31 + 100));
      if ( v30 >= v10 )
        v21 = 0;
    }
    if ( v21 && v37 )
      v21 = 0;
    if ( !v21 )
    {
      v35->m_DamList[0].m_pChar = v35->m_pp->pDst;
      v35->m_DamList[0].m_nDamage = 0;
      v35->m_nDamagedObjNum = 1;
      return;
    }
  }
  v25 = (float)(v35->m_pp->nAddAttPnt + CAttack::_CalcGenAttPnt((CAttack *)&v35->m_pp, 0));
  v26 = FLOAT_1_0;
  if ( v35->m_pp->nWpType == 7 )
  {
    _effect_parameter::GetEff_Rate(&v35->m_pAttChar->m_EP, 29);
    v26 = FLOAT_1_0;
  }
  else
  {
    _effect_parameter::GetEff_Rate(&v35->m_pAttChar->m_EP, v35->m_pp->nClass);
    v26 = FLOAT_1_0;
  }
  CMonsterAttack::ModifyMonsterAttFc(v35, v26);
  v25 = v25 * 1.0;
  v33 = v36->nAttactType;
  if ( v33 < 0 )
  {
LABEL_45:
    v35->m_DamList[0].m_pChar = v35->m_pp->pDst;
    v35->m_DamList[0].m_nDamage = 0;
    v35->m_nDamagedObjNum = 1;
    goto LABEL_46;
  }
  if ( v33 > 2 )
  {
    if ( v33 == 5 )
    {
      if ( v35->m_pp->nExtentRange > 0 )
      {
        v15 = &v35->m_pp->pDst;
        bBackAttack = 0;
        LODWORD(pDst) = 0;
        CAttack::FlashDamageProc((CAttack *)&v35->m_pp, *((_DWORD *)v15 + 9), (signed int)ffloor(v25), 90, 0, 0);
      }
      goto LABEL_46;
    }
    if ( v33 == 6 )
    {
      if ( v35->m_pp->nExtentRange > 0 )
      {
        v16 = v35->m_pp->fArea;
        v17 = &v35->m_pp->pDst;
        bBackAttack = 0;
        LODWORD(pDst) = 0;
        CAttack::AreaDamageProc((CAttack *)&v35->m_pp, *((_DWORD *)v17 + 9), (signed int)ffloor(v25), v16, 0, 0);
      }
      goto LABEL_46;
    }
    goto LABEL_45;
  }
  v35->m_DamList[0].m_pChar = v35->m_pp->pDst;
  v11 = &v35->m_pp->pDst;
  v12 = &v35->m_pp->pDst;
  v13 = &v35->m_pp->pDst;
  v14 = &v35->m_pp->pDst;
  nAttPnt = (signed int)ffloor(v25);
  bBackAttack = *((_BYTE *)v11 + 100);
  pDst = *v12;
  v35->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                  v35->m_pAttChar,
                                  nAttPnt,
                                  *((_DWORD *)v14 + 2),
                                  *((_DWORD *)v13 + 3),
                                  pDst,
                                  bBackAttack);
  v35->m_nDamagedObjNum = 1;
LABEL_46:
  CAttack::CalcAvgDamage((CAttack *)&v35->m_pp);
}
