/*
 * Function: ?PushMsg@CMsgData@@AEAAXPEAU_message@@@Z
 * Address: 0x140438180
 */

void __fastcall CMsgData::PushMsg(CMsgData *this, _message *pMsg)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMsgData *v5; // [sp+30h] [bp+8h]@1
  _message *v6; // [sp+38h] [bp+10h]@1

  v6 = pMsg;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CMyCriticalSection::Lock(&v5->m_csList);
  v6->pNext = &v5->m_gmListTail;
  v6->pPrev = v5->m_gmListTail.pPrev;
  v5->m_gmListTail.pPrev->pNext = v6;
  v5->m_gmListTail.pPrev = v6;
  CMyCriticalSection::Unlock(&v5->m_csList);
}
