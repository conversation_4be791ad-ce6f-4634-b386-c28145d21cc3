/*
 * Function: ??0CLevel@@QEAA@XZ
 * Address: 0x1404E2C40
 */

_QWORD *__fastcall CLevel::CLevel(_QWORD *a1)
{
  _QWORD *v1; // rbx@1
  CBsp *v2; // rax@1
  __int64 v3; // rax@2
  void *v4; // rax@4
  __int64 v5; // rax@5

  v1 = a1;
  *a1 = &CLevel::`vftable';
  CAniCamera::CAniCamera(a1 + 45);
  CTimer::CTimer((_DWORD *)v1 + 106);
  CExtDummy::CExtDummy((__int64)(v1 + 66));
  v2 = (CBsp *)operator new(0x29F0ui64);
  if ( v2 )
    v3 = CBsp::CBsp(v2);
  else
    v3 = 0i64;
  v1[43] = v3;
  v4 = operator new(0x508ui64);
  if ( v4 )
    LODWORD(v5) = CSkyBox::CSkyBox(v4);
  else
    v5 = 0i64;
  v1[44] = v5;
  *((_DWORD *)v1 + 85) = 0;
  *((_BYTE *)v1 + 8) = 0;
  *((_DWORD *)v1 + 136) = 0;
  *((_DWORD *)v1 + 137) = 0;
  *((_DWORD *)v1 + 138) = 0;
  *((_DWORD *)v1 + 139) = 0;
  return v1;
}
