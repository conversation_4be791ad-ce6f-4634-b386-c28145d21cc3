/*
 * Function: ?ReservationForceClose@CPlayer@@QEAAXXZ
 * Address: 0x1400663B0
 */

void __fastcall CPlayer::ReservationForceClose(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CPlayer *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _DELAY_PROCESS::Push(&CPlayer::s_BillingForceCloseDelay, v4->m_ObjID.m_wIndex, v4->m_dwObjSerial);
}
