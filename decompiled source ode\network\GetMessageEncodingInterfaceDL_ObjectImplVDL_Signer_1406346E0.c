/*
 * Function: ?GetMessageEncodingInterface@?$DL_ObjectImpl@V?$DL_SignerBase@VInteger@CryptoPP@@@CryptoPP@@U?$DL_SignatureSchemeOptions@V?$DL_SS@UDL_SignatureKeys_GFP@CryptoPP@@V?$DL_Algorithm_NR@VInteger@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_NR@2@VSHA1@2@H@CryptoPP@@UDL_SignatureKeys_GFP@2@V?$DL_Algorithm_NR@VInteger@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_NR@2@VSHA1@2@@2@V?$DL_PrivateKey_GFP@VDL_GroupParameters_GFP@CryptoPP@@@2@@CryptoPP@@MEBAAEBVPK_SignatureMessageEncodingMethod@2@XZ
 * Address: 0x1406346E0
 */

int CryptoPP::DL_ObjectImpl<CryptoPP::DL_SignerBase<CryptoPP::Integer>,CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DL_SS<CryptoPP::DL_SignatureKeys_GFP,CryptoPP::DL_Algorithm_NR<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_NR,CryptoPP::SHA1,int>,CryptoPP::DL_SignatureKeys_GFP,CryptoPP::DL_Algorithm_NR<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_NR,CryptoPP::SHA1>,CryptoPP::DL_PrivateKey_GFP<CryptoPP::DL_GroupParameters_GFP>>::GetMessageEncodingInterface()
{
  __int64 v0; // rax@1
  char v2; // [sp+20h] [bp-18h]@1
  unsigned __int8 v3; // [sp+21h] [bp-17h]@1

  memset(&v3, 0, sizeof(v3));
  LODWORD(v0) = CryptoPP::Singleton<CryptoPP::DL_SignatureMessageEncodingMethod_NR,CryptoPP::NewObject<CryptoPP::DL_SignatureMessageEncodingMethod_NR>,0>::Singleton<CryptoPP::DL_SignatureMessageEncodingMethod_NR,CryptoPP::NewObject<CryptoPP::DL_SignatureMessageEncodingMethod_NR>,0>(
                  &v2,
                  v3);
  return CryptoPP::Singleton<CryptoPP::DL_SignatureMessageEncodingMethod_NR,CryptoPP::NewObject<CryptoPP::DL_SignatureMessageEncodingMethod_NR>,0>::Ref(v0);
}
