/*
 * Function: ?IsEnableSkill@CEquipItemSFAgent@@QEAAEHPEAU_skill_fld@@@Z
 * Address: 0x140121200
 */

char __fastcall CEquipItemSFAgent::IsEnableSkill(CEquipItemSFAgent *this, int nEquipTblIndex, _skill_fld *pSkill)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  _STORAGE_LIST::_db_con *v7; // [sp+20h] [bp-18h]@4
  int v8; // [sp+28h] [bp-10h]@6
  CEquipItemSFAgent *v9; // [sp+40h] [bp+8h]@1
  int nEquipTblIndexa; // [sp+48h] [bp+10h]@1

  nEquipTblIndexa = nEquipTblIndex;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = CEquipItemSFAgent::GetEquip(v9, nEquipTblIndex);
  if ( v7 )
  {
    v8 = nEquipTblIndexa;
    if ( nEquipTblIndexa == 7 && v9->m_pMaster->m_byModeType )
      result = 13;
    else
      result = 0;
  }
  else
  {
    result = 22;
  }
  return result;
}
