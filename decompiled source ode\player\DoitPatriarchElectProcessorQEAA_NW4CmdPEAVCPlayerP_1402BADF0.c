/*
 * Function: ?Doit@PatriarchElectProcessor@@QEAA_NW4Cmd@@PEAVCPlayer@@PEAD@Z
 * Address: 0x1402BADF0
 */

bool __fastcall PatriarchElectProcessor::Doit(PatriarchElectProcessor *this, Cmd eCmd, CPlayer *pOne, char *pdata)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@9
  __int64 v7; // [sp+0h] [bp-48h]@1
  int v8; // [sp+20h] [bp-28h]@6
  ClassOrderProcessor *v9; // [sp+28h] [bp-20h]@6
  Cmd v10; // [sp+30h] [bp-18h]@20
  PatriarchElectProcessor *v11; // [sp+50h] [bp+8h]@1
  Cmd v12; // [sp+58h] [bp+10h]@1
  CPlayer *v13; // [sp+60h] [bp+18h]@1
  char *v14; // [sp+68h] [bp+20h]@1

  v14 = pdata;
  v13 = pOne;
  v12 = eCmd;
  v11 = this;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( eCmd == 17 || eCmd == 18 )
  {
    v9 = ClassOrderProcessor::Instance();
    v8 = ((int (__fastcall *)(ClassOrderProcessor *, _QWORD, CPlayer *, char *))v9->vfptr->Doit)(
           v9,
           (unsigned int)v12,
           v13,
           v14);
    if ( v8 )
    {
      if ( v13 )
        PatriarchElectProcessor::SendMsg_ResultCode(v11, v13->m_id.wIndex, v8);
      result = 1;
    }
    else
    {
      result = 1;
    }
    return result;
  }
  if ( !v11->_kRunningProcessor
    || (v8 = ((int (__fastcall *)(ElectProcessor *, _QWORD))v11->_kRunningProcessor->vfptr->Doit)(
               v11->_kRunningProcessor,
               (unsigned int)eCmd),
        v8 == 255) )
  {
    if ( !v13 || !v13->m_bOper )
      return 1;
    v10 = v12;
    if ( (signed int)v12 < 2 )
      goto LABEL_29;
    if ( (signed int)v10 <= 3 )
    {
      v8 = 2;
    }
    else if ( v10 == 7 )
    {
      v8 = 9;
    }
    else
    {
      if ( (signed int)v10 <= 13 || (signed int)v10 > 18 )
      {
LABEL_29:
        v8 = 2;
        goto LABEL_30;
      }
      v8 = 21;
    }
LABEL_30:
    PatriarchElectProcessor::SendMsg_ResultCode(v11, v13->m_id.wIndex, v8);
    return 1;
  }
  if ( v8 )
  {
    if ( v13 )
      PatriarchElectProcessor::SendMsg_ResultCode(v11, v13->m_id.wIndex, v8);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
