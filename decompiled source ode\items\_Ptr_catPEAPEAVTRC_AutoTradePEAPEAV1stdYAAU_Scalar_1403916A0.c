/*
 * Function: ??$_Ptr_cat@PEAPEAVTRC_AutoTrade@@PEAPEAV1@@std@@YA?AU_Scalar_ptr_iterator_tag@0@AEAPEAPEAVTRC_AutoTrade@@0@Z
 * Address: 0x1403916A0
 */

char __fastcall std::_Ptr_cat<TRC_AutoTrade * *,TRC_AutoTrade * *>(TRC_AutoTrade ***__formal, TRC_AutoTrade ***a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  char v6; // [sp+24h] [bp-24h]@4

  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return v6;
}
