/*
 * Function: ??$_Uninit_move@PEAU?$pair@KK@std@@PEAU12@V?$allocator@U?$pair@KK@std@@@2@U_Undefined_move_tag@2@@std@@YAPEAU?$pair@KK@0@PEAU10@00AEAV?$allocator@U?$pair@KK@std@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14038CC00
 */

std::pair<unsigned long,unsigned long> *__fastcall std::_Uninit_move<std::pair<unsigned long,unsigned long> *,std::pair<unsigned long,unsigned long> *,std::allocator<std::pair<unsigned long,unsigned long>>,std::_Undefined_move_tag>(std::pair<unsigned long,unsigned long> *_First, std::pair<unsigned long,unsigned long> *_Last, std::pair<unsigned long,unsigned long> *_Dest, std::allocator<std::pair<unsigned long,unsigned long> > *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-28h]@1
  std::pair<unsigned long,unsigned long> *_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  return stdext::unchecked_uninitialized_copy<std::pair<unsigned long,unsigned long> *,std::pair<unsigned long,unsigned long> *,std::allocator<std::pair<unsigned long,unsigned long>>>(
           _Firsta,
           _Last,
           _Dest,
           _Al);
}
