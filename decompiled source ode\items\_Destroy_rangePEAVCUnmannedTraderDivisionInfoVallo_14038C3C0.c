/*
 * Function: ??$_Destroy_range@PEAVCUnmannedTraderDivisionInfo@@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@YAXPEAPEAVCUnmannedTraderDivisionInfo@@0AEAV?$allocator@PEAVCUnmannedTraderDivisionInfo@@@0@U_Scalar_ptr_iterator_tag@0@@Z
 * Address: 0x14038C3C0
 */

void __fastcall std::_Destroy_range<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(CUnmannedTraderDivisionInfo **_First, CUnmannedTraderDivisionInfo **_Last, std::allocator<CUnmannedTraderDivisionInfo *> *_Al, std::_Scalar_ptr_iterator_tag __formal)
{
  ;
}
