/*
 * Function: ?Initialize@?$DL_GroupParameters_EC@VEC2N@CryptoPP@@@CryptoPP@@QEAAXAEBVEC2N@2@AEBUEC2NPoint@2@AEBVInteger@2@2@Z
 * Address: 0x140557550
 */

int __fastcall CryptoPP::DL_GroupParameters_EC<CryptoPP::EC2N>::Initialize(__int64 a1, __int64 a2, __int64 a3)
{
  __int64 v4; // [sp+30h] [bp+8h]@1
  __int64 v5; // [sp+40h] [bp+18h]@1

  v5 = a3;
  v4 = a1;
  CryptoPP::EcPrecomputation<CryptoPP::EC2N>::SetCurve(a1 + 24);
  (*(void (__fastcall **)(__int64, __int64))(*(_QWORD *)v4 + 16i64))(v4, v5);
  CryptoPP::Integer::operator=(v4 + 344);
  return CryptoPP::Integer::operator=(v4 + 392);
}
