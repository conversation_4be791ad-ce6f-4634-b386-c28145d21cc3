/*
 * Function: _stdext::unchecked_uninitialized_copy_std::_Vector_const_iterator_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo____CUnmannedTraderRegistItemInfo_____ptr64_std::allocator_CUnmannedTraderRegistItemInfo____::_1_::dtor$1
 * Address: 0x140369C70
 */

void __fastcall stdext::unchecked_uninitialized_copy_std::_Vector_const_iterator_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo____CUnmannedTraderRegistItemInfo_____ptr64_std::allocator_CUnmannedTraderRegistItemInfo____::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(*(std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > **)(a2 + 176));
}
