/*
 * Function: _CUnmannedTraderGroupItemInfoTable::Init_::_1_::dtor$0
 * Address: 0x14036B330
 */

void __fastcall CUnmannedTraderGroupItemInfoTable::Init_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::vector<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::~vector<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>((std::vector<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *)(a2 + 40));
}
