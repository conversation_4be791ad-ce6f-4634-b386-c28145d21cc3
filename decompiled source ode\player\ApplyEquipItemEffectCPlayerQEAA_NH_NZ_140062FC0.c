/*
 * Function: ?ApplyEquipItemEffect@CPlayer@@QEAA_NH_N@Z
 * Address: 0x140062FC0
 */

char __fastcall CPlayer::ApplyEquipItemEffect(CPlayer *this, int iItemEffectCode, bool bEquip)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  char v7; // [sp+20h] [bp-28h]@4
  int j; // [sp+24h] [bp-24h]@4
  _STORAGE_LIST::_storage_con *pCon; // [sp+28h] [bp-20h]@9
  _ITEM_EFFECT *v10; // [sp+30h] [bp-18h]@13
  int k; // [sp+38h] [bp-10h]@14
  CPlayer *v12; // [sp+50h] [bp+8h]@1
  int v13; // [sp+58h] [bp+10h]@1
  bool v14; // [sp+60h] [bp+18h]@1

  v14 = bEquip;
  v13 = iItemEffectCode;
  v12 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = 0;
  for ( j = 0; j < 15; ++j )
  {
    if ( v12->m_byEffectEquipCode[j] == 1 )
    {
      pCon = (_STORAGE_LIST::_storage_con *)(j >= 8 ? &v12->m_Param.m_dbEmbellish.m_pStorageList[j - 8] : &v12->m_Param.m_dbEquip.m_pStorageList[j]);
      if ( pCon->m_bLoad )
      {
        if ( CPlayer::IsEffectableEquip(v12, pCon) )
        {
          v10 = CPlayer::_GetItemEffect(v12, (_STORAGE_LIST::_db_con *)pCon);
          if ( v10 )
          {
            for ( k = 0; k < 4; ++k )
            {
              if ( v13 == v10[k].nEffectCode )
                CPlayer::apply_normal_item_std_effect(v12, v10[k].nEffectCode, v10[k].fEffectValue, v14);
            }
          }
        }
      }
    }
  }
  return v7;
}
