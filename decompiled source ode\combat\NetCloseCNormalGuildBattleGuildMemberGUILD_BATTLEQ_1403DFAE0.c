/*
 * Function: ?NetClose@CNormalGuildBattleGuildMember@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403DFAE0
 */

void __usercall GUILD_BATTLE::CNormalGuildBattleGuildMember::NetClose(GUILD_BATTLE::CNormalGuildBattleGuildMember *this@<rcx>, long double a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  GUILD_BATTLE::CNormalGuildBattleGuildMember::ReturnBindPos(v5);
  CPlayerDB::GetPvPPoint(&v5->m_pkMember->pPlayer->m_Param);
  v5->m_dPvpPoint = a2;
}
