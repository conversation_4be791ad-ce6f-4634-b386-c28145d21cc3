/*
 * Function: ?db_Insert_Item@CMainThread@@QEAAEKKKKE@Z
 * Address: 0x1401B0C00
 */

char __fastcall CMainThread::db_Insert_Item(CMainThread *this, unsigned int dwSerial, unsigned int dwItemCodeK, unsigned int dwItemCodeD, unsigned int dwItemCodeU, char byType)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v9; // [sp+0h] [bp-38h]@1
  unsigned int dwItemCode_U; // [sp+20h] [bp-18h]@4
  char v11; // [sp+28h] [bp-10h]@4
  CMainThread *v12; // [sp+40h] [bp+8h]@1

  v12 = this;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v11 = byType;
  dwItemCode_U = dwItemCodeU;
  if ( CRFWorldDatabase::Insert_ItemChargeInGame(
         v12->m_pWorldDB,
         dwSerial,
         dwItemCodeK,
         dwItemCodeD,
         dwItemCodeU,
         byType) )
  {
    result = 0;
  }
  else
  {
    result = 24;
  }
  return result;
}
