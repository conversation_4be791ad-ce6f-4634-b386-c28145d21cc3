/*
 * Function: ?Spy@MessageQueue@CryptoPP@@QEBAPEBEAEA_K@Z
 * Address: 0x1406548E0
 */

char *__fastcall CryptoPP::MessageQueue::Spy(CryptoPP::MessageQueue *this, unsigned __int64 *a2)
{
  char *v2; // ST20_8@1
  __int64 v3; // rax@1
  __int64 v5; // [sp+28h] [bp-10h]@1
  CryptoPP::MessageQueue *v6; // [sp+40h] [bp+8h]@1
  __int64 *v7; // [sp+48h] [bp+10h]@1

  v7 = (__int64 *)a2;
  v6 = this;
  v2 = CryptoPP::ByteQueue::Spy((CryptoPP::ByteQueue *)((char *)this + 32), a2);
  LODWORD(v3) = (*(int (__fastcall **)(CryptoPP::MessageQueue *))(*(_QWORD *)v6 + 120i64))(v6);
  v5 = v3;
  *v7 = CryptoPP::UnsignedMin<unsigned __int64,unsigned __int64>(v7, &v5);
  return v2;
}
