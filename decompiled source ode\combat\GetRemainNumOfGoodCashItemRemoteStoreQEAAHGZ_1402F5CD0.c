/*
 * Function: ?GetRemainNumOfGood@CashItemRemoteStore@@QEAAHG@Z
 * Address: 0x1402F5CD0
 */

__int64 __fastcall CashItemRemoteStore::GetRemainNumOfGood(CashItemRemoteStore *this, unsigned __int16 wStoreIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  int v5; // eax@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@6
  CashItemRemoteStore *v8; // [sp+40h] [bp+8h]@1
  unsigned __int16 v9; // [sp+48h] [bp+10h]@1

  v9 = wStoreIndex;
  v8 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v8->_pkRemainInfo )
  {
    v7 = wStoreIndex;
    v5 = CRecordData::GetRecordNum(&v8->_kRecGoods);
    if ( v7 < v5 )
      result = v8->_pkRemainInfo[v9].nRemainNum;
    else
      result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
