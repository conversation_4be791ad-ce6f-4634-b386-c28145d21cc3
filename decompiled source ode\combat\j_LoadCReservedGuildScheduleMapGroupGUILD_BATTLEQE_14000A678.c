/*
 * Function: j_?Load@CReservedGuildScheduleMapGroup@GUILD_BATTLE@@QEAA_NEAEAU_worlddb_guild_battle_reserved_schedule_info@@@Z
 * Address: 0x14000A678
 */

bool __fastcall GUILD_BATTLE::CReservedGuildScheduleMapGroup::Load(GUILD_BATTLE::CReservedGuildScheduleMapGroup *this, char byDayID, _worlddb_guild_battle_reserved_schedule_info *kInfo)
{
  return GUILD_BATTLE::CReservedGuildScheduleMapGroup::Load(this, byDayID, kInfo);
}
