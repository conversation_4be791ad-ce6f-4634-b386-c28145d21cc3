/*
 * Function: ?AlgorithmName@?$AlgorithmImpl@VCBC_Decryption@CryptoPP@@V?$CipherModeFinalTemplate_CipherHolder@V?$BlockCipherFinal@$00VDec@Rijndael@CryptoPP@@@CryptoPP@@VCBC_Decryption@2@@2@@CryptoPP@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
 * Address: 0x140453230
 */

std::basic_string<char,std::char_traits<char>,std::allocator<char> > *__fastcall CryptoPP::AlgorithmImpl<CryptoPP::CBC_Decryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption>>::AlgorithmName(CryptoPP::AlgorithmImpl<CryptoPP::CBC_Decryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption> > *this, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *result)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *resulta; // [sp+48h] [bp+10h]@1

  resulta = result;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption>::StaticAlgorithmName(result);
  return resulta;
}
