/*
 * Function: ?ClearDBRecord@CNormalGuildBattle@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403E3740
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattle::ClearDBRecord(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v3; // rax@5
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int dwMapID; // [sp+20h] [bp-18h]@4
  char byNumber; // [sp+28h] [bp-10h]@4
  GUILD_BATTLE::CNormalGuildBattle *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  byNumber = 0;
  dwMapID = 0;
  if ( CRFWorldDatabase::UpdateGuildBattleInfo(pkDB, v8->m_dwID, 0, 0, 0, 0) )
  {
    result = 1;
  }
  else
  {
    v3 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v3,
      "CNormalGuildBattle::ClearDBRecord()g_Main.m_pWorldDB->UpdateGuildBattleInfo( %u, 0, 0, 0, 0 ) Fail!",
      v8->m_dwID);
    result = 0;
  }
  return result;
}
