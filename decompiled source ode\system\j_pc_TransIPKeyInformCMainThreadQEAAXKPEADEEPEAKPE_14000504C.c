/*
 * Function: j_?pc_TransIPKeyInform@CMainThread@@QEAAXKPEADEEPEAKPEAU_GLBID@@K_NF0PEAU_SYSTEMTIME@@JE0E0E0E3H311@Z
 * Address: 0x14000504C
 */

void __fastcall CMainThread::pc_TransIPKeyInform(CMainThread *this, unsigned int dwAccountSerial, char *pszAccountID, char byUserDgr, char bySubDgr, unsigned int *pdwKey, _GLBID *pgidGlobal, unsigned int dwClientIP, bool bChatLock, __int16 iType, char *szCMS, _SYSTEMTIME *pstEndDate, int lRemainTime, char byUILock, char *szUILockPW, char byUILockFailCnt, char *szAccountPW, char byUILock_HintIndex, char *usz<PERSON><PERSON><PERSON>_HintAnswer, char byUILockFindPassFailCount, bool bIsPcBang, int nTrans, bool bAgeLimit, unsigned int *pdwRequestMoveCharacterSerialList, unsigned int *pdwTournamentCharacterSerialList)
{
  CMainThread::pc_TransIPKeyInform(
    this,
    dwAccountSerial,
    pszAccountID,
    byUserDgr,
    bySubDgr,
    pdwKey,
    pgidGlobal,
    dwClientIP,
    bChatLock,
    iType,
    szCMS,
    pstEndDate,
    lRemainTime,
    byUILock,
    szUILockPW,
    byUILockFailCnt,
    szAccountPW,
    byUILock_HintIndex,
    uszUILock_HintAnswer,
    byUILockFindPassFailCount,
    bIsPcBang,
    nTrans,
    bAgeLimit,
    pdwRequestMoveCharacterSerialList,
    pdwTournamentCharacterSerialList);
}
