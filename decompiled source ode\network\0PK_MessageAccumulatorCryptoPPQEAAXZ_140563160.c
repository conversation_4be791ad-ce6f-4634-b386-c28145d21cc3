/*
 * Function: ??0PK_MessageAccumulator@CryptoPP@@QEAA@XZ
 * Address: 0x140563160
 */

CryptoPP::PK_MessageAccumulator *__fastcall CryptoPP::PK_MessageAccumulator::PK_MessageAccumulator(CryptoPP::PK_MessageAccumulator *this)
{
  CryptoPP::PK_MessageAccumulator *v2; // [sp+30h] [bp+8h]@1

  v2 = this;
  CryptoPP::HashTransformation::HashTransformation((CryptoPP::HashTransformation *)&this->vfptr);
  return v2;
}
