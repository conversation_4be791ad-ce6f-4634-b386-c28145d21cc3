/*
 * Function: ?ReadRecord@CItemLootTable@@QEAA_NPEADPEAVCRecordData@@0@Z
 * Address: 0x140203900
 */

bool __fastcall CItemLootTable::ReadRecord(CItemLootTable *this, char *szFile, CRecordData *pItemRec, char *pszErrMsg)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v7; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@8
  CItemLootTable::_linker_code **v9; // [sp+28h] [bp-20h]@8
  unsigned __int64 v10; // [sp+30h] [bp-18h]@8
  CItemLootTable *v11; // [sp+50h] [bp+8h]@1
  CRecordData *pItemReca; // [sp+60h] [bp+18h]@1
  char *pszErrMsga; // [sp+68h] [bp+20h]@1

  pszErrMsga = pszErrMsg;
  pItemReca = pItemRec;
  v11 = this;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( CRecordData::ReadRecord(&v11->m_tblLoot, szFile, 0x694u, pszErrMsg) )
  {
    v11->m_nLootNum = CRecordData::GetRecordNum(&v11->m_tblLoot);
    if ( v11->m_nLootNum > 0 )
    {
      __trace("loot rec num: %d", v11->m_nLootNum);
      v10 = v11->m_nLootNum;
      v9 = (CItemLootTable::_linker_code **)operator new[](saturated_mul(8ui64, v10));
      v11->m_ppLinkCode = v9;
      for ( j = 0; j < v11->m_nLootNum; ++j )
        v11->m_ppLinkCode[j] = 0i64;
      CEventLootTable::ReadRecord(v11->m_pTblEvent);
      result = CItemLootTable::Indexing(v11, pItemReca, pszErrMsga);
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
