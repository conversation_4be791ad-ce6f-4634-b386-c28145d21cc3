/*
 * Function: ?CheatCancelRegist@CUnmannedTraderUserInfoTable@@QEAA_NGKE@Z
 * Address: 0x140363A00
 */

bool __fastcall CUnmannedTraderUserInfoTable::CheatCancelRegist(CUnmannedTraderUserInfoTable *this, unsigned __int16 wInx, unsigned int dwOwnerSerial, char byNth)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@7
  __int64 v7; // [sp+0h] [bp-38h]@1
  CUnmannedTraderUserInfo *v8; // [sp+20h] [bp-18h]@4
  CPlayer *v9; // [sp+28h] [bp-10h]@4
  CUnmannedTraderUserInfoTable *v10; // [sp+40h] [bp+8h]@1
  char v11; // [sp+58h] [bp+20h]@1

  v11 = byNth;
  v10 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = CUnmannedTraderUserInfoTable::FindUser(v10, wInx, dwOwnerSerial);
  v9 = 0i64;
  if ( !CUnmannedTraderUserInfo::IsNull(v8) && (v9 = CUnmannedTraderUserInfo::FindOwner(v8)) != 0i64 && v9->m_bOper )
    result = CUnmannedTraderUserInfo::CheatCancelRegist(v8, v11);
  else
    result = 0;
  return result;
}
