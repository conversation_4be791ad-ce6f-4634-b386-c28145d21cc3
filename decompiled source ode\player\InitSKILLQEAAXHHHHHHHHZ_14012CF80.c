/*
 * Function: ?Init@SKILL@@QEAAXHHHHHHHH@Z
 * Address: 0x14012CF80
 */

void __fastcall SKILL::Init(SKILL *this, int type, int dmg, int minprob, int maxprob, int len, int castdelay, int delay, int el)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v11; // [sp+0h] [bp-38h]@1
  int v12; // [sp+20h] [bp-18h]@4
  float v13; // [sp+24h] [bp-14h]@7
  SKILL *v14; // [sp+40h] [bp+8h]@1
  int v15; // [sp+58h] [bp+20h]@1

  v15 = minprob;
  v14 = this;
  v9 = &v11;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v14->m_bLoad = 1;
  v14->m_Type = 0;
  v14->m_Element = el;
  v14->m_StdDmg = dmg;
  v14->m_IsCritical = 0;
  v12 = (signed int)ffloor((float)v14->m_StdDmg * 0.89999998);
  if ( v14->m_StdDmg - v12 <= 0 )
    v14->m_MinDmg = 0;
  else
    v14->m_MinDmg = rand() % (v14->m_StdDmg - v12) + v12;
  v14->m_MaxDmg = 2 * v14->m_StdDmg - v14->m_MinDmg;
  v13 = (float)(v14->m_MaxDmg + 125) / (float)(v14->m_MaxDmg + 50);
  v14->m_CritDmg = (signed int)ffloor((float)((float)v14->m_MaxDmg * v13) + 0.5);
  v14->m_MinProb = v15;
  v14->m_MaxProb = maxprob;
  v14->m_Len = len;
  v14->m_CastDelay = castdelay;
  v14->m_Delay = delay;
  v14->m_Active = 1;
  v14->m_BefTime = 0;
}
