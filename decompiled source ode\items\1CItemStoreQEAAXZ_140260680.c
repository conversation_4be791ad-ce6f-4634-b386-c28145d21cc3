/*
 * Function: ??1CItemStore@@QEAA@XZ
 * Address: 0x140260680
 */

void __fastcall CItemStore::~CItemStore(CItemStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  void *v4; // [sp+20h] [bp-18h]@5
  void *v5; // [sp+28h] [bp-10h]@7
  CItemStore *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_pStorageItem )
  {
    v4 = v6->m_pStorageItem;
    operator delete[](v4);
    v6->m_pStorageItem = 0i64;
  }
  if ( v6->m_pLimitStorageItem )
  {
    v5 = v6->m_pLimitStorageItem;
    operator delete[](v5);
    v6->m_pLimitStorageItem = 0i64;
  }
}
