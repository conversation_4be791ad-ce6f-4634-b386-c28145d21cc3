/*
 * Function: j_?deallocate@?$allocator@PEAVCUnmannedTraderSortType@@@std@@QEAAXPEAPEAVCUnmannedTraderSortType@@_K@Z
 * Address: 0x14000C47D
 */

void __fastcall std::allocator<CUnmannedTraderSortType *>::deallocate(std::allocator<CUnmannedTraderSortType *> *this, CUnmannedTraderSortType **_Ptr, unsigned __int64 __formal)
{
  std::allocator<CUnmannedTraderSortType *>::deallocate(this, _Ptr, __formal);
}
