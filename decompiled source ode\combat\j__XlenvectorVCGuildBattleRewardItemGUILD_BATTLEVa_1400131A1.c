/*
 * Function: j_?_<PERSON><PERSON>@?$vector@VCGuildBattleRewardItem@GUILD_BATTLE@@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@std@@KAXXZ
 * Address: 0x1400131A1
 */

void __noreturn std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_<PERSON>len()
{
  std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_<PERSON><PERSON>();
}
