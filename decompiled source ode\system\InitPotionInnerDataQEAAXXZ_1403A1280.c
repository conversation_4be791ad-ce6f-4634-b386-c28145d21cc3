/*
 * Function: ?Init@PotionInnerData@@QEAAXXZ
 * Address: 0x1403A1280
 */

void __fastcall PotionInnerData::Init(PotionInnerData *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  PotionInnerData *Dst; // [sp+30h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(Dst, 0, 0x26ui64);
  Dst->m_bPotionDelayIndexList[0] = 1;
  Dst->m_bPotionDelayIndexList[1] = 1;
  Dst->m_bPotionDelayIndexList[2] = 1;
  Dst->m_bPotionDelayIndexList[3] = 1;
  Dst->m_bPotionDelayIndexList[4] = 1;
  Dst->m_bPotionDelayIndexList[5] = 1;
  Dst->m_bPotionDelayIndexList[6] = 1;
  Dst->m_bPotionDelayIndexList[7] = 1;
  Dst->m_bPotionDelayIndexList[8] = 1;
  Dst->m_bPotionDelayIndexList[9] = 1;
  Dst->m_bPotionDelayIndexList[10] = 1;
  Dst->m_bPotionDelayIndexList[11] = 1;
  Dst->m_bPotionDelayIndexList[12] = 1;
  Dst->m_bPotionDelayIndexList[13] = 1;
  Dst->m_bPotionDelayIndexList[14] = 1;
  Dst->m_bPotionDelayIndexList[15] = 1;
  Dst->m_bPotionDelayIndexList[16] = 1;
  Dst->m_bPotionDelayIndexList[17] = 1;
  Dst->m_bPotionDelayIndexList[18] = 1;
  Dst->m_bPotionDelayIndexList[19] = 1;
  Dst->m_bPotionDelayIndexList[20] = 1;
  Dst->m_bPotionDelayIndexList[21] = 1;
  Dst->m_bPotionDelayIndexList[22] = 1;
  Dst->m_bPotionDelayIndexList[23] = 1;
  Dst->m_bPotionDelayIndexList[24] = 1;
  Dst->m_bPotionDelayIndexList[25] = 1;
  Dst->m_bPotionDelayIndexList[26] = 1;
  Dst->m_bPotionDelayIndexList[27] = 1;
  Dst->m_bPotionDelayIndexList[28] = 1;
  Dst->m_bPotionDelayIndexList[33] = 1;
  Dst->m_bPotionDelayIndexList[34] = 1;
  Dst->m_bPotionDelayIndexList[35] = 1;
  Dst->m_bPotionDelayIndexList[36] = 1;
  Dst->m_bPotionDelayIndexList[37] = 1;
}
