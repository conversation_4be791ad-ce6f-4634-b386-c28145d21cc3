/*
 * Function: ?InitFunction<PERSON>ey@@YAXPEAD@Z
 * Address: 0x140511B40
 */

void __usercall InitFunctionKey(char *a1@<rcx>, int a2@<xmm0>)
{
  char *v2; // rsi@1
  __int64 v3; // rcx@3
  char *v4; // rdi@3
  char v5; // al@4
  FILE *v6; // rax@5
  FILE *v7; // rbp@5
  signed __int64 v8; // rsi@11
  signed int v9; // ebx@11
  char *v10; // rdx@12
  int v11; // eax@13
  int v12; // ecx@13
  char String; // [sp+20h] [bp-328h]@7
  __int64 Filename; // [sp+120h] [bp-228h]@1
  __int16 v15; // [sp+128h] [bp-220h]@1
  char Dst; // [sp+12Ah] [bp-21Eh]@1
  int v17; // [sp+130h] [bp-218h]@2
  char v18; // [sp+134h] [bp-214h]@2
  char Dest; // [sp+220h] [bp-128h]@12

  v2 = a1;
  Filename = *(_QWORD *)((char *)&_ImageBase.unused + (_QWORD)&a_System[-5368709120i64]);
  v15 = *(_WORD *)((char *)&_ImageBase.unused + (_QWORD)&a_System[-5368709112i64]);
  memset_0(&Dst, 0, 0xF6ui64);
  memset_0(dword_184A890F0, 0, 0x30ui64);
  if ( v2 )
  {
    v4 = (char *)&Filename + strlen((const char *)&Filename) + 1;
    v3 = 0i64;
    do
    {
      v5 = v2[v3++];
      v4[v3 - 2] = v5;
    }
    while ( v5 );
  }
  else
  {
    Filename = *(_QWORD *)((char *)&_ImageBase.unused + (_QWORD)&a_SystemDefault[-5368709120i64]);
    *(_QWORD *)&v15 = *(_QWORD *)((char *)&_ImageBase.unused + (_QWORD)&a_SystemDefault[-5368709112i64]);
    v17 = *(int *)((char *)&_ImageBase.unused + (_QWORD)&a_SystemDefault[-5368709104i64]);
    v18 = *((_BYTE *)&_ImageBase.unused + (_QWORD)&a_SystemDefault[-5368709100i64]);
  }
  v6 = fopen((const char *)&Filename, "rt");
  v7 = v6;
  if ( v6 )
  {
    if ( fscanf(v6, "%s", &String) != -1 )
    {
      do
      {
        if ( !memcmp("*BindHotKey", &String, 0xCui64) )
        {
          fscanf(v7, "%s", &String);
          while ( memcmp(&String, "}", 2ui64) )
          {
            _strupr(&String);
            if ( String == 70 )
            {
              v8 = 0i64;
              v9 = 1;
              while ( 1 )
              {
                sprintf(&Dest, "F%1d", (unsigned int)v9);
                v10 = &String;
                do
                {
                  v11 = (unsigned __int8)v10[&Dest - &String];
                  v12 = (unsigned __int8)*v10 - v11;
                  if ( (unsigned __int8)*v10 != v11 )
                    break;
                  ++v10;
                }
                while ( v11 );
                if ( !v12 )
                  break;
                ++v8;
                ++v9;
                if ( v8 >= 12 )
                  goto LABEL_19;
              }
              fscanf(v7, "%s", &String);
              dword_184A890F0[v8] = sub_140511900(&String, a2);
            }
LABEL_19:
            fscanf(v7, "%s", &String);
          }
        }
      }
      while ( fscanf(v7, "%s", &String) != -1 );
    }
    fclose(v7);
  }
  else
  {
    InsertConsoleStringQ("can't open hotkey!");
  }
}
