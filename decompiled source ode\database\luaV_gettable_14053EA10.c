/*
 * Function: luaV_gettable
 * Address: 0x14053EA10
 */

__int64 __fastcall luaV_gettable(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  __int64 v4; // r13@1
  __int64 v5; // r14@1
  __int64 v6; // rbx@1
  __int64 v7; // r12@1
  signed int v8; // esi@1
  __int64 v9; // rdi@3
  __int64 v10; // rax@3
  __int64 v11; // rbp@3
  __int64 v12; // rcx@4
  __int64 v13; // rax@6
  __int64 v14; // r8@6
  __int64 v15; // r9@6
  __int64 v16; // rdi@6
  __int64 result; // rax@7
  __int64 v18; // rax@8

  v4 = a4;
  v5 = a3;
  v6 = a2;
  v7 = a1;
  v8 = 0;
  while ( 1 )
  {
    if ( *(_DWORD *)(v6 + 8) == 5 )
    {
      v9 = *(_QWORD *)v6;
      LODWORD(v10) = luaH_get(*(_QWORD *)v6, v5);
      v11 = v10;
      if ( *(_DWORD *)(v10 + 8)
        || (v12 = *(_QWORD *)(v9 + 16)) == 0
        || *(_BYTE *)(v12 + 10) & 1
        || (LODWORD(v13) = luaT_gettm(v12, 0, *(_QWORD *)(*(_QWORD *)(v7 + 32) + 296i64)), (v16 = v13) == 0) )
      {
        *(_QWORD *)v4 = *(_QWORD *)v11;
        result = *(_DWORD *)(v11 + 8);
        *(_DWORD *)(v4 + 8) = result;
        return result;
      }
    }
    else
    {
      LODWORD(v18) = luaT_gettmbyobj(v7, v6, 0);
      v16 = v18;
      if ( !*(_DWORD *)(v18 + 8) )
        luaG_typeerror(v7, v6, (__int64)"index");
    }
    if ( *(_DWORD *)(v16 + 8) == 6 )
      break;
    ++v8;
    v6 = v16;
    if ( v8 >= 100 )
      luaG_runerror(v7, (__int64)"loop in gettable", v14, v15);
  }
  return sub_14053E8D0(v7, v4, v16, v6, v5);
}
