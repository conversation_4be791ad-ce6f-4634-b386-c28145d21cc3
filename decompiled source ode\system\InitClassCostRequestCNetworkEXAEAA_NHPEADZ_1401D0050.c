/*
 * Function: ?InitClassCostRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D0050
 */

char __fastcall CNetworkEX::InitClassCostRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-88h]@1
  CPlayer *v7; // [sp+30h] [bp-58h]@4
  char szMsg[4]; // [sp+44h] [bp-44h]@6
  char pbyType; // [sp+64h] [bp-24h]@6
  char v10; // [sp+65h] [bp-23h]@6

  v3 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = &g_Player + n;
  if ( v7->m_bOper )
  {
    *(_DWORD *)szMsg = CPlayer::GetInitClassCost(v7);
    pbyType = 11;
    v10 = 27;
    CNetProcess::LoadSendMsg(unk_1414F2088, v7->m_ObjID.m_wIndex, &pbyType, szMsg, 4u);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
