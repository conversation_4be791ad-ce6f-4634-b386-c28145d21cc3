/*
 * Function: ?DrawBBox@CLevel@@QEAAXQEAF0K@Z
 * Address: 0x1404E2670
 */

void __fastcall CLevel::DrawBBox(CLevel *this, __int16 *const a2, __int16 *const a3, int a4)
{
  float v4; // xmm1_4@1
  signed __int64 v5; // rax@1
  float v6; // [sp+20h] [bp-28h]@1
  float v7; // [sp+24h] [bp-24h]@1
  float v8; // [sp+28h] [bp-20h]@1
  float v9; // [sp+30h] [bp-18h]@1

  v4 = (float)a2[1];
  v6 = (float)*a2;
  v5 = (unsigned int)a2[2];
  v7 = v4;
  v8 = (float)(signed int)v5;
  CLevel::DrawBBox(this, &v6, &v9, v5, a4);
}
