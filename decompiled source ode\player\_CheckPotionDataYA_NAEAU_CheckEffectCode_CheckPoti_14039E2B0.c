/*
 * Function: ?_CheckPotionData@@YA_NAEAU_CheckEffectCode@_CheckPotion_fld@@PEAVCPlayer@@@Z
 * Address: 0x14039E2B0
 */

char __usercall _CheckPotionData@<al>(_CheckPotion_fld::_CheckEffectCode *CheckEffectNode@<rcx>, CPlayer *pApplyPlayer@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-78h]@1
  float v7; // [sp+20h] [bp-58h]@11
  float v8; // [sp+24h] [bp-54h]@20
  bool v9; // [sp+28h] [bp-50h]@29
  int v10; // [sp+2Ch] [bp-4Ch]@6
  int v11; // [sp+30h] [bp-48h]@12
  int v12; // [sp+34h] [bp-44h]@14
  int v13; // [sp+38h] [bp-40h]@16
  int v14; // [sp+3Ch] [bp-3Ch]@18
  int v15; // [sp+40h] [bp-38h]@19
  int v16; // [sp+44h] [bp-34h]@21
  int v17; // [sp+48h] [bp-30h]@23
  int v18; // [sp+4Ch] [bp-2Ch]@25
  int v19; // [sp+50h] [bp-28h]@27
  int v20; // [sp+54h] [bp-24h]@28
  float v21; // [sp+58h] [bp-20h]@31
  int v22; // [sp+5Ch] [bp-1Ch]@33
  float v23; // [sp+60h] [bp-18h]@35
  int v24; // [sp+64h] [bp-14h]@37
  _CheckPotion_fld::_CheckEffectCode *v25; // [sp+80h] [bp+8h]@1

  v25 = CheckEffectNode;
  v3 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pApplyPlayer )
  {
    v10 = v25->m_nContParamCode;
    if ( v10 == -1 )
    {
      result = 1;
    }
    else if ( v10 )
    {
      if ( v10 == 1 )
      {
        _effect_parameter::GetEff_Plus(&pApplyPlayer->m_EP, v25->m_nContParamIndex);
        v8 = a3;
        if ( v25->m_nContValSt == -1 )
        {
          v16 = v25->m_fContValue > v8;
          result = v16;
        }
        else if ( v25->m_nContValSt )
        {
          if ( v25->m_nContValSt == 1 )
          {
            v18 = v25->m_fContValue == v8;
            result = v18;
          }
          else if ( v25->m_nContValSt == 2 )
          {
            v19 = v8 >= v25->m_fContValue;
            result = v19;
          }
          else
          {
            v20 = v8 > v25->m_fContValue;
            result = v20;
          }
        }
        else
        {
          v17 = v25->m_fContValue >= v8;
          result = v17;
        }
      }
      else if ( v10 == 2 )
      {
        v9 = _effect_parameter::GetEff_State(&pApplyPlayer->m_EP, v25->m_nContParamIndex);
        if ( v25->m_nContValSt == 1 )
        {
          if ( v9 )
            v21 = FLOAT_1_0;
          else
            v21 = 0.0;
          v22 = v25->m_fContValue == v21;
          result = v22;
        }
        else
        {
          if ( v9 )
            v23 = 0.0;
          else
            v23 = FLOAT_1_0;
          v24 = v25->m_fContValue == v23;
          result = v24;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      _effect_parameter::GetEff_Rate(&pApplyPlayer->m_EP, v25->m_nContParamIndex);
      v7 = a3;
      if ( v25->m_nContValSt == -1 )
      {
        v11 = v25->m_fContValue > v7;
        result = v11;
      }
      else if ( v25->m_nContValSt )
      {
        if ( v25->m_nContValSt == 1 )
        {
          v13 = v25->m_fContValue == v7;
          result = v13;
        }
        else if ( v25->m_nContValSt == 2 )
        {
          v14 = v7 >= v25->m_fContValue;
          result = v14;
        }
        else
        {
          v15 = v7 > v25->m_fContValue;
          result = v15;
        }
      }
      else
      {
        v12 = v25->m_fContValue >= v7;
        result = v12;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
