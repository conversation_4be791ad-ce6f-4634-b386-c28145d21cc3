/*
 * Function: ?DrawMapR<PERSON>@CLevel@@QEAAXXZ
 * Address: 0x1404E0850
 */

void __fastcall CLevel::DrawMapRender(CLevel *this)
{
  CLevel *v1; // rsi@1
  struct IDirect3DDevice8 *v2; // rax@1
  IUnknownVtbl *v3; // rdi@1
  struct IDirect3DDevice8 *v4; // rbx@1
  unsigned __int32 v5; // eax@1

  v1 = this;
  v2 = GetD3dDevice();
  v3 = v2->vfptr;
  v4 = v2;
  v5 = CN_GetFieldColor();
  ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, _QWORD))v3[16].Release)(v4, 60i64, v5);
  CBsp::DrawBspRender(v1->mBsp);
  CBsp::DrawDynamicLights(v1->mBsp);
  RTMovieRender();
}
