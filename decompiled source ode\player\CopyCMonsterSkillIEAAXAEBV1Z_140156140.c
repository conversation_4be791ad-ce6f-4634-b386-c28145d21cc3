/*
 * Function: ?Copy@CMonsterSkill@@IEAAXAEBV1@@Z
 * Address: 0x140156140
 */

void __fastcall CMonsterSkill::Copy(CMonsterSkill *this, CMonsterSkill *Cls)
{
  this->m_bExit = Cls->m_bExit;
  this->m_UseType = Cls->m_UseType;
  this->m_nSFCode = Cls->m_nSFCode;
  this->m_wSFIndex = Cls->m_wSFIndex;
  this->m_pSF_Fld = Cls->m_pSF_Fld;
  this->m_BefTime = Cls->m_BefTime;
  this->m_dwDelayTime = Cls->m_dwDelayTime;
  this->m_fAttackDist = Cls->m_fAttackDist;
  this->m_dwCastDelay = Cls->m_dwCastDelay;
  this->m_nSFLv = Cls->m_nSFLv;
  this->m_Element = Cls->m_Element;
  this->m_StdDmg = Cls->m_StdDmg;
  this->m_MinDmg = Cls->m_MinDmg;
  this->m_MaxDmg = Cls->m_MaxDmg;
  this->m_MinProb = Cls->m_MinProb;
  this->m_MaxProb = Cls->m_MaxProb;
  this->m_nMotive = Cls->m_nMotive;
  this->m_nMotivevalue = Cls->m_nMotivevalue;
  this->m_nCaseType = Cls->m_nCaseType;
  this->m_nAccumulationCount = Cls->m_nAccumulationCount;
  this->m_pSPConst = Cls->m_pSPConst;
}
