/*
 * Function: SQLInstallTranslator
 * Address: 0x1404DAAD4
 */

int __fastcall SQLInstallTranslator(const char *lpszInfFile, const char *lpszTranslator, const char *lpszPathIn, char *lpszPathOut, unsigned __int16 cbPathOutMax, unsigned __int16 *pcbPathOut, unsigned __int16 fRequest, unsigned int *lpdwUsageCount)
{
  const char *v8; // rbp@1
  char *v9; // rbx@1
  const char *v10; // rdi@1
  const char *v11; // rsi@1
  __int64 (__cdecl *v12)(); // rax@1
  int result; // eax@2

  v8 = lpszInfFile;
  v9 = lpszPathOut;
  v10 = lpszPathIn;
  v11 = lpszTranslator;
  v12 = ODBC___GetSetupProc("SQLInstallTranslator");
  if ( v12 )
    result = ((int (__fastcall *)(const char *, const char *, const char *, char *))v12)(v8, v11, v10, v9);
  else
    result = 0;
  return result;
}
