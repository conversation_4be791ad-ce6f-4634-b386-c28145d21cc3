/*
 * Function: ?Insert_Economy_History@CRFWorldDatabase@@QEAA_NKPEAU_worlddb_economy_history_info@_worlddb_economy_history_info_array@@@Z
 * Address: 0x140493810
 */

bool __fastcall CRFWorldDatabase::Insert_Economy_History(CRFWorldDatabase *this, unsigned int dwDate, _worlddb_economy_history_info_array::_worlddb_economy_history_info *pEconomyData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-408h]@1
  long double v7; // [sp+20h] [bp-3E8h]@5
  long double v8; // [sp+28h] [bp-3E0h]@5
  long double v9; // [sp+30h] [bp-3D8h]@5
  long double v10; // [sp+38h] [bp-3D0h]@5
  long double v11; // [sp+40h] [bp-3C8h]@5
  unsigned int v12; // [sp+48h] [bp-3C0h]@5
  long double v13; // [sp+50h] [bp-3B8h]@5
  long double v14; // [sp+58h] [bp-3B0h]@5
  long double v15; // [sp+60h] [bp-3A8h]@5
  long double v16; // [sp+68h] [bp-3A0h]@5
  long double v17; // [sp+70h] [bp-398h]@5
  long double v18; // [sp+78h] [bp-390h]@5
  long double v19; // [sp+80h] [bp-388h]@5
  long double v20; // [sp+88h] [bp-380h]@5
  long double v21; // [sp+90h] [bp-378h]@5
  long double v22; // [sp+98h] [bp-370h]@5
  long double v23; // [sp+A0h] [bp-368h]@5
  long double v24; // [sp+A8h] [bp-360h]@5
  long double v25; // [sp+B0h] [bp-358h]@5
  long double v26; // [sp+B8h] [bp-350h]@5
  long double v27; // [sp+C0h] [bp-348h]@5
  long double v28; // [sp+C8h] [bp-340h]@5
  long double v29; // [sp+D0h] [bp-338h]@5
  long double v30; // [sp+D8h] [bp-330h]@5
  char Dest; // [sp+F0h] [bp-318h]@4
  char v32; // [sp+F1h] [bp-317h]@4
  _worlddb_economy_history_info_array::_worlddb_economy_history_info pEconomyDataa; // [sp+310h] [bp-F8h]@4
  char v34; // [sp+3E4h] [bp-24h]@4
  unsigned __int64 v35; // [sp+3F0h] [bp-18h]@4
  CRFWorldDatabase *v36; // [sp+410h] [bp+8h]@1
  unsigned int dwDatea; // [sp+418h] [bp+10h]@1
  _worlddb_economy_history_info_array::_worlddb_economy_history_info *v38; // [sp+420h] [bp+18h]@1

  v38 = pEconomyData;
  dwDatea = dwDate;
  v36 = this;
  v3 = &v6;
  for ( i = 256i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v35 = (unsigned __int64)&v6 ^ _security_cookie;
  Dest = 0;
  memset(&v32, 0, 0x1FFui64);
  v34 = CRFWorldDatabase::Select_Exist_Economy(v36, dwDate, &pEconomyDataa);
  if ( v34 == 2 )
  {
    v30 = v38->dCutOre[2][2];
    v29 = v38->dCutOre[1][2];
    v28 = v38->dCutOre[0][2];
    v27 = v38->dCutOre[2][1];
    v26 = v38->dCutOre[1][1];
    v25 = v38->dCutOre[0][1];
    v24 = v38->dCutOre[2][0];
    v23 = v38->dCutOre[1][0];
    v22 = v38->dCutOre[0][0];
    v21 = v38->dMineOre[2][2];
    v20 = v38->dMineOre[1][2];
    v19 = v38->dMineOre[0][2];
    v18 = v38->dMineOre[2][1];
    v17 = v38->dMineOre[1][1];
    v16 = v38->dMineOre[0][1];
    v15 = v38->dMineOre[2][0];
    v14 = v38->dMineOre[1][0];
    v13 = v38->dMineOre[0][0];
    v12 = v38->dwManageValue;
    v11 = v38->dTradeGold[2];
    v10 = v38->dTradeDalant[2];
    v9 = v38->dTradeGold[1];
    v8 = v38->dTradeDalant[1];
    v7 = v38->dTradeGold[0];
    sprintf(
      &Dest,
      "{ CALL pInsert_Economy_History( %d, %f, %f, %f, %f, %f, %f, %d, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f"
      ", %f, %f, %f, %f, %f ) }",
      dwDatea,
      v38->dTradeDalant[0]);
    result = CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v36->vfptr, &Dest, 1);
  }
  else if ( v34 )
  {
    result = 0;
  }
  else
  {
    v30 = v38->dCutOre[2][2];
    v29 = v38->dCutOre[1][2];
    v28 = v38->dCutOre[0][2];
    v27 = v38->dCutOre[2][1];
    v26 = v38->dCutOre[1][1];
    v25 = v38->dCutOre[0][1];
    v24 = v38->dCutOre[2][0];
    v23 = v38->dCutOre[1][0];
    v22 = v38->dCutOre[0][0];
    v21 = v38->dMineOre[2][2];
    v20 = v38->dMineOre[1][2];
    v19 = v38->dMineOre[0][2];
    v18 = v38->dMineOre[2][1];
    v17 = v38->dMineOre[1][1];
    v16 = v38->dMineOre[0][1];
    v15 = v38->dMineOre[2][0];
    v14 = v38->dMineOre[1][0];
    v13 = v38->dMineOre[0][0];
    v12 = v38->dwManageValue;
    v11 = v38->dTradeGold[2];
    v10 = v38->dTradeDalant[2];
    v9 = v38->dTradeGold[1];
    v8 = v38->dTradeDalant[1];
    v7 = v38->dTradeGold[0];
    sprintf(
      &Dest,
      "{ CALL pUpdate_Economy_History( %d, %f, %f, %f, %f, %f, %f, %d, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f, %f"
      ", %f, %f, %f, %f, %f ) }",
      dwDatea,
      v38->dTradeDalant[0]);
    result = CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v36->vfptr, &Dest, 1);
  }
  return result;
}
