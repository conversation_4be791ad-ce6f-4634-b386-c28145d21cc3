/*
 * Function: ?InputRecoverableMessage@?$DL_SignerBase@UECPPoint@CryptoPP@@@CryptoPP@@UEBAXAEAVPK_MessageAccumulator@2@PEBE_K@Z
 * Address: 0x140564610
 */

int __fastcall CryptoPP::DL_SignerBase<CryptoPP::ECPPoint>::InputRecoverableMessage(__int64 a1, __int64 a2, const char *a3, unsigned __int64 a4)
{
  __int64 v4; // ST40_8@1
  __int64 v5; // rax@1
  __int64 v6; // ST48_8@1
  __int64 v7; // rax@1
  __int64 v9; // [sp+90h] [bp+8h]@1
  char *t; // [sp+A0h] [bp+18h]@1
  unsigned __int64 len; // [sp+A8h] [bp+20h]@1

  len = a4;
  t = (char *)a3;
  v9 = a1;
  v4 = a2;
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::Assign(
    (CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(a2 + 8),
    a3,
    a4);
  LODWORD(v5) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v9 + 128i64))(v9);
  v6 = v5;
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v4 + 56));
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v4 + 56));
  LODWORD(v7) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v4 + 144i64))(v4);
  return (*(int (__fastcall **)(__int64, __int64, char *, unsigned __int64))(*(_QWORD *)v6 + 40i64))(v6, v7, t, len);
}
