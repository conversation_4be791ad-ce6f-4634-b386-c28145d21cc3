/*
 * Function: ?GetField@CNormalGuildBattleFieldList@GUILD_BATTLE@@QEAAPEAVCNormalGuildBattleField@2@K@Z
 * Address: 0x1403EE950
 */

GUILD_BATTLE::CNormalGuildBattleField *__fastcall GUILD_BATTLE::CNormalGuildBattleFieldList::GetField(GUILD_BATTLE::CNormalGuildBattleFieldList *this, unsigned int dwMapID)
{
  GUILD_BATTLE::CNormalGuildBattleField *result; // rax@3

  if ( this->m_dwCnt > dwMapID && this->m_pkField )
    result = &this->m_pkField[dwMapID];
  else
    result = 0i64;
  return result;
}
