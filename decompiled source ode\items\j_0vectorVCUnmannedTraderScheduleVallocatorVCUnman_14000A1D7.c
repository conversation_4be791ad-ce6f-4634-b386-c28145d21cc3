/*
 * Function: j_??0?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@QEAA@XZ
 * Address: 0x14000A1D7
 */

void __fastcall std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this)
{
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(this);
}
