/*
 * Function: j_?Select_GuildMemberData@CRFWorldDatabase@@QEAA_NGKPEAU_worlddb_guild_member_info@@@Z
 * Address: 0x1400092F5
 */

bool __fastcall CRFWorldDatabase::Select_GuildMemberData(CRFWorldDatabase *this, unsigned __int16 wMaxMember, unsigned int dwGuildSerial, _worlddb_guild_member_info *pGuildMemberInfo)
{
  return CRFWorldDatabase::Select_GuildMemberData(this, wMaxMember, dwGuildSerial, pGuildMemberInfo);
}
