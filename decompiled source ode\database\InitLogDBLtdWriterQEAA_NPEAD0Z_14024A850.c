/*
 * Function: ?InitLogDB@LtdWriter@@QEAA_NPEAD0@Z
 * Address: 0x14024A850
 */

char __fastcall LtdWriter::InitLogDB(LtdWriter *this, char *szDBName, char *szIP)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@5
  __int64 v6; // rax@5
  char result; // al@8
  CNationSettingManager *v8; // rax@9
  CNationSettingManager *v9; // rax@9
  const char *v10; // rax@9
  __int64 v11; // [sp+0h] [bp-68h]@1
  unsigned __int16 wPort; // [sp+20h] [bp-48h]@7
  CRFDBItemLog *v13; // [sp+30h] [bp-38h]@7
  CRFDBItemLog *v14; // [sp+38h] [bp-30h]@4
  __int64 v15; // [sp+40h] [bp-28h]@4
  CRFDBItemLog *v16; // [sp+48h] [bp-20h]@5
  char *passWord; // [sp+50h] [bp-18h]@9
  LtdWriter *v18; // [sp+70h] [bp+8h]@1
  const char *szDSN; // [sp+78h] [bp+10h]@1
  const char *szServer; // [sp+80h] [bp+18h]@1

  szServer = szIP;
  szDSN = szDBName;
  v18 = this;
  v3 = &v11;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = -2i64;
  v14 = (CRFDBItemLog *)operator new(0x400ui64);
  if ( v14 )
  {
    v5 = LtdWriter::_GetLocalDate(v18);
    CRFDBItemLog::CRFDBItemLog(v14, v5);
    v16 = (CRFDBItemLog *)v6;
  }
  else
  {
    v16 = 0i64;
  }
  v13 = v16;
  v18->m_pLtdDB = v16;
  wPort = -4103;
  if ( CRFNewDatabase::ConfigUserODBC((CRFNewDatabase *)&v18->m_pLtdDB->vfptr, szDSN, szServer, szDSN, 0xEFF9u) )
  {
    v8 = CTSingleton<CNationSettingManager>::Instance();
    passWord = (char *)CNationSettingManager::GetWorldDBPW(v8);
    v9 = CTSingleton<CNationSettingManager>::Instance();
    v10 = CNationSettingManager::GetWorldDBID(v9);
    if ( CRFNewDatabase::StartDataBase((CRFNewDatabase *)&v18->m_pLtdDB->vfptr, szDSN, v10, passWord) )
    {
      v18->m_bInitDB = 1;
      result = 1;
    }
    else
    {
      CLogFile::Write(&v18->m_logLtdWriter, "LtdWriter::Failed call StartDataBase()");
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(&v18->m_logLtdWriter, "LtdWriter::Failed call ConfigUserODBC()");
    result = 0;
  }
  return result;
}
