/*
 * Function: j_?Select_UnmannedTraderSellInfo@CRFWorldDatabase@@QEAAEEKEAEAU_unmannedtrader_seller_info@@@Z
 * Address: 0x140010DB1
 */

char __fastcall CRFWorldDatabase::Select_UnmannedTraderSellInfo(CRFWorldDatabase *this, char byType, unsigned int dwRegistSerial, char byRace, _unmannedtrader_seller_info *kData)
{
  return CRFWorldDatabase::Select_UnmannedTraderSellInfo(this, byType, dwRegistSerial, byRace, kData);
}
