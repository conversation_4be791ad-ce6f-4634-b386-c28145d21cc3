/*
 * Function: ?db_Update_Avator@CMainThread@@QEAAEKPEAU_AVATOR_DATA@@0_N@Z
 * Address: 0x1401A5360
 */

char __fastcall CMainThread::db_Update_Avator(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, bool bCheckLowHigh)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // rax@43
  int v9; // eax@45
  int v10; // ecx@45
  int v11; // edx@45
  unsigned int v12; // er8@45
  __int64 v13; // [sp+0h] [bp-9E8h]@1
  char *pSzQuery; // [sp+20h] [bp-9C8h]@29
  bool v15[8]; // [sp+28h] [bp-9C0h]@33
  unsigned __int16 wDiePoint[4]; // [sp+30h] [bp-9B8h]@44
  long double v17; // [sp+38h] [bp-9B0h]@45
  long double v18; // [sp+40h] [bp-9A8h]@60
  long double v19; // [sp+48h] [bp-9A0h]@60
  char *v20; // [sp+50h] [bp-998h]@60
  char v21; // [sp+60h] [bp-988h]@74
  CCheckSumCharacAccountTrunkData v22; // [sp+78h] [bp-970h]@35
  double v23; // [sp+B8h] [bp-930h]@40
  char szError; // [sp+D0h] [bp-918h]@59
  char v25; // [sp+D1h] [bp-917h]@59
  _worlddb_update_char_query pUpdateQuery; // [sp+8F0h] [bp-F8h]@73
  char v27; // [sp+9B0h] [bp-38h]@36
  char v28; // [sp+9B1h] [bp-37h]@60
  char v29; // [sp+9B2h] [bp-36h]@62
  char v30; // [sp+9B3h] [bp-35h]@64
  char v31; // [sp+9B4h] [bp-34h]@66
  char v32; // [sp+9B5h] [bp-33h]@68
  char v33; // [sp+9B6h] [bp-32h]@70
  char v34; // [sp+9B7h] [bp-31h]@72
  char v35; // [sp+9B8h] [bp-30h]@76
  __int64 v36; // [sp+9C0h] [bp-28h]@4
  CLogFile *v37; // [sp+9C8h] [bp-20h]@45
  unsigned __int64 v38; // [sp+9D0h] [bp-18h]@4
  CMainThread *v39; // [sp+9F0h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+9F8h] [bp+10h]@1
  _AVATOR_DATA *pNewDataa; // [sp+A00h] [bp+18h]@1
  _AVATOR_DATA *pOldDataa; // [sp+A08h] [bp+20h]@1

  pOldDataa = pOldData;
  pNewDataa = pNewData;
  dwSeriala = dwSerial;
  v39 = this;
  v5 = &v13;
  for ( i = 632i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v36 = -2i64;
  v38 = (unsigned __int64)&v13 ^ _security_cookie;
  if ( CMainThread::_db_Update_Base(v39, dwSerial, pNewData, pOldData, pszBaseQuery, bCheckLowHigh) )
  {
    if ( CMainThread::_db_Update_General(v39, dwSeriala, pNewDataa, pOldDataa, pszGeneralQuery, bCheckLowHigh) )
    {
      if ( CMainThread::_db_Update_Supplement(v39, dwSeriala, pNewDataa, pOldDataa, pszSupplementQuery, 2048) )
      {
        if ( CMainThread::_db_Update_Inven(v39, dwSeriala, pNewDataa, pOldDataa, pszInvenQuery) )
        {
          if ( pNewDataa->dbAvator.m_byRaceSexCode >> 1
            || CMainThread::_db_Update_Unit(v39, dwSeriala, pNewDataa, pOldDataa, pszUnitQuery) )
          {
            if ( CMainThread::_db_Update_UI(v39, dwSeriala, pNewDataa, pOldDataa, pszUIQuery) )
            {
              if ( CMainThread::_db_Update_Quest(v39, dwSeriala, pNewDataa, pOldDataa, pszQuestQuery) )
              {
                if ( CMainThread::_db_Update_NpcQuest_History(v39, dwSeriala, pNewDataa, pOldDataa, pszNPCQuestQuery) )
                {
                  if ( CMainThread::_db_Update_Start_NpcQuest_History(v39, dwSeriala, pNewDataa, pOldDataa) )
                  {
                    if ( CMainThread::_db_Update_Buddy(v39, dwSeriala, pNewDataa, pOldDataa, wszBuddyQuery) )
                    {
                      if ( CMainThread::_db_Update_ItemCombineEx(
                             v39,
                             dwSeriala,
                             pNewDataa,
                             pOldDataa,
                             pszItemCombineExQuery) )
                      {
                        if ( CMainThread::_db_Update_MacroData(v39, dwSeriala, &pNewDataa->dbMacro, &pOldDataa->dbMacro) )
                        {
                          pSzQuery = wszTrunkQuery;
                          if ( CMainThread::_db_Update_Trunk(
                                 v39,
                                 pNewDataa->dbAvator.m_dwAccountSerial,
                                 pNewDataa,
                                 pOldDataa,
                                 wszTrunkQuery) )
                          {
                            pSzQuery = wszExtTrunkQuery;
                            if ( CMainThread::_db_Update_Trunk_Extend(
                                   v39,
                                   pNewDataa->dbAvator.m_dwAccountSerial,
                                   pNewDataa,
                                   pOldDataa,
                                   wszExtTrunkQuery) )
                            {
                              *(_DWORD *)v15 = 2048;
                              pSzQuery = pszTimeLimitInfoQuery;
                              if ( CMainThread::_db_Update_TimeLimitInfo(
                                     v39,
                                     pNewDataa->dbAvator.m_dwAccountSerial,
                                     pNewDataa,
                                     pOldDataa,
                                     pszTimeLimitInfoQuery,
                                     2048) )
                              {
                                CCheckSumCharacAccountTrunkData::CCheckSumCharacAccountTrunkData(
                                  &v22,
                                  dwSeriala,
                                  pNewDataa->dbAvator.m_dwAccountSerial,
                                  pNewDataa->dbAvator.m_byRaceSexCode >> 1);
                                CCheckSumCharacAccountTrunkData::Encode(&v22, pNewDataa);
                                if ( CCheckSumCharacAccountTrunkData::Update(&v22, v39->m_pWorldDB) )
                                {
                                  if ( pOldDataa->dbAvator.m_dwGuildSerial != -1 )
                                  {
                                    if ( pOldDataa->dbAvator.m_dwGuildSerial )
                                    {
                                      if ( pOldDataa->dbAvator.m_dPvPPoint != pNewDataa->dbAvator.m_dPvPPoint )
                                      {
                                        v23 = pNewDataa->dbAvator.m_dPvPPoint - pOldDataa->dbAvator.m_dPvPPoint;
                                        if ( !CRFWorldDatabase::Update_IncreaseWeeklyGuildKillPvpPointSum(
                                                v39->m_pWorldDB,
                                                pOldDataa->dbAvator.m_dwGuildSerial,
                                                v23) )
                                          CLogFile::Write(
                                            &v39->m_logSystemError,
                                            "Update_IncreaseWeeklyGuildKillPvpPointSum(%u,%f) failed!",
                                            pNewDataa->dbAvator.m_dwGuildSerial,
                                            v23);
                                      }
                                    }
                                  }
                                  if ( pNewDataa->m_byHSKTime <= 2 )
                                  {
                                    v8 = pNewDataa->m_byHSKTime;
                                    if ( pNewDataa->m_byCristalBattleDBInfo != 3 )
                                    {
                                      wDiePoint[0] = pNewDataa->m_wDiePoint;
                                      *(_WORD *)v15 = pNewDataa->m_wKillPoint;
                                      LODWORD(pSzQuery) = pNewDataa->m_iPvpPoint;
                                      if ( !CRFWorldDatabase::Update_CristalBattleCharInfo(
                                              v39->m_pWorldDB,
                                              dwSeriala,
                                              pNewDataa->m_byHSKTime,
                                              pNewDataa->m_byPvpGrade,
                                              (int)pSzQuery,
                                              *(unsigned __int16 *)v15,
                                              wDiePoint[0]) )
                                      {
                                        v9 = pNewDataa->m_wDiePoint;
                                        v10 = pNewDataa->m_wKillPoint;
                                        v11 = pNewDataa->m_byPvpGrade;
                                        v12 = pNewDataa->m_byHSKTime;
                                        v37 = &v39->m_logSystemError;
                                        LODWORD(v17) = v9;
                                        *(_DWORD *)wDiePoint = v10;
                                        *(_DWORD *)v15 = pNewDataa->m_iPvpPoint;
                                        LODWORD(pSzQuery) = v11;
                                        CLogFile::Write(
                                          &v39->m_logSystemError,
                                          "Web[Pvp.Pk] Update Fail :: CharSerial[%d], HSKTime[%d], CharGrade[%d], PvpPoin"
                                          "t[%d], KillPoint[%d], DiePoint[%d]",
                                          dwSeriala,
                                          v12);
                                      }
                                      if ( !pNewDataa->m_bCristalBattleDateUpdate
                                        && !CRFWorldDatabase::update_cristalbattle_date(
                                              v39->m_pWorldDB,
                                              dwSeriala,
                                              pNewDataa->m_byHSKTime) )
                                      {
                                        if ( CRFWorldDatabase::Insert_Supplement(v39->m_pWorldDB, dwSeriala) )
                                        {
                                          if ( !CRFWorldDatabase::update_cristalbattle_date(
                                                  v39->m_pWorldDB,
                                                  dwSeriala,
                                                  pNewDataa->m_byHSKTime) )
                                            CLogFile::Write(
                                              &v39->m_logSystemError,
                                              "Web[CristalBattle] Date 'Update&Insert&Update' Fail :: CharSerial[%d]",
                                              dwSeriala);
                                        }
                                        else
                                        {
                                          CLogFile::Write(
                                            &v39->m_logSystemError,
                                            "Web[CristalBattle] Date 'Update&Insert' Fail :: CharSerial[%d]",
                                            dwSeriala);
                                        }
                                      }
                                    }
                                  }
                                  if ( pNewDataa->dbPersonalAmineInven.bUsable
                                    && CRFWorldDatabase::select_amine_personal(v39->m_pWorldDB, dwSeriala) == 2
                                    && !CRFWorldDatabase::insert_amine_personal(v39->m_pWorldDB, dwSeriala) )
                                  {
                                    CLogFile::Write(
                                      &v39->m_logSystemError,
                                      "db_Update_Avator() : insert_amine_personal(sr:%d) failed call",
                                      dwSeriala);
                                  }
                                  if ( !CMainThread::_db_update_inven_AMP(
                                          v39,
                                          dwSeriala,
                                          pNewDataa,
                                          pOldDataa,
                                          pszAMPInven) )
                                    CLogFile::Write(
                                      &v39->m_logSystemError,
                                      "db_Update_Avator() : _db_Update_inven_AMP(sr:%d) Failed call",
                                      dwSeriala);
                                  szError = 0;
                                  memset(&v25, 0, 0x7FFui64);
                                  if ( CMainThread::_db_Update_PvpPointLimit(
                                         v39,
                                         dwSeriala,
                                         pNewDataa,
                                         pOldDataa,
                                         pszPvpPointLimitQuery,
                                         &szError) )
                                  {
                                    if ( CMainThread::_db_Update_CryMsg(
                                           v39,
                                           dwSeriala,
                                           pNewDataa,
                                           pOldDataa,
                                           pszCryMsgQuery) )
                                    {
                                      if ( CMainThread::_db_Update_PvpOrderView(
                                             v39,
                                             dwSeriala,
                                             pNewDataa,
                                             pOldDataa,
                                             pszPvpOrderViewQurey,
                                             &szError) )
                                      {
                                        *(_QWORD *)v15 = &szError;
                                        pSzQuery = pszPrimiumPlayTimeQuery;
                                        if ( CMainThread::_db_Update_PrimiumPlayTime(
                                               v39,
                                               pNewDataa->dbAvator.m_dwAccountSerial,
                                               pNewDataa,
                                               pOldDataa,
                                               pszPrimiumPlayTimeQuery,
                                               &szError) )
                                        {
                                          if ( CMainThread::_db_Update_PotionDelay(
                                                 v39,
                                                 dwSeriala,
                                                 pNewDataa,
                                                 pOldDataa,
                                                 pszPotionDelayQuery,
                                                 2048) )
                                          {
                                            if ( CMainThread::_db_Update_OreCutting(
                                                   v39,
                                                   dwSeriala,
                                                   pNewDataa,
                                                   pOldDataa,
                                                   pszOreCuttingQuery,
                                                   2048) )
                                            {
                                              if ( CMainThread::_db_Update_PcBangFavor(
                                                     v39,
                                                     dwSeriala,
                                                     pNewDataa,
                                                     pOldDataa,
                                                     pszPcBangFavorQuery,
                                                     2048) )
                                              {
                                                pUpdateQuery.szBaseQuery = pszBaseQuery;
                                                pUpdateQuery.szGeneralQuery = pszGeneralQuery;
                                                pUpdateQuery.szInvenQuery = pszInvenQuery;
                                                pUpdateQuery.szUnitQuery = pszUnitQuery;
                                                pUpdateQuery.szUIQuery = pszUIQuery;
                                                pUpdateQuery.szQuestQuery = pszQuestQuery;
                                                pUpdateQuery.wszBuddyQuery = wszBuddyQuery;
                                                pUpdateQuery.szItemCombineExQuery = pszItemCombineExQuery;
                                                pUpdateQuery.wszTrunkQuery = wszTrunkQuery;
                                                pUpdateQuery.szAMPInvenQuery = pszAMPInven;
                                                pUpdateQuery.szPvpPointLimitQuery = pszPvpPointLimitQuery;
                                                pUpdateQuery.wszBossCryMsgQuery = pszCryMsgQuery;
                                                pUpdateQuery.szPvpOrderViewQurey = pszPvpOrderViewQurey;
                                                pUpdateQuery.szNPCQuestQuery = pszNPCQuestQuery;
                                                pUpdateQuery.szPcBangPlayTimeQuery = pszPrimiumPlayTimeQuery;
                                                pUpdateQuery.szSupplementQuery = pszSupplementQuery;
                                                pUpdateQuery.szPotionDelayQuery = pszPotionDelayQuery;
                                                pUpdateQuery.szOreCuttingQuery = pszOreCuttingQuery;
                                                pUpdateQuery.szPcBangFavorQuery = pszPcBangFavorQuery;
                                                pUpdateQuery.wszExtTrunkQuery = wszExtTrunkQuery;
                                                pUpdateQuery.szTimeLimitInfoQuery = pszTimeLimitInfoQuery;
                                                if ( CRFWorldDatabase::Update_CharacterData(
                                                       v39->m_pWorldDB,
                                                       dwSeriala,
                                                       &pUpdateQuery) )
                                                {
                                                  v21 = 0;
                                                }
                                                else
                                                {
                                                  v21 = 24;
                                                }
                                                v35 = v21;
                                                CCheckSumCharacAccountTrunkData::~CCheckSumCharacAccountTrunkData(&v22);
                                                result = v35;
                                              }
                                              else
                                              {
                                                CLogFile::Write(
                                                  &v39->m_logSystemError,
                                                  "_db_Update_PcBangFavor(sr:%d) => _db_Update_PcBangFavor..failed ..",
                                                  dwSeriala);
                                                v34 = 24;
                                                CCheckSumCharacAccountTrunkData::~CCheckSumCharacAccountTrunkData(&v22);
                                                result = v34;
                                              }
                                            }
                                            else
                                            {
                                              CLogFile::Write(
                                                &v39->m_logSystemError,
                                                "_db_Update_OreCutting(sr:%d) => _db_Update_OreCutting..failed ..",
                                                dwSeriala);
                                              v33 = 24;
                                              CCheckSumCharacAccountTrunkData::~CCheckSumCharacAccountTrunkData(&v22);
                                              result = v33;
                                            }
                                          }
                                          else
                                          {
                                            CLogFile::Write(
                                              &v39->m_logSystemError,
                                              "_db_Update_PotionDelay(sr:%d) => _db_Update_PotionDelay..failed ..",
                                              dwSeriala);
                                            v32 = 24;
                                            CCheckSumCharacAccountTrunkData::~CCheckSumCharacAccountTrunkData(&v22);
                                            result = v32;
                                          }
                                        }
                                        else
                                        {
                                          CLogFile::Write(
                                            &v39->m_logSystemError,
                                            "_db_Update_PrimiumPlayTime(sr:%d) => _db_Update_PrimiumPlayTime..failed ..",
                                            pNewDataa->dbAvator.m_dwAccountSerial);
                                          v31 = 24;
                                          CCheckSumCharacAccountTrunkData::~CCheckSumCharacAccountTrunkData(&v22);
                                          result = v31;
                                        }
                                      }
                                      else
                                      {
                                        CLogFile::Write(
                                          &v39->m_logSystemError,
                                          "_db_Update_PvpOrderView(sr:%d) => _db_Update_PvpOrderView..failed ..",
                                          dwSeriala);
                                        v30 = 24;
                                        CCheckSumCharacAccountTrunkData::~CCheckSumCharacAccountTrunkData(&v22);
                                        result = v30;
                                      }
                                    }
                                    else
                                    {
                                      CLogFile::Write(
                                        &v39->m_logSystemError,
                                        "_db_Update_CryMsg(sr:%d) => _db_Update_CryMsg..failed ..",
                                        dwSeriala);
                                      v29 = 24;
                                      CCheckSumCharacAccountTrunkData::~CCheckSumCharacAccountTrunkData(&v22);
                                      result = v29;
                                    }
                                  }
                                  else
                                  {
                                    v20 = &szError;
                                    v19 = pNewDataa->dbPvpPointLimit.dUsePoint;
                                    v18 = pNewDataa->dbPvpPointLimit.dLimitPoint;
                                    v17 = pNewDataa->dbPvpPointLimit.dOriginalPoint;
                                    *(_QWORD *)wDiePoint = pNewDataa->dbPvpPointLimit.tUpdatedate;
                                    *(long double *)v15 = pOldDataa->dbPvpPointLimit.dUsePoint;
                                    pSzQuery = *(char **)&pOldDataa->dbPvpPointLimit.dLimitPoint;
                                    CLogFile::Write(
                                      &v39->m_logSystemError,
                                      "_db_Update_PvpPointLimit(...) : CharSerial(%u)  old( tUpdatedate(%d), dOriginalPoi"
                                      "nt(%f), dLimitPoint(%f), dUsePoint(%f) ) , new( tUpdatedate(%d), dOriginalPoint(%f"
                                      "), dLimitPoint(%f), dUsePoint(%f) ), ErrMsg(%s) Fail!",
                                      pOldDataa->dbPvpPointLimit.tUpdatedate,
                                      pOldDataa->dbPvpPointLimit.dOriginalPoint);
                                    v28 = 24;
                                    CCheckSumCharacAccountTrunkData::~CCheckSumCharacAccountTrunkData(&v22);
                                    result = v28;
                                  }
                                }
                                else
                                {
                                  CLogFile::Write(
                                    &v39->m_logSystemError,
                                    "_db_Update_NpcData(sr:%d) => _db_Update_NpcData..failed ..",
                                    dwSeriala);
                                  v27 = 24;
                                  CCheckSumCharacAccountTrunkData::~CCheckSumCharacAccountTrunkData(&v22);
                                  result = v27;
                                }
                              }
                              else
                              {
                                CLogFile::Write(
                                  &v39->m_logSystemError,
                                  "_db_Update_TimeLimitInfo(sr:%d) => _db_Update_TimeLimitInfo..failed ..",
                                  pNewDataa->dbAvator.m_dwAccountSerial);
                                result = 24;
                              }
                            }
                            else
                            {
                              CLogFile::Write(
                                &v39->m_logSystemError,
                                "_db_Update_Trunk_Extend(sr:%d) => _db_Update_Trunk_Extend..failed ..",
                                dwSeriala);
                              result = 24;
                            }
                          }
                          else
                          {
                            CLogFile::Write(
                              &v39->m_logSystemError,
                              "_db_Update_Trunk(sr:%d) => _db_Update_Trunk..failed ..",
                              dwSeriala);
                            result = 24;
                          }
                        }
                        else
                        {
                          CLogFile::Write(
                            &v39->m_logSystemError,
                            "_db_Update_MacroData(sr:%d) => _db_Update_MacroData..failed ..",
                            dwSeriala);
                          result = 24;
                        }
                      }
                      else
                      {
                        CLogFile::Write(
                          &v39->m_logSystemError,
                          "_db_Update_ItemCombineEx(sr:%d) => _db_Update_ItemCombineEx..failed ..",
                          dwSeriala);
                        result = 24;
                      }
                    }
                    else
                    {
                      CLogFile::Write(
                        &v39->m_logSystemError,
                        "_db_Update_Buddy(sr:%d) => _db_Update_Buddy..failed ..",
                        dwSeriala);
                      result = 24;
                    }
                  }
                  else
                  {
                    CLogFile::Write(
                      &v39->m_logSystemError,
                      "_db_Update_Start_NpcQuest_History(sr:%d) => _db_Update_NpcQuest_History..failed ..",
                      dwSeriala);
                    result = 24;
                  }
                }
                else
                {
                  CLogFile::Write(
                    &v39->m_logSystemError,
                    "_db_Update_NpcQuest_History(sr:%d) => _db_Update_NpcQuest_History..failed ..",
                    dwSeriala);
                  result = 24;
                }
              }
              else
              {
                CLogFile::Write(
                  &v39->m_logSystemError,
                  "_db_Update_Quest(sr:%d) => _db_Update_Quest..failed ..",
                  dwSeriala);
                result = 24;
              }
            }
            else
            {
              CLogFile::Write(&v39->m_logSystemError, "_db_Update_UI(sr:%d) => _db_Update_UI..failed ..", dwSeriala);
              result = 24;
            }
          }
          else
          {
            CLogFile::Write(&v39->m_logSystemError, "db_Update_Avator(sr:%d) => _db_Update_Unit..failed ..", dwSeriala);
            result = 24;
          }
        }
        else
        {
          CLogFile::Write(&v39->m_logSystemError, "db_Update_Avator(sr:%d) => _db_Update_Inven..failed ..", dwSeriala);
          result = 24;
        }
      }
      else
      {
        CLogFile::Write(
          &v39->m_logSystemError,
          "_db_Update_Supplement(sr:%d) => _db_Update_Supplement..failed ..",
          dwSeriala);
        result = 24;
      }
    }
    else
    {
      CLogFile::Write(&v39->m_logSystemError, "db_Update_Avator(sr:%d) => _db_Update_General..failed ..", dwSeriala);
      result = 24;
    }
  }
  else
  {
    CLogFile::Write(&v39->m_logSystemError, "db_Update_Avator(sr:%d) => _db_Update_Base..failed ..", dwSeriala);
    result = 24;
  }
  return result;
}
