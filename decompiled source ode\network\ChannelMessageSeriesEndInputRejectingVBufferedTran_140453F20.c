/*
 * Function: ?ChannelMessageSeriesEnd@?$InputRejecting@VBufferedTransformation@CryptoPP@@@CryptoPP@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H_N@Z
 * Address: 0x140453F20
 */

void __fastcall __noreturn CryptoPP::InputRejecting<CryptoPP::BufferedTransformation>::ChannelMessageSeriesEnd(CryptoPP::InputRejecting<CryptoPP::BufferedTransformation> *this, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *__formal, int a3, bool a4)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-78h]@1
  CryptoPP::InputRejecting<CryptoPP::BufferedTransformation>::InputRejected v7; // [sp+20h] [bp-58h]@4

  v4 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  CryptoPP::InputRejecting<CryptoPP::BufferedTransformation>::InputRejected::InputRejected(&v7);
  CxxThrowException_0(&v7, &TI4_AUInputRejected___InputRejecting_VBufferedTransformation_CryptoPP___CryptoPP__);
}
