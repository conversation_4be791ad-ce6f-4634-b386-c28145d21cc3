/*
 * Function: ?ct_recall_monster@@YA_NPEAVCPlayer@@@Z
 * Address: 0x1402907F0
 */

bool __fastcall ct_recall_monster(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-88h]@1
  int nCreateNum; // [sp+20h] [bp-68h]@7
  char szTran; // [sp+38h] [bp-50h]@9
  unsigned __int64 v7; // [sp+70h] [bp-18h]@4
  CPlayer *v8; // [sp+90h] [bp+8h]@1

  v8 = pOne;
  v1 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( v8 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      nCreateNum = 1;
      if ( s_nWordCount >= 2 )
        nCreateNum = atoi(s_pwszDstCheat[1]);
      W2M(s_pwszDstCheat[0], &szTran, 0x20u);
      result = CPlayer::mgr_recall_mon(v8, &szTran, nCreateNum);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
