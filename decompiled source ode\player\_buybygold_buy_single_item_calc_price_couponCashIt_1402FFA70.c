/*
 * Function: ?_buybygold_buy_single_item_calc_price_coupon@CashItemRemoteStore@@AEAAKPEAVCPlayer@@PEAU_request_csi_buy_clzo@@EHPEA_NAEAK@Z
 * Address: 0x1402FFA70
 */

__int64 __fastcall CashItemRemoteStore::_buybygold_buy_single_item_calc_price_coupon(CashItemRemoteStore *this, CPlayer *pOne, _request_csi_buy_clzo *pRecv, char byOverlapNum, int nCsPrice, bool *bCouponUseCheck, unsigned int *dwDiscount)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v10; // [sp+0h] [bp-68h]@1
  int v11; // [sp+20h] [bp-48h]@4
  int v12; // [sp+24h] [bp-44h]@4
  int v13; // [sp+28h] [bp-40h]@4
  int j; // [sp+2Ch] [bp-3Ch]@4
  _STORAGE_LIST *v15; // [sp+30h] [bp-38h]@7
  _STORAGE_LIST::_db_con *v16; // [sp+38h] [bp-30h]@7
  _base_fld *v17; // [sp+40h] [bp-28h]@8
  int v18; // [sp+48h] [bp-20h]@9
  int v19; // [sp+4Ch] [bp-1Ch]@14
  int v20; // [sp+50h] [bp-18h]@14
  CPlayer *v21; // [sp+78h] [bp+10h]@1
  _request_csi_buy_clzo *v22; // [sp+80h] [bp+18h]@1
  char v23; // [sp+88h] [bp+20h]@1

  v23 = byOverlapNum;
  v22 = pRecv;
  v21 = pOne;
  v7 = &v10;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v11 = 0;
  v12 = (unsigned __int8)byOverlapNum * nCsPrice;
  v13 = 0;
  for ( j = 0; j < v22->byCouponNum; ++j )
  {
    v15 = v21->m_Param.m_pStoragePtr[v22->CouponItem[j].byStorageCode];
    v16 = _STORAGE_LIST::GetPtrFromSerial(v15, v22->CouponItem[j].wItemSerial);
    if ( v16 )
    {
      v17 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v16->m_byTableCode, v16->m_wItemIndex);
      if ( v17 )
      {
        v18 = 10 * *(_DWORD *)&v17[4].m_strCode[8];
        if ( *(_DWORD *)&v17[4].m_strCode[4] )
        {
          v19 = nCsPrice;
          v20 = 10 * (signed int)floor((double)(*(_DWORD *)&v17[4].m_strCode[8] * nCsPrice / 0x64u) * 0.1 + 0.5);
          v13 += v20;
          *dwDiscount += *(_DWORD *)&v17[4].m_strCode[8];
          bCouponUseCheck[j] = 1;
        }
        else if ( v18 < (unsigned int)v12 && !bCouponUseCheck[j] )
        {
          v12 -= *(_DWORD *)&v17[4].m_strCode[8];
          *dwDiscount += *(_DWORD *)&v17[4].m_strCode[8];
          bCouponUseCheck[j] = 1;
        }
      }
    }
  }
  if ( v13 )
    v11 = (unsigned __int8)v23 * (nCsPrice - v13);
  else
    v11 = v12;
  return (unsigned int)v11;
}
