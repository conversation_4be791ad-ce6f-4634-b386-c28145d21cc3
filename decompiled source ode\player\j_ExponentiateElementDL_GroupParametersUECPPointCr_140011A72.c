/*
 * Function: j_?ExponentiateElement@?$DL_GroupParameters@UECPPoint@CryptoPP@@@CryptoPP@@UEBA?AUECPPoint@2@AEBU32@AEBVInteger@2@@Z
 * Address: 0x140011A72
 */

CryptoPP::ECPPoint *__fastcall CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::ExponentiateElement(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *this, CryptoPP::ECPPoint *result, CryptoPP::ECPPoint *base, CryptoPP::Integer *exponent)
{
  return CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::ExponentiateElement(this, result, base, exponent);
}
