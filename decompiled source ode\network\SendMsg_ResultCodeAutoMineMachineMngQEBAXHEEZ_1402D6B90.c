/*
 * Function: ?SendMsg_ResultCode@AutoMineMachineMng@@QEBAXHEE@Z
 * Address: 0x1402D6B90
 */

void __fastcall AutoMineMachineMng::SendMsg_ResultCode(AutoMineMachineMng *this, int n, char byType, char byRetCode)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v6; // ax@4
  __int64 v7; // [sp+0h] [bp-78h]@1
  _pt_automine_result_zocl v8; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v10; // [sp+55h] [bp-23h]@4
  int dwClientIndex; // [sp+88h] [bp+10h]@1

  dwClientIndex = n;
  v4 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8.byType = byType;
  v8.byRetCode = byRetCode;
  pbyType = 14;
  v10 = 27;
  v6 = _pt_automine_result_zocl::size(&v8);
  CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &v8.byType, v6);
}
