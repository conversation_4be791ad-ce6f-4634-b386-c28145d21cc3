/*
 * Function: ?Select_PostRecvStorageCheck@CRFWorldDatabase@@QEAAHK@Z
 * Address: 0x1404B23E0
 */

signed __int64 __fastcall CRFWorldDatabase::Select_PostRecvStorageCheck(CRFWorldDatabase *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v5; // [sp+0h] [bp-128h]@1
  void *SQLStmt; // [sp+20h] [bp-108h]@4
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-100h]@21
  SQLLEN v8; // [sp+38h] [bp-F0h]@21
  __int16 v9; // [sp+44h] [bp-E4h]@9
  unsigned __int16 TargetValue; // [sp+54h] [bp-D4h]@4
  char DstBuf; // [sp+80h] [bp-A8h]@4
  char v12; // [sp+81h] [bp-A7h]@4
  unsigned __int64 v13; // [sp+110h] [bp-18h]@4
  CRFWorldDatabase *v14; // [sp+130h] [bp+8h]@1

  v14 = this;
  v2 = &v5;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  TargetValue = 0;
  DstBuf = 0;
  memset(&v12, 0, 0x7Fui64);
  LODWORD(SQLStmt) = 100;
  sprintf_s(
    &DstBuf,
    0x80ui64,
    "select count(serial) from tbl_PostStorage where owner=%d and dck=0 and poststate<%d",
    dwSerial);
  if ( v14->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v14->vfptr, &DstBuf);
  if ( v14->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v14->vfptr) )
  {
    v9 = SQLExecDirect_0(v14->m_hStmtSelect, &DstBuf, -3);
    if ( v9 && v9 != 1 )
    {
      if ( v9 == 100 )
      {
        result = 4294967294i64;
      }
      else
      {
        SQLStmt = v14->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v9, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v9, v14->m_hStmtSelect);
        result = 0xFFFFFFFFi64;
      }
    }
    else
    {
      v9 = SQLFetch_0(v14->m_hStmtSelect);
      if ( v9 && v9 != 1 )
      {
        if ( v14->m_hStmtSelect )
          SQLCloseCursor_0(v14->m_hStmtSelect);
        if ( v9 == 100 )
        {
          result = 0xFFFFFFFFi64;
        }
        else
        {
          SQLStmt = v14->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v9, &DstBuf, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v9, v14->m_hStmtSelect);
          result = 4294967294i64;
        }
      }
      else
      {
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v14->m_hStmtSelect, 1u, 5, &TargetValue, 0i64, &v8);
        if ( v9 && v9 != 1 )
        {
          SQLStmt = v14->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v9, &DstBuf, "SQLGetData", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v9, v14->m_hStmtSelect);
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          result = 4294967294i64;
        }
        else
        {
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          if ( v14->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v14->vfptr, "%s Success", &DstBuf);
          result = TargetValue;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v14->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 0xFFFFFFFFi64;
  }
  return result;
}
