/*
 * Function: ??0PK_MessageAccumulatorBase@CryptoPP@@QEAA@XZ
 * Address: 0x140562F60
 */

CryptoPP::PK_MessageAccumulatorBase *__fastcall CryptoPP::PK_MessageAccumulatorBase::PK_MessageAccumulatorBase(CryptoPP::PK_MessageAccumulatorBase *this)
{
  CryptoPP::PK_MessageAccumulatorBase *v2; // [sp+40h] [bp+8h]@1

  v2 = this;
  CryptoPP::PK_MessageAccumulator::PK_MessageAccumulator((CryptoPP::PK_MessageAccumulator *)&this->vfptr);
  CryptoPP::Sec<PERSON>lock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::Sec<PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v2->m_recoverableMessage,
    0i64);
  CryptoPP::Sec<PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::Se<PERSON><PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v2->m_representative,
    0i64);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v2->m_presignature,
    0i64);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v2->m_semisignature,
    0i64);
  CryptoPP::Integer::Integer(&v2->m_k);
  CryptoPP::Integer::Integer(&v2->m_s);
  v2->m_empty = 1;
  return v2;
}
