/*
 * Function: ?Init@_POSTDATA_DB_BASE@@QEAAXXZ
 * Address: 0x140077680
 */

void __fastcall _POSTDATA_DB_BASE::Init(_POSTDATA_DB_BASE *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _POSTDATA_DB_BASE *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _POSTSTORAGE_DB_BASE::Init(&v4->dbPost);
  _RETURNPOST_DB_BASE::Init(&v4->dbRetPost);
  _DELPOST_DB_BASE::Init(&v4->dbDelPost);
}
