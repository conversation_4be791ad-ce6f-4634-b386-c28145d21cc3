/*
 * Function: ??$_Uninit_fill_n@PEAPEAURECV_DATA@@_KPEAU1@V?$allocator@PEAURECV_DATA@@@std@@@std@@YAXPEAPEAURECV_DATA@@_KAEBQEAU1@AEAV?$allocator@PEAURECV_DATA@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14031B360
 */

void __fastcall std::_Uninit_fill_n<RECV_DATA * *,unsigned __int64,RECV_DATA *,std::allocator<RECV_DATA *>>(RECV_DATA **_First, unsigned __int64 _Count, RECV_DATA *const *_Val, std::allocator<RECV_DATA *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-28h]@1
  RECV_DATA **_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  stdext::unchecked_fill_n<RECV_DATA * *,unsigned __int64,RECV_DATA *>(_Firsta, _Count, _Val);
}
