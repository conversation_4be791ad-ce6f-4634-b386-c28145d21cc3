/*
 * Function: ?SendComfirmWeb@CRaceBossMsgController@@IEAAXEPEAVCMsg@RACE_BOSS_MSG@@@Z
 * Address: 0x1402A10F0
 */

void __fastcall CRaceBossMsgController::SendComfirmWeb(CRaceBossMsgController *this, char ucRace, RACE_BOSS_MSG::CMsg *pkMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  const char *v5; // rax@7
  const char *v6; // rax@7
  __int64 v7; // [sp+0h] [bp-1A8h]@1
  unsigned int Dst; // [sp+40h] [bp-168h]@7
  int v9; // [sp+44h] [bp-164h]@7
  char v10; // [sp+48h] [bp-160h]@7
  char Dest; // [sp+49h] [bp-15Fh]@7
  char v12; // [sp+59h] [bp-14Fh]@7
  char v13; // [sp+5Ah] [bp-14Eh]@7
  char v14; // [sp+8Ah] [bp-11Eh]@7
  char pbyType; // [sp+174h] [bp-34h]@7
  char v16; // [sp+175h] [bp-33h]@7
  unsigned __int64 v17; // [sp+190h] [bp-18h]@4
  char v18; // [sp+1B8h] [bp+10h]@1
  RACE_BOSS_MSG::CMsg *v19; // [sp+1C0h] [bp+18h]@1

  v19 = pkMsg;
  v18 = ucRace;
  v3 = &v7;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v17 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( unk_1799C608D && unk_1799C608E )
  {
    memset_0(&Dst, 0, 0x119ui64);
    Dst = RACE_BOSS_MSG::CMsg::GetID(v19);
    v9 = unk_1799C608C;
    v10 = v18;
    v5 = RACE_BOSS_MSG::CMsg::GetBossName(v19);
    strncpy(&Dest, v5, 0x10ui64);
    v12 = 0;
    v6 = RACE_BOSS_MSG::CMsg::GetMsg(v19);
    strncpy(&v13, v6, 0x30ui64);
    v14 = 0;
    pbyType = 51;
    v16 = 9;
    if ( unk_1799C9ADE )
      CNetProcess::LoadSendMsg(unk_1414F2098, unk_1799C9ADD, &pbyType, (char *)&Dst, 0x119u);
  }
}
