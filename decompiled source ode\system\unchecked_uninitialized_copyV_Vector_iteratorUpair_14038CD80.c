/*
 * Function: ??$unchecked_uninitialized_copy@V?$_Vector_iterator@U?$pair@K<PERSON>@std@@V?$allocator@U?$pair@KK@std@@@2@@std@@PEAU?$pair@KK@2@V?$allocator@U?$pair@KK@std@@@2@@stdext@@YAPEAU?$pair@KK@std@@V?$_Vector_iterator@U?$pair@KK@std@@V?$allocator@U?$pair@KK@std@@@2@@2@0PEAU12@AEAV?$allocator@U?$pair@KK@std@@@2@@Z
 * Address: 0x14038CD80
 */

std::pair<unsigned long,unsigned long> *__fastcall stdext::unchecked_uninitialized_copy<std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>,std::pair<unsigned long,unsigned long> *,std::allocator<std::pair<unsigned long,unsigned long>>>(std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *_First, std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *_Last, std::pair<unsigned long,unsigned long> *_Dest, std::allocator<std::pair<unsigned long,unsigned long> > *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *v6; // rax@4
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *v7; // rax@4
  __int64 v9; // [sp+0h] [bp-A8h]@1
  std::pair<unsigned long,unsigned long> *v10; // [sp+30h] [bp-78h]@4
  std::_Range_checked_iterator_tag v11; // [sp+38h] [bp-70h]@4
  std::_Nonscalar_ptr_iterator_tag v12; // [sp+39h] [bp-6Fh]@4
  char v13; // [sp+40h] [bp-68h]@4
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *v14; // [sp+58h] [bp-50h]@4
  char v15; // [sp+60h] [bp-48h]@4
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *v16; // [sp+78h] [bp-30h]@4
  __int64 v17; // [sp+80h] [bp-28h]@4
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *v18; // [sp+88h] [bp-20h]@4
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *v19; // [sp+90h] [bp-18h]@4
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *v20; // [sp+98h] [bp-10h]@4
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *__formal; // [sp+B0h] [bp+8h]@1
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *__that; // [sp+B8h] [bp+10h]@1
  std::pair<unsigned long,unsigned long> *v23; // [sp+C0h] [bp+18h]@1
  std::allocator<std::pair<unsigned long,unsigned long> > *v24; // [sp+C8h] [bp+20h]@1

  v24 = _Al;
  v23 = _Dest;
  __that = _Last;
  __formal = _First;
  v4 = &v9;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v17 = -2i64;
  memset(&v11, 0, sizeof(v11));
  v12 = std::_Ptr_cat<std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>,std::pair<unsigned long,unsigned long> *>(
          __formal,
          &v23);
  v14 = (std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *)&v13;
  v16 = (std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *)&v15;
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>(
    (std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *)&v13,
    __that);
  v18 = v6;
  v19 = v6;
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>(
    v16,
    __formal);
  v20 = v7;
  v10 = std::_Uninit_copy<std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>,std::pair<unsigned long,unsigned long> *,std::allocator<std::pair<unsigned long,unsigned long>>>(
          v7,
          v19,
          v23,
          v24,
          v12,
          v11);
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::~_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>(__formal);
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::~_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>(__that);
  return v10;
}
