/*
 * Function: j_??R?$hash_compare@PEAUScheduleMSG@@U?$less@PEAUScheduleMSG@@@std@@@stdext@@QEBA_KAEBQEAUScheduleMSG@@@Z
 * Address: 0x14000FCAE
 */

unsigned __int64 __fastcall stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>::operator()(stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> > *this, ScheduleMSG *const *_Keyval)
{
  return stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>::operator()(this, _Keyval);
}
