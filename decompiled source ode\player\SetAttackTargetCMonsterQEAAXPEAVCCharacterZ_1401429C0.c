/*
 * Function: ?SetAttackTarget@CMonster@@QEAAXPEAVCCharacter@@@Z
 * Address: 0x1401429C0
 */

void __fastcall CMonster::SetAttackTarget(CMonster *this, CCharacter *p)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMonster *v5; // [sp+30h] [bp+8h]@1
  CCharacter *pTarget; // [sp+38h] [bp+10h]@1

  pTarget = p;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v5->m_pTargetChar && p && p != v5->m_pTargetChar )
    CMonster::CheckEventEmotionPresentation(v5, 7, p);
  v5->m_pTargetChar = pTarget;
  CMonster::SendMsg_Change_MonsterTarget(v5, v5->m_pTargetChar);
}
