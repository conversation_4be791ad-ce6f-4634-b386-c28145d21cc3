/*
 * Function: ?Init@AggroCaculateData@@IEAAXXZ
 * Address: 0x14015BEF0
 */

void __fastcall AggroCaculateData::Init(AggroCaculateData *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  AggroCaculateData *Dst; // [sp+30h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  Dst->m_nTotalCount = 0;
  Dst->m_bLoad = 0;
  memset_0(Dst, 0, 0x48ui64);
  memset_0(Dst->m_SpecialData, 0, 0x190ui64);
}
