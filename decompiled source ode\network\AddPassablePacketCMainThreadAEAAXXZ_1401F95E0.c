/*
 * Function: ?AddPassablePacket@CMainThread@@AEAAXXZ
 * Address: 0x1401F95E0
 */

void __fastcall CMainThread::AddPassablePacket(CMainThread *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1

  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CNetworkEX::SetPassablePacket(&g_Network, 0, 4, 3);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 4, 4);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 4, 5);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 4, 6);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 4, 7);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 4, -79);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 4, 18);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 4, 20);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 4, 25);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 4, 30);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 5, 7);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 5, 8);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 5, 9);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 5, 10);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 5, 11);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 5, 12);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 5, 13);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 5, 14);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 5, 15);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 5, -105);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 5, -104);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 2, 10);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 2, 11);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 2, 11);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 13, 29);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 14, 7);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 14, 8);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 14, 25);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 14, 60);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 14, 66);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 14, 55);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 14, 68);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 56, 6);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 16, 20);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 16, 21);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 16, 22);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 16, 24);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 16, 25);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 16, 25);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 17, 3);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 17, 6);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 17, 9);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 17, 9);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 17, 17);
  CNetworkEX::SetPassablePacket(&g_Network, 0, 17, 26);
}
