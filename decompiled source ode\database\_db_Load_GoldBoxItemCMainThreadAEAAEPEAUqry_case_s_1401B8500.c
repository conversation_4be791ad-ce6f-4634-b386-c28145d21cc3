/*
 * Function: ?_db_Load_GoldBoxItem@CMainThread@@AEAAEPEAUqry_case_select_golden_box_item@@PEAH@Z
 * Address: 0x1401B8500
 */

char __fastcall CMainThread::_db_Load_GoldBoxItem(CMainThread *this, qry_case_select_golden_box_item *pDbGoldenboxitem, int *pnDBSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CGoldenBoxItemMgr *v6; // rax@12
  __int64 v7; // rax@12
  __int64 v8; // [sp+0h] [bp-6D8h]@1
  _worlddb_golden_box_item goldenboxitem; // [sp+30h] [bp-6A8h]@4
  int pnSerial; // [sp+6A4h] [bp-34h]@4
  char v11; // [sp+6B4h] [bp-24h]@4
  int j; // [sp+6B8h] [bp-20h]@11
  int k; // [sp+6BCh] [bp-1Ch]@13
  unsigned __int64 v14; // [sp+6C8h] [bp-10h]@4
  CMainThread *v15; // [sp+6E0h] [bp+8h]@1
  qry_case_select_golden_box_item *v16; // [sp+6E8h] [bp+10h]@1
  int *v17; // [sp+6F0h] [bp+18h]@1

  v17 = pnDBSerial;
  v16 = pDbGoldenboxitem;
  v15 = this;
  v3 = &v8;
  for ( i = 436i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = (unsigned __int64)&v8 ^ _security_cookie;
  pnSerial = 0;
  v11 = CRFWorldDatabase::Select_GodenBoxItem(v15->m_pWorldDB, &goldenboxitem, &pnSerial);
  if ( v11 == 1 )
    return 24;
  if ( v11 != 2 )
    goto LABEL_20;
  if ( !CRFWorldDatabase::Insert_GoldenBoxItem(v15->m_pWorldDB) )
    return 24;
  if ( CRFWorldDatabase::Select_GodenBoxItem(v15->m_pWorldDB, &goldenboxitem, &pnSerial) )
  {
    result = 24;
  }
  else
  {
LABEL_20:
    *v17 = pnSerial;
    v16->bydck = goldenboxitem.bydck;
    v16->dwStarterBoxCnt = goldenboxitem.dwStarterBoxCnt;
    for ( j = 0; ; ++j )
    {
      v6 = CGoldenBoxItemMgr::Instance();
      v7 = (unsigned __int8)CGoldenBoxItemMgr::GetLoopCount(v6);
      if ( j >= (unsigned __int8)v7 )
        break;
      v16->nBoxcode[j] = goldenboxitem.nBox_item_code[j];
      v16->wBoxMax[j] = goldenboxitem.wBox_item_max[j];
      v16->bygolden_item_num[j] = goldenboxitem.bygolden_item_num[j];
      for ( k = 0; k < goldenboxitem.bygolden_item_num[j]; ++k )
      {
        v16->List[j][k].ncode = goldenboxitem.List[j][k].nGoldencode;
        v16->List[j][k].wcount = goldenboxitem.List[j][k].wGoldencount;
      }
    }
    result = 0;
  }
  return result;
}
