/*
 * Function: ?NotifyOwnerAttackInform@CGuardTower@@QEAAXPEAVCCharacter@@@Z
 * Address: 0x1401312E0
 */

void __fastcall CGuardTower::NotifyOwnerAttackInform(CGuardTower *this, CCharacter *pDst)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float v4; // xmm0_4@7
  float v5; // xmm0_4@8
  float v6; // xmm1_4@8
  __int64 v7; // [sp+0h] [bp-38h]@1
  int v8; // [sp+20h] [bp-18h]@8
  float v9; // [sp+24h] [bp-14h]@8
  float v10; // [sp+28h] [bp-10h]@8
  CGuardTower *v11; // [sp+40h] [bp+8h]@1
  CCharacter *v12; // [sp+48h] [bp+10h]@1

  v12 = pDst;
  v11 = this;
  v2 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pDst )
  {
    if ( v11->m_pMasterSetTarget != pDst )
    {
      v4 = pDst->m_fCurPos[1] - v11->m_fCurPos[1];
      abs(v4);
      if ( v4 <= 400.0 )
      {
        v8 = *(_DWORD *)&v11->m_pRecordSet[5].m_strCode[24];
        v5 = (float)v8;
        v9 = (float)v8;
        ((void (__fastcall *)(CCharacter *))v12->vfptr->GetWidth)(v12);
        v6 = v9 + (float)(v5 / 2.0);
        v10 = v9 + (float)(v5 / 2.0);
        GetSqrt(v11->m_fCurPos, v12->m_fCurPos);
        if ( v6 <= v10 )
        {
          v11->m_pMasterSetTarget = v12;
          v11->m_pTarget = 0i64;
        }
      }
    }
  }
}
