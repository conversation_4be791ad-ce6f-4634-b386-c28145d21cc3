/*
 * Function: j_?Update_PostRegistry@CRFWorldDatabase@@QEAA_NKKPEAD000H_KKKEE1@Z
 * Address: 0x140002D2E
 */

bool __fastcall CRFWorldDatabase::Update_PostRegistry(CRFWorldDatabase *this, unsigned int dwIndex, unsigned int dwSenderSerial, char *wszSendName, char *wszRecvName, char *wszTitle, char *wszContent, int nK, unsigned __int64 dwD, unsigned int dwU, unsigned int dwGold, char bySendRace, char bySenderDgr, unsigned __int64 lnUID)
{
  return CRFWorldDatabase::Update_PostRegistry(
           this,
           dwIndex,
           dwSenderSerial,
           wszSendName,
           wszRecvName,
           wszTitle,
           wszContent,
           nK,
           dwD,
           dwU,
           dwGold,
           bySendRace,
           bySenderDgr,
           lnUID);
}
