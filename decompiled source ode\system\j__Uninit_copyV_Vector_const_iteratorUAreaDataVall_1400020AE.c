/*
 * Function: j_??$_Uninit_copy@V?$_Vector_const_iterator@UAreaData@@V?$allocator@UAreaData@@@std@@@std@@PEAUAreaData@@V?$allocator@UAreaData@@@2@@std@@YAPEAUAreaData@@V?$_Vector_const_iterator@UAreaData@@V?$allocator@UAreaData@@@std@@@0@0PEAU1@AEAV?$allocator@UAreaData@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400020AE
 */

AreaData *__fastcall std::_Uninit_copy<std::_Vector_const_iterator<AreaData,std::allocator<AreaData>>,AreaData *,std::allocator<AreaData>>(std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *_First, std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *_Last, AreaData *_Dest, std::allocator<AreaData> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<std::_Vector_const_iterator<AreaData,std::allocator<AreaData>>,AreaData *,std::allocator<AreaData>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
