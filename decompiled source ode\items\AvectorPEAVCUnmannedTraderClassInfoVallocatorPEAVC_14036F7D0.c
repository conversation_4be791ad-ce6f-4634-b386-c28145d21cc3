/*
 * Function: ??A?$vector@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@QEAAAEAPEAVCUnmannedTraderClassInfo@@_K@Z
 * Address: 0x14036F7D0
 */

CUnmannedTraderClassInfo **__fastcall std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator[](std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this, unsigned __int64 _Pos)
{
  return &this->_Myfirst[_Pos];
}
