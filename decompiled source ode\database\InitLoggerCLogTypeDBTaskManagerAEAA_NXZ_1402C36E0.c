/*
 * Function: ?InitLogger@CLogTypeDBTaskManager@@AEAA_NXZ
 * Address: 0x1402C36E0
 */

char __fastcall CLogTypeDBTaskManager::InitLogger(CLogTypeDBTaskManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@5
  char result; // al@8
  unsigned int v5; // eax@9
  __int64 v6; // [sp+0h] [bp-108h]@1
  bool bDate; // [sp+20h] [bp-E8h]@9
  bool bAddCount; // [sp+28h] [bp-E0h]@9
  char _Dest[128]; // [sp+40h] [bp-C8h]@9
  CLogFile *v10; // [sp+D0h] [bp-38h]@7
  CLogFile *v11; // [sp+D8h] [bp-30h]@4
  __int64 v12; // [sp+E0h] [bp-28h]@4
  CLogFile *v13; // [sp+E8h] [bp-20h]@5
  unsigned __int64 v14; // [sp+F0h] [bp-18h]@4
  CLogTypeDBTaskManager *v15; // [sp+110h] [bp+8h]@1

  v15 = this;
  v1 = &v6;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = -2i64;
  v14 = (unsigned __int64)&v6 ^ _security_cookie;
  v11 = (CLogFile *)operator new(0xB8ui64);
  if ( v11 )
  {
    CLogFile::CLogFile(v11);
    v13 = (CLogFile *)v3;
  }
  else
  {
    v13 = 0i64;
  }
  v10 = v13;
  v15->m_pkLogger = v13;
  if ( v15->m_pkLogger )
  {
    v5 = GetKorLocalTime();
    sprintf_s<128>((char (*)[128])_Dest, "..\\ZoneServerLog\\Systemlog\\LogTypeDBTaskError%d.log", v5);
    bAddCount = 1;
    bDate = 1;
    CLogFile::SetWriteLogFile(v15->m_pkLogger, _Dest, 1, 0, 1, 1);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
