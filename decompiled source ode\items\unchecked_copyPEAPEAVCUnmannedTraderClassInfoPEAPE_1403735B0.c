/*
 * Function: ??$unchecked_copy@PEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@@stdext@@YAPEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@00@Z
 * Address: 0x1403735B0
 */

CUnmannedTraderClassInfo **__fastcall stdext::unchecked_copy<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *>(CUnmannedTraderClassInfo **_First, CUnmannedTraderClassInfo **_Last, CUnmannedTraderClassInfo **_Dest)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  std::random_access_iterator_tag *v5; // rax@4
  __int64 v7; // [sp+0h] [bp-48h]@1
  std::_Scalar_ptr_iterator_tag v8; // [sp+20h] [bp-28h]@4
  std::_Range_checked_iterator_tag v9; // [sp+28h] [bp-20h]@4
  std::_Range_checked_iterator_tag v10; // [sp+30h] [bp-18h]@4
  std::_Scalar_ptr_iterator_tag v11; // [sp+31h] [bp-17h]@4
  CUnmannedTraderClassInfo **v12; // [sp+32h] [bp-16h]@4
  CUnmannedTraderClassInfo **__formal; // [sp+50h] [bp+8h]@1
  CUnmannedTraderClassInfo **_Lasta; // [sp+58h] [bp+10h]@1
  CUnmannedTraderClassInfo **_Desta; // [sp+60h] [bp+18h]@1

  _Desta = _Dest;
  _Lasta = _Last;
  __formal = _First;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  memset(&v10, 0, sizeof(v10));
  v11 = std::_Ptr_cat<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *>(&__formal, &_Desta);
  LOBYTE(v5) = std::_Iter_random<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *>(&v12, &__formal);
  v9 = v10;
  v8 = v11;
  return std::_Copy_opt<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *,std::random_access_iterator_tag>(
           __formal,
           _Lasta,
           _Desta,
           (std::random_access_iterator_tag)v5->0,
           v11,
           v10);
}
