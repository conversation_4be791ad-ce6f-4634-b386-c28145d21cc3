/*
 * Function: ?PostSendRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CE6B0
 */

char __fastcall CNetworkEX::PostSendRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CPostSystemManager *v6; // rax@6
  __int64 v7; // [sp+0h] [bp-78h]@1
  unsigned int dwGold; // [sp+30h] [bp-48h]@6
  char byRace; // [sp+38h] [bp-40h]@6
  char *wszRecvName; // [sp+40h] [bp-38h]@4
  CPlayer *pOne; // [sp+48h] [bp-30h]@4
  int v12; // [sp+50h] [bp-28h]@6
  _STORAGE_POS_INDIV *v13; // [sp+58h] [bp-20h]@6
  char *v14; // [sp+60h] [bp-18h]@6
  char *wszTitle; // [sp+68h] [bp-10h]@6

  v3 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  wszRecvName = pBuf;
  pOne = &g_Player + n;
  if ( pOne->m_bOper )
  {
    v12 = CPlayerDB::GetRaceCode(&pOne->m_Param);
    v13 = (_STORAGE_POS_INDIV *)(wszRecvName + 239);
    v14 = wszRecvName + 38;
    wszTitle = wszRecvName + 17;
    v6 = CPostSystemManager::Instace();
    byRace = v12;
    dwGold = *(_DWORD *)(wszRecvName + 243);
    CPostSystemManager::PostSendRequest(v6, pOne, wszRecvName, wszTitle, v14, v13, dwGold, v12);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
