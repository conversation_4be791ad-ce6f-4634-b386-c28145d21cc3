/*
 * Function: ??0?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@QEAA@XZ
 * Address: 0x140367140
 */

void __fastcall std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<CUnmannedTraderUserInfo> v3; // al@4
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  std::allocator<CUnmannedTraderUserInfo> *v6; // [sp+28h] [bp-10h]@4
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (std::allocator<CUnmannedTraderUserInfo> *)&v5;
  std::allocator<CUnmannedTraderUserInfo>::allocator<CUnmannedTraderUserInfo>((std::allocator<CUnmannedTraderUserInfo> *)&v5);
  std::_Vector_val<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Vector_val<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(
    (std::_Vector_val<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)&v7->_Myfirstiter,
    v3);
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Buy(v7, 0i64);
}
