/*
 * Function: _CryptoPP::IteratedHashWithStaticTransform_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_32_CryptoPP::SHA256_0_::IteratedHashWithStaticTransform_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_32_CryptoPP::SHA256_0__::_1_::dtor$1
 * Address: 0x14044EE50
 */

void __fastcall CryptoPP::IteratedHashWithStaticTransform_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_32_CryptoPP::SHA256_0_::IteratedHashWithStaticTransform_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_32_CryptoPP::SHA256_0__::_1_::dtor_1(__int64 a1, __int64 a2)
{
  CryptoPP::FixedSizeSecBlock<unsigned int,16,CryptoPP::FixedSizeAllocatorWithCleanup<unsigned int,16,CryptoPP::NullAllocator<unsigned int>,0>>::~FixedSizeSecBlock<unsigned int,16,CryptoPP::FixedSizeAllocatorWithCleanup<unsigned int,16,CryptoPP::NullAllocator<unsigned int>,0>>((CryptoPP::FixedSizeSecBlock<unsigned int,16,CryptoPP::FixedSizeAllocatorWithCleanup<unsigned int,16,CryptoPP::NullAllocator<unsigned int>,0> > *)(*(_QWORD *)(a2 + 64) + 104i64));
}
