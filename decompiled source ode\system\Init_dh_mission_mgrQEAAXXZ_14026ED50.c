/*
 * Function: ?Init@_dh_mission_mgr@@QEAAXXZ
 * Address: 0x14026ED50
 */

void __fastcall _dh_mission_mgr::Init(_dh_mission_mgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _dh_mission_mgr *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->pCurMssionPtr = 0i64;
  for ( j = 0; j < 8; ++j )
    _dh_mission_mgr::_count::Init(&v5->Count[j]);
  memset_0(v5->bOpenPortal, 0, 0x80ui64);
  v5->dwMissionStartTime = timeGetTime();
  for ( j = 0; j < 100; ++j )
    _dh_mission_mgr::_if_change::Init(&v5->IfCont[j]);
  memset_0(v5->bInnerCheck, 0, 0x40ui64);
  v5->nRespawnActNum = 0;
  v5->nAddLimMSecTime = 0;
}
