/*
 * Function: j_?GetMapInx@CNormalGuildBattleFieldList@GUILD_BATTLE@@QEAA_NEKAEAK@Z
 * Address: 0x14000FA2E
 */

bool __fastcall GUILD_BATTLE::CNormalGuildBattleFieldList::GetMapInx(GUILD_BATTLE::CNormalGuildBattleFieldList *this, char by<PERSON><PERSON>, unsigned int dwMapCode, unsigned int *dwMapInx)
{
  return GUILD_BATTLE::CNormalGuildBattleFieldList::GetMapInx(this, byRace, dwMapCode, dwMapInx);
}
