/*
 * Function: ?_InitCandidate@CandidateRegister@@AEAAXXZ
 * Address: 0x1402B70C0
 */

void __fastcall CandidateRegister::_InitCandidate(CandidateRegister *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CandidateMgr *v3; // rax@7
  CPvpUserAndGuildRankingSystem *v4; // rax@8
  CPvpUserAndGuildRankingSystem *v5; // rax@8
  CPvpUserAndGuildRankingSystem *v6; // rax@11
  CandidateMgr *v7; // rax@13
  CandidateMgr *v8; // rax@17
  __int64 v9; // [sp+0h] [bp-58h]@1
  int j; // [sp+20h] [bp-38h]@4
  int v11; // [sp+24h] [bp-34h]@7
  unsigned int dwAvatorSerial; // [sp+28h] [bp-30h]@8
  unsigned int v13; // [sp+2Ch] [bp-2Ch]@8
  _PVP_RANK_DATA *pData; // [sp+30h] [bp-28h]@11
  unsigned int dwIdx; // [sp+38h] [bp-20h]@14
  _candidate_info *v16; // [sp+40h] [bp-18h]@17
  unsigned int v17; // [sp+48h] [bp-10h]@11
  CandidateRegister *v18; // [sp+60h] [bp+8h]@1

  v18 = this;
  v1 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 3; ++j )
  {
    v3 = CandidateMgr::Instance();
    v11 = CandidateMgr::GetCandidateCnt_1st(v3, j);
    if ( v11 )
      goto LABEL_14;
    v4 = CPvpUserAndGuildRankingSystem::Instance();
    dwAvatorSerial = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v4, j, 0);
    v5 = CPvpUserAndGuildRankingSystem::Instance();
    v13 = CPvpUserAndGuildRankingSystem::FindRank(v5, j, dwAvatorSerial);
    if ( !dwAvatorSerial || v13 == -1 )
      v13 = 1;
    v17 = v13 - 1;
    v6 = CPvpUserAndGuildRankingSystem::Instance();
    pData = CPvpUserAndGuildRankingSystem::GetCurrentPvpRankData(v6, j, v17);
    if ( pData && pData->dwAvatorSerial )
    {
      v7 = CandidateMgr::Instance();
      CandidateMgr::Regist(v7, j, pData);
LABEL_14:
      v18->_kSend[j].byCnt = 0;
      for ( dwIdx = 0; (signed int)dwIdx < 500; ++dwIdx )
      {
        v8 = CandidateMgr::Instance();
        v16 = CandidateMgr::GetCandidate(v8, j, dwIdx);
        if ( v16 && v16->bLoad && v16->eStatus == 1 )
        {
          v18->_kSend[j].Candidacy[v18->_kSend[j].byCnt].byGrade = v16->byGrade;
          v18->_kSend[j].Candidacy[v18->_kSend[j].byCnt].dPvpPoint = v16->dPvpPoint;
          v18->_kSend[j].Candidacy[v18->_kSend[j].byCnt].dwWinCnt = v16->dwWinCnt;
          strcpy_s(v18->_kSend[j].Candidacy[v18->_kSend[j].byCnt].wszAvatorName, 0x11ui64, v16->wszName);
          if ( v16->dwGuildSerial != -1 )
            strcpy_s(v18->_kSend[j].Candidacy[v18->_kSend[j].byCnt].wszGuildName, 0x11ui64, v16->wszGuildName);
          ++v18->_kSend[j].byCnt;
        }
      }
      CandidateRegister::_SortCandidacyByPvpPoint(v18, j);
      continue;
    }
  }
  v18->_bInitCandidate = 1;
}
