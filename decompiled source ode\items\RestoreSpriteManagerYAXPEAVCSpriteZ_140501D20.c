/*
 * Function: ?RestoreSpriteManager@@YAXPEAVCSprite@@@Z
 * Address: 0x140501D20
 */

void __fastcall RestoreSpriteManager(struct CSprite *a1)
{
  struct CSprite *v1; // rbx@1
  char *v2; // rax@2
  int v3; // er8@2
  void *v4; // r10@2
  char *v5; // r9@2
  char *v6; // rdx@2
  char v7; // cl@3
  int v8; // edx@4
  void *v9; // rax@5

  v1 = a1;
  if ( qword_184A79D78 )
  {
    v2 = CSprite::GetFileName(a1);
    v3 = dword_184A79D80;
    v4 = qword_184A79D78;
    v5 = (char *)qword_184A79D78 + 136 * dword_184A79D80;
    v6 = (char *)qword_184A79D78 + 136 * dword_184A79D80;
    do
    {
      v7 = *v2;
      ++v6;
      ++v2;
      *(v6 - 1) = v7;
    }
    while ( v7 );
    v8 = dword_14097896C;
    *((_QWORD *)v5 + 16) = v1;
    dword_184A79D80 = v3 + 1;
    if ( v3 + 1 >= v8 )
    {
      v9 = ReAlloc(v4, 136 * v8, 136 * (v8 + 16));
      dword_14097896C += 16;
      qword_184A79D78 = v9;
    }
  }
}
