/*
 * Function: ?InitNextSetOwnerDate@CWeeklyGuildRankManager@@AEAA_NXZ
 * Address: 0x1402CD250
 */

char __fastcall CWeeklyGuildRankManager::InitNextSetOwnerDate(CWeeklyGuildRankManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  ATL::CTimeSpan *v4; // rax@9
  int v5; // eax@9
  ATL::CTime *v6; // rax@9
  ATL::CTime *v7; // rax@9
  __int64 v8; // [sp+0h] [bp-A8h]@1
  ATL::CTime result; // [sp+48h] [bp-60h]@4
  int v10; // [sp+54h] [bp-54h]@4
  int lDays; // [sp+58h] [bp-50h]@4
  ATL::CTime v12; // [sp+68h] [bp-40h]@9
  ATL::CTimeSpan v13; // [sp+78h] [bp-30h]@9
  ATL::CTime v14; // [sp+80h] [bp-28h]@9
  ATL::CTime v15; // [sp+88h] [bp-20h]@9
  int nDay; // [sp+90h] [bp-18h]@9
  int nMonth; // [sp+94h] [bp-14h]@9
  CWeeklyGuildRankManager *v18; // [sp+B0h] [bp+8h]@1

  v18 = this;
  v1 = &v8;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  ATL::CTime::GetTickCount(&result);
  v10 = ATL::CTime::GetDayOfWeek(&result);
  lDays = 0;
  if ( v10 == 1 )
  {
    lDays = 1;
  }
  else
  {
    if ( v10 > 7 )
      return 0;
    lDays = 7 - v10 + 2;
  }
  ATL::CTimeSpan::CTimeSpan(&v13, lDays, 0, 0, 0);
  ATL::CTime::operator+=(&result, (ATL::CTimeSpan)v4->m_timeSpan);
  nDay = ATL::CTime::GetDay(&result);
  nMonth = ATL::CTime::GetMonth(&result);
  v5 = ATL::CTime::GetYear(&result);
  ATL::CTime::CTime(&v12, v5, nMonth, nDay, 0, 0, 0, -1);
  v18->m_tNextSetOwnerTime = ATL::CTime::GetTime(&v12);
  ATL::CTime::CTime(&v14, v18->m_tNextSetOwnerTime);
  v10 = ATL::CTime::GetDayOfWeek(v6);
  ATL::CTime::CTime(&v15, v18->m_tNextSetOwnerTime);
  ATL::CTime::GetDay(v7);
  return 1;
}
