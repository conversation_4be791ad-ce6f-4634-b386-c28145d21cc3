/*
 * Function: ??0CNationSettingManager@@AEAA@XZ
 * Address: 0x140229190
 */

void __fastcall CNationSettingManager::CNationSettingManager(CNationSettingManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CNationSettingManager *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CTSingleton<CNationSettingManager>::CTSingleton<CNationSettingManager>((CTSingleton<CNationSettingManager> *)&v4->vfptr);
  v4->vfptr = (CTSingleton<CNationSettingManager>Vtbl *)&CNationSettingManager::`vftable';
  v4->m_pData = (CNationSettingData *)&CNationSettingDataNULL::ms_NULL;
}
