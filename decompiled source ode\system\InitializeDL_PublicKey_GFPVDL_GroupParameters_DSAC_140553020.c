/*
 * Function: ?Initialize@?$DL_PublicKey_GFP@VDL_GroupParameters_DSA@CryptoPP@@@CryptoPP@@QEAAXAEBVDL_GroupParameters_IntegerBased@2@AEBVInteger@2@@Z
 * Address: 0x140553020
 */

int __fastcall CryptoPP::DL_PublicKey_GFP<CryptoPP::DL_GroupParameters_DSA>::Initialize(__int64 a1, struct CryptoPP::DL_GroupParameters_IntegerBased *a2, __int64 a3)
{
  CryptoPP::DL_GroupParameters_IntegerBased *v3; // rax@1
  __int64 v5; // [sp+30h] [bp+8h]@1
  struct CryptoPP::DL_GroupParameters_IntegerBased *v6; // [sp+38h] [bp+10h]@1
  __int64 v7; // [sp+40h] [bp+18h]@1

  v7 = a3;
  v6 = a2;
  v5 = a1;
  LODWORD(v3) = CryptoPP::DL_KeyImpl<CryptoPP::X509PublicKey,CryptoPP::DL_GroupParameters_DSA,CryptoPP::OID>::AccessGroupParameters(a1 + 8);
  CryptoPP::DL_GroupParameters_IntegerBased::Initialize(v3, v6);
  return (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v5 + 24i64))(v5, v7);
}
