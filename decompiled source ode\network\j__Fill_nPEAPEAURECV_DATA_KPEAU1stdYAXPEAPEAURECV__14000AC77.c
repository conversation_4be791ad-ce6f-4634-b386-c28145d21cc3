/*
 * Function: j_??$_Fill_n@PEAPEAURECV_DATA@@_KPEAU1@@std@@YAXPEAPEAURECV_DATA@@_KAEBQEAU1@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000AC77
 */

void __fastcall std::_Fill_n<RECV_DATA * *,unsigned __int64,RECV_DATA *>(RECV_DATA **_First, unsigned __int64 _Count, RECV_DATA *const *_Val, std::_Range_checked_iterator_tag __formal)
{
  std::_Fill_n<RECV_DATA * *,unsigned __int64,RECV_DATA *>(_First, _Count, _Val, __formal);
}
