/*
 * Function: ?SetCurPos@CPlayerDB@@QEAAXPEAM@Z
 * Address: 0x140079EE0
 */

void __fastcall CPlayerDB::SetCurPos(CPlayerDB *this, float *fPos)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CPlayerDB *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  memcpy_0(v5->m_dbChar.m_fStartPos, fPos, 0xCui64);
}
