/*
 * Function: j_?_Destroy@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@IEAAXPEAPEAVCLogTypeDBTask@@0@Z
 * Address: 0x14000119A
 */

void __fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Destroy(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, CLogTypeDBTask **_First, CLogTypeDBTask **_Last)
{
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Destroy(this, _First, _Last);
}
