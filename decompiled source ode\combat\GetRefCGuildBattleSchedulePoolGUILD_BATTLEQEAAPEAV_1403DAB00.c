/*
 * Function: ?GetRef@CGuildBattleSchedulePool@GUILD_BATTLE@@QEAAPEAVCGuildBattleSchedule@2@K@Z
 * Address: 0x1403DAB00
 */

GUILD_BATTLE::CGuildBattleSchedule *__fastcall GUILD_BATTLE::CGuildBattleSchedulePool::GetRef(GUILD_BATTLE::CGuildBattleSchedulePool *this, unsigned int dwSID)
{
  GUILD_BATTLE::CGuildBattleSchedule *result; // rax@3

  if ( this->m_ppkSchedule && this->m_dwMaxScheduleCnt > dwSID )
  {
    if ( this->m_ppkSchedule[dwSID] )
      result = this->m_ppkSchedule[dwSID];
    else
      result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
