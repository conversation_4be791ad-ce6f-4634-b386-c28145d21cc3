/*
 * Function: ?SendCancleInfomManager@CRaceBossMsgController@@IEAAXGEKPEBD@Z
 * Address: 0x1402A16C0
 */

void __fastcall CRaceBossMsgController::SendCancleInfomManager(CRaceBossMsgController *this, unsigned __int16 usInx, char ucRet, unsigned int dwMsgID, const char *pwszName)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-98h]@1
  char Dst; // [sp+38h] [bp-60h]@4
  char Dest; // [sp+3Dh] [bp-5Bh]@5
  char v10; // [sp+4Dh] [bp-4Bh]@5
  char pbyType; // [sp+64h] [bp-34h]@6
  char v12; // [sp+65h] [bp-33h]@6
  unsigned __int64 v13; // [sp+80h] [bp-18h]@4
  unsigned __int16 v14; // [sp+A8h] [bp+10h]@1
  char v15; // [sp+B0h] [bp+18h]@1

  v15 = ucRet;
  v14 = usInx;
  v5 = &v7;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v13 = (unsigned __int64)&v7 ^ _security_cookie;
  memset_0(&Dst, 0, 0x16ui64);
  Dst = v15;
  if ( pwszName )
  {
    strncpy(&Dest, pwszName, 0x10ui64);
    v10 = 0;
  }
  pbyType = 52;
  v12 = 5;
  CNetProcess::LoadSendMsg(unk_1414F2088, v14, &pbyType, &Dst, 0x16u);
}
