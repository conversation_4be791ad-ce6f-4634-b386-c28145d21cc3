/*
 * Function: ?IsOtherInvalidObj<PERSON><PERSON>@@YAEPEAVCGameObject@@PEAMPEAVCTrap@@PEAU_TrapItem_fld@@@Z
 * Address: 0x1401404B0
 */

char __usercall IsOtherInvalidObjNear@<al>(CGameObject *pEster@<rcx>, float *pfEstPos@<rdx>, CTrap *pEstObj@<r8>, _TrapItem_fld *pEstTrapItemInfo@<r9>, double a5@<xmm0>)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // eax@4
  _sec_info *v8; // rax@9
  int v10; // eax@22
  __int64 v11; // [sp+0h] [bp-A8h]@1
  _pnt_rect pRect; // [sp+28h] [bp-80h]@4
  int j; // [sp+44h] [bp-64h]@4
  int k; // [sp+48h] [bp-60h]@6
  unsigned int dwSecIndex; // [sp+4Ch] [bp-5Ch]@9
  CObjectList *v16; // [sp+50h] [bp-58h]@9
  CObjectList *v17; // [sp+58h] [bp-50h]@10
  CObjectListVtbl *v18; // [sp+60h] [bp-48h]@12
  CObjectListVtbl *v19; // [sp+68h] [bp-40h]@12
  CObjectListVtbl *v20; // [sp+70h] [bp-38h]@19
  int v21; // [sp+78h] [bp-30h]@19
  double v22; // [sp+80h] [bp-28h]@19
  CObjectListVtbl *v23; // [sp+88h] [bp-20h]@22
  double v24; // [sp+90h] [bp-18h]@23
  int v25; // [sp+98h] [bp-10h]@22
  CGameObject *v26; // [sp+B0h] [bp+8h]@1
  float *fTar; // [sp+B8h] [bp+10h]@1
  CTrap *v28; // [sp+C0h] [bp+18h]@1
  _TrapItem_fld *v29; // [sp+C8h] [bp+20h]@1

  v29 = pEstTrapItemInfo;
  v28 = pEstObj;
  fTar = pfEstPos;
  v26 = pEster;
  v5 = &v11;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v7 = CGameObject::GetCurSecNum(v26);
  CMapData::GetRectInRadius(v26->m_pCurMap, &pRect, 3, v7);
  for ( j = pRect.nStarty; j <= pRect.nEndy; ++j )
  {
    for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
    {
      v8 = CMapData::GetSecInfo(v26->m_pCurMap);
      dwSecIndex = v8->m_nSecNumW * j + k;
      v16 = CMapData::GetSectorListObj(v26->m_pCurMap, v26->m_wMapLayerIndex, dwSecIndex);
      if ( v16 )
      {
        v17 = (CObjectList *)v16->m_Head.m_pNext;
        while ( (_object_list_point *)v17 != &v16->m_Tail )
        {
          v18 = v17->vfptr;
          v17 = (CObjectList *)v17->m_Head.m_pItem;
          v19 = v18 + 2;
          if ( !LOBYTE(v18[2].__vecDelDtor)
            && (BYTE1(v19->__vecDelDtor) == 7 || !BYTE1(v19->__vecDelDtor))
            && (CObjectListVtbl *)v28 != v18 )
          {
            if ( BYTE1(v19->__vecDelDtor) == 7 )
            {
              *(float *)&a5 = *((float *)&v18[5].__vecDelDtor + 1) - fTar[1];
              abs(*(float *)&a5);
              if ( *(float *)&a5 <= 100.0 )
              {
                v20 = v18;
                v21 = (signed int)ffloor(*((float *)v18[1].__vecDelDtor + 91));
                GetSqrt((float *)&v18[5], fTar);
                v22 = *(float *)&a5;
                a5 = (double)v21;
                if ( (double)v21 > v22 )
                  return 15;
              }
            }
            else
            {
              v23 = v18;
              v25 = CPlayerDB::GetRaceCode((CPlayerDB *)&v18[244]);
              v10 = CPlayerDB::GetRaceCode((CPlayerDB *)&v26[10].m_bCorpse);
              if ( v25 != v10 )
              {
                GetSqrt((float *)&v23[5], fTar);
                v24 = *(float *)&a5;
                a5 = v29->m_fGADst;
                if ( a5 > v24 )
                  return 18;
              }
            }
          }
        }
      }
    }
  }
  return 0;
}
