/*
 * Function: ?GetAttackFC@CAttack@@QEAAMPEAVCPlayer@@E_N1@Z
 * Address: 0x14016DD00
 */

float __fastcall CAttack::GetAttackFC(CAttack *this, CPlayer *pPlayer, char bySkill, bool bNear, bool bUnit)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  float result; // xmm0_4@5
  float v8; // xmm0_4@10
  float v9; // xmm0_4@11
  float v10; // xmm0_4@22
  float v11; // xmm0_4@23
  float v12; // xmm0_4@34
  __int64 v13; // [sp+0h] [bp-68h]@1
  char *v14; // [sp+30h] [bp-38h]@4
  _base_fld *v15; // [sp+38h] [bp-30h]@4
  int v16; // [sp+40h] [bp-28h]@8
  float v17; // [sp+44h] [bp-24h]@8
  float v18; // [sp+48h] [bp-20h]@11
  float v19; // [sp+4Ch] [bp-1Ch]@23
  float v20; // [sp+50h] [bp-18h]@34
  CPlayer *v21; // [sp+78h] [bp+10h]@1
  char v22; // [sp+80h] [bp+18h]@1
  bool v23; // [sp+88h] [bp+20h]@1

  v23 = bNear;
  v22 = bySkill;
  v21 = pPlayer;
  v5 = &v13;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v14 = &pPlayer->m_Param.m_dbEquip.m_pStorageList[6].m_bLoad;
  v15 = 0i64;
  if ( *v14 )
  {
    v15 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 6, *(_WORD *)(v14 + 3));
    if ( v15 )
    {
      v16 = _MASTERY_PARAM::GetMasteryPerMast(&v21->m_pmMst, 0, v21->m_pmWpn.byWpClass);
      v17 = 0.0;
      if ( v22 )
      {
        if ( v22 == 1 )
        {
          if ( bUnit )
          {
            _effect_parameter::GetEff_Rate(&v21->m_EP, 32);
            v10 = (float)(*(float *)&v15[10].m_strCode[0] * 0.0) + (float)v16;
            v17 = (float)(*(float *)&v15[10].m_strCode[0] * 0.0) + (float)v16;
          }
          else
          {
            v19 = (float)v21->m_pmWpn.nGaMaxAF;
            v11 = v19;
            _effect_parameter::GetEff_Rate(&v21->m_EP, 32);
            v10 = (float)(v19 * v11) + (float)v16;
            v17 = v10;
          }
          if ( !v23 || GetWeaponClass(*(_WORD *)(v14 + 3)) )
          {
            if ( v23 || GetWeaponClass(*(_WORD *)(v14 + 3)) != 1 )
            {
              v17 = 0.0;
            }
            else
            {
              _effect_parameter::GetEff_Rate(&v21->m_EP, 3);
              v17 = v17 * v10;
            }
          }
          else
          {
            _effect_parameter::GetEff_Rate(&v21->m_EP, 2);
            v17 = v17 * v10;
          }
        }
        else if ( bUnit )
        {
          _effect_parameter::GetEff_Rate(&v21->m_EP, 32);
          v17 = (float)(*(float *)&v15[10].m_strCode[24] * 0.0) + (float)v21->m_pmMst.m_mtyStaff;
        }
        else
        {
          v20 = (float)v21->m_pmWpn.nMaMaxAF;
          v12 = v20;
          _effect_parameter::GetEff_Rate(&v21->m_EP, 32);
          v17 = (float)(v20 * v12) + (float)v21->m_pmMst.m_mtyStaff;
        }
      }
      else
      {
        if ( bUnit )
        {
          _effect_parameter::GetEff_Rate(&v21->m_EP, 32);
          v8 = (float)(*(float *)&v15[10].m_strCode[0] * 0.0) + (float)(signed int)CPlayer::s_nAddMstFc[v16];
          v17 = (float)(*(float *)&v15[10].m_strCode[0] * 0.0) + (float)(signed int)CPlayer::s_nAddMstFc[v16];
        }
        else
        {
          v18 = (float)v21->m_pmWpn.nGaMaxAF;
          v9 = v18;
          _effect_parameter::GetEff_Rate(&v21->m_EP, 32);
          v8 = (float)(v18 * v9) + (float)(signed int)CPlayer::s_nAddMstFc[v16];
          v17 = v8;
        }
        if ( !v23 || GetWeaponClass(*(_WORD *)(v14 + 3)) )
        {
          if ( v23 || GetWeaponClass(*(_WORD *)(v14 + 3)) != 1 )
          {
            v17 = 0.0;
          }
          else
          {
            _effect_parameter::GetEff_Rate(&v21->m_EP, 1);
            v17 = v17 * v8;
          }
        }
        else
        {
          _effect_parameter::GetEff_Rate(&v21->m_EP, 0);
          v17 = v17 * v8;
        }
      }
      result = v17;
    }
    else
    {
      result = 0.0;
    }
  }
  else
  {
    result = 0.0;
  }
  return result;
}
