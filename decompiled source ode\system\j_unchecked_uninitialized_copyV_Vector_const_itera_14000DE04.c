/*
 * Function: j_??$unchecked_uninitialized_copy@V?$_Vector_const_iterator@UAreaData@@V?$allocator@UAreaData@@@std@@@std@@PEAUAreaData@@V?$allocator@UAreaData@@@2@@stdext@@YAPEAUAreaData@@V?$_Vector_const_iterator@UAreaData@@V?$allocator@UAreaData@@@std@@@std@@0PEAU1@AEAV?$allocator@UAreaData@@@3@@Z
 * Address: 0x14000DE04
 */

AreaData *__fastcall stdext::unchecked_uninitialized_copy<std::_Vector_const_iterator<AreaData,std::allocator<AreaData>>,AreaData *,std::allocator<AreaData>>(std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *_First, std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > *_Last, AreaData *_Dest, std::allocator<AreaData> *_Al)
{
  return stdext::unchecked_uninitialized_copy<std::_Vector_const_iterator<AreaData,std::allocator<AreaData>>,AreaData *,std::allocator<AreaData>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
