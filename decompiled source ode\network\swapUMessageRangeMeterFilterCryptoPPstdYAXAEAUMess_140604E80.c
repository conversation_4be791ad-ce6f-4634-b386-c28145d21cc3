/*
 * Function: ??$swap@UMessageRange@MeterFilter@CryptoPP@@@std@@YAXAEAUMessageRange@MeterFilter@CryptoPP@@0@Z
 * Address: 0x140604E80
 */

char *__fastcall std::swap<CryptoPP::MeterFilter::MessageRange>(const void *a1, const void *a2)
{
  char *result; // rax@1
  char v3; // [sp+0h] [bp-68h]@1
  char v4; // [sp+18h] [bp-50h]@1
  char v5; // [sp+30h] [bp-38h]@1

  qmemcpy(&v5, a1, 0x18ui64);
  qmemcpy(&v3, &v5, 0x18ui64);
  qmemcpy(&v4, a2, 0x18ui64);
  qmemcpy((void *)a1, &v4, 0x18ui64);
  result = &v3;
  qmemcpy((void *)a2, &v3, 0x18ui64);
  return result;
}
