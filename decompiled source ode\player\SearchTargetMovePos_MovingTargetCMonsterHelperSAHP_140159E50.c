/*
 * Function: ?SearchTargetMovePos_MovingTarget@CMonsterHelper@@SAHPEAVCMonster@@PEAVCCharacter@@AEAY02M@Z
 * Address: 0x140159E50
 */

signed __int64 __usercall CMonsterHelper::SearchTargetMovePos_MovingTarget@<rax>(CMonster *pMon@<rcx>, CCharacter *pTargetCharacter@<rdx>, float (*tarPos)[3]@<r8>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  float v6; // xmm0_4@4
  signed __int64 result; // rax@6
  __int64 v8; // [sp+0h] [bp-38h]@1
  float v9; // [sp+20h] [bp-18h]@4
  double v10; // [sp+28h] [bp-10h]@4
  CMonster *v11; // [sp+40h] [bp+8h]@1
  CCharacter *v12; // [sp+48h] [bp+10h]@1
  float (*out)[3]; // [sp+50h] [bp+18h]@1

  out = tarPos;
  v12 = pTargetCharacter;
  v11 = pMon;
  v4 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  ((void (__fastcall *)(CMonster *))v11->vfptr->GetAttackRange)(v11);
  v6 = a4 * 0.69999999;
  v9 = v6;
  GetSqrt(v12->m_fCurPos, v11->m_fCurPos);
  v10 = v6;
  if ( v9 < (double)v6 || v9 < 1.0 )
  {
    CMonsterHelper::GetDirection((float (*)[3])v12->m_fCurPos, (float (*)[3])v12->m_fTarPos, out, 50.0);
    result = 1i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
