/*
 * Function: ?SendMsg_RealFixPosition@CGameObject@@UEAAX_N@Z
 * Address: 0x14017B050
 */

void __fastcall CGameObject::SendMsg_RealFixPosition(CGameObject *this, bool bCircle)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+38h] [bp-40h]@4
  char v6; // [sp+39h] [bp-3Fh]@4
  unsigned __int16 v7; // [sp+3Ah] [bp-3Eh]@4
  unsigned int v8; // [sp+3Ch] [bp-3Ch]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v10; // [sp+55h] [bp-23h]@4
  CGameObject *v11; // [sp+80h] [bp+8h]@1

  v11 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  szMsg = v11->m_ObjID.m_byKind;
  v6 = v11->m_ObjID.m_byID;
  v7 = v11->m_ObjID.m_wIndex;
  v8 = v11->m_dwObjSerial;
  pbyType = 4;
  v10 = 10;
  if ( bCircle )
    CGameObject::CircleReport(v11, &pbyType, &szMsg, 8, 0);
  else
    CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, &szMsg, 8u);
}
