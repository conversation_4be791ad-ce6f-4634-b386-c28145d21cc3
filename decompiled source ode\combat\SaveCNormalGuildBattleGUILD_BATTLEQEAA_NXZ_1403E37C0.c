/*
 * Function: ?Save@CNormalGuildBattle@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403E37C0
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattle::Save(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned int v4; // eax@6
  GUILD_BATTLE::CGuildBattleLogger *v5; // rax@7
  __int64 v6; // [sp+0h] [bp-68h]@1
  unsigned int dwMapID; // [sp+20h] [bp-48h]@6
  char byNumber[4]; // [sp+28h] [bp-40h]@6
  int v9; // [sp+30h] [bp-38h]@7
  unsigned int v10; // [sp+40h] [bp-28h]@6
  unsigned int dwP2GuildSerial; // [sp+44h] [bp-24h]@6
  int v12; // [sp+48h] [bp-20h]@7
  unsigned int v13; // [sp+4Ch] [bp-1Ch]@7
  unsigned int v14; // [sp+50h] [bp-18h]@7
  unsigned int v15; // [sp+54h] [bp-14h]@7
  GUILD_BATTLE::CNormalGuildBattle *v16; // [sp+70h] [bp+8h]@1

  v16 = this;
  v1 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v16->m_bInit )
  {
    v10 = GUILD_BATTLE::CNormalGuildBattleField::GetMapCode(v16->m_pkField);
    dwP2GuildSerial = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v16->m_k2P);
    v4 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v16->m_k1P);
    byNumber[0] = v16->m_byGuildBattleNumber;
    dwMapID = v10;
    if ( CRFWorldDatabase::UpdateGuildBattleInfo(pkDB, v16->m_dwID, v4, dwP2GuildSerial, v10, byNumber[0]) )
    {
      result = 1;
    }
    else
    {
      v12 = v16->m_byGuildBattleNumber;
      v13 = GUILD_BATTLE::CNormalGuildBattleField::GetMapID(v16->m_pkField);
      v14 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v16->m_k2P);
      v15 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v16->m_k1P);
      v5 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v9 = v12;
      *(_DWORD *)byNumber = v13;
      dwMapID = v14;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v5,
        "CNormalGuildBattle::Save()g_Main.m_pWorldDB->UpdateGuildBattleInfo( %u, %u, %u, %u, %u ) Fail!",
        v16->m_dwID,
        v15);
      result = 0;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
