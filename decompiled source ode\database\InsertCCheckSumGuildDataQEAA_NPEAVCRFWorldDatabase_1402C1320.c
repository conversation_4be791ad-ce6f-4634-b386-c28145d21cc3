/*
 * Function: ?Insert@CCheckSumGuildData@@QEAA_NPEAVCRFWorldDatabase@@@Z
 * Address: 0x1402C1320
 */

bool __fastcall CCheckSumGuildData::Insert(CCheckSumGuildData *this, CRFWorldDatabase *pkDB)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CCheckSumGuildData *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pkDB )
    result = CRFWorldDatabase::Insert_UnitData(pkDB, v6->m_dwGuildSerial, v6->m_dValues);
  else
    result = 0;
  return result;
}
