/*
 * Function: ?CheckDestroyerIsArriveMine@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x14027CED0
 */

void __fastcall CHolyStoneSystem::CheckDestroyerIsArriveMine(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@10
  float *v4; // rax@16
  unsigned __int32 v5; // ecx@16
  char v6; // al@17
  __int64 v7; // [sp+0h] [bp-48h]@1
  CHolyScheduleData::__HolyScheduleNode *v8; // [sp+20h] [bp-28h]@17
  unsigned int v9; // [sp+28h] [bp-20h]@17
  int nControlSec; // [sp+2Ch] [bp-1Ch]@17
  CExtDummy *v11; // [sp+30h] [bp-18h]@16
  CHolyStoneSystem *v12; // [sp+50h] [bp+8h]@1

  v12 = this;
  v1 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CHolyStoneSystem::GetHolyMasterRace(v12) != -1
    && CHolyStoneSystem::GetSceneCode(v12) == 2
    && v12->m_SaveData.m_eDestroyerState == 2 )
  {
    if ( v12->m_pkDestroyer && CHolyStoneSystem::GetDestroyerSerial(v12) == v12->m_pkDestroyer->m_dwObjSerial
      || (v3 = CHolyStoneSystem::GetDestroyerSerial(v12),
          (v12->m_pkDestroyer = GetPtrPlayerFromSerial(&g_Player, 2532, v3)) != 0i64) )
    {
      if ( v12->m_pkDestroyer->m_bLive && v12->m_pkDestroyer->m_bOper )
      {
        if ( v12->m_pkDestroyer->m_bCorpse )
        {
          v12->m_SaveData.m_eDestroyerState = 0;
          v12->m_SaveData.m_dwDestroyerSerial = -1;
          CHolyStoneSystem::ReleaseLastAttBuff(v12);
          v12->m_SaveData.m_dwDestroyerGuildSerial = -1;
          CHolyStoneSystemDataMgr::SaveStateData(&v12->m_SaveData);
          CHolyStoneSystem::SendIsArriveDestroyer(v12, 0);
        }
        else
        {
          v4 = v12->m_pkDestroyer->m_fCurPos;
          v5 = v12->m_HolyKeeperData.ActiveDummy.m_wLineIndex;
          v11 = &v12->m_HolyKeeperData.pCreateMap->m_Dummy;
          if ( CExtDummy::IsInBBox(v11, v5, v4) )
          {
            v6 = CHolyStoneSystem::GetNumOfTime(v12);
            v8 = CHolyScheduleData::GetIndex(&v12->m_ScheculeData, (unsigned __int8)v6);
            v12->m_SaveData.m_dwTerm[1] += v8->m_nSceneTime[6];
            v12->m_dwCheckTime[5] = v12->m_SaveData.m_dwTerm[1] + v12->m_SaveData.m_dwTerm[0] + v12->m_dwCheckTime[3];
            v12->m_SaveData.m_eDestroyerState = 1;
            CHolyStoneSystemDataMgr::SaveStateData(&v12->m_SaveData);
            CHolyStoneSystem::SendIsArriveDestroyer(v12, 1);
            CLogFile::Write(&v12->m_logQuestDestroy, ">> Arrive!");
            v9 = v12->m_dwCheckTime[2] - GetLoopTime();
            nControlSec = (v8->m_nSceneTime[2] + v12->m_SaveData.m_dwTerm[1] + v12->m_SaveData.m_dwTerm[0] + v9) / 0x3E8;
            CHolyStoneSystem::SendSMS_MineTimeExtend(v12, nControlSec);
          }
        }
      }
    }
  }
}
