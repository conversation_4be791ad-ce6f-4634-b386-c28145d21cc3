/*
 * Function: ?init@lua_tinker@@YAXPEAUlua_State@@@Z
 * Address: 0x140443F70
 */

void __fastcall lua_tinker::init(struct lua_State *L)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  struct lua_State *La; // [sp+30h] [bp+8h]@1

  La = L;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  lua_tinker::init_s64(La);
  lua_tinker::init_u64(La);
}
