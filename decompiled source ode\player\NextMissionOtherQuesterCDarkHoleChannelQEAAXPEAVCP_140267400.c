/*
 * Function: ?NextMissionOtherQuester@CDarkHoleChannel@@QEAAXPEAVCPlayer@@PEAV_dh_mission_setup@@@Z
 * Address: 0x140267400
 */

void __fastcall CDarkHoleChannel::NextMissionOtherQuester(CDarkHoleChannel *this, CPlayer *pLeader, _dh_mission_setup *pNextMission)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  _dh_quest_setup *v5; // rax@10
  char *v6; // rax@10
  _dh_quest_setup *v7; // rcx@11
  char *v8; // rcx@11
  __int64 v9; // [sp+0h] [bp-88h]@1
  float *pfStartPos; // [sp+20h] [bp-68h]@10
  int j; // [sp+30h] [bp-58h]@4
  float pNewPos; // [sp+48h] [bp-40h]@9
  __int64 v13; // [sp+68h] [bp-20h]@11
  __int64 v14; // [sp+70h] [bp-18h]@11
  CDarkHoleChannel *v15; // [sp+90h] [bp+8h]@1
  CPlayer *v16; // [sp+98h] [bp+10h]@1
  _dh_mission_setup *v17; // [sp+A0h] [bp+18h]@1

  v17 = pNextMission;
  v16 = pLeader;
  v15 = this;
  v3 = &v9;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; j < 32; ++j )
  {
    if ( _dh_player_mgr::IsFill(&v15->m_Quester[j]) && v15->m_Quester[j].dwSerial != v16->m_dwObjSerial )
    {
      if ( CMapData::GetRandPosInDummy(v15->m_pQuestSetup->pUseMap, v17->pStartDummy, &pNewPos, 1) )
      {
        v5 = v15->m_pQuestSetup;
        pfStartPos = &pNewPos;
        CPlayer::OutOfMap(v15->m_Quester[j].pOne, v5->pUseMap, v15->m_wLayerIndex, 4, &pNewPos);
        v6 = (char *)v15->m_pQuestSetup->pUseMap->m_pMapSet;
        LOBYTE(pfStartPos) = 4;
        CPlayer::SendMsg_GotoRecallResult(v15->m_Quester[j].pOne, 0, *v6, &pNewPos, 4);
      }
      else
      {
        v7 = v15->m_pQuestSetup;
        v13 = 48i64 * j;
        pfStartPos = v16->m_fCurPos;
        CPlayer::OutOfMap(v15->m_Quester[j].pOne, v7->pUseMap, v15->m_wLayerIndex, 4, v16->m_fCurPos);
        v8 = (char *)v15->m_pQuestSetup->pUseMap->m_pMapSet;
        v14 = 48i64 * j;
        LOBYTE(pfStartPos) = 4;
        CPlayer::SendMsg_GotoRecallResult(v15->m_Quester[j].pOne, 0, *v8, v16->m_fCurPos, 4);
      }
    }
  }
}
