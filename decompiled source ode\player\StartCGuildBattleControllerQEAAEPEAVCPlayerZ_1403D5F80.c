/*
 * Function: ?Start@CGuildBattleController@@QEAAEPEAVCPlayer@@@Z
 * Address: 0x1403D5F80
 */

char __fastcall CGuildBattleController::Start(CGuildBattleController *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleManager *v4; // rax@7
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int dwGuildSerial; // [sp+20h] [bp-18h]@7
  unsigned int dwCharacSerial; // [sp+24h] [bp-14h]@7
  int v9; // [sp+28h] [bp-10h]@5
  CPlayer *pkPlayera; // [sp+48h] [bp+10h]@1

  pkPlayera = pkPlayer;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pkPlayer->m_Param.m_pGuild )
    v9 = pkPlayer->m_Param.m_pGuild->m_dwSerial;
  else
    v9 = -1;
  dwGuildSerial = v9;
  dwCharacSerial = pkPlayer->m_pUserDB->m_dwSerial;
  v4 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
  return GUILD_BATTLE::CNormalGuildBattleManager::Start(v4, pkPlayera, dwGuildSerial, dwCharacSerial);
}
