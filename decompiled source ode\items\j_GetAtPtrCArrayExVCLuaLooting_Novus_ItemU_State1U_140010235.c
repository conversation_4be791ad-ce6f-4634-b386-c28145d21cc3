/*
 * Function: j_?GetAtPtr@?$CArrayEx@VCLuaLooting_Novus_Item@@U_State@1@@US@@QEAAPEAVCLuaLooting_Novus_Item@@K@Z
 * Address: 0x140010235
 */

CLuaLooting_Novus_Item *__fastcall US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::GetAtPtr(US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State> *this, unsigned int dwIndex)
{
  return US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::GetAtPtr(this, dwIndex);
}
