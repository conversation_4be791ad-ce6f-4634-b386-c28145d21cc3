/*
 * Function: ?make_wpactive_skill_attack_param@CPlayer@@QEAAXPEAVCCharacter@@PEAU_skill_fld@@PEAMEHPEAU_db_con@_STORAGE_LIST@@MPEAU_attack_param@@PEAH@Z
 * Address: 0x14008B0A0
 */

void __fastcall CPlayer::make_wpactive_skill_attack_param(CPlayer *this, CCharacter *pDst, _skill_fld *pSkillFld, float *pfAttackPos, char byEffectCode, int nAttType, _STORAGE_LIST::_db_con *pBulletItem, float fAddBulletFc, _attack_param *pAP, int *nShotNum)
{
  __int64 *v10; // rdi@1
  signed __int64 i; // rcx@1
  float v12; // xmm0_4@11
  int v13; // eax@11
  float v14; // xmm0_4@11
  int v15; // eax@11
  float v16; // xmm0_4@12
  int v17; // eax@12
  float v18; // xmm0_4@12
  int v19; // eax@12
  __int64 v20; // [sp+0h] [bp-58h]@1
  _base_fld *v21; // [sp+20h] [bp-38h]@8
  float v22; // [sp+28h] [bp-30h]@11
  float v23; // [sp+2Ch] [bp-2Ch]@11
  float v24; // [sp+30h] [bp-28h]@11
  float v25; // [sp+34h] [bp-24h]@11
  float v26; // [sp+38h] [bp-20h]@12
  float v27; // [sp+3Ch] [bp-1Ch]@12
  float v28; // [sp+40h] [bp-18h]@12
  float v29; // [sp+44h] [bp-14h]@12
  CPlayer *pTarget; // [sp+60h] [bp+8h]@1
  CMonster *v31; // [sp+68h] [bp+10h]@1
  _skill_fld *v32; // [sp+70h] [bp+18h]@1
  float *Src; // [sp+78h] [bp+20h]@1

  Src = pfAttackPos;
  v32 = pSkillFld;
  v31 = (CMonster *)pDst;
  pTarget = this;
  v10 = &v20;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v10 = -858993460;
    v10 = (__int64 *)((char *)v10 + 4);
  }
  pAP->pDst = pDst;
  if ( pDst )
    pAP->nPart = CCharacter::GetAttackRandomPart(pDst);
  else
    pAP->nPart = CCharacter::GetAttackRandomPart((CCharacter *)&pTarget->vfptr);
  if ( pBulletItem )
  {
    v21 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 10, pBulletItem->m_wItemIndex);
    pAP->nTol = *(_DWORD *)&v21[6].m_strCode[40];
  }
  else
  {
    pAP->nTol = pTarget->m_pmWpn.byAttTolType;
  }
  pAP->nClass = pTarget->m_pmWpn.byWpClass;
  if ( pTarget->m_pmWpn.byWpType == 7 )
  {
    v26 = (float)pTarget->m_pmWpn.nGaMinAF;
    v16 = v26;
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
    v27 = (float)(v26 * v16) * fAddBulletFc;
    v17 = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 6, 0);
    pAP->nMinAF = (signed int)ffloor(v27 + (float)v17);
    v28 = (float)pTarget->m_pmWpn.nGaMaxAF;
    v18 = v28;
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
    v29 = (float)(v28 * v18) * fAddBulletFc;
    v19 = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 6, 0);
    pAP->nMaxAF = (signed int)ffloor(v29 + (float)v19);
  }
  else
  {
    v22 = (float)pTarget->m_pmWpn.nGaMinAF;
    v12 = v22;
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
    v23 = (float)(v22 * v12) * fAddBulletFc;
    v13 = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 0, pTarget->m_pmWpn.byWpClass);
    pAP->nMinAF = (signed int)ffloor(v23 + (float)v13);
    v24 = (float)pTarget->m_pmWpn.nGaMaxAF;
    v14 = v24;
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
    v25 = (float)(v24 * v14) * fAddBulletFc;
    v15 = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 0, pTarget->m_pmWpn.byWpClass);
    pAP->nMaxAF = (signed int)ffloor(v25 + (float)v15);
  }
  pAP->nMinSel = pTarget->m_pmWpn.byGaMinSel;
  pAP->nMaxSel = pTarget->m_pmWpn.byGaMaxSel;
  pAP->nExtentRange = 20;
  if ( pBulletItem )
  {
    pAP->nShotNum = v32->m_nAttNeedBt;
    if ( pBulletItem->m_dwDur < v32->m_nAttNeedBt )
      pAP->nShotNum = pBulletItem->m_dwDur;
    *nShotNum += pAP->nShotNum;
  }
  if ( nAttType == 3 )
    pAP->nAddAttPnt = (signed int)ffloor((float)((int (__fastcall *)(CPlayer *))pTarget->vfptr->GetHP)(pTarget) * 0.89999998);
  pAP->pFld = (_base_fld *)v32;
  pAP->byEffectCode = byEffectCode;
  if ( byEffectCode )
  {
    pAP->nLevel = 1;
    pAP->nMastery = 99;
  }
  else
  {
    pAP->nLevel = pTarget->m_pmWpn.nActiveEffLvl;
    if ( pAP->nLevel > 7 )
      pAP->nLevel = 7;
    pAP->nMastery = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 3, v32->m_nMastIndex);
  }
  memcpy_0(pAP->fArea, Src, 0xCui64);
  pAP->nMaxAttackPnt = pTarget->m_nMaxAttackPnt;
  if ( v31 && v31->m_ObjID.m_byKind == 1 && !CMonster::IsViewArea(v31, (CCharacter *)&pTarget->vfptr) )
    pAP->bBackAttack = 1;
}
