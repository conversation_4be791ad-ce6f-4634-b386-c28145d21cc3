/*
 * Function: ?Set<PERSON>@CPlayer@@UEAA_NH_N@Z
 * Address: 0x14005EFA0
 */

char __fastcall CPlayer::SetHP(CPlayer *this, int nHP, bool bOver)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@6
  int v6; // eax@9
  char result; // al@11
  signed int v8; // eax@15
  __int64 v9; // [sp+0h] [bp-48h]@1
  int v10; // [sp+20h] [bp-28h]@4
  int v11; // [sp+24h] [bp-24h]@15
  CGameObjectVtbl *v12; // [sp+28h] [bp-20h]@15
  int v13; // [sp+30h] [bp-18h]@15
  int v14; // [sp+34h] [bp-14h]@15
  CGameObjectVtbl *v15; // [sp+38h] [bp-10h]@15
  CPlayer *v16; // [sp+50h] [bp+8h]@1
  int dwHP; // [sp+58h] [bp+10h]@1
  bool v18; // [sp+60h] [bp+18h]@1

  v18 = bOver;
  dwHP = nHP;
  v16 = this;
  v3 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = ((int (__fastcall *)(CPlayer *))v16->vfptr->GetHP)(v16);
  if ( !v18 && dwHP > v10 )
  {
    v5 = ((int (__fastcall *)(CPlayer *))v16->vfptr->GetMaxHP)(v16);
    if ( v10 >= v5 || dwHP <= ((int (__fastcall *)(_QWORD))v16->vfptr->GetMaxHP)(v16) )
    {
      v6 = ((int (__fastcall *)(CPlayer *))v16->vfptr->GetMaxHP)(v16);
      if ( v10 >= v6 && dwHP >= v10 )
        return 0;
    }
    else
    {
      dwHP = ((int (__fastcall *)(CPlayer *))v16->vfptr->GetMaxHP)(v16);
    }
  }
  if ( dwHP <= 0 )
  {
    dwHP = 0;
    if ( v16->m_bNeverDie )
    {
      if ( ((int (__fastcall *)(_QWORD))v16->vfptr->GetMaxHP)(v16) / 2 > 0 )
      {
        v11 = rand();
        v12 = v16->vfptr;
        v13 = ((int (__fastcall *)(CPlayer *))v12->GetMaxHP)(v16) / 2;
        v14 = v11 % v13;
        v15 = v16->vfptr;
        v8 = ((int (__fastcall *)(CPlayer *))v15->GetMaxHP)(v16);
        dwHP = v8 / 2 + v14;
      }
    }
  }
  if ( v10 == dwHP )
  {
    result = 0;
  }
  else
  {
    CPlayerDB::SetHP(&v16->m_Param, dwHP);
    result = 1;
  }
  return result;
}
