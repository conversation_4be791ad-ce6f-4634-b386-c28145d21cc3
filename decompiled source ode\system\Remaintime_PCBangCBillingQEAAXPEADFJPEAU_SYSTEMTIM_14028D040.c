/*
 * Function: ?Remaintime_PCBang@CBilling@@QEAAXPEADFJPEAU_SYSTEMTIME@@@Z
 * Address: 0x14028D040
 */

void __fastcall CBilling::Remaintime_PCBang(CBilling *this, char *szCMSCode, __int16 iType, int lRemaintime, _SYSTEMTIME *pstEndDate)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@7
  CPlayer *v9; // [sp+28h] [bp-10h]@10
  CBilling *v10; // [sp+40h] [bp+8h]@1
  const char *Str; // [sp+48h] [bp+10h]@1
  __int16 v12; // [sp+50h] [bp+18h]@1
  int lRemainTime; // [sp+58h] [bp+20h]@1

  lRemainTime = lRemaintime;
  v12 = iType;
  Str = szCMSCode;
  v10 = this;
  v5 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( v10->m_bOper && szCMSCode && strlen_0(szCMSCode) >= 1 )
  {
    for ( j = 0; j < 2532; ++j )
    {
      v9 = &g_Player + j;
      if ( v9 && v9->m_bLive && !strcmp_0(v9->m_pUserDB->m_BillingInfo.szCMS, Str) )
      {
        CPlayer::SendMsg_RemainTimeInform(v9, v12, lRemainTime, pstEndDate);
        CUserDB::SetRemainTime(v9->m_pUserDB, lRemainTime);
      }
    }
  }
}
