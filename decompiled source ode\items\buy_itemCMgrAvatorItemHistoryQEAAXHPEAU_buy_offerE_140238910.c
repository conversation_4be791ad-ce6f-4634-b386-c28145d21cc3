/*
 * Function: ?buy_item@CMgrAvatorItemHistory@@QEAAXHPEAU_buy_offer@@EKKKKPEAD@Z
 * Address: 0x140238910
 */

void __fastcall CMgrAvatorItemHistory::buy_item(CMgrAvatorItemHistory *this, int n, _buy_offer *pOffer, char byOfferNum, unsigned int dwCostDalant, unsigned int dwCostGold, unsigned int dwNewDalant, unsigned int dwNewGold, char *pszFileName)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  char *v11; // rax@6
  __int64 v12; // [sp+0h] [bp-78h]@1
  char *v13; // [sp+20h] [bp-58h]@4
  __int64 v14; // [sp+28h] [bp-50h]@4
  unsigned int v15; // [sp+30h] [bp-48h]@4
  char *v16; // [sp+38h] [bp-40h]@4
  char *v17; // [sp+40h] [bp-38h]@4
  int j; // [sp+50h] [bp-28h]@4
  char *v19; // [sp+58h] [bp-20h]@6
  _base_fld *v20; // [sp+60h] [bp-18h]@6
  CMgrAvatorItemHistory *v21; // [sp+80h] [bp+8h]@1
  _buy_offer *v22; // [sp+90h] [bp+18h]@1
  char v23; // [sp+98h] [bp+20h]@1

  v23 = byOfferNum;
  v22 = pOffer;
  v21 = this;
  v9 = &v12;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  sData[0] = 0;
  v17 = v21->m_szCurTime;
  v16 = v21->m_szCurDate;
  v15 = dwNewGold;
  LODWORD(v14) = dwNewDalant;
  LODWORD(v13) = dwCostGold;
  sprintf(sBuf, "BUY: num:%d pay(D:%u G:%u) $D:%u $G:%u [%s %s]\r\n", (unsigned __int8)byOfferNum, dwCostDalant);
  strcat_0(sData, sBuf);
  for ( j = 0; j < (unsigned __int8)v23; ++j )
  {
    v19 = &v22[j].Item.m_bLoad;
    v20 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v22[j].Item.m_byTableCode, v22[j].Item.m_wItemIndex);
    v11 = DisplayItemUpgInfo((unsigned __int8)v19[1], *(_DWORD *)(v19 + 13));
    v14 = *((_QWORD *)v19 + 3);
    v13 = v11;
    sprintf(sBuf, "\t+ %s_%u_@%s[%I64u]\r\n", v20->m_strCode, *(_QWORD *)(v19 + 5));
    strcat_0(sData, sBuf);
  }
  CMgrAvatorItemHistory::WriteFile(v21, pszFileName, sData);
}
