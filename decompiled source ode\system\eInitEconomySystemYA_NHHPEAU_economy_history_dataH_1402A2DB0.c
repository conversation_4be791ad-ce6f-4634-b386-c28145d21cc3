/*
 * Function: ?eInitEconomySystem@@YA_NHHPEAU_economy_history_data@@H0@Z
 * Address: 0x1402A2DB0
 */

char __fastcall eInitEconomySystem(int nCurMgrValue, int nNextMgrValue, _economy_history_data *pData, int nHisNum, _economy_history_data *pCurData)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-288h]@1
  int j; // [sp+20h] [bp-268h]@10
  int k; // [sp+24h] [bp-264h]@13
  _economy_calc_data pDataa; // [sp+40h] [bp-248h]@17
  int l; // [sp+144h] [bp-144h]@17
  int m; // [sp+148h] [bp-140h]@23
  _economy_calc_data v14; // [sp+160h] [bp-128h]@30
  int n; // [sp+264h] [bp-24h]@30
  int ii; // [sp+268h] [bp-20h]@36
  unsigned __int64 v17; // [sp+278h] [bp-10h]@4
  _economy_history_data *v18; // [sp+2A0h] [bp+18h]@1
  int v19; // [sp+2A8h] [bp+20h]@1

  v19 = nHisNum;
  v18 = pData;
  v5 = &v8;
  for ( i = 160i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v17 = (unsigned __int64)&v8 ^ _security_cookie;
  if ( e_EconomySystem.m_bLoad )
  {
    result = 1;
  }
  else if ( nHisNum >= 12 )
  {
    _ECONOMY_SYSTEM::Init(&e_EconomySystem);
    e_EconomySystem.m_dwLastDate = eGetLocalDate();
    if ( _ReadEconomyIniFile() )
    {
      for ( j = 0; j < 3; ++j )
        _economy_history_data::Init(&e_EconomyHistory[j]);
      for ( k = 0; k < v19; ++k )
      {
        for ( j = 0; j < 3; ++j )
        {
          pDataa.dTradeDalant[j] = v18[k].dTradeDalant[j];
          pDataa.dTradeGold[j] = DOUBLE_1_0;
          for ( l = 0; l < 3; ++l )
          {
            pDataa.dOreMineCount[j][l] = v18[k].dOreMineCount[j][l];
            pDataa.dOreCutCount[j][l] = v18[k].dOreCutCount[j][l];
          }
        }
        _UpdateNewEconomy(&pDataa);
        for ( j = 0; j < 3; ++j )
        {
          e_EconomyHistory[k].dTradeDalant[j] = pDataa.dTradeDalant[j];
          e_EconomyHistory[k].dTradeGold[j] = pDataa.dTradeGold[j];
          e_EconomyHistory[k].wEconomyGuide[j] = pDataa.out_wEconomyGuide[j];
          for ( m = 0; m < 3; ++m )
          {
            e_EconomyHistory[k].dOreMineCount[j][m] = pDataa.dOreMineCount[j][m];
            e_EconomyHistory[k].dOreCutCount[j][m] = pDataa.dOreCutCount[j][m];
          }
        }
      }
      e_EconomySystem.m_byCurHour = GetCurrentHour();
      for ( j = 0; j < 3; ++j )
      {
        v14.dTradeDalant[j] = v18[11].dTradeDalant[j];
        v14.dTradeGold[j] = DOUBLE_1_0;
        for ( n = 0; n < 3; ++n )
        {
          v14.dOreMineCount[j][n] = v18[11].dOreMineCount[j][n];
          v14.dOreCutCount[j][n] = v18[11].dOreCutCount[j][n];
        }
      }
      _UpdateNewEconomy(&v14);
      for ( j = 0; j < 3; ++j )
      {
        e_EconomySystem.m_CurRate[j].dOldTradeDalant = v14.dTradeDalant[j];
        e_EconomySystem.m_CurRate[j].dOldTradeGold = v14.dTradeGold[j];
        e_EconomySystem.m_CurRate[j].fPayExgRate = v14.out_fPayExgRate[j];
        e_EconomySystem.m_CurRate[j].fTexRate = v14.out_fTexRate[j];
        e_EconomySystem.m_CurRate[j].fOreRate = v14.out_fOreRate[j];
        e_EconomySystem.m_CurRate[j].wEconomyGuide = v14.out_wEconomyGuide[j];
        e_EconomySystem.m_CurRate[j].dwTexRate = v14.out_dwTexRate[j];
        for ( ii = 0; ii < 3; ++ii )
        {
          e_EconomySystem.m_CurRate[j].dOldOreMineCount[ii] = v14.dOreMineCount[j][ii];
          e_EconomySystem.m_CurRate[j].dOldOreCutCount[ii] = v14.dOreCutCount[j][ii];
        }
        e_EconomySystem.m_dCurTradeDalant[j] = pCurData->dTradeDalant[j];
        e_EconomySystem.m_dCurTradeGold[j] = pCurData->dTradeGold[j];
        *(_QWORD *)&e_EconomySystem.m_dBufTradeGold[j] = 0i64;
        *(_QWORD *)&e_EconomySystem.m_dBufTradeDalant[j] = 0i64;
        for ( ii = 0; ii < 3; ++ii )
        {
          e_EconomySystem.m_dCurOreMineCount[j][ii] = pCurData->dOreMineCount[j][ii];
          e_EconomySystem.m_dCurOreCutCount[j][ii] = pCurData->dOreCutCount[j][ii];
          *(_QWORD *)&e_EconomySystem.m_dBufOreMineCount[j][ii] = 0i64;
          *(_QWORD *)&e_EconomySystem.m_dBufOreCutCount[j][ii] = 0i64;
        }
      }
      e_EconomySystem.m_bLoad = 1;
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    MyMessageBox(
      "Economy Error",
      "number of history(%d) is more than MAX_ECONOMY_HISTORY (%d)",
      (unsigned int)nHisNum,
      12i64);
    result = 0;
  }
  return result;
}
