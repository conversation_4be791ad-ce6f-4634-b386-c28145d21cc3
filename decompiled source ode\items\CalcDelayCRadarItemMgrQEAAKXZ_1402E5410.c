/*
 * Function: ?CalcDelay@CRadarItemMgr@@QEAAKXZ
 * Address: 0x1402E5410
 */

__int64 __fastcall CRadarItemMgr::CalcDelay(CRadarItemMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  DWORD v5; // [sp+20h] [bp-18h]@4
  DWORD v6; // [sp+24h] [bp-14h]@4
  CRadarItemMgr *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = timeGetTime();
  v6 = v7->m_dwDelayTime + v7->m_dwStartTime;
  if ( v6 )
  {
    if ( v5 < v6 )
    {
      v7->m_dwDelayTime -= v5 - v7->m_dwStartTime;
      v7->m_dwStartTime = v5;
    }
    else
    {
      CRadarItemMgr::Init(v7);
    }
    result = v7->m_dwDelayTime;
  }
  else
  {
    result = v7->m_dwDelayTime;
  }
  return result;
}
