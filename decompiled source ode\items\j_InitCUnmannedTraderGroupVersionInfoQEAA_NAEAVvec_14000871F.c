/*
 * Function: j_?Init@CUnmannedTraderGroupVersionInfo@@QEAA_NAEAV?$vector@U?$pair@KK@std@@V?$allocator@U?$pair@KK@std@@@2@@std@@@Z
 * Address: 0x14000871F
 */

bool __fastcall CUnmannedTraderGroupVersionInfo::Init(CUnmannedTraderGroupVersionInfo *this, std::vector<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *vecInfo)
{
  return CUnmannedTraderGroupVersionInfo::Init(this, vecInfo);
}
