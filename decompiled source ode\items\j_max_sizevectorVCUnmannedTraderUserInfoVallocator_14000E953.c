/*
 * Function: j_?max_size@?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@QEBA_KXZ
 * Address: 0x14000E953
 */

unsigned __int64 __fastcall std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::max_size(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this)
{
  return std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::max_size(this);
}
