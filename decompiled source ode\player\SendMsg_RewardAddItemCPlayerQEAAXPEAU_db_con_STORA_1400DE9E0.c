/*
 * Function: ?SendMsg_RewardAddItem@CPlayer@@QEAAXPEAU_db_con@_STORAGE_LIST@@E@Z
 * Address: 0x1400DE9E0
 */

void __fastcall CPlayer::SendMsg_RewardAddItem(CPlayer *this, _STORAGE_LIST::_db_con *pItem, char byReason)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  unsigned __int16 v7; // [sp+39h] [bp-4Fh]@4
  unsigned __int64 v8; // [sp+3Bh] [bp-4Dh]@4
  unsigned int v9; // [sp+43h] [bp-45h]@4
  unsigned __int16 v10; // [sp+47h] [bp-41h]@4
  char v11; // [sp+49h] [bp-3Fh]@4
  char v12; // [sp+4Ah] [bp-3Eh]@4
  unsigned int v13; // [sp+4Bh] [bp-3Dh]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v15; // [sp+65h] [bp-23h]@4
  CPlayer *v16; // [sp+90h] [bp+8h]@1

  v16 = this;
  v3 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szMsg = pItem->m_byTableCode;
  v7 = pItem->m_wItemIndex;
  v8 = pItem->m_dwDur;
  v9 = pItem->m_dwLv;
  v10 = pItem->m_wSerial;
  v11 = byReason;
  v12 = pItem->m_byCsMethod;
  v13 = pItem->m_dwT;
  pbyType = 11;
  v15 = 9;
  CNetProcess::LoadSendMsg(unk_1414F2088, v16->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x17u);
}
