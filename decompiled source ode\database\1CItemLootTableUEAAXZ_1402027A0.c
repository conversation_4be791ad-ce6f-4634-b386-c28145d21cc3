/*
 * Function: ??1CItemLootTable@@UEAA@XZ
 * Address: 0x1402027A0
 */

void __fastcall CItemLootTable::~CItemLootTable(CItemLootTable *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@13
  __int64 v4; // [sp+0h] [bp-68h]@1
  int j; // [sp+20h] [bp-48h]@5
  void *v6; // [sp+28h] [bp-40h]@8
  void *v7; // [sp+30h] [bp-38h]@10
  CEventLootTable *v8; // [sp+38h] [bp-30h]@12
  CEventLootTable *v9; // [sp+40h] [bp-28h]@12
  __int64 v10; // [sp+48h] [bp-20h]@4
  __int64 v11; // [sp+50h] [bp-18h]@13
  CItemLootTable *v12; // [sp+70h] [bp+8h]@1

  v12 = this;
  v1 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = -2i64;
  v12->vfptr = (CItemLootTableVtbl *)&CItemLootTable::`vftable';
  if ( v12->m_ppLinkCode )
  {
    for ( j = 0; j < v12->m_nLootNum; ++j )
    {
      if ( v12->m_ppLinkCode[j] )
      {
        v6 = v12->m_ppLinkCode[j];
        operator delete[](v6);
        v12->m_ppLinkCode[j] = 0i64;
      }
    }
    v7 = v12->m_ppLinkCode;
    operator delete[](v7);
    v12->m_ppLinkCode = 0i64;
  }
  if ( v12->m_pTblEvent )
  {
    v9 = v12->m_pTblEvent;
    v8 = v9;
    if ( v9 )
    {
      LODWORD(v3) = ((int (__fastcall *)(CEventLootTable *, signed __int64))v8->vfptr->__vecDelDtor)(v8, 1i64);
      v11 = v3;
    }
    else
    {
      v11 = 0i64;
    }
  }
  CRecordData::~CRecordData(&v12->m_tblLoot);
}
