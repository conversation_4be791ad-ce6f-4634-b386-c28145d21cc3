/*
 * Function: ?IsAvailableSuggest@CGuildBattleController@@QEAAEPEAVCGuild@@KKKK@Z
 * Address: 0x1403D5BD0
 */

char __fastcall CGuildBattleController::IsAvailableSuggest(CGuildBattleController *this, CGuild *pSrcGuild, unsigned int dwDestGuild, unsigned int dwStartTime, unsigned int dwNumber, unsigned int dwMapCode)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  GUILD_BATTLE::CNormalGuildBattleFieldList *v9; // rax@12
  GUILD_BATTLE::CGuildBattleScheduleManager *v10; // rax@14
  __int64 v11; // [sp+0h] [bp-58h]@1
  char v12; // [sp+20h] [bp-38h]@8
  CGuild *v13; // [sp+28h] [bp-30h]@10
  unsigned int dwMapInx; // [sp+34h] [bp-24h]@12
  CGuild *v15; // [sp+68h] [bp+10h]@1
  unsigned int dwSerial; // [sp+70h] [bp+18h]@1
  unsigned int dwStartTimeInx; // [sp+78h] [bp+20h]@1
  int dwStartTimeInxa; // [sp+78h] [bp+20h]@12

  dwStartTimeInx = dwStartTime;
  dwSerial = dwDestGuild;
  v15 = pSrcGuild;
  v6 = &v11;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( pSrcGuild->m_dwSerial == dwDestGuild )
  {
    result = 118;
  }
  else if ( 10 * (dwNumber + 1) <= 0x32 )
  {
    v12 = CGuild::SrcGuildIsAvailableBattleRequestState(pSrcGuild);
    if ( v12 )
    {
      result = v12;
    }
    else
    {
      v13 = GetGuildDataFromSerial(g_Guild, 500, dwSerial);
      if ( v13 )
      {
        dwStartTimeInxa = dwStartTimeInx + 1;
        dwMapInx = 0;
        v9 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
        if ( GUILD_BATTLE::CNormalGuildBattleFieldList::GetMapInx(v9, v15->m_byRace, dwMapCode, &dwMapInx) )
        {
          v10 = GUILD_BATTLE::CGuildBattleScheduleManager::Instance();
          v12 = GUILD_BATTLE::CGuildBattleScheduleManager::IsEmptyTime(v10, dwMapInx, dwStartTimeInxa);
          if ( v12 )
          {
            result = v12;
          }
          else if ( v15->m_byGrade >= GUILD_BATTLE::LIMIT_SRC_GRADE )
          {
            if ( v13->m_byGrade >= GUILD_BATTLE::LIMIT_DEST_GRADE )
            {
              if ( abs_0(v15->m_byGrade - v13->m_byGrade) <= 4 )
              {
                if ( v15->m_dTotalGold >= 5000.0 )
                {
                  if ( v15->m_byRace == v13->m_byRace )
                    result = CGuild::DestGuildIsAvailableBattleRequestState(v13);
                  else
                    result = 117;
                }
                else
                {
                  result = 116;
                }
              }
              else
              {
                result = 115;
              }
            }
            else
            {
              result = 114;
            }
          }
          else
          {
            result = 113;
          }
        }
        else
        {
          result = 120;
        }
      }
      else
      {
        result = 111;
      }
    }
  }
  else
  {
    result = 126;
  }
  return result;
}
