/*
 * Function: ?OnLoop@CMapOperation@@QEAAXXZ
 * Address: 0x140196F30
 */

void __fastcall CMapOperation::OnLoop(CMapOperation *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@5
  CPlayer *v5; // [sp+28h] [bp-20h]@8
  int k; // [sp+30h] [bp-18h]@18
  int l; // [sp+34h] [bp-14h]@27
  CMapOperation *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CMyTimer::CountingTimer(&v8->m_tmrRecover) )
  {
    for ( j = 0; j < 2532; ++j )
    {
      v5 = &g_Player + j;
      if ( v5->m_bLive )
      {
        if ( !v5->m_bCorpse )
        {
          CPlayer::AutoRecover(v5);
          if ( CPlayerDB::GetRaceCode(&v5->m_Param) == 1 )
            CPlayer::AutoRecover_Animus(v5);
        }
        if ( !CPlayerDB::GetRaceCode(&v5->m_Param) )
          CPlayer::AutoCharge_Booster(v5);
      }
    }
  }
  if ( CMyTimer::CountingTimer(&v8->m_tmrSystem) )
  {
    if ( v8->m_bReSpawnMonster )
      CMapOperation::RespawnMonster(v8);
    for ( k = 0; k < v8->m_nMapNum; ++k )
    {
      if ( v8->m_Map[k].m_bUse )
        CMapData::OnLoop(&v8->m_Map[k]);
    }
    CMonsterEventRespawn::CheckRespawnEvent(&g_MonsterEventRespawn);
    CMonsterEventSet::CheckEventSetRespawn(g_MonsterEventSet);
  }
  if ( CMyTimer::CountingTimer(&v8->m_tmrObjTerm) )
  {
    if ( !v8->m_nLoopStartPoint )
      R3CalculateTime();
    for ( l = v8->m_nLoopStartPoint; l < CGameObject::s_nTotalObjectNum; l += 10 )
    {
      if ( *(_BYTE *)(CGameObject::s_pTotalObject[l] + 24i64) )
        CGameObject::OnLoop((CGameObject *)CGameObject::s_pTotalObject[l]);
    }
    ++v8->m_nLoopStartPoint;
    v8->m_nLoopStartPoint %= 10;
  }
}
