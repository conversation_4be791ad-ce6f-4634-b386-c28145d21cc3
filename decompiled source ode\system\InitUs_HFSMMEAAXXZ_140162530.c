/*
 * Function: ?Init@Us_HFSM@@MEAAXXZ
 * Address: 0x140162530
 */

void __fastcall Us_HFSM::Init(Us_HFSM *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@4
  Us_HFSM *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 0xA; ++j )
    Us_FSM_Node::Init(&v5->m_ArNode[j]);
  UsPoint<UsStateTBL>::operator=(&v5->m_spShareStateTBLPtr, 0i64);
  v5->m_dwUsedCount = 0;
  v5->m_bSet = 0;
  v5->m_pObject = 0i64;
}
