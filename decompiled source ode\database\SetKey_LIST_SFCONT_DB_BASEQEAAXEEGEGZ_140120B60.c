/*
 * Function: ?Set<PERSON><PERSON>@_LIST@_SFCONT_DB_BASE@@QEAAXEEGEG@Z
 * Address: 0x140120B60
 */

void __fastcall _SFCONT_DB_BASE::_LIST::SetKey(_SFCONT_DB_BASE::_LIST *this, char pl_byOrder, char pl_byEffectCode, unsigned __int16 pl_wEffectIndex, char pl_byLv, unsigned __int16 pl_wLeftTime)
{
  this->dwKey = 0;
  this->dwKey |= (unsigned __int8)pl_byOrder << 28;
  this->dwKey |= (unsigned __int8)pl_byEffectCode << 26;
  this->dwKey |= pl_wEffectIndex << 16;
  this->dwKey |= (unsigned __int8)pl_byLv << 12;
  this->dwKey |= pl_wLeftTime;
}
