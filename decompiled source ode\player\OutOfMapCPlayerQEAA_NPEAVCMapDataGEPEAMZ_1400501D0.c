/*
 * Function: ?OutOfMap@CPlayer@@QEAA_NPEAVCMapData@@GEPEAM@Z
 * Address: 0x1400501D0
 */

char __fastcall CPlayer::OutOfMap(CPlayer *this, CMapData *pIntoMap, unsigned __int16 wLayerIndex, char byMapOutType, float *pfStartPos)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // eax@12
  unsigned int v8; // eax@13
  __int64 v10; // [sp+0h] [bp-38h]@1
  unsigned int dwMapEnvCode; // [sp+20h] [bp-18h]@13
  CPlayer *pExiter; // [sp+40h] [bp+8h]@1
  CMapData *v13; // [sp+48h] [bp+10h]@1
  unsigned __int16 v14; // [sp+50h] [bp+18h]@1
  char v15; // [sp+58h] [bp+20h]@1

  v15 = byMapOutType;
  v14 = wLayerIndex;
  v13 = pIntoMap;
  pExiter = this;
  v5 = &v10;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( !pExiter->m_bInGuildBattle
    && pExiter->m_byUserDgr >= 2
    && !pExiter->m_bObserver
    && pIntoMap != pExiter->m_pCurMap
    && pIntoMap->m_pMapSet->m_nRaceVillageCode < 3
    && CPlayerDB::GetRaceCode(&pExiter->m_Param) != pIntoMap->m_pMapSet->m_nRaceVillageCode )
  {
    CPlayer::mgr_tracing(pExiter, 1);
  }
  if ( pExiter->m_pCurMap == (CMapData *)*((_QWORD *)&g_TransportShip
                                         + 10162 * CPlayerDB::GetRaceCode(&pExiter->m_Param)
                                         + 2) )
  {
    v7 = CPlayerDB::GetRaceCode(&pExiter->m_Param);
    CTransportShip::ExitMember((CTransportShip *)&g_TransportShip + v7, pExiter, 0);
  }
  pExiter->m_bMapLoading = 1;
  CPlayer::_AnimusReturn(pExiter, 3);
  CPlayer::DTradeInit(pExiter);
  pExiter->m_byMapInModeBuffer = v15;
  dwMapEnvCode = CLevel::GetEnvironment(&v13->m_Level);
  CPlayer::SendMsg_MapEnvInform(pExiter, v13->m_pMapSet->m_dwIndex, dwMapEnvCode);
  CPlayer::SendMsg_MapOut(pExiter, v15, v13->m_pMapSet->m_dwIndex);
  v8 = CGameObject::GetCurSecNum((CGameObject *)&pExiter->vfptr);
  CMapData::ExitMap(pExiter->m_pCurMap, (CGameObject *)&pExiter->vfptr, v8);
  CGameObject::SetCurSecNum((CGameObject *)&pExiter->vfptr, 0xFFFFFFFF);
  if ( v13->m_pMapSet->m_nMapType )
  {
    if ( pExiter->m_pUserDB )
      CUserDB::Update_Map(pExiter->m_pUserDB, pExiter->m_pCurMap->m_pMapSet->m_dwIndex, pExiter->m_fCurPos);
  }
  else
  {
    if ( pExiter->m_pUserDB )
      CUserDB::Update_Map(pExiter->m_pUserDB, v13->m_pMapSet->m_dwIndex, pfStartPos);
    memcpy_0(pExiter->m_fCurPos, pfStartPos, 0xCui64);
  }
  if ( (CPlayer *)CGameObject::s_pSelectObject == pExiter )
    CGameObject::s_pSelectObject = 0i64;
  if ( _effect_parameter::GetEff_State(&pExiter->m_EP, 14) )
    CCharacter::RemoveSFContHelpByEffect((CCharacter *)&pExiter->vfptr, 2, 14);
  if ( pExiter->m_bMineMode )
  {
    pExiter->m_bMineMode = 0;
    pExiter->m_dwMineNextTime = -1;
    CPlayer::SendMsg_MineCancle(pExiter);
  }
  pExiter->m_pCurMap = v13;
  pExiter->m_wMapLayerIndex = v14;
  CPlayerDB::SetMapCode(&pExiter->m_Param, v13->m_pMapSet->m_dwIndex);
  CGameObject::SetCurPos((CGameObject *)&pExiter->vfptr, pfStartPos);
  memcpy_0(pExiter->m_fTarPos, pExiter->m_fCurPos, 0xCui64);
  pExiter->m_bMove = 0;
  memcpy_0(pExiter->m_fLastRecvPos, pExiter->m_fCurPos, 0xCui64);
  pExiter->m_byLastRecvMapIndex = pExiter->m_pCurMap->m_pMapSet->m_dwIndex;
  return 1;
}
