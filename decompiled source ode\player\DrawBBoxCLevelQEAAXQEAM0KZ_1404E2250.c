/*
 * Function: ?DrawBBox@CLevel@@QEAAXQEAM0K@Z
 * Address: 0x1404E2250
 */

void __usercall CLevel::DrawBBox(CLevel *this@<rcx>, float *const a2@<rdx>, float *const a3@<r8>, signed __int64 a4@<rax>, int a5@<r9d>)
{
  void *v5; // rsp@1
  int v6; // esi@1
  float *v7; // rdi@1
  float *v8; // rbx@1
  struct IDirect3DDevice8 *v9; // rax@1
  struct IDirect3DDevice8 *v10; // rax@1
  int v11; // xmm5_4@1
  int v12; // xmm3_4@1
  int v13; // xmm1_4@1
  int v14; // xmm4_4@1
  int v15; // xmm2_4@1
  int v16; // xmm0_4@1
  float v17; // [sp+0h] [bp-BBC8h]@1
  int v18; // [sp+4h] [bp-BBC4h]@1
  int v19; // [sp+8h] [bp-BBC0h]@1
  int v20; // [sp+Ch] [bp-BBBCh]@1
  int v21; // [sp+10h] [bp-BBB8h]@1
  float v22; // [sp+14h] [bp-BBB4h]@1
  int v23; // [sp+18h] [bp-BBB0h]@1
  int v24; // [sp+1Ch] [bp-BBACh]@1
  int v25; // [sp+20h] [bp-BBA8h]@1
  int v26; // [sp+24h] [bp-BBA4h]@1
  float v27; // [sp+28h] [bp-BBA0h]@1
  int v28; // [sp+2Ch] [bp-BB9Ch]@1
  int v29; // [sp+30h] [bp-BB98h]@1
  int v30; // [sp+34h] [bp-BB94h]@1
  int v31; // [sp+38h] [bp-BB90h]@1
  float v32; // [sp+3Ch] [bp-BB8Ch]@1
  int v33; // [sp+40h] [bp-BB88h]@1
  int v34; // [sp+44h] [bp-BB84h]@1
  int v35; // [sp+48h] [bp-BB80h]@1
  unsigned int v36; // [sp+4Ch] [bp-BB7Ch]@1
  int v37; // [sp+50h] [bp-BB78h]@1
  int v38; // [sp+54h] [bp-BB74h]@1
  int v39; // [sp+58h] [bp-BB70h]@1
  unsigned int v40; // [sp+5Ch] [bp-BB6Ch]@1
  int v41; // [sp+60h] [bp-BB68h]@1
  int v42; // [sp+64h] [bp-BB64h]@1
  int v43; // [sp+68h] [bp-BB60h]@1
  unsigned int v44; // [sp+6Ch] [bp-BB5Ch]@1
  int v45; // [sp+70h] [bp-BB58h]@1
  int v46; // [sp+74h] [bp-BB54h]@1
  int v47; // [sp+78h] [bp-BB50h]@1
  unsigned int v48; // [sp+7Ch] [bp-BB4Ch]@1
  int v49; // [sp+80h] [bp-BB48h]@1
  int v50; // [sp+84h] [bp-BB44h]@1
  int v51; // [sp+88h] [bp-BB40h]@1
  unsigned int v52; // [sp+8Ch] [bp-BB3Ch]@1
  int v53; // [sp+90h] [bp-BB38h]@1
  int v54; // [sp+94h] [bp-BB34h]@1
  int v55; // [sp+98h] [bp-BB30h]@1
  unsigned int v56; // [sp+9Ch] [bp-BB2Ch]@1
  int v57; // [sp+A0h] [bp-BB28h]@1
  int v58; // [sp+A4h] [bp-BB24h]@1
  int v59; // [sp+A8h] [bp-BB20h]@1
  unsigned int v60; // [sp+ACh] [bp-BB1Ch]@1
  int v61; // [sp+B0h] [bp-BB18h]@1
  int v62; // [sp+B4h] [bp-BB14h]@1
  int v63; // [sp+B8h] [bp-BB10h]@1
  unsigned int v64; // [sp+BCh] [bp-BB0Ch]@1
  int v65; // [sp+C0h] [bp-BB08h]@1
  int v66; // [sp+C4h] [bp-BB04h]@1
  int v67; // [sp+C8h] [bp-BB00h]@1
  unsigned int v68; // [sp+CCh] [bp-BAFCh]@1
  int v69; // [sp+D0h] [bp-BAF8h]@1
  int v70; // [sp+D4h] [bp-BAF4h]@1
  int v71; // [sp+D8h] [bp-BAF0h]@1
  unsigned int v72; // [sp+DCh] [bp-BAECh]@1
  int v73; // [sp+E0h] [bp-BAE8h]@1
  int v74; // [sp+E4h] [bp-BAE4h]@1
  int v75; // [sp+E8h] [bp-BAE0h]@1
  unsigned int v76; // [sp+ECh] [bp-BADCh]@1
  int v77; // [sp+F0h] [bp-BAD8h]@1
  int v78; // [sp+F4h] [bp-BAD4h]@1
  int v79; // [sp+F8h] [bp-BAD0h]@1
  unsigned int v80; // [sp+FCh] [bp-BACCh]@1
  int v81; // [sp+100h] [bp-BAC8h]@1
  int v82; // [sp+104h] [bp-BAC4h]@1
  int v83; // [sp+108h] [bp-BAC0h]@1
  unsigned int v84; // [sp+10Ch] [bp-BABCh]@1
  int v85; // [sp+110h] [bp-BAB8h]@1
  int v86; // [sp+114h] [bp-BAB4h]@1
  int v87; // [sp+118h] [bp-BAB0h]@1
  unsigned int v88; // [sp+11Ch] [bp-BAACh]@1
  int v89; // [sp+120h] [bp-BAA8h]@1
  int v90; // [sp+124h] [bp-BAA4h]@1
  int v91; // [sp+128h] [bp-BAA0h]@1
  unsigned int v92; // [sp+12Ch] [bp-BA9Ch]@1
  int v93; // [sp+130h] [bp-BA98h]@1
  int v94; // [sp+134h] [bp-BA94h]@1
  int v95; // [sp+138h] [bp-BA90h]@1
  unsigned int v96; // [sp+13Ch] [bp-BA8Ch]@1
  int v97; // [sp+140h] [bp-BA88h]@1
  int v98; // [sp+144h] [bp-BA84h]@1
  int v99; // [sp+148h] [bp-BA80h]@1
  unsigned int v100; // [sp+14Ch] [bp-BA7Ch]@1
  int v101; // [sp+150h] [bp-BA78h]@1
  int v102; // [sp+154h] [bp-BA74h]@1
  int v103; // [sp+158h] [bp-BA70h]@1
  unsigned int v104; // [sp+15Ch] [bp-BA6Ch]@1
  int v105; // [sp+160h] [bp-BA68h]@1
  int v106; // [sp+164h] [bp-BA64h]@1
  int v107; // [sp+168h] [bp-BA60h]@1
  unsigned int v108; // [sp+16Ch] [bp-BA5Ch]@1
  int v109; // [sp+170h] [bp-BA58h]@1
  int v110; // [sp+174h] [bp-BA54h]@1
  int v111; // [sp+178h] [bp-BA50h]@1
  unsigned int v112; // [sp+17Ch] [bp-BA4Ch]@1
  int v113; // [sp+180h] [bp-BA48h]@1
  int v114; // [sp+184h] [bp-BA44h]@1
  int v115; // [sp+188h] [bp-BA40h]@1
  unsigned int v116; // [sp+18Ch] [bp-BA3Ch]@1
  int v117; // [sp+190h] [bp-BA38h]@1
  int v118; // [sp+194h] [bp-BA34h]@1
  int v119; // [sp+198h] [bp-BA30h]@1
  unsigned int v120; // [sp+19Ch] [bp-BA2Ch]@1
  int v121; // [sp+1A0h] [bp-BA28h]@1
  int v122; // [sp+1A4h] [bp-BA24h]@1
  int v123; // [sp+1A8h] [bp-BA20h]@1
  unsigned int v124; // [sp+1ACh] [bp-BA1Ch]@1
  int v125; // [sp+1B0h] [bp-BA18h]@1
  int v126; // [sp+1B4h] [bp-BA14h]@1
  int v127; // [sp+1B8h] [bp-BA10h]@1
  unsigned int v128; // [sp+1BCh] [bp-BA0Ch]@1

  v5 = alloca(a4);
  v6 = a5;
  v7 = a3;
  v31 = 0;
  v30 = 0;
  v29 = 0;
  v8 = a2;
  v28 = 0;
  v26 = 0;
  v25 = 0;
  v24 = 0;
  v23 = 0;
  v21 = 0;
  v20 = 0;
  v19 = 0;
  v18 = 0;
  v32 = FLOAT_1_0;
  v27 = FLOAT_1_0;
  v22 = FLOAT_1_0;
  v17 = FLOAT_1_0;
  v9 = GetD3dDevice();
  ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, int *))v9->vfptr[12].AddRef)(v9, 256i64, (int *)&v17);
  v10 = GetD3dDevice();
  ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, _QWORD))v10->vfptr[20].AddRef)(v10, 0i64, 0i64);
  v36 = v6 | 0xFF000000;
  v40 = v6 | 0xFF000000;
  v44 = v6 | 0xFF000000;
  v11 = *((_DWORD *)v8 + 1);
  v12 = *(_DWORD *)v8;
  v13 = *((_DWORD *)v8 + 2);
  v14 = *(_DWORD *)v7;
  v15 = *((_DWORD *)v7 + 2);
  v16 = *((_DWORD *)v7 + 1);
  v48 = v6 | 0xFF000000;
  v52 = v6 | 0xFF000000;
  v56 = v6 | 0xFF000000;
  v33 = v12;
  v34 = v11;
  v35 = v13;
  v60 = v6 | 0xFF000000;
  v64 = v6 | 0xFF000000;
  v68 = v6 | 0xFF000000;
  v37 = v14;
  v38 = v11;
  v39 = v13;
  v72 = v6 | 0xFF000000;
  v41 = v12;
  v42 = v11;
  v43 = v13;
  v45 = v12;
  v46 = v11;
  v47 = v15;
  v49 = v14;
  v50 = v11;
  v51 = v13;
  v53 = v14;
  v54 = v11;
  v55 = v15;
  v57 = v12;
  v58 = v11;
  v59 = v15;
  v61 = v14;
  v62 = v11;
  v63 = v15;
  v65 = v12;
  v66 = v16;
  v67 = v13;
  v69 = v14;
  v70 = v16;
  v71 = v13;
  v73 = v12;
  v74 = v16;
  v75 = v13;
  v77 = v12;
  v78 = v16;
  v76 = v6 | 0xFF000000;
  v80 = v6 | 0xFF000000;
  v84 = v6 | 0xFF000000;
  v79 = v15;
  v81 = v14;
  v82 = v16;
  v88 = v6 | 0xFF000000;
  v92 = v6 | 0xFF000000;
  v96 = v6 | 0xFF000000;
  v83 = v13;
  v85 = v14;
  v86 = v16;
  v100 = v6 | 0xFF000000;
  v104 = v6 | 0xFF000000;
  v108 = v6 | 0xFF000000;
  v87 = v15;
  v89 = v12;
  v90 = v16;
  v112 = v6 | 0xFF000000;
  v116 = v6 | 0xFF000000;
  v120 = v6 | 0xFF000000;
  v91 = v15;
  v93 = v14;
  v94 = v16;
  v95 = v15;
  v97 = v12;
  v98 = v11;
  v124 = v6 | 0xFF000000;
  v128 = v6 | 0xFF000000;
  v99 = v13;
  v101 = v12;
  v102 = v16;
  v103 = v13;
  v105 = v14;
  v106 = v11;
  v107 = v13;
  v109 = v14;
  v110 = v16;
  v111 = v13;
  v113 = v12;
  v114 = v11;
  v115 = v15;
  v117 = v12;
  v118 = v16;
  v119 = v15;
  v121 = v14;
  v122 = v11;
  v123 = v15;
  v125 = v14;
  v126 = v16;
  v127 = v15;
  DrawPrimitiveUPLine(12, &v33);
}
