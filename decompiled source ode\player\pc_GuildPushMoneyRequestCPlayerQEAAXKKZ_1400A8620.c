/*
 * Function: ?pc_GuildPushMoneyRequest@CPlayer@@QEAAXKK@Z
 * Address: 0x1400A8620
 */

void __fastcall CPlayer::pc_GuildPushMoneyRequest(CPlayer *this, unsigned int dwPushDalant, unsigned int dwPushGold)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@20
  int v6; // eax@20
  unsigned int v7; // eax@24
  __int64 v8; // [sp+0h] [bp-F8h]@1
  char v9; // [sp+40h] [bp-B8h]@4
  CGuild *v10; // [sp+48h] [bp-B0h]@4
  double v11; // [sp+50h] [bp-A8h]@4
  double v12; // [sp+58h] [bp-A0h]@4
  char v13; // [sp+60h] [bp-98h]@20
  _qry_case_inputgmoney v14; // [sp+80h] [bp-78h]@20
  char *v15; // [sp+D0h] [bp-28h]@24
  unsigned int v16; // [sp+D8h] [bp-20h]@24
  unsigned __int64 v17; // [sp+E0h] [bp-18h]@4
  CPlayer *v18; // [sp+100h] [bp+8h]@1
  signed int dwSub; // [sp+108h] [bp+10h]@1
  signed int dwPushGolda; // [sp+110h] [bp+18h]@1

  dwPushGolda = dwPushGold;
  dwSub = dwPushDalant;
  v18 = this;
  v3 = &v8;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v17 = (unsigned __int64)&v8 ^ _security_cookie;
  v9 = 0;
  v10 = v18->m_Param.m_pGuild;
  v11 = 0.0;
  v12 = 0.0;
  if ( v10 )
  {
    if ( CGuild::GetMemberFromSerial(v10, v18->m_dwObjSerial) )
    {
      if ( v10->m_bIOWait )
      {
        v9 = 103;
      }
      else if ( (unsigned int)dwSub <= 0xB2D05E00 && (unsigned int)dwPushGolda <= 0xB2D05E00 )
      {
        if ( CPlayerDB::GetDalant(&v18->m_Param) >= dwSub && CPlayerDB::GetGold(&v18->m_Param) >= dwPushGolda )
        {
          CGuild::GetTotalDalant(v10);
          v11 = (double)dwSub + 0.0;
          CGuild::GetTotalGold(v10);
          v12 = (double)dwSub + 0.0 + (double)dwPushGolda;
          if ( v11 > 1000000000.0 || v12 > 500000.0 )
            v9 = 104;
        }
        else
        {
          v9 = 102;
        }
      }
      else
      {
        v9 = 101;
      }
    }
    else
    {
      v9 = -54;
    }
  }
  else
  {
    v9 = -54;
  }
  if ( !v9 )
  {
    v13 = 1;
    v14.in_pusherserial = v18->m_id.dwSerial;
    v14.tmp_guildindex = v10->m_nIndex;
    v14.in_guildserial = v10->m_dwSerial;
    v14.dwAddGold = dwPushGolda;
    v14.dwAddDalant = dwSub;
    *(_QWORD *)&v14.out_totalgold = 0i64;
    *(_QWORD *)&v14.out_totaldalant = 0i64;
    v14.in_date[0] = GetCurrentMonth();
    v14.in_date[1] = GetCurrentDay();
    v14.in_date[2] = GetCurrentHour();
    v14.in_date[3] = GetCurrentMin();
    v5 = CPlayerDB::GetCharNameW(&v18->m_Param);
    strcpy_0(v14.in_w_pushername, v5);
    v6 = _qry_case_inputgmoney::size(&v14);
    if ( !CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 19, (char *)&v14, v6) )
      v13 = 0;
    if ( v13 )
    {
      v10->m_bIOWait = 1;
      CPlayer::SubDalant(v18, dwSub);
      CPlayer::SubGold(v18, dwPushGolda);
      v15 = v18->m_szItemHistoryFileName;
      v16 = CPlayerDB::GetGold(&v18->m_Param);
      v7 = CPlayerDB::GetDalant(&v18->m_Param);
      CMgrAvatorItemHistory::guild_push_money(
        &CPlayer::s_MgrItemHistory,
        v18->m_ObjID.m_wIndex,
        v10->m_aszName,
        dwSub,
        dwPushGolda,
        v7,
        v16,
        v15);
      CPlayer::SendMsg_AlterMoneyInform(v18, 0);
    }
    else
    {
      v9 = -1;
    }
  }
  CPlayer::SendMsg_GuildPushMoneyResult(v18, v9);
}
