/*
 * Function: ??_G?$CipherModeFinalTemplate_ExternalCipher@VCBC_CTS_Encryption@CryptoPP@@@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x14055D1E0
 */

void *__fastcall CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Encryption>::`scalar deleting destructor'(void *a1, int a2)
{
  void *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Encryption>::~CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Encryption>();
  if ( v4 & 1 )
    operator delete(v3);
  return v3;
}
