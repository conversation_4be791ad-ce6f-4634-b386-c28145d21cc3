/*
 * Function: ??1CNormalGuildBattle@GUILD_BATTLE@@QEAA@XZ
 * Address: 0x1403E2FB0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::~CNormalGuildBattle(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattle *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  v5->vfptr = (GUILD_BATTLE::CGuildBattleVtbl *)&GUILD_BATTLE::CNormalGuildBattle::`vftable';
  GUILD_BATTLE::CNormalGuildBattleGuild::~CNormalGuildBattleGuild(&v5->m_k2P);
  GUILD_BATTLE::CNormalGuildBattleGuild::~CNormalGuildBattleGuild(&v5->m_k1P);
  GUILD_BATTLE::CNormalGuildBattleLogger::~CNormalGuildBattleLogger(&v5->m_kLogger);
  GUILD_BATTLE::CGuildBattle::~CGuildBattle((GUILD_BATTLE::CGuildBattle *)&v5->vfptr);
}
