/*
 * Function: ??$unchecked_uninitialized_copy@V?$_Vector_const_iterator@VInteger@CryptoPP@@V?$allocator@VInteger@CryptoPP@@@std@@@std@@PEAVInteger@CryptoPP@@V?$allocator@VInteger@CryptoPP@@@2@@stdext@@YAPEAVInteger@CryptoPP@@V?$_Vector_const_iterator@VInteger@CryptoPP@@V?$allocator@VInteger@CryptoPP@@@std@@@std@@0PEAV12@AEAV?$allocator@VInteger@CryptoPP@@@4@@Z
 * Address: 0x14059BDD0
 */

__int64 __fastcall stdext::unchecked_uninitialized_copy<std::_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>,CryptoPP::Integer *,std::allocator<CryptoPP::Integer>>(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  __int64 v4; // rax@1
  __int64 v5; // rax@1
  __int64 v6; // rax@1
  __int64 v7; // ST30_8@1
  char v9; // [sp+38h] [bp-70h]@1
  char v10; // [sp+39h] [bp-6Fh]@1
  char v11; // [sp+40h] [bp-68h]@1
  char *v12; // [sp+58h] [bp-50h]@1
  char v13; // [sp+60h] [bp-48h]@1
  char *v14; // [sp+78h] [bp-30h]@1
  __int64 v15; // [sp+80h] [bp-28h]@1
  __int64 v16; // [sp+88h] [bp-20h]@1
  __int64 v17; // [sp+90h] [bp-18h]@1
  __int64 v18; // [sp+98h] [bp-10h]@1
  __int64 v19; // [sp+B0h] [bp+8h]@1
  __int64 v20; // [sp+B8h] [bp+10h]@1
  __int64 v21; // [sp+C0h] [bp+18h]@1
  __int64 v22; // [sp+C8h] [bp+20h]@1

  v22 = a4;
  v21 = a3;
  v20 = a2;
  v19 = a1;
  v15 = -2i64;
  memset(&v9, 0, sizeof(v9));
  v10 = std::_Ptr_cat<std::_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>,CryptoPP::Integer *>(
          a1,
          &v21);
  v12 = &v11;
  v14 = &v13;
  v4 = std::_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>(
         (__int64)&v11,
         v20);
  v16 = v4;
  v17 = v4;
  v5 = std::_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>(
         (__int64)v14,
         v19);
  v18 = v5;
  LODWORD(v6) = std::_Uninit_copy<std::_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>,CryptoPP::Integer *,std::allocator<CryptoPP::Integer>>(
                  v5,
                  v17,
                  v21,
                  v22);
  v7 = v6;
  std::_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::~_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>();
  std::_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::~_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>();
  return v7;
}
