/*
 * Function: ?CheckDiff@CCheckSumGuildData@@QEAAHPEAVCRFWorldDatabase@@PEADAEAV1@@Z
 * Address: 0x1402C0F70
 */

signed __int64 __fastcall CCheckSumGuildData::CheckDiff(CCheckSumGuildData *this, CRFWorldDatabase *pkDB, char *wszName, CCheckSumGuildData *kSrcValue)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  long double v7; // xmm0_8@9
  __int64 v8; // [sp+0h] [bp-68h]@1
  long double v9; // [sp+20h] [bp-48h]@10
  long double v10; // [sp+28h] [bp-40h]@10
  char v11; // [sp+30h] [bp-38h]@7
  char v12; // [sp+44h] [bp-24h]@10
  int j; // [sp+54h] [bp-14h]@7
  long double v14; // [sp+58h] [bp-10h]@10
  CCheckSumGuildData *v15; // [sp+70h] [bp+8h]@1
  CRFWorldDatabase *v16; // [sp+78h] [bp+10h]@1
  char *wszNamea; // [sp+80h] [bp+18h]@1
  CCheckSumGuildData *v18; // [sp+88h] [bp+20h]@1

  v18 = kSrcValue;
  wszNamea = wszName;
  v16 = pkDB;
  v15 = this;
  v4 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pkDB && wszName )
  {
    v11 = 1;
    for ( j = 0; j < 2; ++j )
    {
      v7 = v15->m_dValues[j];
      if ( v7 != v18->m_dValues[j] )
      {
        v11 = 0;
        CCheckSumBaseConverter::ProcCode((CCheckSumBaseConverter *)&v12, j + 6, v15->m_dwGuildSerial, v18->m_dValues[j]);
        v14 = v7;
        CCheckSumBaseConverter::ProcCode((CCheckSumBaseConverter *)&v12, j + 6, v15->m_dwGuildSerial, v15->m_dValues[j]);
        v10 = v14;
        v9 = v7;
        if ( !CRFWorldDatabase::Insert_UnitLog(v16, v15->m_dwGuildSerial, wszNamea, j, v7, v14) )
          return 0xFFFFFFFFi64;
      }
    }
    result = v11 == 0;
  }
  else
  {
    result = 1i64;
  }
  return result;
}
