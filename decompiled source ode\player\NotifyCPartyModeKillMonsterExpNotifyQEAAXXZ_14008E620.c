/*
 * Function: ?Notify@CPartyModeKillMonsterExpNotify@@QEAAXXZ
 * Address: 0x14008E620
 */

void __fastcall CPartyModeKillMonsterExpNotify::Notify(CPartyModeKillMonsterExpNotify *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned __int8 j; // [sp+20h] [bp-18h]@10
  int v5; // [sp+24h] [bp-14h]@8
  CPartyModeKillMonsterExpNotify *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_bKillMonster && v6->m_byMemberCnt )
  {
    if ( v6->m_byMemberCnt >= 8 )
      v5 = 8;
    else
      v5 = v6->m_byMemberCnt;
    v6->m_byMemberCnt = v5;
    for ( j = 0; j < (signed int)v6->m_byMemberCnt; ++j )
      CPartyModeKillMonsterExpNotify::CExpInfo::Notify(&v6->m_kInfo[j]);
  }
}
