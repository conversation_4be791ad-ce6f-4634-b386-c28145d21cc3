/*
 * Function: ?ProcCode@CCheckSumBaseConverter@@QEAANEKN@Z
 * Address: 0x1402C1440
 */

double __fastcall CCheckSumBaseConverter::ProcCode(CCheckSumBaseConverter *this, char byIndex, unsigned int dwSerial, long double dValue)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp-10h] [bp-28h]@1
  __int64 v8; // [sp+0h] [bp-18h]@4
  char *v9; // [sp+8h] [bp-10h]@4

  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = (unsigned int)(signed int)floor(dValue);
  v9 = (char *)&CCheckSumBaseConverter::ms_i64Keys + 80 * (unsigned __int8)byIndex;
  return (double)(signed int)(*(_DWORD *)&v9[8 * (dwSerial % 0xA)] ^ v8);
}
