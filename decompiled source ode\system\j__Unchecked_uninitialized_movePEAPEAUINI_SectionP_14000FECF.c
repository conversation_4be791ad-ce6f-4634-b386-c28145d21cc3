/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAPEAUINI_Section@@PEAPEAU1@V?$allocator@PEAUINI_Section@@@std@@@stdext@@YAPEAPEAUINI_Section@@PEAPEAU1@00AEAV?$allocator@PEAUINI_Section@@@std@@@Z
 * Address: 0x14000FECF
 */

INI_Section **__fastcall stdext::_Unchecked_uninitialized_move<INI_Section * *,INI_Section * *,std::allocator<INI_Section *>>(INI_Section **_First, INI_Section **_Last, INI_Section **_Dest, std::allocator<INI_Section *> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<INI_Section * *,INI_Section * *,std::allocator<INI_Section *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
