/*
 * Function: ??0?$ptr2user@VCMonster@@@lua_tinker@@QEAA@PEAVCMonster@@@Z
 * Address: 0x14040B880
 */

void __fastcall lua_tinker::ptr2user<CMonster>::ptr2user<CMonster>(lua_tinker::ptr2user<CMonster> *this, CMonster *t)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  lua_tinker::ptr2user<CMonster> *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  lua_tinker::user::user((lua_tinker::user *)&v5->vfptr, t);
  v5->vfptr = (lua_tinker::userVtbl *)&lua_tinker::ptr2user<CMonster>::`vftable';
}
