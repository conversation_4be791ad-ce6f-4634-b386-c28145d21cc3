/*
 * Function: ?GetPtrBaseClass@CPlayerDB@@QEAAPEAU_class_fld@@XZ
 * Address: 0x14007DC50
 */

_class_fld *__fastcall CPlayerDB::GetPtrBaseClass(CPlayerDB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  _class_fld *v4; // [sp+0h] [bp-18h]@1
  CPlayerDB *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = (__int64 *)&v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5->m_pClassHistory[0] )
    v4 = v5->m_pClassHistory[0];
  else
    v4 = v5->m_pClassData;
  return v4;
}
