/*
 * Function: ?_db_Load_OreCutting@CMainThread@@AEAAEKPEAU_CUTTING_DB_BASE@@@Z
 * Address: 0x1401B6DA0
 */

char __fastcall CMainThread::_db_Load_OreCutting(CMainThread *this, unsigned int dwSerial, _CUTTING_DB_BASE *pDbCutting)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-E8h]@1
  _worlddb_ore_cutting pOreCutting; // [sp+30h] [bp-B8h]@4
  char v8; // [sp+D4h] [bp-14h]@4
  int j; // [sp+D8h] [bp-10h]@13
  CMainThread *v10; // [sp+F0h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+F8h] [bp+10h]@1
  _CUTTING_DB_BASE *v12; // [sp+100h] [bp+18h]@1

  v12 = pDbCutting;
  dwSeriala = dwSerial;
  v10 = this;
  v3 = &v6;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = CRFWorldDatabase::Select_OreCutting(v10->m_pWorldDB, dwSerial, &pOreCutting);
  if ( v8 == 1 )
    return 24;
  if ( v8 != 2 )
    goto LABEL_21;
  if ( !CRFWorldDatabase::Insert_OreCutting(v10->m_pWorldDB, dwSeriala) )
    return 24;
  if ( CRFWorldDatabase::Select_OreCutting(v10->m_pWorldDB, dwSeriala, &pOreCutting) )
  {
    result = 24;
  }
  else
  {
LABEL_21:
    if ( v12->m_bOldDataLoad )
    {
      result = 0;
    }
    else
    {
      _CUTTING_DB_BASE::Init(v12);
      for ( j = 0; j < 20; ++j )
      {
        if ( pOreCutting.List[j].nKey != -1 )
        {
          _INVENKEY::LoadDBKey((_INVENKEY *)&v12->m_List[j], pOreCutting.List[j].nKey);
          v12->m_List[j].dwDur = pOreCutting.List[j].dwDur;
          ++v12->m_byLeftNum;
        }
      }
      result = 0;
    }
  }
  return result;
}
