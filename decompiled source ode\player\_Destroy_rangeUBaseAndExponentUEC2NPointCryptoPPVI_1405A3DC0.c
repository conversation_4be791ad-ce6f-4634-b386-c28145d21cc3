/*
 * Function: ??$_Destroy_range@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@YAXPEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@0AEAV?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@0@U_Nonscalar_ptr_iterator_tag@0@@Z
 * Address: 0x1405A3DC0
 */

__int64 __fastcall std::_Destroy_range<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>(__int64 a1, __int64 a2, __int64 a3)
{
  __int64 result; // rax@2
  __int64 i; // [sp+30h] [bp+8h]@1
  __int64 v5; // [sp+38h] [bp+10h]@1
  __int64 v6; // [sp+40h] [bp+18h]@1

  v6 = a3;
  v5 = a2;
  for ( i = a1; ; i += 96i64 )
  {
    result = v5;
    if ( i == v5 )
      break;
    std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>::destroy(v6, i);
  }
  return result;
}
