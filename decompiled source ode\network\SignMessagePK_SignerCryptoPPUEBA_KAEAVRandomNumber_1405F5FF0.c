/*
 * Function: ?SignMessage@PK_Signer@CryptoPP@@UEBA_KAEAVRandomNumberGenerator@2@PEBE_KPEAE@Z
 * Address: 0x1405F5FF0
 */

__int64 __fastcall CryptoPP::PK_Signer::SignMessage(CryptoPP::PK_Signer *this, struct CryptoPP::RandomNumberGenerator *a2, const unsigned __int8 *a3, __int64 a4, unsigned __int8 *a5)
{
  __int64 v5; // rax@1
  __int64 v6; // rax@1
  __int64 v7; // rax@1
  __int64 v8; // rax@1
  char v10; // [sp+30h] [bp-38h]@1
  __int64 v11; // [sp+38h] [bp-30h]@1
  __int64 v12; // [sp+40h] [bp-28h]@1
  __int64 v13; // [sp+48h] [bp-20h]@1
  CryptoPP::PK_SignatureSchemeVtbl *v14; // [sp+50h] [bp-18h]@1
  CryptoPP::PK_Signer *v15; // [sp+70h] [bp+8h]@1
  struct CryptoPP::RandomNumberGenerator *v16; // [sp+78h] [bp+10h]@1
  const unsigned __int8 *v17; // [sp+80h] [bp+18h]@1
  __int64 v18; // [sp+88h] [bp+20h]@1

  v18 = a4;
  v17 = a3;
  v16 = a2;
  v15 = this;
  v12 = -2i64;
  LODWORD(v5) = ((int (*)(void))this->vfptr[1].__vecDelDtor)();
  std::auto_ptr<CryptoPP::PK_MessageAccumulator>::auto_ptr<CryptoPP::PK_MessageAccumulator>(&v10, v5);
  LODWORD(v6) = std::auto_ptr<CryptoPP::PK_MessageAccumulator>::operator->(&v10);
  v13 = v6;
  (*(void (__fastcall **)(__int64, const unsigned __int8 *, __int64))(*(_QWORD *)v6 + 24i64))(v6, v17, v18);
  LODWORD(v7) = std::auto_ptr<CryptoPP::PK_MessageAccumulator>::operator*(&v10);
  v14 = v15->vfptr;
  LODWORD(v8) = ((int (__fastcall *)(CryptoPP::PK_Signer *, struct CryptoPP::RandomNumberGenerator *, __int64, unsigned __int8 *))v14[1].MaxRecoverableLength)(
                  v15,
                  v16,
                  v7,
                  a5);
  v11 = v8;
  std::auto_ptr<CryptoPP::PK_MessageAccumulator>::~auto_ptr<CryptoPP::PK_MessageAccumulator>(&v10);
  return v11;
}
