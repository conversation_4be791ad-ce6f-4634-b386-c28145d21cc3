/*
 * Function: ?SendCurrHonorGuildList@CHonorGuild@@QEAAXGEE@Z
 * Address: 0x14025ED00
 */

void __fastcall CHonorGuild::SendCurrHonorGuildList(CHonorGuild *this, unsigned __int16 wIndex, char byRace, char byUI)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-58h]@1
  unsigned __int16 nLen; // [sp+20h] [bp-38h]@4
  char pbyType; // [sp+34h] [bp-24h]@4
  char v9; // [sp+35h] [bp-23h]@4
  CHonorGuild *v10; // [sp+60h] [bp+8h]@1
  unsigned __int16 v11; // [sp+68h] [bp+10h]@1
  char v12; // [sp+70h] [bp+18h]@1

  v12 = byRace;
  v11 = wIndex;
  v10 = this;
  v4 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  pbyType = 27;
  v9 = 112;
  v10->m_pCurrHonorGuild[(unsigned __int8)byRace]->byUI = byUI;
  nLen = _guild_honor_list_result_zocl::size(v10->m_pCurrHonorGuild[(unsigned __int8)byRace]);
  CNetProcess::LoadSendMsg(unk_1414F2088, v11, &pbyType, &v10->m_pCurrHonorGuild[(unsigned __int8)v12]->byListNum, nLen);
}
