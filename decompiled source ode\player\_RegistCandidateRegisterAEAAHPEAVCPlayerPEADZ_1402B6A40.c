/*
 * Function: ?_<PERSON><PERSON>@CandidateRegister@@AEAAHPEAVCPlayer@@PEAD@Z
 * Address: 0x1402B6A40
 */

signed __int64 __fastcall CandidateRegister::_Regist(CandidateRegister *this, CPlayer *pOne, char *pdata)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  CandidateMgr *v6; // rax@8
  CandidateMgr *v7; // rax@10
  unsigned int v8; // eax@10
  PatriarchElectProcessor *v9; // rax@12
  unsigned __int16 v10; // ax@12
  unsigned int v11; // eax@12
  CMoneySupplyMgr *v12; // rax@16
  __int64 v13; // [sp+0h] [bp-68h]@1
  int v14; // [sp+30h] [bp-38h]@6
  char v15; // [sp+34h] [bp-34h]@8
  int v16; // [sp+38h] [bp-30h]@12
  unsigned int dwAvatorSerial; // [sp+3Ch] [bp-2Ch]@10
  unsigned int n; // [sp+40h] [bp-28h]@12
  unsigned int dwClientIndex; // [sp+44h] [bp-24h]@12
  char *pszFileName; // [sp+48h] [bp-20h]@12
  int nLv; // [sp+50h] [bp-18h]@16
  int v22; // [sp+54h] [bp-14h]@16
  CandidateRegister *v23; // [sp+70h] [bp+8h]@1
  CPlayer *pOnea; // [sp+78h] [bp+10h]@1

  pOnea = pOne;
  v23 = this;
  v3 = &v13;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v23->_bEnable )
  {
    v14 = CandidateRegister::_CheckPlayerInfo(v23, pOne);
    if ( v14 )
    {
      result = (unsigned int)v14;
    }
    else
    {
      v15 = CPlayerDB::GetRaceCode(&pOnea->m_Param);
      v6 = CandidateMgr::Instance();
      if ( CandidateMgr::Regist(v6, pOnea) )
      {
        dwAvatorSerial = CPlayerDB::GetCharSerial(&pOnea->m_Param);
        v7 = CandidateMgr::Instance();
        v8 = CandidateMgr::GetWinCnt(v7, v15, dwAvatorSerial);
        if ( CandidateRegister::_AddToPacket(v23, pOnea, v8) )
        {
          n = pOnea->m_id.wIndex;
          v9 = PatriarchElectProcessor::Instance();
          PatriarchElectProcessor::SendMsg_ResultCode(v9, n, 1);
          CPlayer::SubDalant(pOnea, 0x989680u);
          CPlayer::SendMsg_AlterMoneyInform(pOnea, 0);
          v10 = _pt_result_fcandidacy_list_zocl::size(&v23->_kSend[(unsigned __int8)v15]);
          dwClientIndex = pOnea->m_ObjID.m_wIndex;
          CNetProcess::LoadSendMsg(
            unk_1414F2088,
            dwClientIndex,
            v23->_byPtType,
            &v23->_kSend[(unsigned __int8)v15].byCnt,
            v10);
          pszFileName = pOnea->m_szItemHistoryFileName;
          v11 = CPlayerDB::GetCharSerial(&pOnea->m_Param);
          CMgrAvatorItemHistory::raceboss_candidate(&CPlayer::s_MgrItemHistory, -10000000, v11, pszFileName);
          v16 = CPlayerDB::GetLevel(&pOnea->m_Param);
          if ( v16 == 30 || v16 == 40 || v16 == 50 || v16 == 60 )
          {
            nLv = CPlayerDB::GetLevel(&pOnea->m_Param);
            v22 = CPlayerDB::GetRaceCode(&pOnea->m_Param);
            v12 = CMoneySupplyMgr::Instance();
            CMoneySupplyMgr::UpdateFeeMoneyData(v12, v22, nLv, 0x989680u);
          }
          result = 0i64;
        }
        else
        {
          result = 18i64;
        }
      }
      else
      {
        result = 18i64;
      }
    }
  }
  else
  {
    result = 2i64;
  }
  return result;
}
