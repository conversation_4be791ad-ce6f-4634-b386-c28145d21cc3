/*
 * Function: ?dev_quest_complete_other@CPlayer@@QEAA_NPEAD@Z
 * Address: 0x1400BA4F0
 */

char __fastcall CPlayer::dev_quest_complete_other(CPlayer *this, char *pwszCharName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-58h]@1
  CUserDB *v6; // [sp+20h] [bp-38h]@4
  CPlayer *v7; // [sp+28h] [bp-30h]@6
  int j; // [sp+30h] [bp-28h]@8
  _QUEST_DB_BASE::_LIST *pSlotData; // [sp+38h] [bp-20h]@11
  int k; // [sp+40h] [bp-18h]@12
  _base_fld *v11; // [sp+48h] [bp-10h]@15

  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = SearchAvatorWithName(g_UserDB, 2532, pwszCharName);
  if ( v6 )
  {
    v7 = &g_Player + v6->m_idWorld.wIndex;
    if ( v7 )
    {
      for ( j = 0; ; ++j )
      {
        if ( j >= 30 )
          return 0;
        pSlotData = &v7->m_Param.m_QuestDB.m_List[j];
        if ( pSlotData->byQuestType != 255 )
          break;
      }
      for ( k = 0; k < 3; ++k )
        pSlotData->wNum[k] = -1;
      pSlotData->dwPassSec = -1;
      CUserDB::Update_QuestUpdate(v6, j, pSlotData, 1);
      v11 = CRecordData::GetRecord(CQuestMgr::s_tblQuest, pSlotData->wIndex);
      if ( *(_DWORD *)&v11[13].m_strCode[60] || *(_DWORD *)&v11[1].m_strCode[24] )
        CPlayer::SendMsg_SelectQuestReward(v7, j);
      else
        CPlayer::Emb_CompleteQuest(v7, j, -1, -1);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
