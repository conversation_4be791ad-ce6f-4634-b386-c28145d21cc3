/*
 * Function: ?max_size@?$deque@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@QEBA_KXZ
 * Address: 0x14031ABF0
 */

unsigned __int64 __fastcall std::deque<RECV_DATA,std::allocator<RECV_DATA>>::max_size(std::deque<RECV_DATA,std::allocator<RECV_DATA> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::deque<RECV_DATA,std::allocator<RECV_DATA> > *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return std::allocator<RECV_DATA>::max_size(&v5->_Alval);
}
