/*
 * Function: ?IsAddAbleTalikToItem@@YA_NEGKGPEAH@Z
 * Address: 0x14003E5F0
 */

char __fastcall IsAddAbleTalikToItem(char byItemTableCode, unsigned __int16 wItemIndex, unsigned int dwItemCurLv, unsigned __int16 wTalikIndex, int *pnTalikLim)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char v8; // al@10
  __int64 v9; // [sp+0h] [bp-68h]@1
  CRecordData *v10; // [sp+20h] [bp-48h]@4
  char v11; // [sp+28h] [bp-40h]@8
  int j; // [sp+2Ch] [bp-3Ch]@8
  int v13; // [sp+30h] [bp-38h]@13
  _base_fld *v14; // [sp+38h] [bp-30h]@14
  _base_fld *v15; // [sp+40h] [bp-28h]@29
  int v16; // [sp+48h] [bp-20h]@50
  char v17; // [sp+4Ch] [bp-1Ch]@50
  int k; // [sp+50h] [bp-18h]@50
  char v19; // [sp+54h] [bp-14h]@52
  char v20; // [sp+70h] [bp+8h]@1
  unsigned __int16 v21; // [sp+78h] [bp+10h]@1
  unsigned int dwLvBit; // [sp+80h] [bp+18h]@1
  unsigned __int16 v23; // [sp+88h] [bp+20h]@1

  v23 = wTalikIndex;
  dwLvBit = dwItemCurLv;
  v21 = wItemIndex;
  v20 = byItemTableCode;
  v5 = &v9;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v10 = &s_ptblItemData[(unsigned __int8)v20];
  if ( (signed int)wTalikIndex >= 13 )
    return 0;
  if ( v20 == 6 && IsTalikAboutTol(wTalikIndex) )
  {
    v11 = GetItemUpgedLv(dwLvBit);
    for ( j = 0; j < (unsigned __int8)v11; ++j )
    {
      v8 = GetTalikFromSocket(dwLvBit, j);
      if ( IsTalikAboutTol((unsigned __int8)v8) )
        return 0;
    }
  }
  v13 = (unsigned __int8)v20;
  switch ( v20 )
  {
    case 6:
      v14 = CRecordData::GetRecord(v10, v21);
      if ( !v14 )
        return 0;
      if ( *(_DWORD *)&v14[6].m_strCode[8] == 6
        || *(_DWORD *)&v14[6].m_strCode[8] == 8
        || *(_DWORD *)&v14[6].m_strCode[8] == 7
        || *(_DWORD *)&v14[6].m_strCode[8] == 5 )
      {
        v13 = 7;
      }
      break;
    case 19:
      v13 = 8;
      break;
    case 7:
      v13 = 9;
      break;
  }
  if ( v13 > 9 )
    return 0;
  if ( v20 == 7 )
  {
    v15 = CRecordData::GetRecord(v10, v21);
    if ( !v15 )
      return 0;
    if ( *(float *)&v15[5].m_strCode[28] <= 0.0 )
    {
      if ( *(float *)&v15[5].m_strCode[32] <= 0.0 )
      {
        if ( *(float *)&v15[5].m_strCode[36] <= 0.0 )
        {
          if ( *(float *)&v15[5].m_strCode[40] <= 0.0 )
          {
            if ( v23 != 3 )
              return 0;
          }
          else if ( v23 != 10 )
          {
            return 0;
          }
        }
        else if ( v23 != 9 )
        {
          return 0;
        }
      }
      else if ( v23 != 8 )
      {
        return 0;
      }
    }
    else if ( v23 != 7 )
    {
      return 0;
    }
  }
  if ( pnTalikLim[v13] <= 0 )
  {
    result = 0;
  }
  else
  {
    v16 = 0;
    v17 = GetItemUpgedLv(dwLvBit);
    for ( k = 0; k < (unsigned __int8)v17; ++k )
    {
      v19 = GetTalikFromSocket(dwLvBit, k);
      if ( (unsigned __int8)v19 == v23 )
        ++v16;
      if ( v16 > pnTalikLim[v13] )
        return 0;
    }
    result = 1;
  }
  return result;
}
