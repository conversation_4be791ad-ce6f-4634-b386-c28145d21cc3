/*
 * Function: ?AssistForceToOne@CCharacter@@QEAA_NPEAV1@PEAU_force_fld@@H@Z
 * Address: 0x1401764F0
 */

bool __fastcall CCharacter::AssistForceToOne(CCharacter *this, CCharacter *pDst, _force_fld *pForceFld, int nForceLv)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // r9@5
  __int128 v8; // xmm2@22
  __int64 v9; // [sp+0h] [bp-B8h]@1
  unsigned int dwAttackSerial; // [sp+20h] [bp-98h]@5
  int bOtherPlayerSupport; // [sp+28h] [bp-90h]@5
  int bTempSkill[2]; // [sp+30h] [bp-88h]@5
  CCharacter *v13; // [sp+38h] [bp-80h]@5
  char v14; // [sp+44h] [bp-74h]@4
  char v15; // [sp+64h] [bp-54h]@4
  CMonster *v16; // [sp+78h] [bp-40h]@13
  void (__fastcall *v17)(CCharacter *, CPlayer *, _force_fld *, char *); // [sp+80h] [bp-38h]@20
  CMonster *v18; // [sp+88h] [bp-30h]@25
  int v19; // [sp+90h] [bp-28h]@30
  CGameObjectVtbl *v20; // [sp+98h] [bp-20h]@5
  int v21; // [sp+A0h] [bp-18h]@28
  CCharacter *pCharacter; // [sp+C0h] [bp+8h]@1
  CPlayer *v23; // [sp+C8h] [bp+10h]@1
  _force_fld *v24; // [sp+D0h] [bp+18h]@1
  int v25; // [sp+D8h] [bp+20h]@1

  v25 = nForceLv;
  v24 = pForceFld;
  v23 = (CPlayer *)pDst;
  pCharacter = this;
  v4 = &v9;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v14 = 0;
  v15 = 0;
  if ( pForceFld->m_nContEffectType != -1 )
  {
    v20 = pDst->vfptr;
    v13 = pCharacter;
    *(_QWORD *)bTempSkill = &v14;
    LOBYTE(bOtherPlayerSupport) = nForceLv;
    LOWORD(dwAttackSerial) = pForceFld->m_nContEffectSec[nForceLv - 1];
    v6 = pForceFld->m_dwIndex;
    LOBYTE(pForceFld) = 1;
    v15 = ((int (__fastcall *)(CCharacter *, _QWORD, _force_fld *, __int64))v20[1].__vecDelDtor)(
            pDst,
            LOBYTE(v24->m_nContEffectType),
            pForceFld,
            v6);
    if ( !pCharacter->m_ObjID.m_byID && !v15 )
    {
      if ( v24->m_nContEffectType )
      {
        if ( v24->m_nContEffectType == 1 && !v23->m_ObjID.m_byID )
        {
          v16 = (CMonster *)CPlayer::GetTargetObj(v23);
          if ( v16 )
          {
            if ( !v16->m_ObjID.m_byKind
              && v16->m_ObjID.m_byID == 1
              && (CMonsterAggroMgr::SearchAggroNode(&v16->m_AggroMgr, (CCharacter *)&v23->vfptr)
               || (CPlayer *)CMonster::GetAttackTarget(v16) == v23) )
            {
              bTempSkill[0] = 0;
              bOtherPlayerSupport = 1;
              dwAttackSerial = v24->m_dwIndex;
              CMonsterAggroMgr::SetAggro(&v16->m_AggroMgr, pCharacter, 0, 1, dwAttackSerial, 1, 0);
            }
          }
        }
      }
      else if ( v23->m_ObjID.m_byID == 1 )
      {
        bTempSkill[0] = 0;
        bOtherPlayerSupport = 0;
        dwAttackSerial = v24->m_dwIndex;
        CMonsterAggroMgr::SetAggro(
          (CMonsterAggroMgr *)&v23->m_Param.m_dbInven.m_List[28].m_wSerial,
          pCharacter,
          0,
          1,
          dwAttackSerial,
          0,
          0);
      }
    }
  }
  if ( v24->m_nTempEffectType != -1 )
  {
    v17 = (void (__fastcall *)(CCharacter *, CPlayer *, _force_fld *, char *))g_TempEffectFunc[v24->m_nTempEffectType];
    if ( v15 )
      return 0;
    v8 = LODWORD(v24->m_fTempValue[v25 - 1]);
    v17(pCharacter, v23, pForceFld, &v15);
    if ( !v15 && !pCharacter->m_ObjID.m_byID )
    {
      if ( v23->m_ObjID.m_byID )
      {
        if ( v23->m_ObjID.m_byID == 1 )
        {
          bTempSkill[0] = 1;
          bOtherPlayerSupport = 0;
          dwAttackSerial = v24->m_dwIndex;
          CMonsterAggroMgr::SetAggro(
            (CMonsterAggroMgr *)&v23->m_Param.m_dbInven.m_List[28].m_wSerial,
            pCharacter,
            0,
            1,
            dwAttackSerial,
            0,
            1);
        }
      }
      else
      {
        v18 = (CMonster *)CPlayer::GetTargetObj(v23);
        v21 = v18 && !v18->m_ObjID.m_byKind && v18->m_ObjID.m_byID == 1;
        v19 = v21;
        if ( v21
          && (CMonsterAggroMgr::SearchAggroNode(&v18->m_AggroMgr, (CCharacter *)&v23->vfptr)
           || (CPlayer *)CMonster::GetAttackTarget(v18) == v23) )
        {
          bTempSkill[0] = 1;
          bOtherPlayerSupport = 1;
          dwAttackSerial = v24->m_dwIndex;
          CMonsterAggroMgr::SetAggro(&v18->m_AggroMgr, pCharacter, 0, 1, dwAttackSerial, 1, 1);
        }
      }
    }
  }
  return v15 == 0;
}
