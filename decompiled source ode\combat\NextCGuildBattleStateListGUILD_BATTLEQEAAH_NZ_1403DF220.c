/*
 * Function: ?Next@CGuildBattleStateList@GUILD_BATTLE@@QEAAH_N@Z
 * Address: 0x1403DF220
 */

signed __int64 __fastcall GUILD_BATTLE::CGuildBattleStateList::Next(GUILD_BATTLE::CGuildBattleStateList *this, bool bForce)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@7
  GUILD_BATTLE::CGuildBattleStateList *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( bForce || !GUILD_BATTLE::CGuildBattleStateList::IsEmpty(v7) )
  {
    ++v7->m_iState;
    v6 = GUILD_BATTLE::CGuildBattleStateList::CheckLoop(v7);
    if ( v6 == 2 )
    {
      ((void (__fastcall *)(GUILD_BATTLE::CGuildBattleStateList *))v7->vfptr->SetNextState)(v7);
      result = 1i64;
    }
    else
    {
      result = (unsigned int)v6;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
