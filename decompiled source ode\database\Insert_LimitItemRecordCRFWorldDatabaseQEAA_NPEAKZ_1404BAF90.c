/*
 * Function: ?Insert_LimitItemRecord@CRFWorldDatabase@@QEAA_NPEAK@Z
 * Address: 0x1404BAF90
 */

char __fastcall CRFWorldDatabase::Insert_LimitItemRecord(CRFWorldDatabase *this, unsigned int *pdwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-108h]@1
  void *SQLStmt; // [sp+20h] [bp-E8h]@15
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-E0h]@23
  SQLLEN v8; // [sp+38h] [bp-D0h]@23
  __int16 v9; // [sp+44h] [bp-C4h]@11
  char DstBuf; // [sp+60h] [bp-A8h]@4
  char v11; // [sp+61h] [bp-A7h]@4
  unsigned __int64 v12; // [sp+F0h] [bp-18h]@4
  CRFWorldDatabase *v13; // [sp+110h] [bp+8h]@1
  unsigned int *TargetValue; // [sp+118h] [bp+10h]@1

  TargetValue = pdwSerial;
  v13 = this;
  v2 = &v5;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = (unsigned __int64)&v5 ^ _security_cookie;
  DstBuf = 0;
  memset(&v11, 0, 0x7Fui64);
  sprintf_s(&DstBuf, 0x80ui64, "{ CALL pInsert_StoreLimitItemRecord_061212 }");
  if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v13->vfptr, &DstBuf, 1) )
  {
    sprintf_s(&DstBuf, 0x80ui64, "Select count(serial) from tbl_StoreLimitItem_061212");
    if ( v13->m_bSaveDBLog )
      CRFNewDatabase::Log((CRFNewDatabase *)&v13->vfptr, &DstBuf);
    if ( v13->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v13->vfptr) )
    {
      v9 = SQLExecDirect_0(v13->m_hStmtSelect, &DstBuf, -3);
      if ( v9 && v9 != 1 )
      {
        if ( v9 == 100 )
        {
          result = 0;
        }
        else
        {
          SQLStmt = v13->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v9, &DstBuf, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v9, v13->m_hStmtSelect);
          result = 0;
        }
      }
      else
      {
        v9 = SQLFetch_0(v13->m_hStmtSelect);
        if ( v9 && v9 != 1 )
        {
          if ( v9 != 100 )
          {
            SQLStmt = v13->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v9, &DstBuf, "SQLFetch", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v9, v13->m_hStmtSelect);
          }
          if ( v13->m_hStmtSelect )
            SQLCloseCursor_0(v13->m_hStmtSelect);
          result = 0;
        }
        else
        {
          StrLen_or_IndPtr = &v8;
          SQLStmt = 0i64;
          v9 = SQLGetData_0(v13->m_hStmtSelect, 1u, -18, TargetValue, 0i64, &v8);
          if ( v9 && v9 != 1 )
          {
            SQLStmt = v13->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v9, &DstBuf, "SQLGetData", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v9, v13->m_hStmtSelect);
            if ( v13->m_hStmtSelect )
              SQLCloseCursor_0(v13->m_hStmtSelect);
            result = 0;
          }
          else
          {
            if ( v13->m_hStmtSelect )
              SQLCloseCursor_0(v13->m_hStmtSelect);
            if ( v13->m_bSaveDBLog )
              CRFNewDatabase::FmtLog((CRFNewDatabase *)&v13->vfptr, "%s Success", &DstBuf);
            result = 1;
          }
        }
      }
    }
    else
    {
      CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v13->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
