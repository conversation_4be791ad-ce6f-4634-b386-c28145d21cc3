/*
 * Function: j_??$_Uninit_move@V?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@PEAPEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@2@U_Undefined_move_tag@2@@std@@YAPEAPEAVCLogTypeDBTask@@V?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@0@0PEAPEAV1@AEAV?$allocator@PEAVCLogTypeDBTask@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140003E31
 */

CLogTypeDBTask **__fastcall std::_Uninit_move<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>,std::_Undefined_move_tag>(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_First, std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_Last, CLogTypeDBTask **_Dest, std::allocator<CLogTypeDBTask *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
