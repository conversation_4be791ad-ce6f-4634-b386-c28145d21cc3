/*
 * Function: j_?Load@CReservedGuildScheduleDayGroup@GUILD_BATTLE@@QEAA_NEIAEAU_worlddb_guild_battle_reserved_schedule_info@@@Z
 * Address: 0x140005CA4
 */

bool __fastcall GUILD_BATTLE::CReservedGuildScheduleDayGroup::Load(GUILD_BATTLE::CReservedGuildScheduleDayGroup *this, char byDay<PERSON>, unsigned int uiMapInx, _worlddb_guild_battle_reserved_schedule_info *kInfo)
{
  return GUILD_BATTLE::CReservedGuildScheduleDayGroup::Load(this, byDayID, uiMapInx, kInfo);
}
