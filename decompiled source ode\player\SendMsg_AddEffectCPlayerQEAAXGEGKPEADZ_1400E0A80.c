/*
 * Function: ?SendMsg_AddEffect@CPlayer@@QEAAXGEGKPEAD@Z
 * Address: 0x1400E0A80
 */

void __fastcall CPlayer::SendMsg_AddEffect(CPlayer *this, unsigned __int16 wEffectCode, char byLv, unsigned __int16 wDurSec, unsigned int dwPlayerSerial, char *wszPlayerName)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-A8h]@1
  char szMsg; // [sp+38h] [bp-70h]@4
  unsigned __int16 v10; // [sp+39h] [bp-6Fh]@4
  unsigned __int16 v11; // [sp+3Bh] [bp-6Dh]@4
  unsigned int v12; // [sp+3Dh] [bp-6Bh]@4
  char Dst; // [sp+41h] [bp-67h]@5
  char pbyType; // [sp+74h] [bp-34h]@6
  char v15; // [sp+75h] [bp-33h]@6
  unsigned __int64 v16; // [sp+90h] [bp-18h]@4
  CPlayer *v17; // [sp+B0h] [bp+8h]@1
  unsigned __int16 v18; // [sp+B8h] [bp+10h]@1
  char v19; // [sp+C0h] [bp+18h]@1

  v19 = byLv;
  v18 = wEffectCode;
  v17 = this;
  v6 = &v8;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v16 = (unsigned __int64)&v8 ^ _security_cookie;
  v10 = wEffectCode;
  szMsg = byLv;
  v11 = wDurSec;
  v12 = dwPlayerSerial;
  if ( dwPlayerSerial )
    strcpy_s(&Dst, 0x11ui64, wszPlayerName);
  pbyType = 17;
  v15 = 10;
  CNetProcess::LoadSendMsg(unk_1414F2088, v17->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x1Au);
  CPlayer::SendData_PartyMemberEffect(v17, 0, v18, v19);
}
