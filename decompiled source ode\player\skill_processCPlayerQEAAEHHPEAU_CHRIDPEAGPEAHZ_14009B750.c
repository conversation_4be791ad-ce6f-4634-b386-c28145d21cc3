/*
 * Function: ?skill_process@CPlayer@@QEAAEHHPEAU_CHRID@@PEAGPEAH@Z
 * Address: 0x14009B750
 */

char __usercall CPlayer::skill_process@<al>(CPlayer *this@<rcx>, int nEffectCode@<edx>, int nSkillIndex@<r8d>, _CHRID *pidDst@<r9>, float a5@<xmm0>, unsigned __int16 *pConsumeSerial, int *pnLv)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  CGuildRoomSystem *v10; // rax@74
  int v11; // eax@94
  float v12; // xmm0_4@94
  CGuildRoomSystem *v13; // rax@102
  int v14; // eax@109
  int v15; // eax@112
  int v16; // eax@124
  int v17; // eax@125
  __int64 v18; // [sp+0h] [bp-1D8h]@1
  int *pnConsume; // [sp+20h] [bp-1B8h]@115
  bool *pbOverLap; // [sp+28h] [bp-1B0h]@115
  bool *pbUpMty; // [sp+30h] [bp-1A8h]@115
  _skill_fld *pSkillFld; // [sp+40h] [bp-198h]@4
  unsigned __int16 pwDelPoint; // [sp+54h] [bp-184h]@4
  char v24; // [sp+56h] [bp-182h]@4
  int nSkillLv; // [sp+64h] [bp-174h]@4
  CCharacter *pDst; // [sp+68h] [bp-170h]@4
  int pnClassGrade; // [sp+74h] [bp-164h]@4
  char v28; // [sp+84h] [bp-154h]@24
  char v29; // [sp+85h] [bp-153h]@78
  _STORAGE_LIST::_db_con *ppConsumeItems; // [sp+98h] [bp-140h]@91
  char v31; // [sp+A0h] [bp-138h]@91
  int v32; // [sp+C8h] [bp-110h]@91
  char v33; // [sp+CCh] [bp-10Ch]@91
  bool v34; // [sp+F4h] [bp-E4h]@91
  char v35; // [sp+F5h] [bp-E3h]@91
  char pbyErrorCode; // [sp+114h] [bp-C4h]@98
  bool v37; // [sp+134h] [bp-A4h]@98
  char v38; // [sp+144h] [bp-94h]@98
  unsigned int dwAlter; // [sp+148h] [bp-90h]@107
  CPlayer *v40; // [sp+150h] [bp-88h]@114
  int nParamCode; // [sp+158h] [bp-80h]@121
  int nValue; // [sp+15Ch] [bp-7Ch]@127
  CUserDB *v43; // [sp+168h] [bp-70h]@74
  int v44; // [sp+170h] [bp-68h]@74
  CGuild *v45; // [sp+178h] [bp-60h]@74
  float v46; // [sp+180h] [bp-58h]@94
  CUserDB *v47; // [sp+188h] [bp-50h]@102
  int v48; // [sp+190h] [bp-48h]@102
  CGuild *v49; // [sp+198h] [bp-40h]@102
  int v50; // [sp+1A0h] [bp-38h]@109
  CGameObjectVtbl *v51; // [sp+1A8h] [bp-30h]@109
  int v52; // [sp+1B0h] [bp-28h]@112
  CGameObjectVtbl *v53; // [sp+1B8h] [bp-20h]@112
  int v54; // [sp+1C0h] [bp-18h]@125
  unsigned __int64 v55; // [sp+1C8h] [bp-10h]@4
  CPlayer *v56; // [sp+1E0h] [bp+8h]@1
  int nEffectCodea; // [sp+1E8h] [bp+10h]@1
  int n; // [sp+1F0h] [bp+18h]@1
  _CHRID *v59; // [sp+1F8h] [bp+20h]@1

  v59 = pidDst;
  n = nSkillIndex;
  nEffectCodea = nEffectCode;
  v56 = this;
  v7 = &v18;
  for ( i = 116i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v55 = (unsigned __int64)&v18 ^ _security_cookie;
  pSkillFld = (_skill_fld *)CRecordData::GetRecord(&stru_1799C8410 + nEffectCode, nSkillIndex);
  pwDelPoint = 0;
  memset(&v24, 0, 4ui64);
  nSkillLv = 1;
  pDst = 0i64;
  pnClassGrade = -1;
  if ( nEffectCodea )
  {
    if ( nEffectCodea == 2 && !CPlayerDB::IsActableClassSkill(&v56->m_Param, pSkillFld->m_strCode, &pnClassGrade) )
      return 15;
  }
  else
  {
    if ( pSkillFld->m_nMastIndex > 8u )
      return 8;
    if ( !CPlayer::_pre_check_skill_enable(v56, pSkillFld) )
      return 21;
    if ( !CPlayer::_pre_check_skill_gradelimit(v56, pSkillFld) )
      return 34;
    if ( pSkillFld->m_nClass == 4 && (CPlayer::IsChaosMode(v56) || CPlayer::IsPunished(v56, 1, 0)) )
      return 35;
  }
  if ( (!nEffectCodea || nEffectCodea == 2) && v56->m_bInGuildBattle && v56->m_bTakeGravityStone )
    return 27;
  v28 = -1;
  if ( nEffectCodea )
  {
    if ( nEffectCodea == 2 )
      v28 = pnClassGrade;
  }
  else
  {
    if ( pSkillFld->m_nMastIndex < 8u )
      v28 = pSkillFld->m_nMastIndex;
    if ( pSkillFld->m_nClass == 2 )
      v28 = -1;
  }
  if ( !v56->m_bSFDelayNotCheck
    && !_ATTACK_DELAY_CHECKER::IsDelay(&v56->m_AttDelayChker, nEffectCodea, pSkillFld->m_dwIndex, v28) )
  {
    return 9;
  }
  if ( pSkillFld->m_nTempEffectType >= 150 )
    return 21;
  if ( pSkillFld->m_nContEffectType == 1 && _effect_parameter::GetEff_State(&v56->m_EP, 2) )
    return 7;
  if ( CPlayer::IsRidingUnit(v56) )
    return 14;
  if ( CPlayerDB::GetRaceCode(&v56->m_Param) == 2 && CPlayer::IsActingSiegeMode(v56) )
    return 21;
  if ( v56->m_dwSelfDestructionTime )
    return 26;
  pDst = (CCharacter *)CMainThread::GetObjectA(&g_Main, 0, v59->byID, v59->wIndex);
  if ( !pDst )
    return 2;
  if ( !CCharacter::IsEffectableDst((CCharacter *)&v56->vfptr, pSkillFld->m_strActableDst, pDst) )
    return 5;
  if ( !pDst->m_bLive )
    return 2;
  if ( pSkillFld->m_nTempEffectType == -1 && pSkillFld->m_nContEffectType == -1 )
    return 8;
  if ( pSkillFld->m_nContEffectType != -1
    && !(unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsRecvableContEffect)(pDst) )
  {
    return 13;
  }
  if ( _effect_parameter::GetEff_State(&pDst->m_EP, 20) )
  {
    if ( pSkillFld->m_nTempEffectType == -1 )
      return 24;
    if ( !IsUsableTempEffectAtStoneState(pSkillFld->m_nTempEffectType) )
      return 24;
  }
  if ( _effect_parameter::GetEff_State(&pDst->m_EP, 28) )
    return 24;
  if ( _effect_parameter::GetEff_State(&v56->m_EP, 21) )
    return 25;
  if ( !pSkillFld->m_nContEffectType
    && !(unsigned __int8)((int (__fastcall *)(CPlayer *))v56->vfptr->IsAttackableInTown)(v56)
    && !(unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsAttackableInTown)(pDst) )
  {
    if ( (unsigned __int8)((int (__fastcall *)(CPlayer *))v56->vfptr->IsInTown)(v56) )
      return 18;
    if ( (unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsInTown)(pDst) )
      return 18;
    if ( v56->m_Param.m_pGuild )
    {
      v43 = v56->m_pUserDB;
      v44 = v56->m_ObjID.m_wIndex;
      v45 = v56->m_Param.m_pGuild;
      v10 = CGuildRoomSystem::GetInstance();
      if ( CGuildRoomSystem::IsGuildRoomMemberIn(v10, v45->m_dwSerial, v44, v43->m_dwSerial) )
        return 18;
    }
  }
  if ( !nEffectCodea )
  {
    if ( pSkillFld->m_nClass == 2 )
    {
      v29 = CEquipItemSFAgent::IsEnableSkill(&v56->EquipItemSFAgent, pSkillFld);
      if ( v29 )
        return v29;
    }
    else
    {
      if ( !CPlayer::IsSFUsableSFMastery(v56, 3, pSkillFld->m_nMastIndex) )
        return 3;
      if ( !CPlayer::IsSFActableByClass(v56, 0, (_base_fld *)&pSkillFld->m_dwIndex) )
        return 16;
    }
  }
  if ( v56->m_byMoveType == 2 )
  {
    result = 28;
  }
  else if ( CPlayer::IsSFUseableRace(v56, nEffectCodea, pSkillFld->m_dwIndex) )
  {
    if ( CPlayer::IsSFUsableGauge(v56, nEffectCodea, pSkillFld->m_dwIndex, &pwDelPoint) )
    {
      ppConsumeItems = 0i64;
      memset(&v31, 0, 0x10ui64);
      v32 = 0;
      memset(&v33, 0, 8ui64);
      v34 = 0;
      memset(&v35, 0, 2ui64);
      if ( CPlayer::GetUseConsumeItem(v56, pSkillFld->m_ConsumeItemList, pConsumeSerial, &ppConsumeItems, &v32, &v34) )
      {
        if ( !nEffectCodea )
        {
          v11 = _MASTERY_PARAM::GetSkillLv(&v56->m_pmMst, n);
          v12 = (float)v11;
          v46 = (float)v11;
          _effect_parameter::GetEff_Plus(&v56->m_EP, 19);
          a5 = v46 + v12;
          nSkillLv = (signed int)ffloor(a5);
          if ( nSkillLv > 7 )
            nSkillLv = 7;
        }
        if ( pnLv )
          *pnLv = nSkillLv;
        pbyErrorCode = 0;
        v37 = 0;
        v38 = 0;
        if ( CCharacter::AssistSkill(
               (CCharacter *)&v56->vfptr,
               pDst,
               nEffectCodea,
               pSkillFld,
               nSkillLv,
               &pbyErrorCode,
               &v37) )
        {
          v38 = 1;
          if ( v37 && !(unsigned __int8)((int (__fastcall *)(CPlayer *))v56->vfptr->IsInTown)(v56) )
          {
            if ( !v56->m_Param.m_pGuild
              || (v47 = v56->m_pUserDB,
                  v48 = v56->m_ObjID.m_wIndex,
                  v49 = v56->m_Param.m_pGuild,
                  v13 = CGuildRoomSystem::GetInstance(),
                  !CGuildRoomSystem::IsGuildRoomMemberIn(v13, v49->m_dwSerial, v48, v47->m_dwSerial)) )
            {
              if ( pSkillFld->m_nClass != 2 && pSkillFld->m_nClass != 4 && !nEffectCodea && pSkillFld->m_nMastIndex < 8u )
              {
                dwAlter = 1;
                if ( pSkillFld->m_nClass == 1 )
                  dwAlter = 2;
                v50 = ((int (__fastcall *)(CPlayer *))v56->vfptr->GetObjRace)(v56);
                v51 = pDst->vfptr;
                v14 = ((int (__fastcall *)(CCharacter *))v51->GetObjRace)(pDst);
                if ( v50 != v14 || !CPlayer::IsChaosMode(v56) || pSkillFld->m_nContEffectType )
                {
                  v52 = ((int (__fastcall *)(CPlayer *))v56->vfptr->GetObjRace)(v56);
                  v53 = pDst->vfptr;
                  v15 = ((int (__fastcall *)(CCharacter *))v53->GetObjRace)(pDst);
                  if ( v52 != v15 || pDst->m_ObjID.m_byID )
                  {
                    LOBYTE(pbUpMty) = 1;
                    pbOverLap = (bool *)"CPlayer::skill_process()---0";
                    LOBYTE(pnConsume) = 0;
                    CPlayer::Emb_AlterStat(v56, 3, pSkillFld->m_dwIndex, dwAlter, 0, "CPlayer::skill_process()---0", 1);
                  }
                  else
                  {
                    v40 = (CPlayer *)pDst;
                    if ( !CPlayer::IsPunished((CPlayer *)pDst, 1, 0) )
                    {
                      LOBYTE(pbUpMty) = 1;
                      pbOverLap = (bool *)"CPlayer::skill_process()---0";
                      LOBYTE(pnConsume) = 0;
                      CPlayer::Emb_AlterStat(
                        v56,
                        3,
                        pSkillFld->m_dwIndex,
                        dwAlter,
                        0,
                        "CPlayer::skill_process()---0",
                        1);
                    }
                  }
                }
              }
            }
          }
          if ( pSkillFld->m_nClass == 4 )
          {
            CMyTimer::BeginTimer(&v56->m_tmrAuraSkill, 0x1388u);
            CPlayer::skill_process_for_aura(v56, pSkillFld->m_dwIndex);
          }
        }
        if ( v38 )
        {
          for ( nParamCode = 0; nParamCode < 3; ++nParamCode )
          {
            if ( (signed int)*(&pwDelPoint + nParamCode) > 0 )
            {
              v16 = CPlayer::GetGauge(v56, nParamCode);
              if ( v16 - *(&pwDelPoint + nParamCode) <= 0 )
              {
                v54 = 0;
              }
              else
              {
                v17 = CPlayer::GetGauge(v56, nParamCode);
                v54 = v17 - *(&pwDelPoint + nParamCode);
              }
              nValue = v54;
              CPlayer::SetGauge(v56, nParamCode, v54, 1);
            }
          }
          CPlayer::SendMsg_Recover(v56);
          CPlayer::DeleteUseConsumeItem(v56, &ppConsumeItems, &v32, &v34);
          _effect_parameter::GetEff_Plus(&v56->m_EP, 12);
          _ATTACK_DELAY_CHECKER::SetDelay(&v56->m_AttDelayChker, (signed int)ffloor(pSkillFld->m_fActDelay + a5));
        }
        result = pbyErrorCode;
      }
      else
      {
        result = 32;
      }
    }
    else
    {
      result = 6;
    }
  }
  else
  {
    result = 4;
  }
  return result;
}
