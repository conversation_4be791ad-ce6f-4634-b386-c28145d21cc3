/*
 * Function: ?dtor$1@?0??_Reset@?$_Greedy_node@W4agent_status@Concurrency@@@Concurrency@@UEAAXXZ@4HA_0
 * Address: 0x140579D20
 */

int __fastcall `Concurrency::_Greedy_node<enum  Concurrency::agent_status>::_Reset'::`1'::dtor$1(__int64 a1, __int64 a2)
{
  return CryptoPP::AlgorithmParameters<CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,int const *>,int>::~AlgorithmParameters<CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,int const *>,int>(a2 + 72);
}
