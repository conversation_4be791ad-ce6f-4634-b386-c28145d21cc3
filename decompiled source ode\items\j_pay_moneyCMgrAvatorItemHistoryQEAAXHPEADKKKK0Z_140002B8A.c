/*
 * Function: j_?pay_money@CMgrAvatorItemHistory@@QEAAXHPEADKKKK0@Z
 * Address: 0x140002B8A
 */

void __fastcall CMgrAvatorItemHistory::pay_money(CMgrAvatorItemHistory *this, int n, char *psz<PERSON><PERSON>e, unsigned int dwPayDalant, unsigned int dwPayGold, unsigned int dwNewDalant, unsigned int dwNewGold, char *pszFileName)
{
  CMgrAvatorItemHistory::pay_money(this, n, pszClause, dwPayDalant, dwPayGold, dwNewDalant, dwNewGold, pszFileName);
}
