/*
 * Function: ?patriarch_push_money@CMgrAvatorItemHistory@@QEAAXPEADKK0@Z
 * Address: 0x1402403F0
 */

void __fastcall CMgrAvatorItemHistory::patriarch_push_money(CMgrAvatorItemHistory *this, char *pwszPatriarchName, unsigned int dwPushDalant, unsigned int dwLeftDalant, char *pszFileName)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  unsigned int v8; // [sp+20h] [bp-28h]@4
  char *v9; // [sp+28h] [bp-20h]@4
  char *v10; // [sp+30h] [bp-18h]@4
  CMgrAvatorItemHistory *v11; // [sp+50h] [bp+8h]@1
  char *v12; // [sp+58h] [bp+10h]@1
  unsigned int v13; // [sp+60h] [bp+18h]@1
  unsigned int v14; // [sp+68h] [bp+20h]@1

  v14 = dwLeftDalant;
  v13 = dwPushDalant;
  v12 = pwszPatriarchName;
  v11 = this;
  v5 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -*********;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  memset_0(sData, 0, 0x4E20ui64);
  v10 = v11->m_szCurTime;
  v9 = v11->m_szCurDate;
  v8 = v14;
  sprintf(sData, "[PATRIARCH PUSH TAX MONEY]: PATRIARCH(%s) pay(D:%u) $D:%u [%s %s]\r\n", v12, v13);
  CMgrAvatorItemHistory::WriteFile(v11, pszFileName, sData);
}
