/*
 * Function: j_?destroy@?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@std@@@std@@QEAAXPEAV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@2@@Z
 * Address: 0x140006280
 */

void __fastcall std::allocator<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>>::destroy(std::allocator<std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> > *this, std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *_Ptr)
{
  std::allocator<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>>::destroy(
    this,
    _Ptr);
}
