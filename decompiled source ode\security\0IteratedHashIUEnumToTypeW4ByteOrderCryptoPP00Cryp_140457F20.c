/*
 * Function: ??0?$IteratedHash@IU?$EnumToType@W4ByteOrder@CryptoPP@@$00@CryptoPP@@$0EA@VHashTransformation@2@@CryptoPP@@QEAA@AEBV01@@Z
 * Address: 0x140457F20
 */

void __fastcall CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>(CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation> *this, CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation> *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  __int64 v5; // [sp+20h] [bp-18h]@4
  CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation> *v6; // [sp+40h] [bp+8h]@1
  CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation> *__thata; // [sp+48h] [bp+10h]@1

  __thata = __that;
  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = -2i64;
  CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation>::IteratedHashBase<unsigned int,CryptoPP::HashTransformation>(
    (CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation> *)&v6->vfptr,
    (CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation> *)&__that->vfptr);
  CryptoPP::FixedSizeSecBlock<unsigned int,16,CryptoPP::FixedSizeAllocatorWithCleanup<unsigned int,16,CryptoPP::NullAllocator<unsigned int>,0>>::FixedSizeSecBlock<unsigned int,16,CryptoPP::FixedSizeAllocatorWithCleanup<unsigned int,16,CryptoPP::NullAllocator<unsigned int>,0>>(
    &v6->m_data,
    &__thata->m_data);
}
