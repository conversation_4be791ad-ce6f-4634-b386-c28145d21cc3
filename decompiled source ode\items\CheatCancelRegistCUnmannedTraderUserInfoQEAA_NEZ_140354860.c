/*
 * Function: ?CheatCancelRegist@CUnmannedTraderUserInfo@@QEAA_NE@Z
 * Address: 0x140354860
 */

bool __fastcall CUnmannedTraderUserInfo::CheatCancelRegist(CUnmannedTraderUserInfo *this, char byNth)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderUserInfo *v6; // [sp+30h] [bp+8h]@1
  char v7; // [sp+38h] [bp+10h]@1

  v7 = byNth;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( (unsigned __int8)byNth == 255 )
  {
    result = CUnmannedTraderUserInfo::CheatCancelRegistAll(v6);
  }
  else if ( std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(&v6->m_vecLoadItemInfo) > (unsigned __int8)byNth )
  {
    result = CUnmannedTraderUserInfo::CheatCancelRegistSingle(v6, v7);
  }
  else
  {
    result = 0;
  }
  return result;
}
