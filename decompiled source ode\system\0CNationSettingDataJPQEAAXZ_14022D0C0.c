/*
 * Function: ??0CNationSettingDataJP@@QEAA@XZ
 * Address: 0x14022D0C0
 */

void __fastcall CNationSettingDataJP::CNationSettingDataJP(CNationSettingDataJP *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CNationSettingDataJP *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CNationSettingData::CNationSettingData((CNationSettingData *)&v4->vfptr);
  v4->vfptr = (CNationSettingDataVtbl *)&CNationSettingDataJP::`vftable';
}
