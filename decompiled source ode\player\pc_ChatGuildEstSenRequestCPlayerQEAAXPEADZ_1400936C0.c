/*
 * Function: ?pc_ChatGuildEstSenRequest@CPlayer@@QEAAXPEAD@Z
 * Address: 0x1400936C0
 */

void __fastcall CPlayer::pc_ChatGuildEstSenRequest(CPlayer *this, char *pwszChatData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@12
  CChatStealSystem *v5; // rax@12
  __int64 v6; // [sp+0h] [bp-1C8h]@1
  _announ_message_receipt_udp Dst; // [sp+40h] [bp-188h]@12
  char pbyType; // [sp+174h] [bp-54h]@12
  char v9; // [sp+175h] [bp-53h]@12
  CGuild *v10; // [sp+188h] [bp-40h]@12
  _guild_member_info *v11; // [sp+190h] [bp-38h]@12
  CPlayer *v12; // [sp+198h] [bp-30h]@12
  int v13; // [sp+1A0h] [bp-28h]@12
  int j; // [sp+1A4h] [bp-24h]@12
  unsigned __int64 v15; // [sp+1B0h] [bp-18h]@4
  CPlayer *pPlayer; // [sp+1D0h] [bp+8h]@1
  const char *Str; // [sp+1D8h] [bp+10h]@1

  Str = pwszChatData;
  pPlayer = this;
  v2 = &v6;
  for ( i = 112i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v15 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( pPlayer->m_pUserDB
    && !pPlayer->m_pUserDB->m_bChatLock
    && pPlayer->m_Param.m_pGuild
    && pPlayer->m_Param.m_pGuildMemPtr
    && (pPlayer->m_Param.m_byClassInGuild == 1 || pPlayer->m_Param.m_byClassInGuild == 2) )
  {
    _announ_message_receipt_udp::_announ_message_receipt_udp(&Dst);
    Dst.byMessageType = 11;
    Dst.bySenderRace = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
    Dst.dwSenderSerial = pPlayer->m_dwObjSerial;
    v4 = CPlayerDB::GetCharNameW(&pPlayer->m_Param);
    strcpy_0(Dst.wszSenderName, v4);
    Dst.bySize = strlen_0(Str);
    memcpy_0(Dst.wszChatData, Str, (unsigned __int8)Dst.bySize);
    Dst.wszChatData[(unsigned __int8)Dst.bySize] = 0;
    Dst.byPvpGrade = -1;
    pbyType = 2;
    v9 = 11;
    v10 = pPlayer->m_Param.m_pGuild;
    v11 = v10->m_MemberData;
    v12 = 0i64;
    v5 = CChatStealSystem::Instance();
    CChatStealSystem::StealChatMsg(v5, pPlayer, Dst.byMessageType, (char *)Str);
    v13 = _announ_message_receipt_udp::size(&Dst);
    for ( j = 0; j < v10->m_nMemberNum; ++j )
    {
      if ( _guild_member_info::IsFill(&v11[j])
        && v11[j].pPlayer
        && (v11[j].byClassInGuild == 1 || v11[j].byClassInGuild == 2) )
      {
        v12 = v11[j].pPlayer;
        CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, &Dst.byMessageType, v13);
      }
    }
  }
}
