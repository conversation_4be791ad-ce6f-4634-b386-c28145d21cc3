/*
 * Function: ?SendMsg_GuildInfoUpdateInform@CGuild@@QEAAXXZ
 * Address: 0x140255850
 */

void __fastcall CGuild::SendMsg_GuildInfoUpdateInform(CGuild *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-98h]@1
  char szMsg[4]; // [sp+38h] [bp-60h]@4
  char v5; // [sp+3Ch] [bp-5Ch]@4
  unsigned int v6; // [sp+3Dh] [bp-5Bh]@4
  unsigned int v7; // [sp+41h] [bp-57h]@4
  unsigned int v8; // [sp+45h] [bp-53h]@4
  unsigned int v9; // [sp+49h] [bp-4Fh]@4
  unsigned int v10; // [sp+4Dh] [bp-4Bh]@4
  char pbyType; // [sp+74h] [bp-24h]@4
  char v12; // [sp+75h] [bp-23h]@4
  int j; // [sp+84h] [bp-14h]@4
  CPlayer *v14; // [sp+88h] [bp-10h]@7
  CGuild *v15; // [sp+A0h] [bp+8h]@1

  v15 = this;
  v1 = &v3;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(_DWORD *)szMsg = v15->m_dwSerial;
  v5 = v15->m_byGrade;
  v6 = v15->m_dwEmblemBack;
  v7 = v15->m_dwEmblemMark;
  v8 = v15->m_dwGuildBattleTotWin;
  v9 = v15->m_dwGuildBattleTotDraw;
  v10 = v15->m_dwGuildBattleTotLose;
  pbyType = 27;
  v12 = 40;
  for ( j = 0; j < 2532; ++j )
  {
    v14 = &g_Player + j;
    if ( v14->m_bLive )
      CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_ObjID.m_wIndex, &pbyType, szMsg, 0x19u);
  }
}
