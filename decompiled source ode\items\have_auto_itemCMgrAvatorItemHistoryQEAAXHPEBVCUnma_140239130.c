/*
 * Function: ?have_auto_item@CMgrAvatorItemHistory@@QEAAXHPEBVCUnmannedTraderRegistItemInfo@@E@Z
 * Address: 0x140239130
 */

void __fastcall CMgrAvatorItemHistory::have_auto_item(CMgrAvatorItemHistory *this, int n, CUnmannedTraderRegistItemInfo *pkInfo, char byMaxCnt)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char v6; // al@13
  const __int64 *v7; // rax@13
  __int64 v8; // rax@14
  int v9; // eax@15
  int v10; // ecx@15
  char v11; // al@16
  unsigned int v12; // eax@16
  __int64 v13; // [sp+0h] [bp-1E8h]@1
  int v14; // [sp+20h] [bp-1C8h]@6
  char *v15; // [sp+28h] [bp-1C0h]@15
  unsigned __int64 v16; // [sp+30h] [bp-1B8h]@15
  char *v17; // [sp+38h] [bp-1B0h]@15
  unsigned int v18; // [sp+40h] [bp-1A8h]@16
  unsigned int v19; // [sp+48h] [bp-1A0h]@16
  char *v20; // [sp+50h] [bp-198h]@16
  int v21; // [sp+58h] [bp-190h]@16
  tm *v22; // [sp+60h] [bp-188h]@7
  char Dest; // [sp+80h] [bp-168h]@7
  char v24; // [sp+81h] [bp-167h]@7
  char v25; // [sp+184h] [bp-64h]@7
  unsigned __int8 j; // [sp+185h] [bp-63h]@7
  _base_fld *v27; // [sp+188h] [bp-60h]@13
  int na; // [sp+198h] [bp-50h]@13
  int v29; // [sp+19Ch] [bp-4Ch]@16
  unsigned int v30; // [sp+1A0h] [bp-48h]@16
  unsigned int v31; // [sp+1A4h] [bp-44h]@16
  unsigned int dwLvBit; // [sp+1A8h] [bp-40h]@16
  char *v33; // [sp+1B0h] [bp-38h]@16
  unsigned __int64 v34; // [sp+1B8h] [bp-30h]@16
  char *v35; // [sp+1C0h] [bp-28h]@16
  int v36; // [sp+1C8h] [bp-20h]@16
  unsigned __int64 v37; // [sp+1D0h] [bp-18h]@4
  CUnmannedTraderRegistItemInfo *v38; // [sp+200h] [bp+18h]@1
  char v39; // [sp+208h] [bp+20h]@1

  v39 = byMaxCnt;
  v38 = pkInfo;
  v4 = &v13;
  for ( i = 120i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v37 = (unsigned __int64)&v13 ^ _security_cookie;
  if ( pkInfo && byMaxCnt )
  {
    v22 = 0i64;
    Dest = 0;
    memset(&v24, 0, 0xFFui64);
    v25 = 0;
    for ( j = 0; j < (signed int)(unsigned __int8)v39; ++j )
    {
      if ( CUnmannedTraderRegistItemInfo::IsRegist(&v38[j]) )
      {
        if ( !v25 )
          strcat_0(sData, "\r\n== UnmannedTrader Regist Info ==\r\n");
        ++v25;
        na = CUnmannedTraderRegistItemInfo::GetItemIndex(&v38[j]);
        v6 = CUnmannedTraderRegistItemInfo::GetTableCode(&v38[j]);
        v27 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)v6, na);
        v7 = CUnmannedTraderRegistItemInfo::GetStartTimePtr(&v38[j]);
        v22 = localtime_5(v7);
        if ( v22 )
        {
          v9 = v22->tm_mon + 1;
          v10 = v22->tm_year;
          LODWORD(v17) = v22->tm_sec;
          LODWORD(v16) = v22->tm_min;
          LODWORD(v15) = v22->tm_hour;
          v14 = v22->tm_mday;
          sprintf(&Dest, "%04d-%02d-%02d %02d:%02d:%02d", (unsigned int)(v10 + 1900), (unsigned int)v9);
        }
        else
        {
          v8 = CUnmannedTraderRegistItemInfo::GetStartTime(&v38[j]);
          sprintf(&Dest, "invalid(%u)", v8);
        }
        v29 = (unsigned __int8)CUnmannedTraderRegistItemInfo::GetSellTurm(&v38[j]);
        v30 = CUnmannedTraderRegistItemInfo::GetPrice(&v38[j]);
        v31 = CUnmannedTraderRegistItemInfo::GetETSerial(&v38[j]);
        dwLvBit = CUnmannedTraderRegistItemInfo::GetU(&v38[j]);
        v11 = CUnmannedTraderRegistItemInfo::GetTableCode(&v38[j]);
        v33 = DisplayItemUpgInfo((unsigned __int8)v11, dwLvBit);
        v34 = CUnmannedTraderRegistItemInfo::GetD(&v38[j]);
        v35 = v27->m_strCode;
        v36 = (unsigned __int8)CUnmannedTraderRegistItemInfo::GetStorageIndex(&v38[j]);
        v12 = CUnmannedTraderRegistItemInfo::GetRegistSerial(&v38[j]);
        v21 = v29;
        v20 = &Dest;
        v19 = v30;
        v18 = v31;
        v17 = v33;
        v16 = v34;
        v15 = v35;
        v14 = v36;
        sprintf(sBuf, "%u : reg(%u) storageinx(%u) %s_%u_@%s[%u] price(%u) regtime(%s) sellturm(%u) \r\n", j, v12);
        strcat_0(sData, sBuf);
      }
    }
    if ( v25 )
      strcat_0(sData, "================================\r\n\r\n");
  }
  else
  {
    v14 = (unsigned __int8)byMaxCnt;
    sprintf_s(sBuf, 0x2800ui64, "\r\n== UnmannedTrader Regist Info pkInfo(%p) byMacCnt(%u) ==\r\n", pkInfo);
    strcat_s(sData, 0x4E20ui64, sBuf);
  }
}
