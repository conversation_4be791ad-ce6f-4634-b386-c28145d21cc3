/*
 * Function: ??$read@Utable@lua_tinker@@@lua_tinker@@YA?AUtable@0@PEAUlua_State@@H@Z
 * Address: 0x140445700
 */

lua_tinker::table *__fastcall lua_tinker::read<lua_tinker::table>(lua_tinker::table *result, struct lua_State *L, int index)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@4
  lua_tinker::table *v8; // [sp+40h] [bp+8h]@1

  v8 = result;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = 0;
  lua_tinker::table::table(v8, L, index);
  return v8;
}
