/*
 * Function: ?SendMsg_Notify_Get_Golden_Box@CPlayer@@QEAAXEKPEADPEAU_db_con@_STORAGE_LIST@@_N@Z
 * Address: 0x1400E9100
 */

void __fastcall CPlayer::SendMsg_Notify_Get_Golden_Box(CPlayer *this, char byBoxType, unsigned int dwCharSerial, char *szCharName, _STORAGE_LIST::_db_con *pItem, bool bCircle)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-A8h]@1
  char szMsg; // [sp+38h] [bp-70h]@4
  char v10; // [sp+39h] [bp-6Fh]@4
  unsigned __int16 v11; // [sp+3Ah] [bp-6Eh]@4
  char v12; // [sp+3Ch] [bp-6Ch]@4
  unsigned int v13; // [sp+3Dh] [bp-6Bh]@4
  char Dest; // [sp+41h] [bp-67h]@4
  char pbyType; // [sp+74h] [bp-34h]@4
  char v16; // [sp+75h] [bp-33h]@4
  int j; // [sp+84h] [bp-24h]@6
  CPlayer *v18; // [sp+88h] [bp-20h]@9
  unsigned __int64 v19; // [sp+98h] [bp-10h]@4
  CPlayer *v20; // [sp+B0h] [bp+8h]@1

  v20 = this;
  v6 = &v8;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v19 = (unsigned __int64)&v8 ^ _security_cookie;
  szMsg = byBoxType;
  v10 = pItem->m_byTableCode;
  v12 = pItem->m_dwDur;
  v11 = pItem->m_wItemIndex;
  v13 = dwCharSerial;
  strcpy_0(&Dest, szCharName);
  pbyType = 13;
  v16 = -107;
  if ( bCircle )
  {
    CGameObject::CircleReport((CGameObject *)&v20->vfptr, &pbyType, &szMsg, 26, 1);
  }
  else
  {
    for ( j = 0; j < 2532; ++j )
    {
      v18 = &g_Player + j;
      if ( v18 )
      {
        if ( v18->m_bLive )
          CNetProcess::LoadSendMsg(unk_1414F2088, v18->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x1Au);
      }
    }
  }
}
