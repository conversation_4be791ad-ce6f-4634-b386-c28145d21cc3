/*
 * Function: j_??$_Uninit_move@PEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAV12@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@YAPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@PEAV10@00AEAV?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@0@U_Swap_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140005696
 */

std::basic_string<char,std::char_traits<char>,std::allocator<char> > *__fastcall std::_Uninit_move<std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_First, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_Last, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_Dest, std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *_Al, std::_Swap_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
