/*
 * Function: ?NotifyBattleResult@CNormalGuildBattle@GUILD_BATTLE@@QEAAXE@Z
 * Address: 0x1403E6A90
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::NotifyBattleResult(GUILD_BATTLE::CNormalGuildBattle *this, char byResult)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@4
  char *v5; // rax@4
  char v6; // al@9
  char v7; // al@10
  __int64 v8; // [sp+0h] [bp-B8h]@1
  char pbyType; // [sp+34h] [bp-84h]@4
  char v10; // [sp+35h] [bp-83h]@4
  char szMsg; // [sp+58h] [bp-60h]@4
  char Dest; // [sp+59h] [bp-5Fh]@4
  char v13; // [sp+6Ah] [bp-4Eh]@4
  CPlayer *v14; // [sp+88h] [bp-30h]@4
  int j; // [sp+90h] [bp-28h]@4
  int v16; // [sp+A0h] [bp-18h]@9
  int v17; // [sp+A4h] [bp-14h]@10
  unsigned __int64 v18; // [sp+A8h] [bp-10h]@4
  GUILD_BATTLE::CNormalGuildBattle *v19; // [sp+C0h] [bp+8h]@1

  v19 = this;
  v2 = &v8;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v18 = (unsigned __int64)&v8 ^ _security_cookie;
  pbyType = 27;
  v10 = 89;
  szMsg = byResult;
  v4 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v19->m_pkRed);
  strcpy_0(&Dest, v4);
  v5 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v19->m_pkBlue);
  strcpy_0(&v13, v5);
  v14 = 0i64;
  for ( j = 0; j < 2532; ++j )
  {
    v14 = &g_Player + j;
    if ( v14->m_bLive )
    {
      if ( !v14->m_bInGuildBattle )
      {
        v16 = CPlayerDB::GetRaceCode(&v14->m_Param);
        v6 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildRace(&v19->m_k1P);
        if ( v16 == (unsigned __int8)v6
          || (v17 = CPlayerDB::GetRaceCode(&v14->m_Param),
              v7 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildRace(&v19->m_k2P),
              v17 == (unsigned __int8)v7) )
        {
          CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x23u);
        }
      }
    }
  }
}
