/*
 * Function: ?InitHolySystem@CHolyStoneSystem@@QEAA_NXZ
 * Address: 0x14027AC60
 */

char __fastcall CHolyStoneSystem::InitHolySystem(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  COreAmountMgr *v4; // rax@13
  COreAmountMgr *v5; // rax@17
  COreAmountMgr *v6; // rax@17
  COreAmountMgr *v7; // rax@21
  COreAmountMgr *v8; // rax@21
  __int64 v9; // [sp+0h] [bp-98h]@1
  bool v10; // [sp+20h] [bp-78h]@4
  CHolyScheduleData::__HolyScheduleNode *ScheduleNode; // [sp+28h] [bp-70h]@8
  int j; // [sp+30h] [bp-68h]@10
  __holy_stone_data *v13; // [sp+38h] [bp-60h]@12
  _stone_create_setdata Dst; // [sp+48h] [bp-50h]@12
  bool v15; // [sp+84h] [bp-14h]@16
  CHolyStoneSystem *clsHolyStoneSystem; // [sp+A0h] [bp+8h]@1

  clsHolyStoneSystem = this;
  v1 = &v9;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = 0;
  if ( CHolyStoneSystemDataMgr::LoadIni(clsHolyStoneSystem) )
  {
    if ( CHolyStoneSystemDataMgr::LoadSceduleData(&clsHolyStoneSystem->m_ScheculeData) )
    {
      ScheduleNode = CHolyScheduleData::GetIndex(&clsHolyStoneSystem->m_ScheculeData, 1);
      if ( ScheduleNode )
      {
        for ( j = 0; j < clsHolyStoneSystem->m_nHolyStoneNum; ++j )
        {
          v13 = &clsHolyStoneSystem->m_HolyStoneData[j];
          _stone_create_setdata::_stone_create_setdata(&Dst);
          Dst.m_pMap = v13->pCreateMap;
          Dst.m_nLayerIndex = 0;
          Dst.m_pRecordSet = (_base_fld *)&v13->pRec->m_dwIndex;
          memcpy_0(Dst.m_fStartPos, v13->CreateDummy.m_fCenterPos, 0xCui64);
          Dst.pDumPosition = &v13->CreateDummy;
          Dst.byMasterRace = v13->nRace;
          CHolyStone::Create(&g_Stone[j], &Dst);
        }
        v4 = COreAmountMgr::Instance();
        if ( !COreAmountMgr::LoadINI(v4) )
        {
          MyMessageBox("COreAmountMgr Data Init", "COreAmountMgr::Instance()->LoadINI() Fail!");
          ServerProgramExit("COreAmountMgr Data Init", 0);
        }
        v15 = GetPrivateProfileIntA("Schedule", "ScheduleInit", 1, ".\\Initialize\\WorldSystem.ini") != 0;
        v10 = v15;
        if ( v15 )
        {
          WritePrivateProfileStringA("Schedule", "ScheduleInit", "0", ".\\Initialize\\WorldSystem.ini");
          CHolyStoneSaveData::DefaultInit(&clsHolyStoneSystem->m_SaveData, ScheduleNode);
          v5 = COreAmountMgr::Instance();
          COreAmountMgr::InitRemainOreAmount(v5, 0xFFFFFFFF, 0xFFFFFFFF);
          v6 = COreAmountMgr::Instance();
          COreAmountMgr::InitTransferOre(v6, 0, 0);
        }
        if ( !v10
          && (!CHolyStoneSystemDataMgr::LoadStateData(&clsHolyStoneSystem->m_SaveData)
           || !CHolyStoneSystem::ContinueStartSystem(clsHolyStoneSystem)) )
        {
          CHolyStoneSaveData::DefaultInit(&clsHolyStoneSystem->m_SaveData, ScheduleNode);
          v7 = COreAmountMgr::Instance();
          COreAmountMgr::InitRemainOreAmount(v7, 0xFFFFFFFF, 0xFFFFFFFF);
          v8 = COreAmountMgr::Instance();
          COreAmountMgr::InitTransferOre(v8, 0, 0);
          v10 = 1;
        }
        if ( v10 )
          CHolyStoneSystemDataMgr::SaveStateData(&clsHolyStoneSystem->m_SaveData);
        clsHolyStoneSystem->m_bScheduleCodePre = 0;
        CHolyStoneSystem::InitQuestCash_Other(clsHolyStoneSystem);
        result = 1;
      }
      else
      {
        MyMessageBox("Error", "CHolyStoneSystem::InitHolySystem() : pFirstSchedule = m_ScheculeData.GetIndex(1) NULL!");
        result = 0;
      }
    }
    else
    {
      MyMessageBox(
        "Error",
        "CHolyStoneSystem::InitHolySystem() : CHolyStoneSystemDataMgr::LoadSceduleData( m_ScheculeData ) Fail!");
      result = 0;
    }
  }
  else
  {
    MyMessageBox("Error", "CHolyStoneSystem::InitHolySystem() : CHolyStoneSystemDataMgr::LoadIni(*this) Fail!");
    result = 0;
  }
  return result;
}
