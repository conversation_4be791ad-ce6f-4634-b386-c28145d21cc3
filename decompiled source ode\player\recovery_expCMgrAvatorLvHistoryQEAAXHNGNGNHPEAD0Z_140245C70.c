/*
 * Function: ?recovery_exp@CMgrAvatorLvHistory@@QEAAXHNGNGNHPEAD0@Z
 * Address: 0x140245C70
 */

void __fastcall CMgrAvatorLvHistory::recovery_exp(CMgrAvatorLvHistory *this, int n, long double dOldExp, unsigned __int16 wOldExpRate, long double dNewExp, unsigned __int16 wNewExpRate, long double dLossExp, int nProbPro, char *pCause, char *pszFileName)
{
  __int64 *v10; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v12; // [sp-20h] [bp-68h]@1
  long double v13; // [sp+0h] [bp-48h]@4
  int v14; // [sp+8h] [bp-40h]@4
  char *v15; // [sp+10h] [bp-38h]@4
  long double v16; // [sp+18h] [bp-30h]@4
  int v17; // [sp+20h] [bp-28h]@4
  char *v18; // [sp+28h] [bp-20h]@4
  char *v19; // [sp+30h] [bp-18h]@4
  CMgrAvatorLvHistory *v20; // [sp+50h] [bp+8h]@1

  v20 = this;
  v10 = &v12;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v10 = -858993460;
    v10 = (__int64 *)((char *)v10 + 4);
  }
  v19 = v20->m_szCurTime;
  v18 = v20->m_szCurDate;
  v17 = nProbPro;
  v16 = dLossExp;
  v15 = pCause;
  v14 = wNewExpRate;
  v13 = dNewExp;
  sprintf(sData_0, "EXP RECOV %.0f(%d) -> %.0f(%d) : %s (%.0f * %d / 100)[%s %s]\r\n\r\n", dOldExp, wOldExpRate);
  CMgrAvatorLvHistory::WriteFile(v20, pszFileName, sData_0);
}
