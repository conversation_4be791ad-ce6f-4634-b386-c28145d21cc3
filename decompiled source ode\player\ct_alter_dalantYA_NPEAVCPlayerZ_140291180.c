/*
 * Function: ?ct_alter_dalant@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140291180
 */

bool __fastcall ct_alter_dalant(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  unsigned __int32 v4; // eax@7
  __int64 v5; // [sp+0h] [bp-48h]@1
  char *EndPtr; // [sp+28h] [bp-20h]@7
  CPlayer *v7; // [sp+50h] [bp+8h]@1

  v7 = pOne;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7 )
  {
    if ( s_nWordCount < 1 )
    {
      result = CPlayer::dev_dalant(v7, 0xFFFFFFFF);
    }
    else
    {
      v4 = strtoul(s_pwszDstCheat[0], &EndPtr, 10);
      result = CPlayer::dev_dalant(v7, v4);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
