/*
 * Function: ?SelectUsedRecordNum@CItemStoreManager@@QEAA_NPEAK@Z
 * Address: 0x140349F40
 */

char __fastcall CItemStoreManager::SelectUsedRecordNum(CItemStoreManager *this, unsigned int *pdwUsedNum)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CItemStoreManager *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CRFWorldDatabase::Select_UsedLimitItemRecordNum(pkDB, pdwUsedNum) == 1 )
  {
    CItemStoreManager::Log(
      v6,
      "CItemStoreManager::SelectUsedRecordNum\r\n\t\tg_Main.m_pWorldDB->Select_UsedLimitItemRecordNum() Fail!\r\n");
    result = 0;
  }
  else
  {
    result = 1;
  }
  return result;
}
