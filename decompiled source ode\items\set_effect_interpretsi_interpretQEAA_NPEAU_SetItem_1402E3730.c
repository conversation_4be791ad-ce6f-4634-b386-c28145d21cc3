/*
 * Function: ?set_effect_interpret@si_interpret@@QEAA_NPEAU_SetItemEff_fld@@@Z
 * Address: 0x1402E3730
 */

char __fastcall si_interpret::set_effect_interpret(si_interpret *this, _SetItemEff_fld *pFld)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-58h]@1
  int v6; // [sp+20h] [bp-38h]@4
  int j; // [sp+24h] [bp-34h]@9
  char v8; // [sp+28h] [bp-30h]@9
  char v9; // [sp+29h] [bp-2Fh]@9
  char Dst; // [sp+34h] [bp-24h]@9
  char v11; // [sp+35h] [bp-23h]@9
  char v12; // [sp+36h] [bp-22h]@11
  si_interpret *v13; // [sp+60h] [bp+8h]@1
  _SetItemEff_fld *v14; // [sp+68h] [bp+10h]@1

  v14 = pFld;
  v13 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = strlen_0(pFld->m_strEffrule);
  if ( v6 >= 4 )
  {
    if ( v6 % -4 || v6 / 4 > 8 )
    {
      v13->byEffectTypeCount = 0;
      result = 1;
    }
    else
    {
      si_interpret::init(v13);
      v8 = 0;
      v9 = 0;
      Dst = 0;
      memset(&v11, 0, 2ui64);
      v6 /= 4;
      for ( j = 0; j < v6; ++j )
      {
        memset_0(&Dst, 0, 3ui64);
        memcpy_0(&Dst, &v14->m_strEffrule[4 * j], 2ui64);
        v12 = 0;
        v8 = atoi(&Dst);
        memset_0(&Dst, 0, 3ui64);
        memcpy_0(&Dst, &v14->m_strEffrule[4 * j + 2], 2ui64);
        v12 = 0;
        v9 = atoi(&Dst);
        si_effect::set_effect_count_info(&v13->effect_type[j], v8, v9);
      }
      v13->byEffectTypeCount = j;
      v13->effect_info[0].iEffectCode = v14->m_nEff1Code;
      v13->effect_info[0].fEffectValue = v14->m_fEff1Unit;
      v13->effect_info[1].iEffectCode = v14->m_nEff2Code;
      v13->effect_info[1].fEffectValue = v14->m_fEff2Unit;
      v13->effect_info[2].iEffectCode = v14->m_nEff3Code;
      v13->effect_info[2].fEffectValue = v14->m_fEff3Unit;
      v13->effect_info[3].iEffectCode = v14->m_nEff4Code;
      v13->effect_info[3].fEffectValue = v14->m_fEff4Unit;
      v13->effect_info[4].iEffectCode = v14->m_nEff5Code;
      v13->effect_info[4].fEffectValue = v14->m_fEff5Unit;
      v13->effect_info[5].iEffectCode = v14->m_nEff6Code;
      v13->effect_info[5].fEffectValue = v14->m_fEff6Unit;
      v13->effect_info[6].iEffectCode = v14->m_nEff7Code;
      v13->effect_info[6].fEffectValue = v14->m_fEff7Unit;
      v13->effect_info[7].iEffectCode = v14->m_nEff8Code;
      v13->effect_info[7].fEffectValue = v14->m_fEff8Unit;
      result = 1;
    }
  }
  else
  {
    v13->byEffectTypeCount = 0;
    result = 1;
  }
  return result;
}
