/*
 * Function: ?Init@CNormalGuildBattleManager@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403D3670
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleManager::Init(GUILD_BATTLE::CNormalGuildBattleManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleFieldList *v3; // rax@6
  char result; // al@7
  __int64 v5; // rax@13
  __int64 v6; // [sp+0h] [bp-68h]@1
  GUILD_BATTLE::CGuildBattleLogger *v7; // [sp+20h] [bp-48h]@8
  unsigned int dwID; // [sp+28h] [bp-40h]@10
  GUILD_BATTLE::CNormalGuildBattle **v9; // [sp+30h] [bp-38h]@8
  GUILD_BATTLE::CNormalGuildBattle *v10; // [sp+38h] [bp-30h]@15
  GUILD_BATTLE::CNormalGuildBattle *v11; // [sp+40h] [bp-28h]@12
  __int64 v12; // [sp+48h] [bp-20h]@4
  unsigned __int64 v13; // [sp+50h] [bp-18h]@8
  GUILD_BATTLE::CNormalGuildBattle *v14; // [sp+58h] [bp-10h]@13
  GUILD_BATTLE::CNormalGuildBattleManager *v15; // [sp+70h] [bp+8h]@1

  v15 = this;
  v1 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = -2i64;
  if ( CMainThread::IsReleaseServiceMode(&g_Main) )
  {
    GUILD_BATTLE::LIMIT_SRC_GRADE = 2;
    GUILD_BATTLE::LIMIT_DEST_GRADE = 2;
    CUnmannedTraderEnvironmentValue::Unmanned_Trader_Check_Schedule_Delay = 300000;
  }
  v3 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
  v15->m_uiMapCnt = GUILD_BATTLE::CNormalGuildBattleFieldList::GetMapCnt(v3);
  if ( v15->m_uiMapCnt )
  {
    v15->m_uiMaxBattleCnt = 46 * v15->m_uiMapCnt;
    v7 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    v13 = v15->m_uiMaxBattleCnt;
    v9 = (GUILD_BATTLE::CNormalGuildBattle **)operator new[](saturated_mul(8ui64, v13));
    v15->m_ppkNormalBattle = v9;
    if ( v15->m_ppkNormalBattle )
    {
      memset_0(v15->m_ppkNormalBattle, 0, 8i64 * v15->m_uiMaxBattleCnt);
      for ( dwID = 0; dwID < v15->m_uiMaxBattleCnt; ++dwID )
      {
        v11 = (GUILD_BATTLE::CNormalGuildBattle *)operator new(0x55B8ui64);
        if ( v11 )
        {
          GUILD_BATTLE::CNormalGuildBattle::CNormalGuildBattle(v11, dwID);
          v14 = (GUILD_BATTLE::CNormalGuildBattle *)v5;
        }
        else
        {
          v14 = 0i64;
        }
        v10 = v14;
        v15->m_ppkNormalBattle[dwID] = v14;
        if ( !v15->m_ppkNormalBattle[dwID] )
        {
          GUILD_BATTLE::CGuildBattleLogger::Log(
            v7,
            "GuildBattleManager::Init() : new CNormalGuildBattle( %u ) Fail!",
            dwID);
          return 0;
        }
        if ( !GUILD_BATTLE::CNormalGuildBattle::CreateLogger(v15->m_ppkNormalBattle[dwID]) )
        {
          GUILD_BATTLE::CGuildBattleLogger::Log(
            v7,
            "CNormalGuildBattleManager::Init() : m_ppkNormalBattle[i]->CreateLogger() Fail!",
            dwID);
          return 0;
        }
      }
      v15->m_ppkTodayBattle = v15->m_ppkNormalBattle;
      v15->m_ppkTomorrowBattle = &v15->m_ppkNormalBattle[23 * v15->m_uiMapCnt];
      result = 1;
    }
    else
    {
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v7,
        "CNormalGuildBattleManager::Init() : new CNormalGuildBattle * [%u] Fail!",
        v15->m_uiMaxBattleCnt);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
