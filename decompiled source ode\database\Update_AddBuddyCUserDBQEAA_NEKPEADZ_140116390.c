/*
 * Function: ?Update_AddBuddy@CUserDB@@QEAA_NEKPEAD@Z
 * Address: 0x140116390
 */

char __fastcall CUserDB::Update_AddBuddy(CUserDB *this, char bySlotIndex, unsigned int dwAdderSerial, char *pwszAdderName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  CUserDB *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8->m_AvatorData.dbBuddy.m_List[(unsigned __int8)bySlotIndex].dwSerial = dwAdderSerial;
  strcpy_0(v8->m_AvatorData.dbBuddy.m_List[(unsigned __int8)bySlotIndex].wszName, pwszAdderName);
  v8->m_bDataUpdate = 1;
  return 1;
}
