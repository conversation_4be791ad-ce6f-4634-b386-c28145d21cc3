/*
 * Function: ?AddTrunkGold@CPlayerDB@@QEAAXK@Z
 * Address: 0x14010C330
 */

void __fastcall CPlayerDB::AddTrunkGold(CPlayerDB *this, unsigned int dwPush)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  double v4; // [sp+0h] [bp-18h]@1
  CPlayerDB *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v2 = (__int64 *)&v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = v5->m_dTrunkGold + (double)(signed int)dwPush;
  if ( v4 > 500000.0 || v5->m_dTrunkGold > v4 )
    v4 = DOUBLE_500000_0;
  v5->m_dTrunkGold = v4;
}
