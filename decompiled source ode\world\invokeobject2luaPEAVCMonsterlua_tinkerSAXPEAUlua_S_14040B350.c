/*
 * Function: ?invoke@?$object2lua@PEAVCMonster@@@lua_tinker@@SAXPEAUlua_State@@PEAVCMonster@@@Z
 * Address: 0x14040B350
 */

void __fastcall lua_tinker::object2lua<CMonster *>::invoke(lua_tinker::object2lua<CMonster *> *this, struct lua_State *L, CMonster *val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  const char *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-28h]@1
  struct lua_State *La; // [sp+30h] [bp+8h]@1

  La = (struct lua_State *)this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  lua_tinker::ptr2lua<CMonster>::invoke(La, (CMonster *)L);
  v5 = lua_tinker::class_name<CMonster>::name(0i64);
  lua_tinker::meta_push(La, v5);
  lua_setmetatable(La, 4294967294i64);
}
