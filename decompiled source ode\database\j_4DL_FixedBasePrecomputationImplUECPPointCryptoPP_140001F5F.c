/*
 * Function: j_??4?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@CryptoPP@@QEAAAEAV01@AEBV01@@Z
 * Address: 0x140001F5F
 */

CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint> *__fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::operator=(CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint> *this, CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint> *__that)
{
  return CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::operator=(this, __that);
}
