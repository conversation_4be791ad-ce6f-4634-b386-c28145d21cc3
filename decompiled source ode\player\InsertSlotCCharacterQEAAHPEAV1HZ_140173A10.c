/*
 * Function: ?InsertSlot@CCharacter@@QEAAHPEAV1@H@Z
 * Address: 0x140173A10
 */

signed __int64 __fastcall CCharacter::InsertSlot(CCharacter *this, CCharacter *p, int pos)
{
  signed __int64 result; // rax@2

  if ( this->m_AroundSlot[pos] == p )
  {
    result = 1i64;
  }
  else if ( this->m_AroundSlot[pos] )
  {
    result = 0i64;
  }
  else
  {
    this->m_AroundSlot[pos] = p;
    ++this->m_AroundNum;
    result = 1i64;
  }
  return result;
}
