/*
 * Function: ??$make_heap@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@@std@@YAXV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@0@Z
 * Address: 0x140603360
 */

int std::make_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>()
{
  signed __int64 v0; // rax@1
  __int64 v1; // rax@2
  __int64 v2; // rax@2
  __int64 v3; // rax@2
  __int64 v4; // rax@2
  char v6; // [sp+20h] [bp-E8h]@2
  char *v7; // [sp+40h] [bp-C8h]@2
  char v8; // [sp+48h] [bp-C0h]@2
  char *v9; // [sp+68h] [bp-A0h]@2
  char v10; // [sp+70h] [bp-98h]@2
  char *v11; // [sp+90h] [bp-78h]@2
  char v12; // [sp+98h] [bp-70h]@2
  char *v13; // [sp+B8h] [bp-50h]@2
  __int64 v14; // [sp+C0h] [bp-48h]@1
  __int64 v15; // [sp+C8h] [bp-40h]@2
  __int64 v16; // [sp+D0h] [bp-38h]@2
  __int64 v17; // [sp+D8h] [bp-30h]@2
  __int64 v18; // [sp+E0h] [bp-28h]@2
  __int64 v19; // [sp+E8h] [bp-20h]@2
  __int64 v20; // [sp+F0h] [bp-18h]@2
  __int64 v21; // [sp+F8h] [bp-10h]@2

  v14 = -2i64;
  LODWORD(v0) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-();
  if ( v0 > 1 )
  {
    v7 = &v6;
    v9 = &v8;
    v11 = &v10;
    v13 = &v12;
    v1 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)&v6);
    v15 = v1;
    LODWORD(v2) = std::_Val_type<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(v1);
    v16 = v2;
    v3 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v9);
    v17 = v3;
    LODWORD(v4) = std::_Dist_type<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(v3);
    v18 = v4;
    v19 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v11);
    v20 = v19;
    v21 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v13);
    std::_Make_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,__int64,CryptoPP::MeterFilter::MessageRange>(
      v21,
      v20,
      v18,
      v16);
  }
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
}
