/*
 * Function: ?pc_AlterItemSlotRequest@CPlayer@@QEAAXEPEAU__list@_alter_item_slot_request_clzo@@@Z
 * Address: 0x1400FD570
 */

void __fastcall CPlayer::pc_AlterItemSlotRequest(CPlayer *this, char byNum, _alter_item_slot_request_clzo::__list *pList)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@5
  _STORAGE_LIST::_db_con *v7; // [sp+28h] [bp-10h]@8
  CPlayer *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1
  _alter_item_slot_request_clzo::__list *v10; // [sp+50h] [bp+18h]@1

  v10 = pList;
  v9 = byNum;
  v8 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v8->m_pUserDB )
  {
    for ( j = 0; j < (unsigned __int8)v9; ++j )
    {
      v7 = _STORAGE_LIST::GetPtrFromSerial(v8->m_Param.m_pStoragePtr[v10[j].byStorageIndex], v10[j].dwItemSerial);
      if ( v7 )
      {
        v7->m_byClientIndex = v10[j].byClientSlotIndex;
        CUserDB::Update_ItemSlot(v8->m_pUserDB, v10[j].byStorageIndex, v7->m_byStorageIndex, v10[j].byClientSlotIndex);
      }
    }
  }
}
