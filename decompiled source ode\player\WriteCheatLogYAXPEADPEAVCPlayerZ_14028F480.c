/*
 * Function: ?WriteCheatLog@@YAXPEADPEAVCPlayer@@@Z
 * Address: 0x14028F480
 */

void __fastcall WriteCheatLog(char *pwszCommand, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@5
  __int64 v5; // [sp+0h] [bp-558h]@1
  char Dest; // [sp+30h] [bp-528h]@4
  char v7; // [sp+31h] [bp-527h]@4
  int v8; // [sp+534h] [bp-24h]@7
  unsigned __int64 v9; // [sp+540h] [bp-18h]@4
  char *lpwStr; // [sp+560h] [bp+8h]@1

  lpwStr = pwszCommand;
  v2 = &v5;
  for ( i = 340i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  Dest = 0;
  memset(&v7, 0, 0x4FFui64);
  if ( pOne )
  {
    v4 = CPlayerDB::GetCharNameA(&pOne->m_Param);
    sprintf(&Dest, "[ %s ] >> ", v4);
  }
  else
  {
    sprintf(&Dest, "[ GM tool ] >> ");
  }
  v8 = strlen_0(&Dest);
  W2M(lpwStr, &Dest + v8, 1280 - v8);
  CLogFile::Write(&s_logCheat, &Dest);
}
