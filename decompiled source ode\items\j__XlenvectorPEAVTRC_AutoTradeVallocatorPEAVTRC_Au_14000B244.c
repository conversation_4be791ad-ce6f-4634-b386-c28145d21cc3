/*
 * Function: j_?_<PERSON><PERSON>@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@KAXXZ
 * Address: 0x14000B244
 */

void __fastcall __noreturn std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_<PERSON>len(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this)
{
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_<PERSON><PERSON>(this);
}
