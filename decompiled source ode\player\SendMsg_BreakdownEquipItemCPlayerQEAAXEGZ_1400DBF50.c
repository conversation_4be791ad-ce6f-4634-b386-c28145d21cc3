/*
 * Function: ?SendMsg_BreakdownEquipItem@CPlayer@@QEAAXEG@Z
 * Address: 0x1400DBF50
 */

void __fastcall CPlayer::SendMsg_BreakdownEquipItem(CPlayer *this, char byPartIndex, unsigned __int16 wSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  char szMsg[2]; // [sp+38h] [bp-50h]@4
  unsigned int v7; // [sp+3Ah] [bp-4Eh]@4
  __int16 v8; // [sp+3Eh] [bp-4Ah]@4
  char v9; // [sp+40h] [bp-48h]@4
  unsigned __int16 v10; // [sp+41h] [bp-47h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v12; // [sp+65h] [bp-23h]@4
  CPlayer *v13; // [sp+90h] [bp+8h]@1
  char v14; // [sp+98h] [bp+10h]@1
  unsigned __int16 v15; // [sp+A0h] [bp+18h]@1

  v15 = wSerial;
  v14 = byPartIndex;
  v13 = this;
  v3 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  *(_WORD *)szMsg = v13->m_ObjID.m_wIndex;
  v7 = v13->m_dwObjSerial;
  v8 = CPlayer::GetVisualVer(v13);
  v9 = v14;
  v10 = v15;
  pbyType = 5;
  v12 = 19;
  CGameObject::CircleReport((CGameObject *)&v13->vfptr, &pbyType, szMsg, 11, 1);
}
