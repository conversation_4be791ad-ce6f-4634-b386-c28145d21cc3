/*
 * Function: ?_complete_tsk_cash_rollback@CashDbWorker@@IEAAXPEAVTask@@@Z
 * Address: 0x1402EFE80
 */

void __fastcall CashDbWorker::_complete_tsk_cash_rollback(CashDbWorker *this, Task *pkTsk)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  int nCash; // [sp+20h] [bp-58h]@10
  char *pFileName; // [sp+28h] [bp-50h]@10
  char *v7; // [sp+30h] [bp-48h]@11
  int v8; // [sp+38h] [bp-40h]@11
  char *v9; // [sp+40h] [bp-38h]@4
  CPlayer *v10; // [sp+48h] [bp-30h]@4
  char *szRet; // [sp+50h] [bp-28h]@4
  int j; // [sp+58h] [bp-20h]@4
  CLogFile *v13; // [sp+60h] [bp-18h]@11
  CashDbWorker *v14; // [sp+80h] [bp+8h]@1

  v14 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = Task::GetTaskBuf(pkTsk);
  v10 = CashDbWorker::_get_player(v14, *((_WORD *)v9 + 4), *((_DWORD *)v9 + 1));
  szRet = 0i64;
  for ( j = 0; j < *((_DWORD *)v9 + 8); ++j )
  {
    if ( v9[48 * j + 144] )
      szRet = "FAILED";
    else
      szRet = "SUCCEED";
    if ( v10 )
    {
      CPlayer::SetCashAmount(v10, *((_DWORD *)v9 + 9));
      pFileName = v10->m_szItemHistoryFileName;
      nCash = *(_DWORD *)&v9[48 * j + 148];
      CMgrAvatorItemHistory::rollback_cashitem(
        &CPlayer::s_MgrItemHistory,
        szRet,
        *(_QWORD *)&v9[48 * j + 112],
        &v9[48 * j + 120],
        nCash,
        v10->m_szItemHistoryFileName);
    }
    else
    {
      v13 = &v14->_kLogger[1];
      v8 = *(_DWORD *)&v9[48 * j + 148];
      v7 = &v9[48 * j + 120];
      pFileName = *(char **)&v9[48 * j + 112];
      nCash = *((_DWORD *)v9 + 1);
      CLogFile::Write(&v14->_kLogger[1], "ROLLBACK_%s AC:%u AV:%u UID:%I64u ICODE:%s Cash:%d", szRet, *(_DWORD *)v9);
    }
  }
}
