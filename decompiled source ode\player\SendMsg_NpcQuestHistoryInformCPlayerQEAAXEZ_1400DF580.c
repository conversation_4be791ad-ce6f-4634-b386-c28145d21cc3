/*
 * Function: ?SendMsg_NpcQuestHistoryInform@CPlayer@@QEAAXE@Z
 * Address: 0x1400DF580
 */

void __fastcall CPlayer::SendMsg_NpcQuestHistoryInform(CPlayer *this, char bySlotIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-A8h]@1
  char *Source; // [sp+30h] [bp-78h]@4
  char szMsg; // [sp+48h] [bp-60h]@4
  char Dest; // [sp+49h] [bp-5Fh]@4
  char pbyType; // [sp+74h] [bp-34h]@4
  char v9; // [sp+75h] [bp-33h]@4
  unsigned __int64 v10; // [sp+90h] [bp-18h]@4
  CPlayer *v11; // [sp+B0h] [bp+8h]@1

  v11 = this;
  v2 = &v4;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = (unsigned __int64)&v4 ^ _security_cookie;
  Source = v11->m_QuestMgr.m_pQuestData->m_History[(unsigned __int8)bySlotIndex].szQuestCode;
  szMsg = bySlotIndex;
  strcpy_0(&Dest, Source);
  pbyType = 24;
  v9 = 19;
  CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, &szMsg, 9u);
}
