/*
 * Function: SQLConfigDriver
 * Address: 0x1404DABB4
 */

int __fastcall SQLConfigDriver(HWND__ *hwndParent, unsigned __int16 fRequest, const char *lpszDriver, const char *lpszArgs, char *lpszMsg, unsigned __int16 cbMsgMax, unsigned __int16 *pcbMsgOut)
{
  HWND__ *v7; // rbp@1
  const char *v8; // rbx@1
  const char *v9; // rdi@1
  unsigned __int16 v10; // si@1
  __int64 (__cdecl *v11)(); // rax@1
  int result; // eax@2

  v7 = hwndParent;
  v8 = lpszArgs;
  v9 = lpszDriver;
  v10 = fRequest;
  v11 = ODBC___GetSetupProc("SQLConfigDriver");
  if ( v11 )
    result = ((int (__fastcall *)(HWND__ *, _QWORD, const char *, const char *))v11)(v7, v10, v9, v8);
  else
    result = 0;
  return result;
}
