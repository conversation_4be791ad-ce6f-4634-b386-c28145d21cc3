/*
 * Function: ?post_storage@CMgrAvatorItemHistory@@QEAAXPEAVCPostStorage@@PEAD@Z
 * Address: 0x14023E880
 */

void __fastcall CMgrAvatorItemHistory::post_storage(CMgrAvatorItemHistory *this, CPostStorage *pStorage, char *pFileName)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@10
  __int64 v6; // [sp+0h] [bp-158h]@1
  const char *v7; // [sp+20h] [bp-138h]@4
  const char *v8; // [sp+28h] [bp-130h]@4
  const char *v9; // [sp+30h] [bp-128h]@4
  char *v10; // [sp+38h] [bp-120h]@4
  char *v11; // [sp+40h] [bp-118h]@4
  const char *v12; // [sp+48h] [bp-110h]@4
  const char *v13; // [sp+50h] [bp-108h]@4
  char szTran[2]; // [sp+68h] [bp-F0h]@4
  char v15; // [sp+6Ah] [bp-EEh]@4
  char v16[2]; // [sp+98h] [bp-C0h]@4
  char v17; // [sp+9Ah] [bp-BEh]@4
  char DstBuf[2]; // [sp+D0h] [bp-88h]@4
  char v19; // [sp+D2h] [bp-86h]@4
  int v20; // [sp+114h] [bp-44h]@4
  int nIndex; // [sp+118h] [bp-40h]@5
  CPostData *v22; // [sp+120h] [bp-38h]@7
  _base_fld *v23; // [sp+128h] [bp-30h]@10
  const char *v24; // [sp+138h] [bp-20h]@13
  unsigned __int64 v25; // [sp+140h] [bp-18h]@4
  CMgrAvatorItemHistory *v26; // [sp+160h] [bp+8h]@1
  CPostStorage *v27; // [sp+168h] [bp+10h]@1
  char *pszFileName; // [sp+170h] [bp+18h]@1

  pszFileName = pFileName;
  v27 = pStorage;
  v26 = this;
  v3 = &v6;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v25 = (unsigned __int64)&v6 ^ _security_cookie;
  *(_WORD *)szTran = 0;
  memset(&v15, 0, 0x13ui64);
  *(_WORD *)v16 = 0;
  memset(&v17, 0, 0xFui64);
  *(_WORD *)DstBuf = 0;
  memset(&v19, 0, 0x3Eui64);
  sData[0] = 0;
  strcat_s(sData, 0x4E20ui64, "\r\n\t============\r\n\r\n");
  v20 = CPostStorage::GetSize(v27);
  v13 = "[Gold]";
  v12 = "[Item]";
  v11 = "[Title]";
  v10 = "[Sender]";
  v9 = "[State]";
  v8 = "[PostSerial]";
  v7 = "[No]";
  sprintf_s(
    sBuf,
    0x2800ui64,
    "POST STORAGE >> POST NUM = %d\r\n\r\n\t%s%14s%11s%18s%17s%37s%15s\r\n",
    (unsigned int)v20);
  strcat_s(sData, 0x4E20ui64, sBuf);
  if ( v20 > 0 )
  {
    for ( nIndex = 0; nIndex < 50; ++nIndex )
    {
      v22 = CPostStorage::GetPostDataFromInx(v27, nIndex);
      if ( v22 && (unsigned __int8)CPostData::GetState(v22) != 255 )
      {
        if ( _INVENKEY::IsFilled(&v22->m_Key) )
        {
          v23 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v22->m_Key.byTableCode, v22->m_Key.wItemIndex);
          v5 = DisplayItemUpgInfo(v22->m_Key.byTableCode, v22->m_dwUpt);
          v9 = (const char *)v22->m_lnUID;
          v8 = v5;
          v7 = (const char *)v22->m_dwDur;
          sprintf_s(DstBuf, 0x40ui64, "%s_%I64u_@%s[%I64u]", v23->m_strCode);
        }
        else
        {
          sprintf_s(DstBuf, 0x40ui64, "NoItem");
        }
        W2M(v22->m_wszTitle, szTran, 0x15u);
        W2M(v22->m_wszSendName, v16, 0x11u);
        if ( v22->m_byState == 1 )
          v24 = "Read";
        else
          v24 = "NotRead";
        LODWORD(v12) = v22->m_dwGold;
        v11 = DstBuf;
        v10 = szTran;
        v9 = v16;
        v8 = v24;
        LODWORD(v7) = v22->m_dwPSSerial;
        sprintf_s(sBuf, 0x2800ui64, "\t%3d %13u %10s %17s %22s %34s %10u\r\n", v22->m_nNumber);
        strcat_s(sData, 0x4E20ui64, sBuf);
      }
    }
  }
  strcat_s(sData, 0x4E20ui64, "\r\n\t============\r\n\r\n");
  CMgrAvatorItemHistory::WriteFile(v26, pszFileName, sData);
}
