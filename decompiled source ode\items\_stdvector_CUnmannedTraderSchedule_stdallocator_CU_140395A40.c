/*
 * Function: _std::vector_CUnmannedTraderSchedule_std::allocator_CUnmannedTraderSchedule___::erase_::_1_::dtor$1
 * Address: 0x140395A40
 */

void __fastcall std::vector_CUnmannedTraderSchedule_std::allocator_CUnmannedTraderSchedule___::erase_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(*(std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > **)(a2 + 96));
}
