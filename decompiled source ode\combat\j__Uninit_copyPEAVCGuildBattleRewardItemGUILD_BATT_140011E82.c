/*
 * Function: j_??$_Uninit_copy@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@std@@YAPEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@00AEAV?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140011E82
 */

GUILD_BATTLE::CGuildBattleRewardItem *__fastcall std::_Uninit_copy<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(GUILD_BATTLE::CGuildBattleRewardItem *_First, GUILD_BATTLE::CGuildBattleRewardItem *_Last, GUILD_BATTLE::CGuildBattleRewardItem *_Dest, std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
