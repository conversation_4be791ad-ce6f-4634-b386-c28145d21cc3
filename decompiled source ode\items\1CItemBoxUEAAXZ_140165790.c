/*
 * Function: ??1CItemBox@@UEAA@XZ
 * Address: 0x140165790
 */

void __fastcall CItemBox::~CItemBox(CItemBox *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  void *v4; // [sp+20h] [bp-18h]@5
  CItemBox *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->vfptr = (CGameObjectVtbl *)&CItemBox::`vftable';
  if ( v5->m_szThrowerItemHistoryFileName )
  {
    v4 = v5->m_szThrowerItemHistoryFileName;
    operator delete[](v4);
    v5->m_szThrowerItemHistoryFileName = 0i64;
  }
  CGameObject::~CGameObject((CGameObject *)&v5->vfptr);
}
