/*
 * Function: ?GetMaxDMGSFContCount@CMonster@@QEAAHXZ
 * Address: 0x140147050
 */

__int64 __fastcall CMonster::GetMaxDMGSFContCount(CMonster *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // [sp+0h] [bp-18h]@1
  CMonster *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = (int *)&v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  v4 = *(_DWORD *)&v5->m_pRecordSet[25].m_strCode[0];
  if ( (signed int)v4 > 8 )
    v4 = 8;
  if ( (v4 & 0x80000000) != 0 )
    v4 = 0;
  return v4;
}
