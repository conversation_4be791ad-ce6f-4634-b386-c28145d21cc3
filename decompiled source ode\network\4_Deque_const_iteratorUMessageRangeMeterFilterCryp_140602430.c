/*
 * Function: ??4?$_Deque_const_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@QEAAAEAV01@AEBV01@@Z
 * Address: 0x140602430
 */

__int64 __fastcall std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator=(__int64 a1, __int64 a2)
{
  __int64 v3; // [sp+30h] [bp+8h]@1
  __int64 v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  std::_Ranit<CryptoPP::MeterFilter::MessageRange,__int64,CryptoPP::MeterFilter::MessageRange const *,CryptoPP::MeterFilter::MessageRange const &>::operator=();
  *(_QWORD *)(v3 + 16) = *(_QWORD *)(v4 + 16);
  *(_QWORD *)(v3 + 24) = *(_QWORD *)(v4 + 24);
  return v3;
}
