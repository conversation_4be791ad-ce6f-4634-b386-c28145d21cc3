/*
 * Function: ??1MessageQueue@CryptoPP@@UEAA@XZ
 * Address: 0x1406552F0
 */

void __fastcall CryptoPP::MessageQueue::~MessageQueue(CryptoPP::MessageQueue *this)
{
  CryptoPP::MessageQueue *v1; // [sp+40h] [bp+8h]@1

  v1 = this;
  std::deque<unsigned int,std::allocator<unsigned int>>::~deque<unsigned int,std::allocator<unsigned int>>((char *)this + 168);
  std::deque<unsigned __int64,std::allocator<unsigned __int64>>::~deque<unsigned __int64,std::allocator<unsigned __int64>>((char *)v1 + 112);
  CryptoPP::ByteQueue::~ByteQueue((CryptoPP::ByteQueue *)((char *)v1 + 32));
  CryptoPP::AutoSignaling<CryptoPP::BufferedTransformation>::~AutoSignaling<CryptoPP::BufferedTransformation>((CryptoPP::BufferedTransformation *)v1);
}
