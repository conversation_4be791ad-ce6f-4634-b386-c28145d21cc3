/*
 * Function: ?Set@CAggroNode@@QEAAXPEAVCCharacter@@@Z
 * Address: 0x140161810
 */

void __fastcall CAggroNode::Set(CAggroNode *this, CCharacter *pCharacter)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CAggroNode *v5; // [sp+30h] [bp+8h]@1
  CCharacter *v6; // [sp+38h] [bp+10h]@1

  v6 = pCharacter;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CAggroNode::Init(v5);
  if ( v6 )
  {
    v5->m_pCharacter = v6;
    v5->m_dwObjectSerial = v6->m_dwObjSerial;
  }
}
