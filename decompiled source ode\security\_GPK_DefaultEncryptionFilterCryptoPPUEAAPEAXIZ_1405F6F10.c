/*
 * Function: ??_GPK_DefaultEncryptionFilter@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x1405F6F10
 */

CryptoPP::PK_DefaultEncryptionFilter *__fastcall CryptoPP::PK_DefaultEncryptionFilter::`scalar deleting destructor'(CryptoPP::PK_DefaultEncryptionFilter *a1, int a2)
{
  CryptoPP::PK_DefaultEncryptionFilter *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::PK_DefaultEncryptionFilter::~PK_DefaultEncryptionFilter(a1);
  if ( v4 & 1 )
    operator delete((void *)v3);
  return v3;
}
