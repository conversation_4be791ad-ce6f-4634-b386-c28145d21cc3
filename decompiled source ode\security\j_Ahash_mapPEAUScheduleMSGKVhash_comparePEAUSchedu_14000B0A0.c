/*
 * Function: j_??A?$hash_map@PEAUScheduleMSG@@KV?$hash_compare@PEAUScheduleMSG@@U?$less@PEAUScheduleMSG@@@std@@@stdext@@V?$allocator@U?$pair@QEAUScheduleMSG@@K@std@@@std@@@stdext@@QEAAAEAKAEBQEAUScheduleMSG@@@Z
 * Address: 0x14000B0A0
 */

unsigned int *__fastcall stdext::hash_map<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::operator[](stdext::hash_map<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > > *this, ScheduleMSG *const *_Keyval)
{
  return stdext::hash_map<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::operator[](
           this,
           _Keyval);
}
