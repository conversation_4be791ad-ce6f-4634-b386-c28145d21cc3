/*
 * Function: ??0CSUItemSystem@@QEAA@XZ
 * Address: 0x1402E3D70
 */

void __fastcall CSUItemSystem::CSUItemSystem(CSUItemSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  __int64 v4; // [sp+30h] [bp-18h]@4
  CSUItemSystem *ptr; // [sp+50h] [bp+8h]@1

  ptr = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  `eh vector constructor iterator'(
    ptr,
    0xB0ui64,
    1,
    (void (__cdecl *)(void *))CRecordData::CRecordData,
    (void (__cdecl *)(void *))CRecordData::~CRecordData);
  CSetItemType::CSetItemType(&ptr->m_SetItemType);
  CSUItemSystem::Class_Init(ptr);
}
