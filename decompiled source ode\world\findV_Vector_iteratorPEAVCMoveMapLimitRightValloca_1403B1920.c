/*
 * Function: ??$find@V?$_Vector_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@PEAVCMoveMapLimitRight@@@std@@YA?AV?$_Vector_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@0@V10@0AEBQEAVCMoveMapLimitRight@@@Z
 * Address: 0x1403B1920
 */

std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *__fastcall std::find<std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>,CMoveMapLimitRight *>(std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *result, std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_First, std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_Last, CMoveMapLimitRight *const *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v6; // rax@4
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v7; // rax@4
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v8; // rax@4
  __int64 v10; // [sp+0h] [bp-B8h]@1
  char v11; // [sp+20h] [bp-98h]@4
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v12; // [sp+38h] [bp-80h]@4
  char v13; // [sp+40h] [bp-78h]@4
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v14; // [sp+58h] [bp-60h]@4
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > v15; // [sp+60h] [bp-58h]@4
  int v16; // [sp+78h] [bp-40h]@4
  __int64 v17; // [sp+80h] [bp-38h]@4
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v18; // [sp+88h] [bp-30h]@4
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v19; // [sp+90h] [bp-28h]@4
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v20; // [sp+98h] [bp-20h]@4
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v21; // [sp+A0h] [bp-18h]@4
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v22; // [sp+A8h] [bp-10h]@4
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v23; // [sp+C0h] [bp+8h]@1
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v24; // [sp+C8h] [bp+10h]@1
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *__that; // [sp+D0h] [bp+18h]@1
  CMoveMapLimitRight *const *v26; // [sp+D8h] [bp+20h]@1

  v26 = _Val;
  __that = _Last;
  v24 = _First;
  v23 = result;
  v4 = &v10;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v17 = -2i64;
  v16 = 0;
  v12 = (std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v11;
  v14 = (std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v13;
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(
    (std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v11,
    _Last);
  v18 = v6;
  v19 = v6;
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(
    v14,
    v24);
  v20 = v7;
  v8 = std::_Find<std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>,CMoveMapLimitRight *>(
         &v15,
         v7,
         v19,
         v26);
  v21 = v8;
  v22 = v8;
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator=(v24, v8);
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(&v15);
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(
    v23,
    v24);
  v16 |= 1u;
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(v24);
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(__that);
  return v23;
}
