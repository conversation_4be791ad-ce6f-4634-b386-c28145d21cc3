/*
 * Function: ??1CPlayer@@UEAA@XZ
 * Address: 0x140048050
 */

void __fastcall CPlayer::~CPlayer(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  void *v4; // [sp+20h] [bp-18h]@5
  __int64 v5; // [sp+28h] [bp-10h]@4
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = -2i64;
  v6->vfptr = (CGameObjectVtbl *)&CPlayer::`vftable';
  if ( CPlayer::s_pnLinkForceItemToEffect )
  {
    v4 = CPlayer::s_pnLinkForceItemToEffect;
    operator delete[](CPlayer::s_pnLinkForceItemToEffect);
    CPlayer::s_pnLinkForceItemToEffect = 0i64;
  }
  CMyTimer::~CMyTimer(&v6->m_tmrPremiumPVPInform);
  CMyTimer::~CMyTimer(&v6->m_tmrEffectEndTime);
  CMyTimer::~CMyTimer(&v6->m_tmrEffectStartTime);
  CCouponMgr::~CCouponMgr(&v6->m_kPcBangCoupon);
  CPvpCashPoint::~CPvpCashPoint(&v6->m_kPvpCashPoint);
  CMyTimer::~CMyTimer(&v6->m_tmrAuraSkill);
  CPvpPointLimiter::~CPvpPointLimiter(&v6->m_kPvpPointLimiter);
  CMyTimer::~CMyTimer(&v6->m_tmrGroupTargeting);
  CMyTimer::~CMyTimer(&v6->m_tmrAccumPlayingTime);
  CExtPotionBuf::~CExtPotionBuf(&v6->m_PotionBufUse);
  CPotionParam::~CPotionParam(&v6->m_PotionParam);
  CMyTimer::~CMyTimer(&v6->m_tmrBilling);
  CMyTimer::~CMyTimer(&v6->m_tmrIntervalSec);
  CMyTimer::~CMyTimer(&v6->m_tmrSiegeTime);
  CEquipItemSFAgent::~CEquipItemSFAgent(&v6->EquipItemSFAgent);
  CSetItemEffect::~CSetItemEffect(&v6->m_clsSetItem);
  ItemCombineMgr::~ItemCombineMgr(&v6->m_ItemCombineMgr);
  CQuestMgr::~CQuestMgr(&v6->m_QuestMgr);
  _BUDDY_LIST::~_BUDDY_LIST(&v6->m_pmBuddy);
  CRealMoveRequestDelayChecker::~CRealMoveRequestDelayChecker(&v6->m_kMoveDelayChecker);
  CPlayerDB::~CPlayerDB(&v6->m_Param);
  CCharacter::~CCharacter((CCharacter *)&v6->vfptr);
}
