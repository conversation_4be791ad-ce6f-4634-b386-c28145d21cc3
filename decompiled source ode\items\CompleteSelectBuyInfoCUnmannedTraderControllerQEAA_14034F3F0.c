/*
 * Function: ?CompleteSelectBuyInfo@CUnmannedTraderController@@QEAAXEPEAD@Z
 * Address: 0x14034F3F0
 */

void __fastcall CUnmannedTraderController::CompleteSelectBuyInfo(CUnmannedTraderController *this, char byRet, char *pLoadData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // al@10
  CUnmannedTraderTaxRateManager *v6; // rax@11
  unsigned __int16 v7; // ax@28
  CUnmannedTraderUserInfoTable *v8; // rax@29
  __int64 v9; // [sp+0h] [bp-5D8h]@1
  char *pQryData; // [sp+20h] [bp-5B8h]@12
  int nSize; // [sp+28h] [bp-5B0h]@12
  int v12; // [sp+30h] [bp-5A8h]@12
  int v13; // [sp+38h] [bp-5A0h]@12
  int v14; // [sp+40h] [bp-598h]@12
  char *v15; // [sp+50h] [bp-588h]@4
  unsigned __int16 Dst; // [sp+70h] [bp-568h]@4
  unsigned int dwOwner; // [sp+74h] [bp-564h]@4
  char v18; // [sp+78h] [bp-560h]@4
  char v19; // [sp+79h] [bp-55Fh]@4
  char v20; // [sp+7Ah] [bp-55Eh]@4
  char v21; // [sp+7Bh] [bp-55Dh]@4
  char v22; // [sp+88h] [bp-550h]@4
  unsigned __int8 v23; // [sp+89h] [bp-54Fh]@4
  int v24; // [sp+90h] [bp-548h]@7
  int v25; // [sp+94h] [bp-544h]@7
  char v26[16]; // [sp+98h] [bp-540h]@7
  int v27; // [sp+A8h] [bp-530h]@7
  char v28[17]; // [sp+ACh] [bp-52Ch]@7
  char v29[3]; // [sp+BDh] [bp-51Bh]@7
  int v30[7]; // [sp+C0h] [bp-518h]@11
  int v31[223]; // [sp+DCh] [bp-4FCh]@7
  CPlayer *v32; // [sp+458h] [bp-180h]@4
  int v33; // [sp+460h] [bp-178h]@4
  unsigned __int64 ui64AddMoney; // [sp+464h] [bp-174h]@4
  int j; // [sp+46Ch] [bp-16Ch]@4
  CPlayer *v36; // [sp+470h] [bp-168h]@20
  _unmannedtrader_buy_item_result_zocl v37; // [sp+490h] [bp-148h]@25
  int k; // [sp+584h] [bp-54h]@25
  char pbyType; // [sp+594h] [bp-44h]@28
  char v40; // [sp+595h] [bp-43h]@28
  __int64 v41; // [sp+5B0h] [bp-28h]@11
  __int64 v42; // [sp+5B8h] [bp-20h]@11
  CPlayer *v43; // [sp+5C0h] [bp-18h]@22
  unsigned __int64 v44; // [sp+5C8h] [bp-10h]@4
  CUnmannedTraderController *v45; // [sp+5E0h] [bp+8h]@1

  v45 = this;
  v3 = &v9;
  for ( i = 372i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v44 = (unsigned __int64)&v9 ^ _security_cookie;
  v15 = pLoadData;
  memset_0(&Dst, 0, 0x3E0ui64);
  Dst = *(_WORD *)v15;
  dwOwner = *((_DWORD *)v15 + 1);
  v18 = v15[8];
  v22 = v15[13];
  v23 = v15[14];
  v19 = v15[10];
  v20 = v15[11];
  v21 = v15[12];
  v32 = 0i64;
  v33 = 0;
  LODWORD(ui64AddMoney) = 0;
  BYTE4(ui64AddMoney) = 0;
  for ( j = 0; j < (unsigned __int8)v15[14]; ++j )
  {
    *(&v24 + 24 * j) = *(_DWORD *)&v15[72 * j + 16];
    *(&v25 + 24 * j) = *(_DWORD *)&v15[72 * j + 24];
    strcpy_s(&v26[96 * j], 0xDui64, &v15[72 * j + 48]);
    *(&v27 + 24 * j) = *(_DWORD *)&v15[72 * j + 44];
    strcpy_s(&v28[96 * j], 0x11ui64, &v15[72 * j + 61]);
    v31[24 * j] = *(_DWORD *)&v15[72 * j + 20];
    v29[96 * j] = v15[72 * j + 84];
    if ( !v29[96 * j] )
    {
      v32 = GetPtrPlayerFromSerial(&g_Player, 2532, *(_DWORD *)&v15[72 * j + 24]);
      if ( v32 && !v32->m_bLive )
      {
        v5 = CPlayerDB::GetRaceSexCode(&v32->m_Param);
        v15[72 * j + 28] = v5;
        *(_DWORD *)&v15[72 * j + 32] = CPlayerDB::GetDalant(&v32->m_Param);
        *(_DWORD *)&v15[72 * j + 36] = CPlayerDB::GetGuildSerial(&v32->m_Param);
        v15[72 * j + 40] = v32->m_byUserDgr;
      }
      v41 = 72i64 * j;
      v42 = 72i64 * j;
      v6 = CUnmannedTraderTaxRateManager::Instance();
      v30[24 * j] = CUnmannedTraderTaxRateManager::GetTax(
                      v6,
                      v15[8],
                      *(_DWORD *)&v15[v42 + 36],
                      *(_DWORD *)&v15[v41 + 20]);
      if ( *(_DWORD *)&v15[72 * j + 20] <= (unsigned int)v30[24 * j] )
      {
        v14 = v30[24 * j];
        v13 = *(_DWORD *)&v15[72 * j + 20];
        v12 = *(_DWORD *)&v15[72 * j + 32];
        nSize = *(_DWORD *)&v15[72 * j + 36];
        LODWORD(pQryData) = dwOwner;
        CUnmannedTraderController::Log(
          v45,
          "CUnmannedTraderController::CompleteSelectBuyInfo(...) Exceed Tax Price!\r\n"
          "\t\t dwRegistSerial(%u) dwSeller(%u)\r\n"
          "\t\t dwBuyer(%u) dwGuildSerial(%u) dwCurDalant(%u)\r\n"
          "\t\t dwPrice(%u) dwTax(%u)\r\n",
          (unsigned int)*(&v24 + 24 * j),
          (unsigned int)*(&v25 + 24 * j));
        v30[24 * j] = (signed int)ffloor((float)*(signed int *)&v15[72 * j + 20] * 0.*********);
      }
      LODWORD(ui64AddMoney) = *(_DWORD *)&v15[72 * j + 20] - v30[24 * j] + ui64AddMoney;
      if ( CanAddMoneyForMaxLimMoney((unsigned int)ui64AddMoney, *(_DWORD *)&v15[72 * j + 32]) )
      {
        if ( (unsigned __int8)v15[9] == (unsigned __int8)v15[72 * j + 40] )
          ++BYTE4(ui64AddMoney);
        else
          v29[96 * j] = 35;
      }
      else
      {
        v29[96 * j] = 34;
      }
    }
  }
  if ( (signed int)BYTE4(ui64AddMoney) <= 0 )
  {
    v36 = GetPtrPlayerFromSerial(&g_Player, 2532, *((_DWORD *)v15 + 1));
    if ( v36 && v36->m_bOper )
      v43 = v36;
    else
      v43 = 0i64;
    v36 = v43;
    if ( v43 )
    {
      _unmannedtrader_buy_item_result_zocl::_unmannedtrader_buy_item_result_zocl(&v37);
      memset_0(&v37, 0, 0xE6ui64);
      v37.byRetCode = -1;
      v37.byNum = v15[14];
      for ( k = 0; k < v23; ++k )
      {
        v37.List[k].byRet = v29[96 * k];
        v37.List[k].dwPrice = v31[24 * k];
      }
      v37.dwLeftDalant = CPlayerDB::GetDalant(&v36->m_Param);
      pbyType = 30;
      v40 = 31;
      v7 = _unmannedtrader_buy_item_result_zocl::size(&v37);
      CNetProcess::LoadSendMsg(unk_1414F2088, v36->m_ObjID.m_wIndex, &pbyType, &v37.byRetCode, v7);
    }
    v8 = CUnmannedTraderUserInfoTable::Instance();
    CUnmannedTraderUserInfoTable::ClearRequest(v8, Dst, dwOwner);
  }
  else
  {
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 65, (char *)&Dst, 992);
  }
}
