/*
 * Function: ?IsExistRequestMoveCharacterList@CUserDB@@QEAAEK@Z
 * Address: 0x14011BAE0
 */

char __fastcall CUserDB::IsExistRequestMoveCharacterList(CUserDB *this, unsigned int dwCharSerial)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CUserDB *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  for ( j = 0; j < 3; ++j )
  {
    if ( v6->m_dwRequestMoveCharacterSerialList[j] || v6->m_dwTournamentCharacterSerialList[j] )
    {
      if ( v6->m_dwRequestMoveCharacterSerialList[j] == dwCharSerial )
        return 1;
      if ( v6->m_dwTournamentCharacterSerialList[j] == dwCharSerial )
        return 2;
    }
  }
  return 0;
}
