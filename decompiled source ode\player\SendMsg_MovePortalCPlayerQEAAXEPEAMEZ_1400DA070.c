/*
 * Function: ?SendMsg_MovePortal@CPlayer@@QEAAXEPEAME@Z
 * Address: 0x1400DA070
 */

void __fastcall CPlayer::SendMsg_MovePortal(CPlayer *this, char byMapIndex, float *pfStartPos, char byZoneCode)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  char v8; // [sp+39h] [bp-4Fh]@4
  char Dst; // [sp+3Ah] [bp-4Eh]@4
  char v10; // [sp+46h] [bp-42h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v12; // [sp+65h] [bp-23h]@4
  CPlayer *v13; // [sp+90h] [bp+8h]@1
  char v14; // [sp+A8h] [bp+20h]@1

  v14 = byZoneCode;
  v13 = this;
  v4 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = 0;
  v8 = byMapIndex;
  memcpy_0(&Dst, pfStartPos, 0xCui64);
  v10 = v14;
  pbyType = 8;
  v12 = 2;
  CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, &szMsg, 0xFu);
}
