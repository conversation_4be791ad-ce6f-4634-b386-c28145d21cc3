/*
 * Function: ?GetMaxExponent@DL_GroupParameters_IntegerBased@CryptoPP@@UEBA?AVInteger@2@XZ
 * Address: 0x1406321C0
 */

struct CryptoPP::Integer *__fastcall CryptoPP::DL_GroupParameters_IntegerBased::GetMaxExponent(CryptoPP::DL_GroupParameters_IntegerBased *this, struct CryptoPP::Integer *retstr)
{
  CryptoPP::Integer *v2; // rax@1
  int v3; // eax@1
  unsigned int v4; // edx@1
  unsigned int v5; // eax@1
  struct CryptoPP::Integer *v6; // rax@1
  CryptoPP::Integer *v7; // rax@1
  CryptoPP::Integer *v8; // rax@1
  CryptoPP::Integer *v9; // rax@1
  CryptoPP::Integer v11; // [sp+20h] [bp-C8h]@1
  CryptoPP::Integer b; // [sp+48h] [bp-A0h]@1
  CryptoPP::Integer result; // [sp+70h] [bp-78h]@1
  int v14; // [sp+98h] [bp-50h]@1
  __int64 v15; // [sp+A0h] [bp-48h]@1
  __int64 v16; // [sp+A8h] [bp-40h]@1
  int v17; // [sp+B0h] [bp-38h]@1
  struct CryptoPP::Integer *v18; // [sp+B8h] [bp-30h]@1
  CryptoPP::Integer *v19; // [sp+C0h] [bp-28h]@1
  CryptoPP::ASN1ObjectVtbl *v20; // [sp+C8h] [bp-20h]@1
  CryptoPP::Integer *v21; // [sp+D0h] [bp-18h]@1
  CryptoPP::Integer *v22; // [sp+D8h] [bp-10h]@1
  CryptoPP::DL_GroupParameters_IntegerBased *v23; // [sp+F0h] [bp+8h]@1
  CryptoPP::Integer *v24; // [sp+F8h] [bp+10h]@1

  v24 = retstr;
  v23 = this;
  v15 = -2i64;
  v14 = 0;
  CryptoPP::Integer::Integer(&b, 1);
  v16 = *(_QWORD *)&v23[-1].gap48[8];
  v17 = (*(int (__fastcall **)(signed __int64))(v16 + 48))((signed __int64)&v23[-1].gap48[8]);
  LODWORD(v2) = (*(int (__fastcall **)(signed __int64))(*(_QWORD *)&v23[-1].gap48[8] + 32i64))((signed __int64)&v23[-1].gap48[8]);
  v3 = CryptoPP::Integer::BitCount(v2);
  v5 = CryptoPP::DiscreteLogWorkFactor((CryptoPP *)(unsigned int)(v3 * v17), v4);
  v6 = CryptoPP::Integer::Power2(&v11, 2 * v5);
  v18 = v6;
  v19 = v6;
  v20 = v23->vfptr;
  LODWORD(v7) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters_IntegerBased *))v20[2].__vecDelDtor)(v23);
  v8 = CryptoPP::operator-(&result, v7, &b);
  v21 = v8;
  v22 = v8;
  v9 = CryptoPP::STDMIN<CryptoPP::Integer>(v8, v19);
  CryptoPP::Integer::Integer(v24, v9);
  v14 |= 1u;
  CryptoPP::Integer::~Integer(&result);
  CryptoPP::Integer::~Integer(&v11);
  CryptoPP::Integer::~Integer(&b);
  return v24;
}
