/*
 * Function: ?GetQuery@CUnmannedTraderSortType@@QEBAPEBDXZ
 * Address: 0x14035F6F0
 */

char *__fastcall CUnmannedTraderSortType::GetQuery(CUnmannedTraderSortType *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // [sp+0h] [bp-18h]@1
  CUnmannedTraderSortType *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = (__int64 *)&v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5->m_szQuery[0] )
    v4 = v5->m_szQuery;
  else
    v4 = 0i64;
  return v4;
}
