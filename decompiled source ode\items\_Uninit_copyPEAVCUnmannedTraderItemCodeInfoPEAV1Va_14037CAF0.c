/*
 * Function: ??$_Uninit_copy@PEAVCUnmannedTraderItemCodeInfo@@PEAV1@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@YAPEAVCUnmannedTraderItemCodeInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderItemCodeInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14037CAF0
 */

CUnmannedTraderItemCodeInfo *__fastcall std::_Uninit_copy<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *,std::allocator<CUnmannedTraderItemCodeInfo>>(CUnmannedTraderItemCodeInfo *_First, CUnmannedTraderItemCodeInfo *_Last, CUnmannedTraderItemCodeInfo *_Dest, std::allocator<CUnmannedTraderItemCodeInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-38h]@1
  CUnmannedTraderItemCodeInfo *v10; // [sp+20h] [bp-18h]@4
  __int64 v11; // [sp+28h] [bp-10h]@4
  CUnmannedTraderItemCodeInfo *_Val; // [sp+40h] [bp+8h]@1
  CUnmannedTraderItemCodeInfo *v13; // [sp+48h] [bp+10h]@1
  CUnmannedTraderItemCodeInfo *_Ptr; // [sp+50h] [bp+18h]@1
  std::allocator<CUnmannedTraderItemCodeInfo> *v15; // [sp+58h] [bp+20h]@1

  v15 = _Al;
  _Ptr = _Dest;
  v13 = _Last;
  _Val = _First;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v11 = -2i64;
  v10 = _Dest;
  while ( _Val != v13 )
  {
    std::allocator<CUnmannedTraderItemCodeInfo>::construct(v15, _Ptr, _Val);
    ++_Ptr;
    ++_Val;
  }
  return _Ptr;
}
