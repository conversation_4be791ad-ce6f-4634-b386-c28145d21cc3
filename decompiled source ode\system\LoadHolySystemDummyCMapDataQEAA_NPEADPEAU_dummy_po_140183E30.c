/*
 * Function: ?LoadHolySystemDummy@CMapData@@QEAA_NPEADPEAU_dummy_position@@@Z
 * Address: 0x140183E30
 */

bool __fastcall CMapData::LoadHolySystemDummy(CMapData *this, char *pszDummyCode, _dummy_position *pPos)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-D8h]@1
  char Dest; // [sp+30h] [bp-A8h]@4
  unsigned __int64 v8; // [sp+C0h] [bp-18h]@4
  CMapData *v9; // [sp+E0h] [bp+8h]@1
  char *pszDummyCodea; // [sp+E8h] [bp+10h]@1
  _dummy_position *pDummyPos; // [sp+F0h] [bp+18h]@1

  pDummyPos = pPos;
  pszDummyCodea = pszDummyCode;
  v9 = this;
  v3 = &v6;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = (unsigned __int64)&v6 ^ _security_cookie;
  sprintf(&Dest, ".\\map\\%s\\%s.spt", v9->m_pMapSet->m_strCode, v9->m_pMapSet->m_strCode);
  if ( CDummyPosTable::FindDummy(&Dest, pszDummyCodea, pDummyPos) )
  {
    if ( CMapData::ConvertLocal(v9, pDummyPos) )
      result = CMapData::CheckCenterPosDummy(v9, pDummyPos) != 0;
    else
      result = 0;
  }
  else
  {
    MyMessageBox(
      "CMapData Error",
      "CDummyPosTable::FindDummy(%s, %s) == false",
      v9->m_pMapSet->m_strCode,
      pszDummyCodea);
    result = 0;
  }
  return result;
}
