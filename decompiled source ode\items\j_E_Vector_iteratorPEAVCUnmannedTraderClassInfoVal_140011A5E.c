/*
 * Function: j_??E?$_Vector_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@QEAAAEAV01@XZ
 * Address: 0x140011A5E
 */

std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *__fastcall std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator++(std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this)
{
  return std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator++(this);
}
