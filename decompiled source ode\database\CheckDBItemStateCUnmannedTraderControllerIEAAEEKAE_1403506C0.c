/*
 * Function: ?CheckDBItemState@CUnmannedTraderController@@IEAAEEKAEAE0@Z
 * Address: 0x1403506C0
 */

char __fastcall CUnmannedTraderController::CheckDBItemState(CUnmannedTraderController *this, char byType, unsigned int dwRegistSerial, char *byState, char *byProcRet)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-38h]@1
  char v9; // [sp+20h] [bp-18h]@4
  int v10; // [sp+24h] [bp-14h]@8
  char *byStatea; // [sp+58h] [bp+20h]@1

  byStatea = byState;
  v5 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v9 = CRFWorldDatabase::Select_UnmannedTraderItemState(pkDB, byType, dwRegistSerial, byState);
  if ( v9 == 2 )
  {
    *byProcRet = 8;
    result = 0;
  }
  else if ( v9 == 1 )
  {
    *byProcRet = 38;
    result = 24;
  }
  else
  {
    v10 = (unsigned __int8)*byStatea;
    switch ( v10 )
    {
      case 1:
      case 2:
        *byProcRet = 0;
        break;
      case 3:
      case 4:
      case 10:
        *byProcRet = 40;
        break;
      case 5:
      case 6:
      case 7:
        *byProcRet = 41;
        break;
      case 0:
      case 8:
      case 9:
        *byProcRet = 45;
        break;
      default:
        break;
    }
    result = 0;
  }
  return result;
}
