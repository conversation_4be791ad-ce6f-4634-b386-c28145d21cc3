/*
 * Function: j_?_Insert_n@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@IEAAXV?$_Vector_iterator@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@2@_KAEBVCMoveMapLimitRightInfo@@@Z
 * Address: 0x14000B460
 */

void __fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Insert_n(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *_Where, unsigned __int64 _Count, CMoveMapLimitRightInfo *_Val)
{
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Insert_n(this, _Where, _Count, _Val);
}
