/*
 * Function: ?SendMsg_SkillResult@CPlayer@@QEAAXEPEAU_CHRID@@EH@Z
 * Address: 0x1400DF950
 */

void __fastcall CPlayer::SendMsg_SkillResult(CPlayer *this, char byErrCode, _CHRID *pidDst, char bySkillIndex, int nSFLv)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-C8h]@1
  char szMsg; // [sp+34h] [bp-94h]@4
  char pbyType; // [sp+54h] [bp-74h]@4
  char v10; // [sp+55h] [bp-73h]@4
  char v11; // [sp+78h] [bp-50h]@9
  char v12; // [sp+79h] [bp-4Fh]@9
  unsigned __int16 v13; // [sp+7Ah] [bp-4Eh]@9
  unsigned int v14; // [sp+7Ch] [bp-4Ch]@9
  char Dst; // [sp+80h] [bp-48h]@9
  char v16; // [sp+87h] [bp-41h]@9
  char v17; // [sp+88h] [bp-40h]@9
  char v18; // [sp+A4h] [bp-24h]@9
  char v19; // [sp+A5h] [bp-23h]@9
  CPlayer *v20; // [sp+D0h] [bp+8h]@1
  char v21; // [sp+D8h] [bp+10h]@1
  _CHRID *Src; // [sp+E0h] [bp+18h]@1
  char v23; // [sp+E8h] [bp+20h]@1

  v23 = bySkillIndex;
  Src = pidDst;
  v21 = byErrCode;
  v20 = this;
  v5 = &v7;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  szMsg = byErrCode;
  pbyType = 17;
  v10 = 5;
  CNetProcess::LoadSendMsg(unk_1414F2088, v20->m_ObjID.m_wIndex, &pbyType, &szMsg, 2u);
  if ( (!v21 || v21 == 100)
    && (v20->m_nCirclePlayerNum <= 500 || v20->m_ObjID.m_byID != Src->byID || v20->m_dwObjSerial != Src->dwSerial) )
  {
    v11 = v21;
    memcpy_0(&Dst, Src, 7ui64);
    v12 = v20->m_ObjID.m_byID;
    v13 = v20->m_ObjID.m_wIndex;
    v14 = v20->m_dwObjSerial;
    v16 = v23;
    v17 = nSFLv;
    v18 = 17;
    v19 = 6;
    CGameObject::CircleReport((CGameObject *)&v20->vfptr, &v18, &v11, 18, 0);
  }
}
