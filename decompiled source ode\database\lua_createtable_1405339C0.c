/*
 * Function: lua_createtable
 * Address: 0x1405339C0
 */

int __fastcall lua_createtable(__int64 a1, unsigned int a2, unsigned int a3)
{
  unsigned int v3; // esi@1
  unsigned int v4; // ebp@1
  __int64 v5; // rdi@1
  __int64 v6; // rbx@3
  __int64 v7; // rax@3

  v3 = a3;
  v4 = a2;
  v5 = a1;
  if ( *(_QWORD *)(*(_QWORD *)(a1 + 32) + 120i64) >= *(_QWORD *)(*(_QWORD *)(a1 + 32) + 112i64) )
    luaC_step(a1);
  v6 = *(_QWORD *)(v5 + 16);
  LODWORD(v7) = luaH_new(v5, v4, v3);
  *(_QWORD *)v6 = v7;
  *(_DWORD *)(v6 + 8) = 5;
  *(_QWORD *)(v5 + 16) += 16i64;
  return v7;
}
