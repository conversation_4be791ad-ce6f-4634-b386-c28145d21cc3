/*
 * Function: ?SetRankRate@CPlayer@@QEAAXGK@Z
 * Address: 0x140064850
 */

void __fastcall CPlayer::SetRankRate(CPlayer *this, unsigned __int16 wRankRate, unsigned int dwRank)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v6->m_Param.m_dbChar.m_wRankRate != wRankRate || v6->m_Param.m_dbChar.m_dwRank != dwRank )
  {
    v6->m_Param.m_dbChar.m_wRankRate = wRankRate;
    v6->m_Param.m_dbChar.m_dwRank = dwRank;
    CPlayer::SendMsg_AlterPvPRank(v6, wRankRate, dwRank);
  }
}
