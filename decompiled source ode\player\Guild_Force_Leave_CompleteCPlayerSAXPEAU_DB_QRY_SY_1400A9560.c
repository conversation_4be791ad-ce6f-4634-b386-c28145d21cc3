/*
 * Function: ?Guild_Force_Leave_Complete@CPlayer@@SAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1400A9560
 */

void __fastcall CPlayer::Guild_Force_Leave_Complete(_DB_QRY_SYN_DATA *pData)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CGuildBattleController *v3; // rax@10
  CGuildRoomSystem *v4; // rax@11
  CGuildRoomSystem *v5; // rax@12
  CPlayer::CashChangeStateFlag *v6; // rax@13
  char *v7; // rax@15
  __int64 v8; // [sp+0h] [bp-C8h]@1
  int nMemNum; // [sp+20h] [bp-A8h]@15
  char *pszFileName; // [sp+28h] [bp-A0h]@15
  bool b<PERSON>unish; // [sp+30h] [bp-98h]@15
  CGuild *v12; // [sp+40h] [bp-88h]@4
  unsigned int dwMemberSerial; // [sp+48h] [bp-80h]@4
  int v14; // [sp+4Ch] [bp-7Ch]@4
  int v15; // [sp+50h] [bp-78h]@4
  char *v16; // [sp+58h] [bp-70h]@4
  char v17; // [sp+60h] [bp-68h]@4
  _guild_member_info *v18; // [sp+68h] [bp-60h]@8
  CPlayer *v19; // [sp+70h] [bp-58h]@10
  CPlayer::CashChangeStateFlag v20; // [sp+78h] [bp-50h]@13
  CUserDB *v21; // [sp+80h] [bp-48h]@11
  int n; // [sp+88h] [bp-40h]@11
  CGuild *v23; // [sp+90h] [bp-38h]@11
  CUserDB *v24; // [sp+98h] [bp-30h]@12
  int v25; // [sp+A0h] [bp-28h]@12
  CGuild *v26; // [sp+A8h] [bp-20h]@12
  char *v27; // [sp+B0h] [bp-18h]@15
  int v28; // [sp+B8h] [bp-10h]@15
  _DB_QRY_SYN_DATA *v29; // [sp+D0h] [bp+8h]@1

  v29 = pData;
  v1 = &v8;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = 0i64;
  dwMemberSerial = -1;
  v14 = -1;
  v15 = -1;
  v16 = v29->m_sData;
  dwMemberSerial = *(_DWORD *)&v29->m_sData[0];
  v14 = *(_DWORD *)&v29->m_sData[4];
  v15 = *(_DWORD *)&v29->m_sData[8];
  v17 = 0;
  v12 = &g_Guild[v14];
  if ( CGuild::IsFill(v12) && v12->m_dwSerial == v15 )
    v17 = 1;
  if ( v17 )
  {
    v18 = CGuild::GetMemberFromSerial(v12, dwMemberSerial);
    if ( v18 )
    {
      if ( v18->pPlayer )
      {
        v3 = CGuildBattleController::Instance();
        CGuildBattleController::LeaveGuild(v3, v18->pPlayer);
        v19 = v18->pPlayer;
        if ( v19->m_Param.m_pGuild )
        {
          v21 = v19->m_pUserDB;
          n = v19->m_ObjID.m_wIndex;
          v23 = v19->m_Param.m_pGuild;
          v4 = CGuildRoomSystem::GetInstance();
          if ( CGuildRoomSystem::IsGuildRoomMemberIn(v4, v23->m_dwSerial, n, v21->m_dwSerial) )
          {
            v24 = v19->m_pUserDB;
            v25 = v19->m_ObjID.m_wIndex;
            v26 = v19->m_Param.m_pGuild;
            v5 = CGuildRoomSystem::GetInstance();
            CGuildRoomSystem::SetPlayerOut(v5, v26->m_dwSerial, v25, v24->m_dwSerial);
          }
        }
        v18->pPlayer->m_Param.m_bGuildLock = 0;
        v18->pPlayer->m_Param.m_pGuild = 0i64;
        v18->pPlayer->m_Param.m_pGuildMemPtr = 0i64;
        CPlayer::CashChangeStateFlag::CashChangeStateFlag(&v20, 0);
        CPlayer::UpdateVisualVer(v18->pPlayer, (CPlayer::CashChangeStateFlag)v6->0);
        CPlayer::SendMsg_GuildJoinOtherInform(v18->pPlayer);
        CPlayer::SetLastAttBuff(v18->pPlayer, 0);
        if ( v16[24] )
          CPlayer::SendMsg_GuildForceLeaveBoradori(v18->pPlayer);
        v27 = v12->m_szHistoryFileName;
        v28 = v12->m_nMemberNum - 1;
        v7 = CPlayerDB::GetCharNameA(&v18->pPlayer->m_Param);
        bPunish = v16[24];
        pszFileName = v27;
        nMemNum = v28;
        CMgrGuildHistory::leave_member(&CGuild::s_MgrHistory, v7, v18->dwSerial, 0, v28, v27, bPunish);
      }
      CGuild::SendMsg_LeaveMember(v12, dwMemberSerial, 0, v16[24]);
      CGuild::PopMember(v12, dwMemberSerial);
    }
  }
}
