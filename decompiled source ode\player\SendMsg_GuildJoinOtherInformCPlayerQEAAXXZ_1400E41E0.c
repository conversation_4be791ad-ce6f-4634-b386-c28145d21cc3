/*
 * Function: ?SendMsg_GuildJoinOtherInform@CPlayer@@QEAAXXZ
 * Address: 0x1400E41E0
 */

void __fastcall CPlayer::SendMsg_GuildJoinOtherInform(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-88h]@1
  char szMsg[4]; // [sp+38h] [bp-50h]@4
  unsigned int v5; // [sp+3Ch] [bp-4Ch]@5
  unsigned __int16 v6; // [sp+40h] [bp-48h]@7
  char pbyType; // [sp+64h] [bp-24h]@7
  char v8; // [sp+65h] [bp-23h]@7
  CPlayer *v9; // [sp+90h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(_DWORD *)szMsg = v9->m_id.dwSerial;
  if ( v9->m_Param.m_pGuild )
    v5 = v9->m_Param.m_pGuild->m_dwSerial;
  else
    v5 = -1;
  v6 = v9->m_wVisualVer;
  pbyType = 27;
  v8 = 39;
  CGameObject::CircleReport((CGameObject *)&v9->vfptr, &pbyType, szMsg, 10, 0);
}
