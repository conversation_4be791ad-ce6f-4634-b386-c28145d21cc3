/*
 * Function: ?SendMsg_MissionInfo@CDarkHoleChannel@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x14026B7C0
 */

void __fastcall CDarkHoleChannel::SendMsg_MissionInfo(CDarkHoleChannel *this, CPlayer *pDst)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@16
  __int64 v5; // [sp+0h] [bp-E38h]@1
  _darkhole_mission_info_inform_zocl v6; // [sp+40h] [bp-DF8h]@4
  int j; // [sp+DE4h] [bp-54h]@7
  _dh_job_setup *v8; // [sp+DE8h] [bp-50h]@9
  _dh_mission_mgr::_if_change *v9; // [sp+DF0h] [bp-48h]@12
  char pbyType; // [sp+E04h] [bp-34h]@16
  char v11; // [sp+E05h] [bp-33h]@16
  unsigned __int64 v12; // [sp+E20h] [bp-18h]@4
  CDarkHoleChannel *v13; // [sp+E40h] [bp+8h]@1
  CPlayer *v14; // [sp+E48h] [bp+10h]@1

  v14 = pDst;
  v13 = this;
  v2 = &v5;
  for ( i = 908i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = (unsigned __int64)&v5 ^ _security_cookie;
  _darkhole_mission_info_inform_zocl::_darkhole_mission_info_inform_zocl(&v6);
  strcpy_0(v6.szDescirptCode, v13->m_MissionMgr.pCurMssionPtr->szDescirptCode);
  if ( _dh_mission_mgr::GetLimMSecTime(&v13->m_MissionMgr) == -1 )
    v6.dwLimTimeSec = -1;
  else
    v6.dwLimTimeSec = _dh_mission_mgr::GetLimMSecTime(&v13->m_MissionMgr) / 0x3E8;
  v6.dwPassTimeSec = (timeGetTime() - v13->m_MissionMgr.dwMissionStartTime) / 0x3E8;
  v6.byOrder = v13->m_MissionMgr.pCurMssionPtr->byJobOrder;
  v6.byJobNum = v13->m_MissionMgr.pCurMssionPtr->nEmbJobSetupNum;
  for ( j = 0; j < v13->m_MissionMgr.pCurMssionPtr->nEmbJobSetupNum; ++j )
  {
    v8 = v13->m_MissionMgr.pCurMssionPtr->EmbJobSetup[j];
    v6.Job[j].byType = v8->eventType;
    v6.Job[j].byTableCode = v8->JobSetup.byTable;
    if ( v8->JobSetup.pEventFld )
      v6.Job[j].wRecordIndex = v8->JobSetup.pEventFld->m_dwIndex;
    else
      v6.Job[j].wRecordIndex = -1;
    v6.Job[j].zNeedCount = v8->JobSetup.nEventCount;
    strcpy_0(v6.Job[j].szDescirptCode, v8->szDescirptCode);
    v6.Job[j].bPass = v13->m_MissionMgr.Count[j].bPass;
    v6.Job[j].wCurCount = v13->m_MissionMgr.Count[j].nCount;
    v6.Job[j].bDisable = 1;
    v9 = _dh_mission_mgr::SearchCurMissionCont(&v13->m_MissionMgr);
    if ( v9 )
    {
      if ( v9->pszDespt )
        strcpy_0(v6.szDescirptCode, v9->pszDespt);
    }
  }
  pbyType = 35;
  v11 = 17;
  v4 = _darkhole_mission_info_inform_zocl::size(&v6);
  CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_ObjID.m_wIndex, &pbyType, v6.szDescirptCode, v4);
}
