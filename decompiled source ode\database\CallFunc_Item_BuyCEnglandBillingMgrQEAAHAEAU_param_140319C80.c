/*
 * Function: ?CallFunc_Item_Buy@CEnglandBillingMgr@@QEAAHAEAU_param_cash_update@@HH@Z
 * Address: 0x140319C80
 */

signed __int64 __fastcall CEnglandBillingMgr::CallFunc_Item_Buy(CEnglandBillingMgr *this, _param_cash_update *rParam, int n, int nIdx)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char *v6; // rax@4
  int v7; // eax@4
  int v8; // eax@4
  CEngNetworkBillEX *v9; // rax@4
  signed __int64 result; // rax@5
  RECV_DATA *v11; // rax@7
  __int64 v12; // [sp+0h] [bp-158h]@1
  char *v13; // [sp+20h] [bp-138h]@4
  char *v14; // [sp+28h] [bp-130h]@4
  char *v15; // [sp+30h] [bp-128h]@4
  char *v16; // [sp+38h] [bp-120h]@4
  const char *v17; // [sp+40h] [bp-118h]@4
  int v18; // [sp+48h] [bp-110h]@4
  char *v19; // [sp+50h] [bp-108h]@4
  char Dst; // [sp+68h] [bp-F0h]@4
  char v21; // [sp+69h] [bp-EFh]@4
  char *DstBuf; // [sp+88h] [bp-D0h]@4
  unsigned int v23; // [sp+90h] [bp-C8h]@4
  char *Str; // [sp+98h] [bp-C0h]@4
  int v25; // [sp+A0h] [bp-B8h]@4
  char *szMsg; // [sp+A8h] [bp-B0h]@4
  char pbyType[2]; // [sp+B4h] [bp-A4h]@4
  int v28; // [sp+C4h] [bp-94h]@4
  RECV_DATA *_Val; // [sp+C8h] [bp-90h]@9
  char *v30; // [sp+D8h] [bp-80h]@4
  char *v31; // [sp+E0h] [bp-78h]@4
  char *v32; // [sp+E8h] [bp-70h]@4
  void *v33; // [sp+F0h] [bp-68h]@4
  void *v34; // [sp+F8h] [bp-60h]@4
  RECV_DATA *v35; // [sp+100h] [bp-58h]@9
  RECV_DATA *v36; // [sp+108h] [bp-50h]@6
  void *v37; // [sp+110h] [bp-48h]@9
  void *v38; // [sp+118h] [bp-40h]@9
  __int64 v39; // [sp+120h] [bp-38h]@4
  size_t v40; // [sp+128h] [bp-30h]@4
  size_t v41; // [sp+130h] [bp-28h]@4
  RECV_DATA *v42; // [sp+138h] [bp-20h]@7
  unsigned __int64 v43; // [sp+140h] [bp-18h]@4
  CEnglandBillingMgr *v44; // [sp+160h] [bp+8h]@1
  _param_cash_update *v45; // [sp+168h] [bp+10h]@1
  int v46; // [sp+170h] [bp+18h]@1
  int v47; // [sp+178h] [bp+20h]@1

  v47 = nIdx;
  v46 = n;
  v45 = rParam;
  v44 = this;
  v4 = &v12;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v39 = -2i64;
  v43 = (unsigned __int64)&v12 ^ _security_cookie;
  Dst = 0;
  memset(&v21, 0, 0x13ui64);
  v6 = inet_ntoa((struct in_addr)rParam->in_dwIP);
  strcpy_s(&Dst, 0x14ui64, v6);
  v30 = (char *)operator new[](0x200ui64);
  DstBuf = v30;
  memset_0(v30, 0, 0x200ui64);
  v7 = v45->in_item[(signed __int64)v46].in_byOverlapNum * v45->in_item[(signed __int64)v46].in_nPrice;
  v19 = &Dst;
  v18 = v7;
  v17 = "NULL";
  v16 = v45->in_item[(signed __int64)v46].in_strItemCode;
  v15 = v45->in_szAvatorName;
  v14 = v45->in_szSvrName;
  v13 = v45->in_szAcc;
  sprintf_s(DstBuf, 0x200ui64, "|%d|%s|%s|%s|%s|%s|%d|||%s\n", (unsigned int)(v47 + 1));
  v23 = strlen_0(DstBuf) + 1;
  v31 = (char *)operator new[](0xAui64);
  Str = v31;
  memset_0(v31, 0, 0xAui64);
  sprintf_s(Str, 0xAui64, "61%05d04", v23);
  v40 = strlen_0(Str);
  v8 = strlen_0(DstBuf);
  v25 = v8 + v40;
  v32 = (char *)operator new[](v8 + (signed int)v40 + 1);
  szMsg = v32;
  memset_0(v32, 0, v25 + 1);
  v13 = DstBuf;
  sprintf_s(szMsg, v25 + 1, "%s%s", Str);
  v33 = Str;
  operator delete(Str);
  v34 = DstBuf;
  operator delete(DstBuf);
  strcpy(pbyType, "\x01");
  v41 = strlen_0(szMsg);
  v9 = CTSingleton<CEngNetworkBillEX>::Instance();
  v28 = CEngNetworkBillEX::Send(v9, pbyType, szMsg, v41);
  CLogFile::Write(&v44->m_logBill, "Cash Query : %s", szMsg);
  if ( v28 )
  {
    v36 = (RECV_DATA *)operator new(0x18ui64);
    if ( v36 )
    {
      RECV_DATA::RECV_DATA(v36);
      v42 = v11;
    }
    else
    {
      v42 = 0i64;
    }
    v35 = v42;
    _Val = v42;
    v42->bResult = 0;
    _Val->dwSeq = v47 + 1;
    _Val->wType = 61;
    _Val->pData = v45;
    std::deque<RECV_DATA,std::allocator<RECV_DATA>>::push_front(&g_vRecvData, _Val);
    v37 = _Val;
    operator delete(_Val);
    v38 = szMsg;
    operator delete(szMsg);
    result = 0i64;
  }
  else
  {
    ResumeThread(m_hThread);
    CLogFile::Write(&v44->m_logBill, "Item Buy Fail.");
    result = 1i64;
  }
  return result;
}
