/*
 * Function: ??0?$_Deque_const_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@QEAA@AEBV01@@Z
 * Address: 0x140601830
 */

std::_Iterator_base *__fastcall std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>(std::_Iterator_base *a1, __int64 a2)
{
  std::_Iterator_base *v3; // [sp+30h] [bp+8h]@1
  __int64 v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  std::_Ranit<CryptoPP::MeterFilter::MessageRange,__int64,CryptoPP::MeterFilter::MessageRange const *,CryptoPP::MeterFilter::MessageRange const &>::_Ranit<CryptoPP::MeterFilter::MessageRange,__int64,CryptoPP::MeterFilter::MessageRange const *,CryptoPP::MeterFilter::MessageRange const &>(a1);
  v3[1]._Mycont = *(std::_Container_base **)(v4 + 16);
  v3[1]._Mynextiter = *(std::_Iterator_base **)(v4 + 24);
  return v3;
}
