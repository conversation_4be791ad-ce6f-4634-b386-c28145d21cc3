/*
 * Function: ?Init@CMonsterAggroMgr@@QEAAXXZ
 * Address: 0x14015DCA0
 */

void __fastcall CMonsterAggroMgr::Init(CMonsterAggroMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CMonsterAggroMgr *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 10; ++j )
    CAggroNode::Init(&v5->m_AggroPool[j]);
  v5->m_dwAggroCount = 0;
  v5->m_dwAllResetLastTime = 0;
  v5->m_dwShortRankLastTime = 0;
  v5->m_dwShortRankTimer = CMonster::GetAggroShortTime(v5->m_pMonster);
  v5->m_dwAllResetTimer = CMonster::GetAggroResetTime(v5->m_pMonster);
  v5->m_pTopDamageCharacter = 0i64;
  v5->m_pTopAggroCharacter = 0i64;
  v5->m_pKingPowerDamageCharacter = 0i64;
}
