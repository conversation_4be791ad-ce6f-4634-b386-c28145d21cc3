/*
 * Function: ?OnlyOnceInit@CMonsterHierarchy@@IEAAXPEAVCMonster@@@Z
 * Address: 0x140157300
 */

void __fastcall CMonsterHierarchy::OnlyOnceInit(CMonsterHierarchy *this, CMonster *pThis)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMonsterHierarchy *v5; // [sp+30h] [bp+8h]@1
  CMonster *v6; // [sp+38h] [bp+10h]@1

  v6 = pThis;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CMonsterHierarchy::Init(v5);
  v5->m_pThisMon = v6;
}
