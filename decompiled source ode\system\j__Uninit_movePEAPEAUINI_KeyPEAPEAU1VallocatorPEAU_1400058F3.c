/*
 * Function: j_??$_Uninit_move@PEAPEAUINI_Key@@PEAPEAU1@V?$allocator@PEAUINI_Key@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAUINI_Key@@PEAPEAU1@00AEAV?$allocator@PEAUINI_Key@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400058F3
 */

INI_Key **__fastcall std::_Uninit_move<INI_Key * *,INI_Key * *,std::allocator<INI_Key *>,std::_Undefined_move_tag>(INI_Key **_First, INI_Key **_Last, INI_Key **_Dest, std::allocator<INI_Key *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<INI_Key * *,INI_Key * *,std::allocator<INI_Key *>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _<PERSON>,
           __formal,
           a6);
}
