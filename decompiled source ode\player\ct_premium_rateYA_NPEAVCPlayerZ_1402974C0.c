/*
 * Function: ?ct_premium_rate@@YA_NPEAVCPlayer@@@Z
 * Address: 0x1402974C0
 */

char __fastcall ct_premium_rate(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v4; // [sp+0h] [bp-188h]@1
  bool bFilter[8]; // [sp+20h] [bp-168h]@7
  char *pwszMessage; // [sp+28h] [bp-160h]@7
  char byPvpGrade[8]; // [sp+30h] [bp-158h]@7
  char *pwszSender; // [sp+38h] [bp-150h]@7
  double v9; // [sp+40h] [bp-148h]@7
  char DstBuf; // [sp+60h] [bp-128h]@7
  char v11; // [sp+61h] [bp-127h]@7
  unsigned __int64 v12; // [sp+170h] [bp-18h]@4
  CPlayer *v13; // [sp+190h] [bp+8h]@1

  v13 = pOne;
  v1 = &v4;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( v13 && v13->m_bOper )
  {
    DstBuf = 0;
    memset(&v11, 0, 0xFEui64);
    v9 = (float)(PCBANG_PRIMIUM_FAVOR::PLAYER_EXP * 100.0);
    *(double *)&pwszSender = (float)(PCBANG_PRIMIUM_FAVOR::ANIMUS_EXP * 100.0);
    *(double *)byPvpGrade = (float)(PCBANG_PRIMIUM_FAVOR::BASE_MASTERY * 100.0);
    *(double *)&pwszMessage = (float)(PCBANG_PRIMIUM_FAVOR::SKILL_FORCE_MASTERY * 100.0);
    *(double *)bFilter = (float)(PCBANG_PRIMIUM_FAVOR::MINING_SPEED * 100.0);
    sprintf_s(
      &DstBuf,
      0xFFui64,
      "PC Item Looting: %f Percent\n"
      "PC Mine Speed: %f Percent\n"
      "PC Force Liver: %f Percent\n"
      "PC Mastery: %f Percent\n"
      "PC Animus Exp: %f Percent\n"
      "PC Character Exp: %f Percent\n",
      (float)(PCBANG_PRIMIUM_FAVOR::ITEM_DROP * 100.0));
    CPlayer::SendData_ChatTrans(v13, 0, 0xFFFFFFFF, -1, 0, &DstBuf, -1, 0i64);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
