/*
 * Function: ?FindPotionEffectDst@CCharacter@@QEAAHHH_NPEAV1@PEADPEAPEAV1@PEA_N@Z
 * Address: 0x140177200
 */

__int64 __fastcall CCharacter::FindPotionEffectDst(CCharacter *this, int nAreaType, int nEffectAreaVal, bool bBenefit, CCharacter *pOriDst, char *psActableDst, CCharacter **ppDsts, bool *pbPath)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  int v11; // eax@6
  int v12; // eax@7
  int v13; // eax@8
  int v14; // eax@9
  __int64 v15; // [sp+0h] [bp-58h]@1
  CCharacter **v16; // [sp+30h] [bp-28h]@6
  CCharacter **v17; // [sp+38h] [bp-20h]@7
  unsigned int v18; // [sp+40h] [bp-18h]@4
  int v19; // [sp+44h] [bp-14h]@4
  CCharacter *v20; // [sp+60h] [bp+8h]@1

  v20 = this;
  v8 = &v15;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v18 = 0;
  *ppDsts = pOriDst;
  ++v18;
  v19 = nAreaType;
  switch ( nAreaType )
  {
    case 0:
      result = v18;
      break;
    case 1:
      v16 = &ppDsts[v18];
      v11 = CCharacter::_GetAreaEffectMember(
              v20,
              pOriDst,
              bBenefit,
              nEffectAreaVal,
              pOriDst->m_fCurPos,
              psActableDst,
              v16);
      result = v11 + v18;
      break;
    case 10:
      v17 = &ppDsts[v18];
      v12 = CCharacter::_GetFlashEffectMember(
              v20,
              pOriDst,
              bBenefit,
              nEffectAreaVal,
              s_nLimitAngle_1,
              pOriDst,
              psActableDst,
              v17);
      result = v12 + v18;
      break;
    case 2:
      v13 = CCharacter::_GetPartyEffectMember(v20, pOriDst, 0, &ppDsts[v18]);
      result = v13 + v18;
      break;
    case 3:
      v14 = CCharacter::_GetPartyEffectMember(v20, pOriDst, 1, &ppDsts[v18]);
      result = v14 + v18;
      break;
    case 4:
      if ( pOriDst == v20 )
        goto LABEL_12;
      ppDsts[v18] = v20;
      pbPath[v18++] = 1;
      result = v18;
      break;
    default:
LABEL_12:
      result = v18;
      break;
  }
  return result;
}
