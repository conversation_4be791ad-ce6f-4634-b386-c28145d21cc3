/*
 * Function: _std::vector_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo___::_Insert_n_::_1_::catch$1
 * Address: 0x140379760
 */

void __fastcall __noreturn std::vector_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo___::_Insert_n_::_1_::catch_1(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Destroy(
    *(std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > **)(a2 + 256),
    (CUnmannedTraderItemCodeInfo *)(*(_QWORD *)(*(_QWORD *)(a2 + 264) + 16i64) + 72i64 * *(_QWORD *)(a2 + 272)),
    (CUnmannedTraderItemCodeInfo *)(*(_QWORD *)(*(_QWORD *)(a2 + 256) + 24i64) + 72i64 * *(_QWORD *)(a2 + 272)));
  CxxThrowException_0(0i64, 0i64);
}
