/*
 * Function: ?SearchTargetMovePos_StopTarget@CMonsterHelper@@SAHPEAVCMonster@@PEAVCCharacter@@AEAY02M@Z
 * Address: 0x1401599B0
 */

signed __int64 __usercall CMonsterHelper::SearchTargetMovePos_StopTarget@<rax>(CMonster *pMon@<rcx>, CCharacter *pTargetCharacter@<rdx>, float (*tarPos)[3]@<r8>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  float v7; // xmm0_4@7
  double v8; // xmm0_8@7
  float v9; // xmm0_4@15
  float v10; // xmm0_4@15
  float v11; // xmm0_4@16
  float v12; // xmm0_4@16
  float v13; // xmm0_4@19
  float v14; // xmm0_4@19
  __int64 v15; // [sp+0h] [bp-98h]@1
  int v16; // [sp+30h] [bp-68h]@7
  int v17; // [sp+34h] [bp-64h]@7
  float v18; // [sp+38h] [bp-60h]@7
  double v19; // [sp+40h] [bp-58h]@7
  float v20; // [sp+48h] [bp-50h]@10
  float v21; // [sp+4Ch] [bp-4Ch]@10
  int pos; // [sp+50h] [bp-48h]@10
  int v23; // [sp+54h] [bp-44h]@13
  int v24; // [sp+58h] [bp-40h]@14
  float v25; // [sp+5Ch] [bp-3Ch]@15
  float v26[3]; // [sp+68h] [bp-30h]@15
  float v27; // [sp+84h] [bp-14h]@7
  CMonster *p; // [sp+A0h] [bp+8h]@1
  CCharacter *v29; // [sp+A8h] [bp+10h]@1
  float *v30; // [sp+B0h] [bp+18h]@1

  v30 = (float *)tarPos;
  v29 = pTargetCharacter;
  p = pMon;
  v4 = &v15;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( !pTargetCharacter || !p )
    return 0i64;
  v16 = 0;
  v17 = 0;
  ((void (__fastcall *)(CMonster *))p->vfptr->GetAttackRange)(p);
  v27 = a4;
  ((void (__fastcall *)(CMonster *))p->vfptr->GetAttackRange)(p);
  v7 = v27 - (float)(0.2 * a4);
  v18 = v7;
  Get3DSqrt(v29->m_fCurPos, p->m_fCurPos);
  v19 = v7;
  v8 = v18;
  if ( v18 >= v19 )
  {
    *(float *)&v8 = v18;
    if ( v18 >= 1.0 )
      return 0i64;
  }
  CMonsterHelper::GetAngle(p->m_fCurPos, v29->m_fCurPos);
  v20 = *(float *)&v8;
  v21 = FLOAT_5_0;
  pos = (signed int)floor(*(float *)&v8 / (2.0 * 3.1415926535) * 5.0 + 0.5) - 1;
  if ( pos < 0 || pos >= 5 )
    pos = 4;
  CCharacter::RemoveSlot(v29, (CCharacter *)&p->vfptr);
  v23 = CCharacter::GetNearEmptySlot(v29, pos, v18, p->m_fCurPos, v30);
  if ( v23 < 0 )
  {
    memcpy_0(v30, v29->m_fCurPos, 0xCui64);
    v13 = v20;
    cos(v20);
    v16 = (signed int)ffloor(v13 * v18);
    v14 = v20;
    sin(v20);
    v17 = (signed int)ffloor(v14 * v18);
    *v30 = *v30 + (float)v16;
    v30[2] = v30[2] + (float)v17;
  }
  else
  {
    v24 = CCharacter::InsertSlot(v29, (CCharacter *)&p->vfptr, v23);
    if ( !v24 )
      return 0i64;
    v9 = 2.0 * 3.1415926535 * (float)(v23 + 1) / v21;
    v25 = v9;
    cos(v9);
    v16 = (signed int)ffloor(v9 * v18);
    v10 = v25;
    sin(v25);
    v17 = (signed int)ffloor(v10 * v18);
    memcpy_0(v30, v29->m_fCurPos, 0xCui64);
    *v30 = *v30 + (float)v16;
    v30[2] = v30[2] + (float)v17;
    if ( !(unsigned int)CBsp::CanYouGoThere(p->m_pCurMap->m_Level.mBsp, p->m_fCurPos, v30, (float (*)[3])v26) )
    {
      memcpy_0(v30, v29->m_fCurPos, 0xCui64);
      v11 = v20;
      cos(v20);
      v16 = (signed int)ffloor(v11 * v18);
      v12 = v20;
      sin(v20);
      v17 = (signed int)ffloor(v12 * v18);
      *v30 = *v30 + (float)v16;
      v30[2] = v30[2] + (float)v17;
    }
  }
  return 1i64;
}
