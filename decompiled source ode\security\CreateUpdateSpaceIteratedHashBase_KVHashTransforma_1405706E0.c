/*
 * Function: ?CreateUpdateSpace@?$IteratedHashBase@_KVHashTransformation@CryptoPP@@@CryptoPP@@UEAAPEAEAEA_K@Z
 * Address: 0x1405706E0
 */

__int64 __fastcall CryptoPP::IteratedHashBase<unsigned __int64,CryptoPP::HashTransformation>::CreateUpdateSpace(__int64 a1, _QWORD *a2)
{
  unsigned int v2; // eax@1
  unsigned int v3; // ST20_4@1
  __int64 v4; // rax@1
  unsigned int b; // [sp+24h] [bp-14h]@1
  __int64 v7; // [sp+40h] [bp+8h]@1
  _QWORD *v8; // [sp+48h] [bp+10h]@1

  v8 = a2;
  v7 = a1;
  b = (*(int (**)(void))(*(_QWORD *)a1 + 64i64))();
  v2 = CryptoPP::ModPowerOf2<unsigned __int64,unsigned int>((const unsigned __int64 *)(v7 + 8), &b);
  v3 = v2;
  *v8 = b - v2;
  LODWORD(v4) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v7 + 176i64))(v7);
  return v3 + v4;
}
