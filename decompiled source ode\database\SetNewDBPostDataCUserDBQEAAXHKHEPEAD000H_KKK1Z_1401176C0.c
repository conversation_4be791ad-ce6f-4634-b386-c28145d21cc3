/*
 * Function: ?SetNewDBPostData@CUserDB@@QEAAXHKHEPEAD000H_KKK1@Z
 * Address: 0x1401176C0
 */

void __fastcall CUserDB::SetNewDBPostData(CUserDB *this, int n, unsigned int dwSerial, int nNumber, char byState, char *wszSendName, char *wszRecvName, char *wszTitle, char *wszContent, int nKey, unsigned __int64 dwDur, unsigned int dwUpt, unsigned int dwGold, unsigned __int64 lnUID)
{
  __int64 *v14; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v16; // [sp+0h] [bp-28h]@1
  CUserDB *v17; // [sp+30h] [bp+8h]@1
  int v18; // [sp+38h] [bp+10h]@1

  v18 = n;
  v17 = this;
  v14 = &v16;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v14 = -858993460;
    v14 = (__int64 *)((char *)v14 + 4);
  }
  if ( n >= 0 && n < 50 )
  {
    v17->m_AvatorData.dbPostData.dbPost.m_PostList[n].dwPSSerial = dwSerial;
    v17->m_AvatorData.dbPostData.dbPost.m_PostList[n].nNumber = nNumber;
    v17->m_AvatorData.dbPostData.dbPost.m_PostList[n].byState = byState;
    v17->m_AvatorData.dbPostData.dbPost.m_PostList[n].nKey = nKey;
    v17->m_AvatorData.dbPostData.dbPost.m_PostList[n].dwDur = dwDur;
    v17->m_AvatorData.dbPostData.dbPost.m_PostList[n].dwUpt = dwUpt;
    v17->m_AvatorData.dbPostData.dbPost.m_PostList[n].dwGold = dwGold;
    v17->m_AvatorData.dbPostData.dbPost.m_PostList[n].lnUID = lnUID;
    strcpy_s(v17->m_AvatorData.dbPostData.dbPost.m_PostList[n].wszSendName, 0x11ui64, wszSendName);
    strcpy_s(v17->m_AvatorData.dbPostData.dbPost.m_PostList[v18].wszRecvName, 0x11ui64, wszRecvName);
    strcpy_s(v17->m_AvatorData.dbPostData.dbPost.m_PostList[v18].wszTitle, 0x15ui64, wszTitle);
    strcpy_s(v17->m_AvatorData.dbPostData.dbPost.m_PostList[v18].wszContent, 0xC9ui64, wszContent);
    v17->m_AvatorData.dbPostData.dbPost.m_PostList[v18].bNew = 1;
    v17->m_AvatorData.dbPostData.dbPost.m_PostList[v18].bUpdate = 1;
    v17->m_AvatorData.dbPostData.dbPost.m_bUpdate = 1;
    v17->m_bDataUpdate = 1;
  }
}
