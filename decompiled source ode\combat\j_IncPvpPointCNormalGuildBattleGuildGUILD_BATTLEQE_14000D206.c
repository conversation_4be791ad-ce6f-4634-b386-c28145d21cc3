/*
 * Function: j_?IncPvpPoint@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXNEAEAVCNormalGuildBattleLogger@2@@Z
 * Address: 0x14000D206
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::IncPvpPoint(GUILD_BATTLE::CNormalGuildBattleGuild *this, long double dTotalInc, char byWin, GUILD_BATTLE::CNormalGuildBattleLogger *kLogger)
{
  GUILD_BATTLE::CNormalGuildBattleGuild::IncPvpPoint(this, dTotalInc, byWin, kLogger);
}
