/*
 * Function: _CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption_::StaticAlgorithmName_::_1_::dtor$0
 * Address: 0x140459740
 */

int __fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption_::StaticAlgorithmName_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  return std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(a2 + 32);
}
