/*
 * Function: ?ApplySetItemEffect@CPlayer@@QEAAXPEAVsi_interpret@@KEE_N@Z
 * Address: 0x140063130
 */

void __fastcall CPlayer::ApplySetItemEffect(CPlayer *this, si_interpret *pSI, unsigned int dwSetItem, char bySetItemNum, char bySetEffectNum, bool bSetEffect)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-38h]@1
  int v9; // [sp+20h] [bp-18h]@4
  int idx; // [sp+24h] [bp-14h]@4
  int nEffCode; // [sp+28h] [bp-10h]@4
  int v12; // [sp+2Ch] [bp-Ch]@4
  CPlayer *v13; // [sp+40h] [bp+8h]@1
  si_interpret *v14; // [sp+48h] [bp+10h]@1

  v14 = pSI;
  v13 = this;
  v6 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  nEffCode = 0;
  v12 = 0;
  idx = 0;
  v9 = (unsigned __int8)bySetEffectNum;
  while ( idx < v9 )
  {
    nEffCode = si_interpret::GetEffectCode(v14, idx);
    si_interpret::GetEffectValue(v14, idx);
    v12 = 0;
    CPlayer::apply_normal_item_std_effect(v13, nEffCode, 0.0, bSetEffect);
    ++idx;
  }
}
