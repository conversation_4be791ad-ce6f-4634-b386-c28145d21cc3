/*
 * Function: ??_G?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@QEAAPEAXI@Z
 * Address: 0x1405A88E0
 */

void *__fastcall CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::`scalar deleting destructor'(__int64 a1, int a2)
{
  void *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = (void *)a1;
  CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(a1);
  if ( v4 & 1 )
    operator delete(v3);
  return v3;
}
