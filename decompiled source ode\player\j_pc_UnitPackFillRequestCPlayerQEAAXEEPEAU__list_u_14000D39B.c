/*
 * Function: j_?pc_UnitPackFillRequest@CPlayer@@QEAAXEEPEAU__list@_unit_pack_fill_request_clzo@@H@Z
 * Address: 0x14000D39B
 */

void __fastcall CPlayer::pc_UnitPackFillRequest(CPlayer *this, char bySlotIndex, char by<PERSON><PERSON><PERSON><PERSON>, _unit_pack_fill_request_clzo::__list *pList, int bUseNPCLinkIntem)
{
  CPlayer::pc_UnitPackFillRequest(this, bySlotIndex, byFillNum, pList, bUseNPCLinkIntem);
}
