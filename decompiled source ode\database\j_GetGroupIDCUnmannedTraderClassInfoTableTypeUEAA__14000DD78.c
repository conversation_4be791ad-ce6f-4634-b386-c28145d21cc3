/*
 * Function: j_?GetGroupID@CUnmannedTraderClassInfoTableType@@UEAA_NEGAEAE0@Z
 * Address: 0x14000DD78
 */

bool __fastcall CUnmannedTraderClassInfoTableType::GetGroupID(CUnmannedTraderClassInfoTableType *this, char byTableCode, unsigned __int16 wItemTableIndex, char *byClass, char *bySubClass)
{
  return CUnmannedTraderClassInfoTableType::GetGroupID(this, byTableCode, wItemTableIndex, byClass, bySubClass);
}
