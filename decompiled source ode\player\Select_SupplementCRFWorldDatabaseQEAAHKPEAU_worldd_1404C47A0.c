/*
 * Function: ?Select_Supplement@CRFWorldDatabase@@QEAAHKPEAU_worlddb_character_supplement_info@@@Z
 * Address: 0x1404C47A0
 */

signed __int64 __fastcall CRFWorldDatabase::Select_Supplement(CRFWorldDatabase *this, unsigned int dwSerial, _worlddb_character_supplement_info *pSupplement)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v6; // [sp+0h] [bp-C8h]@1
  void *SQLStmt; // [sp+20h] [bp-A8h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-A0h]@22
  char DstBuf; // [sp+40h] [bp-88h]@4
  SQLLEN v10; // [sp+98h] [bp-30h]@22
  __int16 v11; // [sp+A4h] [bp-24h]@9
  unsigned __int8 v12; // [sp+A8h] [bp-20h]@16
  unsigned __int64 v13; // [sp+B8h] [bp-10h]@4
  CRFWorldDatabase *v14; // [sp+D0h] [bp+8h]@1
  _worlddb_character_supplement_info *TargetValue; // [sp+E0h] [bp+18h]@1

  TargetValue = pSupplement;
  v14 = this;
  v3 = &v6;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = (unsigned __int64)&v6 ^ _security_cookie;
  sprintf_s(&DstBuf, 0x40ui64, "{ CALL pSelect_Supplement_20080428( %d ) }", dwSerial);
  if ( v14->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v14->vfptr, &DstBuf);
  if ( v14->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v14->vfptr) )
  {
    v11 = SQLExecDirect_0(v14->m_hStmtSelect, &DstBuf, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v14->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v11, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v11, v14->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v11 = SQLFetch_0(v14->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        v12 = 0;
        if ( v11 == 100 )
        {
          v12 = 2;
        }
        else
        {
          SQLStmt = v14->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v11, &DstBuf, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v11, v14->m_hStmtSelect);
          v12 = 1;
        }
        if ( v14->m_hStmtSelect )
          SQLCloseCursor_0(v14->m_hStmtSelect);
        result = v12;
      }
      else
      {
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v14->m_hStmtSelect, 1u, 8, TargetValue, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v14->m_hStmtSelect, 2u, -7, &TargetValue->bLastAttBuff, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v14->m_hStmtSelect, 3u, 4, &TargetValue->dwBufPotionEndTime, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v14->m_hStmtSelect, 4u, 4, &TargetValue->dwRaceBuffClear, 0i64, &v10);
        if ( v14->m_hStmtSelect )
          SQLCloseCursor_0(v14->m_hStmtSelect);
        if ( v14->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v14->vfptr, "%s Success", &DstBuf);
        result = 0i64;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v14->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1i64;
  }
  return result;
}
