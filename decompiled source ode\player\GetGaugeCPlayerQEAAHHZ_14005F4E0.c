/*
 * Function: ?GetGauge@CPlayer@@QEAAHH@Z
 * Address: 0x14005F4E0
 */

int __fastcall CPlayer::GetGauge(CPlayer *this, int nParamCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@7
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = nParamCode;
  if ( nParamCode )
  {
    if ( v6 == 1 )
    {
      result = CPlayer::GetFP(v7);
    }
    else if ( v6 == 2 )
    {
      result = CPlayer::GetSP(v7);
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = ((int (__fastcall *)(CPlayer *))v7->vfptr->GetHP)(v7);
  }
  return result;
}
