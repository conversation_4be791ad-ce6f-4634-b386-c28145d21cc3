/*
 * Function: ??$_Destroy_range@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@YAXPEAPEAVCMoveMapLimitInfo@@0AEAV?$allocator@PEAVCMoveMapLimitInfo@@@0@U_Scalar_ptr_iterator_tag@0@@Z
 * Address: 0x1403A3700
 */

void __fastcall std::_Destroy_range<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, std::allocator<CMoveMapLimitInfo *> *_Al, std::_Scalar_ptr_iterator_tag __formal)
{
  ;
}
