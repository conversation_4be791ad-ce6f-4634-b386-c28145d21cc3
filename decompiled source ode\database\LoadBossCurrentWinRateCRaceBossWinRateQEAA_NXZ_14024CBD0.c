/*
 * Function: ?LoadBossCurrentWinRate@CRaceBossWinRate@@QEAA_NXZ
 * Address: 0x14024CBD0
 */

char __fastcall CRaceBossWinRate::LoadBossCurrentWinRate(CRaceBossWinRate *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v3; // rax@11
  __int64 v5; // [sp+0h] [bp-E8h]@1
  char szOutDay; // [sp+38h] [bp-B0h]@4
  char v7; // [sp+39h] [bp-AFh]@4
  _SYSTEMTIME SystemTime; // [sp+68h] [bp-80h]@4
  int nSubDay; // [sp+84h] [bp-64h]@4
  unsigned int v10; // [sp+88h] [bp-60h]@8
  unsigned int dwTotalCnt; // [sp+94h] [bp-54h]@12
  unsigned int v12; // [sp+B4h] [bp-34h]@12
  int j; // [sp+C4h] [bp-24h]@8
  char v14; // [sp+C8h] [bp-20h]@12
  unsigned __int64 v15; // [sp+D8h] [bp-10h]@4
  CRaceBossWinRate *v16; // [sp+F0h] [bp+8h]@1

  v16 = this;
  v1 = &v5;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v15 = (unsigned __int64)&v5 ^ _security_cookie;
  szOutDay = 0;
  memset(&v7, 0, 8ui64);
  GetLocalTime(&SystemTime);
  nSubDay = 0;
  if ( (signed int)SystemTime.wDayOfWeek <= 1 )
  {
    if ( (signed int)SystemTime.wDayOfWeek < 1 )
      nSubDay = 6;
  }
  else
  {
    nSubDay = SystemTime.wDayOfWeek - 1;
  }
  GetSubDayStr(nSubDay, &szOutDay);
  v10 = -1;
  for ( j = 0; j < 3; ++j )
  {
    v3 = CPvpUserAndGuildRankingSystem::Instance();
    v10 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v3, j, 0);
    if ( v10 != -1 )
    {
      v12 = 0;
      dwTotalCnt = 0;
      v14 = CRFWorldDatabase::Select_RaceBossCurrentWinRate(pkDB, j, &szOutDay, &dwTotalCnt, &v12);
      if ( v14 == 1 )
        return 0;
      v16->m_byTotalBattleCnt = dwTotalCnt;
      v16->m_byWinCnt[j] = v12;
    }
  }
  return 1;
}
