/*
 * Function: j_?Select_AccountItemCharge@CRFWorldDatabase@@QEAA_NKPEAEPEANPEAKPEA_K202PEAH@Z
 * Address: 0x14000B55A
 */

bool __fastcall CRFWorldDatabase::Select_AccountItemCharge(CRFWorldDatabase *this, unsigned int dwAccountSerial, char *pbyType, long double *pdMoney, unsigned int *pdwItemCode_K, unsigned __int64 *pdwItemCode_D, unsigned int *pdwItemCode_U, char *pbyRace, unsigned int *pdwDBID, int *piTime)
{
  return CRFWorldDatabase::Select_AccountItemCharge(
           this,
           dwAccountSerial,
           pbyType,
           pdMoney,
           pdwItemCode_K,
           pdwItemCode_D,
           pdwItemCode_U,
           pbyRace,
           pdwDBID,
           piTime);
}
