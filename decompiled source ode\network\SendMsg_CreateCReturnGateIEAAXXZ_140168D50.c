/*
 * Function: ?SendMsg_Create@CReturnGate@@IEAAXXZ
 * Address: 0x140168D50
 */

void __fastcall CReturnGate::SendMsg_Create(CReturnGate *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v3; // rax@4
  __int64 v4; // [sp+0h] [bp-A8h]@1
  char szMsg[2]; // [sp+38h] [bp-70h]@4
  unsigned int v6; // [sp+3Ah] [bp-6Eh]@4
  unsigned int v7; // [sp+3Eh] [bp-6Ah]@4
  char Dest; // [sp+42h] [bp-66h]@4
  __int16 pShort; // [sp+53h] [bp-55h]@4
  char pbyType; // [sp+74h] [bp-34h]@4
  char v11; // [sp+75h] [bp-33h]@4
  unsigned __int64 v12; // [sp+90h] [bp-18h]@4
  CReturnGate *v13; // [sp+B0h] [bp+8h]@1

  v13 = this;
  v1 = &v4;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = (unsigned __int64)&v4 ^ _security_cookie;
  *(_WORD *)szMsg = v13->m_ObjID.m_wIndex;
  v6 = v13->m_dwObjSerial;
  v7 = v13->m_dwOwnerSerial;
  v3 = CPlayerDB::GetCharNameW(&v13->m_pkOwner->m_Param);
  strcpy_0(&Dest, v3);
  FloatToShort(v13->m_fCurPos, &pShort, 3);
  pbyType = 8;
  v11 = 7;
  CGameObject::CircleReport((CGameObject *)&v13->vfptr, &pbyType, szMsg, 33, 0);
}
