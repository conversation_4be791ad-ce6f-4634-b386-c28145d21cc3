/*
 * Function: ?insert@?$list@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@std@@QEAA?AV?$_Iterator@$0A@@12@V312@AEBU?$pair@$$CBHPEAVCNationSettingFactory@@@2@@Z
 * Address: 0x14021CFC0
 */

std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *__fastcall std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::insert(std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > > *this, std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *result, std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *_Where, std::pair<int const ,CNationSettingFactory *> *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *v6; // rax@4
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *v7; // rax@4
  __int64 v9; // [sp+0h] [bp-68h]@1
  char v10; // [sp+20h] [bp-48h]@4
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *v11; // [sp+38h] [bp-30h]@4
  int v12; // [sp+40h] [bp-28h]@4
  __int64 v13; // [sp+48h] [bp-20h]@4
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *v14; // [sp+50h] [bp-18h]@4
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > > *v15; // [sp+70h] [bp+8h]@1
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *v16; // [sp+78h] [bp+10h]@1
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *__that; // [sp+80h] [bp+18h]@1
  std::pair<int const ,CNationSettingFactory *> *v18; // [sp+88h] [bp+20h]@1

  v18 = _Val;
  __that = _Where;
  v16 = result;
  v15 = this;
  v4 = &v9;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v13 = -2i64;
  v12 = 0;
  v11 = (std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *)&v10;
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>::_Iterator<0>(
    (std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *)&v10,
    _Where);
  v14 = v6;
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Insert(
    v15,
    v6,
    v18);
  v7 = std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>::operator--(__that);
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>::_Iterator<0>(
    v16,
    v7);
  v12 |= 1u;
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>::~_Iterator<0>(__that);
  return v16;
}
