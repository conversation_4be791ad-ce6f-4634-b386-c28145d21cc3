/*
 * Function: ?insert@?$vector@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@QEAA?AV?$_Vector_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@2@V32@AEBQEAVCUnmannedTraderClassInfo@@@Z
 * Address: 0x140370420
 */

std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *__fastcall std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::insert(std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this, std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *result, std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_Where, CUnmannedTraderClassInfo *const *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v6; // rax@9
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v7; // rax@9
  __int64 v9; // [sp+0h] [bp-C8h]@1
  __int64 _Off; // [sp+20h] [bp-A8h]@7
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > resulta; // [sp+28h] [bp-A0h]@6
  char v12; // [sp+40h] [bp-88h]@9
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v13; // [sp+58h] [bp-70h]@9
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > v14; // [sp+60h] [bp-68h]@9
  int v15; // [sp+78h] [bp-50h]@4
  __int64 v16; // [sp+80h] [bp-48h]@4
  __int64 v17; // [sp+88h] [bp-40h]@5
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v18; // [sp+90h] [bp-38h]@6
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_Right; // [sp+98h] [bp-30h]@6
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v20; // [sp+A0h] [bp-28h]@9
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v21; // [sp+A8h] [bp-20h]@9
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v22; // [sp+B0h] [bp-18h]@9
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v23; // [sp+D0h] [bp+8h]@1
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v24; // [sp+D8h] [bp+10h]@1
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *__that; // [sp+E0h] [bp+18h]@1
  CUnmannedTraderClassInfo *const *v26; // [sp+E8h] [bp+20h]@1

  v26 = _Val;
  __that = _Where;
  v24 = result;
  v23 = this;
  v4 = &v9;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v16 = -2i64;
  v15 = 0;
  if ( std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::size(v23) )
  {
    v18 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::begin(v23, &resulta);
    _Right = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)v18;
    v15 |= 1u;
    v17 = std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator-(
            __that,
            (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v18->_Mycont);
  }
  else
  {
    v17 = 0i64;
  }
  _Off = v17;
  if ( v15 & 1 )
  {
    v15 &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&resulta);
  }
  v13 = (std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v12;
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
    (std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v12,
    __that);
  v20 = v6;
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Insert_n(v23, v6, 1ui64, v26);
  v7 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::begin(v23, &v14);
  v21 = v7;
  v22 = v7;
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator+(v7, v24, _Off);
  v15 |= 2u;
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&v14);
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(__that);
  return v24;
}
