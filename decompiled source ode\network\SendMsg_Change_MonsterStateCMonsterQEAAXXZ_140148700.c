/*
 * Function: ?SendMsg_Change_MonsterState@CMonster@@QEAAXXZ
 * Address: 0x140148700
 */

void __fastcall CMonster::SendMsg_Change_MonsterState(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  unsigned __int16 v5; // [sp+38h] [bp-40h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v7; // [sp+55h] [bp-23h]@4
  CMonster *v8; // [sp+80h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(_DWORD *)szMsg = v8->m_dwObjSerial;
  v5 = CMonster::GetMonStateInfo(v8);
  pbyType = 11;
  v7 = -105;
  CGameObject::CircleReport((CGameObject *)&v8->vfptr, &pbyType, szMsg, 6, 0);
}
