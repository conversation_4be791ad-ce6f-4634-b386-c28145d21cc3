/*
 * Function: ?GetBase@?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@CryptoPP@@UEBAAEBUECPPoint@2@AEBV?$DL_GroupPrecomputation@UECPPoint@CryptoPP@@@2@@Z
 * Address: 0x14044EAC0
 */

CryptoPP::ECPPoint *__fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::GetBase(CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint> *this, CryptoPP::DL_GroupPrecomputation<CryptoPP::ECPPoint> *group)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  CryptoPP::ECPPoint *v6; // [sp+28h] [bp-10h]@5
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint> *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( (unsigned __int8)((int (__fastcall *)(CryptoPP::DL_GroupPrecomputation<CryptoPP::ECPPoint> *))group->vfptr->NeedConversions)(group) )
    v6 = &v7->m_base;
  else
    v6 = std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::operator[](&v7->m_bases, 0i64);
  return v6;
}
