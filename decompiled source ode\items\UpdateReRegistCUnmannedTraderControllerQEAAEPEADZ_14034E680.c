/*
 * Function: ?UpdateReRegist@CUnmannedTraderController@@QEAAEPEAD@Z
 * Address: 0x14034E680
 */

char __fastcall CUnmannedTraderController::UpdateReRegist(CUnmannedTraderController *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-E8h]@1
  char *byProcRet; // [sp+20h] [bp-C8h]@7
  unsigned int dwTax; // [sp+28h] [bp-C0h]@22
  _SYSTEMTIME *kCurTime; // [sp+30h] [bp-B8h]@22
  char *v9; // [sp+40h] [bp-A8h]@4
  char Dst; // [sp+58h] [bp-90h]@4
  char v11; // [sp+74h] [bp-74h]@4
  char byState; // [sp+84h] [bp-64h]@4
  char v13; // [sp+A4h] [bp-44h]@4
  unsigned int pdwRegister; // [sp+C4h] [bp-24h]@4
  int j; // [sp+D4h] [bp-14h]@4
  CUnmannedTraderController *v16; // [sp+F0h] [bp+8h]@1

  v16 = this;
  v2 = &v5;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = pData;
  memset_0(&Dst, 0, 0x10ui64);
  GetLocalTime((LPSYSTEMTIME)&Dst);
  v11 = 0;
  byState = -1;
  v13 = 0;
  pdwRegister = 0;
  for ( j = 0; j < (unsigned __int8)v9[4]; ++j )
  {
    byState = -1;
    byProcRet = &v13;
    CUnmannedTraderController::CheckDBItemState(v16, *v9, *(_DWORD *)&v9[28 * j + 32], &byState, &v13);
    if ( v13 != 8 && v13 != 38 )
    {
      if ( byState == 6 || byState == 13 )
      {
        if ( v9[28 * j + 13] )
        {
          if ( v9[28 * j + 12] )
          {
            byProcRet = &Dst;
            if ( !CRFWorldDatabase::Update_UnmannedTraderItemState(
                    pkDB,
                    *v9,
                    *(_DWORD *)&v9[28 * j + 32],
                    5,
                    (_SYSTEMTIME *)&Dst) )
              v9[28 * j + 12] = 30;
          }
          else
          {
            v11 = CRFWorldDatabase::Select_UnmannedTraderRegister(pkDB, *v9, *(_DWORD *)&v9[28 * j + 32], &pdwRegister);
            if ( v11 )
            {
              v9[28 * j + 12] = 24;
            }
            else if ( pdwRegister == *((_DWORD *)v9 + 2) )
            {
              kCurTime = (_SYSTEMTIME *)&Dst;
              dwTax = *(_DWORD *)&v9[28 * j + 16];
              LODWORD(byProcRet) = *(_DWORD *)&v9[28 * j + 28];
              if ( !CRFWorldDatabase::Update_UnmannedTraderReRegist(
                      pkDB,
                      *v9,
                      *(_DWORD *)&v9[28 * j + 32],
                      v9[28 * j + 36],
                      (unsigned int)byProcRet,
                      dwTax,
                      (_SYSTEMTIME *)&Dst) )
                v9[28 * j + 12] = 24;
            }
            else
            {
              v9[28 * j + 12] = -54;
            }
          }
        }
        else
        {
          byProcRet = &Dst;
          if ( !CRFWorldDatabase::Update_UnmannedTraderItemState(
                  pkDB,
                  *v9,
                  *(_DWORD *)&v9[28 * j + 32],
                  v9[28 * j + 36],
                  (_SYSTEMTIME *)&Dst) )
            v9[28 * j + 12] = 30;
        }
      }
      else
      {
        v9[28 * j + 12] = -53;
      }
    }
    else
    {
      v9[28 * j + 12] = v13;
    }
  }
  return 0;
}
