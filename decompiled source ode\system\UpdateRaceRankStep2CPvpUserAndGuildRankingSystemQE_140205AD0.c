/*
 * Function: ?UpdateRaceRankStep2@CPvpUserAndGuildRankingSystem@@QEAAEPEAD@Z
 * Address: 0x140205AD0
 */

char __fastcall CPvpUserAndGuildRankingSystem::UpdateRaceRankStep2(CPvpUserAndGuildRankingSystem *this, char *szData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPvpUserAndGuildRankingSystem *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return CUserRankingProcess::UpdateRaceRankStep2(&v6->m_kUserRankingProcess, szData);
}
