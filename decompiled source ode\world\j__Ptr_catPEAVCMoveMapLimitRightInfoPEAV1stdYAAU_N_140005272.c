/*
 * Function: j_??$_Ptr_cat@PEAVCMoveMapLimitRightInfo@@PEAV1@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAPEAVCMoveMapLimitRightInfo@@0@Z
 * Address: 0x140005272
 */

std::_Nonscalar_ptr_iterator_tag __fastcall std::_Ptr_cat<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *>(CMoveMapLimitRightInfo **__formal, CMoveMapLimitRightInfo **a2)
{
  return std::_Ptr_cat<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *>(__formal, a2);
}
