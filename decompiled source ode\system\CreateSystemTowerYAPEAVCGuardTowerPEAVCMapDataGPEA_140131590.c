/*
 * Function: ?CreateSystemTower@@YAPEAVCGuardTower@@PEAVCMapData@@GPEAMHEH@Z
 * Address: 0x140131590
 */

CGuardTower *__fastcall CreateSystemTower(CMapData *pMap, unsigned __int16 wLayer, float *fPos, int nTowerIndex, char byRaceCode, int nIniIndex)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  CGuardTower *result; // rax@10
  __int64 v9; // [sp+0h] [bp-A8h]@1
  CGuardTower *v10; // [sp+20h] [bp-88h]@4
  int j; // [sp+28h] [bp-80h]@4
  _tower_create_setdata Dst; // [sp+40h] [bp-68h]@11
  CMapData *v13; // [sp+B0h] [bp+8h]@1
  unsigned __int16 v14; // [sp+B8h] [bp+10h]@1
  float *Src; // [sp+C0h] [bp+18h]@1
  int n; // [sp+C8h] [bp+20h]@1

  n = nTowerIndex;
  Src = fPos;
  v14 = wLayer;
  v13 = pMap;
  v6 = &v9;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10 = 0i64;
  for ( j = 0; j < 500; ++j )
  {
    if ( !g_Tower[j].m_bLive )
    {
      v10 = &g_Tower[j];
      break;
    }
  }
  if ( v10 )
  {
    _tower_create_setdata::_tower_create_setdata(&Dst);
    Dst.m_pMap = v13;
    Dst.m_nLayerIndex = v14;
    Dst.m_pRecordSet = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 25, n);
    if ( Dst.m_pRecordSet )
    {
      memcpy_0(Dst.m_fStartPos, Src, 0xCui64);
      Dst.pMaster = 0i64;
      Dst.byRaceCode = byRaceCode;
      Dst.pItem = 0i64;
      Dst.nIniIndex = nIniIndex;
      if ( CGuardTower::Create(v10, &Dst) )
        result = v10;
      else
        result = 0i64;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
