/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAURoomCharInfo@@PEAU1@V?$allocator@URoomCharInfo@@@std@@@stdext@@YAPEAURoomCharInfo@@PEAU1@00AEAV?$allocator@URoomCharInfo@@@std@@@Z
 * Address: 0x1400106D6
 */

RoomCharInfo *__fastcall stdext::_Unchecked_uninitialized_move<RoomCharInfo *,RoomCharInfo *,std::allocator<RoomCharInfo>>(RoomCharInfo *_First, RoomCharInfo *_Last, RoomCharInfo *_Dest, std::allocator<RoomCharInfo> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<RoomCharInfo *,RoomCharInfo *,std::allocator<RoomCharInfo>>(
           _First,
           _Last,
           _Dest,
           _<PERSON>);
}
