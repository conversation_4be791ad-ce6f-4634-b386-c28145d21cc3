/*
 * Function: SQLWriteFileDSN
 * Address: 0x1404DACFC
 */

int __fastcall SQLWriteFileDSN(const char *lpszFileName, const char *lpszAppName, const char *lpszKeyName, const char *lpszString)
{
  const char *v4; // rbp@1
  const char *v5; // rbx@1
  const char *v6; // rdi@1
  const char *v7; // rsi@1
  __int64 (__cdecl *v8)(); // rax@1
  int result; // eax@2

  v4 = lpszFileName;
  v5 = lpszString;
  v6 = lpszKeyName;
  v7 = lpszAppName;
  v8 = ODBC___GetSetupProc("SQLWriteFileDSN");
  if ( v8 )
    result = ((int (__fastcall *)(const char *, const char *, const char *, const char *))v8)(v4, v7, v6, v5);
  else
    result = 0;
  return result;
}
