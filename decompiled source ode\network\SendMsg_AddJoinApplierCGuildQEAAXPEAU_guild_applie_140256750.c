/*
 * Function: ?SendMsg_AddJoinApplier@CGuild@@QEAAXPEAU_guild_applier_info@@@Z
 * Address: 0x140256750
 */

void __usercall CGuild::SendMsg_AddJoinApplier(CGuild *this@<rcx>, _guild_applier_info *p@<rdx>, double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-A8h]@1
  char szMsg[4]; // [sp+38h] [bp-70h]@4
  char Dest; // [sp+3Ch] [bp-6Ch]@4
  char v9; // [sp+4Dh] [bp-5Bh]@4
  int v10; // [sp+4Eh] [bp-5Ah]@4
  unsigned int v11; // [sp+52h] [bp-56h]@4
  char pbyType; // [sp+74h] [bp-34h]@4
  char v13; // [sp+75h] [bp-33h]@4
  int j; // [sp+84h] [bp-24h]@4
  unsigned __int64 v15; // [sp+90h] [bp-18h]@4
  CGuild *v16; // [sp+B0h] [bp+8h]@1
  _guild_applier_info *v17; // [sp+B8h] [bp+10h]@1

  v17 = p;
  v16 = this;
  v3 = &v6;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = (unsigned __int64)&v6 ^ _security_cookie;
  *(_DWORD *)szMsg = p->pPlayer->m_dwObjSerial;
  v5 = CPlayerDB::GetCharNameW(&p->pPlayer->m_Param);
  strcpy_0(&Dest, v5);
  v9 = ((int (__fastcall *)(CPlayer *))v17->pPlayer->vfptr->GetLevel)(v17->pPlayer);
  CPlayerDB::GetPvPPoint(&v17->pPlayer->m_Param);
  v10 = (signed int)floor(a3);
  v11 = v17->dwApplyTime;
  pbyType = 27;
  v13 = 8;
  for ( j = 0; j < 50; ++j )
  {
    if ( _guild_member_info::IsFill(&v16->m_MemberData[j])
      && v16->m_MemberData[j].pPlayer
      && (v16->m_MemberData[j].byClassInGuild == 1 || v16->m_MemberData[j].byClassInGuild == 2) )
    {
      CNetProcess::LoadSendMsg(unk_1414F2088, v16->m_MemberData[j].pPlayer->m_ObjID.m_wIndex, &pbyType, szMsg, 0x1Eu);
    }
  }
}
