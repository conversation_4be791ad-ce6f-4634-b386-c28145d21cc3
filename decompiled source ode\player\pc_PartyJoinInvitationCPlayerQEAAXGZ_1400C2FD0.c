/*
 * Function: ?pc_PartyJoinInvitation@CPlayer@@QEAAXG@Z
 * Address: 0x1400C2FD0
 */

void __usercall CPlayer::pc_PartyJoinInvitation(CPlayer *this@<rcx>, unsigned __int16 wDstIndex@<dx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@16
  CNationSettingManager *v6; // rax@21
  int v7; // eax@24
  __int64 v8; // [sp+0h] [bp-58h]@1
  CPlayer *v9; // [sp+20h] [bp-38h]@7
  int v10; // [sp+28h] [bp-30h]@16
  __int16 *pDest2; // [sp+30h] [bp-28h]@21
  __int16 *pDest1; // [sp+38h] [bp-20h]@21
  float v13; // [sp+40h] [bp-18h]@24
  CPlayer *v14; // [sp+60h] [bp+8h]@1
  unsigned __int16 v15; // [sp+68h] [bp+10h]@1

  v15 = wDstIndex;
  v14 = this;
  v3 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v14->m_id.wIndex) == 99 )
  {
    CPlayer::SendMsg_TLStatusPenalty(v14, 1);
    return;
  }
  if ( !CPlayer::IsPunished(v14, 2, 1) )
  {
    v9 = &g_Player + v15;
    if ( v9->m_bLive )
    {
      if ( !v9->m_bCorpse && v9->m_pCurMap == v14->m_pCurMap )
      {
        if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v9->m_id.wIndex) == 99 )
        {
          CPlayer::SendMsg_TLStatusPenalty(v14, 3);
          return;
        }
        if ( !CPlayer::IsPunished(v9, 2, 0) )
        {
          _effect_parameter::GetEff_Have(&v9->m_EP, 50);
          if ( a3 > 0.0 )
          {
            CPlayer::SendMsg_JadeEffectErr(v14, 1);
            return;
          }
          v10 = CPlayerDB::GetRaceCode(&v14->m_Param);
          v5 = CPlayerDB::GetRaceCode(&v9->m_Param);
          if ( v10 == v5 )
          {
            if ( v9->m_byUserDgr )
            {
              if ( !v14->m_byUserDgr )
                return;
            }
            else if ( v14->m_byUserDgr )
            {
              return;
            }
            pDest2 = &v9->m_pUserDB->m_BillingInfo.iType;
            pDest1 = &v14->m_pUserDB->m_BillingInfo.iType;
            v6 = CTSingleton<CNationSettingManager>::Instance();
            if ( !CNationSettingManager::IsPersonalFreeFixedAmountBillingType(v6, pDest1, pDest2)
              && !CPartyPlayer::IsPartyMode(v9->m_pPartyMgr)
              && !v14->m_pPartyMgr->m_bLock )
            {
              _effect_parameter::GetEff_Have(&v9->m_EP, 53);
              v13 = a3;
              v7 = ((int (__fastcall *)(CPlayer *))v9->vfptr->GetLevel)(v9);
              if ( CPartyPlayer::IsJoinPartyLevel(v14->m_pPartyMgr, v7, v13) )
              {
                if ( CPartyPlayer::IsPartyBoss(v14->m_pPartyMgr) || !CPartyPlayer::IsPartyMode(v14->m_pPartyMgr) )
                  CPlayer::SendMsg_PartyJoinInvitationQuestion(v14, v15);
              }
              else
              {
                CPlayer::SendMsg_PartyJoinFailLevel(v14);
              }
            }
          }
        }
      }
    }
  }
}
