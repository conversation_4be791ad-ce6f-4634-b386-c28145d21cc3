/*
 * Function: ?LoadINISubProcLoadCode@CRaceBuffInfoByHolyQuest@@CA_NIPEADPEAPEAU_skill_fld@@PEAE@Z
 * Address: 0x1403B44B0
 */

char __fastcall CRaceBuffInfoByHolyQuest::LoadINISubProcLoadCode(unsigned int uiTh, char *szItemName, _skill_fld **ppFld, char *byLv)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-968h]@1
  DWORD nSize[2]; // [sp+20h] [bp-948h]@4
  LPCSTR lpFileName; // [sp+28h] [bp-940h]@12
  int v10; // [sp+30h] [bp-938h]@12
  char DstBuf; // [sp+50h] [bp-918h]@4
  char v12; // [sp+51h] [bp-917h]@4
  char ReturnedString; // [sp+470h] [bp-4F8h]@4
  char v14; // [sp+471h] [bp-4F7h]@4
  char szRecordCode; // [sp+890h] [bp-D8h]@4
  char v16; // [sp+891h] [bp-D7h]@4
  char Str; // [sp+8D0h] [bp-98h]@4
  char *ppszDst; // [sp+928h] [bp-40h]@4
  char *v19; // [sp+930h] [bp-38h]@4
  int v20; // [sp+944h] [bp-24h]@4
  unsigned __int64 v21; // [sp+950h] [bp-18h]@4
  unsigned int v22; // [sp+970h] [bp+8h]@1
  _skill_fld **v23; // [sp+980h] [bp+18h]@1
  char *v24; // [sp+988h] [bp+20h]@1

  v24 = byLv;
  v23 = ppFld;
  v22 = uiTh;
  v4 = &v7;
  for ( i = 600i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v21 = (unsigned __int64)&v7 ^ _security_cookie;
  DstBuf = 0;
  memset(&v12, 0, 0x3FFui64);
  ReturnedString = 0;
  memset(&v14, 0, 0x3FFui64);
  szRecordCode = 0;
  memset(&v16, 0, 0x7Fui64);
  ppszDst = &szRecordCode;
  v19 = &Str;
  v20 = 0;
  nSize[0] = v22;
  sprintf_s(&DstBuf, 0x400ui64, "%s%u", szItemName);
  GetPrivateProfileStringA("RaceBuff", &DstBuf, "NULL", &ReturnedString, 0x400u, ".\\Initialize\\NewHolySystem.ini");
  if ( !strcmp_0(&ReturnedString, "NULL") )
  {
    CLogFile::Write(
      &stru_1799C8F30,
      "CRaceBuffInfoByHolyQuestList::LoadINISubProcLoadCode() : th(%u) %s NULL!",
      v22,
      &DstBuf);
    result = 0;
  }
  else
  {
    szRecordCode = 0;
    Str = 0;
    v20 = ParsingCommandA(&ReturnedString, 2, &ppszDst, 64);
    if ( v20 )
    {
      *v23 = (_skill_fld *)CRecordData::GetRecord(&stru_1799C8410, &szRecordCode);
      if ( *v23 )
      {
        *v24 = atoi(&Str);
        if ( !*v24 || (signed int)(unsigned __int8)*v24 > 7 )
        {
          v10 = 7;
          lpFileName = &Str;
          *(_QWORD *)nSize = &szRecordCode;
          CLogFile::Write(
            &stru_1799C8F30,
            "CRaceBuffInfoByHolyQuestList::LoadINISubProcLoadCode() : th(%u) %s = %s %s SkillLv Invalid! max_skill_level(%d)",
            v22,
            &DstBuf);
        }
        result = 1;
      }
      else
      {
        *(_QWORD *)nSize = &szRecordCode;
        CLogFile::Write(
          &stru_1799C8F30,
          "CRaceBuffInfoByHolyQuestList::LoadINISubProcLoadCode() : th(%u) %s = %s Skill Not Exist!",
          v22,
          &DstBuf);
        result = 0;
      }
    }
    else
    {
      CLogFile::Write(
        &stru_1799C8F30,
        "CRaceBuffInfoByHolyQuestList::LoadINISubProcLoadCode() : th(%u) %s ::ParsingCommandA NULL!",
        v22,
        &DstBuf);
      result = 0;
    }
  }
  return result;
}
