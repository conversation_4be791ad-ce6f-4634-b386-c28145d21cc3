/*
 * Function: ?SendMsg_Create@CParkingUnit@@QEAAXXZ
 * Address: 0x140167CE0
 */

void __fastcall CParkingUnit::SendMsg_Create(CParkingUnit *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-A8h]@1
  char szMsg[2]; // [sp+38h] [bp-70h]@4
  unsigned int v5; // [sp+3Ah] [bp-6Eh]@4
  char v6; // [sp+3Eh] [bp-6Ah]@4
  char v7; // [sp+3Fh] [bp-69h]@4
  char Dst; // [sp+40h] [bp-68h]@4
  unsigned int v9; // [sp+46h] [bp-62h]@4
  __int16 pShort; // [sp+4Ah] [bp-5Eh]@4
  char v11; // [sp+50h] [bp-58h]@4
  char pbyType; // [sp+74h] [bp-34h]@4
  char v13; // [sp+75h] [bp-33h]@4
  unsigned __int64 v14; // [sp+90h] [bp-18h]@4
  CParkingUnit *v15; // [sp+B0h] [bp+8h]@1

  v15 = this;
  v1 = &v3;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v14 = (unsigned __int64)&v3 ^ _security_cookie;
  *(_WORD *)szMsg = v15->m_ObjID.m_wIndex;
  v5 = v15->m_dwObjSerial;
  v6 = v15->m_byCreateType;
  v7 = v15->m_byFrame;
  memcpy_0(&Dst, v15->m_byPartCode, 6ui64);
  v9 = v15->m_dwOwnerSerial;
  FloatToShort(v15->m_fCurPos, &pShort, 3);
  v11 = v15->m_byTransDistCode;
  pbyType = 3;
  v13 = 21;
  CGameObject::CircleReport((CGameObject *)&v15->vfptr, &pbyType, szMsg, 25, 0);
}
