/*
 * Function: ?SendMsg_TrunkExtendResult@CPlayer@@QEAAXEEKK@Z
 * Address: 0x1400E3520
 */

void __fastcall CPlayer::SendMsg_TrunkExtendResult(CPlayer *this, char byRetCode, char bySlotNum, unsigned int dwLeftDalant, unsigned int dwConsumDalant)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  char v9; // [sp+39h] [bp-4Fh]@4
  unsigned int v10; // [sp+3Ah] [bp-4Eh]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v12; // [sp+65h] [bp-23h]@4
  CPlayer *v13; // [sp+90h] [bp+8h]@1

  v13 = this;
  v5 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  szMsg = byRetCode;
  v9 = (unsigned __int8)bySlotNum / 20;
  v10 = dwLeftDalant;
  pbyType = 34;
  v12 = 8;
  CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, &szMsg, 0xAu);
}
