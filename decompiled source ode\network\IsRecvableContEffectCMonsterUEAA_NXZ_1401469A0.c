/*
 * Function: ?IsRecvableContEffect@CMonster@@UEAA_NXZ
 * Address: 0x1401469A0
 */

bool __fastcall CMonster::IsRecvableContEffect(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMonster *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( _effect_parameter::GetEff_State(&v5->m_EP, 20) )
    result = 0;
  else
    result = _effect_parameter::GetEff_State(&v5->m_EP, 28) == 0;
  return result;
}
