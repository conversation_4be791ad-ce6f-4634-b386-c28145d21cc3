/*
 * Function: ?SetPlayerState@RFEvent_ClassRefine@@UEAA_NQEAXH@Z
 * Address: 0x140328A60
 */

char __fastcall RFEvent_ClassRefine::SetPlayerState(RFEvent_ClassRefine *this, void *const p, int size)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  void *v6; // [sp+0h] [bp-18h]@1
  RFEvent_ClassRefine *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v3 = (__int64 *)&v6;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( size == 20i64 )
  {
    v6 = p;
    v7->_pkParticipant[*(_WORD *)p].nAvatorSerial = *((_DWORD *)p + 1);
    if ( *((_DWORD *)v6 + 3) >= v7->_kEvent.nStartDate )
    {
      v7->_pkParticipant[*(_WORD *)v6].dwRefineDate = *((_DWORD *)v6 + 3);
      v7->_pkParticipant[*(_WORD *)v6].nCurRefineCnt = *((_BYTE *)v6 + 16);
      v7->_pkParticipant[*(_WORD *)v6].bChange = 0;
    }
    else
    {
      v7->_pkParticipant[*(_WORD *)v6].dwRefineDate = v7->_kEvent.nStartDate;
      v7->_pkParticipant[*(_WORD *)v6].nCurRefineCnt = 0;
      v7->_pkParticipant[*(_WORD *)v6].bChange = 1;
    }
    v7->_pkParticipant[*(_WORD *)v6].nSock = *(_WORD *)v6;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
