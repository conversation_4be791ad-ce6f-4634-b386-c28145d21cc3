/*
 * Function: ??4CUnmannedTraderUserInfo@@QEAAAEAV0@AEBV0@@Z
 * Address: 0x14036A270
 */

CUnmannedTraderUserInfo *__fastcall CUnmannedTraderUserInfo::operator=(CUnmannedTraderUserInfo *this, CUnmannedTraderUserInfo *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderUserInfo *v6; // [sp+30h] [bp+8h]@1
  CUnmannedTraderUserInfo *v7; // [sp+38h] [bp+10h]@1

  v7 = __that;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6->m_eState = __that->m_eState;
  v6->m_wInx = __that->m_wInx;
  v6->m_dwUserSerial = __that->m_dwUserSerial;
  v6->m_byRegistCnt = __that->m_byRegistCnt;
  v6->m_byMaxRegistCnt = __that->m_byMaxRegistCnt;
  v6->m_kRequestState.m_eState = __that->m_kRequestState.m_eState;
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator=(
    &v6->m_vecRegistItemInfo,
    &__that->m_vecRegistItemInfo);
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator=(
    &v6->m_vecLoadItemInfo,
    &v7->m_vecLoadItemInfo);
  return v6;
}
