/*
 * Function: ?SF_MakePortalReturnBindPositionPartyMember@CPlayer@@UEAA_NPEAVCCharacter@@MAEAE@Z
 * Address: 0x1400A00E0
 */

char __fastcall CPlayer::SF_MakePortalReturnBindPositionPartyMember(CPlayer *this, CCharacter *pDstObj, float fEffectValue, char *byRet)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CReturnGateController *v7; // rax@6
  __int64 v8; // [sp+0h] [bp-28h]@1
  CPlayer *pkOwner; // [sp+38h] [bp+10h]@1
  char *v10; // [sp+48h] [bp+20h]@1

  v10 = byRet;
  pkOwner = (CPlayer *)pDstObj;
  v4 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pDstObj->m_ObjID.m_byID )
  {
    result = 0;
  }
  else
  {
    v7 = CReturnGateController::Instance();
    if ( CReturnGateController::Open(v7, pkOwner) )
    {
      result = 1;
    }
    else
    {
      *v10 = -1;
      result = 0;
    }
  }
  return result;
}
