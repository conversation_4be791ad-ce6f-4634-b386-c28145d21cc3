/*
 * Function: ?AlterSchedule@CHolyStoneSystem@@QEAAXEE@Z
 * Address: 0x14027B2B0
 */

void __fastcall CHolyStoneSystem::AlterSchedule(CHolyStoneSystem *this, char byScheduleCode, char byNumOfTime)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-68h]@1
  int Dst[7]; // [sp+38h] [bp-30h]@8
  int v7; // [sp+54h] [bp-14h]@8
  int v8; // [sp+58h] [bp-10h]@10
  char v9; // [sp+5Ch] [bp-Ch]@4
  CHolyStoneSystem *v10; // [sp+70h] [bp+8h]@1
  char v11; // [sp+80h] [bp+18h]@1

  v11 = byNumOfTime;
  v10 = this;
  v3 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = byScheduleCode;
  if ( byScheduleCode )
  {
    if ( v9 == 1 )
    {
      v10->m_bScheduleCodePre = 0;
      v7 = v10->m_SaveData.m_nHolyMasterRace;
      memcpy_0(Dst, v10->m_SaveData.m_nStoneHP_Buffer, 0xCui64);
      CHolyStoneSystem::SetScene(v10, v11, 1, 0, 0);
      if ( v7 >= 0 && v7 < 3 )
      {
        v8 = (signed int)ffloor((float)((float)v10->m_SaveData.m_nStartStoneHP * 0.2) + (float)Dst[v7]);
        if ( (unsigned int)v8 > v10->m_SaveData.m_nStartStoneHP )
          v8 = v10->m_SaveData.m_nStartStoneHP;
        ((void (__fastcall *)(struct CHolyStone *, _QWORD, _QWORD))g_Stone[v7].vfptr->SetHP)(
          &g_Stone[v7],
          (unsigned int)v8,
          0i64);
      }
      CLogFile::Write(&v10->m_logQuest, "Holy Quset Start >> ");
    }
  }
  else
  {
    v10->m_bScheduleCodePre = 1;
    CHolyStoneSystem::InitQuestCash_Other(v10);
    v10->m_dwNextStartTime = timeGetTime() + 3600000;
    CHolyStoneSystem::SendMsg_NoticeNextQuest(v10, -1, 1);
    CLogFile::Write(&v10->m_logQuest, "Notice Next Quest >> ");
  }
}
