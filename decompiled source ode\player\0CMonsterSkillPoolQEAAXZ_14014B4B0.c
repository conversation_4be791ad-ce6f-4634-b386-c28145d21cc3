/*
 * Function: ??0CMonsterSkillPool@@QEAA@XZ
 * Address: 0x14014B4B0
 */

void __fastcall CMonsterSkillPool::CMonsterSkillPool(CMonsterSkillPool *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  __int64 v4; // [sp+30h] [bp-18h]@4
  CMonsterSkillPool *v5; // [sp+50h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  `eh vector constructor iterator'(
    v5->m_MonSkill,
    0x60ui64,
    16,
    (void (__cdecl *)(void *))CMonsterSkill::CMonsterSkill,
    (void (__cdecl *)(void *))CMonsterSkill::~CMonsterSkill);
  CMonsterSkillPool::Init(v5);
}
