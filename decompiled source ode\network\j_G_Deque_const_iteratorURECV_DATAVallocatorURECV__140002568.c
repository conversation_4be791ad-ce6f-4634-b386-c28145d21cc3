/*
 * Function: j_??G?$_Deque_const_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEBA_JAEBV01@@Z
 * Address: 0x140002568
 */

__int64 __fastcall std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator-(std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this, std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Right)
{
  return std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator-(this, _Right);
}
