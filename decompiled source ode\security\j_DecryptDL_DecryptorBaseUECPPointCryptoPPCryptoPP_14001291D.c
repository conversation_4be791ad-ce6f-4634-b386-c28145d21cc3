/*
 * Function: j_?Decrypt@?$DL_DecryptorBase@UECPPoint@CryptoPP@@@CryptoPP@@UEBA?AUDecodingResult@2@AEAVRandomNumberGenerator@2@PEBE_KPEAEAEBVNameV<PERSON>uePairs@2@@Z
 * Address: 0x14001291D
 */

CryptoPP::DecodingResult *__fastcall CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>::Decrypt(CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint> *this, CryptoPP::DecodingResult *result, CryptoPP::RandomNumberGenerator *rng, const char *ciphertext, unsigned __int64 ciphertextLength, char *plaintext, CryptoPP::NameValuePairs *parameters)
{
  return CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>::Decrypt(
           this,
           result,
           rng,
           ciphertext,
           ciphertextLength,
           plaintext,
           parameters);
}
