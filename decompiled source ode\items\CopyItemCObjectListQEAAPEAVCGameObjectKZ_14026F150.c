/*
 * Function: ?CopyItem@CObjectList@@QEAAPEAVCGameObject@@K@Z
 * Address: 0x14026F150
 */

CObjectListVtbl *__fastcall CObjectList::CopyItem(CObjectList *this, unsigned int dwIndex)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // [sp+0h] [bp-18h]@1
  CObjectList *v6; // [sp+8h] [bp-10h]@4
  CObjectList *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  v5 = 0;
  v6 = (CObjectList *)v7->m_Head.m_pNext;
  while ( (_object_list_point *)v6 != &v7->m_Tail )
  {
    if ( dwIndex == v5 )
      return v6->vfptr;
    ++v5;
  }
  return 0i64;
}
