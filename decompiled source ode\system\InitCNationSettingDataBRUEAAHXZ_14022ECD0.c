/*
 * Function: ?Init@CNationSettingDataBR@@UEAAHXZ
 * Address: 0x14022ECD0
 */

signed __int64 __fastcall CNationSettingDataBR::Init(CNationSettingDataBR *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v4; // rax@9
  __int64 v5; // [sp+0h] [bp-58h]@1
  INationGameGuardSystem *v6; // [sp+20h] [bp-38h]@11
  INationGameGuardSystem *v7; // [sp+28h] [bp-30h]@11
  CHackShieldExSystem *v8; // [sp+30h] [bp-28h]@8
  __int64 v9; // [sp+38h] [bp-20h]@4
  INationGameGuardSystem *v10; // [sp+40h] [bp-18h]@9
  CNationSettingDataBR *v11; // [sp+60h] [bp+8h]@1

  v11 = this;
  v1 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = -2i64;
  if ( CNationSettingData::GetFireGuardEnableSetting((CNationSettingData *)&v11->vfptr) )
  {
    MyMessageBox("CNationSettingDataBR::Init()", "FireGuard Setting Enabled Invalid!");
    CLogFile::Write(&stru_1799C8F30, "CNationSettingDataBR::Init() : FireGuard Setting Enabled Invalid!");
    result = 0xFFFFFFFFi64;
  }
  else if ( CNationSettingData::GetTimeLimitEnableSetting((CNationSettingData *)&v11->vfptr) )
  {
    MyMessageBox("CNationSettingDataBR::Init()", "Time Limit System Setting Eanbled Invalid!");
    CLogFile::Write(&stru_1799C8F30, "CNationSettingDataBR::Init() : Time Limit System Setting Eanbled Invalid!");
    result = 4294967294i64;
  }
  else
  {
    v8 = (CHackShieldExSystem *)operator new(0x38ui64);
    if ( v8 )
    {
      CHackShieldExSystem::CHackShieldExSystem(v8);
      v10 = (INationGameGuardSystem *)v4;
    }
    else
    {
      v10 = 0i64;
    }
    v7 = v10;
    v6 = v10;
    if ( (unsigned __int8)CHackShieldExSystem::Init<HACKSHEILD_PARAM_ANTICP>() )
    {
      v11->m_pGameGuardSystem = v6;
      if ( (unsigned __int8)((int (__fastcall *)(CNationSettingDataBR *))v11->vfptr->ReadSystemPass)(v11) )
      {
        result = 0i64;
      }
      else
      {
        MyMessageBox("CNationSettingDataBR::Init()", "All Event Error!");
        CLogFile::Write(&stru_1799C8F30, "CNationSettingDataBR::Init() : All Event Error!");
        result = 0xFFFFFFFFi64;
      }
    }
    else
    {
      result = 4294967293i64;
    }
  }
  return result;
}
