/*
 * Function: ?Update_Post@CUserDB@@QEAAXHKHEH_KKK0@Z
 * Address: 0x140117900
 */

void __fastcall CUserDB::Update_Post(CUserDB *this, int n, unsigned int dwSerial, int nNumber, char byState, int nKey, unsigned __int64 dwDur, unsigned int dwUpt, unsigned int dwGold, unsigned __int64 lnUID)
{
  if ( n >= 0 && n < 50 )
  {
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].dwPSSerial = dwSerial;
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].nNumber = nNumber;
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].byState = byState;
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].nKey = nKey;
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].dwDur = dwDur;
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].dwUpt = dwUpt;
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].dwGold = dwGold;
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].lnUID = lnUID;
    this->m_AvatorData.dbPostData.dbPost.m_PostList[n].bUpdate = 1;
    this->m_AvatorData.dbPostData.dbPost.m_bUpdate = 1;
    this->m_bDataUpdate = 1;
  }
}
