/*
 * Function: ?GetTerm@CGuildBattleState@GUILD_BATTLE@@UEAA?AVCTimeSpan@ATL@@XZ
 * Address: 0x14007F7C0
 */

ATL::CTimeSpan *__fastcall GUILD_BATTLE::CGuildBattleState::GetTerm(GUILD_BATTLE::CGuildBattleState *this, ATL::CTimeSpan *result)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  ATL::CTimeSpan *v6; // [sp+38h] [bp+10h]@1

  v6 = result;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  ATL::CTimeSpan::CTimeSpan(result, 0i64);
  return v6;
}
