/*
 * Function: ?DividePvpPoint@CNormalGuildBattle@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403E6490
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::DividePvpPoint(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CWeeklyGuildRankManager *v3; // rax@6
  CWeeklyGuildRankManager *v4; // rax@6
  const char *v5; // rax@6
  CWeeklyGuildRankManager *v6; // rax@7
  CWeeklyGuildRankManager *v7; // rax@7
  const char *v8; // rax@7
  __int64 v9; // [sp+0h] [bp-A8h]@1
  double v10; // [sp+20h] [bp-88h]@6
  int v11; // [sp+28h] [bp-80h]@6
  const char *v12; // [sp+30h] [bp-78h]@6
  unsigned int v13; // [sp+38h] [bp-70h]@6
  double v14; // [sp+40h] [bp-68h]@6
  int v15; // [sp+48h] [bp-60h]@6
  bool v16; // [sp+50h] [bp-58h]@6
  bool v17; // [sp+51h] [bp-57h]@6
  unsigned int dwGuildSerial; // [sp+54h] [bp-54h]@6
  unsigned int v19; // [sp+58h] [bp-50h]@6
  int v20; // [sp+5Ch] [bp-4Ch]@6
  unsigned int v21; // [sp+60h] [bp-48h]@6
  const char *v22; // [sp+68h] [bp-40h]@6
  int v23; // [sp+70h] [bp-38h]@6
  unsigned int v24; // [sp+74h] [bp-34h]@6
  unsigned int v25; // [sp+78h] [bp-30h]@7
  int v26; // [sp+7Ch] [bp-2Ch]@7
  unsigned int v27; // [sp+80h] [bp-28h]@7
  const char *v28; // [sp+88h] [bp-20h]@7
  unsigned int v29; // [sp+90h] [bp-18h]@7
  int v30; // [sp+94h] [bp-14h]@7
  unsigned int v31; // [sp+98h] [bp-10h]@7
  GUILD_BATTLE::CNormalGuildBattle *v32; // [sp+B0h] [bp+8h]@1

  v32 = this;
  v1 = &v9;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v32->m_pkWin && v32->m_pkLose )
  {
    GUILD_BATTLE::CNormalGuildBattleGuild::IncPvpPoint(v32->m_pkWin, 2000.0, 1, &v32->m_kLogger);
    GUILD_BATTLE::CNormalGuildBattleGuild::IncPvpPoint(v32->m_pkLose, 100.0, 0, &v32->m_kLogger);
    v25 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(v32->m_pkLose);
    v6 = CWeeklyGuildRankManager::Instance();
    v26 = CWeeklyGuildRankManager::PushDQSIncWeeklyPvpPointSum(v6, v25, 100.0);
    v27 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(v32->m_pkLose);
    v28 = GUILD_BATTLE::CNormalGuildBattleGuild::GetColorName(v32->m_pkLose);
    v29 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(v32->m_pkWin);
    v7 = CWeeklyGuildRankManager::Instance();
    v30 = CWeeklyGuildRankManager::PushDQSIncWeeklyPvpPointSum(v7, v29, 2000.0);
    v31 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(v32->m_pkWin);
    v8 = GUILD_BATTLE::CNormalGuildBattleGuild::GetColorName(v32->m_pkWin);
    v15 = v26;
    v14 = DOUBLE_100_0;
    v13 = v27;
    v12 = v28;
    v11 = v30;
    v10 = DOUBLE_2000_0;
    GUILD_BATTLE::CNormalGuildBattleLogger::Log(
      &v32->m_kLogger,
      "CNormalGuildBattle::DividePVPPoint() : Win %s(%u)%.0f DB(%d), Lose %s(%u)%.0f DB(%d)",
      v8,
      v31);
  }
  else
  {
    GUILD_BATTLE::CNormalGuildBattleGuild::IncPvpPoint(&v32->m_k1P, 500.0, 2, &v32->m_kLogger);
    GUILD_BATTLE::CNormalGuildBattleGuild::IncPvpPoint(&v32->m_k2P, 500.0, 2, &v32->m_kLogger);
    dwGuildSerial = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v32->m_k1P);
    v3 = CWeeklyGuildRankManager::Instance();
    v16 = CWeeklyGuildRankManager::PushDQSIncWeeklyPvpPointSum(v3, dwGuildSerial, 500.0);
    v19 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v32->m_k2P);
    v4 = CWeeklyGuildRankManager::Instance();
    v17 = CWeeklyGuildRankManager::PushDQSIncWeeklyPvpPointSum(v4, v19, 500.0);
    v20 = v17;
    v21 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v32->m_k2P);
    v22 = GUILD_BATTLE::CNormalGuildBattleGuild::GetColorName(&v32->m_k2P);
    v23 = v16;
    v24 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v32->m_k1P);
    v5 = GUILD_BATTLE::CNormalGuildBattleGuild::GetColorName(&v32->m_k1P);
    v15 = v20;
    v14 = DOUBLE_500_0;
    v13 = v21;
    v12 = v22;
    v11 = v23;
    v10 = DOUBLE_500_0;
    GUILD_BATTLE::CNormalGuildBattleLogger::Log(
      &v32->m_kLogger,
      "CNormalGuildBattle::DividePVPPoint() : draw %s(%u)%.0f DB(%d), %s(%u)%.0f DB(%d)",
      v5,
      v24);
  }
}
