/*
 * Function: ?_Assign_n@?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@IEAAX_KAEBVCUnmannedTraderSchedule@@@Z
 * Address: 0x1403951F0
 */

void __fastcall std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Assign_n(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, unsigned __int64 _Count, CUnmannedTraderSchedule *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-138h]@1
  CUnmannedTraderSchedule v6; // [sp+28h] [bp-110h]@4
  char v7; // [sp+58h] [bp-E0h]@4
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *result; // [sp+70h] [bp-C8h]@4
  char v9; // [sp+78h] [bp-C0h]@4
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v10; // [sp+90h] [bp-A8h]@4
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > v11; // [sp+98h] [bp-A0h]@4
  char v12; // [sp+B0h] [bp-88h]@4
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v13; // [sp+C8h] [bp-70h]@4
  char v14; // [sp+D0h] [bp-68h]@4
  __int64 v15; // [sp+F0h] [bp-48h]@4
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v16; // [sp+F8h] [bp-40h]@4
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v17; // [sp+100h] [bp-38h]@4
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v18; // [sp+108h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v19; // [sp+110h] [bp-28h]@4
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v20; // [sp+140h] [bp+8h]@1
  unsigned __int64 v21; // [sp+148h] [bp+10h]@1

  v21 = _Count;
  v20 = this;
  v3 = &v5;
  for ( i = 74i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = -2i64;
  qmemcpy(&v14, _Val, 0x20ui64);
  qmemcpy(&v6, &v14, sizeof(v6));
  result = (std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)&v7;
  v10 = (std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)&v9;
  v16 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::end(
          v20,
          (std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)&v7);
  v17 = v16;
  v18 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::begin(v20, v10);
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::erase(v20, &v11, v18, v17);
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(&v11);
  v13 = (std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)&v12;
  v19 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::begin(
          v20,
          (std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)&v12);
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::insert(v20, v19, v21, &v6);
  CUnmannedTraderSchedule::~CUnmannedTraderSchedule(&v6);
}
