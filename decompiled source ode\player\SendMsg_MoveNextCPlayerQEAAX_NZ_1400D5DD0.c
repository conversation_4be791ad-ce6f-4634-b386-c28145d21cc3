/*
 * Function: ?SendMsg_MoveNext@CPlayer@@QEAAX_N@Z
 * Address: 0x1400D5DD0
 */

void __usercall CPlayer::SendMsg_MoveNext(CPlayer *this@<rcx>, bool bOtherSend@<dl>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-98h]@1
  char szMsg[4]; // [sp+38h] [bp-60h]@4
  __int16 pShort; // [sp+3Ch] [bp-5Ch]@4
  __int16 v8; // [sp+42h] [bp-56h]@4
  __int16 v9; // [sp+44h] [bp-54h]@4
  __int16 v10; // [sp+46h] [bp-52h]@4
  char v11; // [sp+48h] [bp-50h]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v13; // [sp+65h] [bp-33h]@4
  unsigned __int64 v14; // [sp+80h] [bp-18h]@4
  CPlayer *v15; // [sp+A0h] [bp+8h]@1
  bool v16; // [sp+A8h] [bp+10h]@1

  v16 = bOtherSend;
  v15 = this;
  v3 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = (unsigned __int64)&v5 ^ _security_cookie;
  *(_DWORD *)szMsg = v15->m_dwObjSerial;
  FloatToShort(v15->m_fCurPos, &pShort, 3);
  v8 = (signed int)ffloor(v15->m_fTarPos[0]);
  v9 = (signed int)ffloor(v15->m_fTarPos[2]);
  CPlayer::GetAddSpeed(v15);
  v10 = (signed int)ffloor(a3);
  v11 = v15->m_byMoveDirect;
  pbyType = 4;
  v13 = 4;
  if ( v16 )
    CGameObject::CircleReport((CGameObject *)&v15->vfptr, &pbyType, szMsg, 17, 1);
  else
    CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, szMsg, 0x11u);
}
