/*
 * Function: ?mc_RespawnMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z
 * Address: 0x1402759D0
 */

bool __fastcall mc_RespawnMonster(strFILE *fstr, CDarkHoleDungeonQuestSetup *pSetup, char *pszoutErrMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // rax@26
  __int64 v7; // [sp+0h] [bp-268h]@1
  _react_obj poutReactObject; // [sp+48h] [bp-220h]@4
  _react_area poutReactArea; // [sp+88h] [bp-1E0h]@9
  char poutszWord; // [sp+C0h] [bp-1A8h]@11
  int pnoutVal; // [sp+154h] [bp-114h]@15
  char Str1; // [sp+180h] [bp-E8h]@17
  int v13; // [sp+214h] [bp-54h]@21
  int v14; // [sp+224h] [bp-44h]@23
  __respawn_monster *v15; // [sp+230h] [bp-38h]@28
  __respawn_monster *v16; // [sp+238h] [bp-30h]@25
  __int64 v17; // [sp+240h] [bp-28h]@4
  __respawn_monster *v18; // [sp+248h] [bp-20h]@26
  unsigned __int64 v19; // [sp+250h] [bp-18h]@4
  strFILE *fstra; // [sp+270h] [bp+8h]@1
  CDarkHoleDungeonQuestSetup *pSetupa; // [sp+278h] [bp+10h]@1
  char *v22; // [sp+280h] [bp+18h]@1

  v22 = pszoutErrMsg;
  pSetupa = pSetup;
  fstra = fstr;
  v3 = &v7;
  for ( i = 152i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v17 = -2i64;
  v19 = (unsigned __int64)&v7 ^ _security_cookie;
  _react_obj::_react_obj(&poutReactObject);
  if ( GetReactObject(fstra, pSetupa, &poutReactObject, 1, 6u, v22, 0i64) )
  {
    if ( (signed int)poutReactObject.wNum > 0 && (signed int)poutReactObject.wNum <= 128 )
    {
      _react_area::_react_area(&poutReactArea);
      if ( GetReactArea(fstra, pSetupa, &poutReactArea, 3u, v22, 0i64) )
      {
        if ( strFILE::word(fstra, &poutszWord) )
        {
          if ( !strcmp_0(&poutszWord, "term:") )
          {
            if ( strFILE::word(fstra, &pnoutVal) )
            {
              if ( strFILE::word(fstra, &Str1) )
              {
                if ( !strcmp_0(&Str1, "lim:") )
                {
                  if ( strFILE::word(fstra, &v13) )
                  {
                    v14 = pSetupa->m_pCurLoadMission->nRespawnMonsterNum;
                    if ( v14 < 32 )
                    {
                      v16 = (__respawn_monster *)operator new(0x48ui64);
                      if ( v16 )
                      {
                        __respawn_monster::__respawn_monster(v16);
                        v18 = (__respawn_monster *)v6;
                      }
                      else
                      {
                        v18 = 0i64;
                      }
                      v15 = v18;
                      pSetupa->m_pCurLoadMission->pRespawnMonster[v14] = v18;
                      _react_obj::copy(&pSetupa->m_pCurLoadMission->pRespawnMonster[v14]->ReactObj, &poutReactObject);
                      _react_area::copy(&pSetupa->m_pCurLoadMission->pRespawnMonster[v14]->ReactArea, &poutReactArea);
                      pSetupa->m_pCurLoadMission->pRespawnMonster[v14]->dwTermMSec = 1000 * pnoutVal;
                      pSetupa->m_pCurLoadMission->pRespawnMonster[v14]->nLim = v13;
                      ++pSetupa->m_pCurLoadMission->nRespawnMonsterNum;
                      result = 1;
                    }
                    else
                    {
                      result = _false(fstra, pSetupa);
                    }
                  }
                  else
                  {
                    result = _false(fstra, pSetupa);
                  }
                }
                else
                {
                  result = _false(fstra, pSetupa);
                }
              }
              else
              {
                result = _false(fstra, pSetupa);
              }
            }
            else
            {
              result = _false(fstra, pSetupa);
            }
          }
          else
          {
            result = _false(fstra, pSetupa);
          }
        }
        else
        {
          result = _false(fstra, pSetupa);
        }
      }
      else
      {
        result = _false(fstra, pSetupa);
      }
    }
    else
    {
      result = _false(fstra, pSetupa);
    }
  }
  else
  {
    result = _false(fstra, pSetupa);
  }
  return result;
}
