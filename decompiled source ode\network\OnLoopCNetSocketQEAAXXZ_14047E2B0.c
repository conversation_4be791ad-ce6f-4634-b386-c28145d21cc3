/*
 * Function: ?OnLoop@CNetSocket@@QEAAXXZ
 * Address: 0x14047E2B0
 */

void __fastcall CNetSocket::OnLoop(CNetSocket *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-68h]@1
  DWORD v4; // [sp+20h] [bp-48h]@5
  unsigned int pdwOutIndex; // [sp+34h] [bp-34h]@6
  _IP_CHECK_NODE *v6; // [sp+48h] [bp-20h]@7
  unsigned int v7; // [sp+50h] [bp-18h]@7
  CNetSocket *v8; // [sp+70h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CNetTimer::CountingTimer(&v8->m_tmrListCheckerIPCheck) )
  {
    v4 = timeGetTime();
    while ( CNetIndexList::CopyFront(&v8->m_listIPCheck, &pdwOutIndex) )
    {
      v6 = &v8->m_ndIPCheck[pdwOutIndex];
      v7 = v4 - v6->dwWaitStartTime;
      if ( v7 <= v8->m_SockType.m_dwIPCheckTerm )
        break;
      CNetIndexList::PopNode_Front(&v8->m_listIPCheck, &pdwOutIndex);
      CNetIndexList::PushNode_Back(&v8->m_listIPCheck_Empty, pdwOutIndex);
    }
  }
}
