/*
 * Function: ?_buybygold_set_cashitem_dblog_sheet@CashItemRemoteStore@@AEAAXPEAVCPlayer@@PEAU_param_cashitem_dblog@@@Z
 * Address: 0x1402FF0E0
 */

void __fastcall CashItemRemoteStore::_buybygold_set_cashitem_dblog_sheet(CashItemRemoteStore *this, CPlayer *pOne, _param_cashitem_dblog *pSheet)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CashItemRemoteStore *v6; // [sp+30h] [bp+8h]@1
  _param_cashitem_dblog *v7; // [sp+40h] [bp+18h]@1

  v7 = pSheet;
  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pSheet->byLv = CPlayerDB::GetLevel(&pOne->m_Param);
  v7->in_bAdjustDiscount = CashItemRemoteStore::is_cde_time(v6);
  v7->in_bOneN_One = CashItemRemoteStore::IsEventTime(v6, 1);
  v7->in_bSetDiscount = CashItemRemoteStore::IsEventTime(v6, 0);
  v7->in_bLimited_Sale = CashItemRemoteStore::IsEventTime(v6, 2);
}
