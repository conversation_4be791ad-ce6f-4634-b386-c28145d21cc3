/*
 * Function: j_??$_Uninit_move@PEAPEAU_PVP_RANK_DATA@@PEAPEAU1@V?$allocator@PEAU_PVP_RANK_DATA@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAU_PVP_RANK_DATA@@PEAPEAU1@00AEAV?$allocator@PEAU_PVP_RANK_DATA@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000789C
 */

_PVP_RANK_DATA **__fastcall std::_Uninit_move<_PVP_RANK_DATA * *,_PVP_RANK_DATA * *,std::allocator<_PVP_RANK_DATA *>,std::_Undefined_move_tag>(_PVP_RANK_DATA **_First, _PVP_RANK_DATA **_Last, _PVP_RANK_DATA **_Dest, std::allocator<_PVP_RANK_DATA *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<_PVP_RANK_DATA * *,_PVP_RANK_DATA * *,std::allocator<_PVP_RANK_DATA *>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
