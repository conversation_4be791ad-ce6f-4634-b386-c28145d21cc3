/*
 * Function: _stdext::_Hash_stdext::_Hmap_traits_ScheduleMSG_____ptr64_unsigned_long_stdext::hash_compare_ScheduleMSG_____ptr64_std::less_ScheduleMSG_____ptr64____std::allocator_std::pair_ScheduleMSG_____ptr64_const_unsigned_long____0___::insert_::_1_::dtor$0
 * Address: 0x140422540
 */

void __fastcall stdext::_Hash_stdext::_Hmap_traits_ScheduleMSG_____ptr64_unsigned_long_stdext::hash_compare_ScheduleMSG_____ptr64_std::less_ScheduleMSG_____ptr64____std::allocator_std::pair_ScheduleMSG_____ptr64_const_unsigned_long____0___::insert_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>::~_Iterator<0>((std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *)(a2 + 72));
}
