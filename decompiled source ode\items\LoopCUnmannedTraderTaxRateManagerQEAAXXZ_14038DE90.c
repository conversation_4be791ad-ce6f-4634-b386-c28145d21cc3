/*
 * Function: ?Loop@CUnmannedTraderTaxRateManager@@QEAAXXZ
 * Address: 0x14038DE90
 */

void __fastcall CUnmannedTraderTaxRateManager::Loop(CUnmannedTraderTaxRateManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  TRC_AutoTrade **v3; // rax@10
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@8
  CUnmannedTraderTaxRateManager *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_pkTimer
    && !std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::empty(&v6->m_vecTRC)
    && CMyTimer::CountingTimer(v6->m_pkTimer) )
  {
    for ( j = 0; j < 3; ++j )
    {
      v3 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](&v6->m_vecTRC, j);
      TRC_AutoTrade::ChangeTaxRate(*v3);
    }
  }
}
