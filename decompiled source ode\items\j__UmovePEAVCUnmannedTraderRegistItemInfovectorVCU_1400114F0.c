/*
 * Function: j_??$_Umove@PEAVCUnmannedTraderRegistItemInfo@@@?$vector@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@IEAAPEAVCUnmannedTraderRegistItemInfo@@PEAV2@00@Z
 * Address: 0x1400114F0
 */

CUnmannedTraderRegistItemInfo *__fastcall std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Umove<CUnmannedTraderRegistItemInfo *>(std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *this, CUnmannedTraderRegistItemInfo *_First, CUnmannedTraderRegistItemInfo *_Last, CUnmannedTraderRegistItemInfo *_Ptr)
{
  return std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Umove<CUnmannedTraderRegistItemInfo *>(
           this,
           _First,
           _Last,
           _Ptr);
}
