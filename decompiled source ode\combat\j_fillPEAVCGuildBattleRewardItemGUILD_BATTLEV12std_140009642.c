/*
 * Function: j_??$fill@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@V12@@std@@YAXPEAVCGuildBattleRewardItem@GUILD_BATTLE@@0AEBV12@@Z
 * Address: 0x140009642
 */

void __fastcall std::fill<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem>(GUILD_BATTLE::CGuildBattleRewardItem *_First, GUILD_BATTLE::CGuildBattleRewardItem *_Last, GUILD_BATTLE::CGuildBattleRewardItem *_Val)
{
  std::fill<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem>(_First, _Last, _Val);
}
