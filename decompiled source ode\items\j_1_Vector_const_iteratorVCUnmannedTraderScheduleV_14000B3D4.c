/*
 * Function: j_??1?$_Vector_const_iterator@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@QEAA@XZ
 * Address: 0x14000B3D4
 */

void __fastcall std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this)
{
  std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(this);
}
