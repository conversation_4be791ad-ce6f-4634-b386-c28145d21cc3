/*
 * Function: ?SendMsg_InPcBangTime@CCouponMgr@@QEAAXG@Z
 * Address: 0x1403FE040
 */

void __fastcall CCouponMgr::SendMsg_InPcBangTime(CCouponMgr *this, unsigned __int16 wIdx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@4
  __int64 v5; // [sp+0h] [bp-88h]@1
  _notify_cont_play_time_zocl v6; // [sp+30h] [bp-58h]@4
  _notify_cont_play_time_zocl v7; // [sp+44h] [bp-44h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v9; // [sp+65h] [bp-23h]@4
  CCouponMgr *v10; // [sp+90h] [bp+8h]@1
  unsigned __int16 v11; // [sp+98h] [bp+10h]@1

  v11 = wIdx;
  v10 = this;
  v2 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6.byContTime = v10->m_dwContTime / 0x3C;
  v6.byContMin = v10->m_dwContTime % 0x3C;
  v7 = v6;
  pbyType = 59;
  v9 = 7;
  v4 = _notify_cont_play_time_zocl::size(&v7);
  CNetProcess::LoadSendMsg(unk_1414F2088, v11, &pbyType, &v7.byContTime, v4);
}
