/*
 * Function: ?FindEmptyNPC@@YAPEAVCMerchant@@PEAV1@H@Z
 * Address: 0x140139CD0
 */

CMerchant *__fastcall FindEmptyNPC(CMerchant *pList, int nMax)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  DWORD v6; // [sp+20h] [bp-18h]@4
  int j; // [sp+24h] [bp-14h]@4
  DWORD v8; // [sp+28h] [bp-10h]@8
  CMerchant *v9; // [sp+40h] [bp+8h]@1
  int v10; // [sp+48h] [bp+10h]@1

  v10 = nMax;
  v9 = pList;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = timeGetTime();
  for ( j = 0; j < v10; ++j )
  {
    if ( !v9[j].m_bLive )
    {
      v8 = v6 - v9[j].m_dwLastDestroyTime;
      if ( v8 > 0x7530 )
        return &v9[j];
    }
  }
  return 0i64;
}
