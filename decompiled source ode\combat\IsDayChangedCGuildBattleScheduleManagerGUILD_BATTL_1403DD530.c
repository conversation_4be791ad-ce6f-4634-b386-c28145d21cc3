/*
 * Function: ?IsDayChanged@CGuildBattleScheduleManager@GUILD_BATTLE@@AEAAHXZ
 * Address: 0x1403DD530
 */

signed __int64 __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::IsDayChanged(GUILD_BATTLE::CGuildBattleScheduleManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  ATL::CTimeSpan *v4; // rax@5
  signed __int64 v5; // rax@6
  __int64 v6; // [sp+0h] [bp-88h]@1
  ATL::CTime result; // [sp+38h] [bp-50h]@4
  ATL::CTime v8; // [sp+58h] [bp-30h]@5
  int v9; // [sp+64h] [bp-24h]@5
  int v10; // [sp+68h] [bp-20h]@5
  ATL::CTimeSpan v11; // [sp+70h] [bp-18h]@5
  int v12; // [sp+78h] [bp-10h]@4
  GUILD_BATTLE::CGuildBattleScheduleManager *v13; // [sp+90h] [bp+8h]@1

  v13 = this;
  v1 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  ATL::CTime::GetTickCount(&result);
  v12 = ATL::CTime::GetDay(v13->m_pkOldDayTime);
  v3 = ATL::CTime::GetDay(&result);
  if ( v12 == v3 )
  {
    v5 = 0i64;
  }
  else
  {
    ATL::CTimeSpan::CTimeSpan(&v11, 1, 0, 0, 0);
    ATL::CTime::operator+(v13->m_pkOldDayTime, &v8, (ATL::CTimeSpan)v4->m_timeSpan);
    v9 = ATL::CTime::GetDay(&result);
    v10 = ATL::CTime::GetDay(&v8);
    if ( v9 == v10 )
    {
      v13->m_pkOldDayTime->m_time = result.m_time;
      v5 = 1i64;
    }
    else
    {
      v13->m_pkOldDayTime->m_time = result.m_time;
      v5 = 0xFFFFFFFFi64;
    }
  }
  return v5;
}
