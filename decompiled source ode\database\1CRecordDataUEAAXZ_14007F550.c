/*
 * Function: ??1CRecordData@@UEAA@XZ
 * Address: 0x14007F550
 */

void __fastcall CRecordData::~CRecordData(CRecordData *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@5
  void *v5; // [sp+28h] [bp-20h]@8
  void *v6; // [sp+30h] [bp-18h]@10
  void *v7; // [sp+38h] [bp-10h]@12
  CRecordData *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8->vfptr = (CRecordDataVtbl *)&CRecordData::`vftable';
  if ( v8->m_ppsRecord )
  {
    for ( j = 0; j < v8->m_nLowNum; ++j )
    {
      if ( v8->m_ppsRecord[j] )
      {
        v5 = v8->m_ppsRecord[j];
        operator delete[](v5);
        v8->m_ppsRecord[j] = 0i64;
      }
    }
    v6 = v8->m_ppsRecord;
    operator delete[](v6);
    v8->m_ppsRecord = 0i64;
  }
  if ( v8->m_pdwHashList )
  {
    v7 = v8->m_pdwHashList;
    operator delete[](v7);
    v8->m_pdwHashList = 0i64;
  }
}
