/*
 * Function: ?CompleteReRegist@CUnmannedTraderController@@QEAAXPEAD@Z
 * Address: 0x140207530
 */

void __fastcall CUnmannedTraderController::CompleteReRegist(CUnmannedTraderController *this, char *pLoadData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfoTable *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-28h]@1
  char *pLoadDataa; // [sp+38h] [bp+10h]@1

  pLoadDataa = pLoadData;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = CUnmannedTraderUserInfoTable::Instance();
  CUnmannedTraderUserInfoTable::CompleteReRegist(v4, pLoadDataa);
}
