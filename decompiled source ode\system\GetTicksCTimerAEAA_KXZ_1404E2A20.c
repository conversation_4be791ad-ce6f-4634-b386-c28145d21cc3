/*
 * Function: ?GetTicks@CTimer@@AEAA_KXZ
 * Address: 0x1404E2A20
 */

unsigned __int64 __fastcall CTimer::GetTicks(CTimer *this)
{
  unsigned __int64 result; // rax@2
  LARGE_INTEGER PerformanceCount; // [sp+38h] [bp+10h]@1

  if ( QueryPerformanceCounter(&PerformanceCount) )
  {
    for ( result = PerformanceCount.QuadPart; !PerformanceCount.QuadPart; result = PerformanceCount.QuadPart )
      QueryPerformanceCounter(&PerformanceCount);
  }
  else
  {
    result = timeGetTime();
  }
  return result;
}
