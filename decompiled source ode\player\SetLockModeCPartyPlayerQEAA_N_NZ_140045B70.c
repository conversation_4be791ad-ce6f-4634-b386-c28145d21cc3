/*
 * Function: ?SetLockMode@CPartyPlayer@@QEAA_N_N@Z
 * Address: 0x140045B70
 */

char __fastcall CPartyPlayer::SetLockMode(CPartyPlayer *this, bool bLock)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPartyPlayer *v6; // [sp+30h] [bp+8h]@1
  bool v7; // [sp+38h] [bp+10h]@1

  v7 = bLock;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CPartyPlayer::IsPartyBoss(v6) )
  {
    if ( v6->m_bLock == v7 )
    {
      result = 0;
    }
    else
    {
      v6->m_bLock = v7;
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
