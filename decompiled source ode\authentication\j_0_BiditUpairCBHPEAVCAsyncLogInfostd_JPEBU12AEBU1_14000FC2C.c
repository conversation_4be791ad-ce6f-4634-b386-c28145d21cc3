/*
 * Function: j_??0?$_Bidit@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@_JPEBU12@AEBU12@@std@@QEAA@XZ
 * Address: 0x14000FC2C
 */

void __fastcall std::_Bidit<std::pair<int const,CAsyncLogInfo *>,__int64,std::pair<int const,CAsyncLogInfo *> const *,std::pair<int const,CAsyncLogInfo *> const &>::_Bidit<std::pair<int const,CAsyncLogInfo *>,__int64,std::pair<int const,CAsyncLogInfo *> const *,std::pair<int const,CAsyncLogInfo *> const &>(std::_Bidit<std::pair<int const ,CAsyncLogInfo *>,__int64,std::pair<int const ,CAsyncLogInfo *> const *,std::pair<int const ,CAsyncLogInfo *> const &> *this)
{
  std::_Bidit<std::pair<int const,CAsyncLogInfo *>,__int64,std::pair<int const,CAsyncLogInfo *> const *,std::pair<int const,CAsyncLogInfo *> const &>::_Bidit<std::pair<int const,CAsyncLogInfo *>,__int64,std::pair<int const,CAsyncLogInfo *> const *,std::pair<int const,CAsyncLogInfo *> const &>(this);
}
