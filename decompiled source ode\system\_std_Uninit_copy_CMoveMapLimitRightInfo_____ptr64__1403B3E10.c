/*
 * Function: _std::_Uninit_copy_CMoveMapLimitRightInfo_____ptr64_CMoveMapLimitRightInfo_____ptr64_std::allocator_CMoveMapLimitRightInfo____::_1_::catch$0
 * Address: 0x1403B3E10
 */

void __fastcall __noreturn std::_Uninit_copy_CMoveMapLimitRightInfo_____ptr64_CMoveMapLimitRightInfo_____ptr64_std::allocator_CMoveMapLimitRightInfo____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 80); *(_QWORD *)(i + 32) += 40i64 )
    std::allocator<CMoveMapLimitRightInfo>::destroy(
      *(std::allocator<CMoveMapLimitRightInfo> **)(i + 88),
      *(CMoveMapLimitRightInfo **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
