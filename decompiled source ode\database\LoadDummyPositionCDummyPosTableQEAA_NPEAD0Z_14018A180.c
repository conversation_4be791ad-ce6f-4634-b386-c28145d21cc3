/*
 * Function: ?LoadDummyPosition@CDummyPosTable@@QEAA_NPEAD0@Z
 * Address: 0x14018A180
 */

char __fastcall CDummyPosTable::LoadDummyPosition(CDummyPosTable *this, char *szTextFileName, char *szPrefix)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  size_t v6; // rax@8
  size_t v7; // rax@18
  __int64 v8; // [sp+0h] [bp-128h]@1
  FILE *File; // [sp+20h] [bp-108h]@4
  char Str1; // [sp+40h] [bp-E8h]@7
  char Src; // [sp+41h] [bp-E7h]@19
  int v12; // [sp+C4h] [bp-64h]@6
  int v13; // [sp+C8h] [bp-60h]@15
  int j; // [sp+CCh] [bp-5Ch]@19
  int v15; // [sp+D0h] [bp-58h]@15
  char *String; // [sp+D8h] [bp-50h]@19
  char *Source; // [sp+E0h] [bp-48h]@19
  int __n[2]; // [sp+F0h] [bp-38h]@12
  void *v19; // [sp+F8h] [bp-30h]@15
  void *__t; // [sp+100h] [bp-28h]@12
  __int64 v21; // [sp+108h] [bp-20h]@4
  void *v22; // [sp+110h] [bp-18h]@13
  unsigned __int64 v23; // [sp+118h] [bp-10h]@4
  CDummyPosTable *v24; // [sp+130h] [bp+8h]@1
  const char *Str; // [sp+140h] [bp+18h]@1

  Str = szPrefix;
  v24 = this;
  v3 = &v8;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v21 = -2i64;
  v23 = (unsigned __int64)&v8 ^ _security_cookie;
  File = fopen(szTextFileName, "rt");
  if ( File )
  {
    v12 = 0;
    while ( fscanf(File, "%s", &Str1) != -1 )
    {
      v6 = strlen_0(Str);
      if ( !strncmp(&Str1, Str, v6) )
      {
        ++v12;
      }
      else if ( !strcmp_0(&Str1, "[HelperObjectEnd]") )
      {
        break;
      }
    }
    v24->m_nDumPosDataNum = v12;
    *(_QWORD *)__n = v24->m_nDumPosDataNum;
    __t = operator new[](saturated_mul(0x9Cui64, *(unsigned __int64 *)__n));
    if ( __t )
    {
      `vector constructor iterator'(__t, 0x9Cui64, __n[0], (void *(__cdecl *)(void *))_dummy_position::_dummy_position);
      v22 = __t;
    }
    else
    {
      v22 = 0i64;
    }
    v19 = v22;
    v24->m_pDumPos = (_dummy_position *)v22;
    rewind(File);
    v13 = 0;
    v15 = 0;
    while ( fscanf(File, "%s", &Str1) != -1 )
    {
      if ( Str1 == 42 )
      {
        v7 = strlen_0(Str);
        if ( !strncmp(&Str1, Str, v7) )
        {
          v24->m_pDumPos[v13].m_wLineIndex = v15;
          String = _strdup(&Src);
          Source = _strlwr(String);
          strcpy_0(v24->m_pDumPos[v13].m_szCode, Source);
          free(String);
          for ( j = 0; j < 3; ++j )
            fscanf(File, "%d", &v24->m_pDumPos[v13].m_zLocalMin[j]);
          for ( j = 0; j < 3; ++j )
            fscanf(File, "%d", &v24->m_pDumPos[v13].m_zLocalMax[j]);
          ++v13;
        }
        ++v15;
      }
      else if ( !strcmp_0(&Str1, "[HelperObjectEnd]") )
      {
        break;
      }
    }
    fclose(File);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
