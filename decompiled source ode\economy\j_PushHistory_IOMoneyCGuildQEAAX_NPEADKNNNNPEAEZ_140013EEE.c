/*
 * Function: j_?PushHist<PERSON>_<PERSON><PERSON><PERSON>@CGuild@@QEAAX_NPEADKNNNNPEAE@Z
 * Address: 0x140013EEE
 */

void __fastcall CGuild::PushHistory_IOMoney(CGuild *this, bool bInput, char *pwszIOerName, unsigned int dwIOerSerial, long double dIODalant, long double dIOGold, long double dLeftDalant, long double dLeftGold, char *pbyDate)
{
  CGuild::PushHistory_IOMoney(
    this,
    bInput,
    pwszIOerName,
    dwIOerSerial,
    dIODalant,
    dIOGold,
    dLeftDalant,
    dLeftGold,
    pbyDate);
}
