/*
 * Function: ?Join<PERSON>uild@CGuildBattleController@@QEAAXHKK@Z
 * Address: 0x1400AD450
 */

void __fastcall CGuildBattleController::JoinGuild(CGuildBattleController *this, int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleManager *v6; // rax@4
  __int64 v7; // [sp+0h] [bp-28h]@1
  int na; // [sp+38h] [bp+10h]@1
  unsigned int dwGuildSeriala; // [sp+40h] [bp+18h]@1
  unsigned int dwCharacSeriala; // [sp+48h] [bp+20h]@1

  dwCharacSeriala = dwCharacSerial;
  dwGuildSeriala = dwGuildSerial;
  na = n;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v6 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
  GUILD_BATTLE::CNormalGuildBattleManager::JoinGuild(v6, na, dwGuildSeriala, dwCharacSeriala);
}
