/*
 * Function: ?GetLootAuthor@CPartyPlayer@@QEAAPEAVCPlayer@@XZ
 * Address: 0x140045C50
 */

CPlayer *__fastcall CPartyPlayer::GetLootAuthor(CPartyPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CPlayer *result; // rax@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  CPlayer *v5; // [sp+20h] [bp-18h]@6
  char v6; // [sp+28h] [bp-10h]@6
  CPartyPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CPartyPlayer::IsPartyMode(v7) )
  {
    v5 = 0i64;
    v6 = v7->m_pPartyBoss->m_byLootShareSystem;
    if ( v6 )
    {
      if ( v6 == 1 )
      {
        v5 = &g_Player + v7->m_pPartyBoss->m_wZoneIndex;
      }
      else if ( v6 == 2 )
      {
        if ( v7->m_pPartyBoss->m_pLootAuthor )
        {
          v5 = &g_Player + v7->m_pPartyBoss->m_pLootAuthor->m_wZoneIndex;
        }
        else
        {
          v5 = &g_Player + v7->m_pPartyBoss->m_wZoneIndex;
          v7->m_pPartyBoss->m_pLootAuthor = v7->m_pPartyBoss;
        }
      }
    }
    else
    {
      v5 = 0i64;
    }
    result = v5;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
