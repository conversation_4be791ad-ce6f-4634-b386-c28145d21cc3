/*
 * Function: _std::vector_CUnmannedTraderSchedule_std::allocator_CUnmannedTraderSchedule___::_Insert_n_::_1_::catch$0
 * Address: 0x140396340
 */

void __fastcall __noreturn std::vector_CUnmannedTraderSchedule_std::allocator_CUnmannedTraderSchedule___::_Insert_n_::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1

  v2 = a2;
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Destroy(
    *(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > **)(a2 + 240),
    *(CUnmannedTraderSchedule **)(a2 + 96),
    *(CUnmannedTraderSchedule **)(a2 + 104));
  std::allocator<CUnmannedTraderSchedule>::deallocate(
    (std::allocator<CUnmannedTraderSchedule> *)(*(_QWORD *)(v2 + 240) + 8i64),
    *(CUnmannedTraderSchedule **)(v2 + 96),
    *(_QWORD *)(v2 + 88));
  CxxThrowException_0(0i64, 0i64);
}
