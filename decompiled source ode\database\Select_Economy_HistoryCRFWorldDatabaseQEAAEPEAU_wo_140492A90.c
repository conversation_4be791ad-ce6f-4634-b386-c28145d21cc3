/*
 * Function: ?Select_Economy_History@CRFWorldDatabase@@QEAAEPEAU_worlddb_economy_history_info_array@@K@Z
 * Address: 0x140492A90
 */

char __fastcall CRFWorldDatabase::Select_Economy_History(CRFWorldDatabase *this, _worlddb_economy_history_info_array *pEconomyData, unsigned int dwDate)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-198h]@1
  void *SQLStmt; // [sp+20h] [bp-178h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-170h]@16
  SQLLEN v9; // [sp+38h] [bp-160h]@16
  __int16 v10; // [sp+44h] [bp-154h]@9
  char Dest; // [sp+60h] [bp-138h]@4
  int v12; // [sp+164h] [bp-34h]@4
  __int16 v13; // [sp+168h] [bp-30h]@16
  int j; // [sp+16Ch] [bp-2Ch]@16
  int k; // [sp+170h] [bp-28h]@18
  int l; // [sp+174h] [bp-24h]@26
  unsigned __int64 v17; // [sp+180h] [bp-18h]@4
  CRFWorldDatabase *v18; // [sp+1A0h] [bp+8h]@1
  _worlddb_economy_history_info_array *v19; // [sp+1A8h] [bp+10h]@1

  v19 = pEconomyData;
  v18 = this;
  v3 = &v6;
  for ( i = 100i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v17 = (unsigned __int64)&v6 ^ _security_cookie;
  v12 = 0;
  sprintf(&Dest, "{ CALL pSelect_Last_Economy_History ( %d )}", dwDate);
  if ( v18->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v18->vfptr, &Dest);
  if ( v18->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v18->vfptr) )
  {
    v10 = SQLExecDirectA_0(v18->m_hStmtSelect, &Dest, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v18->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v10, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v10, v18->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      while ( 1 )
      {
        v10 = SQLFetch_0(v18->m_hStmtSelect);
        if ( v10 && v10 != 1 )
          break;
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, 1u, 8, v19->EconomyData[v12].dTradeDalant, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, 2u, 8, &v19->EconomyData[v12].dTradeDalant[1], 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, 3u, 8, &v19->EconomyData[v12].dTradeDalant[2], 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, 4u, 8, &v19->EconomyData[v12], 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, 5u, 8, &v19->EconomyData[v12].dTradeGold[1], 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, 6u, 8, &v19->EconomyData[v12].dTradeGold[2], 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, 7u, 4, &v19->EconomyData[v12].dwManageValue, 0i64, &v9);
        v13 = 8;
        for ( j = 0; j < 3; ++j )
        {
          for ( k = 0; k < 3; ++k )
          {
            StrLen_or_IndPtr = &v9;
            SQLStmt = 0i64;
            v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 8, &v19->EconomyData[v12].dMineOre[k][j], 0i64, &v9);
            if ( v10 )
            {
              if ( v10 != 1 )
                break;
            }
          }
        }
        for ( j = 0; j < 3; ++j )
        {
          for ( l = 0; l < 3; ++l )
          {
            StrLen_or_IndPtr = &v9;
            SQLStmt = 0i64;
            v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 8, &v19->EconomyData[v12].dCutOre[l][j], 0i64, &v9);
            if ( v10 )
            {
              if ( v10 != 1 )
                break;
            }
          }
        }
        ++v12;
        if ( v10 )
        {
          if ( v10 != 1 )
            break;
        }
      }
      v19->wRowCount = v12;
      if ( v18->m_hStmtUpdate )
        SQLCloseCursor_0(v18->m_hStmtSelect);
      if ( v18->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v18->vfptr, "%s Success", &Dest);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v18->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
