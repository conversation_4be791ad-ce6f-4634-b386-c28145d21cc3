/*
 * Function: ?Init@MD5@@QEAAXXZ
 * Address: 0x140442210
 */

void __fastcall MD5::Init(MD5 *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  MD5 *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->count[1] = 0;
  v4->count[0] = 0;
  v4->state[0] = 1732584193;
  v4->state[1] = -271733879;
  v4->state[2] = -1732584194;
  v4->state[3] = 271733878;
  memset_0(v4->buffer, 0, 0x40ui64);
}
