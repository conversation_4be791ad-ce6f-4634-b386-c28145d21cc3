/*
 * Function: ?Encrypt@TF_EncryptorBase@CryptoPP@@UEBAXAEAVRandomNumberGenerator@2@PEBE_KPEAEAEBVNameValuePairs@2@@Z
 * Address: 0x140623980
 */

void __fastcall CryptoPP::TF_EncryptorBase::Encrypt(CryptoPP::TF_EncryptorBase *this, struct CryptoPP::RandomNumberGenerator *a2, const unsigned __int8 *a3, unsigned __int64 a4, unsigned __int8 *a5, const struct CryptoPP::NameValuePairs *a6)
{
  unsigned __int64 v6; // rax@1
  __int64 v7; // rax@2
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v8; // rax@2
  unsigned __int64 v9; // rax@4
  __int64 *v10; // rax@4
  __int64 v11; // rax@4
  __int64 v12; // rax@4
  unsigned __int64 v13; // rax@4
  char *v14; // rax@4
  CryptoPP::Integer *v15; // rax@4
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v16; // [sp+40h] [bp-1B8h]@4
  CryptoPP::InvalidArgument v17; // [sp+58h] [bp-1A0h]@2
  char v18; // [sp+A8h] [bp-150h]@2
  char v19; // [sp+D8h] [bp-120h]@2
  CryptoPP::Integer v20; // [sp+108h] [bp-F0h]@4
  CryptoPP::Integer v21; // [sp+130h] [bp-C8h]@4
  __int64 v22; // [sp+158h] [bp-A0h]@1
  CryptoPP::ClonableVtbl *v23; // [sp+160h] [bp-98h]@2
  __int64 v24; // [sp+168h] [bp-90h]@2
  __int64 v25; // [sp+170h] [bp-88h]@2
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v26; // [sp+178h] [bp-80h]@2
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *s; // [sp+180h] [bp-78h]@2
  CryptoPP::TF_Base<CryptoPP::RandomizedTrapdoorFunction,CryptoPP::PK_EncryptionMessageEncodingMethod>Vtbl *v28; // [sp+188h] [bp-70h]@4
  __int64 *v29; // [sp+190h] [bp-68h]@4
  __int64 v30; // [sp+198h] [bp-60h]@4
  __int64 v31; // [sp+1A0h] [bp-58h]@4
  CryptoPP::TF_Base<CryptoPP::RandomizedTrapdoorFunction,CryptoPP::PK_EncryptionMessageEncodingMethod>Vtbl *v32; // [sp+1A8h] [bp-50h]@4
  __int64 v33; // [sp+1B0h] [bp-48h]@4
  unsigned __int64 v34; // [sp+1B8h] [bp-40h]@4
  unsigned __int64 v35; // [sp+1C0h] [bp-38h]@4
  CryptoPP::Integer *v36; // [sp+1C8h] [bp-30h]@4
  CryptoPP::Integer *v37; // [sp+1D0h] [bp-28h]@4
  CryptoPP::Integer *v38; // [sp+1D8h] [bp-20h]@4
  CryptoPP::Integer *v39; // [sp+1E0h] [bp-18h]@4
  CryptoPP::TF_EncryptorBase *v40; // [sp+200h] [bp+8h]@1
  struct CryptoPP::RandomNumberGenerator *v41; // [sp+208h] [bp+10h]@1
  const unsigned __int8 *v42; // [sp+210h] [bp+18h]@1
  unsigned __int64 v43; // [sp+218h] [bp+20h]@1

  v43 = a4;
  v42 = a3;
  v41 = a2;
  v40 = this;
  v22 = -2i64;
  LODWORD(v6) = ((int (*)(void))this->vfptr->FixedMaxPlaintextLength)();
  if ( v43 > v6 )
  {
    v23 = v40->vfptr;
    LODWORD(v7) = ((int (__fastcall *)(signed __int64, char *))v23[1].__vecDelDtor)((signed __int64)&v40->vfptr, &v18);
    v24 = v7;
    v25 = v7;
    LODWORD(v8) = std::operator+<char,std::char_traits<char>,std::allocator<char>>(
                    &v19,
                    v7,
                    ": message too long for this public key");
    v26 = v8;
    s = v8;
    CryptoPP::InvalidArgument::InvalidArgument(&v17, v8);
    CxxThrowException_0((__int64)&v17, (__int64)&TI3_AVInvalidArgument_CryptoPP__);
  }
  LODWORD(v9) = CryptoPP::TF_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::TF_Base<CryptoPP::RandomizedTrapdoorFunction,CryptoPP::PK_EncryptionMessageEncodingMethod>>::PaddedBlockByteLength(v40);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v16,
    v9);
  v28 = v40->vfptr;
  LODWORD(v10) = ((int (__fastcall *)(signed __int64))v28->GetMessageEncodingInterface)((signed __int64)&v40->vfptr);
  v29 = v10;
  LODWORD(v11) = CryptoPP::TF_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::TF_Base<CryptoPP::RandomizedTrapdoorFunction,CryptoPP::PK_EncryptionMessageEncodingMethod>>::PaddedBlockBitLength(v40);
  v30 = v11;
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v16);
  v31 = *v29;
  (*(void (__fastcall **)(__int64 *, struct CryptoPP::RandomNumberGenerator *, const unsigned __int8 *, unsigned __int64))(v31 + 24))(
    v29,
    v41,
    v42,
    v43);
  v32 = v40->vfptr;
  LODWORD(v12) = ((int (__fastcall *)(signed __int64))v32->GetTrapdoorFunctionInterface)((signed __int64)&v40->vfptr);
  v33 = v12;
  LODWORD(v13) = ((int (__fastcall *)(CryptoPP::TF_EncryptorBase *))v40->vfptr->FixedCiphertextLength)(v40);
  v34 = v13;
  v35 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v16);
  v14 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v16);
  v36 = CryptoPP::Integer::Integer(&v20, (const unsigned __int8 *)v14, v35, 0);
  v37 = v36;
  LODWORD(v15) = (*(int (__fastcall **)(__int64, CryptoPP::Integer *, struct CryptoPP::RandomNumberGenerator *, CryptoPP::Integer *))(*(_QWORD *)v33 + 40i64))(
                   v33,
                   &v21,
                   v41,
                   v36);
  v38 = v15;
  v39 = v15;
  CryptoPP::Integer::Encode(v15, a5, v34, 0);
  CryptoPP::Integer::~Integer(&v21);
  CryptoPP::Integer::~Integer(&v20);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v16);
}
