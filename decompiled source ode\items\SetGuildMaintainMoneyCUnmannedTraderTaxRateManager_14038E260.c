/*
 * Function: ?SetGuildMaintainMoney@CUnmannedTraderTaxRateManager@@QEAAXEKK@Z
 * Address: 0x14038E260
 */

void __fastcall CUnmannedTraderTaxRateManager::SetGuildMaintainMoney(CUnmannedTraderTaxRateManager *this, char byRace, unsigned int dwTax, unsigned int dwSeller)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  TRC_AutoTrade **v6; // rax@7
  __int64 v7; // [sp+0h] [bp-28h]@1
  CUnmannedTraderTaxRateManager *v8; // [sp+30h] [bp+8h]@1
  char v9; // [sp+38h] [bp+10h]@1
  unsigned int dwTaxa; // [sp+40h] [bp+18h]@1
  unsigned int dwSellera; // [sp+48h] [bp+20h]@1

  dwSellera = dwSeller;
  dwTaxa = dwTax;
  v9 = byRace;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( !std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::empty(&v8->m_vecTRC)
    && std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::size(&v8->m_vecTRC) > (unsigned __int8)v9 )
  {
    v6 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](&v8->m_vecTRC, (unsigned __int8)v9);
    TRC_AutoTrade::SetGuildMaintainMoney(*v6, dwTaxa, dwSellera);
  }
}
