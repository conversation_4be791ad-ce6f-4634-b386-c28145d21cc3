/*
 * Function: ?Inform_ConditionalEvent@CashItemRemoteStore@@QEAAXG@Z
 * Address: 0x1402FC970
 */

void __fastcall CashItemRemoteStore::Inform_ConditionalEvent(CashItemRemoteStore *this, unsigned __int16 wIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  char *pEMsg; // [sp+20h] [bp-28h]@9
  unsigned __int16 v6; // [sp+30h] [bp-18h]@6
  CashItemRemoteStore *v7; // [sp+50h] [bp+8h]@1
  unsigned __int16 v8; // [sp+58h] [bp+10h]@1

  v8 = wIndex;
  v7 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7->m_con_event.m_ini.m_bUseConEvent && CashItemRemoteStore::isConEventTime(v7) )
  {
    v6 = 0;
    if ( v7->m_con_event.m_ini.m_byEventKind == 2 )
      v6 = v7->m_cde.m_ini.m_wCsDiscount;
    switch ( v7->m_con_event.m_conevent_status )
    {
      case 2:
        pEMsg = v7->m_con_event.m_ini.m_szStartMsg;
        ICsSendInterface::SendMsg_ConditionalEventInform(
          v8,
          v7->m_con_event.m_ini.m_byEventKind,
          v6,
          v7->m_con_event.m_conevent_status,
          v7->m_con_event.m_ini.m_szStartMsg);
        break;
      case 3:
        pEMsg = v7->m_con_event.m_ini.m_szMiddletMsg;
        ICsSendInterface::SendMsg_ConditionalEventInform(
          v8,
          v7->m_con_event.m_ini.m_byEventKind,
          v6,
          v7->m_con_event.m_conevent_status,
          v7->m_con_event.m_ini.m_szMiddletMsg);
        break;
      case 4:
        pEMsg = v7->m_con_event.m_ini.m_szEndMsg;
        ICsSendInterface::SendMsg_ConditionalEventInform(
          v8,
          v7->m_con_event.m_ini.m_byEventKind,
          0,
          v7->m_con_event.m_conevent_status,
          v7->m_con_event.m_ini.m_szEndMsg);
        break;
    }
  }
}
