/*
 * Function: ??0_qry_case_post_send@@QEAA@XZ
 * Address: 0x1403282A0
 */

void __fastcall _qry_case_post_send::_qry_case_post_send(_qry_case_post_send *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _qry_case_post_send *Dst; // [sp+30h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  `vector constructor iterator'(
    Dst->List,
    0x138ui64,
    15,
    (void *(__cdecl *)(void *))_qry_case_post_send::__list::__list);
  memset_0(Dst, 0, 0x1250ui64);
}
