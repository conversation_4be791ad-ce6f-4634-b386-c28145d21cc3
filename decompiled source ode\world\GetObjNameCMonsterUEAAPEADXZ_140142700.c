/*
 * Function: ?GetObjName@CMonster@@UEAAPEADXZ
 * Address: 0x140142700
 */

char *__fastcall CMonster::GetObjName(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed int v3; // eax@4
  signed int v4; // ecx@4
  signed int v5; // edx@4
  signed __int64 v6; // r8@4
  __int64 v8; // [sp+0h] [bp-58h]@1
  int v9; // [sp+20h] [bp-38h]@4
  int v10; // [sp+28h] [bp-30h]@4
  int v11; // [sp+30h] [bp-28h]@4
  char *v12; // [sp+40h] [bp-18h]@4
  CMonster *v13; // [sp+60h] [bp+8h]@1

  v13 = this;
  v1 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = (signed int)ffloor(v13->m_fCurPos[2]);
  v4 = (signed int)ffloor(v13->m_fCurPos[1]);
  v5 = (signed int)ffloor(v13->m_fCurPos[0]);
  v6 = (signed __int64)v13->m_pCurMap->m_pMapSet->m_strCode;
  v12 = v13->m_pMonRec->m_strName;
  v11 = v3;
  v10 = v4;
  v9 = v5;
  sprintf(szName_6, "[MONSTER] >> %s (pos: %s {%d, %d, %d})", v12, v6);
  return szName_6;
}
