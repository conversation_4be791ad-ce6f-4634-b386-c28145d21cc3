/*
 * Function: ?SubCompleteBuyIncreaseVesion@CUnmannedTraderUserInfoTable@@AEAAXEE@Z
 * Address: 0x1403657B0
 */

void __fastcall CUnmannedTraderUserInfoTable::SubCompleteBuyIncreaseVesion(CUnmannedTraderUserInfoTable *this, char byDivision, char byClass)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderGroupItemInfoTable *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-28h]@1
  CUnmannedTraderUserInfoTable *v7; // [sp+30h] [bp+8h]@1
  char v8; // [sp+38h] [bp+10h]@1
  char v9; // [sp+40h] [bp+18h]@1

  v9 = byClass;
  v8 = byDivision;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = CUnmannedTraderGroupItemInfoTable::Instance();
  if ( !CUnmannedTraderGroupItemInfoTable::IncreaseVersion(v5, v8, v9) )
    CUnmannedTraderUserInfoTable::Log(
      v7,
      "CUnmannedTraderUserInfoTable::SubCompleteBuyIncreaseVesion( byDivision(%u), byClass(%u) )\r\n"
      "\t\tCUnmannedTraderGroupItemInfoTable::Instance()->IncreaseVersion( byDivision, byClass ) Fail!\r\n",
      (unsigned __int8)v8,
      (unsigned __int8)v9);
}
