/*
 * Function: ?_delay_check@_ATTACK_DELAY_CHECKER@@QEAA_NEGE@Z
 * Address: 0x14008ED10
 */

char __fastcall _ATTACK_DELAY_CHECKER::_delay_check(_ATTACK_DELAY_CHECKER *this, char code, unsigned __int16 index, char mastery)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v7; // [sp+0h] [bp-48h]@1
  DWORD v8; // [sp+20h] [bp-28h]@4
  int v9; // [sp+24h] [bp-24h]@4
  unsigned int v10; // [sp+28h] [bp-20h]@5
  DWORD v11; // [sp+2Ch] [bp-1Ch]@5
  unsigned int v12; // [sp+30h] [bp-18h]@18
  DWORD v13; // [sp+34h] [bp-14h]@18
  int j; // [sp+38h] [bp-10h]@30
  int k; // [sp+3Ch] [bp-Ch]@46
  _ATTACK_DELAY_CHECKER *v16; // [sp+50h] [bp+8h]@1
  char v17; // [sp+58h] [bp+10h]@1
  unsigned __int16 v18; // [sp+60h] [bp+18h]@1
  char v19; // [sp+68h] [bp+20h]@1

  v19 = mastery;
  v18 = index;
  v17 = code;
  v16 = this;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = timeGetTime();
  v9 = 0;
  v16->byTemp_EffectCode = v17;
  v16->wTemp_EffectIndex = v18;
  v16->byTemp_EffectMastery = v19;
  if ( (unsigned __int8)v17 == 255 )
  {
    v10 = v16->dwLastSFAttackTime + 1000;
    v11 = v8 - v10;
    if ( ((v8 - v10) & 0x80000000) != 0 )
    {
      if ( (signed int)-v11 > _ATTACK_DELAY_CHECKER::s_nSpareTime )
        return 0;
      v16->m_nNextAddTime = -v11;
    }
    v11 = v8 - v16->dwNextGenTime;
    if ( (v11 & 0x80000000) != 0 )
    {
      if ( (signed int)-v11 > _ATTACK_DELAY_CHECKER::s_nSpareTime )
        return 0;
      if ( v16->m_nNextAddTime )
      {
        if ( (signed int)-v11 > v16->m_nNextAddTime )
          v16->m_nNextAddTime = -v11;
      }
      else
      {
        v16->m_nNextAddTime = -v11;
      }
    }
    result = 1;
  }
  else
  {
    v12 = v16->dwLastGnAttackTime + 1000;
    v13 = v8 - v12;
    if ( ((v8 - v12) & 0x80000000) != 0 )
    {
      if ( (signed int)-v13 > _ATTACK_DELAY_CHECKER::s_nSpareTime )
        return 0;
      v16->m_nNextAddTime = -v13;
    }
    v13 = v8 - v16->dwNextEffTime;
    if ( (v13 & 0x80000000) != 0 )
    {
      if ( (signed int)-v13 > _ATTACK_DELAY_CHECKER::s_nSpareTime )
        return 0;
      if ( v16->m_nNextAddTime )
      {
        if ( (signed int)-v13 > v16->m_nNextAddTime )
          v16->m_nNextAddTime = -v13;
      }
      else
      {
        v16->m_nNextAddTime = -v13;
      }
    }
    for ( j = 0; j < 10; ++j )
    {
      if ( _ATTACK_DELAY_CHECKER::_mas_list::fill(&v16->MAS[j])
        && v16->MAS[j].byEffectCode == (unsigned __int8)v17
        && v16->MAS[j].byMastery == (unsigned __int8)v19 )
      {
        v13 = v8 - v16->MAS[j].dwNextTime;
        if ( (v13 & 0x80000000) != 0 )
        {
          if ( (signed int)-v13 > _ATTACK_DELAY_CHECKER::s_nSpareTime )
            return 0;
          if ( v16->m_nNextAddTime )
          {
            if ( (signed int)-v13 > v16->m_nNextAddTime )
              v16->m_nNextAddTime = -v13;
          }
          else
          {
            v16->m_nNextAddTime = -v13;
          }
        }
        break;
      }
    }
    for ( k = 0; k < 10; ++k )
    {
      if ( _ATTACK_DELAY_CHECKER::_eff_list::fill((_ATTACK_DELAY_CHECKER::_eff_list *)v16 + k)
        && v16->EFF[k].byEffectCode == (unsigned __int8)v17
        && v16->EFF[k].wEffectIndex == v18 )
      {
        v13 = v8 - v16->EFF[k].dwNextTime;
        if ( (v13 & 0x80000000) != 0 )
        {
          if ( (signed int)-v13 > _ATTACK_DELAY_CHECKER::s_nSpareTime )
            return 0;
          if ( v16->m_nNextAddTime )
          {
            if ( (signed int)-v13 > v16->m_nNextAddTime )
              v16->m_nNextAddTime = -v13;
          }
          else
          {
            v16->m_nNextAddTime = -v13;
          }
        }
        break;
      }
    }
    result = 1;
  }
  return result;
}
