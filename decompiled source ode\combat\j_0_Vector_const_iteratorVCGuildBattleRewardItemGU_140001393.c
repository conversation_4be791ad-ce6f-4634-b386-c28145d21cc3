/*
 * Function: j_??0?$_Vector_const_iterator@VCGuildBattleRewardItem@GUILD_BATTLE@@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@std@@QEAA@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@@Z
 * Address: 0x140001393
 */

void __fastcall std::_Vector_const_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Vector_const_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(std::_Vector_const_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *this, GUILD_BATTLE::CGuildBattleRewardItem *_Ptr)
{
  std::_Vector_const_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Vector_const_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(
    this,
    _Ptr);
}
