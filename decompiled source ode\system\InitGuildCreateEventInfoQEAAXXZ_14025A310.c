/*
 * Function: ?Init@GuildCreateEventInfo@@QEAAXXZ
 * Address: 0x14025A310
 */

void __fastcall GuildCreateEventInfo::Init(GuildCreateEventInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  GuildCreateEventInfo *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_bStartedEvent = 0;
  memset_0(&v4->m_EventInfo, 0, 0xCui64);
  memset_0(&v4->m_ModifyInfo, 0, 0xCui64);
  GetLastWriteFileTime("..\\WorldInfo\\WorldInfo.ini", &v4->m_ftWrite);
  GuildCreateEventInfo::ReadEventInfo(v4);
  memcpy_0(&v4->m_EventInfo, &v4->m_ModifyInfo, 0xCui64);
  if ( !GuildCreateEventInfo::CheckEventDate(v4) )
    GuildCreateEventInfo::SetConsumeDalantFree(v4, 0);
  CMyTimer::BeginTimer(&v4->m_tmDataFileCheckTime, 0x2710u);
}
