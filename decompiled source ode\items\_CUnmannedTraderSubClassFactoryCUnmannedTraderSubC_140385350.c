/*
 * Function: _CUnmannedTraderSubClassFactory::CUnmannedTraderSubClassFactory_::_1_::dtor$0
 * Address: 0x140385350
 */

void __fastcall CUnmannedTraderSubClassFactory::CUnmannedTraderSubClassFactory_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(*(std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > **)(a2 + 160));
}
