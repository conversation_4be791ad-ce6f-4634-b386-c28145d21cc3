/*
 * Function: ?Verify@HashTransformation@CryptoPP@@UEAA_NPEBE@Z
 * Address: 0x14044DED0
 */

int __fastcall CryptoPP::HashTransformation::Verify(CryptoPP::HashTransformation *this, const char *digest)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@4
  __int64 v6; // [sp+0h] [bp-38h]@1
  CryptoPP::ClonableVtbl *v7; // [sp+20h] [bp-18h]@4
  CryptoPP::HashTransformation *v8; // [sp+40h] [bp+8h]@1
  const char *v9; // [sp+48h] [bp+10h]@1

  v9 = digest;
  v8 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = ((int (__fastcall *)(CryptoPP::HashTransformation *))v8->vfptr[3].Clone)(v8);
  v7 = v8->vfptr;
  return ((int (__fastcall *)(CryptoPP::HashTransformation *, const char *, _QWORD))v7[8].__vecDelDtor)(
           v8,
           v9,
           (unsigned int)v4);
}
