/*
 * Function: _CUnmannedTraderUserInfo::CheckSellComplete_::_1_::dtor$1
 * Address: 0x140356890
 */

void __fastcall CUnmannedTraderUserInfo::CheckSellComplete_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>((std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)(a2 + 104));
}
