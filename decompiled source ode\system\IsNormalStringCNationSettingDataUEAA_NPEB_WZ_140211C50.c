/*
 * Function: ?IsNormalString@CNationSettingData@@UEAA_NPEB_W@Z
 * Address: 0x140211C50
 */

bool __fastcall CNationSettingData::IsNormalString(CNationSettingData *this, const wchar_t *wszString)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  wchar_t *wszException; // [sp+20h] [bp-18h]@4
  CNationSettingData *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  wszException = L"[]";
  return CNationSettingData::IsNormalStringDefProc(v7, wszString, L"[]");
}
