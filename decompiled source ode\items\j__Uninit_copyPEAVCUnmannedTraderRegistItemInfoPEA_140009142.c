/*
 * Function: j_??$_Uninit_copy@PEAVCUnmannedTraderRegistItemInfo@@PEAV1@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@YAPEAVCUnmannedTraderRegistItemInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderRegistItemInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140009142
 */

CUnmannedTraderRegistItemInfo *__fastcall std::_Uninit_copy<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *,std::allocator<CUnmannedTraderRegistItemInfo>>(CUnmannedTraderRegistItemInfo *_First, CUnmannedTraderRegistItemInfo *_Last, CUnmannedTraderRegistItemInfo *_Dest, std::allocator<CUnmannedTraderRegistItemInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *,std::allocator<CUnmannedTraderRegistItemInfo>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
