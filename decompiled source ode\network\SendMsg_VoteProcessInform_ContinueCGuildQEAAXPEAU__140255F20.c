/*
 * Function: ?SendMsg_VoteProcessInform_Continue@CGuild@@QEAAXPEAU_guild_member_info@@@Z
 * Address: 0x140255F20
 */

void __fastcall CGuild::SendMsg_VoteProcessInform_Continue(CGuild *this, _guild_member_info *pMem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@17
  __int64 v5; // [sp+0h] [bp-118h]@1
  int v6; // [sp+30h] [bp-E8h]@8
  int j; // [sp+34h] [bp-E4h]@8
  _guild_vote_process_inform_zocl v8; // [sp+50h] [bp-C8h]@13
  char pbyType; // [sp+E4h] [bp-34h]@17
  char v10; // [sp+E5h] [bp-33h]@17
  unsigned __int64 v11; // [sp+100h] [bp-18h]@4
  CGuild *v12; // [sp+120h] [bp+8h]@1
  _guild_member_info *v13; // [sp+128h] [bp+10h]@1

  v13 = pMem;
  v12 = this;
  v2 = &v5;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( v12->m_bNowProcessSgtMter
    && pMem->pPlayer
    && (v12->m_SuggestedMatter.byMatterType != 5 || pMem->byClassInGuild == 2) )
  {
    v6 = 0;
    for ( j = 0; j < v12->m_SuggestedMatter.nTotal_VotableMemNum; ++j )
    {
      if ( v12->m_SuggestedMatter.VotableMem[j]->pPlayer )
        ++v6;
    }
    _guild_vote_process_inform_zocl::_guild_vote_process_inform_zocl(&v8);
    v8.bStart = 0;
    v8.byMatterType = v12->m_SuggestedMatter.byMatterType;
    v8.dwMatterDst = v12->m_SuggestedMatter.dwMatterDst;
    v8.dwMatterObj1 = v12->m_SuggestedMatter.dwMatterObj1;
    v8.dwMatterObj2 = v12->m_SuggestedMatter.dwMatterObj2;
    v8.dwMatterObj3 = v12->m_SuggestedMatter.dwMatterObj3;
    v8.dwMatterVoteSynKey = v12->m_SuggestedMatter.dwMatterVoteSynKey;
    v8.dwSuggesterSerial = v12->m_dwSuggesterSerial;
    v8.byTotalSeniorNum = v12->m_SuggestedMatter.nTotal_VotableMemNum;
    v8.byLoginSeniorNum = v6;
    v8.bActed = v13->bVote;
    v8.byApprPoint = v12->m_SuggestedMatter.byVoteState[0];
    v8.byOppoPoint = v12->m_SuggestedMatter.byVoteState[1];
    v8.byCommentLen = strlen_0(v12->m_SuggestedMatter.wszComment);
    strcpy_0(v8.wszComment, v12->m_SuggestedMatter.wszComment);
    if ( v8.byMatterType == 4 )
    {
      strcpy_0(v8.wszDestGuildName, v12->m_GuildBattleSugestMatter.pkDest->m_wszName);
      v8.byDestGuildGrade = v12->m_GuildBattleSugestMatter.pkDest->m_byGrade;
      v8.byDestGuildRace = v12->m_GuildBattleSugestMatter.pkDest->m_byRace;
    }
    else if ( v8.byMatterType == 5 )
    {
      strcpy_0(v8.wszDestGuildName, v12->m_GuildBattleSugestMatter.pkSrc->m_wszName);
      v8.byDestGuildGrade = v12->m_GuildBattleSugestMatter.pkSrc->m_byGrade;
      v8.byDestGuildRace = v12->m_GuildBattleSugestMatter.pkSrc->m_byRace;
    }
    pbyType = 27;
    v10 = 24;
    v4 = _guild_vote_process_inform_zocl::size(&v8);
    CNetProcess::LoadSendMsg(unk_1414F2088, v13->pPlayer->m_ObjID.m_wIndex, &pbyType, (char *)&v8.bStart, v4);
  }
}
