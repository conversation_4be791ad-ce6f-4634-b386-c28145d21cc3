/*
 * Function: j_??4?$_Ranit@VCUnmannedTraderRegistItemInfo@@_JPEBV1@AEBV1@@std@@QEAAAEAU01@AEBU01@@Z
 * Address: 0x140005BC3
 */

std::_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &> *__fastcall std::_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &>::operator=(std::_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &> *this, std::_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &> *__that)
{
  return std::_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &>::operator=(
           this,
           __that);
}
