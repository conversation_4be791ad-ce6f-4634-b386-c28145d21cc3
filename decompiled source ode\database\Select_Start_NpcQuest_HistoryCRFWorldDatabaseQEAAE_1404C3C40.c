/*
 * Function: ?Select_Start_NpcQuest_History@CRFWorldDatabase@@QEAAEKPEAU_worlddb_start_npc_quest_complete_history@@K@Z
 * Address: 0x1404C3C40
 */

char __fastcall CRFWorldDatabase::Select_Start_NpcQuest_History(CRFWorldDatabase *this, unsigned int dwSerial, _worlddb_start_npc_quest_complete_history *pNpcQHis, unsigned int dwCount)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  _worlddb_start_npc_quest_complete_history::__list *v7; // rax@18
  char *v8; // rax@18
  __int64 *v9; // rax@18
  __int64 v10; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@18
  SQLLEN v13; // [sp+38h] [bp-150h]@18
  __int16 v14; // [sp+44h] [bp-144h]@9
  char DstBuf; // [sp+60h] [bp-128h]@4
  unsigned int j; // [sp+164h] [bp-24h]@14
  unsigned __int64 v17; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v18; // [sp+190h] [bp+8h]@1
  _worlddb_start_npc_quest_complete_history *v19; // [sp+1A0h] [bp+18h]@1
  unsigned int v20; // [sp+1A8h] [bp+20h]@1

  v20 = dwCount;
  v19 = pNpcQHis;
  v18 = this;
  v4 = &v10;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v17 = (unsigned __int64)&v10 ^ _security_cookie;
  sprintf_s(&DstBuf, 0x100ui64, "{ CALL pSelect_Start_Npc_Quest_History( %d ) }", dwSerial);
  if ( v18->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v18->vfptr, &DstBuf);
  if ( v18->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v18->vfptr) )
  {
    v14 = SQLExecDirectA_0(v18->m_hStmtSelect, &DstBuf, -3);
    if ( v14 && v14 != 1 )
    {
      if ( v14 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v18->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v14, &DstBuf, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v14, v18->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      for ( j = 0; j < v20; ++j )
      {
        v14 = SQLFetch_0(v18->m_hStmtSelect);
        if ( v14 )
        {
          if ( v14 != 1 )
            break;
        }
        v7 = &v19->List[j];
        StrLen_or_IndPtr = &v13;
        SQLStmt = (void *)64;
        v14 = SQLGetData_0(v18->m_hStmtSelect, 1u, 1, v7, 64i64, &v13);
        v8 = &v19->List[j].byLevel;
        StrLen_or_IndPtr = &v13;
        SQLStmt = 0i64;
        v14 = SQLGetData_0(v18->m_hStmtSelect, 2u, -6, v8, 0i64, &v13);
        v9 = &v19->List[j].nEndTime;
        StrLen_or_IndPtr = &v13;
        SQLStmt = 0i64;
        v14 = SQLGetData_0(v18->m_hStmtSelect, 3u, -27, v9, 0i64, &v13);
      }
      if ( v18->m_hStmtSelect )
        SQLCloseCursor_0(v18->m_hStmtSelect);
      if ( v18->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v18->vfptr, "%s Success", &DstBuf);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v18->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1;
  }
  return result;
}
