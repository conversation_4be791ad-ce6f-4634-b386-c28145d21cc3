/*
 * Function: ?DE_ConvertTargetDest@@YA_NPEAVCCharacter@@0MAEAE@Z
 * Address: 0x14017E1C0
 */

int __fastcall DE_ConvertTargetDest(CCharacter *pActChar, CCharacter *pTargetChar, float fEffectValue, char *byRet)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  CCharacter *v8; // [sp+30h] [bp+8h]@1

  v8 = pActChar;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return ((int (__fastcall *)(CCharacter *))v8->vfptr->SF_ConvertTargetDest)(v8);
}
