/*
 * Function: ?_DestroySDM@CMonster@@SAXXZ
 * Address: 0x140149460
 */

void CMonster::_DestroySDM(void)
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-38h]@1
  void *v3; // [sp+20h] [bp-18h]@6

  v0 = &v2;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  if ( CMonster::s_nAllocNum <= 0 )
  {
    if ( CMonster::s_idxMonsterLoot )
    {
      v3 = CMonster::s_idxMonsterLoot;
      operator delete[](CMonster::s_idxMonsterLoot);
      CMonster::s_idxMonsterLoot = 0i64;
    }
  }
}
