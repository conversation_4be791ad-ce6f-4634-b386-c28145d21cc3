/*
 * Function: ?SendMsg_GoodsList@ICsSendInterface@@SAXGPEBU_param_cash_select@@@Z
 * Address: 0x14030C620
 */

void __fastcall ICsSendInterface::SendMsg_GoodsList(unsigned __int16 wSock, _param_cash_select *psheet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@7
  __int64 v5; // [sp+0h] [bp-98h]@1
  _result_csi_goods_list_zocl v6; // [sp+38h] [bp-60h]@4
  int j; // [sp+54h] [bp-44h]@4
  char pbyType; // [sp+64h] [bp-34h]@7
  char v9; // [sp+65h] [bp-33h]@7
  unsigned __int64 v10; // [sp+80h] [bp-18h]@4
  unsigned __int16 v11; // [sp+A0h] [bp+8h]@1

  v11 = wSock;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  v6.nCashAmount = psheet->out_dwCashAmount;
  v6.bAdjustDiscountRate = psheet->in_bAdjustDiscount;
  v6.bOneNOne = psheet->in_bOneN_One;
  v6.bSetDiscount = psheet->in_bSetDiscount;
  for ( j = 0; j < 4; ++j )
    v6.bySetDiscount[j] = psheet->in_bySetDiscount[j];
  v6.bLimSale = psheet->in_bLimited_Sale;
  v6.byLimDiscount = psheet->in_byLimDiscount;
  pbyType = 57;
  v9 = 2;
  v4 = _result_csi_goods_list_zocl::size(&v6);
  CNetProcess::LoadSendMsg(unk_1414F2088, v11, &pbyType, (char *)&v6, v4);
}
