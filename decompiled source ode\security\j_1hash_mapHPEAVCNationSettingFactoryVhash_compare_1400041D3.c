/*
 * Function: j_??1?$hash_map@HPEAVCNationSettingFactory@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@std@@@stdext@@QEAA@XZ
 * Address: 0x1400041D3
 */

void __fastcall stdext::hash_map<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::~hash_map<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>>(stdext::hash_map<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationSettingFactory *> > > *this)
{
  stdext::hash_map<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::~hash_map<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>>(this);
}
