/*
 * Function: ?Sign@PK_Signer@CryptoPP@@UEBA_KAEAVRandomNumberGenerator@2@PEAVPK_MessageAccumulator@2@PEAE@Z
 * Address: 0x1405F5F50
 */

__int64 __fastcall CryptoPP::PK_Signer::Sign(CryptoPP::PK_Signer *this, struct CryptoPP::RandomNumberGenerator *a2, struct CryptoPP::PK_MessageAccumulator *a3, unsigned __int8 *a4)
{
  __int64 v4; // rax@1
  __int64 v5; // rax@1
  char v7; // [sp+30h] [bp-28h]@1
  __int64 v8; // [sp+38h] [bp-20h]@1
  __int64 v9; // [sp+40h] [bp-18h]@1
  CryptoPP::PK_SignatureSchemeVtbl *v10; // [sp+48h] [bp-10h]@1
  CryptoPP::PK_Signer *v11; // [sp+60h] [bp+8h]@1
  struct CryptoPP::RandomNumberGenerator *v12; // [sp+68h] [bp+10h]@1
  unsigned __int8 *v13; // [sp+78h] [bp+20h]@1

  v13 = a4;
  v12 = a2;
  v11 = this;
  v9 = -2i64;
  std::auto_ptr<CryptoPP::PK_MessageAccumulator>::auto_ptr<CryptoPP::PK_MessageAccumulator>(&v7, a3);
  LODWORD(v4) = std::auto_ptr<CryptoPP::PK_MessageAccumulator>::operator*(&v7);
  v10 = v11->vfptr;
  LODWORD(v5) = ((int (__fastcall *)(CryptoPP::PK_Signer *, struct CryptoPP::RandomNumberGenerator *, __int64, unsigned __int8 *))v10[1].MaxRecoverableLength)(
                  v11,
                  v12,
                  v4,
                  v13);
  v8 = v5;
  std::auto_ptr<CryptoPP::PK_MessageAccumulator>::~auto_ptr<CryptoPP::PK_MessageAccumulator>(&v7);
  return v8;
}
