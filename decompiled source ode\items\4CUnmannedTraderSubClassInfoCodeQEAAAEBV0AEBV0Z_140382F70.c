/*
 * Function: ??4CUnmannedTraderSubClassInfoCode@@QEAAAEBV0@AEBV0@@Z
 * Address: 0x140382F70
 */

CUnmannedTraderSubClassInfoCode *__fastcall CUnmannedTraderSubClassInfoCode::operator=(CUnmannedTraderSubClassInfoCode *this, CUnmannedTraderSubClassInfoCode *lhs)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-B8h]@1
  char v6; // [sp+20h] [bp-98h]@5
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *result; // [sp+38h] [bp-80h]@5
  char v8; // [sp+40h] [bp-78h]@5
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v9; // [sp+58h] [bp-60h]@5
  char v10; // [sp+60h] [bp-58h]@5
  std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v11; // [sp+78h] [bp-40h]@5
  __int64 v12; // [sp+80h] [bp-38h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v13; // [sp+88h] [bp-30h]@5
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v14; // [sp+90h] [bp-28h]@5
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v15; // [sp+98h] [bp-20h]@5
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v16; // [sp+A0h] [bp-18h]@5
  std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v17; // [sp+A8h] [bp-10h]@5
  CUnmannedTraderSubClassInfoCode *v18; // [sp+C0h] [bp+8h]@1
  CUnmannedTraderSubClassInfoCode *lhsa; // [sp+C8h] [bp+10h]@1

  lhsa = lhs;
  v18 = this;
  v2 = &v5;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = -2i64;
  CUnmannedTraderSubClassInfo::Copy(
    (CUnmannedTraderSubClassInfo *)&v18->vfptr,
    (CUnmannedTraderSubClassInfo *)&lhs->vfptr);
  std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::clear(&v18->m_vecCodeList);
  if ( !std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::empty(&lhsa->m_vecCodeList) )
  {
    result = (std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)&v6;
    v9 = (std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)&v8;
    v11 = (std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)&v10;
    v13 = std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::end(
            &lhsa->m_vecCodeList,
            (std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)&v6);
    v14 = v13;
    v15 = std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::begin(
            &lhsa->m_vecCodeList,
            v9);
    v16 = v15;
    v17 = std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::begin(
            &v18->m_vecCodeList,
            v11);
    std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::insert<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>>(
      &v18->m_vecCodeList,
      v17,
      v16,
      v14);
  }
  return v18;
}
