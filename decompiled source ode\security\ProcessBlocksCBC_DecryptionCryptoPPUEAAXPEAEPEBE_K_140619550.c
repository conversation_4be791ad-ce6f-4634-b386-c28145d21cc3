/*
 * Function: ?ProcessBlocks@CBC_Decryption@CryptoPP@@UEAAXPEAEPEBE_K@Z
 * Address: 0x140619550
 */

void __fastcall CryptoPP::CBC_Decryption::ProcessBlocks(CryptoPP::CBC_Decryption *this, unsigned __int8 *a2, const unsigned __int8 *a3, __int64 a4)
{
  char *v4; // ST30_8@2
  char *v5; // rax@2
  unsigned int v6; // [sp+20h] [bp-48h]@1
  CryptoPP::CBC_Decryption *v7; // [sp+70h] [bp+8h]@1
  unsigned __int8 *v8; // [sp+78h] [bp+10h]@1
  const unsigned __int8 *v9; // [sp+80h] [bp+18h]@1
  __int64 v10; // [sp+88h] [bp+20h]@1

  v10 = a4;
  v9 = a3;
  v8 = a2;
  v7 = this;
  v6 = CryptoPP::CipherModeBase::BlockSize((CryptoPP::CipherModeBase *)&this->vfptr);
  do
  {
    qmemcpy(
      CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator void *(&v7->m_temp),
      v9,
      v6);
    v4 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v7->m_register);
    v5 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v7->m_temp);
    ((void (__fastcall *)(CryptoPP::SimpleKeyedTransformation<CryptoPP::BlockTransformation> *, char *, char *, unsigned __int8 *))v7->m_cipher->vfptr[1].Clone)(
      v7->m_cipher,
      v5,
      v4,
      v8);
    CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::swap(
      &v7->m_register,
      &v7->m_temp);
    v9 += v6;
    v8 += v6;
    --v10;
  }
  while ( v10 );
}
