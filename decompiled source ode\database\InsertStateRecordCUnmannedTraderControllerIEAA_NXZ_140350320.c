/*
 * Function: ?InsertStateRecord@CUnmannedTraderController@@IEAA_NXZ
 * Address: 0x140350320
 */

char __fastcall CUnmannedTraderController::InsertStateRecord(CUnmannedTraderController *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  wchar_t *v4; // rax@17
  unsigned int v5; // eax@25
  unsigned int v6; // eax@26
  __int64 v7; // [sp+0h] [bp-98h]@1
  unsigned int pdwCnt; // [sp+24h] [bp-74h]@4
  char v9; // [sp+34h] [bp-64h]@4
  _unmannedtrader_stade_id_info *pkInfo; // [sp+38h] [bp-60h]@7
  unsigned int v11; // [sp+40h] [bp-58h]@15
  char v12; // [sp+44h] [bp-54h]@15
  unsigned int uiInx; // [sp+48h] [bp-50h]@15
  _unmannedtrader_stade_id_info *v14; // [sp+50h] [bp-48h]@7
  void *v15; // [sp+58h] [bp-40h]@11
  void *v16; // [sp+60h] [bp-38h]@20
  __int64 v17; // [sp+68h] [bp-30h]@7
  unsigned int v18; // [sp+70h] [bp-28h]@13
  wchar_t *Str2; // [sp+78h] [bp-20h]@17
  wchar_t **ppwszStr; // [sp+80h] [bp-18h]@25
  CUnmannedTraderController *v21; // [sp+A0h] [bp+8h]@1

  v21 = this;
  v1 = &v7;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  pdwCnt = 0;
  v9 = CRFWorldDatabase::Select_UnmannedTraderItemStateInfoCnt(pkDB, &pdwCnt);
  if ( v9 && v9 != 2 )
  {
    CUnmannedTraderController::Log(
      v21,
      "CUnmannedTraderController::InsertStateRecord()\r\n"
      "\t\tg_Main.m_pWorldDB->Select_UnmannedTraderItemStateInfoCnt() Fail!\r\n");
    result = 0;
  }
  else
  {
    v17 = pdwCnt;
    v14 = (_unmannedtrader_stade_id_info *)operator new[](saturated_mul(0x104ui64, pdwCnt));
    pkInfo = v14;
    if ( v14 )
    {
      v9 = CRFWorldDatabase::Select_UnmannedTraderItemStateInfo(pkDB, pkInfo, pdwCnt);
      if ( v9 && v9 != 2 )
      {
        CUnmannedTraderController::Log(
          v21,
          "CUnmannedTraderController::InsertStateRecord()\r\n"
          "\t\tg_Main.m_pWorldDB->Select_UnmannedTraderItemStateInfo() Fail!\r\n");
        v15 = pkInfo;
        operator delete(pkInfo);
        result = 0;
      }
      else
      {
        if ( pdwCnt >= 0xE )
          v18 = 14;
        else
          v18 = pdwCnt;
        v11 = v18;
        v12 = 1;
        for ( uiInx = 0; uiInx < v11; ++uiInx )
        {
          Str2 = pkInfo[uiInx].wszDesc;
          v4 = CUnmannedTraderItemState::GetStateStrW(uiInx);
          if ( wcscmp(v4, Str2) )
          {
            v12 = 0;
            break;
          }
        }
        v16 = pkInfo;
        operator delete(pkInfo);
        if ( v12 && pdwCnt == 14 )
        {
          result = 1;
        }
        else if ( CRFWorldDatabase::Truncate_UnmannedTraderItemStateRecord(pkDB) )
        {
          ppwszStr = CUnmannedTraderItemState::GetStateStrList();
          v5 = CUnmannedTraderItemState::GetMaxStateCnt();
          if ( CRFWorldDatabase::Insert_UnmannedTraderItemStateRecord(pkDB, v5, ppwszStr) )
          {
            result = 1;
          }
          else
          {
            v6 = CUnmannedTraderItemState::GetMaxStateCnt();
            CUnmannedTraderController::Log(
              v21,
              "CUnmannedTraderController::InsertStateRecord()\r\n"
              "\t\tg_Main.m_pWorldDB->Insert_UnmannedTraderItemStateRecord( %u, wszStateName ) Fail!\r\n",
              v6);
            result = 0;
          }
        }
        else
        {
          CUnmannedTraderController::Log(
            v21,
            "CUnmannedTraderController::InsertStateRecord()\r\n"
            "\t\tg_Main.m_pWorldDB->Truncate_UnmannedTraderItemStateRecord() Fail!\r\n");
          result = 0;
        }
      }
    }
    else
    {
      CUnmannedTraderController::Log(
        v21,
        "CUnmannedTraderController::InsertStateRecord()\r\n\t\tnew _unmannedtrader_stade_id_info[%u] NULL!\r\n",
        pdwCnt);
      result = 0;
    }
  }
  return result;
}
