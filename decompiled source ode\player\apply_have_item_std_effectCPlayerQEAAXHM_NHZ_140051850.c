/*
 * Function: ?apply_have_item_std_effect@CPlayer@@QEAAXHM_NH@Z
 * Address: 0x140051850
 */

void __fastcall CPlayer::apply_have_item_std_effect(CPlayer *this, int nEffCode, float fVal, bool bAdd, int nDiffCnt)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  float v7; // xmm0_4@51
  __int64 v8; // [sp+0h] [bp-48h]@1
  int v9; // [sp+30h] [bp-18h]@4
  int v10; // [sp+34h] [bp-14h]@4
  int v11; // [sp+38h] [bp-10h]@4
  CPlayer *v12; // [sp+50h] [bp+8h]@1
  bool v13; // [sp+68h] [bp+20h]@1

  v13 = bAdd;
  v12 = this;
  v5 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v9 = 0;
  v10 = 1;
  v11 = nEffCode - 15;
  switch ( nEffCode )
  {
    case 15:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 9, fVal, bAdd);
      break;
    case 16:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 10, fVal, bAdd);
      break;
    case 17:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 11, fVal, bAdd);
      break;
    case 18:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 0, fVal, bAdd);
      _effect_parameter::SetEff_Rate(&v12->m_EP, 1, fVal, v13);
      _effect_parameter::SetEff_Rate(&v12->m_EP, 2, fVal, v13);
      _effect_parameter::SetEff_Rate(&v12->m_EP, 3, fVal, v13);
      _effect_parameter::SetEff_Rate(&v12->m_EP, 4, fVal, v13);
      _effect_parameter::SetEff_Rate(&v12->m_EP, 29, fVal, v13);
      break;
    case 19:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 0, fVal, bAdd);
      break;
    case 20:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 2, fVal, bAdd);
      break;
    case 21:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 1, fVal, bAdd);
      break;
    case 22:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 3, fVal, bAdd);
      break;
    case 23:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 4, fVal, bAdd);
      break;
    case 24:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 6, fVal, bAdd);
      break;
    case 25:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 12, fVal, bAdd);
      break;
    case 26:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 13, fVal, bAdd);
      break;
    case 27:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 40, fVal, bAdd);
      break;
    case 29:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 41, fVal, bAdd);
      break;
    case 30:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 42, fVal, bAdd);
      break;
    case 31:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 43, fVal, bAdd);
      break;
    case 32:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 44, fVal, bAdd);
      break;
    case 33:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 57, fVal, bAdd);
      break;
    case 34:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 58, fVal, bAdd);
      break;
    case 35:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 59, fVal, bAdd);
      break;
    case 36:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 60, fVal, bAdd);
      break;
    case 37:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 61, fVal, bAdd);
      break;
    case 38:
      _effect_parameter::SetEff_Rate(&v12->m_EP, 0, fVal, bAdd);
      _effect_parameter::SetEff_Rate(&v12->m_EP, 1, fVal, v13);
      break;
    case 39:
      _effect_parameter::SetEff_Plus(&v12->m_EP, 6, fVal, bAdd);
      _effect_parameter::SetEff_Plus(&v12->m_EP, 7, fVal, v13);
      break;
    case 40:
      _effect_parameter::SetEff_Plus(&v12->m_EP, 23, 1.0, bAdd);
      break;
    case 41:
      _effect_parameter::SetEff_Plus(&v12->m_EP, 20, fVal, bAdd);
      break;
    case 43:
      _effect_parameter::SetEff_Plus(&v12->m_EP, 0, fVal, bAdd);
      break;
    case 44:
      _effect_parameter::SetEff_Plus(&v12->m_EP, 1, fVal, bAdd);
      break;
    case 45:
      _effect_parameter::SetEff_Plus(&v12->m_EP, 31, fVal, bAdd);
      break;
    case 46:
      _effect_parameter::SetEff_Plus(&v12->m_EP, 30, fVal, bAdd);
      break;
    case 47:
      _effect_parameter::SetEff_Plus(&v12->m_EP, 14, fVal, bAdd);
      break;
    case 48:
      _effect_parameter::SetEff_Plus(&v12->m_EP, 40, fVal, bAdd);
      break;
    case 49:
      _effect_parameter::SetEff_Plus(&v12->m_EP, 3, fVal, bAdd);
      break;
    case 50:
      CPlayer::HideNameEffect(v12, bAdd);
      break;
    case 56:
      _effect_parameter::SetEff_State(&v12->m_EP, 8, bAdd);
      break;
    case 57:
      _effect_parameter::SetEff_Plus(&v12->m_EP, 29, fVal, bAdd);
      break;
    case 59:
      CPlayer::SetMstPt(v12, 0, fVal, bAdd, 0);
      break;
    case 60:
      CPlayer::SetMstPt(v12, 0, fVal, bAdd, 1);
      break;
    case 61:
      CPlayer::SetMstPt(v12, 4, fVal, bAdd, 0);
      break;
    case 62:
      CPlayer::SetMstPt(v12, 1, fVal, bAdd, 0);
      break;
    case 63:
      CPlayer::SetMstPt(v12, 2, fVal, bAdd, 0);
      break;
    case 64:
      CPlayer::SetMstPt(v12, 6, fVal, bAdd, 0);
      break;
    case 65:
      CPlayer::SetMstPt(v12, 6, fVal, bAdd, 0);
      break;
    case 76:
      _effect_parameter::SetEff_Plus(&v12->m_EP, 22, fVal, bAdd);
      break;
    case 77:
      if ( nDiffCnt > 0 && bAdd )
      {
        v7 = (double)nDiffCnt * 0.01;
        CPlayer::DecHalfSFContDam(v12, v7);
      }
      break;
    case 78:
      _effect_parameter::SetEff_Plus(&v12->m_EP, 28, fVal, bAdd);
      break;
    case 79:
      if ( nDiffCnt != 1 && fVal >= 1.0 )
        CPlayer::SetEquipJadeEffect(v12, 79, fVal, bAdd);
      break;
    case 80:
      if ( nDiffCnt != 1 && fVal >= 1.0 )
        CPlayer::SetEquipJadeEffect(v12, 80, fVal, bAdd);
      break;
    default:
      return;
  }
}
