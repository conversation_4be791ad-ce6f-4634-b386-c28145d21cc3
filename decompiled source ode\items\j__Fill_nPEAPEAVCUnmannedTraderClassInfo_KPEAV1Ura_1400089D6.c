/*
 * Function: j_??$_Fill_n@PEAPEAVCUnmannedTraderClassInfo@@_KPEAV1@Urandom_access_iterator_tag@std@@@std@@YAXPEAPEAVCUnmannedTraderClassInfo@@_KAEBQEAV1@Urandom_access_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400089D6
 */

void __fastcall std::_Fill_n<CUnmannedTraderClassInfo * *,unsigned __int64,CUnmannedTraderClassInfo *,std::random_access_iterator_tag>(CUnmannedTraderClassInfo **_First, unsigned __int64 _Count, CUnmannedTraderClassInfo *const *_Val, std::random_access_iterator_tag __formal, std::_Range_checked_iterator_tag a5)
{
  std::_Fill_n<CUnmannedTraderClassInfo * *,unsigned __int64,CUnmannedTraderClassInfo *,std::random_access_iterator_tag>(
    _First,
    _Count,
    _Val,
    __formal,
    a5);
}
