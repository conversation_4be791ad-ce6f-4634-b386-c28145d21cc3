/*
 * Function: ?SetDamage@CGuardTower@@UEAAHHPEAVCCharacter@@H_NHK1@Z
 * Address: 0x14012FE50
 */

__int64 __usercall CGuardTower::SetDamage@<rax>(CGuardTower *this@<rcx>, int nDam@<edx>, CCharacter *pDst@<r8>, int nDstLv@<r9d>, float a5@<xmm0>, bool bCrt, int nAttackType, unsigned int dwAttackSerial, bool bJadeReturn)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  CCharacter *v11; // rdx@16
  int v12; // eax@16
  __int64 v14; // [sp+0h] [bp-38h]@1
  int v15; // [sp+20h] [bp-18h]@6
  int v16; // [sp+24h] [bp-14h]@16
  CGameObjectVtbl *v17; // [sp+28h] [bp-10h]@16
  CGuardTower *v18; // [sp+40h] [bp+8h]@1
  CCharacter *v19; // [sp+50h] [bp+18h]@1

  v19 = pDst;
  v18 = this;
  v9 = &v14;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  if ( nDam > 1 )
  {
    if ( v18->m_nHP - nDam <= 0 )
      v15 = 0;
    else
      v15 = v18->m_nHP - nDam;
    v18->m_nHP = v15;
  }
  if ( v18->m_nHP )
  {
    if ( v18->m_pMasterTwr )
      CPlayer::SendMsg_AlterTowerHP(v18->m_pMasterTwr, v18->m_wItemSerial, v18->m_nHP);
    GetSqrt(v18->m_fCurPos, v19->m_fCurPos);
    if ( a5 > (float)*(signed int *)&v18->m_pRecordSet[5].m_strCode[24] )
    {
      if ( v18->m_pTarget )
      {
        v16 = ((int (__fastcall *)(CCharacter *))v19->vfptr->GetLevel)(v19);
        v11 = v18->m_pTarget;
        v17 = v18->m_pTarget->vfptr;
        v12 = ((int (__fastcall *)(CCharacter *))v17->GetLevel)(v11);
        if ( v16 < v12 )
          v18->m_pTarget = v19;
      }
      else
      {
        v18->m_pTarget = v19;
      }
    }
  }
  else
  {
    CGuardTower::Destroy(v18, 1, 0);
  }
  return v18->m_nHP;
}
