/*
 * Function: _std::vector_CUnmannedTraderGroupDivisionVersionInfo_std::allocator_CUnmannedTraderGroupDivisionVersionInfo___::insert_::_1_::dtor$4
 * Address: 0x140398850
 */

void __fastcall std::vector_CUnmannedTraderGroupDivisionVersionInfo_std::allocator_CUnmannedTraderGroupDivisionVersionInfo___::insert_::_1_::dtor_4(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 120) & 2 )
  {
    *(_DWORD *)(a2 + 120) &= 0xFFFFFFFD;
    std::_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::~_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(*(std::_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > **)(a2 + 216));
  }
}
