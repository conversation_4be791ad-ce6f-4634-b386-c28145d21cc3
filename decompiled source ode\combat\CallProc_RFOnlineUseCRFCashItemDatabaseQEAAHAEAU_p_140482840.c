/*
 * Function: ?CallProc_RFOnlineUse@CRFCashItemDatabase@@QEAAHAEAU_param_cash_update@@H@Z
 * Address: 0x140482840
 */

signed __int64 __usercall CRFCashItemDatabase::CallProc_RFOnlineUse@<rax>(CRFCashItemDatabase *this@<rcx>, _param_cash_update *rParam@<rdx>, int nIdx@<r8d>, signed __int64 a4@<rax>)
{
  void *v4; // rsp@1
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  CCashDBWorkManager *v7; // rax@4
  signed __int64 result; // rax@8
  __int64 v9; // [sp-20h] [bp-14C8h]@1
  unsigned __int64 tBufferSize; // [sp+0h] [bp-14A8h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+8h] [bp-14A0h]@22
  SQLLEN v12; // [sp+18h] [bp-1490h]@22
  __int16 v13; // [sp+24h] [bp-1484h]@9
  char wszQuery; // [sp+40h] [bp-1468h]@4
  char v15; // [sp+41h] [bp-1467h]@4
  unsigned __int8 v16; // [sp+1444h] [bp-64h]@16
  char TargetValue; // [sp+1454h] [bp-54h]@22
  char v18; // [sp+1455h] [bp-53h]@22
  unsigned __int8 v19; // [sp+1464h] [bp-44h]@24
  int v20; // [sp+1474h] [bp-34h]@30
  unsigned __int8 v21; // [sp+1484h] [bp-24h]@32
  unsigned __int64 v22; // [sp+1490h] [bp-18h]@4
  CRFCashItemDatabase *v23; // [sp+14B0h] [bp+8h]@1
  _param_cash_update *rParama; // [sp+14B8h] [bp+10h]@1
  int nIdxa; // [sp+14C0h] [bp+18h]@1

  nIdxa = nIdx;
  rParama = rParam;
  v23 = this;
  v4 = alloca(a4);
  v5 = &v9;
  for ( i = 1328i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v22 = (unsigned __int64)&v9 ^ _security_cookie;
  wszQuery = 0;
  memset(&v15, 0, 0x13FFui64);
  v7 = CTSingleton<CCashDBWorkManager>::Instance();
  CCashDBWorkManager::GetUseCashQueryStr(v7, rParama, nIdxa, &wszQuery, 0x1400ui64);
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v23->vfptr, &wszQuery);
  if ( v23->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v23->vfptr) )
  {
    v13 = SQLExecDirectA_0(v23->m_hStmtSelect, &wszQuery, -3);
    if ( v13 && v13 != 1 )
    {
      if ( v13 == 100 )
      {
        result = 2i64;
      }
      else
      {
        tBufferSize = (unsigned __int64)v23->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog(
          (CRFNewDatabase *)&v23->vfptr,
          v13,
          &wszQuery,
          "SQLExecDirectA",
          (void *)tBufferSize);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v13, v23->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v13 = SQLFetch_0(v23->m_hStmtSelect);
      if ( v13 && v13 != 1 )
      {
        v16 = 0;
        if ( v13 == 100 )
        {
          v16 = 2;
        }
        else
        {
          tBufferSize = (unsigned __int64)v23->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v13, &wszQuery, "SQLFetch", (void *)tBufferSize);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v13, v23->m_hStmtSelect);
          v16 = 1;
        }
        if ( v23->m_hStmtSelect )
          SQLCloseCursor_0(v23->m_hStmtSelect);
        result = v16;
      }
      else
      {
        TargetValue = 0;
        memset(&v18, 0, sizeof(v18));
        StrLen_or_IndPtr = &v12;
        tBufferSize = 2i64;
        v13 = SQLGetData_0(v23->m_hStmtSelect, 1u, 1, &TargetValue, 2i64, &v12);
        if ( v13 && v13 != 1 )
        {
          v19 = 0;
          if ( v13 == 100 )
          {
            v19 = 2;
          }
          else
          {
            tBufferSize = (unsigned __int64)v23->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v13, &wszQuery, "SQLFetch", (void *)tBufferSize);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v13, v23->m_hStmtSelect);
            v19 = 1;
          }
          if ( v23->m_hStmtSelect )
            SQLCloseCursor_0(v23->m_hStmtSelect);
          result = v19;
        }
        else
        {
          rParama->in_item[(signed __int64)nIdxa].out_cState = TargetValue;
          v20 = 0;
          StrLen_or_IndPtr = &v12;
          tBufferSize = 0i64;
          v13 = SQLGetData_0(v23->m_hStmtSelect, 2u, 4, &v20, 0i64, &v12);
          if ( v13 && v13 != 1 )
          {
            v21 = 0;
            if ( v13 == 100 )
            {
              v21 = 2;
            }
            else
            {
              tBufferSize = (unsigned __int64)v23->m_hStmtSelect;
              CRFNewDatabase::ErrorMsgLog(
                (CRFNewDatabase *)&v23->vfptr,
                v13,
                &wszQuery,
                "SQLFetch",
                (void *)tBufferSize);
              CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v13, v23->m_hStmtSelect);
              v21 = 1;
            }
            if ( v23->m_hStmtSelect )
              SQLCloseCursor_0(v23->m_hStmtSelect);
            result = v21;
          }
          else
          {
            rParama->in_item[(signed __int64)nIdxa].out_nCashAmount = v20;
            rParama->out_nCashAmount = v20;
            if ( v23->m_hStmtSelect )
              SQLCloseCursor_0(v23->m_hStmtSelect);
            if ( v23->m_bSaveDBLog )
              CRFNewDatabase::FmtLog((CRFNewDatabase *)&v23->vfptr, "%s Success", &wszQuery);
            result = 0i64;
          }
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v23->vfptr, "ReConnectDataBase Fail. Query : %s", &wszQuery);
    result = 1i64;
  }
  return result;
}
