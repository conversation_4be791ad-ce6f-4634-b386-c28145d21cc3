/*
 * Function: ?SendMsg_FixPosition@CHolyStone@@UEAAXH@Z
 * Address: 0x140137B80
 */

void __fastcall CHolyStone::SendMsg_FixPosition(CHolyStone *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-98h]@1
  char szMsg[2]; // [sp+38h] [bp-60h]@4
  unsigned __int16 v6; // [sp+3Ah] [bp-5Eh]@4
  unsigned int v7; // [sp+3Ch] [bp-5Ch]@4
  __int16 pShort; // [sp+40h] [bp-58h]@4
  char v9; // [sp+46h] [bp-52h]@4
  bool v10; // [sp+47h] [bp-51h]@4
  bool v11; // [sp+48h] [bp-50h]@4
  int v12; // [sp+49h] [bp-4Fh]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v14; // [sp+65h] [bp-33h]@4
  unsigned __int64 v15; // [sp+80h] [bp-18h]@4
  CHolyStone *v16; // [sp+A0h] [bp+8h]@1
  int dwClientIndex; // [sp+A8h] [bp+10h]@1

  dwClientIndex = n;
  v16 = this;
  v2 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v15 = (unsigned __int64)&v4 ^ _security_cookie;
  *(_WORD *)szMsg = v16->m_pRecordSet->m_dwIndex;
  v6 = v16->m_ObjID.m_wIndex;
  v7 = v16->m_dwObjSerial;
  FloatToShort(v16->m_fCurPos, &pShort, 3);
  v9 = v16->m_byMasterRaceCode;
  v10 = v16->m_bOper;
  v11 = v16->m_nHP > 0;
  v12 = -1;
  v12 = CHolyStoneSystem::GetControlLeftTime(&g_HolySys);
  pbyType = 4;
  v14 = -90;
  CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, szMsg, 0x15u);
}
