/*
 * Function: ??0CUnmannedTraderSortType@@QEAA@K@Z
 * Address: 0x140376C90
 */

void __fastcall CUnmannedTraderSortType::CUnmannedTraderSortType(CUnmannedTraderSortType *this, unsigned int dwID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CUnmannedTraderSortType *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5->m_dwID = dwID;
  strcpy_0(v5->m_szTypeName, CUnmannedTraderEnvironmentValue::Unmanned_Trader_Default_Sort_Type_Name);
  strcpy_0(v5->m_sz<PERSON><PERSON><PERSON>, CUnmannedTraderEnvironmentValue::Unmanned_Trader_Default_Sort_Query);
}
