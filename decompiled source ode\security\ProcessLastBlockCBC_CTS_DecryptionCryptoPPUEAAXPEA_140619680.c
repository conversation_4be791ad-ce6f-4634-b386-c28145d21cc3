/*
 * Function: ?ProcessLastBlock@CBC_CTS_Decryption@CryptoPP@@UEAAXPEAEPEBE_K@Z
 * Address: 0x140619680
 */

void __fastcall CryptoPP::CBC_CTS_Decryption::ProcessLastBlock(CryptoPP::CBC_CTS_Decryption *this, unsigned __int8 *a2, const unsigned __int8 *a3, unsigned __int64 a4)
{
  unsigned __int64 v4; // ST40_8@4
  unsigned __int8 *v5; // rax@4
  char *v6; // rax@4
  unsigned __int64 v7; // r9@4
  void *v8; // ST48_8@6
  unsigned __int8 *v9; // rax@6
  unsigned __int8 *v10; // ST50_8@6
  unsigned __int8 *v11; // ST58_8@6
  unsigned __int8 *v12; // rax@6
  unsigned __int64 v13; // [sp+20h] [bp-58h]@0
  char *v14; // [sp+28h] [bp-50h]@2
  unsigned __int8 *v15; // [sp+30h] [bp-48h]@2
  CryptoPP::CBC_CTS_Decryption *v16; // [sp+80h] [bp+8h]@1
  unsigned __int8 *v17; // [sp+88h] [bp+10h]@1
  const unsigned __int8 *v18; // [sp+90h] [bp+18h]@1
  unsigned __int8 *v19; // [sp+98h] [bp+20h]@1

  v19 = (unsigned __int8 *)a4;
  v18 = a3;
  v17 = a2;
  v16 = this;
  LOBYTE(v13) = a4 <= CryptoPP::CipherModeBase::BlockSize((CryptoPP::CipherModeBase *)&this->vfptr);
  if ( (_BYTE)v13 )
  {
    v15 = (unsigned __int8 *)v18;
    v14 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v16->m_register);
  }
  else
  {
    v15 = (unsigned __int8 *)&v18[CryptoPP::CipherModeBase::BlockSize((CryptoPP::CipherModeBase *)&v16->vfptr)];
    v14 = (char *)v18;
    v19 -= CryptoPP::CipherModeBase::BlockSize((CryptoPP::CipherModeBase *)&v16->vfptr);
  }
  v4 = CryptoPP::CipherModeBase::BlockSize((CryptoPP::CipherModeBase *)&v16->vfptr);
  qmemcpy(
    CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator void *(&v16->m_temp),
    v14,
    v4);
  v5 = (unsigned __int8 *)CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v16->m_temp);
  CryptoPP::BlockTransformation::ProcessBlock((CryptoPP::BlockTransformation *)&v16->m_cipher->vfptr, v5);
  v6 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v16->m_temp);
  CryptoPP::xorbuf((CryptoPP *)v6, v15, v19, v7);
  if ( (_BYTE)v13 )
  {
    qmemcpy(
      v17,
      CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator void *(&v16->m_temp),
      (unsigned __int64)v19);
  }
  else
  {
    v8 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator void *(&v16->m_temp);
    qmemcpy(
      &v17[CryptoPP::CipherModeBase::BlockSize((CryptoPP::CipherModeBase *)&v16->vfptr)],
      v8,
      (unsigned __int64)v19);
    qmemcpy(
      CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator void *(&v16->m_temp),
      v15,
      (unsigned __int64)v19);
    v9 = (unsigned __int8 *)CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v16->m_temp);
    CryptoPP::BlockTransformation::ProcessBlock((CryptoPP::BlockTransformation *)&v16->m_cipher->vfptr, v9);
    v10 = (unsigned __int8 *)CryptoPP::CipherModeBase::BlockSize((CryptoPP::CipherModeBase *)&v16->vfptr);
    v11 = (unsigned __int8 *)CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v16->m_register);
    v12 = (unsigned __int8 *)CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v16->m_temp);
    CryptoPP::xorbuf((CryptoPP *)v17, v12, v11, v10, v13);
  }
}
