/*
 * Function: ??$_Iter_random@V?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@PEAPEAVCUnmannedTraderClassInfo@@@std@@YA?AUrandom_access_iterator_tag@0@AEBV?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@0@AEBQEAPEAVCUnmannedTraderClassInfo@@@Z
 * Address: 0x1403761B0
 */

std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *__fastcall std::_Iter_random<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,CUnmannedTraderClassInfo * *>(std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *__formal, CUnmannedTraderClassInfo **const *a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v6; // [sp+50h] [bp+8h]@1

  v6 = __formal;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return v6;
}
