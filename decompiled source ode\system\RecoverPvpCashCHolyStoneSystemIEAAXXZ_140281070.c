/*
 * Function: ?RecoverPvpCash@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x140281070
 */

void __fastcall CHolyStoneSystem::RecoverPvpCash(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@11
  __int64 v4; // [sp+0h] [bp-58h]@1
  int j; // [sp+30h] [bp-28h]@4
  CPlayer *v6; // [sp+38h] [bp-20h]@7
  char v7; // [sp+40h] [bp-18h]@11
  char v8; // [sp+41h] [bp-17h]@11
  char v9; // [sp+42h] [bp-16h]@11
  char v10; // [sp+43h] [bp-15h]@11

  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 2532; ++j )
  {
    v6 = &g_Player + j;
    if ( v6->m_bLive )
    {
      if ( v6->m_bOper )
      {
        if ( v6->m_byHSKQuestCode != 100 )
        {
          v7 = CHolyStoneSystem::GetNumOfTime(&g_HolySys);
          v8 = CHolyStoneSystem::GetStartHour(&g_HolySys);
          v9 = CHolyStoneSystem::GetStartDay(&g_HolySys);
          v10 = CHolyStoneSystem::GetStartMonth(&g_HolySys);
          v3 = CHolyStoneSystem::GetStartYear(&g_HolySys);
          if ( MiningTicket::AuthLastMentalTicket(&v6->m_MinigTicket, v3, v10, v9, v8, v7) )
          {
            if ( !CPvpCashPoint::GetRaceWarRecvr(&v6->m_kPvpCashPoint) )
              CPlayer::RewardRaceWarPvpCash(v6);
          }
        }
      }
    }
  }
}
