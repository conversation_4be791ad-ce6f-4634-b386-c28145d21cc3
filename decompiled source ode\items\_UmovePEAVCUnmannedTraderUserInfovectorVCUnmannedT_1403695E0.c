/*
 * Function: ??$_Umove@PEAVCUnmannedTraderUserInfo@@@?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@IEAAPEAVCUnmannedTraderUserInfo@@PEAV2@00@Z
 * Address: 0x1403695E0
 */

CUnmannedTraderUserInfo *__fastcall std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Umove<CUnmannedTraderUserInfo *>(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this, CUnmannedTraderUserInfo *_First, CUnmannedTraderUserInfo *_Last, CUnmannedTraderUserInfo *_Ptr)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return stdext::_Unchecked_uninitialized_move<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo *,std::allocator<CUnmannedTraderUserInfo>>(
           _First,
           _Last,
           _Ptr,
           &v8->_Alval);
}
