/*
 * Function: ?GetBitCountHi@?$IteratedHashBase@_KV?$SimpleKeyedTransformation@VHashTransformation@CryptoPP@@@CryptoPP@@@CryptoPP@@IEBA_KXZ
 * Address: 0x140551820
 */

signed __int64 __fastcall CryptoPP::IteratedHashBase<unsigned __int64,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::GetBitCountHi(__int64 a1)
{
  return (*(_QWORD *)(a1 + 16) >> 61) + 8i64 * *(_QWORD *)(a1 + 24);
}
