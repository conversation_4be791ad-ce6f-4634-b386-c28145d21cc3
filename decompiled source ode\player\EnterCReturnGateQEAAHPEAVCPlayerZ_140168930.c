/*
 * Function: ?Enter@CReturnGate@@QEAAHPEAVCPlayer@@@Z
 * Address: 0x140168930
 */

signed __int64 __fastcall CReturnGate::Enter(CReturnGate *this, CPlayer *pkObj)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  float *pfStartPos; // [sp+20h] [bp-18h]@11
  CReturnGate *v7; // [sp+40h] [bp+8h]@1
  CPlayer *pkObja; // [sp+48h] [bp+10h]@1

  pkObja = pkObj;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CReturnGate::IsValidPosition(v7, pkObj->m_fCurPos) )
  {
    if ( v7->m_pkOwner == pkObja || CPartyPlayer::IsPartyMember(v7->m_pkOwner->m_pPartyMgr, pkObja) )
    {
      if ( v7->m_pDestMap )
      {
        pfStartPos = v7->m_fBindPos;
        CPlayer::OutOfMap(pkObja, v7->m_pDestMap, 0, 3, v7->m_fBindPos);
        CReturnGate::SendMsg_MovePortal(v7, pkObja);
        result = 0i64;
      }
      else
      {
        result = 5i64;
      }
    }
    else
    {
      result = 4i64;
    }
  }
  else
  {
    result = 3i64;
  }
  return result;
}
