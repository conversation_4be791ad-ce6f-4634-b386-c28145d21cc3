/*
 * Function: j_??0?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEAA@XZ
 * Address: 0x14000D1BB
 */

void __fastcall std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this)
{
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(this);
}
