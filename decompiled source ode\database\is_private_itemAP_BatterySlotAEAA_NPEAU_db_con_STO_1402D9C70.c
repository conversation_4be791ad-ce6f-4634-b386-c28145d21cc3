/*
 * Function: ?is_private_item@AP_BatterySlot@@AEAA_NPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1402D9C70
 */

bool __fastcall AP_BatterySlot::is_private_item(AP_BatterySlot *this, _STORAGE_LIST::_db_con *pItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  _base_fld *v6; // [sp+20h] [bp-18h]@4
  _base_fld *v7; // [sp+28h] [bp-10h]@4

  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
  v7 = v6;
  if ( !strcmp_0(v6->m_strCode, "itttt04") )
  {
    result = 1;
  }
  else if ( !strcmp_0(v7->m_strCode, "itttt05") )
  {
    result = 1;
  }
  else if ( !strcmp_0(v7->m_strCode, "itcsa01") )
  {
    result = 1;
  }
  else
  {
    result = strcmp_0(v7->m_strCode, "itcsa02") == 0;
  }
  return result;
}
