/*
 * Function: ??0?$_Deque_val@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@IEAA@V?$allocator@URECV_DATA@@@1@@Z
 * Address: 0x14031FA30
 */

void __fastcall std::_Deque_val<RECV_DATA,std::allocator<RECV_DATA>>::_Deque_val<RECV_DATA,std::allocator<RECV_DATA>>(std::_Deque_val<RECV_DATA,std::allocator<RECV_DATA> > *this, __int64 _Al)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<RECV_DATA> v4; // al@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  std::allocator<RECV_DATA> *v7; // [sp+28h] [bp-10h]@4
  std::_Deque_val<RECV_DATA,std::allocator<RECV_DATA> > *v8; // [sp+40h] [bp+8h]@1
  std::allocator<RECV_DATA> *__formal; // [sp+48h] [bp+10h]@1

  __formal = (std::allocator<RECV_DATA> *)_Al;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = (std::allocator<RECV_DATA> *)&v6;
  std::allocator<RECV_DATA>::allocator<RECV_DATA>((std::allocator<RECV_DATA> *)&v6, (std::allocator<RECV_DATA> *)_Al);
  std::_Deque_map<RECV_DATA,std::allocator<RECV_DATA>>::_Deque_map<RECV_DATA,std::allocator<RECV_DATA>>(
    (std::_Deque_map<RECV_DATA,std::allocator<RECV_DATA> > *)&v8->_Myfirstiter,
    v4);
  std::allocator<RECV_DATA>::allocator<RECV_DATA>(&v8->_Alval, __formal);
}
