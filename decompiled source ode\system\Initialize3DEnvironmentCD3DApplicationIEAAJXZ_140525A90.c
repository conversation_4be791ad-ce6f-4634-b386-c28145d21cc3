/*
 * Function: ?Initialize3DEnvironment@CD3DApplication@@IEAAJXZ
 * Address: 0x140525A90
 */

__int64 __fastcall CD3DApplication::Initialize3DEnvironment(CD3DApplication *this)
{
  CD3DApplication *v1; // rdi@1
  unsigned int v2; // ebp@1
  signed __int64 v3; // rax@1
  signed __int64 v4; // rsi@1
  bool v5; // cf@2
  __int64 v6; // r8@3
  __int64 v7; // rdx@3
  int v8; // er9@4
  _DWORD *v9; // rcx@4
  __int64 v10; // rax@9
  unsigned int v11; // edx@10
  int v12; // er8@11
  char *v13; // rcx@11
  signed __int64 v14; // rbx@18
  int v15; // eax@18
  int v16; // eax@19
  bool v17; // zf@21
  int v18; // eax@21
  int v19; // eax@22
  int v20; // ST20_4@26
  signed int v21; // ebx@26
  int v22; // eax@29
  const char *v23; // rdx@30
  int v24; // eax@36
  char *v25; // rcx@38
  const char *v26; // rdx@39
  HICON v27; // rax@56
  tagRECT *v28; // rcx@59
  __int64 v30; // rcx@65
  CD3DApplication *v31; // rax@69
  tagRECT Rect; // [sp+40h] [bp-28h]@59
  signed __int64 v33; // [sp+70h] [bp+8h]@1
  char *v34; // [sp+78h] [bp+10h]@1
  __int64 v35; // [sp+80h] [bp+18h]@54

  v1 = this;
  v2 = 0;
  v34 = (char *)this + 17344 * *((_DWORD *)this + 43363) + 8;
  v3 = 3248i64 * *((_DWORD *)this + 4336 * *((_DWORD *)this + 43363) + 4336);
  v4 = (signed __int64)&v34[v3 + 1096];
  v33 = v4 + 20i64 * *(_DWORD *)&v34[v3 + 4328] + 232;
  if ( !*((_DWORD *)this + 43370) )
    goto LABEL_18;
  *(_DWORD *)&v34[v3 + 4332] = 0;
  v5 = *((_DWORD *)this + 43519) < 0x20u;
  *((_DWORD *)this + 43364) = 0;
  if ( v5 )
  {
    v11 = 0;
    if ( *(_DWORD *)&v34[v3 + 1324] <= 0u )
      goto LABEL_18;
    v12 = *((_DWORD *)this + 43522);
    v13 = &v34[v3 + 1332];
    while ( *((_DWORD *)v13 - 1) != v12 || *(_DWORD *)v13 != *((_DWORD *)v1 + 43523) )
    {
      ++v11;
      v13 += 20;
      if ( v11 >= *(_DWORD *)&v34[v3 + 1324] )
        goto LABEL_18;
    }
    v10 = v11;
  }
  else
  {
    v6 = *(_DWORD *)&v34[v3 + 1324];
    v7 = *(_DWORD *)&v34[v3 + 1324];
    if ( (signed int)v6 < 0 )
      goto LABEL_18;
    v8 = *((_DWORD *)this + 43522);
    v9 = (_DWORD *)(v4 + 20 * v6 + 236);
    while ( *(v9 - 1) != v8 || *v9 != *((_DWORD *)v1 + 43523) )
    {
      LODWORD(v6) = v6 - 1;
      v9 -= 5;
      if ( --v7 < 0 )
        goto LABEL_18;
    }
    v10 = (signed int)v6;
  }
  v33 = v4 + 20 * v10 + 232;
LABEL_18:
  (**(void (__fastcall ***)(CD3DApplication *))v1)(v1);
  v14 = (signed __int64)v1 + 173488;
  *(_QWORD *)v14 = 0i64;
  *(_QWORD *)(v14 + 8) = 0i64;
  *(_QWORD *)(v14 + 16) = 0i64;
  *(_QWORD *)(v14 + 24) = 0i64;
  *(_QWORD *)(v14 + 32) = 0i64;
  *(_QWORD *)(v14 + 40) = 0i64;
  *(_QWORD *)(v14 + 48) = 0i64;
  v15 = *(_DWORD *)(v4 + 3236);
  *((_DWORD *)v1 + 43375) = 1;
  *((_DWORD *)v1 + 43380) = v15;
  if ( *(_DWORD *)(v4 + 3236) )
    v16 = *(_DWORD *)(v4 + 3240);
  else
    v16 = *(_DWORD *)(v4 + 3244);
  v17 = *((_DWORD *)v1 + 43364) == 0;
  *((_DWORD *)v1 + 43376) = v16;
  v18 = *((_DWORD *)v1 + 43518);
  *((_DWORD *)v1 + 43377) = 1;
  *((_DWORD *)v1 + 43381) = v18;
  *((_DWORD *)v1 + 43382) = *(_DWORD *)(v33 + 16);
  *((_QWORD *)v1 + 21689) = *((_QWORD *)v1 + 21693);
  if ( v17 )
  {
    *(_DWORD *)v14 = *(_DWORD *)v33;
    *((_DWORD *)v1 + 43373) = *(_DWORD *)(v33 + 4);
    v19 = *(_DWORD *)(v33 + 8);
  }
  else
  {
    *(_DWORD *)v14 = *((_DWORD *)v1 + 43465) - *((_DWORD *)v1 + 43463);
    *((_DWORD *)v1 + 43373) = *((_DWORD *)v1 + 43466) - *((_DWORD *)v1 + 43464);
    v19 = *((_DWORD *)v34 + 271);
  }
  *((_DWORD *)v1 + 43374) = v19;
  if ( _bittest((const signed __int32 *)(v4 + 36), 0xCu) )
    CD3DApplication::DisplayErrorMsg(v1, -2113929203, 0);
  v20 = *(_DWORD *)(v33 + 12);
  v21 = (*(int (__fastcall **)(_QWORD, _QWORD, _QWORD, _QWORD))(**((_QWORD **)v1 + 21696) + 120i64))(
          *((_QWORD *)v1 + 21696),
          *((_DWORD *)v1 + 43363),
          *(_DWORD *)v4,
          *((_QWORD *)v1 + 21694));
  if ( v21 < 0 )
    goto LABEL_67;
  if ( *((_DWORD *)v1 + 43364) )
    SetWindowPos(
      *((HWND *)v1 + 21693),
      (HWND)0xFFFFFFFE,
      *((_DWORD *)v1 + 43459),
      *((_DWORD *)v1 + 43460),
      *((_DWORD *)v1 + 43461) - *((_DWORD *)v1 + 43459),
      *((_DWORD *)v1 + 43462) - *((_DWORD *)v1 + 43460),
      0x40u);
  (*(void (__fastcall **)(_QWORD, signed __int64))(**((_QWORD **)v1 + 21697) + 56i64))(
    *((_QWORD *)v1 + 21697),
    (signed __int64)v1 + 173584);
  *((_DWORD *)v1 + 43457) = *(_DWORD *)(v33 + 12);
  v22 = *(_DWORD *)v4;
  if ( *(_DWORD *)v4 == 2 )
  {
    v23 = "REF";
LABEL_35:
    lstrcpyA((LPSTR)v1 + 173880, v23);
    goto LABEL_36;
  }
  if ( v22 == 1 )
  {
    v23 = "HAL";
    goto LABEL_35;
  }
  if ( v22 == 3 )
  {
    v23 = "SW";
    goto LABEL_35;
  }
LABEL_36:
  v24 = *(_DWORD *)(v33 + 12);
  if ( *(_DWORD *)(v33 + 12) & 0x40 && v24 & 0x10 )
  {
    v25 = (char *)v1 + 173880;
    if ( *(_DWORD *)v4 == 1 )
      v26 = " (pure hw vp)";
    else
      v26 = " (simulated pure hw vp)";
    goto LABEL_51;
  }
  if ( *(_DWORD *)(v33 + 12) & 0x40 )
  {
    v25 = (char *)v1 + 173880;
    if ( *(_DWORD *)v4 == 1 )
      v26 = " (hw vp)";
    else
      v26 = " (simulated hw vp)";
    goto LABEL_51;
  }
  if ( (char)v24 < 0 )
  {
    v25 = (char *)v1 + 173880;
    if ( *(_DWORD *)v4 == 1 )
      v26 = " (mixed vp)";
    else
      v26 = " (simulated mixed vp)";
    goto LABEL_51;
  }
  if ( v24 & 0x20 )
  {
    v25 = (char *)v1 + 173880;
    v26 = " (sw vp)";
LABEL_51:
    lstrcatA(v25, v26);
  }
  if ( *(_DWORD *)v4 == 1 )
  {
    lstrcatA((LPSTR)v1 + 173880, ": ");
    lstrcatA((LPSTR)v1 + 173880, v34 + 512);
  }
  (*(void (__fastcall **)(_QWORD, _QWORD, _QWORD, __int64 *))(**((_QWORD **)v1 + 21697) + 128i64))(
    *((_QWORD *)v1 + 21697),
    0i64,
    0i64,
    &v35);
  (*(void (__fastcall **)(__int64, signed __int64))(*(_QWORD *)v35 + 64i64))(v35, (signed __int64)v1 + 173796);
  (*(void (**)(void))(*(_QWORD *)v35 + 16i64))();
  if ( *((_DWORD *)v1 + 43524) && !*((_DWORD *)v1 + 43364) )
  {
    v27 = (HICON)GetClassLongPtrA(*((HWND *)v1 + 21693), -12);
    D3DUtil_SetDeviceCursor(*((struct IDirect3DDevice8 **)v1 + 21697), v27, 0);
    (*(void (__fastcall **)(_QWORD, signed __int64))(**((_QWORD **)v1 + 21697) + 96i64))(*((_QWORD *)v1 + 21697), 1i64);
  }
  if ( *((_DWORD *)v1 + 43525) )
  {
    if ( *((_DWORD *)v1 + 43364) )
    {
      v28 = 0i64;
    }
    else
    {
      GetWindowRect(*((HWND *)v1 + 21693), &Rect);
      v28 = &Rect;
    }
    ClipCursor(v28);
  }
  v21 = (*(int (__fastcall **)(CD3DApplication *))(*(_QWORD *)v1 + 24i64))(v1);
  if ( v21 >= 0 )
  {
    v21 = (*(int (__fastcall **)(CD3DApplication *))(*(_QWORD *)v1 + 32i64))(v1);
    if ( v21 >= 0 )
    {
      *((_DWORD *)v1 + 43365) = 1;
      return 0i64;
    }
  }
  (*(void (__fastcall **)(CD3DApplication *))(*(_QWORD *)v1 + 56i64))(v1);
  (*(void (__fastcall **)(CD3DApplication *))(*(_QWORD *)v1 + 64i64))(v1);
  v30 = *((_QWORD *)v1 + 21697);
  if ( v30 )
  {
    (*(void (**)(void))(*(_QWORD *)v30 + 16i64))();
    *((_QWORD *)v1 + 21697) = 0i64;
  }
LABEL_67:
  if ( *(_DWORD *)v4 == 1 )
  {
    *((_DWORD *)v1 + 43363) = 0;
    if ( *((_DWORD *)v1 + 274) > 0u )
    {
      v31 = (CD3DApplication *)((char *)v1 + 1104);
      while ( *(_DWORD *)v31 != 2 )
      {
        ++v2;
        v31 = (CD3DApplication *)((char *)v31 + 3248);
        if ( v2 >= *((_DWORD *)v1 + 274) )
          goto LABEL_74;
      }
      *((_DWORD *)v1 + 4336) = v2;
      *((_DWORD *)v1 + 43364) = *((_DWORD *)v1 + 812 * v2 + 1085);
    }
LABEL_74:
    if ( *((_DWORD *)v1 + 812 * *((_DWORD *)v1 + 4336) + 276) == 2 )
    {
      SetWindowPos(
        *((HWND *)v1 + 21693),
        (HWND)0xFFFFFFFE,
        *((_DWORD *)v1 + 43459),
        *((_DWORD *)v1 + 43460),
        *((_DWORD *)v1 + 43461) - *((_DWORD *)v1 + 43459),
        *((_DWORD *)v1 + 43462) - *((_DWORD *)v1 + 43460),
        0x40u);
      (**(void (__fastcall ***)(CD3DApplication *))v1)(v1);
      CD3DApplication::DisplayErrorMsg(v1, v21, 2);
      v21 = CD3DApplication::Initialize3DEnvironment(v1);
    }
  }
  return (unsigned int)v21;
}
