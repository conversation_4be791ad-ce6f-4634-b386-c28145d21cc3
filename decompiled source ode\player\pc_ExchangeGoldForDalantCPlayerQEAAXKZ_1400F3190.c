/*
 * Function: ?pc_ExchangeGoldForDalant@CPlayer@@QEAAXK@Z
 * Address: 0x1400F3190
 */

void __fastcall CPlayer::pc_ExchangeGoldForDalant(CPlayer *this, unsigned int dwGold)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@4
  unsigned int v5; // eax@9
  unsigned int v6; // eax@12
  CMoneySupplyMgr *v7; // rax@16
  __int64 v8; // [sp+0h] [bp-88h]@1
  char v9; // [sp+40h] [bp-48h]@4
  unsigned int dwCurGold; // [sp+44h] [bp-44h]@4
  unsigned int dwCurDalant; // [sp+48h] [bp-40h]@4
  int v12; // [sp+4Ch] [bp-3Ch]@4
  unsigned int v13; // [sp+50h] [bp-38h]@4
  unsigned int nAmount; // [sp+54h] [bp-34h]@4
  unsigned __int64 ui64AddMoney; // [sp+58h] [bp-30h]@4
  char *v16; // [sp+60h] [bp-28h]@12
  unsigned int v17; // [sp+68h] [bp-20h]@12
  int nLv; // [sp+6Ch] [bp-1Ch]@16
  int v19; // [sp+70h] [bp-18h]@16
  CPlayer *p; // [sp+90h] [bp+8h]@1
  unsigned int dwSub; // [sp+98h] [bp+10h]@1

  dwSub = dwGold;
  p = this;
  v2 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = 0;
  dwCurGold = CPlayerDB::GetGold(&p->m_Param);
  dwCurDalant = CPlayerDB::GetDalant(&p->m_Param);
  v4 = CPlayerDB::GetRaceCode(&p->m_Param);
  v12 = eGetRate(v4);
  v13 = v12 * dwSub;
  nAmount = (signed int)ffloor((float)(signed int)(v12 * dwSub) * 0.1);
  LODWORD(ui64AddMoney) = v12 * dwSub - nAmount;
  if ( IsBeNearStore(p, 10) )
  {
    if ( dwSub <= dwCurGold )
    {
      if ( (_DWORD)ui64AddMoney )
      {
        v5 = CPlayerDB::GetDalant(&p->m_Param);
        if ( !CanAddMoneyForMaxLimMoney((unsigned int)ui64AddMoney, v5) )
          v9 = 2;
      }
    }
    else
    {
      v9 = 1;
    }
  }
  else
  {
    v9 = 13;
  }
  if ( !v9 )
  {
    CPlayer::SubGold(p, dwSub);
    CPlayer::AddDalant(p, ui64AddMoney, 0);
    v16 = p->m_szItemHistoryFileName;
    v17 = CPlayerDB::GetGold(&p->m_Param);
    v6 = CPlayerDB::GetDalant(&p->m_Param);
    CMgrAvatorItemHistory::exchange_money(
      &CPlayer::s_MgrItemHistory,
      p->m_ObjID.m_wIndex,
      dwCurDalant,
      dwCurGold,
      v6,
      v17,
      v16);
    HIDWORD(ui64AddMoney) = CPlayerDB::GetLevel(&p->m_Param);
    if ( HIDWORD(ui64AddMoney) == 30
      || HIDWORD(ui64AddMoney) == 40
      || HIDWORD(ui64AddMoney) == 50
      || HIDWORD(ui64AddMoney) == 60 )
    {
      nLv = CPlayerDB::GetLevel(&p->m_Param);
      v19 = CPlayerDB::GetRaceCode(&p->m_Param);
      v7 = CMoneySupplyMgr::Instance();
      CMoneySupplyMgr::UpdateFeeMoneyData(v7, v19, nLv, nAmount);
    }
  }
  CPlayer::SendMsg_ExchangeMoneyResult(p, v9);
}
