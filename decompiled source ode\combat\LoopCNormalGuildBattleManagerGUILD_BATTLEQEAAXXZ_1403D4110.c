/*
 * Function: ?Loop@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403D4110
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleManager::Loop(GUILD_BATTLE::CNormalGuildBattleManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int v4; // [sp+20h] [bp-18h]@7
  unsigned int j; // [sp+24h] [bp-14h]@7
  GUILD_BATTLE::CNormalGuildBattleManager *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_bLoad && v6->m_ppkTodayBattle )
  {
    v4 = 23 * v6->m_uiMapCnt;
    for ( j = 0; j < v4; ++j )
    {
      if ( GUILD_BATTLE::CNormalGuildBattle::IsProc(v6->m_ppkTodayBattle[j]) )
        GUILD_BATTLE::CNormalGuildBattle::Process(v6->m_ppkTodayBattle[j]);
    }
  }
}
