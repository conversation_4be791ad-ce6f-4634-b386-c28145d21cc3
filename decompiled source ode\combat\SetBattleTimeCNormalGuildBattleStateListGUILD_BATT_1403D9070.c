/*
 * Function: ?SetBattleTime@CNormalGuildBattleStateList@GUILD_BATTLE@@QEAAXVCTimeSpan@ATL@@@Z
 * Address: 0x1403D9070
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleStateList::SetBattleTime(GUILD_BATTLE::CNormalGuildBattleStateList *this, ATL::CTimeSpan kTime)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleStateList *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  GUILD_BATTLE::CNormalGuildBattleStateInBattle::SetBattleTime(&v5->INBATTLE, kTime);
}
