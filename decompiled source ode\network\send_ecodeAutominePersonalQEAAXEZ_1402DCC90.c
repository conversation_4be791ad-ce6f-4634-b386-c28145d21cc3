/*
 * Function: ?send_ecode@AutominePersonal@@QEAAXE@Z
 * Address: 0x1402DCC90
 */

void __fastcall AutominePersonal::send_ecode(AutominePersonal *this, char byCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@6
  __int64 v5; // [sp+0h] [bp-78h]@1
  _personal_amine_errmsg_zocl v6; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  AutominePersonal *v9; // [sp+80h] [bp+8h]@1
  char v10; // [sp+88h] [bp+10h]@1

  v10 = byCode;
  v9 = this;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _personal_amine_errmsg_zocl::_personal_amine_errmsg_zocl(&v6);
  v6.byErrCode = v10;
  pbyType = 14;
  v8 = 62;
  if ( v9->m_pOwner )
  {
    if ( v9->m_pOwner->m_bOper )
    {
      v4 = _personal_amine_errmsg_zocl::size(&v6);
      CNetProcess::LoadSendMsg(unk_1414F2088, v9->m_pOwner->m_id.wIndex, &pbyType, &v6.byErrCode, v4);
    }
  }
}
