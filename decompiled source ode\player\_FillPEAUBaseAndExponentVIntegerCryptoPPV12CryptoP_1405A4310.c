/*
 * Function: ??$_Fill@PEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@U12@@std@@YAXPEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@0AEBU12@@Z
 * Address: 0x1405A4310
 */

__int64 __fastcall std::_Fill<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>(__int64 a1, __int64 a2, __int64 a3)
{
  __int64 result; // rax@2
  __int64 i; // [sp+30h] [bp+8h]@1
  __int64 v5; // [sp+38h] [bp+10h]@1
  __int64 v6; // [sp+40h] [bp+18h]@1

  v6 = a3;
  v5 = a2;
  for ( i = a1; ; i += 80i64 )
  {
    result = v5;
    if ( i == v5 )
      break;
    CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::operator=(i, v6);
  }
  return result;
}
