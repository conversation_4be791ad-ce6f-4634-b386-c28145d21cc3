/*
 * Function: ?SaveAll@BossSchedule_Map@@QEAA_NXZ
 * Address: 0x140419FB0
 */

char __fastcall BossSchedule_Map::SaveAll(BossSchedule_Map *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@5
  BossSchedule *pSchedule; // [sp+28h] [bp-10h]@7
  BossSchedule_Map *pMapSchedule; // [sp+40h] [bp+8h]@1

  pMapSchedule = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( pMapSchedule->m_ScheduleList )
  {
    for ( j = 0; j < pMapSchedule->m_nCount; ++j )
    {
      pSchedule = pMapSchedule->m_ScheduleList[j];
      if ( !pSchedule )
        return 0;
      if ( !pMapSchedule->m_pSystem )
        return 0;
      CBossMonsterScheduleSystem::Savechedule(pMapSchedule->m_pSystem, pMapSchedule, pSchedule);
    }
  }
  return 1;
}
