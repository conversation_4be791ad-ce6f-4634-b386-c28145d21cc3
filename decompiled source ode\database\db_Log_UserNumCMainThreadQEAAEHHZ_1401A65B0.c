/*
 * Function: ?db_Log_UserNum@CMainThread@@QEAAEHH@Z
 * Address: 0x1401A65B0
 */

char __fastcall CMainThread::db_Log_UserNum(CMainThread *this, int nAveragePerHour, int nMaxPerHour)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  CMainThread *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( CRFWorldDatabase::Insert_UserNum_Log(v7->m_pWorldDB, nAveragePerHour, nMaxPerHour) )
    result = 0;
  else
    result = 24;
  return result;
}
