/*
 * Function: ?CreateLogFile@CNormalGuildBattle@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403E7040
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::CreateLogFile(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@8
  GUILD_BATTLE::CGuildBattleLogger *v4; // rax@9
  __int64 v5; // [sp+0h] [bp-488h]@1
  char *v6; // [sp+20h] [bp-468h]@8
  char *v7; // [sp+28h] [bp-460h]@8
  char v8; // [sp+30h] [bp-458h]@4
  char Dest; // [sp+50h] [bp-438h]@8
  const char *v10; // [sp+458h] [bp-30h]@4
  char *v11; // [sp+460h] [bp-28h]@4
  char *v12; // [sp+468h] [bp-20h]@4
  unsigned __int64 v13; // [sp+478h] [bp-10h]@4
  GUILD_BATTLE::CNormalGuildBattle *v14; // [sp+490h] [bp+8h]@1

  v14 = this;
  v1 = &v5;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  v8 = 1;
  v10 = "NONE";
  v11 = GUILD_BATTLE::CNormalGuildBattleGuild::GetANSIGuildName(&v14->m_k1P);
  v12 = GUILD_BATTLE::CNormalGuildBattleGuild::GetANSIGuildName(&v14->m_k2P);
  if ( !v11 )
  {
    v11 = (char *)v10;
    v8 = 0;
  }
  if ( !v12 )
  {
    v12 = (char *)v10;
    v8 = 0;
  }
  v3 = v14->m_dwID % 0x17;
  v7 = v12;
  v6 = v11;
  sprintf(&Dest, "[%u]_(%02d:00)_[%s]:[%s]_", v14->m_dwID, v3);
  GUILD_BATTLE::CNormalGuildBattleLogger::CreateLogFile(&v14->m_kLogger, &Dest);
  if ( !v8 )
  {
    v4 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(v4, "CNormalGuildBattle::CreateLogFile() Fail!");
  }
}
