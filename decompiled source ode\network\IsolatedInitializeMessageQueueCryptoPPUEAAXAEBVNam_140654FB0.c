/*
 * Function: ?IsolatedInitialize@MessageQueue@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x140654FB0
 */

void __fastcall CryptoPP::MessageQueue::IsolatedInitialize(CryptoPP::MessageQueue *this, const struct CryptoPP::NameValuePairs *a2)
{
  __int64 v2; // [sp+20h] [bp-28h]@1
  int v3; // [sp+28h] [bp-20h]@1
  __int64 v4; // [sp+30h] [bp-18h]@1
  CryptoPP::MessageQueue *v5; // [sp+50h] [bp+8h]@1

  v5 = this;
  v4 = *((_QWORD *)this + 4);
  (*(void (__fastcall **)(char *))(v4 + 56))((char *)this + 32);
  v2 = 0i64;
  std::deque<unsigned __int64,std::allocator<unsigned __int64>>::assign((char *)v5 + 112, 1i64, &v2);
  v3 = 0;
  std::deque<unsigned int,std::allocator<unsigned int>>::assign((char *)v5 + 168, 1i64, &v3);
}
