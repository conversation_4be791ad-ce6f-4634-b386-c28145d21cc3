/*
 * Function: ?Load@CCheckSumCharacAccountTrunkData@@QEAAHPEAVCRFWorldDatabase@@AEAV1@@Z
 * Address: 0x1402C0750
 */

signed __int64 __fastcall CCheckSumCharacAccountTrunkData::Load(CCheckSumCharacAccountTrunkData *this, CRFWorldDatabase *pkDB, CCheckSumCharacAccountTrunkData *kSrcValue)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int v7; // [sp+20h] [bp-18h]@9
  char v8; // [sp+24h] [bp-14h]@9
  CCheckSumCharacAccountTrunkData *v9; // [sp+40h] [bp+8h]@1
  CRFWorldDatabase *pkDBa; // [sp+48h] [bp+10h]@1
  CCheckSumCharacAccountTrunkData *v11; // [sp+50h] [bp+18h]@1

  v11 = kSrcValue;
  pkDBa = pkDB;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( !v9->m_dwSerial || v9->m_dwSerial == -1 || v9->m_dwAccountSerial == -1 || !pkDB )
    return 0i64;
  v7 = 0;
  v8 = CRFWorldDatabase::Select_NpcData(pkDB, v9->m_dwSerial, v9->m_dwValues);
  if ( v8 == 1 )
    return 0xFFFFFFFFi64;
  if ( v8 == 2 )
  {
    if ( !CCheckSumCharacAccountTrunkData::InsertCharacData(v11, pkDBa) )
      return 0xFFFFFFFFi64;
    memcpy_0(v9->m_dwValues, v11->m_dwValues, 0x18ui64);
    v7 = 1;
  }
  v8 = CRFWorldDatabase::Select_AnimusData(pkDBa, v9->m_dwAccountSerial, v9->m_byRace, v9->m_dValues);
  if ( v8 == 1 )
    return 24i64;
  if ( v8 == 2 )
  {
    if ( !CCheckSumCharacAccountTrunkData::InsertTrunkData(v11, pkDBa) )
      return 0xFFFFFFFFi64;
    memcpy_0(v9->m_dValues, v11->m_dValues, 0x10ui64);
    v7 = 1;
  }
  return v7;
}
