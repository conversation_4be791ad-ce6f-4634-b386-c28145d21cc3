/*
 * Function: ?UpdateGuildBattleInfo@CRFWorldDatabase@@QEAA_NKKKKE@Z
 * Address: 0x1404A29E0
 */

bool __fastcall CRFWorldDatabase::UpdateGuildBattleInfo(CRFWorldDatabase *this, unsigned int dwID, unsigned int dwP1GuildSerial, unsigned int dwP2GuildSerial, unsigned int dwMapID, char byNumber)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-478h]@1
  unsigned int v10; // [sp+20h] [bp-458h]@4
  unsigned int v11; // [sp+28h] [bp-450h]@4
  int v12; // [sp+30h] [bp-448h]@4
  char Dest; // [sp+50h] [bp-428h]@4
  unsigned __int64 v14; // [sp+460h] [bp-18h]@4
  CRFWorldDatabase *v15; // [sp+480h] [bp+8h]@1

  v15 = this;
  v6 = &v9;
  for ( i = 284i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v14 = (unsigned __int64)&v9 ^ _security_cookie;
  v12 = (unsigned __int8)byNumber;
  v11 = dwMapID;
  v10 = dwP2GuildSerial;
  sprintf(&Dest, "{ CALL pUpdate_ReservedGuildBattleInfo( %u, %u, %u, %u, %u ) }", dwID, dwP1GuildSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v15->vfptr, &Dest, 1);
}
