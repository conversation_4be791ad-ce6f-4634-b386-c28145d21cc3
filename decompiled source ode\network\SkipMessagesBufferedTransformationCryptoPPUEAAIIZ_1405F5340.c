/*
 * Function: ?SkipMessages@BufferedTransformation@CryptoPP@@UEAAII@Z
 * Address: 0x1405F5340
 */

int __fastcall CryptoPP::BufferedTransformation::SkipMessages(CryptoPP::BufferedTransformation *this, unsigned int a2)
{
  __int64 v2; // rax@1
  CryptoPP *v3; // rcx@1
  __int64 v4; // rax@2
  int result; // eax@2
  CryptoPP::BitBucket *v6; // rax@3
  CryptoPP::BufferedTransformation *v7; // [sp+40h] [bp+8h]@1
  unsigned int v8; // [sp+48h] [bp+10h]@1

  v8 = a2;
  v7 = this;
  LODWORD(v2) = ((int (*)(void))this->vfptr[20].Clone)();
  if ( v2 )
  {
    LODWORD(v4) = ((int (__fastcall *)(CryptoPP::BufferedTransformation *))v7->vfptr[20].Clone)(v7);
    result = (*(int (__fastcall **)(__int64, _QWORD))(*(_QWORD *)v4 + 208i64))(v4, v8);
  }
  else
  {
    v6 = CryptoPP::TheBitBucket(v3);
    result = CryptoPP::BufferedTransformation::TransferMessagesTo(
               v7,
               v6,
               v8,
               &CryptoPP::BufferedTransformation::NULL_CHANNEL);
  }
  return result;
}
