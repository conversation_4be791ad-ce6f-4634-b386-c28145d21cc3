/*
 * Function: ??0?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@QEAA@XZ
 * Address: 0x140394C70
 */

void __fastcall std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<CUnmannedTraderSchedule> v3; // al@4
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  std::allocator<CUnmannedTraderSchedule> *v6; // [sp+28h] [bp-10h]@4
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (std::allocator<CUnmannedTraderSchedule> *)&v5;
  std::allocator<CUnmannedTraderSchedule>::allocator<CUnmannedTraderSchedule>((std::allocator<CUnmannedTraderSchedule> *)&v5);
  std::_Vector_val<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Vector_val<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(
    (std::_Vector_val<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)&v7->_Myfirstiter,
    v3);
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Buy(v7, 0i64);
}
