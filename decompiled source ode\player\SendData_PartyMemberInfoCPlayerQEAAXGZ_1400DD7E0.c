/*
 * Function: ?SendData_PartyMemberInfo@CPlayer@@QEAAXG@Z
 * Address: 0x1400DD7E0
 */

void __fastcall CPlayer::SendData_PartyMemberInfo(CPlayer *this, unsigned __int16 wDstIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@12
  __int64 v5; // [sp+0h] [bp-E8h]@1
  _party_member_info_upd v6; // [sp+40h] [bp-A8h]@4
  char v7; // [sp+94h] [bp-54h]@4
  int j; // [sp+98h] [bp-50h]@4
  int k; // [sp+9Ch] [bp-4Ch]@6
  bool *v10; // [sp+A0h] [bp-48h]@9
  char pbyType; // [sp+B4h] [bp-34h]@12
  char v12; // [sp+B5h] [bp-33h]@12
  unsigned __int64 v13; // [sp+D0h] [bp-18h]@4
  CPlayer *v14; // [sp+F0h] [bp+8h]@1
  unsigned __int16 v15; // [sp+F8h] [bp+10h]@1

  v15 = wDstIndex;
  v14 = this;
  v2 = &v5;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  _party_member_info_upd::_party_member_info_upd(&v6);
  v6.dwMemSerial = v14->m_dwObjSerial;
  v6.wHPRate = (*(int (__fastcall **)(CPlayer *))&v14->vfptr->gap8[8])(v14);
  v6.wFPRate = CPlayer::CalcCurFPRate(v14);
  v6.wSPRate = CPlayer::CalcCurSPRate(v14);
  v6.byLv = CPlayerDB::GetLevel(&v14->m_Param);
  v6.byMapCode = CPlayerDB::GetMapCode(&v14->m_Param);
  v6.zPos[0] = (signed int)ffloor(v14->m_fCurPos[0]);
  v6.zPos[1] = (signed int)ffloor(v14->m_fCurPos[2]);
  v6.byContEffectNum = 0;
  v7 = 0;
  for ( j = 0; j < 2; ++j )
  {
    for ( k = 0; k < 8; ++k )
    {
      v10 = &v14->m_SFCont[j][k].m_bExist;
      if ( *v10 )
      {
        v6.Effect[(unsigned __int8)v7].wEffectCode = CCharacter::CalcEffectBit(
                                                       (CCharacter *)&v14->vfptr,
                                                       v10[1],
                                                       *((_WORD *)v10 + 1));
        v6.Effect[(unsigned __int8)v7++].byEffectLv = v10[4];
      }
    }
  }
  v6.byContEffectNum = v7;
  pbyType = 16;
  v12 = 19;
  v4 = _party_member_info_upd::size(&v6);
  CNetProcess::LoadSendMsg(unk_1414F2088, v15, &pbyType, (char *)&v6, v4);
}
