/*
 * Function: ?GetPtrPlayerFromCharSerial@@YAPEAVCPlayer@@HK@Z
 * Address: 0x140066520
 */

CPlayer *__fastcall GetPtrPlayerFromCharSerial(int nNum, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  int v7; // [sp+40h] [bp+8h]@1
  unsigned int v8; // [sp+48h] [bp+10h]@1

  v8 = dwSerial;
  v7 = nNum;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < v7; ++j )
  {
    if ( *(&g_Player.m_bLive + 50856 * j)
      && CPlayerDB::GetCharSerial((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * j)) == v8 )
    {
      return &g_Player + j;
    }
  }
  return 0i64;
}
