/*
 * Function: ?SendMsg_FixPosition@CReturnGate@@UEAAXH@Z
 * Address: 0x140168BD0
 */

void __fastcall CReturnGate::SendMsg_FixPosition(CReturnGate *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-A8h]@1
  char szMsg[2]; // [sp+38h] [bp-70h]@4
  unsigned int v7; // [sp+3Ah] [bp-6Eh]@4
  unsigned int v8; // [sp+3Eh] [bp-6Ah]@4
  char Dest; // [sp+42h] [bp-66h]@4
  __int16 pShort; // [sp+53h] [bp-55h]@4
  char pbyType; // [sp+74h] [bp-34h]@4
  char v12; // [sp+75h] [bp-33h]@4
  unsigned __int64 v13; // [sp+90h] [bp-18h]@4
  CReturnGate *v14; // [sp+B0h] [bp+8h]@1
  int dwClientIndex; // [sp+B8h] [bp+10h]@1

  dwClientIndex = n;
  v14 = this;
  v2 = &v5;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  *(_WORD *)szMsg = v14->m_ObjID.m_wIndex;
  v7 = v14->m_dwObjSerial;
  v8 = v14->m_dwOwnerSerial;
  v4 = CPlayerDB::GetCharNameW(&v14->m_pkOwner->m_Param);
  strcpy_0(&Dest, v4);
  FloatToShort(v14->m_fCurPos, &pShort, 3);
  pbyType = 4;
  v12 = -86;
  CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, szMsg, 0x21u);
}
