/*
 * Function: ?IsContPushBefore@CUserDB@@QEAAPEAU_AVATOR_DATA@@XZ
 * Address: 0x140110EC0
 */

_AVATOR_DATA *__fastcall CUserDB::IsContPushBefore(CUserDB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  _AVATOR_DATA *result; // rax@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  unsigned int v5; // [sp+20h] [bp-28h]@13
  char *v6; // [sp+30h] [bp-18h]@14
  CUserDB *v7; // [sp+50h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7->m_bDBWaitState )
  {
    if ( v7->m_pDBPushData )
    {
      if ( v7->m_pDBPushData->m_bUse )
      {
        if ( v7->m_pDBPushData->m_byQryCase == 12 )
        {
          if ( !memcmp_0(&v7->m_idWorld, &v7->m_pDBPushData->m_idWorld, 6ui64) )
          {
            v6 = v7->m_pDBPushData->m_sData;
            if ( *(_DWORD *)v6 == v7->m_dwSerial )
            {
              result = (_AVATOR_DATA *)(v6 + 4);
            }
            else
            {
              v5 = v7->m_dwSerial;
              CLogFile::Write(
                &stru_1799C9718,
                "Before Push Cont Save FAIL >> id: %s, name: %s (%d) : LoadSheet->dwAvatorSerial != m_dwSerial",
                v7->m_dwAccountSerial,
                v7->m_aszAvatorName);
              result = 0i64;
            }
          }
          else
          {
            v5 = v7->m_dwSerial;
            CLogFile::Write(
              &stru_1799C9718,
              "Before Push Cont Save FAIL >> id: %s, name: %s (%d) : if(memcmp(&m_idWorld, &pLoadSheet->m_idWorld, sizeof(_CLID)))",
              v7->m_dwAccountSerial,
              v7->m_aszAvatorName);
            result = 0i64;
          }
        }
        else
        {
          result = 0i64;
        }
      }
      else
      {
        result = 0i64;
      }
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
