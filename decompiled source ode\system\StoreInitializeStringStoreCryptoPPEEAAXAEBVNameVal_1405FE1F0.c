/*
 * Function: ?StoreInitialize@StringStore@CryptoPP@@EEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x1405FE1F0
 */

void __fastcall CryptoPP::StringStore::StoreInitialize(CryptoPP::StringStore *this, const struct CryptoPP::NameValuePairs *a2)
{
  CryptoPP::Name *v2; // rcx@1
  const char *v3; // rax@1
  CryptoPP::ConstByteArrayParameter value; // [sp+20h] [bp-C8h]@1
  CryptoPP::InvalidArgument v5; // [sp+50h] [bp-98h]@2
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+A0h] [bp-48h]@2
  unsigned __int8 v7; // [sp+D0h] [bp-18h]@2
  __int64 v8; // [sp+D8h] [bp-10h]@1
  CryptoPP::StringStore *v9; // [sp+F0h] [bp+8h]@1
  const struct CryptoPP::NameValuePairs *v10; // [sp+F8h] [bp+10h]@1

  v10 = a2;
  v9 = this;
  v8 = -2i64;
  CryptoPP::ConstByteArrayParameter::ConstByteArrayParameter(&value, 0i64, 0);
  v3 = CryptoPP::Name::InputBuffer(v2);
  if ( !CryptoPP::NameValuePairs::GetValue<CryptoPP::ConstByteArrayParameter>(
          (CryptoPP::NameValuePairs *)v10,
          v3,
          &value) )
  {
    memset(&v7, 0, sizeof(v7));
    std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
      &s,
      "StringStore: missing InputBuffer argument",
      v7);
    CryptoPP::InvalidArgument::InvalidArgument(&v5, &s);
    CxxThrowException_0((__int64)&v5, (__int64)&TI3_AVInvalidArgument_CryptoPP__);
  }
  v9->m_store = CryptoPP::ConstByteArrayParameter::begin(&value);
  v9->m_length = CryptoPP::ConstByteArrayParameter::size(&value);
  v9->m_count = 0i64;
  CryptoPP::ConstByteArrayParameter::~ConstByteArrayParameter(&value);
}
