/*
 * Function: ?_SearchAggroNode@CMonsterAggroMgr@@IEAAPEAUCAggroNode@@PEAVCCharacter@@@Z
 * Address: 0x14015E210
 */

CAggroNode *__fastcall CMonsterAggroMgr::_SearchAggroNode(CMonsterAggroMgr *this, CCharacter *pCharacter)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@5
  CMonsterAggroMgr *v7; // [sp+40h] [bp+8h]@1
  CCharacter *v8; // [sp+48h] [bp+10h]@1

  v8 = pCharacter;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pCharacter )
  {
    for ( j = 0; j < 10; ++j )
    {
      if ( v7->m_AggroPool[j].m_pCharacter == v8
        && v7->m_AggroPool[j].m_dwObjectSerial == v8->m_dwObjSerial
        && CAggroNode::IsLive(&v7->m_AggroPool[j]) )
      {
        return &v7->m_AggroPool[j];
      }
    }
  }
  return 0i64;
}
