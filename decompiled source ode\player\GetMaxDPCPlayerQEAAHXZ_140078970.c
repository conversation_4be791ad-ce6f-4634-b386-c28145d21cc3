/*
 * Function: ?GetMaxDP@CPlayer@@QEAAHXZ
 * Address: 0x140078970
 */

__int64 __fastcall CPlayer::GetMaxDP(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  float v3; // xmm0_4@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  float v6; // [sp+20h] [bp-18h]@4
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (float)v7->m_nMaxDP;
  v3 = v6;
  _effect_parameter::GetEff_Plus(&v7->m_EP, 35);
  return (unsigned int)(signed int)ffloor(v6 + v3);
}
