/*
 * Function: ??$_Ptr_cat@PEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@PEAU12@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAPEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@0@Z
 * Address: 0x1405A3ED0
 */

char std::_Ptr_cat<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *>()
{
  char v1; // [sp+0h] [bp-18h]@0

  return v1;
}
