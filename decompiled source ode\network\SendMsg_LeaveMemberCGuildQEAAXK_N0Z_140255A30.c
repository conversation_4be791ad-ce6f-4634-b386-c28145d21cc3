/*
 * Function: ?SendMsg_LeaveMember@CGuild@@QEAAXK_N0@Z
 * Address: 0x140255A30
 */

void __fastcall CGuild::SendMsg_LeaveMember(CGuild *this, unsigned int dwMemberSerial, bool bSelf, bool bPunish)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  bool v8; // [sp+35h] [bp-43h]@4
  unsigned int v9; // [sp+36h] [bp-42h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v11; // [sp+55h] [bp-23h]@4
  int j; // [sp+64h] [bp-14h]@4
  CGuild *v13; // [sp+80h] [bp+8h]@1
  unsigned int v14; // [sp+88h] [bp+10h]@1

  v14 = dwMemberSerial;
  v13 = this;
  v4 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = bSelf;
  v9 = dwMemberSerial;
  v8 = bPunish;
  pbyType = 27;
  v11 = 18;
  for ( j = 0; j < 50; ++j )
  {
    if ( _guild_member_info::IsFill(&v13->m_MemberData[j])
      && v13->m_MemberData[j].pPlayer
      && v14 != v13->m_MemberData[j].pPlayer->m_dwObjSerial )
    {
      CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_MemberData[j].pPlayer->m_ObjID.m_wIndex, &pbyType, &szMsg, 6u);
    }
  }
}
