/*
 * Function: ?SearchNearEnemy@CTrap@@QEAAPEAVCCharacter@@XZ
 * Address: 0x14013FE20
 */

CCharacter *__fastcall CTrap::SearchNearEnemy(CTrap *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  _sec_info *v4; // rax@9
  int v5; // eax@13
  __int64 v6; // rdx@16
  float v7; // xmm0_4@18
  float v8; // xmm0_4@19
  float v9; // xmm0_4@19
  float *v10; // rax@29
  __int64 v12; // [sp+0h] [bp-148h]@1
  _pnt_rect pRect; // [sp+28h] [bp-120h]@4
  __int64 v14; // [sp+48h] [bp-100h]@4
  float v15[3]; // [sp+58h] [bp-F0h]@29
  int v16; // [sp+74h] [bp-D4h]@4
  int v17; // [sp+78h] [bp-D0h]@4
  __int64 v18[10]; // [sp+90h] [bp-B8h]@22
  int j; // [sp+E4h] [bp-64h]@4
  int k; // [sp+E8h] [bp-60h]@6
  unsigned int dwSecIndex; // [sp+ECh] [bp-5Ch]@9
  CObjectList *v22; // [sp+F0h] [bp-58h]@9
  _object_list_point *v23; // [sp+F8h] [bp-50h]@10
  CCharacter *v24; // [sp+100h] [bp-48h]@12
  char *v25; // [sp+108h] [bp-40h]@12
  int v26; // [sp+110h] [bp-38h]@27
  int l; // [sp+114h] [bp-34h]@27
  int v28; // [sp+118h] [bp-30h]@29
  int v29; // [sp+11Ch] [bp-2Ch]@13
  CGameObjectVtbl *v30; // [sp+120h] [bp-28h]@13
  float v31; // [sp+128h] [bp-20h]@19
  float v32; // [sp+12Ch] [bp-1Ch]@19
  CMapData *v33; // [sp+130h] [bp-18h]@29
  CTrap *v34; // [sp+150h] [bp+8h]@1

  v34 = this;
  v1 = &v12;
  for ( i = 80i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v14 = 0i64;
  v16 = (signed int)ffloor(*(float *)&v34->m_pRecordSet[5].m_strCode[20]);
  v17 = 0;
  v3 = CGameObject::GetCurSecNum((CGameObject *)&v34->vfptr);
  CMapData::GetRectInRadius(v34->m_pCurMap, &pRect, 1, v3);
  for ( j = pRect.nStarty; j <= pRect.nEndy; ++j )
  {
    for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
    {
      v4 = CMapData::GetSecInfo(v34->m_pCurMap);
      dwSecIndex = v4->m_nSecNumW * j + k;
      v22 = CMapData::GetSectorListPlayer(v34->m_pCurMap, v34->m_wMapLayerIndex, dwSecIndex);
      if ( v22 )
      {
        v23 = v22->m_Head.m_pNext;
        while ( v23 != &v22->m_Tail )
        {
          v24 = (CCharacter *)v23->m_pItem;
          v23 = v23->m_pNext;
          v25 = &v24->m_ObjID.m_byKind;
          if ( !v24->m_bCorpse )
          {
            v29 = ((int (__fastcall *)(CCharacter *))v24->vfptr->GetObjRace)(v24);
            v30 = v34->vfptr;
            v5 = ((int (__fastcall *)(CTrap *))v30->GetObjRace)(v34);
            if ( v29 != v5
              && ((unsigned __int8)((int (__fastcall *)(CCharacter *))v24->vfptr->IsAttackableInTown)(v24)
               || !(unsigned __int8)((int (__fastcall *)(CCharacter *))v24->vfptr->IsInTown)(v24))
              && !CCharacter::GetStealth(v24, 1) )
            {
              LOBYTE(v6) = 1;
              if ( (unsigned __int8)((int (__fastcall *)(CCharacter *, __int64))v24->vfptr->IsBeAttackedAble)(v24, v6) )
              {
                v7 = v24->m_fCurPos[1] - v34->m_fCurPos[1];
                abs(v7);
                if ( v7 <= 100.0 )
                {
                  v8 = (float)v16;
                  v31 = (float)v16;
                  ((void (__fastcall *)(CCharacter *))v24->vfptr->GetWidth)(v24);
                  v9 = v31 + (float)(v8 / 2.0);
                  v32 = v9;
                  GetSqrt(v24->m_fCurPos, v34->m_fCurPos);
                  if ( v9 <= v32 && !v25[1] && v17 < 10 )
                    v18[v17++] = (__int64)v24;
                }
              }
            }
          }
        }
      }
    }
  }
  if ( v17 > 0 )
  {
    v26 = rand() % v17;
    for ( l = 0; l < v17; ++l )
    {
      v28 = (l + v26) % v17;
      v10 = (float *)(v18[v28] + 40);
      v33 = v34->m_pCurMap;
      if ( (unsigned int)CBsp::CanYouGoThere(v33->m_Level.mBsp, v34->m_fCurPos, v10, (float (*)[3])v15) )
        return (CCharacter *)v18[v28];
    }
  }
  return 0i64;
}
