/*
 * Function: ??0?$_Vector_const_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAA@PEAPEAVCLogTypeDBTask@@@Z
 * Address: 0x1402C6A70
 */

void __fastcall std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, CLogTypeDBTask **_Ptr)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v5; // [sp+30h] [bp+8h]@1
  CLogTypeDBTask **v6; // [sp+38h] [bp+10h]@1

  v6 = _Ptr;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &>::_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &>((std::_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &> *)&v5->_Mycont);
  v5->_Myptr = v6;
}
