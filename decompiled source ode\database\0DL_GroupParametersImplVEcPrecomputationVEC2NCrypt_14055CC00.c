/*
 * Function: ??0?$DL_GroupParametersImpl@V?$EcPrecomputation@VEC2N@CryptoPP@@@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@UEC2NPoint@CryptoPP@@@2@V?$DL_GroupParameters@UEC2NPoint@CryptoPP@@@2@@CryptoPP@@QEAA@XZ
 * Address: 0x14055CC00
 */

__int64 __fastcall CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::EC2N>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>,CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>>::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::EC2N>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>,CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>>(__int64 a1, int a2)
{
  __int64 v3; // [sp+40h] [bp+8h]@1

  v3 = a1;
  if ( a2 )
  {
    *(_QWORD *)(a1 + 8) = &CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::EC2N>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>,CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>>::`vbtable';
    CryptoPP::CryptoMaterial::CryptoMaterial((CryptoPP::CryptoMaterial *)(a1 + 312));
  }
  CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>::DL_GroupParameters<CryptoPP::EC2NPoint>(v3, 0i64);
  *(_QWORD *)v3 = &CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::EC2N>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>,CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>>::`vftable'{for `CryptoPP::GeneratableCryptoMaterial'};
  *(_QWORD *)(v3 + *(_DWORD *)(*(_QWORD *)(v3 + 8) + 4i64) + 8) = &CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::EC2N>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>,CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>>::`vftable'{for `CryptoPP::CryptoMaterial'};
  *(_DWORD *)(v3 + *(_DWORD *)(*(_QWORD *)(v3 + 8) + 4i64) + 4) = 0;
  CryptoPP::EcPrecomputation<CryptoPP::EC2N>::EcPrecomputation<CryptoPP::EC2N>(v3 + 24);
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>(v3 + 152);
  return v3;
}
