/*
 * Function: ?Exit_Account_Request@CUserDB@@QEAAXXZ
 * Address: 0x140111020
 */

void __usercall CUserDB::Exit_Account_Request(CUserDB *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager *v5; // rax@4
  unsigned int v6; // eax@10
  unsigned int v7; // eax@11
  __int64 v8; // rax@11
  int v9; // eax@16
  __int64 v10; // [sp-20h] [bp-12458h]@1
  char *pQryData; // [sp+0h] [bp-12438h]@13
  int nSize; // [sp+8h] [bp-12430h]@13
  _qry_sheet_logout Dst; // [sp+20h] [bp-12418h]@7
  void *Src; // [sp+122F8h] [bp-140h]@7
  __int64 v15; // [sp+12300h] [bp-138h]@11
  __int64 v16; // [sp+12308h] [bp-130h]@12
  _qry_case_lobby_logout v17; // [sp+12320h] [bp-118h]@16
  __int64 v18; // [sp+12400h] [bp-38h]@4
  int n; // [sp+12408h] [bp-30h]@4
  int v20; // [sp+1240Ch] [bp-2Ch]@10
  __int64 v21; // [sp+12410h] [bp-28h]@10
  __int64 v22; // [sp+12418h] [bp-20h]@11
  unsigned __int64 v23; // [sp+12420h] [bp-18h]@4
  CUserDB *v24; // [sp+12440h] [bp+8h]@1

  v24 = this;
  v2 = alloca(a2);
  v3 = &v10;
  for ( i = 18708i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v18 = -2i64;
  v23 = (unsigned __int64)&v10 ^ _security_cookie;
  n = v24->m_idWorld.wIndex;
  v5 = CTSingleton<CNationSettingManager>::Instance();
  CNationSettingManager::OnDisConnectSession(v5, n);
  if ( v24->m_bActive )
  {
    if ( v24->m_dwSerial == -1 )
    {
      _qry_case_lobby_logout::_qry_case_lobby_logout(&v17);
      v17.dwAccountSerial = v24->m_dwAccountSerial;
      strcpy_s(v17.szLobbyHistoryFileName, 0x40ui64, v24->m_szLobbyHistoryFileName);
      v9 = _qry_case_lobby_logout::size(&v17);
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -85, (char *)&v17, v9);
      CUserDB::Exit_Account_Complete(v24, 0);
    }
    else
    {
      _qry_sheet_logout::_qry_sheet_logout(&Dst);
      Dst.dwAvatorSerial = v24->m_dwSerial;
      memcpy_0(&Dst.NewData, &v24->m_AvatorData, 0x915Fui64);
      Src = CUserDB::IsContPushBefore(v24);
      if ( Src )
        memcpy_0(&Dst.OldData, Src, 0x915Fui64);
      else
        memcpy_0(&Dst.OldData, &v24->m_AvatorData_bk, 0x915Fui64);
      v20 = v24->m_bNoneUpdateData == 0;
      Dst.bCheckLowHigh = v20;
      Dst.bUpdateRefineCnt = 0;
      v6 = v24->m_idWorld.wIndex;
      v21 = *qword_1799C9AF0;
      if ( (unsigned __int8)(*(int (__fastcall **)(_QWORD *, _QWORD))(v21 + 40))(qword_1799C9AF0, v6) )
      {
        v7 = v24->m_idWorld.wIndex;
        v22 = *qword_1799C9AF0;
        LODWORD(v8) = (*(int (__fastcall **)(_QWORD *, _QWORD, _QWORD))(v22 + 64))(
                        qword_1799C9AF0,
                        v7,
                        v24->m_idWorld.dwSerial);
        v15 = v8;
        if ( v8 )
        {
          Dst.bUpdateRefineCnt = 1;
          v16 = v15;
          Dst.byRefinedCnt = *(_BYTE *)(v15 + 16);
          Dst.dwRefineDate = *(_DWORD *)(v15 + 12);
        }
      }
      nSize = _qry_sheet_logout::size(&Dst);
      pQryData = (char *)&Dst;
      if ( CMainThread::PushDQSData(&g_Main, v24->m_dwAccountSerial, &v24->m_idWorld, 5, (char *)&Dst, nSize) )
      {
        v24->m_bDBWaitState = 1;
        _qry_sheet_logout::~_qry_sheet_logout(&Dst);
      }
      else
      {
        CUserDB::Exit_Account_Complete(v24, 100);
        _qry_sheet_logout::~_qry_sheet_logout(&Dst);
      }
    }
  }
  else
  {
    CUserDB::ParamInit(v24);
  }
}
