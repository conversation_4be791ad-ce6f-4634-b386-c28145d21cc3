/*
 * Function: j_?_Insert_n@?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@IEAAXV?$_Vector_iterator@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@2@_KAEBVCUnmannedTraderSchedule@@@Z
 * Address: 0x1400038E1
 */

void __fastcall std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Insert_n(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *_Where, unsigned __int64 _Count, CUnmannedTraderSchedule *_Val)
{
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Insert_n(this, _Where, _Count, _Val);
}
