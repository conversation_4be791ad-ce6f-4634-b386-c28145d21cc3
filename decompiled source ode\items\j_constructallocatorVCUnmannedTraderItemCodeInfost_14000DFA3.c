/*
 * Function: j_?construct@?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@QEAAXPEAVCUnmannedTraderItemCodeInfo@@AEBV3@@Z
 * Address: 0x14000DFA3
 */

void __fastcall std::allocator<CUnmannedTraderItemCodeInfo>::construct(std::allocator<CUnmannedTraderItemCodeInfo> *this, CUnmannedTraderItemCodeInfo *_Ptr, CUnmannedTraderItemCodeInfo *_Val)
{
  std::allocator<CUnmannedTraderItemCodeInfo>::construct(this, _Ptr, _Val);
}
