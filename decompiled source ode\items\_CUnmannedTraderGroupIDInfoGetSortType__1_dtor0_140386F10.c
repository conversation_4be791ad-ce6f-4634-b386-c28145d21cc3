/*
 * Function: _CUnmannedTraderGroupIDInfo::GetSortType_::_1_::dtor$0
 * Address: 0x140386F10
 */

void __fastcall CUnmannedTraderGroupIDInfo::GetSortType_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>((std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)(a2 + 56));
}
