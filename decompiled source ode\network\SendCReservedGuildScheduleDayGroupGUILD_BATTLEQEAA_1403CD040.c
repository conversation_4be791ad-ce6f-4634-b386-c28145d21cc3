/*
 * Function: ?Send@CReservedGuildScheduleDayGroup@GUILD_BATTLE@@QEAAXEIHKEK@Z
 * Address: 0x1403CD040
 */

void __fastcall GUILD_BATTLE::CReservedGuildScheduleDayGroup::Send(GUILD_BATTLE::CReservedGuildScheduleDayGroup *this, char byDayID, unsigned int uiMapID, int n, unsigned int dwVer, char byPage, unsigned int dwGuildSerial)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+34h] [bp-54h]@8
  char v11; // [sp+35h] [bp-53h]@8
  char v12; // [sp+36h] [bp-52h]@8
  char pbyType; // [sp+54h] [bp-34h]@8
  char v14; // [sp+55h] [bp-33h]@8
  GUILD_BATTLE::CReservedGuildSchedulePage *pkSelfInfoPage; // [sp+68h] [bp-20h]@12
  GUILD_BATTLE::CReservedGuildSchedulePage *v16; // [sp+70h] [bp-18h]@10
  GUILD_BATTLE::CReservedGuildScheduleDayGroup *v17; // [sp+90h] [bp+8h]@1
  char v18; // [sp+98h] [bp+10h]@1
  unsigned int v19; // [sp+A0h] [bp+18h]@1
  int dwClientIndex; // [sp+A8h] [bp+20h]@1

  dwClientIndex = n;
  v19 = uiMapID;
  v18 = byDayID;
  v17 = this;
  v7 = &v9;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  if ( v17->m_pkList && v17->m_uiMapCnt > uiMapID )
  {
    if ( GUILD_BATTLE::CReservedGuildScheduleMapGroup::GetMaxPage(&v17->m_pkList[uiMapID]) )
    {
      if ( dwGuildSerial == -1 )
        v16 = 0i64;
      else
        v16 = GUILD_BATTLE::CReservedGuildScheduleDayGroup::Find(v17, dwGuildSerial);
      pkSelfInfoPage = v16;
      GUILD_BATTLE::CReservedGuildScheduleMapGroup::Send(&v17->m_pkList[v19], dwClientIndex, dwVer, byPage, v16);
    }
    else
    {
      v12 = v18;
      szMsg = v17->m_byToday;
      v11 = v17->m_byTommorow;
      pbyType = 27;
      v14 = 60;
      CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &szMsg, 3u);
    }
  }
}
