/*
 * Function: ??0CItemUpgradeTable@@QEAA@XZ
 * Address: 0x140202BC0
 */

void __fastcall CItemUpgradeTable::CItemUpgradeTable(CItemUpgradeTable *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CItemUpgradeTable *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->vfptr = (CItemUpgradeTableVtbl *)&CItemUpgradeTable::`vftable';
  CRecordData::CRecordData(&v4->m_tblItemUpgrade);
  v4->m_nResNum = 0;
  v4->m_pwResIndex = 0i64;
}
