/*
 * Function: ?Request@CMoveMapLimitManager@@QEAAEHHHKHPEAD@Z
 * Address: 0x1403A19D0
 */

char __fastcall CMoveMapLimitManager::Request(CMoveMapLimitManager *this, int iLimitType, int iRequetType, int iMapInx, unsigned int dwStoreRecordIndex, int iUserInx, char *pRequest)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  CMoveMapLimitRightInfo *v9; // rax@4
  __int64 v11; // [sp+0h] [bp-48h]@1
  CMoveMapLimitManager *v12; // [sp+50h] [bp+8h]@1
  int iLimitTypea; // [sp+58h] [bp+10h]@1
  int iRequetTypea; // [sp+60h] [bp+18h]@1
  int iMapInxa; // [sp+68h] [bp+20h]@1

  iMapInxa = iMapInx;
  iRequetTypea = iRequetType;
  iLimitTypea = iLimitType;
  v12 = this;
  v7 = &v11;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v9 = CMoveMapLimitRightInfoList::Get(&v12->m_kRightInfo, iUserInx);
  return CMoveMapLimitInfoList::Request(
           &v12->m_kLimitInfo,
           iLimitTypea,
           iRequetTypea,
           iMapInxa,
           dwStoreRecordIndex,
           iUserInx,
           pRequest,
           v9);
}
