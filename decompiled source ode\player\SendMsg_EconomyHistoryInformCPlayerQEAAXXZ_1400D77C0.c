/*
 * Function: ?SendMsg_EconomyHistoryInform@CPlayer@@QEAAXXZ
 * Address: 0x1400D77C0
 */

void __fastcall CPlayer::SendMsg_EconomyHistoryInform(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-D8h]@1
  char szMsg[88]; // [sp+40h] [bp-98h]@8
  _economy_history_data *v5; // [sp+98h] [bp-40h]@4
  int j; // [sp+A0h] [bp-38h]@4
  int k; // [sp+A4h] [bp-34h]@6
  char pbyType; // [sp+B4h] [bp-24h]@10
  char v9; // [sp+B5h] [bp-23h]@10
  CPlayer *v10; // [sp+E0h] [bp+8h]@1

  v10 = this;
  v1 = &v3;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = eGetGuideHistory();
  for ( j = 0; j < 3; ++j )
  {
    for ( k = 0; k < 12; ++k )
      *((_WORD *)&szMsg[24 * j] + k) = v5[k].wEconomyGuide[j];
  }
  pbyType = 12;
  v9 = 14;
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, szMsg, 0x48u);
}
