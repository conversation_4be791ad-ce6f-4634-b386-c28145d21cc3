/*
 * Function: j_??$make_pair@HPEAVCNationSettingFactory@@@std@@YA?AU?$pair@HPEAVCNationSettingFactory@@@0@HPEAVCNationSettingFactory@@@Z
 * Address: 0x140009728
 */

std::pair<int,CNationSettingFactory *> *__fastcall std::make_pair<int,CNationSettingFactory *>(std::pair<int,CNationSettingFactory *> *result, int _Val1, CNationSettingFactory *_Val2)
{
  return std::make_pair<int,CNationSettingFactory *>(result, _Val1, _Val2);
}
