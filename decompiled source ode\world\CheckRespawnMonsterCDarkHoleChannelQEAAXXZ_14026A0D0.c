/*
 * Function: ?CheckRespawnMonster@CDarkHoleChannel@@QEAAXXZ
 * Address: 0x14026A0D0
 */

void __fastcall CDarkHoleChannel::CheckRespawnMonster(CDarkHoleChannel *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  _dh_quest_setup *v3; // rcx@26
  _dh_quest_setup *v4; // rcx@28
  __int64 v5; // [sp+0h] [bp-D8h]@1
  CMonster *pParent; // [sp+20h] [bp-B8h]@26
  bool bRobExp; // [sp+28h] [bp-B0h]@26
  bool bRewardExp; // [sp+30h] [bp-A8h]@26
  bool bDungeon; // [sp+38h] [bp-A0h]@26
  bool bWithoutFail; // [sp+40h] [bp-98h]@26
  bool bApplyRopExpField; // [sp+48h] [bp-90h]@26
  int v12; // [sp+50h] [bp-88h]@6
  DWORD v13; // [sp+54h] [bp-84h]@7
  int j; // [sp+58h] [bp-80h]@7
  _dh_mission_mgr::_respawn_monster_act *v15; // [sp+60h] [bp-78h]@10
  __respawn_monster *v16; // [sp+68h] [bp-70h]@10
  int k; // [sp+70h] [bp-68h]@14
  _dummy_position *pPos; // [sp+78h] [bp-60h]@19
  _dummy_position *v19; // [sp+80h] [bp-58h]@22
  float pNewPos; // [sp+98h] [bp-40h]@24
  _monster_fld *v21; // [sp+B8h] [bp-20h]@26
  _monster_fld *v22; // [sp+C0h] [bp-18h]@28
  __int64 v23; // [sp+C8h] [bp-10h]@28
  CDarkHoleChannel *v24; // [sp+E0h] [bp+8h]@1

  v24 = this;
  v1 = &v5;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v24->m_pQuestSetup )
  {
    if ( v24->m_MissionMgr.pCurMssionPtr )
    {
      v12 = v24->m_MissionMgr.nRespawnActNum;
      if ( v12 )
      {
        v13 = timeGetTime();
        for ( j = 0; ; ++j )
        {
          if ( j >= v12 )
            return;
          v15 = &v24->m_MissionMgr.RespawnMonsterAct[j];
          v16 = v15->pData;
          if ( v13 - v24->m_MissionMgr.RespawnMonsterAct[j].dwLastRespawnTime >= v16->dwTermMSec )
          {
            v15->dwLastRespawnTime = v13;
            if ( v15->bStart )
            {
              if ( v16->nLim <= 0 || v16->nLim > v15->nCum )
                break;
            }
          }
LABEL_8:
          ;
        }
        for ( k = 0; ; ++k )
        {
          if ( k >= v16->ReactObj.wNum )
            goto LABEL_8;
          if ( !v15->NowMonster[k].pMon || v15->NowMonster[k].pMon->m_dwObjSerial != v15->NowMonster[k].dwSerial )
          {
            pPos = 0i64;
            if ( v16->ReactArea.AreaDefType == 1 )
            {
              pPos = v16->ReactArea.obj.dummy.pPos;
            }
            else
            {
              if ( v16->ReactArea.AreaDefType != 2 )
                continue;
              v19 = v16->ReactArea.obj.dummy.pPos;
              pPos = *(_dummy_position **)&v19->m_szCode[8 * (rand() % *(_DWORD *)&v19->m_szCode[8]) + 16];
            }
            if ( CMapData::GetRandPosInDummy(v24->m_pQuestSetup->pUseMap, pPos, &pNewPos, 1) )
            {
              if ( v16->ReactObj.ObjDefType == 2 )
              {
                v21 = v16->ReactObj.obj.monster.pMonsterFld;
                v3 = v24->m_pQuestSetup;
                bApplyRopExpField = 0;
                bWithoutFail = 1;
                bDungeon = 1;
                bRewardExp = 1;
                bRobExp = 0;
                pParent = 0i64;
                v15->NowMonster[k].pMon = CreateRepMonster(
                                            v3->pUseMap,
                                            v24->m_wLayerIndex,
                                            &pNewPos,
                                            v21->m_strCode,
                                            0i64,
                                            0,
                                            1,
                                            1,
                                            1,
                                            0);
              }
              else
              {
                if ( v16->ReactObj.ObjDefType != 4 )
                  goto LABEL_8;
                v22 = v16->ReactObj.obj.monster.pMonsterFld;
                v23 = *(_QWORD *)&v22->m_strCode[8 * (rand() % *(_DWORD *)&v22->m_strCode[4]) + 12];
                v4 = v24->m_pQuestSetup;
                bApplyRopExpField = 0;
                bWithoutFail = 1;
                bDungeon = 1;
                bRewardExp = 1;
                bRobExp = 0;
                pParent = 0i64;
                v15->NowMonster[k].pMon = CreateRepMonster(
                                            v4->pUseMap,
                                            v24->m_wLayerIndex,
                                            &pNewPos,
                                            (char *)(v23 + 4),
                                            0i64,
                                            0,
                                            1,
                                            1,
                                            1,
                                            0);
              }
              if ( v15->NowMonster[k].pMon )
              {
                v15->NowMonster[k].dwSerial = v15->NowMonster[k].pMon->m_dwObjSerial;
                if ( ++v15->nCum >= v16->nLim )
                  goto LABEL_8;
              }
            }
          }
        }
      }
    }
  }
}
