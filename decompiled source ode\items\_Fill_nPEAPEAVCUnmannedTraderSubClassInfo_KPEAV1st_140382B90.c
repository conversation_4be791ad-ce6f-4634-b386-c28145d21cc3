/*
 * Function: ??$_Fill_n@PEAPEAVCUnmannedTraderSubClassInfo@@_KPEAV1@@std@@YAXPEAPEAVCUnmannedTraderSubClassInfo@@_KAEBQEAV1@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140382B90
 */

void __fastcall std::_Fill_n<CUnmannedTraderSubClassInfo * *,unsigned __int64,CUnmannedTraderSubClassInfo *>(CUnmannedTraderSubClassInfo **_First, unsigned __int64 _Count, CUnmannedTraderSubClassInfo *const *_Val, std::_Range_checked_iterator_tag __formal)
{
  memset64(_First, (unsigned __int64)*_Val, _Count);
}
