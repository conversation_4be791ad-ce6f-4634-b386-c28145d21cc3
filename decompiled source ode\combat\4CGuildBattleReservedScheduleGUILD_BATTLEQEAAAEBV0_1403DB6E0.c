/*
 * Function: ??4CGuildBattleReservedSchedule@GUILD_BATTLE@@QEAAAEBV01@AEBV01@@Z
 * Address: 0x1403DB6E0
 */

GUILD_BATTLE::CGuildBattleReservedSchedule *__fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::operator=(GUILD_BATTLE::CGuildBattleReservedSchedule *this, GUILD_BATTLE::CGuildBattleReservedSchedule *kObj)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int j; // [sp+0h] [bp-18h]@1
  GUILD_BATTLE::CGuildBattleReservedSchedule *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = (int *)&j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  for ( j = 0; j < 0x17; ++j )
    v6->m_pkSchedule[j] = kObj->m_pkSchedule[j];
  return v6;
}
