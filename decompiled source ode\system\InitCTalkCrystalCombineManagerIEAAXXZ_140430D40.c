/*
 * Function: ?Init@CTalkCrystalCombineManager@@IEAAXXZ
 * Address: 0x140430D40
 */

void __fastcall CTalkCrystalCombineManager::Init(CTalkCrystalCombineManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CTalkCrystalCombineManager *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->m_pCurrentPlayer = 0i64;
  for ( j = 0; j < 24; ++j )
    _talk_crystal_matrial_combine_node::Init((_talk_crystal_matrial_combine_node *)v5 + j);
}
