/*
 * Function: ?OutOfSec@CPlayer@@UEAAXXZ
 * Address: 0x140055510
 */

void __fastcall CPlayer::OutOfSec(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v3; // rax@5
  __int64 v4; // [sp+0h] [bp-E8h]@1
  char Dest; // [sp+40h] [bp-A8h]@5
  unsigned __int64 v6; // [sp+D0h] [bp-18h]@4
  CPlayer *v7; // [sp+F0h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( v7->m_pUserDB )
  {
    v3 = CPlayerDB::GetCharNameA(&v7->m_Param);
    sprintf(&Dest, "CLOSE>> OutOfSec() ID: %s, NM: %s", v7->m_pUserDB->m_szAccountID, v3);
    CNetworkEX::Close(&g_Network, 0, v7->m_ObjID.m_wIndex, 0, &Dest);
  }
  else
  {
    CNetworkEX::Close(&g_Network, 0, v7->m_ObjID.m_wIndex, 0, 0i64);
  }
  v7->m_bOutOfMap = 1;
}
