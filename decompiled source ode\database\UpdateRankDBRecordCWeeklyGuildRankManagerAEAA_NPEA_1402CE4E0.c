/*
 * Function: ?UpdateRankDBRecord@CWeeklyGuildRankManager@@AEAA_NPEADPEAU_pvppoint_guild_rank_info@@@Z
 * Address: 0x1402CE4E0
 */

char __fastcall CWeeklyGuildRankManager::UpdateRankDBRecord(CWeeklyGuildRankManager *this, char *szDate, _pvppoint_guild_rank_info *pkInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v6; // [sp+0h] [bp-48h]@1
  unsigned int v7; // [sp+20h] [bp-28h]@10
  int v8; // [sp+28h] [bp-20h]@10
  unsigned __int16 j; // [sp+30h] [bp-18h]@7
  char *szDatea; // [sp+58h] [bp+10h]@1
  _pvppoint_guild_rank_info *v11; // [sp+60h] [bp+18h]@1

  v11 = pkInfo;
  szDatea = szDate;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( szDate && pkInfo )
  {
    for ( j = 0; j < (signed int)v11->wCount; ++j )
    {
      if ( !CRFWorldDatabase::Update_PvpPointGuildRankRecord(pkDB, szDatea, v11->list[j].dwSerial, v11->list[j].wRank) )
      {
        v8 = v11->list[j].wRank;
        v7 = v11->list[j].dwSerial;
        CLogFile::Write(
          &stru_1799C9380,
          "CWeeklyGuildRankManager::UpdateRankDBRecord( %s, ... ) : g_Main.m_pWorldDB->Update_WeeklyGuildRankRecord( %s, %u, %u ) Fail!",
          szDatea,
          szDatea);
        return 0;
      }
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
