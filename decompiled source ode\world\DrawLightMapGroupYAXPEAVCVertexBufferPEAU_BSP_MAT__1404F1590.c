/*
 * Function: ?DrawLightMapGroup@@YAXPEAVCVertexBuffer@@PEAU_BSP_MAT_GROUP@@@Z
 * Address: 0x1404F1590
 */

void __fastcall DrawLightMapGroup(struct CVertexBuffer *a1, struct _BSP_MAT_GROUP *a2)
{
  struct _BSP_MAT_GROUP *v2; // rsi@1
  struct IDirect3DDevice8 *v3; // rax@1
  struct IDirect3DDevice8 *v4; // rdi@1
  int v5; // ST28_4@2
  unsigned int v6; // ST20_4@2
  IUnknownVtbl *v7; // rbx@6
  struct IDirect3DTexture8 *v8; // rax@6
  int v9; // ST28_4@6
  unsigned int v10; // ST20_4@6

  v2 = a2;
  v3 = GetD3dDevice();
  v4 = v3;
  if ( v2->MtlId == -1 )
  {
    ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, _QWORD))v3->vfptr[20].AddRef)(v3, 0i64, 0i64);
    v5 = v2->TriNum;
    v6 = v2->IBMinIndex;
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, _QWORD, _QWORD))v4->vfptr[23].Release)(
      v4,
      4i64,
      v2->VBMinIndex,
      v2->VCnt);
  }
  else
  {
    GetMainMaterial();
    ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, _QWORD))v4->vfptr[20].AddRef)(v4, 0i64, 0i64);
    if ( v2->LgtId == -1 )
    {
      MultiTexOff();
      ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64, signed __int64))v4->vfptr[21].QueryInterface)(
        v4,
        0i64,
        1i64,
        5i64);
    }
    else
    {
      MultiTexOn();
      SetLightMap(v2->LgtId);
    }
    ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64, signed __int64))v4->vfptr[21].QueryInterface)(
      v4,
      0i64,
      1i64,
      4i64);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64, signed __int64))v4->vfptr[21].QueryInterface)(
      v4,
      1i64,
      1i64,
      4i64);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64))v4->vfptr[21].QueryInterface)(
      v4,
      0i64,
      3i64);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64, signed __int64))v4->vfptr[21].QueryInterface)(
      v4,
      1i64,
      1i64,
      2i64);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64, signed __int64))v4->vfptr[21].QueryInterface)(
      v4,
      0i64,
      1i64,
      2i64);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64))v4->vfptr[21].QueryInterface)(
      v4,
      2i64,
      1i64);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64, signed __int64))v4->vfptr[21].QueryInterface)(
      v4,
      2i64,
      4i64,
      1i64);
    v7 = v4->vfptr;
    v8 = R3GetSurface(v2->LgtId);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, struct IDirect3DTexture8 *))v7[20].AddRef)(v4, 0i64, v8);
    v9 = v2->TriNum;
    v10 = v2->IBMinIndex;
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, _QWORD, _QWORD))v4->vfptr[23].Release)(
      v4,
      4i64,
      v2->VBMinIndex,
      v2->VCnt);
  }
}
