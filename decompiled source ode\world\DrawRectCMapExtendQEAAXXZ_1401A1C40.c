/*
 * Function: ?DrawRect@CMapExtend@@QEAAXXZ
 * Address: 0x1401A1C40
 */

void __fastcall CMapExtend::DrawRect(CMapExtend *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-58h]@1
  HDC hdc; // [sp+28h] [bp-30h]@5
  int v5; // [sp+34h] [bp-24h]@5
  IDirectDrawSurface7 *v6; // [sp+38h] [bp-20h]@5
  IDirectDrawSurface7 *v7; // [sp+40h] [bp-18h]@6
  CMapExtend *v8; // [sp+60h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v8->m_bSetArea )
  {
    v6 = CSurface::GetDDrawSurface(*v8->m_pSF);
    v5 = ((int (__fastcall *)(IDirectDrawSurface7 *, HDC *))v6->vfptr[5].Release)(v6, &hdc);
    if ( !v5 )
    {
      SelectObject(hdc, v8->m_hPen);
      MoveToEx(hdc, v8->m_ptStartScreen.x, v8->m_ptStartScreen.y, 0i64);
      LineTo(hdc, v8->m_ptMoveScreen.x, v8->m_ptStartScreen.y);
      LineTo(hdc, v8->m_ptMoveScreen.x, v8->m_ptMoveScreen.y);
      LineTo(hdc, v8->m_ptStartScreen.x, v8->m_ptMoveScreen.y);
      LineTo(hdc, v8->m_ptStartScreen.x, v8->m_ptStartScreen.y);
      v7 = CSurface::GetDDrawSurface(*v8->m_pSF);
      ((void (__fastcall *)(IDirectDrawSurface7 *, HDC))v7->vfptr[8].Release)(v7, hdc);
    }
  }
}
