/*
 * Function: j_??$unchecked_uninitialized_copy@PEAPEAU_PVP_RANK_PACKED_DATA@@PEAPEAU1@V?$allocator@PEAU_PVP_RANK_PACKED_DATA@@@std@@@stdext@@YAPEAPEAU_PVP_RANK_PACKED_DATA@@PEAPEAU1@00AEAV?$allocator@PEAU_PVP_RANK_PACKED_DATA@@@std@@@Z
 * Address: 0x1400080DF
 */

_PVP_RANK_PACKED_DATA **__fastcall stdext::unchecked_uninitialized_copy<_PVP_RANK_PACKED_DATA * *,_PVP_RANK_PACKED_DATA * *,std::allocator<_PVP_RANK_PACKED_DATA *>>(_PVP_RANK_PACKED_DATA **_First, _PVP_RANK_PACKED_DATA **_Last, _PVP_RANK_PACKED_DATA **_Dest, std::allocator<_PVP_RANK_PACKED_DATA *> *_Al)
{
  return stdext::unchecked_uninitialized_copy<_PVP_RANK_PACKED_DATA * *,_PVP_RANK_PACKED_DATA * *,std::allocator<_PVP_RANK_PACKED_DATA *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
