/*
 * Function: ?LoadBuyCashMode@CashItemRemoteStore@@AEAA_NXZ
 * Address: 0x1402F4D90
 */

char __fastcall CashItemRemoteStore::LoadBuyCashMode(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-68h]@1
  char ReturnedString; // [sp+34h] [bp-34h]@4
  char v6; // [sp+35h] [bp-33h]@4
  char v7; // [sp+44h] [bp-24h]@4
  unsigned __int64 v8; // [sp+50h] [bp-18h]@4
  CashItemRemoteStore *v9; // [sp+70h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = (unsigned __int64)&v4 ^ _security_cookie;
  ReturnedString = 0;
  memset(&v6, 0, 5ui64);
  v7 = 0;
  GetPrivateProfileStringA("ServerMode", "BuyCashItemByGold", "X", &ReturnedString, 6u, "..\\WorldInfo\\WorldInfo.ini");
  if ( !_stricmp(&ReturnedString, "true") )
  {
    v9->_bIsBuyCashItemByGold = 1;
  }
  else
  {
    if ( _stricmp(&ReturnedString, "false") )
    {
      MyMessageBox(
        "CashItemRemoteStore::LoadBuyCashMode()",
        "WorldInfo.ini File\r\n[ServerMode]\r\nBuyCashItemByGold = %s Invalid!!",
        &ReturnedString);
      return 0;
    }
    v9->_bIsBuyCashItemByGold = 0;
  }
  if ( CMainThread::IsTestServer(&g_Main) && !v9->_bIsBuyCashItemByGold )
  {
    v9->_bIsBuyCashItemByGold = 1;
    MyMessageBox(
      "CashItemRemoteStore::LoadBuyCashMode()",
      "WorldInfo.ini File\r\n"
      "[ServerMode]\r\n"
      "RegularTestServerMode  = true\r\n"
      "BuyCashItemByGold = false -> true Force Changed!!");
  }
  return 1;
}
