/*
 * Function: j_?Load@CNormalGuildBattleManager@GUILD_BATTLE@@QEAA_NHIHHHH@Z
 * Address: 0x1400044A3
 */

bool __fastcall GUILD_BATTLE::CNormalGuildBattleManager::Load(GUILD_BATTLE::CNormalGuildBattleManager *this, int iCurDay, unsigned int uiOldMapCnt, int iToday, int iTodayDayID, int iTomorrow, int iTomorrowDayID)
{
  return GUILD_BATTLE::CNormalGuildBattleManager::Load(
           this,
           iCurDay,
           uiOldMapCnt,
           iToday,
           iTodayDayID,
           iTomorrow,
           iTomorrowDayID);
}
