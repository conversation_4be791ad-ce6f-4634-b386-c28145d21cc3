/*
 * Function: ?Init@_Result_ItemList_Buff@_combine_ex_item_result_zocl@@QEAAXXZ
 * Address: 0x1400B8430
 */

void __fastcall _combine_ex_item_result_zocl::_Result_ItemList_Buff::Init(_combine_ex_item_result_zocl::_Result_ItemList_Buff *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _combine_ex_item_result_zocl::_Result_ItemList_Buff *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->byItemListNum = 0;
  for ( j = 0; j < 24; ++j )
    _combine_ex_item_result_zocl::__item::Init(&v5->RewardItemList[j]);
}
