/*
 * Function: j_??$_Copy_opt@PEAPEAVCMoveMapLimitRight@@PEAPEAV1@Urandom_access_iterator_tag@std@@@std@@YAPEAPEAVCMoveMapLimitRight@@PEAPEAV1@00Urandom_access_iterator_tag@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000B1FE
 */

CMoveMapLimitRight **__fastcall std::_Copy_opt<CMoveMapLimitRight * *,CMoveMapLimitRight * *,std::random_access_iterator_tag>(CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, CMoveMapLimitRight **_Dest, std::random_access_iterator_tag __formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Copy_opt<CMoveMapLimitRight * *,CMoveMapLimitRight * *,std::random_access_iterator_tag>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
