/*
 * Function: ?CascadeExponentiate@?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@CryptoPP@@UEBA?AVInteger@2@AEBV?$DL_GroupPrecomputation@VInteger@CryptoPP@@@2@AEBV32@AEBV?$DL_FixedBasePrecomputation@VInteger@CryptoPP@@@2@1@Z
 * Address: 0x14056F8C0
 */

__int64 __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::CascadeExponentiate(__int64 a1, __int64 a2, __int64 a3, __int64 a4, __int64 a5, __int64 a6)
{
  signed __int64 v6; // rax@1
  __int64 v7; // rax@1
  __int64 v8; // rax@1
  __int64 v9; // rax@1
  __int64 v10; // rax@1
  char v12; // [sp+28h] [bp-E0h]@1
  char v13; // [sp+50h] [bp-B8h]@1
  char *v14; // [sp+68h] [bp-A0h]@1
  char v15; // [sp+70h] [bp-98h]@1
  char *v16; // [sp+88h] [bp-80h]@1
  CryptoPP::Integer v17; // [sp+90h] [bp-78h]@1
  int v18; // [sp+B8h] [bp-50h]@1
  __int64 v19; // [sp+C0h] [bp-48h]@1
  __int64 v20; // [sp+C8h] [bp-40h]@1
  __int64 v21; // [sp+D0h] [bp-38h]@1
  __int64 v22; // [sp+D8h] [bp-30h]@1
  __int64 v23; // [sp+E0h] [bp-28h]@1
  __int64 v24; // [sp+E8h] [bp-20h]@1
  __int64 v25; // [sp+F0h] [bp-18h]@1
  __int64 v26; // [sp+F8h] [bp-10h]@1
  __int64 v27; // [sp+110h] [bp+8h]@1
  __int64 v28; // [sp+118h] [bp+10h]@1
  __int64 v29; // [sp+120h] [bp+18h]@1
  __int64 v30; // [sp+128h] [bp+20h]@1

  v30 = a4;
  v29 = a3;
  v28 = a2;
  v27 = a1;
  v19 = -2i64;
  v18 = 0;
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>(&v12);
  v20 = std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::size(v27 + 96);
  v6 = std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::size(a5 + 96);
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::reserve(
    &v12,
    v6 + v20);
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::PrepareCascade(v27, v29, &v12, v30);
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::PrepareCascade(a5, v29, &v12, a6);
  v14 = &v13;
  v16 = &v15;
  LODWORD(v7) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::end(
                  &v12,
                  &v13);
  v21 = v7;
  v22 = v7;
  LODWORD(v8) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::begin(
                  &v12,
                  v16);
  v23 = v8;
  v24 = v8;
  LODWORD(v9) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v29 + 24i64))(v29);
  LODWORD(v10) = CryptoPP::GeneralCascadeMultiplication<CryptoPP::Integer,std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>>(
                   &v17,
                   v9,
                   v24,
                   v22);
  v25 = v10;
  v26 = v10;
  (*(void (__fastcall **)(__int64, __int64, __int64))(*(_QWORD *)v29 + 16i64))(v29, v28, v10);
  v18 |= 1u;
  CryptoPP::Integer::~Integer(&v17);
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::~vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>(&v12);
  return v28;
}
