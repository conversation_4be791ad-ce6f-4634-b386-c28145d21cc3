/*
 * Function: ?dtor$2@?0??_Reset@?$_Greedy_node@W4agent_status@Concurrency@@@Concurrency@@UEAAXXZ@4HA_0
 * Address: 0x1405EA670
 */

void __fastcall `Concurrency::_Greedy_node<enum  Concurrency::agent_status>::_Reset'::`1'::dtor$2(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 112) & 1 )
  {
    *(_DWORD *)(a2 + 112) &= 0xFFFFFFFE;
    CryptoPP::Integer::~Integer(*(CryptoPP::Integer **)(a2 + 152));
  }
}
