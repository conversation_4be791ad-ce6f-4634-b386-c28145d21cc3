/*
 * Function: ?CalcCurHPRate@CPlayer@@UEAAGXZ
 * Address: 0x14005F560
 */

__int64 __fastcall CPlayer::CalcCurHPRate(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@7
  signed int v4; // eax@8
  __int64 v5; // [sp+0h] [bp-48h]@1
  int v6; // [sp+20h] [bp-28h]@5
  _base_fld *v7; // [sp+28h] [bp-20h]@5
  float v8; // [sp+30h] [bp-18h]@8
  CPlayer *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CPlayer::IsRidingUnit(v9) )
  {
    v6 = 10000;
    v7 = CRecordData::GetRecord(&stru_1799C8BA0, v9->m_pUsingUnit->byFrame);
    if ( *(_DWORD *)&v7[1].m_strCode[0] > 0 )
      v6 = *(_DWORD *)&v7[1].m_strCode[0];
    result = (unsigned int)(signed int)ffloor((float)((float)(signed int)v9->m_pUsingUnit->dwGauge / (float)v6) * 10000.0);
  }
  else
  {
    v8 = (float)((int (__fastcall *)(CPlayer *))v9->vfptr->GetHP)(v9);
    v4 = ((int (__fastcall *)(CPlayer *))v9->vfptr->GetMaxHP)(v9);
    result = (unsigned int)(signed int)ffloor((float)(v8 / (float)v4) * 10000.0);
  }
  return result;
}
