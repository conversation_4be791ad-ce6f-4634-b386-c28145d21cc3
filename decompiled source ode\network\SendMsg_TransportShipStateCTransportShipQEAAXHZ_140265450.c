/*
 * Function: ?SendMsg_TransportShipState@CTransportShip@@QEAAXH@Z
 * Address: 0x140265450
 */

void __fastcall CTransportShip::SendMsg_TransportShipState(CTransportShip *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  bool v6; // [sp+35h] [bp-43h]@4
  char v7; // [sp+36h] [bp-42h]@4
  DWORD v8; // [sp+37h] [bp-41h]@5
  char pbyType; // [sp+54h] [bp-24h]@4
  char v10; // [sp+55h] [bp-23h]@4
  unsigned int dwClientIndex; // [sp+64h] [bp-14h]@5
  CPlayer *v12; // [sp+68h] [bp-10h]@8
  CTransportShip *v13; // [sp+80h] [bp+8h]@1
  int v14; // [sp+88h] [bp+10h]@1

  v14 = n;
  v13 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  szMsg = v13->m_bAnchor;
  v6 = v13->m_bHurry;
  v7 = v13->m_byDirect;
  pbyType = 33;
  v10 = 1;
  if ( n == -1 )
  {
    v8 = 0;
    for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
    {
      v12 = &g_Player + (signed int)dwClientIndex;
      if ( v12->m_bLive )
      {
        if ( CPlayerDB::GetRaceCode(&v12->m_Param) == v13->m_byRaceCode_Layer )
          CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &szMsg, 7u);
      }
    }
  }
  else
  {
    v8 = timeGetTime() - v13->m_dwEventCreateTime;
    CNetProcess::LoadSendMsg(unk_1414F2088, v14, &pbyType, &szMsg, 7u);
  }
}
