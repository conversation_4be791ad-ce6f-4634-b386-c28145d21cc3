/*
 * Function: ??$_Move_backward_opt@PEAV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@std@@PEAV123@Urandom_access_iterator_tag@3@U_Undefined_move_tag@3@@std@@YAPEAV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@0@PEAV120@00Urandom_access_iterator_tag@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140224730
 */

std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *__fastcall std::_Move_backward_opt<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0> *,std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0> *,std::random_access_iterator_tag,std::_Undefined_move_tag>(std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *_First, std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *_Last, std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *_Dest, std::random_access_iterator_tag _First_dest_cat, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v10; // [sp+30h] [bp-18h]@4
  std::_Nonscalar_ptr_iterator_tag v11; // [sp+31h] [bp-17h]@4
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *__formala; // [sp+50h] [bp+8h]@1
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *_Lasta; // [sp+58h] [bp+10h]@1
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *_Desta; // [sp+60h] [bp+18h]@1
  std::input_iterator_tag v15; // [sp+68h] [bp+20h]@1

  v15 = _First_dest_cat.0;
  _Desta = _Dest;
  _Lasta = _Last;
  __formala = _First;
  v6 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  memset(&v10, 0, sizeof(v10));
  v11 = std::_Ptr_cat<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0> *,std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0> *>(
          &__formala,
          &_Desta);
  return std::_Copy_backward_opt<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0> *,std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0> *,std::random_access_iterator_tag>(
           __formala,
           _Lasta,
           _Desta,
           (std::random_access_iterator_tag)v15,
           v11,
           v10);
}
