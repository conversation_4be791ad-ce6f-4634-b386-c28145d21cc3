/*
 * Function: ??$_Fill@PEAVCUnmannedTraderGroupDivisionVersionInfo@@V1@@std@@YAXPEAVCUnmannedTraderGroupDivisionVersionInfo@@0AEBV1@@Z
 * Address: 0x14039B160
 */

void __fastcall std::_Fill<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo>(CUnmannedTraderGroupDivisionVersionInfo *_First, CUnmannedTraderGroupDivisionVersionInfo *_Last, CUnmannedTraderGroupDivisionVersionInfo *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderGroupDivisionVersionInfo *v6; // [sp+30h] [bp+8h]@1
  CUnmannedTraderGroupDivisionVersionInfo *v7; // [sp+38h] [bp+10h]@1
  CUnmannedTraderGroupDivisionVersionInfo *lhs; // [sp+40h] [bp+18h]@1

  lhs = _Val;
  v7 = _Last;
  v6 = _First;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  while ( v6 != v7 )
  {
    CUnmannedTraderGroupDivisionVersionInfo::operator=(v6, lhs);
    ++v6;
  }
}
