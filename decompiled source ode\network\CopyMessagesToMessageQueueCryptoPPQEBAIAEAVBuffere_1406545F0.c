/*
 * Function: ?CopyMessagesTo@MessageQueue@CryptoPP@@QEBAIAEAVBufferedTransformation@2@IAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
 * Address: 0x1406545F0
 */

__int64 __fastcall CryptoPP::MessageQueue::CopyMessagesTo(__int64 a1, __int64 a2, unsigned int a3, __int64 a4)
{
  __int64 v4; // rax@3
  __int64 v5; // rax@3
  __int64 *v6; // rax@9
  char v8; // [sp+20h] [bp-E8h]@1
  unsigned int v9; // [sp+40h] [bp-C8h]@1
  CryptoPP::ByteQueue::Walker v10; // [sp+50h] [bp-B8h]@1
  char v11; // [sp+A0h] [bp-68h]@6
  char v12; // [sp+A8h] [bp-60h]@3
  unsigned int v13; // [sp+C8h] [bp-40h]@12
  int v14; // [sp+CCh] [bp-3Ch]@1
  __int64 v15; // [sp+D0h] [bp-38h]@1
  __int64 v16; // [sp+D8h] [bp-30h]@3
  __int64 v17; // [sp+E0h] [bp-28h]@3
  int v18; // [sp+E8h] [bp-20h]@4
  __int64 v19; // [sp+110h] [bp+8h]@1
  __int64 v20; // [sp+118h] [bp+10h]@1
  unsigned int v21; // [sp+120h] [bp+18h]@1
  __int64 v22; // [sp+128h] [bp+20h]@1

  v22 = a4;
  v21 = a3;
  v20 = a2;
  v19 = a1;
  v15 = -2i64;
  v14 = 0;
  CryptoPP::ByteQueue::Walker::Walker(&v10, (const struct CryptoPP::ByteQueue *)(a1 + 32));
  std::deque<unsigned __int64,std::allocator<unsigned __int64>>::begin(v19 + 112, &v8);
  v9 = 0;
  while ( 1 )
  {
    v18 = v9 < v21
       && (LODWORD(v4) = std::deque<unsigned __int64,std::allocator<unsigned __int64>>::end(v19 + 112, &v12),
           v16 = v4,
           v17 = v4,
           v14 |= 1u,
           LODWORD(v5) = std::_Deque_const_iterator<unsigned __int64,std::allocator<unsigned __int64>,0>::operator--(v4),
           (unsigned __int8)std::_Deque_const_iterator<unsigned __int64,std::allocator<unsigned __int64>,0>::operator!=(
                              &v8,
                              v5));
    v11 = v18;
    if ( v14 & 1 )
    {
      v14 &= 0xFFFFFFFE;
      std::_Deque_const_iterator<unsigned __int64,std::allocator<unsigned __int64>,0>::~_Deque_const_iterator<unsigned __int64,std::allocator<unsigned __int64>,0>(&v12);
    }
    if ( !v11 )
      break;
    LODWORD(v6) = std::_Deque_const_iterator<unsigned __int64,std::allocator<unsigned __int64>,0>::operator*(&v8);
    CryptoPP::BufferedTransformation::TransferTo((__int64)&v10, v20, *v6, v22);
    if ( (*(int (__fastcall **)(__int64))(*(_QWORD *)v19 + 112i64))(v19) )
    {
      (*(void (__fastcall **)(__int64))(*(_QWORD *)v19 + 112i64))(v19);
      CryptoPP::BufferedTransformation::ChannelMessageEnd(v20, v22);
    }
    ++v9;
    std::_Deque_const_iterator<unsigned __int64,std::allocator<unsigned __int64>,0>::operator++(&v8);
  }
  v13 = v9;
  std::_Deque_const_iterator<unsigned __int64,std::allocator<unsigned __int64>,0>::~_Deque_const_iterator<unsigned __int64,std::allocator<unsigned __int64>,0>(&v8);
  CryptoPP::ByteQueue::Walker::~Walker(&v10);
  return v13;
}
