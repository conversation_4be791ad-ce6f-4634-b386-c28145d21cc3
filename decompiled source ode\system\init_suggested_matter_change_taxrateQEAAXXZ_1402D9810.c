/*
 * Function: ?init@_suggested_matter_change_taxrate@@QEAAXXZ
 * Address: 0x1402D9810
 */

void __fastcall _suggested_matter_change_taxrate::init(_suggested_matter_change_taxrate *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _suggested_matter_change_taxrate *Dst; // [sp+30h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(Dst, 0, 0x4Dui64);
  Dst->dwNext = 5;
}
