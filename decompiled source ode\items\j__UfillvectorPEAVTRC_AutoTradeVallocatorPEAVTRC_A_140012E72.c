/*
 * Function: j_?_Ufill@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@IEAAPEAPEAVTRC_AutoTrade@@PEAPEAV3@_KAEBQEAV3@@Z
 * Address: 0x140012E72
 */

TRC_AutoTrade **__fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Ufill(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, TRC_AutoTrade **_Ptr, unsigned __int64 _Count, TRC_AutoTrade *const *_Val)
{
  return std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Ufill(this, _Ptr, _Count, _Val);
}
