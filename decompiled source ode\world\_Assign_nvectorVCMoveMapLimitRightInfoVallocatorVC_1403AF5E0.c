/*
 * Function: ?_Assign_n@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@IEAAX_KAEBVCMoveMapLimitRightInfo@@@Z
 * Address: 0x1403AF5E0
 */

void __fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Assign_n(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, unsigned __int64 _Count, CMoveMapLimitRightInfo *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-108h]@1
  CMoveMapLimitRightInfo v6; // [sp+28h] [bp-E0h]@4
  char v7; // [sp+58h] [bp-B0h]@4
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *result; // [sp+70h] [bp-98h]@4
  char v9; // [sp+78h] [bp-90h]@4
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v10; // [sp+90h] [bp-78h]@4
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > v11; // [sp+98h] [bp-70h]@4
  char v12; // [sp+B0h] [bp-58h]@4
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v13; // [sp+C8h] [bp-40h]@4
  __int64 v14; // [sp+D0h] [bp-38h]@4
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v15; // [sp+D8h] [bp-30h]@4
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v16; // [sp+E0h] [bp-28h]@4
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v17; // [sp+E8h] [bp-20h]@4
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v18; // [sp+F0h] [bp-18h]@4
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v19; // [sp+110h] [bp+8h]@1
  unsigned __int64 v20; // [sp+118h] [bp+10h]@1

  v20 = _Count;
  v19 = this;
  v3 = &v5;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = -2i64;
  CMoveMapLimitRightInfo::CMoveMapLimitRightInfo(&v6, _Val);
  result = (std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *)&v7;
  v10 = (std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *)&v9;
  v15 = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::end(
          v19,
          (std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *)&v7);
  v16 = v15;
  v17 = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::begin(v19, v10);
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::erase(v19, &v11, v17, v16);
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::~_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(&v11);
  v13 = (std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *)&v12;
  v18 = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::begin(
          v19,
          (std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *)&v12);
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::insert(v19, v18, v20, &v6);
  CMoveMapLimitRightInfo::~CMoveMapLimitRightInfo(&v6);
}
