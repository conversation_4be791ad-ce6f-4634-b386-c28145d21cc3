/*
 * Function: ?_check_target_object@CPlayer@@QEAAXXZ
 * Address: 0x1400FF020
 */

void __fastcall CPlayer::_check_target_object(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  CGameObject *v4; // [sp+20h] [bp-18h]@5
  int v5; // [sp+28h] [bp-10h]@13
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_TargetObject.pObject )
  {
    v4 = v6->m_TargetObject.pObject;
    if ( v4->m_bLive )
    {
      if ( v6->m_TargetObject.byKind == v4->m_ObjID.m_byKind
        && v6->m_TargetObject.byID == v4->m_ObjID.m_byID
        && v6->m_TargetObject.dwSerial == v4->m_dwObjSerial )
      {
        if ( v6->m_TargetObject.pObject->m_pCurMap == v6->m_pCurMap )
        {
          v5 = (unsigned __int16)(*(int (__fastcall **)(__int64))&v4->vfptr->gap8[8])((__int64)v4);
          if ( abs_0(v5 - v6->m_TargetObject.wHPRate) > 100 )
          {
            v6->m_TargetObject.wHPRate = v5;
            CPlayer::SendMsg_TargetObjectHPInform(v6);
          }
          CPlayer::SendTargetMonsterSFContInfo(v6);
          CPlayer::SendTargetPlayerDamageContInfo(v6);
        }
        else
        {
          CPlayer::__target::init(&v6->m_TargetObject);
        }
      }
      else
      {
        CPlayer::__target::init(&v6->m_TargetObject);
      }
    }
    else
    {
      CPlayer::__target::init(&v6->m_TargetObject);
    }
  }
}
