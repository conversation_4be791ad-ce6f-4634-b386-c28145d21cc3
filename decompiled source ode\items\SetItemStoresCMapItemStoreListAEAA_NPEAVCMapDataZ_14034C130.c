/*
 * Function: ?SetItemStores@CMapItemStoreList@@AEAA_NPEAVCMapData@@@Z
 * Address: 0x14034C130
 */

char __fastcall CMapItemStoreList::SetItemStores(CMapItemStoreList *this, CMapData *pMap)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CItemStore *v5; // rax@11
  __int64 v6; // [sp+0h] [bp-48h]@1
  _base_fld *pRec; // [sp+20h] [bp-28h]@11
  int nIndex; // [sp+30h] [bp-18h]@9
  _store_dummy *pDum; // [sp+38h] [bp-10h]@11
  CMapItemStoreList *v10; // [sp+50h] [bp+8h]@1
  CMapData *pExistMap; // [sp+58h] [bp+10h]@1

  pExistMap = pMap;
  v10 = this;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v10->m_nItemStoreNum > 0 )
  {
    if ( pMap && pMap->m_pItemStoreDummy )
    {
      for ( nIndex = 0; nIndex < v10->m_nItemStoreNum; ++nIndex )
      {
        pDum = &pExistMap->m_pItemStoreDummy[nIndex];
        v5 = &v10->m_ItemStore[nIndex];
        pRec = pDum->m_pStoreRec;
        CItemStore::Init(v5, nIndex, pExistMap, pDum, pRec);
      }
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
