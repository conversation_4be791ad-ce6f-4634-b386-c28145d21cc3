/*
 * Function: _CUnmannedTraderDivisionInfo::_CUnmannedTraderDivisionInfo_::_1_::dtor$1
 * Address: 0x14036D3C0
 */

void __fastcall CUnmannedTraderDivisionInfo::_CUnmannedTraderDivisionInfo_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::~vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>((std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *)(*(_QWORD *)(a2 + 64) + 176i64));
}
