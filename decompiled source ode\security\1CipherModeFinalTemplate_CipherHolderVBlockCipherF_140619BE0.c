/*
 * Function: ??1?$CipherModeFinalTemplate_CipherHolder@V?$BlockCipherFinal@$0A@VBase@DES@CryptoPP@@@CryptoPP@@VCBC_Encryption@2@@CryptoPP@@UEAA@XZ
 * Address: 0x140619BE0
 */

int __fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>,CryptoPP::CBC_Encryption>::~CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>,CryptoPP::CBC_Encryption>(__int64 a1)
{
  int result; // eax@2
  __int64 v2; // [sp+40h] [bp+8h]@1

  v2 = a1;
  CryptoPP::AlgorithmImpl<CryptoPP::CBC_Encryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>,CryptoPP::CBC_Encryption>>::~AlgorithmImpl<CryptoPP::CBC_Encryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>,CryptoPP::CBC_Encryption>>();
  if ( v2 )
    result = CryptoPP::ObjectHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>>::~ObjectHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>>(v2 + 72);
  else
    result = CryptoPP::ObjectHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>>::~ObjectHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>>(0i64);
  return result;
}
