/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAVCUnmannedTraderGroupDivisionVersionInfo@@_KV1@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@stdext@@YAXPEAVCUnmannedTraderGroupDivisionVersionInfo@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@Z
 * Address: 0x140001FA5
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CUnmannedTraderGroupDivisionVersionInfo *,unsigned __int64,CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(CUnmannedTraderGroupDivisionVersionInfo *_First, unsigned __int64 _Count, CUnmannedTraderGroupDivisionVersionInfo *_Val, std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderGroupDivisionVersionInfo *,unsigned __int64,CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(
    _First,
    _Count,
    _Val,
    _Al);
}
