/*
 * Function: ?ct_recv_change_atrad_taxrate@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140293B60
 */

bool __fastcall ct_recv_change_atrad_taxrate(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  CUnmannedTraderTaxRateManager *v4; // rax@11
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@8
  unsigned int dwNewTaxRate; // [sp+24h] [bp-14h]@8
  char *pCheaterName; // [sp+28h] [bp-10h]@11
  CPlayer *v9; // [sp+40h] [bp+8h]@1

  v9 = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v9 )
  {
    if ( s_nWordCount == 2 )
    {
      v6 = atoi(s_pwszDstCheat[0]);
      dwNewTaxRate = atoi(s_pwszDstCheat[1]);
      if ( v6 >= 0 && v6 <= 2 )
      {
        pCheaterName = CPlayerDB::GetCharNameW(&v9->m_Param);
        v4 = CUnmannedTraderTaxRateManager::Instance();
        result = CUnmannedTraderTaxRateManager::CheatChangeTaxRate(v4, v6, dwNewTaxRate, pCheaterName) != 0;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
