/*
 * Function: ??1CNationSettingManager@@EEAA@XZ
 * Address: 0x1402291F0
 */

void __fastcall CNationSettingManager::~CNationSettingManager(CNationSettingManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  CNationSettingData *v4; // [sp+20h] [bp-28h]@5
  CNationSettingData *v5; // [sp+28h] [bp-20h]@5
  __int64 v6; // [sp+30h] [bp-18h]@4
  void *v7; // [sp+38h] [bp-10h]@6
  CNationSettingManager *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = -2i64;
  v8->vfptr = (CTSingleton<CNationSettingManager>Vtbl *)&CNationSettingManager::`vftable';
  if ( v8->m_pData )
  {
    v5 = v8->m_pData;
    v4 = v5;
    if ( v5 )
      v7 = CNationSettingData::`scalar deleting destructor'(v4, 1u);
    else
      v7 = 0i64;
  }
  CTSingleton<CNationSettingManager>::~CTSingleton<CNationSettingManager>((CTSingleton<CNationSettingManager> *)&v8->vfptr);
}
