/*
 * Function: ?UpdateGoalCnt@CCurrentGuildBattleInfoManager@GUILD_BATTLE@@QEAAXIEK@Z
 * Address: 0x1403CE340
 */

void __fastcall GUILD_BATTLE::CCurrentGuildBattleInfoManager::UpdateGoalCnt(GUILD_BATTLE::CCurrentGuildBattleInfoManager *this, unsigned int uiMapID, char byColorInx, unsigned int dwGoalCnt)
{
  if ( this->m_bInit && this->m_uiMapCnt > uiMapID && (signed int)(unsigned __int8)byColorInx < 2 )
  {
    if ( byColorInx )
      this->m_pkInfo[uiMapID].dwRighBluetGoalCnt = dwGoalCnt;
    else
      this->m_pkInfo[uiMapID].dwLeftRedGoalCnt = dwGoalCnt;
    this->m_pbUpdate[uiMapID] = 1;
  }
}
