/*
 * Function: j_?patriarch_push_money@CMgrAvatorItemHistory@@QEAAXPEADKK0@Z
 * Address: 0x14000884B
 */

void __fastcall CMgrAvatorItemHistory::patriarch_push_money(CMgrAvatorItemHistory *this, char *pwszPatriarchName, unsigned int dwPushDalant, unsigned int dwLeftDalant, char *pszFileName)
{
  CMgrAvatorItemHistory::patriarch_push_money(this, pwszPatriarchName, dwPushDalant, dwLeftDalant, pszFileName);
}
