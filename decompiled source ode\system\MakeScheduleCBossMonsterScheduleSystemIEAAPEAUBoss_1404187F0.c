/*
 * Function: ?MakeSchedule@CBossMonsterScheduleSystem@@IEAAPEAUBossSchedule@@PEAUBossSchedule_Map@@PEAU_mon_active@@PEAU_mon_block@@HH@Z
 * Address: 0x1404187F0
 */

BossSchedule *__fastcall CBossMonsterScheduleSystem::MakeSchedule(CBossMonsterScheduleSystem *this, BossSchedule_Map *pMapSchedule, _mon_active *pMonAct, _mon_block *pBlock, int nActIndex, int nBlockIndex)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  BossSchedule *result; // rax@5
  __int64 v9; // rax@9
  __int64 v10; // [sp+0h] [bp-58h]@1
  char (*_Dest)[64]; // [sp+20h] [bp-38h]@11
  __int64 v12; // [sp+28h] [bp-30h]@11
  BossSchedule *v13; // [sp+30h] [bp-28h]@8
  __int64 v14; // [sp+38h] [bp-20h]@4
  __int64 v15; // [sp+40h] [bp-18h]@9
  BossSchedule_Map *v16; // [sp+68h] [bp+10h]@1
  _mon_active *v17; // [sp+70h] [bp+18h]@1
  _mon_block *v18; // [sp+78h] [bp+20h]@1

  v18 = pBlock;
  v17 = pMonAct;
  v16 = pMapSchedule;
  v6 = &v10;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v14 = -2i64;
  if ( pBlock )
  {
    if ( pMonAct )
    {
      v13 = (BossSchedule *)operator new(0xB0ui64);
      if ( v13 )
      {
        BossSchedule::BossSchedule(v13);
        v15 = v9;
      }
      else
      {
        v15 = 0i64;
      }
      v12 = v15;
      _Dest = (char (*)[64])v15;
      strcpy_s<64>((char (*)[64])(v15 + 64), v17->m_pActRec->m_strCode);
      *(_QWORD *)&(*_Dest)[128] = v18;
      *(_QWORD *)&(*_Dest)[136] = v17;
      *(_DWORD *)&(*_Dest)[144] = nBlockIndex;
      *(_DWORD *)&(*_Dest)[148] = nActIndex;
      *(_WORD *)&(*_Dest)[160] = 0;
      ATL::CTime::operator=(&(*_Dest)[152], 0i64);
      *(_QWORD *)&(*_Dest)[168] = v16;
      sprintf_s<64>(_Dest, "BL_%d_%d", *(_DWORD *)&(*_Dest)[144], *(_DWORD *)&(*_Dest)[148]);
      _mon_active::SetBossSchedule(v17, (BossSchedule *)_Dest);
      result = (BossSchedule *)_Dest;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
