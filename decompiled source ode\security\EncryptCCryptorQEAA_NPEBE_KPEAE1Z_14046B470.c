/*
 * Function: ?Encrypt@CCryptor@@QEAA_NPEBE_KPEAE1@Z
 * Address: 0x14046B470
 */

bool __fastcall CCryptor::Encrypt(CCryptor *this, const char *pText, unsigned __int64 tLength, char *pCipherText, unsigned __int64 tCipherTextLength)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v8; // [sp+0h] [bp-38h]@1
  unsigned __int64 v9; // [sp+20h] [bp-18h]@6
  CCryptor *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v5 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( v10->m_pkParam )
  {
    v9 = tCipherTextLength;
    result = CCryptParam::Encrypt(v10->m_pkParam, pText, tLength, pCipherText, tCipherTextLength);
  }
  else
  {
    result = 0;
  }
  return result;
}
