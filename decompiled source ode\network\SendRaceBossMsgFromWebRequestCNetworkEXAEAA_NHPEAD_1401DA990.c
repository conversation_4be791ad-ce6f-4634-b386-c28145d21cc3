/*
 * Function: ?SendRaceBossMsgFromWebRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401DA990
 */

char __fastcall CNetworkEX::SendRaceBossMsgFromWebRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@4
  __int64 v7; // [sp+0h] [bp-198h]@1
  char *v8; // [sp+30h] [bp-168h]@4
  _qry_case_sendwebracebosssms v9; // [sp+50h] [bp-148h]@4
  unsigned __int64 v10; // [sp+180h] [bp-18h]@4

  v3 = &v7;
  for ( i = 100i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = (unsigned __int64)&v7 ^ _security_cookie;
  v8 = pBuf;
  v9.dwWebSendDBID = *(_DWORD *)pBuf;
  strcpy_0(v9.szCharacterName, pBuf + 4);
  strcpy_0(v9.wszMsg, v8 + 21);
  v5 = _qry_case_sendwebracebosssms::size(&v9);
  CMainThread::PushDQSData(&g_Main, 0, 0i64, 25, (char *)&v9, v5);
  return 1;
}
