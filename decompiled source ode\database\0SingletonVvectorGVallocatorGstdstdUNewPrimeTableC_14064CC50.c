/*
 * Function: ??0?$Singleton@V?$vector@GV?$allocator@G@std@@@std@@UNewPrimeTable@CryptoPP@@$0A@@CryptoPP@@QEAA@UNewPrimeTable@1@@Z
 * Address: 0x14064CC50
 */

__int64 __fastcall CryptoPP::Singleton<std::vector<unsigned short,std::allocator<unsigned short>>,CryptoPP::NewPrimeTable,0>::Singleton<std::vector<unsigned short,std::allocator<unsigned short>>,CryptoPP::NewPrimeTable,0>(__int64 a1)
{
  return a1;
}
