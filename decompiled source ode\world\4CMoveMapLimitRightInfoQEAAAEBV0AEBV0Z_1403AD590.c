/*
 * Function: ??4CMoveMapLimitRightInfo@@QEAAAEBV0@AEBV0@@Z
 * Address: 0x1403AD590
 */

CMoveMapLimitRightInfo *__fastcall CMoveMapLimitRightInfo::operator=(CMoveMapLimitRightInfo *this, CMoveMapLimitRightInfo *rhs)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@6
  __int64 v6; // [sp+0h] [bp-B8h]@1
  CMoveMapLimitRight *_Val; // [sp+28h] [bp-90h]@4
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v8; // [sp+38h] [bp-80h]@4
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > result; // [sp+48h] [bp-70h]@4
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > v10; // [sp+78h] [bp-40h]@4
  CMoveMapLimitRight *v11; // [sp+98h] [bp-20h]@6
  __int64 v12; // [sp+A0h] [bp-18h]@4
  CMoveMapLimitRightInfo *v13; // [sp+C0h] [bp+8h]@1
  CMoveMapLimitRightInfo *v14; // [sp+C8h] [bp+10h]@1

  v14 = rhs;
  v13 = this;
  v2 = &v6;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = -2i64;
  CMoveMapLimitRightInfo::CleanUp(v13);
  _Val = 0i64;
  v8 = (std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)v14;
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::end(&v14->m_vecRight, &result);
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::begin(v8, &v10);
  while ( std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator!=(
            &v10,
            &result) )
  {
    v11 = *std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator*(&v10);
    v4 = CMoveMapLimitRight::GetType(v11);
    _Val = CMoveMapLimitRight::Create(v4);
    std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::push_back(&v13->m_vecRight, &_Val);
    std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator++(&v10);
  }
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(&v10);
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(&result);
  return v13;
}
