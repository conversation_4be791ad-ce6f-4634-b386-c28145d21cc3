/*
 * Function: ?IntoMap@CPlayer@@QEAA_NE@Z
 * Address: 0x1400505A0
 */

char __fastcall CPlayer::IntoMap(CPlayer *this, char byMapInMode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  CGuildBattleController *v5; // rax@8
  _sec_info *v6; // rax@9
  int v7; // eax@12
  __int64 v8; // [sp+0h] [bp-38h]@1
  char v9; // [sp+20h] [bp-18h]@8
  unsigned int dwSecIndex; // [sp+24h] [bp-14h]@9
  CPlayer *pkPlayer; // [sp+40h] [bp+8h]@1
  char v12; // [sp+48h] [bp+10h]@1

  v12 = byMapInMode;
  pkPlayer = this;
  v2 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pkPlayer->m_pCurMap->m_pMapSet->m_nMapType != 1
    || _LAYER_SET::IsActiveLayer(&pkPlayer->m_pCurMap->m_ls[pkPlayer->m_wMapLayerIndex]) )
  {
    if ( v12 == 6 )
    {
      v5 = CGuildBattleController::Instance();
      v9 = CGuildBattleController::Start(v5, pkPlayer);
    }
    dwSecIndex = CGameObject::CalcSecIndex((CGameObject *)&pkPlayer->vfptr);
    v6 = CMapData::GetSecInfo(pkPlayer->m_pCurMap);
    if ( dwSecIndex < v6->m_nSecNum )
    {
      CMapData::EnterMap(pkPlayer->m_pCurMap, (CGameObject *)&pkPlayer->vfptr, dwSecIndex);
      CGameObject::SetCurSecNum((CGameObject *)&pkPlayer->vfptr, dwSecIndex);
      CPlayer::SendData_PartyMemberPos(pkPlayer);
      if ( pkPlayer->m_pCurMap == (CMapData *)*((_QWORD *)&g_TransportShip
                                              + 10162 * CPlayerDB::GetRaceCode(&pkPlayer->m_Param)
                                              + 2) )
      {
        v7 = CPlayerDB::GetRaceCode(&pkPlayer->m_Param);
        CTransportShip::EnterMember((CTransportShip *)&g_TransportShip + v7, pkPlayer);
      }
      CPlayer::SetStateFlag(pkPlayer);
      CPlayer::CheckPos_Region(pkPlayer);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
