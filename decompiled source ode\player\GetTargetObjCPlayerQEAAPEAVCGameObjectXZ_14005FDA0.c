/*
 * Function: ?GetTargetObj@CPlayer@@QEAAPEAVCGameObject@@XZ
 * Address: 0x14005FDA0
 */

CGameObject *__fastcall CPlayer::GetTargetObj(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CGameObject *result; // rax@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  CGameObject *v5; // [sp+20h] [bp-18h]@6
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_TargetObject.pObject )
  {
    v5 = v6->m_TargetObject.pObject;
    if ( v5->m_bLive )
    {
      if ( v6->m_TargetObject.byKind == v5->m_ObjID.m_byKind
        && v6->m_TargetObject.byID == v5->m_ObjID.m_byID
        && v6->m_TargetObject.dwSerial == v5->m_dwObjSerial )
      {
        if ( v6->m_TargetObject.pObject->m_pCurMap == v6->m_pCurMap )
        {
          result = v6->m_TargetObject.pObject;
        }
        else
        {
          CPlayer::__target::init(&v6->m_TargetObject);
          result = 0i64;
        }
      }
      else
      {
        CPlayer::__target::init(&v6->m_TargetObject);
        result = 0i64;
      }
    }
    else
    {
      CPlayer::__target::init(&v6->m_TargetObject);
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
