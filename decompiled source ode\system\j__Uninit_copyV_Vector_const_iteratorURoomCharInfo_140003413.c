/*
 * Function: j_??$_Uninit_copy@V?$_Vector_const_iterator@URoomCharInfo@@V?$allocator@URoomCharInfo@@@std@@@std@@PEAURoomCharInfo@@V?$allocator@URoomCharInfo@@@2@@std@@YAPEAURoomCharInfo@@V?$_Vector_const_iterator@URoomCharInfo@@V?$allocator@URoomCharInfo@@@std@@@0@0PEAU1@AEAV?$allocator@URoomCharInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140003413
 */

RoomCharInfo *__fastcall std::_Uninit_copy<std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo>>,RoomCharInfo *,std::allocator<RoomCharInfo>>(std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo> > *_First, std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo> > *_Last, RoomCharInfo *_Dest, std::allocator<RoomCharInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo>>,RoomCharInfo *,std::allocator<RoomCharInfo>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
