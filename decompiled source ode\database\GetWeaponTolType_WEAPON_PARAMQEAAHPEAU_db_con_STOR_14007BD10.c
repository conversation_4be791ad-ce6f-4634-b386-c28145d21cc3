/*
 * Function: ?GetWeaponTolType@_WEAPON_PARAM@@QEAAHPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x14007BD10
 */

signed __int64 __fastcall _WEAPON_PARAM::GetWeaponTolType(_WEAPON_PARAM *this, _STORAGE_LIST::_db_con *pItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  char v7; // [sp+24h] [bp-14h]@6
  _base_fld *v8; // [sp+28h] [bp-10h]@16
  _STORAGE_LIST::_db_con *v9; // [sp+48h] [bp+10h]@1

  v9 = pItem;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 7; ++j )
  {
    v7 = GetTalikFromSocket(v9->m_dwLv, j);
    if ( v7 == 15 )
      break;
    if ( v7 == 7 )
      return 0i64;
    if ( v7 == 8 )
      return 1i64;
    if ( v7 == 9 )
      return 2i64;
    if ( v7 == 10 )
      return 3i64;
  }
  v8 = CRecordData::GetRecord(_WEAPON_PARAM::s_pWeaponData, v9->m_wItemIndex);
  return *(_DWORD *)&v8[9].m_strCode[20];
}
