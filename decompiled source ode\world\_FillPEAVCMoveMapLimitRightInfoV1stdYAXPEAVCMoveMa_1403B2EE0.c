/*
 * Function: ??$_Fill@PEAVCMoveMapLimitRightInfo@@V1@@std@@YAXPEAVCMoveMapLimitRightInfo@@0AEBV1@@Z
 * Address: 0x1403B2EE0
 */

void __fastcall std::_Fill<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo>(CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, CMoveMapLimitRightInfo *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMoveMapLimitRightInfo *v6; // [sp+30h] [bp+8h]@1
  CMoveMapLimitRightInfo *v7; // [sp+38h] [bp+10h]@1
  CMoveMapLimitRightInfo *rhs; // [sp+40h] [bp+18h]@1

  rhs = _Val;
  v7 = _Last;
  v6 = _First;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  while ( v6 != v7 )
  {
    CMoveMapLimitRightInfo::operator=(v6, rhs);
    ++v6;
  }
}
