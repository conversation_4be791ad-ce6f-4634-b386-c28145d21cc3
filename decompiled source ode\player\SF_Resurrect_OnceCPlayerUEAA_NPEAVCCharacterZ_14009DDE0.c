/*
 * Function: ?SF_Resurrect_Once@CPlayer@@UEAA_NPEAVCCharacter@@@Z
 * Address: 0x14009DDE0
 */

char __fastcall CPlayer::SF_Resurrect_Once(CPlayer *this, CCharacter *pDstObj)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  CPlayer *v7; // [sp+48h] [bp+10h]@1

  v7 = (CPlayer *)pDstObj;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  if ( !pDstObj->m_ObjID.m_byID && !((int (__fastcall *)(CCharacter *))pDstObj->vfptr->GetHP)(pDstObj) )
    v6 = 1;
  if ( v6 )
  {
    CPlayer::Resurrect(v7);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
