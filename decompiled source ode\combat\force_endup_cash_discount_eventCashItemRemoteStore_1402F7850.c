/*
 * Function: ?force_endup_cash_discount_event@CashItemRemoteStore@@QEAAXXZ
 * Address: 0x1402F7850
 */

void __fastcall CashItemRemoteStore::force_endup_cash_discount_event(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CashItemRemoteStore *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v4->m_cde.m_ini.m_bUseCashDiscount )
  {
    CashItemRemoteStore::set_cde_status(v4, 5);
    if ( CashItemRemoteStore::is_cde_time(v4) )
    {
      CashItemRemoteStore::inform_cashdiscount_status_all(v4, 5, &v4->m_cde.m_ini);
    }
    else
    {
      v4->m_cde.m_ini.m_bUseCashDiscount = 0;
      CLogFile::Write(&v4->m_cde.m_cde_log, "A Event which was Registated is ended up by perforce");
    }
  }
}
