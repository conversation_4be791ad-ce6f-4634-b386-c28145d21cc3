/*
 * Function: ?Create@CNationSettingFactoryTW@@UEAAPEAVCNationSettingData@@HPEBD_N@Z
 * Address: 0x14022F700
 */

CNationSettingData *__fastcall CNationSettingFactoryTW::Create(CNationSettingFactoryTW *this, int iNationCode, const char *szNationCodeStr, bool bServiceMode)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingData *v6; // rax@5
  CNationSettingData *result; // rax@8
  __int64 v8; // [sp+0h] [bp-58h]@1
  CNationSettingData *pkData; // [sp+20h] [bp-38h]@7
  CNationSettingData *v10; // [sp+28h] [bp-30h]@7
  CNationSettingDataTW *v11; // [sp+30h] [bp-28h]@4
  __int64 v12; // [sp+38h] [bp-20h]@4
  CNationSettingData *v13; // [sp+40h] [bp-18h]@5
  CNationSettingFactoryTW *v14; // [sp+60h] [bp+8h]@1
  int v15; // [sp+68h] [bp+10h]@1
  char *_Source; // [sp+70h] [bp+18h]@1
  bool v17; // [sp+78h] [bp+20h]@1

  v17 = bServiceMode;
  _Source = (char *)szNationCodeStr;
  v15 = iNationCode;
  v14 = this;
  v4 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = -2i64;
  v11 = (CNationSettingDataTW *)operator new(0x1C0ui64);
  if ( v11 )
  {
    CNationSettingDataTW::CNationSettingDataTW(v11);
    v13 = v6;
  }
  else
  {
    v13 = 0i64;
  }
  v10 = v13;
  pkData = v13;
  if ( v13 )
  {
    pkData->m_bServiceMode = v17;
    pkData->m_iNationCode = v15;
    strcpy_s<3>((char (*)[3])pkData->m_szNationCodeStr, _Source);
    pkData->m_iANSICodePage = 950;
    if ( pkData->m_bServiceMode )
    {
      strcpy_s<16>((char (*)[16])pkData->m_szCashDBIP, "************");
      strcpy_s<64>((char (*)[64])pkData->m_szCashDBName, "RF_DB_CHG");
      strcpy_s<64>((char (*)[64])pkData->m_szCashDBID, "ccr");
      strcpy_s<64>((char (*)[64])pkData->m_szCashDBPW, "ji3g4zo4j4");
    }
    else
    {
      strcpy_s<16>((char (*)[16])pkData->m_szCashDBIP, "*************");
      strcpy_s<64>((char (*)[64])pkData->m_szCashDBName, "COIN_TEST");
      strcpy_s<64>((char (*)[64])pkData->m_szCashDBID, "coin_test");
      strcpy_s<64>((char (*)[64])pkData->m_szCashDBPW, "rftest");
    }
    strcpy_s<64>((char (*)[64])pkData->m_szWorldDBID, "rfworld");
    strcpy_s<64>((char (*)[64])pkData->m_szWorldDBPW, "e1eoaks*dnjfem");
    if ( CNationSettingFactory::RegistCheatTable((CNationSettingFactory *)&v14->vfptr, pkData) )
      result = pkData;
    else
      result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
