/*
 * Function: ?RequestCombineAcceptProcess@ItemCombineMgr@@QEAAEPEAU_combine_ex_item_accept_request_clzo@@PEAU_combine_ex_item_accept_result_zocl@@@Z
 * Address: 0x1402AD660
 */

char __fastcall ItemCombineMgr::RequestCombineAcceptProcess(ItemCombineMgr *this, _combine_ex_item_accept_request_clzo *pRecv, _combine_ex_item_accept_result_zocl *pSend)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-198h]@1
  char v7; // [sp+20h] [bp-178h]@4
  _ITEMCOMBINE_DB_BASE Dst; // [sp+40h] [bp-158h]@4
  ItemCombineMgr *v9; // [sp+1A0h] [bp+8h]@1
  _combine_ex_item_accept_request_clzo *pRecva; // [sp+1A8h] [bp+10h]@1
  _combine_ex_item_accept_result_zocl *pSenda; // [sp+1B0h] [bp+18h]@1

  pSenda = pSend;
  pRecva = pRecv;
  v9 = this;
  v3 = &v6;
  for ( i = 100i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = 0;
  _ITEMCOMBINE_DB_BASE::_ITEMCOMBINE_DB_BASE(&Dst);
  memcpy_0(&Dst, &v9->m_pMaster->m_Param.m_ItemCombineDB, 0x134ui64);
  ItemCombineMgr::ClearDB_CombineResult(v9);
  if ( _ITEMCOMBINE_DB_BASE::IsCombineData(&Dst) )
  {
    if ( Dst.m_dwCheckKey == pRecva->dwCheckKey && (unsigned __int8)Dst.m_byDlgType == pRecva->byDlgType )
    {
      if ( ItemCombineMgr::MakeNewItems(v9, &Dst, pRecva, pSenda) )
        v7 = 16;
    }
    else
    {
      v7 = 15;
    }
  }
  else
  {
    v7 = 14;
  }
  pSenda->byErrCode = v7;
  return v7;
}
