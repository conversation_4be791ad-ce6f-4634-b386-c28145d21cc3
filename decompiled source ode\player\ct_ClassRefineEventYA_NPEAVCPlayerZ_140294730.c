/*
 * Function: ?ct_ClassRefineEvent@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140294730
 */

char __fastcall ct_ClassRefineEvent(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v4; // [sp+0h] [bp-68h]@1
  char v5; // [sp+20h] [bp-48h]@7
  char v6; // [sp+38h] [bp-30h]@19
  int v7; // [sp+3Ch] [bp-2Ch]@9
  int v8; // [sp+40h] [bp-28h]@11
  int v9; // [sp+44h] [bp-24h]@13
  unsigned __int8 v10; // [sp+54h] [bp-14h]@17
  CPlayer *v11; // [sp+70h] [bp+8h]@1

  v11 = pOne;
  v1 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v11 && v11->m_bOper )
  {
    v5 = 0;
    if ( !strncmp(s_pwszDstCheat[0], "true", 4ui64) )
      v5 = 1;
    v7 = atoi(s_pwszDstCheat[1]);
    if ( v7 <= 256 )
    {
      v8 = atoi(s_pwszDstCheat[2]);
      if ( v8 >= GetLocalDate() )
      {
        v9 = atoi(s_pwszDstCheat[3]);
        if ( v9 > GetLocalDate() )
        {
          if ( v8 <= (unsigned int)v9 )
          {
            v10 = 0;
            if ( !strncmp(s_pwszDstCheat[4], "true", 4ui64) )
              v10 = 1;
            (*(void (__fastcall **)(_QWORD *, char *, signed __int64, _QWORD))(*qword_1799C9AF0 + 16i64))(
              qword_1799C9AF0,
              &v6,
              16i64,
              v10);
            result = 1;
          }
          else
          {
            result = 0;
          }
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
