/*
 * Function: ??0?$CHashMapPtrPool@HVCNationSettingFactory@@@@QEAA@XZ
 * Address: 0x1402299F0
 */

void __fastcall CHashMapPtrPool<int,CNationSettingFactory>::CHashMapPtrPool<int,CNationSettingFactory>(CHashMapPtrPool<int,CNationSettingFactory> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CHashMapPtrPool<int,CNationSettingFactory> *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_bCleanUp = 1;
  stdext::hash_map<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::hash_map<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>>(&v4->m_mapData);
}
