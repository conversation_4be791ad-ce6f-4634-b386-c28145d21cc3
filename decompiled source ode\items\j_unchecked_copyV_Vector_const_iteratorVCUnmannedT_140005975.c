/*
 * Function: j_??$unchecked_copy@V?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@PEAVCUnmannedTraderItemCodeInfo@@@stdext@@YAPEAVCUnmannedTraderItemCodeInfo@@V?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@0PEAV1@@Z
 * Address: 0x140005975
 */

CUnmannedTraderItemCodeInfo *__fastcall stdext::unchecked_copy<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,CUnmannedTraderItemCodeInfo *>(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_First, std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_Last, CUnmannedTraderItemCodeInfo *_Dest)
{
  return stdext::unchecked_copy<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,CUnmannedTraderItemCodeInfo *>(
           _First,
           _Last,
           _Dest);
}
