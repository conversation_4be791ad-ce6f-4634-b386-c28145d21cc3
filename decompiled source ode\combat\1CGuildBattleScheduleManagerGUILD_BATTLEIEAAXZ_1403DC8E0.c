/*
 * Function: ??1CGuildBattleScheduleManager@GUILD_BATTLE@@IEAA@XZ
 * Address: 0x1403DC8E0
 */

void __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::~CGuildBattleScheduleManager(GUILD_BATTLE::CGuildBattleScheduleManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@8
  __int64 v4; // [sp+0h] [bp-58h]@1
  void *v5; // [sp+20h] [bp-38h]@5
  CMyTimer *v6; // [sp+28h] [bp-30h]@7
  CMyTimer *v7; // [sp+30h] [bp-28h]@7
  __int64 v8; // [sp+38h] [bp-20h]@4
  __int64 v9; // [sp+40h] [bp-18h]@8
  GUILD_BATTLE::CGuildBattleScheduleManager *v10; // [sp+60h] [bp+8h]@1

  v10 = this;
  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = -2i64;
  if ( v10->m_pkOldDayTime )
  {
    v5 = v10->m_pkOldDayTime;
    operator delete(v5);
    v10->m_pkOldDayTime = 0i64;
  }
  if ( v10->m_pkTimer )
  {
    v7 = v10->m_pkTimer;
    v6 = v7;
    if ( v7 )
    {
      LODWORD(v3) = ((int (__fastcall *)(CMyTimer *, signed __int64))v6->vfptr->__vecDelDtor)(v6, 1i64);
      v9 = v3;
    }
    else
    {
      v9 = 0i64;
    }
    v10->m_pkTimer = 0i64;
  }
  `eh vector destructor iterator'(
    v10->m_kSchdule,
    0x18ui64,
    2,
    (void (__cdecl *)(void *))GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::~CGuildBattleReservedScheduleMapGroup);
}
