/*
 * Function: ??D?$_Vector_const_iterator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEBAAEBU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@XZ
 * Address: 0x1405A0B00
 */

__int64 __fastcall std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator*(__int64 a1)
{
  return *(_QWORD *)(a1 + 16);
}
