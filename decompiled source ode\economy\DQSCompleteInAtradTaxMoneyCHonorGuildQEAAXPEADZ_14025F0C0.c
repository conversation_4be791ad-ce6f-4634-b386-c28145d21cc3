/*
 * Function: ?DQSCompleteInAtradTaxMoney@CHonorGuild@@QEAAXPEAD@Z
 * Address: 0x14025F0C0
 */

void __fastcall CHonorGuild::DQSCompleteInAtradTaxMoney(CHonorGuild *this, char *pdata)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CAsyncLogger *v4; // rax@5
  long double v5; // xmm0_8@6
  __int64 v6; // [sp+0h] [bp-58h]@1
  _DWORD bInPut[2]; // [sp+20h] [bp-38h]@5
  char *pbyDate; // [sp+28h] [bp-30h]@5
  long double v9; // [sp+30h] [bp-28h]@5
  char *pszFileName; // [sp+38h] [bp-20h]@6
  char *v11; // [sp+40h] [bp-18h]@4
  CGuild *v12; // [sp+48h] [bp-10h]@4

  v2 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = pdata;
  v12 = GetGuildDataFromSerial(g_Guild, 500, *((_DWORD *)pdata + 1));
  if ( v12 )
  {
    v12->m_dTotalDalant = *((long double *)v11 + 2);
    CGuild::MakeDownMemberPacket(v12);
    v12->m_byMoneyOutputKind = 6;
    v5 = (double)*((signed int *)v11 + 3);
    pbyDate = v11 + 32;
    LOBYTE(bInPut[0]) = 1;
    CGuild::SendMsg_IOMoney(v12, *((_DWORD *)v11 + 2), v5, 0.0, 1, v11 + 32);
    pszFileName = v12->m_szHistoryFileName;
    v9 = *((double *)v11 + 3);
    pbyDate = (char *)*((_QWORD *)v11 + 2);
    bInPut[0] = 0;
    CMgrGuildHistory::push_money(
      &CGuild::s_MgrHistory,
      "Auto Trade Tax",
      *((_DWORD *)v11 + 2),
      *((_DWORD *)v11 + 3),
      0,
      *(long double *)&pbyDate,
      v9,
      v12->m_szHistoryFileName);
  }
  else
  {
    v4 = CAsyncLogger::Instance();
    v9 = *((double *)v11 + 2);
    LODWORD(pbyDate) = *((_DWORD *)v11 + 3);
    bInPut[0] = *((_DWORD *)v11 + 2);
    CAsyncLogger::FormatLog(
      v4,
      8,
      "Faild CHonorGuild::DQSCompleteInAtradTaxMoney(GuildSerial:%d,seller:%d,in:@%d,total:@%.0f)",
      *((_DWORD *)v11 + 1));
  }
}
