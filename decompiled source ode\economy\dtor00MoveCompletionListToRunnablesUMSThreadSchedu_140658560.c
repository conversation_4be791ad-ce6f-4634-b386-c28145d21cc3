/*
 * Function: ?dtor$0@?0??MoveCompletionListToRunnables@UMSThreadScheduler@details@Concurrency@@QEAA_NVlocation@3@@Z@4HA_2
 * Address: 0x140658560
 */

int __fastcall `Concurrency::details::UMSThreadScheduler::MoveCompletionListToRunnables'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  __int64 v2; // rcx@1

  v2 = *(_QWORD *)(a2 + 152);
  return std::_Deque_iterator<unsigned __int64,std::allocator<unsigned __int64>,0>::~_Deque_iterator<unsigned __int64,std::allocator<unsigned __int64>,0>();
}
