/*
 * Function: ?CreateFieldObject@CNormalGuildBattleField@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403EC630
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleField::CreateFieldObject(GUILD_BATTLE::CNormalGuildBattleField *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  GUILD_BATTLE::CGuildBattleLogger *v4; // rax@9
  GUILD_BATTLE::CGuildBattleLogger *v5; // rax@14
  GUILD_BATTLE::CGuildBattleLogger *v6; // rax@19
  __int64 v7; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@6
  int v9; // [sp+24h] [bp-14h]@9
  int v10; // [sp+28h] [bp-10h]@14
  int v11; // [sp+2Ch] [bp-Ch]@19
  GUILD_BATTLE::CNormalGuildBattleField *v12; // [sp+40h] [bp+8h]@1

  v12 = this;
  v1 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v12->m_bInit )
  {
    for ( j = 0; j < v12->m_ui1PGoalPosCnt; ++j )
    {
      if ( !CCircleZone::Create(&v12->m_pk1PGoalZone[j], v12->m_pkMap, 0) )
      {
        v9 = GUILD_BATTLE::CNormalGuildBattleField::GetMapCode(v12);
        v4 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v4,
          "CNormalGuildBattleField::CreateFieldObject()m_pk1PGoalZone[%u].Create( %d, 0 ) Fail!",
          j,
          (unsigned int)v9);
        return 0;
      }
    }
    for ( j = 0; j < v12->m_ui2PGoalPosCnt; ++j )
    {
      if ( !CCircleZone::Create(&v12->m_pk2PGoalZone[j], v12->m_pkMap, 1) )
      {
        v10 = GUILD_BATTLE::CNormalGuildBattleField::GetMapCode(v12);
        v5 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v5,
          "CNormalGuildBattleField::CreateFieldObject()m_pk2PGoalZone[%u].Create( %d, 1 ) Fail!",
          j,
          (unsigned int)v10);
        return 0;
      }
    }
    for ( j = 0; j < v12->m_uiRegenPosCnt; ++j )
    {
      if ( !CGravityStoneRegener::Create(&v12->m_pkRegenPos[j], v12->m_pkMap) )
      {
        v11 = GUILD_BATTLE::CNormalGuildBattleField::GetMapCode(v12);
        v6 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v6,
          "CNormalGuildBattleField::CreateFieldObject()m_pkRegenPos[%u].Create( %d ) Fail!",
          j,
          (unsigned int)v11);
        return 0;
      }
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
