/*
 * Function: _stdext::unchecked_uninitialized_copy_std::_Vector_const_iterator_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo____CUnmannedTraderItemCodeInfo_____ptr64_std::allocator_CUnmannedTraderItemCodeInfo____::_1_::dtor$1
 * Address: 0x14037C520
 */

void __fastcall stdext::unchecked_uninitialized_copy_std::_Vector_const_iterator_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo____CUnmannedTraderItemCodeInfo_____ptr64_std::allocator_CUnmannedTraderItemCodeInfo____::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(*(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > **)(a2 + 176));
}
