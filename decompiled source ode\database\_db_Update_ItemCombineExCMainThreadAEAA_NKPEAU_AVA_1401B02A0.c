/*
 * Function: ?_db_Update_ItemCombineEx@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD@Z
 * Address: 0x1401B02A0
 */

char __fastcall CMainThread::_db_Update_ItemCombineEx(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *pSzQuery)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v7; // ecx@6
  int v8; // edx@6
  unsigned int v9; // edi@6
  int v10; // eax@8
  size_t v11; // rax@10
  __int64 v13; // [sp+0h] [bp-108h]@1
  unsigned int v14; // [sp+20h] [bp-E8h]@6
  unsigned int v15; // [sp+28h] [bp-E0h]@6
  unsigned int v16; // [sp+30h] [bp-D8h]@6
  unsigned int v17; // [sp+38h] [bp-D0h]@6
  char Dest; // [sp+50h] [bp-B8h]@5
  char v19; // [sp+51h] [bp-B7h]@5
  size_t Size; // [sp+D4h] [bp-34h]@5
  unsigned int j; // [sp+DCh] [bp-2Ch]@6
  __int64 v22; // [sp+E8h] [bp-20h]@8
  __int64 v23; // [sp+F0h] [bp-18h]@8
  unsigned __int64 v24; // [sp+F8h] [bp-10h]@4
  unsigned int v25; // [sp+118h] [bp+10h]@1
  _AVATOR_DATA *v26; // [sp+120h] [bp+18h]@1
  _AVATOR_DATA *v27; // [sp+128h] [bp+20h]@1

  v27 = pOldData;
  v26 = pNewData;
  v25 = dwSerial;
  v5 = &v13;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v24 = (unsigned __int64)&v13 ^ _security_cookie;
  if ( pNewData->dbItemCombineEx.m_bIsResult )
  {
    Dest = 0;
    memset(&v19, 0, 0x7Fui64);
    sprintf(pSzQuery, "UPDATE tbl_itemcombine_ex_result SET ");
    LODWORD(Size) = strlen_0(pSzQuery);
    if ( v26->dbItemCombineEx.m_dwCheckKey != v27->dbItemCombineEx.m_dwCheckKey )
    {
      v7 = v26->dbItemCombineEx.m_bySelectItemCount;
      v8 = v26->dbItemCombineEx.m_byDlgType;
      v9 = v26->dbItemCombineEx.m_bIsResult;
      v17 = v26->dbItemCombineEx.m_byItemListNum;
      v16 = v7;
      v15 = v8;
      v14 = v26->dbItemCombineEx.m_dwCheckKey;
      sprintf(
        &Dest,
        "IsResult=%d,Dalant=%d,CheckKey=%d,DlgType=%d,ItemSelectCount=%d,ItemListCount=%d,",
        v9,
        v26->dbItemCombineEx.m_dwDalant);
      strcat_0(pSzQuery, &Dest);
      HIDWORD(Size) = v26->dbItemCombineEx.m_byItemListNum;
      for ( j = 0; (signed int)j < SHIDWORD(Size); ++j )
      {
        v22 = 12i64 * (signed int)j;
        v23 = 12i64 * (signed int)j;
        v10 = _COMBINEKEY::CovDBKey(&v26->dbItemCombineEx.m_List[j].Key);
        v17 = v26->dbItemCombineEx.m_List[(unsigned __int64)v22 / 0xC].dwUpt;
        v16 = j;
        v15 = v26->dbItemCombineEx.m_List[(unsigned __int64)v23 / 0xC].dwDur;
        v14 = j;
        sprintf(&Dest, "K%d=%d,D%d=%d,U%d=%d,", j, (unsigned int)v10);
        strcat_0(pSzQuery, &Dest);
      }
      sprintf(&Dest, "resulteffecttype=%d,", v26->dbItemCombineEx.m_dwResultEffectType);
      strcat_0(pSzQuery, &Dest);
      sprintf(&Dest, "resulteffectmsgcode=%d,", v26->dbItemCombineEx.m_dwResultEffectMsgCode);
      strcat_0(pSzQuery, &Dest);
    }
    v11 = strlen_0(pSzQuery);
    if ( v11 <= (unsigned int)Size )
    {
      memset_0(pSzQuery, 0, (unsigned int)Size);
    }
    else
    {
      sprintf(&Dest, "WHERE Serial=%d", v25);
      pSzQuery[strlen_0(pSzQuery) - 1] = 32;
      strcat_0(pSzQuery, &Dest);
    }
  }
  else if ( pNewData->dbItemCombineEx.m_bIsResult != pOldData->dbItemCombineEx.m_bIsResult )
  {
    sprintf(pSzQuery, "{ CALL pUpdate_CombineEx_Result_Pop( %d ) }", dwSerial);
  }
  return 1;
}
