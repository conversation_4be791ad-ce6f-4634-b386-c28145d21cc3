/*
 * Function: ?Load@CItemStoreManager@@QEAA_NXZ
 * Address: 0x140349120
 */

char __fastcall CItemStoreManager::Load(CItemStoreManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-58h]@1
  unsigned int dwStoreNum; // [sp+20h] [bp-38h]@4
  unsigned int j; // [sp+24h] [bp-34h]@4
  int k; // [sp+28h] [bp-30h]@6
  unsigned int pdwUsedNum; // [sp+34h] [bp-24h]@12
  CItemStoreManager *v9; // [sp+60h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  dwStoreNum = 0;
  for ( j = 0; (signed int)j < v9->m_nMapItemStoreListNum; ++j )
  {
    for ( k = 0; k < v9->m_MapItemStoreList[j].m_nItemStoreNum; ++k )
    {
      if ( v9->m_MapItemStoreList[j].m_ItemStore[k].m_nLimitStorageItemNum > 0 )
        ++dwStoreNum;
    }
  }
  pdwUsedNum = 0;
  if ( !CItemStoreManager::SelectUsedRecordNum(v9, &pdwUsedNum) )
    return 0;
  dwStoreNum += 90;
  if ( dwStoreNum )
  {
    if ( dwStoreNum < pdwUsedNum )
      dwStoreNum = pdwUsedNum;
    if ( !CItemStoreManager::SelectTotalRecordNum(v9, &pdwUsedNum) )
      return 0;
    if ( dwStoreNum > pdwUsedNum && !CItemStoreManager::InsertNotEnoughLimitItemRecord(v9, dwStoreNum - pdwUsedNum) )
      return 0;
    if ( !_qry_case_all_store_limit_item::Init(&v9->m_Sheet, dwStoreNum) )
      return 0;
    if ( !CItemStoreManager::SelectStoreLimitItem(v9) )
      return 0;
    for ( j = 0; j < v9->m_Sheet.dwCount; ++j )
      CItemStoreManager::SetStoreLimitItemData(v9, &v9->m_Sheet.pStoreList[j]);
    CItemStoreManager::SetEnforceInitNormalStore(v9);
  }
  CItemStoreManager::SetUpdateDBDataDoNotCheck(v9);
  _qry_case_all_store_limit_item::DataInit(&v9->m_Sheet);
  return 1;
}
