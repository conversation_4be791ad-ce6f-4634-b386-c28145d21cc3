/*
 * Function: ??1?$_Ranit@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@_JPEBU12@AEBU12@@std@@QEAA@XZ
 * Address: 0x14058A6B0
 */

void __fastcall std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,__int64,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const &>::~_Ranit<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,__int64,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const &>(std::_Iterator_base *a1)
{
  std::_Iterator_base::~_Iterator_base(a1);
}
