/*
 * Function: ?_Ufill@?$vector@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@IEAAPEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@PEAU34@_KAEBU34@@Z
 * Address: 0x140594600
 */

signed __int64 __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Ufill(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  __int64 v5; // [sp+38h] [bp+10h]@1
  __int64 v6; // [sp+40h] [bp+18h]@1

  v6 = a3;
  v5 = a2;
  stdext::unchecked_uninitialized_fill_n<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> *,unsigned __int64,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>(
    a2,
    a3,
    a4,
    a1 + 8);
  return 96 * v6 + v5;
}
