/*
 * Function: ??1CMapData@@UEAA@XZ
 * Address: 0x140180590
 */

void __fastcall CMapData::~CMapData(CMapData *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-A8h]@1
  void *v4; // [sp+20h] [bp-88h]@5
  void *v5; // [sp+28h] [bp-80h]@7
  void *v6; // [sp+30h] [bp-78h]@9
  void *v7; // [sp+38h] [bp-70h]@11
  void *v8; // [sp+40h] [bp-68h]@13
  void *v9; // [sp+48h] [bp-60h]@15
  _LAYER_SET *v10; // [sp+50h] [bp-58h]@17
  _LAYER_SET *v11; // [sp+58h] [bp-50h]@17
  _MULTI_BLOCK *v12; // [sp+60h] [bp-48h]@22
  _MULTI_BLOCK *v13; // [sp+68h] [bp-40h]@22
  CExtDummy *v14; // [sp+70h] [bp-38h]@27
  CExtDummy *v15; // [sp+78h] [bp-30h]@27
  __int64 v16; // [sp+80h] [bp-28h]@4
  void *v17; // [sp+88h] [bp-20h]@18
  void *v18; // [sp+90h] [bp-18h]@23
  void *v19; // [sp+98h] [bp-10h]@28
  CMapData *v20; // [sp+B0h] [bp+8h]@1

  v20 = this;
  v1 = &v3;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v16 = -2i64;
  v20->vfptr = (CMapDataVtbl *)&CMapData::`vftable';
  if ( v20->m_pMonBlock )
  {
    v4 = v20->m_pMonBlock;
    operator delete[](v4);
    v20->m_pMonBlock = 0i64;
  }
  if ( v20->m_pPortal )
  {
    v5 = v20->m_pPortal;
    operator delete(v5);
    v20->m_pPortal = 0i64;
  }
  if ( v20->m_pItemStoreDummy )
  {
    v6 = v20->m_pItemStoreDummy;
    operator delete[](v6);
    v20->m_pItemStoreDummy = 0i64;
  }
  if ( v20->m_pStartDummy )
  {
    v7 = v20->m_pStartDummy;
    operator delete[](v7);
    v20->m_pStartDummy = 0i64;
  }
  if ( v20->m_pBindDummy )
  {
    v8 = v20->m_pBindDummy;
    operator delete[](v8);
    v20->m_pBindDummy = 0i64;
  }
  if ( v20->m_pResDummy )
  {
    v9 = v20->m_pResDummy;
    operator delete[](v9);
    v20->m_pResDummy = 0i64;
  }
  if ( v20->m_ls )
  {
    v11 = v20->m_ls;
    v10 = v11;
    if ( v11 )
      v17 = _LAYER_SET::`vector deleting destructor'(v10, 3u);
    else
      v17 = 0i64;
    v20->m_ls = 0i64;
  }
  if ( v20->m_mb )
  {
    v13 = v20->m_mb;
    v12 = v13;
    if ( v13 )
      v18 = _MULTI_BLOCK::`vector deleting destructor'(v12, 3u);
    else
      v18 = 0i64;
    v20->m_mb = 0i64;
  }
  if ( v20->m_pExtDummy_Town )
  {
    CExtDummy::ReleaseExtDummy(v20->m_pExtDummy_Town);
    v15 = v20->m_pExtDummy_Town;
    v14 = v15;
    if ( v15 )
      v19 = CExtDummy::`scalar deleting destructor'(v14, 1u);
    else
      v19 = 0i64;
    v20->m_pExtDummy_Town = 0i64;
  }
  CExtDummy::ReleaseExtDummy(&v20->m_Dummy);
  CLevel::ReleaseLevel(&v20->m_Level);
  CMyTimer::~CMyTimer(&v20->m_tmrMineGradeReSet);
  CDummyPosTable::~CDummyPosTable(&v20->m_tbQuestDumPos);
  CDummyPosTable::~CDummyPosTable(&v20->m_tbResDumPosLow);
  CDummyPosTable::~CDummyPosTable(&v20->m_tbResDumPosMiddle);
  CDummyPosTable::~CDummyPosTable(&v20->m_tbResDumPosHigh);
  CDummyPosTable::~CDummyPosTable(&v20->m_tbBindDumPos);
  CDummyPosTable::~CDummyPosTable(&v20->m_tbStartDumPos);
  CDummyPosTable::~CDummyPosTable(&v20->m_tbStoreDumPos);
  CDummyPosTable::~CDummyPosTable(&v20->m_tbPortalDumPos);
  CDummyPosTable::~CDummyPosTable(&v20->m_tbMonDumPos);
  CRecordData::~CRecordData(&v20->m_tbPortal);
  CRecordData::~CRecordData(&v20->m_tbMonBlk);
  CDummyPosTable::~CDummyPosTable(&v20->m_tbSafeDumPos);
  CExtDummy::~CExtDummy(&v20->m_Dummy);
  CLevel::~CLevel(&v20->m_Level);
}
