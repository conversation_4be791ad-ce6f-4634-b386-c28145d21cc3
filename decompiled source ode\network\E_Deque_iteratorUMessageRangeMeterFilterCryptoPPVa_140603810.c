/*
 * Function: ??E?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@QEAA?AV01@H@Z
 * Address: 0x140603810
 */

__int64 __fastcall std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator++(__int64 a1, __int64 a2)
{
  char v3; // [sp+20h] [bp-38h]@1
  int v4; // [sp+40h] [bp-18h]@1
  __int64 v5; // [sp+48h] [bp-10h]@1
  __int64 v6; // [sp+60h] [bp+8h]@1
  __int64 v7; // [sp+68h] [bp+10h]@1

  v7 = a2;
  v6 = a1;
  v5 = -2i64;
  v4 = 0;
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)&v3);
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator++(v6);
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>(v7);
  v4 |= 1u;
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return v7;
}
