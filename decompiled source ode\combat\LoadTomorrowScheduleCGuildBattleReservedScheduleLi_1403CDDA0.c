/*
 * Function: ?LoadTomorrowSchedule@CGuildBattleReservedScheduleListManager@GUILD_BATTLE@@AEAA_NXZ
 * Address: 0x1403CDDA0
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleListManager::LoadTomorrowSchedule(GUILD_BATTLE::CGuildBattleReservedScheduleListManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  GUILD_BATTLE::CGuildBattleLogger *v4; // rax@9
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int uiMapID; // [sp+20h] [bp-18h]@6
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7->m_uiMapCnt )
  {
    for ( uiMapID = 0; uiMapID < v7->m_uiMapCnt; ++uiMapID )
    {
      if ( !GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateTomorrowSchedule(v7, uiMapID) )
      {
        v4 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v4,
          "CGuildBattleReservedScheduleListManager::LoadTodaySchedule() : UpdateTomorrowSchedule( %u ) Fail!",
          uiMapID);
        return 0;
      }
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
