/*
 * Function: ?mgr_goto_store@CPlayer@@QEAA_NHPEAD@Z
 * Address: 0x1400BA860
 */

char __fastcall CPlayer::mgr_goto_store(CPlayer *this, int nRaceCode, char *pszNPCName)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CItemStoreManager *v6; // rax@11
  size_t v7; // rax@15
  char v8; // al@20
  __int64 v9; // [sp+0h] [bp-68h]@1
  CMapData *pIntoMap; // [sp+30h] [bp-38h]@9
  float *v11; // [sp+38h] [bp-30h]@11
  CMapItemStoreList *v12; // [sp+40h] [bp-28h]@11
  int j; // [sp+48h] [bp-20h]@13
  bool *v14; // [sp+50h] [bp-18h]@15
  int nSerial; // [sp+58h] [bp-10h]@11
  CPlayer *v16; // [sp+70h] [bp+8h]@1
  int v17; // [sp+78h] [bp+10h]@1
  const char *Str; // [sp+80h] [bp+18h]@1

  Str = pszNPCName;
  v17 = nRaceCode;
  v16 = this;
  v3 = &v9;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v16->m_pmTrd.bDTradeMode )
  {
    result = 0;
  }
  else if ( CGameObject::GetCurSecNum((CGameObject *)&v16->vfptr) == -1 || v16->m_bMapLoading )
  {
    result = 0;
  }
  else
  {
    pIntoMap = CMapOperation::GetStartMap(&g_MapOper, v17);
    if ( pIntoMap )
    {
      v11 = 0i64;
      nSerial = (unsigned __int8)CMapData::GetMapCode(pIntoMap);
      v6 = CItemStoreManager::Instance();
      v12 = CItemStoreManager::GetMapItemStoreListBySerial(v6, nSerial);
      if ( v12 )
      {
        for ( j = 0; j < v12->m_nItemStoreNum; ++j )
        {
          v14 = &v12->m_ItemStore[j].m_bLive;
          v7 = strlen_0(Str);
          if ( !strncmp((const char *)(*((_QWORD *)v14 + 4) + 196i64), Str, v7) )
          {
            v11 = (float *)(*(_QWORD *)(*((_QWORD *)v14 + 3) + 16i64) + 128i64);
            break;
          }
        }
        if ( v11 )
        {
          CPlayer::OutOfMap(v16, pIntoMap, 0, 4, v11);
          v8 = CPlayerDB::GetMapCode(&v16->m_Param);
          CPlayer::SendMsg_GotoRecallResult(v16, 0, v8, v11, 4);
          result = 1;
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  return result;
}
