/*
 * Function: ?set_cde_status@CashItemRemoteStore@@QEAAXE@Z
 * Address: 0x1402F72D0
 */

void __fastcall CashItemRemoteStore::set_cde_status(CashItemRemoteStore *this, char byStatus)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // ecx@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  CLogFile *v6; // [sp+20h] [bp-18h]@4
  CashItemRemoteStore *v7; // [sp+40h] [bp+8h]@1
  char v8; // [sp+48h] [bp+10h]@1

  v8 = byStatus;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = v7->m_cde.m_cde_status;
  v6 = &v7->m_cde.m_cde_log;
  CLogFile::Write(&v7->m_cde.m_cde_log, "Convert Status : %d -> %d", v4, (unsigned __int8)byStatus);
  if ( v8 == 7 )
    CLogFile::Write(&v7->m_cde.m_cde_log, "[Discount Event Expired]");
  v7->m_cde.m_cde_status = v8;
}
