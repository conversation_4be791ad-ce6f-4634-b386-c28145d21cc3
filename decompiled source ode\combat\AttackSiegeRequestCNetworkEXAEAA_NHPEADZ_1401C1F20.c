/*
 * Function: ?AttackSiegeRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C1F20
 */

char __fastcall CNetworkEX::AttackSiegeRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@9
  __int64 v7; // [sp+0h] [bp-88h]@1
  unsigned __int16 wBulletSerial; // [sp+20h] [bp-68h]@12
  unsigned __int16 wEffBtSerial; // [sp+28h] [bp-60h]@12
  char *v10; // [sp+30h] [bp-58h]@4
  CPlayer *v11; // [sp+38h] [bp-50h]@4
  CCharacter *pDst; // [sp+40h] [bp-48h]@12
  float pfAttackPos; // [sp+58h] [bp-30h]@12
  float v14; // [sp+5Ch] [bp-2Ch]@12
  int v15; // [sp+60h] [bp-28h]@12
  CNetworkEX *v16; // [sp+90h] [bp+8h]@1

  v16 = this;
  v3 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = pBuf;
  v11 = &g_Player + n;
  if ( !v11->m_bOper || v11->m_pmTrd.bDTradeMode || v11->m_bCorpse )
  {
    result = 1;
  }
  else if ( (signed int)(unsigned __int8)v10[7] < 5 )
  {
    if ( CPlayer::IsActingSiegeMode(v11) )
    {
      result = 1;
    }
    else
    {
      pDst = (CCharacter *)CMainThread::GetObjectA(&g_Main, 0, (unsigned __int8)*v10, *(_WORD *)(v10 + 1));
      pfAttackPos = (float)*(_WORD *)(v10 + 3);
      v14 = (float)*(_WORD *)(v10 + 5);
      v15 = 0;
      wEffBtSerial = *((_WORD *)v10 + 5);
      wBulletSerial = *((_WORD *)v10 + 4);
      CPlayer::pc_PlayAttack_Siege(v11, pDst, &pfAttackPos, v10[7], wBulletSerial, wEffBtSerial);
      result = 1;
    }
  }
  else
  {
    v6 = CPlayerDB::GetCharNameA(&v11->m_Param);
    CLogFile::Write(&v16->m_LogFile, "odd.. %s: AttackSiegeRequest()..  if(pRecv->byAttPart >= base_fix_num)", v6);
    result = 0;
  }
  return result;
}
