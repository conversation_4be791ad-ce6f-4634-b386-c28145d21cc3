/*
 * Function: ?SendResponseAcceptResult@GMCallMgr@@IEAAXPEAVCPlayer@@0H@Z
 * Address: 0x1402AAE40
 */

void __fastcall GMCallMgr::SendResponseAcceptResult(GMCallMgr *this, CPlayer *pOneGM, CPlayer *pOneUser, int nErrorCode)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4

  v4 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  *(_DWORD *)szMsg = nErrorCode;
  pbyType = 55;
  v9 = 6;
  CNetProcess::LoadSendMsg(unk_1414F2088, pOneGM->m_ObjID.m_wIndex, &pbyType, szMsg, 4u);
}
