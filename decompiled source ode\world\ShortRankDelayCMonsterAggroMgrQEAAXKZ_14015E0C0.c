/*
 * Function: ?ShortRankDelay@CMonsterAggroMgr@@QEAAXK@Z
 * Address: 0x14015E0C0
 */

void __fastcall CMonsterAggroMgr::ShortRankDelay(CMonsterAggroMgr *this, unsigned int dwDelayTime)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  CMonsterAggroMgr *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5->m_dwShortRankLastTime = dwDelayTime + GetLoopTime();
}
