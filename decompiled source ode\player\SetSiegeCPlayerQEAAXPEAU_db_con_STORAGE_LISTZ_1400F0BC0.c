/*
 * Function: ?SetSiege@CPlayer@@QEAAXPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1400F0BC0
 */

void __fastcall CPlayer::SetSiege(CPlayer *this, _STORAGE_LIST::_db_con *pSiegeItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPlayer::CashChangeStateFlag *v4; // rax@6
  CPlayer::CashChangeStateFlag *v5; // rax@9
  __int64 v6; // [sp+0h] [bp-38h]@1
  CPlayer::CashChangeStateFlag v7; // [sp+20h] [bp-18h]@6
  CPlayer::CashChangeStateFlag v8; // [sp+24h] [bp-14h]@9
  CPlayer *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pSiegeItem )
  {
    if ( v9->m_pSiegeItem )
      return;
    v9->m_pSiegeItem = pSiegeItem;
    _STORAGE_LIST::_storage_con::lock((_STORAGE_LIST::_storage_con *)&v9->m_pSiegeItem->m_bLoad, 1);
    CPlayer::CashChangeStateFlag::CashChangeStateFlag(&v7, 0);
    CPlayer::UpdateVisualVer(v9, (CPlayer::CashChangeStateFlag)v4->0);
    CPlayer::apply_case_equip_std_effect(v9, v9->m_pSiegeItem, 1);
    CPlayer::SendMsg_TransformSiegeModeResult(v9, 0);
  }
  else
  {
    if ( !v9->m_pSiegeItem )
      return;
    CPlayer::apply_case_equip_std_effect(v9, v9->m_pSiegeItem, 0);
    _STORAGE_LIST::_storage_con::lock((_STORAGE_LIST::_storage_con *)&v9->m_pSiegeItem->m_bLoad, 0);
    v9->m_pSiegeItem = 0i64;
    CPlayer::CashChangeStateFlag::CashChangeStateFlag(&v8, 0);
    CPlayer::UpdateVisualVer(v9, (CPlayer::CashChangeStateFlag)v5->0);
    CPlayer::SendMsg_ReleaseSiegeModeResult(v9, 0);
  }
  if ( v9->m_bMove )
  {
    CCharacter::Stop((CCharacter *)&v9->vfptr);
    CGameObject::SendMsg_BreakStop((CGameObject *)&v9->vfptr);
  }
}
