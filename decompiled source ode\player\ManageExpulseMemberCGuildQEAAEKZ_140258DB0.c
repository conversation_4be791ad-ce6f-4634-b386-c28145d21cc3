/*
 * Function: ?ManageExpulseMember@CGuild@@QEAAEK@Z
 * Address: 0x140258DB0
 */

char __fastcall CGuild::ManageExpulseMember(CGuild *this, unsigned int dwMemberSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v5; // eax@8
  __int64 v6; // [sp+0h] [bp-88h]@1
  _guild_member_info *v7; // [sp+30h] [bp-58h]@4
  _qry_case_forceleave v8; // [sp+48h] [bp-40h]@8
  CGuild *v9; // [sp+90h] [bp+8h]@1
  unsigned int dwMemberSeriala; // [sp+98h] [bp+10h]@1

  dwMemberSeriala = dwMemberSerial;
  v9 = this;
  v2 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = CGuild::GetMemberFromSerial(v9, dwMemberSerial);
  if ( v7 )
  {
    if ( v7->pPlayer )
      v7->pPlayer->m_Param.m_bGuildLock = 1;
    v8.in_leaverserial = dwMemberSeriala;
    v8.in_guildIndex = v9->m_nIndex;
    v8.in_guildserial = v9->m_dwSerial;
    v8.in_seniornum = CGuild::GetMemberNum(v9) - 1;
    v8.in_apprnum = CGuild::GetMemberNum(v9) - 1;
    v8.in_MemberNum = CGuild::GetMemberNum(v9) - 1;
    v8.in_bPunish = 0;
    v5 = _qry_case_forceleave::size(&v8);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 18, (char *)&v8, v5);
    result = 0;
  }
  else
  {
    result = -54;
  }
  return result;
}
