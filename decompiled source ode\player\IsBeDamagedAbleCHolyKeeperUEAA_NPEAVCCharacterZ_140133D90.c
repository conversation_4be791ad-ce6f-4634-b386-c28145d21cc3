/*
 * Function: ?IsBeDamagedAble@CHolyKeeper@@UEAA_NPEAVCCharacter@@@Z
 * Address: 0x140133D90
 */

bool __fastcall CHolyKeeper::IsBeDamagedAble(<PERSON><PERSON><PERSON>eeper *this, CCharacter *pAtter)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CCharacter *v6; // [sp+38h] [bp+10h]@1

  v6 = pAtter;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return pAtter->m_ObjID.m_byID
      || !CMainThread::IsReleaseServiceMode(&g_Main)
      || !BYTE2(v6[25].m_SFCont[0][5].m_nCumulCounter);
}
