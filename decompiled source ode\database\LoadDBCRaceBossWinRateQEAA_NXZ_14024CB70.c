/*
 * Function: ?LoadDB@CRaceBossWinRate@@QEAA_NXZ
 * Address: 0x14024CB70
 */

bool __fastcall CRaceBossWinRate::LoadDB(CRaceBossWinRate *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CRaceBossWinRate *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CRaceBossWinRate::LoadBossCurrentWinRate(v5) )
    result = CRaceBossWinRate::LoadBossAccmulationWinRate(v5, 0i64) == 0;
  else
    result = 0;
  return result;
}
