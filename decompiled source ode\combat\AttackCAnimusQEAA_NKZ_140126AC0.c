/*
 * Function: ?Attack@CAnimus@@QEAA_NK@Z
 * Address: 0x140126AC0
 */

char __fastcall CAnimus::Attack(CAnimus *this, unsigned int skill)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CCharacter *v5; // rcx@8
  int v6; // eax@16
  __int64 v7; // [sp+0h] [bp-438h]@1
  int nSkillIndex; // [sp+20h] [bp-418h]@11
  int v9; // [sp+28h] [bp-410h]@16
  int v10; // [sp+30h] [bp-408h]@16
  char v11; // [sp+38h] [bp-400h]@16
  float v12[3]; // [sp+48h] [bp-3F0h]@8
  int v13; // [sp+64h] [bp-3D4h]@11
  _attack_param pAP; // [sp+80h] [bp-3B8h]@11
  CAttack pAT; // [sp+120h] [bp-318h]@11
  int j; // [sp+414h] [bp-24h]@14
  CMapData *v17; // [sp+418h] [bp-20h]@8
  CCharacter *v18; // [sp+420h] [bp-18h]@16
  CGameObjectVtbl *v19; // [sp+428h] [bp-10h]@16
  CAnimus *v20; // [sp+440h] [bp+8h]@1
  unsigned int v21; // [sp+448h] [bp+10h]@1

  v21 = skill;
  v20 = this;
  v2 = &v7;
  for ( i = 268i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v20->m_pTarget )
  {
    if ( CAnimus::IsValidTarget(v20) )
    {
      v5 = v20->m_pTarget;
      v17 = v20->m_pCurMap;
      if ( (unsigned int)CBsp::CanYouGoThere(v17->m_Level.mBsp, v5->m_fCurPos, v20->m_fCurPos, (float (*)[3])v12) )
      {
        if ( !v20->m_Skill[v21].m_Type )
        {
          v13 = CAnimus::GetAttackPart(v20);
          _attack_param::_attack_param(&pAP);
          nSkillIndex = v21;
          CAnimus::make_gen_attack_param(v20, v20->m_pTarget, v13, &pAP, v21);
          CAttack::CAttack(&pAT, (CCharacter *)&v20->vfptr);
          CAttack::AttackGen(&pAT, &pAP, 0, 0);
          CAnimus::SendMsg_Attack_Gen(v20, &pAT);
          if ( !pAT.m_bFailure && v20->m_byRoleCode != 1 )
            CAnimus::CalcAttExp(v20, &pAT);
          for ( j = 0; j < pAT.m_nDamagedObjNum; ++j )
          {
            v6 = ((int (__fastcall *)(CAnimus *))v20->vfptr->GetLevel)(v20);
            v18 = pAT.m_DamList[j].m_pChar;
            v19 = v18->vfptr;
            v11 = 1;
            v10 = 0;
            v9 = -1;
            LOBYTE(nSkillIndex) = pAT.m_bIsCrtAtt;
            ((void (__fastcall *)(CCharacter *, _QWORD, CAnimus *, _QWORD))v19->SetDamage)(
              v18,
              pAT.m_DamList[j].m_nDamage,
              v20,
              (unsigned int)v6);
          }
        }
        result = 1;
      }
      else
      {
        v20->m_pTarget = 0i64;
        result = 0;
      }
    }
    else
    {
      v20->m_pTarget = 0i64;
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
