/*
 * Function: ?Attack@CMonster@@QEAAHPEAVCCharacter@@PEAVCMonsterSkill@@@Z
 * Address: 0x14014E4C0
 */

__int64 __fastcall CMonster::Attack(CMonster *this, CCharacter *pDst, CMonsterSkill *pskill)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@17
  unsigned int v7; // eax@18
  int v8; // eax@23
  unsigned int v9; // eax@24
  int v10; // eax@29
  unsigned int v11; // eax@30
  int v12; // eax@35
  unsigned int v13; // eax@36
  __int64 v14; // [sp+0h] [bp-FB8h]@1
  _attack_param *v15; // [sp+20h] [bp-F98h]@17
  int v16; // [sp+28h] [bp-F90h]@17
  unsigned int v17; // [sp+30h] [bp-F88h]@17
  char v18; // [sp+38h] [bp-F80h]@17
  unsigned int v19; // [sp+40h] [bp-F78h]@4
  _attack_param pAP; // [sp+60h] [bp-F58h]@15
  CMonsterAttack pAT; // [sp+100h] [bp-EB8h]@15
  int m; // [sp+404h] [bp-BB4h]@15
  _attack_param pParam; // [sp+420h] [bp-B98h]@19
  CMonsterAttack pAt; // [sp+4C0h] [bp-AF8h]@21
  int j; // [sp+7C4h] [bp-7F4h]@21
  _attack_param v26; // [sp+7E0h] [bp-7D8h]@25
  CMonsterAttack v27; // [sp+880h] [bp-738h]@27
  int k; // [sp+B84h] [bp-434h]@27
  _attack_param v29; // [sp+BA0h] [bp-418h]@33
  CMonsterAttack v30; // [sp+C40h] [bp-378h]@33
  int l; // [sp+F44h] [bp-74h]@33
  int v32; // [sp+F48h] [bp-70h]@10
  CCharacter *v33; // [sp+F50h] [bp-68h]@17
  CGameObjectVtbl *v34; // [sp+F58h] [bp-60h]@17
  _base_fld *v35; // [sp+F60h] [bp-58h]@23
  CGameObjectVtbl *v36; // [sp+F68h] [bp-50h]@23
  CCharacter *v37; // [sp+F70h] [bp-48h]@23
  CGameObjectVtbl *v38; // [sp+F78h] [bp-40h]@23
  _base_fld *v39; // [sp+F80h] [bp-38h]@29
  CGameObjectVtbl *v40; // [sp+F88h] [bp-30h]@29
  CCharacter *v41; // [sp+F90h] [bp-28h]@29
  CGameObjectVtbl *v42; // [sp+F98h] [bp-20h]@29
  CCharacter *v43; // [sp+FA0h] [bp-18h]@35
  CGameObjectVtbl *v44; // [sp+FA8h] [bp-10h]@35
  CMonster *v45; // [sp+FC0h] [bp+8h]@1
  CCharacter *pDsta; // [sp+FC8h] [bp+10h]@1
  CMonsterSkill *pSkill; // [sp+FD0h] [bp+18h]@1

  pSkill = pskill;
  pDsta = pDst;
  v45 = this;
  v3 = &v14;
  for ( i = 1004i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v19 = 0;
  if ( !pskill )
    return v19;
  if ( pDst )
    CMonster::UpdateLookAtPos(v45, pDst->m_fCurPos);
  if ( !CMonsterSkill::IsAttackAble(pSkill) )
    return v19;
  v32 = CMonsterSkill::GetType(pSkill);
  if ( v32 )
  {
    switch ( v32 )
    {
      case 1:
        _attack_param::_attack_param(&pParam);
        if ( !CMonster::make_skill_attack_param(v45, pDsta, pSkill, 0, &pParam) )
          return 0i64;
        CMonsterAttack::CMonsterAttack(&pAt, (CCharacter *)&v45->vfptr);
        CMonsterAttack::AttackMonsterSkill(&pAt, &pParam);
        CMonster::SendMsg_Attack_Skill(v45, &pAt);
        for ( j = 0; j < pAt.m_nDamagedObjNum; ++j )
        {
          v35 = CMonsterSkill::GetFld(pSkill);
          v36 = v45->vfptr;
          v8 = ((int (__fastcall *)(CMonster *))v36->GetLevel)(v45);
          v37 = pAt.m_DamList[j].m_pChar;
          v38 = v37->vfptr;
          v18 = 0;
          v17 = v35->m_dwIndex;
          v16 = 0;
          LOBYTE(v15) = pAt.m_bIsCrtAtt;
          ((void (__fastcall *)(CCharacter *, _QWORD, CMonster *, _QWORD))v38->SetDamage)(
            v37,
            pAt.m_DamList[j].m_nDamage,
            v45,
            (unsigned int)v8);
        }
        v9 = GetLoopTime();
        CMonsterSkill::Use(pSkill, v9, 1);
        break;
      case 2:
        _attack_param::_attack_param(&v26);
        if ( !CMonster::make_skill_attack_param(v45, pDsta, pSkill, 2, &v26) )
          return 0i64;
        CMonsterAttack::CMonsterAttack(&v27, (CCharacter *)&v45->vfptr);
        CMonsterAttack::AttackMonsterSkill(&v27, &v26);
        CMonster::SendMsg_Attack_Skill(v45, &v27);
        for ( k = 0; k < v27.m_nDamagedObjNum; ++k )
        {
          v39 = CMonsterSkill::GetFld(pSkill);
          v40 = v45->vfptr;
          v10 = ((int (__fastcall *)(CMonster *))v40->GetLevel)(v45);
          v41 = v27.m_DamList[k].m_pChar;
          v42 = v41->vfptr;
          v18 = 0;
          v17 = v39->m_dwIndex;
          v16 = 2;
          LOBYTE(v15) = v27.m_bIsCrtAtt;
          ((void (__fastcall *)(CCharacter *, _QWORD, CMonster *, _QWORD))v42->SetDamage)(
            v41,
            v27.m_DamList[k].m_nDamage,
            v45,
            (unsigned int)v10);
        }
        v11 = GetLoopTime();
        CMonsterSkill::Use(pSkill, v11, 1);
        break;
      case 3:
        if ( _effect_parameter::GetEff_State(&v45->m_EP, 1) )
          return 0i64;
        _attack_param::_attack_param(&v29);
        CMonster::make_force_attack_param(v45, pDsta, pSkill, &v29);
        CMonsterAttack::CMonsterAttack(&v30, (CCharacter *)&v45->vfptr);
        CMonsterAttack::AttackMonsterForce(&v30, &v29);
        CMonster::SendMsg_Attack_Force(v45, &v30);
        for ( l = 0; l < v30.m_nDamagedObjNum; ++l )
        {
          v12 = ((int (__fastcall *)(CMonster *))v45->vfptr->GetLevel)(v45);
          v43 = v30.m_DamList[l].m_pChar;
          v44 = v43->vfptr;
          v18 = 1;
          v17 = 0;
          v16 = -1;
          LOBYTE(v15) = 0;
          ((void (__fastcall *)(CCharacter *, _QWORD, CMonster *, _QWORD))v44->SetDamage)(
            v43,
            v30.m_DamList[l].m_nDamage,
            v45,
            (unsigned int)v12);
        }
        v19 = 1;
        v13 = GetLoopTime();
        CMonsterSkill::Use(pSkill, v13, 1);
        break;
    }
  }
  else
  {
    _attack_param::_attack_param(&pAP);
    CMonster::make_gen_attack_param(v45, pDsta, &pAP);
    CMonsterAttack::CMonsterAttack(&pAT, (CCharacter *)&v45->vfptr);
    CMonsterAttack::AttackMonsterGen(&pAT, &pAP, v45->m_bApparition);
    CMonster::SendMsg_Attack_Gen(v45, &pAT);
    for ( m = 0; m < pAT.m_nDamagedObjNum; ++m )
    {
      v6 = ((int (__fastcall *)(CMonster *))v45->vfptr->GetLevel)(v45);
      v33 = pAT.m_DamList[m].m_pChar;
      v34 = v33->vfptr;
      v18 = 1;
      v17 = 0;
      v16 = -1;
      LOBYTE(v15) = pAT.m_bIsCrtAtt;
      ((void (__fastcall *)(CCharacter *, _QWORD, CMonster *, _QWORD))v34->SetDamage)(
        v33,
        pAT.m_DamList[m].m_nDamage,
        v45,
        (unsigned int)v6);
    }
    v19 = 1;
    v7 = GetLoopTime();
    CMonsterSkill::Use(pSkill, v7, 1);
  }
  if ( v45->m_bMove )
  {
    if ( v19 )
      CCharacter::Stop((CCharacter *)&v45->vfptr);
  }
  return v19;
}
