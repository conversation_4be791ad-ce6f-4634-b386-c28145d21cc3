/*
 * Function: j_??$make_pair@KPEAU_TimeItem_fld@@@std@@YA?AU?$pair@KPEAU_TimeItem_fld@@@0@KPEAU_TimeItem_fld@@@Z
 * Address: 0x14000C8D8
 */

std::pair<unsigned long,_TimeItem_fld *> *__fastcall std::make_pair<unsigned long,_TimeItem_fld *>(std::pair<unsigned long,_TimeItem_fld *> *result, unsigned int _Val1, _TimeItem_fld *_Val2)
{
  return std::make_pair<unsigned long,_TimeItem_fld *>(result, _Val1, _Val2);
}
