/*
 * Function: ?<PERSON><PERSON><PERSON><PERSON>@CAtlTraceSettings@@QEAAXJ@Z
 * Address: 0x140675500
 */

void __fastcall CAtlTraceSettings::Mark<PERSON><PERSON><PERSON>(CAtlTraceSettings *this, int nCookie)
{
  CAtlTraceSettings *v2; // [sp+40h] [bp+8h]@1
  int v3; // [sp+48h] [bp+10h]@1

  v3 = nCookie;
  v2 = this;
  if ( !nCookie
    && CrtDbgReportW_0(
         2i64,
         (__int64)L"f:\\dd\\vctools\\vc7libs\\ship\\atlmfc\\src\\atl\\atls\\atltracemodulemanager.h",
         84i64,
         0i64) == 1 )
  {
    __debugbreak();
  }
  v2->m_nCookie = v3;
}
