/*
 * Function: ?DeleteExchangeEventItem@CExchangeEvent@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x140329CB0
 */

void __fastcall CExchangeEvent::DeleteExchangeEventItem(CExchangeEvent *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@8
  __int64 v5; // [sp+0h] [bp-48h]@1
  bool bDelete; // [sp+20h] [bp-28h]@19
  char *strErrorCodePos; // [sp+28h] [bp-20h]@19
  int j; // [sp+30h] [bp-18h]@7
  char *v9; // [sp+38h] [bp-10h]@7
  CExchangeEvent *v10; // [sp+50h] [bp+8h]@1
  CPlayer *v11; // [sp+58h] [bp+10h]@1

  v11 = pOne;
  v10 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pOne && pOne->m_bLive )
  {
    v9 = 0i64;
    for ( j = 0; ; ++j )
    {
      v4 = CPlayerDB::GetBagNum(&v11->m_Param);
      if ( j >= 20 * (unsigned __int8)v4 )
        break;
      v9 = &v11->m_Param.m_dbInven.m_pStorageList[j].m_bLoad;
      if ( v9
        && *v9
        && ((unsigned __int8)v9[1] == v10->m_EventItemInfo[0].byTableCode
         && *(_WORD *)(v9 + 3) == v10->m_EventItemInfo[0].dwIndex
         || (unsigned __int8)v9[1] == v10->m_EventItemInfo[1].byTableCode
         && *(_WORD *)(v9 + 3) == v10->m_EventItemInfo[1].dwIndex
         || (unsigned __int8)v9[1] == v10->m_EventItemInfo[2].byTableCode
         && *(_WORD *)(v9 + 3) == v10->m_EventItemInfo[2].dwIndex
         || (unsigned __int8)v9[1] == v10->m_EventItemInfo[3].byTableCode
         && *(_WORD *)(v9 + 3) == v10->m_EventItemInfo[3].dwIndex) )
      {
        strErrorCodePos = 0i64;
        bDelete = 1;
        if ( !CPlayer::Emb_DelStorage(v11, 0, v9[49], 0, 1, 0i64) )
          return;
        CPlayer::SendMsg_DeleteStorageInform(v11, 0, *(_WORD *)(v9 + 17));
        CPlayer::SendMsg_BuddhaEventMsg(v11, 2);
      }
    }
  }
}
