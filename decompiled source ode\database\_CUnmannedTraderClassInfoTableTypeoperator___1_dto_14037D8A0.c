/*
 * Function: _CUnmannedTraderClassInfoTableType::operator__::_1_::dtor$0
 * Address: 0x14037D8A0
 */

void __fastcall CUnmannedTraderClassInfoTableType::operator__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(*(std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > **)(a2 + 56));
}
