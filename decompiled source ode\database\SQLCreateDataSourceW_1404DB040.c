/*
 * Function: SQLCreateDataSourceW
 * Address: 0x1404DB040
 */

int __fastcall SQLCreateDataSourceW(HWND__ *hwndParent, const unsigned __int16 *lpszDSN)
{
  HWND__ *v2; // rdi@1
  const unsigned __int16 *v3; // rbx@1
  __int64 (__cdecl *v4)(); // rax@1
  int result; // eax@2

  v2 = hwndParent;
  v3 = lpszDSN;
  v4 = ODBC___GetSetupProc("SQLCreateDataSourceW");
  if ( v4 )
    result = ((int (__fastcall *)(HWND__ *, const unsigned __int16 *))v4)(v2, v3);
  else
    result = 0;
  return result;
}
