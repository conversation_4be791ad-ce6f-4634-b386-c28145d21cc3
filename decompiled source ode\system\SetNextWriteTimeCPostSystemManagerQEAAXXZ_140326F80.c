/*
 * Function: ?SetNextWriteTime@CPostSystemManager@@QEAAXXZ
 * Address: 0x140326F80
 */

void __fastcall CPostSystemManager::SetNextWriteTime(CPostSystemManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  ATL::CTimeSpan *v3; // rax@4
  ATL::CTime *v4; // rax@4
  int v5; // eax@4
  ATL::CTime *v6; // rax@4
  __int64 v7; // [sp+0h] [bp-88h]@1
  ATL::CTime v8; // [sp+48h] [bp-40h]@4
  ATL::CTime result; // [sp+58h] [bp-30h]@4
  ATL::CTimeSpan v10; // [sp+60h] [bp-28h]@4
  ATL::CTime v11; // [sp+68h] [bp-20h]@4
  ATL::CTimeSpan *v12; // [sp+70h] [bp-18h]@4
  int nDay; // [sp+78h] [bp-10h]@4
  int nMonth; // [sp+7Ch] [bp-Ch]@4
  CPostSystemManager *v15; // [sp+90h] [bp+8h]@1

  v15 = this;
  v1 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  ATL::CTimeSpan::CTimeSpan(&v10, 1, 0, 0, 0);
  v12 = v3;
  v4 = ATL::CTime::GetTickCount(&result);
  ATL::CTime::operator+(v4, &v8, (ATL::CTimeSpan)v12->m_timeSpan);
  nDay = ATL::CTime::GetDay(&v8);
  nMonth = ATL::CTime::GetMonth(&v8);
  v5 = ATL::CTime::GetYear(&v8);
  ATL::CTime::CTime(&v11, v5, nMonth, nDay, 0, 0, 0, -1);
  v15->m_tNextWrite = ATL::CTime::GetTime(v6);
}
