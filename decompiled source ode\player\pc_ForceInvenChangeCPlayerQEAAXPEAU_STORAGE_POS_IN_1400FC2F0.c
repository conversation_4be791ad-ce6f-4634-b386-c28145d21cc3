/*
 * Function: ?pc_ForceInvenChange@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@G@Z
 * Address: 0x1400FC2F0
 */

void __fastcall CPlayer::pc_ForceInvenChange(CPlayer *this, _STORAGE_POS_INDIV *pItem, unsigned __int16 wReplaceSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-168h]@1
  bool bDelete; // [sp+20h] [bp-148h]@39
  char *strErrorCodePos; // [sp+28h] [bp-140h]@39
  char v8; // [sp+30h] [bp-138h]@4
  void *Src; // [sp+38h] [bp-130h]@4
  void *v10; // [sp+40h] [bp-128h]@4
  _STORAGE_LIST *v11; // [sp+48h] [bp-120h]@4
  _STORAGE_LIST *v12; // [sp+50h] [bp-118h]@4
  int j; // [sp+58h] [bp-110h]@31
  char *v14; // [sp+60h] [bp-108h]@34
  _STORAGE_LIST::_db_con Dst; // [sp+78h] [bp-F0h]@39
  _STORAGE_LIST::_db_con v16; // [sp+C8h] [bp-A0h]@42
  _STORAGE_LIST::_db_con v17; // [sp+118h] [bp-50h]@47
  CPlayer *v18; // [sp+170h] [bp+8h]@1
  _STORAGE_POS_INDIV *v19; // [sp+178h] [bp+10h]@1
  unsigned __int16 v20; // [sp+180h] [bp+18h]@1

  v20 = wReplaceSerial;
  v19 = pItem;
  v18 = this;
  v3 = &v5;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = 0;
  Src = 0i64;
  v10 = 0i64;
  v11 = v18->m_Param.m_pStoragePtr[pItem->byStorageCode];
  v12 = 0i64;
  if ( pItem->byStorageCode )
  {
    if ( pItem->byStorageCode != 3 )
      return;
    v12 = v18->m_Param.m_pStoragePtr[0];
  }
  else
  {
    v12 = v18->m_Param.m_pStoragePtr[3];
  }
  Src = _STORAGE_LIST::GetPtrFromSerial(v11, pItem->wItemSerial);
  if ( Src )
  {
    if ( *((_BYTE *)Src + 1) == 15 )
    {
      if ( *((_BYTE *)Src + 19) )
      {
        v8 = 11;
      }
      else
      {
        if ( v18->m_pCurMap->m_pMapSet->m_nMapType != 1 )
        {
          if ( v20 != 0xFFFF )
          {
            v10 = _STORAGE_LIST::GetPtrFromSerial(v12, v20);
            if ( !v10 )
            {
              v8 = 2;
              goto $RESULT_94;
            }
            if ( *((_BYTE *)v10 + 1) != 15 )
            {
              v8 = 2;
              goto $RESULT_94;
            }
            if ( *((_BYTE *)v10 + 19) )
            {
              v8 = 11;
              goto $RESULT_94;
            }
            if ( *((_DWORD *)CPlayer::s_pnLinkForceItemToEffect + *(_WORD *)((char *)Src + 3)) != *((_DWORD *)CPlayer::s_pnLinkForceItemToEffect
                                                                                                  + *(_WORD *)((char *)v10 + 3)) )
            {
              v8 = 3;
              goto $RESULT_94;
            }
          }
          if ( v20 != 0xFFFF || _STORAGE_LIST::GetIndexEmptyCon(v12) != 255 )
          {
            if ( v20 == 0xFFFF && !v19->byStorageCode )
            {
              for ( j = 0; j < v12->m_nUsedNum; ++j )
              {
                v14 = &v12->m_pStorageList[j].m_bLoad;
                if ( *v14
                  && *((_DWORD *)CPlayer::s_pnLinkForceItemToEffect + *(_WORD *)(v14 + 3)) == *((_DWORD *)CPlayer::s_pnLinkForceItemToEffect
                                                                                              + *(_WORD *)((char *)Src + 3)) )
                {
                  v8 = 4;
                  goto $RESULT_94;
                }
              }
            }
          }
          else
          {
            v8 = 5;
          }
          goto $RESULT_94;
        }
        v8 = 12;
      }
    }
    else
    {
      v8 = 1;
    }
  }
  else
  {
    v8 = 1;
  }
$RESULT_94:
  if ( !v8 )
  {
    _STORAGE_LIST::_db_con::_db_con(&Dst);
    memcpy_0(&Dst, Src, 0x32ui64);
    strErrorCodePos = "CPlayer::pc_ForceInvenChange() -- 0";
    bDelete = 0;
    if ( !CPlayer::Emb_DelStorage(
            v18,
            v11->m_nListCode,
            *((_BYTE *)Src + 49),
            0,
            0,
            "CPlayer::pc_ForceInvenChange() -- 0") )
    {
      CPlayer::SendMsg_ForceInvenChange(v18, -1);
      return;
    }
    if ( v10 )
    {
      _STORAGE_LIST::_db_con::_db_con(&v16);
      memcpy_0(&v16, v10, 0x32ui64);
      bDelete = 0;
      if ( !CPlayer::Emb_AddStorage(v18, v11->m_nListCode, (_STORAGE_LIST::_storage_con *)&v16.m_bLoad, 1, 0) )
      {
        bDelete = 0;
        CPlayer::Emb_AddStorage(v18, v11->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0);
        CPlayer::SendMsg_ForceInvenChange(v18, -1);
        return;
      }
      strErrorCodePos = "CPlayer::pc_ForceInvenChange() -- 1";
      bDelete = 0;
      if ( !CPlayer::Emb_DelStorage(
              v18,
              v12->m_nListCode,
              *((_BYTE *)v10 + 49),
              0,
              0,
              "CPlayer::pc_ForceInvenChange() -- 1") )
      {
        strErrorCodePos = 0i64;
        bDelete = 0;
        CPlayer::Emb_DelStorage(v18, v11->m_nListCode, *((_BYTE *)v10 + 49), 0, 0, 0i64);
        bDelete = 0;
        CPlayer::Emb_AddStorage(v18, v11->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0);
        CPlayer::SendMsg_ForceInvenChange(v18, -1);
        return;
      }
    }
    if ( v10 )
    {
      _STORAGE_LIST::_db_con::_db_con(&v17);
      memcpy_0(&v17, v10, 0x32ui64);
      bDelete = 0;
      CPlayer::Emb_AddStorage(v18, v12->m_nListCode, (_STORAGE_LIST::_storage_con *)&v17.m_bLoad, 1, 0);
      strErrorCodePos = 0i64;
      bDelete = 0;
      CPlayer::Emb_DelStorage(v18, v11->m_nListCode, *((_BYTE *)v10 + 49), 0, 0, 0i64);
      bDelete = 0;
      CPlayer::Emb_AddStorage(v18, v11->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0);
      CPlayer::SendMsg_ForceInvenChange(v18, -1);
      return;
    }
    bDelete = 0;
    if ( !CPlayer::Emb_AddStorage(v18, v12->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0) )
    {
      bDelete = 0;
      CPlayer::Emb_AddStorage(v18, v11->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0);
      CPlayer::SendMsg_ForceInvenChange(v18, -1);
      return;
    }
    CPlayer::Emb_EquipLink(v18);
  }
  CPlayer::SendMsg_ForceInvenChange(v18, v8);
}
