/*
 * Function: ?GetBase@?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@CryptoPP@@UEBAAEBVInteger@2@AEBV?$DL_GroupPrecomputation@VInteger@CryptoPP@@@2@@Z
 * Address: 0x140551630
 */

signed __int64 __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::GetBase(__int64 a1, int (__fastcall ***a2)(_QWORD))
{
  signed __int64 v2; // rax@3
  signed __int64 v4; // [sp+28h] [bp-10h]@2
  __int64 v5; // [sp+40h] [bp+8h]@1

  v5 = a1;
  if ( (unsigned __int8)(**a2)(a2) )
  {
    v4 = v5 + 8;
  }
  else
  {
    LODWORD(v2) = std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::operator[](v5 + 96, 0i64);
    v4 = v2;
  }
  return v4;
}
