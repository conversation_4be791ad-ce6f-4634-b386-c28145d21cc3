/*
 * Function: ?SendMsg_GuildJoinAcceptInform@CGuild@@QEAAXPEAU_guild_member_info@@K@Z
 * Address: 0x140256AE0
 */

void __fastcall CGuild::SendMsg_GuildJoinAcceptInform(CGuild *this, _guild_member_info *p, unsigned int dwAcceptSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-A8h]@1
  char szMsg[4]; // [sp+38h] [bp-70h]@4
  unsigned int v7; // [sp+3Ch] [bp-6Ch]@4
  char Dest; // [sp+40h] [bp-68h]@4
  char v9; // [sp+51h] [bp-57h]@4
  char v10; // [sp+52h] [bp-56h]@4
  unsigned int v11; // [sp+53h] [bp-55h]@4
  char v12; // [sp+57h] [bp-51h]@4
  char pbyType; // [sp+74h] [bp-34h]@4
  char v14; // [sp+75h] [bp-33h]@4
  int j; // [sp+84h] [bp-24h]@4
  unsigned __int64 v16; // [sp+90h] [bp-18h]@4
  CGuild *v17; // [sp+B0h] [bp+8h]@1
  _guild_member_info *v18; // [sp+B8h] [bp+10h]@1

  v18 = p;
  v17 = this;
  v3 = &v5;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v16 = (unsigned __int64)&v5 ^ _security_cookie;
  *(_DWORD *)szMsg = dwAcceptSerial;
  v7 = p->dwSerial;
  strcpy_0(&Dest, p->wszName);
  v10 = v18->byLv;
  v11 = v18->dwPvpPoint;
  v9 = v18->byClassInGuild;
  v12 = v18->byRank;
  pbyType = 27;
  v14 = 15;
  for ( j = 0; j < 50; ++j )
  {
    if ( _guild_member_info::IsFill(&v17->m_MemberData[j]) && v17->m_MemberData[j].pPlayer )
    {
      if ( v18 == &v17->m_MemberData[j] )
        CGuild::SendMsg_DownPacket(v17, 2, &v17->m_MemberData[j]);
      else
        CNetProcess::LoadSendMsg(unk_1414F2088, v17->m_MemberData[j].pPlayer->m_ObjID.m_wIndex, &pbyType, szMsg, 0x20u);
    }
  }
}
