/*
 * Function: ?dev_up_pvp@CPlayer@@QEAA_NN@Z
 * Address: 0x1400BC6F0
 */

char __usercall CPlayer::dev_up_pvp@<al>(CPlayer *this@<rcx>, long double dPoint@<xmm1>, __int64 a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp-20h] [bp-38h]@1
  __int64 v7; // [sp+0h] [bp-18h]@4
  CPlayer *v8; // [sp+20h] [bp+8h]@1

  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CPlayerDB::GetPvPPoint(&v8->m_Param);
  v7 = a3;
  CPlayer::AlterPvPPoint(v8, dPoint, cheat, 0xFFFFFFFF);
  return 1;
}
