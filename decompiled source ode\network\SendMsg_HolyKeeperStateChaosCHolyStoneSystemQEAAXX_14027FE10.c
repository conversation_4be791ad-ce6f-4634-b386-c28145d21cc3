/*
 * Function: ?SendMsg_HolyKeeperState<PERSON><PERSON>s@CHolyStoneSystem@@QEAAXXZ
 * Address: 0x14027FE10
 */

void __fastcall CHolyStoneSystem::SendMsg_HolyKeeperStateChaos(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  char v5; // [sp+38h] [bp-40h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v7; // [sp+55h] [bp-23h]@4
  unsigned int dwClientIndex; // [sp+64h] [bp-14h]@4
  CHolyStoneSystem *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(_DWORD *)szMsg = g_Keeper->m_dwObjSerial;
  v5 = CHolyStoneSystem::GetHolyMasterRace(v9);
  pbyType = 25;
  v7 = 15;
  for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
  {
    if ( *(&g_Player.m_bLive + 50856 * (signed int)dwClientIndex) )
      CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, szMsg, 5u);
  }
}
