/*
 * Function: ??0?$_List_ptr@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@2@@std@@IEAA@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@1@@Z
 * Address: 0x140315A80
 */

void __fastcall std::_List_ptr<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_List_ptr<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>(std::_List_ptr<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *this, __int64 _Al)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<std::pair<int const ,_TimeItem_fld const *> > v4; // al@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  std::allocator<std::pair<int const ,_TimeItem_fld const *> > *v7; // [sp+28h] [bp-10h]@4
  std::_List_ptr<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *v8; // [sp+40h] [bp+8h]@1
  std::allocator<std::pair<int const ,_TimeItem_fld const *> > *__formal; // [sp+48h] [bp+10h]@1

  __formal = (std::allocator<std::pair<int const ,_TimeItem_fld const *> > *)_Al;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = (std::allocator<std::pair<int const ,_TimeItem_fld const *> > *)&v6;
  std::allocator<std::pair<int const,_TimeItem_fld const *>>::allocator<std::pair<int const,_TimeItem_fld const *>>(
    (std::allocator<std::pair<int const ,_TimeItem_fld const *> > *)&v6,
    (std::allocator<std::pair<int const ,_TimeItem_fld const *> > *)_Al);
  std::_List_nod<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_List_nod<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>(
    (std::_List_nod<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *)&v8->_Myfirstiter,
    v4);
  std::allocator<std::_List_nod<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Node *>::allocator<std::_List_nod<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Node *>(
    &v8->_Alptr,
    __formal);
}
