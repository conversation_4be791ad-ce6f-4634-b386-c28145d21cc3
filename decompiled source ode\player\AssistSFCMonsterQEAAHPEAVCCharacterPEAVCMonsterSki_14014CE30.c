/*
 * Function: ?AssistSF@CMonster@@QEAAHPEAVCCharacter@@PEAVCMonsterSkill@@@Z
 * Address: 0x14014CE30
 */

int __fastcall CMonster::AssistSF(CMonster *this, CCharacter *pDst, CMonsterSkill *pskill)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@9
  CMonster *v8; // [sp+40h] [bp+8h]@1
  CCharacter *pDsta; // [sp+48h] [bp+10h]@1
  CMonsterSkill *pskilla; // [sp+50h] [bp+18h]@1

  pskilla = pskill;
  pDsta = pDst;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pskill && pDst )
  {
    if ( pDst )
      CMonster::UpdateLookAtPos(v8, pDst->m_fCurPos);
    v7 = CMonsterSkill::GetUseType(pskilla);
    switch ( v7 )
    {
      case 1:
        result = CMonster::_AssistSF_Cont_Dmg(v8, pDsta, pskilla);
        break;
      case 2:
        result = CMonster::_AssistSF_Cont_Support(v8, pDsta, pskilla);
        break;
      case 3:
        result = CMonster::_AssistSF_Cont_Temp(v8, pDsta, pskilla);
        break;
      default:
        result = 0;
        break;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
