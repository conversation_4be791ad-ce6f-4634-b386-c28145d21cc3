/*
 * Function: ??0__list@_qry_case_all_store_limit_item@@QEAA@XZ
 * Address: 0x14034BC20
 */

void __fastcall _qry_case_all_store_limit_item::__list::__list(_qry_case_all_store_limit_item::__list *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _qry_case_all_store_limit_item::__list *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  `vector constructor iterator'(
    v4->ItemData,
    8ui64,
    16,
    (void *(__cdecl *)(void *))_limit_item_db_data::_limit_item_db_data);
  _qry_case_all_store_limit_item::__list::init(v4);
}
