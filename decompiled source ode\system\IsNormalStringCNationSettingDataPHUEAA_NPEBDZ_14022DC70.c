/*
 * Function: ?IsNormalString@CNationSettingDataPH@@UEAA_NPEBD@Z
 * Address: 0x14022DC70
 */

bool __fastcall CNationSettingDataPH::IsNormalString(CNationSettingDataPH *this, const char *szString)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CNationSettingDataPH *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return CNationSettingData::IsNormalStringDefProc((CNationSettingData *)&v6->vfptr, szString, 0i64);
}
