/*
 * Function: ?ct_server_time@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140294260
 */

char __fastcall ct_server_time(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v4; // eax@8
  int v5; // ecx@8
  __int64 v6; // [sp+0h] [bp-198h]@1
  bool bFilter[4]; // [sp+20h] [bp-178h]@8
  char *pwszMessage; // [sp+28h] [bp-170h]@8
  char byPvpGrade[4]; // [sp+30h] [bp-168h]@8
  char *pwszSender; // [sp+38h] [bp-160h]@8
  __int64 _Time; // [sp+48h] [bp-150h]@6
  tm *v12; // [sp+58h] [bp-140h]@6
  char Dest; // [sp+70h] [bp-128h]@7
  unsigned __int64 v14; // [sp+180h] [bp-18h]@4
  CPlayer *v15; // [sp+1A0h] [bp+8h]@1

  v15 = pOne;
  v1 = &v6;
  for ( i = 100i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v14 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( v15 )
  {
    time_9(&_Time);
    v12 = localtime_7(&_Time);
    if ( v12 )
    {
      v4 = v12->tm_mon + 1;
      v5 = v12->tm_year;
      LODWORD(pwszSender) = v12->tm_sec;
      *(_DWORD *)byPvpGrade = v12->tm_min;
      LODWORD(pwszMessage) = v12->tm_hour;
      *(_DWORD *)bFilter = v12->tm_mday;
      sprintf(&Dest, "%04d-%02d-%02d %02d:%02d:%02d", (unsigned int)(v5 + 1900), (unsigned int)v4);
    }
    else
    {
      sprintf(&Dest, "::localtime Fail!");
    }
    CPlayer::SendData_ChatTrans(v15, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
