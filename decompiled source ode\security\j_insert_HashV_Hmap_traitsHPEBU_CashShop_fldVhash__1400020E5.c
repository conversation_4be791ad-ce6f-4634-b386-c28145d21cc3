/*
 * Function: j_?insert@?$_Hash@V?$_Hmap_traits@HPEBU_CashShop_fld@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEBU_CashShop_fld@@@std@@@std@@$0A@@stdext@@@stdext@@QEAA?AU?$pair@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEBU_CashShop_fld@@@std@@V?$allocator@U?$pair@$$CBHPEBU_CashShop_fld@@@std@@@2@@std@@_N@std@@AEBU?$pair@$$CBHPEBU_CashShop_fld@@@4@@Z
 * Address: 0x1400020E5
 */

std::pair<std::list<std::pair<int const ,_CashShop_fld const *>,std::allocator<std::pair<int const ,_CashShop_fld const *> > >::_Iterator<0>,bool> *__fastcall stdext::_Hash<stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_CashShop_fld const *>>,0>>::insert(stdext::_Hash<stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_CashShop_fld const *> >,0> > *this, std::pair<std::list<std::pair<int const ,_CashShop_fld const *>,std::allocator<std::pair<int const ,_CashShop_fld const *> > >::_Iterator<0>,bool> *result, std::pair<int const ,_CashShop_fld const *> *_Val)
{
  return stdext::_Hash<stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_CashShop_fld const *>>,0>>::insert(
           this,
           result,
           _Val);
}
