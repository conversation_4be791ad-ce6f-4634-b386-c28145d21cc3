/*
 * Function: ?GetYAngleByte@CMonster@@QEAAEXZ
 * Address: 0x14014CB50
 */

__int64 __usercall CMonster::GetYAngleByte@<rax>(CMonster *this@<rcx>, float a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMonster *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CMonster::GetYAngle(v6);
  return (unsigned int)(signed int)ffloor((float)(255.0 * a2) / 65535.0);
}
