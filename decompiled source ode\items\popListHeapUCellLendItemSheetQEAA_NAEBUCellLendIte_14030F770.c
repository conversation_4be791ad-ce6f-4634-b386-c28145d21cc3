/*
 * Function: ?pop@?$ListHeap@UCell@LendItemSheet@@@@QEAA_NAEBUCell@LendItemSheet@@@Z
 * Address: 0x14030F770
 */

char __fastcall ListHeap<LendItemSheet::Cell>::pop(ListHeap<LendItemSheet::Cell> *this, LendItemSheet::Cell *data)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  ListHeap<LendItemSheet::Cell>::CIndexListEx *v6; // [sp+20h] [bp-28h]@4
  LendItemSheet::Cell *v7; // [sp+28h] [bp-20h]@8
  CNetIndexList::_index_node *v8; // [sp+30h] [bp-18h]@9
  ListHeap<LendItemSheet::Cell> *v9; // [sp+50h] [bp+8h]@1
  LendItemSheet::Cell *rhs; // [sp+58h] [bp+10h]@1

  rhs = data;
  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = &v9->_listData;
  while ( (CNetIndexList::_index_node *)v6 != &v9->_listData.m_Tail )
  {
    v6 = (ListHeap<LendItemSheet::Cell>::CIndexListEx *)v6->m_Head.m_pNext;
    if ( (CNetIndexList::_index_node *)v6 == &v9->_listData.m_Tail )
      return 0;
    v7 = &v9->_pBuf[v6->m_Head.m_dwIndex];
    if ( LendItemSheet::Cell::operator==(v7, rhs) )
    {
      v6->m_Head.m_pPrev->m_pNext = v6->m_Head.m_pNext;
      v6->m_Head.m_pNext->m_pPrev = v6->m_Head.m_pPrev;
      --v9->_listData.m_dwCount;
      v8 = &v9->_listData.m_BufTail;
      v6->m_Head.m_pNext = &v9->_listData.m_BufTail;
      v6->m_Head.m_pPrev = v8->m_pPrev;
      v8->m_pPrev->m_pNext = (CNetIndexList::_index_node *)v6;
      v8->m_pPrev = (CNetIndexList::_index_node *)v6;
      ++v9->_listData.m_dwBufCount;
      CNetIndexList::PushNode_Back((CNetIndexList *)&v9->_listEmpty.m_Head, v6->m_Head.m_dwIndex);
      return 1;
    }
  }
  return 0;
}
