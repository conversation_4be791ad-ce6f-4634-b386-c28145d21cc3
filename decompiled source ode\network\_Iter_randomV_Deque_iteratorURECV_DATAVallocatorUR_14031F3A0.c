/*
 * Function: ??$_Iter_random@V?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@V12@@std@@YA?AUrandom_access_iterator_tag@0@AEBV?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@0@0@Z
 * Address: 0x14031F3A0
 */

std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::_Iter_random<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>>(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__formal, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v6; // [sp+50h] [bp+8h]@1

  v6 = __formal;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return v6;
}
