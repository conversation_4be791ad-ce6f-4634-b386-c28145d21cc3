/*
 * Function: ??1?$deque@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@QEAA@XZ
 * Address: 0x14031FB80
 */

void __fastcall std::deque<RECV_DATA,std::allocator<RECV_DATA>>::~deque<RECV_DATA,std::allocator<RECV_DATA>>(std::deque<RECV_DATA,std::allocator<RECV_DATA> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  std::deque<RECV_DATA,std::allocator<RECV_DATA> > *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::deque<RECV_DATA,std::allocator<RECV_DATA>>::_Tidy(v4);
}
