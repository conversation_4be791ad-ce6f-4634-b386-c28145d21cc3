/*
 * Function: j_?Add@CGuildBattleReservedSchedule@GUILD_BATTLE@@QEAAEKKPEAPEAVCGuildBattleSchedule@2@@Z
 * Address: 0x140012021
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::Add(GUILD_BATTLE::CGuildBattleReservedSchedule *this, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt, GUILD_BATTLE::CGuildBattleSchedule **ppkSchedule)
{
  return GUILD_BATTLE::CGuildBattleReservedSchedule::Add(this, dwStartTimeInx, dwElapseTimeCnt, ppkSchedule);
}
