/*
 * Function: ?PreCheckPotion@CPotionMgr@@QEAAHPEAVCPlayer@@AEAPEAVCCharacter@@PEBU_PotionItem_fld@@KPEAU_skill_fld@@_N@Z
 * Address: 0x14039CDD0
 */

signed __int64 __usercall CPotionMgr::PreCheckPotion@<rax>(CPotionMgr *this@<rcx>, CPlayer *pUsePlayer@<rdx>, CCharacter **pTarget<PERSON>haracter@<r8>, _PotionItem_fld *pfB@<r9>, float a5@<xmm0>, unsigned int nCurTime, _skill_fld *pFld, bool bCheckDist)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  CRaceBuffManager *v11; // rax@119
  CRaceBuffManager *v12; // rax@119
  CRaceBuffManager *v13; // rax@127
  CRaceBuffManager *v14; // rax@127
  CGuildRoomSystem *v15; // rax@142
  CMapData *v16; // rax@142
  CGuildRoomSystem *v17; // rax@143
  CMapData *v18; // rax@143
  CGuildRoomSystem *v19; // rax@144
  CGuildRoomSystem *v20; // rax@149
  CGuildRoomSystem *v21; // rax@150
  CGuildRoomSystem *v22; // rax@151
  int v23; // eax@158
  int v24; // eax@159
  int v25; // eax@160
  __int64 v26; // [sp+0h] [bp-118h]@1
  CPlayer *v27; // [sp+20h] [bp-F8h]@28
  CPlayer *v28; // [sp+28h] [bp-F0h]@34
  float v29; // [sp+30h] [bp-E8h]@42
  CCharacter *v30; // [sp+38h] [bp-E0h]@46
  CCharacter *v31; // [sp+40h] [bp-D8h]@50
  int j; // [sp+48h] [bp-D0h]@78
  int n; // [sp+4Ch] [bp-CCh]@81
  _base_fld *v34; // [sp+50h] [bp-C8h]@81
  _base_fld *v35; // [sp+58h] [bp-C0h]@81
  CCharacter *v36; // [sp+60h] [bp-B8h]@113
  CPlayer *pOne; // [sp+68h] [bp-B0h]@119
  unsigned int uiContinueCnt; // [sp+70h] [bp-A8h]@119
  int v39; // [sp+74h] [bp-A4h]@119
  CPlayer *pkPlayer; // [sp+78h] [bp-A0h]@127
  unsigned int v41; // [sp+80h] [bp-98h]@127
  int v42; // [sp+84h] [bp-94h]@127
  CCharacter *v43; // [sp+88h] [bp-90h]@137
  CPlayer *v44; // [sp+90h] [bp-88h]@156
  int v45; // [sp+98h] [bp-80h]@164
  CCharacter *v46; // [sp+A0h] [bp-78h]@164
  int k; // [sp+A8h] [bp-70h]@166
  char *v48; // [sp+B0h] [bp-68h]@169
  int v49; // [sp+B8h] [bp-60h]@142
  int v50; // [sp+BCh] [bp-5Ch]@143
  __int64 v51; // [sp+C0h] [bp-58h]@144
  int v52; // [sp+C8h] [bp-50h]@144
  __int64 v53; // [sp+D0h] [bp-48h]@144
  int v54; // [sp+D8h] [bp-40h]@149
  int v55; // [sp+DCh] [bp-3Ch]@150
  CUserDB *v56; // [sp+E0h] [bp-38h]@151
  int v57; // [sp+E8h] [bp-30h]@151
  CGuild *v58; // [sp+F0h] [bp-28h]@151
  int v59; // [sp+F8h] [bp-20h]@158
  CGameObjectVtbl *v60; // [sp+100h] [bp-18h]@158
  int v61; // [sp+108h] [bp-10h]@159
  int v62; // [sp+10Ch] [bp-Ch]@160
  CPotionMgr *v63; // [sp+120h] [bp+8h]@1
  CPlayer *v64; // [sp+128h] [bp+10h]@1
  CCharacter **v65; // [sp+130h] [bp+18h]@1
  _PotionItem_fld *pPotionFld; // [sp+138h] [bp+20h]@1

  pPotionFld = pfB;
  v65 = pTargetCharacter;
  v64 = pUsePlayer;
  v63 = this;
  v8 = &v26;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  if ( !pUsePlayer || !*pTargetCharacter || !pfB || !pFld )
    return 0xFFFFFFFFi64;
  if ( Major_Cash_Item )
  {
    if ( pFld->m_nTempEffectType == 48 || pFld->m_nTempEffectType == 49 )
      return 25i64;
    if ( pFld->m_nTempEffectType == 70 || pFld->m_nTempEffectType == 71 )
      return 25i64;
  }
  _effect_parameter::GetEff_Have(&pUsePlayer->m_EP, 58);
  if ( a5 == 0.0 && !CPotionParam::IsUsableActDelay(&v64->m_PotionParam, pPotionFld->m_nDelayType, nCurTime) )
    return 9i64;
  if ( !CCharacter::IsPotionEffectableDst((CCharacter *)&v64->vfptr, pFld->m_strActableDst, *v65) )
  {
    if ( pFld->m_strActableDst[0] != 49 )
      return 19i64;
    *v65 = (CCharacter *)v64;
  }
  if ( !(*v65)->m_ObjID.m_byID
    && pFld->m_nTempEffectType != 70
    && pFld->m_nTempEffectType != 71
    && pFld->m_nTempEffectType != 48
    && pFld->m_nTempEffectType != 49 )
  {
    v27 = (CPlayer *)*v65;
    if ( v64 != v27 && CPlayer::IsRidingUnit(v27) )
      return 19i64;
  }
  if ( !(*v65)->m_ObjID.m_byID )
  {
    if ( pFld->m_nTempEffectType == 48 || pFld->m_nTempEffectType == 70 )
    {
      v28 = (CPlayer *)*v65;
      if ( CPlayer::IsSiegeMode(v28) )
        return 19i64;
    }
    if ( (pFld->m_nTempEffectType == 49 || pFld->m_nTempEffectType == 71) && CPlayer::IsSiegeMode(v64) )
      return 19i64;
  }
  if ( bCheckDist && (CCharacter *)v64 != *v65 )
  {
    GetSqrt(v64->m_fCurPos, (*v65)->m_fCurPos);
    v29 = a5;
    if ( a5 > (float)pPotionFld->m_nUseRange )
      return 20i64;
  }
  if ( pPotionFld->m_nDelayType == 9 && !(*v65)->m_ObjID.m_byID )
  {
    v30 = *v65;
    if ( *(_DWORD *)&v30[26].m_SFContAura[0][6].m_byLv - *(_DWORD *)&v30[26].m_SFContAura[0][6].m_bExist > nCurTime )
      return 17i64;
  }
  if ( pPotionFld->m_nDelayType == 14 && !(*v65)->m_ObjID.m_byID )
  {
    v31 = *v65;
    if ( !HIBYTE(v31[25].m_SFContAura[0][5].m_wDurSec) )
      return 23i64;
  }
  if ( pPotionFld->m_nDelayType == 35
    && !(*v65)->m_ObjID.m_byID
    && CHolyStoneSystem::GetDestroyerState(&g_HolySys) == 2
    && CHolyStoneSystem::GetDestroyerSerial(&g_HolySys) == v64->m_dwObjSerial )
  {
    return 45i64;
  }
  if ( pPotionFld->m_nUseState )
  {
    if ( pPotionFld->m_nUseState == 1
      && (unsigned __int8)((int (__fastcall *)(CPlayer *))v64->vfptr->Is_Battle_Mode)(v64) )
    {
      return 28i64;
    }
    if ( pPotionFld->m_nUseState == 2
      && !(unsigned __int8)((int (__fastcall *)(CPlayer *))v64->vfptr->Is_Battle_Mode)(v64) )
    {
      return 27i64;
    }
  }
  if ( v64->m_bInGuildBattle && v64->m_bTakeGravityStone )
  {
    if ( pFld->m_nContEffectType != -1 )
      return 14i64;
    if ( pFld->m_nTempEffectType != -1 && pFld->m_nTempEffectType < 150 && pFld->m_nTempEffectType != 37 )
      return 14i64;
  }
  if ( pPotionFld->m_nUseState == 3 && !v64->m_bCorpse )
    return 29i64;
  if ( CExtPotionBuf::IsExtPotionUse(&v64->m_PotionBufUse) )
  {
    if ( pFld->m_nTempEffectType == 53 )
      return 30i64;
    for ( j = 0; j < 2; ++j )
    {
      if ( _ContPotionData::IsLive((_ContPotionData *)&v64->m_PotionParam + j) )
      {
        n = _ContPotionData::GetEffectIndex((_ContPotionData *)&v64->m_PotionParam + j);
        v34 = CRecordData::GetRecord(&v63->m_tblPotionEffectData, n);
        v35 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 13, n);
        if ( v34 )
        {
          if ( *(_DWORD *)&v34[13].m_strCode[36] == pFld->m_nEffLimType
            && v35
            && pPotionFld->m_nPotionCheck < (signed int)v35[5].m_dwIndex
            && *(_DWORD *)&v34[13].m_strCode[36] != -1
            && pFld->m_nEffLimType != -1 )
          {
            return 31i64;
          }
        }
        if ( v34
          && *(_DWORD *)&v34[13].m_strCode[36] != pFld->m_nEffLimType
          && *(_DWORD *)&v34[13].m_strCode[36] == pFld->m_nEffLimType2
          && v35
          && pPotionFld->m_nPotionCheck < (signed int)v35[5].m_dwIndex
          && *(_DWORD *)&v34[13].m_strCode[36] != -1
          && pFld->m_nEffLimType2 != -1
          || v34
          && __PAIR__(*(_DWORD *)&v34[13].m_strCode[36], *(_DWORD *)&v34[13].m_strCode[36]) != __PAIR__(
                                                                                                 pFld->m_nEffLimType2,
                                                                                                 pFld->m_nEffLimType)
          && *(_DWORD *)&v34[13].m_strCode[40] == pFld->m_nEffLimType
          && v35
          && pPotionFld->m_nPotionCheck < (signed int)v35[5].m_dwIndex
          && *(_DWORD *)&v34[13].m_strCode[40] != -1
          && pFld->m_nEffLimType != -1
          || v34
          && __PAIR__(*(_DWORD *)&v34[13].m_strCode[36], *(_DWORD *)&v34[13].m_strCode[36]) != __PAIR__(
                                                                                                 pFld->m_nEffLimType2,
                                                                                                 pFld->m_nEffLimType)
          && *(_DWORD *)&v34[13].m_strCode[40] != pFld->m_nEffLimType
          && *(_DWORD *)&v34[13].m_strCode[40] == pFld->m_nEffLimType2
          && v35
          && pPotionFld->m_nPotionCheck < (signed int)v35[5].m_dwIndex
          && *(_DWORD *)&v34[13].m_strCode[40] != -1
          && pFld->m_nEffLimType2 != -1 )
        {
          return 31i64;
        }
      }
    }
  }
  if ( pFld->m_nTempEffectType == 54 && !(*v65)->m_ObjID.m_byID )
  {
    v36 = *v65;
    if ( (signed int)(unsigned __int8)CPlayerDB::GetTrunkSlotNum((CPlayerDB *)&v36[1].m_fOldPos[2]) <= 0 )
      return 33i64;
    if ( (signed int)(unsigned __int8)CPlayerDB::GetExtTrunkSlotNum((CPlayerDB *)&v36[1].m_fOldPos[2]) >= 40 )
      return 34i64;
  }
  if ( pFld->m_nTempEffectType == 55 && !(*v65)->m_ObjID.m_byID )
  {
    pOne = (CPlayer *)*v65;
    v11 = CRaceBuffManager::Instance();
    uiContinueCnt = CRaceBuffManager::GetRaceBuffLevel(v11, pOne);
    v12 = CRaceBuffManager::Instance();
    v39 = CRaceBuffManager::CancelPlayerRaceBuff(v12, pOne, RT_FAIL, uiContinueCnt);
    if ( (uiContinueCnt & 0x80000000) != 0 || (signed int)uiContinueCnt > 2 )
      return 35i64;
    if ( v39 == -3 || (float)(signed int)(uiContinueCnt + 1) > pFld->m_fTempValue[0] )
      return 36i64;
  }
  if ( pFld->m_nTempEffectType == 56 && !(*v65)->m_ObjID.m_byID )
  {
    pkPlayer = (CPlayer *)*v65;
    v13 = CRaceBuffManager::Instance();
    v41 = CRaceBuffManager::GetRaceBuffLevel(v13, pkPlayer);
    v14 = CRaceBuffManager::Instance();
    v42 = CRaceBuffManager::CancelPlayerRaceBuff(v14, pkPlayer, RT_LOSE, v41);
    if ( (v41 & 0x80000000) != 0 || (signed int)v41 > 2 )
      return 35i64;
    if ( v42 == -3 || (float)(signed int)(v41 + 1) > pFld->m_fTempValue[0] )
      return 36i64;
  }
  if ( pFld->m_nTempEffectType == 48
    || pFld->m_nTempEffectType == 49
    || pFld->m_nTempEffectType == 70
    || pFld->m_nTempEffectType == 71 )
  {
    v43 = *v65;
    if ( !CPotionMgr::CheckPotionUsableMap(v63, pPotionFld, v43->m_pCurMap) )
      return 37i64;
    if ( pFld->m_nTempEffectType != 48 && pFld->m_nTempEffectType != 70 )
    {
      if ( pFld->m_nTempEffectType == 49 || pFld->m_nTempEffectType == 71 )
      {
        if ( v64->m_Param.m_pGuild )
        {
          v54 = CPlayerDB::GetRaceCode(&v64->m_Param);
          v20 = CGuildRoomSystem::GetInstance();
          if ( v64->m_pCurMap == CGuildRoomSystem::GetMapData(v20, v54, 0)
            || (v55 = CPlayerDB::GetRaceCode(&v64->m_Param),
                v21 = CGuildRoomSystem::GetInstance(),
                v64->m_pCurMap == CGuildRoomSystem::GetMapData(v21, v55, 1)) )
          {
            v56 = v64->m_pUserDB;
            v57 = v64->m_ObjID.m_wIndex;
            v58 = v64->m_Param.m_pGuild;
            v22 = CGuildRoomSystem::GetInstance();
            CGuildRoomSystem::RoomOut(v22, v58->m_dwSerial, v57, v56->m_dwSerial);
          }
        }
      }
    }
    else if ( *(_QWORD *)&v43[23].m_SFCont[0][0].m_wszPlayerName[16] )
    {
      v49 = CPlayerDB::GetRaceCode((CPlayerDB *)&v43[1].m_fOldPos[2]);
      v15 = CGuildRoomSystem::GetInstance();
      v16 = CGuildRoomSystem::GetMapData(v15, v49, 0);
      if ( v43->m_pCurMap == v16
        || (v50 = CPlayerDB::GetRaceCode((CPlayerDB *)&v43[1].m_fOldPos[2]),
            v17 = CGuildRoomSystem::GetInstance(),
            v18 = CGuildRoomSystem::GetMapData(v17, v50, 1),
            v43->m_pCurMap == v18) )
      {
        v51 = *(_QWORD *)&v43[1].m_nScreenPos[0];
        v52 = v43->m_ObjID.m_wIndex;
        v53 = *(_QWORD *)&v43[23].m_SFCont[0][0].m_wszPlayerName[16];
        v19 = CGuildRoomSystem::GetInstance();
        CGuildRoomSystem::RoomOut(v19, *(_DWORD *)(v53 + 12), v52, *(_DWORD *)(v51 + 92));
      }
    }
  }
  if ( !CPotionMgr::CheckPotionUsableMap(v63, pPotionFld, v64->m_pCurMap) )
    return 37i64;
  if ( !(*v65)->m_ObjID.m_byID && pFld->m_nTempEffectType == 73 )
  {
    v44 = (CPlayer *)*v65;
    if ( !v44 )
      return 25i64;
    v59 = ((int (__fastcall *)(CPlayer *))v44->vfptr->GetHP)(v44);
    v60 = v44->vfptr;
    v23 = ((int (__fastcall *)(CPlayer *))v60->GetMaxHP)(v44);
    if ( v59 == v23 )
    {
      v61 = CPlayer::GetFP(v44);
      v24 = CPlayer::GetMaxFP(v44);
      if ( v61 == v24 )
      {
        v62 = CPlayer::GetSP(v44);
        v25 = CPlayer::GetMaxSP(v44);
        if ( v62 == v25 )
          return 46i64;
      }
    }
  }
  if ( (*v65)->m_ObjID.m_byID || pFld->m_nTempEffectType != 74 )
    goto LABEL_176;
  v45 = 0;
  v46 = *v65;
  if ( !v46 )
    return 25i64;
  for ( k = 0; k < 8; ++k )
  {
    v48 = (char *)v46->m_SFCont + 48 * k;
    if ( *v48 )
      ++v45;
  }
  if ( v45 )
LABEL_176:
    result = 0i64;
  else
    result = 47i64;
  return result;
}
