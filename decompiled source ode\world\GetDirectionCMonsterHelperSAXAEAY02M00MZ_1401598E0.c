/*
 * Function: ?GetDirection@CMonsterHelper@@SAXAEAY02M00M@Z
 * Address: 0x1401598E0
 */

void __usercall CMonsterHelper::GetDirection(float (*cur)[3]@<rcx>, float (*tar)[3]@<rdx>, float (*out)[3]@<r8>, float deg@<xmm3>, float a5@<xmm0>)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  float v7; // xmm0_4@4
  __int64 v8; // [sp+0h] [bp-38h]@1
  float v9; // [sp+20h] [bp-18h]@4
  float v10; // [sp+24h] [bp-14h]@4
  float v11; // [sp+28h] [bp-10h]@4
  float *plr; // [sp+40h] [bp+8h]@1
  float (*v13)[3]; // [sp+50h] [bp+18h]@1

  v13 = out;
  plr = (float *)cur;
  v5 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  CMonsterHelper::GetAngle((float *)tar, plr);
  v9 = a5;
  cos(a5);
  v10 = a5 * deg;
  v7 = v9;
  sin(v9);
  v11 = v7 * deg;
  *(float *)&v13 = *plr + v10;
  (*v13)[2] = plr[2] + v11;
  (*v13)[1] = plr[1];
}
