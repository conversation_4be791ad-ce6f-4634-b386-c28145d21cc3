/*
 * Function: ?CheckBuyComplete@CUnmannedTraderUserInfo@@QEAAEPEAVCPlayer@@K@Z
 * Address: 0x140356460
 */

char __fastcall CUnmannedTraderUserInfo::CheckBuyComplete(CUnmannedTraderUserInfo *this, CPlayer *pkBuyer, unsigned int dwPrice)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  CPlayer *v7; // [sp+38h] [bp+10h]@1

  v7 = pkBuyer;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( dwPrice <= CPlayerDB::GetDalant(&pkBuyer->m_Param) )
  {
    if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&v7->m_Param.m_dbInven.m_nListNum) == 255 )
      result = 42;
    else
      result = 0;
  }
  else
  {
    result = 43;
  }
  return result;
}
