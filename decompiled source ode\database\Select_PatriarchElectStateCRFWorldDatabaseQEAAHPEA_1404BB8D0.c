/*
 * Function: ?Select_PatriarchElectState@CRFWorldDatabase@@QEAAHPEAU_sel_patriarch_elect_state@@@Z
 * Address: 0x1404BB8D0
 */

signed __int64 __fastcall CRFWorldDatabase::Select_PatriarchElectState(CRFWorldDatabase *this, _sel_patriarch_elect_state *pSheet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v5; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@22
  SQLLEN v8; // [sp+38h] [bp-150h]@22
  __int16 v9; // [sp+44h] [bp-144h]@9
  char _Dest[256]; // [sp+60h] [bp-128h]@4
  unsigned __int8 v11; // [sp+164h] [bp-24h]@16
  int j; // [sp+168h] [bp-20h]@22
  unsigned __int8 v13; // [sp+16Ch] [bp-1Ch]@27
  unsigned __int64 v14; // [sp+178h] [bp-10h]@4
  CRFWorldDatabase *v15; // [sp+190h] [bp+8h]@1
  _sel_patriarch_elect_state *v16; // [sp+198h] [bp+10h]@1

  v16 = pSheet;
  v15 = this;
  v2 = &v5;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v14 = (unsigned __int64)&v5 ^ _security_cookie;
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0xFFui64);
  sprintf_s<256>((char (*)[256])_Dest, "{ CALL pSelect_Patriarch_Elect_20080708 ( '%s' ) }", pSheet);
  if ( v15->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v15->vfptr, _Dest);
  if ( v15->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v15->vfptr) )
  {
    v9 = SQLExecDirect_0(v15->m_hStmtSelect, _Dest, -3);
    if ( v9 && v9 != 1 )
    {
      if ( v9 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v15->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v9, _Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v9, v15->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v9 = SQLFetch_0(v15->m_hStmtSelect);
      if ( v9 && v9 != 1 )
      {
        v11 = 0;
        if ( v9 == 100 )
        {
          v11 = 2;
        }
        else
        {
          SQLStmt = v15->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v9, _Dest, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v9, v15->m_hStmtSelect);
          v11 = 1;
        }
        if ( v15->m_hStmtSelect )
          SQLCloseCursor_0(v15->m_hStmtSelect);
        result = v11;
      }
      else
      {
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v15->m_hStmtSelect, 1u, -18, &v16->dwSerial, 0i64, &v8);
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v15->m_hStmtSelect, 2u, -6, &v16->byProcType, 0i64, &v8);
        for ( j = 0; j < 3; ++j )
        {
          StrLen_or_IndPtr = &v8;
          SQLStmt = 0i64;
          v9 = SQLGetData_0(v15->m_hStmtSelect, 3 * j + 3, -18, &v16->dwNonVoteCnt[j], 0i64, &v8);
          StrLen_or_IndPtr = &v8;
          SQLStmt = 0i64;
          v9 = SQLGetData_0(v15->m_hStmtSelect, 3 * j + 4, -18, &v16->dwVoteCnt[j], 0i64, &v8);
          StrLen_or_IndPtr = &v8;
          SQLStmt = 0i64;
          v9 = SQLGetData_0(v15->m_hStmtSelect, 3 * j + 5, -18, &v16->dwHighGradeNum[j], 0i64, &v8);
        }
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v15->m_hStmtSelect, 0xCu, -18, &v16->dwResetServerToken, 0i64, &v8);
        if ( v9 && v9 != 1 )
        {
          v13 = 0;
          if ( v9 == 100 )
          {
            v13 = 2;
          }
          else
          {
            SQLStmt = v15->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v9, _Dest, "SQLGetData", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v9, v15->m_hStmtSelect);
            v13 = 1;
          }
          if ( v15->m_hStmtSelect )
            SQLCloseCursor_0(v15->m_hStmtSelect);
          result = v13;
        }
        else
        {
          if ( v15->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v15->vfptr, "%s Success", _Dest);
          if ( v15->m_hStmtSelect )
            SQLCloseCursor_0(v15->m_hStmtSelect);
          result = 0i64;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v15->vfptr, "ReConnectDataBase Fail. Query : %s", _Dest);
    result = 1i64;
  }
  return result;
}
