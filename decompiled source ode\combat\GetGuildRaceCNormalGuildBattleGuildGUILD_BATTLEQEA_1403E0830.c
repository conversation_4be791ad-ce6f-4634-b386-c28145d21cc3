/*
 * Function: ?GetGuildRace@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAEXZ
 * Address: 0x1403E0830
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildRace(GUILD_BATTLE::CNormalGuildBattleGuild *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // [sp+0h] [bp-18h]@1
  GUILD_BATTLE::CNormalGuildBattleGuild *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  if ( v5->m_pkGuild )
    v4 = v5->m_pkGuild->m_byRace;
  else
    v4 = -1;
  return v4;
}
