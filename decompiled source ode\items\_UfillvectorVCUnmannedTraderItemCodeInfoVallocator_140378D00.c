/*
 * Function: ?_Ufill@?$vector@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@IEAAPEAVCUnmannedTraderItemCodeInfo@@PEAV3@_KAEBV3@@Z
 * Address: 0x140378D00
 */

CUnmannedTraderItemCodeInfo *__fastcall std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Ufill(std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *this, CUnmannedTraderItemCodeInfo *_Ptr, unsigned __int64 _Count, CUnmannedTraderItemCodeInfo *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v8; // [sp+30h] [bp+8h]@1
  CUnmannedTraderItemCodeInfo *_First; // [sp+38h] [bp+10h]@1
  unsigned __int64 _Counta; // [sp+40h] [bp+18h]@1

  _Counta = _Count;
  _First = _Ptr;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderItemCodeInfo *,unsigned __int64,CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(
    _Ptr,
    _Count,
    _Val,
    &v8->_Alval);
  return &_First[_Counta];
}
