/*
 * Function: ?DataInit@_qry_case_all_store_limit_item@@QEAAXXZ
 * Address: 0x14034BD30
 */

void __fastcall _qry_case_all_store_limit_item::DataInit(_qry_case_all_store_limit_item *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@4
  _qry_case_all_store_limit_item *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->dwCount = 0;
  for ( j = 0; j < v5->dwMax; ++j )
    _qry_case_all_store_limit_item::__list::init(&v5->pStoreList[j]);
}
