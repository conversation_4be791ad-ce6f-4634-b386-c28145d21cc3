/*
 * Function: ?Select_PvpPointLimitInfo@CRFWorldDatabase@@QEAAEKAEAU_pvppointlimit_info@@@Z
 * Address: 0x1404AFC70
 */

char __fastcall CRFWorldDatabase::Select_PvpPointLimitInfo(CRFWorldDatabase *this, unsigned int dwSerial, _pvppointlimit_info *kInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-208h]@1
  void *SQLStmt; // [sp+20h] [bp-1E8h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-1E0h]@22
  char Dest; // [sp+40h] [bp-1C8h]@4
  SQLLEN v10; // [sp+158h] [bp-B0h]@22
  __int16 v11; // [sp+164h] [bp-A4h]@9
  int v12; // [sp+168h] [bp-A0h]@4
  __int16 TargetValue; // [sp+178h] [bp-90h]@22
  unsigned __int16 v14; // [sp+17Ah] [bp-8Eh]@22
  unsigned __int16 v15; // [sp+17Ch] [bp-8Ch]@22
  unsigned __int16 v16; // [sp+17Eh] [bp-8Ah]@22
  unsigned __int16 v17; // [sp+180h] [bp-88h]@22
  unsigned __int16 v18; // [sp+182h] [bp-86h]@22
  tm _Tm; // [sp+1A8h] [bp-60h]@22
  __int64 v20; // [sp+1D8h] [bp-30h]@22
  char v21; // [sp+1E0h] [bp-28h]@16
  unsigned __int64 v22; // [sp+1F0h] [bp-18h]@4
  CRFWorldDatabase *v23; // [sp+210h] [bp+8h]@1
  _pvppointlimit_info *v24; // [sp+220h] [bp+18h]@1

  v24 = kInfo;
  v23 = this;
  v3 = &v6;
  for ( i = 128i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v22 = (unsigned __int64)&v6 ^ _security_cookie;
  v12 = 0;
  sprintf(&Dest, "{ CALL pSelect_ppliinfo( %u ) }", dwSerial);
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v23->vfptr, &Dest);
  if ( v23->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v23->vfptr) )
  {
    v11 = SQLExecDirect_0(v23->m_hStmtSelect, &Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v23->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v11, &Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v11, v23->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v11 = SQLFetch_0(v23->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        v21 = 0;
        if ( v11 == 100 )
        {
          v21 = 2;
        }
        else
        {
          SQLStmt = v23->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v11, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v11, v23->m_hStmtSelect);
          v21 = 1;
        }
        if ( v23->m_hStmtSelect )
          SQLCloseCursor_0(v23->m_hStmtSelect);
        result = v21;
      }
      else
      {
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v23->m_hStmtSelect, 1u, 93, &TargetValue, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v23->m_hStmtSelect, 2u, -7, &v24->bUseUp, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v23->m_hStmtSelect, 3u, -6, &v24->byLimitRate, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v23->m_hStmtSelect, 4u, 8, &v24->dOriginalPoint, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v23->m_hStmtSelect, 5u, 8, &v24->dLimitPoint, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v23->m_hStmtSelect, 6u, 8, &v24->dUsePoint, 0i64, &v10);
        _Tm.tm_year = TargetValue - 1900;
        _Tm.tm_mon = v14 - 1;
        _Tm.tm_mday = v15;
        _Tm.tm_hour = v16;
        _Tm.tm_min = v17;
        _Tm.tm_sec = v18;
        _Tm.tm_isdst = -1;
        v20 = mktime_3(&_Tm);
        if ( v20 == -1 )
          v20 = 0i64;
        v24->tUpdatedate = v20;
        if ( v23->m_hStmtSelect )
          SQLCloseCursor_0(v23->m_hStmtSelect);
        if ( v23->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v23->vfptr, "%s Success", &Dest);
        result = 0;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v23->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
