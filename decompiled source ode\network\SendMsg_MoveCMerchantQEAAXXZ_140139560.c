/*
 * Function: ?SendMsg_Move@CMerchant@@QEAAXXZ
 * Address: 0x140139560
 */

void __fastcall CMerchant::SendMsg_Move(CMerchant *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-98h]@1
  char szMsg[4]; // [sp+38h] [bp-60h]@4
  __int16 pShort; // [sp+3Ch] [bp-5Ch]@4
  __int16 v6; // [sp+42h] [bp-56h]@4
  __int16 v7; // [sp+44h] [bp-54h]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v9; // [sp+65h] [bp-33h]@4
  unsigned __int64 v10; // [sp+80h] [bp-18h]@4
  CMerchant *v11; // [sp+A0h] [bp+8h]@1

  v11 = this;
  v1 = &v3;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = (unsigned __int64)&v3 ^ _security_cookie;
  *(_DWORD *)szMsg = v11->m_dwObjSerial;
  FloatToShort(v11->m_fCurPos, &pShort, 3);
  v6 = (signed int)ffloor(v11->m_fTarPos[0]);
  v7 = (signed int)ffloor(v11->m_fTarPos[2]);
  pbyType = 4;
  v9 = 6;
  CGameObject::CircleReport((CGameObject *)&v11->vfptr, &pbyType, szMsg, 14, 0);
}
