/*
 * Function: j_?_Destroy@?$vector@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@IEAAXPEAVCUnmannedTraderRegistItemInfo@@0@Z
 * Address: 0x14000CE9B
 */

void __fastcall std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Destroy(std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *this, CUnmannedTraderRegistItemInfo *_First, CUnmannedTraderRegistItemInfo *_Last)
{
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Destroy(
    this,
    _First,
    _Last);
}
