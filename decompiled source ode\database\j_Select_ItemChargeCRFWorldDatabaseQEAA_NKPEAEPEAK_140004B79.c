/*
 * Function: j_?Select_ItemCharge@CRFWorldDatabase@@QEAA_NKPEAEPEAKPEA_K11PEAH@Z
 * Address: 0x140004B79
 */

bool __fastcall CRFWorldDatabase::Select_ItemCharge(CRFWorldDatabase *this, unsigned int dwAvatorSerial, char *pbyType, unsigned int *pDwItemCode_K, unsigned __int64 *pDwItemCode_D, unsigned int *pDwItemCode_U, unsigned int *pDwItemChargeIndex, int *piTime)
{
  return CRFWorldDatabase::Select_ItemCharge(
           this,
           dwAvatorSerial,
           pbyType,
           pDwItemCode_K,
           pDwItemCode_D,
           pDwItemCode_U,
           pDwItemChargeIndex,
           piTime);
}
