/*
 * Function: ??$unchecked_uninitialized_fill_n@PEAPEAU_PVP_RANK_PACKED_DATA@@_KPEAU1@V?$allocator@PEAU_PVP_RANK_PACKED_DATA@@@std@@@stdext@@YAXPEAPEAU_PVP_RANK_PACKED_DATA@@_KAEBQEAU1@AEAV?$allocator@PEAU_PVP_RANK_PACKED_DATA@@@std@@@Z
 * Address: 0x140334660
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<_PVP_RANK_PACKED_DATA * *,unsigned __int64,_PVP_RANK_PACKED_DATA *,std::allocator<_PVP_RANK_PACKED_DATA *>>(_PVP_RANK_PACKED_DATA **_First, unsigned __int64 _Count, _PVP_RANK_PACKED_DATA *const *_Val, std::allocator<_PVP_RANK_PACKED_DATA *> *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v7; // [sp+30h] [bp-18h]@4
  std::_Scalar_ptr_iterator_tag v8; // [sp+31h] [bp-17h]@4
  _PVP_RANK_PACKED_DATA **__formal; // [sp+50h] [bp+8h]@1
  unsigned __int64 _Counta; // [sp+58h] [bp+10h]@1
  _PVP_RANK_PACKED_DATA **_Vala; // [sp+60h] [bp+18h]@1
  std::allocator<_PVP_RANK_PACKED_DATA *> *v12; // [sp+68h] [bp+20h]@1

  v12 = _Al;
  _Vala = (_PVP_RANK_PACKED_DATA **)_Val;
  _Counta = _Count;
  __formal = _First;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v7, 0, sizeof(v7));
  v8 = std::_Ptr_cat<_PVP_RANK_PACKED_DATA * *,_PVP_RANK_PACKED_DATA * *>(&__formal, &__formal);
  std::_Uninit_fill_n<_PVP_RANK_PACKED_DATA * *,unsigned __int64,_PVP_RANK_PACKED_DATA *,std::allocator<_PVP_RANK_PACKED_DATA *>>(
    __formal,
    _Counta,
    _Vala,
    v12,
    v8,
    v7);
}
