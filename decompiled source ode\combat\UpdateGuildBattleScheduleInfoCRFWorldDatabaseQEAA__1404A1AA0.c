/*
 * Function: ?UpdateGuildBattleScheduleInfo@CRFWorldDatabase@@QEAA_NKKE_JG@Z
 * Address: 0x1404A1AA0
 */

bool __fastcall CRFWorldDatabase::UpdateGuildBattleScheduleInfo(CRFWorldDatabase *this, unsigned int dwID, unsigned int dwSLID, char byState, __int64 tStartTime, unsigned __int16 wTurmMin)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  int v8; // eax@4
  __int64 v10; // [sp+0h] [bp-4C8h]@1
  int v11; // [sp+20h] [bp-4A8h]@4
  int v12; // [sp+28h] [bp-4A0h]@4
  int v13; // [sp+30h] [bp-498h]@4
  int v14; // [sp+38h] [bp-490h]@4
  int v15; // [sp+40h] [bp-488h]@4
  int v16; // [sp+48h] [bp-480h]@4
  int v17; // [sp+50h] [bp-478h]@4
  char Dest; // [sp+70h] [bp-458h]@4
  ATL::CTime v19; // [sp+488h] [bp-40h]@4
  int v20; // [sp+4A0h] [bp-28h]@4
  int v21; // [sp+4A4h] [bp-24h]@4
  int v22; // [sp+4A8h] [bp-20h]@4
  int v23; // [sp+4ACh] [bp-1Ch]@4
  int v24; // [sp+4B0h] [bp-18h]@4
  int v25; // [sp+4B4h] [bp-14h]@4
  unsigned __int64 v26; // [sp+4B8h] [bp-10h]@4
  CRFWorldDatabase *v27; // [sp+4D0h] [bp+8h]@1
  unsigned int v28; // [sp+4D8h] [bp+10h]@1
  char v29; // [sp+4E8h] [bp+20h]@1

  v29 = byState;
  v28 = dwID;
  v27 = this;
  v6 = &v10;
  for ( i = 304i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v26 = (unsigned __int64)&v10 ^ _security_cookie;
  ATL::CTime::CTime(&v19, tStartTime);
  v20 = wTurmMin;
  v21 = ATL::CTime::GetSecond(&v19);
  v22 = ATL::CTime::GetMinute(&v19);
  v23 = ATL::CTime::GetHour(&v19);
  v24 = ATL::CTime::GetDay(&v19);
  v25 = ATL::CTime::GetMonth(&v19);
  v8 = ATL::CTime::GetYear(&v19);
  v17 = v20;
  v16 = v21;
  v15 = v22;
  v14 = v23;
  v13 = v24;
  v12 = v25;
  v11 = v8;
  sprintf(
    &Dest,
    "{ CALL pUpdate_ScheduleInfo( %u, %u, '%04d-%02d-%02d %02d:%02d:%02d', %u ) }",
    v28,
    (unsigned __int8)v29);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v27->vfptr, &Dest, 1);
}
