/*
 * Function: ?SFContInsertMessage@CPlayer@@UEAAXEE_NPEAV1@@Z
 * Address: 0x140053D30
 */

void __fastcall CPlayer::SFContInsertMessage(CPlayer *this, char byContCode, char byListIndex, bool bAuraSkill, CPlayer *pPlayerAct)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v7; // ax@10
  unsigned __int16 v8; // ax@11
  char v9; // al@14
  __int64 v10; // [sp+0h] [bp-68h]@1
  unsigned int dwPlayerSerial; // [sp+20h] [bp-48h]@10
  char *wszPlayerName; // [sp+28h] [bp-40h]@10
  unsigned __int16 wDurSec; // [sp+30h] [bp-38h]@14
  _sf_continous *pSF_Cont; // [sp+40h] [bp-28h]@4
  char *v15; // [sp+48h] [bp-20h]@11
  unsigned int v16; // [sp+50h] [bp-18h]@11
  CPlayer *v17; // [sp+70h] [bp+8h]@1
  char v18; // [sp+78h] [bp+10h]@1
  char v19; // [sp+80h] [bp+18h]@1
  bool v20; // [sp+88h] [bp+20h]@1

  v20 = bAuraSkill;
  v19 = byListIndex;
  v18 = byContCode;
  v17 = this;
  v5 = &v10;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  pSF_Cont = 0i64;
  if ( bAuraSkill )
    pSF_Cont = &v17->m_SFContAura[(unsigned __int8)byContCode][(unsigned __int8)byListIndex];
  else
    pSF_Cont = &v17->m_SFCont[(unsigned __int8)byContCode][(unsigned __int8)byListIndex];
  CEquipItemSFAgent::StartContSF(&v17->EquipItemSFAgent, pSF_Cont);
  if ( _IsXmasSnowEffect(pSF_Cont) )
    v17->m_bSnowMan = 1;
  if ( pPlayerAct )
  {
    v15 = CPlayerDB::GetCharNameW(&pPlayerAct->m_Param);
    v16 = CPlayerDB::GetCharSerial(&pPlayerAct->m_Param);
    v8 = CCharacter::CalcEffectBit((CCharacter *)&v17->vfptr, pSF_Cont->m_byEffectCode, pSF_Cont->m_wEffectIndex);
    wszPlayerName = v15;
    dwPlayerSerial = v16;
    CPlayer::SendMsg_AddEffect(v17, v8, pSF_Cont->m_byLv, pSF_Cont->m_wDurSec, v16, v15);
  }
  else
  {
    v7 = CCharacter::CalcEffectBit((CCharacter *)&v17->vfptr, pSF_Cont->m_byEffectCode, pSF_Cont->m_wEffectIndex);
    wszPlayerName = 0i64;
    dwPlayerSerial = 0;
    CPlayer::SendMsg_AddEffect(v17, v7, pSF_Cont->m_byLv, pSF_Cont->m_wDurSec, 0, 0i64);
  }
  if ( v17->m_pUserDB )
  {
    if ( !v20 )
    {
      v9 = pSF_Cont->m_byLv - 1;
      wDurSec = pSF_Cont->m_wDurSec;
      LOBYTE(wszPlayerName) = v9;
      LOWORD(dwPlayerSerial) = pSF_Cont->m_wEffectIndex;
      CUserDB::Update_SFContInsert(v17->m_pUserDB, v18, v19, pSF_Cont->m_byEffectCode, dwPlayerSerial, v9, wDurSec);
    }
  }
}
