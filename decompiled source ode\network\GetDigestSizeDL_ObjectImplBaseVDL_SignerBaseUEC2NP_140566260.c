/*
 * Function: ?GetDigestSize@?$DL_ObjectImplBase@V?$DL_SignerBase@UEC2NPoint@CryptoPP@@@CryptoPP@@U?$DL_SignatureSchemeOptions@V?$DL_SS@U?$DL_Keys_ECDSA@VEC2N@CryptoPP@@@CryptoPP@@V?$DL_Algorithm_ECDSA@VEC2N@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@H@CryptoPP@@U?$DL_Keys_ECDSA@VEC2N@CryptoPP@@@2@V?$DL_Algorithm_ECDSA@VEC2N@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@@2@V?$DL_PrivateKey_WithSignaturePairwiseConsistencyTest@V?$DL_PrivateKey_EC@VEC2N@CryptoPP@@@CryptoPP@@U?$ECDSA@VEC2N@CryptoPP@@VSHA1@2@@2@@2@@CryptoPP@@MEBA_KXZ
 * Address: 0x140566260
 */

signed __int64 CryptoPP::DL_ObjectImplBase<CryptoPP::DL_SignerBase<CryptoPP::EC2NPoint>,CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DL_SS<CryptoPP::DL_Keys_ECDSA<CryptoPP::EC2N>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::EC2N>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1,int>,CryptoPP::DL_Keys_ECDSA<CryptoPP::EC2N>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::EC2N>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>,CryptoPP::DL_PrivateKey_WithSignaturePairwiseConsistencyTest<CryptoPP::DL_PrivateKey_EC<CryptoPP::EC2N>,CryptoPP::ECDSA<CryptoPP::EC2N,CryptoPP::SHA1>>>::GetDigestSize()
{
  return 20i64;
}
