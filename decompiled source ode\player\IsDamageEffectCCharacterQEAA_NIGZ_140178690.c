/*
 * Function: ?IsDamageEffect@CCharacter@@QEAA_NIG@Z
 * Address: 0x140178690
 */

char __fastcall CCharacter::IsDamageEffect(CCharacter *this, unsigned int uiEffectCodeType, unsigned __int16 wEffectIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  char v7; // [sp+20h] [bp-28h]@4
  _base_fld *v8; // [sp+28h] [bp-20h]@4
  _base_fld *v9; // [sp+30h] [bp-18h]@4
  unsigned int v10; // [sp+38h] [bp-10h]@4

  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = 0;
  v8 = 0i64;
  v9 = 0i64;
  v10 = uiEffectCodeType;
  if ( uiEffectCodeType )
  {
    if ( v10 == 1 )
    {
      v9 = CRecordData::GetRecord(&stru_1799C8410 + 1, wEffectIndex);
      if ( v9 && *(_DWORD *)&v9[12].m_strCode[48] )
        v7 = 1;
    }
    else if ( v10 == 2 )
    {
      v8 = CRecordData::GetRecord(&stru_1799C8410 + 2, wEffectIndex);
      if ( v8 )
      {
        if ( *(_DWORD *)&v8[13].m_strCode[32] )
          v7 = 1;
      }
    }
  }
  else
  {
    v8 = CRecordData::GetRecord(&stru_1799C8410, wEffectIndex);
    if ( v8 && !*(_DWORD *)&v8[13].m_strCode[32] )
      v7 = 1;
  }
  return v7;
}
