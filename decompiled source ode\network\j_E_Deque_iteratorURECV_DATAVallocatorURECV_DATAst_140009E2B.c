/*
 * Function: j_??E?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEAA?AV01@H@Z
 * Address: 0x140009E2B
 */

std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator++(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *result, int __formal)
{
  return std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator++(this, result, __formal);
}
