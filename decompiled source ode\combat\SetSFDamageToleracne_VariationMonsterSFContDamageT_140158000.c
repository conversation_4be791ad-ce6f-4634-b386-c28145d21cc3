/*
 * Function: ?SetSFDamageToleracne_Variation@MonsterSFContDamageToleracne@@QEAAXM@Z
 * Address: 0x140158000
 */

void __fastcall MonsterSFContDamageToleracne::SetSFDamageToleracne_Variation(MonsterSFContDamageToleracne *this, float fAddValue)
{
  this->m_fToleranceProb = this->m_fToleranceProb + fAddValue;
  if ( this->m_fToleranceProb > this->m_fToleranceProbMax )
    this->m_fToleranceProb = this->m_fToleranceProbMax;
  if ( this->m_fToleranceProb < 0.0 )
    LODWORD(this->m_fToleranceProb) = 0;
}
