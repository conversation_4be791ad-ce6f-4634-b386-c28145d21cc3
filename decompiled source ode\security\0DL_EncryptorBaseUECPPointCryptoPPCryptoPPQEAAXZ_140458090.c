/*
 * Function: ??0?$DL_EncryptorBase@UECPPoint@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x140458090
 */

void __fastcall CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint>::DL_EncryptorBase<CryptoPP::ECPPoint>(CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint> *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>>::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>>((CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> > *)&v4->vfptr);
}
