/*
 * Function: ?ct_SetGuildGradeByName@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140294F20
 */

bool __fastcall ct_SetGuildGradeByName(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@8
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6 )
  {
    if ( s_nWordCount >= 2 )
    {
      v5 = 0;
      v5 = atoi(s_pwszDstCheat[1]);
      result = CPlayer::dev_SetGuildGradeByName(v6, s_pwszDstCheat[0], v5);
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
