/*
 * Function: _IsNonwritableInCurrentImage
 * Address: 0x1404DE5F0
 */

__int64 __fastcall IsNonwritableInCurrentImage(char *pTarget)
{
  __int64 result; // rax@2
  _IMAGE_SECTION_HEADER *v2; // [sp+28h] [bp-30h]@3
  char *v3; // [sp+60h] [bp+8h]@1

  v3 = pTarget;
  if ( (unsigned int)ValidateImageBase((char *)&_ImageBase) )
  {
    v2 = FindPESection((char *)&_ImageBase, v3 - (char *)&_ImageBase);
    if ( v2 )
      result = (v2->Characteristics & 0x80000000) == 0;
    else
      result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
