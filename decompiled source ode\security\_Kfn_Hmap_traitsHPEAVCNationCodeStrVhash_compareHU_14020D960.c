/*
 * Function: ?_Kfn@?$_Hmap_traits@HPEAVCNationCodeStr@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@@std@@$0A@@stdext@@SAAEBHAEBU?$pair@$$CBHPEAVCNationCodeStr@@@std@@@Z
 * Address: 0x14020D960
 */

stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationCodeStr *> >,0> *__fastcall stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationCodeStr *>>,0>::_Kfn(stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationCodeStr *> >,0> *this, std::pair<int const ,CNationCodeStr *> *_Val)
{
  return this;
}
