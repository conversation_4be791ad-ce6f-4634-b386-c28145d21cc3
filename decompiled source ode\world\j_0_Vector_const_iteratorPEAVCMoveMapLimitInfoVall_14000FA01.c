/*
 * Function: j_??0?$_Vector_const_iterator@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEAA@AEBV01@@Z
 * Address: 0x14000FA01
 */

void __fastcall std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *__that)
{
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(
    this,
    __that);
}
