/*
 * Function: j_??1?$_Ranit@VCUnmannedTraderRegistItemInfo@@_JPEBV1@AEBV1@@std@@QEAA@XZ
 * Address: 0x14000288D
 */

void __fastcall std::_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &>::~_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &>(std::_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &> *this)
{
  std::_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &>::~_Ranit<CUnmannedTraderRegistItemInfo,__int64,CUnmannedTraderRegistItemInfo const *,CUnmannedTraderRegistItemInfo const &>(this);
}
