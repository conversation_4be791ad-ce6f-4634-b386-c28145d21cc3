/*
 * Function: ?ManageAcceptORRefuseGuildBattle@CGuild@@QEAAE_N@Z
 * Address: 0x140259EF0
 */

char __fastcall CGuild::ManageAcceptORRefuseGuildBattle(CGuild *this, bool bAccept)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CGuildBattleController *v5; // rax@7
  CHonorGuild *v6; // rax@11
  CMoneySupplyMgr *v7; // rax@12
  CHonorGuild *v8; // rax@13
  CMoneySupplyMgr *v9; // rax@14
  __int64 v10; // [sp+0h] [bp-58h]@1
  char byNumber; // [sp+20h] [bp-38h]@7
  unsigned int dwMapInx; // [sp+28h] [bp-30h]@7
  char v13; // [sp+30h] [bp-28h]@4
  CGuild *v14; // [sp+38h] [bp-20h]@13
  CGuild *v15; // [sp+40h] [bp-18h]@13
  CGuild *v16; // [sp+48h] [bp-10h]@14
  CGuild *v17; // [sp+60h] [bp+8h]@1

  v17 = this;
  v2 = &v10;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = 0;
  if ( v17->m_GuildBattleSugestMatter.eState == 3 )
  {
    if ( bAccept )
    {
      v5 = CGuildBattleController::Instance();
      dwMapInx = v17->m_GuildBattleSugestMatter.dwMapIdx;
      byNumber = v17->m_GuildBattleSugestMatter.dwNumber;
      v13 = CGuildBattleController::Add(
              v5,
              v17->m_GuildBattleSugestMatter.pkSrc,
              v17->m_GuildBattleSugestMatter.pkDest,
              v17->m_GuildBattleSugestMatter.dwStartTime,
              byNumber,
              dwMapInx);
      if ( v13 )
      {
        if ( v13 == 112 )
          v13 = 119;
        CGuild::PushDQSInGuildBattleCost(v17->m_GuildBattleSugestMatter.pkSrc);
        CGuild::SendMsg_ApplyGuildBattleResultInform(v17->m_GuildBattleSugestMatter.pkSrc, v13, v17->m_wszName);
      }
      else
      {
        CGuild::PushDQSDestGuildOutputGuildBattleCost(v17);
        v6 = CHonorGuild::Instance();
        if ( CHonorGuild::CheckHonorGuild(v6, v17->m_byRace, v17->m_dwSerial) )
        {
          v7 = CMoneySupplyMgr::Instance();
          CMoneySupplyMgr::UpdateHonorGuildMoneyData(v7, 1, v17->m_byRace, 0x1388u);
        }
        v14 = v17->m_GuildBattleSugestMatter.pkSrc;
        v15 = v17->m_GuildBattleSugestMatter.pkSrc;
        v8 = CHonorGuild::Instance();
        if ( CHonorGuild::CheckHonorGuild(v8, v15->m_byRace, v14->m_dwSerial) )
        {
          v16 = v17->m_GuildBattleSugestMatter.pkSrc;
          v9 = CMoneySupplyMgr::Instance();
          CMoneySupplyMgr::UpdateHonorGuildMoneyData(v9, 1, v16->m_byRace, 0x1388u);
        }
      }
    }
    else
    {
      CGuild::SendMsg_GuildBattleRefused(v17->m_GuildBattleSugestMatter.pkSrc, v17->m_wszName);
      CGuild::PushDQSInGuildBattleCost(v17->m_GuildBattleSugestMatter.pkSrc);
    }
    _guild_battle_suggest_matter::Clear(&v17->m_GuildBattleSugestMatter.pkSrc->m_GuildBattleSugestMatter);
    _guild_battle_suggest_matter::Clear(&v17->m_GuildBattleSugestMatter);
    result = v13;
  }
  else
  {
    result = 111;
  }
  return result;
}
