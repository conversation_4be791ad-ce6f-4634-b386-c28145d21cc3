/*
 * Function: ?SendMsg_Destroy@CGuardTower@@QEAAXE@Z
 * Address: 0x140130780
 */

void __fastcall CGuardTower::SendMsg_Destroy(CGuardTower *this, char byDesType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-88h]@1
  char szMsg[2]; // [sp+38h] [bp-50h]@4
  unsigned int v6; // [sp+3Ah] [bp-4Eh]@4
  char v7; // [sp+3Eh] [bp-4Ah]@4
  unsigned int v8; // [sp+3Fh] [bp-49h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v10; // [sp+65h] [bp-23h]@4
  CGuardTower *v11; // [sp+90h] [bp+8h]@1

  v11 = this;
  v2 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  *(_WORD *)szMsg = v11->m_ObjID.m_wIndex;
  v6 = v11->m_dwObjSerial;
  v7 = byDesType;
  v8 = v11->m_dwMasterSerial;
  pbyType = 3;
  v10 = 27;
  CGameObject::CircleReport((CGameObject *)&v11->vfptr, &pbyType, szMsg, 11, 0);
}
