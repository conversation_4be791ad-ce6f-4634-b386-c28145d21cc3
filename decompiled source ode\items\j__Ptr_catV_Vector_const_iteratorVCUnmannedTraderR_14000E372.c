/*
 * Function: j_??$_Ptr_cat@V?$_Vector_const_iterator@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@PEAVCUnmannedTraderRegistItemInfo@@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAV?$_Vector_const_iterator@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@0@AEAPEAVCUnmannedTraderRegistItemInfo@@@Z
 * Address: 0x14000E372
 */

std::_Nonscalar_ptr_iterator_tag __fastcall std::_Ptr_cat<std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>,CUnmannedTraderRegistItemInfo *>(std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *__formal, CUnmannedTraderRegistItemInfo **a2)
{
  return std::_Ptr_cat<std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>,CUnmannedTraderRegistItemInfo *>(
           __formal,
           a2);
}
