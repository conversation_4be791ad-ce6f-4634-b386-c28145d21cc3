/*
 * Function: ??$_Fill_n@PEAPEAUMessageRange@MeterFilter@CryptoPP@@_KPEAU123@@std@@YAXPEAPEAUMessageRange@MeterFilter@CryptoPP@@_KAEBQEAU123@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140605760
 */

signed __int64 __fastcall std::_Fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned __int64,CryptoPP::MeterFilter::MessageRange *>(_QWORD *a1, __int64 a2, _QWORD *a3)
{
  signed __int64 result; // rax@3
  _QWORD *v4; // [sp+8h] [bp+8h]@1
  __int64 v5; // [sp+10h] [bp+10h]@1

  v5 = a2;
  v4 = a1;
  while ( v5 )
  {
    *v4 = *a3;
    --v5;
    result = (signed __int64)(v4 + 1);
    ++v4;
  }
  return result;
}
