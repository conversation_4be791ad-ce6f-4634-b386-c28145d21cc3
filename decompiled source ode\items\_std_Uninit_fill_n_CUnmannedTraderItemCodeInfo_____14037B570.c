/*
 * Function: _std::_Uninit_fill_n_CUnmannedTraderItemCodeInfo_____ptr64_unsigned___int64_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo____::_1_::catch$0
 * Address: 0x14037B570
 */

void __fastcall __noreturn std::_Uninit_fill_n_CUnmannedTraderItemCodeInfo_____ptr64_unsigned___int64_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 64); *(_QWORD *)(i + 32) += 72i64 )
    std::allocator<CUnmannedTraderItemCodeInfo>::destroy(
      *(std::allocator<CUnmannedTraderItemCodeInfo> **)(i + 88),
      *(CUnmannedTraderItemCodeInfo **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
