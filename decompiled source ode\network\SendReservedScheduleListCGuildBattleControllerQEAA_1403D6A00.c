/*
 * Function: ?SendReservedScheduleList@CGuildBattleController@@QEAAXHIKEEK@Z
 * Address: 0x1403D6A00
 */

void __fastcall CGuildBattleController::SendReservedScheduleList(CGuildBattleController *this, int n, unsigned int uiMapID, unsigned int dwVer, char byDay, char byPage, unsigned int dwGuildSerial)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager *v9; // rax@4
  __int64 v10; // [sp+0h] [bp-48h]@1
  int na; // [sp+58h] [bp+10h]@1
  unsigned int uiMapIDa; // [sp+60h] [bp+18h]@1
  unsigned int dwVera; // [sp+68h] [bp+20h]@1

  dwVera = dwVer;
  uiMapIDa = uiMapID;
  na = n;
  v7 = &v10;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v9 = GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Instance();
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Send(v9, uiMapIDa, na, dwVera, byDay, byPage, dwGuildSerial);
}
