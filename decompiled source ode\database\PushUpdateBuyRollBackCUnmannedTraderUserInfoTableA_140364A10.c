/*
 * Function: ?PushUpdateBuyRollBack@CUnmannedTraderUserInfoTable@@AEAAXPEAU_qry_case_unmandtrader_buy_update_wait@@@Z
 * Address: 0x140364A10
 */

void __fastcall CUnmannedTraderUserInfoTable::PushUpdateBuyRollBack(CUnmannedTraderUserInfoTable *this, _qry_case_unmandtrader_buy_update_wait *pkQuery)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-E8h]@1
  unsigned __int16 Dst; // [sp+40h] [bp-A8h]@4
  unsigned int v6; // [sp+44h] [bp-A4h]@4
  char v7; // [sp+48h] [bp-A0h]@4
  char v8; // [sp+49h] [bp-9Fh]@4
  unsigned __int8 v9; // [sp+4Ah] [bp-9Eh]@4
  int v10; // [sp+50h] [bp-98h]@8
  char v11[128]; // [sp+54h] [bp-94h]@8
  unsigned __int8 v12; // [sp+D4h] [bp-14h]@4
  int j; // [sp+D8h] [bp-10h]@4
  _qry_case_unmandtrader_buy_update_wait *v14; // [sp+F8h] [bp+10h]@1

  v14 = pkQuery;
  v2 = &v4;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  memset_0(&Dst, 0, 0x84ui64);
  v8 = v14->byType;
  Dst = v14->wInx;
  v6 = v14->dwBuyer;
  v7 = v14->byRace;
  v9 = v14->byNum;
  v12 = 0;
  for ( j = 0; j < v14->byNum; ++j )
  {
    if ( !v14->List[j].byProcRet )
    {
      *(&v10 + 3 * v12) = v14->List[j].dwRegistSerial;
      v11[12 * (unsigned __int64)v12++] = v14->List[j].byOldState;
    }
  }
  v9 = v12;
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 66, (char *)&Dst, 132);
}
