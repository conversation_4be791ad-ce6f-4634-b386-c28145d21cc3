/*
 * Function: ??$_Iter_random@PEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@PEAU12@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@0@Z
 * Address: 0x1405A4840
 */

__int64 __fastcall std::_Iter_random<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> *>(__int64 a1)
{
  return a1;
}
