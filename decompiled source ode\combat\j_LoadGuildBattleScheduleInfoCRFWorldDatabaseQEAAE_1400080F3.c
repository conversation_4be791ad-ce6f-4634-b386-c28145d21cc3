/*
 * Function: j_?LoadGuildBattleScheduleInfo@CRFWorldDatabase@@QEAAEIIPEAU_worlddb_guild_battle_schedule_list@@@Z
 * Address: 0x1400080F3
 */

char __fastcall CRFWorldDatabase::LoadGuildBattleScheduleInfo(CRFWorldDatabase *this, unsigned int uiStartListID, unsigned int uiScheduleUnitCnt, _worlddb_guild_battle_schedule_list *pkInfo)
{
  return CRFWorldDatabase::LoadGuildBattleScheduleInfo(this, uiStartListID, uiScheduleUnitCnt, pkInfo);
}
