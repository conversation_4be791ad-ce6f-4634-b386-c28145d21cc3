/*
 * Function: ??0?$_Tree_val@V?$_Tmap_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UAreaList@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UAreaList@@@std@@@2@$0A@@std@@@std@@IEAA@AEBU?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@1@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UAreaList@@@std@@@1@@Z
 * Address: 0x140193DB0
 */

void __fastcall std::_Tree_val<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>>,0>>::_Tree_val<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>>,0>>(std::_Tree_val<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> >,0> > *this, std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *_Parg, __int64 _Al)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> > v5; // al@4
  __int64 v6; // [sp+0h] [bp-38h]@1
  char v7; // [sp+20h] [bp-18h]@4
  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> > *v8; // [sp+28h] [bp-10h]@4
  std::_Tree_val<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> >,0> > *v9; // [sp+40h] [bp+8h]@1
  std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *_Parga; // [sp+48h] [bp+10h]@1
  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> > *__formal; // [sp+50h] [bp+18h]@1

  __formal = (std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> > *)_Al;
  _Parga = _Parg;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = (std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> > *)&v7;
  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>>::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>>(
    (std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> > *)&v7,
    (std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> > *)_Al);
  std::_Tree_ptr<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>>,0>>::_Tree_ptr<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>>,0>>(
    (std::_Tree_ptr<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> >,0> > *)&v9->_Myfirstiter,
    _Parga,
    v5);
  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>>::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>>(
    &v9->_Alval,
    __formal);
}
