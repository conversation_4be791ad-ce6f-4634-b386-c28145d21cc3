/*
 * Function: ?Emb_CheckActForQuest@CPlayer@@QEAA_NHPEADG_N@Z
 * Address: 0x1400CAA70
 */

char __fastcall CPlayer::Emb_CheckActForQuest(CPlayer *this, int nActCode, char *pszReqCode, unsigned __int16 wAddCount, bool bParty)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-78h]@1
  _quest_check_result *v9; // [sp+30h] [bp-48h]@4
  int j; // [sp+38h] [bp-40h]@6
  char *v11; // [sp+40h] [bp-38h]@9
  _QUEST_DB_BASE::_LIST *pSlotData; // [sp+48h] [bp-30h]@9
  char v13; // [sp+50h] [bp-28h]@10
  int l; // [sp+54h] [bp-24h]@11
  int k; // [sp+58h] [bp-20h]@17
  bool v16; // [sp+5Ch] [bp-1Ch]@22
  _base_fld *v17; // [sp+60h] [bp-18h]@28
  CPlayer *v18; // [sp+80h] [bp+8h]@1

  v18 = this;
  v5 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v9 = CQuestMgr::CheckReqAct(&v18->m_QuestMgr, nActCode, pszReqCode, wAddCount, bParty);
  if ( v9 )
  {
    for ( j = 0; j < v9->m_byCheckNum; ++j )
    {
      v11 = &v9->m_List[j].byQuestDBSlot;
      pSlotData = &v18->m_Param.m_QuestDB.m_List[(unsigned __int8)*v11];
      pSlotData->wNum[v9->m_List[j].byActIndex] = v9->m_List[j].wCount;
      if ( !CQuestMgr::CheckFailCondition(&v18->m_QuestMgr, *v11, 1, 0i64) )
      {
        v13 = 1;
        if ( v11[4] )
        {
          for ( k = 0; k < 3; ++k )
            pSlotData->wNum[k] = -1;
        }
        else
        {
          for ( l = 0; l < 3; ++l )
          {
            if ( pSlotData->wNum[l] != 0xFFFF )
            {
              v13 = 0;
              break;
            }
          }
        }
        if ( v13 )
          pSlotData->dwPassSec = -1;
        CPlayer::SendMsg_QuestProcess(v18, *v11, v11[1], *((_WORD *)v11 + 1));
        v16 = 0;
        if ( *((_WORD *)v11 + 1) == 0xFFFF || v13 )
          v16 = 1;
        if ( v18->m_pUserDB )
          CUserDB::Update_QuestUpdate(v18->m_pUserDB, *v11, pSlotData, v16);
        if ( v13 )
        {
          v17 = CRecordData::GetRecord(CQuestMgr::s_tblQuest, pSlotData->wIndex);
          if ( *(_DWORD *)&v17[13].m_strCode[60] || *(_DWORD *)&v17[1].m_strCode[24] )
            CPlayer::SendMsg_SelectQuestReward(v18, *v11);
          else
            CPlayer::Emb_CompleteQuest(v18, *v11, -1, -1);
        }
      }
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
