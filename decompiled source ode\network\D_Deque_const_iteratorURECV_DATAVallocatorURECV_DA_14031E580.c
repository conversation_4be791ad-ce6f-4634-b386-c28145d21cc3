/*
 * Function: ??D?$_Deque_const_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEBAAEBURECV_DATA@@XZ
 * Address: 0x14031E580
 */

RECV_DATA *__fastcall std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator*(std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v3; // rt1@4
  unsigned __int64 v5; // [sp+0h] [bp-18h]@1
  __int64 v6; // [sp+8h] [bp-10h]@4
  std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v1 = (__int64 *)&v5;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = v7->_Myoff;
  v3 = v7->_Myoff;
  v6 = 0i64;
  if ( v7->_Mycont->_Mapsize <= v5 )
    v5 -= v7->_Mycont->_Mapsize;
  return &v7->_Mycont->_Map[v5][v6];
}
