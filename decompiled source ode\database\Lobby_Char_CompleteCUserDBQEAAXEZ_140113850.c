/*
 * Function: ?Lobby_Char_Complete@CUserDB@@QEAAXE@Z
 * Address: 0x140113850
 */

void __fastcall CUserDB::Lobby_Char_Complete(CUserDB *this, char byRetCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@9
  unsigned __int16 v5; // ax@10
  __int64 v6; // [sp+0h] [bp-E8h]@1
  unsigned int nLen[2]; // [sp+20h] [bp-C8h]@10
  bool bFirst; // [sp+28h] [bp-C0h]@10
  char *pszFileName; // [sp+30h] [bp-B8h]@10
  int j; // [sp+40h] [bp-A8h]@4
  _moveout_user_result_zone v11; // [sp+58h] [bp-90h]@9
  char pbyType; // [sp+84h] [bp-64h]@9
  char v13; // [sp+85h] [bp-63h]@9
  char Dst; // [sp+A8h] [bp-40h]@10
  char v15; // [sp+C4h] [bp-24h]@10
  char v16; // [sp+C5h] [bp-23h]@10
  CUserDB *v17; // [sp+F0h] [bp+8h]@1
  char v18; // [sp+F8h] [bp+10h]@1

  v18 = byRetCode;
  v17 = this;
  v2 = &v6;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v17->m_bDBWaitState = 0;
  v17->m_bField = 0;
  v17->m_dwSerial = -1;
  _SYNC_STATE::re_lobby(&v17->m_ss);
  v17->m_bDataUpdate = 0;
  _AVATOR_DATA::InitData(&v17->m_AvatorData);
  _AVATOR_DATA::InitData(&v17->m_AvatorData_bk);
  for ( j = 0; j < 3; ++j )
    _REGED::init(&v17->m_RegedList[j]);
  if ( v17->m_byUserDgr )
  {
    v17->m_byUILock = 2;
    v11.byRetCode = v18;
    pbyType = 1;
    v13 = 7;
    v4 = _moveout_user_result_zone::size(&v11);
    CNetProcess::LoadSendMsg(unk_1414F2088, v17->m_idWorld.wIndex, &pbyType, &v11.byRetCode, v4);
  }
  else
  {
    _DELAY_PROCESS::Push(&CUserDB::s_MoveLobbyDelay, v17->m_idWorld.wIndex, v17->m_idWorld.dwSerial);
    v17->m_byUILock = 1;
  }
  pszFileName = v17->m_szLobbyHistoryFileName;
  bFirst = 0;
  nLen[0] = v17->m_dwIP;
  CMgrAccountLobbyHistory::enter_lobby(
    &CUserDB::s_MgrLobbyHistory,
    v17->m_dwAccountSerial,
    v17->m_szAccountID,
    v17->m_byUserDgr,
    nLen[0],
    0,
    v17->m_szLobbyHistoryFileName);
  memcpy_0(&Dst, &v17->m_gidGlobal, 8ui64);
  v15 = 1;
  v16 = 13;
  v5 = _enter_lobby_report_wrac::size((_enter_lobby_report_wrac *)&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2090, 0, &v15, &Dst, v5);
}
