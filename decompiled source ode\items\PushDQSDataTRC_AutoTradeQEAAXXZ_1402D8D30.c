/*
 * Function: ?PushDQSData@TRC_AutoTrade@@QEAAXXZ
 * Address: 0x1402D8D30
 */

void __usercall TRC_AutoTrade::PushDQSData(TRC_AutoTrade *this@<rcx>, float a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@7
  __int64 v5; // [sp+0h] [bp-C8h]@1
  _insert_trc_info v6; // [sp+40h] [bp-88h]@4
  unsigned __int64 v7; // [sp+B0h] [bp-18h]@4
  TRC_AutoTrade *v8; // [sp+D0h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = (unsigned __int64)&v5 ^ _security_cookie;
  v6.byRace = v8->m_byRace;
  v6.byMatterType = v8->m_suggested.byMatterType;
  v6.dwMatterDst = v8->m_suggested.dwMatterDst;
  strcpy_0(v6.wszMatterDst, v8->m_suggested.wszMatterDst);
  v6.dwGSerial = -1;
  strcpy_0(v6.szGuildName, "*");
  if ( v8->m_suggested.dwNext < 5 || v8->m_suggested.dwNext > 0x14 )
    v8->m_suggested.dwNext = 5;
  v6.dwNext = v8->m_suggested.dwNext;
  TRC_AutoTrade::get_taxrate(v8);
  v6.byCurrTax = (signed int)ffloor(a2 * 100.0);
  v4 = _insert_trc_info::size(&v6);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 51, &v6.byRace, v4);
}
