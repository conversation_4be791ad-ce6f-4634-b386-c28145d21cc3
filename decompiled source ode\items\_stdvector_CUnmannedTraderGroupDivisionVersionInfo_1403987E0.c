/*
 * Function: _std::vector_CUnmannedTraderGroupDivisionVersionInfo_std::allocator_CUnmannedTraderGroupDivisionVersionInfo___::insert_::_1_::dtor$1
 * Address: 0x1403987E0
 */

void __fastcall std::vector_CUnmannedTraderGroupDivisionVersionInfo_std::allocator_CUnmannedTraderGroupDivisionVersionInfo___::insert_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 120) & 1 )
  {
    *(_DWORD *)(a2 + 120) &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::~_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>((std::_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *)(a2 + 40));
  }
}
