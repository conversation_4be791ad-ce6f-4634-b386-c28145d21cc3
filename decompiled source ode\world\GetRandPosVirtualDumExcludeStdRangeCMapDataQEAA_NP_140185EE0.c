/*
 * Function: ?GetRandPosVirtualDumExcludeStdRange@CMapData@@QEAA_NPEAMHH0@Z
 * Address: 0x140185EE0
 */

char __fastcall CMapData::GetRandPosVirtualDumExcludeStdRange(CMapData *this, float *pStdPos, int nRange, int iExcludeRange, float *pNewPos)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // eax@10
  int v9; // eax@14
  __int64 v10; // [sp+0h] [bp-F8h]@1
  float v11; // [sp+28h] [bp-D0h]@4
  float v12; // [sp+2Ch] [bp-CCh]@4
  float v13; // [sp+30h] [bp-C8h]@4
  float v14; // [sp+58h] [bp-A0h]@4
  float v15; // [sp+5Ch] [bp-9Ch]@4
  float v16; // [sp+60h] [bp-98h]@4
  float v17[3]; // [sp+88h] [bp-70h]@13
  float v18; // [sp+B8h] [bp-40h]@4
  float v19; // [sp+BCh] [bp-3Ch]@4
  float v20; // [sp+D8h] [bp-20h]@4
  float v21; // [sp+DCh] [bp-1Ch]@4
  int v22; // [sp+E4h] [bp-14h]@4
  float v23; // [sp+E8h] [bp-10h]@5
  float v24; // [sp+ECh] [bp-Ch]@5
  CMapData *v25; // [sp+100h] [bp+8h]@1
  float *v26; // [sp+108h] [bp+10h]@1
  int v27; // [sp+110h] [bp+18h]@1

  v27 = nRange;
  v26 = pStdPos;
  v25 = this;
  v5 = &v10;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v11 = *pStdPos - (float)(nRange / 2);
  v12 = pStdPos[1] - 100.0;
  v13 = pStdPos[2] - (float)(nRange / 2);
  v14 = *pStdPos + (float)(nRange / 2);
  v15 = pStdPos[1] + 100.0;
  v16 = pStdPos[2] + (float)(nRange / 2);
  v18 = *pStdPos - (float)(iExcludeRange / 2);
  v20 = *pStdPos + (float)(iExcludeRange / 2);
  v19 = pStdPos[2] - (float)(iExcludeRange / 2);
  v21 = pStdPos[2] + (float)(iExcludeRange / 2);
  v22 = 0;
  while ( 1 )
  {
    while ( 1 )
    {
      do
      {
        v23 = *v26 - (float)(v27 / 2);
        *pNewPos = v23 + (float)(rand() % v27);
        v24 = v26[2] - (float)(v27 / 2);
        pNewPos[2] = v24 + (float)(rand() % v27);
      }
      while ( *pNewPos >= v18 && v20 >= *pNewPos && pNewPos[2] >= v19 && v21 >= pNewPos[2] );
      pNewPos[1] = CBsp::GetFirstYpos(v25->m_Level.mBsp, pNewPos, &v11, &v14);
      if ( -65535.0 != pNewPos[1] )
        break;
      v7 = v22++;
      if ( v7 > 50 )
        return 0;
    }
    if ( (unsigned int)CBsp::CanYouGoThere(v25->m_Level.mBsp, v26, pNewPos, (float (*)[3])v17) )
      break;
    v9 = v22++;
    if ( v9 > 50 )
      return 0;
  }
  return 1;
}
