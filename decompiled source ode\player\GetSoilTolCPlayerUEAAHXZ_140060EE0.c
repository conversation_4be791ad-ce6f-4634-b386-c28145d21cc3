/*
 * Function: ?GetSoilTol@CPlayer@@UEAAHXZ
 * Address: 0x140060EE0
 */

__int64 __fastcall CPlayer::GetSoilTol(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  float v3; // xmm0_4@4
  float v4; // xmm0_4@4
  __int64 result; // rax@10
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@4
  float v8; // [sp+24h] [bp-14h]@4
  float v9; // [sp+28h] [bp-10h]@4
  CPlayer *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v1 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = (float)v10->m_nTolValue[2];
  v3 = v8;
  _effect_parameter::GetEff_Plus(&v10->m_EP, 17);
  v4 = v8 + v3;
  v9 = v4;
  _effect_parameter::GetEff_Rate(&v10->m_EP, 27);
  v7 = (signed int)ffloor(v9 * v4);
  if ( v7 >= -100 )
  {
    if ( v7 > 100 )
      v7 = 100;
  }
  else
  {
    v7 = -100;
  }
  if ( _effect_parameter::GetEff_State(&v10->m_EP, 19) && v7 > 0 )
    result = (unsigned int)-v7;
  else
    result = (unsigned int)v7;
  return result;
}
