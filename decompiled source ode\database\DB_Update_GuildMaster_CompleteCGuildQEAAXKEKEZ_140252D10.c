/*
 * Function: ?DB_Update_GuildMaster_Complete@CGuild@@QEAAXKEKE@Z
 * Address: 0x140252D10
 */

void __fastcall CGuild::DB_Update_GuildMaster_Complete(CGuild *this, unsigned int in_guild_prev_masterSerial, char in_guild_prev_masterPrevGrade, unsigned int in_guild_new_masterSerial, char in_guild_new_masterPrevGrade)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  CGuildMasterEffect *v7; // rax@12
  CGuildMasterEffect *v8; // rax@22
  unsigned __int16 v9; // ax@30
  __int64 v10; // [sp+0h] [bp-1C8h]@1
  _guild_member_info *v11; // [sp+30h] [bp-198h]@7
  _guild_member_info *v12; // [sp+38h] [bp-190h]@10
  _guild_alter_member_grade_inform_zocl v13; // [sp+50h] [bp-178h]@10
  int v14; // [sp+184h] [bp-44h]@10
  int j; // [sp+188h] [bp-40h]@15
  char pbyType; // [sp+194h] [bp-34h]@25
  char v17; // [sp+195h] [bp-33h]@25
  int k; // [sp+1A4h] [bp-24h]@25
  _guild_member_info *v19; // [sp+1A8h] [bp-20h]@5
  _guild_member_info *v20; // [sp+1B0h] [bp-18h]@8
  CGuild *v21; // [sp+1D0h] [bp+8h]@1
  unsigned int v22; // [sp+1E8h] [bp+20h]@1

  v22 = in_guild_new_masterSerial;
  v21 = this;
  v5 = &v10;
  for ( i = 112i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( in_guild_prev_masterSerial == -1 )
    v19 = 0i64;
  else
    v19 = CGuild::GetMemberFromSerial(v21, in_guild_prev_masterSerial);
  v11 = v19;
  if ( v22 == -1 )
    v20 = 0i64;
  else
    v20 = CGuild::GetMemberFromSerial(v21, v22);
  v12 = v20;
  _guild_alter_member_grade_inform_zocl::_guild_alter_member_grade_inform_zocl(&v13);
  v14 = 0;
  if ( v11 )
  {
    v11->byClassInGuild = 0;
    v13.MemberList[v14].dwMemberSerial = v11->dwSerial;
    v13.MemberList[v14].byRank = v11->byRank;
    v13.MemberList[v14].byGrade = 0;
    if ( v11->pPlayer )
    {
      v11->pPlayer->m_Param.m_byClassInGuild = 0;
      v7 = CGuildMasterEffect::GetInstance();
      CGuildMasterEffect::out_player(v7, v11->pPlayer, v21->m_byGrade);
    }
    ++v14;
  }
  _guild_master_info::init(&v21->m_MasterData);
  v21->m_bPossibleElectMaster = 1;
  if ( v12 )
  {
    for ( j = 0; j < 3; ++j )
    {
      if ( v21->m_pGuildCommittee[j] && v21->m_pGuildCommittee[j]->dwSerial == v12->dwSerial )
      {
        v21->m_pGuildCommittee[j] = 0i64;
        break;
      }
    }
    v12->byClassInGuild = 2;
    v21->m_MasterData.pMember = v12;
    v21->m_MasterData.dwSerial = v12->dwSerial;
    v21->m_MasterData.byPrevGrade = in_guild_new_masterPrevGrade;
    v13.MemberList[v14].dwMemberSerial = v12->dwSerial;
    v13.MemberList[v14].byRank = v12->byRank;
    v13.MemberList[v14].byGrade = 2;
    if ( v12->pPlayer )
    {
      v12->pPlayer->m_Param.m_byClassInGuild = 2;
      v8 = CGuildMasterEffect::GetInstance();
      CGuildMasterEffect::in_player(v8, v12->pPlayer, v21->m_byGrade);
    }
    v21->m_bPossibleElectMaster = 0;
    ++v14;
    CGuildList::SetGuildMaster(&CGuild::s_GuildList, v21->m_byRace, v21->m_wszName, v21->m_MasterData.pMember->wszName);
  }
  CGuild::MakeDownMemberPacket(v21);
  v13.byAlterMemberNum = v14;
  if ( (_BYTE)v14 )
  {
    pbyType = 27;
    v17 = 32;
    for ( k = 0; k < 50; ++k )
    {
      if ( _guild_member_info::IsFill(&v21->m_MemberData[k]) )
      {
        if ( v21->m_MemberData[k].pPlayer )
        {
          v9 = _guild_alter_member_grade_inform_zocl::size(&v13);
          CNetProcess::LoadSendMsg(
            unk_1414F2088,
            v21->m_MemberData[k].pPlayer->m_ObjID.m_wIndex,
            &pbyType,
            &v13.byAlterMemberNum,
            v9);
        }
      }
    }
  }
}
