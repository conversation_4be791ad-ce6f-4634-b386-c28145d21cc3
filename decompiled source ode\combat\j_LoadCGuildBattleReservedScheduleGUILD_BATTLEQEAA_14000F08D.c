/*
 * Function: j_?Load@CGuildBattleReservedSchedule@GUILD_BATTLE@@QEAA_N_NPEAU_worlddb_guild_battle_schedule_list@@@Z
 * Address: 0x14000F08D
 */

bool __fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::Load(GUILD_BATTLE::CGuildBattleReservedSchedule *this, bool bToday, _worlddb_guild_battle_schedule_list *pkInfo)
{
  return GUILD_BATTLE::CGuildBattleReservedSchedule::Load(this, bToday, pkInfo);
}
