/*
 * Function: ?SF_SelfDestruction@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 * Address: 0x1400A0990
 */

char __fastcall CPlayer::SF_SelfDestruction(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  CPlayer *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( CCharacter::GetStealth((CCharacter *)&v7->vfptr, 1) )
  {
    result = 0;
  }
  else
  {
    v7->m_dwSelfDestructionTime = GetLoopTime() + 5000;
    v7->m_fSelfDestructionDamage = fEffectValue;
    result = 1;
  }
  return result;
}
