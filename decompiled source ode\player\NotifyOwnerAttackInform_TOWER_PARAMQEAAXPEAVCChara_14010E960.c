/*
 * Function: ?NotifyOwnerAttackInform@_TOWER_PARAM@@QEAAXPEAVCCharacter@@@Z
 * Address: 0x14010E960
 */

void __fastcall _TOWER_PARAM::NotifyOwnerAttackInform(_TOWER_PARAM *this, CCharacter *pDst)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _TOWER_PARAM *v6; // [sp+40h] [bp+8h]@1
  CCharacter *pDsta; // [sp+48h] [bp+10h]@1

  pDsta = pDst;
  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 6; ++j )
  {
    if ( v6->m_List[j].m_pTowerObj )
      CGuardTower::NotifyOwnerAttackInform(v6->m_List[j].m_pTowerObj, pDsta);
  }
}
