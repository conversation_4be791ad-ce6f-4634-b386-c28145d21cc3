/*
 * Function: j_??$_Uninit_copy@PEAPEAUINI_Key@@PEAPEAU1@V?$allocator@PEA<PERSON><PERSON>_Key@@@std@@@std@@YAPEAPEAUINI_Key@@PEAPEAU1@00AEAV?$allocator@P<PERSON><PERSON><PERSON>_Key@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140006E7E
 */

INI_Key **__fastcall std::_Uninit_copy<INI_Key * *,INI_Key * *,std::allocator<INI_Key *>>(INI_Key **_First, INI_Key **_Last, INI_Key **_Dest, std::allocator<INI_Key *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<INI_Key * *,INI_Key * *,std::allocator<INI_Key *>>(_First, _Last, _Dest, __formal, a5, a6);
}
