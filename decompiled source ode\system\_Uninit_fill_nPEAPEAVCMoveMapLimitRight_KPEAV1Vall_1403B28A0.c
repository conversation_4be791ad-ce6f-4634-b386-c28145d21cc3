/*
 * Function: ??$_Uninit_fill_n@PEAPEAVCMoveMapLimitRight@@_KPEAV1@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@YAXPEAPEAVCMoveMapLimitRight@@_KAEBQEAV1@AEAV?$allocator@PEAVCMoveMapLimitRight@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1403B28A0
 */

void __fastcall std::_Uninit_fill_n<CMoveMapLimitRight * *,unsigned __int64,CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(CMoveMapLimitRight **_First, unsigned __int64 _Count, CMoveMapLimitRight *const *_Val, std::allocator<CMoveMapLimitRight *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-28h]@1
  CMoveMapLimitRight **_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  stdext::unchecked_fill_n<CMoveMapLimitRight * *,unsigned __int64,CMoveMapLimitRight *>(_Firsta, _Count, _Val);
}
