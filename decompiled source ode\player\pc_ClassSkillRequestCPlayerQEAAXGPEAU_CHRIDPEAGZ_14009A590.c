/*
 * Function: ?pc_ClassSkillRequest@CPlayer@@QEAAXGPEAU_CHRID@@PEAG@Z
 * Address: 0x14009A590
 */

void __fastcall CPlayer::pc_ClassSkillRequest(CPlayer *this, unsigned __int16 wSkillIndex, _CHRID *pidDst, unsigned __int16 *pConsumeSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  bool v7; // [sp+30h] [bp-18h]@4
  char v8; // [sp+31h] [bp-17h]@4
  CPlayer *v9; // [sp+50h] [bp+8h]@1
  unsigned __int16 v10; // [sp+58h] [bp+10h]@1
  _CHRID *pidDsta; // [sp+60h] [bp+18h]@1
  unsigned __int16 *v12; // [sp+68h] [bp+20h]@1

  v12 = pConsumeSerial;
  pidDsta = pidDst;
  v10 = wSkillIndex;
  v9 = this;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7 = CCharacter::GetStealth((CCharacter *)&v9->vfptr, 1);
  v8 = CPlayer::skill_process(v9, 2, v10, pidDsta, v12, 0i64);
  if ( (!v8 || v8 == 100) && v7 )
    CCharacter::BreakStealth((CCharacter *)&v9->vfptr);
  CPlayer::SendMsg_ClassSkillResult(v9, v8, pidDsta, v10);
}
