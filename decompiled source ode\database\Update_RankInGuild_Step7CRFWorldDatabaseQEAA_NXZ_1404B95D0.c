/*
 * Function: ?Update_RankInGuild_Step7@CRFWorldDatabase@@QEAA_NXZ
 * Address: 0x1404B95D0
 */

char __fastcall CRFWorldDatabase::Update_RankInGuild_Step7(CRFWorldDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CRFWorldDatabase *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v5->vfptr,
    "CRFWorldDatabase::Update_RankInGuild_Step7() : Start drop #tbl_RankInGuild Table");
  if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v5->vfptr, "drop table #tbl_RankInGuild", 0) )
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v5->vfptr,
      "CRFWorldDatabase::Update_RankInGuild_Step7() : End drop #tbl_RankInGuild Table");
    result = 1;
  }
  else
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v5->vfptr,
      "CRFWorldDatabase::Update_RankInGuild_Step7() : drop table #tbl_RankInGuild Fail!");
    result = 1;
  }
  return result;
}
