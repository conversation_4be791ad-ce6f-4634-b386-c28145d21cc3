/*
 * Function: ?GetGenAttackProb@CAnimus@@UEAAHPEAVCCharacter@@H_N@Z
 * Address: 0x1401298D0
 */

__int64 __fastcall CAnimus::GetGenAttackProb(CAnimus *this, CCharacter *pDst, int nPart, bool bBackAttack)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed int v6; // eax@4
  int v7; // eax@4
  __int64 v9; // [sp+0h] [bp-48h]@1
  int v10; // [sp+20h] [bp-28h]@4
  double v11; // [sp+28h] [bp-20h]@4
  float v12; // [sp+30h] [bp-18h]@4
  CAnimus *v13; // [sp+50h] [bp+8h]@1
  CCharacter *v14; // [sp+58h] [bp+10h]@1
  bool v15; // [sp+68h] [bp+20h]@1

  v15 = bBackAttack;
  v14 = pDst;
  v13 = this;
  v4 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = (double)v13->m_pRecord->m_nAttSklUnit;
  v12 = (float)((int (__fastcall *)(CCharacter *))pDst->vfptr->GetLevel)(pDst) * 1.0;
  v6 = ((int (__fastcall *)(CCharacter *, _QWORD))v13->m_pTarget->vfptr->GetDefSkill)(v13->m_pTarget, v15);
  v10 = (signed int)floor(v11 - (float)(v12 + (float)v6) / 4.0 + 95.0);
  v7 = ((int (__fastcall *)(CCharacter *))v14->vfptr->GetAvoidRate)(v14);
  v10 -= v7;
  if ( v10 >= 5 )
  {
    if ( v10 > 95 )
      v10 = 95;
  }
  else
  {
    v10 = 5;
  }
  return (unsigned int)v10;
}
