/*
 * Function: ?ct_goto_npc@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140296600
 */

bool __fastcall ct_goto_npc(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  __int64 v4; // [sp+0h] [bp-B8h]@1
  char Dest; // [sp+30h] [bp-88h]@9
  CMerchant *pNpc; // [sp+78h] [bp-40h]@9
  __int64 v7; // [sp+80h] [bp-38h]@9
  int j; // [sp+88h] [bp-30h]@9
  char *Str1; // [sp+90h] [bp-28h]@11
  unsigned __int64 v10; // [sp+A0h] [bp-18h]@4
  CPlayer *v11; // [sp+C0h] [bp+8h]@1

  v11 = pOne;
  v1 = &v4;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( v11 && v11->m_bOper )
  {
    if ( s_nWordCount == 1 )
    {
      strcpy_0(&Dest, s_pwszDstCheat[0]);
      pNpc = 0i64;
      v7 = 0i64;
      for ( j = 0; j < CMerchant::s_nLiveNum; ++j )
      {
        Str1 = CItemStore::GetNpcCode(g_NPC[j].m_pItemStore);
        if ( Str1 && !strcmp_0(Str1, &Dest) )
        {
          pNpc = &g_NPC[j];
          break;
        }
      }
      if ( pNpc )
        result = CPlayer::dev_goto_npc(v11, pNpc);
      else
        result = 0;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
