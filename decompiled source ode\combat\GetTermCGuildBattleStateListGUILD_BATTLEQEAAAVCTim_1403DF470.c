/*
 * Function: ?GetTerm@CGuildBattleStateList@GUILD_BATTLE@@QEAA?AVCTimeSpan@ATL@@XZ
 * Address: 0x1403DF470
 */

ATL::CTimeSpan *__fastcall GUILD_BATTLE::CGuildBattleStateList::GetTerm(GUILD_BATTLE::CGuildBattleStateList *this, ATL::CTimeSpan *result)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleStateList *v6; // [sp+30h] [bp+8h]@1
  ATL::CTimeSpan *v7; // [sp+38h] [bp+10h]@1

  v7 = result;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  ((void (__fastcall *)(GUILD_BATTLE::CGuildBattleState *))v6->m_pkNextState->vfptr->GetTerm)(v6->m_pkNextState);
  return v7;
}
