/*
 * Function: j_?reg_auto_trade@CMgrAvatorItemHistory@@QEAAXHKPEAU_db_con@_STORAGE_LIST@@KKKPEAD@Z
 * Address: 0x14000CAD1
 */

void __fastcall CMgrAvatorItemHistory::reg_auto_trade(CMgrAvatorItemHistory *this, int n, unsigned int dwRegistSerial, _STORAGE_LIST::_db_con *pRegItem, unsigned int dwPrice, unsigned int dwfee, unsigned int dwLeftDalant, char *pszFileName)
{
  CMgrAvatorItemHistory::reg_auto_trade(this, n, dwRegistSerial, pRegItem, dwPrice, dwfee, dwLeftDalant, pszFileName);
}
