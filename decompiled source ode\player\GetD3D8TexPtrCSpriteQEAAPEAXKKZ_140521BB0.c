/*
 * Function: ?GetD3D8TexPtr@CSprite@@QEAAPEAXKK@Z
 * Address: 0x140521BB0
 */

void *__fastcall CSprite::GetD3D8TexPtr(CSprite *this, unsigned int a2, unsigned int a3)
{
  void *result; // rax@6

  if ( a2 == 0xFFFF )
    a2 = *((_DWORD *)this + 13);
  if ( a3 == 0xFFFF )
    a3 = *((_DWORD *)this + 14);
  if ( *((_DWORD *)this + 1) <= a2 || *((_WORD *)this + a2 + 10) <= a3 )
    result = 0i64;
  else
    result = *(void **)(*((_QWORD *)this + a2 + 9) + 24i64 * a3 + 16);
  return result;
}
