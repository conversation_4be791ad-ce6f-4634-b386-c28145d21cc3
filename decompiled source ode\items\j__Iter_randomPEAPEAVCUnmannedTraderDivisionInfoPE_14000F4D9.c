/*
 * Function: j_??$_Iter_random@PEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV1@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCUnmannedTraderDivisionInfo@@0@Z
 * Address: 0x14000F4D9
 */

std::random_access_iterator_tag __fastcall std::_Iter_random<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo * *>(CUnmannedTraderDivisionInfo **const *__formal, CUnmannedTraderDivisionInfo **const *a2)
{
  return std::_Iter_random<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo * *>(__formal, a2);
}
