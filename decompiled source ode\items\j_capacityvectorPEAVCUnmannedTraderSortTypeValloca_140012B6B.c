/*
 * Function: j_?capacity@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEBA_KXZ
 * Address: 0x140012B6B
 */

unsigned __int64 __fastcall std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::capacity(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this)
{
  return std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::capacity(this);
}
