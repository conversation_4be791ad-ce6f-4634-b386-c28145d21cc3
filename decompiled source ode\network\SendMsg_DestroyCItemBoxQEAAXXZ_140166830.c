/*
 * Function: ?SendMsg_Destroy@CItemBox@@QEAAXXZ
 * Address: 0x140166830
 */

void __fastcall CItemBox::SendMsg_Destroy(CItemBox *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[2]; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v6; // [sp+55h] [bp-23h]@4
  CItemBox *v7; // [sp+80h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(_WORD *)szMsg = v7->m_ObjID.m_wIndex;
  pbyType = 3;
  v6 = 28;
  CGameObject::CircleReport((CGameObject *)&v7->vfptr, &pbyType, szMsg, 2, 0);
}
