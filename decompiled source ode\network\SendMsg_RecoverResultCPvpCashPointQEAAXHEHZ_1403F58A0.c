/*
 * Function: ?SendMsg_RecoverResult@CPvpCashPoint@@QEAAXHEH@Z
 * Address: 0x1403F58A0
 */

void __fastcall CPvpCashPoint::SendMsg_RecoverResult(CPvpCashPoint *this, int n, char byRet, int nRecvrPoint)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  int v8; // [sp+35h] [bp-43h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v10; // [sp+55h] [bp-23h]@4

  v4 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = byRet;
  v8 = nRecvrPoint;
  pbyType = 12;
  v10 = 22;
  CNetProcess::LoadSendMsg(unk_1414F2088, n, &pbyType, &szMsg, 5u);
}
