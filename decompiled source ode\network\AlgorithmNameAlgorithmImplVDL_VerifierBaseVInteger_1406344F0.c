/*
 * Function: ?AlgorithmName@?$AlgorithmImpl@V?$DL_VerifierBase@VInteger@CryptoPP@@@CryptoPP@@V?$DL_SS@UDL_SignatureKeys_GFP@CryptoPP@@V?$DL_Algorithm_GDSA@VInteger@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@H@2@@CryptoPP@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
 * Address: 0x1406344F0
 */

__int64 __fastcall CryptoPP::AlgorithmImpl<CryptoPP::DL_VerifierBase<CryptoPP::Integer>,CryptoPP::DL_SS<CryptoPP::DL_SignatureKeys_GFP,CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1,int>>::AlgorithmName(__int64 a1, __int64 a2)
{
  __int64 v3; // [sp+48h] [bp+10h]@1

  v3 = a2;
  CryptoPP::DL_SS<CryptoPP::DL_SignatureKeys_GFP,CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1,int>::StaticAlgorithmName(a2);
  return v3;
}
