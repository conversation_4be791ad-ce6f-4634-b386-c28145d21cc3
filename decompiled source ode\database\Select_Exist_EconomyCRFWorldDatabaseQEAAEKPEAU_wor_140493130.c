/*
 * Function: ?Select_Exist_Economy@CRFWorldDatabase@@QEAAEKPEAU_worlddb_economy_history_info@_worlddb_economy_history_info_array@@@Z
 * Address: 0x140493130
 */

char __fastcall CRFWorldDatabase::Select_Exist_Economy(CRFWorldDatabase *this, unsigned int dwDate, _worlddb_economy_history_info_array::_worlddb_economy_history_info *pEconomyData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-198h]@1
  void *SQLStmt; // [sp+20h] [bp-178h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-170h]@22
  SQLLEN v9; // [sp+38h] [bp-160h]@22
  __int16 v10; // [sp+44h] [bp-154h]@9
  char Dest; // [sp+60h] [bp-138h]@4
  int v12; // [sp+164h] [bp-34h]@4
  char v13; // [sp+168h] [bp-30h]@16
  __int16 v14; // [sp+16Ch] [bp-2Ch]@22
  int j; // [sp+170h] [bp-28h]@22
  int k; // [sp+174h] [bp-24h]@24
  int l; // [sp+178h] [bp-20h]@32
  unsigned __int64 v18; // [sp+188h] [bp-10h]@4
  CRFWorldDatabase *v19; // [sp+1A0h] [bp+8h]@1
  _worlddb_economy_history_info_array::_worlddb_economy_history_info *TargetValue; // [sp+1B0h] [bp+18h]@1

  TargetValue = pEconomyData;
  v19 = this;
  v3 = &v6;
  for ( i = 100i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v18 = (unsigned __int64)&v6 ^ _security_cookie;
  v12 = 0;
  sprintf(&Dest, "{ CALL pSelect_Exist_Economy( %d ) }", dwDate);
  if ( v19->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v19->vfptr, &Dest);
  if ( v19->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v19->vfptr) )
  {
    v10 = SQLExecDirectA_0(v19->m_hStmtSelect, &Dest, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v19->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v19->vfptr, v10, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v19->vfptr, v10, v19->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v10 = SQLFetch_0(v19->m_hStmtSelect);
      if ( v10 && v10 != 1 )
      {
        v13 = 0;
        if ( v10 == 100 )
        {
          v13 = 2;
        }
        else
        {
          SQLStmt = v19->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v19->vfptr, v10, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v19->vfptr, v10, v19->m_hStmtSelect);
          v13 = 1;
        }
        if ( v19->m_hStmtSelect )
          SQLCloseCursor_0(v19->m_hStmtSelect);
        result = v13;
      }
      else
      {
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v19->m_hStmtSelect, 1u, 8, TargetValue->dTradeDalant, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v19->m_hStmtSelect, 2u, 8, &TargetValue->dTradeDalant[1], 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v19->m_hStmtSelect, 3u, 8, &TargetValue->dTradeDalant[2], 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v19->m_hStmtSelect, 4u, 8, TargetValue, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v19->m_hStmtSelect, 5u, 8, &TargetValue->dTradeGold[1], 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v19->m_hStmtSelect, 6u, 8, &TargetValue->dTradeGold[2], 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v19->m_hStmtSelect, 7u, 4, &TargetValue->dwManageValue, 0i64, &v9);
        v14 = 8;
        for ( j = 0; j < 3; ++j )
        {
          for ( k = 0; k < 3; ++k )
          {
            StrLen_or_IndPtr = &v9;
            SQLStmt = 0i64;
            v10 = SQLGetData_0(v19->m_hStmtSelect, v14++, 8, &TargetValue->dMineOre[k][j], 0i64, &v9);
            if ( v10 )
            {
              if ( v10 != 1 )
                break;
            }
          }
        }
        for ( j = 0; j < 3; ++j )
        {
          for ( l = 0; l < 3; ++l )
          {
            StrLen_or_IndPtr = &v9;
            SQLStmt = 0i64;
            v10 = SQLGetData_0(v19->m_hStmtSelect, v14++, 8, &TargetValue->dCutOre[l][j], 0i64, &v9);
            if ( v10 )
            {
              if ( v10 != 1 )
                break;
            }
          }
        }
        if ( v10 && v10 != 1 )
        {
          SQLStmt = v19->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v19->vfptr, v10, &Dest, "SQLGetData", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v19->vfptr, v10, v19->m_hStmtSelect);
          if ( v19->m_hStmtSelect )
            SQLCloseCursor_0(v19->m_hStmtSelect);
          result = 1;
        }
        else
        {
          if ( v19->m_hStmtSelect )
            SQLCloseCursor_0(v19->m_hStmtSelect);
          if ( v19->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v19->vfptr, "%s Success", &Dest);
          result = 0;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v19->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
