/*
 * Function: ?AddDefaultDBRecord@CNormalGuildBattleManager@GUILD_BATTLE@@IEAA_NXZ
 * Address: 0x1403D4FD0
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleManager::AddDefaultDBRecord(GUILD_BATTLE::CNormalGuildBattleManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v3; // rax@5
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleManager *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CRFWorldDatabase::InsertGuildBattleDefaultRecord(pkDB, 46 * v6->m_uiMapCnt) )
  {
    result = 1;
  }
  else
  {
    v3 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v3,
      "CNormalGuildBattleManager::AddDefaultDBRecord() g_Main.m_pWorldDB->InsertGuildBattleDefaultRecord( %u ) Fail!",
      46i64);
    result = 0;
  }
  return result;
}
