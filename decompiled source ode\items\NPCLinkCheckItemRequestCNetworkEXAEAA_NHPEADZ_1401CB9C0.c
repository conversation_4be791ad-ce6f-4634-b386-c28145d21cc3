/*
 * Function: ?NPCLinkCheckItemRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CB9C0
 */

char __fastcall CNetworkEX::NPCLinkCheckItemRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  _STORAGE_POS_INDIV *pStorage; // [sp+20h] [bp-18h]@4
  CPlayer *v8; // [sp+28h] [bp-10h]@4

  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pStorage = (_STORAGE_POS_INDIV *)pBuf;
  v8 = &g_Player + n;
  if ( v8->m_bOper )
  {
    CPlayer::pc_NPCLinkCheckItemRequest(v8, pStorage);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
