/*
 * Function: ??$_Uninit_fill_n@PEAUECPPoint@CryptoPP@@_KU12@V?$allocator@UECPPoint@CryptoPP@@@std@@@std@@YAXPEAUECPPoint@CryptoPP@@_KAEBU12@AEAV?$allocator@UECPPoint@CryptoPP@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405A3890
 */

signed __int64 __fastcall std::_Uninit_fill_n<CryptoPP::ECPPoint *,unsigned __int64,CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>(CryptoPP::ECPPoint *a1, __int64 a2, CryptoPP::ECPPoint *a3, std::allocator<CryptoPP::ECPPoint> *a4)
{
  signed __int64 result; // rax@1
  CryptoPP::ECPPoint *_Ptr; // [sp+40h] [bp+8h]@1
  __int64 v6; // [sp+48h] [bp+10h]@1
  CryptoPP::ECPPoint *_Val; // [sp+50h] [bp+18h]@1
  std::allocator<CryptoPP::ECPPoint> *v8; // [sp+58h] [bp+20h]@1

  v8 = a4;
  _Val = a3;
  v6 = a2;
  _Ptr = a1;
  result = (signed __int64)a1;
  while ( v6 )
  {
    std::allocator<CryptoPP::ECPPoint>::construct(v8, _Ptr, _Val);
    --v6;
    result = (signed __int64)&_Ptr[1].identity;
    ++_Ptr;
  }
  return result;
}
