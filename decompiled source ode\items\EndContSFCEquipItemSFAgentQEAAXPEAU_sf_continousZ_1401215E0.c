/*
 * Function: ?EndContSF@CEquipItemSFAgent@@QEAAXPEAU_sf_continous@@@Z
 * Address: 0x1401215E0
 */

void __fastcall CEquipItemSFAgent::EndContSF(CEquipItemSFAgent *this, _sf_continous *pSF_Cont)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int nEquipTblIndex; // [sp+20h] [bp-18h]@5
  CEquipItemSFAgent *v6; // [sp+40h] [bp+8h]@1
  _sf_continous *v7; // [sp+48h] [bp+10h]@1

  v7 = pSF_Cont;
  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pSF_Cont )
  {
    for ( nEquipTblIndex = 0; nEquipTblIndex < 8; ++nEquipTblIndex )
    {
      if ( v6->m_pContSF[nEquipTblIndex] == v7 )
        CEquipItemSFAgent::SetSFCont(v6, nEquipTblIndex, 0i64);
    }
  }
}
