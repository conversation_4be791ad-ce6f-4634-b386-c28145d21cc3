/*
 * Function: ?IsSFUsableGauge@CPlayer@@QEAA_NEGPEAG@Z
 * Address: 0x1400A1250
 */

char __fastcall CPlayer::IsSFUsableGauge(CPlayer *this, char byEffectCode, unsigned __int16 wEffectIndex, unsigned __int16 *pwDelPoint)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  float v6; // xmm0_4@5
  float v7; // xmm0_4@6
  int v8; // eax@13
  char result; // al@14
  int v10; // eax@15
  int v11; // eax@17
  __int64 v12; // [sp+0h] [bp-68h]@1
  int v13; // [sp+28h] [bp-40h]@5
  int v14; // [sp+2Ch] [bp-3Ch]@5
  int v15; // [sp+30h] [bp-38h]@5
  _base_fld *v16; // [sp+48h] [bp-20h]@5
  _base_fld *v17; // [sp+50h] [bp-18h]@6
  float v18; // [sp+58h] [bp-10h]@5
  float v19; // [sp+5Ch] [bp-Ch]@6
  CPlayer *v20; // [sp+70h] [bp+8h]@1
  unsigned __int16 *v21; // [sp+88h] [bp+20h]@1

  v21 = pwDelPoint;
  v20 = this;
  v4 = &v12;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( byEffectCode == 1 )
  {
    v16 = CRecordData::GetRecord(&stru_1799C8410 + (unsigned __int8)byEffectCode, wEffectIndex);
    v13 = *(_DWORD *)&v16[9].m_strCode[4];
    v18 = (float)*(signed int *)&v16[9].m_strCode[8];
    v6 = v18;
    _effect_parameter::GetEff_Rate(&v20->m_EP, 7);
    v14 = (signed int)ffloor(v18 * v6);
    v15 = *(_DWORD *)&v16[9].m_strCode[12];
  }
  else
  {
    v17 = CRecordData::GetRecord(&stru_1799C8410 + (unsigned __int8)byEffectCode, wEffectIndex);
    v13 = *(_DWORD *)&v17[9].m_strCode[4];
    v19 = (float)*(signed int *)&v17[9].m_strCode[8];
    v7 = v19;
    _effect_parameter::GetEff_Rate(&v20->m_EP, 7);
    v14 = (signed int)ffloor(v19 * v7);
    v15 = *(_DWORD *)&v17[9].m_strCode[12];
  }
  if ( v13 < 0 )
    v13 = 0;
  if ( v14 < 0 )
    v14 = 0;
  if ( v15 < 0 )
    v15 = 0;
  v8 = ((int (__fastcall *)(CPlayer *))v20->vfptr->GetHP)(v20);
  if ( v8 >= v13 )
  {
    v10 = CPlayer::GetFP(v20);
    if ( v10 >= v14 )
    {
      v11 = CPlayer::GetSP(v20);
      if ( v11 >= v15 )
      {
        if ( v21 )
        {
          *v21 = v13;
          v21[1] = v14;
          v21[2] = v15;
        }
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
