/*
 * Function: ?Init@TimeItem@@QEAA_NXZ
 * Address: 0x14030E160
 */

bool __fastcall TimeItem::Init(TimeItem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-178h]@1
  bool bDate[4]; // [sp+20h] [bp-158h]@4
  LPCSTR lpPathName; // [sp+30h] [bp-148h]@4
  char DstBuf; // [sp+50h] [bp-128h]@4
  char v8; // [sp+51h] [bp-127h]@4
  unsigned __int64 v9; // [sp+160h] [bp-18h]@4
  TimeItem *v10; // [sp+180h] [bp+8h]@1

  v10 = this;
  v1 = &v4;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = (unsigned __int64)&v4 ^ _security_cookie;
  lpPathName = "..\\ZoneServerLog\\SystemLog";
  CreateDirectoryA("..\\ZoneServerLog\\SystemLog", 0i64);
  DstBuf = 0;
  memset(&v8, 0, 0xFFui64);
  *(_DWORD *)bDate = GetKorLocalTime();
  sprintf_s(&DstBuf, 0x100ui64, "%s\\TimeItem_%d.log", lpPathName);
  CLogFile::SetWriteLogFile(&v10->_kLogger, &DstBuf, 1, 0, 1, 1);
  return TimeItem::ReadGoods(v10) != 0;
}
