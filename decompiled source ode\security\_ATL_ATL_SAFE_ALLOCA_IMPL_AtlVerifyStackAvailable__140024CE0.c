/*
 * Function: _ATL::_ATL_SAFE_ALLOCA_IMPL::_AtlVerifyStackAvailable_::_1_::filt$0
 * Address: 0x140024CE0
 */

__int64 __fastcall ATL::_ATL_SAFE_ALLOCA_IMPL::_AtlVerifyStackAvailable_::_1_::filt_0(__int64 a1, __int64 a2)
{
  signed __int64 v2; // rbp@1

  v2 = a2 + 48;
  *(_QWORD *)(v2 + 104) = a1;
  *(_DWORD *)(v2 + 112) = ***(_DWORD ***)(a2 + 152);
  *(_DWORD *)(a2 + 184) = *(_DWORD *)(a2 + 160) == -1073741571;
  return *(_DWORD *)(a2 + 184);
}
