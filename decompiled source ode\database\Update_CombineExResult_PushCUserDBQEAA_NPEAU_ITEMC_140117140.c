/*
 * Function: ?Update_CombineExResult_Push@CUserDB@@QEAA_NPEAU_ITEMCOMBINE_DB_BASE@@@Z
 * Address: 0x140117140
 */

char __fastcall CUserDB::Update_CombineExResult_Push(CUserDB *this, _ITEMCOMBINE_DB_BASE *pItemCombineDB_IN)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  void *Dst; // [sp+20h] [bp-18h]@4
  CUserDB *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  Dst = &v7->m_AvatorData.dbItemCombineEx;
  memcpy_0(&v7->m_AvatorData.dbItemCombineEx, pItemCombineDB_IN, 0x134ui64);
  v7->m_bDataUpdate = 1;
  return 1;
}
