/*
 * Function: ?SendMsg_NotifyGetExpInfo@CPlayer@@QEAAXNNN@Z
 * Address: 0x1400DE640
 */

void __fastcall CPlayer::SendMsg_NotifyGetExpInfo(CPlayer *this, long double dOldExp, long double dAlterExp, long double dCurExp)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v6; // ax@5
  __int64 v7; // [sp-20h] [bp-5A8h]@1
  unsigned __int16 nLen[4]; // [sp+0h] [bp-588h]@5
  long double v9; // [sp+8h] [bp-580h]@5
  long double v10; // [sp+10h] [bp-578h]@5
  _trans_gm_msg_inform_zocl Dst; // [sp+30h] [bp-558h]@5
  char pbyType; // [sp+554h] [bp-34h]@5
  char v13; // [sp+555h] [bp-33h]@5
  unsigned __int64 v14; // [sp+570h] [bp-18h]@4
  CPlayer *v15; // [sp+590h] [bp+8h]@1

  v15 = this;
  v4 = &v7;
  for ( i = 360i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v14 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( !CMainThread::IsReleaseServiceMode(&g_Main) )
  {
    _trans_gm_msg_inform_zocl::_trans_gm_msg_inform_zocl(&Dst);
    memset_0(&Dst, 0, 0x503ui64);
    ++dwInx;
    v10 = dCurExp;
    v9 = dAlterExp;
    *(long double *)nLen = dOldExp;
    Dst.wMsgSize = _snprintf(
                     Dst.wszChatData,
                     0xFFui64,
                     "%u Old Exp     : %f\n   Alter Exp   : %f\n   Cur Exp     : %f\n",
                     dwInx);
    pbyType = 2;
    v13 = 13;
    v6 = _trans_gm_msg_inform_zocl::size(&Dst);
    CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, (char *)&Dst, v6);
  }
}
