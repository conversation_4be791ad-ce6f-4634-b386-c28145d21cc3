/*
 * Function: j_?deallocate@?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@QEAAXPEAVCUnmannedTraderRegistItemInfo@@_K@Z
 * Address: 0x140002991
 */

void __fastcall std::allocator<CUnmannedTraderRegistItemInfo>::deallocate(std::allocator<CUnmannedTraderRegistItemInfo> *this, CUnmannedTraderRegistItemInfo *_Ptr, unsigned __int64 __formal)
{
  std::allocator<CUnmannedTraderRegistItemInfo>::deallocate(this, _Ptr, __formal);
}
