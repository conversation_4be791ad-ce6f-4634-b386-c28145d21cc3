/*
 * Function: ?UpdateClearRerservedDayInfo@CGuildBattleController@@QEAA_NKKKK@Z
 * Address: 0x1403D6CF0
 */

bool __fastcall CGuildBattleController::UpdateClearRerservedDayInfo(CGuildBattleController *this, unsigned int dwStartSLID, unsigned int dwEndSLID, unsigned int dwStartSID, unsigned int dwEndSID)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleScheduler *v7; // rax@4
  GUILD_BATTLE::CNormalGuildBattleManager *v8; // rax@5
  __int64 v10; // [sp+0h] [bp-38h]@1
  int v11; // [sp+20h] [bp-18h]@6
  unsigned int dwStartSLIDa; // [sp+48h] [bp+10h]@1
  unsigned int dwEndSLIDa; // [sp+50h] [bp+18h]@1
  unsigned int dwStartSIDa; // [sp+58h] [bp+20h]@1

  dwStartSIDa = dwStartSID;
  dwEndSLIDa = dwEndSLID;
  dwStartSLIDa = dwStartSLID;
  v5 = &v10;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v7 = GUILD_BATTLE::CGuildBattleScheduler::Instance();
  v11 = GUILD_BATTLE::CGuildBattleScheduler::UpdateClearGuildBattleScheduleDayInfo(v7, dwStartSLIDa, dwEndSLIDa)
     && (v8 = GUILD_BATTLE::CNormalGuildBattleManager::Instance(),
         GUILD_BATTLE::CNormalGuildBattleManager::UpdateClearGuildBattleDayInfo(v8, dwStartSIDa, dwEndSID));
  return v11;
}
