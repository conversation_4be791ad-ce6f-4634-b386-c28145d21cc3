/*
 * Function: ?ct_add_guild_schedule@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140292B70
 */

bool __fastcall ct_add_guild_schedule(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  CGuildBattleController *v4; // rax@16
  __int64 v5; // [sp+0h] [bp-118h]@1
  char Dest; // [sp+50h] [bp-C8h]@9
  CGuild *pSrcGuild; // [sp+D8h] [bp-40h]@8
  CGuild *pDestGuild; // [sp+E0h] [bp-38h]@10
  unsigned int dwStartTime; // [sp+E8h] [bp-30h]@12
  int v10; // [sp+ECh] [bp-2Ch]@14
  unsigned int dwMapInx; // [sp+F0h] [bp-28h]@14
  char v12; // [sp+F4h] [bp-24h]@16
  unsigned __int64 v13; // [sp+108h] [bp-10h]@4
  CPlayer *v14; // [sp+120h] [bp+8h]@1

  v14 = pOne;
  v1 = &v5;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( v14 )
  {
    if ( s_nWordCount == 5 )
    {
      pSrcGuild = GetGuildPtrFromName(g_Guild, 500, s_pwszDstCheat[0]);
      if ( pSrcGuild )
      {
        pDestGuild = GetGuildPtrFromName(g_Guild, 500, s_pwszDstCheat[1]);
        if ( pDestGuild )
        {
          dwStartTime = atoi(s_pwszDstCheat[2]) - 1;
          if ( (signed int)dwStartTime > 0 )
          {
            v10 = atoi(s_pwszDstCheat[3]);
            dwMapInx = atoi(s_pwszDstCheat[4]);
            if ( (dwStartTime & 0x80000000) == 0 )
            {
              v4 = CGuildBattleController::Instance();
              v12 = CGuildBattleController::Add(v4, pSrcGuild, pDestGuild, dwStartTime, v10, dwMapInx);
              sprintf(&Dest, "Add GuildBattle Schedule : %u", (unsigned __int8)v12);
              CPlayer::SendData_ChatTrans(v14, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
              result = v12 == 0;
            }
            else
            {
              result = 0;
            }
          }
          else
          {
            result = 0;
          }
        }
        else
        {
          sprintf(&Dest, "Invalid Dest Guild : %s", s_pwszDstCheat[1]);
          CPlayer::SendData_ChatTrans(v14, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
          result = 0;
        }
      }
      else
      {
        sprintf(&Dest, "Invalid Src Guild : %s", s_pwszDstCheat[0]);
        CPlayer::SendData_ChatTrans(v14, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
