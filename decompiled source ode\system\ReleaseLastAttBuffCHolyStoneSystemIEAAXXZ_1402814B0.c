/*
 * Function: ?ReleaseLastAttBuff@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x1402814B0
 */

void __fastcall CHolyStoneSystem::ReleaseLastAttBuff(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@10
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int v5; // [sp+20h] [bp-18h]@4
  int j; // [sp+24h] [bp-14h]@5
  CHolyStoneSystem *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = CHolyStoneSystem::GetDestroyerGuildSerial(v7);
  if ( v5 != -1 )
  {
    for ( j = 0; j < 2532; ++j )
    {
      if ( *(&g_Player.m_bLive + 50856 * j) && *((_QWORD *)&g_Player.m_Param.m_pGuild + 6357 * j) )
      {
        v3 = CPlayerDB::GetGuildSerial((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * j));
        if ( v3 == v5 )
          CPlayer::SetLastAttBuff(&g_Player + j, 0);
      }
    }
  }
}
