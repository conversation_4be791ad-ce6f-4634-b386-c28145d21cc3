/*
 * Function: ?Insert_SettlementOwnerLog@CRFWorldDatabase@@QEAA_NEEKPEBDGENNK@Z
 * Address: 0x1404B0250
 */

bool __fastcall CRFWorldDatabase::Insert_SettlementOwnerLog(CRFWorldDatabase *this, char byNth, char byRace, unsigned int dwGuildSerial, const char *wszGuildName, unsigned __int16 wRank, char byGrade, long double dKillPvpPoint, long double dGuildBattlePvpPoint, unsigned int dwSumLv)
{
  __int64 *v10; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v13; // [sp+0h] [bp-4F8h]@1
  int v14; // [sp+20h] [bp-4D8h]@4
  int v15; // [sp+28h] [bp-4D0h]@4
  int v16; // [sp+30h] [bp-4C8h]@4
  int v17; // [sp+38h] [bp-4C0h]@4
  int v18; // [sp+40h] [bp-4B8h]@4
  unsigned int v19; // [sp+48h] [bp-4B0h]@4
  const char *v20; // [sp+50h] [bp-4A8h]@4
  int v21; // [sp+58h] [bp-4A0h]@4
  int v22; // [sp+60h] [bp-498h]@4
  long double v23; // [sp+68h] [bp-490h]@4
  long double v24; // [sp+70h] [bp-488h]@4
  unsigned int v25; // [sp+78h] [bp-480h]@4
  unsigned __int16 Dst; // [sp+88h] [bp-470h]@4
  unsigned __int16 v27; // [sp+8Ah] [bp-46Eh]@4
  unsigned __int16 v28; // [sp+8Eh] [bp-46Ah]@4
  unsigned __int16 v29; // [sp+90h] [bp-468h]@4
  unsigned __int16 v30; // [sp+92h] [bp-466h]@4
  char Dest; // [sp+C0h] [bp-438h]@4
  unsigned __int64 v32; // [sp+4D0h] [bp-28h]@4
  CRFWorldDatabase *v33; // [sp+500h] [bp+8h]@1
  char v34; // [sp+508h] [bp+10h]@1
  char v35; // [sp+510h] [bp+18h]@1
  unsigned int v36; // [sp+518h] [bp+20h]@1

  v36 = dwGuildSerial;
  v35 = byRace;
  v34 = byNth;
  v33 = this;
  v10 = &v13;
  for ( i = 312i64; i; --i )
  {
    *(_DWORD *)v10 = -858993460;
    v10 = (__int64 *)((char *)v10 + 4);
  }
  v32 = (unsigned __int64)&v13 ^ _security_cookie;
  memset_0(&Dst, 0, 0x10ui64);
  GetLocalTime((LPSYSTEMTIME)&Dst);
  v25 = dwSumLv;
  v24 = dGuildBattlePvpPoint;
  v23 = dKillPvpPoint;
  v22 = (unsigned __int8)byGrade;
  v21 = wRank;
  v20 = wszGuildName;
  v19 = v36;
  v18 = (unsigned __int8)v35;
  v17 = (unsigned __int8)v34;
  v16 = v30;
  v15 = v29;
  v14 = v28;
  sprintf(
    &Dest,
    "{ CALL pInsert_settlementownerlog('%04d-%02d-%02d %02d:%02d', %u, %u, %d, '%s', %d, %u, %f, %f, %d) }",
    Dst,
    v27);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v33->vfptr, &Dest, 1);
}
