/*
 * Function: ?Get@CGravityStone@@QEAAEGKPEAVCPlayer@@@Z
 * Address: 0x140164CE0
 */

char __fastcall CGravityStone::Get(CGravityStone *this, unsigned __int16 wIndex, unsigned int dwObjSerial, CPlayer *pkPlayer)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-28h]@1
  CGravityStone *v8; // [sp+30h] [bp+8h]@1
  CPlayer *pkPlayera; // [sp+48h] [bp+20h]@1

  pkPlayera = pkPlayer;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v8->m_bLive )
  {
    if ( v8->m_ObjID.m_wIndex == wIndex && v8->m_dwObjSerial == dwObjSerial )
    {
      if ( CPlayer::IsRidingUnit(pkPlayer) )
      {
        result = -117;
      }
      else if ( CGravityStone::IsNearPosition(v8, pkPlayera->m_fCurPos) )
      {
        CGravityStone::SetOwner(v8, pkPlayera);
        CGravityStone::Destroy(v8);
        result = 0;
      }
      else
      {
        result = -126;
      }
    }
    else
    {
      result = -124;
    }
  }
  else
  {
    result = -124;
  }
  return result;
}
