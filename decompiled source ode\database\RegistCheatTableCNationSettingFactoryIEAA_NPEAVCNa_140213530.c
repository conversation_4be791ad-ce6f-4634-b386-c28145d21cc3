/*
 * Function: ?RegistCheatTable@CNationSettingFactory@@IEAA_NPEAVCNationSettingData@@@Z
 * Address: 0x140213530
 */

char __fastcall CNationSettingFactory::RegistCheatTable(CNationSettingFactory *this, CNationSettingData *pkData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CNationSettingFactory *v6; // [sp+30h] [bp+8h]@1
  CNationSettingData *pkDataa; // [sp+38h] [bp+10h]@1

  pkDataa = pkData;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CNationSettingFactory::RegistCheatTableUnion(v6, pkData) )
  {
    if ( CNationSettingFactory::RegistCheatTableOnlyInternal(v6, pkDataa) )
    {
      CNationSettingFactory::RegistCheatEndRecord(v6, pkDataa);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
