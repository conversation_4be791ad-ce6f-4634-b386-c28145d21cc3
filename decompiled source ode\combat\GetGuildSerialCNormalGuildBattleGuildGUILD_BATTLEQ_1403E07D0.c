/*
 * Function: ?GetGuildSerial@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAKXZ
 * Address: 0x1403E07D0
 */

__int64 __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(GUILD_BATTLE::CNormalGuildBattleGuild *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // [sp+0h] [bp-18h]@1
  GUILD_BATTLE::CNormalGuildBattleGuild *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = (int *)&v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  if ( v5->m_pkGuild )
    v4 = v5->m_pkGuild->m_dwSerial;
  else
    v4 = 0;
  return v4;
}
