/*
 * Function: ?DrawMap@CMapDisplay@@AEAAXXZ
 * Address: 0x14019F450
 */

void __fastcall CMapDisplay::DrawMap(CMapDisplay *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  IDirectDrawSurface7 *v3; // rax@4
  __int64 v4; // [sp+0h] [bp-48h]@1
  __int64 v5; // [sp+20h] [bp-28h]@4
  int v6; // [sp+28h] [bp-20h]@4
  IDirectDrawSurface7 *v7; // [sp+30h] [bp-18h]@4
  IUnknownVtbl *v8; // [sp+38h] [bp-10h]@4
  CMapDisplay *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = CSurface::GetDDrawSurface(v9->m_pSFMap);
  v3 = CSurface::GetDDrawSurface(v9->m_pSFBuf);
  v8 = v7->vfptr;
  v6 = 0;
  v5 = 0i64;
  ((void (__fastcall *)(IDirectDrawSurface7 *, _QWORD, _QWORD, IDirectDrawSurface7 *))v8[2].AddRef)(v7, 0i64, 0i64, v3);
}
