/*
 * Function: ?RecvClientLine@CHackShieldExSystem@@UEAA_NHPEAU_MSG_HEADER@@PEAD@Z
 * Address: 0x1404172C0
 */

bool __fastcall CHackShieldExSystem::RecvClientLine(CHackShieldExSystem *this, int n, _MSG_HEADER *pHeader, char *pMsg)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  BASE_HACKSHEILD_PARAMVtbl *v7; // rax@9
  __int64 v8; // [sp+0h] [bp-48h]@1
  __int64 v9; // [sp+20h] [bp-28h]@9
  char *v10; // [sp+28h] [bp-20h]@9
  BASE_HACKSHEILD_PARAM *v11; // [sp+30h] [bp-18h]@7
  __int64 v12; // [sp+38h] [bp-10h]@9
  CHackShieldExSystem *v13; // [sp+50h] [bp+8h]@1
  int na; // [sp+58h] [bp+10h]@1
  _MSG_HEADER *v15; // [sp+60h] [bp+18h]@1
  char *v16; // [sp+68h] [bp+20h]@1

  v16 = pMsg;
  v15 = pHeader;
  na = n;
  v13 = this;
  v4 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v13->m_bInit && v13->m_bActive )
  {
    v11 = CHackShieldExSystem::GetParam(v13, n);
    if ( v11 )
    {
      v12 = v15->m_wSize - 4i64;
      v7 = v11->vfptr;
      v10 = v16;
      v9 = v12;
      result = ((int (__fastcall *)(BASE_HACKSHEILD_PARAM *, CHackShieldExSystem *, _QWORD, _QWORD))v7->OnRecvSession)(
                 v11,
                 v13,
                 (unsigned int)na,
                 v15->m_byType[1]);
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
