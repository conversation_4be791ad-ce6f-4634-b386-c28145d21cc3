/*
 * Function: j_??$_Copy_backward_opt@PEAPEAVCLogTypeDBTask@@PEAPEAV1@Urandom_access_iterator_tag@std@@@std@@YAPEAPEAVCLogTypeDBTask@@PEAPEAV1@00Urandom_access_iterator_tag@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400109DD
 */

CLogTypeDBTask **__fastcall std::_Copy_backward_opt<CLogTypeDBTask * *,CLogTypeDBTask * *,std::random_access_iterator_tag>(CLogTypeDBTask **_First, CLogTypeDBTask **_Last, CLogTypeDBTask **_Dest, std::random_access_iterator_tag __formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Copy_backward_opt<CLogTypeDBTask * *,C<PERSON>ogTypeDBTask * *,std::random_access_iterator_tag>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
