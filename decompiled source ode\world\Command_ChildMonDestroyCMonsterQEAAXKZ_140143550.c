/*
 * Function: ?Command_Child<PERSON>onD<PERSON>roy@CMonster@@QEAAXK@Z
 * Address: 0x140143550
 */

void __fastcall CMonster::Command_ChildMonDestroy(CMonster *this, unsigned int dwAfterKillTerm)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMonster *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v5->m_bLive )
  {
    v5->m_dwDestroyNextTime = dwAfterKillTerm + GetLoopTime();
    CMonsterHierarchy::SetParent(&v5->m_MonHierarcy, 0i64);
  }
}
