/*
 * Function: j_?construct@?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@std@@QEAAXPEAU?$pair@$$CBHPEBU_TimeItem_fld@@@2@AEBU32@@Z
 * Address: 0x140001ABE
 */

void __fastcall std::allocator<std::pair<int const,_TimeItem_fld const *>>::construct(std::allocator<std::pair<int const ,_TimeItem_fld const *> > *this, std::pair<int const ,_TimeItem_fld const *> *_Ptr, std::pair<int const ,_TimeItem_fld const *> *_Val)
{
  std::allocator<std::pair<int const,_TimeItem_fld const *>>::construct(this, _Ptr, _Val);
}
