/*
 * Function: ?SetWinnerInfo@CBattleTournamentInfo@@QEAA_NKPEADE@Z
 * Address: 0x1403FEB70
 */

char __fastcall CBattleTournamentInfo::SetWinnerInfo(CBattleTournamentInfo *this, unsigned int dwSerial, char *pwszCharName, char byGrade)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-28h]@1
  CBattleTournamentInfo *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v8->m_nCurNum < 48 )
  {
    v8->m_WinnerInfo[v8->m_nCurNum].dwSerial = dwSerial;
    v8->m_WinnerInfo[v8->m_nCurNum].byGrade = byGrade;
    strcpy_s(v8->m_WinnerInfo[v8->m_nCurNum].wszCharName, 0x11ui64, pwszCharName);
    ++v8->m_nCurNum;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
