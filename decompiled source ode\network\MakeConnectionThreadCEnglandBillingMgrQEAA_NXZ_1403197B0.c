/*
 * Function: ?MakeConnectionThread@CEnglandBillingMgr@@QEAA_NXZ
 * Address: 0x1403197B0
 */

HANDLE __fastcall CEnglandBillingMgr::MakeConnectionThread(CEnglandBillingMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  HANDLE result; // rax@4
  __int64 v4; // [sp+0h] [bp-58h]@1
  unsigned int ThreadId; // [sp+34h] [bp-24h]@4
  CEnglandBillingMgr *lpParameter; // [sp+60h] [bp+8h]@1

  lpParameter = this;
  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  ThreadId = 0;
  result = CreateThread(0i64, 0i64, (LPTHREAD_START_ROUTINE)SendLoop, lpParameter, 4u, &ThreadId);
  m_hThread = result;
  LOBYTE(result) = 0;
  return result;
}
