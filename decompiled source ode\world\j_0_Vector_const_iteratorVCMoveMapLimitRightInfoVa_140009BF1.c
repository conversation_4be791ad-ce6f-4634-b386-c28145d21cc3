/*
 * Function: j_??0?$_Vector_const_iterator@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEAA@AEBV01@@Z
 * Address: 0x140009BF1
 */

void __fastcall std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *__that)
{
  std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(
    this,
    __that);
}
