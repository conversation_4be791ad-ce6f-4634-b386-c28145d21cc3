/*
 * Function: ?AuthMiningTicket@CHolyStoneSystem@@QEAA_NI@Z
 * Address: 0x14027DBD0
 */

bool __fastcall CHolyStoneSystem::AuthMiningTicket(CHolyStoneSystem *this, unsigned int dwKey)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@4
  __int64 v6; // [sp+0h] [bp-58h]@1
  MiningTicket::_AuthKeyTicket v7; // [sp+34h] [bp-24h]@4
  char v8; // [sp+44h] [bp-14h]@4
  char v9; // [sp+45h] [bp-13h]@4
  char v10; // [sp+46h] [bp-12h]@4
  char v11; // [sp+47h] [bp-11h]@4
  CHolyStoneSystem *v12; // [sp+60h] [bp+8h]@1
  unsigned int v13; // [sp+68h] [bp+10h]@1

  v13 = dwKey;
  v12 = this;
  v2 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = CHolyStoneSystem::GetNumOfTime(v12);
  v9 = CHolyStoneSystem::GetStartHour(v12);
  v10 = CHolyStoneSystem::GetStartDay(v12);
  v11 = CHolyStoneSystem::GetStartMonth(v12);
  v4 = CHolyStoneSystem::GetStartYear(v12);
  MiningTicket::_AuthKeyTicket::Set(&v7, v4, v11, v10, v9, v8);
  return v7.0 == v13;
}
