/*
 * Function: j_??$_Copy_opt@V?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@V12@Urandom_access_iterator_tag@2@@std@@YA?AV?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@0@V10@00Urandom_access_iterator_tag@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140008198
 */

std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::_Copy_opt<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::random_access_iterator_tag>(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *result, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_First, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Last, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Dest, std::random_access_iterator_tag __formal, std::_Nonscalar_ptr_iterator_tag a6, std::_Range_checked_iterator_tag a7)
{
  return std::_Copy_opt<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::random_access_iterator_tag>(
           result,
           _First,
           _Last,
           _Dest,
           __formal,
           a6,
           a7);
}
