/*
 * Function: ?Destro<PERSON><PERSON><PERSON><PERSON>eeper@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x14027DB80
 */

void __fastcall CHolyStoneSystem::DestroyHolyKeeper(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1

  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( g_Keeper->m_bLive )
    CHolyKeeper::Exit(g_Keeper);
}
