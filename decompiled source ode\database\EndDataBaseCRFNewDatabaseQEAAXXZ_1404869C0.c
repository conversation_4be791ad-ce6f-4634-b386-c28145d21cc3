/*
 * Function: ?EndDataBase@CRFNewDatabase@@QEAAXXZ
 * Address: 0x1404869C0
 */

void __fastcall CRFNewDatabase::EndDataBase(CRFNewDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CRFNewDatabase *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CRFNewDatabase::Log(v4, " ");
  CRFNewDatabase::Log(v4, "EndDatabase : Start");
  if ( v4->m_bConectionActive )
  {
    CRFNewDatabase::Log(v4, "EndDatabase() : FreeHandle() Start");
    CRFNewDatabase::FreeSelectHandle(v4);
    CRFNewDatabase::FreeUpdateHandle(v4);
    if ( v4->m_hDbc )
      SQLDisconnect_0(v4->m_hDbc);
    if ( v4->m_hEnv )
      SQLFreeHandle_0(1, v4->m_hEnv);
    if ( v4->m_hDbc )
      SQLFreeHandle_0(2, v4->m_hDbc);
    CRFNewDatabase::Log(v4, "EndDatabase() : FreeHandle() End");
  }
  v4->m_bConectionActive = 0;
  v4->m_hEnv = 0i64;
  v4->m_hDbc = 0i64;
  v4->m_hStmtSelect = 0i64;
  v4->m_hStmtUpdate = 0i64;
  CRFNewDatabase::Log(v4, "EndDatabase : End");
}
