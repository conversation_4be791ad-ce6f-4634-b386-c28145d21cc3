/*
 * Function: ?Select_PostRegistryData@CRFWorldDatabase@@QEAAEKPEAVCPostData@@@Z
 * Address: 0x1404B11C0
 */

char __fastcall CRFWorldDatabase::Select_PostRegistryData(CRFWorldDatabase *this, unsigned int dwMax, CPostData *pPostData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-168h]@1
  void *SQLStmt; // [sp+20h] [bp-148h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-140h]@16
  SQLLEN v9; // [sp+38h] [bp-130h]@16
  __int16 v10; // [sp+44h] [bp-124h]@9
  int v11; // [sp+48h] [bp-120h]@4
  char DstBuf; // [sp+60h] [bp-108h]@4
  char v13; // [sp+61h] [bp-107h]@4
  char v14; // [sp+B4h] [bp-B4h]@17
  char v15; // [sp+D4h] [bp-94h]@17
  unsigned int TargetValue; // [sp+F4h] [bp-74h]@16
  int pl_nKey; // [sp+114h] [bp-54h]@17
  char v18; // [sp+134h] [bp-34h]@17
  unsigned __int64 v19; // [sp+150h] [bp-18h]@4
  CRFWorldDatabase *v20; // [sp+170h] [bp+8h]@1
  unsigned int v21; // [sp+178h] [bp+10h]@1
  CPostData *v22; // [sp+180h] [bp+18h]@1

  v22 = pPostData;
  v21 = dwMax;
  v20 = this;
  v3 = &v6;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v19 = (unsigned __int64)&v6 ^ _security_cookie;
  v11 = 0;
  DstBuf = 0;
  memset(&v13, 0, 0x3Fui64);
  sprintf_s(&DstBuf, 0x40ui64, "select * from tbl_PostRegistry where dck=0");
  if ( v20->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v20->vfptr, &DstBuf);
  if ( v20->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v20->vfptr) )
  {
    v10 = SQLExecDirectA_0(v20->m_hStmtSelect, &DstBuf, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v20->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v20->vfptr, v10, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v20->vfptr, v10, v20->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      while ( 1 )
      {
        v10 = SQLFetch_0(v20->m_hStmtSelect);
        if ( v10 )
        {
          if ( v10 != 1 )
            break;
        }
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v20->m_hStmtSelect, 1u, -18, &TargetValue, 0i64, &v9);
        if ( TargetValue < v21 )
        {
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v20->m_hStmtSelect, 2u, -7, &v18, 0i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v20->m_hStmtSelect, 3u, 4, &v22[TargetValue].m_dwSenderSerial, 0i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = (void *)17;
          v10 = SQLGetData_0(v20->m_hStmtSelect, 4u, 1, v22[TargetValue].m_wszSendName, 17i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = (void *)17;
          v10 = SQLGetData_0(v20->m_hStmtSelect, 5u, 1, v22[TargetValue].m_wszRecvName, 17i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = (void *)21;
          v10 = SQLGetData_0(v20->m_hStmtSelect, 6u, 1, v22[TargetValue].m_wszTitle, 21i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = (void *)201;
          v10 = SQLGetData_0(v20->m_hStmtSelect, 7u, 1, v22[TargetValue].m_wszContent, 201i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v20->m_hStmtSelect, 8u, 4, &pl_nKey, 0i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v20->m_hStmtSelect, 9u, -25, &v22[TargetValue].m_dwDur, 0i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v20->m_hStmtSelect, 0xAu, 4, &v22[TargetValue].m_dwUpt, 0i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v20->m_hStmtSelect, 0xBu, 4, &v22[TargetValue].m_dwGold, 0i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v20->m_hStmtSelect, 0xCu, 5, &v14, 0i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v20->m_hStmtSelect, 0xDu, 5, &v15, 0i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v20->m_hStmtSelect, 0xEu, -25, &v22[TargetValue].m_lnUID, 0i64, &v9);
          v22[TargetValue].m_bySendRace = v14;
          v22[TargetValue].m_bySenderDgr = v15;
          _INVENKEY::LoadDBKey(&v22[TargetValue].m_Key, pl_nKey);
          v22[TargetValue].m_byState = 0;
        }
      }
      if ( v20->m_hStmtSelect )
        SQLCloseCursor_0(v20->m_hStmtSelect);
      if ( v20->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v20->vfptr, "%s Success", &DstBuf);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v20->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1;
  }
  return result;
}
