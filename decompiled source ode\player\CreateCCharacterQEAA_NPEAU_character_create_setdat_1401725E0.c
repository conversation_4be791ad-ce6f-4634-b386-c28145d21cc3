/*
 * Function: ?Create@<PERSON>haracter@@QEAA_NPEAU_character_create_setdata@@@Z
 * Address: 0x1401725E0
 */

char __fastcall CCharacter::Create(CCharacter *this, _character_create_setdata *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CCharacter *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CGameObject::Create((CGameObject *)&v6->vfptr, (_object_create_setdata *)&pData->m_pRecordSet) )
  {
    memcpy_0(v6->m_fTarPos, v6->m_fCurPos, 0xCui64);
    v6->m_dwNextGenAttackTime = GetLoopTime();
    v6->m_nContEffectSec = -1;
    CMyTimer::BeginTimer(&v6->m_tmrSFCont, 0x3E8u);
    v6->m_wEffectTempValue = 0;
    v6->m_dwEffSerialCounter = 1;
    memset_0(v6->m_SFCont, 0, 0x300ui64);
    memset_0(v6->m_SFContAura, 0, 0x300ui64);
    v6->m_bLastContEffectUpdate = 0;
    v6->m_wLastContEffect = -1;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
