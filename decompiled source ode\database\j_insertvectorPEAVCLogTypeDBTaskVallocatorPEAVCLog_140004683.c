/*
 * Function: j_?insert@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAA?AV?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@2@V32@AEBQEAVCLogTypeDBTask@@@Z
 * Address: 0x140004683
 */

std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *__fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::insert(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *result, std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_Where, CLogTypeDBTask *const *_Val)
{
  return std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::insert(this, result, _Where, _Val);
}
