/*
 * Function: ?Initialize@?$DL_GroupParameters_EC@VECP@CryptoPP@@@CryptoPP@@QEAAXAEBVECP@2@AEBUECPPoint@2@AEBVInteger@2@2@Z
 * Address: 0x140556840
 */

int __fastcall CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::Initialize(__int64 a1, __int64 a2, __int64 a3)
{
  __int64 v4; // [sp+30h] [bp+8h]@1
  __int64 v5; // [sp+40h] [bp+18h]@1

  v5 = a3;
  v4 = a1;
  CryptoPP::EcPrecomputation<CryptoPP::ECP>::SetCurve(a1 + 24);
  (*(void (__fastcall **)(__int64, __int64))(*(_QWORD *)v4 + 16i64))(v4, v5);
  CryptoPP::Integer::operator=(v4 + 272);
  return CryptoPP::Integer::operator=(v4 + 320);
}
