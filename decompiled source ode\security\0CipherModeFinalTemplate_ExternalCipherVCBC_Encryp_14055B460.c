/*
 * Function: ??0?$CipherModeFinalTemplate_ExternalCipher@VCBC_Encryption@CryptoPP@@@CryptoPP@@QEAA@AEAV?$SimpleKeyedTransformation@VBlockTransformation@CryptoPP@@@1@@Z
 * Address: 0x14055B460
 */

CryptoPP::CBC_Encryption *__fastcall CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_Encryption>::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_Encryption>(CryptoPP::CBC_Encryption *a1, __int64 a2)
{
  CryptoPP::CBC_Encryption *v3; // [sp+40h] [bp+8h]@1
  __int64 v4; // [sp+48h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::CBC_Encryption::CBC_Encryption(a1);
  v3->vfptr = (CryptoPP::ClonableVtbl *)&CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_Encryption>::`vftable'{for `CryptoPP::StreamTransformation'};
  v3->vfptr = (CryptoPP::SimpleKeyingInterfaceVtbl *)&CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_Encryption>::`vftable'{for `CryptoPP::SimpleKeyingInterface'};
  CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_Encryption>::SetCipher(v3, v4);
  return v3;
}
