/*
 * Function: ?CompleteCreate@CUnmannedTraderUserInfoTable@@QEAAXG@Z
 * Address: 0x140363B30
 */

void __fastcall CUnmannedTraderUserInfoTable::CompleteCreate(CUnmannedTraderUserInfoTable *this, unsigned __int16 wInx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfo *v4; // rax@7
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderUserInfoTable *v6; // [sp+30h] [bp+8h]@1
  unsigned __int16 v7; // [sp+38h] [bp+10h]@1

  v7 = wInx;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( !std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::empty(&v6->m_veckInfo)
    && std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::size(&v6->m_veckInfo) > v7 )
  {
    v4 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator[](&v6->m_veckInfo, v7);
    CUnmannedTraderUserInfo::CompleteCreate(v4, v6->m_pkLogger);
  }
}
