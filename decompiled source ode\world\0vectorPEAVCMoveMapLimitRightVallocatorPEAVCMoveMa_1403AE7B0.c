/*
 * Function: ??0?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEAA@XZ
 * Address: 0x1403AE7B0
 */

void __fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<CMoveMapLimitRight *> v3; // al@4
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  std::allocator<CMoveMapLimitRight *> *v6; // [sp+28h] [bp-10h]@4
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (std::allocator<CMoveMapLimitRight *> *)&v5;
  std::allocator<CMoveMapLimitRight *>::allocator<CMoveMapLimitRight *>((std::allocator<CMoveMapLimitRight *> *)&v5);
  std::_Vector_val<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Vector_val<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(
    (std::_Vector_val<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)&v7->_Myfirstiter,
    v3);
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Buy(v7, 0i64);
}
