/*
 * Function: SQL<PERSON>riteFileDSNW
 * Address: 0x1404DB3D4
 */

int __fastcall SQLWriteFileDSNW(const unsigned __int16 *lpszFileName, const unsigned __int16 *lpszAppName, const unsigned __int16 *lpszKeyName, const unsigned __int16 *lpszString)
{
  const unsigned __int16 *v4; // rbp@1
  const unsigned __int16 *v5; // rbx@1
  const unsigned __int16 *v6; // rdi@1
  const unsigned __int16 *v7; // rsi@1
  __int64 (__cdecl *v8)(); // rax@1
  int result; // eax@2

  v4 = lpszFileName;
  v5 = lpszString;
  v6 = lpszKeyName;
  v7 = lpszAppName;
  v8 = ODBC___GetSetupProc("SQLWriteFileDSNW");
  if ( v8 )
    result = ((int (__fastcall *)(const unsigned __int16 *, const unsigned __int16 *, const unsigned __int16 *, const unsigned __int16 *))v8)(
               v4,
               v7,
               v6,
               v5);
  else
    result = 0;
  return result;
}
