/*
 * Function: ?Get_BoxItem_Count@CGoldenBoxItemMgr@@QEAAGEK@Z
 * Address: 0x140414070
 */

unsigned __int16 __fastcall CGoldenBoxItemMgr::Get_BoxItem_Count(CGoldenBoxItemMgr *this, char byIndex, unsigned int dwIndex)
{
  int *v3; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CGoldenBoxItemMgr *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v3 = &j;
  for ( i = 4i64; i; --i )
  {
    *v3 = -858993460;
    ++v3;
  }
  for ( j = 0; j < v7->m_golden_box_item.m_bygolden_item_num[(unsigned __int8)byIndex]; ++j )
  {
    if ( v7->m_golden_box_item.m_golden_box_item_info[(unsigned __int8)byIndex][j].m_dwIndex == dwIndex )
      return v7->m_golden_box_item.m_golden_box_item_info[(unsigned __int8)byIndex][j].m_wNum;
  }
  return 0;
}
