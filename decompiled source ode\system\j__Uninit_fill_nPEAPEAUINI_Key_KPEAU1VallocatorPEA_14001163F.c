/*
 * Function: j_??$_Uninit_fill_n@PEAPEAUINI_Key@@_KPEAU1@V?$allocator@P<PERSON>UI<PERSON>_Key@@@std@@@std@@YAXPEAPEAUINI_Key@@_KAEBQEAU1@AEAV?$allocator@PEAUINI_Key@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14001163F
 */

void __fastcall std::_Uninit_fill_n<INI_Key * *,unsigned __int64,INI_Key *,std::allocator<INI_Key *>>(INI_Key **_First, unsigned __int64 _Count, INI_Key *const *_Val, std::allocator<INI_Key *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<INI_Key * *,unsigned __int64,INI_Key *,std::allocator<INI_Key *>>(
    _First,
    _Count,
    _Val,
    __formal,
    a5,
    a6);
}
