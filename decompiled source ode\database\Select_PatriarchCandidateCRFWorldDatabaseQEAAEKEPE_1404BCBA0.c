/*
 * Function: ?Select_PatriarchCandidate@CRFWorldDatabase@@QEAAEKEPEAU_candidate_info@@@Z
 * Address: 0x1404BCBA0
 */

char __fastcall CRFWorldDatabase::Select_PatriarchCandidate(CRFWorldDatabase *this, unsigned int dwSerial, char byRace, _candidate_info *p)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v7; // [sp+0h] [bp-298h]@1
  void *SQLStmt; // [sp+20h] [bp-278h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-270h]@24
  char Dest; // [sp+40h] [bp-258h]@4
  SQLLEN v11; // [sp+258h] [bp-40h]@24
  __int16 v12; // [sp+264h] [bp-34h]@9
  int j; // [sp+268h] [bp-30h]@14
  char v14; // [sp+26Ch] [bp-2Ch]@18
  char v15; // [sp+26Dh] [bp-2Bh]@26
  char v16; // [sp+26Eh] [bp-2Ah]@34
  char v17; // [sp+26Fh] [bp-29h]@42
  char v18; // [sp+270h] [bp-28h]@50
  char v19; // [sp+271h] [bp-27h]@58
  char v20; // [sp+272h] [bp-26h]@66
  char v21; // [sp+273h] [bp-25h]@74
  char v22; // [sp+274h] [bp-24h]@82
  char v23; // [sp+275h] [bp-23h]@90
  char v24; // [sp+276h] [bp-22h]@98
  char v25; // [sp+277h] [bp-21h]@106
  char v26; // [sp+278h] [bp-20h]@114
  char v27; // [sp+279h] [bp-1Fh]@122
  unsigned __int64 v28; // [sp+288h] [bp-10h]@4
  CRFWorldDatabase *v29; // [sp+2A0h] [bp+8h]@1
  _candidate_info *v30; // [sp+2B8h] [bp+20h]@1

  v30 = p;
  v29 = this;
  v4 = &v7;
  for ( i = 164i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v28 = (unsigned __int64)&v7 ^ _security_cookie;
  sprintf(
    &Dest,
    "select Race, Lv, Rank, Grade, PvpPoint, ASerial, AName, GSerial, GName, WinCnt, Score, ClassType, State from [dbo].["
    "tbl_patriarch_candidate] where eSerial = %d and race = %d ",
    dwSerial,
    (unsigned __int8)byRace);
  if ( v29->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v29->vfptr, &Dest);
  if ( v29->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v29->vfptr) )
  {
    v12 = SQLExecDirect_0(v29->m_hStmtSelect, &Dest, -3);
    if ( v12 && v12 != 1 )
    {
      if ( v12 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v29->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v29->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v29->vfptr, v12, v29->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      for ( j = 0; j < 500; ++j )
      {
        v12 = SQLFetch_0(v29->m_hStmtSelect);
        if ( v12 && v12 != 1 )
        {
          v14 = 0;
          if ( v12 == 100 )
          {
            v14 = 2;
          }
          else
          {
            SQLStmt = v29->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v29->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v29->vfptr, v12, v29->m_hStmtSelect);
            v14 = 1;
          }
          if ( v29->m_hStmtSelect )
            SQLCloseCursor_0(v29->m_hStmtSelect);
          return v14;
        }
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v29->m_hStmtSelect, 1u, -6, &v30[j].byRace, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          v15 = 0;
          if ( v12 == 100 )
          {
            v15 = 2;
          }
          else
          {
            SQLStmt = v29->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v29->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v29->vfptr, v12, v29->m_hStmtSelect);
            v15 = 1;
          }
          if ( v29->m_hStmtSelect )
            SQLCloseCursor_0(v29->m_hStmtSelect);
          return v15;
        }
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v29->m_hStmtSelect, 2u, -6, &v30[j].byLevel, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          v16 = 0;
          if ( v12 == 100 )
          {
            v16 = 2;
          }
          else
          {
            SQLStmt = v29->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v29->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v29->vfptr, v12, v29->m_hStmtSelect);
            v16 = 1;
          }
          if ( v29->m_hStmtSelect )
            SQLCloseCursor_0(v29->m_hStmtSelect);
          return v16;
        }
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v29->m_hStmtSelect, 3u, 4, &v30[j].dwRank, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          v17 = 0;
          if ( v12 == 100 )
          {
            v17 = 2;
          }
          else
          {
            SQLStmt = v29->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v29->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v29->vfptr, v12, v29->m_hStmtSelect);
            v17 = 1;
          }
          if ( v29->m_hStmtSelect )
            SQLCloseCursor_0(v29->m_hStmtSelect);
          return v17;
        }
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v29->m_hStmtSelect, 4u, -6, &v30[j].byGrade, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          v18 = 0;
          if ( v12 == 100 )
          {
            v18 = 2;
          }
          else
          {
            SQLStmt = v29->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v29->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v29->vfptr, v12, v29->m_hStmtSelect);
            v18 = 1;
          }
          if ( v29->m_hStmtSelect )
            SQLCloseCursor_0(v29->m_hStmtSelect);
          return v18;
        }
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v29->m_hStmtSelect, 5u, 8, &v30[j].dPvpPoint, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          v19 = 0;
          if ( v12 == 100 )
          {
            v19 = 2;
          }
          else
          {
            SQLStmt = v29->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v29->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v29->vfptr, v12, v29->m_hStmtSelect);
            v19 = 1;
          }
          if ( v29->m_hStmtSelect )
            SQLCloseCursor_0(v29->m_hStmtSelect);
          return v19;
        }
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v29->m_hStmtSelect, 6u, 4, &v30[j].dwAvatorSerial, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          v20 = 0;
          if ( v12 == 100 )
          {
            v20 = 2;
          }
          else
          {
            SQLStmt = v29->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v29->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v29->vfptr, v12, v29->m_hStmtSelect);
            v20 = 1;
          }
          if ( v29->m_hStmtSelect )
            SQLCloseCursor_0(v29->m_hStmtSelect);
          return v20;
        }
        StrLen_or_IndPtr = &v11;
        SQLStmt = (void *)17;
        v12 = SQLGetData_0(v29->m_hStmtSelect, 7u, 1, v30[j].wszName, 17i64, &v11);
        if ( v12 && v12 != 1 )
        {
          v21 = 0;
          if ( v12 == 100 )
          {
            v21 = 2;
          }
          else
          {
            SQLStmt = v29->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v29->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v29->vfptr, v12, v29->m_hStmtSelect);
            v21 = 1;
          }
          if ( v29->m_hStmtSelect )
            SQLCloseCursor_0(v29->m_hStmtSelect);
          return v21;
        }
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v29->m_hStmtSelect, 8u, 4, &v30[j].dwGuildSerial, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          v22 = 0;
          if ( v12 == 100 )
          {
            v22 = 2;
          }
          else
          {
            SQLStmt = v29->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v29->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v29->vfptr, v12, v29->m_hStmtSelect);
            v22 = 1;
          }
          if ( v29->m_hStmtSelect )
            SQLCloseCursor_0(v29->m_hStmtSelect);
          return v22;
        }
        StrLen_or_IndPtr = &v11;
        SQLStmt = (void *)17;
        v12 = SQLGetData_0(v29->m_hStmtSelect, 9u, 1, v30[j].wszGuildName, 17i64, &v11);
        if ( v12 && v12 != 1 )
        {
          v23 = 0;
          if ( v12 == 100 )
          {
            v23 = 2;
          }
          else
          {
            SQLStmt = v29->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v29->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v29->vfptr, v12, v29->m_hStmtSelect);
            v23 = 1;
          }
          if ( v29->m_hStmtSelect )
            SQLCloseCursor_0(v29->m_hStmtSelect);
          return v23;
        }
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v29->m_hStmtSelect, 0xAu, 4, &v30[j].dwWinCnt, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          v24 = 0;
          if ( v12 == 100 )
          {
            v24 = 2;
          }
          else
          {
            SQLStmt = v29->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v29->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v29->vfptr, v12, v29->m_hStmtSelect);
            v24 = 1;
          }
          if ( v29->m_hStmtSelect )
            SQLCloseCursor_0(v29->m_hStmtSelect);
          return v24;
        }
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v29->m_hStmtSelect, 0xBu, 4, &v30[j].dwScore, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          v25 = 0;
          if ( v12 == 100 )
          {
            v25 = 2;
          }
          else
          {
            SQLStmt = v29->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v29->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v29->vfptr, v12, v29->m_hStmtSelect);
            v25 = 1;
          }
          if ( v29->m_hStmtSelect )
            SQLCloseCursor_0(v29->m_hStmtSelect);
          return v25;
        }
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v29->m_hStmtSelect, 0xCu, -6, &v30[j].eClassType, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          v26 = 0;
          if ( v12 == 100 )
          {
            v26 = 2;
          }
          else
          {
            SQLStmt = v29->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v29->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v29->vfptr, v12, v29->m_hStmtSelect);
            v26 = 1;
          }
          if ( v29->m_hStmtSelect )
            SQLCloseCursor_0(v29->m_hStmtSelect);
          return v26;
        }
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v29->m_hStmtSelect, 0xDu, -6, &v30[j].eStatus, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          v27 = 0;
          if ( v12 == 100 )
          {
            v27 = 2;
          }
          else
          {
            SQLStmt = v29->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v29->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v29->vfptr, v12, v29->m_hStmtSelect);
            v27 = 1;
          }
          if ( v29->m_hStmtSelect )
            SQLCloseCursor_0(v29->m_hStmtSelect);
          return v27;
        }
      }
      if ( v29->m_hStmtSelect )
        SQLCloseCursor_0(v29->m_hStmtSelect);
      if ( v29->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v29->vfptr, "%s Success", &Dest);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v29->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
