/*
 * Function: ?_<PERSON><PERSON><PERSON>axFP@CPlayer@@QEAAHXZ
 * Address: 0x14005CDF0
 */

__int64 __fastcall CPlayer::_CalcMaxFP(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  float v3; // xmm0_4@8
  float v4; // xmm0_4@8
  float v5; // xmm0_4@8
  float v6; // xmm0_4@8
  float v7; // xmm0_4@8
  float v8; // xmm0_4@9
  float v9; // xmm0_4@9
  float v10; // xmm0_4@9
  float v11; // xmm0_4@9
  float v12; // xmm0_4@9
  float v13; // xmm0_4@11
  float v14; // xmm0_4@11
  __int64 v16; // [sp+0h] [bp-68h]@1
  float v17; // [sp+20h] [bp-48h]@4
  int v18; // [sp+24h] [bp-44h]@4
  float v19; // [sp+28h] [bp-40h]@8
  float v20; // [sp+2Ch] [bp-3Ch]@8
  float v21; // [sp+30h] [bp-38h]@8
  float v22; // [sp+34h] [bp-34h]@8
  float v23; // [sp+38h] [bp-30h]@8
  float v24; // [sp+3Ch] [bp-2Ch]@8
  float v25; // [sp+40h] [bp-28h]@9
  float v26; // [sp+44h] [bp-24h]@9
  float v27; // [sp+48h] [bp-20h]@9
  float v28; // [sp+4Ch] [bp-1Ch]@9
  float v29; // [sp+50h] [bp-18h]@9
  float v30; // [sp+54h] [bp-14h]@9
  float v31; // [sp+58h] [bp-10h]@10
  float v32; // [sp+5Ch] [bp-Ch]@11
  CPlayer *v33; // [sp+70h] [bp+8h]@1

  v33 = this;
  v1 = &v16;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v17 = 0.0;
  v18 = CPlayerDB::GetRaceCode(&v33->m_Param);
  if ( v18 )
  {
    if ( v18 == 1 )
    {
      _MASTERY_PARAM::GetAveForceMasteryPerClass(&v33->m_pmMst, 0);
      v25 = 0.0 * 0.40000001;
      _MASTERY_PARAM::GetAveForceMasteryPerClass(&v33->m_pmMst, 2);
      v8 = v25 + (float)((float)(0.0 * 0.40000001) * 0.075000003);
      v26 = v25 + (float)((float)(0.0 * 0.40000001) * 0.075000003);
      _MASTERY_PARAM::GetAveForceMasteryPerClass(&v33->m_pmMst, 3);
      v9 = v26 + (float)(v8 * 0.1125);
      v27 = v9;
      _MASTERY_PARAM::GetAveForceMasteryPerClass(&v33->m_pmMst, 4);
      v10 = v27 + (float)(v9 * 0.1125);
      v28 = v10;
      _MASTERY_PARAM::GetAveForceMasteryPerClass(&v33->m_pmMst, 5);
      v11 = v28 + (float)(v10 * 0.075000003);
      v29 = v11;
      _MASTERY_PARAM::GetAveSkillMasteryPerClass(&v33->m_pmMst, 0);
      v12 = v29 + (float)(v11 * 0.1125);
      v30 = v12;
      _MASTERY_PARAM::GetAveSkillMasteryPerClass(&v33->m_pmMst, 1);
      v17 = v30 + (float)(v12 * 0.1125);
    }
    else if ( v18 == 2 )
    {
      _MASTERY_PARAM::GetAveSkillMasteryPerClass(&v33->m_pmMst, 0);
      v31 = 0.0 * 0.5;
      _MASTERY_PARAM::GetAveSkillMasteryPerClass(&v33->m_pmMst, 1);
      v17 = v31 + (float)((float)(0.0 * 0.5) * 0.5);
    }
  }
  else
  {
    _MASTERY_PARAM::GetAveForceMasteryPerClass(&v33->m_pmMst, 1);
    v19 = 0.0 * 0.40000001;
    _MASTERY_PARAM::GetAveForceMasteryPerClass(&v33->m_pmMst, 2);
    v3 = v19 + (float)((float)(0.0 * 0.40000001) * 0.075000003);
    v20 = v19 + (float)((float)(0.0 * 0.40000001) * 0.075000003);
    _MASTERY_PARAM::GetAveForceMasteryPerClass(&v33->m_pmMst, 3);
    v4 = v20 + (float)(v3 * 0.1125);
    v21 = v4;
    _MASTERY_PARAM::GetAveForceMasteryPerClass(&v33->m_pmMst, 4);
    v5 = v21 + (float)(v4 * 0.1125);
    v22 = v5;
    _MASTERY_PARAM::GetAveForceMasteryPerClass(&v33->m_pmMst, 5);
    v6 = v22 + (float)(v5 * 0.075000003);
    v23 = v6;
    _MASTERY_PARAM::GetAveSkillMasteryPerClass(&v33->m_pmMst, 0);
    v7 = v23 + (float)(v6 * 0.1125);
    v24 = v7;
    _MASTERY_PARAM::GetAveSkillMasteryPerClass(&v33->m_pmMst, 1);
    v17 = v24 + (float)(v7 * 0.1125);
  }
  v17 = v17 + (float)v33->m_nAddPointByClass[1];
  v32 = (float)CPlayerDB::GetLevel(&v33->m_Param);
  v13 = v17;
  pow(v17, 2);
  v14 = v32 * v13;
  sqrt(v14);
  return (unsigned int)(signed int)ffloor((float)(v14 * 5.0) + 40.0);
}
