/*
 * Function: ?ProcUseMoveScroll@CMoveMapLimitInfoPortal@@AEAAEHPEADPEAVCMoveMapLimitRightInfo@@@Z
 * Address: 0x1403A4520
 */

char __fastcall CMoveMapLimitInfoPortal::ProcUseMoveScroll(CMoveMapLimitInfoPortal *this, int iUserInx, char *pRequest, CMoveMapLimitRightInfo *pkRight)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char v6; // al@7
  __int64 v7; // [sp+0h] [bp-F8h]@1
  std::vector<char *,std::allocator<char *> > *v8; // [sp+20h] [bp-D8h]@4
  std::_Vector_const_iterator<char *,std::allocator<char *> > _Right; // [sp+38h] [bp-C0h]@4
  std::_Vector_const_iterator<char *,std::allocator<char *> > v10; // [sp+68h] [bp-90h]@4
  char *Str1; // [sp+88h] [bp-70h]@6
  std::_Vector_iterator<char *,std::allocator<char *> > result; // [sp+90h] [bp-68h]@4
  std::_Vector_iterator<char *,std::allocator<char *> > v13; // [sp+A8h] [bp-50h]@4
  char v14; // [sp+C0h] [bp-38h]@7
  __int64 v15; // [sp+C8h] [bp-30h]@4
  std::_Vector_iterator<char *,std::allocator<char *> > *v16; // [sp+D0h] [bp-28h]@4
  std::_Vector_const_iterator<char *,std::allocator<char *> > *__that; // [sp+D8h] [bp-20h]@4
  std::_Vector_iterator<char *,std::allocator<char *> > *v18; // [sp+E0h] [bp-18h]@4
  std::_Vector_const_iterator<char *,std::allocator<char *> > *v19; // [sp+E8h] [bp-10h]@4
  CMoveMapLimitInfoPortal *v20; // [sp+100h] [bp+8h]@1
  const char *Str2; // [sp+110h] [bp+18h]@1
  CMoveMapLimitRightInfo *v22; // [sp+118h] [bp+20h]@1

  v22 = pkRight;
  Str2 = pRequest;
  v20 = this;
  v4 = &v7;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v15 = -2i64;
  v8 = &v20->m_vecAllowDummyCode;
  v16 = std::vector<char *,std::allocator<char *>>::end(&v20->m_vecAllowDummyCode, &result);
  __that = (std::_Vector_const_iterator<char *,std::allocator<char *> > *)v16;
  std::_Vector_const_iterator<char *,std::allocator<char *>>::_Vector_const_iterator<char *,std::allocator<char *>>(
    &_Right,
    (std::_Vector_const_iterator<char *,std::allocator<char *> > *)&v16->_Mycont);
  std::_Vector_iterator<char *,std::allocator<char *>>::~_Vector_iterator<char *,std::allocator<char *>>(&result);
  v18 = std::vector<char *,std::allocator<char *>>::begin(v8, &v13);
  v19 = (std::_Vector_const_iterator<char *,std::allocator<char *> > *)v18;
  std::_Vector_const_iterator<char *,std::allocator<char *>>::_Vector_const_iterator<char *,std::allocator<char *>>(
    &v10,
    (std::_Vector_const_iterator<char *,std::allocator<char *> > *)&v18->_Mycont);
  std::_Vector_iterator<char *,std::allocator<char *>>::~_Vector_iterator<char *,std::allocator<char *>>(&v13);
  while ( std::_Vector_const_iterator<char *,std::allocator<char *>>::operator!=(&v10, &_Right) )
  {
    Str1 = *std::_Vector_const_iterator<char *,std::allocator<char *>>::operator*(&v10);
    if ( !strcmp_0(Str1, Str2) )
    {
      v14 = 0;
      std::_Vector_const_iterator<char *,std::allocator<char *>>::~_Vector_const_iterator<char *,std::allocator<char *>>(&v10);
      std::_Vector_const_iterator<char *,std::allocator<char *>>::~_Vector_const_iterator<char *,std::allocator<char *>>(&_Right);
      return v14;
    }
    std::_Vector_const_iterator<char *,std::allocator<char *>>::operator++(&v10);
  }
  std::_Vector_const_iterator<char *,std::allocator<char *>>::~_Vector_const_iterator<char *,std::allocator<char *>>(&v10);
  std::_Vector_const_iterator<char *,std::allocator<char *>>::~_Vector_const_iterator<char *,std::allocator<char *>>(&_Right);
  if ( v22 && CMoveMapLimitRightInfo::IsHaveRight(v22, v20->m_eType) )
    v6 = 0;
  else
    v6 = 10;
  return v6;
}
