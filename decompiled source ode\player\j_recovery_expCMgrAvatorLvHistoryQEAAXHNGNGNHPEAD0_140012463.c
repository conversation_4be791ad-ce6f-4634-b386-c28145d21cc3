/*
 * Function: j_?recovery_exp@CMgrAvatorLvHistory@@QEAAXHNGNGNHPEAD0@Z
 * Address: 0x140012463
 */

void __fastcall CMgrAvatorLvHistory::recovery_exp(CMgrAvatorLvHistory *this, int n, long double dOldExp, unsigned __int16 wOldExpRate, long double dNewExp, unsigned __int16 wNewExpRate, long double dLossExp, int nProbPro, char *pCause, char *pszFileName)
{
  CMgrAvatorLvHistory::recovery_exp(
    this,
    n,
    dOldExp,
    wOldExpRate,
    dNewExp,
    wNewExpRate,
    dLossExp,
    nProbPro,
    pCause,
    pszFileName);
}
