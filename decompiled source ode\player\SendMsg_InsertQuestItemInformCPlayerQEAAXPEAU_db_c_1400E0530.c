/*
 * Function: ?SendMsg_InsertQuestItemInform@CPlayer@@QEAAXPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1400E0530
 */

void __fastcall CPlayer::SendMsg_InsertQuestItemInform(CPlayer *this, _STORAGE_LIST::_db_con *pItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  unsigned __int16 v6; // [sp+39h] [bp-4Fh]@4
  int v7; // [sp+3Bh] [bp-4Dh]@4
  unsigned int v8; // [sp+3Fh] [bp-49h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v10; // [sp+65h] [bp-23h]@4
  CPlayer *v11; // [sp+90h] [bp+8h]@1

  v11 = this;
  v2 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  szMsg = pItem->m_byTableCode;
  v6 = pItem->m_wItemIndex;
  v7 = pItem->m_dwDur;
  v8 = pItem->m_dwLv;
  pbyType = 24;
  v10 = 15;
  CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, &szMsg, 0xBu);
}
