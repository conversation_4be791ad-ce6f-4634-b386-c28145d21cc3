/*
 * Function: j_??0?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@QEAA@XZ
 * Address: 0x14000381E
 */

void __fastcall std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::allocator<CUnmannedTraderGroupDivisionVersionInfo>(std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *this)
{
  std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::allocator<CUnmannedTraderGroupDivisionVersionInfo>(this);
}
