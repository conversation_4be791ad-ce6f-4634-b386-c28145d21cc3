/*
 * Function: ?Join@CGuildBattleController@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x1403D5ED0
 */

void __fastcall CGuildBattleController::Join(CGuildBattleController *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleManager *v4; // rax@7
  __int64 v5; // [sp+0h] [bp-38h]@1
  int n; // [sp+20h] [bp-18h]@4
  unsigned int dwGuildSerial; // [sp+24h] [bp-14h]@7
  unsigned int dwCharacSerial; // [sp+28h] [bp-10h]@7
  int v9; // [sp+2Ch] [bp-Ch]@5

  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  n = pkPlayer->m_ObjID.m_wIndex;
  if ( pkPlayer->m_Param.m_pGuild )
    v9 = pkPlayer->m_Param.m_pGuild->m_dwSerial;
  else
    v9 = -1;
  dwGuildSerial = v9;
  dwCharacSerial = pkPlayer->m_pUserDB->m_dwSerial;
  v4 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
  GUILD_BATTLE::CNormalGuildBattleManager::Join(v4, n, dwGuildSerial, dwCharacSerial);
}
