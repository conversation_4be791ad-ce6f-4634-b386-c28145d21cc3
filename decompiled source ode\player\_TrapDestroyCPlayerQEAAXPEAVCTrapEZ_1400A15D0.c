/*
 * Function: ?_<PERSON><PERSON><PERSON><PERSON><PERSON>@CPlayer@@QEAAXPEAVCTrap@@E@Z
 * Address: 0x1400A15D0
 */

void __fastcall CPlayer::_TrapDestroy(CPlayer *this, CTrap *pTrap, char byDestroyCode)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( _TRAP_PARAM::PopItem(&v6->m_pmTrp, pTrap->m_dwObjSerial) )
    CPlayer::SendMsg_MadeTrapNumInform(v6, v6->m_pmTrp.m_nCount);
}
