/*
 * Function: ?db_input_guild_money_atradetax@CMainThread@@QEAAEKKKPEAN0PEAE@Z
 * Address: 0x1401B1B80
 */

char __fastcall CMainThread::db_input_guild_money_atradetax(CMainThread *this, unsigned int dwPusherSerial, unsigned int dwGuildSerial, unsigned int dwAddDalant, long double *dTotalDalant, long double *dTotalGold, char *byDate)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v10; // ecx@10
  int v11; // edx@10
  unsigned int v12; // edi@10
  __int64 v13; // [sp+0h] [bp-248h]@1
  long double dResultDalant; // [sp+20h] [bp-228h]@10
  long double dResultGold; // [sp+28h] [bp-220h]@10
  char *wszDate; // [sp+30h] [bp-218h]@10
  unsigned int dwAvatorSerial; // [sp+38h] [bp-210h]@10
  char *pwszName; // [sp+40h] [bp-208h]@10
  _worlddb_guild_info::__guild_info pGuildData; // [sp+60h] [bp-1E8h]@6
  CCheckSumGuildData v20; // [sp+1C8h] [bp-80h]@8
  char DstBuf; // [sp+1F8h] [bp-50h]@10
  char v22; // [sp+1F9h] [bp-4Fh]@10
  char v23; // [sp+220h] [bp-28h]@9
  char v24; // [sp+221h] [bp-27h]@11
  char v25; // [sp+222h] [bp-26h]@12
  __int64 v26; // [sp+228h] [bp-20h]@4
  unsigned __int64 v27; // [sp+230h] [bp-18h]@4
  CMainThread *v28; // [sp+250h] [bp+8h]@1
  unsigned int v29; // [sp+258h] [bp+10h]@1
  unsigned int dwGuildSeriala; // [sp+260h] [bp+18h]@1
  signed int dwDalant; // [sp+268h] [bp+20h]@1

  dwDalant = dwAddDalant;
  dwGuildSeriala = dwGuildSerial;
  v29 = dwPusherSerial;
  v28 = this;
  v7 = &v13;
  for ( i = 144i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v26 = -2i64;
  v27 = (unsigned __int64)&v13 ^ _security_cookie;
  if ( CRFWorldDatabase::Update_InputGuildMoney(v28->m_pWorldDB, dwGuildSerial, dwAddDalant, 0) )
  {
    if ( CRFWorldDatabase::Select_GuildData(v28->m_pWorldDB, dwGuildSeriala, &pGuildData) )
    {
      *dTotalDalant = pGuildData.dDalant;
      *dTotalGold = pGuildData.dGold;
      CCheckSumGuildData::CCheckSumGuildData(&v20, dwGuildSeriala);
      CCheckSumGuildData::Encode(&v20, pGuildData.dDalant, pGuildData.dGold);
      if ( CCheckSumGuildData::Update(&v20, v28->m_pWorldDB) )
      {
        DstBuf = 0;
        memset(&v22, 0, 8ui64);
        v10 = (unsigned __int8)byDate[2];
        v11 = (unsigned __int8)byDate[1];
        v12 = (unsigned __int8)*byDate;
        LODWORD(wszDate) = (unsigned __int8)byDate[3];
        LODWORD(dResultGold) = v10;
        LODWORD(dResultDalant) = v11;
        sprintf_s(&DstBuf, 9ui64, "%02d%02d%02d%02d", v12);
        pwszName = 0i64;
        dwAvatorSerial = v29;
        wszDate = &DstBuf;
        dResultGold = *dTotalGold;
        dResultDalant = *dTotalDalant;
        if ( CRFWorldDatabase::Insert_GuildMoneyHistory(
               v28->m_pWorldDB,
               dwGuildSeriala,
               (double)dwDalant,
               0.0,
               dResultDalant,
               dResultGold,
               &DstBuf,
               v29,
               0i64) )
        {
          v25 = 0;
          CCheckSumGuildData::~CCheckSumGuildData(&v20);
          result = v25;
        }
        else
        {
          v24 = 24;
          CCheckSumGuildData::~CCheckSumGuildData(&v20);
          result = v24;
        }
      }
      else
      {
        v23 = 24;
        CCheckSumGuildData::~CCheckSumGuildData(&v20);
        result = v23;
      }
    }
    else
    {
      result = 24;
    }
  }
  else
  {
    result = 24;
  }
  return result;
}
