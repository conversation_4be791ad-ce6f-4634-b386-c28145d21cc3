/*
 * Function: ?Update_GuildMemberCount@CRFWorldDatabase@@QEAA_NKG@Z
 * Address: 0x14049DCD0
 */

bool __fastcall CRFWorldDatabase::Update_GuildMemberCount(CRFWorldDatabase *this, unsigned int dwGuildSerial, unsigned __int16 wMemberNum)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-158h]@1
  char Dest; // [sp+30h] [bp-128h]@4
  char v8; // [sp+31h] [bp-127h]@4
  unsigned __int64 v9; // [sp+140h] [bp-18h]@4
  CRFWorldDatabase *v10; // [sp+160h] [bp+8h]@1

  v10 = this;
  v3 = &v6;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = (unsigned __int64)&v6 ^ _security_cookie;
  Dest = 0;
  memset(&v8, 0, 0xFFui64);
  sprintf(&Dest, "update tbl_Guild set MemberCount = %d where serial = %d", wMemberNum, dwGuildSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &Dest, 0);
}
