/*
 * Function: ?ChangeLayer@CMapDisplay@@QEAA_NG@Z
 * Address: 0x14019F1C0
 */

char __fastcall CMapDisplay::ChangeLayer(CMapDisplay *this, unsigned __int16 wLayerIndex)
{
  char *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  char v5; // [sp+0h] [bp-18h]@1
  CMapDisplay *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 += 4;
  }
  if ( v6->m_bDisplayMode )
  {
    v5 = 0;
    if ( wLayerIndex < v6->m_pActMap->m_pMapSet->m_nLayerNum && v6->m_wLayerIndex != wLayerIndex )
    {
      v6->m_wLayerIndex = wLayerIndex;
      v5 = 1;
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
