/*
 * Function: ?SendRegistItemErrorResult@CUnmannedTraderUserInfo@@QEAAXGEGK@Z
 * Address: 0x140357B10
 */

void __fastcall CUnmannedTraderUserInfo::SendRegistItemErrorResult(CUnmannedTraderUserInfo *this, unsigned __int16 wInx, char byRet, unsigned __int16 wItemSerial, unsigned int dwRetParam1)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  unsigned __int16 v9; // [sp+35h] [bp-43h]@4
  unsigned int v10; // [sp+37h] [bp-41h]@4
  char pbyType; // [sp+54h] [bp-24h]@8
  char v12; // [sp+55h] [bp-23h]@8
  char v13; // [sp+64h] [bp-14h]@4

  v5 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  szMsg = byRet;
  v9 = wItemSerial;
  v10 = -1;
  v13 = byRet;
  if ( byRet == 6 || v13 == 24 || v13 == -56 )
    v10 = dwRetParam1;
  pbyType = 30;
  v12 = 29;
  CNetProcess::LoadSendMsg(unk_1414F2088, wInx, &pbyType, &szMsg, 7u);
}
