/*
 * Function: ?Select_GuildMoneyIOData@CRFWorldDatabase@@QEAA_NKPEAU_worlddb_guild_money_io_info@@@Z
 * Address: 0x14049A5A0
 */

char __fastcall CRFWorldDatabase::Select_GuildMoneyIOData(CRFWorldDatabase *this, unsigned int dwGuildSerial, _worlddb_guild_money_io_info *pGuildIOData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  char v6; // al@20
  __int64 v7; // [sp+0h] [bp-258h]@1
  void *SQLStmt; // [sp+20h] [bp-238h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-230h]@16
  SQLLEN v10; // [sp+38h] [bp-220h]@16
  __int16 v11; // [sp+44h] [bp-214h]@9
  char Dest; // [sp+60h] [bp-1F8h]@4
  int v13; // [sp+164h] [bp-F4h]@4
  long double TargetValue; // [sp+178h] [bp-E0h]@14
  long double v15; // [sp+198h] [bp-C0h]@14
  long double v16; // [sp+1B8h] [bp-A0h]@14
  long double v17; // [sp+1D8h] [bp-80h]@14
  char Src; // [sp+1F8h] [bp-60h]@14
  char v19; // [sp+1F9h] [bp-5Fh]@14
  char Dst; // [sp+224h] [bp-34h]@18
  char v21; // [sp+225h] [bp-33h]@18
  int j; // [sp+234h] [bp-24h]@18
  unsigned __int64 v23; // [sp+240h] [bp-18h]@4
  CRFWorldDatabase *v24; // [sp+260h] [bp+8h]@1
  _worlddb_guild_money_io_info *v25; // [sp+270h] [bp+18h]@1

  v25 = pGuildIOData;
  v24 = this;
  v3 = &v7;
  for ( i = 148i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v23 = (unsigned __int64)&v7 ^ _security_cookie;
  v13 = 0;
  sprintf(
    &Dest,
    "select top 100 InoutDalant, InoutGold, ResultDalant, ResultGold, AvatorSerial, AvatorName, LogDate from tbl_GuildMon"
    "eyHistory_Log where GuildSerial = %d order by serial desc",
    dwGuildSerial);
  if ( v24->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v24->vfptr, &Dest);
  if ( v24->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v24->vfptr) )
  {
    v11 = SQLExecDirect_0(v24->m_hStmtSelect, &Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 0;
      }
      else
      {
        SQLStmt = v24->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v24->vfptr, v11, &Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v24->vfptr, v11, v24->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      while ( 1 )
      {
        TargetValue = 0.0;
        v15 = 0.0;
        v16 = 0.0;
        v17 = 0.0;
        Src = 0;
        memset(&v19, 0, 8ui64);
        v11 = SQLFetch_0(v24->m_hStmtSelect);
        if ( v11 )
        {
          if ( v11 != 1 )
            break;
        }
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v24->m_hStmtSelect, 1u, 8, &TargetValue, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v24->m_hStmtSelect, 2u, 8, &v15, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v24->m_hStmtSelect, 3u, 8, &v16, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v24->m_hStmtSelect, 4u, 8, &v17, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v24->m_hStmtSelect, 5u, 4, &v25->IOData[(signed __int64)v13].dwIOerSerial, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = (void *)17;
        v11 = SQLGetData_0(v24->m_hStmtSelect, 6u, 1, &v25->IOData[(signed __int64)v13], 17i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = (void *)9;
        v11 = SQLGetData_0(v24->m_hStmtSelect, 7u, 1, &Src, 9i64, &v10);
        if ( v11 )
        {
          if ( v11 != 1 )
            break;
        }
        v25->IOData[(signed __int64)v13].dIODalant = TargetValue;
        v25->IOData[(signed __int64)v13].dIOGold = v15;
        v25->IOData[(signed __int64)v13].dLeftDalant = v16;
        v25->IOData[(signed __int64)v13].dLeftGold = v17;
        Dst = 0;
        memset(&v21, 0, 2ui64);
        for ( j = 0; j < 4; ++j )
        {
          memset_0(&Dst, 0, 3ui64);
          memcpy_0(&Dst, &Src + 2 * j, 2ui64);
          v6 = atoi(&Dst);
          v25->IOData[(signed __int64)v13].byDate[j] = v6;
        }
        ++v13;
      }
      v25->wRecordCount = v13;
      if ( v24->m_hStmtSelect )
        SQLCloseCursor_0(v24->m_hStmtSelect);
      if ( v24->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v24->vfptr, "%s Success", &Dest);
      result = 1;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v24->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
