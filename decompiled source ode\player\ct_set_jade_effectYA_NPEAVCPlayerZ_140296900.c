/*
 * Function: ?ct_set_jade_effect@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140296900
 */

char __fastcall ct_set_jade_effect(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  float v4; // xmm0_4@8
  __int64 v5; // [sp+0h] [bp-48h]@1
  unsigned __int16 v6; // [sp+30h] [bp-18h]@8
  float v7; // [sp+34h] [bp-14h]@8
  bool v8; // [sp+38h] [bp-10h]@10
  CPlayer *v9; // [sp+50h] [bp+8h]@1

  v9 = pOne;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v9 )
  {
    if ( s_nWordCount == 2 )
    {
      v6 = atoi(s_pwszDstCheat[0]);
      v4 = atof(s_pwszDstCheat[1]);
      v7 = v4;
      if ( v4 <= 1.0 )
      {
        v9->m_EP.m_pDataParam->m_fEff_Have[v6] = v7;
        v8 = 0;
        if ( v7 > 0.0 )
          v8 = 1;
        CPlayer::apply_have_item_std_effect(v9, v6, v7, v8, 0);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
