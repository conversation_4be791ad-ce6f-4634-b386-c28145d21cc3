/*
 * Function: ?HandleMouseMessages@CD3DArcBall@@QEAA_JPEAUHWND__@@I_<PERSON>_<PERSON>@Z
 * Address: 0x14052C510
 */

__int64 __fastcall CD3DArcBall::HandleMouseMessages(CD3DArcBall *this, HWND a2, int a3, char a4, __int64 a5)
{
  CD3DArcBall *v5; // rdi@1
  int v6; // er8@3
  int v7; // er8@4
  int v8; // er8@5
  int v9; // er8@6
  struct D3DXVECTOR3 *v11; // rax@10
  signed __int64 v12; // rcx@13
  float v13; // xmm2_4@18
  float v14; // xmm0_4@19
  struct D3DXVECTOR3 v15; // [sp+20h] [bp-38h]@13
  struct D3DXVECTOR3 v16; // [sp+30h] [bp-28h]@10
  struct D3DXQUATERNION v17; // [sp+40h] [bp-18h]@13

  v5 = this;
  if ( !(dword_184A8968C & 1) )
    dword_184A8968C |= 1u;
  v6 = a3 - 512;
  if ( v6 )
  {
    v7 = v6 - 1;
    if ( v7 )
    {
      v8 = v7 - 1;
      if ( v8 )
      {
        v9 = v8 - 2;
        if ( v9 && v9 != 3 )
          return 0i64;
        goto LABEL_21;
      }
      *((_DWORD *)this + 76) = 0;
    }
    else
    {
      *((_DWORD *)this + 76) = 1;
      v11 = CD3DArcBall::ScreenToVector(this, &v16, (unsigned __int16)a5, WORD1(a5));
      stru_184A89680.x = v11->x;
      stru_184A89680.y = v11->y;
      stru_184A89680.z = v11->z;
      *((_DWORD *)v5 + 4) = *((_DWORD *)v5 + 8);
      *((_DWORD *)v5 + 5) = *((_DWORD *)v5 + 9);
      *((_DWORD *)v5 + 6) = *((_DWORD *)v5 + 10);
      *((_DWORD *)v5 + 7) = *((_DWORD *)v5 + 11);
    }
  }
  else
  {
    if ( a4 & 1 )
    {
      if ( *((_DWORD *)this + 76) )
      {
        CD3DArcBall::ScreenToVector(this, &v15, (unsigned __int16)a5, WORD1(a5));
        D3DXQuaternionAxisToAxis(&v17, &stru_184A89680, &v15);
        v12 = (signed __int64)v5 + 32;
        *(_DWORD *)v12 = *((_DWORD *)v5 + 4);
        *(_DWORD *)(v12 + 4) = *((_DWORD *)v5 + 5);
        *(_DWORD *)(v12 + 8) = *((_DWORD *)v5 + 6);
        *(_DWORD *)(v12 + 12) = *((_DWORD *)v5 + 7);
        D3DXQuaternionMultiply_0((char *)v5 + 32, (char *)v5 + 32, &v17);
        D3DXMatrixRotationQuaternion_0((char *)v5 + 112, &v17);
      }
      else
      {
        *((_DWORD *)this + 43) = 1065353216;
        *((_DWORD *)this + 38) = 1065353216;
        *((_DWORD *)this + 33) = 1065353216;
        *((_DWORD *)this + 28) = 1065353216;
        *((_DWORD *)this + 42) = 0;
        *((_DWORD *)this + 41) = 0;
        *((_DWORD *)this + 40) = 0;
        *((_DWORD *)this + 39) = 0;
        *((_DWORD *)this + 37) = 0;
        *((_DWORD *)this + 36) = 0;
        *((_DWORD *)this + 35) = 0;
        *((_DWORD *)this + 34) = 0;
        *((_DWORD *)this + 32) = 0;
        *((_DWORD *)this + 31) = 0;
        *((_DWORD *)this + 30) = 0;
        *((_DWORD *)this + 29) = 0;
      }
      D3DXMatrixRotationQuaternion_0((char *)v5 + 48, (char *)v5 + 32);
      *((_DWORD *)v5 + 76) = 1;
      return 1i64;
    }
    if ( a4 & 2 || a4 & 0x10 )
    {
      v13 = (float)((float)(dword_184A8967C - WORD1(a5)) * *((float *)this + 3)) / (float)*((signed int *)this + 1);
      if ( a4 & 2 )
        v14 = (float)*(signed int *)this;
      D3DXMatrixTranslation_0((char *)this + 240);
      D3DXMatrixMultiply_0((char *)v5 + 176, (char *)v5 + 176, (char *)v5 + 240);
LABEL_21:
      dword_184A8967C = WORD1(a5);
      dword_184A89678 = (unsigned __int16)a5;
      return 1i64;
    }
  }
  return 1i64;
}
