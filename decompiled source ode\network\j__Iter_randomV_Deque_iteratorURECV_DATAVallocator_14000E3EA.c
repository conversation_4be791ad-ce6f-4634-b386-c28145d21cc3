/*
 * Function: j_??$_Iter_random@V?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@V12@@std@@YA?AUrandom_access_iterator_tag@0@AEBV?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@0@0@Z
 * Address: 0x14000E3EA
 */

std::random_access_iterator_tag __fastcall std::_Iter_random<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>>(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__formal, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *a2)
{
  return std::_Iter_random<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>>(
           __formal,
           a2);
}
