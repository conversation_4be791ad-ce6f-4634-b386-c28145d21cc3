/*
 * Function: ?dev_avator_copy@CPlayer@@QEAA_NPEAD@Z
 * Address: 0x1400BDA50
 */

char __fastcall CPlayer::dev_avator_copy(CPlayer *this, char *pwszDstName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v5; // eax@10
  int v6; // edx@12
  int v7; // edx@12
  __int64 v8; // [sp+0h] [bp-58h]@1
  char *pszFileName; // [sp+20h] [bp-38h]@12
  CUserDB *v10; // [sp+30h] [bp-28h]@6
  CPlayer *v11; // [sp+38h] [bp-20h]@8
  int v12; // [sp+40h] [bp-18h]@10
  CPlayer *v13; // [sp+60h] [bp+8h]@1

  v13 = this;
  v2 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v13->m_pUserDB )
  {
    v10 = SearchAvatorWithName(g_UserDB, 2532, pwszDstName);
    if ( v10 )
    {
      v11 = &g_Player + v10->m_idWorld.wIndex;
      if ( v11->m_bLive )
      {
        v12 = CPlayerDB::GetRaceSexCode(&v11->m_Param);
        v5 = CPlayerDB::GetRaceSexCode(&v13->m_Param);
        if ( v12 == v5 )
        {
          CUserDB::Update_CopyAll(v13->m_pUserDB, &v10->m_AvatorData);
          v6 = v13->m_ObjID.m_wIndex;
          pszFileName = v13->m_szItemHistoryFileName;
          CMgrAvatorItemHistory::char_copy(
            &CPlayer::s_MgrItemHistory,
            v6,
            v10->m_aszAvatorName,
            v10->m_dwSerial,
            v13->m_szItemHistoryFileName);
          v7 = v13->m_ObjID.m_wIndex;
          pszFileName = v13->m_szLvHistoryFileName;
          CMgrAvatorLvHistory::char_copy(
            &CPlayer::s_MgrLvHistory,
            v7,
            v10->m_aszAvatorName,
            v10->m_dwSerial,
            v13->m_szLvHistoryFileName);
          CNetworkEX::Close(&g_Network, 0, v13->m_ObjID.m_wIndex, 0, 0i64);
          result = 1;
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
