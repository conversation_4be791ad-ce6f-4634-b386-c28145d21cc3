/*
 * Function: ??0PK_DeterministicSignatureMessageEncodingMethod@CryptoPP@@QEAA@XZ
 * Address: 0x14058FDF0
 */

CryptoPP::PK_DeterministicSignatureMessageEncodingMethod *__fastcall CryptoPP::PK_DeterministicSignatureMessageEncodingMethod::PK_DeterministicSignatureMessageEncodingMethod(CryptoPP::PK_DeterministicSignatureMessageEncodingMethod *this)
{
  CryptoPP::PK_DeterministicSignatureMessageEncodingMethod *v2; // [sp+30h] [bp+8h]@1

  v2 = this;
  CryptoPP::PK_SignatureMessageEncodingMethod::PK_SignatureMessageEncodingMethod((CryptoPP::PK_SignatureMessageEncodingMethod *)&this->vfptr);
  return v2;
}
