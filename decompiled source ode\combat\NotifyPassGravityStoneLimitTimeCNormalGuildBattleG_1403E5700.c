/*
 * Function: ?NotifyPassGravityStoneLimitTime@CNormalGuildBattle@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403E5700
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::NotifyPassGravityStoneLimitTime(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-68h]@1
  char pMsg; // [sp+24h] [bp-44h]@4
  char byType; // [sp+44h] [bp-24h]@4
  char v6; // [sp+45h] [bp-23h]@4
  GUILD_BATTLE::CNormalGuildBattle *v7; // [sp+70h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  pMsg = -1;
  byType = 27;
  v6 = 85;
  GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(&v7->m_k1P, &byType, &pMsg, 1u);
  GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(&v7->m_k2P, &byType, &pMsg, 1u);
}
