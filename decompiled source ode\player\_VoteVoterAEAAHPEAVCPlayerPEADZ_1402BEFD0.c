/*
 * Function: ?_Vote@Voter@@AEAAHPEAVCPlayer@@PEAD@Z
 * Address: 0x1402BEFD0
 */

signed __int64 __fastcall Voter::_Vote(Voter *this, CPlayer *pOne, char *pdata)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  unsigned int v6; // eax@11
  char *v7; // rax@17
  CandidateMgr *v8; // rax@23
  PatriarchElectProcessor *v9; // rax@24
  __int64 v10; // [sp+0h] [bp-B8h]@1
  char *pQryData; // [sp+20h] [bp-98h]@11
  bool v12; // [sp+30h] [bp-88h]@4
  char v13; // [sp+31h] [bp-87h]@4
  char *v14; // [sp+38h] [bp-80h]@8
  char *v15; // [sp+40h] [bp-78h]@14
  char v16; // [sp+48h] [bp-70h]@17
  char v17[4]; // [sp+54h] [bp-64h]@24
  char *v18; // [sp+68h] [bp-50h]@9
  CLogFile *v19; // [sp+70h] [bp-48h]@11
  int v20; // [sp+78h] [bp-40h]@15
  char *Str2; // [sp+80h] [bp-38h]@17
  PatriarchElectProcessor *v22; // [sp+88h] [bp-30h]@18
  unsigned int *v23; // [sp+90h] [bp-28h]@21
  unsigned int *v24; // [sp+98h] [bp-20h]@22
  char *wszName; // [sp+A0h] [bp-18h]@23
  Voter *v26; // [sp+C0h] [bp+8h]@1
  CPlayer *v27; // [sp+C8h] [bp+10h]@1
  char *v28; // [sp+D0h] [bp+18h]@1

  v28 = pdata;
  v27 = pOne;
  v26 = this;
  v3 = &v10;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = 0;
  v13 = CPlayerDB::GetRaceCode(&pOne->m_Param);
  if ( v26->_kCandidateInfo[(unsigned __int8)v13].byCnt < 2 )
    return 8i64;
  if ( v27 && v27->m_pUserDB )
  {
    if ( v27->m_pUserDB->m_AvatorData.dbAvator.m_bOverlapVote )
      return 10i64;
    v15 = v28;
    if ( v27->m_Param.m_byPvPGrade < 4 )
      v20 = 1;
    else
      v20 = 2;
    v16 = v20;
    Str2 = v15 + 1;
    v7 = CPlayerDB::GetCharNameW(&v27->m_Param);
    if ( !strcmp_0(v7, Str2) )
    {
      v22 = (PatriarchElectProcessor *)((char *)PatriarchElectProcessor::Instance() + 4 * (unsigned __int8)v13);
      v22->m_dwNonvoteCnt[0] += (unsigned __int8)v16;
      v12 = 1;
    }
    else
    {
      if ( !Voter::IsRegistedVotePaper(v26, v13, v15 + 1) )
        return 19i64;
      v23 = &PatriarchElectProcessor::Instance()->m_dwTotalVoteCnt[(unsigned __int8)v13];
      *v23 += (unsigned __int8)v16;
      if ( v27->m_Param.m_byPvPGrade >= 4 )
      {
        v24 = &PatriarchElectProcessor::Instance()->m_dwHighGradeNum[(unsigned __int8)v13];
        ++*v24;
      }
      wszName = v15 + 1;
      v8 = CandidateMgr::Instance();
      CandidateMgr::AddScore(v8, v13, wszName, v16);
    }
    v27->m_pUserDB->m_AvatorData.dbAvator.m_bOverlapVote = 1;
    CPlayer::PushDQSUpdateVoteAvilable(v27);
    *(_DWORD *)v17 = CPlayerDB::GetCharSerial(&v27->m_Param);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -117, v17, 4);
    v9 = PatriarchElectProcessor::Instance();
    if ( !PatriarchElectProcessor::GetTimeCheck(v9) )
    {
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 115, 0i64, 0);
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 120, 0i64, 0);
    }
    Voter::_SetVoteScoreInfo(v26, v13, v15 + 1, v12);
    Voter::_SendVoteScoreAll(v26, v13);
    result = 0i64;
  }
  else
  {
    v14 = v28;
    if ( v27 )
      v18 = CPlayerDB::GetCharNameW(&v27->m_Param);
    else
      v18 = "NULL";
    v6 = (unsigned __int8)*v14;
    v19 = &v26->_kSysLog;
    pQryData = v18;
    CLogFile::Write(&v26->_kSysLog, "_Vote() Invalid player pointer : Candidate(%s) Rank(%d) : %s", v14 + 1, v6);
    result = 0i64;
  }
  return result;
}
