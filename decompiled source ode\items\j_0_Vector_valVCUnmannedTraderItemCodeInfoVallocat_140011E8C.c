/*
 * Function: j_??0?$_Vector_val@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@IEAA@V?$allocator@VCUnmannedTraderItemCodeInfo@@@1@@Z
 * Address: 0x140011E8C
 */

void __fastcall std::_Vector_val<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Vector_val<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(std::_Vector_val<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *this, std::allocator<CUnmannedTraderItemCodeInfo> _Al)
{
  std::_Vector_val<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Vector_val<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(
    this,
    _Al);
}
