/*
 * Function: ?Clear@CGuildBattleController@@QEAAXXZ
 * Address: 0x1403D62F0
 */

void __fastcall CGuildBattleController::Clear(CGuildBattleController *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleManager *v3; // rax@4
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager *v4; // rax@4
  GUILD_BATTLE::CPossibleBattleGuildListManager *v5; // rax@4
  GUILD_BATTLE::CGuildBattleLogger *v6; // rax@5
  __int64 v7; // [sp+0h] [bp-28h]@1
  CGuildBattleController *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v1 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
  GUILD_BATTLE::CNormalGuildBattleManager::Clear(v3);
  v4 = GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Instance();
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Clear(v4);
  v5 = GUILD_BATTLE::CPossibleBattleGuildListManager::Instance();
  GUILD_BATTLE::CPossibleBattleGuildListManager::Clear(v5);
  if ( !CGuildBattleController::SaveINI(v8) )
  {
    v6 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(v6, "CGuildBattleController::Clear() SaveINI() Fail!");
  }
}
