/*
 * Function: ?SendMsg_RealAddLimTime@CDarkHoleChannel@@QEAAXHPEAD@Z
 * Address: 0x14026CB80
 */

void __fastcall CDarkHoleChannel::SendMsg_RealAddLimTime(CDarkHoleChannel *this, int nAddSec, char *pMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@13
  __int64 v6; // [sp+0h] [bp-A8h]@1
  _darkhole_real_add_time_inform_zocl v7; // [sp+38h] [bp-70h]@4
  char pbyType; // [sp+74h] [bp-34h]@10
  char v9; // [sp+75h] [bp-33h]@10
  int j; // [sp+84h] [bp-24h]@10
  _dh_player_mgr *v11; // [sp+88h] [bp-20h]@12
  unsigned __int64 v12; // [sp+98h] [bp-10h]@4
  CDarkHoleChannel *v13; // [sp+B0h] [bp+8h]@1
  const char *Source; // [sp+C0h] [bp+18h]@1

  Source = pMsg;
  v13 = this;
  v3 = &v6;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = (unsigned __int64)&v6 ^ _security_cookie;
  v7.nAddSec = nAddSec;
  if ( _dh_mission_mgr::GetLimMSecTime(&v13->m_MissionMgr) == -1 )
    v7.dwTotalLimSec = -1;
  else
    v7.dwTotalLimSec = _dh_mission_mgr::GetLimMSecTime(&v13->m_MissionMgr) / 0x3E8;
  if ( Source )
    strcpy_0(v7.szMsg, Source);
  else
    strcpy_0(v7.szMsg, "-1");
  pbyType = 35;
  v9 = 19;
  for ( j = 0; j < 32; ++j )
  {
    v11 = &v13->m_Quester[j];
    if ( _dh_player_mgr::IsFill(v11) )
    {
      v5 = _darkhole_real_add_time_inform_zocl::size(&v7);
      CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_Quester[j].pOne->m_ObjID.m_wIndex, &pbyType, (char *)&v7, v5);
    }
  }
}
