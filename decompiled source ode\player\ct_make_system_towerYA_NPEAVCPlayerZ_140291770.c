/*
 * Function: ?ct_make_system_tower@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140291770
 */

bool __fastcall ct_make_system_tower(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szTran; // [sp+28h] [bp-50h]@7
  unsigned __int64 v6; // [sp+60h] [bp-18h]@4
  CPlayer *v7; // [sp+80h] [bp+8h]@1

  v7 = pOne;
  v1 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( v7 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      W2M(s_pwszDstCheat[0], &szTran, 0x20u);
      result = CPlayer::mgr_make_system_tower(v7, &szTran);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
