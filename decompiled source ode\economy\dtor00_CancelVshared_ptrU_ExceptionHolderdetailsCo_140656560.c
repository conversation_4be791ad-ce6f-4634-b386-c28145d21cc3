/*
 * Function: ?dtor$0@?0???$_Cancel@V?$shared_ptr@U_ExceptionHolder@details@Concurrency@@@std@@@?$task_completion_event@E@Concurrency@@QEBA_NV?$shared_ptr@U_ExceptionHolder@details@Concurrency@@@std@@PEAX@Z@4HA_10
 * Address: 0x140656560
 */

int __fastcall `Concurrency::task_completion_event<unsigned char>::_Cancel<std::shared_ptr<Concurrency::details::_ExceptionHolder>>'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  __int64 v2; // rcx@1

  v2 = *(_QWORD *)(a2 + 120);
  return std::_Deque_const_iterator<unsigned __int64,std::allocator<unsigned __int64>,0>::~_Deque_const_iterator<unsigned __int64,std::allocator<unsigned __int64>,0>();
}
