/*
 * Function: ?Put2@<PERSON>h<PERSON><PERSON><PERSON>@CryptoPP@@UEAA_KPEBE_KH_N@Z
 * Address: 0x1405FCB80
 */

unsigned __int64 __fastcall CryptoPP::HashFilter::Put2(CryptoPP::HashFilter *this, const unsigned __int8 *a2, __int64 a3, unsigned int a4, bool a5)
{
  __int64 v6; // rax@12
  __int64 v7; // rax@12
  __int64 v8; // [sp+40h] [bp-58h]@12
  unsigned __int64 v9; // [sp+48h] [bp-50h]@7
  __int64 v10; // [sp+50h] [bp-48h]@7
  unsigned __int64 v11; // [sp+58h] [bp-40h]@14
  __int64 v12; // [sp+60h] [bp-38h]@14
  int v13; // [sp+68h] [bp-30h]@1
  __int64 v14; // [sp+70h] [bp-28h]@12
  __int64 v15; // [sp+78h] [bp-20h]@12
  __int64 *v16; // [sp+80h] [bp-18h]@12
  __int64 v17; // [sp+88h] [bp-10h]@12
  CryptoPP::HashFilter *v18; // [sp+A0h] [bp+8h]@1
  const unsigned __int8 *v19; // [sp+A8h] [bp+10h]@1
  __int64 v20; // [sp+B0h] [bp+18h]@1
  unsigned int v21; // [sp+B8h] [bp+20h]@1

  v21 = a4;
  v20 = a3;
  v19 = a2;
  v18 = this;
  v13 = this->m_continueAt;
  if ( v13 )
  {
    if ( v13 != 1 )
    {
      if ( v13 != 2 )
        _wassert(L"false", L"D:\\RF Project\\RF_Server64\\28 Crypto++\\filters.cpp", 0x2DDu);
      goto LABEL_13;
    }
    goto LABEL_6;
  }
  this->m_inputPosition = 0i64;
  ((void (__fastcall *)(CryptoPP::HashTransformation *))this->m_hashModule->vfptr[1].Clone)(this->m_hashModule);
  if ( v18->m_putMessage )
  {
LABEL_6:
    if ( CryptoPP::Filter::Output((__int64)v18, 1, (__int64)v19, v20, 0) )
    {
      v9 = v20 - v18->m_inputPosition;
      v10 = 1i64;
      return *CryptoPP::STDMAX<unsigned __int64>(&v10, &v9);
    }
  }
  if ( !v21 )
    return 0i64;
  v18->m_digestSize = ((int (__fastcall *)(CryptoPP::HashTransformation *))v18->m_hashModule->vfptr[3].Clone)(v18->m_hashModule);
  if ( v18->m_truncatedDigestSize >= 0 && v18->m_truncatedDigestSize < v18->m_digestSize )
    v18->m_digestSize = v18->m_truncatedDigestSize;
  v8 = v18->m_digestSize;
  v14 = v18->m_digestSize;
  v15 = v18->m_digestSize;
  LODWORD(v6) = ((int (__fastcall *)(CryptoPP::HashFilter *))v18->vfptr[20].Clone)(v18);
  v18->m_space = (char *)CryptoPP::FilterPutSpaceHelper::HelpCreatePutSpace((__int64)&v18->m_tempSpace, v6);
  v7 = v18->m_digestSize;
  v16 = (__int64 *)v18->m_hashModule;
  v17 = *v16;
  (*(void (__fastcall **)(__int64 *, char *, __int64))(v17 + 112))(v16, v18->m_space, v7);
LABEL_13:
  if ( CryptoPP::Filter::Output((__int64)v18, 2, (__int64)v18->m_space, v18->m_digestSize, v21) )
  {
    v11 = v18->m_digestSize - v18->m_inputPosition;
    v12 = 1i64;
    return *CryptoPP::STDMAX<unsigned __int64>(&v12, &v11);
  }
  return 0i64;
}
