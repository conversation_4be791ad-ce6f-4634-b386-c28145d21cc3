/*
 * Function: ?StartThread@CProcessThread@@QEAA_NPEAU_THREAD_CONFIG@@K@Z
 * Address: 0x14043E020
 */

char __fastcall CProcessThread::StartThread(CProcessThread *this, _THREAD_CONFIG *pConfig, unsigned int dwSynDataNum)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 v5; // rax@4
  unsigned __int8 v6; // cf@6
  unsigned __int64 v7; // rax@6
  __int64 v9; // [sp+0h] [bp-78h]@1
  unsigned int j; // [sp+30h] [bp-48h]@12
  unsigned int dwIndex; // [sp+34h] [bp-44h]@15
  int count[2]; // [sp+38h] [bp-40h]@4
  _SYN_DATA *v13; // [sp+40h] [bp-38h]@11
  void *v14; // [sp+48h] [bp-30h]@8
  char *v15; // [sp+50h] [bp-28h]@14
  __int64 v16; // [sp+58h] [bp-20h]@4
  _SYN_DATA *v17; // [sp+60h] [bp-18h]@9
  CProcessThread *ArgList; // [sp+80h] [bp+8h]@1
  _THREAD_CONFIG *Src; // [sp+88h] [bp+10h]@1
  unsigned int dwMaxBufNum; // [sp+90h] [bp+18h]@1

  dwMaxBufNum = dwSynDataNum;
  Src = pConfig;
  ArgList = this;
  v3 = &v9;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v16 = -2i64;
  ArgList->m_dwSynDataNum = dwSynDataNum;
  memcpy_0(&ArgList->m_Config, pConfig, 0x108ui64);
  CLogFile::SetWriteLogFile(&ArgList->m_logSystem, Src->szLogFileName, 1, 0, 1, 1);
  *(_QWORD *)count = dwMaxBufNum;
  v5 = 24i64 * dwMaxBufNum;
  if ( !is_mul_ok(0x18ui64, dwMaxBufNum) )
    v5 = -1i64;
  v6 = __CFADD__(v5, 8i64);
  v7 = v5 + 8;
  if ( v6 )
    v7 = -1i64;
  v14 = operator new[](v7);
  if ( v14 )
  {
    *(_DWORD *)v14 = count[0];
    `eh vector constructor iterator'(
      (char *)v14 + 8,
      0x18ui64,
      count[0],
      (void (__cdecl *)(void *))_SYN_DATA::_SYN_DATA,
      (void (__cdecl *)(void *))_SYN_DATA::~_SYN_DATA);
    v17 = (_SYN_DATA *)((char *)v14 + 8);
  }
  else
  {
    v17 = 0i64;
  }
  v13 = v17;
  ArgList->m_pSynData = v17;
  if ( ArgList->m_dwSynDataSize )
  {
    for ( j = 0; j < dwMaxBufNum; ++j )
    {
      v15 = (char *)operator new[](ArgList->m_dwSynDataSize);
      ArgList->m_pSynData[j].m_psData = v15;
    }
  }
  CNetIndexList::SetList(&ArgList->m_listData, dwMaxBufNum);
  CNetIndexList::SetList(&ArgList->m_listDataComplete, dwMaxBufNum);
  CNetIndexList::SetList(&ArgList->m_listDataEmpty, dwMaxBufNum);
  for ( dwIndex = 0; dwIndex < dwMaxBufNum; ++dwIndex )
    CNetIndexList::PushNode_Back(&ArgList->m_listDataEmpty, dwIndex);
  ArgList->m_bThread = 1;
  _beginthread((void (__cdecl *)(void *))CProcessThread::Thread, 0, ArgList);
  return 1;
}
