/*
 * Function: ??$_Adjust_heap@V?$_Vector_iterator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@_JU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@YAXV?$_Vector_iterator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@0@_J1U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@Z
 * Address: 0x1405AA5A0
 */

void __fastcall std::_Adjust_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>(__int64 a1, __int64 a2, __int64 a3, CryptoPP::ECPPoint *a4)
{
  __int64 v4; // rax@3
  __int64 v5; // rax@3
  char v6; // ST30_1@3
  CryptoPP::ECPPoint *v7; // rax@5
  CryptoPP::ECPPoint *v8; // rax@5
  CryptoPP::ECPPoint *v9; // rax@7
  CryptoPP::ECPPoint *v10; // rax@7
  __int64 v11; // [sp+20h] [bp-1F8h]@1
  __int64 i; // [sp+28h] [bp-1F0h]@1
  char v13; // [sp+38h] [bp-1E0h]@3
  char v14; // [sp+50h] [bp-1C8h]@3
  char v15; // [sp+68h] [bp-1B0h]@5
  char v16; // [sp+80h] [bp-198h]@5
  char v17; // [sp+98h] [bp-180h]@7
  char v18; // [sp+B0h] [bp-168h]@7
  char v19; // [sp+C8h] [bp-150h]@8
  char *v20; // [sp+148h] [bp-D0h]@8
  char v21; // [sp+150h] [bp-C8h]@8
  char *v22; // [sp+168h] [bp-B0h]@8
  __int64 v23; // [sp+170h] [bp-A8h]@1
  __int64 v24; // [sp+178h] [bp-A0h]@3
  __int64 v25; // [sp+180h] [bp-98h]@3
  __int64 v26; // [sp+188h] [bp-90h]@3
  __int64 v27; // [sp+190h] [bp-88h]@3
  __int64 v28; // [sp+198h] [bp-80h]@3
  __int64 v29; // [sp+1A0h] [bp-78h]@5
  __int64 v30; // [sp+1A8h] [bp-70h]@5
  CryptoPP::ECPPoint *v31; // [sp+1B0h] [bp-68h]@5
  __int64 v32; // [sp+1B8h] [bp-60h]@5
  __int64 v33; // [sp+1C0h] [bp-58h]@5
  __int64 v34; // [sp+1C8h] [bp-50h]@7
  __int64 v35; // [sp+1D0h] [bp-48h]@7
  CryptoPP::ECPPoint *v36; // [sp+1D8h] [bp-40h]@7
  __int64 v37; // [sp+1E0h] [bp-38h]@7
  __int64 v38; // [sp+1E8h] [bp-30h]@7
  CryptoPP::ECPPoint *v39; // [sp+1F0h] [bp-28h]@8
  CryptoPP::ECPPoint *v40; // [sp+1F8h] [bp-20h]@8
  __int64 v41; // [sp+200h] [bp-18h]@8
  __int64 v42; // [sp+220h] [bp+8h]@1
  __int64 v43; // [sp+228h] [bp+10h]@1
  __int64 v44; // [sp+230h] [bp+18h]@1
  CryptoPP::ECPPoint *v45; // [sp+238h] [bp+20h]@1

  v45 = a4;
  v44 = a3;
  v43 = a2;
  v42 = a1;
  v23 = -2i64;
  v11 = a2;
  for ( i = 2 * a2 + 2; i < v44; i = 2 * i + 2 )
  {
    v24 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator+(
            v42,
            (__int64)&v14,
            i - 1);
    v25 = v24;
    LODWORD(v4) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator*();
    v26 = v4;
    v27 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator+(
            v42,
            (__int64)&v13,
            i);
    v28 = v27;
    LODWORD(v5) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator*();
    v6 = CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::operator<(v5, v26);
    std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
    std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
    if ( v6 )
      --i;
    v29 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator+(
            v42,
            (__int64)&v16,
            i);
    v30 = v29;
    LODWORD(v7) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator*();
    v31 = v7;
    v32 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator+(
            v42,
            (__int64)&v15,
            v43);
    v33 = v32;
    LODWORD(v8) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator*();
    CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::operator=(v8, v31);
    v43 = i;
    std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
    std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
  }
  if ( i == v44 )
  {
    v34 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator+(
            v42,
            (__int64)&v18,
            v44 - 1);
    v35 = v34;
    LODWORD(v9) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator*();
    v36 = v9;
    v37 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator+(
            v42,
            (__int64)&v17,
            v43);
    v38 = v37;
    LODWORD(v10) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator*();
    CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::operator=(v10, v36);
    std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
    std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
    v43 = v44 - 1;
  }
  v20 = &v19;
  v22 = &v21;
  v39 = CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(
          (CryptoPP::ECPPoint *)&v19,
          v45);
  v40 = v39;
  v41 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>((__int64)v22);
  std::_Push_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>(
    v41,
    v43,
    v11,
    v40);
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
  CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>((__int64)v45);
}
