/*
 * Function: j_?size@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEBA_KXZ
 * Address: 0x1400080A3
 */

unsigned __int64 __fastcall std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::size(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this)
{
  return std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::size(this);
}
