/*
 * Function: ?GetGroupID@CUnmannedTraderGroupItemInfoTable@@QEAA_NEGAEAE00AEAK@Z
 * Address: 0x1403600C0
 */

bool __fastcall CUnmannedTraderGroupItemInfoTable::GetGroupID(CUnmannedTraderGroupItemInfoTable *this, char byTableCode, unsigned __int16 wItemTableIndex, char *byDivision, char *byClass, char *bySubClass, unsigned int *dwListIndex)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v10; // [sp+0h] [bp-48h]@1
  CUnmannedTraderGroupItemInfoTable *v11; // [sp+50h] [bp+8h]@1

  v11 = this;
  v7 = &v10;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  return CUnmannedTraderGroupIDInfo::GetGroupID(
           &v11->m_kGroupIDInfo,
           byTableCode,
           wItemTableIndex,
           byDivision,
           byClass,
           bySubClass,
           dwListIndex);
}
