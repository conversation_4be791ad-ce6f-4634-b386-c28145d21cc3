/*
 * Function: j_??1?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEAA@XZ
 * Address: 0x140008314
 */

void __fastcall std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this)
{
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(this);
}
