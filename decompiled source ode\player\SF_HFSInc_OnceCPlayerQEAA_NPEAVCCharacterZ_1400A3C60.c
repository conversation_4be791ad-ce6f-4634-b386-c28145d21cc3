/*
 * Function: ?SF_HFSInc_Once@CPlayer@@QEAA_NPEAVCCharacter@@@Z
 * Address: 0x1400A3C60
 */

char __fastcall CPlayer::SF_HFSInc_Once(CPlayer *this, CCharacter *pDstObj)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@9
  __int64 v5; // [sp+0h] [bp-58h]@1
  char v6; // [sp+20h] [bp-38h]@4
  int v7; // [sp+24h] [bp-34h]@8
  unsigned int v8; // [sp+28h] [bp-30h]@8
  unsigned int v9; // [sp+2Ch] [bp-2Ch]@8
  int v10; // [sp+30h] [bp-28h]@8
  int nFP; // [sp+34h] [bp-24h]@8
  int v12; // [sp+38h] [bp-20h]@8
  int v13; // [sp+3Ch] [bp-1Ch]@8
  int nSP; // [sp+40h] [bp-18h]@8
  int v15; // [sp+44h] [bp-14h]@8
  CPlayer *v16; // [sp+68h] [bp+10h]@1

  v16 = (CPlayer *)pDstObj;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  if ( !pDstObj->m_ObjID.m_byID && !pDstObj->m_bCorpse )
    v6 = 1;
  if ( v6 )
  {
    v7 = ((int (__fastcall *)(CCharacter *))pDstObj->vfptr->GetHP)(pDstObj);
    v8 = ((int (__fastcall *)(CPlayer *))v16->vfptr->GetMaxHP)(v16);
    v9 = v8 - v7;
    v10 = CPlayer::GetFP(v16);
    nFP = CPlayer::GetMaxFP(v16);
    v12 = nFP - v10;
    v13 = CPlayer::GetSP(v16);
    nSP = CPlayer::GetMaxSP(v16);
    v15 = nSP - v13;
    if ( (float)((float)v7 / (float)(signed int)v8) <= 0.80000001 )
    {
      if ( (signed int)v9 > 0 )
      {
        ((void (__fastcall *)(CPlayer *, _QWORD, _QWORD))v16->vfptr->SetHP)(v16, v8, 0i64);
        (*(void (__fastcall **)(CPlayer *))&v16->vfptr->gap8[72])(v16);
      }
      if ( v12 > 0 )
      {
        CPlayer::SetFP(v16, nFP, 0);
        CPlayer::SendMsg_SetFPInform(v16);
      }
      if ( v15 > 0 )
      {
        CPlayer::SetSP(v16, nSP, 0);
        CPlayer::SendMsg_SetSPInform(v16);
      }
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
