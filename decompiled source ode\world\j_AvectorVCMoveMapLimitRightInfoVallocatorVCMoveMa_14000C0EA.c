/*
 * Function: j_??A?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEAAAEAVCMoveMapLimitRightInfo@@_K@Z
 * Address: 0x14000C0EA
 */

CMoveMapLimitRightInfo *__fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::operator[](std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, unsigned __int64 _Pos)
{
  return std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::operator[](this, _Pos);
}
