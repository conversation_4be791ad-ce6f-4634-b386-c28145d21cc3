/*
 * Function: j_??0?$_Vector_val@PEAVCUnmannedTraderDivisionInfo@@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@IEAA@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@1@@Z
 * Address: 0x140012A6C
 */

void __fastcall std::_Vector_val<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_Vector_val<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(std::_Vector_val<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *this, std::allocator<CUnmannedTraderDivisionInfo *> _Al)
{
  std::_Vector_val<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_Vector_val<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(
    this,
    _Al);
}
