/*
 * Function: ??0?$DL_ObjectImplBase@V?$DL_VerifierBase@UECPPoint@CryptoPP@@@CryptoPP@@U?$DL_SignatureSchemeOptions@V?$DL_SS@U?$DL_Keys_ECDSA@VECP@CryptoPP@@@CryptoPP@@V?$DL_Algorithm_ECDSA@VECP@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@H@CryptoPP@@U?$DL_Keys_ECDSA@VECP@CryptoPP@@@2@V?$DL_Algorithm_ECDSA@VECP@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@@2@V?$DL_PublicKey_EC@VECP@CryptoPP@@@2@@CryptoPP@@QEAA@XZ
 * Address: 0x140568230
 */

__int64 __fastcall CryptoPP::DL_ObjectImplBase<CryptoPP::DL_VerifierBase<CryptoPP::ECPPoint>,CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DL_SS<CryptoPP::DL_Keys_ECDSA<CryptoPP::ECP>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::ECP>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1,int>,CryptoPP::DL_Keys_ECDSA<CryptoPP::ECP>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::ECP>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>,CryptoPP::DL_PublicKey_EC<CryptoPP::ECP>>::DL_ObjectImplBase<CryptoPP::DL_VerifierBase<CryptoPP::ECPPoint>,CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DL_SS<CryptoPP::DL_Keys_ECDSA<CryptoPP::ECP>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::ECP>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1,int>,CryptoPP::DL_Keys_ECDSA<CryptoPP::ECP>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::ECP>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>,CryptoPP::DL_PublicKey_EC<CryptoPP::ECP>>(__int64 a1)
{
  __int64 v2; // [sp+40h] [bp+8h]@1

  v2 = a1;
  CryptoPP::AlgorithmImpl<CryptoPP::DL_VerifierBase<CryptoPP::ECPPoint>,CryptoPP::DL_SS<CryptoPP::DL_Keys_ECDSA<CryptoPP::ECP>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::ECP>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1,int>>::AlgorithmImpl<CryptoPP::DL_VerifierBase<CryptoPP::ECPPoint>,CryptoPP::DL_SS<CryptoPP::DL_Keys_ECDSA<CryptoPP::ECP>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::ECP>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1,int>>();
  CryptoPP::DL_PublicKey_EC<CryptoPP::ECP>::DL_PublicKey_EC<CryptoPP::ECP>((CryptoPP::DL_PublicKey_EC<CryptoPP::ECP> *)(v2 + 24));
  return v2;
}
