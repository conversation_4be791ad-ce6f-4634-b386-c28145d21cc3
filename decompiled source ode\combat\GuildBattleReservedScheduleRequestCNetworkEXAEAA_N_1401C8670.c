/*
 * Function: ?GuildBattleReservedScheduleRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C8670
 */

char __fastcall CNetworkEX::GuildBattleReservedScheduleRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CGuildBattleController *v6; // rax@8
  __int64 v7; // [sp+0h] [bp-68h]@1
  char byDay; // [sp+20h] [bp-48h]@8
  char byPage; // [sp+28h] [bp-40h]@8
  unsigned int dwGuildSerial; // [sp+30h] [bp-38h]@8
  char *v11; // [sp+40h] [bp-28h]@4
  CPlayer *v12; // [sp+48h] [bp-20h]@4
  unsigned int v13; // [sp+50h] [bp-18h]@6
  unsigned int uiMapID; // [sp+54h] [bp-14h]@8
  int na; // [sp+78h] [bp+10h]@1

  na = n;
  v3 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = pBuf;
  v12 = &g_Player + n;
  if ( v12->m_bOper )
  {
    v13 = -1;
    if ( v12->m_Param.m_pGuild )
      v13 = v12->m_Param.m_pGuild->m_dwSerial;
    uiMapID = CPlayerDB::GetRaceCode(&v12->m_Param);
    v6 = CGuildBattleController::Instance();
    dwGuildSerial = v13;
    byPage = v11[5];
    byDay = v11[4];
    CGuildBattleController::SendReservedScheduleList(v6, na, uiMapID, *(_DWORD *)v11, byDay, byPage, v13);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
