/*
 * Function: j_??$_Unchecked_uninitialized_move@V?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@PEAPEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@2@@stdext@@YAPEAPEAVCLogTypeDBTask@@V?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@0PEAPEAV1@AEAV?$allocator@PEAVCLogTypeDBTask@@@3@@Z
 * Address: 0x14000A565
 */

CLogTypeDBTask **__fastcall stdext::_Unchecked_uninitialized_move<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>>(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_First, std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_Last, CLogTypeDBTask **_Dest, std::allocator<CLogTypeDBTask *> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
