/*
 * Function: ?On_HS_SCENE_KEEPER_DEATTACKABLE_TIME@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x14027C610
 */

void __fastcall CHolyStoneSystem::On_HS_SCENE_KEEPER_DEATTACKABLE_TIME(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  COreAmountMgr *v3; // rax@6
  COreAmountMgr *v4; // rax@6
  COreAmountMgr *v5; // rax@6
  COreAmountMgr *v6; // rax@6
  COreAmountMgr *v7; // rax@6
  unsigned int v8; // eax@6
  COreAmountMgr *v9; // rax@6
  COreAmountMgr *v10; // rax@6
  COreAmountMgr *v11; // rax@6
  __int64 v12; // [sp+0h] [bp-38h]@1
  unsigned int dwRemain; // [sp+20h] [bp-18h]@6
  unsigned int v14; // [sp+24h] [bp-14h]@6
  unsigned int dwTotal; // [sp+28h] [bp-10h]@6
  CHolyStoneSystem *v16; // [sp+40h] [bp+8h]@1

  v16 = this;
  v1 = &v12;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CHolyStoneSystem::GetHolyMasterRace(v16) != -1 )
  {
    CHolyStoneSystem::CreateHolyKeeper(v16, 1);
    CHolyKeeper::SetDamageAbleState(g_Keeper, 0);
    CHolyStoneSystem::SendMsg_EnterKeeper(v16, -1);
  }
  v3 = COreAmountMgr::Instance();
  COreAmountMgr::ReLoad(v3);
  v4 = COreAmountMgr::Instance();
  COreAmountMgr::InitRemainOreAmount(v4, 0xFFFFFFFF, 0xFFFFFFFF);
  v5 = COreAmountMgr::Instance();
  COreAmountMgr::InsertOreLog(v5, 1);
  v6 = COreAmountMgr::Instance();
  v14 = COreAmountMgr::GetRemainOre(v6);
  v7 = COreAmountMgr::Instance();
  v8 = COreAmountMgr::GetOreTransferAmount(v7);
  dwRemain = v8 + v14;
  v9 = COreAmountMgr::Instance();
  dwTotal = COreAmountMgr::GetTotalOre(v9);
  v10 = COreAmountMgr::Instance();
  COreAmountMgr::InitRemainOreAmount(v10, dwRemain, dwTotal);
  v11 = COreAmountMgr::Instance();
  COreAmountMgr::InitTransferOre(v11, 0, 0);
}
