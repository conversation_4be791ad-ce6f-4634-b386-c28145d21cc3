/*
 * Function: ?ReEnterMember@CTransportShip@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x140264B30
 */

void __fastcall CTransportShip::ReEnterMember(CTransportShip *this, CPlayer *pExiter)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  CTransportShip::__mgr_member *v5; // [sp+20h] [bp-18h]@4
  CTransportShip *v6; // [sp+40h] [bp+8h]@1
  CPlayer *v7; // [sp+48h] [bp+10h]@1

  v7 = pExiter;
  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CNetIndexList::FindNode(&v6->m_listLogoffMember, pExiter->m_dwObjSerial);
  v5 = CTransportShip::GetEmptyNewMember(v6);
  if ( v5 )
  {
    v5->pPtr = v7;
    v5->dwSerial = v7->m_dwObjSerial;
  }
}
