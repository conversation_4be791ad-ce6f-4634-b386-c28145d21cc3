/*
 * Function: ?GetSize@?$CArrayEx@VCLuaLooting_Novus_Item@@U_State@1@@US@@QEBAKXZ
 * Address: 0x1404057B0
 */

unsigned int __fastcall US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::GetSize(US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State> *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return US::CArray<CLuaLooting_Novus_Item>::GetSize(&v5->m_DataAr);
}
