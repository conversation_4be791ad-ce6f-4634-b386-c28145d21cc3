/*
 * Function: ?GetVoidValue@?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@UEBA_NPEBDAEBVtype_info@@PEAX@Z
 * Address: 0x140552380
 */

int __fastcall CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::GetVoidValue(__int64 a1)
{
  __int64 v1; // rax@1
  __int64 v2; // rax@1
  char v4; // [sp+40h] [bp-38h]@1

  v1 = CryptoPP::GetValueHelper<CryptoPP::DL_GroupParameters_IntegerBased,CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>>(
         (__int64)&v4,
         a1 - 232);
  LODWORD(v2) = CryptoPP::GetValueHelperClass<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>,CryptoPP::DL_GroupParameters_IntegerBased>::Assignable(v1);
  return CryptoPP::GetValueHelperClass<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>,CryptoPP::DL_GroupParameters_IntegerBased>::operator bool(v2);
}
