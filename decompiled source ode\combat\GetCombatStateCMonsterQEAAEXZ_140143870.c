/*
 * Function: ?GetCombatState@CMonster@@QEAAEXZ
 * Address: 0x140143870
 */

char __fastcall CMonster::GetCombatState(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMonster *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CMonster::GetMoveType(v6) == 1 )
    LOBYTE(v3) = 1;
  else
    v3 = (v6->m_nCommonStateChunk >> 1) & 1;
  return v3;
}
