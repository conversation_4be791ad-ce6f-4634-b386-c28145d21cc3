/*
 * Function: ?IsOverOneDay@CPlayer@@QEAA_NXZ
 * Address: 0x14004CC20
 */

char __fastcall CPlayer::IsOverOneDay(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-178h]@1
  char Buffer; // [sp+28h] [bp-150h]@4
  char v6; // [sp+29h] [bp-14Fh]@4
  char v7; // [sp+2Bh] [bp-14Dh]@4
  char v8; // [sp+2Ch] [bp-14Ch]@4
  char v9; // [sp+2Eh] [bp-14Ah]@4
  char v10; // [sp+2Fh] [bp-149h]@4
  char v11; // [sp+58h] [bp-120h]@4
  char v12; // [sp+59h] [bp-11Fh]@4
  char v13; // [sp+5Bh] [bp-11Dh]@4
  char v14; // [sp+5Ch] [bp-11Ch]@4
  char Str; // [sp+88h] [bp-F0h]@4
  char v16; // [sp+89h] [bp-EFh]@4
  char v17; // [sp+8Ah] [bp-EEh]@4
  char v18; // [sp+B8h] [bp-C0h]@4
  char v19; // [sp+B9h] [bp-BFh]@4
  char v20; // [sp+BAh] [bp-BEh]@4
  char v21; // [sp+E8h] [bp-90h]@4
  char v22; // [sp+E9h] [bp-8Fh]@4
  char v23; // [sp+EAh] [bp-8Eh]@4
  char v24; // [sp+118h] [bp-60h]@4
  char v25; // [sp+119h] [bp-5Fh]@4
  char v26; // [sp+11Ah] [bp-5Eh]@4
  char v27; // [sp+11Bh] [bp-5Dh]@4
  char v28; // [sp+11Ch] [bp-5Ch]@4
  unsigned int v29; // [sp+134h] [bp-44h]@4
  int v30; // [sp+138h] [bp-40h]@4
  int v31; // [sp+13Ch] [bp-3Ch]@4
  int v32; // [sp+140h] [bp-38h]@4
  int v33; // [sp+144h] [bp-34h]@4
  unsigned int v34; // [sp+148h] [bp-30h]@4
  unsigned int v35; // [sp+14Ch] [bp-2Ch]@4
  unsigned int v36; // [sp+150h] [bp-28h]@4
  unsigned int v37; // [sp+154h] [bp-24h]@4
  unsigned int v38; // [sp+158h] [bp-20h]@4
  unsigned __int64 v39; // [sp+168h] [bp-10h]@4
  CPlayer *v40; // [sp+180h] [bp+8h]@1

  v40 = this;
  v1 = &v4;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v39 = (unsigned __int64)&v4 ^ _security_cookie;
  v29 = v40->m_pUserDB->m_AvatorData.dbAvator.m_dwLastConnTime;
  _strdate(&Buffer);
  _strtime(&v11);
  Str = v9;
  v16 = v10;
  v17 = 0;
  v18 = Buffer;
  v19 = v6;
  v20 = 0;
  v21 = v7;
  v22 = v8;
  v23 = 0;
  v24 = v11;
  v25 = v12;
  v26 = v13;
  v27 = v14;
  v28 = 0;
  v30 = atoi(&Str);
  v31 = atoi(&v18);
  v32 = atoi(&v21);
  v33 = atoi(&v24);
  v34 = v29 / 0xF4240;
  v35 = v29 / 0xF4240 / 0x64;
  v36 = v29 / 0xF4240 % 0x64;
  v37 = v29 % 0xF4240 / 0x2710;
  v38 = v29 % 0x2710;
  if ( v30 == v35 )
  {
    if ( v31 - v36 > 1 )
      return 1;
    if ( v31 - v36 == 1 && v32 >= v37 )
      return 1;
  }
  else
  {
    if ( v30 - v35 > 1 )
      return 1;
    if ( v31 != 1 || v36 != 12 )
      return 1;
    if ( v32 >= v37 )
      return 1;
  }
  return 0;
}
