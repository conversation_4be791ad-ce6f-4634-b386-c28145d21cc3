/*
 * Function: ?MessageRepresentativeLength@?$DL_SignatureSchemeBase@VPK_Signer@CryptoPP@@V?$DL_PrivateKey@VInteger@CryptoPP@@@2@@CryptoPP@@IEBA_KXZ
 * Address: 0x14056BEB0
 */

unsigned __int64 __fastcall CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Signer,CryptoPP::DL_PrivateKey<CryptoPP::Integer>>::MessageRepresentativeLength(__int64 a1)
{
  CryptoPP *v1; // rax@1

  LODWORD(v1) = CryptoPP::DL_SignatureSchemeBase<CryptoPP::P<PERSON>_Signer,CryptoPP::DL_PrivateKey<CryptoPP::Integer>>::MessageRepresentativeBitLength(a1);
  return CryptoPP::BitsToBytes(v1);
}
