/*
 * Function: ?GetGroupID@CUnmannedTraderDivisionInfo@@QEAA_NEGAEAE00AEAK@Z
 * Address: 0x14036DC20
 */

char __fastcall CUnmannedTraderDivisionInfo::GetGroupID(CUnmannedTraderDivisionInfo *this, char byTableCode, unsigned __int16 wItemTableIndex, char *byDivision, char *byClass, char *bySubClass, unsigned int *dwListIndex)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char v9; // al@5
  CUnmannedTraderClassInfoVtbl *v10; // rax@8
  __int64 v11; // [sp+0h] [bp-C8h]@1
  char *v12; // [sp+20h] [bp-A8h]@8
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > result; // [sp+38h] [bp-90h]@6
  unsigned int v14; // [sp+54h] [bp-74h]@6
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > v15; // [sp+58h] [bp-70h]@10
  bool v16; // [sp+70h] [bp-58h]@7
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > v17; // [sp+78h] [bp-50h]@7
  char v18; // [sp+90h] [bp-38h]@9
  bool v19; // [sp+91h] [bp-37h]@11
  __int64 v20; // [sp+98h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v21; // [sp+A0h] [bp-28h]@7
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_Right; // [sp+A8h] [bp-20h]@7
  CUnmannedTraderClassInfo *v23; // [sp+B0h] [bp-18h]@8
  CUnmannedTraderDivisionInfo *v24; // [sp+D0h] [bp+8h]@1
  char v25; // [sp+D8h] [bp+10h]@1
  unsigned __int16 v26; // [sp+E0h] [bp+18h]@1
  char *v27; // [sp+E8h] [bp+20h]@1

  v27 = byDivision;
  v26 = wItemTableIndex;
  v25 = byTableCode;
  v24 = this;
  v7 = &v11;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v20 = -2i64;
  if ( std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::empty(&v24->m_vecClass) )
  {
    *dwListIndex = 0;
    v9 = 0;
  }
  else
  {
    std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::begin(&v24->m_vecClass, &result);
    v14 = 0;
    while ( 1 )
    {
      v21 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::end(
              &v24->m_vecClass,
              &v17);
      _Right = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)v21;
      v16 = std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator!=(
              (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&result._Mycont,
              (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v21->_Mycont);
      std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&v17);
      if ( !v16 )
        break;
      v23 = *std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator*(&result);
      v10 = v23->vfptr;
      v12 = bySubClass;
      if ( (unsigned __int8)((int (__fastcall *)(CUnmannedTraderClassInfo *, _QWORD, _QWORD, char *))v10->GetGroupID)(
                              v23,
                              (unsigned __int8)v25,
                              v26,
                              byClass) )
      {
        *v27 = v24->m_dwID;
        *dwListIndex = v14;
        v18 = 1;
        std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&result);
        return v18;
      }
      std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator++(
        &result,
        &v15,
        0);
      ++v14;
      std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&v15);
    }
    v19 = 0;
    std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&result);
    v9 = v19;
  }
  return v9;
}
