/*
 * Function: ?trans_ground_item@CMgrAvatorItemHistory@@QEAAXPEAU_db_con@_STORAGE_LIST@@PEADK11@Z
 * Address: 0x140238600
 */

void __fastcall CMgrAvatorItemHistory::trans_ground_item(CMgrAvatorItemHistory *this, _STORAGE_LIST::_db_con *pItem, char *pszTakerName, unsigned int dwTakerSerial, char *pszTakerID, char *pszFileName)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char *v8; // rax@4
  __int64 v9; // [sp+0h] [bp-88h]@1
  char *v10; // [sp+20h] [bp-68h]@4
  unsigned __int64 v11; // [sp+28h] [bp-60h]@4
  char *v12; // [sp+30h] [bp-58h]@4
  unsigned int v13; // [sp+38h] [bp-50h]@4
  char *v14; // [sp+40h] [bp-48h]@4
  char *v15; // [sp+48h] [bp-40h]@4
  char *v16; // [sp+50h] [bp-38h]@4
  _base_fld *v17; // [sp+60h] [bp-28h]@4
  char *v18; // [sp+68h] [bp-20h]@4
  char *v19; // [sp+70h] [bp-18h]@4
  int nTableCode; // [sp+78h] [bp-10h]@4
  CMgrAvatorItemHistory *v21; // [sp+90h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v22; // [sp+98h] [bp+10h]@1
  char *v23; // [sp+A0h] [bp+18h]@1
  unsigned int v24; // [sp+A8h] [bp+20h]@1

  v24 = dwTakerSerial;
  v23 = pszTakerName;
  v22 = pItem;
  v21 = this;
  v6 = &v9;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v17 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
  v18 = v21->m_szCurTime;
  v19 = v21->m_szCurDate;
  nTableCode = v22->m_byTableCode;
  v8 = DisplayItemUpgInfo(nTableCode, v22->m_dwLv);
  v16 = v18;
  v15 = v19;
  v14 = pszTakerID;
  v13 = v24;
  v12 = v23;
  v11 = v22->m_lnUID;
  v10 = v8;
  sprintf(sData, "TRANS: %s_%u_@%s[%I64u] taker(%s:%d id:%s) [%s %s]\r\n", v17->m_strCode, v22->m_dwDur);
  CMgrAvatorItemHistory::WriteFile(v21, pszFileName, sData);
}
