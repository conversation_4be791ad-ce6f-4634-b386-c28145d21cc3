/*
 * Function: ?_Insert_n@?$vector@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@IEAAXV?$_Vector_iterator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@2@_KAEBU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@Z
 * Address: 0x1405976C0
 */

int __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Insert_n(__int64 a1, __int64 a2, unsigned __int64 a3, __int64 a4)
{
  __int64 v4; // rax@2
  __int64 v5; // rax@4
  __int64 v6; // rax@5
  __int64 v7; // rax@8
  __int64 v8; // rax@10
  __int64 v9; // rax@10
  __int64 v10; // r8@11
  __int64 v11; // rax@15
  char v13; // [sp+20h] [bp-D8h]@1
  unsigned __int64 v14; // [sp+A0h] [bp-58h]@1
  __int64 v15; // [sp+A8h] [bp-50h]@10
  __int64 v16; // [sp+B0h] [bp-48h]@10
  __int64 v17; // [sp+B8h] [bp-40h]@15
  __int64 v18; // [sp+C0h] [bp-38h]@1
  __int64 v19; // [sp+C8h] [bp-30h]@2
  unsigned __int64 v20; // [sp+D0h] [bp-28h]@5
  unsigned __int64 v21; // [sp+D8h] [bp-20h]@6
  __int64 v22; // [sp+E0h] [bp-18h]@10
  __int64 v23; // [sp+E8h] [bp-10h]@10
  __int64 v24; // [sp+100h] [bp+8h]@1
  __int64 v25; // [sp+108h] [bp+10h]@1
  unsigned __int64 v26; // [sp+110h] [bp+18h]@1
  __int64 v27; // [sp+110h] [bp+18h]@10

  v26 = a3;
  v25 = a2;
  v24 = a1;
  v18 = -2i64;
  CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(
    &v13,
    a4);
  v14 = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::capacity(v24);
  if ( v26 )
  {
    v19 = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::size(v24);
    LODWORD(v4) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::max_size(v24);
    if ( v4 - v19 < v26 )
      std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Xlen();
    v5 = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::size(v24);
    if ( v14 >= v26 + v5 )
    {
      if ( (*(_QWORD *)(v24 + 24) - *(_QWORD *)(v25 + 16)) >> 7 >= v26 )
      {
        v17 = *(_QWORD *)(v24 + 24);
        LODWORD(v11) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Umove<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *>(
                         v24,
                         v17 - (v26 << 7),
                         v17,
                         *(_QWORD *)(v24 + 24));
        *(_QWORD *)(v24 + 24) = v11;
        stdext::_Unchecked_move_backward<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *>(
          *(_QWORD *)(v25 + 16),
          v17 - (v26 << 7),
          v17);
        std::fill<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>(
          *(_QWORD *)(v25 + 16),
          *(_QWORD *)(v25 + 16) + (v26 << 7),
          &v13);
      }
      else
      {
        std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Umove<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *>(
          v24,
          *(_QWORD *)(v25 + 16),
          *(_QWORD *)(v24 + 24),
          *(_QWORD *)(v25 + 16) + (v26 << 7));
        std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Ufill(
          v24,
          *(_QWORD *)(v24 + 24),
          v26 - ((*(_QWORD *)(v24 + 24) - *(_QWORD *)(v25 + 16)) >> 7),
          (__int64)&v13);
        *(_QWORD *)(v24 + 24) += v26 << 7;
        std::fill<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>(
          *(_QWORD *)(v25 + 16),
          *(_QWORD *)(v24 + 24) - (v26 << 7),
          &v13);
      }
    }
    else
    {
      v20 = v14 / 2;
      LODWORD(v6) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::max_size(v24);
      if ( v6 - v20 >= v14 )
        v21 = v14 / 2 + v14;
      else
        v21 = 0i64;
      v14 = v21;
      v7 = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::size(v24);
      if ( v14 < v26 + v7 )
        v14 = v26
            + std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::size(v24);
      LODWORD(v8) = std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>::allocate(
                      v24 + 8,
                      v14);
      v16 = v8;
      v15 = v8;
      LODWORD(v9) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Umove<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *>(
                      v24,
                      *(_QWORD *)(v24 + 16),
                      *(_QWORD *)(v25 + 16),
                      v8);
      v22 = v9;
      v15 = v9;
      v23 = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Ufill(
              v24,
              v9,
              v26,
              (__int64)&v13);
      v15 = v23;
      std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Umove<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *>(
        v24,
        *(_QWORD *)(v25 + 16),
        *(_QWORD *)(v24 + 24),
        v23);
      v27 = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::size(v24)
          + v26;
      if ( *(_QWORD *)(v24 + 16) )
      {
        std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Destroy(
          v24,
          *(_QWORD *)(v24 + 16),
          *(_QWORD *)(v24 + 24));
        v10 = (*(_QWORD *)(v24 + 32) - *(_QWORD *)(v24 + 16)) >> 7;
        std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>::deallocate(
          v24 + 8,
          *(void **)(v24 + 16));
      }
      *(_QWORD *)(v24 + 32) = (v14 << 7) + v16;
      *(_QWORD *)(v24 + 24) = (v27 << 7) + v16;
      *(_QWORD *)(v24 + 16) = v16;
    }
  }
  CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>((__int64)&v13);
  return std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
}
