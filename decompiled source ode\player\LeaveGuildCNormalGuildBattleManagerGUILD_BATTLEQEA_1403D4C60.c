/*
 * Function: ?LeaveGuild@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAEPEAVCPlayer@@@Z
 * Address: 0x1403D4C60
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleManager::LeaveGuild(GUILD_BATTLE::CNormalGuildBattleManager *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CNormalGuildBattle *v6; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleManager *v7; // [sp+40h] [bp+8h]@1
  CPlayer *pkPlayera; // [sp+48h] [bp+10h]@1

  pkPlayera = pkPlayer;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = GUILD_BATTLE::CNormalGuildBattleManager::GetBattleByGuildSerial(v7, pkPlayer->m_Param.m_pGuild->m_dwSerial);
  if ( v6 )
    result = GUILD_BATTLE::CNormalGuildBattle::LeaveGuild(v6, pkPlayera);
  else
    result = -114;
  return result;
}
