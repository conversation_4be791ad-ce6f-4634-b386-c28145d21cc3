/*
 * Function: ??$unchecked_uninitialized_fill_n@PEAV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@@2@@std@@_KV123@V?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@@2@@std@@@3@@stdext@@YAXPEAV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@@2@@std@@_KAEBV123@AEAV?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@@2@@std@@@3@@Z
 * Address: 0x14020A0F0
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0> *,unsigned __int64,std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0>,std::allocator<std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0>>>(std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> *_First, unsigned __int64 _Count, std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> *_Val, std::allocator<std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> > *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v7; // [sp+30h] [bp-18h]@4
  std::_Nonscalar_ptr_iterator_tag v8; // [sp+31h] [bp-17h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> *__formal; // [sp+50h] [bp+8h]@1
  unsigned __int64 _Counta; // [sp+58h] [bp+10h]@1
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> *_Vala; // [sp+60h] [bp+18h]@1
  std::allocator<std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> > *_Ala; // [sp+68h] [bp+20h]@1

  _Ala = _Al;
  _Vala = _Val;
  _Counta = _Count;
  __formal = _First;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v7, 0, sizeof(v7));
  v8 = std::_Ptr_cat<std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0> *,std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0> *>(
         &__formal,
         &__formal);
  std::_Uninit_fill_n<std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0> *,unsigned __int64,std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0>,std::allocator<std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0>>>(
    __formal,
    _Counta,
    _Vala,
    _Ala,
    v8,
    v7);
}
