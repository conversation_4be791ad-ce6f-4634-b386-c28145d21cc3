/*
 * Function: ??1CHolyStoneSystem@@QEAA@XZ
 * Address: 0x14027AA80
 */

void __fastcall CHolyStoneSystem::~CHolyStoneSystem(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CHolyStoneSystem *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CMyTimer::~CMyTimer(&v5->m_tmrCumPlayer);
  CHolyScheduleData::~CHolyScheduleData(&v5->m_ScheculeData);
  CMyTimer::~CMyTimer(&v5->m_tmrHSKSystem);
  CLogFile::~CLogFile(&v5->m_logPer10Min);
  CLogFile::~CLogFile(&v5->m_logQuestDestroy);
  CLogFile::~CLogFile(&v5->m_logQuest);
  CRecordData::~CRecordData(&v5->m_tblQuest);
}
