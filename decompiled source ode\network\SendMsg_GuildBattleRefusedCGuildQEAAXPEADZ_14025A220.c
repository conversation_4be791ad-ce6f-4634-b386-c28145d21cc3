/*
 * Function: ?SendMsg_GuildBattleRefused@CGuild@@QEAAXPEAD@Z
 * Address: 0x14025A220
 */

void __fastcall CGuild::SendMsg_GuildBattleRefused(CGuild *this, char *pwszName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-98h]@1
  char Dest; // [sp+38h] [bp-60h]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v7; // [sp+65h] [bp-33h]@4
  CPlayer *v8; // [sp+78h] [bp-20h]@5
  unsigned __int64 v9; // [sp+88h] [bp-10h]@4
  CGuild *v10; // [sp+A0h] [bp+8h]@1

  v10 = this;
  v2 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v4 ^ _security_cookie;
  strcpy_0(&Dest, pwszName);
  pbyType = 27;
  v7 = 93;
  if ( v10->m_MasterData.pMember )
  {
    v8 = v10->m_MasterData.pMember->pPlayer;
    if ( v8 )
      CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, &Dest, 0x11u);
  }
}
