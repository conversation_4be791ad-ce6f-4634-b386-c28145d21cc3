/*
 * Function: ?GetEmptyInstanceItemStore@CItemStoreManager@@QEAAPEAVCMapItemStoreList@@XZ
 * Address: 0x140348AB0
 */

CMapItemStoreList *__fastcall CItemStoreManager::GetEmptyInstanceItemStore(CItemStoreManager *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CItemStoreManager *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &j;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  for ( j = 0; j < v5->m_nInstanceItemStoreListNum; ++j )
  {
    if ( !v5->m_InstanceItemStoreList[j].m_bUse )
      return &v5->m_InstanceItemStoreList[j];
  }
  return 0i64;
}
