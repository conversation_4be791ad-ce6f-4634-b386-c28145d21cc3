/*
 * Function: ?SetUpdateRaceBossSerial@CPvpUserAndGuildRankingSystem@@QEAAXEEK@Z
 * Address: 0x1402B9B70
 */

void __fastcall CPvpUserAndGuildRankingSystem::SetUpdateRaceBossSerial(CPvpUserAndGuildRankingSystem *this, char byRace, char byNth, unsigned int dwSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CPvpUserAndGuildRankingSystem *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v4 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  CUserRankingProcess::SetUpdateRaceBossSerial(&v7->m_kUserRankingProcess, byRace, byNth, dwSerial);
}
