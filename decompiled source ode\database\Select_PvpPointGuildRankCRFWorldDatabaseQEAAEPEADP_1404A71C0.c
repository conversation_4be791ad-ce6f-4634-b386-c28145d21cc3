/*
 * Function: ?Select_PvpPointGuildRank@CRFWorldDatabase@@QEAAEPEADPEAU_pvppoint_guild_rank_info@@@Z
 * Address: 0x1404A71C0
 */

char __fastcall CRFWorldDatabase::Select_PvpPointGuildRank(CRFWorldDatabase *this, char *szDate, _pvppoint_guild_rank_info *pkInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  _pvppoint_guild_rank_info::_list *v6; // rax@19
  unsigned __int16 *v7; // rax@19
  char *v8; // rax@19
  char *v9; // rax@19
  char *v10; // rax@19
  char *v11; // rax@19
  long double *v12; // rax@19
  long double *v13; // rax@19
  long double *v14; // rax@19
  __int64 v15; // rax@19
  __int64 v16; // [sp+0h] [bp-488h]@1
  void *SQLStmt; // [sp+20h] [bp-468h]@15
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-460h]@19
  char Dest; // [sp+40h] [bp-448h]@6
  SQLLEN v20; // [sp+458h] [bp-30h]@19
  __int16 v21; // [sp+464h] [bp-24h]@11
  int v22; // [sp+468h] [bp-20h]@6
  unsigned __int64 v23; // [sp+478h] [bp-10h]@4
  CRFWorldDatabase *v24; // [sp+490h] [bp+8h]@1
  _pvppoint_guild_rank_info *v25; // [sp+4A0h] [bp+18h]@1

  v25 = pkInfo;
  v24 = this;
  v3 = &v16;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v23 = (unsigned __int64)&v16 ^ _security_cookie;
  if ( pkInfo )
  {
    v22 = 0;
    sprintf(
      &Dest,
      "select top %u r.[serial], r.[rank], r.[id], r.[race], r.[grade], r.[killpvppoint], r.[guildbattlepvppoint], r.[sum"
      "pvppoint] from [dbo].[tbl_PvpPointGuildRank%s] as r join [dbo].[tbl_guild] as g on r.[serial] = g.[serial] and g.["
      "dck] = 0 where r.[sumpvppoint] > 0 order by r.[race], r.[sumpvppoint] desc",
      500i64,
      szDate);
    if ( v24->m_bSaveDBLog )
      CRFNewDatabase::Log((CRFNewDatabase *)&v24->vfptr, &Dest);
    if ( v24->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v24->vfptr) )
    {
      v21 = SQLExecDirect_0(v24->m_hStmtSelect, &Dest, -3);
      if ( v21 && v21 != 1 )
      {
        if ( v21 == 100 )
        {
          result = 2;
        }
        else
        {
          SQLStmt = v24->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v24->vfptr, v21, &Dest, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v24->vfptr, v21, v24->m_hStmtSelect);
          result = 1;
        }
      }
      else
      {
        for ( v25->wCount = 0; ; ++v25->wCount )
        {
          v21 = SQLFetch_0(v24->m_hStmtSelect);
          if ( v21 )
          {
            if ( v21 != 1 )
              break;
          }
          v6 = &v25->list[v25->wCount];
          StrLen_or_IndPtr = &v20;
          SQLStmt = 0i64;
          v21 = SQLGetData_0(v24->m_hStmtSelect, 1u, -18, v6, 0i64, &v20);
          v7 = &v25->list[v25->wCount].wRank;
          StrLen_or_IndPtr = &v20;
          SQLStmt = 0i64;
          v21 = SQLGetData_0(v24->m_hStmtSelect, 2u, -17, v7, 0i64, &v20);
          v8 = v25->list[v25->wCount].wszGuildName;
          StrLen_or_IndPtr = &v20;
          SQLStmt = (void *)17;
          v21 = SQLGetData_0(v24->m_hStmtSelect, 3u, 1, v8, 17i64, &v20);
          v9 = v25->list[v25->wCount].wszGuildName;
          StrLen_or_IndPtr = &v20;
          SQLStmt = (void *)17;
          v21 = SQLGetData_0(v24->m_hStmtSelect, 2u, 1, v9, 17i64, &v20);
          v10 = &v25->list[v25->wCount].byRace;
          StrLen_or_IndPtr = &v20;
          SQLStmt = 0i64;
          v21 = SQLGetData_0(v24->m_hStmtSelect, 4u, -6, v10, 0i64, &v20);
          v11 = &v25->list[v25->wCount].byGrade;
          StrLen_or_IndPtr = &v20;
          SQLStmt = 0i64;
          v21 = SQLGetData_0(v24->m_hStmtSelect, 5u, -6, v11, 0i64, &v20);
          v12 = &v25->list[v25->wCount].dKillPvpPoint;
          StrLen_or_IndPtr = &v20;
          SQLStmt = 0i64;
          v21 = SQLGetData_0(v24->m_hStmtSelect, 6u, 8, v12, 0i64, &v20);
          v13 = &v25->list[v25->wCount].dGuildBattlePvpPoint;
          StrLen_or_IndPtr = &v20;
          SQLStmt = 0i64;
          v21 = SQLGetData_0(v24->m_hStmtSelect, 7u, 8, v13, 0i64, &v20);
          v14 = &v25->list[v25->wCount].dSumPvpPoint;
          StrLen_or_IndPtr = &v20;
          SQLStmt = 0i64;
          v21 = SQLGetData_0(v24->m_hStmtSelect, 8u, 8, v14, 0i64, &v20);
          v15 = v25->list[v25->wCount].byRace;
          if ( v25->list[v25->wCount].byRace >= 3 )
            ++v25->wRaceCnt[3];
          else
            ++v25->wRaceCnt[v25->list[v25->wCount].byRace];
        }
        if ( v24->m_hStmtSelect )
          SQLCloseCursor_0(v24->m_hStmtSelect);
        if ( v24->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v24->vfptr, "%s Success", &Dest);
        result = 0;
      }
    }
    else
    {
      CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v24->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
