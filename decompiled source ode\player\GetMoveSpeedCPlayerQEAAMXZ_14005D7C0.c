/*
 * Function: ?GetMoveSpeed@CPlayer@@QEAAMXZ
 * Address: 0x14005D7C0
 */

float __fastcall CPlayer::GetMoveSpeed(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  float result; // xmm0_4@13
  __int64 v4; // [sp+0h] [bp-78h]@1
  float v5; // [sp+30h] [bp-48h]@4
  _base_fld *v6; // [sp+38h] [bp-40h]@5
  int j; // [sp+40h] [bp-38h]@5
  _base_fld *v8; // [sp+48h] [bp-30h]@7
  _base_fld *v9; // [sp+50h] [bp-28h]@11
  char v10; // [sp+58h] [bp-20h]@14
  _base_fld *v11; // [sp+60h] [bp-18h]@22
  _base_fld *v12; // [sp+68h] [bp-10h]@26
  CPlayer *v13; // [sp+80h] [bp+8h]@1

  v13 = this;
  v1 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = 0.0;
  if ( CPlayer::IsRidingUnit(v13) )
  {
    v6 = CRecordData::GetRecord(&stru_1799C8BA0, v13->m_pUsingUnit->byFrame);
    v5 = *(float *)&v6[1].m_strCode[12];
    for ( j = 0; j < 6; ++j )
    {
      v8 = CRecordData::GetRecord(&stru_1799C86D0 + j, v13->m_pUsingUnit->byPart[j]);
      if ( v8 )
        v5 = *(float *)&v8[4].m_strCode[52];
    }
    if ( v13->m_byMoveType == 1 )
    {
      v9 = CRecordData::GetRecord(&stru_1799C86D0 + 5, v13->m_pUsingUnit->byPart[5]);
      if ( v9 )
        v5 = v5 + *(float *)&v9[5].m_strCode[60];
    }
    result = v5;
  }
  else
  {
    v10 = v13->m_byMoveType;
    if ( v10 )
    {
      if ( v10 == 1 )
      {
        if ( v13->m_bInGuildBattle && v13->m_bTakeGravityStone )
        {
          result = FLOAT_3_0;
        }
        else
        {
          v11 = v13->m_pRecordSet;
          _effect_parameter::GetEff_Plus(&v13->m_EP, 20);
          result = *(float *)&v11[2].m_strCode[4] + 0.0;
        }
      }
      else if ( v10 == 2 )
      {
        if ( v13->m_bInGuildBattle && v13->m_bTakeGravityStone )
        {
          result = FLOAT_3_0;
        }
        else
        {
          v12 = v13->m_pRecordSet;
          CEquipItemSFAgent::GetBoosterAddSpeed(&v13->EquipItemSFAgent);
          result = *(float *)&v12[2].m_strCode[4] + 0.0;
        }
      }
      else
      {
        result = 0.0;
      }
    }
    else
    {
      result = *(float *)&v13->m_pRecordSet[2].m_strCode[0];
    }
  }
  return result;
}
