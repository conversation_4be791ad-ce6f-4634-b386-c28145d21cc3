/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAKPEAKV?$allocator@K@std@@@stdext@@YAPEAKPEAK00AEAV?$allocator@K@std@@@Z
 * Address: 0x140004813
 */

unsigned int *__fastcall stdext::_Unchecked_uninitialized_move<unsigned long *,unsigned long *,std::allocator<unsigned long>>(unsigned int *_First, unsigned int *_Last, unsigned int *_Dest, std::allocator<unsigned long> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<unsigned long *,unsigned long *,std::allocator<unsigned long>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
