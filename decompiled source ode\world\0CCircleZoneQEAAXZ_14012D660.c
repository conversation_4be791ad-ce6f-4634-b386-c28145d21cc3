/*
 * Function: ??0CCircleZone@@QEAA@XZ
 * Address: 0x14012D660
 */

void __fastcall CCircleZone::CCircleZone(CCircleZone *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CCircleZone *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CCharacter::CCharacter((CCharacter *)&v4->vfptr);
  v4->vfptr = (CGameObjectVtbl *)&CCircleZone::`vftable';
  v4->m_eState = -1;
  v4->m_iPortalInx = -1;
  v4->m_byColor = -1;
  v4->m_pkGoalPos = 0i64;
}
