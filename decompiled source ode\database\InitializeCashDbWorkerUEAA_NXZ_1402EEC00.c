/*
 * Function: ?Initialize@CashDbWorker@@UEAA_NXZ
 * Address: 0x1402EEC00
 */

char __fastcall CashDbWorker::Initialize(CashDbWorker *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CashDbWorker *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CashDbWorker::_init_loggers(v5) )
  {
    if ( (unsigned __int8)((int (__fastcall *)(CashDbWorker *))v5->vfptr[5].DoWork)(v5) )
    {
      if ( Worker::Initialize((Worker *)&v5->vfptr, 1, 1) )
      {
        result = 1;
      }
      else
      {
        MyMessageBox("CashDbWorker::Initialize()", "Worker::Initialize(1, 1) Fail!");
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
