/*
 * Function: ?Init@CGuildBattleLogger@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403CE8D0
 */

char __fastcall GUILD_BATTLE::CGuildBattleLogger::Init(GUILD_BATTLE::CGuildBattleLogger *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@6
  char result; // al@9
  __int64 v5; // [sp+0h] [bp-48h]@1
  CLogFile *v6; // [sp+20h] [bp-28h]@8
  CLogFile *v7; // [sp+28h] [bp-20h]@5
  __int64 v8; // [sp+30h] [bp-18h]@4
  CLogFile *v9; // [sp+38h] [bp-10h]@6
  GUILD_BATTLE::CGuildBattleLogger *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = -2i64;
  CreateDirectoryA("..\\ZoneServerLog\\Systemlog\\GuildBattle", 0i64);
  clear_file("..\\ZoneServerLog\\Systemlog\\GuildBattle", 0xFu);
  if ( v10->m_pkLogger
    || ((v7 = (CLogFile *)operator new(0xB8ui64)) == 0i64 ? (v9 = 0i64) : (CLogFile::CLogFile(v7), v9 = (CLogFile *)v3),
        v6 = v9,
        (v10->m_pkLogger = v9) != 0i64) )
  {
    GUILD_BATTLE::CGuildBattleLogger::CreateLogFile(v10, "System");
    result = 1;
  }
  else
  {
    CLogFile::Write(&stru_1799C8F30, "CGuildBattleLogger::Init() new CLogFile Fail!");
    result = 0;
  }
  return result;
}
