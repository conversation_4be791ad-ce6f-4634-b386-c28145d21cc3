/*
 * Function: SQLRemoveDriver
 * Address: 0x1404DAB7C
 */

int __fastcall SQLRemoveDriver(const char *lpszDriver, int fRemoveDSN, unsigned int *lpdwUsageCount)
{
  const char *v3; // rsi@1
  unsigned int *v4; // rbx@1
  int v5; // edi@1
  __int64 (__cdecl *v6)(); // rax@1
  int result; // eax@2

  v3 = lpszDriver;
  v4 = lpdwUsageCount;
  v5 = fRemoveDSN;
  v6 = ODBC___GetSetupProc("SQLRemoveDriver");
  if ( v6 )
    result = ((int (__fastcall *)(const char *, _QWORD, unsigned int *))v6)(v3, (unsigned int)v5, v4);
  else
    result = 0;
  return result;
}
