/*
 * Function: j_?Select_UnmannedTraderSearchPageInfo@CRFWorldDatabase@@QEAAEEEEEEKKPEBDPEAU_unmannedtrader_page_info@@@Z
 * Address: 0x1400102E4
 */

char __fastcall CRFWorldDatabase::Select_UnmannedTraderSearchPageInfo(CRFWorldDatabase *this, char byType, char byRace, char byClass1, char byClass2, char byClass3, unsigned int dwMaxRowCount, unsigned int dwExcludeRowCount, const char *szSortQuery, _unmannedtrader_page_info *pkInfo)
{
  return CRFWorldDatabase::Select_UnmannedTraderSearchPageInfo(
           this,
           byType,
           byRace,
           byClass1,
           byClass2,
           byClass3,
           dwMaxRowCount,
           dwExcludeRowCount,
           szSortQuery,
           pkInfo);
}
