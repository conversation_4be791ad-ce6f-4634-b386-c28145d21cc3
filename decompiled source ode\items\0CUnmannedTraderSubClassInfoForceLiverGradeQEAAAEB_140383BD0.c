/*
 * Function: ??0CUnmannedTraderSubClassInfoForceLiverGrade@@QEAA@AEBV0@@Z
 * Address: 0x140383BD0
 */

void __fastcall CUnmannedTraderSubClassInfoForceLiverGrade::CUnmannedTraderSubClassInfoForceLiverGrade(CUnmannedTraderSubClassInfoForceLiverGrade *this, CUnmannedTraderSubClassInfoForceLiverGrade *lhs)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  __int64 v5; // [sp+20h] [bp-18h]@4
  CUnmannedTraderSubClassInfoForceLiverGrade *v6; // [sp+40h] [bp+8h]@1
  CUnmannedTraderSubClassInfoForceLiverGrade *lhsa; // [sp+48h] [bp+10h]@1

  lhsa = lhs;
  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = -2i64;
  CUnmannedTraderSubClassInfo::CUnmannedTraderSubClassInfo((CUnmannedTraderSubClassInfo *)&v6->vfptr, 0xFFFFFFFF);
  v6->vfptr = (CUnmannedTraderSubClassInfoVtbl *)&CUnmannedTraderSubClassInfoForceLiverGrade::`vftable';
  strcpy_0(v6->m_szName, "forcelivergrade");
  CUnmannedTraderSubClassInfoForceLiverGrade::operator=(v6, lhsa);
}
