/*
 * Function: ?dev_up_skill@CPlayer@@QEAA_NPEADH@Z
 * Address: 0x1400BBAC0
 */

char __fastcall CPlayer::dev_up_skill(CPlayer *this, char *pszSkillCode, int nCum)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  _base_fld *v7; // [sp+20h] [bp-18h]@4
  CPlayer *v8; // [sp+40h] [bp+8h]@1
  int dwNewData; // [sp+50h] [bp+18h]@1

  dwNewData = nCum;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = CRecordData::GetRecord(&stru_1799C8410, pszSkillCode);
  if ( v7 )
  {
    CPlayer::Emb_UpdateStat(v8, v7->m_dwIndex + 4, dwNewData, 0);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
