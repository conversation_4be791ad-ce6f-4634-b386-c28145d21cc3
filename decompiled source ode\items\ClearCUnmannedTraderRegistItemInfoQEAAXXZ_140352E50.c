/*
 * Function: ?Clear@CUnmannedTraderRegistItemInfo@@QEAAXXZ
 * Address: 0x140352E50
 */

void __fastcall CUnmannedTraderRegistItemInfo::Clear(CUnmannedTraderRegistItemInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CUnmannedTraderRegistItemInfo *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CUnmannedTraderItemState::Clear(&v4->m_kState);
  v4->m_dwRegistSerial = 0;
  v4->m_wItemSerial = -1;
  v4->m_dwETSerialNumber = 0;
  v4->m_dwPrice = 0;
  v4->m_tStartTime = 0i64;
  v4->m_bySellTurm = 0;
  v4->m_dwBuyerSerial = 0;
  v4->m_dwTax = 0;
  v4->m_tResultTime = 0i64;
  v4->m_wszBuyerName[0] = 0;
  v4->m_szBuyerAccount[0] = 0;
  v4->m_byTableCode = -1;
  v4->m_wItemIndex = 255;
  v4->m_byStorageIndex = -1;
  v4->m_dwD = -1i64;
  v4->m_dwU = 0x7FFFFFF;
}
