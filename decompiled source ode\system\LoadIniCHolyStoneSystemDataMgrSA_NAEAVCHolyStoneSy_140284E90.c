/*
 * Function: ?<PERSON><PERSON><PERSON><PERSON>@CHolyStoneSystemDataMgr@@SA_NAEAVCHolyStoneSystem@@@Z
 * Address: 0x140284E90
 */

char __fastcall CHolyStoneSystemDataMgr::<PERSON>adIni(CHolyStoneSystem *clsHolyStoneSystem)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@7
  unsigned int v4; // eax@7
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-2C8h]@1
  char pszErrMsg; // [sp+40h] [bp-288h]@4
  char _Dest[128]; // [sp+E0h] [bp-1E8h]@7
  char Dest; // [sp+180h] [bp-148h]@33
  char ReturnedString; // [sp+1E0h] [bp-E8h]@7
  float v11; // [sp+224h] [bp-A4h]@7
  float v12; // [sp+228h] [bp-A0h]@7
  unsigned int j; // [sp+22Ch] [bp-9Ch]@31
  __holy_stone_data *v14; // [sp+230h] [bp-98h]@33
  int v15; // [sp+238h] [bp-90h]@53
  _base_fld *v16; // [sp+240h] [bp-88h]@55
  char Dst; // [sp+260h] [bp-68h]@58
  bool v18; // [sp+2B0h] [bp-18h]@31
  bool v19; // [sp+2B1h] [bp-17h]@31
  unsigned __int64 v20; // [sp+2B8h] [bp-10h]@4
  CHolyStoneSystem *v21; // [sp+2D0h] [bp+8h]@1

  v21 = clsHolyStoneSystem;
  v1 = &v6;
  for ( i = 176i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v20 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( !CRecordData::ReadRecord(&v21->m_tblQuest, ".\\script\\HolyStoneKeepperQuest.dat", 0x968u, &pszErrMsg) )
  {
    MyMessageBox("CHolyStoneSystem Data init", &pszErrMsg);
    ServerProgramExit("CHolyStoneSystem Data init", 0);
  }
  v3 = GetKorLocalTime();
  sprintf_s<128>((char (*)[128])_Dest, "..\\ZoneServerLog\\ServiceLog\\HolyStoneSystem%d.log", v3);
  CLogFile::SetWriteLogFile(&v21->m_logQuest, _Dest, 1, 0, 1, 1);
  v4 = GetKorLocalTime();
  sprintf(_Dest, "..\\ZoneServerLog\\ServiceLog\\HolyStoneDestroy%d.log", v4);
  CLogFile::SetWriteLogFile(&v21->m_logQuestDestroy, _Dest, 1, 0, 1, 1);
  v11 = FLOAT_100_0;
  v11 = (float)(signed int)GetPrivateProfileIntA("HolySystem", "KeeperHPRate", 100, ".\\Initialize\\NewHolySystem.ini");
  v21->m_fKeeperHPRate = v11 / 100.0;
  v12 = FLOAT_100_0;
  v12 = (float)(signed int)GetPrivateProfileIntA(
                             "HolySystem",
                             "FirstKeeperHPRate",
                             100,
                             ".\\Initialize\\NewHolySystem.ini");
  v21->m_fFirstKeeperHPRate = v12 / 100.0;
  GetPrivateProfileStringA(
    "HolySystem",
    "KeeperCreateMap",
    "-1",
    &ReturnedString,
    0x40u,
    ".\\Initialize\\NewHolySystem.ini");
  if ( !strncmp(&ReturnedString, "-1", 2ui64) )
  {
    MyMessageBox("CHolyStoneSystemDataeMgr::LoadIni()", "Keeper Map Code Error(%s) : no defined", &ReturnedString);
    return 0;
  }
  v21->m_HolyKeeperData.pCreateMap = CMapOperation::GetMap(&g_MapOper, &ReturnedString);
  if ( !v21->m_HolyKeeperData.pCreateMap )
  {
    MyMessageBox("CHolyStoneSystemDataeMgr::LoadIni()", "Keeper Map Code Error(%s) : unregistered map", &ReturnedString);
    return 0;
  }
  if ( v21->m_HolyKeeperData.pCreateMap->m_pMapSet->m_nMapType )
  {
    MyMessageBox("CHolyStoneSystemDataeMgr::LoadIni()", "Keeper Map Code Error(%s) : no standard map", &ReturnedString);
    return 0;
  }
  GetPrivateProfileStringA(
    "HolySystem",
    "KeeperCreateDummy",
    "-1",
    &ReturnedString,
    0x40u,
    ".\\Initialize\\NewHolySystem.ini");
  if ( !strncmp(&ReturnedString, "-1", 2ui64) )
  {
    MyMessageBox(
      "CHolyStoneSystemDataeMgr::LoadIni()",
      "Keeper create-dummy Code Error(%s) : no defined",
      &ReturnedString);
    return 0;
  }
  if ( !CMapData::LoadHolySystemDummy(
          v21->m_HolyKeeperData.pCreateMap,
          &ReturnedString,
          &v21->m_HolyKeeperData.CreateDummy) )
  {
    MyMessageBox(
      "CHolyStoneSystemDataeMgr::LoadIni()",
      "Keeper create-dummy Code Error(%s) : dummy error",
      &ReturnedString);
    return 0;
  }
  GetPrivateProfileStringA(
    "HolySystem",
    "KeeperActiveDummy",
    "-1",
    &ReturnedString,
    0x40u,
    ".\\Initialize\\NewHolySystem.ini");
  if ( !strncmp(&ReturnedString, "-1", 2ui64) )
  {
    MyMessageBox(
      "CHolyStoneSystemDataeMgr::LoadIni()",
      "Keeper active-dummy Code Error(%s) : no defined",
      &ReturnedString);
    return 0;
  }
  if ( !CMapData::LoadHolySystemDummy(
          v21->m_HolyKeeperData.pCreateMap,
          &ReturnedString,
          &v21->m_HolyKeeperData.ActiveDummy) )
  {
    MyMessageBox(
      "CHolyStoneSystemDataeMgr::LoadIni()",
      "Keeper active-dummy Code Error(%s) : dummy error",
      &ReturnedString);
    return 0;
  }
  GetPrivateProfileStringA(
    "HolySystem",
    "KeeperCenterDummy",
    "-1",
    &ReturnedString,
    0x40u,
    ".\\Initialize\\NewHolySystem.ini");
  if ( !strncmp(&ReturnedString, "-1", 2ui64) )
  {
    MyMessageBox(
      "CHolyStoneSystemDataeMgr::LoadIni()",
      "Keeper center-dummy Code Error(%s) : no defined",
      &ReturnedString);
    return 0;
  }
  if ( !CMapData::LoadHolySystemDummy(
          v21->m_HolyKeeperData.pCreateMap,
          &ReturnedString,
          &v21->m_HolyKeeperData.CenterDummy) )
  {
    MyMessageBox(
      "CHolyStoneSystemDataeMgr::LoadIni()",
      "Keeper center-dummy Code Error(%s) : dummy error",
      &ReturnedString);
    return 0;
  }
  GetPrivateProfileStringA(
    "HolySystem",
    "KeeperMonsterCode",
    "-1",
    &ReturnedString,
    0x40u,
    ".\\Initialize\\NewHolySystem.ini");
  if ( !strncmp(&ReturnedString, "-1", 2ui64) )
  {
    MyMessageBox("CHolyStoneSystemDataeMgr::LoadIni()", "Keeper Code Error(%s) :  no defined", &ReturnedString);
    return 0;
  }
  v21->m_HolyKeeperData.pRec = (_monster_fld *)CRecordData::GetRecord(&stru_1799C6210, &ReturnedString);
  if ( !v21->m_HolyKeeperData.pRec )
  {
    MyMessageBox("CHolyStoneSystemDataeMgr::LoadIni()", "Keeper Code Error(%s) : unregistered code", &ReturnedString);
    return 0;
  }
  v21->m_nHolyStoneNum = GetPrivateProfileIntA("HolySystem", "StoneNum", 0, ".\\Initialize\\NewHolySystem.ini");
  if ( v21->m_nHolyStoneNum > 3 )
  {
    MyMessageBox("CHolyStoneSystemDataeMgr::LoadIni()", "number of stone error(%d)", v21->m_nHolyStoneNum);
    return 0;
  }
  GetPrivateProfileStringA(
    "HolyWarFreeMining",
    "FreeMining",
    "FALSE",
    &ReturnedString,
    0x40u,
    ".\\Initialize\\NewHolySystem.ini");
  v18 = strcmp_0(&ReturnedString, "TRUE") == 0;
  v21->bFreeMining = v18;
  GetPrivateProfileStringA(
    "HolySystem",
    "MentalPass",
    "FALSE",
    &ReturnedString,
    0x40u,
    ".\\Initialize\\NewHolySystem.ini");
  v19 = strcmp_0(&ReturnedString, "TRUE") == 0;
  v21->m_pMentalPass = v19;
  for ( j = 0; (signed int)j < v21->m_nHolyStoneNum; ++j )
  {
    v14 = &v21->m_HolyStoneData[j];
    sprintf(&Dest, "StoneCreateMap%d", j);
    GetPrivateProfileStringA("HolySystem", &Dest, "-1", &ReturnedString, 0x40u, ".\\Initialize\\NewHolySystem.ini");
    if ( !strncmp(&ReturnedString, "-1", 2ui64) )
    {
      MyMessageBox(
        "CHolyStoneSystemDataeMgr::LoadIni()",
        "%dth stone create-map code error(%s) : no defined",
        j,
        &ReturnedString);
      return 0;
    }
    v14->pCreateMap = CMapOperation::GetMap(&g_MapOper, &ReturnedString);
    if ( !v14->pCreateMap )
    {
      MyMessageBox(
        "CHolyStoneSystemDataeMgr::LoadIni()",
        "%dth stone create-map code error(%s) : unregistered map",
        j,
        &ReturnedString);
      return 0;
    }
    if ( v14->pCreateMap->m_pMapSet->m_nMapType )
    {
      MyMessageBox(
        "CHolyStoneSystemDataeMgr::LoadIni()",
        "%dth stone create-map code error(%s) : no standard map",
        j,
        &ReturnedString);
      return 0;
    }
    sprintf(&Dest, "StoneCreateDummy%d", j);
    GetPrivateProfileStringA("HolySystem", &Dest, "-1", &ReturnedString, 0x40u, ".\\Initialize\\NewHolySystem.ini");
    if ( !strncmp(&ReturnedString, "-1", 2ui64) )
    {
      MyMessageBox(
        "CHolyStoneSystemDataeMgr::LoadIni()",
        "%dth stone create-dummy code error(%s) : no defined",
        j,
        &ReturnedString);
      return 0;
    }
    if ( !CMapData::LoadHolySystemDummy(v14->pCreateMap, &ReturnedString, &v14->CreateDummy) )
    {
      MyMessageBox(
        "CHolyStoneSystemDataeMgr::LoadIni()",
        "%dth stone create-dummy code error(%s) : dummy error",
        j,
        &ReturnedString);
      return 0;
    }
    sprintf(&Dest, "StoneMonsterCode%d", j);
    GetPrivateProfileStringA("HolySystem", &Dest, "-1", &ReturnedString, 0x40u, ".\\Initialize\\NewHolySystem.ini");
    if ( !strncmp(&ReturnedString, "-1", 2ui64) )
    {
      MyMessageBox("CHolyStoneSystemDataeMgr::LoadIni()", "%dth stone code error(%s) : no defined", j, &ReturnedString);
      return 0;
    }
    v14->pRec = (_monster_fld *)CRecordData::GetRecord(&stru_1799C6210, &ReturnedString);
    if ( !v14->pRec )
    {
      MyMessageBox(
        "CHolyStoneSystemDataeMgr::LoadIni()",
        "%dth stone code error(%s) : unregistered code",
        j,
        &ReturnedString);
      return 0;
    }
    sprintf(&Dest, "StoneMasterRace%d", j);
    v14->nRace = GetPrivateProfileIntA("HolySystem", &Dest, -1, ".\\Initialize\\NewHolySystem.ini");
    if ( v14->nRace == -1 || v14->nRace >= 3u )
    {
      MyMessageBox("CHolyStoneSystemDataeMgr::LoadIni()", "%dth stone master-race code error(%d)", j, v14->nRace);
      return 0;
    }
  }
  GetPrivateProfileStringA(
    "HolySystem",
    "HolyMental",
    "-1",
    v21->m_strHolyMental,
    0x40u,
    ".\\Initialize\\NewHolySystem.ini");
  if ( !strncmp(&ReturnedString, "-1", 2ui64) )
  {
    MyMessageBox(
      "CHolyStoneSystemDataeMgr::LoadIni()",
      "HolyMental Item not Setting (%s) : no defined",
      v21->m_strHolyMental);
    return 0;
  }
  v15 = GetItemTableCode(v21->m_strHolyMental);
  if ( v15 == -1 )
  {
    MyMessageBox(
      "CHolyStoneSystemDataeMgr::LoadIni()",
      "HolyMental Item not Setting (%s) : no Invalid code",
      v21->m_strHolyMental);
    return 0;
  }
  v16 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v15, v21->m_strHolyMental);
  if ( !v16 )
  {
    MyMessageBox(
      "CHolyStoneSystemDataeMgr::LoadIni()",
      "HolyMental Item not Setting (%s) : not find",
      v21->m_strHolyMental);
    return 0;
  }
  if ( !v21->m_HolyKeeperData.pCreateMap )
    goto LABEL_73;
  memset_0(&Dst, 0, 0x40ui64);
  GetPrivateProfileStringA("PortalDummyName", "BellaDummyName", "NULL", &Dst, 0x40u, ".\\Initialize\\NewHolySystem.ini");
  if ( !strcmp_0("NULL", &Dst) )
  {
    MyMessageBox("CHolyStoneSystemDataMgr::LoadIni()", "Holy PortalDummy Setting Error<No Ini Portal Info:Bella>");
    return 0;
  }
  v21->m_pPortalDummy[0] = CMapData::GetPortal(v21->m_HolyKeeperData.pCreateMap, &Dst);
  if ( !v21->m_pPortalDummy[0] )
  {
    MyMessageBox("CHolyStoneSystemDataMgr::LoadIni()", "Holy PortalDummy Setting Error<No Ini Portal Info:Bella>");
    return 0;
  }
  memset_0(&Dst, 0, 0x40ui64);
  GetPrivateProfileStringA("PortalDummyName", "CoraDummyName", "NULL", &Dst, 0x40u, ".\\Initialize\\NewHolySystem.ini");
  if ( !strcmp_0("NULL", &Dst) )
  {
    MyMessageBox("CHolyStoneSystemDataMgr::LoadIni()", "Holy PortalDummy Setting Error<No Ini Portal Info:Cora>");
    return 0;
  }
  v21->m_pPortalDummy[1] = CMapData::GetPortal(v21->m_HolyKeeperData.pCreateMap, &Dst);
  if ( !v21->m_pPortalDummy[1] )
  {
    MyMessageBox("CHolyStoneSystemDataMgr::LoadIni()", "Holy PortalDummy Setting Error<No Ini Portal Info:Cora>");
    return 0;
  }
  memset_0(&Dst, 0, 0x40ui64);
  GetPrivateProfileStringA("PortalDummyName", "AccDummyName", "NULL", &Dst, 0x40u, ".\\Initialize\\NewHolySystem.ini");
  if ( !strcmp_0("NULL", &Dst) )
  {
    MyMessageBox("CHolyStoneSystemDataMgr::LoadIni()", "Holy PortalDummy Setting Error<No Ini Portal Info:Acc>");
    return 0;
  }
  v21->m_pPortalDummy[2] = CMapData::GetPortal(v21->m_HolyKeeperData.pCreateMap, &Dst);
  if ( v21->m_pPortalDummy[2] )
  {
LABEL_73:
    v21->m_nRaceBattlePoint[0][1] = GetPrivateProfileIntA(
                                      "RaceBattlePoint",
                                      "WinEmptyScaner",
                                      3000,
                                      ".\\Initialize\\NewHolySystem.ini");
    v21->m_nRaceBattlePoint[1][0] = GetPrivateProfileIntA(
                                      "RaceBattlePoint",
                                      "FailGetScaner",
                                      -1000,
                                      ".\\Initialize\\NewHolySystem.ini");
    v21->m_nRaceBattlePoint[1][1] = GetPrivateProfileIntA(
                                      "RaceBattlePoint",
                                      "FailEmptyScaner",
                                      -2500,
                                      ".\\Initialize\\NewHolySystem.ini");
    v21->m_nRaceBattlePoint[2][0] = GetPrivateProfileIntA(
                                      "RaceBattlePoint",
                                      "LoseGetScaner",
                                      -2000,
                                      ".\\Initialize\\NewHolySystem.ini");
    v21->m_nRaceBattlePoint[2][1] = GetPrivateProfileIntA(
                                      "RaceBattlePoint",
                                      "LoseEmptyScaner",
                                      -5000,
                                      ".\\Initialize\\NewHolySystem.ini");
    result = 1;
  }
  else
  {
    MyMessageBox("CHolyStoneSystemDataMgr::LoadIni()", "Holy PortalDummy Setting Error<No Ini Portal Info:Acc>");
    result = 0;
  }
  return result;
}
