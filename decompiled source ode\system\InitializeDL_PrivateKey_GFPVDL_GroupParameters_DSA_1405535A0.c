/*
 * Function: ?Initialize@?$DL_PrivateKey_GFP@VDL_GroupParameters_DSA@CryptoPP@@@CryptoPP@@QEAAXAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x1405535A0
 */

void __fastcall CryptoPP::DL_PrivateKey_GFP<CryptoPP::DL_GroupParameters_DSA>::Initialize(__int64 a1, struct CryptoPP::RandomNumberGenerator *a2, unsigned int a3)
{
  CryptoPP::GeneratableCryptoMaterial::GenerateRandomWithKeySize(
    (CryptoPP::GeneratableCryptoMaterial *)(a1 + 16),
    a2,
    a3);
}
