/*
 * Function: j_??0?$_List_ptr@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@2@@std@@IEAA@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@1@@Z
 * Address: 0x14000A9D9
 */

void __fastcall std::_List_ptr<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_List_ptr<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>(std::_List_ptr<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *this, std::allocator<std::pair<int const ,_TimeItem_fld const *> > _Al)
{
  std::_List_ptr<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_List_ptr<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>(
    this,
    _Al);
}
