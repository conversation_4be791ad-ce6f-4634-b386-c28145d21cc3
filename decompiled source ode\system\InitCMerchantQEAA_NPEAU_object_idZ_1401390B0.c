/*
 * Function: ?Init@CMerchant@@QEAA_NPEAU_object_id@@@Z
 * Address: 0x1401390B0
 */

char __fastcall CMerchant::Init(CMerchant *this, _object_id *pID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMerchant *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CCharacter::Init((CCharacter *)&v6->vfptr, pID);
  v6->m_pItemStore = 0i64;
  v6->m_dwLastDestroyTime = 0;
  v6->m_byRaceCode = 0;
  memset_0(v6->m_nLeftTicketNum, -1, 8ui64);
  return 1;
}
