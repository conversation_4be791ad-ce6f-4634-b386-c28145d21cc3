/*
 * Function: ?GetC<PERSON>rName@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAPEBDXZ
 * Address: 0x1403EB320
 */

const char *__fastcall GUILD_BATTLE::CNormalGuildBattleGuild::GetColorName(GUILD_BATTLE::CNormalGuildBattleGuild *this)
{
  const char *result; // rax@2

  if ( this->m_byColorInx < 3 )
    result = (const char *)GUILD_BATTLE::CNormalGuildBattleGuild::COLOR_STR[(unsigned __int64)this->m_byColorInx];
  else
    result = (const char *)qword_140972210;
  return result;
}
