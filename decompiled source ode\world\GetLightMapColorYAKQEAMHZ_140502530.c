/*
 * Function: ?GetLightMapColor@@YAKQEAMH@Z
 * Address: 0x140502530
 */

signed __int64 __fastcall GetLightMapColor(float *const a1, int a2)
{
  float v2; // xmm2_4@1
  float v3; // xmm1_4@3
  bool v4; // zf@9
  signed __int64 result; // rax@10
  __int64 v6; // r8@11
  unsigned int v7; // eax@11

  v2 = *a1;
  if ( *a1 == 0.0 )
    v2 = 0.0;
  v3 = a1[1];
  *a1 = v2;
  if ( v3 <= 0.0 )
    v3 = 0.0;
  a1[1] = v3;
  if ( v2 >= 1.0 )
    v2 = FLOAT_1_0;
  *a1 = v2;
  if ( v3 >= 1.0 )
    v3 = FLOAT_1_0;
  v4 = dword_184A79D88 == 0;
  a1[1] = v3;
  if ( v4 )
  {
    result = 0xFFFFFFFFi64;
  }
  else
  {
    v6 = *((_QWORD *)stLightmap + a2);
    v7 = *(_WORD *)(*(_QWORD *)(v6 + 8)
                  + 2
                  * ((signed int)ffloor((float)*(_WORD *)v6 * v2)
                   + *(_WORD *)v6 * (signed __int64)(signed int)ffloor((float)*(_WORD *)(v6 + 2) * v3)));
    result = (v7 >> 11 << 19) | 8 * (v7 & 0x1F) | (((v7 >> 5) & 0x3F) << 10) | 0xFF000000;
  }
  return result;
}
