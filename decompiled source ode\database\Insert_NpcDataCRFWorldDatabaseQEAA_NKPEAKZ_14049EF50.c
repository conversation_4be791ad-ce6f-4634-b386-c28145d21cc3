/*
 * Function: ?Insert_NpcData@CRFWorldDatabase@@QEAA_NKPEAK@Z
 * Address: 0x14049EF50
 */

bool __fastcall CRFWorldDatabase::Insert_NpcData(CRFWorldDatabase *this, unsigned int dwSerial, unsigned int *pNpcData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-188h]@1
  unsigned int v7; // [sp+20h] [bp-168h]@4
  unsigned int v8; // [sp+28h] [bp-160h]@4
  unsigned int v9; // [sp+30h] [bp-158h]@4
  unsigned int v10; // [sp+38h] [bp-150h]@4
  unsigned int v11; // [sp+40h] [bp-148h]@4
  char Dest; // [sp+60h] [bp-128h]@4
  unsigned __int64 v13; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v14; // [sp+190h] [bp+8h]@1

  v14 = this;
  v3 = &v6;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = (unsigned __int64)&v6 ^ _security_cookie;
  v11 = pNpcData[5];
  v10 = pNpcData[4];
  v9 = pNpcData[3];
  v8 = pNpcData[2];
  v7 = pNpcData[1];
  sprintf(&Dest, "{ CALL pInsert_NpcData2( %d, %d, %d, %d, %d, %d, %d ) }", dwSerial, *pNpcData);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v14->vfptr, &Dest, 1);
}
