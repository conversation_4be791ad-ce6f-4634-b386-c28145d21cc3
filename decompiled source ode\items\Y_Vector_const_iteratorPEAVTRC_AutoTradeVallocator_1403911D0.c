/*
 * Function: ??Y?$_Vector_const_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAAAEAV01@_J@Z
 * Address: 0x1403911D0
 */

std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *__fastcall std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator+=(std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, __int64 _Off)
{
  this->_Myptr += _Off;
  return this;
}
