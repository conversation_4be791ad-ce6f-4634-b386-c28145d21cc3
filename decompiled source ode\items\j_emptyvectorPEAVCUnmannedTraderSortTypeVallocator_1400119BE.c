/*
 * Function: j_?empty@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEBA_NXZ
 * Address: 0x1400119BE
 */

bool __fastcall std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::empty(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this)
{
  return std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::empty(this);
}
