/*
 * Function: ?pc_DTradeAskRequest@CPlayer@@QEAAXG@Z
 * Address: 0x1400F3790
 */

void __usercall CPlayer::pc_DTradeAskRequest(CPlayer *this@<rcx>, unsigned __int16 wDstIndex@<dx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@10
  __int64 v6; // [sp+0h] [bp-48h]@1
  char v7; // [sp+20h] [bp-28h]@4
  CGameObject *v8; // [sp+28h] [bp-20h]@4
  int v9; // [sp+30h] [bp-18h]@10
  CPlayer *pAsker; // [sp+50h] [bp+8h]@1

  pAsker = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = 0;
  v8 = (CGameObject *)(&g_Player.vfptr + 6357 * wDstIndex);
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, pAsker->m_id.wIndex) == 99 )
  {
    pAsker->m_pmTrd.bDTradeMode = 0;
    v8[229].m_bCorpse = 0;
    v7 = 23;
  }
  else if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v8[226].m_rtPer100.m_wCurTable) == 99 )
  {
    pAsker->m_pmTrd.bDTradeMode = 0;
    v8[229].m_bCorpse = 0;
    v7 = 24;
  }
  else if ( v8->m_bLive
         && BYTE1(v8[9].m_SectorNetPoint.m_pNext)
         && (v9 = CPlayerDB::GetRaceCode((CPlayerDB *)&v8[10].m_bCorpse),
             v5 = CPlayerDB::GetRaceCode(&pAsker->m_Param),
             v9 == v5)
         && !v8->m_bCorpse
         && v8->m_pCurMap == pAsker->m_pCurMap
         && CGameObject::GetCurSecNum(v8) != -1
         && (_effect_parameter::GetEff_Have((_effect_parameter *)v8[9].m_fOldPos, 50), a3 <= 0.0) )
  {
    if ( pAsker->m_pmTrd.bDTradeMode || v8[229].m_bCorpse )
    {
      v7 = 1;
    }
    else if ( pAsker->m_pCurMap->m_pMapSet->m_nMapType == 1 )
    {
      v7 = 3;
    }
    else if ( pAsker->m_byUserDgr == BYTE4(v8[9].m_SectorNetPoint.m_pNext) )
    {
      GetSqrt(v8->m_fCurPos, pAsker->m_fCurPos);
      if ( a3 > 100.0 )
        v7 = 2;
    }
    else
    {
      v7 = 3;
    }
  }
  else
  {
    v7 = 3;
  }
  if ( !v7 )
  {
    pAsker->m_pmTrd.wDTradeDstIndex = v8->m_ObjID.m_wIndex;
    pAsker->m_pmTrd.dwDTradeDstSerial = v8->m_dwObjSerial;
    CPlayer::SendMsg_DTradeAskInform((CPlayer *)v8, pAsker);
  }
  CPlayer::SendMsg_DTradeAskResult(pAsker, v7);
}
