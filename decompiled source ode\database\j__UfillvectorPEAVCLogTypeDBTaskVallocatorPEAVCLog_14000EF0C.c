/*
 * Function: j_?_Ufill@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@IEAAPEAPEAVCLogTypeDBTask@@PEAPEAV3@_KAEBQEAV3@@Z
 * Address: 0x14000EF0C
 */

CLogTypeDBTask **__fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Ufill(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, CLogTypeDBTask **_Ptr, unsigned __int64 _Count, CLogTypeDBTask *const *_Val)
{
  return std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Ufill(this, _Ptr, _Count, _Val);
}
