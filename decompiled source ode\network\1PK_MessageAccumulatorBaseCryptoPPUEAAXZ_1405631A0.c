/*
 * Function: ??1PK_MessageAccumulatorBase@CryptoPP@@UEAA@XZ
 * Address: 0x1405631A0
 */

void __fastcall CryptoPP::PK_MessageAccumulatorBase::~PK_MessageAccumulatorBase(CryptoPP::PK_MessageAccumulatorBase *this)
{
  CryptoPP::PK_MessageAccumulatorBase *v1; // [sp+40h] [bp+8h]@1

  v1 = this;
  CryptoPP::Integer::~Integer(&this->m_s);
  CryptoPP::Integer::~Integer(&v1->m_k);
  CryptoPP::Sec<PERSON>lock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~Sec<PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v1->m_semisignature);
  CryptoPP::Sec<PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~Sec<PERSON><PERSON><unsigned char,CryptoPP::AllocatorWith<PERSON>leanup<unsigned char,0>>(&v1->m_presignature);
  CryptoPP::Sec<PERSON>lock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v1->m_representative);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v1->m_recoverableMessage);
  CryptoPP::PK_MessageAccumulator::~PK_MessageAccumulator((CryptoPP::PK_MessageAccumulator *)&v1->vfptr);
}
