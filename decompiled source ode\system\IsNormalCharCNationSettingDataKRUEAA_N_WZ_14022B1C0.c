/*
 * Function: ?IsNormalChar@CNationSettingDataKR@@UEAA_N_W@Z
 * Address: 0x14022B1C0
 */

bool __fastcall CNationSettingDataKR::IsNormalChar(CNationSettingDataKR *this, const wchar_t wcChar)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // [sp+0h] [bp-18h]@1

  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  return (signed int)wcChar >= 44032 && (signed int)wcChar <= 55203;
}
