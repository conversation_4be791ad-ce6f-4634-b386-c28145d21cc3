/*
 * Function: ?IncPvpPoint@CNormalGuildBattleGuildMember@GUILD_BATTLE@@QEAANNAEAVCNormalGuildBattleLogger@2@@Z
 * Address: 0x1403DFCE0
 */

long double __fastcall GUILD_BATTLE::CNormalGuildBattleGuildMember::IncPvpPoint(GUILD_BATTLE::CNormalGuildBattleGuildMember *this, long double dInc, GUILD_BATTLE::CNormalGuildBattleLogger *kLogger)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  long double result; // xmm0_8@5
  char *v6; // rax@8
  __int64 v7; // [sp-20h] [bp-58h]@1
  long double v8; // [sp+0h] [bp-38h]@7
  long double v9; // [sp+8h] [bp-30h]@8
  CPlayer *v10; // [sp+20h] [bp-18h]@8
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v11; // [sp+40h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattleLogger *v12; // [sp+50h] [bp+18h]@1

  v12 = kLogger;
  v11 = this;
  v3 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( dInc == 0.0 )
  {
    result = 0.0;
  }
  else if ( GUILD_BATTLE::CNormalGuildBattleGuildMember::IsExist(v11) )
  {
    v10 = v11->m_pkMember->pPlayer;
    CPlayer::AlterPvPPoint(v10, dInc, guildbattle, 0xFFFFFFFF);
    v6 = CPlayerDB::GetCharNameW(&v10->m_Param);
    v9 = dInc;
    LODWORD(v8) = v10->m_dwObjSerial;
    GUILD_BATTLE::CNormalGuildBattleLogger::Log(
      v12,
      "CNormalGuildBattleGuildMember::IncPvpPoint( dInc(%f) ) : %s(%d) IncPvpPoint(%f)!",
      dInc,
      v6);
    result = dInc;
  }
  else
  {
    GUILD_BATTLE::CNormalGuildBattleGuildMember::PushDQSPvpPoint(v11, (signed int)floor(dInc));
    v8 = dInc;
    GUILD_BATTLE::CNormalGuildBattleLogger::Log(
      v12,
      "CNormalGuildBattleGuildMember::IncPvpPoint( dInc(%f) ) : NotConnected! Serial(%d) IncPvpPoint(%f)!",
      dInc,
      v11->m_dwSerial);
    result = dInc;
  }
  return result;
}
