/*
 * Function: ??1_param_cashitem_dblog@@QEAA@XZ
 * Address: 0x140304D50
 */

void __fastcall _param_cashitem_dblog::~_param_cashitem_dblog(_param_cashitem_dblog *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _param_cashitem_dblog *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _param_cash::~_param_cash((_param_cash *)&v4->in_dwAccountSerial);
}
