/*
 * Function: ?_check_party_target_object@CPlayer@@QEAAXXZ
 * Address: 0x1400FF5B0
 */

void __fastcall CPlayer::_check_party_target_object(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  CGameObject *pObject; // [sp+20h] [bp-18h]@4
  CPlayer *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  pObject = 0i64;
  if ( CPartyPlayer::IsPartyMode(v5->m_pPartyMgr) && CPartyPlayer::IsPartyBoss(v5->m_pPartyMgr) )
  {
    pObject = v5->m_GroupTargetObject[0].pObject;
    if ( pObject )
    {
      if ( pObject->m_bLive )
      {
        if ( v5->m_GroupTargetObject[0].byKind == pObject->m_ObjID.m_byKind
          && v5->m_GroupTargetObject[0].byID == pObject->m_ObjID.m_byID
          && v5->m_GroupTargetObject[0].dwSerial == pObject->m_dwObjSerial )
        {
          CPlayer::pc_RefreshGroupTargetPosition(v5, 0, pObject);
        }
        else
        {
          CPlayer::pc_ReleaseGroupTargetObjectRequest(v5, 0);
        }
      }
      else
      {
        CPlayer::pc_ReleaseGroupTargetObjectRequest(v5, 0);
      }
    }
  }
}
