/*
 * Function: ?dev_loot_material@CPlayer@@QEAA_NXZ
 * Address: 0x1400BD000
 */

char __fastcall CPlayer::dev_loot_material(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@5
  int v5; // eax@10
  int v6; // eax@15
  __int64 v7; // [sp+0h] [bp-158h]@1
  CMapData *pMap; // [sp+30h] [bp-128h]@6
  unsigned __int16 wLayerIndex; // [sp+38h] [bp-120h]@6
  float *pStdPos; // [sp+40h] [bp-118h]@6
  bool bHide; // [sp+48h] [bp-110h]@6
  int nIndex; // [sp+50h] [bp-108h]@4
  _STORAGE_LIST::_db_con pItem; // [sp+68h] [bp-F0h]@6
  _STORAGE_LIST::_db_con v14; // [sp+B8h] [bp-A0h]@11
  _STORAGE_LIST::_db_con v15; // [sp+108h] [bp-50h]@16
  CPlayer *pOwner; // [sp+160h] [bp+8h]@1

  pOwner = this;
  v1 = &v7;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( nIndex = 0; ; ++nIndex )
  {
    v3 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 20);
    if ( nIndex >= v3 )
      break;
    _STORAGE_LIST::_db_con::_db_con(&pItem);
    pItem.m_byTableCode = 20;
    pItem.m_wItemIndex = nIndex;
    pItem.m_dwDur = 99i64;
    pItem.m_dwLv = 0xFFFFFFF;
    bHide = 1;
    pStdPos = pOwner->m_fCurPos;
    wLayerIndex = pOwner->m_wMapLayerIndex;
    pMap = pOwner->m_pCurMap;
    if ( !CreateItemBox(&pItem, pOwner, 0xFFFFFFFF, 0, 0i64, 2, pMap, wLayerIndex, pOwner->m_fCurPos, 1) )
      return 1;
  }
  for ( nIndex = 0; ; ++nIndex )
  {
    v5 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 18);
    if ( nIndex >= v5 )
      break;
    _STORAGE_LIST::_db_con::_db_con(&v14);
    v14.m_byTableCode = 18;
    v14.m_wItemIndex = nIndex;
    v14.m_dwDur = 99i64;
    v14.m_dwLv = 0xFFFFFFF;
    bHide = 1;
    pStdPos = pOwner->m_fCurPos;
    wLayerIndex = pOwner->m_wMapLayerIndex;
    pMap = pOwner->m_pCurMap;
    if ( !CreateItemBox(&v14, pOwner, 0xFFFFFFFF, 0, 0i64, 2, pMap, wLayerIndex, pOwner->m_fCurPos, 1) )
      return 1;
  }
  for ( nIndex = 0; ; ++nIndex )
  {
    v6 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 11);
    if ( nIndex >= v6 )
      break;
    _STORAGE_LIST::_db_con::_db_con(&v15);
    v15.m_byTableCode = 11;
    v15.m_wItemIndex = nIndex;
    v15.m_dwDur = GetItemDurPoint(11, nIndex);
    v15.m_dwLv = 0xFFFFFFF;
    bHide = 1;
    pStdPos = pOwner->m_fCurPos;
    wLayerIndex = pOwner->m_wMapLayerIndex;
    pMap = pOwner->m_pCurMap;
    if ( !CreateItemBox(&v15, pOwner, 0xFFFFFFFF, 0, 0i64, 2, pMap, wLayerIndex, pOwner->m_fCurPos, 1) )
      return 1;
  }
  return 1;
}
