/*
 * Function: _std::vector_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64___::_Ucopy_std::_Vector_const_iterator_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64______::_1_::dtor$0
 * Address: 0x140381CE0
 */

void __fastcall std::vector_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64___::_Ucopy_std::_Vector_const_iterator_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64______::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(*(std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > **)(a2 + 176));
}
