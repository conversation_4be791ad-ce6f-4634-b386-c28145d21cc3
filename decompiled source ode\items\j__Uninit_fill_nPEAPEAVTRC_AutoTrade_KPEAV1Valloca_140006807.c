/*
 * Function: j_??$_Uninit_fill_n@PEAPEAVTRC_AutoTrade@@_KPEAV1@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@YAXPEAPEAVTRC_AutoTrade@@_KAEBQEAV1@AEAV?$allocator@PEAVTRC_AutoTrade@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140006807
 */

void __fastcall std::_Uninit_fill_n<TRC_AutoTrade * *,unsigned __int64,TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(TRC_AutoTrade **_First, unsigned __int64 _Count, TRC_AutoTrade *const *_Val, std::allocator<TRC_AutoTrade *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<TRC_AutoTrade * *,unsigned __int64,TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(
    _First,
    _Count,
    _Val,
    __formal,
    a5,
    a6);
}
