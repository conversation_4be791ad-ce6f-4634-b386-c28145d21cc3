/*
 * Function: ?SendMsg_TicketCheck@CTransportShip@@QEAAXH_NG@Z
 * Address: 0x140265330
 */

void __fastcall CTransportShip::SendMsg_TicketCheck(CTransportShip *this, int n, bool bPass, unsigned __int16 wTicketSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  unsigned __int16 v8; // [sp+35h] [bp-43h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v10; // [sp+55h] [bp-23h]@4

  v4 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = bPass;
  v8 = wTicketSerial;
  pbyType = 33;
  v10 = 3;
  CNetProcess::LoadSendMsg(unk_1414F2088, n, &pbyType, &szMsg, 3u);
}
