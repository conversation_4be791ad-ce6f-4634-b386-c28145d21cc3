/*
 * Function: ?Get@CGuildBattleSchedulePool@GUILD_BATTLE@@QEAAPEAVCGuildBattleSchedule@2@K@Z
 * Address: 0x1403DAA30
 */

GUILD_BATTLE::CGuildBattleSchedule *__fastcall GUILD_BATTLE::CGuildBattleSchedulePool::Get(GUILD_BATTLE::CGuildBattleSchedulePool *this, unsigned int dwSID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleSchedule *result; // rax@6
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleSchedulePool *v6; // [sp+30h] [bp+8h]@1
  unsigned int v7; // [sp+38h] [bp+10h]@1

  v7 = dwSID;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v6->m_ppkSchedule && v6->m_dwMaxScheduleCnt > dwSID )
  {
    if ( v6->m_ppkSchedule[dwSID] )
    {
      if ( GUILD_BATTLE::CGuildBattleSchedule::IsEmpty(v6->m_ppkSchedule[dwSID])
        || GUILD_BATTLE::CGuildBattleSchedule::IsDone(v6->m_ppkSchedule[v7]) )
      {
        GUILD_BATTLE::CGuildBattleSchedule::Clear(v6->m_ppkSchedule[v7]);
        result = v6->m_ppkSchedule[v7];
      }
      else
      {
        result = 0i64;
      }
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
