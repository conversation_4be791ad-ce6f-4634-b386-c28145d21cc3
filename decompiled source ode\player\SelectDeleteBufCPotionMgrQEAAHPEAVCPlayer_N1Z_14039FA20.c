/*
 * Function: ?SelectDeleteBuf@CPotionMgr@@QEAAHPEAVCPlayer@@_N1@Z
 * Address: 0x14039FA20
 */

__int64 __fastcall CPotionMgr::SelectDeleteBuf(CPotionMgr *this, CPlayer *pOne, bool bUse, bool bRemove)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  int v8; // [sp+20h] [bp-28h]@4
  unsigned int v9; // [sp+24h] [bp-24h]@4
  unsigned int j; // [sp+28h] [bp-20h]@4
  unsigned int v11; // [sp+2Ch] [bp-1Ch]@16
  unsigned int k; // [sp+30h] [bp-18h]@16
  CPotionMgr *v13; // [sp+50h] [bp+8h]@1
  CPlayer *pApplyPlayer; // [sp+58h] [bp+10h]@1
  bool v15; // [sp+60h] [bp+18h]@1
  bool v16; // [sp+68h] [bp+20h]@1

  v16 = bRemove;
  v15 = bUse;
  pApplyPlayer = pOne;
  v13 = this;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = 0;
  v9 = 0;
  for ( j = 0; (signed int)j < 2; ++j )
  {
    if ( v15 )
    {
      if ( _ContPotionData::IsLive((_ContPotionData *)&pApplyPlayer->m_PotionParam + (signed int)j) )
        ++v8;
      else
        v9 = j;
    }
    else
    {
      ++v8;
      v9 = j;
    }
  }
  if ( v8 )
  {
    if ( v8 == 2 )
    {
      v11 = -1;
      for ( k = 0; (signed int)k < 2; ++k )
      {
        if ( pApplyPlayer->m_PotionParam.m_ContCommonPotionData[k].m_dwStartSec < v11 )
        {
          v11 = pApplyPlayer->m_PotionParam.m_ContCommonPotionData[k].m_dwStartSec;
          v9 = k;
        }
      }
    }
  }
  else
  {
    v9 = 0;
  }
  if ( v16 )
    CPotionMgr::RemovePotionContEffect(
      v13,
      pApplyPlayer,
      (_ContPotionData *)&pApplyPlayer->m_PotionParam + (signed int)v9);
  return v9;
}
