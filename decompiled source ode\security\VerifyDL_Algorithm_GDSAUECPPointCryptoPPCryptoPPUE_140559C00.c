/*
 * Function: ?Verify@?$DL_Algorithm_GDSA@UECPPoint@CryptoPP@@@CryptoPP@@UEBA_NAEBV?$DL_GroupParameters@UECPPoint@CryptoPP@@@2@AEBV?$DL_PublicKey@UECPPoint@CryptoPP@@@2@AEBVInteger@2@22@Z
 * Address: 0x140559C00
 */

char __fastcall CryptoPP::DL_Algorithm_GDSA<CryptoPP::ECPPoint>::Verify(CryptoPP::Integer *a, CryptoPP::Integer *a2, __int64 a3, CryptoPP::Integer *a4, CryptoPP::Integer *aa, CryptoPP::Integer *a6)
{
  CryptoPP::Integer *v6; // rax@1
  char v7; // al@12
  CryptoPP::Integer *v8; // rax@13
  CryptoPP::Integer *v9; // rax@13
  __int64 v10; // rax@13
  CryptoPP::Integer *v11; // rax@13
  CryptoPP::Integer *v12; // rax@13
  CryptoPP::Integer v13; // [sp+20h] [bp-248h]@13
  CryptoPP::Integer *v14; // [sp+48h] [bp-220h]@1
  CryptoPP::Integer v15; // [sp+50h] [bp-218h]@13
  CryptoPP::Integer v16; // [sp+78h] [bp-1F0h]@13
  char v17; // [sp+A0h] [bp-1C8h]@7
  CryptoPP::Integer b; // [sp+A8h] [bp-1C0h]@2
  CryptoPP::Integer v19; // [sp+D0h] [bp-198h]@4
  CryptoPP::Integer result; // [sp+F8h] [bp-170h]@13
  CryptoPP::Integer v21; // [sp+120h] [bp-148h]@13
  bool v22; // [sp+148h] [bp-120h]@13
  CryptoPP::ECPPoint v23; // [sp+150h] [bp-118h]@13
  CryptoPP::Integer v24; // [sp+1A8h] [bp-C0h]@13
  CryptoPP::Integer v25; // [sp+1D0h] [bp-98h]@13
  int v26; // [sp+1F8h] [bp-70h]@1
  __int64 v27; // [sp+200h] [bp-68h]@1
  int v28; // [sp+208h] [bp-60h]@5
  CryptoPP::Integer *v29; // [sp+210h] [bp-58h]@13
  CryptoPP::Integer *v30; // [sp+218h] [bp-50h]@13
  CryptoPP::Integer *v31; // [sp+220h] [bp-48h]@13
  CryptoPP::Integer *v32; // [sp+228h] [bp-40h]@13
  __int64 v33; // [sp+230h] [bp-38h]@13
  __int64 v34; // [sp+238h] [bp-30h]@13
  CryptoPP::Integer *v35; // [sp+240h] [bp-28h]@13
  CryptoPP::Integer *v36; // [sp+248h] [bp-20h]@13
  CryptoPP::Integer *v37; // [sp+250h] [bp-18h]@13
  CryptoPP::Integer *v38; // [sp+258h] [bp-10h]@13
  CryptoPP::Integer *v39; // [sp+278h] [bp+10h]@1
  __int64 v40; // [sp+280h] [bp+18h]@1
  CryptoPP::Integer *v41; // [sp+288h] [bp+20h]@1

  v41 = a4;
  v40 = a3;
  v39 = a2;
  v27 = -2i64;
  v26 = 0;
  LODWORD(v6) = ((int (__fastcall *)(CryptoPP::Integer *))a2->vfptr[2].__vecDelDtor)(a2);
  v14 = v6;
  v28 = CryptoPP::operator>=(aa, v6)
     || (CryptoPP::Integer::Integer(&b, 1), v26 |= 1u, CryptoPP::operator<(aa, &b))
     || CryptoPP::operator>=(a6, v14)
     || (CryptoPP::Integer::Integer(&v19, 1), v26 |= 2u, CryptoPP::operator<(a6, &v19));
  v17 = v28;
  if ( v26 & 2 )
  {
    v26 &= 0xFFFFFFFD;
    CryptoPP::Integer::~Integer(&v19);
  }
  if ( v26 & 1 )
  {
    v26 &= 0xFFFFFFFE;
    CryptoPP::Integer::~Integer(&b);
  }
  if ( v17 )
  {
    v7 = 0;
  }
  else
  {
    CryptoPP::Integer::InverseMod(a6, &v13, v14);
    v8 = CryptoPP::operator*(&result, v41, &v13);
    v29 = v8;
    v30 = v8;
    CryptoPP::operator%(&v16, v8, v14);
    CryptoPP::Integer::~Integer(&result);
    v9 = CryptoPP::operator*(&v21, aa, &v13);
    v31 = v9;
    v32 = v9;
    CryptoPP::operator%(&v15, v9, v14);
    CryptoPP::Integer::~Integer(&v21);
    LODWORD(v10) = (*(int (__fastcall **)(__int64, CryptoPP::ECPPoint *, CryptoPP::Integer *, CryptoPP::Integer *))(*(_QWORD *)v40 + 40i64))(
                     v40,
                     &v23,
                     &v16,
                     &v15);
    v33 = v10;
    v34 = v10;
    LODWORD(v11) = ((int (__fastcall *)(CryptoPP::Integer *, CryptoPP::Integer *, __int64))v39->vfptr[3].BEREncode)(
                     v39,
                     &v24,
                     v10);
    v35 = v11;
    v36 = v11;
    v12 = CryptoPP::operator%(&v25, v11, v14);
    v37 = v12;
    v38 = v12;
    v22 = CryptoPP::operator==(aa, v12);
    CryptoPP::Integer::~Integer(&v25);
    CryptoPP::Integer::~Integer(&v24);
    CryptoPP::ECPPoint::~ECPPoint(&v23);
    CryptoPP::Integer::~Integer(&v15);
    CryptoPP::Integer::~Integer(&v16);
    CryptoPP::Integer::~Integer(&v13);
    v7 = v22;
  }
  return v7;
}
