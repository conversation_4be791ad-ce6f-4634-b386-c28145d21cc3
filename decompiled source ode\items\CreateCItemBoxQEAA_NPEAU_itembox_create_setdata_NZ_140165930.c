/*
 * Function: ?Create@CItemBox@@QEAA_NPEAU_itembox_create_setdata@@_N@Z
 * Address: 0x140165930
 */

char __fastcall CItemBox::Create(CItemBox *this, _itembox_create_setdata *pParam, bool bHide)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@15
  char result; // al@33
  __int64 v7; // [sp+0h] [bp-28h]@1
  CItemBox *v8; // [sp+30h] [bp+8h]@1
  _itembox_create_setdata *pData; // [sp+38h] [bp+10h]@1
  bool v10; // [sp+40h] [bp+18h]@1

  v10 = bHide;
  pData = pParam;
  v8 = this;
  v3 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( CGameObject::Create((CGameObject *)&v8->vfptr, (_object_create_setdata *)&pParam->m_pRecordSet) )
  {
    v8->m_dwObjSerial = CItemBox::s_dwSerialCounter++;
    v8->m_bHide = v10;
    v8->m_bCompDgr = 0;
    v8->m_dwPartyBossSerial = pData->dwPartyBossSerial;
    v8->m_byEventLootAuth = pData->byEventItemLootAuth;
    v8->m_bHolyScanner = pData->bHolyScanner;
    if ( pData->pAttacker )
    {
      v8->m_byEventRaceCode = CPlayerDB::GetRaceCode(&pData->pAttacker->m_Param);
      v8->m_dwEventPartyBoss = CPartyPlayer::IsPartyMode(pData->pAttacker->m_pPartyMgr) ? pData->pAttacker->m_pPartyMgr->m_pPartyBoss->m_id.dwSerial : CPlayerDB::GetCharSerial(&pData->pAttacker->m_Param);
      if ( pData->pAttacker->m_Param.m_pGuild )
        v8->m_dwEventGuildSerial = pData->pAttacker->m_Param.m_pGuild->m_dwSerial;
    }
    if ( pData->pOwner )
    {
      v8->m_dwOwnerSerial = pData->pOwner->m_dwObjSerial;
      v8->m_wOwnerIndex = pData->pOwner->m_ObjID.m_wIndex;
      v8->m_bPartyShare = pData->bParty;
    }
    if ( pData->pThrower )
    {
      v8->m_dwThrowerSerial = pData->pThrower->m_dwObjSerial;
      v8->m_wThrowerIndex = pData->pThrower->m_ObjID.m_wIndex;
      v8->m_byThrowerID = pData->pThrower->m_ObjID.m_byID;
      v8->m_byThrowerRaceCode = -1;
      v8->m_bCompDgr = 0;
      if ( !pData->pThrower->m_ObjID.m_byID )
      {
        v5 = CPlayerDB::GetCharNameW((CPlayerDB *)&pData->pThrower[1].m_fOldPos[2]);
        strcpy_0(v8->m_wszThrowerName, v5);
        W2M(v8->m_wszThrowerName, v8->m_aszThrowerName, 0x11u);
        v8->m_dwThrowerCharSerial = CPlayerDB::GetCharSerial((CPlayerDB *)&pData->pThrower[1].m_fOldPos[2]);
        strcpy_0(v8->m_szThrowerID, (const char *)(*(_QWORD *)&pData->pThrower[1].m_nScreenPos[0] + 32i64));
        v8->m_byThrowerRaceCode = CPlayerDB::GetRaceCode((CPlayerDB *)&pData->pThrower[1].m_fOldPos[2]);
        v8->m_byThrowerDegree = BYTE4(pData->pThrower[1].vfptr);
        strcpy_0(v8->m_szThrowerItemHistoryFileName, (const char *)pData->pThrower[27].m_nScreenPos);
      }
      if ( pData->pThrower->m_ObjID.m_byID == 1 )
      {
        if ( pData->pOwner && pData->pOwner->m_byUserDgr )
          v8->m_bCompDgr = 1;
        v8->m_wMonRecIndex = **(_WORD **)&pData->pThrower[1].m_fAbsPos[1];
        v8->m_bBossMob = *(_DWORD *)(*(_QWORD *)&pData->pThrower[1].m_fAbsPos[1] + 272i64) == 1;
      }
    }
    v8->m_byCreateCode = pData->byCreateCode;
    memcpy_0(&v8->m_Item, &pData->Item, 0x32ui64);
    v8->m_dwLootStartTime = GetLoopTime();
    if ( v8->m_byCreateCode == 1 )
    {
      v8->m_nStateCode = 1;
      v8->m_dwLootStartTime += 20000;
    }
    else if ( v8->m_byCreateCode != 2 && v8->m_byCreateCode != 3 )
    {
      if ( v8->m_byCreateCode == 4 )
      {
        v8->m_nStateCode = 0;
        v8->m_dwLootStartTime += 60000;
      }
      else if ( v8->m_byCreateCode == 6 )
      {
        v8->m_nStateCode = 0;
        v8->m_dwLootStartTime += 60000;
      }
      else
      {
        v8->m_nStateCode = 0;
      }
    }
    else
    {
      v8->m_nStateCode = 0;
      v8->m_dwLootStartTime += 40000;
    }
    ++CItemBox::s_nLiveNum;
    CItemBox::SendMsg_Create(v8);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
