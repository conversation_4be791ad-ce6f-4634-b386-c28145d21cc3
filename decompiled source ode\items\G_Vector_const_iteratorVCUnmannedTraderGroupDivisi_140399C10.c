/*
 * Function: ??G?$_Vector_const_iterator@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@QEBA_JAEBV01@@Z
 * Address: 0x140399C10
 */

__int64 __fastcall std::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::operator-(std::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *this, std::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *_Right)
{
  __int128 v2; // ax@1
  __int128 v4; // tt@1

  v2 = (char *)this->_Myptr - (char *)_Right->_Myptr;
  *(_QWORD *)&v4 = v2;
  *((_QWORD *)&v4 + 1) = *((_QWORD *)&v2 + 1);
  return v4 / 48;
}
