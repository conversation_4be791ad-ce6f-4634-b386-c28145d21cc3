/*
 * Function: ?LimLvNpcQuestDelete@CPlayer@@QEAAXE@Z
 * Address: 0x1400CB810
 */

void __fastcall CPlayer::LimLvNpcQuestDelete(CPlayer *this, char byQuestDBSlot)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1
  char v6; // [sp+38h] [bp+10h]@1

  v6 = byQuestDBSlot;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CPlayer::SendMsg_QuestFailure(v5, 6, byQuestDBSlot);
  CQuestMgr::DeleteQuestData(&v5->m_QuestMgr, v6);
  CUserDB::Update_QuestDelete(v5->m_pUserDB, v6);
}
