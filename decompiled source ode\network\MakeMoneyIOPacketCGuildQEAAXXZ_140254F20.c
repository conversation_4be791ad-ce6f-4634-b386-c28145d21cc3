/*
 * Function: ?MakeMoneyIOPacket@CGuild@@QEAAXXZ
 * Address: 0x140254F20
 */

void __fastcall CGuild::MakeMoneyIOPacket(CGuild *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-68h]@1
  int v4; // [sp+20h] [bp-48h]@4
  int v5; // [sp+24h] [bp-44h]@4
  void *Dst; // [sp+28h] [bp-40h]@4
  int j; // [sp+30h] [bp-38h]@4
  char *Str; // [sp+38h] [bp-30h]@6
  unsigned __int8 Src; // [sp+44h] [bp-24h]@6
  CGuild *v10; // [sp+70h] [bp+8h]@1

  v10 = this;
  v1 = &v3;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10->m_MoneyIO_List->wDataSize = 0;
  v4 = 0;
  v5 = 0;
  Dst = v10->m_MoneyIO_List->sData;
  memcpy_0(Dst, &v10->m_nIOMoneyHistoryNum, 1ui64);
  Dst = (char *)Dst + 1;
  ++v5;
  v4 = v10->m_nIOMoneyHistoryNum;
  for ( j = 0; j < v10->m_nIOMoneyHistoryNum; ++j )
  {
    Str = v10->m_IOMoneyHistory[(signed __int64)--v4].wszIOerName;
    Src = strlen_0(Str);
    memcpy_0(Dst, &Src, 1ui64);
    Dst = (char *)Dst + 1;
    ++v5;
    memcpy_0(Dst, Str, Src);
    Dst = (char *)Dst + Src;
    v5 += Src;
    memcpy_0(Dst, Str + 24, 8ui64);
    Dst = (char *)Dst + 8;
    v5 += 8;
    memcpy_0(Dst, Str + 32, 8ui64);
    Dst = (char *)Dst + 8;
    v5 += 8;
    memcpy_0(Dst, Str + 40, 8ui64);
    Dst = (char *)Dst + 8;
    v5 += 8;
    memcpy_0(Dst, Str + 48, 8ui64);
    Dst = (char *)Dst + 8;
    v5 += 8;
    memcpy_0(Dst, Str + 56, 4ui64);
    Dst = (char *)Dst + 4;
    v5 += 4;
  }
  v10->m_MoneyIO_List->wDataSize = v5;
}
