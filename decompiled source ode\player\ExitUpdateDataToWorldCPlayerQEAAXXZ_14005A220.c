/*
 * Function: ?ExitUpdateDataToWorld@CPlayer@@QEAAXXZ
 * Address: 0x14005A220
 */

void __usercall CPlayer::ExitUpdateDataToWorld(CPlayer *this@<rcx>, long double a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float *v4; // rax@6
  __int64 v5; // [sp+0h] [bp-98h]@1
  int j; // [sp+20h] [bp-78h]@6
  _EXIT_ALTER_PARAM pCon; // [sp+38h] [bp-60h]@6
  _UNIT_DB_BASE::_LIST *pData; // [sp+78h] [bp-20h]@10
  char v9; // [sp+80h] [bp-18h]@12
  int k; // [sp+84h] [bp-14h]@12
  int l; // [sp+88h] [bp-10h]@14
  CPlayer *v12; // [sp+A0h] [bp+8h]@1

  v12 = this;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v12->m_pUserDB && !v12->m_pUserDB->m_bNoneUpdateData )
  {
    j = 0;
    pCon.dwHP = CPlayerDB::GetHP(&v12->m_Param);
    pCon.dwFP = CPlayerDB::GetFP(&v12->m_Param);
    pCon.dwSP = CPlayerDB::GetSP(&v12->m_Param);
    pCon.dwDP = CPlayerDB::GetDP(&v12->m_Param);
    CPlayerDB::GetExp(&v12->m_Param);
    pCon.dExp = a2;
    pCon.byMapCode = CPlayerDB::GetMapCode(&v12->m_Param);
    v4 = CPlayerDB::GetCurPos(&v12->m_Param);
    memcpy_0(pCon.fStartPos, v4, 0xCui64);
    pCon.dwDalant = CPlayerDB::GetDalant(&v12->m_Param);
    pCon.dwGold = CPlayerDB::GetGold(&v12->m_Param);
    CUserDB::Update_Param(v12->m_pUserDB, &pCon);
    if ( !CPlayerDB::GetRaceCode(&v12->m_Param) )
    {
      for ( j = 0; j < 4; ++j )
      {
        pData = &v12->m_Param.m_UnitDB.m_List[j];
        if ( v12->m_Param.m_UnitDB.m_List[j].byFrame != 255 )
          CUserDB::Update_UnitData(v12->m_pUserDB, j, pData);
      }
    }
    v9 = 0;
    for ( k = 0; k < 2; ++k )
    {
      for ( l = 0; l < 8; ++l )
      {
        if ( v12->m_SFCont[k][l].m_bExist )
        {
          v9 = 0;
          for ( j = 0; j < 8; ++j )
          {
            if ( v12->m_SFCont[k][j].m_bExist
              && l != j
              && v12->m_SFCont[k][l].m_dwStartSec < v12->m_SFCont[k][j].m_dwStartSec )
            {
              ++v9;
            }
          }
          _SFCONT_DB_BASE::_LIST::SetOrder(
            (_SFCONT_DB_BASE::_LIST *)&v12->m_pUserDB->m_AvatorData.dbSfcont + 8 * k + l,
            v9);
        }
      }
    }
  }
}
