/*
 * Function: j_??0?$_List_val@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@2@@std@@QEAA@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@1@@Z
 * Address: 0x1400036CA
 */

void __fastcall std::_List_val<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_List_val<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>(std::_List_val<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *this, std::allocator<std::pair<int const ,_TimeItem_fld const *> > _Al)
{
  std::_List_val<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_List_val<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>(
    this,
    _Al);
}
