/*
 * Function: ?VerifyHash@CCryptor@@QEAA_NPEBE_KPEAE1@Z
 * Address: 0x14046B780
 */

bool __fastcall CCryptor::VerifyHash(CCryptor *this, const char *pBuff, unsigned __int64 tBufSize, char *pHash, unsigned __int64 tHashSize)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v8; // [sp+0h] [bp-28h]@1
  CCryptor *v9; // [sp+30h] [bp+8h]@1

  v9 = this;
  v5 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( tHashSize == 32 )
    result = (unsigned __int8)((int (__fastcall *)(CryptoPP::SHA256 *, char *, const char *, unsigned __int64))v9->m_pHash->vfptr[6].Clone)(
                                v9->m_pHash,
                                pHash,
                                pBuff,
                                tBufSize) != 0;
  else
    result = 0;
  return result;
}
