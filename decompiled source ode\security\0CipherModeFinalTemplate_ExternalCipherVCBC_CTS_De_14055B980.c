/*
 * Function: ??0?$CipherModeFinalTemplate_ExternalCipher@VCBC_CTS_Decryption@CryptoPP@@@CryptoPP@@QEAA@AEAV?$SimpleKeyedTransformation@VBlockTransformation@CryptoPP@@@1@PEBEH@Z
 * Address: 0x14055B980
 */

CryptoPP::CBC_CTS_Decryption *__fastcall CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Decryption>::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Decryption>(CryptoPP::CBC_CTS_Decryption *a1, __int64 a2, __int64 a3, unsigned int a4)
{
  CryptoPP::CBC_CTS_Decryption *v5; // [sp+40h] [bp+8h]@1
  __int64 v6; // [sp+48h] [bp+10h]@1
  __int64 v7; // [sp+50h] [bp+18h]@1
  unsigned int v8; // [sp+58h] [bp+20h]@1

  v8 = a4;
  v7 = a3;
  v6 = a2;
  v5 = a1;
  CryptoPP::CBC_CTS_Decryption::CBC_CTS_Decryption(a1);
  v5->vfptr = (CryptoPP::ClonableVtbl *)&CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Decryption>::`vftable'{for `CryptoPP::StreamTransformation'};
  v5->vfptr = (CryptoPP::SimpleKeyingInterfaceVtbl *)&CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Decryption>::`vftable'{for `CryptoPP::SimpleKeyingInterface'};
  CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Decryption>::SetCipherWithIV(v5, v6, v7, v8);
  return v5;
}
