/*
 * Function: ??1?$_Const_iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@QEAA@XZ
 * Address: 0x1403C11F0
 */

void __fastcall std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Const_iterator<0>::~_Const_iterator<0>(std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Const_iterator<0> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Const_iterator<0> *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::_Bidit<std::pair<int const,CAsyncLogInfo *>,__int64,std::pair<int const,CAsyncLogInfo *> const *,std::pair<int const,CAsyncLogInfo *> const &>::~_Bidit<std::pair<int const,CAsyncLogInfo *>,__int64,std::pair<int const,CAsyncLogInfo *> const *,std::pair<int const,CAsyncLogInfo *> const &>((std::_Bidit<std::pair<int const ,CAsyncLogInfo *>,__int64,std::pair<int const ,CAsyncLogInfo *> const *,std::pair<int const ,CAsyncLogInfo *> const &> *)&v4->_Mycont);
}
