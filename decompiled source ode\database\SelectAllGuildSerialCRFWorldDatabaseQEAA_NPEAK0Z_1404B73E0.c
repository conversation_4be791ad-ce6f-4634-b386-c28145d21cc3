/*
 * Function: ?SelectAllGuildSerial@CRFWorldDatabase@@QEAA_NPEAK0@Z
 * Address: 0x1404B73E0
 */

char __usercall CRFWorldDatabase::SelectAllGuildSerial@<al>(CRFWorldDatabase *this@<rcx>, unsigned int *pdwCount@<rdx>, unsigned int *pdwSerial@<r8>, signed __int64 a4@<rax>)
{
  void *v4; // rsp@1
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  unsigned int *v8; // rax@16
  __int64 v9; // [sp-20h] [bp-2888h]@1
  void *SQLStmt; // [sp+0h] [bp-2868h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+8h] [bp-2860h]@16
  SQLLEN v12; // [sp+18h] [bp-2850h]@16
  __int16 v13; // [sp+24h] [bp-2844h]@9
  char Dest; // [sp+40h] [bp-2828h]@4
  unsigned __int64 v15; // [sp+2850h] [bp-18h]@4
  CRFWorldDatabase *v16; // [sp+2870h] [bp+8h]@1
  unsigned int *v17; // [sp+2878h] [bp+10h]@1
  unsigned int *v18; // [sp+2880h] [bp+18h]@1

  v18 = pdwSerial;
  v17 = pdwCount;
  v16 = this;
  v4 = alloca(a4);
  v5 = &v9;
  for ( i = 2592i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v15 = (unsigned __int64)&v9 ^ _security_cookie;
  *pdwCount = 0;
  sprintf(&Dest, "select top %u [Serial] from [dbo].[tbl_guild] where [DCK]=0 order by serial", 500i64);
  if ( v16->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v16->vfptr, &Dest);
  if ( v16->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v16->vfptr) )
  {
    v13 = SQLExecDirectA_0(v16->m_hStmtSelect, &Dest, -3);
    if ( v13 && v13 != 1 )
    {
      if ( v13 == 100 )
      {
        result = 1;
      }
      else
      {
        SQLStmt = v16->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v13, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v13, v16->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      while ( 1 )
      {
        v13 = SQLFetch_0(v16->m_hStmtSelect);
        if ( v13 )
        {
          if ( v13 != 1 )
            break;
        }
        v8 = &v18[*v17];
        StrLen_or_IndPtr = &v12;
        SQLStmt = 0i64;
        v13 = SQLGetData_0(v16->m_hStmtSelect, 1u, 4, v8, 0i64, &v12);
        ++*v17;
      }
      if ( v16->m_hStmtSelect )
        SQLCloseCursor_0(v16->m_hStmtSelect);
      if ( v16->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v16->vfptr, "%s Success", &Dest);
      result = 1;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v16->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
