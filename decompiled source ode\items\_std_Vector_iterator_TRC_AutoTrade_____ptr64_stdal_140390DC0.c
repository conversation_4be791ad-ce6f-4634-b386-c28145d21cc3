/*
 * Function: _std::_Vector_iterator_TRC_AutoTrade_____ptr64_std::allocator_TRC_AutoTrade_____ptr64___::operator__::_1_::dtor$0_0
 * Address: 0x140390DC0
 */

void __fastcall std::_Vector_iterator_TRC_AutoTrade_____ptr64_std::allocator_TRC_AutoTrade_____ptr64___::operator__::_1_::dtor_0_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::~_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>((std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *)(a2 + 40));
}
