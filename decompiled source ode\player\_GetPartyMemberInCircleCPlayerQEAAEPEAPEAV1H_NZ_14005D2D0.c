/*
 * Function: ?_GetPartyMemberInCircle@CPlayer@@QEAAEPEAPEAV1@H_N@Z
 * Address: 0x14005D2D0
 */

unsigned __int8 __fastcall CPlayer::_GetPartyMemberInCircle(CPlayer *this, CPlayer **out_ppMember, int nMax, bool bOne)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int8 result; // al@5
  float v7; // xmm0_4@18
  float v8; // xmm0_4@19
  float v9; // xmm0_4@20
  __int64 v10; // [sp+0h] [bp-48h]@1
  CPartyPlayer **v11; // [sp+20h] [bp-28h]@4
  unsigned __int8 v12; // [sp+28h] [bp-20h]@6
  int j; // [sp+2Ch] [bp-1Ch]@6
  CPlayer *v14; // [sp+30h] [bp-18h]@10
  CPlayer *v15; // [sp+50h] [bp+8h]@1
  CPlayer **v16; // [sp+58h] [bp+10h]@1
  bool v17; // [sp+68h] [bp+20h]@1

  v17 = bOne;
  v16 = out_ppMember;
  v15 = this;
  v4 = &v10;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = CPartyPlayer::GetPtrPartyMember(v15->m_pPartyMgr);
  if ( v11 )
  {
    v12 = 0;
    for ( j = 0; j < 8 && v11[j]; ++j )
    {
      v14 = &g_Player + v11[j]->m_wZoneIndex;
      if ( v17 || v14 != v15 )
      {
        if ( v14->m_bLive )
        {
          if ( !v14->m_bCorpse && v14->m_pCurMap == v15->m_pCurMap && v14->m_wMapLayerIndex == v15->m_wMapLayerIndex )
          {
            v7 = v14->m_fCurPos[0] - v15->m_fCurPos[0];
            abs(v7);
            if ( v7 <= 540.0 )
            {
              v8 = v14->m_fCurPos[2] - v15->m_fCurPos[2];
              abs(v8);
              if ( v8 <= 540.0 )
              {
                v9 = v14->m_fCurPos[1] - v15->m_fCurPos[1];
                abs(v9);
                if ( v9 <= 100.0 )
                  v16[v12++] = v14;
              }
            }
          }
        }
      }
    }
    result = v12;
  }
  else
  {
    result = 0;
  }
  return result;
}
