/*
 * Function: ?CheckwIndexAndType@CUnmannedTraderUserInfoTable@@AEAA_NGEPEBD@Z
 * Address: 0x1401D4660
 */

bool __fastcall CUnmannedTraderUserInfoTable::CheckwIndexAndType(CUnmannedTraderUserInfoTable *this, unsigned __int16 wInx, char byType, const char *szCallFuncName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  CUnmannedTraderUserInfoTable *v8; // [sp+30h] [bp+8h]@1
  unsigned __int16 v9; // [sp+38h] [bp+10h]@1
  char v10; // [sp+40h] [bp+18h]@1

  v10 = byType;
  v9 = wInx;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return !std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::empty(&v8->m_veckInfo)
      && std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::size(&v8->m_veckInfo) > v9
      && (signed int)(unsigned __int8)v10 < 2;
}
