/*
 * Function: ?dtor$3@?0??OnInitControl@CMFCLinkCtrl@@IEAA_J_K_J@Z@4HA_0
 * Address: 0x1405A9FF0
 */

// Microsoft VisualC v7/11 64bit runtime
int `CMFCLinkCtrl::OnInitControl'::`1'::dtor$3()
{
  return std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
}
