/*
 * Function: ?SubDalant@CPlayer@@QEAAXK@Z
 * Address: 0x140055C40
 */

void __fastcall CPlayer::SubDalant(CPlayer *this, unsigned int dwSub)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@6
  unsigned int v5; // eax@7
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int dwDt; // [sp+20h] [bp-18h]@4
  unsigned int gold; // [sp+24h] [bp-14h]@7
  CPlayer *v9; // [sp+40h] [bp+8h]@1
  unsigned int v10; // [sp+48h] [bp+10h]@1

  v10 = dwSub;
  v9 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  dwDt = CPlayerDB::GetDalant(&v9->m_Param) - dwSub;
  if ( v10 > CPlayerDB::GetDalant(&v9->m_Param) )
    dwDt = 0;
  v4 = CPlayerDB::GetDalant(&v9->m_Param);
  if ( dwDt != v4 )
  {
    CPlayerDB::SetDalant(&v9->m_Param, dwDt);
    gold = CPlayerDB::GetGold(&v9->m_Param);
    v5 = CPlayerDB::GetDalant(&v9->m_Param);
    CUserDB::Update_Money(v9->m_pUserDB, v5, gold);
  }
}
