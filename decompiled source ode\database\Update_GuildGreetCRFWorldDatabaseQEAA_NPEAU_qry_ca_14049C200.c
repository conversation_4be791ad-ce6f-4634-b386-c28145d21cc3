/*
 * Function: ?Update_GuildGreet@CRFWorldDatabase@@QEAA_NPEAU_qry_case_guild_greetingmsg@@@Z
 * Address: 0x14049C200
 */

bool __fastcall CRFWorldDatabase::Update_GuildGreet(CRFWorldDatabase *this, _qry_case_guild_greetingmsg *pSheet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-458h]@1
  char Dest; // [sp+30h] [bp-428h]@4
  char v7; // [sp+31h] [bp-427h]@4
  unsigned __int64 v8; // [sp+440h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+460h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 276i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = (unsigned __int64)&v5 ^ _security_cookie;
  Dest = 0;
  memset(&v7, 0, 0x3FFui64);
  sprintf(&Dest, "update tbl_guild set GMsg='%s' where Serial=%d", pSheet->in_guildgreetingmsg, pSheet->in_guildserial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &Dest, 1);
}
