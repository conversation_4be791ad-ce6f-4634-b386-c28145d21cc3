/*
 * Function: j_?IsUsableCoupon@CashItemRemoteStore@@QEAA_NPEAU_request_csi_buy_clzo@@U_STORAGE_POS_INDIV@@PEAVCPlayer@@PEA_N@Z
 * Address: 0x140005D80
 */

bool __fastcall CashItemRemoteStore::IsUsableCoupon(CashItemRemoteStore *this, _request_csi_buy_clzo *pBuyList, _STORAGE_POS_INDIV pCoupon, CPlayer *pOne, bool *bCheck)
{
  return CashItemRemoteStore::IsUsableCoupon(this, pBuy<PERSON>ist, pCoupon, pOne, bCheck);
}
