/*
 * Function: _CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint_::_DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__::_1_::dtor$0
 * Address: 0x140449230
 */

void __fastcall CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint_::_DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  CryptoPP::ECPPoint::~ECPPoint((CryptoPP::ECPPoint *)(*(_QWORD *)(a2 + 64) + 8i64));
}
