/*
 * Function: ?ct_animus_recall_term@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140291040
 */

bool __fastcall ct_animus_recall_term(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  bool v5; // [sp+20h] [bp-18h]@7
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      v5 = atoi(s_pwszDstCheat[0]) == 0;
      result = CPlayer::dev_animus_recall_time_free(v6, v5);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
