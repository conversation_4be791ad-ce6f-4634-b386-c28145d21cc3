/*
 * Function: ?GetNextMessage@MessageQueue@CryptoPP@@UEAA_NXZ
 * Address: 0x140654540
 */

char __fastcall CryptoPP::MessageQueue::GetNextMessage(CryptoPP::MessageQueue *this)
{
  _DWORD *v1; // rax@3
  unsigned __int64 v2; // rax@4
  char result; // al@6
  CryptoPP::MessageQueue *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  if ( !(*(int (**)(void))(*(_QWORD *)this + 184i64))()
    || (unsigned __int8)(*(int (__fastcall **)(CryptoPP::MessageQueue *))(*(_QWORD *)v4 + 128i64))(v4) )
  {
    result = 0;
  }
  else
  {
    std::deque<unsigned __int64,std::allocator<unsigned __int64>>::pop_front((char *)v4 + 112);
    LODWORD(v1) = std::deque<unsigned int,std::allocator<unsigned int>>::operator[]((char *)v4 + 168, 0i64);
    if ( !*v1 )
    {
      LODWORD(v2) = std::deque<unsigned int,std::allocator<unsigned int>>::size((char *)v4 + 168);
      if ( v2 > 1 )
        std::deque<unsigned int,std::allocator<unsigned int>>::pop_front((char *)v4 + 168);
    }
    result = 1;
  }
  return result;
}
