/*
 * Function: j_??$_Iter_random@PEAPEAVTRC_AutoTrade@@PEAPEAV1@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVTRC_AutoTrade@@0@Z
 * Address: 0x140008305
 */

std::random_access_iterator_tag __fastcall std::_Iter_random<TRC_AutoTrade * *,TRC_AutoTrade * *>(TRC_AutoTrade **const *__formal, TRC_AutoTrade **const *a2)
{
  return std::_Iter_random<TRC_AutoTrade * *,TRC_AutoTrade * *>(__formal, a2);
}
