/*
 * Function: ?dtor$0@?0??AddRunnableContext@ScheduleGroupSegmentBase@details@Concurrency@@IEAAXPEAVInternalContextBase@23@Vlocation@3@@Z@4HA_6
 * Address: 0x140658580
 */

int __fastcall `Concurrency::details::ScheduleGroupSegmentBase::AddRunnableContext'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  __int64 v2; // rcx@1

  v2 = *(_QWORD *)(a2 + 192);
  return std::_Deque_iterator<unsigned __int64,std::allocator<unsigned __int64>,0>::~_Deque_iterator<unsigned __int64,std::allocator<unsigned __int64>,0>();
}
