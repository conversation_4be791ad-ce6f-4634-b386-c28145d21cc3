/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAH_KHV?$allocator@H@std@@@stdext@@YAXPEAH_KAEBHAEAV?$allocator@H@std@@@Z
 * Address: 0x140011482
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<int *,unsigned __int64,int,std::allocator<int>>(int *_First, unsigned __int64 _Count, const int *_Val, std::allocator<int> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<int *,unsigned __int64,int,std::allocator<int>>(_First, _Count, _Val, _Al);
}
