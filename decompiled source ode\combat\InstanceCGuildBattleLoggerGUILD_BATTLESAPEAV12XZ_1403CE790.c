/*
 * Function: ?Instance@CGuildBattleLogger@GUILD_BATTLE@@SAPEAV12@XZ
 * Address: 0x1403CE790
 */

GUILD_BATTLE::CGuildBattleLogger *__cdecl GUILD_BATTLE::CGuildBattleLogger::Instance()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  struct GUILD_BATTLE::CGuildBattleLogger *v2; // rax@6
  __int64 v4; // [sp+0h] [bp-48h]@1
  struct GUILD_BATTLE::CGuildBattleLogger *v5; // [sp+20h] [bp-28h]@8
  GUILD_BATTLE::CGuildBattleLogger *v6; // [sp+28h] [bp-20h]@5
  __int64 v7; // [sp+30h] [bp-18h]@4
  struct GUILD_BATTLE::CGuildBattleLogger *v8; // [sp+38h] [bp-10h]@6

  v0 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  v7 = -2i64;
  if ( !GUILD_BATTLE::CGuildBattleLogger::ms_Instance )
  {
    v6 = (GUILD_BATTLE::CGuildBattleLogger *)operator new(8ui64);
    if ( v6 )
    {
      GUILD_BATTLE::CGuildBattleLogger::CGuildBattleLogger(v6);
      v8 = v2;
    }
    else
    {
      v8 = 0i64;
    }
    v5 = v8;
    GUILD_BATTLE::CGuildBattleLogger::ms_Instance = v8;
  }
  return GUILD_BATTLE::CGuildBattleLogger::ms_Instance;
}
