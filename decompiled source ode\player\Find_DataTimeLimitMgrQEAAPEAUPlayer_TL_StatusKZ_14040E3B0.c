/*
 * Function: ?Find_Data@TimeLimitMgr@@QEAAPEAUPlayer_TL_Status@@K@Z
 * Address: 0x14040E3B0
 */

Player_TL_Status *__fastcall TimeLimitMgr::Find_Data(TimeLimitMgr *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CPlayer *v7; // [sp+28h] [bp-10h]@7
  TimeLimitMgr *v8; // [sp+40h] [bp+8h]@1
  unsigned int v9; // [sp+48h] [bp+10h]@1

  v9 = dwSerial;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 2532; ++j )
  {
    v7 = &g_Player + j;
    if ( v7->m_bLive && CPlayerDB::GetCharSerial(&v7->m_Param) == v9 && v8->m_lstTLStaus[j].m_bUse )
      return &v8->m_lstTLStaus[j];
  }
  return 0i64;
}
