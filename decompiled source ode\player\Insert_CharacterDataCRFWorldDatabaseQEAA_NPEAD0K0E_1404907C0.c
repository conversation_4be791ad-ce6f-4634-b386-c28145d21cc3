/*
 * Function: ?Insert_CharacterData@CRFWorldDatabase@@QEAA_NPEAD0K0EEKHPEAK@Z
 * Address: 0x1404907C0
 */

char __fastcall CRFWorldDatabase::Insert_CharacterData(CRFWorldDatabase *this, char *pwszCharacterName, char *wszClassCode, unsigned int dwAccountSerial, char *wszAccount, char bySlotIndex, char byRaceSexCode, unsigned int dwBaseShape, int nMapIndex, unsigned int *pDwSerial)
{
  __int64 *v10; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v13; // [sp+0h] [bp-2B8h]@1
  void *SQLStmt; // [sp+20h] [bp-298h]@4
  char *v15; // [sp+28h] [bp-290h]@4
  int v16; // [sp+30h] [bp-288h]@4
  int v17; // [sp+38h] [bp-280h]@4
  unsigned int v18; // [sp+40h] [bp-278h]@4
  __int16 v19; // [sp+50h] [bp-268h]@9
  char Dest; // [sp+70h] [bp-248h]@4
  char szLog; // [sp+190h] [bp-128h]@16
  unsigned __int64 v22; // [sp+2A0h] [bp-18h]@4
  CRFWorldDatabase *v23; // [sp+2C0h] [bp+8h]@1
  char *pwszCharacterNamea; // [sp+2C8h] [bp+10h]@1

  pwszCharacterNamea = pwszCharacterName;
  v23 = this;
  v10 = &v13;
  for ( i = 172i64; i; --i )
  {
    *(_DWORD *)v10 = -*********;
    v10 = (__int64 *)((char *)v10 + 4);
  }
  v22 = (unsigned __int64)&v13 ^ _security_cookie;
  v18 = dwBaseShape;
  v17 = (unsigned __int8)byRaceSexCode;
  v16 = (unsigned __int8)bySlotIndex;
  v15 = wszAccount;
  LODWORD(SQLStmt) = dwAccountSerial;
  sprintf(&Dest, "{ CALL pInsert_Base( '%s', '%s', %d, '%s', %d, %d, %d ) }", pwszCharacterName, wszClassCode);
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v23->vfptr, &Dest);
  if ( !v23->m_hStmtUpdate && !CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v23->vfptr) )
  {
    CRFNewDatabase::ErrLog((CRFNewDatabase *)&v23->vfptr, "ReConnectDataBase Fail. Query : Insert_CharacterData");
    return 0;
  }
  CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v23->vfptr, 0);
  v19 = SQLExecDirectA_0(v23->m_hStmtUpdate, &Dest, -3);
  if ( v19 && v19 != 1 )
  {
    SQLStmt = v23->m_hStmtUpdate;
    CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v19, &Dest, "_SQLExecDirect", SQLStmt);
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v23->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v23->vfptr, 1);
    CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v19, v23->m_hStmtUpdate);
    return 0;
  }
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::FmtLog((CRFNewDatabase *)&v23->vfptr, "%s Success", &Dest);
  if ( !CRFWorldDatabase::Select_CharacterSerial(v23, pwszCharacterNamea, pDwSerial) )
  {
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v23->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v23->vfptr, 1);
    return 0;
  }
  sprintf(&szLog, "{ CALL pInsert_General( %d, %d ) }", *pDwSerial, (unsigned int)nMapIndex);
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v23->vfptr, &szLog);
  v19 = SQLExecDirectA_0(v23->m_hStmtUpdate, &szLog, -3);
  if ( v19 && v19 != 1 )
  {
    SQLStmt = v23->m_hStmtUpdate;
    CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v19, &szLog, "SQLExecDirectA", SQLStmt);
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v23->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v23->vfptr, 1);
    CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v19, v23->m_hStmtUpdate);
    return 0;
  }
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::FmtLog((CRFNewDatabase *)&v23->vfptr, "%s Success", &szLog);
  sprintf(&szLog, "{ CALL pInsert_supplement( %d ) }", *pDwSerial);
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v23->vfptr, &szLog);
  v19 = SQLExecDirectA_0(v23->m_hStmtUpdate, &szLog, -3);
  if ( v19 && v19 != 1 )
  {
    SQLStmt = v23->m_hStmtUpdate;
    CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v19, &szLog, "SQLExecDirectA", SQLStmt);
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v23->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v23->vfptr, 1);
    CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v19, v23->m_hStmtUpdate);
    return 0;
  }
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::FmtLog((CRFNewDatabase *)&v23->vfptr, "%s Success", &szLog);
  sprintf(&szLog, "{ CALL pInsert_inven( %d ) }", *pDwSerial);
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v23->vfptr, &szLog);
  v19 = SQLExecDirectA_0(v23->m_hStmtUpdate, &szLog, -3);
  if ( v19 && v19 != 1 )
  {
    SQLStmt = v23->m_hStmtUpdate;
    CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v19, &szLog, "SQLExecDirectA", SQLStmt);
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v23->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v23->vfptr, 1);
    CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v19, v23->m_hStmtUpdate);
    return 0;
  }
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::FmtLog((CRFNewDatabase *)&v23->vfptr, "%s Success", &szLog);
  sprintf(&szLog, "{ CALL pInsert_Quest( %d ) }", *pDwSerial);
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v23->vfptr, &szLog);
  v19 = SQLExecDirectA_0(v23->m_hStmtUpdate, &szLog, -3);
  if ( v19 && v19 != 1 )
  {
    SQLStmt = v23->m_hStmtUpdate;
    CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v19, &szLog, "SQLExecDirectA", SQLStmt);
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v23->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v23->vfptr, 1);
    CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v19, v23->m_hStmtUpdate);
    return 0;
  }
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::FmtLog((CRFNewDatabase *)&v23->vfptr, "%s Success", &szLog);
  sprintf(&szLog, "{ CALL pInsert_UserInterface( %d ) }", *pDwSerial);
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v23->vfptr, &szLog);
  v19 = SQLExecDirectA_0(v23->m_hStmtUpdate, &szLog, -3);
  if ( v19 && v19 != 1 )
  {
    SQLStmt = v23->m_hStmtUpdate;
    CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v19, &szLog, "SQLExecDirectA", SQLStmt);
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v23->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v23->vfptr, 1);
    CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v19, v23->m_hStmtUpdate);
    return 0;
  }
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::FmtLog((CRFNewDatabase *)&v23->vfptr, "%s Success", &szLog);
  if ( !((signed int)(unsigned __int8)byRaceSexCode >> 1) )
  {
    sprintf(&szLog, "{ CALL pInsert_Unit( %d ) }", *pDwSerial);
    if ( v23->m_bSaveDBLog )
      CRFNewDatabase::Log((CRFNewDatabase *)&v23->vfptr, &szLog);
    v19 = SQLExecDirectA_0(v23->m_hStmtUpdate, &szLog, -3);
    if ( v19 && v19 != 1 )
    {
      SQLStmt = v23->m_hStmtUpdate;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v19, &szLog, "SQLExecDirectA", SQLStmt);
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v23->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v23->vfptr, 1);
      CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v19, v23->m_hStmtUpdate);
      return 0;
    }
    if ( v23->m_bSaveDBLog )
      CRFNewDatabase::FmtLog((CRFNewDatabase *)&v23->vfptr, "%s Success", &szLog);
  }
  sprintf(&szLog, "{ CALL pInsert_CombineEx_Result( %d ) }", *pDwSerial);
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v23->vfptr, &szLog);
  v19 = SQLExecDirectA_0(v23->m_hStmtUpdate, &szLog, -3);
  if ( v19 && v19 != 1 )
  {
    SQLStmt = v23->m_hStmtUpdate;
    CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v19, &szLog, "SQLExecDirectA", SQLStmt);
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v23->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v23->vfptr, 1);
    CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v19, v23->m_hStmtUpdate);
    result = 0;
  }
  else
  {
    if ( v23->m_bSaveDBLog )
      CRFNewDatabase::FmtLog((CRFNewDatabase *)&v23->vfptr, "%s Success", &szLog);
    sprintf(&szLog, "{ CALL pInsert_OreCutting( %d ) }", *pDwSerial);
    if ( v23->m_bSaveDBLog )
      CRFNewDatabase::Log((CRFNewDatabase *)&v23->vfptr, &szLog);
    v19 = SQLExecDirectA_0(v23->m_hStmtUpdate, &szLog, -3);
    if ( v19 && v19 != 1 )
    {
      SQLStmt = v23->m_hStmtUpdate;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v19, &szLog, "SQLExecDirectA", SQLStmt);
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v23->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v23->vfptr, 1);
      CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v19, v23->m_hStmtUpdate);
      result = 0;
    }
    else
    {
      if ( v23->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v23->vfptr, "%s Success", &szLog);
      CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&v23->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v23->vfptr, 1);
      result = 1;
    }
  }
  return result;
}
