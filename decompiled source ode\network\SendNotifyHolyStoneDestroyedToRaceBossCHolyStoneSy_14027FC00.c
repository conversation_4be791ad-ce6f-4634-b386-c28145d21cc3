/*
 * Function: ?SendNotifyHolyStoneDestroyedToRaceBoss@CHolyStoneSystem@@QEAAXXZ
 * Address: 0x14027FC00
 */

void __fastcall CHolyStoneSystem::SendNotifyHolyStoneDestroyedToRaceBoss(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v3; // rax@10
  __int64 v4; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+34h] [bp-54h]@11
  char pbyType; // [sp+54h] [bp-34h]@4
  char v7; // [sp+55h] [bp-33h]@4
  int j; // [sp+64h] [bp-24h]@4
  CPlayer *v9; // [sp+68h] [bp-20h]@7
  unsigned int dwSerial; // [sp+70h] [bp-18h]@10
  int v11; // [sp+74h] [bp-14h]@10

  v1 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  pbyType = 13;
  v7 = 34;
  for ( j = 0; j < 2532; ++j )
  {
    v9 = &g_Player + j;
    if ( v9->m_bLive && v9->m_bOper )
    {
      dwSerial = CPlayerDB::GetCharSerial(&v9->m_Param);
      v11 = CPlayerDB::GetRaceCode(&v9->m_Param);
      v3 = CPvpUserAndGuildRankingSystem::Instance();
      if ( CPvpUserAndGuildRankingSystem::IsCurrentRaceBossGroup(v3, v11, dwSerial) )
        CNetProcess::LoadSendMsg(unk_1414F2088, v9->m_ObjID.m_wIndex, &pbyType, &szMsg, 1u);
    }
  }
}
