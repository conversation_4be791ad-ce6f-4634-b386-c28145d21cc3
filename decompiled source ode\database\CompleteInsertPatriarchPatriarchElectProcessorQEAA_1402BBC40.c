/*
 * Function: ?CompleteInsertPatriarch@PatriarchElectProcessor@@QEAAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1402BBC40
 */

void __fastcall PatriarchElectProcessor::CompleteInsertPatriarch(PatriarchElectProcessor *this, _DB_QRY_SYN_DATA *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CandidateMgr *v4; // rax@4
  CPvpUserAndGuildRankingSystem *v5; // rax@4
  unsigned int v6; // eax@4
  ClassOrderProcessor *v7; // rax@9
  CPvpUserAndGuildRankingSystem *v8; // rax@10
  CPvpUserAndGuildRankingSystem *v9; // rax@10
  ClassOrderProcessor *v10; // rax@10
  int v11; // eax@14
  unsigned __int16 v12; // ax@15
  char v13; // al@15
  unsigned __int16 v14; // ax@17
  __int64 v15; // [sp+0h] [bp-128h]@1
  char *pwszAvatorName; // [sp+20h] [bp-108h]@5
  int v17; // [sp+28h] [bp-100h]@5
  char *v18; // [sp+30h] [bp-F8h]@4
  _candidate_info *v19; // [sp+38h] [bp-F0h]@4
  CPlayer *v20; // [sp+40h] [bp-E8h]@4
  int j; // [sp+48h] [bp-E0h]@10
  CPlayer *v22; // [sp+50h] [bp-D8h]@13
  char pbyType; // [sp+64h] [bp-C4h]@15
  char v24; // [sp+65h] [bp-C3h]@15
  _pt_inform_appoint_zocl v25; // [sp+88h] [bp-A0h]@15
  char v26; // [sp+B4h] [bp-74h]@17
  char v27; // [sp+B5h] [bp-73h]@17
  _pt_result_appoint_zocl v28; // [sp+D8h] [bp-50h]@17
  char *v29; // [sp+110h] [bp-18h]@9
  unsigned __int64 v30; // [sp+118h] [bp-10h]@4
  PatriarchElectProcessor *v31; // [sp+130h] [bp+8h]@1
  _DB_QRY_SYN_DATA *v32; // [sp+138h] [bp+10h]@1

  v32 = pData;
  v31 = this;
  v2 = &v15;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v30 = (unsigned __int64)&v15 ^ _security_cookie;
  v18 = pData->m_sData;
  v4 = CandidateMgr::Instance();
  v19 = CandidateMgr::GetPatriarchGroupBySerial(v4, *v18, *((_DWORD *)v18 + 2));
  v5 = CPvpUserAndGuildRankingSystem::Instance();
  v6 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v5, *v18, 0);
  v20 = GetPtrPlayerFromSerial(&g_Player, 2532, v6);
  if ( v32->m_byResult )
  {
    v17 = (unsigned __int8)*v18;
    LODWORD(pwszAvatorName) = *((_DWORD *)v18 + 2);
    CLogFile::Write(&v31->_kSysLog, "FAILED DB_RET(%s_%d):%d-Cost:%d", "Appoint Patriarch Insert", v31->_dwElectSerial);
    if ( v19 && v20 )
    {
      if ( v20->m_bOper )
      {
        v29 = v19->wszName;
        v7 = ClassOrderProcessor::Instance();
        pwszAvatorName = v29;
        ClassOrderProcessor::SendMsg_QueryAppointResult(v7, v20->m_id.wIndex, 6, v19->eClassType, v29);
        _candidate_info::_Init(v19);
      }
    }
  }
  else
  {
    v8 = CPvpUserAndGuildRankingSystem::Instance();
    CPvpUserAndGuildRankingSystem::SetUpdateRaceBossSerial(v8, v19->byRace, v19->eClassType, v19->dwAvatorSerial);
    CNotifyNotifyRaceLeaderSownerUTaxrate::UpdateRaceLeader(&stru_1799C9AF8, v19->byRace, v19->eClassType, v19->wszName);
    v9 = CPvpUserAndGuildRankingSystem::Instance();
    CPvpUserAndGuildRankingSystem::ApplyUpdatedBossInfo(v9);
    v10 = ClassOrderProcessor::Instance();
    ClassOrderProcessor::UpdatePacket(v10, v19->byRace, v19->eClassType);
    for ( j = 0; j < 2532; ++j )
    {
      v22 = &g_Player + j;
      if ( v22->m_bOper )
      {
        v11 = CPlayerDB::GetRaceCode(&v22->m_Param);
        if ( v11 == v19->byRace )
        {
          pbyType = 56;
          v24 = 14;
          _pt_inform_appoint_zocl::_pt_inform_appoint_zocl(&v25);
          v25.byClassType = LOBYTE(v19->eClassType) - 5;
          strcpy_0(v25.wszAvatorName, v19->wszName);
          v12 = _pt_inform_appoint_zocl::size(&v25);
          CNetProcess::LoadSendMsg(unk_1414F2088, v22->m_id.wIndex, &pbyType, &v25.byClassType, v12);
          v13 = CPlayerDB::GetRaceCode(&v22->m_Param);
          CNotifyNotifyRaceLeaderSownerUTaxrate::Notify(&stru_1799C9AF8, v13, v22->m_ObjID.m_wIndex);
        }
      }
    }
    if ( v20 )
    {
      v26 = 56;
      v27 = 13;
      _pt_result_appoint_zocl::_pt_result_appoint_zocl(&v28);
      v28.byLevel = v19->byLevel;
      v28.byClassType = LOBYTE(v19->eClassType) - 5;
      v28.dPvpPoint = v19->dPvpPoint;
      strcpy_0(v28.wszAvatorName, v19->wszName);
      v14 = _pt_result_appoint_zocl::size(&v28);
      CNetProcess::LoadSendMsg(unk_1414F2088, v20->m_id.wIndex, &v26, &v28.byLevel, v14);
    }
  }
}
