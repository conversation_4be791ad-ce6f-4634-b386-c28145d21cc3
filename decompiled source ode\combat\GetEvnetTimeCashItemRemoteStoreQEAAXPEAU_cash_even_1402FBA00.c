/*
 * Function: ?GetEvnetTime@CashItemRemoteStore@@QEAAXPEAU_cash_event_time@@H@Z
 * Address: 0x1402FBA00
 */

void __fastcall CashItemRemoteStore::GetEvnetTime(CashItemRemoteStore *this, _cash_event_time *pETime, int itime)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  __time32_t Time; // [sp+24h] [bp-24h]@4
  struct tm *Tm; // [sp+38h] [bp-10h]@4
  _cash_event_time *v8; // [sp+58h] [bp+10h]@1
  int v9; // [sp+60h] [bp+18h]@1

  v9 = itime;
  v8 = pETime;
  v3 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  _time32(&Time);
  Tm = _localtime32(&Time);
  Tm->tm_min += 5;
  v8->m_EventTime[0] = _mktime32(Tm);
  Tm = _localtime32(v8->m_EventTime);
  v8->m_nYear[0] = Tm->tm_year;
  v8->m_nMonth[0] = Tm->tm_mon;
  v8->m_nDay[0] = Tm->tm_mday;
  v8->m_nHour[0] = Tm->tm_hour;
  v8->m_nMinute[0] = Tm->tm_min;
  Tm->tm_min += v9;
  v8->m_EventTime[1] = _mktime32(Tm);
  Tm = _localtime32(&v8->m_EventTime[1]);
  v8->m_nYear[1] = Tm->tm_year;
  v8->m_nMonth[1] = Tm->tm_mon;
  v8->m_nDay[1] = Tm->tm_mday;
  v8->m_nHour[1] = Tm->tm_hour;
  v8->m_nMinute[1] = Tm->tm_min;
}
