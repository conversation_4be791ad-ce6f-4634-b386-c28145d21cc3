/*
 * Function: ?max_size@?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@std@@QEBA_KXZ
 * Address: 0x140315A10
 */

signed __int64 __fastcall std::allocator<std::pair<int const,_TimeItem_fld const *>>::max_size(std::allocator<std::pair<int const ,_TimeItem_fld const *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-18h]@1

  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return 0xFFFFFFFFFFFFFFFi64;
}
