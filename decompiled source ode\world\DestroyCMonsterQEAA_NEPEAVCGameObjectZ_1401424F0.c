/*
 * Function: ?Destroy@CMonster@@QEAA_NEPEAVCGameObject@@@Z
 * Address: 0x1401424F0
 */

bool __usercall CMonster::Destroy@<al>(CMonster *this@<rcx>, char byDestroyCode@<dl>, CGameObject *pAttObj@<r8>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  CMonster *v8; // [sp+30h] [bp+8h]@1
  char v9; // [sp+38h] [bp+10h]@1
  CGameObject *pAttObja; // [sp+40h] [bp+18h]@1

  pAttObja = pAttObj;
  v9 = byDestroyCode;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  CLuaSignalReActor::SetSignalAndAction(&v8->m_LuaSignalReActor, 0);
  if ( v8->m_pMonRec->m_bMonsterCondition == 1 )
    CMonster::_BossDieWriteLog_Start(v8, v9, pAttObja);
  CMonsterHierarchy::OnChildMonsterDestroy(&v8->m_MonHierarcy);
  v8->m_dwLastDestroyTime = GetLoopTime();
  if ( v8->m_pTargetChar )
    CCharacter::RemoveSlot(v8->m_pTargetChar, (CCharacter *)&v8->vfptr);
  v8->m_pTargetChar = 0i64;
  CMonster::CheckEmotionPresentation(v8);
  if ( v8->m_bMove )
  {
    CMonster::GetMoveSpeed(v8);
    CCharacter::MoveBreak((CCharacter *)&v8->vfptr, a4);
  }
  CMonster::SendMsg_Destroy(v8, v9);
  v8->m_bOper = 0;
  if ( !v9 )
  {
    if ( pAttObja )
    {
      if ( (CPlayer *)pAttObja != &sPlayerDum )
      {
        CMonster::CheckLootItem(v8, (CPlayer *)pAttObja);
        if ( v8->m_pMonRec->m_bMonsterCondition == 1 )
          CMonster::_BossDieWriteLog_End(v8);
      }
    }
  }
  if ( v8->m_pActiveRec )
  {
    _mon_active::SetCurMonNum(v8->m_pActiveRec, -1);
    if ( v8->m_pDumPosition )
      _dummy_position::SetActiveMonNum(v8->m_pDumPosition, -1);
    v8->m_pActiveRec = 0i64;
  }
  v8->m_bDungeon = 0;
  v8->m_dwObjSerial = -1;
  --CMonster::s_nLiveNum;
  CLuaSignalReActor::Free(&v8->m_LuaSignalReActor);
  return CCharacter::Destroy((CCharacter *)&v8->vfptr);
}
