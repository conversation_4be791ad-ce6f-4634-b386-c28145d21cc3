/*
 * Function: ?GiveEventItem@CExchangeEvent@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x14032A630
 */

void __fastcall CExchangeEvent::GiveEventItem(CExchangeEvent *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-98h]@1
  EventItemInfo *v5; // [sp+30h] [bp-68h]@8
  void *Src; // [sp+38h] [bp-60h]@10
  _STORAGE_LIST::_db_con Dst; // [sp+48h] [bp-50h]@11
  CExchangeEvent *v8; // [sp+A0h] [bp+8h]@1
  CPlayer *v9; // [sp+A8h] [bp+10h]@1

  v9 = pOne;
  v8 = this;
  v2 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pOne && pOne->m_bOper )
  {
    if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&pOne->m_Param.m_dbInven.m_nListNum) == 255 )
    {
      CPlayer::SendMsg_BuddhaEventMsg(v9, 1);
    }
    else
    {
      v5 = CExchangeEvent::GetEventItemInfo(v8, 0);
      if ( v5 )
      {
        if ( v5->byTableCode != 255 )
        {
          Src = MakeLoot(v5->byTableCode, v5->dwIndex);
          if ( Src )
          {
            _STORAGE_LIST::_db_con::_db_con(&Dst);
            memcpy_0(&Dst, Src, 0x32ui64);
            Src = &Dst;
            Dst.m_wSerial = CPlayerDB::GetNewItemSerial(&v9->m_Param);
            if ( CPlayer::Emb_AddStorage(v9, 0, (_STORAGE_LIST::_storage_con *)Src, 0, 1) )
            {
              CPlayer::SendMsg_RewardAddItem(v9, (_STORAGE_LIST::_db_con *)Src, 7);
              CPlayer::SendMsg_BuddhaEventMsg(v9, 0);
            }
          }
        }
      }
    }
  }
}
