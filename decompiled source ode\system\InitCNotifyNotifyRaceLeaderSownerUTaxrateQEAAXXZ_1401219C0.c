/*
 * Function: ?Init@CNotifyNotifyRaceLeaderSownerUTaxrate@@QEAAXXZ
 * Address: 0x1401219C0
 */

void __fastcall CNotifyNotifyRaceLeaderSownerUTaxrate::Init(CNotifyNotifyRaceLeaderSownerUTaxrate *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager *v3; // rax@8
  char *v4; // rax@8
  CNationSettingManager *v5; // rax@9
  char *v6; // rax@9
  CNationSettingManager *v7; // rax@9
  char *v8; // rax@9
  CNationSettingManager *v9; // rax@9
  char *v10; // rax@9
  CNationSettingManager *v11; // rax@9
  char *v12; // rax@9
  __int64 v13; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  int k; // [sp+24h] [bp-14h]@6
  CNotifyNotifyRaceLeaderSownerUTaxrate *v16; // [sp+40h] [bp+8h]@1

  v16 = this;
  v1 = &v13;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 3; ++j )
  {
    for ( k = 0; k < 9; ++k )
    {
      v3 = CTSingleton<CNationSettingManager>::Instance();
      v4 = CNationSettingManager::GetNoneString(v3);
      strcpy_s((char *)v16 + 222 * j + 17 * k, 0x11ui64, v4);
    }
    v5 = CTSingleton<CNationSettingManager>::Instance();
    v6 = CNationSettingManager::GetNoneString(v5);
    strcpy_s(v16->m_Send[j].wszSettlement1OwnerGuildName, 0x11ui64, v6);
    v7 = CTSingleton<CNationSettingManager>::Instance();
    v8 = CNationSettingManager::GetNoneString(v7);
    strcpy_s(v16->m_Send[j].wszSettlement1OwnerGuildMasterName, 0x11ui64, v8);
    v9 = CTSingleton<CNationSettingManager>::Instance();
    v10 = CNationSettingManager::GetNoneString(v9);
    strcpy_s(v16->m_Send[j].wszSettlement2OwnerGuildName, 0x11ui64, v10);
    v11 = CTSingleton<CNationSettingManager>::Instance();
    v12 = CNationSettingManager::GetNoneString(v11);
    strcpy_s(v16->m_Send[j].wszSettlement2OwnerGuildMasterName, 0x11ui64, v12);
    v16->m_Send[j].byTaxRate = 5;
  }
}
