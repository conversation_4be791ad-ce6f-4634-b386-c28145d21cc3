/*
 * Function: j_?price_auto_trade@CMgrAvatorItemHistory@@QEAAXHKPEAU_db_con@_STORAGE_LIST@@KKKPEAD@Z
 * Address: 0x1400025A4
 */

void __fastcall CMgrAvatorItemHistory::price_auto_trade(CMgrAvatorItemHistory *this, int n, unsigned int dwRegistSerial, _STORAGE_LIST::_db_con *pRegItem, unsigned int dwTax, unsigned int dwOldPrice, unsigned int dwNewPrice, char *pszFileName)
{
  CMgrAvatorItemHistory::price_auto_trade(this, n, dwRegistSerial, pRegItem, dwTax, dwOldPrice, dwNewPrice, pszFileName);
}
