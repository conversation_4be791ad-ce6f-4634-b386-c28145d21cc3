/*
 * Function: ?ConnectThread@CNetProcess@@CAXPEAX@Z
 * Address: 0x14047B230
 */

void __fastcall CNetProcess::ConnectThread(void *pv)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-68h]@1
  _DWORD *v4; // [sp+20h] [bp-48h]@4
  _DWORD *v5; // [sp+28h] [bp-40h]@4
  __int64 v6; // [sp+30h] [bp-38h]@4
  CNetSocket *v7; // [sp+38h] [bp-30h]@4
  unsigned int pdwOutIndex; // [sp+44h] [bp-24h]@6
  sockaddr_in *pAddr; // [sp+58h] [bp-10h]@7
  _DWORD *v10; // [sp+70h] [bp+8h]@1

  v10 = pv;
  v1 = &v3;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = v10;
  v5 = v10;
  v6 = *((_QWORD *)v10 + 1);
  v7 = (CNetSocket *)(v6 + 8);
  while ( *v5 )
  {
    while ( CNetIndexList::CopyFront((CNetIndexList *)(v6 + 70256), &pdwOutIndex) )
    {
      pAddr = (sockaddr_in *)(*(_QWORD *)(v6 + 70248) + 20i64 * pdwOutIndex);
      *(_DWORD *)&pAddr[1].sin_family = CNetSocket::Connect(v7, pdwOutIndex, pAddr);
      if ( CNetIndexList::PopNode_Front((CNetIndexList *)(v6 + 70256), &pdwOutIndex) )
        CNetIndexList::PushNode_Back((CNetIndexList *)(v6 + 70416), pdwOutIndex);
    }
    Sleep(0xAu);
  }
  _endthreadex(0);
}
