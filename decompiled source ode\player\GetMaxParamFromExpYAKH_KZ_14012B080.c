/*
 * Function: ?GetMaxParamFromExp@@YAKH_K@Z
 * Address: 0x14012B080
 */

__int64 __fastcall GetMaxParamFromExp(int nAnimusClass, unsigned __int64 dwExp)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  __int64 v5; // [sp+0h] [bp-58h]@1
  _animus_fld *v6; // [sp+20h] [bp-38h]@4
  unsigned int v7; // [sp+34h] [bp-24h]@6
  int nAnimusClassa; // [sp+60h] [bp+8h]@1

  nAnimusClassa = nAnimusClass;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = GetAnimusFldFromExp(nAnimusClassa, dwExp);
  if ( v6 )
  {
    LOWORD(v7) = v6->m_nMaxHP;
    HIWORD(v7) = v6->m_nMaxFP;
    result = v7;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
