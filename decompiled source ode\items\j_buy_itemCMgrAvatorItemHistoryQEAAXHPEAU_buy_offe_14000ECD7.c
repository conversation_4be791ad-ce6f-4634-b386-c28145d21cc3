/*
 * Function: j_?buy_item@CMgrAvatorItemHistory@@QEAAXHPEAU_buy_offer@@EKKKKPEAD@Z
 * Address: 0x14000ECD7
 */

void __fastcall CMgrAvatorItemHistory::buy_item(CMgrAvatorItemHistory *this, int n, _buy_offer *pOffer, char by<PERSON><PERSON><PERSON><PERSON>, unsigned int dwCostDalant, unsigned int dwCostGold, unsigned int dwNewDalant, unsigned int dwNewGold, char *pszFileName)
{
  CMgrAvatorItemHistory::buy_item(
    this,
    n,
    pOffer,
    byOfferNum,
    dwCostDalant,
    dwCostGold,
    dwNewDalant,
    dwNewGold,
    pszFileName);
}
