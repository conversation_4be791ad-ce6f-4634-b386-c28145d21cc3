/*
 * Function: ?InitRemainOreAmount@COreAmountMgr@@QEAAXKK@Z
 * Address: 0x1403F93E0
 */

void __fastcall COreAmountMgr::InitRemainOreAmount(COreAmountMgr *this, unsigned int dwRemain, unsigned int dwTotal)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  int v6; // [sp+20h] [bp-28h]@5
  int v7; // [sp+24h] [bp-24h]@8
  int v8; // [sp+28h] [bp-20h]@8
  unsigned int v9; // [sp+2Ch] [bp-1Ch]@11
  unsigned int v10; // [sp+30h] [bp-18h]@7
  unsigned int v11; // [sp+34h] [bp-14h]@14
  COreAmountMgr *v12; // [sp+50h] [bp+8h]@1
  unsigned int v13; // [sp+58h] [bp+10h]@1
  unsigned int v14; // [sp+60h] [bp+18h]@1

  v14 = dwTotal;
  v13 = dwRemain;
  v12 = this;
  v3 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CLogFile::Write(
    &v12->m_logOreAmount,
    "Pre InitRemainOreAmount Ore Amount >> Remain:%d, Total:%d",
    v12->m_dwRemainOreAmount,
    v12->m_dwTotalOreAmount);
  if ( v13 == -1 )
  {
    v6 = CPlayer::s_nLiveNum;
    if ( CPlayer::s_nLiveNum >= 200 )
    {
      v10 = 297 * v12->m_dwTotalOreSet;
      v12->m_dwTotalOreAmount = v6 / 10 * v10;
    }
    else
    {
      v12->m_dwTotalOreAmount = 297 * v12->m_dwMinOreSet;
    }
    v7 = GetCurwDay();
    v8 = (unsigned __int8)CHolyStoneSystem::GetNumOfTime(&g_HolySys) - 1;
    if ( v12->m_fMultipleRate[v7][v8] != 1.0 )
    {
      CLogFile::Write(&v12->m_logOreAmount, "Pre Apply Multiple Rate Ore Amount >> Total:%d", v12->m_dwTotalOreAmount);
      v12->m_dwTotalOreAmount = (signed int)ffloor((float)(signed int)v12->m_dwTotalOreAmount * v12->m_fMultipleRate[v7][v8]);
      CLogFile::Write(&v12->m_logOreAmount, "Post Apply Multiple Rate Ore Amount >> Total:%d", v12->m_dwTotalOreAmount);
    }
    v12->m_dwRemainOreAmount = v12->m_dwTotalOreAmount;
    if ( !CHolyStoneSystem::GetDestroyerState(&g_HolySys) )
    {
      v9 = 70 * v12->m_dwRemainOreAmount / 0x64;
      CLogFile::Write(
        &v12->m_logOreAmount,
        "Reduce Ore Amount [Touch Down Fail] >> Pre:%d, Post:%d",
        v12->m_dwRemainOreAmount,
        v9);
      v12->m_dwRemainOreAmount = v9;
    }
  }
  else
  {
    if ( v14 == -1 )
      v11 = 0;
    else
      v11 = v14;
    v12->m_dwTotalOreAmount = v11;
    v12->m_dwRemainOreAmount = v13;
    if ( v12->m_dwRemainOreAmount > v12->m_dwTotalOreAmount )
      v12->m_dwTotalOreAmount = v12->m_dwRemainOreAmount;
  }
  v12->m_bCheckExhOreLog = 1;
  CLogFile::Write(
    &v12->m_logOreAmount,
    "Post InitRemainOreAmount Ore Amount >> Remain:%d, Total:%d",
    v12->m_dwRemainOreAmount,
    v12->m_dwTotalOreAmount);
  COreAmountMgr::UpdateDepositeRate(v12);
}
