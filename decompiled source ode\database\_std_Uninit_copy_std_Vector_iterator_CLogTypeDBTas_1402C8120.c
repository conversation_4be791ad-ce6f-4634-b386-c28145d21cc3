/*
 * Function: _std::_Uninit_copy_std::_Vector_iterator_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64____CLogTypeDBTask_____ptr64_____ptr64_std::allocator_CLogTypeDBTask_____ptr64____::_1_::catch$0
 * Address: 0x1402C8120
 */

void __fastcall __noreturn std::_Uninit_copy_std::_Vector_iterator_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64____CLogTypeDBTask_____ptr64_____ptr64_std::allocator_CLogTypeDBTask_____ptr64____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 112); *(_QWORD *)(i + 32) += 8i64 )
    std::allocator<CLogTypeDBTask *>::destroy(
      *(std::allocator<CLogTypeDBTask *> **)(i + 120),
      *(CLogTypeDBTask ***)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
