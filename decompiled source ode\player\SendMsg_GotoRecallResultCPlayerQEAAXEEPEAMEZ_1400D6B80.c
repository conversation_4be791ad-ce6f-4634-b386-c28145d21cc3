/*
 * Function: ?SendMsg_GotoRecallResult@CPlayer@@QEAAXEEPEAME@Z
 * Address: 0x1400D6B80
 */

void __fastcall CPlayer::SendMsg_GotoRecallResult(CPlayer *this, char byErrCode, char byMapCode, float *pfStartPos, char byMapInType)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  char v9; // [sp+39h] [bp-4Fh]@4
  char Dst; // [sp+3Ah] [bp-4Eh]@4
  char v11; // [sp+46h] [bp-42h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v13; // [sp+65h] [bp-23h]@4
  CPlayer *v14; // [sp+90h] [bp+8h]@1

  v14 = this;
  v5 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  szMsg = byErrCode;
  v9 = byMapCode;
  memcpy_0(&Dst, pfStartPos, 0xCui64);
  v11 = byMapInType;
  pbyType = 4;
  v13 = 29;
  CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_ObjID.m_wIndex, &pbyType, &szMsg, 0xFu);
}
