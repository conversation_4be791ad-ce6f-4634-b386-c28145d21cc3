/*
 * Function: _std::vector_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo___::_Insert_n_::_1_::catch$1
 * Address: 0x140368B00
 */

void __fastcall __noreturn std::vector_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo___::_Insert_n_::_1_::catch_1(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Destroy(
    *(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > **)(a2 + 272),
    (CUnmannedTraderUserInfo *)(*(_QWORD *)(*(_QWORD *)(a2 + 280) + 16i64) + 104i64 * *(_QWORD *)(a2 + 288)),
    (CUnmannedTraderUserInfo *)(*(_QWORD *)(*(_QWORD *)(a2 + 272) + 24i64) + 104i64 * *(_QWORD *)(a2 + 288)));
  CxxThrowException_0(0i64, 0i64);
}
