/*
 * Function: ?GetInfo@CNormalGuildBattle@GUILD_BATTLE@@QEAA_NAEAU_guild_battle_current_battle_info_result_zocl@@@Z
 * Address: 0x1403E39A0
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattle::GetInfo(GUILD_BATTLE::CNormalGuildBattle *this, _guild_battle_current_battle_info_result_zocl *kInfo)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v5; // rax@8
  char *v6; // rax@8
  __int64 v7; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattle *v8; // [sp+30h] [bp+8h]@1
  _guild_battle_current_battle_info_result_zocl *Dest; // [sp+38h] [bp+10h]@1

  Dest = kInfo;
  v8 = this;
  v2 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v8->m_bInit && v8->m_pkRed && v8->m_pkBlue )
  {
    v5 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v8->m_pkRed);
    strcpy_0(Dest->wszLeftRedName, v5);
    v6 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v8->m_pkBlue);
    strcpy_0(Dest->wszRightBlueName, v6);
    Dest->dwLeftRedScore = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(v8->m_pkRed);
    Dest->dwRighBluetScore = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(v8->m_pkBlue);
    Dest->dwLeftRedGoalCnt = GUILD_BATTLE::CNormalGuildBattleGuild::GetGoalCnt(v8->m_pkRed);
    Dest->dwRighBluetGoalCnt = GUILD_BATTLE::CNormalGuildBattleGuild::GetGoalCnt(v8->m_pkBlue);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
