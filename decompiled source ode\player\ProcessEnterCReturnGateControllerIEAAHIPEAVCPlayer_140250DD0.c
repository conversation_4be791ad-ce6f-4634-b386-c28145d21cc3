/*
 * Function: ?ProcessEnter@CReturnGateController@@IEAAHIPEAVCPlayer@@@Z
 * Address: 0x140250DD0
 */

int __fastcall CReturnGateController::ProcessEnter(CReturnGateController *this, unsigned int uiGateInx, CPlayer *pkObj)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  CReturnGate *pkGate; // [sp+20h] [bp-18h]@6
  CReturnGateController *v8; // [sp+40h] [bp+8h]@1
  CPlayer *pkObja; // [sp+50h] [bp+18h]@1

  pkObja = pkObj;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pkObj->m_bInGuildBattle )
  {
    result = 6;
  }
  else
  {
    pkGate = CReturnGateController::GetGate(v8, uiGateInx);
    if ( pkGate && CReturnGate::IsOpen(pkGate) )
    {
      if ( CReturnGate::IsValidOwner(pkGate) )
      {
        result = CReturnGate::Enter(pkGate, pkObja);
      }
      else
      {
        CReturnGateController::Close(v8, pkGate);
        result = 2;
      }
    }
    else
    {
      result = 1;
    }
  }
  return result;
}
