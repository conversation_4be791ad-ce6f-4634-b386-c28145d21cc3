/*
 * Function: j_?_Insert_n@?$vector@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@IEAAXV?$_Vector_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@2@_KAEBQEAVCUnmannedTraderSubClassInfo@@@Z
 * Address: 0x14001303E
 */

void __fastcall std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Insert_n(std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *this, std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *_Where, unsigned __int64 _Count, CUnmannedTraderSubClassInfo *const *_Val)
{
  std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Insert_n(
    this,
    _Where,
    _Count,
    _Val);
}
