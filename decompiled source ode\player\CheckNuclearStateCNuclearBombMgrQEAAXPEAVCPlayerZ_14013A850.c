/*
 * Function: ?CheckNuclearState@CNuclearBombMgr@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x14013A850
 */

void __fastcall CNuclearBombMgr::CheckNuclearState(CNuclearBombMgr *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@15
  __int64 v5; // [sp+0h] [bp-108h]@1
  _nuclear_bomb_current_state_zocl v6; // [sp+40h] [bp-C8h]@5
  int j; // [sp+C4h] [bp-44h]@5
  int k; // [sp+C8h] [bp-40h]@7
  float *v9; // [sp+D0h] [bp-38h]@11
  char pbyType; // [sp+E4h] [bp-24h]@15
  char v11; // [sp+E5h] [bp-23h]@15
  CNuclearBombMgr *v12; // [sp+110h] [bp+8h]@1
  CPlayer *v13; // [sp+118h] [bp+10h]@1

  v13 = pOne;
  v12 = this;
  v2 = &v5;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( !strcmp_0(pOne->m_pCurMap->m_pMapSet->m_strCode, "resources") )
  {
    v6.nNum = 0;
    for ( j = 0; j < 3; ++j )
    {
      for ( k = 0; k < 3; ++k )
      {
        if ( CNuclearBomb::GetUse(&v12->m_Missile[j][k])
          && (signed int)(unsigned __int8)CNuclearBomb::GetBombStatus(&v12->m_Missile[j][k]) < 5 )
        {
          v6.nuclear[(unsigned __int8)v6.nNum].byRaceCode = j;
          v6.nuclear[(unsigned __int8)v6.nNum].byUseClass = k;
          v9 = CNuclearBomb::GetMissilePos(&v12->m_Missile[j][k]);
          v6.nuclear[(unsigned __int8)v6.nNum].zPos[0] = *v9;
          v6.nuclear[(unsigned __int8)v6.nNum].zPos[1] = v9[1];
          v6.nuclear[(unsigned __int8)v6.nNum].zPos[2] = v9[2];
          ++v6.nNum;
        }
      }
    }
    if ( (signed int)(unsigned __int8)v6.nNum >= 1 )
    {
      pbyType = 60;
      v11 = 10;
      v4 = _nuclear_bomb_current_state_zocl::size(&v6);
      CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, &v6.nNum, v4);
    }
  }
}
