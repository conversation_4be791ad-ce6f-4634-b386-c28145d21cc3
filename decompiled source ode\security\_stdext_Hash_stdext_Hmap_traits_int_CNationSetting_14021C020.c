/*
 * Function: _stdext::_Hash_stdext::_Hmap_traits_int_CNationSettingFactory_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64____0___::insert_::_1_::dtor$3
 * Address: 0x14021C020
 */

void __fastcall stdext::_Hash_stdext::_Hmap_traits_int_CNationSettingFactory_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64____0___::insert_::_1_::dtor_3(__int64 a1, __int64 a2)
{
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>::~_Iterator<0>((std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *)(a2 + 256));
}
