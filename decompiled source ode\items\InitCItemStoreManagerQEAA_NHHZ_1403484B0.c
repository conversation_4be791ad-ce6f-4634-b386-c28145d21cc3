/*
 * Function: ?Init@CItemStoreManager@@QEAA_NHH@Z
 * Address: 0x1403484B0
 */

char __fastcall CItemStoreManager::Init(CItemStoreManager *this, int nNormalListNum, int nInstanceListNum)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  signed __int64 v6; // rax@11
  unsigned __int8 v7; // cf@13
  unsigned __int64 v8; // rax@13
  signed __int64 v9; // rax@26
  unsigned __int64 v10; // rax@28
  __int64 v11; // [sp+0h] [bp-158h]@1
  char pszErrMsg; // [sp+40h] [bp-118h]@18
  char v13; // [sp+41h] [bp-117h]@18
  CMapItemStoreList *v14; // [sp+D0h] [bp-88h]@7
  CMapItemStoreList *v15; // [sp+D8h] [bp-80h]@7
  int count[2]; // [sp+E0h] [bp-78h]@11
  CMapItemStoreList *v17; // [sp+E8h] [bp-70h]@18
  void *v18; // [sp+F0h] [bp-68h]@15
  CMapItemStoreList *v19; // [sp+F8h] [bp-60h]@22
  CMapItemStoreList *v20; // [sp+100h] [bp-58h]@22
  int v21[2]; // [sp+108h] [bp-50h]@26
  CMapItemStoreList *v22; // [sp+110h] [bp-48h]@33
  void *v23; // [sp+118h] [bp-40h]@30
  __int64 v24; // [sp+120h] [bp-38h]@4
  void *v25; // [sp+128h] [bp-30h]@8
  CMapItemStoreList *v26; // [sp+130h] [bp-28h]@16
  void *v27; // [sp+138h] [bp-20h]@23
  CMapItemStoreList *v28; // [sp+140h] [bp-18h]@31
  unsigned __int64 v29; // [sp+148h] [bp-10h]@4
  CItemStoreManager *v30; // [sp+160h] [bp+8h]@1
  int v31; // [sp+168h] [bp+10h]@1
  int v32; // [sp+170h] [bp+18h]@1

  v32 = nInstanceListNum;
  v31 = nNormalListNum;
  v30 = this;
  v3 = &v11;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v24 = -2i64;
  v29 = (unsigned __int64)&v11 ^ _security_cookie;
  if ( nNormalListNum > 0 )
  {
    if ( v30->m_MapItemStoreList )
    {
      v15 = v30->m_MapItemStoreList;
      v14 = v15;
      if ( v15 )
        v25 = CMapItemStoreList::`vector deleting destructor'(v14, 3u);
      else
        v25 = 0i64;
      v30->m_MapItemStoreList = 0i64;
    }
    v30->m_nMapItemStoreListNum = v31;
    *(_QWORD *)count = v31;
    v6 = 24i64 * v31;
    if ( !is_mul_ok(0x18ui64, v31) )
      v6 = -1i64;
    v7 = __CFADD__(v6, 8i64);
    v8 = v6 + 8;
    if ( v7 )
      v8 = -1i64;
    v18 = operator new[](v8);
    if ( v18 )
    {
      *(_DWORD *)v18 = count[0];
      `eh vector constructor iterator'(
        (char *)v18 + 8,
        0x18ui64,
        count[0],
        (void (__cdecl *)(void *))CMapItemStoreList::CMapItemStoreList,
        (void (__cdecl *)(void *))CMapItemStoreList::~CMapItemStoreList);
      v26 = (CMapItemStoreList *)((char *)v18 + 8);
    }
    else
    {
      v26 = 0i64;
    }
    v17 = v26;
    v30->m_MapItemStoreList = v26;
    pszErrMsg = 0;
    memset(&v13, 0, 0x7Fui64);
    if ( CRecordData::ReadRecord(&v30->m_tblItemStore, ".\\Script\\StoreList.dat", 0x37CCu, &pszErrMsg) )
    {
      if ( v32 > 0 )
      {
        if ( v30->m_InstanceItemStoreList )
        {
          v20 = v30->m_InstanceItemStoreList;
          v19 = v20;
          if ( v20 )
            v27 = CMapItemStoreList::`vector deleting destructor'(v19, 3u);
          else
            v27 = 0i64;
          v30->m_InstanceItemStoreList = 0i64;
        }
        v30->m_nInstanceItemStoreListNum = v32;
        *(_QWORD *)v21 = v32;
        v9 = 24i64 * v32;
        if ( !is_mul_ok(0x18ui64, v32) )
          v9 = -1i64;
        v7 = __CFADD__(v9, 8i64);
        v10 = v9 + 8;
        if ( v7 )
          v10 = -1i64;
        v23 = operator new[](v10);
        if ( v23 )
        {
          *(_DWORD *)v23 = v21[0];
          `eh vector constructor iterator'(
            (char *)v23 + 8,
            0x18ui64,
            v21[0],
            (void (__cdecl *)(void *))CMapItemStoreList::CMapItemStoreList,
            (void (__cdecl *)(void *))CMapItemStoreList::~CMapItemStoreList);
          v28 = (CMapItemStoreList *)((char *)v23 + 8);
        }
        else
        {
          v28 = 0i64;
        }
        v22 = v28;
        v30->m_InstanceItemStoreList = v28;
      }
      if ( CItemStoreManager::InitLogger(v30) )
      {
        CItemStoreManager::SetNextEnforceInitTime(v30);
        CMyTimer::BeginTimer(&v30->m_tmrCheckTime, 0xEA60u);
        CMyTimer::BeginTimer(&v30->m_tmrSaveTime, 0x2BF20u);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      MyMessageBox("CItemStoreManager::Init() : StorList.dat Read Fail!", &pszErrMsg);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
