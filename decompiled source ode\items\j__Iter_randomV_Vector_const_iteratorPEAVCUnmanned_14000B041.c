/*
 * Function: j_??$_Iter_random@V?$_Vector_const_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@PEAPEAVCUnmannedTraderSubClassInfo@@@std@@YA?AUrandom_access_iterator_tag@0@AEBV?$_Vector_const_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@0@AEBQEAPEAVCUnmannedTraderSubClassInfo@@@Z
 * Address: 0x14000B041
 */

std::random_access_iterator_tag __fastcall std::_Iter_random<std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>,CUnmannedTraderSubClassInfo * *>(std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *__formal, CUnmannedTraderSubClassInfo **const *a2)
{
  return std::_Iter_random<std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>,CUnmannedTraderSubClassInfo * *>(
           __formal,
           a2);
}
