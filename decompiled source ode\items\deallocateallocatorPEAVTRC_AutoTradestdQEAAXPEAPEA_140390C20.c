/*
 * Function: ?deallocate@?$allocator@PEAVTRC_AutoTrade@@@std@@QEAAXPEAPEAVTRC_AutoTrade@@_K@Z
 * Address: 0x140390C20
 */

void __fastcall std::allocator<TRC_AutoTrade *>::deallocate(std::allocator<TRC_AutoTrade *> *this, TRC_AutoTrade **_Ptr, unsigned __int64 __formal)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1

  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  operator delete(_Ptr);
}
