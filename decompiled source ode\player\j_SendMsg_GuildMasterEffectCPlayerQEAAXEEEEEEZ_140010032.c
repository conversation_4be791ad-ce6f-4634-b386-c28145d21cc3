/*
 * Function: j_?SendMsg_GuildMasterEffect@CPlayer@@QEAAXEEEEEE@Z
 * Address: 0x140010032
 */

void __fastcall CPlayer::SendMsg_GuildMasterEffect(CPlayer *this, char byState, char by<PERSON>rade, char byEff<PERSON>ub<PERSON><PERSON><PERSON>, char byEff<PERSON>ubD<PERSON><PERSON>, char by<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, char byEffAddDefence)
{
  CPlayer::SendMsg_GuildMasterEffect(
    this,
    byState,
    byGrade,
    byEffSubAttack,
    byEffSubDefence,
    byEffAddAttack,
    byEffAddDefence);
}
