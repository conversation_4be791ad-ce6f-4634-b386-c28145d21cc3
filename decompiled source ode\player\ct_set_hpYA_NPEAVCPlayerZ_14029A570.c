/*
 * Function: ?ct_set_hp@@YA_NPEAVCPlayer@@@Z
 * Address: 0x14029A570
 */

char __fastcall ct_set_hp(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  float v4; // xmm0_4@9
  __int64 v5; // [sp+0h] [bp-38h]@1
  float v6; // [sp+20h] [bp-18h]@9
  float v7; // [sp+24h] [bp-14h]@12
  CPlayer *v8; // [sp+40h] [bp+8h]@1

  v8 = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v8 && v8->m_bOper )
  {
    if ( s_nWordCount >= 1 )
    {
      v4 = atof(s_pwszDstCheat[0]);
      v6 = v4;
      if ( v4 > 0.0 && v6 <= 100.0 )
      {
        v7 = v6 / 100.0;
        CPlayer::dev_set_hp(v8, v6 / 100.0);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
