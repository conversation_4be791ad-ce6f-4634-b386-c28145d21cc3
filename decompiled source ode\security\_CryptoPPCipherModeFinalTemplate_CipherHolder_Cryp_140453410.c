/*
 * Function: _CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_0_CryptoPP::Rijndael::Enc__CryptoPP::CBC_Encryption_::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_0_CryptoPP::Rijndael::Enc__CryptoPP::CBC_Encryption__::_1_::dtor$0
 * Address: 0x140453410
 */

void __fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_0_CryptoPP::Rijndael::Enc__CryptoPP::CBC_Encryption_::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_0_CryptoPP::Rijndael::Enc__CryptoPP::CBC_Encryption__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  CryptoPP::ObjectHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::Rijndael::Enc>>::~ObjectHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::Rijndael::Enc>>((CryptoPP::ObjectHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::Rijndael::Enc> > *)(*(_QWORD *)(a2 + 64) + 72i64));
}
