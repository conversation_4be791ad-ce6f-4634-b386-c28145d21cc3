/*
 * Function: ?Loop@CNormalGuildBattleStateRoundReturnStartPos@GUILD_BATTLE@@MEAAHPEAVCNormalGuildBattle@2@@Z
 * Address: 0x1403F1DA0
 */

signed __int64 __fastcall GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos::Loop(GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos *this, GUILD_BATTLE::CNormalGuildBattle *pkBattle)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleLogger *v4; // rax@5
  signed __int64 result; // rax@5
  __int64 v6; // [sp+0h] [bp-48h]@1
  GUILD_BATTLE::CNormalGuildBattleGuild *v7; // [sp+20h] [bp-28h]@8
  GUILD_BATTLE::CNormalGuildBattleGuild *v8; // [sp+28h] [bp-20h]@8
  GUILD_BATTLE::CNormalGuildBattleField *pkField; // [sp+30h] [bp-18h]@8
  GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos *v10; // [sp+50h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattle *pkBattlea; // [sp+58h] [bp+10h]@1

  pkBattlea = pkBattle;
  v10 = this;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v10->m_pkTimer )
  {
    if ( CMyTimer::CountingTimer(v10->m_pkTimer) )
    {
      v7 = GUILD_BATTLE::CNormalGuildBattle::GetRed(pkBattlea);
      v8 = GUILD_BATTLE::CNormalGuildBattle::GetBlue(pkBattlea);
      pkField = GUILD_BATTLE::CNormalGuildBattle::GetField(pkBattlea);
      GUILD_BATTLE::CNormalGuildBattleGuild::ReturnStartPosAll(v7, pkField);
      GUILD_BATTLE::CNormalGuildBattleGuild::ReturnStartPosAll(v8, pkField);
      GUILD_BATTLE::CNormalGuildBattleStateRound::Log(
        (GUILD_BATTLE::CNormalGuildBattleStateRound *)&v10->vfptr,
        pkBattlea,
        "Loop : Return Start Pos");
      result = 2i64;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    v4 = GUILD_BATTLE::CNormalGuildBattle::GetLogger(pkBattle);
    GUILD_BATTLE::CNormalGuildBattleLogger::Log(
      v4,
      "CNormalGuildBattleStateRoundReturnStartPos::Loop( CNormalGuildBattle * pkBattle ) :  0 == m_pkTimer !");
    result = 0i64;
  }
  return result;
}
