/*
 * Function: j_??0?$_Vector_iterator@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEAA@PEAPEAVCMoveMapLimitInfo@@@Z
 * Address: 0x140006D6B
 */

void __fastcall std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, CMoveMapLimitInfo **_Ptr)
{
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(
    this,
    _Ptr);
}
