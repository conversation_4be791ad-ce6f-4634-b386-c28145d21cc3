/*
 * Function: ?PartyListInit@CPartyPlayer@@QEAAXXZ
 * Address: 0x140044DB0
 */

void __fastcall CPartyPlayer::PartyListInit(CPartyPlayer *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CPartyPlayer *v4; // [sp+20h] [bp+8h]@1

  v4 = this;
  v1 = &j;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  v4->m_pPartyBoss = 0i64;
  for ( j = 0; j < 8; ++j )
    v4->m_pPartyMember[j] = 0i64;
  v4->m_bLock = 0;
}
