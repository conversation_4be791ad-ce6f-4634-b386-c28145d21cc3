/*
 * Function: _CUnmannedTraderUserInfoTable::Find_::_1_::dtor$2
 * Address: 0x140365C60
 */

void __fastcall CUnmannedTraderUserInfoTable::Find_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::~_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>((std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)(a2 + 40));
}
