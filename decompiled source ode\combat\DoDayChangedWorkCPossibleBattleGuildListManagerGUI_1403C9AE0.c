/*
 * Function: ?DoDayChangedWork@CPossibleBattleGuildListManager@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403C9AE0
 */

void __fastcall GUILD_BATTLE::CPossibleBattleGuildListManager::DoDayChangedWork(GUILD_BATTLE::CPossibleBattleGuildListManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CPossibleBattleGuildListManager *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 500; ++j )
  {
    if ( CGuild::IsFill(&g_Guild[j]) )
      CGuild::ClearGuildBattle(&g_Guild[j]);
  }
  GUILD_BATTLE::CPossibleBattleGuildListManager::UpdateGuildList(v5);
}
