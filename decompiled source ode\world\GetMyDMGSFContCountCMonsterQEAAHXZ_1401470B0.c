/*
 * Function: ?GetMyDMGSFContCount@CMonster@@QEAAHXZ
 * Address: 0x1401470B0
 */

__int64 __fastcall CMonster::GetMyDMGSFContCount(CMonster *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // [sp+0h] [bp-28h]@1
  int j; // [sp+4h] [bp-24h]@4
  int k; // [sp+8h] [bp-20h]@6
  bool *v7; // [sp+10h] [bp-18h]@9
  CMonster *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v1 = (int *)&v4;
  for ( i = 8i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  v4 = 0;
  for ( j = 0; j < 2; ++j )
  {
    for ( k = 0; k < 8; ++k )
    {
      v7 = &v8->m_SFCont[j][k].m_bExist;
      if ( *v7 )
        ++v4;
    }
  }
  return v4;
}
