/*
 * Function: ?CheckSPF_MON_MOTIVE_DF@DfAIMgr@@SAHPEAVCMonsterSkill@@HPEAVCMonsterAI@@PEAVCMonster@@PEAPEAVCCharacter@@@Z
 * Address: 0x1401525F0
 */

signed __int64 __fastcall DfAIMgr::CheckSPF_MON_MOTIVE_DF(CMonsterSkill *pSkill, int nMotiveValue, CMonsterAI *pAI, CMonster *pMon, CCharacter **ppTar)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  int v8; // eax@9
  float v9; // xmm0_4@11
  float v10; // xmm0_4@13
  unsigned int v11; // eax@14
  float v12; // xmm1_4@14
  __int64 v13; // [sp+0h] [bp-48h]@1
  CCharacter *v14; // [sp+20h] [bp-28h]@9
  float v15; // [sp+28h] [bp-20h]@11
  float v16; // [sp+2Ch] [bp-1Ch]@13
  float v17; // [sp+30h] [bp-18h]@14
  float v18; // [sp+34h] [bp-14h]@14
  CMonsterSkill *pSkilla; // [sp+50h] [bp+8h]@1
  CMonsterAI *pAIa; // [sp+60h] [bp+18h]@1
  CMonster *pMona; // [sp+68h] [bp+20h]@1

  pMona = pMon;
  pAIa = pAI;
  pSkilla = pSkill;
  v5 = &v13;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( pMon && pAI && pSkilla && ppTar )
  {
    v8 = CMonsterSkill::GetDstCaseType(pSkilla);
    v14 = DfAIMgr::GetWisdomTarget(v8, pAIa, pMona);
    if ( v14 )
    {
      v9 = 0.0;
      v15 = 0.0;
      Get3DSqrt(pMona->m_fCurPos, v14->m_fCurPos);
      v15 = 0.0;
      if ( v14->m_bMove )
      {
        ((void (__fastcall *)(CMonster *))pMona->vfptr->GetAttackRange)(pMona);
        if ( 0.0 < 50.0 )
        {
          v10 = R3GetLoopTime() * 15.0;
          v16 = v10;
          CMonster::GetMoveSpeed(pMona);
          v9 = v15 - (float)((float)(v16 * v10) * 0.40000001);
          v15 = v9;
        }
      }
      CMonster::GetSkillDelayTime(pMona, pSkilla);
      v17 = v9;
      v18 = (float)(signed int)GetLoopTime();
      v11 = CMonsterSkill::GetBeforeTime(pSkilla);
      v12 = v18 - (float)(signed int)v11;
      if ( v12 >= v17 && (CMonsterSkill::GetAttackDist(pSkilla), v12 >= v15) && ppTar )
      {
        *ppTar = v14;
        result = 1i64;
      }
      else
      {
        result = 0i64;
      }
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
