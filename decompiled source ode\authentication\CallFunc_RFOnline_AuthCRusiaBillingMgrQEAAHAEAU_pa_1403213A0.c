/*
 * Function: ?CallFunc_RFOnline_Auth@CRusiaBillingMgr@@QEAAHAEAU_param_cash_select@@@Z
 * Address: 0x1403213A0
 */

__int64 __usercall CRusiaBillingMgr::CallFunc_RFOnline_Auth@<rax>(CRusiaBillingMgr *this@<rcx>, _param_cash_select *rParam@<rdx>, double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool v5; // zf@4
  __int64 v7; // [sp+0h] [bp-28h]@1
  _param_cash_select *v8; // [sp+38h] [bp+10h]@1

  v8 = rParam;
  v3 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CoInitialize(0i64);
  RFACC_CheckBalance(v8->in_szAcc);
  v8->out_dwCashAmount = (signed int)floor(a3);
  v5 = v8->out_dwCashAmount == 0;
  CoUninitialize();
  return 0i64;
}
