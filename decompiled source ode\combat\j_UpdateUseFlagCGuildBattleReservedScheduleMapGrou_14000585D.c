/*
 * Function: j_?UpdateUseFlag@CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAAPEAVCGuildBattleSchedule@2@IK@Z
 * Address: 0x14000585D
 */

GUILD_BATTLE::CGuildBattleSchedule *__fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::UpdateUseFlag(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this, unsigned int uiMapID, unsigned int dwID)
{
  return GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::UpdateUseFlag(this, uiMapID, dwID);
}
