/*
 * Function: ?GetCircleZone@CNormalGuildBattleFieldList@GUILD_BATTLE@@QEAAPEAVCCircleZone@@H@Z
 * Address: 0x1403EED90
 */

CCircleZone *__fastcall GUILD_BATTLE::CNormalGuildBattleFieldList::GetCircleZone(GUILD_BATTLE::CNormalGuildBattleFieldList *this, int iInx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  CCircleZone *v6; // [sp+20h] [bp-18h]@4
  unsigned int j; // [sp+28h] [bp-10h]@4
  GUILD_BATTLE::CNormalGuildBattleFieldList *v8; // [sp+40h] [bp+8h]@1
  int iInxa; // [sp+48h] [bp+10h]@1

  iInxa = iInx;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0i64;
  for ( j = 0; j < v8->m_dwCnt; ++j )
  {
    v6 = GUILD_BATTLE::CNormalGuildBattleField::GetCircleZone(&v8->m_pkField[j], iInxa);
    if ( v6 )
      return v6;
  }
  return 0i64;
}
