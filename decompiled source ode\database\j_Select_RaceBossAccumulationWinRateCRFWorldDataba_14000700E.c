/*
 * Function: j_?Select_RaceBossAccumulationWinRate@CRFWorldDatabase@@QEAAEEKPEAU_raceboss_acc_winrate@@@Z
 * Address: 0x14000700E
 */

char __fastcall CRFWorldDatabase::Select_RaceBossAccumulationWinRate(CRFWorldDatabase *this, char byRace, unsigned int dwBossSerial, _raceboss_acc_winrate *windata)
{
  return CRFWorldDatabase::Select_RaceBossAccumulationWinRate(this, byRace, dwBossSerial, windata);
}
