/*
 * Function: ?SF_SPDec@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 * Address: 0x14009FB60
 */

char __fastcall CPlayer::SF_SPDec(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@6
  int nSP; // [sp+24h] [bp-14h]@8
  CPlayer *v9; // [sp+48h] [bp+10h]@1

  v9 = (CPlayer *)pDstObj;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pDstObj->m_ObjID.m_byID )
  {
    result = 0;
  }
  else
  {
    v7 = CPlayer::GetSP((CPlayer *)pDstObj);
    if ( v7 >= 1 )
    {
      nSP = (signed int)ffloor((float)v7 * fEffectValue);
      if ( nSP <= 1 )
        nSP = 1;
      CPlayer::SetSP(v9, nSP, 0);
      CPlayer::SendMsg_Recover(v9);
      result = 1;
    }
    else
    {
      result = 1;
    }
  }
  return result;
}
