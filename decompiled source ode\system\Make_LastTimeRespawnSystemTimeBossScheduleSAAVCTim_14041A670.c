/*
 * Function: ?Make_LastTimeRespawnSystemTime@BossSchedule@@SA?AVCTime@ATL@@PEAD@Z
 * Address: 0x14041A670
 */

ATL::CTime *__fastcall BossSchedule::Make_LastTimeRespawnSystemTime(ATL::CTime *result, char *strTimeValue)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  ATL::CTime *v4; // rax@5
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v5; // rax@8
  const char *v6; // rax@8
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v7; // rax@8
  const char *v8; // rax@8
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v9; // rax@8
  const char *v10; // rax@8
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v11; // rax@8
  const char *v12; // rax@8
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v13; // rax@8
  const char *v14; // rax@8
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v15; // rax@8
  const char *v16; // rax@8
  int v17; // eax@8
  __int64 v18; // [sp+0h] [bp-A8h]@1
  std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > stringlist; // [sp+48h] [bp-60h]@6
  __int64 v20; // [sp+78h] [bp-30h]@4
  int v21; // [sp+80h] [bp-28h]@8
  int v22; // [sp+84h] [bp-24h]@8
  int v23; // [sp+88h] [bp-20h]@8
  int nDay; // [sp+8Ch] [bp-1Ch]@8
  int nMonth; // [sp+90h] [bp-18h]@8
  ATL::CTime *v26; // [sp+B0h] [bp+8h]@1
  char *strSrc; // [sp+B8h] [bp+10h]@1

  strSrc = strTimeValue;
  v26 = result;
  v2 = &v18;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v20 = -2i64;
  if ( strTimeValue )
  {
    std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(&stringlist);
    if ( SplitString(strSrc, "-", &stringlist) == 7 )
    {
      v5 = std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::operator[](
             &stringlist,
             5ui64);
      LODWORD(v6) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::c_str(v5);
      v21 = atoi(v6);
      v7 = std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::operator[](
             &stringlist,
             4ui64);
      LODWORD(v8) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::c_str(v7);
      v22 = atoi(v8);
      v9 = std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::operator[](
             &stringlist,
             3ui64);
      LODWORD(v10) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::c_str(v9);
      v23 = atoi(v10);
      v11 = std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::operator[](
              &stringlist,
              2ui64);
      LODWORD(v12) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::c_str(v11);
      nDay = atoi(v12);
      v13 = std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::operator[](
              &stringlist,
              1ui64);
      LODWORD(v14) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::c_str(v13);
      nMonth = atoi(v14);
      v15 = std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::operator[](
              &stringlist,
              0i64);
      LODWORD(v16) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::c_str(v15);
      v17 = atoi(v16);
      ATL::CTime::CTime(v26, v17, nMonth, nDay, v23, v22, v21, -1);
      std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(&stringlist);
      v4 = v26;
    }
    else
    {
      ATL::CTime::CTime(v26, 0i64);
      std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(&stringlist);
      v4 = v26;
    }
  }
  else
  {
    ATL::CTime::CTime(v26, 0i64);
    v4 = v26;
  }
  return v4;
}
