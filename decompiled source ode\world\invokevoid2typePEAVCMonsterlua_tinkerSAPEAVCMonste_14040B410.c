/*
 * Function: ?invoke@?$void2type@PEAVCMonster@@@lua_tinker@@SAPEAVCMonster@@PEAX@Z
 * Address: 0x14040B410
 */

CMonster *__fastcall lua_tinker::void2type<CMonster *>::invoke(lua_tinker::void2type<CMonster *> *this, void *ptr)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  lua_tinker::void2type<CMonster *> *input; // [sp+30h] [bp+8h]@1

  input = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return lua_tinker::void2ptr<CMonster>::invoke(input);
}
