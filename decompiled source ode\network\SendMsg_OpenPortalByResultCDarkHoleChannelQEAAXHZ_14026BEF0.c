/*
 * Function: ?SendMsg_OpenPortalByResult@CDarkHoleChannel@@QEAAXH@Z
 * Address: 0x14026BEF0
 */

void __fastcall CDarkHoleChannel::SendMsg_OpenPortalByResult(CDarkHoleChannel *this, int nPortalIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _portal_dummy *v4; // rcx@10
  unsigned __int16 v5; // ax@16
  __int64 v6; // [sp+0h] [bp-148h]@1
  _darkhole_open_all_portal_by_result_inform_zocl v7; // [sp+40h] [bp-108h]@4
  char pbyType; // [sp+E4h] [bp-64h]@4
  char v9; // [sp+E5h] [bp-63h]@4
  CMapData *v10; // [sp+F8h] [bp-50h]@5
  int v11; // [sp+100h] [bp-48h]@8
  int j; // [sp+104h] [bp-44h]@8
  int k; // [sp+108h] [bp-40h]@13
  _dh_player_mgr *v14; // [sp+110h] [bp-38h]@15
  int v15; // [sp+120h] [bp-28h]@6
  __dp_mission_potal *v16; // [sp+128h] [bp-20h]@10
  unsigned __int64 v17; // [sp+130h] [bp-18h]@4
  CDarkHoleChannel *v18; // [sp+150h] [bp+8h]@1

  v18 = this;
  v2 = &v6;
  for ( i = 80i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v17 = (unsigned __int64)&v6 ^ _security_cookie;
  _darkhole_open_all_portal_by_result_inform_zocl::_darkhole_open_all_portal_by_result_inform_zocl(&v7);
  pbyType = 35;
  v9 = 8;
  if ( v18->m_MissionMgr.pCurMssionPtr )
  {
    v10 = v18->m_pQuestSetup->pUseMap;
    if ( v10->m_nPortalNum >= 128 )
      v15 = 128;
    else
      v15 = v10->m_nPortalNum;
    v11 = v15;
    for ( j = 0; j < v11; ++j )
    {
      v4 = v10->m_pPortal;
      v16 = &v18->m_MissionMgr.pCurMssionPtr->facMissionPotal;
      if ( __dp_mission_potal::find(v16, v4[j].m_pDumPos->m_szCode) )
      {
        v7.byPotalIndex[(unsigned __int8)v7.byCnt] = j;
        ++v7.byCnt;
      }
    }
    for ( k = 0; k < 32; ++k )
    {
      v14 = &v18->m_Quester[k];
      if ( _dh_player_mgr::IsFill(v14) )
      {
        v5 = _darkhole_open_all_portal_by_result_inform_zocl::size(&v7);
        CNetProcess::LoadSendMsg(unk_1414F2088, v18->m_Quester[k].pOne->m_ObjID.m_wIndex, &pbyType, &v7.byCnt, v5);
      }
    }
  }
}
