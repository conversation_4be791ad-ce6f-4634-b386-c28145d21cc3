/*
 * Function: ?DownGradeItemRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CAC80
 */

char __fastcall CNetworkEX::DownGradeItemRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@9
  char *v7; // rax@11
  char *v8; // rax@14
  __int64 v9; // [sp+0h] [bp-38h]@1
  _STORAGE_POS_INDIV *pposUpgItem; // [sp+20h] [bp-18h]@4
  CPlayer *v11; // [sp+28h] [bp-10h]@4
  CNetworkEX *v12; // [sp+40h] [bp+8h]@1

  v12 = this;
  v3 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pposUpgItem = (_STORAGE_POS_INDIV *)pBuf;
  v11 = &g_Player + n;
  if ( !v11->m_bOper || v11->m_pmTrd.bDTradeMode || v11->m_bCorpse )
  {
    result = 1;
  }
  else if ( pposUpgItem[1].byStorageCode )
  {
    v6 = CPlayerDB::GetCharNameA(&v11->m_Param);
    CLogFile::Write(
      &v12->m_LogFile,
      "odd.. %s: DownGradeItemRequest()..  if(pRecv->m_posTalik.byStorageCode != _STORAGE_POS::INVEN)",
      v6);
    result = 0;
  }
  else if ( pposUpgItem[2].byStorageCode )
  {
    v7 = CPlayerDB::GetCharNameA(&v11->m_Param);
    CLogFile::Write(
      &v12->m_LogFile,
      "odd.. %s: DownGradeItemRequest()..  if(pRecv->m_posToolItem.byStorageCode != _STORAGE_POS::INVEN)",
      v7);
    result = 0;
  }
  else if ( pposUpgItem->byStorageCode && pposUpgItem->byStorageCode != 1 )
  {
    v8 = CPlayerDB::GetCharNameA(&v11->m_Param);
    CLogFile::Write(
      &v12->m_LogFile,
      "odd.. %s: DownGradeItemRequest()..  if(pRecv->m_posUpgItem.byStorageCode != _STORAGE_POS::INVEN && pRecv->m_posUpg"
      "Item.byStorageCode != _STORAGE_POS::EQUIP)",
      v8);
    result = 0;
  }
  else
  {
    CPlayer::pc_DowngradeItem(v11, pposUpgItem + 1, pposUpgItem + 2, pposUpgItem);
    result = 1;
  }
  return result;
}
