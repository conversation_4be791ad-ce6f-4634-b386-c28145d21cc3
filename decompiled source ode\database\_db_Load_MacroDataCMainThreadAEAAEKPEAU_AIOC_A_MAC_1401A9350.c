/*
 * Function: ?_db_Load_MacroData@CMainThread@@AEAAEKPEAU_AIOC_A_MACRODATA@@@Z
 * Address: 0x1401A9350
 */

char __fastcall CMainThread::_db_Load_MacroData(CMainThread *this, unsigned int dwSerial, _AIOC_A_MACRODATA *pMacro)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  char v7; // [sp+20h] [bp-18h]@4
  int j; // [sp+24h] [bp-14h]@7
  CMainThread *v9; // [sp+40h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+48h] [bp+10h]@1
  _AIOC_A_MACRODATA *Dst; // [sp+50h] [bp+18h]@1

  Dst = pMacro;
  dwSeriala = dwSerial;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = 0;
  memset_0(pMacro, 0, 0xA90ui64);
  v7 = CRFWorldDatabase::Select_MacroData(v9->m_pWorldDB, dwSeriala, Dst);
  if ( v7 == 2 )
  {
    if ( !CRFWorldDatabase::Insert_MacroData(v9->m_pWorldDB, dwSeriala) )
      return 24;
    for ( j = 0; j < 1; ++j )
    {
      Dst->mcr_Potion[j].Potion[0] = -1;
      Dst->mcr_Potion[j].Potion[1] = -1;
      Dst->mcr_Potion[j].Potion[2] = -1;
      Dst->mcr_Potion[j].PotionValue[0] = 0;
      Dst->mcr_Potion[j].PotionValue[1] = 0;
      Dst->mcr_Potion[j].PotionValue[2] = 0;
    }
    for ( j = 0; j < 3; ++j )
    {
      Dst->mcr_Action[j].Action[0] = -1;
      Dst->mcr_Action[j].Action[1] = -1;
      Dst->mcr_Action[j].Action[2] = -1;
      Dst->mcr_Action[j].Action[3] = -1;
      Dst->mcr_Action[j].Action[4] = -1;
      Dst->mcr_Action[j].Action[5] = -1;
      Dst->mcr_Action[j].Action[6] = -1;
      Dst->mcr_Action[j].Action[7] = -1;
      Dst->mcr_Action[j].Action[8] = -1;
      Dst->mcr_Action[j].Action[9] = -1;
    }
    for ( j = 0; j < 2; ++j )
    {
      memset_0(&Dst->mcr_Chat[j], 0, 0x100ui64);
      memset_0(Dst->mcr_Chat[j].Chat[1], 0, 0x100ui64);
      memset_0(Dst->mcr_Chat[j].Chat[2], 0, 0x100ui64);
      memset_0(Dst->mcr_Chat[j].Chat[3], 0, 0x100ui64);
      memset_0(Dst->mcr_Chat[j].Chat[4], 0, 0x100ui64);
    }
  }
  else if ( v7 == 1 )
  {
    return 24;
  }
  return 0;
}
