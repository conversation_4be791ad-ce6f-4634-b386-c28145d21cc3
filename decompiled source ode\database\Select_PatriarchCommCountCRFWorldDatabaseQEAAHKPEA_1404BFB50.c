/*
 * Function: ?Select_PatriarchCommCount@CRFWorldDatabase@@QEAAHKPEADAEAK@Z
 * Address: 0x1404BFB50
 */

signed __int64 __fastcall CRFWorldDatabase::Select_PatriarchCommCount(CRFWorldDatabase *this, unsigned int dwSerial, char *pszDate, unsigned int *dwCnt)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v7; // [sp+0h] [bp-108h]@1
  void *SQLStmt; // [sp+20h] [bp-E8h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-E0h]@22
  SQLLEN v10; // [sp+38h] [bp-D0h]@22
  __int16 v11; // [sp+44h] [bp-C4h]@9
  char Dest; // [sp+60h] [bp-A8h]@4
  unsigned __int8 v13; // [sp+E4h] [bp-24h]@16
  unsigned __int8 v14; // [sp+E5h] [bp-23h]@24
  unsigned __int64 v15; // [sp+F0h] [bp-18h]@4
  CRFWorldDatabase *v16; // [sp+110h] [bp+8h]@1
  unsigned int *TargetValue; // [sp+128h] [bp+20h]@1

  TargetValue = dwCnt;
  v16 = this;
  v4 = &v7;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v15 = (unsigned __int64)&v7 ^ _security_cookie;
  sprintf(
    &Dest,
    "SELECT COUNT(*) FROM [dbo].[tbl_patriarch_comm] WHERE AvatorSerial=%d AND DepositDate='%s' AND DCK=0",
    dwSerial,
    pszDate);
  if ( v16->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v16->vfptr, &Dest);
  if ( v16->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v16->vfptr) )
  {
    v11 = SQLExecDirectA_0(v16->m_hStmtSelect, &Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v16->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v11 = SQLFetch_0(v16->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        v13 = 0;
        if ( v11 == 100 )
        {
          v13 = 2;
        }
        else
        {
          SQLStmt = v16->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
          v13 = 1;
        }
        if ( v16->m_hStmtSelect )
          SQLCloseCursor_0(v16->m_hStmtSelect);
        result = v13;
      }
      else
      {
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 1u, -6, TargetValue, 0i64, &v10);
        if ( v11 && v11 != 1 )
        {
          v14 = 0;
          if ( v11 == 100 )
          {
            v14 = 2;
          }
          else
          {
            SQLStmt = v16->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &Dest, "SQLFetch", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
            v14 = 1;
          }
          if ( v16->m_hStmtSelect )
            SQLCloseCursor_0(v16->m_hStmtSelect);
          result = v14;
        }
        else
        {
          if ( v16->m_hStmtSelect )
            SQLCloseCursor_0(v16->m_hStmtSelect);
          if ( v16->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v16->vfptr, "%s Success", &Dest);
          result = 0i64;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v16->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1i64;
  }
  return result;
}
