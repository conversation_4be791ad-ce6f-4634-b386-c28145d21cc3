/*
 * Function: ?GetWinnerGrade@CBattleTournamentInfo@@QEAAEKPEAD@Z
 * Address: 0x1403FEC30
 */

char __fastcall CBattleTournamentInfo::GetWinnerGrade(CBattleTournamentInfo *this, unsigned int dwSerial, char *pwszCharName)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@7
  CBattleTournamentInfo *v8; // [sp+40h] [bp+8h]@1
  unsigned int v9; // [sp+48h] [bp+10h]@1
  const char *Str2; // [sp+50h] [bp+18h]@1

  Str2 = pwszCharName;
  v9 = dwSerial;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v8->m_bLoad && v8->m_nCurNum > 0 )
  {
    for ( j = 0; j < v8->m_nCurNum; ++j )
    {
      if ( v8->m_WinnerInfo[j].dwSerial == v9 && !strcmp_0(v8->m_WinnerInfo[j].wszCharName, Str2) )
        return v8->m_WinnerInfo[j].byGrade;
    }
    result = -1;
  }
  else
  {
    result = -1;
  }
  return result;
}
