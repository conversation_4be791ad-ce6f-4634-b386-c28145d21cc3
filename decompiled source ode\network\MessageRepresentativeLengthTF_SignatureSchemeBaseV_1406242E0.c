/*
 * Function: ?MessageRepresentativeLength@?$TF_SignatureSchemeBase@VPK_Signer@CryptoPP@@V?$TF_Base@VRandomizedTrapdoorFunctionInverse@CryptoPP@@VPK_SignatureMessageEncodingMethod@2@@2@@CryptoPP@@IEBA_KXZ
 * Address: 0x1406242E0
 */

unsigned __int64 __fastcall CryptoPP::TF_SignatureSchemeBase<CryptoPP::PK_Signer,CryptoPP::TF_Base<CryptoPP::RandomizedTrapdoorFunctionInverse,CryptoPP::PK_SignatureMessageEncodingMethod>>::MessageRepresentativeLength(__int64 a1)
{
  CryptoPP *v1; // rax@1

  LODWORD(v1) = CryptoPP::TF_SignatureSchemeBase<CryptoPP::PK_Signer,CryptoPP::TF_Base<CryptoPP::RandomizedTrapdoorFunctionInverse,CryptoPP::PK_SignatureMessageEncodingMethod>>::MessageRepresentativeBitLength(a1);
  return CryptoPP::BitsToBytes(v1);
}
