/*
 * Function: ?gm_MonsterInit@CMainThread@@QEAA_NPEAVCCharacter@@@Z
 * Address: 0x1401F7A40
 */

unsigned __int8 __fastcall CMainThread::gm_MonsterInit(CMainThread *this, CCharacter *pExt)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  int j; // [sp+30h] [bp-18h]@5
  int v7; // [sp+34h] [bp-14h]@4
  CCharacter *v8; // [sp+58h] [bp+10h]@1

  v8 = pExt;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  byte_141471E98[0] = byte_141471E98[0] == 0;
  v7 = byte_141471E98[0];
  if ( !byte_141471E98[0] )
  {
    for ( j = 0; j < 30000; ++j )
    {
      if ( *((_BYTE *)g_Monster + 6424 * j + 24) )
      {
        if ( (char *)v8 != (char *)g_Monster + 6424 * j )
          CMonster::Destroy((CMonster *)g_Monster + j, 1, 0i64);
      }
    }
  }
  if ( (unsigned int)CPropertySheet::GetActiveIndex((CPropertySheet *)&g_pDoc->m_InfoSheet.vfptr) == 3 )
    CMsgData::PackingMsg(&stru_1415B7048, 0x3EEu, 0, 0, 0);
  return byte_141471E98[0];
}
