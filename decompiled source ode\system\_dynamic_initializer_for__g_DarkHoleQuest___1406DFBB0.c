/*
 * Function: _dynamic_initializer_for__g_DarkHoleQuest__
 * Address: 0x1406DFBB0
 */

__int64 dynamic_initializer_for__g_DarkHoleQuest__()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1

  v0 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  CDarkHoleDungeonQuest::CDarkHoleDungeonQuest(&g_DarkHoleQuest);
  return atexit(dynamic_atexit_destructor_for__g_DarkHoleQuest__);
}
