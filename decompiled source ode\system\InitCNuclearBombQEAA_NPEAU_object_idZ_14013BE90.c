/*
 * Function: ?Init@CNuclearBomb@@QEAA_NPEAU_object_id@@@Z
 * Address: 0x14013BE90
 */

char __fastcall CNuclearBomb::Init(CNuclearBomb *this, _object_id *pID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CNuclearBomb *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CCharacter::Init((CCharacter *)&v6->vfptr, pID);
  v6->m_pMaster = 0i64;
  v6->m_bUse = 0;
  v6->m_dwDelayTime = 0;
  v6->m_dwDurTime = 0;
  v6->m_dwStartTime = 0;
  v6->m_nStartDmLoop = 0;
  return 1;
}
