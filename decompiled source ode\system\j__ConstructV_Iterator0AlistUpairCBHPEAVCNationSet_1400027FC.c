/*
 * Function: j_??$_Construct@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@std@@V123@@std@@YAXPEAV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@0@AEBV120@@Z
 * Address: 0x1400027FC
 */

void __fastcall std::_Construct<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>,std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>>(std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *_Ptr, std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *_Val)
{
  std::_Construct<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>,std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>>(
    _Ptr,
    _Val);
}
