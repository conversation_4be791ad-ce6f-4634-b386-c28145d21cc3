/*
 * Function: ??1?$simple_ptr@VDL_SignatureMessageEncodingMethod_DSA@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x14058EB80
 */

int __fastcall CryptoPP::simple_ptr<CryptoPP::DL_SignatureMessageEncodingMethod_DSA>::~simple_ptr<CryptoPP::DL_SignatureMessageEncodingMethod_DSA>(_QWORD *a1)
{
  int (__fastcall ***v1)(_QWORD, _QWORD); // rax@1

  v1 = (int (__fastcall ***)(_QWORD, _QWORD))*a1;
  if ( *a1 )
    LODWORD(v1) = (**(int (__fastcall ***)(int (__fastcall ***)(_QWORD, _QWORD), signed __int64))*a1)(
                    (int (__fastcall ***)(_QWORD, _QWORD))*a1,
                    1i64);
  return (unsigned __int64)v1;
}
