/*
 * Function: ?NetworkInit@CMainThread@@AEAA_NXZ
 * Address: 0x1401EB330
 */

char __fastcall CMainThread::NetworkInit(CMainThread *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@9
  __int64 v4; // [sp+0h] [bp-3F8h]@1
  char __t; // [sp+40h] [bp-3B8h]@4
  int v6; // [sp+44h] [bp-3B4h]@4
  __int16 v7; // [sp+48h] [bp-3B0h]@6
  __int16 v8; // [sp+4Ah] [bp-3AEh]@4
  int v9; // [sp+54h] [bp-3A4h]@6
  char v10; // [sp+58h] [bp-3A0h]@5
  int v11; // [sp+5Ch] [bp-39Ch]@7
  char v12; // [sp+64h] [bp-394h]@8
  int v13; // [sp+68h] [bp-390h]@7
  int v14; // [sp+6Ch] [bp-38Ch]@6
  int v15; // [sp+70h] [bp-388h]@4
  int v16; // [sp+74h] [bp-384h]@4
  int v17; // [sp+80h] [bp-378h]@6
  int v18; // [sp+84h] [bp-374h]@6
  char v19; // [sp+88h] [bp-370h]@6
  char v20; // [sp+89h] [bp-36Fh]@6
  int v21; // [sp+A0h] [bp-358h]@6
  char Dest; // [sp+A4h] [bp-354h]@8
  int v23; // [sp+128h] [bp-2D0h]@8
  __int16 v24; // [sp+12Eh] [bp-2CAh]@8
  char v25; // [sp+13Ch] [bp-2BCh]@8
  int v26; // [sp+154h] [bp-2A4h]@8
  int v27; // [sp+158h] [bp-2A0h]@8
  int v28; // [sp+164h] [bp-294h]@8
  char v29; // [sp+16Ch] [bp-28Ch]@8
  int v30; // [sp+170h] [bp-288h]@8
  int v31; // [sp+174h] [bp-284h]@8
  char v32; // [sp+188h] [bp-270h]@8
  int v33; // [sp+20Ch] [bp-1ECh]@8
  __int16 v34; // [sp+210h] [bp-1E8h]@8
  __int16 v35; // [sp+212h] [bp-1E6h]@8
  char v36; // [sp+220h] [bp-1D8h]@8
  int v37; // [sp+238h] [bp-1C0h]@8
  int v38; // [sp+23Ch] [bp-1BCh]@8
  int v39; // [sp+248h] [bp-1B0h]@8
  char v40; // [sp+250h] [bp-1A8h]@8
  char v41; // [sp+251h] [bp-1A7h]@8
  int v42; // [sp+254h] [bp-1A4h]@8
  char v43; // [sp+26Ch] [bp-18Ch]@8
  int v44; // [sp+2F0h] [bp-108h]@8
  __int16 v45; // [sp+2F4h] [bp-104h]@8
  __int16 v46; // [sp+2F6h] [bp-102h]@8
  char v47; // [sp+304h] [bp-F4h]@8
  int v48; // [sp+308h] [bp-F0h]@8
  int v49; // [sp+30Ch] [bp-ECh]@8
  int v50; // [sp+31Ch] [bp-DCh]@8
  int v51; // [sp+320h] [bp-D8h]@8
  int v52; // [sp+32Ch] [bp-CCh]@8
  char v53; // [sp+334h] [bp-C4h]@8
  char v54; // [sp+335h] [bp-C3h]@8
  int v55; // [sp+338h] [bp-C0h]@8
  char v56; // [sp+350h] [bp-A8h]@8
  unsigned __int64 v57; // [sp+3E0h] [bp-18h]@4
  CMainThread *v58; // [sp+400h] [bp+8h]@1

  v58 = this;
  v1 = &v4;
  for ( i = 252i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v57 = (unsigned __int64)&v4 ^ _security_cookie;
  `vector constructor iterator'(&__t, 0xE4ui64, 4, (void *(__cdecl *)(void *))_NET_TYPE_PARAM::_NET_TYPE_PARAM);
  v6 = 1;
  v8 = 2532;
  v15 = 1;
  v16 = 1;
  if ( CMainThread::IsReleaseServiceMode(v58) )
    v10 = 8;
  v19 = 10;
  v20 = 2;
  v7 = 27780;
  v14 = 1;
  v17 = 1;
  v9 = 30000;
  v18 = 1;
  v21 = 20;
  if ( CMainThread::IsReleaseServiceMode(v58) )
  {
    v11 = 40000;
    v13 = 10000;
  }
  v12 = 1;
  sprintf(&Dest, "ClientLine");
  v23 = 0;
  v31 = 1;
  v24 = 1;
  v30 = 1;
  v26 = 1;
  v27 = 1;
  v25 = 1;
  v29 = 1;
  v28 = 1;
  sprintf(&v32, "AccountLine");
  v33 = 1;
  v34 = 27555;
  v35 = 2;
  v42 = 1;
  v37 = 1;
  v38 = 1;
  v36 = 1;
  v40 = 1;
  v41 = 1;
  v39 = 1;
  sprintf(&v43, "WebLine");
  v44 = 1;
  v45 = 27556;
  v46 = 1;
  v55 = 1;
  v50 = 1;
  v51 = 1;
  v47 = 1;
  v53 = 1;
  v54 = 1;
  v52 = 1;
  v48 = 1000000;
  v49 = 1000000;
  sprintf(&v56, "BillingLine");
  if ( CNetWorking::SetNetSystem(
         (CNetWorking *)&g_Network.vfptr,
         4u,
         (_NET_TYPE_PARAM *)&__t,
         "GameServer",
         "..\\ZoneServerLog\\NetLog") )
  {
    CMainThread::AddPassablePacket(v58);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
