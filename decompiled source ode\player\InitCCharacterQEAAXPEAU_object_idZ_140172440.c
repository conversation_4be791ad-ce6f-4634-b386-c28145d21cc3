/*
 * Function: ?Init@<PERSON>haracter@@QEAAXPEAU_object_id@@@Z
 * Address: 0x140172440
 */

void __fastcall CCharacter::Init(CCharacter *this, _object_id *pID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CCharacter *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CGameObject::Init((CGameObject *)&v6->vfptr, pID);
  LODWORD(v6->m_fTarPos[0]) = 0;
  LODWORD(v6->m_fTarPos[1]) = 0;
  LODWORD(v6->m_fTarPos[2]) = 0;
  v6->m_AroundNum = 0;
  for ( j = 0; j < 5; ++j )
    v6->m_AroundSlot[j] = 0i64;
  v6->m_dwNextGenAttackTime = -1;
  v6->m_dwEffSerialCounter = 0;
  v6->m_dwEffSerialCounter = 0;
  v6->m_wLastContEffect = -1;
  v6->m_wEffectTempValue = 0;
  v6->m_dwPlayerSerial = 0;
  memset_0(v6->m_wszPlayerName, 0, 0x11ui64);
  v6->m_nContEffectSec = -1;
  _effect_parameter::InitEffParam(&v6->m_EP);
  memset_0(v6->m_SFCont, 0, 0x300ui64);
  memset_0(v6->m_SFContAura, 0, 0x300ui64);
  v6->m_bLastContEffectUpdate = 0;
  v6->m_wLastContEffect = -1;
}
