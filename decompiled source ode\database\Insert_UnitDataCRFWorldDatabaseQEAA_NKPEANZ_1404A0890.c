/*
 * Function: ?Insert_UnitData@CRFWorldDatabase@@QEAA_NKPEAN@Z
 * Address: 0x1404A0890
 */

bool __fastcall CRFWorldDatabase::Insert_UnitData(CRFWorldDatabase *this, unsigned int dwSerial, long double *pUnitData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-468h]@1
  __int64 v7; // [sp+20h] [bp-448h]@4
  char Dest; // [sp+40h] [bp-428h]@4
  unsigned __int64 v9; // [sp+450h] [bp-18h]@4
  CRFWorldDatabase *v10; // [sp+470h] [bp+8h]@1

  v10 = this;
  v3 = &v6;
  for ( i = 280i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = (unsigned __int64)&v6 ^ _security_cookie;
  v7 = *((_QWORD *)pUnitData + 1);
  sprintf(&Dest, "{ CALL pInsert_UnitData (%u, %.0f, %.0f) }", dwSerial, *(_QWORD *)pUnitData);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &Dest, 1);
}
