/*
 * Function: ?_buybygold_buy_single_item_setsenddata@CashItemRemoteStore@@AEAAXAEAU_db_con@_STORAGE_LIST@@AEAU_result_csi_buy_zocl@@@Z
 * Address: 0x140300650
 */

void __fastcall CashItemRemoteStore::_buybygold_buy_single_item_setsenddata(CashItemRemoteStore *this, _STORAGE_LIST::_db_con *GiveItem, _result_csi_buy_zocl *Send)
{
  Send->item[Send->nNum].byTblCode = GiveItem->m_byTableCode;
  Send->item[Send->nNum].wItemIdx = GiveItem->m_wItemIndex;
  Send->item[Send->nNum].dwDur = GiveItem->m_dwDur;
  Send->item[Send->nNum].dwUp = GiveItem->m_dwLv;
  Send->item[Send->nNum].dwItemSerial = GiveItem->m_wSerial;
  Send->item[Send->nNum].byCsMethod = GiveItem->m_byCsMethod;
  Send->item[Send->nNum++].dwT = GiveItem->m_dwT;
}
