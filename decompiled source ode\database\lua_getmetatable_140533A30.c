/*
 * Function: lua_getmetatable
 * Address: 0x140533A30
 */

signed __int64 __fastcall lua_getmetatable(__int64 a1, signed int a2)
{
  _DWORD *v2; // rax@1
  __int64 v3; // r10@1
  __int64 v4; // rcx@1
  __int64 v5; // rcx@3
  signed __int64 result; // rax@6
  __int64 v7; // rax@7

  v2 = sub_140532CD0(a1, a2);
  v4 = v2[2];
  if ( (_DWORD)v4 == 5 || (_DWORD)v4 == 7 )
    v5 = *(_QWORD *)(*(_QWORD *)v2 + 16i64);
  else
    v5 = *(_QWORD *)(*(_QWORD *)(v3 + 32) + 8 * v4 + 224);
  if ( v5 )
  {
    v7 = *(_QWORD *)(v3 + 16);
    *(_QWORD *)v7 = v5;
    *(_DWORD *)(v7 + 8) = 5;
    *(_QWORD *)(v3 + 16) += 16i64;
    result = 1i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
