/*
 * Function: ?pc_RefreshGroupTargetPosition@CPlayer@@QEAAXEPEAVCGameObject@@@Z
 * Address: 0x1400FE7D0
 */

void __fastcall CPlayer::pc_RefreshGroupTargetPosition(CPlayer *this, char byGroupType, CGameObject *pObject)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@19
  int v6; // eax@32
  int v7; // eax@39
  int v8; // eax@44
  __int64 v9; // [sp+0h] [bp-68h]@1
  int j; // [sp+20h] [bp-48h]@10
  CGameObject *v11; // [sp+28h] [bp-40h]@4
  CPartyPlayer **v12; // [sp+30h] [bp-38h]@4
  _guild_member_info *v13; // [sp+38h] [bp-30h]@4
  CGameObject *pObjecta; // [sp+40h] [bp-28h]@15
  CGameObject *v15; // [sp+48h] [bp-20h]@28
  CGameObject *v16; // [sp+50h] [bp-18h]@40
  char v17; // [sp+58h] [bp-10h]@5
  int v18; // [sp+5Ch] [bp-Ch]@39
  CPlayer *v19; // [sp+70h] [bp+8h]@1
  char v20; // [sp+78h] [bp+10h]@1

  v20 = byGroupType;
  v19 = this;
  v3 = &v9;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = 0i64;
  v12 = 0i64;
  v13 = 0i64;
  if ( pObject )
  {
    v17 = byGroupType;
    if ( byGroupType )
    {
      if ( v17 == 1 )
      {
        for ( j = 0; j < 50; ++j )
        {
          v13 = &v19->m_Param.m_pGuild->m_MemberData[j];
          if ( _guild_member_info::IsFill(v13) )
          {
            v11 = (CGameObject *)&v13->pPlayer->vfptr;
            if ( v11 )
            {
              if ( BYTE1(v11[9].m_SectorNetPoint.m_pNext) )
              {
                v15 = (CGameObject *)*((_QWORD *)&v11[257].m_SectorNetPoint.m_pPrev + 9 * (unsigned __int8)v20);
                if ( v15 )
                {
                  if ( v11->m_pCurMap == v15->m_pCurMap && v11->m_wMapLayerIndex == v15->m_wMapLayerIndex )
                  {
                    v6 = CGameObject::GetUseSectorRange(v11);
                    if ( !CGameObject::IsCircleObject(v11, v6, v15) )
                      CPlayer::SendMsg_RefeshGroupTargetPosition((CPlayer *)v11, v20);
                  }
                }
              }
            }
          }
        }
      }
      else if ( v17 == 2 )
      {
        for ( j = 0; j < 2532; ++j )
        {
          v11 = (CGameObject *)(&g_Player.vfptr + 6357 * j);
          if ( BYTE1(v11[9].m_SectorNetPoint.m_pNext) )
          {
            v18 = CPlayerDB::GetRaceCode((CPlayerDB *)&v11[10].m_bCorpse);
            v7 = CPlayerDB::GetRaceCode(&v19->m_Param);
            if ( v18 == v7 )
            {
              v16 = (CGameObject *)*((_QWORD *)&v11[257].m_SectorNetPoint.m_pPrev + 9 * (unsigned __int8)v20);
              if ( v16 )
              {
                if ( v11->m_pCurMap == v16->m_pCurMap && v11->m_wMapLayerIndex == v16->m_wMapLayerIndex )
                {
                  v8 = CGameObject::GetUseSectorRange(v11);
                  if ( !CGameObject::IsCircleObject(v11, v8, v16) )
                    CPlayer::SendMsg_RefeshGroupTargetPosition((CPlayer *)v11, v20);
                }
              }
            }
          }
        }
      }
    }
    else
    {
      v12 = CPartyPlayer::GetPtrPartyMember(v19->m_pPartyMgr);
      if ( v12 )
      {
        for ( j = 0; j < 8; ++j )
        {
          if ( v12[j] )
          {
            v11 = (CGameObject *)(&g_Player.vfptr + 6357 * v12[j]->m_id.wIndex);
            if ( BYTE1(v11[9].m_SectorNetPoint.m_pNext) )
            {
              pObjecta = (CGameObject *)&v11[257].m_SectorNetPoint.m_pPrev->m_pItem;
              if ( pObjecta )
              {
                if ( v11->m_pCurMap == pObjecta->m_pCurMap && v11->m_wMapLayerIndex == pObjecta->m_wMapLayerIndex )
                {
                  v5 = CGameObject::GetUseSectorRange(v11);
                  if ( !CGameObject::IsCircleObject(v11, v5, pObjecta) )
                    CPlayer::SendMsg_RefeshGroupTargetPosition((CPlayer *)v11, 0);
                }
              }
            }
          }
        }
      }
    }
  }
}
