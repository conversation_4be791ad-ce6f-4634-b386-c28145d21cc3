/*
 * Function: sub_140537DB0
 * Address: 0x140537DB0
 */

__int64 __fastcall sub_140537DB0(__int64 a1)
{
  __int64 v1; // rdi@1
  signed int v2; // esi@1
  signed int i; // ebx@1
  const char *v4; // rbp@2
  FILE *v5; // rax@4
  FILE *v6; // rax@5
  FILE *v7; // rax@6

  v1 = a1;
  v2 = lua_gettop(a1);
  lua_getfield(v1, -10002);
  for ( i = 1; i <= v2; ++i )
  {
    lua_pushvalue(v1, -1);
    lua_pushvalue(v1, i);
    lua_call(v1, 1, 1);
    v4 = (const char *)lua_tolstring(v1, -1, 0i64);
    if ( !v4 )
      luaL_error(v1, "'tostring' must return a string to 'print'");
    if ( i > 1 )
    {
      v5 = __iob_func();
      fputs("\t", v5 + 1);
    }
    v6 = __iob_func();
    fputs(v4, v6 + 1);
    lua_settop(v1, -2);
  }
  v7 = __iob_func();
  fputs("\n", v7 + 1);
  return 0i64;
}
