/*
 * Function: j_?SymmetricDecrypt@?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@CryptoPP@@UEBA?AUDecodingResult@2@PEBE0_KPEAEAEBVNameValuePairs@2@@Z
 * Address: 0x1400072E3
 */

CryptoPP::DecodingResult *__fastcall CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>::SymmetricDecrypt(CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *this, CryptoPP::DecodingResult *result, const char *key, const char *ciphertext, unsigned __int64 ciphertextLength, char *plaintext, CryptoPP::NameValuePairs *parameters)
{
  return CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>::SymmetricDecrypt(
           this,
           result,
           key,
           ciphertext,
           ciphertextLength,
           plaintext,
           parameters);
}
