/*
 * Function: j_?LoadXML@CUnmannedTraderSubClassInfo@@UEAA_NPEAVTiXmlElement@@AEAVCLogFile@@KK@Z
 * Address: 0x1400133F9
 */

bool __fastcall CUnmannedTraderSubClassInfo::LoadXML(CUnmannedTraderSubClassInfo *this, TiXmlElement *pkElement, CLogFile *kLogger, unsigned int dwDivisionID, unsigned int dwClassID)
{
  return CUnmannedTraderSubClassInfo::LoadXML(this, pkElement, kLogger, dwDivisionID, dwClassID);
}
