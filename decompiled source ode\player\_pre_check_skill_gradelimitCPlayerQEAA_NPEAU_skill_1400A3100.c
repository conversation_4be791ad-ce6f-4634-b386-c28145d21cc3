/*
 * Function: ?_pre_check_skill_gradelimit@CPlayer@@QEAA_NPEAU_skill_fld@@@Z
 * Address: 0x1400A3100
 */

char __fastcall CPlayer::_pre_check_skill_gradelimit(CPlayer *this, _skill_fld *pSkillFld)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CPvpUserAndGuildRankingSystem *v5; // rax@15
  unsigned int v6; // eax@15
  CPvpUserAndGuildRankingSystem *v7; // rax@18
  CPvpUserAndGuildRankingSystem *v8; // rax@21
  CPvpUserAndGuildRankingSystem *v9; // rax@24
  CPvpUserAndGuildRankingSystem *v10; // rax@27
  CPvpUserAndGuildRankingSystem *v11; // rax@30
  CPvpUserAndGuildRankingSystem *v12; // rax@33
  CPvpUserAndGuildRankingSystem *v13; // rax@36
  CPvpUserAndGuildRankingSystem *v14; // rax@39
  unsigned int v15; // eax@43
  __int64 v16; // [sp+0h] [bp-88h]@1
  char v17; // [sp+20h] [bp-68h]@6
  int j; // [sp+24h] [bp-64h]@6
  int v19; // [sp+28h] [bp-60h]@10
  unsigned int v20; // [sp+2Ch] [bp-5Ch]@15
  int v21; // [sp+30h] [bp-58h]@15
  unsigned int dwSerial; // [sp+34h] [bp-54h]@18
  int v23; // [sp+38h] [bp-50h]@18
  unsigned int v24; // [sp+3Ch] [bp-4Ch]@21
  int v25; // [sp+40h] [bp-48h]@21
  unsigned int v26; // [sp+44h] [bp-44h]@24
  int v27; // [sp+48h] [bp-40h]@24
  unsigned int v28; // [sp+4Ch] [bp-3Ch]@27
  int v29; // [sp+50h] [bp-38h]@27
  unsigned int v30; // [sp+54h] [bp-34h]@30
  int v31; // [sp+58h] [bp-30h]@30
  unsigned int v32; // [sp+5Ch] [bp-2Ch]@33
  int v33; // [sp+60h] [bp-28h]@33
  unsigned int v34; // [sp+64h] [bp-24h]@36
  int v35; // [sp+68h] [bp-20h]@36
  unsigned int v36; // [sp+6Ch] [bp-1Ch]@39
  int v37; // [sp+70h] [bp-18h]@39
  unsigned int v38; // [sp+74h] [bp-14h]@43
  CPlayer *v39; // [sp+90h] [bp+8h]@1
  _skill_fld *v40; // [sp+98h] [bp+10h]@1

  v40 = pSkillFld;
  v39 = this;
  v2 = &v16;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pSkillFld )
  {
    v17 = 0;
    for ( j = 0; j < 12; ++j )
    {
      if ( v40->m_strGradeLimit[j] == 49 )
      {
        v19 = j;
        switch ( j )
        {
          case 0:
            if ( v40->m_nClass != 4 && v40->m_nClass != 5 )
              v17 = 1;
            break;
          case 1:
            v20 = CPlayerDB::GetCharSerial(&v39->m_Param);
            v21 = CPlayerDB::GetRaceCode(&v39->m_Param);
            v5 = CPvpUserAndGuildRankingSystem::Instance();
            v6 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v5, v21, 0);
            if ( v20 == v6 )
              v17 = 1;
            break;
          case 2:
            dwSerial = CPlayerDB::GetCharSerial(&v39->m_Param);
            v23 = CPlayerDB::GetRaceCode(&v39->m_Param);
            v7 = CPvpUserAndGuildRankingSystem::Instance();
            if ( CPvpUserAndGuildRankingSystem::GetBossType(v7, v23, dwSerial) == 1 )
              v17 = 1;
            break;
          case 3:
            v24 = CPlayerDB::GetCharSerial(&v39->m_Param);
            v25 = CPlayerDB::GetRaceCode(&v39->m_Param);
            v8 = CPvpUserAndGuildRankingSystem::Instance();
            if ( CPvpUserAndGuildRankingSystem::GetBossType(v8, v25, v24) == 5 )
              v17 = 1;
            break;
          case 4:
            v26 = CPlayerDB::GetCharSerial(&v39->m_Param);
            v27 = CPlayerDB::GetRaceCode(&v39->m_Param);
            v9 = CPvpUserAndGuildRankingSystem::Instance();
            if ( CPvpUserAndGuildRankingSystem::GetBossType(v9, v27, v26) == 2 )
              v17 = 1;
            break;
          case 5:
            v28 = CPlayerDB::GetCharSerial(&v39->m_Param);
            v29 = CPlayerDB::GetRaceCode(&v39->m_Param);
            v10 = CPvpUserAndGuildRankingSystem::Instance();
            if ( CPvpUserAndGuildRankingSystem::GetBossType(v10, v29, v28) == 6 )
              v17 = 1;
            break;
          case 6:
            v30 = CPlayerDB::GetCharSerial(&v39->m_Param);
            v31 = CPlayerDB::GetRaceCode(&v39->m_Param);
            v11 = CPvpUserAndGuildRankingSystem::Instance();
            if ( CPvpUserAndGuildRankingSystem::GetBossType(v11, v31, v30) == 3 )
              v17 = 1;
            break;
          case 7:
            v32 = CPlayerDB::GetCharSerial(&v39->m_Param);
            v33 = CPlayerDB::GetRaceCode(&v39->m_Param);
            v12 = CPvpUserAndGuildRankingSystem::Instance();
            if ( CPvpUserAndGuildRankingSystem::GetBossType(v12, v33, v32) == 7 )
              v17 = 1;
            break;
          case 8:
            v34 = CPlayerDB::GetCharSerial(&v39->m_Param);
            v35 = CPlayerDB::GetRaceCode(&v39->m_Param);
            v13 = CPvpUserAndGuildRankingSystem::Instance();
            if ( CPvpUserAndGuildRankingSystem::GetBossType(v13, v35, v34) == 4 )
              v17 = 1;
            break;
          case 9:
            v36 = CPlayerDB::GetCharSerial(&v39->m_Param);
            v37 = CPlayerDB::GetRaceCode(&v39->m_Param);
            v14 = CPvpUserAndGuildRankingSystem::Instance();
            if ( CPvpUserAndGuildRankingSystem::GetBossType(v14, v37, v36) == 8 )
              v17 = 1;
            break;
          case 10:
            if ( v39->m_Param.m_pGuild )
            {
              v38 = CPlayerDB::GetCharSerial(&v39->m_Param);
              v15 = CGuild::GetGuildMasterSerial(v39->m_Param.m_pGuild);
              if ( v38 == v15 )
                v17 = 1;
            }
            break;
          case 11:
            if ( v39->m_pPartyMgr
              && CPartyPlayer::IsPartyMode(v39->m_pPartyMgr)
              && CPartyPlayer::IsPartyBoss(v39->m_pPartyMgr) )
            {
              v17 = 1;
            }
            break;
          default:
            break;
        }
        if ( v17 )
          break;
      }
    }
    result = v17;
  }
  else
  {
    result = 0;
  }
  return result;
}
