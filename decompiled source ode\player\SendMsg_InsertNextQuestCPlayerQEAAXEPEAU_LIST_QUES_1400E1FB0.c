/*
 * Function: ?SendMsg_InsertNextQuest@CPlayer@@QEAAXEPEAU_LIST@_QUEST_DB_BASE@@@Z
 * Address: 0x1400E1FB0
 */

void __fastcall CPlayer::SendMsg_InsertNextQuest(CPlayer *this, char bySlotIndex, _QUEST_DB_BASE::_LIST *pQuestDB)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-98h]@1
  _insert_next_quest_inform_zocl Dst; // [sp+38h] [bp-60h]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v8; // [sp+65h] [bp-33h]@4
  unsigned __int64 v9; // [sp+80h] [bp-18h]@4
  CPlayer *v10; // [sp+A0h] [bp+8h]@1
  char v11; // [sp+A8h] [bp+10h]@1
  _QUEST_DB_BASE::_LIST *Src; // [sp+B0h] [bp+18h]@1

  Src = pQuestDB;
  v11 = bySlotIndex;
  v10 = this;
  v3 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  _insert_next_quest_inform_zocl::_insert_next_quest_inform_zocl(&Dst);
  Dst.byQuestDBSlot = v11;
  memcpy_0(&Dst.NewQuestData, Src, 0xDui64);
  pbyType = 24;
  v8 = 111;
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, &Dst.byQuestDBSlot, 0xEu);
}
