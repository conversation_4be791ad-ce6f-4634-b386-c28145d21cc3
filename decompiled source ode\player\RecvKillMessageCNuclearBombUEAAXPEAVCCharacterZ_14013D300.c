/*
 * Function: ?RecvKillMessage@CNuclearBomb@@UEAAXPEAVCCharacter@@@Z
 * Address: 0x14013D300
 */

void __fastcall CNuclearBomb::RecvKillMessage(CNuclearBomb *this, CCharacter *pDier)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CNuclearBomb *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v5->m_pMaster && v5->m_pMaster->m_bLive && v5->m_pMaster->m_bOper && !v5->m_pMaster->m_bCorpse )
    ((void (__fastcall *)(CPlayer *))v5->m_pMaster->vfptr->RecvKillMessage)(v5->m_pMaster);
}
