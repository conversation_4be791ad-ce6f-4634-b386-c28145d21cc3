/*
 * Function: ?GetWeaponAdjust@CPlayer@@UEAAMXZ
 * Address: 0x1400610C0
 */

float __fastcall CPlayer::GetWeaponAdjust(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  float result; // xmm0_4@8
  __int64 v4; // [sp+0h] [bp-58h]@1
  char *v5; // [sp+30h] [bp-28h]@5
  _base_fld *v6; // [sp+38h] [bp-20h]@7
  _base_fld *v7; // [sp+40h] [bp-18h]@11
  CPlayer *v8; // [sp+60h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CPlayer::IsRidingUnit(v8) )
  {
    if ( v8->m_byUsingWeaponPart < 2 )
      v7 = CRecordData::GetRecord(
             &stru_1799C86D0 + v8->m_byUsingWeaponPart,
             v8->m_pUsingUnit->byPart[v8->m_byUsingWeaponPart]);
    result = FLOAT_1_0;
  }
  else
  {
    v5 = &v8->m_Param.m_dbEquip.m_pStorageList[6].m_bLoad;
    if ( *v5
      && CPlayer::GetEffectEquipCode(v8, 1, 6) == 1
      && (v6 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 6, *(_WORD *)(v5 + 3)),
          *(_DWORD *)&v6[6].m_strCode[8] != 10) )
    {
      result = *(float *)&v6[9].m_strCode[40];
    }
    else
    {
      result = 0.0;
    }
  }
  return result;
}
