/*
 * Function: ?Update_RankInGuild_Step3@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x1404B8B50
 */

char __fastcall CRFWorldDatabase::Update_RankInGuild_Step3(CRFWorldDatabase *this, unsigned int dwGuildSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v5; // [sp+0h] [bp-28h]@1
  CRFWorldDatabase *v6; // [sp+30h] [bp+8h]@1
  unsigned int v7; // [sp+38h] [bp+10h]@1

  v7 = dwGuildSerial;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v6->vfptr,
    "CRFWorldDatabase::Update_RankInGuild_Step3( dwGuildSerial(%u) ) : Start!",
    dwGuildSerial);
  if ( CRFNewDatabase::ExecUpdateQuery(
         (CRFNewDatabase *)&v6->vfptr,
         "select IDENTITY(int, 1, 1) AS NewRank, -1 as Grade, serial, CurGrade, lv, Pvppoint into #tbl_RankInGuildAll fro"
         "m #tbl_RankInGuild order by NewGrade desc, rate ",
         0) )
  {
    if ( CRFNewDatabase::ExecUpdateQuery(
           (CRFNewDatabase *)&v6->vfptr,
           "select IDENTITY(int, 1, 1) AS NewRank, -1 as NewRate, -1 as Grade, serial, CurGrade, lv, PvpPoint into #tbl_R"
           "ankInGuildCom from #tbl_RankInGuild where CurGrade <> 0 and CurGrade <> 3 order by NewGrade desc, rate ",
           0) )
    {
      if ( CRFNewDatabase::ExecUpdateQuery(
             (CRFNewDatabase *)&v6->vfptr,
             "update #tbl_RankInGuildCom set NewRate = ( (NewRank*10000)/(select count(*) from #tbl_RankInGuildCom) ) ",
             0) )
      {
        CRFNewDatabase::FmtLog(
          (CRFNewDatabase *)&v6->vfptr,
          "CRFWorldDatabase::Update_RankInGuild_Step3( dwGuildSerial(%u) ) : End!",
          v7);
        result = 1;
      }
      else
      {
        CRFNewDatabase::FmtLog(
          (CRFNewDatabase *)&v6->vfptr,
          "CRFWorldDatabase::Update_RankInGuild_Step3( dwGuildSerial(%u) ) : update #tbl_RankInGuildCom set NewRate = ( ("
          "NewRank*10000)/(select count(*) from #tbl_RankInGuildCom) ) ",
          v7);
        CRFWorldDatabase::Update_RankInGuild_Step6(v6);
        result = 0;
      }
    }
    else
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v6->vfptr,
        "CRFWorldDatabase::Update_RankInGuild_Step3( dwGuildSerial(%u) ) : select IDENTITY(int, 1, 1) AS NewRank, -1 as N"
        "ewRate, -1 as Grade, serial, CurGrade, lv, PvpPoint into #tbl_RankInGuildCom from #tbl_RankInGuild where CurGrad"
        "e <> 0 and CurGrade <> 3 order by NewGrade desc, rate Fail!",
        v7);
      if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
        CRFWorldDatabase::Update_RankInGuild_Step7(v6);
      if ( !CRFWorldDatabase::Update_RankInGuild_Step8(v6) )
        CRFWorldDatabase::Update_RankInGuild_Step8(v6);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v6->vfptr,
      "CRFWorldDatabase::Update_RankInGuild_Step3( dwGuildSerial(%u) ) : select IDENTITY(int, 1, 1) AS NewRank, -1 as Gra"
      "de, serial, CurGrade, lv, Pvppoint into #tbl_RankInGuildAll from #tbl_RankInGuild order by NewGrade desc, rate Fail!",
      v7);
    if ( !CRFWorldDatabase::Update_RankInGuild_Step7(v6) )
      CRFWorldDatabase::Update_RankInGuild_Step7(v6);
    result = 0;
  }
  return result;
}
