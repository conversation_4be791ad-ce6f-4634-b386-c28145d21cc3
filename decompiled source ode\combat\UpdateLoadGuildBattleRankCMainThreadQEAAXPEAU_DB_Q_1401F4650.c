/*
 * Function: ?UpdateLoadGuildBattleRank@CMainThread@@QEAAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1401F4650
 */

void __fastcall CMainThread::UpdateLoadGuildBattleRank(CMainThread *this, _DB_QRY_SYN_DATA *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CGuildBattleController *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  char *v6; // [sp+20h] [bp-18h]@4
  char *pLoadData; // [sp+28h] [bp-10h]@4
  _DB_QRY_SYN_DATA *v8; // [sp+48h] [bp+10h]@1

  v8 = pData;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = pData->m_sData;
  pLoadData = &pData->m_sData[1];
  v4 = CGuildBattleController::Instance();
  CGuildBattleController::CompleteUpdateRank(v4, v8->m_byResult, *v6, pLoadData);
}
