/*
 * Function: ?SendMsg_ReEnterAsk@CPlayer@@QEAAXGK@Z
 * Address: 0x1400DAB70
 */

void __fastcall CPlayer::SendMsg_ReEnterAsk(CPlayer *this, unsigned __int16 wChannelIndex, unsigned int dwChannelSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@4
  __int64 v6; // [sp+0h] [bp-78h]@1
  _darkhole_ask_reenter_inform_zocl v7; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4
  CPlayer *v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v3 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7.wChannelIndex = wChannelIndex;
  v7.dwChannelSerial = dwChannelSerial;
  pbyType = 35;
  v9 = -47;
  v5 = _darkhole_ask_reenter_inform_zocl::size(&v7);
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, (char *)&v7, v5);
}
