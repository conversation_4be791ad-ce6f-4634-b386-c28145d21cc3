/*
 * Function: ?GetSkillDelayTime@CMonster@@QEAAMPEAVCMonsterSkill@@@Z
 * Address: 0x140142C90
 */

float __fastcall CMonster::GetSkillDelayTime(CMonster *this, CMonsterSkill *pSkill)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float result; // xmm0_4@5
  unsigned int v5; // eax@6
  float v6; // xmm0_4@6
  __int64 v7; // [sp+0h] [bp-48h]@1
  float v8; // [sp+30h] [bp-18h]@6
  CMonster *v9; // [sp+50h] [bp+8h]@1
  CMonsterSkill *v10; // [sp+58h] [bp+10h]@1

  v10 = pSkill;
  v9 = this;
  v2 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pSkill )
  {
    v5 = CMonsterSkill::GetNextActionDelayTime(pSkill);
    v6 = (float)(signed int)v5;
    v8 = (float)(signed int)v5;
    if ( CMonsterSkill::GetType(v10) )
    {
      if ( CMonsterSkill::GetType(v10) == 1 )
      {
        _effect_parameter::GetEff_Plus(&v9->m_EP, 13);
        v8 = v8 + (float)(v6 * 2.0);
      }
    }
    else
    {
      _effect_parameter::GetEff_Plus(&v9->m_EP, 12);
      v8 = v8 + (float)(v6 * 2.0);
    }
    result = v8;
  }
  else
  {
    result = 0.0;
  }
  return result;
}
