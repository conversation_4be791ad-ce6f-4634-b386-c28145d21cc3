/*
 * Function: j_??$_Uninit_copy@PEAKPEAKV?$allocator@K@std@@@std@@YAPEAKPEAK00AEAV?$allocator@K@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400102DA
 */

unsigned int *__fastcall std::_Uninit_copy<unsigned long *,unsigned long *,std::allocator<unsigned long>>(unsigned int *_First, unsigned int *_Last, unsigned int *_Dest, std::allocator<unsigned long> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<unsigned long *,unsigned long *,std::allocator<unsigned long>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
