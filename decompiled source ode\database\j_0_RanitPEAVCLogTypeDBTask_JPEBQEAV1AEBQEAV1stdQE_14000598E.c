/*
 * Function: j_??0?$_<PERSON>t@PEAVCLogTypeDBTask@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@AEBU01@@Z
 * Address: 0x14000598E
 */

void __fastcall std::_<PERSON>t<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &>::_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &>(std::_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &> *this, std::_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &> *__that)
{
  std::_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &>::_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &>(
    this,
    __that);
}
