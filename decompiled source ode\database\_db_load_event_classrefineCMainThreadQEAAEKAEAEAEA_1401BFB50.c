/*
 * Function: ?_db_load_event_classrefine@CMainThread@@QEAAEKAEAEAEAK@Z
 * Address: 0x1401BFB50
 */

char __fastcall CMainThread::_db_load_event_classrefine(CMainThread *this, unsigned int dwAvatorSerial, char *byRefinedCnt, unsigned int *dwRefineDate)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  int v8; // [sp+20h] [bp-18h]@4
  CMainThread *v9; // [sp+40h] [bp+8h]@1
  unsigned int dwAvatorSeriala; // [sp+48h] [bp+10h]@1
  char *byRefinedCnta; // [sp+50h] [bp+18h]@1
  unsigned int *dwRefineDatea; // [sp+58h] [bp+20h]@1

  dwRefineDatea = dwRefineDate;
  byRefinedCnta = byRefinedCnt;
  dwAvatorSeriala = dwAvatorSerial;
  v9 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = CRFWorldDatabase::Select_RFEvent_ClassRefine(v9->m_pWorldDB, dwAvatorSerial, byRefinedCnt, dwRefineDate);
  if ( v8 == 2 )
  {
    if ( !CRFWorldDatabase::Insert_RFEvent_ClassRefine(v9->m_pWorldDB, dwAvatorSeriala) )
    {
      *byRefinedCnta = -1;
      return 24;
    }
    *byRefinedCnta = 0;
    *dwRefineDatea = 0;
  }
  else if ( v8 == 1 )
  {
    *byRefinedCnta = -1;
    return 24;
  }
  return 0;
}
