/*
 * Function: ?Final@HashTransformation@CryptoPP@@UEAAXPEAE@Z
 * Address: 0x14044DDD0
 */

void __fastcall CryptoPP::HashTransformation::Final(CryptoPP::HashTransformation *this, char *digest)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  CryptoPP::ClonableVtbl *v6; // [sp+20h] [bp-18h]@4
  CryptoPP::HashTransformation *v7; // [sp+40h] [bp+8h]@1
  char *v8; // [sp+48h] [bp+10h]@1

  v8 = digest;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = ((int (__fastcall *)(CryptoPP::HashTransformation *))v7->vfptr[3].Clone)(v7);
  v6 = v7->vfptr;
  ((void (__fastcall *)(CryptoPP::HashTransformation *, char *, _QWORD))v6[7].__vecDelDtor)(v7, v8, (unsigned int)v4);
}
