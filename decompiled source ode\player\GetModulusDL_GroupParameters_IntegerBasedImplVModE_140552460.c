/*
 * Function: ?GetModulus@?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@UEBAAEBVInteger@2@XZ
 * Address: 0x140552460
 */

const struct CryptoPP::Integer *__fastcall CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::GetModulus(__int64 a1)
{
  return CryptoPP::ModExpPrecomputation::GetModulus((CryptoPP::ModExpPrecomputation *)(a1 + 72));
}
