/*
 * Function: ?RemoveAllContinousEffectGroup@CCharacter@@QEAA_NI@Z
 * Address: 0x140178830
 */

bool __fastcall CCharacter::RemoveAllContinousEffectGroup(CCharacter *this, unsigned int uiEffectCodeType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-58h]@1
  int v6; // [sp+30h] [bp-28h]@4
  int j; // [sp+34h] [bp-24h]@4
  bool *v8; // [sp+38h] [bp-20h]@7
  CCharacter *v9; // [sp+60h] [bp+8h]@1
  unsigned int v10; // [sp+68h] [bp+10h]@1

  v10 = uiEffectCodeType;
  v9 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  for ( j = 0; j < 8; ++j )
  {
    v8 = &v9->m_SFCont[v10][j].m_bExist;
    if ( *v8 )
    {
      CCharacter::RemoveSFContEffect(v9, v10, j, 0, 0);
      ++v6;
    }
  }
  return v6 > 0;
}
