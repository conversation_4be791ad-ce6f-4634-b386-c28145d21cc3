/*
 * Function: ?Init@CMonster@@QEAA_NPEAU_object_id@@@Z
 * Address: 0x140141970
 */

char __fastcall CMonster::Init(CMonster *this, _object_id *pID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMonster *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CCharacter::Init((CCharacter *)&v6->vfptr, pID);
  _effect_parameter::AllocEffParam(&v6->m_EP);
  v6->m_dwLastDestroyTime = 0;
  v6->m_bDungeon = 0;
  v6->m_bOper = 0;
  CMonsterSkillPool::Init(&v6->m_MonsterSkillPool);
  v6->m_bApparition = 0;
  v6->m_dwDestroyNextTime = -1;
  v6->m_dwLastRecoverTime = -1;
  LODWORD(v6->m_fCreatePos[0]) = 0;
  LODWORD(v6->m_fCreatePos[1]) = 0;
  LODWORD(v6->m_fCreatePos[2]) = 0;
  LODWORD(v6->m_fLookAtPos[0]) = 0;
  LODWORD(v6->m_fLookAtPos[1]) = 0;
  LODWORD(v6->m_fLookAtPos[2]) = 0;
  v6->m_bRobExp = 0;
  v6->m_bRewardExp = 0;
  v6->m_bStdItemLoot = 0;
  v6->m_pActiveRec = 0i64;
  v6->m_pMonRec = 0i64;
  v6->m_pDumPosition = 0i64;
  v6->m_nHP = 0;
  v6->m_byCreateDate[0] = 0;
  v6->m_byCreateDate[1] = 0;
  v6->m_byCreateDate[2] = 0;
  v6->m_byCreateDate[3] = 0;
  v6->m_LifeMax = 0;
  v6->m_LifeCicle = -1;
  v6->m_nCommonStateChunk = 0;
  v6->m_nCommonStateChunk = 0;
  LODWORD(v6->m_fStartLookAtPos[0]) = 0;
  LODWORD(v6->m_fStartLookAtPos[1]) = 0;
  LODWORD(v6->m_fStartLookAtPos[2]) = 0;
  v6->m_bRotateMonster = 0;
  v6->m_pTargetChar = 0i64;
  v6->m_DefPart[0] = 0;
  v6->m_DefPart[1] = 0;
  v6->m_DefPart[2] = 0;
  v6->m_DefPart[3] = 0;
  v6->m_DefPart[4] = 0;
  v6->m_nEventItemNum = 0;
  memset_0(v6->m_eventItem, 0, 0x40ui64);
  v6->m_pEventRespawn = 0i64;
  v6->m_pEventSet = 0i64;
  CMonster::_InitSDM_LootTBL();
  return 1;
}
