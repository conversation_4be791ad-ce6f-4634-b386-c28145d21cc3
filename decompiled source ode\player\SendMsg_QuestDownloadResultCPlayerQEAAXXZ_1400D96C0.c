/*
 * Function: ?SendMsg_QuestDownloadResult@CPlayer@@QEAAXXZ
 * Address: 0x1400D96C0
 */

void __fastcall CPlayer::SendMsg_QuestDownloadResult(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@9
  __int64 v4; // [sp+0h] [bp-248h]@1
  _quest_download_result_zocl Dst; // [sp+40h] [bp-208h]@4
  int v6; // [sp+1F4h] [bp-54h]@4
  int j; // [sp+1F8h] [bp-50h]@4
  void *Src; // [sp+200h] [bp-48h]@6
  char pbyType; // [sp+214h] [bp-34h]@9
  char v10; // [sp+215h] [bp-33h]@9
  unsigned __int64 v11; // [sp+230h] [bp-18h]@4
  CPlayer *v12; // [sp+250h] [bp+8h]@1

  v12 = this;
  v1 = &v4;
  for ( i = 144i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11 = (unsigned __int64)&v4 ^ _security_cookie;
  v12->m_bQuestDownload = 1;
  _quest_download_result_zocl::_quest_download_result_zocl(&Dst);
  Dst.byRetCode = 0;
  v6 = 0;
  for ( j = 0; j < 30; ++j )
  {
    Src = (char *)&v12->m_Param.m_QuestDB + 13 * j;
    if ( *(_BYTE *)Src != 255 )
    {
      Dst.SlotInfo[v6].byDBSlotIndex = j;
      memcpy_0(&Dst.SlotInfo[v6++].cont, Src, 0xDui64);
    }
  }
  Dst.bySlotNum = v6;
  pbyType = 3;
  v10 = 12;
  v3 = _quest_download_result_zocl::size(&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, &Dst.byRetCode, v3);
}
