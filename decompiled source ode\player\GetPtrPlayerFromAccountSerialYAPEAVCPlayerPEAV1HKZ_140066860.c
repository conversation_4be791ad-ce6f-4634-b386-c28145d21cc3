/*
 * Function: ?GetPtrPlayerFromAccountSerial@@YAPEAVCPlayer@@PEAV1@HK@Z
 * Address: 0x140066860
 */

CPlayer *__fastcall GetPtrPlayerFromAccountSerial(CPlayer *pData, int nNum, unsigned int dwSerial)
{
  int *v3; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CPlayer *v7; // [sp+20h] [bp+8h]@1

  v7 = pData;
  v3 = &j;
  for ( i = 4i64; i; --i )
  {
    *v3 = -*********;
    ++v3;
  }
  for ( j = 0; j < nNum; ++j )
  {
    if ( v7[j].m_bLive && v7[j].m_pUserDB->m_dwAccountSerial == dwSerial )
      return &v7[j];
  }
  return 0i64;
}
