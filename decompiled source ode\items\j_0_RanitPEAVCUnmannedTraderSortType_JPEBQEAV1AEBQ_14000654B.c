/*
 * Function: j_??0?$_Ranit@PEAVCUnmannedTraderSortType@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@XZ
 * Address: 0x14000654B
 */

void __fastcall std::_<PERSON>t<CUnmannedTraderSortType *,__int64,CUnmannedTraderSortType * const *,CUnmannedTraderSortType * const &>::_<PERSON>t<CUnmannedTraderSortType *,__int64,CUnmannedTraderSortType * const *,CUnmannedTraderSortType * const &>(std::_<PERSON>t<CUnmannedTraderSortType *,__int64,CUnmannedTraderSortType * const *,CUnmannedTraderSortType * const &> *this)
{
  std::_Ranit<CUnmannedTraderSortType *,__int64,CUnmannedTraderSortType * const *,CUnmannedTraderSortType * const &>::_<PERSON>t<CUnmannedTraderSortType *,__int64,CUnmannedTraderSortType * const *,CUnmannedTraderSortType * const &>(this);
}
