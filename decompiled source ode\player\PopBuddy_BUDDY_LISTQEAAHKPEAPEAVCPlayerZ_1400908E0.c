/*
 * Function: ?PopBuddy@_BUDDY_LIST@@QEAAHKPEAPEAVCPlayer@@@Z
 * Address: 0x1400908E0
 */

signed __int64 __fastcall _BUDDY_LIST::PopBuddy(_BUDDY_LIST *this, unsigned int dwSerial, CPlayer **ppPoper)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@4
  _BUDDY_LIST *v8; // [sp+40h] [bp+8h]@1
  unsigned int v9; // [sp+48h] [bp+10h]@1
  CPlayer **v10; // [sp+50h] [bp+18h]@1

  v10 = ppPoper;
  v9 = dwSerial;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; (signed int)j < 50; ++j )
  {
    if ( _BUDDY_LIST::__list::fill((_BUDDY_LIST::__list *)v8 + (signed int)j) && v8->m_List[j].dwSerial == v9 )
    {
      *v10 = v8->m_List[j].pPtr;
      v8->m_List[j].dwSerial = -1;
      return j;
    }
  }
  return 0xFFFFFFFFi64;
}
