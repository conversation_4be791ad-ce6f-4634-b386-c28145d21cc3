/*
 * Function: ?_Insert_n@?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@IEAAXV?$_Vector_iterator@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@2@_KAEBVCUnmannedTraderSchedule@@@Z
 * Address: 0x140395D80
 */

void __fastcall std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Insert_n(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *_Where, unsigned __int64 _Count, CUnmannedTraderSchedule *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v6; // rax@5
  unsigned __int64 v7; // rax@7
  unsigned __int64 v8; // rax@8
  unsigned __int64 v9; // rax@11
  __int64 v10; // [sp+0h] [bp-E8h]@1
  CUnmannedTraderSchedule _Vala; // [sp+28h] [bp-C0h]@4
  unsigned __int64 _Counta; // [sp+58h] [bp-90h]@4
  CUnmannedTraderSchedule *_Ptr; // [sp+60h] [bp-88h]@13
  CUnmannedTraderSchedule *v14; // [sp+68h] [bp-80h]@13
  CUnmannedTraderSchedule *_Last; // [sp+70h] [bp-78h]@18
  char v16; // [sp+78h] [bp-70h]@4
  __int64 v17; // [sp+98h] [bp-50h]@4
  unsigned __int64 v18; // [sp+A0h] [bp-48h]@5
  unsigned __int64 v19; // [sp+A8h] [bp-40h]@8
  unsigned __int64 v20; // [sp+B0h] [bp-38h]@9
  CUnmannedTraderSchedule *v21; // [sp+B8h] [bp-30h]@13
  CUnmannedTraderSchedule *v22; // [sp+C0h] [bp-28h]@13
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v23; // [sp+F0h] [bp+8h]@1
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v24; // [sp+F8h] [bp+10h]@1
  unsigned __int64 v25; // [sp+100h] [bp+18h]@1
  unsigned __int64 v26; // [sp+100h] [bp+18h]@13

  v25 = _Count;
  v24 = _Where;
  v23 = this;
  v4 = &v10;
  for ( i = 54i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v17 = -2i64;
  qmemcpy(&v16, _Val, 0x20ui64);
  qmemcpy(&_Vala, &v16, sizeof(_Vala));
  _Counta = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::capacity(v23);
  if ( v25 )
  {
    v18 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::size(v23);
    v6 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::max_size(v23);
    if ( v6 - v18 < v25 )
      std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Xlen();
    v7 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::size(v23);
    if ( _Counta >= v25 + v7 )
    {
      if ( v23->_Mylast - v24->_Myptr >= v25 )
      {
        _Last = v23->_Mylast;
        v23->_Mylast = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Umove<CUnmannedTraderSchedule *>(
                         v23,
                         &_Last[-v25],
                         _Last,
                         v23->_Mylast);
        stdext::_Unchecked_move_backward<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *>(
          v24->_Myptr,
          &_Last[-v25],
          _Last);
        std::fill<CUnmannedTraderSchedule *,CUnmannedTraderSchedule>(v24->_Myptr, &v24->_Myptr[v25], &_Vala);
      }
      else
      {
        std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Umove<CUnmannedTraderSchedule *>(
          v23,
          v24->_Myptr,
          v23->_Mylast,
          &v24->_Myptr[v25]);
        std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Ufill(
          v23,
          v23->_Mylast,
          v25 - (v23->_Mylast - v24->_Myptr),
          &_Vala);
        v23->_Mylast += v25;
        std::fill<CUnmannedTraderSchedule *,CUnmannedTraderSchedule>(v24->_Myptr, &v23->_Mylast[-v25], &_Vala);
      }
    }
    else
    {
      v19 = _Counta / 2;
      v8 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::max_size(v23);
      if ( v8 - v19 >= _Counta )
        v20 = _Counta / 2 + _Counta;
      else
        v20 = 0i64;
      _Counta = v20;
      v9 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::size(v23);
      if ( _Counta < v25 + v9 )
        _Counta = v25 + std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::size(v23);
      _Ptr = std::allocator<CUnmannedTraderSchedule>::allocate(&v23->_Alval, _Counta);
      v14 = _Ptr;
      v21 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Umove<CUnmannedTraderSchedule *>(
              v23,
              v23->_Myfirst,
              v24->_Myptr,
              _Ptr);
      v14 = v21;
      v22 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Ufill(v23, v21, v25, &_Vala);
      v14 = v22;
      std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Umove<CUnmannedTraderSchedule *>(
        v23,
        v24->_Myptr,
        v23->_Mylast,
        v22);
      v26 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::size(v23) + v25;
      if ( v23->_Myfirst )
      {
        std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Destroy(
          v23,
          v23->_Myfirst,
          v23->_Mylast);
        std::allocator<CUnmannedTraderSchedule>::deallocate(&v23->_Alval, v23->_Myfirst, v23->_Myend - v23->_Myfirst);
      }
      v23->_Myend = &_Ptr[_Counta];
      v23->_Mylast = &_Ptr[v26];
      v23->_Myfirst = _Ptr;
    }
  }
  CUnmannedTraderSchedule::~CUnmannedTraderSchedule(&_Vala);
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(v24);
}
