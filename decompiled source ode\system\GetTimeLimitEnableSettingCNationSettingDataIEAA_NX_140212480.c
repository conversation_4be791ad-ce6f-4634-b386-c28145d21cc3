/*
 * Function: ?GetTimeLimitEnableSetting@CNationSettingData@@IEAA_NXZ
 * Address: 0x140212480
 */

char __fastcall CNationSettingData::GetTimeLimitEnableSetting(CNationSettingData *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-78h]@1
  char ReturnedString; // [sp+38h] [bp-40h]@4
  char v6; // [sp+39h] [bp-3Fh]@4
  unsigned __int64 v7; // [sp+60h] [bp-18h]@4

  v1 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = (unsigned __int64)&v4 ^ _security_cookie;
  ReturnedString = 0;
  memset(&v6, 0, 0xFui64);
  GetPrivateProfileStringA("Time Limit", "Use", "X", &ReturnedString, 0x10u, ".\\Initialize\\WorldSystem.ini");
  if ( !strcmp_0(&ReturnedString, "X") )
  {
    result = 0;
  }
  else if ( !_stricmp(&ReturnedString, "true") )
  {
    result = 1;
  }
  else
  {
    _stricmp(&ReturnedString, "false");
    result = 0;
  }
  return result;
}
