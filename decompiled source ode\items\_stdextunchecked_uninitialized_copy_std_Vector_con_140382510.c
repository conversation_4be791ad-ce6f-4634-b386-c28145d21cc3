/*
 * Function: _stdext::unchecked_uninitialized_copy_std::_Vector_const_iterator_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64____CUnmannedTraderSubClassInfo_____ptr64_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64____::_1_::dtor$2
 * Address: 0x140382510
 */

void __fastcall stdext::unchecked_uninitialized_copy_std::_Vector_const_iterator_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64____CUnmannedTraderSubClassInfo_____ptr64_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64____::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(*(std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > **)(a2 + 88));
}
