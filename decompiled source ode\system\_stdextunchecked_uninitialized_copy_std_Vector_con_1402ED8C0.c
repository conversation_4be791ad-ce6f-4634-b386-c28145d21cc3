/*
 * Function: _stdext::unchecked_uninitialized_copy_std::_Vector_const_iterator_RoomCharInfo_std::allocator_RoomCharInfo____RoomCharInfo_____ptr64_std::allocator_RoomCharInfo____::_1_::dtor$1
 * Address: 0x1402ED8C0
 */

void __fastcall stdext::unchecked_uninitialized_copy_std::_Vector_const_iterator_RoomCharInfo_std::allocator_RoomCharInfo____RoomCharInfo_____ptr64_std::allocator_RoomCharInfo____::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo>>::~_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo>>(*(std::_Vector_const_iterator<RoomCharInfo,std::allocator<RoomCharInfo> > **)(a2 + 176));
}
