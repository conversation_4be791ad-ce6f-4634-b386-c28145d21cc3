/*
 * Function: ?init_objects@AutominePersonalMgr@@QEAA_NXZ
 * Address: 0x1402DE9E0
 */

char __fastcall AutominePersonalMgr::init_objects(AutominePersonalMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v4; // [sp+0h] [bp-78h]@1
  _object_id v5; // [sp+34h] [bp-44h]@9
  unsigned int j; // [sp+44h] [bp-34h]@9
  AutominePersonal *v7; // [sp+48h] [bp-30h]@7
  void *v8; // [sp+50h] [bp-28h]@4
  __int64 v9; // [sp+58h] [bp-20h]@4
  AutominePersonal *v10; // [sp+60h] [bp-18h]@5
  AutominePersonalMgr *v11; // [sp+80h] [bp+8h]@1

  v11 = this;
  v1 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = -2i64;
  v8 = operator new[](0x8ECBC8ui64);
  if ( v8 )
  {
    *(_DWORD *)v8 = 2532;
    `eh vector constructor iterator'(
      (char *)v8 + 8,
      0xE70ui64,
      2532,
      (void (__cdecl *)(void *))AutominePersonal::AutominePersonal,
      (void (__cdecl *)(void *))AutominePersonal::~AutominePersonal);
    v10 = (AutominePersonal *)((char *)v8 + 8);
  }
  else
  {
    v10 = 0i64;
  }
  v7 = v10;
  v11->m_pMachine = v10;
  if ( v11->m_pMachine )
  {
    _object_id::_object_id(&v5);
    for ( j = 0; (signed int)j < 2532; ++j )
    {
      if ( !AutominePersonal::initialize(&v11->m_pMachine[j], j) )
      {
        CLogFile::Write(
          &v11->m_logError,
          "AutominePersonalMgr::init_Objects() >> m_Machine[%d].initialize(%d) failed",
          j,
          j);
        return 0;
      }
    }
    result = 1;
  }
  else
  {
    CLogFile::Write(
      &v11->m_logError,
      "AutominePersonalMgr::init_Objects() >> Failed allocate memory of AutoMinePersonal[MAX_PLAYER].");
    result = 0;
  }
  return result;
}
