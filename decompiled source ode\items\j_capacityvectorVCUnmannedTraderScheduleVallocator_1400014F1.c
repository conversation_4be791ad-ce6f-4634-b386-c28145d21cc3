/*
 * Function: j_?capacity@?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@QEBA_KXZ
 * Address: 0x1400014F1
 */

unsigned __int64 __fastcall std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::capacity(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this)
{
  return std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::capacity(this);
}
