/*
 * Function: ?SendData_PartyMemberMaxHFSP@CPlayer@@QEAAXXZ
 * Address: 0x1400DE120
 */

void __fastcall CPlayer::SendData_PartyMemberMaxHFSP(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-98h]@1
  CPartyPlayer **v4; // [sp+30h] [bp-68h]@5
  char szMsg[4]; // [sp+48h] [bp-50h]@6
  __int16 v6; // [sp+4Ch] [bp-4Ch]@6
  __int16 v7; // [sp+4Eh] [bp-4Ah]@6
  __int16 v8; // [sp+50h] [bp-48h]@6
  int v9; // [sp+64h] [bp-34h]@6
  char pbyType; // [sp+74h] [bp-24h]@6
  char v11; // [sp+75h] [bp-23h]@6
  int j; // [sp+84h] [bp-14h]@6
  CPlayer *v13; // [sp+A0h] [bp+8h]@1

  v13 = this;
  v1 = &v3;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v13->m_pPartyMgr )
  {
    v4 = CPartyPlayer::GetPtrPartyMember(v13->m_pPartyMgr);
    if ( v4 )
    {
      *(_DWORD *)szMsg = v13->m_dwObjSerial;
      v6 = ((int (__fastcall *)(CPlayer *))v13->vfptr->GetMaxHP)(v13);
      v7 = CPlayer::GetMaxFP(v13);
      v8 = CPlayer::GetMaxSP(v13);
      v9 = CPartyPlayer::GetPopPartyMember(v13->m_pPartyMgr);
      pbyType = 16;
      v11 = 25;
      for ( j = 0; j < v9; ++j )
      {
        if ( v4[j] != v13->m_pPartyMgr )
          CNetProcess::LoadSendMsg(unk_1414F2088, v4[j]->m_wZoneIndex, &pbyType, szMsg, 0xAu);
      }
    }
  }
}
