/*
 * Function: ?GetRecord@CMonsterSPGroupTable@@QEAAPEAU_monster_sp_group@@PEAD@Z
 * Address: 0x14015EE60
 */

_monster_sp_group *__fastcall CMonsterSPGroupTable::GetRecord(CMonsterSPGroupTable *this, char *szCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@4
  CMonsterSPGroupTable *v7; // [sp+40h] [bp+8h]@1
  const char *Str2; // [sp+48h] [bp+10h]@1

  Str2 = szCode;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < v7->m_dwRecordNum; ++j )
  {
    if ( !strcmp_0(v7->m_pRecordData[j].m_strCode, Str2) )
      return &v7->m_pRecordData[j];
  }
  return 0i64;
}
