/*
 * Function: ?AlterGold@CPlayer@@QEAAXN@Z
 * Address: 0x1400F7B20
 */

void __fastcall CPlayer::AlterGold(CPlayer *this, long double dGold)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp-28h] [bp-28h]@1
  CPlayer *v5; // [sp+8h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( dGold <= 0.0 )
  {
    if ( dGold < 0.0 )
      CPlayer::SubGold(v5, (signed int)floor(-0.0 - dGold));
  }
  else
  {
    CPlayer::AddGold(v5, (signed int)floor(dGold), 1);
  }
}
