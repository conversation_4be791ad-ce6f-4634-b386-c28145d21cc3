/*
 * Function: ?Init@CUnmannedTraderTaxRateManager@@QEAA_NPEAVCLogFile@@@Z
 * Address: 0x14038DB70
 */

char __fastcall CUnmannedTraderTaxRateManager::Init(CUnmannedTraderTaxRateManager *this, CLogFile *pkLogger)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // rax@5
  char result; // al@8
  TRC_AutoTrade *v6; // rax@12
  TRC_AutoTrade **v7; // rax@16
  __int64 v8; // [sp+0h] [bp-78h]@1
  int j; // [sp+20h] [bp-58h]@9
  CMyTimer *v10; // [sp+28h] [bp-50h]@7
  CMyTimer *v11; // [sp+30h] [bp-48h]@4
  TRC_AutoTrade *_Val; // [sp+38h] [bp-40h]@14
  TRC_AutoTrade *v13; // [sp+40h] [bp-38h]@14
  TRC_AutoTrade *v14; // [sp+48h] [bp-30h]@11
  __int64 v15; // [sp+50h] [bp-28h]@4
  CMyTimer *v16; // [sp+58h] [bp-20h]@5
  TRC_AutoTrade *v17; // [sp+60h] [bp-18h]@12
  CUnmannedTraderTaxRateManager *v18; // [sp+80h] [bp+8h]@1
  CLogFile *v19; // [sp+88h] [bp+10h]@1

  v19 = pkLogger;
  v18 = this;
  v2 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v15 = -2i64;
  v11 = (CMyTimer *)operator new(0x18ui64);
  if ( v11 )
  {
    CMyTimer::CMyTimer(v11);
    v16 = (CMyTimer *)v4;
  }
  else
  {
    v16 = 0i64;
  }
  v10 = v16;
  v18->m_pkTimer = v16;
  if ( v18->m_pkTimer )
  {
    for ( j = 0; j < 3; ++j )
    {
      v14 = (TRC_AutoTrade *)operator new(0x200ui64);
      if ( v14 )
      {
        TRC_AutoTrade::TRC_AutoTrade(v14, j);
        v17 = v6;
      }
      else
      {
        v17 = 0i64;
      }
      v13 = v17;
      _Val = v17;
      std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::push_back(&v18->m_vecTRC, &_Val);
      if ( !*std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::back(&v18->m_vecTRC) )
      {
        CLogFile::Write(v19, "CUnmannedTraderTaxRateManager::Init(...) : new TRC_AutoTrade NULL!\r\n");
        return 0;
      }
      v7 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::back(&v18->m_vecTRC);
      if ( !TRC_AutoTrade::Initialzie(*v7) )
      {
        CLogFile::Write(v19, "CUnmannedTraderTaxRateManager::Init(...) : m_vecTRC.back()->Initialzie() Fail!\r\n");
        return 0;
      }
    }
    CMyTimer::BeginTimer(
      v18->m_pkTimer,
      CUnmannedTraderEnvironmentValue::Unmanned_Trader_Change_Taxrate_Check_Changed_Day_Delay);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
