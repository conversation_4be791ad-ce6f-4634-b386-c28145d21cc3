/*
 * Function: ??0CUnmannedTraderGroupDivisionVersionInfo@@QEAA@AEBV0@@Z
 * Address: 0x140399520
 */

void __fastcall CUnmannedTraderGroupDivisionVersionInfo::CUnmannedTraderGroupDivisionVersionInfo(CUnmannedTraderGroupDivisionVersionInfo *this, CUnmannedTraderGroupDivisionVersionInfo *lhs)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  __int64 v5; // [sp+20h] [bp-18h]@4
  CUnmannedTraderGroupDivisionVersionInfo *v6; // [sp+40h] [bp+8h]@1
  CUnmannedTraderGroupDivisionVersionInfo *lhsa; // [sp+48h] [bp+10h]@1

  lhsa = lhs;
  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = -2i64;
  std::vector<unsigned long,std::allocator<unsigned long>>::vector<unsigned long,std::allocator<unsigned long>>(&v6->m_vecuiVersion);
  CUnmannedTraderGroupDivisionVersionInfo::operator=(v6, lhsa);
}
