/*
 * Function: ?GetVersion@CUnmannedTraderGroupItemInfoTable@@QEAA_NEEAEAK@Z
 * Address: 0x140360890
 */

bool __fastcall CUnmannedTraderGroupItemInfoTable::GetVersion(CUnmannedTraderGroupItemInfoTable *this, char byDivision, char byClass, unsigned int *dwVer)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  CUnmannedTraderGroupItemInfoTable *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return CUnmannedTraderGroupVersionInfo::GetVersion(&v8->m_kVerInfo, byDivision, byClass, dwVer);
}
