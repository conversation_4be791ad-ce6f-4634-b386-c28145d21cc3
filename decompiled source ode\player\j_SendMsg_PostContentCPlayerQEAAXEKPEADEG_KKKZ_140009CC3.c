/*
 * Function: j_?SendMsg_PostContent@CPlayer@@QEAAXEKPEADEG_KKK@Z
 * Address: 0x140009CC3
 */

void __fastcall CPlayer::SendMsg_PostContent(CPlayer *this, char byErrCode, unsigned int dwPostSerial, char *wszContent, char byTableCode, unsigned __int16 wItemIndex, unsigned __int64 dwDur, unsigned int dwLv, unsigned int dwGold)
{
  CPlayer::SendMsg_PostContent(this, byErrCode, dwPostSerial, wszContent, byTableCode, wItemIndex, dwDur, dwLv, dwGold);
}
