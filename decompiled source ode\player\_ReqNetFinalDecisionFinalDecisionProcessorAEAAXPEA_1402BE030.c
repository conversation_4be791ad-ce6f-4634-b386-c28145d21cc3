/*
 * Function: ?_ReqNetFinalDecision@FinalDecisionProcessor@@AEAAXPEAVCPlayer@@@Z
 * Address: 0x1402BE030
 */

void __fastcall FinalDecisionProcessor::_ReqNetFinalDecision(FinalDecisionProcessor *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@4
  __int64 v5; // [sp+0h] [bp-68h]@1
  _pt_notify_final_decision *v6; // [sp+30h] [bp-38h]@4
  char pbyType; // [sp+44h] [bp-24h]@4
  char v8; // [sp+45h] [bp-23h]@4
  FinalDecisionProcessor *v9; // [sp+70h] [bp+8h]@1
  CPlayer *v10; // [sp+78h] [bp+10h]@1

  v10 = pOne;
  v9 = this;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = &v9->_Send[CPlayerDB::GetRaceCode(&pOne->m_Param)];
  pbyType = 56;
  v8 = 7;
  v4 = _pt_notify_final_decision::size(v6);
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, (char *)v6, v4);
}
