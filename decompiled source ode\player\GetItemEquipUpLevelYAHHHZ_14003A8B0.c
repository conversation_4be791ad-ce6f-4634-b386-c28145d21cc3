/*
 * Function: ?GetItemEquipUpLevel@@YAHHH@Z
 * Address: 0x14003A8B0
 */

signed __int64 __fastcall GetItemEquipUpLevel(int nTableCode, int nItemIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  __int64 v5; // [sp+0h] [bp-78h]@1
  CRecordData *v6; // [sp+20h] [bp-58h]@4
  _base_fld *v7; // [sp+28h] [bp-50h]@5
  _base_fld *v8; // [sp+30h] [bp-48h]@8
  _base_fld *v9; // [sp+38h] [bp-40h]@11
  _base_fld *v10; // [sp+40h] [bp-38h]@14
  _base_fld *v11; // [sp+48h] [bp-30h]@17
  _base_fld *v12; // [sp+50h] [bp-28h]@20
  _base_fld *v13; // [sp+58h] [bp-20h]@23
  _base_fld *v14; // [sp+60h] [bp-18h]@26
  int v15; // [sp+68h] [bp-10h]@4
  int v16; // [sp+80h] [bp+8h]@1

  v16 = nTableCode;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = &s_ptblItemData[v16];
  v15 = v16;
  switch ( v16 )
  {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 7:
      v7 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v7 )
        goto LABEL_28;
      result = *(_DWORD *)&v7[4].m_strCode[12];
      break;
    case 6:
      v8 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v8 )
        goto LABEL_28;
      result = *(_DWORD *)&v8[8].m_strCode[12];
      break;
    case 25:
      v9 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v9 )
        goto LABEL_28;
      result = *(_DWORD *)&v9[4].m_strCode[52];
      break;
    case 26:
      v10 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v10 )
        goto LABEL_28;
      result = v10[5].m_dwIndex;
      break;
    case 27:
      v11 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v11 )
        goto LABEL_28;
      result = *(_DWORD *)&v11[4].m_strCode[12];
      break;
    case 33:
      v12 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v12 )
        goto LABEL_28;
      result = *(_DWORD *)&v12[4].m_strCode[56];
      break;
    case 8:
      v13 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v13 )
        goto LABEL_28;
      result = *(_DWORD *)&v13[4].m_strCode[8];
      break;
    case 9:
      v14 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v14 )
        goto LABEL_28;
      result = *(_DWORD *)&v14[4].m_strCode[8];
      break;
    default:
LABEL_28:
      result = 1i64;
      break;
  }
  return result;
}
