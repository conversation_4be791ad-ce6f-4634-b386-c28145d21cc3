/*
 * Function: _std::vector_TRC_AutoTrade_____ptr64_std::allocator_TRC_AutoTrade_____ptr64___::insert_::_1_::dtor$3
 * Address: 0x14038FA40
 */

void __fastcall std::vector_TRC_AutoTrade_____ptr64_std::allocator_TRC_AutoTrade_____ptr64___::insert_::_1_::dtor_3(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::~_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>((std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *)(a2 + 96));
}
