/*
 * Function: ?capacity@?$vector@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEBA_KXZ
 * Address: 0x140594180
 */

signed __int64 __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::capacity(__int64 a1)
{
  signed __int64 v2; // [sp+0h] [bp-18h]@2

  if ( *(_QWORD *)(a1 + 16) )
    v2 = (*(_QWORD *)(a1 + 32) - *(_QWORD *)(a1 + 16)) / 96i64;
  else
    v2 = 0i64;
  return v2;
}
