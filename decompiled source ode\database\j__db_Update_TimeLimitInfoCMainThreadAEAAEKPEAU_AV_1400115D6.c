/*
 * Function: j_?_db_Update_TimeLimitInfo@CMainThread@@AEAAEKPEAU_AVATOR_DATA@@0PEADH@Z
 * Address: 0x1400115D6
 */

char __fastcall CMainThread::_db_Update_TimeLimitInfo(CMainThread *this, unsigned int dwAccSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *szTimeLimitInfoQuery, int nSize)
{
  return CMainThread::_db_Update_TimeLimitInfo(this, dwAccSerial, pNewData, pOldData, szTimeLimitInfoQuery, nSize);
}
