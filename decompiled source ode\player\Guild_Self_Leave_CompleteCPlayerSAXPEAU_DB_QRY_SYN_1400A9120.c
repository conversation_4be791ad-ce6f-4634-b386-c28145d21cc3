/*
 * Function: ?Guild_Self_Leave_Complete@CPlayer@@SAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1400A9120
 */

void __fastcall CPlayer::Guild_Self_Leave_Complete(_DB_QRY_SYN_DATA *pData)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomSystem *v3; // rax@19
  CGuildRoomSystem *v4; // rax@20
  CGuildBattleController *v5; // rax@22
  CGuildMasterEffect *v6; // rax@24
  CPlayer::CashChangeStateFlag *v7; // rax@26
  char *v8; // rax@26
  int v9; // eax@28
  __int64 v10; // [sp+0h] [bp-E8h]@1
  int nMemNum[2]; // [sp+20h] [bp-C8h]@26
  char *pszFileName; // [sp+28h] [bp-C0h]@26
  bool bPunish; // [sp+30h] [bp-B8h]@26
  char v14; // [sp+40h] [bp-A8h]@4
  CPlayer *pkPlayer; // [sp+48h] [bp-A0h]@4
  CGuild *v16; // [sp+50h] [bp-98h]@4
  unsigned int v17; // [sp+58h] [bp-90h]@4
  unsigned int dwMemberSerial; // [sp+5Ch] [bp-8Ch]@4
  int v19; // [sp+60h] [bp-88h]@4
  int v20; // [sp+64h] [bp-84h]@4
  char *v21; // [sp+68h] [bp-80h]@4
  char v22; // [sp+70h] [bp-78h]@4
  _qry_case_disjointguild v23; // [sp+88h] [bp-60h]@28
  CPlayer::CashChangeStateFlag v24; // [sp+94h] [bp-54h]@26
  CUserDB *v25; // [sp+98h] [bp-50h]@19
  int n; // [sp+A0h] [bp-48h]@19
  CGuild *v27; // [sp+A8h] [bp-40h]@19
  CUserDB *v28; // [sp+B0h] [bp-38h]@20
  int v29; // [sp+B8h] [bp-30h]@20
  CGuild *v30; // [sp+C0h] [bp-28h]@20
  char v31; // [sp+C8h] [bp-20h]@24
  char *v32; // [sp+D0h] [bp-18h]@26
  _DB_QRY_SYN_DATA *v33; // [sp+F0h] [bp+8h]@1

  v33 = pData;
  v1 = &v10;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v14 = 0;
  pkPlayer = 0i64;
  v16 = 0i64;
  v17 = -1;
  dwMemberSerial = -1;
  v19 = -1;
  v20 = -1;
  v21 = v33->m_sData;
  v17 = *(_DWORD *)&v33->m_sData[8];
  dwMemberSerial = *(_DWORD *)&v33->m_sData[0];
  v19 = *(_DWORD *)&v33->m_sData[12];
  v20 = *(_DWORD *)&v33->m_sData[16];
  v22 = 0;
  pkPlayer = &g_Player + v17;
  if ( pkPlayer->m_bLive && pkPlayer->m_dwObjSerial == dwMemberSerial )
  {
    pkPlayer->m_Param.m_bGuildLock = 0;
    v22 = 1;
  }
  if ( !v22 )
    pkPlayer = 0i64;
  v22 = 0;
  v16 = &g_Guild[v19];
  if ( CGuild::IsFill(v16) && v16->m_dwSerial == v20 )
    v22 = 1;
  if ( v22 )
  {
    if ( pkPlayer
      && pkPlayer->m_pUserDB
      && pkPlayer->m_Param.m_pGuild
      && (v25 = pkPlayer->m_pUserDB,
          n = pkPlayer->m_ObjID.m_wIndex,
          v27 = pkPlayer->m_Param.m_pGuild,
          v3 = CGuildRoomSystem::GetInstance(),
          CGuildRoomSystem::IsGuildRoomMemberIn(v3, v27->m_dwSerial, n, v25->m_dwSerial))
      && (v28 = pkPlayer->m_pUserDB,
          v29 = pkPlayer->m_ObjID.m_wIndex,
          v30 = pkPlayer->m_Param.m_pGuild,
          v4 = CGuildRoomSystem::GetInstance(),
          CGuildRoomSystem::SetPlayerOut(v4, v30->m_dwSerial, v29, v28->m_dwSerial)) )
    {
      CPlayer::SendMsg_GuildSelfLeaveResult(pkPlayer, -1);
    }
    else
    {
      v5 = CGuildBattleController::Instance();
      CGuildBattleController::LeaveGuild(v5, pkPlayer);
      if ( pkPlayer && pkPlayer->m_Param.m_byClassInGuild == 2 )
      {
        v31 = CGuild::GetGrade(v16);
        v6 = CGuildMasterEffect::GetInstance();
        CGuildMasterEffect::out_player(v6, pkPlayer, v31);
      }
      CGuild::SendMsg_LeaveMember(v16, dwMemberSerial, 1, 0);
      CGuild::PopMember(v16, dwMemberSerial);
      if ( pkPlayer )
      {
        pkPlayer->m_Param.m_pGuild = 0i64;
        CPlayer::SendMsg_GuildSelfLeaveResult(pkPlayer, 0);
        CPlayer::CashChangeStateFlag::CashChangeStateFlag(&v24, 0);
        CPlayer::UpdateVisualVer(pkPlayer, (CPlayer::CashChangeStateFlag)v7->0);
        CPlayer::SendMsg_GuildJoinOtherInform(pkPlayer);
        CPlayer::SetLastAttBuff(pkPlayer, 0);
        v32 = v16->m_szHistoryFileName;
        v8 = CPlayerDB::GetCharNameA(&pkPlayer->m_Param);
        bPunish = 0;
        pszFileName = v32;
        nMemNum[0] = v16->m_nMemberNum;
        CMgrGuildHistory::leave_member(&CGuild::s_MgrHistory, v8, pkPlayer->m_dwObjSerial, 1, nMemNum[0], v32, 0);
      }
      if ( CGuild::GetMemberNum(v16) <= 0 )
      {
        v23.in_guildserial = v16->m_dwSerial;
        v23.tmp_guildindex = v16->m_nIndex;
        v9 = _qry_case_disjointguild::size(&v23);
        CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 22, (char *)&v23, v9);
      }
    }
  }
  else if ( pkPlayer )
  {
    CPlayer::SendMsg_GuildSelfLeaveResult(pkPlayer, -1);
  }
}
