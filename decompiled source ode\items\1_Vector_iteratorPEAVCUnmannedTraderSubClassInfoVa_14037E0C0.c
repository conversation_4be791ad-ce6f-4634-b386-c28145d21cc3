/*
 * Function: ??1?$_Vector_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@QEAA@XZ
 * Address: 0x14037E0C0
 */

void __fastcall std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>((std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)&v4->_Mycont);
}
