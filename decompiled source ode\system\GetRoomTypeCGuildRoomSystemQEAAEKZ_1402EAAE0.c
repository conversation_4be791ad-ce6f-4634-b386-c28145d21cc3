/*
 * Function: ?GetRoomType@CGuildRoomSystem@@QEAAEK@Z
 * Address: 0x1402EAAE0
 */

char __fastcall CGuildRoomSystem::GetRoomType(CGuildRoomSystem *this, unsigned int dwGuildSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomInfo *v4; // rax@7
  CGuildRoomInfo *v5; // rax@8
  CGuildRoomInfo *v6; // rax@9
  __int64 v8; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CGuildRoomSystem *v10; // [sp+40h] [bp+8h]@1
  unsigned int v11; // [sp+48h] [bp+10h]@1

  v11 = dwGuildSerial;
  v10 = this;
  v2 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 90; ++j )
  {
    v4 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v10->m_vecGuildRoom, j);
    if ( CGuildRoomInfo::IsRent(v4) )
    {
      v5 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v10->m_vecGuildRoom, j);
      if ( CGuildRoomInfo::GetGuildSerial(v5) == v11 )
      {
        v6 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v10->m_vecGuildRoom, j);
        return CGuildRoomInfo::GetRoomType(v6);
      }
    }
  }
  return 2;
}
