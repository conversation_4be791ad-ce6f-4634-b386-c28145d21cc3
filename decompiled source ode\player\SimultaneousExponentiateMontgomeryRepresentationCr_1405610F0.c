/*
 * Function: ?SimultaneousExponentiate@MontgomeryRepresentation@CryptoPP@@UEBAXPEAVInteger@2@AEBV32@PEBV32@I@Z
 * Address: 0x1405610F0
 */

void __fastcall CryptoPP::MontgomeryRepresentation::SimultaneousExponentiate(CryptoPP::MontgomeryRepresentation *this, struct CryptoPP::Integer *a2, const struct CryptoPP::Integer *a3, const struct CryptoPP::Integer *a4, unsigned int a5)
{
  CryptoPP::AbstractRing<CryptoPP::Integer>::SimultaneousExponentiate((_DWORD)this);
}
