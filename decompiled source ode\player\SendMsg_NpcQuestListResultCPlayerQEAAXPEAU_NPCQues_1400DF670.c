/*
 * Function: ?SendMsg_NpcQuestListResult@CPlayer@@QEAAXPEAU_NPCQuestIndexTempData@@@Z
 * Address: 0x1400DF670
 */

void __fastcall CPlayer::SendMsg_NpcQuestListResult(CPlayer *this, _NPCQuestIndexTempData *pQuestIndexData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-F8h]@1
  _npc_quest_list_result_zocl v5; // [sp+40h] [bp-B8h]@4
  int j; // [sp+C4h] [bp-34h]@4
  char pbyType; // [sp+D4h] [bp-24h]@7
  char v8; // [sp+D5h] [bp-23h]@7
  CPlayer *v9; // [sp+100h] [bp+8h]@1
  char *v10; // [sp+108h] [bp+10h]@1

  v10 = (char *)pQuestIndexData;
  v9 = this;
  v2 = &v4;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _npc_quest_list_result_zocl::_npc_quest_list_result_zocl(&v5);
  v5.byQuestNum = *v10;
  for ( j = 0; j < *(_DWORD *)v10; ++j )
    v5.QuestIndexList[j] = *(_DWORD *)&v10[8 * j + 8];
  pbyType = 24;
  v8 = 21;
  CNetProcess::LoadSendMsg(unk_1414F2088, v9->m_ObjID.m_wIndex, &pbyType, &v5.byQuestNum, 0x79u);
}
