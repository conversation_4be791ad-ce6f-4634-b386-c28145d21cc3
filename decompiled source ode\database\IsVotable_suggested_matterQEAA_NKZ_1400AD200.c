/*
 * Function: ?IsVotable@_suggested_matter@@QEAA_NK@Z
 * Address: 0x1400AD200
 */

char __fastcall _suggested_matter::IsVotable(_suggested_matter *this, unsigned int dwSerial)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  _suggested_matter *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  for ( j = 0; j < v6->nTotal_VotableMemNum; ++j )
  {
    if ( v6->VotableMem[j]->dwSerial == dwSerial )
      return 1;
  }
  return 0;
}
