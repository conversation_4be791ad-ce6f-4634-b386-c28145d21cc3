/*
 * Function: ??0CItemLootTable@@QEAA@XZ
 * Address: 0x1402024D0
 */

void __fastcall CItemLootTable::CItemLootTable(CItemLootTable *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  CEventLootTable *v5; // [sp+28h] [bp-20h]@4
  __int64 v6; // [sp+30h] [bp-18h]@4
  CEventLootTable *v7; // [sp+38h] [bp-10h]@5
  CItemLootTable *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = -2i64;
  v8->vfptr = (CItemLootTableVtbl *)&CItemLootTable::`vftable';
  CRecordData::CRecordData(&v8->m_tblLoot);
  v8->m_ppLinkCode = 0i64;
  v5 = (CEventLootTable *)operator new(0x10ui64);
  if ( v5 )
  {
    CEventLootTable::CEventLootTable(v5);
    v7 = (CEventLootTable *)v3;
  }
  else
  {
    v7 = 0i64;
  }
  v8->m_pTblEvent = v7;
  v8->m_nLootNum = 0;
}
