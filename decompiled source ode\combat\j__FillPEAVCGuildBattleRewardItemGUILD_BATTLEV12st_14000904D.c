/*
 * Function: j_??$_Fill@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@V12@@std@@YAXPEAVCGuildBattleRewardItem@GUILD_BATTLE@@0AEBV12@@Z
 * Address: 0x14000904D
 */

void __fastcall std::_Fill<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem>(GUILD_BATTLE::CGuildBattleRewardItem *_First, GUILD_BATTLE::CGuildBattleRewardItem *_Last, GUILD_BATTLE::CGuildBattleRewardItem *_Val)
{
  std::_Fill<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem>(_First, _Last, _Val);
}
