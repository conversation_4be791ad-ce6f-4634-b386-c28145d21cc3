/*
 * Function: ?ct_set_ore_amount@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140296F50
 */

bool __fastcall ct_set_ore_amount(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  COreAmountMgr *v4; // rax@14
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int dwTot; // [sp+20h] [bp-18h]@7
  unsigned int dwRemain; // [sp+24h] [bp-14h]@7
  CPlayer *v8; // [sp+40h] [bp+8h]@1

  v8 = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !v8 || !v8->m_bOper )
    return 0;
  dwTot = 0;
  dwRemain = 0;
  if ( s_nWordCount > 2 )
    return 0;
  if ( s_nWordCount == 1 )
  {
    dwRemain = atoi(s_pwszDstCheat[0]);
  }
  else
  {
    if ( s_nWordCount != 2 )
      return 0;
    dwTot = atoi(s_pwszDstCheat[0]);
    dwRemain = atoi(s_pwszDstCheat[1]);
  }
  v4 = COreAmountMgr::Instance();
  return COreAmountMgr::CheatOreAmount(v4, dwTot, dwRemain);
}
