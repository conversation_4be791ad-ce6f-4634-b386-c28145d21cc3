/*
 * Function: ?CompleteLoadGuildBattleTotalRecord@CMainThread@@AEAAXEPEAD@Z
 * Address: 0x1401EDBD0
 */

void __fastcall CMainThread::CompleteLoadGuildBattleTotalRecord(CMainThread *this, char byRet, char *pLoadData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  char *v6; // [sp+20h] [bp-18h]@4
  CGuild *v7; // [sp+28h] [bp-10h]@5
  CMainThread *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = pLoadData;
  if ( !byRet )
  {
    v7 = GetGuildDataFromSerial(g_Guild, 500, *(_DWORD *)v6);
    if ( v7 )
      CGuild::UpdateGuildBattleWinCnt(v7, *((_DWORD *)v6 + 1), *((_DWORD *)v6 + 3), *((_DWORD *)v6 + 2));
    else
      CLogFile::Write(
        &v8->m_logSystemError,
        "CMainThread::CompleteLoadGuildBattleTotalRecord( BYTE byRet(%u), char * pLoadData ) :GetGuildDataFromSerial( g_G"
        "uild, MAX_GUILD, pSheet->dwGuildSerial(%u) ) == NULL!",
        *(_DWORD *)v6,
        *(_DWORD *)v6);
  }
}
