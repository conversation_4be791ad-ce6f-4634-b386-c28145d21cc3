/*
 * Function: ?CopyItemStoreData@CMapItemStoreList@@QEAA_NPEAV1@@Z
 * Address: 0x14034C210
 */

char __fastcall CMapItemStoreList::CopyItemStoreData(CMapItemStoreList *this, CMapItemStoreList *pDest)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  signed __int64 v5; // rax@8
  unsigned __int8 v6; // cf@10
  unsigned __int64 v7; // rax@10
  CItemStore *v8; // rcx@23
  CItemStore *v9; // rcx@23
  __int64 v10; // [sp+0h] [bp-B8h]@1
  int j; // [sp+30h] [bp-88h]@15
  int count[2]; // [sp+38h] [bp-80h]@8
  CItemStore *v13; // [sp+40h] [bp-78h]@15
  void *v14; // [sp+48h] [bp-70h]@12
  void *v15; // [sp+50h] [bp-68h]@20
  void *__t; // [sp+58h] [bp-60h]@17
  void *v17; // [sp+60h] [bp-58h]@23
  void *v18; // [sp+68h] [bp-50h]@20
  __int64 v19; // [sp+70h] [bp-48h]@4
  CItemStore *v20; // [sp+78h] [bp-40h]@13
  void *v21; // [sp+80h] [bp-38h]@18
  void *v22; // [sp+88h] [bp-30h]@21
  __int64 v23; // [sp+90h] [bp-28h]@23
  bool *v24; // [sp+98h] [bp-20h]@23
  __int64 v25; // [sp+A0h] [bp-18h]@23
  bool *v26; // [sp+A8h] [bp-10h]@23
  CMapItemStoreList *v27; // [sp+C0h] [bp+8h]@1
  CMapItemStoreList *v28; // [sp+C8h] [bp+10h]@1

  v28 = pDest;
  v27 = this;
  v2 = &v10;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v19 = -2i64;
  if ( !v27->m_bUse && pDest )
  {
    v27->m_nItemStoreNum = pDest->m_nItemStoreNum;
    if ( v27->m_nItemStoreNum <= 0 )
    {
      result = 0;
    }
    else
    {
      *(_QWORD *)count = v27->m_nItemStoreNum;
      v5 = 120i64 * *(_QWORD *)count;
      if ( !is_mul_ok(0x78ui64, *(unsigned __int64 *)count) )
        v5 = -1i64;
      v6 = __CFADD__(v5, 8i64);
      v7 = v5 + 8;
      if ( v6 )
        v7 = -1i64;
      v14 = operator new[](v7);
      if ( v14 )
      {
        *(_DWORD *)v14 = count[0];
        `eh vector constructor iterator'(
          (char *)v14 + 8,
          0x78ui64,
          count[0],
          (void (__cdecl *)(void *))CItemStore::CItemStore,
          (void (__cdecl *)(void *))CItemStore::~CItemStore);
        v20 = (CItemStore *)((char *)v14 + 8);
      }
      else
      {
        v20 = 0i64;
      }
      v13 = v20;
      v27->m_ItemStore = v20;
      memcpy_s(v27->m_ItemStore, 120i64 * v27->m_nItemStoreNum, v28->m_ItemStore, 120i64 * v28->m_nItemStoreNum);
      for ( j = 0; j < v27->m_nItemStoreNum; ++j )
      {
        v27->m_ItemStore[j].m_bUpdate = 1;
        v27->m_ItemStore[j].m_dwDBSerial = 0;
        CItemStore::SetLimitItemInitTime(&v27->m_ItemStore[j]);
        __t = operator new[](0x100ui64);
        if ( __t )
        {
          `vector constructor iterator'(
            __t,
            0x10ui64,
            16,
            (void *(__cdecl *)(void *))_limit_item_info::_limit_item_info);
          v21 = __t;
        }
        else
        {
          v21 = 0i64;
        }
        v15 = v21;
        v27->m_ItemStore[j].m_pLimitStorageItem = (_limit_item_info *)v21;
        v18 = operator new[](0x2880ui64);
        if ( v18 )
        {
          `vector constructor iterator'(
            v18,
            0x30ui64,
            216,
            (void *(__cdecl *)(void *))_good_storage_info::_good_storage_info);
          v22 = v18;
        }
        else
        {
          v22 = 0i64;
        }
        v17 = v22;
        v27->m_ItemStore[j].m_pStorageItem = (_good_storage_info *)v22;
        v8 = v28->m_ItemStore;
        v23 = 120i64 * j;
        v24 = &v27->m_ItemStore->m_bLive;
        memcpy_s(*(void **)&v24[120 * j + 80], 0x100ui64, v8[j].m_pLimitStorageItem, 0x100ui64);
        v9 = v28->m_ItemStore;
        v25 = 120i64 * j;
        v26 = &v27->m_ItemStore->m_bLive;
        memcpy_s(*(void **)&v26[120 * j + 48], 0x2590ui64, v9[j].m_pStorageItem, 0x2590ui64);
      }
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
