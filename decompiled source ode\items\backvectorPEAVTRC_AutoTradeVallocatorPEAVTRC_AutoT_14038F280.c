/*
 * Function: ?back@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAAAEAPEAVTRC_AutoTrade@@XZ
 * Address: 0x14038F280
 */

TRC_AutoTrade **__fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::back(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v3; // rax@4
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v4; // rax@4
  __int64 v6; // [sp+0h] [bp-88h]@1
  TRC_AutoTrade **v7; // [sp+20h] [bp-68h]@4
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > result; // [sp+28h] [bp-60h]@4
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > v9; // [sp+40h] [bp-48h]@4
  __int64 v10; // [sp+58h] [bp-30h]@4
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v11; // [sp+60h] [bp-28h]@4
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v12; // [sp+68h] [bp-20h]@4
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v13; // [sp+70h] [bp-18h]@4
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v14; // [sp+78h] [bp-10h]@4
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v15; // [sp+90h] [bp+8h]@1

  v15 = this;
  v1 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = -2i64;
  v3 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::end(v15, &result);
  v11 = v3;
  v12 = v3;
  v4 = std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator-(v3, &v9, 1i64);
  v13 = v4;
  v14 = v4;
  v7 = std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator*(v4);
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::~_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(&v9);
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::~_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(&result);
  return v7;
}
