/*
 * Function: j_?_Buy@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@IEAA_N_K@Z
 * Address: 0x14000AD8F
 */

bool __fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Buy(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, unsigned __int64 _Capacity)
{
  return std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Buy(this, _Capacity);
}
