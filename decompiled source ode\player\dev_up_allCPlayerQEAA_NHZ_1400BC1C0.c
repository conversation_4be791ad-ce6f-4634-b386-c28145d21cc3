/*
 * Function: ?dev_up_all@CPlayer@@QEAA_NH@Z
 * Address: 0x1400BC1C0
 */

char __fastcall CPlayer::dev_up_all(CPlayer *this, int nCum)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-98h]@1
  unsigned int dwStatIndex; // [sp+20h] [bp-78h]@6
  int v7; // [sp+38h] [bp-60h]@9
  char v8; // [sp+3Ch] [bp-5Ch]@9
  _base_fld *v9; // [sp+68h] [bp-30h]@9
  int n; // [sp+70h] [bp-28h]@9
  _base_fld *v11; // [sp+78h] [bp-20h]@23
  unsigned int dwNewCum; // [sp+80h] [bp-18h]@26
  float v13; // [sp+84h] [bp-14h]@27
  CPlayer *v14; // [sp+A0h] [bp+8h]@1
  int dwNewData; // [sp+A8h] [bp+10h]@1

  dwNewData = nCum;
  v14 = this;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( nCum >= 0 )
  {
    for ( dwStatIndex = 0; (signed int)dwStatIndex < 2; ++dwStatIndex )
    {
      CPlayer::Emb_UpdateStat(v14, dwStatIndex, dwNewData, 0);
      _MASTERY_PARAM::UpdateCumPerMast(&v14->m_pmMst, 0, dwStatIndex, dwNewData);
      CPlayer::SendMsg_StatInform(v14, dwStatIndex, dwNewData, 0);
    }
    CPlayer::Emb_UpdateStat(v14, 2u, dwNewData, 0);
    _MASTERY_PARAM::UpdateCumPerMast(&v14->m_pmMst, 1, 0, dwNewData);
    CPlayer::SendMsg_StatInform(v14, 2, dwNewData, 0);
    CPlayer::Emb_UpdateStat(v14, 3u, dwNewData, 0);
    _MASTERY_PARAM::UpdateCumPerMast(&v14->m_pmMst, 2, 0, dwNewData);
    CPlayer::SendMsg_StatInform(v14, 3, dwNewData, 0);
    v7 = 0;
    memset(&v8, 0, 0x1Cui64);
    v9 = 0i64;
    for ( n = 0; n < 48; ++n )
    {
      v9 = CRecordData::GetRecord(_MASTERY_PARAM::s_pSkillData, n);
      if ( v9 && *(_DWORD *)&v9[1].m_strCode[4] < 8 && *(_DWORD *)&v9[1].m_strCode[4] >= 0 )
      {
        if ( strncmp(v9->m_strCode, "FF", 2ui64) )
          ++*(&v7 + *(_DWORD *)&v9[1].m_strCode[4]);
      }
    }
    for ( n = 0; n < 8; ++n )
      v14->m_pmMst.m_dwSkillMasteryCum[n] = 0;
    for ( dwStatIndex = 0; (signed int)dwStatIndex < 48; ++dwStatIndex )
    {
      v11 = CRecordData::GetRecord(_MASTERY_PARAM::s_pSkillData, dwStatIndex);
      if ( v11 && strncmp(v11->m_strCode, "FF", 2ui64) )
      {
        dwNewCum = dwNewData;
        if ( *(&v7 + *(_DWORD *)&v11[1].m_strCode[4]) > 0 )
        {
          v13 = (float)(signed int)dwNewCum / (float)*(&v7 + *(_DWORD *)&v11[1].m_strCode[4]);
          dwNewCum = CalcRoundUp(v13);
        }
        CPlayer::Emb_UpdateStat(v14, dwStatIndex + 4, dwNewCum, 0);
        _MASTERY_PARAM::UpdateCumPerMast(&v14->m_pmMst, 3, dwStatIndex, dwNewCum);
        CPlayer::SendMsg_StatInform(v14, dwStatIndex + 4, dwNewCum, 0);
      }
    }
    for ( dwStatIndex = 0; (signed int)dwStatIndex < 3; ++dwStatIndex )
    {
      CPlayer::Emb_UpdateStat(v14, dwStatIndex + 76, dwNewData, 0);
      _MASTERY_PARAM::UpdateCumPerMast(&v14->m_pmMst, 5, dwStatIndex, dwNewData);
      CPlayer::SendMsg_StatInform(v14, dwStatIndex + 76, dwNewData, 0);
    }
    CPlayer::Emb_UpdateStat(v14, 0x4Fu, dwNewData, 0);
    _MASTERY_PARAM::UpdateCumPerMast(&v14->m_pmMst, 6, 0, dwNewData);
    CPlayer::SendMsg_StatInform(v14, 79, dwNewData, 0);
    for ( dwStatIndex = 0; (signed int)dwStatIndex < 24; ++dwStatIndex )
    {
      CPlayer::Emb_UpdateStat(v14, dwStatIndex + 52, dwNewData, 0);
      _MASTERY_PARAM::UpdateCumPerMast(&v14->m_pmMst, 4, dwStatIndex, dwNewData);
      CPlayer::SendMsg_StatInform(v14, dwStatIndex + 52, dwNewData, 0);
    }
    CPlayer::ReCalcMaxHFSP(v14, 1, 0);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
