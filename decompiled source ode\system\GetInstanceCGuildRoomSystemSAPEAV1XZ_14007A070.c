/*
 * Function: ?GetInstance@CGuildRoomSystem@@SAPEAV1@XZ
 * Address: 0x14007A070
 */

CGuildRoomSystem *__cdecl CGuildRoomSystem::GetInstance()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  struct CGuildRoomSystem *v2; // rax@6
  __int64 v4; // [sp+0h] [bp-48h]@1
  struct CGuildRoomSystem *v5; // [sp+20h] [bp-28h]@8
  CGuildRoomSystem *v6; // [sp+28h] [bp-20h]@5
  __int64 v7; // [sp+30h] [bp-18h]@4
  struct CGuildRoomSystem *v8; // [sp+38h] [bp-10h]@6

  v0 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  v7 = -2i64;
  if ( !CGuildRoomSystem::m_sInstance )
  {
    v6 = (CGuildRoomSystem *)operator new(0x58ui64);
    if ( v6 )
    {
      CGuildRoomSystem::CGuildRoomSystem(v6);
      v8 = v2;
    }
    else
    {
      v8 = 0i64;
    }
    v5 = v8;
    CGuildRoomSystem::m_sInstance = v8;
  }
  return CGuildRoomSystem::m_sInstance;
}
