/*
 * Function: ?end@?$_Hash@V?$_Hmap_traits@HPEAVCNationSettingFactory@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@std@@$0A@@stdext@@@stdext@@QEAA?AV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@std@@XZ
 * Address: 0x14021B4D0
 */

std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *__fastcall stdext::_Hash<stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>,0>>::end(stdext::_Hash<stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationSettingFactory *> >,0> > *this, std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *result)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  stdext::_Hash<stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationSettingFactory *> >,0> > *v7; // [sp+40h] [bp+8h]@1
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *resulta; // [sp+48h] [bp+10h]@1

  resulta = result;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::end(
    &v7->_List,
    result);
  return resulta;
}
