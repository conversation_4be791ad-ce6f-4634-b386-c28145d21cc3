/*
 * Function: ?pc_TrunkChangePasswdRequest@CPlayer@@QEAAXPEAD0E0@Z
 * Address: 0x1400F8240
 */

void __fastcall CPlayer::pc_TrunkChangePasswdRequest(CPlayer *this, char *pwszPrevPassword, char *pwszChngPassword, char byHintIndex, char *pwszHintAnswer)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char *v7; // rax@13
  char *v8; // rax@22
  char *v9; // rax@24
  __int64 v10; // [sp+0h] [bp-48h]@1
  const char *v11; // [sp+20h] [bp-28h]@22
  char *v12; // [sp+28h] [bp-20h]@22
  char v13; // [sp+30h] [bp-18h]@4
  CPlayer *p; // [sp+50h] [bp+8h]@1
  const char *Str; // [sp+58h] [bp+10h]@1
  const char *Str1; // [sp+60h] [bp+18h]@1
  char v17; // [sp+68h] [bp+20h]@1

  v17 = byHintIndex;
  Str1 = pwszChngPassword;
  Str = pwszPrevPassword;
  p = this;
  v5 = &v10;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v13 = 0;
  if ( IsBeNearStore(p, 10) )
  {
    if ( p->m_Param.m_bTrunkOpen )
    {
      if ( (signed int)(unsigned __int8)CPlayerDB::GetTrunkSlotNum(&p->m_Param) > 0 )
      {
        if ( strlen_0(Str) >= 2 && strlen_0(Str) <= 0xC )
        {
          v7 = CPlayerDB::GetTrunkPasswdW(&p->m_Param);
          if ( !strcmp_0(Str, v7) )
          {
            if ( strlen_0(Str1) >= 2 && strlen_0(Str1) <= 0xC )
            {
              if ( !strcmp_0(Str1, p->m_pUserDB->m_szUILock_PW) )
              {
                v13 = 24;
              }
              else if ( !IsSQLValidString(Str1) || !IsSQLValidString(pwszHintAnswer) )
              {
                v8 = CPlayerDB::GetCharNameA(&p->m_Param);
                v12 = pwszHintAnswer;
                v11 = Str1;
                CLogFile::Write(
                  &stru_1799C8E78,
                  "CPlayer::pc_TrunkEstRequest() : %u(%s) !::IsSQLValidString( pwszPassword(%s) ) || !::IsSQLValidString("
                  "pwszHintAnswer(%s)) Invalid!",
                  p->m_dwObjSerial,
                  v8);
                v13 = 25;
              }
            }
            else
            {
              v13 = 3;
            }
          }
          else
          {
            v13 = 1;
          }
        }
        else
        {
          v13 = 1;
        }
      }
      else
      {
        v13 = 2;
      }
    }
    else
    {
      v13 = 14;
    }
  }
  else
  {
    v13 = 13;
  }
  if ( !v13 )
  {
    strcpy_0(p->m_Param.m_wszTrunkPasswd, Str1);
    p->m_Param.m_byTrunkHintIndex = v17;
    strcpy_0(p->m_Param.m_wszTrunkHintAnswer, pwszHintAnswer);
    v9 = CPlayerDB::GetTrunkPasswdW(&p->m_Param);
    CUserDB::Update_TrunkPassword(p->m_pUserDB, v9);
    CUserDB::Update_TrunkHint(p->m_pUserDB, p->m_Param.m_byTrunkHintIndex, p->m_Param.m_wszTrunkHintAnswer);
  }
  CPlayer::SendMsg_TrunkChangPasswdResult(p, v13);
}
