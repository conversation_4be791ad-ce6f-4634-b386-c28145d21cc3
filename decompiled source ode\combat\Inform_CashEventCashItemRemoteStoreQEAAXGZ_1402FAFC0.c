/*
 * Function: ?Inform_CashEvent@CashItemRemoteStore@@QEAAXG@Z
 * Address: 0x1402FAFC0
 */

void __fastcall CashItemRemoteStore::Inform_CashEvent(CashItemRemoteStore *this, unsigned __int16 wIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  _cash_lim_sale *pLim; // [sp+20h] [bp-28h]@8
  int j; // [sp+30h] [bp-18h]@4
  CashItemRemoteStore *v7; // [sp+50h] [bp+8h]@1
  unsigned __int16 v8; // [sp+58h] [bp+10h]@1

  v8 = wIndex;
  v7 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 3; ++j )
  {
    if ( v7->m_cash_event[j].m_ini.m_bUseCashEvent )
    {
      if ( CashItemRemoteStore::IsEventTime(v7, j) )
      {
        pLim = &v7->m_lim_event;
        ICsSendInterface::SendMsg_CashEventInform(
          v8,
          j,
          v7->m_cash_event[j].m_event_status,
          &v7->m_cash_event[j].m_ini,
          &v7->m_lim_event);
      }
    }
  }
}
