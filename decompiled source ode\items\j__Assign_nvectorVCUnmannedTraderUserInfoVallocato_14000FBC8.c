/*
 * Function: j_?_Assign_n@?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@IEAAX_KAEBVCUnmannedTraderUserInfo@@@Z
 * Address: 0x14000FBC8
 */

void __fastcall std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Assign_n(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this, unsigned __int64 _Count, CUnmannedTraderUserInfo *_Val)
{
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Assign_n(this, _Count, _Val);
}
