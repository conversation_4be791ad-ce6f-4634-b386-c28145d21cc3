/*
 * Function: ?ReleaseLevel@CLevel@@QEAAXXZ
 * Address: 0x1404DFC40
 */

void __fastcall CLevel::ReleaseLevel(CLevel *this)
{
  CLevel *v1; // rbx@1
  CBsp *v2; // rcx@1
  CSkyBox *v3; // rcx@4

  v1 = this;
  v2 = this->mBsp;
  if ( v2 )
    CBsp::ReleaseBsp(v2);
  v1->mIsLoadedBsp = 0;
  if ( (unsigned int)IsServerMode() )
  {
    ReleaseMainMaterial();
  }
  else
  {
    R3ReleaseAllTextures();
    ReleaseMainMaterial();
    ReleaseLightMap();
    R3ReleasePreTextures();
    v3 = v1->mSkyBox;
    if ( v3 )
      CSkyBox::ReleaseSkyBox(v3);
    dword_184A798D0 = -1;
    v1->mLightTexMemSize = 0;
    v1->mMapTexMemSize = 0;
    v1->mSkyTexMemSize = 0;
    v1->mEntityTexMemSize = 0;
    RTMovieRelease();
    CExtDummy::ReleaseExtDummy(&v1->mDummy);
  }
}
