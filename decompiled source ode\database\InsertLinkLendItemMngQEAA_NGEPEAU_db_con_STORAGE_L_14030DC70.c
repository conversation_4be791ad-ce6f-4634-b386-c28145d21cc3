/*
 * Function: ?InsertLink@LendItemMng@@QEAA_NGEPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x14030DC70
 */

bool __fastcall LendItemMng::InsertLink(LendItemMng *this, unsigned __int16 wIdx, char byStorageCode, _STORAGE_LIST::_db_con *pkItem)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  _TimeItem_fld *v8; // [sp+20h] [bp-18h]@6
  LendItemMng *v9; // [sp+40h] [bp+8h]@1
  unsigned __int16 v10; // [sp+48h] [bp+10h]@1
  char v11; // [sp+50h] [bp+18h]@1
  _STORAGE_LIST::_db_con *pkItema; // [sp+58h] [bp+20h]@1

  pkItema = pkItem;
  v11 = byStorageCode;
  v10 = wIdx;
  v9 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( (signed int)wIdx < 2532 )
  {
    v8 = TimeItem::FindTimeRec(pkItem->m_byTableCode, pkItem->m_wItemIndex);
    if ( v8 )
    {
      if ( v8->m_nCheckType )
      {
        result = LendItemSheet::InsertLink(v9->_ppkLendItem[v10], v11, pkItema) != 0;
      }
      else
      {
        CLogFile::Write(
          &v9->_kLogSys,
          "[NO_NELD]InsertLink() >> StorageCode:%d - %I64u",
          (unsigned __int8)v11,
          pkItema->m_lnUID);
        result = 0;
      }
    }
    else
    {
      CLogFile::Write(
        &v9->_kLogSys,
        "[NO_ITEM]InsertLink() >> StorageCode:%d - %I64u",
        (unsigned __int8)v11,
        pkItema->m_lnUID);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
