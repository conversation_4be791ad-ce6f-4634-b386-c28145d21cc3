/*
 * Function: ?EnterWorld@CPartyPlayer@@QEAAXPEAU_WA_AVATOR_CODE@@G@Z
 * Address: 0x140044CA0
 */

void __fastcall CPartyPlayer::EnterWorld(CPartyPlayer *this, _WA_AVATOR_CODE *pData, unsigned __int16 wZoneIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPartyPlayer *v6; // [sp+30h] [bp+8h]@1
  _WA_AVATOR_CODE *Src; // [sp+38h] [bp+10h]@1

  Src = pData;
  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6->m_bLogin = 1;
  v6->m_wZoneIndex = wZoneIndex;
  memcpy_0(&v6->m_id, pData, 6ui64);
  strcpy_0(v6->m_wszName, Src->m_wszName);
  CPartyPlayer::PartyListInit(v6);
}
