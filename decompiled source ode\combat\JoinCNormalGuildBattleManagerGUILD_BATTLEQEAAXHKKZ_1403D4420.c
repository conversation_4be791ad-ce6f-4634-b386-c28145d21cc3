/*
 * Function: ?Join@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAXHKK@Z
 * Address: 0x1403D4420
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleManager::Join(GUILD_BATTLE::CNormalGuildBattleManager *this, int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-88h]@1
  int v7; // [sp+30h] [bp-58h]@4
  char pbyType; // [sp+44h] [bp-44h]@5
  char v9; // [sp+45h] [bp-43h]@5
  char szMsg; // [sp+64h] [bp-24h]@5
  GUILD_BATTLE::CNormalGuildBattleManager *v11; // [sp+90h] [bp+8h]@1
  int dwClientIndex; // [sp+98h] [bp+10h]@1

  dwClientIndex = n;
  v11 = this;
  v4 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7 = GUILD_BATTLE::CNormalGuildBattleManager::ProcJoin(v11, dwGuildSerial, dwCharacSerial);
  if ( v7 >= 0 )
  {
    pbyType = 27;
    v9 = 57;
    szMsg = v7;
    CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &szMsg, 1u);
  }
}
