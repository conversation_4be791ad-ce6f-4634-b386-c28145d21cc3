/*
 * Function: ?SendMsg_CumDownloadResult@CPlayer@@QEAAXXZ
 * Address: 0x1400D9360
 */

void __fastcall CPlayer::SendMsg_CumDownloadResult(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@9
  __int64 v4; // [sp+0h] [bp-1F8h]@1
  _cum_download_result_zocl Dst; // [sp+40h] [bp-1B8h]@4
  char v6; // [sp+1B4h] [bp-44h]@4
  int j; // [sp+1B8h] [bp-40h]@4
  unsigned __int16 v8; // [sp+1BCh] [bp-3Ch]@7
  char pbyType; // [sp+1C4h] [bp-34h]@9
  char v10; // [sp+1C5h] [bp-33h]@9
  unsigned __int64 v11; // [sp+1E0h] [bp-18h]@4
  CPlayer *v12; // [sp+200h] [bp+8h]@1

  v12 = this;
  v1 = &v4;
  for ( i = 124i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11 = (unsigned __int64)&v4 ^ _security_cookie;
  v12->m_bCumDownload = 1;
  _cum_download_result_zocl::_cum_download_result_zocl(&Dst);
  Dst.byRetCode = 0;
  memcpy_0(&Dst.Stat, &v12->m_pmMst.m_BaseCum, 0x140ui64);
  v6 = 0;
  for ( j = 0; j < GetMaxResKind(); ++j )
  {
    if ( (signed int)v12->m_Param.m_wCuttingResBuffer[j] > 0 )
    {
      LOBYTE(v8) = j;
      HIBYTE(v8) = v12->m_Param.m_wCuttingResBuffer[j];
      Dst.wleftResList[(unsigned __int8)v6++] = v8;
    }
  }
  Dst.byLeftCuttingResNum = v6;
  pbyType = 3;
  v10 = 8;
  v3 = _cum_download_result_zocl::size(&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, &Dst.byRetCode, v3);
}
