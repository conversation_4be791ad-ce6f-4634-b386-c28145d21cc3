/*
 * Function: ?PostReceiverCheck@CPostSystemManager@@QEAAEPEAD@Z
 * Address: 0x140326520
 */

char __fastcall CPostSystemManager::PostReceiverCheck(CPostSystemManager *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-F8h]@1
  char *v6; // [sp+30h] [bp-C8h]@4
  unsigned int pdwOutSerial; // [sp+44h] [bp-B4h]@4
  unsigned int pdwAccSerial; // [sp+64h] [bp-94h]@4
  unsigned int pdwFatigue; // [sp+84h] [bp-74h]@4
  char pbyStatus; // [sp+A4h] [bp-54h]@4
  unsigned int j; // [sp+B4h] [bp-44h]@4
  unsigned int v12; // [sp+C4h] [bp-34h]@7
  char *v13; // [sp+D8h] [bp-20h]@7
  char v14; // [sp+E0h] [bp-18h]@7
  char v15; // [sp+E1h] [bp-17h]@15
  int v16; // [sp+E4h] [bp-14h]@9
  int v17; // [sp+E8h] [bp-10h]@26

  v2 = &v5;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = pData;
  pdwOutSerial = 0;
  pdwAccSerial = 0;
  pdwFatigue = 0;
  pbyStatus = 0;
  for ( j = 0; j < *(_DWORD *)v6; ++j )
  {
    v12 = 0;
    v13 = &v6[32 * j + 4];
    CRFWorldDatabase::Update_PostRegistryDisable(pkDB, *(_DWORD *)v13);
    v14 = CRFWorldDatabase::Select_PostRecvSerialFromName(pkDB, v13 + 7, &pdwOutSerial, &pdwAccSerial, &v12);
    if ( v14 )
    {
      if ( v14 == 2 )
        v16 = 9;
      else
        v16 = 8;
      v13[28] = v16;
    }
    else
    {
      v14 = CRFWorldDatabase::Select_PlayerTimeLimitInfo(pkDB, pdwAccSerial, &pdwFatigue, &pbyStatus);
      if ( v14 && v14 != 2 )
      {
        v13[28] = 8;
      }
      else
      {
        v15 = 0;
        if ( !v13[6] )
          goto LABEL_32;
        if ( (signed int)(unsigned __int8)v13[4] >= 2 )
        {
          if ( pdwAccSerial < 0x77359400 )
            v15 = 1;
        }
        else if ( pdwAccSerial >= 0x77359400 )
        {
          v15 = 1;
        }
        if ( v15 )
        {
          v13[28] = 8;
        }
        else
        {
LABEL_32:
          if ( pbyStatus == 99 )
          {
            v13[28] = 18;
          }
          else
          {
            v17 = (unsigned __int8)v13[5];
            if ( v17 == v12 / 2 )
              *((_DWORD *)v13 + 6) = pdwOutSerial;
            else
              v13[28] = 13;
          }
        }
      }
    }
  }
  return 0;
}
