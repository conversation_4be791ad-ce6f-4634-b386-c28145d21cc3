/*
 * Function: ?P1363_MGF1KDF2_Common@CryptoPP@@YAXAEAVHashTransformation@1@PEAE_KPEBE232_NI@Z
 * Address: 0x140622290
 */

void __fastcall CryptoPP::P1363_MGF1KDF2_Common(CryptoPP *this, struct CryptoPP::HashTransformation *a2, unsigned __int8 *a3, unsigned __int8 *a4, const unsigned __int8 *a5, unsigned __int64 a6, const unsigned __int8 *a7, unsigned __int64 a8, int a9, unsigned int a10)
{
  CryptoPP::BufferedTransformation v10; // [sp+40h] [bp-D8h]@10
  CryptoPP::ArraySink *v11; // [sp+B0h] [bp-68h]@10
  unsigned int v12; // [sp+B8h] [bp-60h]@10
  CryptoPP::ArraySink *v13; // [sp+C0h] [bp-58h]@5
  CryptoPP::ArrayXorSink *v14; // [sp+C8h] [bp-50h]@2
  CryptoPP::ArraySink *v15; // [sp+D0h] [bp-48h]@9
  CryptoPP::ArraySink *v16; // [sp+D8h] [bp-40h]@6
  __int64 v17; // [sp+E0h] [bp-38h]@1
  CryptoPP::ArraySink *v18; // [sp+E8h] [bp-30h]@3
  CryptoPP::ArraySink *v19; // [sp+F0h] [bp-28h]@5
  CryptoPP::ArraySink *v20; // [sp+F8h] [bp-20h]@7
  unsigned int v21; // [sp+100h] [bp-18h]@12
  struct CryptoPP::HashTransformation *v22; // [sp+120h] [bp+8h]@1
  unsigned __int8 *v23; // [sp+128h] [bp+10h]@1
  unsigned __int8 *v24; // [sp+130h] [bp+18h]@1
  unsigned __int8 *v25; // [sp+138h] [bp+20h]@1

  v25 = a4;
  v24 = a3;
  v23 = (unsigned __int8 *)a2;
  v22 = (struct CryptoPP::HashTransformation *)this;
  v17 = -2i64;
  if ( (_BYTE)a8 )
  {
    v14 = (CryptoPP::ArrayXorSink *)operator new(0x30ui64);
    if ( v14 )
      v18 = (CryptoPP::ArraySink *)CryptoPP::ArrayXorSink::ArrayXorSink(v14, v23, (unsigned __int64)v24);
    else
      v18 = 0i64;
    v13 = v18;
    v19 = v18;
  }
  else
  {
    v16 = (CryptoPP::ArraySink *)operator new(0x30ui64);
    if ( v16 )
      v20 = CryptoPP::ArraySink::ArraySink(v16, v23, (unsigned __int64)v24);
    else
      v20 = 0i64;
    v15 = v20;
    v19 = v20;
  }
  v11 = v19;
  CryptoPP::HashFilter::HashFilter(
    (CryptoPP::HashFilter *)&v10,
    v22,
    (struct CryptoPP::BufferedTransformation *)&v19->vfptr,
    0,
    -1);
  v12 = a9;
  while ( CryptoPP::ArraySink::AvailableSize(v11) )
  {
    CryptoPP::BufferedTransformation::Put(&v10, v25, (__int64)a5);
    v21 = v12;
    CryptoPP::BufferedTransformation::PutWord32(&v10, v12++, BIG_ENDIAN_ORDER);
    CryptoPP::BufferedTransformation::Put(&v10, (const unsigned __int8 *)a6, (__int64)a7);
    CryptoPP::BufferedTransformation::MessageEnd(&v10, -1);
  }
  CryptoPP::HashFilter::~HashFilter((CryptoPP::HashFilter *)&v10);
}
