/*
 * Function: ?EnterPlayer@CDarkHole@@QEAA_NPEAVCPlayer@@PEAVCMapData@@GPEAM_N@Z
 * Address: 0x140163BC0
 */

bool __fastcall CDarkHole::EnterPlayer(CDarkHole *this, CPlayer *pEnter, CMapData *pOldMap, unsigned __int16 wOldLayer, float *fOldPos, bool bReconnect)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v9; // [sp+0h] [bp-38h]@1
  unsigned __int16 wLastLayer; // [sp+20h] [bp-18h]@6
  float *pfOldPos; // [sp+28h] [bp-10h]@6
  CDarkHole *v12; // [sp+40h] [bp+8h]@1

  v12 = this;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( v12->m_pChannel )
  {
    pfOldPos = fOldPos;
    wLastLayer = wOldLayer;
    result = CDarkHoleChannel::PushMember(v12->m_pChannel, pEnter, bReconnect, pOldMap, wOldLayer, fOldPos);
  }
  else
  {
    result = 0;
  }
  return result;
}
