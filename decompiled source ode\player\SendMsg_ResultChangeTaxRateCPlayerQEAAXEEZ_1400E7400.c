/*
 * Function: ?SendMsg_ResultChangeTaxRate@CPlayer@@QEAAXEE@Z
 * Address: 0x1400E7400
 */

void __fastcall CPlayer::SendMsg_ResultChangeTaxRate(CPlayer *this, char byRetCode, char byNextTax)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@4
  __int64 v6; // [sp+0h] [bp-78h]@1
  _pt_result_change_tax_rate_zocl v7; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4
  CPlayer *v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v3 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7.byRet = byRetCode;
  v7.byNextTax = byNextTax;
  pbyType = 13;
  v9 = 121;
  v5 = _pt_result_change_tax_rate_zocl::size(&v7);
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, &v7.byRet, v5);
}
