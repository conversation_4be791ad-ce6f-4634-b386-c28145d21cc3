/*
 * Function: ?Init@CGuildBattleRewardItem@GUILD_BATTLE@@QEAA_NG@Z
 * Address: 0x1403C8F30
 */

char __fastcall GUILD_BATTLE::CGuildBattleRewardItem::Init(GUILD_BATTLE::CGuildBattleRewardItem *this, unsigned __int16 usInx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-D58h]@1
  char Dst; // [sp+40h] [bp-D18h]@4
  char ReturnedString; // [sp+460h] [bp-8F8h]@4
  char v8; // [sp+C80h] [bp-D8h]@6
  char v9; // [sp+CC0h] [bp-98h]@6
  char *ppszDst; // [sp+D18h] [bp-40h]@6
  char *Str; // [sp+D20h] [bp-38h]@6
  int v12; // [sp+D34h] [bp-24h]@6
  unsigned __int64 v13; // [sp+D40h] [bp-18h]@4
  GUILD_BATTLE::CGuildBattleRewardItem *v14; // [sp+D60h] [bp+8h]@1
  unsigned __int16 v15; // [sp+D68h] [bp+10h]@1

  v15 = usInx;
  v14 = this;
  v2 = &v5;
  for ( i = 852i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  memset_0(&Dst, 0, 0x400ui64);
  memset_0(&ReturnedString, 0, 0x800ui64);
  sprintf(&Dst, "item%d", v15);
  GetPrivateProfileStringA("RewardItem", &Dst, "X", &ReturnedString, 0x800u, "./Initialize/NormalGuildBattle.ini");
  if ( !strcmp_0(&ReturnedString, "X") )
  {
    result = 0;
  }
  else
  {
    memset_0(&v8, 0, 0x80ui64);
    ppszDst = &v8;
    Str = &v9;
    v12 = ParsingCommandA(&ReturnedString, 2, &ppszDst, 64);
    if ( v12 )
    {
      if ( GUILD_BATTLE::CGuildBattleRewardItem::SetItem(v14, ppszDst) )
      {
        v14->m_ucD = atoi(Str);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  return result;
}
