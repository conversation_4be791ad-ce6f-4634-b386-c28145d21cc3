/*
 * Function: ?UpdateVotedReset_General@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x1404C7760
 */

bool __fastcall CRFWorldDatabase::UpdateVotedReset_General(CRFWorldDatabase *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-4F8h]@1
  unsigned int v6; // [sp+20h] [bp-4D8h]@10
  int v7; // [sp+28h] [bp-4D0h]@10
  int v8; // [sp+30h] [bp-4C8h]@10
  int v9; // [sp+38h] [bp-4C0h]@10
  int v10; // [sp+40h] [bp-4B8h]@10
  char strQuery; // [sp+60h] [bp-498h]@4
  char v12; // [sp+61h] [bp-497h]@4
  _SYSTEMTIME SystemTime; // [sp+478h] [bp-80h]@4
  char DstBuf; // [sp+4A8h] [bp-50h]@4
  char v15; // [sp+4A9h] [bp-4Fh]@4
  unsigned __int16 v16; // [sp+4D4h] [bp-24h]@4
  unsigned __int64 v17; // [sp+4E0h] [bp-18h]@4
  CRFWorldDatabase *v18; // [sp+500h] [bp+8h]@1
  unsigned int v19; // [sp+508h] [bp+10h]@1

  v19 = dwSerial;
  v18 = this;
  v2 = &v5;
  for ( i = 316i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v17 = (unsigned __int64)&v5 ^ _security_cookie;
  strQuery = 0;
  memset(&v12, 0, 0x3FFui64);
  GetLocalTime(&SystemTime);
  DstBuf = 0;
  memset(&v15, 0, 0x1Dui64);
  v16 = 0;
  if ( (signed int)SystemTime.wDay >= 8 )
  {
    v16 = SystemTime.wDay;
  }
  else
  {
    v16 = SystemTime.wDay + 23;
    if ( (signed int)SystemTime.wMonth >= 2 )
    {
      --SystemTime.wMonth;
    }
    else
    {
      SystemTime.wMonth = 12;
      --SystemTime.wYear;
    }
  }
  v10 = SystemTime.wSecond;
  v9 = SystemTime.wMinute;
  v8 = SystemTime.wHour;
  v7 = v16 - 7;
  v6 = SystemTime.wMonth;
  sprintf_s(&DstBuf, 0x1Eui64, "%d-%d-%d %d:%d:%d", SystemTime.wYear);
  v6 = v19;
  sprintf_s(&strQuery, 0x400ui64, "Update tbl_general Set tmRaceBossVote = '%s' Where serial = %d", &DstBuf);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v18->vfptr, &strQuery, 1);
}
