/*
 * Function: ?ct_CdeStart@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140295EA0
 */

bool __fastcall ct_CdeStart(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  CashItemRemoteStore *v4; // rax@13
  __int64 v5; // [sp+0h] [bp-68h]@1
  int iBegin_TT; // [sp+38h] [bp-30h]@8
  int iB30_TT; // [sp+3Ch] [bp-2Ch]@8
  int iB5_TT; // [sp+40h] [bp-28h]@8
  int v9; // [sp+44h] [bp-24h]@8
  CPlayer *v10; // [sp+70h] [bp+8h]@1

  v10 = pOne;
  v1 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v10 )
  {
    if ( s_nWordCount >= 4 )
    {
      iBegin_TT = 0;
      iB30_TT = 0;
      iB5_TT = 0;
      v9 = 0;
      iBegin_TT = atoi(s_pwszDstCheat[0]);
      iB30_TT = atoi(s_pwszDstCheat[1]);
      iB5_TT = atoi(s_pwszDstCheat[2]);
      v9 = atoi(s_pwszDstCheat[3]);
      if ( iBegin_TT > 0 && iB30_TT > 0 && iB5_TT > 0 && v9 > 0 )
      {
        v4 = CashItemRemoteStore::Instance();
        result = CashItemRemoteStore::start_cde(v4, iBegin_TT, iB30_TT, iB5_TT, v9);
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
