/*
 * Function: ??1CMgrAvatorItemHistory@@QEAA@XZ
 * Address: 0x140235D50
 */

void __fastcall CMgrAvatorItemHistory::~CMgrAvatorItemHistory(CMgrAvatorItemHistory *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CMgrAvatorItemHistory *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  v5->m_bIOThread = 0;
  CFrameRate::~CFrameRate(&v5->m_FrameRate);
  CNetIndexList::~CNetIndexList(&v5->m_listLogDataEmpty_200);
  CNetIndexList::~CNetIndexList(&v5->m_listLogData_200);
  CNetIndexList::~CNetIndexList(&v5->m_listLogDataEmpty_1K);
  CNetIndexList::~CNetIndexList(&v5->m_listLogData_1K);
  CNetIndexList::~CNetIndexList(&v5->m_listLogDataEmpty_10K);
  CNetIndexList::~CNetIndexList(&v5->m_listLogData_10K);
  CMyTimer::~CMyTimer(&v5->m_tmrUpdateTime);
}
