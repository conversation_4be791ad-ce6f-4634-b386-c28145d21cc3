/*
 * Function: ?_<PERSON>len@?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@KAXXZ
 * Address: 0x140368020
 */

void __noreturn std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Xlen()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-B8h]@1
  std::length_error v3; // [sp+20h] [bp-98h]@4
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > _Message; // [sp+68h] [bp-50h]@4
  unsigned __int8 v5; // [sp+98h] [bp-20h]@4
  __int64 v6; // [sp+A0h] [bp-18h]@4

  v0 = &v2;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  v6 = -2i64;
  memset(&v5, 0, sizeof(v5));
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
    &_Message,
    "vector<T> too long",
    v5);
  std::length_error::length_error(&v3, &_Message);
  CxxThrowException_0(&v3, &TI3_AVlength_error_std__);
}
