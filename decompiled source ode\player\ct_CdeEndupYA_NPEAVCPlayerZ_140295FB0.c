/*
 * Function: ?ct_CdeEndup@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140295FB0
 */

char __fastcall ct_CdeEndup(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CashItemRemoteStore *v3; // rax@4
  __int64 v5; // [sp+0h] [bp-28h]@1

  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = CashItemRemoteStore::Instance();
  CashItemRemoteStore::force_endup_cash_discount_event(v3);
  return 1;
}
