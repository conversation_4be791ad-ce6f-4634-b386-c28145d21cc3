/*
 * Function: ??R?$NewObject@VDL_SignatureMessageEncodingMethod_DSA@CryptoPP@@@CryptoPP@@QEBAPEAVDL_SignatureMessageEncodingMethod_DSA@1@XZ
 * Address: 0x14058CE00
 */

__int64 CryptoPP::NewObject<CryptoPP::DL_SignatureMessageEncodingMethod_DSA>::operator()()
{
  CryptoPP::DL_SignatureMessageEncodingMethod_DSA *v1; // [sp+20h] [bp-18h]@1
  __int64 v2; // [sp+28h] [bp-10h]@2

  v1 = (CryptoPP::DL_SignatureMessageEncodingMethod_DSA *)operator new(8ui64);
  if ( v1 )
    v2 = CryptoPP::DL_SignatureMessageEncodingMethod_DSA::DL_SignatureMessageEncodingMethod_DSA(v1);
  else
    v2 = 0i64;
  return v2;
}
