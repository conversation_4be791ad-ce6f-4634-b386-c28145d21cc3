/*
 * Function: j_?Max<PERSON>laintextLength@?$DL_CryptoSystemBase@VPK_Encryptor@CryptoPP@@V?$DL_PublicKey@UECPPoint@CryptoPP@@@2@@CryptoPP@@UEBA_K_K@Z
 * Address: 0x14000A876
 */

unsigned __int64 __fastcall CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>>::MaxPlaintextLength(CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> > *this, unsigned __int64 ciphertextLength)
{
  return CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>>::Max<PERSON>laintextLength(
           this,
           ciphertextLength);
}
