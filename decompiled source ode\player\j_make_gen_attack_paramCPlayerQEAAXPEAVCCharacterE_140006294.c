/*
 * Function: j_?make_gen_attack_param@CPlayer@@QEAAXPEAVCCharacter@@EPEAU_BulletItem_fld@@MPEAU_attack_param@@1M@Z
 * Address: 0x140006294
 */

void __fastcall CPlayer::make_gen_attack_param(CPlayer *this, CCharacter *pDst, char by<PERSON>art, _BulletItem_fld *pBulletFld, float fAddBulletFc, _attack_param *pAP, _BulletItem_fld *pEffBtFld, float fAddEffBtFc)
{
  CPlayer::make_gen_attack_param(this, pDst, byPart, pBulletFld, fAddBulletFc, pAP, pEffBtFld, fAddEffBtFc);
}
