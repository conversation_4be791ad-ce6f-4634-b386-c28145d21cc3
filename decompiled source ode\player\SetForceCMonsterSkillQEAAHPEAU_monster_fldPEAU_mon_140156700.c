/*
 * Function: ?<PERSON><PERSON><PERSON><PERSON>@CMonsterSkill@@QEAAHPEAU_monster_fld@@PEAU_monster_sp_fld@@HPEAU_force_fld@@KMKHHH@Z
 * Address: 0x140156700
 */

signed __int64 __fastcall CMonsterSkill::SetForce(CMonsterSkill *this, _monster_fld *pMonsterFld, _monster_sp_fld *pSPCont, int nSFLv, _force_fld *pForceFld, unsigned int dwDelayTime, float fAttackDist, unsigned int dwCastDelay, int nMotive, int nMotiveValue, int skillDestType)
{
  __int64 *v11; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v14; // [sp+0h] [bp-38h]@1
  int v15; // [sp+20h] [bp-18h]@6
  float v16; // [sp+24h] [bp-14h]@9
  CMonsterSkill *v17; // [sp+40h] [bp+8h]@1
  _monster_fld *v18; // [sp+48h] [bp+10h]@1
  _monster_sp_fld *v19; // [sp+50h] [bp+18h]@1
  int v20; // [sp+58h] [bp+20h]@1

  v20 = nSFLv;
  v19 = pSPCont;
  v18 = pMonsterFld;
  v17 = this;
  v11 = &v14;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v11 = -858993460;
    v11 = (__int64 *)((char *)v11 + 4);
  }
  CMonsterSkill::Init(v17);
  v17->m_nSFCode = 3;
  v17->m_pSF_Fld = (_base_fld *)pForceFld;
  v17->m_pSPConst = v19;
  if ( v17->m_pSF_Fld )
  {
    v17->m_wSFIndex = v17->m_pSF_Fld->m_dwIndex;
    v17->m_dwDelayTime = dwDelayTime;
    v17->m_fAttackDist = fAttackDist;
    v17->m_dwCastDelay = dwCastDelay;
    v17->m_nSFLv = v20;
    v17->m_Element = pForceFld->m_nProperty;
    v17->m_StdDmg = (signed int)ffloor(v18->m_fAttFcStd);
    v15 = (signed int)ffloor((float)v17->m_StdDmg * 0.89999998);
    if ( v17->m_StdDmg - v15 <= 0 )
      v17->m_MinDmg = 0;
    else
      v17->m_MinDmg = rand() % (v17->m_StdDmg - v15) + v15;
    v17->m_MaxDmg = 2 * v17->m_StdDmg - v17->m_MinDmg;
    v16 = (float)(v17->m_MaxDmg + 125) / (float)(v17->m_MaxDmg + 50);
    v17->m_MinProb = (signed int)ffloor(v18->m_fMinAFSelProb);
    v17->m_MaxProb = (signed int)ffloor(v18->m_fMaxAFSelProb);
    v17->m_UseType = _Check_SF_UseType((_base_fld *)&pForceFld->m_dwIndex, 1);
    v17->m_nMotive = nMotive;
    v17->m_nMotivevalue = nMotiveValue;
    v17->m_nCaseType = skillDestType;
    v17->m_bExit = 1;
    result = 1i64;
  }
  else
  {
    CMonsterSkill::Init(v17);
    result = 0i64;
  }
  return result;
}
