/*
 * Function: ??0?$AlgorithmImpl@V?$DL_EncryptorBase@VInteger@CryptoPP@@@CryptoPP@@U?$DLIES@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@CryptoPP@@$00@2@@CryptoPP@@QEAA@XZ
 * Address: 0x1406371B0
 */

__int64 __fastcall CryptoPP::AlgorithmImpl<CryptoPP::DL_EncryptorBase<CryptoPP::Integer>,CryptoPP::DLIES<CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,1>>::AlgorithmImpl<CryptoPP::DL_EncryptorBase<CryptoPP::Integer>,CryptoPP::DLIES<CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,1>>(__int64 a1)
{
  __int64 v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  CryptoPP::DL_EncryptorBase<CryptoPP::Integer>::DL_EncryptorBase<CryptoPP::Integer>();
  return v2;
}
