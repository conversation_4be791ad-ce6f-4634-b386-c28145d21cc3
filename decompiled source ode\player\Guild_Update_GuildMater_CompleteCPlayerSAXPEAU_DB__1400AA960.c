/*
 * Function: ?Guild_Update_GuildMater_Complete@CPlayer@@SAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1400AA960
 */

void __fastcall CPlayer::Guild_Update_GuildMater_Complete(_DB_QRY_SYN_DATA *pData)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-68h]@1
  int v4; // [sp+30h] [bp-38h]@4
  int v5; // [sp+34h] [bp-34h]@4
  unsigned int in_guild_prev_masterSerial; // [sp+38h] [bp-30h]@4
  char v7; // [sp+3Ch] [bp-2Ch]@4
  unsigned int in_guild_new_masterSerial; // [sp+40h] [bp-28h]@4
  char v9; // [sp+44h] [bp-24h]@4
  char *v10; // [sp+48h] [bp-20h]@4
  CGuild *v11; // [sp+50h] [bp-18h]@4
  _DB_QRY_SYN_DATA *v12; // [sp+70h] [bp+8h]@1

  v12 = pData;
  v1 = &v3;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -1;
  v5 = -1;
  v10 = v12->m_sData;
  v4 = *(_DWORD *)&v12->m_sData[4];
  v5 = *(_DWORD *)&v12->m_sData[0];
  in_guild_prev_masterSerial = *(_DWORD *)&v12->m_sData[8];
  v7 = v12->m_sData[12];
  in_guild_new_masterSerial = *(_DWORD *)&v12->m_sData[16];
  v9 = v12->m_sData[20];
  v11 = &g_Guild[v4];
  if ( v11 && v11->m_dwSerial == v5 && !v12->m_byResult )
    CGuild::DB_Update_GuildMaster_Complete(v11, in_guild_prev_masterSerial, v7, in_guild_new_masterSerial, v9);
}
