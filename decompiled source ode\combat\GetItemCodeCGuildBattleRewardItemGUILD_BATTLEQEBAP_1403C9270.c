/*
 * Function: ?GetItemCode@CGuildBattleRewardItem@GUILD_BATTLE@@QEBAPEADXZ
 * Address: 0x1403C9270
 */

char *__fastcall GUILD_BATTLE::CGuildBattleRewardItem::GetItemCode(GUILD_BATTLE::CGuildBattleRewardItem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  const char *v4; // [sp+0h] [bp-18h]@1
  GUILD_BATTLE::CGuildBattleRewardItem *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = (__int64 *)&v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5->m_pFld )
    v4 = v5->m_pFld->m_strCode;
  else
    v4 = "None";
  return (char *)v4;
}
