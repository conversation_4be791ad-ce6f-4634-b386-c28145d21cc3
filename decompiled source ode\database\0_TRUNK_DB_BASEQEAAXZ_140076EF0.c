/*
 * Function: ??0_TRUNK_DB_BASE@@QEAA@XZ
 * Address: 0x140076EF0
 */

void __fastcall _TRUNK_DB_BASE::_TRUNK_DB_BASE(_TRUNK_DB_BASE *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _TRUNK_DB_BASE *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  `vector constructor iterator'(v4->m_List, 0x26ui64, 100, (void *(__cdecl *)(void *))_TRUNK_DB_BASE::_LIST::_LIST);
  `vector constructor iterator'(v4->m_ExtList, 0x26ui64, 40, (void *(__cdecl *)(void *))_TRUNK_DB_BASE::_LIST::_LIST);
  _TRUNK_DB_BASE::Init(v4);
}
