/*
 * Function: _std::vector_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo___::erase_::_1_::dtor$0
 * Address: 0x140367EC0
 */

void __fastcall std::vector_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo___::erase_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::~_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(*(std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > **)(a2 + 104));
}
