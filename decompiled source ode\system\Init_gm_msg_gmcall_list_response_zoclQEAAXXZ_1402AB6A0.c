/*
 * Function: ?Init@_gm_msg_gmcall_list_response_zocl@@QEAAXXZ
 * Address: 0x1402AB6A0
 */

void __fastcall _gm_msg_gmcall_list_response_zocl::Init(_gm_msg_gmcall_list_response_zocl *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _gm_msg_gmcall_list_response_zocl *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->m_nCurrPageIndex = -1;
  v5->m_MaxPage = -1;
  v5->m_nCurrPageUserNum = -1;
  v5->m_nMaxUser = -1;
  for ( j = 0; j < 10; ++j )
    _gm_msg_gmcall_list_response_zocl::_call_node::Init(&v5->m_arCallNode[j]);
}
