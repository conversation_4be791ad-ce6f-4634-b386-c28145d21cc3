/*
 * Function: ?Find@CReservedGuildScheduleDayGroup@GUILD_BATTLE@@QEAAPEAVCReservedGuildSchedulePage@2@K@Z
 * Address: 0x1403CCFA0
 */

GUILD_BATTLE::CReservedGuildSchedulePage *__fastcall GUILD_BATTLE::CReservedGuildScheduleDayGroup::Find(GUILD_BATTLE::CReservedGuildScheduleDayGroup *this, unsigned int dwGuildSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CReservedGuildSchedulePage *v6; // [sp+20h] [bp-18h]@4
  unsigned int j; // [sp+28h] [bp-10h]@4
  GUILD_BATTLE::CReservedGuildScheduleDayGroup *v8; // [sp+40h] [bp+8h]@1
  unsigned int dwGuildSeriala; // [sp+48h] [bp+10h]@1

  dwGuildSeriala = dwGuildSerial;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0i64;
  for ( j = 0; j < v8->m_uiMapCnt; ++j )
  {
    v6 = GUILD_BATTLE::CReservedGuildScheduleMapGroup::Find(&v8->m_pkList[j], dwGuildSeriala);
    if ( v6 )
      return v6;
  }
  return 0i64;
}
