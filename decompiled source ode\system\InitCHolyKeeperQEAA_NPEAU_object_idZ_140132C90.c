/*
 * Function: ?Init@CHoly<PERSON>eeper@@QEAA_NPEAU_object_id@@@Z
 * Address: 0x140132C90
 */

char __fastcall CHolyKeeper::Init(<PERSON><PERSON><PERSON>eeper *this, _object_id *pID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CHolyKeeper *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CCharacter::Init((CCharacter *)&v7->vfptr, pID);
  v7->m_dwLastDestroyTime = 0;
  v7->m_nMasterRace = -1;
  v7->m_pPosCreate = 0i64;
  v7->m_pPosActive = 0i64;
  v7->m_pPosCenter = 0i64;
  v7->m_nHP = 0;
  v7->m_pRec = 0i64;
  v7->m_bExit = 0;
  v7->m_bChaos = 0;
  v7->m_dwLastStopTime = -1;
  for ( j = 0; j < 5; ++j )
    v7->m_nDefPart[j] = 0;
  v7->m_pLastMoveTarget = 0i64;
  v7->m_at = 0i64;
  v7->m_bDamageAbleState = 0;
  v7->m_nCurrLootIndex = -1;
  v7->m_nEndLootIndex = -1;
  v7->m_nCurrDropIndex = 0;
  v7->m_wMagnifications = 0;
  v7->m_wRange = 0;
  v7->m_wDropCntOnce = 0;
  return 1;
}
