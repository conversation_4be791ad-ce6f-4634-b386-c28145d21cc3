/*
 * Function: _std::_Vector_iterator_CUnmannedTraderDivisionInfo_____ptr64_std::allocator_CUnmannedTraderDivisionInfo_____ptr64___::operator__::_1_::dtor$1
 * Address: 0x14038A600
 */

void __fastcall std::_Vector_iterator_CUnmannedTraderDivisionInfo_____ptr64_std::allocator_CUnmannedTraderDivisionInfo_____ptr64___::operator__::_1_::dtor_1(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 68) & 1 )
  {
    *(_DWORD *)(a2 + 68) &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(*(std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > **)(a2 + 104));
  }
}
