/*
 * Function: ?ct_goto_monster@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140295DD0
 */

bool __fastcall ct_goto_monster(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@7
  CMonster *pMon; // [sp+28h] [bp-10h]@10
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      v5 = atoi(s_pwszDstCheat[0]);
      if ( v5 >= 0 || v5 < 30000 )
      {
        pMon = (CMonster *)((char *)g_Monster + 6424 * v5);
        if ( pMon && pMon->m_bLive && pMon->m_bOper )
          result = CPlayer::dev_goto_monster(v7, pMon);
        else
          result = 0;
      }
      else
      {
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
