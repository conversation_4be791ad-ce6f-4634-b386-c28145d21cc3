/*
 * Function: ?IsolatedInitialize@HexDecoder@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x14063E1F0
 */

void __fastcall CryptoPP::HexDecoder::IsolatedInitialize(CryptoPP::HexDecoder *this, const struct CryptoPP::NameValuePairs *a2)
{
  CryptoPP::Name *v2; // rcx@1
  CryptoPP::Name *v3; // rcx@1
  CryptoPP::CombinedNameValuePairs v4; // [sp+30h] [bp-E8h]@1
  const int *v5; // [sp+48h] [bp-D0h]@1
  char v6; // [sp+50h] [bp-C8h]@1
  int v7; // [sp+78h] [bp-A0h]@1
  char v8; // [sp+80h] [bp-98h]@1
  __int64 v9; // [sp+C8h] [bp-50h]@1
  const char *v10; // [sp+D0h] [bp-48h]@1
  __int64 v11; // [sp+D8h] [bp-40h]@1
  __int64 v12; // [sp+E0h] [bp-38h]@1
  struct CryptoPP::NameValuePairs *v13; // [sp+E8h] [bp-30h]@1
  struct CryptoPP::NameValuePairs *v14; // [sp+F0h] [bp-28h]@1
  struct CryptoPP::NameValuePairs *v15; // [sp+F8h] [bp-20h]@1
  struct CryptoPP::NameValuePairs *v16; // [sp+100h] [bp-18h]@1
  CryptoPP::HexDecoder *v17; // [sp+120h] [bp+8h]@1
  struct CryptoPP::NameValuePairs *v18; // [sp+128h] [bp+10h]@1

  v18 = (struct CryptoPP::NameValuePairs *)a2;
  v17 = this;
  v9 = -2i64;
  v7 = 4;
  v5 = CryptoPP::HexDecoder::GetDefaultDecodingLookupArray();
  v10 = CryptoPP::Name::Log2Base(v2);
  CryptoPP::Name::DecodingLookupArray(v3);
  v11 = CryptoPP::MakeParameters<int const *>((__int64)&v6);
  v12 = v11;
  v13 = (struct CryptoPP::NameValuePairs *)CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,int const *>::operator()<int>(v11);
  v14 = v13;
  v15 = (struct CryptoPP::NameValuePairs *)CryptoPP::CombinedNameValuePairs::CombinedNameValuePairs(&v4, v18, v13);
  v16 = v15;
  CryptoPP::BaseN_Decoder::IsolatedInitialize(v17, v15);
  CryptoPP::CombinedNameValuePairs::~CombinedNameValuePairs(&v4);
  CryptoPP::AlgorithmParameters<CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,int const *>,int>::~AlgorithmParameters<CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,int const *>,int>((__int64)&v8);
  CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,int const *>::~AlgorithmParameters<CryptoPP::NullNameValuePairs,int const *>((__int64)&v6);
}
