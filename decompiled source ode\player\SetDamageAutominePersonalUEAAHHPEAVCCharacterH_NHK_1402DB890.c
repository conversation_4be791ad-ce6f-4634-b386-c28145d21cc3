/*
 * Function: ?SetDamage@AutominePersonal@@UEAAHHPEAVCCharacter@@H_NHK1@Z
 * Address: 0x1402DB890
 */

int __fastcall AutominePersonal::SetDamage(AutominePersonal *this, int nDam, CCharacter *pDst, int nDstLv, bool bCrt, int nAttackType, unsigned int dwAttackSerial, bool bJadeReturn)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@5
  _STORAGE_LIST::_db_con *v11; // rcx@7
  __int64 v12; // [sp+0h] [bp-48h]@1
  bool bUpdate; // [sp+20h] [bp-28h]@7
  bool bSend; // [sp+28h] [bp-20h]@7
  int *v15; // [sp+30h] [bp-18h]@6
  AutominePersonal *v16; // [sp+50h] [bp+8h]@1

  v16 = this;
  v8 = &v12;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  if ( v16->m_pItem )
  {
    v15 = &v16->m_pOwner->m_Param.m_pStoragePtr[0]->m_nListNum;
    if ( nDam > 1 )
    {
      v11 = v16->m_pItem;
      bSend = 1;
      bUpdate = 0;
      CPlayer::Emb_AlterDurPoint(v16->m_pOwner, 0, v11->m_byStorageIndex, -nDam, 0, 1);
    }
    if ( ((int (__fastcall *)(AutominePersonal *))v16->vfptr->GetHP)(v16) > 0 )
    {
      AutominePersonal::send_attacked(v16);
      result = ((int (__fastcall *)(AutominePersonal *))v16->vfptr->GetHP)(v16);
    }
    else
    {
      CMgrAvatorItemHistory::consume_del_item(
        &CPlayer::s_MgrItemHistory,
        v16->m_pOwner->m_id.wIndex,
        v16->m_pItem,
        v16->m_pOwner->m_szItemHistoryFileName);
      AutominePersonal::unregist_from_map(v16, 1);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
