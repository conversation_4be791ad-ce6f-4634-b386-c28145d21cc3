/*
 * Function: ?IsRoomRented@CGuildRoomSystem@@QEAA_NK@Z
 * Address: 0x1402EA540
 */

char __fastcall CGuildRoomSystem::IsRoomRented(CGuildRoomSystem *this, unsigned int dwGuildSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomInfo *v4; // rax@7
  CGuildRoomInfo *v5; // rax@8
  __int64 v7; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CGuildRoomSystem *v9; // [sp+40h] [bp+8h]@1
  unsigned int v10; // [sp+48h] [bp+10h]@1

  v10 = dwGuildSerial;
  v9 = this;
  v2 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 90; ++j )
  {
    v4 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v9->m_vecGuildRoom, j);
    if ( CGuildRoomInfo::IsRent(v4) )
    {
      v5 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v9->m_vecGuildRoom, j);
      if ( CGuildRoomInfo::GetGuildSerial(v5) == v10 )
        return 1;
    }
  }
  return 0;
}
