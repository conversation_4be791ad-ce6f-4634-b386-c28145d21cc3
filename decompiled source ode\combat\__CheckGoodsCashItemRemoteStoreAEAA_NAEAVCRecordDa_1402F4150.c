/*
 * Function: ?__CheckGoods@CashItemRemoteStore@@AEAA_NAEAVCRecordData@@@Z
 * Address: 0x1402F4150
 */

char __fastcall CashItemRemoteStore::__CheckGoods(CashItemRemoteStore *this, CRecordData *krecPrice)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  int v5; // eax@10
  CNationSettingManager *v6; // rax@33
  __int64 v7; // [sp+0h] [bp-C8h]@1
  int v8; // [sp+20h] [bp-A8h]@35
  int v9; // [sp+28h] [bp-A0h]@35
  int v10; // [sp+30h] [bp-98h]@35
  unsigned int v11; // [sp+38h] [bp-90h]@35
  int v12; // [sp+40h] [bp-88h]@35
  int v13; // [sp+48h] [bp-80h]@35
  _CashShop_str_fld *pFld; // [sp+50h] [bp-78h]@4
  __int64 v15; // [sp+58h] [bp-70h]@4
  int n; // [sp+60h] [bp-68h]@9
  _base_fld *v17; // [sp+68h] [bp-60h]@11
  char v18; // [sp+70h] [bp-58h]@16
  _base_fld *v19; // [sp+78h] [bp-50h]@21
  _TimeItem_fld *v20; // [sp+80h] [bp-48h]@26
  int v21; // [sp+88h] [bp-40h]@33
  int v22; // [sp+8Ch] [bp-3Ch]@33
  char *v23; // [sp+90h] [bp-38h]@35
  int __n[2]; // [sp+98h] [bp-30h]@4
  void *v25; // [sp+A0h] [bp-28h]@7
  void *__t; // [sp+A8h] [bp-20h]@4
  __int64 v27; // [sp+B0h] [bp-18h]@4
  void *v28; // [sp+B8h] [bp-10h]@5
  CashItemRemoteStore *v29; // [sp+D0h] [bp+8h]@1
  CRecordData *v30; // [sp+D8h] [bp+10h]@1

  v30 = krecPrice;
  v29 = this;
  v2 = &v7;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v27 = -2i64;
  pFld = 0i64;
  v15 = 0i64;
  *(_QWORD *)__n = CRecordData::GetRecordNum(&v29->_kRecGoods);
  __t = operator new[](saturated_mul(0x10ui64, *(unsigned __int64 *)__n));
  if ( __t )
  {
    `vector constructor iterator'(
      __t,
      0x10ui64,
      __n[0],
      (void *(__cdecl *)(void *))CashItemRemoteStore::_remain_num_of_good::_remain_num_of_good);
    v28 = __t;
  }
  else
  {
    v28 = 0i64;
  }
  v25 = v28;
  v29->_pkRemainInfo = (CashItemRemoteStore::_remain_num_of_good *)v28;
  if ( v29->_pkRemainInfo )
  {
    for ( n = 0; ; ++n )
    {
      v5 = CRecordData::GetRecordNum(&v29->_kRecGoods);
      if ( n >= v5 )
        return 1;
      v17 = CRecordData::GetRecord(&v29->_kRecGoods, n);
      if ( !v17 )
      {
        if ( CMainThread::IsExcuteService(&g_Main) )
          CLogFile::Write(
            v29->_kLoggers,
            "CashShop Data Load Error : Null Pointer Exception (pCsData), CashShop Index(%d)",
            (unsigned int)n);
        else
          MyMessageBox(
            "CashITemRemoteStore",
            "CashShop Data Load Error : Null Pointer Exception (pCsData), CashShop Index(%d)",
            (unsigned int)n);
        return 0;
      }
      v18 = GetItemTableCode((char *)&v17[1]);
      if ( (unsigned __int8)v18 == -1 )
      {
        if ( CMainThread::IsExcuteService(&g_Main) )
          CLogFile::Write(
            v29->_kLoggers,
            "CashShop Data Load Error : Invalid Table Code, CashShop Item Code(%s)",
            &v17[1]);
        else
          MyMessageBox(
            "CashITemRemoteStore",
            "CashShop Data Load Error : Invalid Table Code, CashShop Item Code(%s)",
            &v17[1]);
        return 0;
      }
      v19 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)v18, (const char *)&v17[1], 7);
      if ( !v19 )
      {
        if ( CMainThread::IsExcuteService(&g_Main) )
          CLogFile::Write(
            v29->_kLoggers,
            "CashShop Data Load Error : Null Pointer Exception (pRec), Not Exist Item! Cash Shop ItemCode(%s)",
            &v17[1]);
        else
          MyMessageBox(
            "CashITemRemoteStore",
            "CashShop Data Load Error : Null Pointer Exception (pRec), Not Exist Item! Cash Shop ItemCode(%s)",
            &v17[1]);
        return 0;
      }
      v20 = TimeItem::FindTimeRec((unsigned __int8)v18, v19->m_dwIndex);
      if ( !IsCashItem(v18, v19->m_dwIndex) )
        break;
      pFld = (_CashShop_str_fld *)CRecordData::GetRecord(v30, v17->m_strCode);
      if ( !pFld )
      {
        MyMessageBox(
          "CashItemRemoteStore::__CheckGoods",
          "CashShop_str.dat : Code(%s) Item(%s) Price Not Exist!",
          v17->m_strCode,
          &v17[1]);
        return 0;
      }
      v6 = CTSingleton<CNationSettingManager>::Instance();
      *(_DWORD *)&v17[1].m_strCode[60] = CNationSettingManager::GetCashItemPrice(v6, pFld);
      v21 = 0;
      v22 = 0;
      if ( v20 )
      {
        v21 = v20->m_nCheckType;
        v22 = v20->m_nUseTime;
      }
      v23 = GetItemKorName((unsigned __int8)v18, v19->m_dwIndex);
      v13 = *(_DWORD *)&v17[3].m_strCode[4];
      v12 = *(_DWORD *)&v17[3].m_strCode[0];
      v11 = v17[3].m_dwIndex;
      v10 = v22;
      v9 = v21;
      v8 = *(_DWORD *)&v17[1].m_strCode[60];
      CLogFile::Write(
        &v29->_kLoggers[1],
        "[%s(%s)], price:%d, method:%d, lentime:%d, discount:%d, event1: %d, event2: %d",
        v23,
        v19->m_strCode);
    }
    if ( CMainThread::IsExcuteService(&g_Main) )
      CLogFile::Write(v29->_kLoggers, "Is not cash item : %s", &v17[1]);
    else
      MyMessageBox("CashITemRemoteStore", "Is not cash item : %s", &v17[1]);
    result = 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
