/*
 * Function: ?Check<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@CMonster@@QEAA_NXZ
 * Address: 0x1401432F0
 */

char __fastcall CMonster::CheckD<PERSON>yDestroy(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMonster *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5->m_dwDestroyNextTime == -1 || GetLoopTime() <= v5->m_dwDestroyNextTime )
  {
    result = 0;
  }
  else
  {
    CMonster::Destroy(v5, 1, 0i64);
    v5->m_dwDestroyNextTime = -1;
    result = 1;
  }
  return result;
}
