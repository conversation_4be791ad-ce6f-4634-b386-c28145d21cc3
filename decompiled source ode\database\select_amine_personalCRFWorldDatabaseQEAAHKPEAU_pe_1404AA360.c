/*
 * Function: ?select_amine_personal@CRFWorldDatabase@@QEAAHKPEAU_personal_amine_inven@@@Z
 * Address: 0x1404AA360
 */

signed __int64 __fastcall CRFWorldDatabase::select_amine_personal(CRFWorldDatabase *this, unsigned int dwSerial, _personal_amine_inven *pInven)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v6; // [sp+0h] [bp-1A8h]@1
  void *SQLStmt; // [sp+20h] [bp-188h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-180h]@24
  __int16 v9; // [sp+30h] [bp-178h]@9
  SQLLEN v10; // [sp+48h] [bp-160h]@24
  char Dest; // [sp+70h] [bp-138h]@4
  char v12; // [sp+71h] [bp-137h]@4
  unsigned __int8 v13; // [sp+174h] [bp-34h]@16
  int v14; // [sp+178h] [bp-30h]@22
  int j; // [sp+17Ch] [bp-2Ch]@22
  unsigned __int8 v16; // [sp+180h] [bp-28h]@26
  unsigned __int64 v17; // [sp+190h] [bp-18h]@4
  CRFWorldDatabase *v18; // [sp+1B0h] [bp+8h]@1
  _personal_amine_inven *v19; // [sp+1C0h] [bp+18h]@1

  v19 = pInven;
  v18 = this;
  v3 = &v6;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v17 = (unsigned __int64)&v6 ^ _security_cookie;
  Dest = 0;
  memset(&v12, 0, 0xFFui64);
  sprintf(&Dest, "{ call pselect_aminepersonal_inven(%d) }", dwSerial);
  if ( v18->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v18->vfptr, &Dest);
  if ( v18->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v18->vfptr) )
  {
    v9 = SQLExecDirect_0(v18->m_hStmtSelect, &Dest, -3);
    if ( v9 && v9 != 1 )
    {
      if ( v9 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v18->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v9, &Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v9, v18->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v9 = SQLFetch_0(v18->m_hStmtSelect);
      if ( v9 && v9 != 1 )
      {
        v13 = 0;
        if ( v9 == 100 )
        {
          v13 = 2;
        }
        else
        {
          SQLStmt = v18->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v9, &Dest, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v9, v18->m_hStmtSelect);
          v13 = 1;
        }
        if ( v18->m_hStmtSelect )
          SQLCloseCursor_0(v18->m_hStmtSelect);
        result = v13;
      }
      else
      {
        v14 = 0;
        for ( j = 0; j < 40; ++j )
        {
          ++v14;
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v9 = SQLGetData_0(v18->m_hStmtSelect, v14++, 4, (char *)v19 + 8 * j, 0i64, &v10);
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v9 = SQLGetData_0(v18->m_hStmtSelect, v14, -6, &v19->list[j].byNum, 0i64, &v10);
          if ( v9 && v9 != 1 )
          {
            v16 = 0;
            if ( v9 == 100 )
            {
              v16 = 2;
            }
            else
            {
              SQLStmt = v18->m_hStmtSelect;
              CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v9, &Dest, "SQLExecDirect", SQLStmt);
              CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v9, v18->m_hStmtSelect);
              v16 = 1;
            }
            if ( v18->m_hStmtSelect )
              SQLCloseCursor_0(v18->m_hStmtSelect);
            return v16;
          }
        }
        if ( v18->m_hStmtSelect )
          SQLCloseCursor_0(v18->m_hStmtSelect);
        if ( v18->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v18->vfptr, "%s Success", &Dest);
        result = 0i64;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v18->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1i64;
  }
  return result;
}
