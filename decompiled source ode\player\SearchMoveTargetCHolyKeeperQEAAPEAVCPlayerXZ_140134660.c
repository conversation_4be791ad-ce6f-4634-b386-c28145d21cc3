/*
 * Function: ?SearchMoveTarget@CHoly<PERSON>eeper@@QEAAPEAVCPlayer@@XZ
 * Address: 0x140134660
 */

CPlayer *__usercall CHolyKeeper::SearchMoveTarget@<rax>(<PERSON><PERSON><PERSON><PERSON><PERSON> *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  float v5; // xmm0_4@15
  unsigned __int32 v6; // ecx@18
  CPlayer *result; // rax@20
  __int64 v8; // [sp-20h] [bp-4F88h]@1
  int v9; // [sp+0h] [bp-4F68h]@4
  __int64 v10[2532]; // [sp+20h] [bp-4F48h]@21
  int j; // [sp+4F44h] [bp-24h]@4
  CCharacter *v12; // [sp+4F48h] [bp-20h]@8
  CExtDummy *v13; // [sp+4F50h] [bp-18h]@18
  CHolyKeeper *v14; // [sp+4F70h] [bp+8h]@1

  v14 = this;
  v2 = alloca(a2);
  v3 = &v8;
  for ( i = 5088i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = 0;
  for ( j = 0; j < 2532; ++j )
  {
    if ( v14->m_bPlayerCircleList[j] )
    {
      v12 = (CCharacter *)(&g_Player.vfptr + 6357 * j);
      if ( v12->m_bLive )
      {
        if ( !v12->m_bCorpse
          && CPlayerDB::GetRaceCode((CPlayerDB *)&v12[1].m_fOldPos[2]) != v14->m_nMasterRace
          && (!CMainThread::IsReleaseServiceMode(&g_Main)
           || BYTE4(v12[1].vfptr) != 2 && !BYTE2(v12[25].m_SFCont[0][5].m_dwPlayerSerial))
          && !CCharacter::GetStealth(v12, 1) )
        {
          v5 = v12->m_fCurPos[1] - v14->m_fCurPos[1];
          abs(v5);
          if ( v5 <= 200.0
            && v12->m_pCurMap == v14->m_pCurMap
            && CGameObject::GetCurSecNum((CGameObject *)&v12->vfptr) != -1 )
          {
            v6 = v14->m_pPosActive->m_wLineIndex;
            v13 = &v14->m_pCurMap->m_Dummy;
            if ( CExtDummy::IsInBBox(v13, v6, v12->m_fCurPos) )
            {
              if ( (CCharacter *)v14->m_pLastMoveTarget == v12 )
                return (CPlayer *)v12;
              v10[v9++] = (__int64)v12;
              if ( v9 >= 2532 )
                break;
            }
          }
        }
      }
    }
  }
  v14->m_pLastMoveTarget = 0i64;
  if ( v9 )
    result = (CPlayer *)v10[rand() % v9];
  else
    result = 0i64;
  return result;
}
