/*
 * Function: j_??0?$_Ranit@PEAVCMoveMapLimitRight@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@AEBU01@@Z
 * Address: 0x1400130ED
 */

void __fastcall std::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &>::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &>(std::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &> *this, std::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &> *__that)
{
  std::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &>::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &>(
    this,
    __that);
}
