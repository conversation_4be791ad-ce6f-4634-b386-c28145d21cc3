/*
 * Function: ?LoopSubProcSendInform@COreAmountMgr@@QEAAXXZ
 * Address: 0x1403F9A90
 */

void __fastcall COreAmountMgr::LoopSubProcSendInform(COreAmountMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int v4; // [sp+20h] [bp-18h]@4
  unsigned int j; // [sp+24h] [bp-14h]@4
  CPlayer *v6; // [sp+28h] [bp-10h]@8
  COreAmountMgr *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = v7->m_uiProccessIndex + 50;
  for ( j = v7->m_uiProccessIndex; j < v4 && j < 0x9E4; ++j )
  {
    v6 = &g_Player + j;
    if ( v6->m_bLive )
    {
      if ( v6->m_bOper )
      {
        CPlayer::SendMsg_RemainOreRate(v6);
        CPlayer::SendMsg_OreTransferCount(v6);
      }
    }
  }
  v7->m_uiProccessIndex = j;
  if ( v7->m_uiProccessIndex >= 0x9E4 )
  {
    v7->m_uiProccessIndex = 0;
    v7->m_bChangeRemainRate = 0;
  }
}
