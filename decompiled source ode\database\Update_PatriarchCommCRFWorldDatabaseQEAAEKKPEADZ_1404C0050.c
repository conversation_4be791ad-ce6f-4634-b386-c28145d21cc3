/*
 * Function: ?Update_PatriarchComm@CRFWorldDatabase@@QEAAEKKPEAD@Z
 * Address: 0x1404C0050
 */

bool __fastcall CRFWorldDatabase::Update_PatriarchComm(CRFWorldDatabase *this, unsigned int dwSerial, unsigned int dwDalant, char *pszDepDate)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-E8h]@1
  char *v8; // [sp+20h] [bp-C8h]@4
  char Dest; // [sp+40h] [bp-A8h]@4
  unsigned __int64 v10; // [sp+D0h] [bp-18h]@4
  CRFWorldDatabase *v11; // [sp+F0h] [bp+8h]@1

  v11 = this;
  v4 = &v7;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v10 = (unsigned __int64)&v7 ^ _security_cookie;
  v8 = pszDepDate;
  sprintf(&Dest, "{ CALL pUpdate_PatriarchComm( %d, %d, '%s') }", dwSerial, dwDalant);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 1);
}
