/*
 * Function: ?InitLogger@CPvpUserAndGuildRankingSystem@@AEAA_NXZ
 * Address: 0x14032B290
 */

char __fastcall CPvpUserAndGuildRankingSystem::InitLogger(CPvpUserAndGuildRankingSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@5
  char result; // al@8
  unsigned int v5; // eax@9
  __int64 v6; // [sp+0h] [bp-488h]@1
  bool bDate; // [sp+20h] [bp-468h]@9
  bool bAddCount; // [sp+28h] [bp-460h]@9
  char _Dest[1024]; // [sp+40h] [bp-448h]@9
  CLogFile *v10; // [sp+450h] [bp-38h]@7
  CLogFile *v11; // [sp+458h] [bp-30h]@4
  __int64 v12; // [sp+460h] [bp-28h]@4
  CLogFile *v13; // [sp+468h] [bp-20h]@5
  unsigned __int64 v14; // [sp+470h] [bp-18h]@4
  CPvpUserAndGuildRankingSystem *v15; // [sp+490h] [bp+8h]@1

  v15 = this;
  v1 = &v6;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = -2i64;
  v14 = (unsigned __int64)&v6 ^ _security_cookie;
  v11 = (CLogFile *)operator new(0xB8ui64);
  if ( v11 )
  {
    CLogFile::CLogFile(v11);
    v13 = (CLogFile *)v3;
  }
  else
  {
    v13 = 0i64;
  }
  v10 = v13;
  v15->m_pkLogger = v13;
  if ( v15->m_pkLogger )
  {
    _Dest[0] = 0;
    memset(&_Dest[1], 0, 0x3FFui64);
    v5 = GetKorLocalTime();
    sprintf_s<1024>((char (*)[1024])_Dest, "..\\ZoneServerLog\\Systemlog\\RankJob%d.log", v5);
    bAddCount = 1;
    bDate = 1;
    CLogFile::SetWriteLogFile(v15->m_pkLogger, _Dest, 1, 0, 1, 1);
    CUserRankingProcess::SetLogger(&v15->m_kUserRankingProcess, v15->m_pkLogger);
    result = 1;
  }
  else
  {
    CLogFile::Write(&stru_1799C8F30, "CPvpUserAndGuildRankingSystem::InitLogger() NULL == new CLogFile!\r\n");
    result = 0;
  }
  return result;
}
