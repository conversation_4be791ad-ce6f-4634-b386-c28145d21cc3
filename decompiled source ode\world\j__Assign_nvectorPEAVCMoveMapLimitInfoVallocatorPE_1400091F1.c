/*
 * Function: j_?_Assign_n@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@IEAAX_KAEBQEAVCMoveMapLimitInfo@@@Z
 * Address: 0x1400091F1
 */

void __fastcall std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Assign_n(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, unsigned __int64 _Count, CMoveMapLimitInfo *const *_Val)
{
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Assign_n(this, _Count, _Val);
}
