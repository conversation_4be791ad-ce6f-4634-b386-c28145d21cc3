/*
 * Function: ?AppointSerialStorageItem@CPlayerDB@@QEAAXXZ
 * Address: 0x14010B950
 */

void __fastcall CPlayerDB::AppointSerialStorageItem(CPlayerDB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CPlayerDB *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 8; ++j )
  {
    if ( v5->m_dbEquip.m_pStorageList[j].m_bLoad )
      v5->m_dbEquip.m_pStorageList[j].m_wSerial = CPlayerDB::GetNewItemSerial(v5);
  }
  for ( j = 0; j < 7; ++j )
  {
    if ( v5->m_dbEmbellish.m_pStorageList[j].m_bLoad )
      v5->m_dbEmbellish.m_pStorageList[j].m_wSerial = CPlayerDB::GetNewItemSerial(v5);
  }
  for ( j = 0; j < 100; ++j )
  {
    if ( v5->m_dbInven.m_pStorageList[j].m_bLoad )
      v5->m_dbInven.m_pStorageList[j].m_wSerial = CPlayerDB::GetNewItemSerial(v5);
  }
  for ( j = 0; j < 88; ++j )
  {
    if ( v5->m_dbForce.m_pStorageList[j].m_bLoad )
      v5->m_dbForce.m_pStorageList[j].m_wSerial = CPlayerDB::GetNewItemSerial(v5);
  }
  for ( j = 0; j < 4; ++j )
  {
    if ( v5->m_dbAnimus.m_pStorageList[j].m_bLoad )
      v5->m_dbAnimus.m_pStorageList[j].m_wSerial = CPlayerDB::GetNewItemSerial(v5);
  }
  for ( j = 0; j < 40; ++j )
  {
    if ( v5->m_dbPersonalAmineInven.m_pStorageList[j].m_bLoad )
      v5->m_dbPersonalAmineInven.m_pStorageList[j].m_wSerial = CPlayerDB::GetNewItemSerial(v5);
  }
  for ( j = 0; j < 100; ++j )
  {
    if ( v5->m_dbTrunk.m_pStorageList[j].m_bLoad )
      v5->m_dbTrunk.m_pStorageList[j].m_wSerial = CPlayerDB::GetNewItemSerial(v5);
  }
  for ( j = 0; j < 40; ++j )
  {
    if ( v5->m_dbExtTrunk.m_pStorageList[j].m_bLoad )
      v5->m_dbExtTrunk.m_pStorageList[j].m_wSerial = CPlayerDB::GetNewItemSerial(v5);
  }
}
