/*
 * Function: ?GetHaveUnitNum@CPlayerDB@@QEAAHXZ
 * Address: 0x140108E60
 */

__int64 __fastcall CPlayerDB::GetHaveUnitNum(CPlayerDB *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // [sp+0h] [bp-18h]@1
  int j; // [sp+4h] [bp-14h]@4
  CPlayerDB *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v1 = (int *)&v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  v4 = 0;
  for ( j = 0; j < 4; ++j )
  {
    if ( v6->m_UnitDB.m_List[j].byFrame != 255 )
      ++v4;
  }
  return v4;
}
