/*
 * Function: ?DE_Potion_RemoveAfterEffect@@YA_NPEAVCCharacter@@0MAEAE@Z
 * Address: 0x14017EA70
 */

char __fastcall DE_Potion_RemoveAfterEffect(CCharacter *pActChar, CCharacter *pTargetChar, float fEffectValue, char *byRet)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-58h]@1
  CCharacter *v8; // [sp+30h] [bp-28h]@9
  int j; // [sp+38h] [bp-20h]@10
  _sf_continous (*v10)[8]; // [sp+40h] [bp-18h]@12
  _base_fld *v11; // [sp+48h] [bp-10h]@12
  CCharacter *v12; // [sp+60h] [bp+8h]@1

  v12 = pActChar;
  v4 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pTargetChar )
  {
    if ( v12->m_ObjID.m_byID || pTargetChar->m_ObjID.m_byID )
    {
      result = 0;
    }
    else
    {
      v8 = pTargetChar;
      if ( HIBYTE(pTargetChar[25].m_SFContAura[0][5].m_wDurSec) )
      {
        for ( j = 0; j < 8; ++j )
        {
          v10 = (_sf_continous (*)[8])((char *)v8->m_SFCont + 48 * j);
          v11 = CRecordData::GetRecord(&stru_1799C8410 + 3, "17");
          if ( v11 && (*v10)[0].m_byEffectCode == 3 && (*v10)[0].m_wEffectIndex == v11->m_dwIndex )
          {
            CCharacter::RemoveSFContEffect(v8, 0, j, 0, 0);
            HIBYTE(v8[25].m_SFContAura[0][5].m_wDurSec) = 0;
            return 1;
          }
        }
      }
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
