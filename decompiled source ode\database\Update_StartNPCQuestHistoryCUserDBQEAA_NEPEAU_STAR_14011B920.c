/*
 * Function: ?Update_StartNPCQuestHistory@CUserDB@@QEAA_NEPEAU_START_NPC_QUEST_HISTORY@_QUEST_DB_BASE@@@Z
 * Address: 0x14011B920
 */

char __fastcall CUserDB::Update_StartNPCQuestHistory(CUserDB *this, char byIndex, _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY *pHisData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@4
  CUserDB *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1
  _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY *Source; // [sp+50h] [bp+18h]@1

  Source = pHisData;
  v9 = byIndex;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = (unsigned __int8)byIndex;
  if ( (unsigned __int8)byIndex < *(&pdwCnt + (v8->m_AvatorData.dbAvator.m_byRaceSexCode >> 1)) )
  {
    strcpy_0(v8->m_AvatorData.dbQuest.m_StartHistory[(unsigned __int8)byIndex].szQuestCode, pHisData->szQuestCode);
    v8->m_AvatorData.dbQuest.m_StartHistory[(unsigned __int8)v9].byLevel = Source->byLevel;
    GetLocalTime(&v8->m_AvatorData.dbQuest.m_StartHistory[(unsigned __int8)v9].tmStartTime);
    v8->m_AvatorData.dbQuest.m_StartHistory[(unsigned __int8)v9].nEndTime = Source->nEndTime;
    ++v8->m_AvatorData.dbQuest.dwListCnt;
    result = 1;
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_NPCQuestHistory(Index OVER) : %d",
      v8->m_aszAvatorName,
      (unsigned __int8)byIndex);
    result = 0;
  }
  return result;
}
