/*
 * Function: ??0CMonsterEventSet@@QEAA@XZ
 * Address: 0x1402A7920
 */

void __fastcall CMonsterEventSet::CMonsterEventSet(CMonsterEventSet *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CMonsterEventSet *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->vfptr = (CMonsterEventSetVtbl *)&CMonsterEventSet::`vftable';
  `vector constructor iterator'(v4->m_EventSet, 0x60D8ui64, 10, (void *(__cdecl *)(void *))_event_set::_event_set);
  `vector constructor iterator'(
    v4->m_EventSetLootingList,
    0xDFCui64,
    100,
    (void *(__cdecl *)(void *))_event_set_looting::_event_set_looting);
  v4->m_bLoadEventLooting = 0;
}
