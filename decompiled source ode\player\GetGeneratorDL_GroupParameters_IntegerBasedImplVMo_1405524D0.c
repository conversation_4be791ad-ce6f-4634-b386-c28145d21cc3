/*
 * Function: ?GetGenerator@?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@QEBAAEBVInteger@2@XZ
 * Address: 0x1405524D0
 */

int __fastcall CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::GetGenerator(__int64 a1)
{
  __int64 v1; // rax@1
  __int64 v3; // [sp+40h] [bp+8h]@1

  v3 = a1;
  LODWORD(v1) = (*(int (__fastcall **)(signed __int64))(*(_QWORD *)(a1 + 8) + 40i64))(a1 + 8);
  return (*(int (__fastcall **)(signed __int64, __int64))(*(_QWORD *)(v3 + 88) + 16i64))(v3 + 88, v1);
}
