/*
 * Function: j_?MakeSchedule@CBossMonsterScheduleSystem@@IEAAPEAUBossSchedule@@PEAUBossSchedule_Map@@PEAU_mon_active@@PEAU_mon_block@@HH@Z
 * Address: 0x1400095F7
 */

BossSchedule *__fastcall CBossMonsterScheduleSystem::MakeSchedule(CBossMonsterScheduleSystem *this, BossSchedule_Map *pMapSchedule, _mon_active *pMonAct, _mon_block *pBlock, int nActIndex, int nBlockIndex)
{
  return CBossMonsterScheduleSystem::MakeSchedule(this, pMapSchedule, pMonAct, pBlock, nActIndex, nBlockIndex);
}
