/*
 * Function: ?SetNextDiscountEventTime@CashItemRemoteStore@@QEAA_NXZ
 * Address: 0x1402F7900
 */

char __fastcall CashItemRemoteStore::SetNextDiscountEventTime(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-68h]@1
  int Dst; // [sp+28h] [bp-40h]@6
  int v6; // [sp+2Ch] [bp-3Ch]@6
  int v7; // [sp+30h] [bp-38h]@6
  int v8; // [sp+34h] [bp-34h]@6
  int v9; // [sp+38h] [bp-30h]@6
  int v10; // [sp+3Ch] [bp-2Ch]@6
  int v11; // [sp+48h] [bp-20h]@6
  CashItemRemoteStore *v12; // [sp+70h] [bp+8h]@1

  v12 = this;
  v1 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v12->m_cde.m_ini.m_bRepeat )
  {
    memset_0(&Dst, 0, 0x24ui64);
    v10 = v12->m_cde.m_ini.m_wYear[0] - 1900;
    v9 = v12->m_cde.m_ini.m_byMonth[0] - 1;
    v8 = v12->m_cde.m_ini.m_byDay[0] + v12->m_cde.m_ini.m_byRepeatDay;
    v7 = v12->m_cde.m_ini.m_byHour[0];
    v6 = v12->m_cde.m_ini.m_byMinute[0];
    Dst = 0;
    v11 = -1;
    v12->m_cde.m_ini.m_NextEventTime[0] = _mktime32((struct tm *)&Dst);
    if ( v12->m_cde.m_ini.m_NextEventTime[0] == -1 )
    {
      v12->m_cde.m_ini.m_bRepeat = 0;
      CLogFile::Write(
        &v12->m_cde.m_cde_log,
        "SetNextDiscountEventTime() : Fail When Calculate Next Discount Event Begin Time");
      result = 0;
    }
    else
    {
      memset_0(&Dst, 0, 0x24ui64);
      v10 = v12->m_cde.m_ini.m_wYear[1] - 1900;
      v9 = v12->m_cde.m_ini.m_byMonth[1] - 1;
      v8 = v12->m_cde.m_ini.m_byDay[1] + v12->m_cde.m_ini.m_byRepeatDay;
      v7 = v12->m_cde.m_ini.m_byHour[1];
      v6 = v12->m_cde.m_ini.m_byMinute[1];
      Dst = 0;
      v11 = -1;
      v12->m_cde.m_ini.m_NextEventTime[1] = _mktime32((struct tm *)&Dst);
      if ( v12->m_cde.m_ini.m_NextEventTime[1] == -1 )
      {
        v12->m_cde.m_ini.m_bRepeat = 0;
        CLogFile::Write(
          &v12->m_cde.m_cde_log,
          "SetNextDiscountEventTime() : Fail When Calculate Next Discount Event End Time");
        result = 0;
      }
      else
      {
        result = 1;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
