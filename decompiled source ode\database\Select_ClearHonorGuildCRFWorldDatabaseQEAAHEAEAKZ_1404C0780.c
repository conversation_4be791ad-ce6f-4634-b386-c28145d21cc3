/*
 * Function: ?Select_ClearHonorGuild@CRFWorldDatabase@@QEAAHEAEAK@Z
 * Address: 0x1404C0780
 */

signed __int64 __fastcall CRFWorldDatabase::Select_ClearHonorGuild(CRFWorldDatabase *this, char byR<PERSON>, unsigned int *dwSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v6; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@22
  SQLLEN v9; // [sp+38h] [bp-150h]@22
  __int16 v10; // [sp+44h] [bp-144h]@9
  char _Dest[256]; // [sp+60h] [bp-128h]@4
  unsigned __int8 v12; // [sp+164h] [bp-24h]@16
  unsigned __int8 v13; // [sp+165h] [bp-23h]@24
  unsigned __int64 v14; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v15; // [sp+190h] [bp+8h]@1
  unsigned int *TargetValue; // [sp+1A0h] [bp+18h]@1

  TargetValue = dwSerial;
  v15 = this;
  v3 = &v6;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = (unsigned __int64)&v6 ^ _security_cookie;
  sprintf_s<256>(
    (char (*)[256])_Dest,
    "Select GuildSerial From tbl_honor_guild Where Race=%d AND GuildSerial=0xFFFFFFFF And DCK=0 AND IsNext=1",
    (unsigned __int8)byRace);
  if ( v15->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v15->vfptr, _Dest);
  if ( v15->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v15->vfptr) )
  {
    v10 = SQLExecDirectA_0(v15->m_hStmtSelect, _Dest, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v15->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v10, _Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v10, v15->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v10 = SQLFetch_0(v15->m_hStmtSelect);
      if ( v10 && v10 != 1 )
      {
        v12 = 0;
        if ( v10 == 100 )
        {
          v12 = 2;
        }
        else
        {
          SQLStmt = v15->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v10, _Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v10, v15->m_hStmtSelect);
          v12 = 1;
        }
        if ( v15->m_hStmtSelect )
          SQLCloseCursor_0(v15->m_hStmtSelect);
        result = v12;
      }
      else
      {
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 1u, 4, TargetValue, 0i64, &v9);
        if ( v10 && v10 != 1 )
        {
          v13 = 0;
          if ( v10 == 100 )
          {
            v13 = 2;
          }
          else
          {
            SQLStmt = v15->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v10, _Dest, "SQLGetData", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v10, v15->m_hStmtSelect);
            v13 = 1;
          }
          if ( v15->m_hStmtSelect )
            SQLCloseCursor_0(v15->m_hStmtSelect);
          result = v13;
        }
        else
        {
          if ( v15->m_hStmtSelect )
            SQLCloseCursor_0(v15->m_hStmtSelect);
          if ( v15->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v15->vfptr, "%s Success", _Dest);
          result = 0i64;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v15->vfptr, "ReConnectDataBase Fail. Query : %s", _Dest);
    result = 1i64;
  }
  return result;
}
