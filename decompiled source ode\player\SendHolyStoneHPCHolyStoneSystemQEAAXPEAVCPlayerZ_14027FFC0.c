/*
 * Function: ?SendHolyS<PERSON>HP@CHolyStoneSystem@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x14027FFC0
 */

void __fastcall CHolyStoneSystem::SendHolyStoneHP(CHolyStoneSystem *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v4; // rax@6
  __int64 v5; // [sp+0h] [bp-88h]@1
  __int16 Dst[8]; // [sp+34h] [bp-54h]@8
  int j; // [sp+44h] [bp-44h]@8
  char pbyType; // [sp+54h] [bp-34h]@11
  char v9; // [sp+55h] [bp-33h]@11
  unsigned int dwSerial; // [sp+70h] [bp-18h]@6
  int v11; // [sp+74h] [bp-14h]@6
  unsigned __int64 v12; // [sp+78h] [bp-10h]@4
  CHolyStoneSystem *v13; // [sp+90h] [bp+8h]@1
  CPlayer *v14; // [sp+98h] [bp+10h]@1

  v14 = pkPlayer;
  v13 = this;
  v2 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( v13->m_SaveData.m_nSceneCode == 1 && v13->m_SaveData.m_nHolyMasterRace == -1 )
  {
    dwSerial = CPlayerDB::GetCharSerial(&pkPlayer->m_Param);
    v11 = CPlayerDB::GetRaceCode(&v14->m_Param);
    v4 = CPvpUserAndGuildRankingSystem::Instance();
    if ( CPvpUserAndGuildRankingSystem::IsCurrentRaceBossGroup(v4, v11, dwSerial) )
    {
      memset_0(Dst, 0, 6ui64);
      for ( j = 0; j < 3; ++j )
        Dst[j] = (*(int (__fastcall **)(struct CHolyStone *))&g_Stone[j].vfptr->gap8[8])(&g_Stone[j]);
      pbyType = 13;
      v9 = 33;
      CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_ObjID.m_wIndex, &pbyType, (char *)Dst, 6u);
    }
  }
}
