/*
 * Function: ?GetAttackPart@CMonster@@QEAAHXZ
 * Address: 0x14014DDC0
 */

signed __int64 __fastcall CMonster::GetAttackPart(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  int v5; // [sp+20h] [bp-28h]@4
  int v6; // [sp+24h] [bp-24h]@4
  int v7; // [sp+28h] [bp-20h]@4
  int v8; // [sp+2Ch] [bp-1Ch]@4
  int v9; // [sp+30h] [bp-18h]@4
  int v10; // [sp+34h] [bp-14h]@4

  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = 20;
  v6 = 43;
  v7 = 65;
  v8 = 83;
  v9 = 100;
  v10 = rand() % 100;
  if ( v10 > 20 )
  {
    if ( v10 > 43 )
    {
      if ( v10 > 65 )
      {
        if ( v10 > 83 )
        {
          if ( v10 > 100 )
            result = 0xFFFFFFFFi64;
          else
            result = 3i64;
        }
        else
        {
          result = 2i64;
        }
      }
      else
      {
        result = 1i64;
      }
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 4i64;
  }
  return result;
}
