/*
 * Function: ??4?$DL_GroupParametersImpl@V?$EcPrecomputation@VECP@CryptoPP@@@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@2@V?$DL_GroupParameters@UECPPoint@CryptoPP@@@2@@CryptoPP@@QEAAAEAV01@AEBV01@@Z
 * Address: 0x14045C350
 */

CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> > *__fastcall CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>>::operator=(CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> > *this, CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> > *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> > *v6; // [sp+30h] [bp+8h]@1
  CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> > *__thata; // [sp+38h] [bp+10h]@1

  __thata = __that;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::operator=(
    (CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *)&v6->vfptr,
    (CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *)&__that->vfptr);
  CryptoPP::EcPrecomputation<CryptoPP::ECP>::operator=(
    (CryptoPP::EcPrecomputation<CryptoPP::ECP> *)v6->gap18,
    (CryptoPP::EcPrecomputation<CryptoPP::ECP> *)__thata->gap18);
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::operator=(&v6->m_gpc, &__thata->m_gpc);
  return v6;
}
