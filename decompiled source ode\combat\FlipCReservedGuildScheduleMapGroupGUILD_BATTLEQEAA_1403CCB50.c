/*
 * Function: ?Flip@CReservedGuildScheduleMapGroup@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403CCB50
 */

void __fastcall GUILD_BATTLE::CReservedGuildScheduleMapGroup::Flip(GUILD_BATTLE::CReservedGuildScheduleMapGroup *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned __int8 j; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CReservedGuildScheduleMapGroup *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < (signed int)v5->m_byMaxPage; ++j )
    GUILD_BATTLE::CReservedGuildSchedulePage::Flip(&v5->m_kList[j]);
}
