/*
 * Function: ?SendMsg_AlterTol@CPlayer@@QEAAXXZ
 * Address: 0x1400DF130
 */

void __fastcall CPlayer::SendMsg_AlterTol(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-88h]@1
  char Dst; // [sp+38h] [bp-50h]@4
  char pbyType; // [sp+54h] [bp-34h]@4
  char v6; // [sp+55h] [bp-33h]@4
  unsigned __int64 v7; // [sp+70h] [bp-18h]@4
  CPlayer *v8; // [sp+90h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = (unsigned __int64)&v3 ^ _security_cookie;
  memcpy_0(&Dst, v8->m_zLastTol, 8ui64);
  pbyType = 11;
  v6 = 20;
  CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, &Dst, 8u);
}
