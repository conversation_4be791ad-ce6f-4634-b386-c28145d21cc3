/*
 * Function: ?SendMsg_NPCLinkItemCheckResult@CPlayer@@QEAAXEPEAU_STORAGE_POS_INDIV@@@Z
 * Address: 0x1400E7B40
 */

void __fastcall CPlayer::SendMsg_NPCLinkItemCheckResult(CPlayer *this, char byResCode, _STORAGE_POS_INDIV *pStorage)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@4
  __int64 v6; // [sp+0h] [bp-78h]@1
  char Dst; // [sp+34h] [bp-44h]@4
  char v8; // [sp+35h] [bp-43h]@4
  unsigned __int16 v9; // [sp+36h] [bp-42h]@4
  char v10; // [sp+38h] [bp-40h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v12; // [sp+55h] [bp-23h]@4
  CPlayer *v13; // [sp+80h] [bp+8h]@1
  char v14; // [sp+88h] [bp+10h]@1
  _STORAGE_POS_INDIV *v15; // [sp+90h] [bp+18h]@1

  v15 = pStorage;
  v14 = byResCode;
  v13 = this;
  v3 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  memset_0(&Dst, 0, 5ui64);
  Dst = v14;
  v8 = v15->byStorageCode;
  v9 = v15->wItemSerial;
  v10 = v15->byNum;
  pbyType = 7;
  v12 = 60;
  v5 = _npclink_check_item_result_zocl::size((_npclink_check_item_result_zocl *)&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, &Dst, v5);
}
