/*
 * Function: ??$_Construct@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@U12@@std@@YAXPEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@AEBU12@@Z
 * Address: 0x1405A8250
 */

CryptoPP::EC2NPoint *__fastcall std::_Construct<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>(void *a1, const struct CryptoPP::EC2NPoint *a2)
{
  CryptoPP::EC2NPoint *v3; // [sp+30h] [bp-28h]@1
  CryptoPP::EC2NPoint *v4; // [sp+40h] [bp-18h]@2
  const struct CryptoPP::EC2NPoint *v5; // [sp+68h] [bp+10h]@1

  v5 = a2;
  v3 = (CryptoPP::EC2NPoint *)operator new(0x60ui64, a1);
  if ( v3 )
    v4 = CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>(
           v3,
           v5);
  else
    v4 = 0i64;
  return v4;
}
