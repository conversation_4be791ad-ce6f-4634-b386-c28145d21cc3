/*
 * Function: ?SetGuildBattleMatter@CGuild@@QEAAXKKKK@Z
 * Address: 0x140259E40
 */

void __fastcall CGuild::SetGuildBattleMatter(CGuild *this, unsigned int dwSrcGuildSerial, unsigned int dwStartTime, unsigned int dwNumber, unsigned int dwMapIdx)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  CGuild *v8; // [sp+30h] [bp+8h]@1
  unsigned int v9; // [sp+40h] [bp+18h]@1
  unsigned int v10; // [sp+48h] [bp+20h]@1

  v10 = dwNumber;
  v9 = dwStartTime;
  v8 = this;
  v5 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v8->m_GuildBattleSugestMatter.pkSrc = GetGuildDataFromSerial(g_Guild, 500, dwSrcGuildSerial);
  v8->m_GuildBattleSugestMatter.pkDest = v8;
  v8->m_GuildBattleSugestMatter.eState = 3;
  v8->m_GuildBattleSugestMatter.dwStartTime = v9;
  v8->m_GuildBattleSugestMatter.dwNumber = v10;
  v8->m_GuildBattleSugestMatter.dwMapIdx = dwMapIdx;
}
