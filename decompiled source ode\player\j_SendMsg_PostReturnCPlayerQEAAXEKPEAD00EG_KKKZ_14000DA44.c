/*
 * Function: j_?SendMsg_PostReturn@CPlayer@@QEAAXEKPEAD00EG_KKK@Z
 * Address: 0x14000DA44
 */

void __fastcall CPlayer::SendMsg_PostReturn(CPlayer *this, char byErrCode, unsigned int dwPostSerial, char *wszRecvName, char *wszTitle, char *wszContent, char byTableCode, unsigned __int16 wItemIndex, unsigned __int64 dwDur, unsigned int dwLv, unsigned int dwGold)
{
  CPlayer::SendMsg_PostReturn(
    this,
    byErrCode,
    dwPostSerial,
    wszRecvName,
    wszTitle,
    wszContent,
    byTableCode,
    wItemIndex,
    dwDur,
    dwLv,
    dwGold);
}
