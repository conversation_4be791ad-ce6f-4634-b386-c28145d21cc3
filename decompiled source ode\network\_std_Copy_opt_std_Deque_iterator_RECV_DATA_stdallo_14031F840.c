/*
 * Function: _std::_Copy_opt_std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::random_access_iterator_tag__::_1_::dtor$3
 * Address: 0x14031F840
 */

void __fastcall std::_Copy_opt_std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::random_access_iterator_tag__::_1_::dtor_3(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 32) & 1 )
  {
    *(_DWORD *)(a2 + 32) &= 0xFFFFFFFE;
    std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(*(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> **)(a2 + 112));
  }
}
