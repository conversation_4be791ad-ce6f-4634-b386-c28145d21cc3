/*
 * Function: ?_SendVoteScore@Voter@@AEAAXPEAVCPlayer@@@Z
 * Address: 0x1402BEDE0
 */

void __fastcall Voter::_SendVoteScore(Voter *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@5
  __int64 v5; // [sp+0h] [bp-68h]@1
  _pt_notify_vote_score_zocl *v6; // [sp+30h] [bp-38h]@5
  char pbyType; // [sp+44h] [bp-24h]@5
  char v8; // [sp+45h] [bp-23h]@5
  Voter *v9; // [sp+70h] [bp+8h]@1
  CPlayer *v10; // [sp+78h] [bp+10h]@1

  v10 = pOne;
  v9 = this;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v9->_kCandidateInfo[CPlayerDB::GetRaceCode(&pOne->m_Param)].byCnt >= 2 )
  {
    v6 = &v9->_kVoteScoreInfo[CPlayerDB::GetRaceCode(&v10->m_Param)];
    pbyType = 56;
    v8 = 6;
    v4 = _pt_notify_vote_score_zocl::size(v6);
    CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, &v6->byRace, v4);
  }
}
