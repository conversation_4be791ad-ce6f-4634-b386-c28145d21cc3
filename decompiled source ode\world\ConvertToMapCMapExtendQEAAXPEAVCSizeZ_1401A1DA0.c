/*
 * Function: ?ConvertToMap@CMapExtend@@QEAAXPEAVCSize@@@Z
 * Address: 0x1401A1DA0
 */

void __fastcall CMapExtend::ConvertToMap(CMapExtend *this, CSize *szMap)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int v5; // [sp+20h] [bp-18h]@4
  unsigned int v6; // [sp+24h] [bp-14h]@4
  CMapExtend *v7; // [sp+40h] [bp+8h]@1
  CSize *v8; // [sp+48h] [bp+10h]@1

  v8 = szMap;
  v7 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = CSurface::GetDDSurfaceDesc(*v7->m_pSF)->dwWidth;
  v6 = CSurface::GetDDSurfaceDesc(*v7->m_pSF)->dwHeight;
  v7->m_ptStartMap.x = v7->m_ptStartScreen.x * v8->cx / (signed int)v5;
  v7->m_ptStartMap.y = v7->m_ptStartScreen.y * v8->cy / (signed int)v6;
  v7->m_ptEndMap.x = v7->m_ptEndScreen.x * v8->cx / (signed int)v5;
  v7->m_ptEndMap.y = v7->m_ptEndScreen.y * v8->cy / (signed int)v6;
  v7->m_sizeExtend.cx = v7->m_ptEndMap.x - v7->m_ptStartMap.x;
  v7->m_sizeExtend.cy = v7->m_ptEndMap.y - v7->m_ptStartMap.y;
  v7->m_ptCenter.x = v7->m_ptStartMap.x + v7->m_sizeExtend.cx / 2;
  v7->m_ptCenter.y = v7->m_ptStartMap.y + v7->m_sizeExtend.cy / 2;
}
