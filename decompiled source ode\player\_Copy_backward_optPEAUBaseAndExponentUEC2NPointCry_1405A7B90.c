/*
 * Function: ??$_Copy_backward_opt@PEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@PEAU12@Urandom_access_iterator_tag@std@@@std@@YAPEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@PEAU12@00Urandom_access_iterator_tag@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405A7B90
 */

_BYTE *__fastcall std::_Copy_backward_opt<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> *,std::random_access_iterator_tag>(_BYTE *a1, _BYTE *a2, _BYTE *a3)
{
  _BYTE *v4; // [sp+30h] [bp+8h]@1
  _BYTE *v5; // [sp+38h] [bp+10h]@1
  _BYTE *v6; // [sp+40h] [bp+18h]@1

  v6 = a3;
  v5 = a2;
  v4 = a1;
  while ( v4 != v5 )
  {
    v5 -= 96;
    v6 -= 96;
    CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::operator=(v6, v5);
  }
  return v6;
}
