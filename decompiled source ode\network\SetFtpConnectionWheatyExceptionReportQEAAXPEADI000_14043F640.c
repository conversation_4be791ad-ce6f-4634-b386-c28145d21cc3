/*
 * Function: ?SetFtpConnection@WheatyExceptionReport@@QEAAXPEADI000@Z
 * Address: 0x14043F640
 */

void __fastcall WheatyExceptionReport::SetFtpConnection(WheatyExceptionReport *this, char *pszFtpIp, unsigned int nFtpPort, char *pszFtpId, char *pszFtpPwd, char *pszFtpDirectory)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-28h]@1
  unsigned int v9; // [sp+40h] [bp+18h]@1
  const char *v10; // [sp+48h] [bp+20h]@1

  v10 = pszFtpId;
  v9 = nFtpPort;
  v6 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  strcpy_0(&WheatyExceptionReport::m_szFtpIp, pszFtpIp);
  WheatyExceptionReport::m_nFtpPort = v9;
  strcpy_0(&WheatyExceptionReport::m_szFtpId, v10);
  strcpy_0(&WheatyExceptionReport::m_szFtpPwd, pszFtpPwd);
  if ( pszFtpDirectory )
    strcpy_0(&WheatyExceptionReport::m_szFtpDirectory, pszFtpDirectory);
}
