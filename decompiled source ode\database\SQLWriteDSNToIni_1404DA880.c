/*
 * Function: SQLWriteDSNToIni
 * Address: 0x1404DA880
 */

int __fastcall SQLWriteDSNToIni(const char *lpszDSN, const char *lpszDriver)
{
  const char *v2; // rdi@1
  const char *v3; // rbx@1
  __int64 (__cdecl *v4)(); // rax@1
  int result; // eax@2

  v2 = lpszDSN;
  v3 = lpszDriver;
  v4 = ODBC___GetSetupProc("SQLWriteDSNToIni");
  if ( v4 )
    result = ((int (__fastcall *)(const char *, const char *))v4)(v2, v3);
  else
    result = 0;
  return result;
}
