/*
 * Function: j_?_Ufill@?$vector@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@IEAAPEAPEAVCUnmannedTraderClassInfo@@PEAPEAV3@_KAEBQEAV3@@Z
 * Address: 0x14000C2D4
 */

CUnmannedTraderClassInfo **__fastcall std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Ufill(std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this, CUnmannedTraderClassInfo **_Ptr, unsigned __int64 _Count, CUnmannedTraderClassInfo *const *_Val)
{
  return std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_U<PERSON>(
           this,
           _Ptr,
           _Count,
           _Val);
}
