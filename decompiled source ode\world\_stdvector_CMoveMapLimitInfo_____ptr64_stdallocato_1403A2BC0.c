/*
 * Function: _std::vector_CMoveMapLimitInfo_____ptr64_std::allocator_CMoveMapLimitInfo_____ptr64___::_Xlen_::_1_::dtor$0
 * Address: 0x1403A2BC0
 */

int __fastcall std::vector_CMoveMapLimitInfo_____ptr64_std::allocator_CMoveMapLimitInfo_____ptr64___::_Xlen_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  return std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(a2 + 104);
}
