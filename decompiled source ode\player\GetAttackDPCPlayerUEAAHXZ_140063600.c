/*
 * Function: ?GetAttackDP@CPlayer@@UEAAHXZ
 * Address: 0x140063600
 */

__int64 __fastcall CPlayer::GetAttackDP(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  unsigned int v5; // [sp+20h] [bp-28h]@4
  char *v6; // [sp+28h] [bp-20h]@5
  _base_fld *v7; // [sp+30h] [bp-18h]@6
  _base_fld *v8; // [sp+38h] [bp-10h]@9
  CPlayer *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = 0;
  if ( CPlayer::IsRidingUnit(v9) )
  {
    v8 = CRecordData::GetRecord(&stru_1799C86D0 + 3, v9->m_pUsingUnit->byPart[3]);
    if ( v8 )
      v5 = *(_DWORD *)&v8[4].m_strCode[40];
  }
  else
  {
    v6 = &v9->m_Param.m_dbEquip.m_pStorageList[6].m_bLoad;
    if ( *v6 )
    {
      v7 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 6, *(_WORD *)(v6 + 3));
      if ( v7 )
        v5 = *(_DWORD *)&v7[9].m_strCode[44];
    }
  }
  return v5;
}
