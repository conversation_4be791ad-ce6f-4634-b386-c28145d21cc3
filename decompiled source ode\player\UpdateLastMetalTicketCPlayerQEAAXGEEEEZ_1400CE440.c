/*
 * Function: ?UpdateLastMetalTicket@CPlayer@@QEAAXGEEEE@Z
 * Address: 0x1400CE440
 */

void __fastcall CPlayer::UpdateLastMetalTicket(CPlayer *this, unsigned __int16 byCurrentYear, char byCurrent<PERSON>onth, char byCurrentDay, char byCurrentHour, char byNumOfTime)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v8; // eax@6
  __int64 v9; // [sp+0h] [bp-38h]@1
  CPlayer *v10; // [sp+40h] [bp+8h]@1
  unsigned __int16 v11; // [sp+48h] [bp+10h]@1
  char v12; // [sp+50h] [bp+18h]@1
  char v13; // [sp+58h] [bp+20h]@1

  v13 = byCurrentDay;
  v12 = byCurrentMonth;
  v11 = byCurrentYear;
  v10 = this;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( !MiningTicket::AuthLastMentalTicket(
          &v10->m_MinigTicket,
          byCurrentYear,
          byCurrentMonth,
          byCurrentDay,
          byCurrentHour,
          byNumOfTime) )
  {
    MiningTicket::SetLastMentalTicket(&v10->m_MinigTicket, v11, v12, v13, byCurrentHour, byNumOfTime);
    if ( v10->m_pUserDB )
    {
      v8 = MiningTicket::GetLastMentalTicket(&v10->m_MinigTicket);
      CUserDB::Update_TakeLastMentalTicket(v10->m_pUserDB, v8);
    }
    CPlayer::SendMsg_HSKQuestActCum(v10);
  }
}
