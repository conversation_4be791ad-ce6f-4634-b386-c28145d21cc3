/*
 * Function: j_??$_Iter_random@PEAVCUnmannedTraderSchedule@@PEAV1@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAVCUnmannedTraderSchedule@@0@Z
 * Address: 0x140002716
 */

std::random_access_iterator_tag __fastcall std::_Iter_random<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *>(CUnmannedTraderSchedule *const *__formal, CUnmannedTraderSchedule *const *a2)
{
  return std::_Iter_random<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *>(__formal, a2);
}
