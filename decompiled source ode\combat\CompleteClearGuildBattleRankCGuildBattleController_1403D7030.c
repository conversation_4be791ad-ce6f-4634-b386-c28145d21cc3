/*
 * Function: ?CompleteClearGuildBattleRank@CGuildBattleController@@QEAAXE@Z
 * Address: 0x1403D7030
 */

void __fastcall CGuildBattleController::CompleteClearGuildBattleRank(CGuildBattleController *this, char byResult)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleRankManager *v4; // rax@4
  CPvpUserAndGuildRankingSystem *v5; // rax@7
  __int64 v6; // [sp+0h] [bp-38h]@1
  const char *v7; // [sp+20h] [bp-18h]@5
  unsigned int v8; // [sp+28h] [bp-10h]@7
  char v9; // [sp+48h] [bp+10h]@1

  v9 = byResult;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = GUILD_BATTLE::CGuildBattleRankManager::Instance();
  GUILD_BATTLE::CGuildBattleRankManager::Clear(v4);
  if ( v9 )
    v7 = "Fail";
  else
    v7 = "Success";
  v8 = (unsigned __int8)v9;
  v5 = CPvpUserAndGuildRankingSystem::Instance();
  CPvpUserAndGuildRankingSystem::Log(
    v5,
    "CGuildBattleController::ClearGuildBattleRankComplete( BYTE byResult(%u) ) : Clear %s!",
    v8,
    v7);
}
