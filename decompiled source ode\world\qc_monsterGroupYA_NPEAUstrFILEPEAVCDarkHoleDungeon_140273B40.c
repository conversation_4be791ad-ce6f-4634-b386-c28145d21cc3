/*
 * Function: ?qc_monsterGroup@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z
 * Address: 0x140273B40
 */

bool __fastcall qc_monsterGroup(strFILE *fstr, CDarkHoleDungeonQuestSetup *pSetup, char *pszoutErrMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // rax@15
  size_t v7; // rax@17
  __int64 v8; // [sp+0h] [bp-1B8h]@1
  char poutszWord; // [sp+30h] [bp-188h]@4
  char szRecordCode; // [sp+D0h] [bp-E8h]@6
  __monster_group *v11; // [sp+158h] [bp-60h]@8
  int j; // [sp+160h] [bp-58h]@8
  const char **v13; // [sp+168h] [bp-50h]@10
  _base_fld *v14; // [sp+170h] [bp-48h]@18
  __monster_group *v15; // [sp+180h] [bp-38h]@17
  __monster_group *v16; // [sp+188h] [bp-30h]@14
  char *v17; // [sp+190h] [bp-28h]@17
  __int64 v18; // [sp+198h] [bp-20h]@4
  __monster_group *v19; // [sp+1A0h] [bp-18h]@15
  unsigned __int64 v20; // [sp+1A8h] [bp-10h]@4
  strFILE *fstra; // [sp+1C0h] [bp+8h]@1
  CDarkHoleDungeonQuestSetup *pSetupa; // [sp+1C8h] [bp+10h]@1

  pSetupa = pSetup;
  fstra = fstr;
  v3 = &v8;
  for ( i = 108i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v18 = -2i64;
  v20 = (unsigned __int64)&v8 ^ _security_cookie;
  if ( strFILE::word(fstra, &poutszWord) )
  {
    if ( strFILE::word(fstra, &szRecordCode) )
    {
      v11 = 0i64;
      for ( j = 0; j < pSetupa->m_pCurLoadQuest->nMonsterGroupNum; ++j )
      {
        v13 = (const char **)&pSetupa->m_pCurLoadQuest->pMonsterGroup[j]->pszGroupName;
        if ( !strcmp_0(*v13, &poutszWord) )
        {
          v11 = (__monster_group *)v13;
          break;
        }
      }
      if ( !v11 )
      {
        v16 = (__monster_group *)operator new(0x110ui64);
        if ( v16 )
        {
          __monster_group::__monster_group(v16);
          v19 = (__monster_group *)v6;
        }
        else
        {
          v19 = 0i64;
        }
        v15 = v19;
        pSetupa->m_pCurLoadQuest->pMonsterGroup[pSetupa->m_pCurLoadQuest->nMonsterGroupNum] = v19;
        v11 = pSetupa->m_pCurLoadQuest->pMonsterGroup[pSetupa->m_pCurLoadQuest->nMonsterGroupNum];
        v7 = strlen_0(&poutszWord);
        v17 = (char *)operator new[](v7 + 1);
        v11->pszGroupName = v17;
        strcpy_0(v11->pszGroupName, &poutszWord);
        ++pSetupa->m_pCurLoadQuest->nMonsterGroupNum;
      }
      v14 = CRecordData::GetRecord(&stru_1799C6210, &szRecordCode);
      if ( v14 )
      {
        v11->pSubMonster[v11->nSubMonsterNum++] = (_monster_fld *)v14;
        result = 1;
      }
      else
      {
        result = _false(fstra, pSetupa);
      }
    }
    else
    {
      result = _false(fstra, pSetupa);
    }
  }
  else
  {
    result = _false(fstra, pSetupa);
  }
  return result;
}
