/*
 * Function: ?result_query@AutominePersonalMgr@@QEAAXEPEAD@Z
 * Address: 0x1402E09A0
 */

void __fastcall AutominePersonalMgr::result_query(AutominePersonalMgr *this, char byRet, char *pdata)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  AutominePersonalMgr *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = *pdata;
  if ( !v6 )
    AutominePersonalMgr::pop_dqs_makestorage(v7, byRet, pdata);
}
