#pragma once

#include <cstdint>
#include <algorithm>

/**
 * @file NetworkMessage.h
 * @brief Network message structures and size calculation utilities
 * 
 * This module contains network message structures and their size calculation
 * methods, refactored from decompiled C source files to modern C++.
 * 
 * @note Refactored from multiple decompiled network message size functions
 */

/**
 * @namespace NetworkMessage
 * @brief Network message utilities and structures
 * 
 * This namespace contains network message structures and utility functions
 * for calculating message sizes, validating data, and managing network
 * communication protocols.
 */
namespace NetworkMessage {

    // Constants for network message limits
    namespace Constants {
        constexpr uint8_t MAX_CHAR_COUNT = 3;
        constexpr uint8_t MAX_CONT_COUNT = 8;
        constexpr int64_t BASE_ADD_CHAR_SIZE = 2;
        constexpr int64_t BASE_ENTER_WORLD_SIZE = 3;
        constexpr int64_t BASE_REGED_CHAR_SIZE = 221;
        constexpr int64_t CHAR_ENTRY_SIZE = 69;
        constexpr int64_t BASE_TARGET_MONSTER_SIZE = 21;
        constexpr int64_t CONT_ENTRY_SIZE = 2;
    }

    /**
     * @brief Add character result message structure
     * 
     * This structure represents the result of adding a character to the game.
     * 
     * @note Original function: _add_char_result_zone::size (Address: 0x14011F870)
     */
    struct AddCharResultZone {
        // Message data would be defined here based on actual protocol
        
        /**
         * @brief Calculates the size of the add character result message
         * @return int64_t The size of the message in bytes
         */
        int64_t size() const;
        
        /**
         * @brief Validates the message data
         * @return bool true if the message data is valid
         */
        bool validate() const;
    };

    /**
     * @brief Enter world result message structure
     * 
     * This structure represents the result of entering the game world.
     * 
     * @note Original function: _enter_world_result_zone::size (Address: 0x14011F250)
     */
    struct EnterWorldResultZone {
        // Message data would be defined here based on actual protocol
        
        /**
         * @brief Calculates the size of the enter world result message
         * @return int64_t The size of the message in bytes
         */
        int64_t size() const;
        
        /**
         * @brief Validates the message data
         * @return bool true if the message data is valid
         */
        bool validate() const;
    };

    /**
     * @brief Registered character result message structure
     * 
     * This structure represents the result of character registration with
     * variable-length character data.
     * 
     * @note Original function: _reged_char_result_zone::size (Address: 0x14011F6F0)
     */
    struct RegedCharResultZone {
        uint8_t byCharNum;  ///< Number of characters (0-3)
        // Additional character data would be defined here
        
        /**
         * @brief Default constructor
         */
        RegedCharResultZone() : byCharNum(0) {}
        
        /**
         * @brief Constructor with character count
         * @param charCount Number of characters
         */
        explicit RegedCharResultZone(uint8_t charCount) : byCharNum(charCount) {
            clampCharNum();
        }
        
        /**
         * @brief Calculates the size of the registered character result message
         * @return int64_t The size of the message in bytes
         */
        int64_t size();
        
        /**
         * @brief Validates the message data
         * @return bool true if the message data is valid
         */
        bool validate() const;
        
        /**
         * @brief Gets the number of characters
         * @return uint8_t The number of characters
         */
        uint8_t getCharNum() const { return byCharNum; }
        
        /**
         * @brief Sets the number of characters with validation
         * @param charCount The number of characters to set
         */
        void setCharNum(uint8_t charCount);
        
    private:
        /**
         * @brief Clamps character number to valid range
         */
        void clampCharNum();
    };

    /**
     * @brief Target monster continuous effect all-inform message structure
     * 
     * This structure represents information about continuous effects on target monsters.
     * 
     * @note Original function: _target_monster_contsf_allinform_zocl::size (Address: 0x1400F0140)
     */
    struct TargetMonsterContsfAllinformZocl {
        uint8_t byContCount;  ///< Number of continuous effects (0-8)
        // Additional effect data would be defined here
        
        /**
         * @brief Default constructor
         */
        TargetMonsterContsfAllinformZocl() : byContCount(0) {}
        
        /**
         * @brief Constructor with effect count
         * @param contCount Number of continuous effects
         */
        explicit TargetMonsterContsfAllinformZocl(uint8_t contCount) : byContCount(contCount) {
            clampContCount();
        }
        
        /**
         * @brief Calculates the size of the target monster continuous effect message
         * @return int64_t The size of the message in bytes
         */
        int64_t size();
        
        /**
         * @brief Validates the message data
         * @return bool true if the message data is valid
         */
        bool validate() const;
        
        /**
         * @brief Gets the number of continuous effects
         * @return uint8_t The number of continuous effects
         */
        uint8_t getContCount() const { return byContCount; }
        
        /**
         * @brief Sets the number of continuous effects with validation
         * @param contCount The number of continuous effects to set
         */
        void setContCount(uint8_t contCount);
        
    private:
        /**
         * @brief Clamps continuous effect count to valid range
         */
        void clampContCount();
    };

    // Utility functions

    /**
     * @brief Validates a character count value
     * @param charCount The character count to validate
     * @return bool true if the character count is valid
     */
    bool IsValidCharCount(uint8_t charCount);

    /**
     * @brief Validates a continuous effect count value
     * @param contCount The continuous effect count to validate
     * @return bool true if the continuous effect count is valid
     */
    bool IsValidContCount(uint8_t contCount);

    /**
     * @brief Calculates the size for a variable-length character message
     * @param charCount Number of characters
     * @param baseSize Base message size
     * @param entrySize Size per character entry
     * @param maxCount Maximum allowed character count
     * @return int64_t The calculated message size
     */
    int64_t CalculateVariableCharSize(uint8_t charCount, 
                                      int64_t baseSize, 
                                      int64_t entrySize, 
                                      uint8_t maxCount);

    /**
     * @brief Calculates the size for a variable-length effect message
     * @param effectCount Number of effects
     * @param baseSize Base message size
     * @param entrySize Size per effect entry
     * @param maxCount Maximum allowed effect count
     * @return int64_t The calculated message size
     */
    int64_t CalculateVariableEffectSize(uint8_t effectCount, 
                                        int64_t baseSize, 
                                        int64_t entrySize, 
                                        uint8_t maxCount);

    /**
     * @brief Clamps a value to a specified range
     * @param value The value to clamp
     * @param maxValue The maximum allowed value
     * @return uint8_t The clamped value
     */
    uint8_t ClampToRange(uint8_t value, uint8_t maxValue);

    /**
     * @brief Gets the name of a message type for debugging
     * @param messageType The message type identifier
     * @return const char* The name of the message type
     */
    const char* GetMessageTypeName(int messageType);

} // namespace NetworkMessage

// Legacy C-style interface for compatibility
extern "C" {
    /**
     * @brief Legacy C interface for AddCharResultZone::size
     */
    int64_t _add_char_result_zone_size(void* this_ptr);
    
    /**
     * @brief Legacy C interface for EnterWorldResultZone::size
     */
    int64_t _enter_world_result_zone_size(void* this_ptr);
    
    /**
     * @brief Legacy C interface for RegedCharResultZone::size
     */
    int64_t _reged_char_result_zone_size(void* this_ptr);
    
    /**
     * @brief Legacy C interface for TargetMonsterContsfAllinformZocl::size
     */
    int64_t _target_monster_contsf_allinform_zocl_size(void* this_ptr);
}
