/*
 * Function: ?end@?$deque@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@QEAA?AV?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@2@XZ
 * Address: 0x14031D670
 */

std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::deque<RECV_DATA,std::allocator<RECV_DATA>>::end(std::deque<RECV_DATA,std::allocator<RECV_DATA> > *this, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *result)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  std::deque<RECV_DATA,std::allocator<RECV_DATA> > *_Pdeque; // [sp+40h] [bp+8h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v8; // [sp+48h] [bp+10h]@1

  v8 = result;
  _Pdeque = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
    result,
    _Pdeque->_Mysize + _Pdeque->_Myoff,
    (std::_Container_base *)&_Pdeque->_Myfirstiter);
  return v8;
}
