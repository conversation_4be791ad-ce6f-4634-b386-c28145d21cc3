/*
 * Function: ?dev_up_forceitem@CPlayer@@QEAA_NH@Z
 * Address: 0x1400BB9A0
 */

char __fastcall CPlayer::dev_up_forceitem(CPlayer *this, int nCum)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-48h]@1
  bool bUpdate; // [sp+20h] [bp-28h]@15
  int n; // [sp+30h] [bp-18h]@10
  char *v8; // [sp+38h] [bp-10h]@13
  CPlayer *v9; // [sp+50h] [bp+8h]@1
  signed int nUpdate; // [sp+58h] [bp+10h]@1

  nUpdate = nCum;
  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( nCum >= 0 )
  {
    if ( v9->m_pUserDB )
    {
      if ( nCum > 0xFFFFFF )
        nUpdate = 0xFFFFFF;
      for ( n = 0; n < 88; ++n )
      {
        v8 = &v9->m_Param.m_dbForce.m_pStorageList[n].m_bLoad;
        if ( *v8 )
        {
          if ( !v8[19] )
          {
            bUpdate = 0;
            CUserDB::Update_ItemDur(v9->m_pUserDB, 3, n, nUpdate, 0);
            _STORAGE_LIST::UpdateCurDur(v9->m_Param.m_pStoragePtr[3], n, nUpdate);
            CPlayer::SendMsg_FcitemInform(v9, *(_WORD *)(v8 + 17), nUpdate);
          }
        }
      }
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
