/*
 * Function: ??0DL_SignatureMessageEncodingMethod_DSA@CryptoPP@@QEAA@XZ
 * Address: 0x14058F7E0
 */

CryptoPP::DL_SignatureMessageEncodingMethod_DSA *__fastcall CryptoPP::DL_SignatureMessageEncodingMethod_DSA::DL_SignatureMessageEncodingMethod_DSA(CryptoPP::DL_SignatureMessageEncodingMethod_DSA *this)
{
  CryptoPP::DL_SignatureMessageEncodingMethod_DSA *v2; // [sp+30h] [bp+8h]@1

  v2 = this;
  CryptoPP::PK_DeterministicSignatureMessageEncodingMethod::PK_DeterministicSignatureMessageEncodingMethod((CryptoPP::PK_DeterministicSignatureMessageEncodingMethod *)&this->vfptr);
  v2->vfptr = (CryptoPP::PK_SignatureMessageEncodingMethodVtbl *)&CryptoPP::DL_SignatureMessageEncodingMethod_DSA::`vftable';
  return v2;
}
