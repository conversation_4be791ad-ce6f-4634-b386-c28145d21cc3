/*
 * Function: ??0table@lua_tinker@@QEAA@PEAUlua_State@@H@Z
 * Address: 0x140446630
 */

void __fastcall lua_tinker::table::table(lua_tinker::table *this, struct lua_State *L, int index)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // rax@7
  __int64 v6; // [sp+0h] [bp-48h]@1
  lua_tinker::table_obj *v7; // [sp+20h] [bp-28h]@9
  lua_tinker::table_obj *v8; // [sp+28h] [bp-20h]@6
  __int64 v9; // [sp+30h] [bp-18h]@4
  lua_tinker::table_obj *v10; // [sp+38h] [bp-10h]@7
  lua_tinker::table *v11; // [sp+50h] [bp+8h]@1
  struct lua_State *La; // [sp+58h] [bp+10h]@1
  int indexa; // [sp+60h] [bp+18h]@1

  indexa = index;
  La = L;
  v11 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = -2i64;
  if ( index < 0 )
    indexa = lua_gettop(L) + index + 1;
  v8 = (lua_tinker::table_obj *)operator new(0x20ui64);
  if ( v8 )
  {
    lua_tinker::table_obj::table_obj(v8, La, indexa);
    v10 = (lua_tinker::table_obj *)v5;
  }
  else
  {
    v10 = 0i64;
  }
  v7 = v10;
  v11->m_obj = v10;
  lua_tinker::table_obj::inc_ref(v11->m_obj);
}
