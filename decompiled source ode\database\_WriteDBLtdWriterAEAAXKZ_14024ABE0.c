/*
 * Function: ?_WriteDB@LtdWriter@@AEAAXK@Z
 * Address: 0x14024ABE0
 */

void __fastcall LtdWriter::_WriteDB(LtdWriter *this, unsigned int dwIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  char j; // [sp+20h] [bp-18h]@5
  LtdWriter *v6; // [sp+40h] [bp+8h]@1
  unsigned int v7; // [sp+48h] [bp+10h]@1

  v7 = dwIndex;
  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CRFDBItemLog::insert_ltd(v6->m_pLtdDB, &v6->m_bufLog[dwIndex]);
  if ( v6->m_bufLog[v7].m_ItemInfo.m_bExist )
  {
    for ( j = 0; (unsigned __int8)j < (signed int)v6->m_bufLog[v7].m_ItemInfo.m_byCnt; ++j )
      CRFDBItemLog::insert_iteminfo(v6->m_pLtdDB, &v6->m_bufLog[v7].m_ItemInfo, j);
  }
  if ( v6->m_bufLog[v7].m_Expend.m_bExist )
    CRFDBItemLog::insert_expend(v6->m_pLtdDB, &v6->m_bufLog[v7].m_Expend);
}
