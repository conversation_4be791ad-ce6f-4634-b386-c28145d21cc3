/*
 * Function: ?pc_UseRecoverLossExpItem@CPlayer@@QEAADG@Z
 * Address: 0x14004E910
 */

char __usercall CPlayer::pc_UseRecoverLossExpItem@<al>(CPlayer *this@<rcx>, unsigned __int16 wItemSerial@<dx>, double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v6; // eax@29
  int v7; // eax@32
  int v8; // eax@42
  __int64 v9; // [sp+0h] [bp-B8h]@1
  bool bUseExpAdditionItem[8]; // [sp+20h] [bp-98h]@42
  unsigned __int16 bSend[4]; // [sp+28h] [bp-90h]@42
  long double v12; // [sp+30h] [bp-88h]@42
  int nProbPro; // [sp+38h] [bp-80h]@42
  char *pCause; // [sp+40h] [bp-78h]@42
  char *pszFileName; // [sp+48h] [bp-70h]@42
  _STORAGE_LIST::_db_con *pItem; // [sp+50h] [bp-68h]@21
  _base_fld *v17; // [sp+58h] [bp-60h]@25
  int v18; // [sp+60h] [bp-58h]@34
  int v19; // [sp+64h] [bp-54h]@37
  double v20; // [sp+68h] [bp-50h]@37
  long double v21; // [sp+70h] [bp-48h]@42
  unsigned int v22; // [sp+78h] [bp-40h]@42
  long double v23; // [sp+80h] [bp-38h]@42
  int v24; // [sp+88h] [bp-30h]@35
  double v25; // [sp+90h] [bp-28h]@38
  char *v26; // [sp+98h] [bp-20h]@42
  char *v27; // [sp+A0h] [bp-18h]@42
  CPlayer *v28; // [sp+C0h] [bp+8h]@1
  unsigned __int16 v29; // [sp+C8h] [bp+10h]@1

  v29 = wItemSerial;
  v28 = this;
  v3 = &v9;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CPlayerDB::GetLossExp(&v28->m_Param);
  if ( a3 == 0.0 )
  {
    result = -1;
  }
  else if ( CGameObject::GetCurSecNum((CGameObject *)&v28->vfptr) == -1 || v28->m_bMapLoading )
  {
    result = -2;
  }
  else if ( v28->m_bCorpse )
  {
    result = -3;
  }
  else if ( v28->m_byStandType == 1 )
  {
    result = -4;
  }
  else if ( CPlayer::IsSiegeMode(v28) )
  {
    result = -5;
  }
  else if ( CPlayer::IsRidingUnit(v28) )
  {
    result = -6;
  }
  else if ( _effect_parameter::GetEff_State(&v28->m_EP, 20) )
  {
    result = -7;
  }
  else if ( _effect_parameter::GetEff_State(&v28->m_EP, 28) )
  {
    result = -7;
  }
  else
  {
    pItem = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v28->m_Param.m_dbInven.m_nListNum, v29);
    if ( pItem )
    {
      if ( pItem->m_byTableCode == 30 )
      {
        v17 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 30, pItem->m_wItemIndex);
        if ( v17 )
        {
          if ( pItem->m_bLock )
          {
            result = -9;
          }
          else
          {
            v6 = CPlayerDB::GetLevel(&v28->m_Param);
            if ( v6 >= *(_DWORD *)&v17[4].m_strCode[0] )
            {
              if ( *(_DWORD *)&v17[4].m_strCode[4] == -1
                || (v7 = CPlayerDB::GetLevel(&v28->m_Param), v7 <= *(_DWORD *)&v17[4].m_strCode[4]) )
              {
                v18 = *(_DWORD *)&v17[4].m_strCode[40] - *(_DWORD *)&v17[4].m_strCode[36] + 1;
                if ( v18 >= 1 )
                  v24 = v18;
                else
                  v24 = 1;
                v18 = v24;
                v19 = *(_DWORD *)&v17[4].m_strCode[36] + rand() % v18;
                CPlayerDB::GetLossExp(&v28->m_Param);
                v20 = a3 * (double)v19 / 100.0;
                if ( v20 >= 0.0 )
                  v25 = v20;
                else
                  v25 = 0.0;
                v20 = v25;
                if ( v25 == 0.0 )
                {
                  result = -10;
                }
                else
                {
                  CPlayerDB::GetExp(&v28->m_Param);
                  v21 = 0.0;
                  v22 = v28->m_dwExpRate;
                  CPlayerDB::GetLossExp(&v28->m_Param);
                  v23 = 0.0;
                  CPlayer::AlterExp(v28, v20, 1, 1, 0);
                  CPlayerDB::SetLossExp(&v28->m_Param, 0.0);
                  CUserDB::Update_LossExp(v28->m_pUserDB, 0.0);
                  LOBYTE(bSend[0]) = 0;
                  bUseExpAdditionItem[0] = 0;
                  CPlayer::Emb_AlterDurPoint(v28, 0, pItem->m_byStorageIndex, -1, 0, 0);
                  CMgrAvatorItemHistory::consume_del_item(
                    &CPlayer::s_MgrItemHistory,
                    v28->m_ObjID.m_wIndex,
                    pItem,
                    v28->m_szItemHistoryFileName);
                  v26 = v28->m_szLvHistoryFileName;
                  v27 = v17->m_strCode;
                  CPlayerDB::GetExp(&v28->m_Param);
                  v8 = v28->m_ObjID.m_wIndex;
                  pszFileName = v26;
                  pCause = v27;
                  nProbPro = v19;
                  v12 = v23;
                  bSend[0] = v28->m_dwExpRate;
                  CMgrAvatorLvHistory::recovery_exp(
                    &CPlayer::s_MgrLvHistory,
                    v8,
                    v21,
                    v22,
                    0.0,
                    bSend[0],
                    v23,
                    v19,
                    v27,
                    v26);
                  result = v19;
                }
              }
              else
              {
                result = -9;
              }
            }
            else
            {
              result = -9;
            }
          }
        }
        else
        {
          result = -8;
        }
      }
      else
      {
        result = -8;
      }
    }
    else
    {
      CPlayer::SendMsg_AdjustAmountInform(v28, 0, v29, 0);
      result = -8;
    }
  }
  return result;
}
