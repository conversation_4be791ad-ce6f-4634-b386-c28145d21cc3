/*
 * Function: ?ScreenShot@CLevel@@QEAAXXZ
 * Address: 0x1404E0910
 */

void __fastcall CLevel::ScreenShot(CLevel *this)
{
  int v1; // ebx@1
  char *v2; // rdi@1
  char Dest; // [sp+30h] [bp-118h]@2

  v1 = 0;
  v2 = this->mMapName;
  while ( 1 )
  {
    sprintf(&Dest, "%s%s%04d.jpg", byte_184A791F0, v2);
    if ( !(unsigned int)IsExistFile(&Dest) )
      break;
    if ( ++v1 >= 9999 )
      return;
  }
  FramebufferToJPG(&Dest);
}
