/*
 * Function: ?InitTransferOre@COreAmountMgr@@QEAAXKE@Z
 * Address: 0x1403F98B0
 */

void __fastcall COreAmountMgr::InitTransferOre(COreAmountMgr *this, unsigned int dwTransAmount, char byTransCount)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  COreAmountMgr *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( byTransCount )
  {
    v6->m_byOreTransferCount = byTransCount;
    v6->m_dwOreTransferAmount = dwTransAmount;
  }
  else
  {
    v6->m_byOreTransferCount = 0;
    v6->m_dwOreTransferAmount = 0;
  }
  COreAmountMgr::UpdateForce(v6);
}
