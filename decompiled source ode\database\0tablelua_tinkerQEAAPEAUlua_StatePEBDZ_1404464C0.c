/*
 * Function: ??0table@lua_tinker@@QEAA@PEAUlua_State@@PEBD@Z
 * Address: 0x1404464C0
 */

void __fastcall lua_tinker::table::table(lua_tinker::table *this, struct lua_State *L, const char *name)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@7
  __int64 v6; // rax@7
  __int64 v7; // [sp+0h] [bp-58h]@1
  lua_tinker::table_obj *v8; // [sp+28h] [bp-30h]@6
  __int64 v9; // [sp+30h] [bp-28h]@4
  int v10; // [sp+38h] [bp-20h]@4
  lua_tinker::table_obj *v11; // [sp+40h] [bp-18h]@7
  lua_tinker::table *v12; // [sp+60h] [bp+8h]@1
  struct lua_State *La; // [sp+68h] [bp+10h]@1
  const char *v14; // [sp+70h] [bp+18h]@1

  v14 = name;
  La = L;
  v12 = this;
  v3 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = -2i64;
  lua_tinker::enum_stack(L);
  lua_pushstring(La, v14);
  lua_gettable(La, 4294957294i64);
  v10 = lua_type(La, 0xFFFFFFFFi64) == 5;
  if ( !v10 )
  {
    lua_settop(La, 4294967294i64);
    lua_createtable(La, 0i64, 0i64);
    lua_pushstring(La, v14);
    lua_pushvalue(La, 4294967294i64);
    lua_settable(La, 4294957294i64);
  }
  v8 = (lua_tinker::table_obj *)operator new(0x20ui64);
  if ( v8 )
  {
    v5 = lua_gettop(La);
    lua_tinker::table_obj::table_obj(v8, La, v5);
    v11 = (lua_tinker::table_obj *)v6;
  }
  else
  {
    v11 = 0i64;
  }
  v12->m_obj = v11;
}
