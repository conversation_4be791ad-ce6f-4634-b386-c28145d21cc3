/*
 * Function: _CUnmannedTraderClassInfoTableCodeType::GetGroupID_::_1_::dtor$0
 * Address: 0x140377580
 */

void __fastcall CUnmannedTraderClassInfoTableCodeType::GetGroupID_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>((std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)(a2 + 56));
}
