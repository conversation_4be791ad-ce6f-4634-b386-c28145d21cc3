/*
 * Function: ?Init@CLogTypeDBTask@@QEAA_NKI@Z
 * Address: 0x1402C1D80
 */

char __fastcall CLogTypeDBTask::Init(CLogTypeDBTask *this, unsigned int dwInx, unsigned int uiSize)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  char *v7; // [sp+20h] [bp-18h]@7
  CLogTypeDBTask *v8; // [sp+40h] [bp+8h]@1
  unsigned int v9; // [sp+48h] [bp+10h]@1

  v9 = dwInx;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( uiSize && dwInx != -1 )
  {
    v7 = (char *)operator new[](uiSize);
    v8->m_pcData = v7;
    if ( v8->m_pcData )
    {
      v8->m_dwInx = v9;
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
