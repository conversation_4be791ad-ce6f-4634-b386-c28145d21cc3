/*
 * Function: j_??$_Destroy_range@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@YAXPEAPEAVCUnmannedTraderSubClassInfo@@0AEAV?$allocator@PEAVCUnmannedTraderSubClassInfo@@@0@@Z
 * Address: 0x140006BB8
 */

void __fastcall std::_Destroy_range<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(CUnmannedTraderSubClassInfo **_First, CUnmannedTraderSubClassInfo **_Last, std::allocator<CUnmannedTraderSubClassInfo *> *_Al)
{
  std::_Destroy_range<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(_First, _Last, _Al);
}
