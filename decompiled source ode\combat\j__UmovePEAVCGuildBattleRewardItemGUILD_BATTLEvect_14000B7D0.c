/*
 * Function: j_??$_Umove@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@@?$vector@VCGuildBattleRewardItem@GUILD_BATTLE@@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@std@@IEAAPEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV23@00@Z
 * Address: 0x14000B7D0
 */

GUILD_BATTLE::CGuildBattleRewardItem *__fastcall std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Umove<GUILD_BATTLE::CGuildBattleRewardItem *>(std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *this, GUILD_BATTLE::CGuildBattleRewardItem *_First, GUILD_BATTLE::CGuildBattleRewardItem *_Last, GUILD_BATTLE::CGuildBattleRewardItem *_Ptr)
{
  return std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Umove<GUILD_BATTLE::CGuildBattleRewardItem *>(
           this,
           _First,
           _Last,
           _Ptr);
}
