/*
 * Function: ??0CMoveMapLimitInfoPortal@@QEAA@IH@Z
 * Address: 0x1403A3EE0
 */

void __fastcall CMoveMapLimitInfoPortal::CMoveMapLimitInfoPortal(CMoveMapLimitInfoPortal *this, unsigned int uiInx, int iType)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  __int64 v6; // [sp+20h] [bp-18h]@4
  CMoveMapLimitInfoPortal *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = -2i64;
  CMoveMapLimitInfo::CMoveMapLimitInfo((CMoveMapLimitInfo *)&v7->vfptr, uiInx, iType);
  v7->vfptr = (CMoveMapLimitInfoVtbl *)&CMoveMapLimitInfoPortal::`vftable';
  v7->m_pkSrcDummy = 0i64;
  v7->m_pkDestDummy = 0i64;
  v7->m_pkRegenDummy = 0i64;
  std::vector<char *,std::allocator<char *>>::vector<char *,std::allocator<char *>>(&v7->m_vecAllowDummyCode);
  v7->m_eNotifyForceMoveHQState = 0;
  v7->m_pkNotifyForceMoveHQTimer = 0i64;
  v7->m_uiProcNotifyInx = 0;
}
