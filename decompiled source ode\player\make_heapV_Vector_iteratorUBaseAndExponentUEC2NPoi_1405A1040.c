/*
 * Function: ??$make_heap@V?$_Vector_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@@std@@YAXV?$_Vector_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@0@0@Z
 * Address: 0x1405A1040
 */

int std::make_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>>()
{
  signed __int64 v0; // rax@1
  __int64 v1; // rax@2
  __int64 v2; // rax@2
  __int64 v3; // rax@2
  __int64 v4; // rax@2
  char v6; // [sp+20h] [bp-C8h]@2
  char *v7; // [sp+38h] [bp-B0h]@2
  char v8; // [sp+40h] [bp-A8h]@2
  char *v9; // [sp+58h] [bp-90h]@2
  char v10; // [sp+60h] [bp-88h]@2
  char *v11; // [sp+78h] [bp-70h]@2
  char v12; // [sp+80h] [bp-68h]@2
  char *v13; // [sp+98h] [bp-50h]@2
  __int64 v14; // [sp+A0h] [bp-48h]@1
  __int64 v15; // [sp+A8h] [bp-40h]@2
  __int64 v16; // [sp+B0h] [bp-38h]@2
  __int64 v17; // [sp+B8h] [bp-30h]@2
  __int64 v18; // [sp+C0h] [bp-28h]@2
  __int64 v19; // [sp+C8h] [bp-20h]@2
  __int64 v20; // [sp+D0h] [bp-18h]@2
  __int64 v21; // [sp+D8h] [bp-10h]@2

  v14 = -2i64;
  LODWORD(v0) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator-();
  if ( v0 > 1 )
  {
    v7 = &v6;
    v9 = &v8;
    v11 = &v10;
    v13 = &v12;
    v1 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)&v6);
    v15 = v1;
    LODWORD(v2) = std::_Val_type<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>>(v1);
    v16 = v2;
    v3 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)v9);
    v17 = v3;
    LODWORD(v4) = std::_Dist_type<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>>(v3);
    v18 = v4;
    v19 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)v11);
    v20 = v19;
    v21 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)v13);
    std::_Make_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>(
      v21,
      v20,
      v18,
      v16);
  }
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
  return std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
}
