/*
 * Function: ?ReRegistItem@CUnmannedTraderRegistItemInfo@@QEAAXK@Z
 * Address: 0x140360770
 */

void __fastcall CUnmannedTraderRegistItemInfo::ReRegistItem(CUnmannedTraderRegistItemInfo *this, unsigned int dwPrice)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  __int64 _Time; // [sp+28h] [bp-20h]@4
  CUnmannedTraderRegistItemInfo *v6; // [sp+50h] [bp+8h]@1
  unsigned int v7; // [sp+58h] [bp+10h]@1

  v7 = dwPrice;
  v6 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _Time = 0i64;
  time_18(&_Time);
  v6->m_tStartTime = _Time;
  v6->m_dwPrice = v7;
  CUnmannedTraderItemState::Set(&v6->m_kState, 1);
}
