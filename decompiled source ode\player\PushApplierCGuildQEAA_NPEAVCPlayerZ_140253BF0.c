/*
 * Function: ?PushApplier@CGuild@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x140253BF0
 */

char __fastcall CGuild::PushApplier(CGuild *this, CPlayer *pApplier)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@6
  _guild_applier_info *p; // [sp+28h] [bp-10h]@9
  CGuild *v8; // [sp+40h] [bp+8h]@1
  CPlayer *v9; // [sp+48h] [bp+10h]@1

  v9 = pApplier;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v8->m_nApplierNum < 32 )
  {
    for ( j = 0; j < 32; ++j )
    {
      p = &v8->m_ApplierData[j];
      if ( !_guild_applier_info::IsFill(p) )
      {
        p->pPlayer = v9;
        p->dwApplyTime = GetLoopTime();
        ++v8->m_nApplierNum;
        CGuild::SendMsg_AddJoinApplier(v8, p);
        CGuild::MakeDownApplierPacket(v8);
        return 1;
      }
    }
    result = 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
