/*
 * Function: ?MessageRepresentativeBitLength@?$DL_SignatureSchemeBase@VPK_Signer@CryptoPP@@V?$DL_PrivateKey@UEC2NPoint@CryptoPP@@@2@@CryptoPP@@IEBA_KXZ
 * Address: 0x14056C310
 */

__int64 __fastcall CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Signer,CryptoPP::DL_PrivateKey<CryptoPP::EC2NPoint>>::MessageRepresentativeBitLength(__int64 a1)
{
  __int64 v1; // rax@1
  CryptoPP::Integer *v2; // rax@1

  LODWORD(v1) = CryptoPP::DL_Base<CryptoPP::DL_PrivateKey<CryptoPP::EC2NPoint>>::GetAbstractGroupParameters(a1 + 16);
  LODWORD(v2) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v1 + 64i64))(v1);
  return CryptoPP::Integer::BitCount(v2);
}
