/*
 * Function: _CryptoPP::DL_GroupParametersImpl_CryptoPP::EcPrecomputation_CryptoPP::ECP__CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__CryptoPP::DL_GroupParameters_CryptoPP::ECPPoint___::DL_GroupParametersImpl_CryptoPP::EcPrecomputation_CryptoPP::ECP__CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__CryptoPP::DL_GroupParameters_CryptoPP::ECPPoint____::_1_::dtor$2
 * Address: 0x1404548F0
 */

void __fastcall CryptoPP::DL_GroupParametersImpl_CryptoPP::EcPrecomputation_CryptoPP::ECP__CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__CryptoPP::DL_GroupParameters_CryptoPP::ECPPoint___::DL_GroupParametersImpl_CryptoPP::EcPrecomputation_CryptoPP::ECP__CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__CryptoPP::DL_GroupParameters_CryptoPP::ECPPoint____::_1_::dtor_2(__int64 a1, __int64 a2)
{
  CryptoPP::EcPrecomputation<CryptoPP::ECP>::~EcPrecomputation<CryptoPP::ECP>((CryptoPP::EcPrecomputation<CryptoPP::ECP> *)(*(_QWORD *)(a2 + 64) + 24i64));
}
