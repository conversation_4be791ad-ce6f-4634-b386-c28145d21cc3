/*
 * Function: ?pc_ThrowUnitRequest@CPlayer@@QEAAXPEAU_CHRID@@PEAG@Z
 * Address: 0x14009B030
 */

void __fastcall CPlayer::pc_ThrowUnitRequest(CPlayer *this, _CHRID *pidDst, unsigned __int16 *pConsumeSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomSystem *v5; // rax@39
  __int64 v6; // [sp+0h] [bp-168h]@1
  char pbyErrorCode; // [sp+44h] [bp-124h]@4
  _base_fld *v8; // [sp+58h] [bp-110h]@4
  _base_fld *v9; // [sp+60h] [bp-108h]@4
  unsigned int *v10; // [sp+68h] [bp-100h]@4
  _skill_fld *pSkillFld; // [sp+70h] [bp-F8h]@4
  CCharacter *pDst; // [sp+78h] [bp-F0h]@4
  __int16 v13; // [sp+84h] [bp-E4h]@4
  char v14; // [sp+86h] [bp-E2h]@4
  _STORAGE_LIST::_db_con *ppConsumeItems; // [sp+A8h] [bp-C0h]@41
  char v16; // [sp+B0h] [bp-B8h]@41
  int v17; // [sp+D8h] [bp-90h]@41
  char v18; // [sp+DCh] [bp-8Ch]@41
  bool v19; // [sp+104h] [bp-64h]@41
  char v20; // [sp+105h] [bp-63h]@41
  unsigned __int16 v21; // [sp+114h] [bp-54h]@50
  bool v22; // [sp+118h] [bp-50h]@51
  bool v23; // [sp+124h] [bp-44h]@51
  CUserDB *v24; // [sp+140h] [bp-28h]@39
  int n; // [sp+148h] [bp-20h]@39
  CGuild *v26; // [sp+150h] [bp-18h]@39
  unsigned __int64 v27; // [sp+158h] [bp-10h]@4
  CPlayer *v28; // [sp+170h] [bp+8h]@1
  _CHRID *pidDsta; // [sp+178h] [bp+10h]@1
  unsigned __int16 *pItemSerials; // [sp+180h] [bp+18h]@1

  pItemSerials = pConsumeSerial;
  pidDsta = pidDst;
  v28 = this;
  v3 = &v6;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v27 = (unsigned __int64)&v6 ^ _security_cookie;
  pbyErrorCode = 0;
  v8 = 0i64;
  v9 = 0i64;
  v10 = 0i64;
  pSkillFld = 0i64;
  pDst = 0i64;
  v13 = 0;
  memset(&v14, 0, 4ui64);
  if ( CPlayer::IsRidingUnit(v28) )
  {
    v10 = &v28->m_pUsingUnit->dwBullet[1];
    if ( *((_WORD *)v10 + 1) && *((_WORD *)v10 + 1) != 0xFFFF )
    {
      v9 = CRecordData::GetRecord(&stru_1799C8AF0, *(_WORD *)v10);
      if ( v9 )
      {
        v8 = CRecordData::GetRecord(&stru_1799C86D0 + 4, v28->m_pUsingUnit->byPart[4]);
        if ( v8 )
        {
          if ( !strncmp(&v9[3].m_strCode[20], "-1", 2ui64) )
          {
            pbyErrorCode = 19;
          }
          else
          {
            pSkillFld = (_skill_fld *)CRecordData::GetRecord(&stru_1799C8410 + 3, &v9[3].m_strCode[20]);
            if ( pSkillFld )
            {
              if ( v28->m_bSFDelayNotCheck || _ATTACK_DELAY_CHECKER::IsDelay(&v28->m_AttDelayChker, -1, 0xFFu, -1) )
              {
                pDst = (CCharacter *)CMainThread::GetObjectA(&g_Main, 0, pidDsta->byID, pidDsta->wIndex);
                if ( pDst )
                {
                  if ( pDst->m_bLive && pDst->m_pCurMap == v28->m_pCurMap )
                  {
                    if ( CCharacter::IsEffectableDst((CCharacter *)&v28->vfptr, pSkillFld->m_strActableDst, pDst) )
                    {
                      if ( pSkillFld->m_nTempEffectType != -1 || pSkillFld->m_nContEffectType != -1 )
                      {
                        if ( pSkillFld->m_nContEffectType != -1 )
                        {
                          if ( !(unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsRecvableContEffect)(pDst) )
                          {
                            pbyErrorCode = 13;
                            goto $RESULT_12;
                          }
                          if ( !pSkillFld->m_nContEffectType
                            && !(unsigned __int8)((int (__fastcall *)(CPlayer *))v28->vfptr->IsAttackableInTown)(v28)
                            && !(unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsAttackableInTown)(pDst) )
                          {
                            if ( (unsigned __int8)((int (__fastcall *)(CPlayer *))v28->vfptr->IsInTown)(v28)
                              || (unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsInTown)(pDst)
                              || v28->m_Param.m_pGuild
                              && (v24 = v28->m_pUserDB,
                                  n = v28->m_ObjID.m_wIndex,
                                  v26 = v28->m_Param.m_pGuild,
                                  v5 = CGuildRoomSystem::GetInstance(),
                                  CGuildRoomSystem::IsGuildRoomMemberIn(v5, v26->m_dwSerial, n, v24->m_dwSerial)) )
                            {
                              pbyErrorCode = 18;
                              goto $RESULT_12;
                            }
                          }
                        }
                        ppConsumeItems = 0i64;
                        memset(&v16, 0, 0x10ui64);
                        v17 = 0;
                        memset(&v18, 0, 8ui64);
                        v19 = 0;
                        memset(&v20, 0, 2ui64);
                        if ( CPlayer::GetUseConsumeItem(
                               v28,
                               pSkillFld->m_ConsumeItemList,
                               pItemSerials,
                               &ppConsumeItems,
                               &v17,
                               &v19) )
                        {
                          if ( pSkillFld->m_nContEffectType != -1 )
                          {
                            if ( pSkillFld->m_nContEffectType == 1 )
                            {
                              if ( CPlayer::_pre_check_in_guild_battle_race(v28, pDst, 0) )
                                pbyErrorCode = 13;
                            }
                            else if ( CPlayer::_pre_check_in_guild_battle_race(v28, pDst, 1) )
                            {
                              pbyErrorCode = 13;
                            }
                          }
                        }
                        else
                        {
                          pbyErrorCode = 32;
                        }
                      }
                      else
                      {
                        pbyErrorCode = 8;
                      }
                    }
                    else
                    {
                      pbyErrorCode = 5;
                    }
                  }
                  else
                  {
                    pbyErrorCode = 2;
                  }
                }
                else
                {
                  pbyErrorCode = 2;
                }
              }
              else
              {
                pbyErrorCode = 9;
              }
            }
            else
            {
              pbyErrorCode = 8;
            }
          }
        }
        else
        {
          pbyErrorCode = 21;
        }
      }
      else
      {
        pbyErrorCode = 21;
      }
    }
    else
    {
      pbyErrorCode = 19;
    }
  }
  else
  {
    pbyErrorCode = 23;
  }
$RESULT_12:
  v21 = -1;
  g_tmpEffectedNum = 0;
  if ( !pbyErrorCode )
  {
    v21 = v9->m_dwIndex;
    v22 = CCharacter::GetStealth((CCharacter *)&v28->vfptr, 1);
    v23 = 0;
    if ( CCharacter::AssistSkill((CCharacter *)&v28->vfptr, pDst, 3, pSkillFld, 1, &pbyErrorCode, &v23) )
    {
      CPlayer::SendMsg_AlterUnitBulletInform(v28, 1, --*((_WORD *)v10 + 1));
      if ( !*((_WORD *)v10 + 1) )
        *v10 = -1;
      CPlayer::DeleteUseConsumeItem(v28, &ppConsumeItems, &v17, &v19);
      _ATTACK_DELAY_CHECKER::SetDelay(&v28->m_AttDelayChker, *(_DWORD *)&v8[4].m_strCode[48]);
      if ( v22 )
        CCharacter::BreakStealth((CCharacter *)&v28->vfptr);
    }
  }
  CPlayer::SendMsg_ThrowUnitResult(v28, pbyErrorCode, pidDsta, v21);
}
