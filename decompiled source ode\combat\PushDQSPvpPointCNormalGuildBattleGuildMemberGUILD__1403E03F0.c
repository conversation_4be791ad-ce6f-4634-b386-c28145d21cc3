/*
 * Function: ?PushDQSPvpPoint@CNormalGuildBattleGuildMember@GUILD_BATTLE@@IEAAXK@Z
 * Address: 0x1403E03F0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuildMember::PushDQSPvpPoint(GUILD_BATTLE::CNormalGuildBattleGuildMember *this, unsigned int dwPvpPoint)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@4
  __int64 v5; // [sp+0h] [bp-68h]@1
  char *pQryData; // [sp+20h] [bp-48h]@4
  _qry_case_addpvppoint v7; // [sp+38h] [bp-30h]@4
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v8; // [sp+70h] [bp+8h]@1
  unsigned int v9; // [sp+78h] [bp+10h]@1

  v9 = dwPvpPoint;
  v8 = this;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7.dwSerial = v8->m_dwSerial;
  v7.dwPoint = dwPvpPoint;
  v7.dwCashBag = 0;
  v4 = _qry_case_addpvppoint::size(&v7);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 13, (char *)&v7, v4);
  *(double *)&pQryData = v8->m_dPvpPoint + (double)(signed int)v9;
  CLogFile::Write(&stru_1799C9940, "[ %d ] type: guildbattle  >> pvp : %d  last: %.0f", v8->m_dwSerial, v9);
}
