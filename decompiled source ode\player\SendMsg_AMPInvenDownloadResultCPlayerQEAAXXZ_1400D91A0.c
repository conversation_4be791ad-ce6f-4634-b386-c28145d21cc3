/*
 * Function: ?SendMsg_AMPInvenDownloadResult@CPlayer@@QEAAXXZ
 * Address: 0x1400D91A0
 */

void __fastcall CPlayer::SendMsg_AMPInvenDownloadResult(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@9
  __int64 v4; // [sp+0h] [bp-158h]@1
  _personal_automine_download_zocl v5; // [sp+40h] [bp-118h]@4
  int v6; // [sp+114h] [bp-44h]@4
  int j; // [sp+118h] [bp-40h]@4
  char *v8; // [sp+120h] [bp-38h]@6
  char pbyType; // [sp+134h] [bp-24h]@9
  char v10; // [sp+135h] [bp-23h]@9
  CPlayer *v11; // [sp+160h] [bp+8h]@1

  v11 = this;
  v1 = &v4;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11->m_bAMPInvenDownload = 1;
  v5.byHaveBag = CPlayerDB::BeHaveBoxOfAMP(&v11->m_Param);
  v6 = 0;
  for ( j = 0; j < 40; ++j )
  {
    v8 = &v11->m_Param.m_dbPersonalAmineInven.m_pStorageList[j].m_bLoad;
    if ( *v8 )
    {
      v5.list[v6].byClientSlotIndex = v8[2];
      v5.list[v6].byTblCode = v8[1];
      v5.list[v6].wItemTblIndex = *(_WORD *)(v8 + 3);
      v5.list[v6++].byDur = v8[5];
    }
  }
  v5.byCnt = v6;
  pbyType = 3;
  v10 = 55;
  v3 = _personal_automine_download_zocl::size(&v5);
  CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, &v5.byHaveBag, v3);
}
