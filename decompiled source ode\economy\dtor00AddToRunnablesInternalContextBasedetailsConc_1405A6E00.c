/*
 * Function: ?dtor$0@?0??AddToRunnables@InternalContextBase@details@Concurrency@@MEAAXVlocation@3@@Z@4HA_4
 * Address: 0x1405A6E00
 */

void __fastcall `Concurrency::details::InternalContextBase::AddToRunnables'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int>>::~_Vector_const_iterator<unsigned int,std::allocator<unsigned int>>(*(std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int> > **)(a2 + 264));
}
