/*
 * Function: ?Init@_ITEMCOMBINE_DB_BASE@@QEAAXXZ
 * Address: 0x1400772D0
 */

void __fastcall _ITEMCOMBINE_DB_BASE::Init(_ITEMCOMBINE_DB_BASE *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _ITEMCOMBINE_DB_BASE *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->m_bIsResult = 0;
  v5->m_byItemListNum = 0;
  v5->m_dwDalant = 0;
  v5->m_dwCheckKey = -1;
  v5->m_bySelectItemCount = 0;
  for ( j = 0; j < 24; ++j )
    _ITEMCOMBINE_DB_BASE::_LIST::Init(&v5->m_List[j]);
}
