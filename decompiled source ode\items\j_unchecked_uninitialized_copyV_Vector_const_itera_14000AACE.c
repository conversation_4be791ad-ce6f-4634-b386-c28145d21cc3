/*
 * Function: j_??$unchecked_uninitialized_copy@V?$_Vector_const_iterator@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@PEAVCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@2@@stdext@@YAPEAVCUnmannedTraderRegistItemInfo@@V?$_Vector_const_iterator@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@0PEAV1@AEAV?$allocator@VCUnmannedTraderRegistItemInfo@@@3@@Z
 * Address: 0x14000AACE
 */

CUnmannedTraderRegistItemInfo *__fastcall stdext::unchecked_uninitialized_copy<std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>,CUnmannedTraderRegistItemInfo *,std::allocator<CUnmannedTraderRegistItemInfo>>(std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *_First, std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *_Last, CUnmannedTraderRegistItemInfo *_Dest, std::allocator<CUnmannedTraderRegistItemInfo> *_Al)
{
  return stdext::unchecked_uninitialized_copy<std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>,CUnmannedTraderRegistItemInfo *,std::allocator<CUnmannedTraderRegistItemInfo>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
