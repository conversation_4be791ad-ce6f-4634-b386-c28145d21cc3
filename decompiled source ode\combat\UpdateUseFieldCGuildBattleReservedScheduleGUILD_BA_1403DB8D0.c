/*
 * Function: ?UpdateUseField@CGuildBattleReservedSchedule@GUILD_BATTLE@@AEAAXKK@Z
 * Address: 0x1403DB8D0
 */

void __fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::UpdateUseField(GUILD_BATTLE::CGuildBattleReservedSchedule *this, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt)
{
  int *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v5; // [sp+0h] [bp-18h]@1
  unsigned int v6; // [sp+4h] [bp-14h]@4
  unsigned int j; // [sp+8h] [bp-10h]@4
  GUILD_BATTLE::CGuildBattleReservedSchedule *v8; // [sp+20h] [bp+8h]@1

  v8 = this;
  v3 = (int *)&v5;
  for ( i = 4i64; i; --i )
  {
    *v3 = -858993460;
    ++v3;
  }
  v5 = dwStartTimeInx;
  v6 = dwElapseTimeCnt + dwStartTimeInx;
  for ( j = dwStartTimeInx; j < v6; ++j )
    v8->m_bUseField[j] = 1;
}
