/*
 * Function: j_??$_Uninit_move@PEAVCMoveMapLimitRightInfo@@PEAV1@V?$allocator@VCMoveMapLimitRightInfo@@@std@@U_Undefined_move_tag@3@@std@@YAPEAVCMoveMapLimitRightInfo@@PEAV1@00AEAV?$allocator@VCMoveMapLimitRightInfo@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140006190
 */

CMoveMapLimitRightInfo *__fastcall std::_Uninit_move<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *,std::allocator<CMoveMapLimitRightInfo>,std::_Undefined_move_tag>(CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, CMoveMapLimitRightInfo *_Dest, std::allocator<CMoveMapLimitRightInfo> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *,std::allocator<CMoveMapLimitRightInfo>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
