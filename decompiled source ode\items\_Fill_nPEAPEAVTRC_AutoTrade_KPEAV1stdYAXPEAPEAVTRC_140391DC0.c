/*
 * Function: ??$_Fill_n@PEAPEAVTRC_AutoTrade@@_KPEAV1@@std@@YAXPEAPEAVTRC_AutoTrade@@_KAEBQEAV1@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140391DC0
 */

void __fastcall std::_Fill_n<TRC_AutoTrade * *,unsigned __int64,TRC_AutoTrade *>(TRC_AutoTrade **_First, unsigned __int64 _Count, TRC_AutoTrade *const *_Val, std::_Range_checked_iterator_tag __formal)
{
  memset64(_First, (unsigned __int64)*_Val, _Count);
}
