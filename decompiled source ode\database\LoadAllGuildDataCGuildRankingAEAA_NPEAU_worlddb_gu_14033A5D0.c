/*
 * Function: ?LoadAllGuildData@CGuildRanking@@AEAA_NPEAU_worlddb_guild_info@@@Z
 * Address: 0x14033A5D0
 */

char __fastcall CGuildRanking::LoadAllGuildData(CGuildRanking *this, _worlddb_guild_info *pkInfo)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-48h]@1
  long double *dGold; // [sp+20h] [bp-28h]@8
  unsigned __int16 j; // [sp+30h] [bp-18h]@6
  __int64 v8; // [sp+38h] [bp-10h]@8
  CGuildRanking *v9; // [sp+50h] [bp+8h]@1
  _worlddb_guild_info *Dst; // [sp+58h] [bp+10h]@1

  Dst = pkInfo;
  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  memset_0(pkInfo, 0, 0x280A8ui64);
  if ( CRFWorldDatabase::Select_AllGuildData(pkDB, Dst) )
  {
    CLogFile::Write(&stru_1799C8F30, "Guild List(%d) Load Complete!!", Dst->wGuildCount);
    for ( j = 0; j < (signed int)Dst->wGuildCount; ++j )
    {
      v8 = 328i64 * j;
      dGold = &Dst->GuildData[j].dGold;
      if ( !CGuildRanking::CheckGuildCheckSum(
              v9,
              Dst->GuildData[j].dwGuildSerial,
              Dst->GuildData[j].wszGuildName,
              &Dst->GuildData[j].dDalant,
              dGold) )
        return 0;
    }
    result = 1;
  }
  else
  {
    MyMessageBox("DatabaseInit", "Select_AllGuildData() fail");
    result = 0;
  }
  return result;
}
