/*
 * Function: ??0CNormalGuildBattleStateCountDown@GUILD_BATTLE@@QEAA@XZ
 * Address: 0x1403F0820
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleStateCountDown::CNormalGuildBattleStateCountDown(GUILD_BATTLE::CNormalGuildBattleStateCountDown *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleStateCountDown *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  GUILD_BATTLE::CNormalGuildBattleState::CNormalGuildBattleState((GUILD_BATTLE::CNormalGuildBattleState *)&v4->vfptr);
  v4->vfptr = (GUILD_BATTLE::CGuildBattleStateVtbl *)&GUILD_BATTLE::CNormalGuildBattleStateCountDown::`vftable';
}
