/*
 * Function: j_?push_back@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAAXAEBQEAVTRC_AutoTrade@@@Z
 * Address: 0x140008F08
 */

void __fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::push_back(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, TRC_AutoTrade *const *_Val)
{
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::push_back(this, _Val);
}
