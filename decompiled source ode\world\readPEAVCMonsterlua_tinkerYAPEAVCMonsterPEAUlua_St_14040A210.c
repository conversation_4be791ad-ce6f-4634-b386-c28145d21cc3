/*
 * Function: ??$read@PEAVCMonster@@@lua_tinker@@YAPEAVCMonster@@PEAUlua_State@@H@Z
 * Address: 0x14040A210
 */

CMonster *__fastcall lua_tinker::read<CMonster *>(struct lua_State *L, int index)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  struct lua_State *La; // [sp+30h] [bp+8h]@1

  La = L;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return lua_tinker::lua2type<CMonster *>(La, index);
}
