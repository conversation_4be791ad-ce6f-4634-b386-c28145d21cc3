/*
 * Function: ??$_Fill@PEAPEAVCUnmannedTraderSortType@@PEAV1@@std@@YAXPEAPEAVCUnmannedTraderSortType@@0AEBQEAV1@@Z
 * Address: 0x140375280
 */

void __fastcall std::_Fill<CUnmannedTraderSortType * *,CUnmannedTraderSortType *>(CUnmannedTraderSortType **_First, CUnmannedTraderSortType **_Last, CUnmannedTraderSortType *const *_Val)
{
  CUnmannedTraderSortType **i; // [sp+10h] [bp+8h]@1

  for ( i = _First; i != _Last; ++i )
    *i = *_Val;
}
