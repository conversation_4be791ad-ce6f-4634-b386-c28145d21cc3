/*
 * Function: j_??$_Insert@V?$_Vector_const_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@@?$vector@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@QEAAXV?$_Vector_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@1@V?$_Vector_const_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@1@1Uforward_iterator_tag@1@@Z
 * Address: 0x140004075
 */

void __fastcall std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Insert<std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>>(std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *this, std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *_Where, std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *_First, std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *_Last, std::forward_iterator_tag __formal)
{
  std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Insert<std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>>(
    this,
    _Where,
    _First,
    _Last,
    __formal);
}
