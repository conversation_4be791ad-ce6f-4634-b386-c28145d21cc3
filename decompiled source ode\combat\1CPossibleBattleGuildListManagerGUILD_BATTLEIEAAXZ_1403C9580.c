/*
 * Function: ??1CPossibleBattleGuildListManager@GUILD_BATTLE@@IEAA@XZ
 * Address: 0x1403C9580
 */

void __fastcall GUILD_BATTLE::CPossibleBattleGuildListManager::~CPossibleBattleGuildListManager(GUILD_BATTLE::CPossibleBattleGuildListManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  unsigned int j; // [sp+20h] [bp-28h]@5
  void *v5; // [sp+28h] [bp-20h]@7
  void *v6; // [sp+30h] [bp-18h]@8
  void *v7; // [sp+38h] [bp-10h]@10
  GUILD_BATTLE::CPossibleBattleGuildListManager *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v8->m_ppkList )
  {
    for ( j = 0; j < 3; ++j )
    {
      v5 = v8->m_ppkList[j];
      operator delete(v5);
    }
    v6 = v8->m_ppkList;
    operator delete[](v6);
  }
  if ( v8->m_pMaxPage )
  {
    v7 = v8->m_pMaxPage;
    operator delete[](v7);
    v8->m_pMaxPage = 0i64;
  }
}
