/*
 * Function: ?SetSerialNumber@_db_con@_STORAGE_LIST@@QEAAXK@Z
 * Address: 0x14010E160
 */

void __fastcall _STORAGE_LIST::_db_con::SetSerialNumber(_STORAGE_LIST::_db_con *this, unsigned int dwSN)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  _STORAGE_LIST::_db_con *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( dwSN )
    v5->m_dwETSerialNumber = dwSN;
  else
    v5->m_dwETSerialNumber = _STORAGE_LIST::_db_con::CalcNewSerialNumber();
}
