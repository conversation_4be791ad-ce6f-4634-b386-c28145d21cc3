/*
 * Function: j_??$_Ptr_cat@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAPEAVCGuildBattleRewardItem@GUILD_BATTLE@@0@Z
 * Address: 0x140009435
 */

std::_Nonscalar_ptr_iterator_tag __fastcall std::_Ptr_cat<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *>(GUILD_BATTLE::CGuildBattleRewardItem **__formal, GUILD_BATTLE::CGuildBattleRewardItem **a2)
{
  return std::_Ptr_cat<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *>(__formal, a2);
}
