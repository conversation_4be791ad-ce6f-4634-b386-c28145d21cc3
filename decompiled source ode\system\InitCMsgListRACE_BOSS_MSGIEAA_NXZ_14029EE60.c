/*
 * Function: ?Init@CMsgList@RACE_BOSS_MSG@@IEAA_NXZ
 * Address: 0x14029EE60
 */

char __fastcall RACE_BOSS_MSG::CMsgList::Init(RACE_BOSS_MSG::CMsgList *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // rax@9
  __int64 v5; // [sp+0h] [bp-68h]@1
  unsigned int dwID; // [sp+20h] [bp-48h]@6
  unsigned int dwIndex; // [sp+24h] [bp-44h]@12
  unsigned int j; // [sp+28h] [bp-40h]@15
  RACE_BOSS_MSG::CMsg **v9; // [sp+30h] [bp-38h]@6
  RACE_BOSS_MSG::CMsg *v10; // [sp+38h] [bp-30h]@11
  RACE_BOSS_MSG::CMsg *v11; // [sp+40h] [bp-28h]@8
  __int64 v12; // [sp+48h] [bp-20h]@4
  unsigned __int64 v13; // [sp+50h] [bp-18h]@6
  RACE_BOSS_MSG::CMsg *v14; // [sp+58h] [bp-10h]@9
  RACE_BOSS_MSG::CMsgList *v15; // [sp+70h] [bp+8h]@1

  v15 = this;
  v1 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = -2i64;
  if ( v15->m_uiSize )
  {
    CNetIndexList::SetList(&v15->m_kEmptyInxList, v15->m_uiSize);
    CNetIndexList::SetList(&v15->m_kUseInxList, v15->m_uiSize);
    CNetIndexList::SetList(&v15->m_kWaitInxList, v15->m_uiSize);
    v13 = v15->m_uiSize;
    v9 = (RACE_BOSS_MSG::CMsg **)operator new[](saturated_mul(8ui64, v13));
    v15->m_ppMsg = v9;
    for ( dwID = 0; dwID < v15->m_uiSize; ++dwID )
    {
      v11 = (RACE_BOSS_MSG::CMsg *)operator new(0x58ui64);
      if ( v11 )
      {
        RACE_BOSS_MSG::CMsg::CMsg(v11, v15->m_ucRace, dwID);
        v14 = (RACE_BOSS_MSG::CMsg *)v4;
      }
      else
      {
        v14 = 0i64;
      }
      v10 = v14;
      v15->m_ppMsg[dwID] = v14;
    }
    for ( dwIndex = 0; dwIndex < 2; ++dwIndex )
      CNetIndexList::PushNode_Back(&v15->m_kEmptyInxList, dwIndex);
    for ( j = 2; j < 4; ++j )
      CNetIndexList::PushNode_Back(&v15->m_kWaitInxList, j);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
