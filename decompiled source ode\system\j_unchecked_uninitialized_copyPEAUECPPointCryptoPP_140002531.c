/*
 * Function: j_??$unchecked_uninitialized_copy@PEAUECPPoint@CryptoPP@@PEAU12@V?$allocator@UECPPoint@CryptoPP@@@std@@@stdext@@YAPEAUECPPoint@CryptoPP@@PEAU12@00AEAV?$allocator@UECPPoint@CryptoPP@@@std@@@Z
 * Address: 0x140002531
 */

CryptoPP::ECPPoint *__fastcall stdext::unchecked_uninitialized_copy<CryptoPP::ECPPoint *,CryptoPP::ECPPoint *,std::allocator<CryptoPP::ECPPoint>>(CryptoPP::ECPPoint *_First, CryptoPP::ECPPoint *_Last, CryptoPP::ECPPoint *_Dest, std::allocator<CryptoPP::ECPPoint> *_Al)
{
  return stdext::unchecked_uninitialized_copy<CryptoPP::ECPPoint *,CryptoPP::ECPPoint *,std::allocator<CryptoPP::ECPPoint>>(
           _First,
           _Last,
           _<PERSON><PERSON>,
           _<PERSON>);
}
