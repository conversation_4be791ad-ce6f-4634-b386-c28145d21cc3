/*
 * Function: ?db_char_set_alive@CMainThread@@QEAAEKEKPEADEPEAU_REGED@@@Z
 * Address: 0x1401B23F0
 */

char __usercall CMainThread::db_char_set_alive@<al>(CMainThread *this@<rcx>, unsigned int dwAccountSerial@<edx>, char byCase@<r8b>, unsigned int dwSerial@<r9d>, signed __int64 a5@<rax>, char *pwszName, char bySlot, _REGED *pAliveAvator)
{
  void *v8; // rsp@1
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v12; // [sp-30h] [bp-B0B8h]@1
  char Dst; // [sp+0h] [bp-B088h]@4
  char Dest; // [sp+AF28h] [bp-160h]@4
  char v15; // [sp+AF29h] [bp-15Fh]@4
  char by<PERSON>harNum; // [sp+AF54h] [bp-134h]@4
  char v17; // [sp+AF64h] [bp-124h]@4
  int j; // [sp+AF68h] [bp-120h]@21
  _worlddb_character_base_info pCharacterData; // [sp+AF80h] [bp-108h]@28
  int k; // [sp+B064h] [bp-24h]@28
  unsigned __int64 v21; // [sp+B070h] [bp-18h]@4
  CMainThread *v22; // [sp+B090h] [bp+8h]@1
  unsigned int dwAccountSeriala; // [sp+B098h] [bp+10h]@1
  char v24; // [sp+B0A0h] [bp+18h]@1
  unsigned int dwSeriala; // [sp+B0A8h] [bp+20h]@1

  dwSeriala = dwSerial;
  v24 = byCase;
  dwAccountSeriala = dwAccountSerial;
  v22 = this;
  v8 = alloca(a5);
  v9 = &v12;
  for ( i = 11308i64; i; --i )
  {
    *(_DWORD *)v9 = -*********;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v21 = (unsigned __int64)&v12 ^ _security_cookie;
  memset_0(&Dst, 0, 0xAF08ui64);
  Dest = 0;
  memset(&v15, 0, 0x10ui64);
  byCharNum = 0;
  v17 = CRFWorldDatabase::Select_CharNumInWorld(v22->m_pWorldDB, dwAccountSeriala, &byCharNum);
  if ( v17 == 1 )
    return 24;
  if ( (signed int)(unsigned __int8)byCharNum >= 3 )
    return 58;
  if ( !CRFWorldDatabase::Select_ArrangeInfo(v22->m_pWorldDB, dwSeriala) )
    return 57;
  strcpy_0(&Dest, pwszName);
  if ( Dest == 42 || Dest == 33 )
    return 47;
  if ( v24 )
  {
    if ( !CRFWorldDatabase::Select_Equal_Name(v22->m_pWorldDB, &Dest) )
      return 25;
    if ( !CRFWorldDatabase::Select_Equal_DeleteName_NoArranged(v22->m_pWorldDB, &Dest) )
      return 25;
  }
  else if ( !CRFWorldDatabase::Select_Equal_Name(v22->m_pWorldDB, &Dest) )
  {
    return 25;
  }
  for ( j = 0; j < 3; ++j )
  {
    if ( !strcmp_0(&Dest, wszNonMakeName_1[j]) )
      return 47;
  }
  if ( CRFWorldDatabase::Update_SetActive(v22->m_pWorldDB, dwSeriala, &Dest, bySlot) )
  {
    CRFWorldDatabase::Update_CharSlot(v22->m_pWorldDB, dwSeriala);
    memset_0(&pCharacterData, 0, 0xE0ui64);
    CRFWorldDatabase::Select_CharacterBaseInfo(v22->m_pWorldDB, dwSeriala, &pCharacterData);
    pAliveAvator->m_bySlotIndex = bySlot;
    pAliveAvator->m_dwRecordNum = dwSeriala;
    strcpy_0(pAliveAvator->m_wszAvatorName, pCharacterData.wszName);
    pAliveAvator->m_byRaceSexCode = pCharacterData.byRace;
    strcpy_0(pAliveAvator->m_szClassCode, pCharacterData.szClassCode);
    pAliveAvator->m_byLevel = pCharacterData.byLevel;
    pAliveAvator->m_dwDalant = pCharacterData.dwDalant;
    pAliveAvator->m_dwGold = pCharacterData.dwGold;
    pAliveAvator->m_dwBaseShape = pCharacterData.dwBaseShape;
    pAliveAvator->m_dwLastConnTime = pCharacterData.dwLastConnTime;
    for ( k = 0; k < 8; ++k )
    {
      _EQUIPKEY::LoadDBKey(&pAliveAvator->m_EquipKey[k], pCharacterData.shEKArray[k]);
      pAliveAvator->m_dwFixEquipLv[k] = pCharacterData.dwEUArray[k];
      pAliveAvator->m_lnUID[k] = pCharacterData.lnUIDArray[k];
      pAliveAvator->m_dwET[k] = pCharacterData.dwETArray[k];
    }
    result = 0;
  }
  else
  {
    result = 24;
  }
  return result;
}
