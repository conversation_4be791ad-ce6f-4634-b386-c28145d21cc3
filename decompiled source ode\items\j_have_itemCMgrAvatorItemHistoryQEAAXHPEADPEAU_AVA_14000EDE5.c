/*
 * Function: j_?have_item@CMgrAvatorItemHistory@@QEAAXHPEADPEAU_AVATOR_DATA@@10KEKK_N0@Z
 * Address: 0x14000EDE5
 */

void __fastcall CMgrAvatorItemHistory::have_item(CMgrAvatorItemHistory *this, int n, char *pszName, _AVATOR_DATA *pLoadData, _AVATOR_DATA *pBackupData, char *pszID, unsigned int dwIDSerial, char byDgr, unsigned int dwIP, unsigned int dwExpRate, bool bStart, char *pszFileName)
{
  CMgrAvatorItemHistory::have_item(
    this,
    n,
    pszName,
    pLoadData,
    pBackupData,
    pszID,
    dwIDSerial,
    byDgr,
    dwIP,
    dwExpRate,
    bStart,
    pszFileName);
}
