/*
 * Function: ?_db_load_losebattlecount@CMainThread@@AEAAEKPEAU_AVATOR_DATA@@@Z
 * Address: 0x1401A99F0
 */

char __fastcall CMainThread::_db_load_losebattlecount(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pCon)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-48h]@1
  int v7; // [sp+20h] [bp-28h]@4
  unsigned int *dwCount; // [sp+28h] [bp-20h]@4
  unsigned int *v9; // [sp+30h] [bp-18h]@6
  unsigned int *v10; // [sp+38h] [bp-10h]@8
  CMainThread *v11; // [sp+50h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+58h] [bp+10h]@1
  _AVATOR_DATA *v13; // [sp+60h] [bp+18h]@1

  v13 = pCon;
  dwSeriala = dwSerial;
  v11 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  dwCount = pCon->dbAvator.m_dwRaceBattleRecord;
  v7 = CRFWorldDatabase::Select_WinBattleCount(
         v11->m_pWorldDB,
         pCon->dbAvator.m_byRaceSexCode >> 1,
         dwSerial,
         pCon->dbAvator.m_dwRaceBattleRecord);
  if ( v7 == 1 )
  {
    result = 24;
  }
  else
  {
    v9 = &v13->dbAvator.m_dwRaceBattleRecord[1];
    v7 = CRFWorldDatabase::Select_FailBattleCount(
           v11->m_pWorldDB,
           v13->dbAvator.m_byRaceSexCode >> 1,
           dwSeriala,
           &v13->dbAvator.m_dwRaceBattleRecord[1]);
    if ( v7 == 1 )
    {
      result = 24;
    }
    else
    {
      v10 = &v13->dbAvator.m_dwRaceBattleRecord[2];
      v7 = CRFWorldDatabase::Select_LoseBattleCount(
             v11->m_pWorldDB,
             v13->dbAvator.m_byRaceSexCode >> 1,
             dwSeriala,
             &v13->dbAvator.m_dwRaceBattleRecord[2]);
      if ( v7 == 1 )
        result = 24;
      else
        result = 0;
    }
  }
  return result;
}
