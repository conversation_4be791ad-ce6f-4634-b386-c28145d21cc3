/*
 * Function: j_??$_Ucopy@PEAVCUnmannedTraderRegistItemInfo@@@?$vector@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@IEAAPEAVCUnmannedTraderRegistItemInfo@@PEAV2@00@Z
 * Address: 0x140011473
 */

CUnmannedTraderRegistItemInfo *__fastcall std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Ucopy<CUnmannedTraderRegistItemInfo *>(std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *this, CUnmannedTraderRegistItemInfo *_First, CUnmannedTraderRegistItemInfo *_Last, CUnmannedTraderRegistItemInfo *_Ptr)
{
  return std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Ucopy<CUnmannedTraderRegistItemInfo *>(
           this,
           _First,
           _Last,
           _Ptr);
}
