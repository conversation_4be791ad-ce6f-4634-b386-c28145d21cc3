/*
 * Function: j_?BuyComplete@CUnmannedTraderUserInfo@@QEAAEPEAVCPlayer@@KPEAD1KK_KKK2PEAVCLogFile@@AEAG@Z
 * Address: 0x1400080B2
 */

char __fastcall CUnmannedTraderUserInfo::BuyComplete(CUnmannedTraderUserInfo *this, CPlayer *pkBuyer, unsigned int dwSellerSerial, char *wszSellerName, char *szSellerAccountName, unsigned int dwRegistSerial, unsigned int dwK, unsigned __int64 dwD, unsigned int dwU, unsigned int dwPrice, unsigned __int64 lnUID, CLogFile *pkLogger, unsigned __int16 *wAddItemSerial)
{
  return CUnmannedTraderUserInfo::BuyComplete(
           this,
           pkBuyer,
           dwSellerSerial,
           wszSellerName,
           szSellerAccountName,
           dwRegistSerial,
           dwK,
           dwD,
           dwU,
           dwPrice,
           lnUID,
           pkLogger,
           wAddItemSerial);
}
