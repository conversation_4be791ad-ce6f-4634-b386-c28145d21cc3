/*
 * Function: j_??$_Uninit_move@PEAPEAVTRC_AutoTrade@@PEAPEAV1@V?$allocator@PEAVTRC_AutoTrade@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAVTRC_AutoTrade@@PEAPEAV1@00AEAV?$allocator@PEAVTRC_AutoTrade@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000FD7B
 */

TRC_AutoTrade **__fastcall std::_Uninit_move<TRC_AutoTrade * *,TRC_AutoTrade * *,std::allocator<TRC_AutoTrade *>,std::_Undefined_move_tag>(TRC_AutoTrade **_First, TRC_AutoTrade **_Last, TRC_AutoTrade **_Dest, std::allocator<TRC_AutoTrade *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<TRC_AutoTrade * *,TRC_AutoTrade * *,std::allocator<TRC_AutoTrade *>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
