/*
 * Function: ?CleanUp@CCurrentGuildBattleInfoManager@GUILD_BATTLE@@IEAAXXZ
 * Address: 0x1403CE510
 */

void __fastcall GUILD_BATTLE::CCurrentGuildBattleInfoManager::CleanUp(GUILD_BATTLE::CCurrentGuildBattleInfoManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  void *v4; // [sp+20h] [bp-18h]@6
  void *v5; // [sp+28h] [bp-10h]@8
  GUILD_BATTLE::CCurrentGuildBattleInfoManager *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_uiMapCnt )
  {
    if ( v6->m_pkInfo )
    {
      v4 = v6->m_pkInfo;
      operator delete[](v4);
      v6->m_uiMapCnt = 0;
    }
    if ( v6->m_pbUpdate )
    {
      v5 = v6->m_pbUpdate;
      operator delete[](v5);
      v6->m_pbUpdate = 0i64;
    }
    v6->m_uiMapCnt = 0;
  }
}
