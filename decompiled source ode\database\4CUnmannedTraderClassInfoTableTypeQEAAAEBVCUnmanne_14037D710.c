/*
 * Function: ??4CUnmannedTraderClassInfoTableType@@QEAAAEBVCUnmannedTraderClassInfo@@AEBV0@@Z
 * Address: 0x14037D710
 */

CUnmannedTraderClassInfoTableType *__fastcall CUnmannedTraderClassInfoTableType::operator=(CUnmannedTraderClassInfoTableType *this, CUnmannedTraderClassInfoTableType *lhs)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-B8h]@1
  char v6; // [sp+20h] [bp-98h]@5
  std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *result; // [sp+38h] [bp-80h]@5
  char v8; // [sp+40h] [bp-78h]@5
  std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *v9; // [sp+58h] [bp-60h]@5
  char v10; // [sp+60h] [bp-58h]@5
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *v11; // [sp+78h] [bp-40h]@5
  __int64 v12; // [sp+80h] [bp-38h]@4
  std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *v13; // [sp+88h] [bp-30h]@5
  std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *v14; // [sp+90h] [bp-28h]@5
  std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *v15; // [sp+98h] [bp-20h]@5
  std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *v16; // [sp+A0h] [bp-18h]@5
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *v17; // [sp+A8h] [bp-10h]@5
  CUnmannedTraderClassInfoTableType *v18; // [sp+C0h] [bp+8h]@1
  CUnmannedTraderClassInfoTableType *lhsa; // [sp+C8h] [bp+10h]@1

  lhsa = lhs;
  v18 = this;
  v2 = &v5;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = -2i64;
  CUnmannedTraderClassInfo::Copy((CUnmannedTraderClassInfo *)&v18->vfptr, (CUnmannedTraderClassInfo *)&lhs->vfptr);
  v18->m_byTableCode = lhsa->m_byTableCode;
  std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::clear(&v18->m_vecSubClass);
  if ( !std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::empty(&lhsa->m_vecSubClass) )
  {
    result = (std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)&v6;
    v9 = (std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)&v8;
    v11 = (std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)&v10;
    v13 = std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::end(
            &lhsa->m_vecSubClass,
            (std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)&v6);
    v14 = v13;
    v15 = std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::begin(
            &lhsa->m_vecSubClass,
            v9);
    v16 = v15;
    v17 = std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::begin(
            &v18->m_vecSubClass,
            v11);
    std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::insert<std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>>(
      &v18->m_vecSubClass,
      v17,
      v16,
      v14);
  }
  return v18;
}
