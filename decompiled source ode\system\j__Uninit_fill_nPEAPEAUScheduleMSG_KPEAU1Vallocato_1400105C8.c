/*
 * Function: j_??$_Uninit_fill_n@PEAPEAUScheduleMSG@@_KPEAU1@V?$allocator@PEAUScheduleMSG@@@std@@@std@@YAXPEAPEAUScheduleMSG@@_KAEBQEAU1@AEAV?$allocator@PEAUScheduleMSG@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400105C8
 */

void __fastcall std::_Uninit_fill_n<ScheduleMSG * *,unsigned __int64,ScheduleMSG *,std::allocator<ScheduleMSG *>>(ScheduleMSG **_First, unsigned __int64 _Count, ScheduleMSG *const *_Val, std::allocator<ScheduleMSG *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<ScheduleMSG * *,unsigned __int64,ScheduleMSG *,std::allocator<ScheduleMSG *>>(
    _First,
    _Count,
    _Val,
    __formal,
    a5,
    a6);
}
