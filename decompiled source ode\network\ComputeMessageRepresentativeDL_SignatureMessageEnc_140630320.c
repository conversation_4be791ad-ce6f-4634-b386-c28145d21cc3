/*
 * Function: ?ComputeMessageRepresentative@DL_SignatureMessageEncodingMethod_DSA@CryptoPP@@UEBAXAEAVRandomNumberGenerator@2@PEBE_KAEAVHashTransformation@2@U?$pair@PEBE_K@std@@_NPEAE2@Z
 * Address: 0x140630320
 */

void __fastcall CryptoPP::DL_SignatureMessageEncodingMethod_DSA::ComputeMessageRepresentative(__int64 a1, __int64 a2, __int64 a3, __int64 a4, unsigned __int8 *a5, CryptoPP *a6, __int64 a7, unsigned __int8 *a8, CryptoPP *a9)
{
  const unsigned __int64 *v9; // rax@5
  unsigned __int64 b; // [sp+20h] [bp-58h]@5
  unsigned __int64 a; // [sp+28h] [bp-50h]@5
  __int64 v12; // [sp+30h] [bp-48h]@5
  CryptoPP::Integer v13; // [sp+38h] [bp-40h]@6
  __int64 v14; // [sp+60h] [bp-18h]@1
  __int64 v15; // [sp+68h] [bp-10h]@5

  v14 = -2i64;
  if ( a4 )
    _wassert(L"recoverableMessageLength == 0", L"D:\\RF Project\\RF_Server64\\28 Crypto++\\gfpcrypt.cpp", 0x4Au);
  if ( *((_QWORD *)a6 + 1) )
    _wassert(L"hashIdentifier.second == 0", L"D:\\RF Project\\RF_Server64\\28 Crypto++\\gfpcrypt.cpp", 0x4Bu);
  a = CryptoPP::BitsToBytes(a9);
  b = (unsigned int)(*(int (__fastcall **)(unsigned __int8 *))(*(_QWORD *)a5 + 56i64))(a5);
  v12 = CryptoPP::SaturatingSubtract<unsigned __int64,unsigned __int64>(&a, &b);
  memset(a8, 0, v12);
  v9 = CryptoPP::STDMIN<unsigned __int64>(&a, &b);
  v15 = *(_QWORD *)a5;
  (*(void (__fastcall **)(unsigned __int8 *, __int64, const unsigned __int64))(v15 + 112))(a5, (__int64)&a8[v12], *v9);
  if ( 8 * b > (unsigned __int64)a9 )
  {
    CryptoPP::Integer::Integer(&v13, a8, a, 0);
    CryptoPP::Integer::operator=>>(&v13, 8 * a - (_QWORD)a9);
    CryptoPP::Integer::Encode(&v13, a8, a, 0);
    CryptoPP::Integer::~Integer(&v13);
  }
}
