/*
 * Function: ?dev_dalant@CPlayer@@QEAA_NK@Z
 * Address: 0x1400BCAD0
 */

char __fastcall CPlayer::dev_dalant(CPlayer *this, unsigned int dwDalant)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@8
  unsigned int v5; // eax@8
  __int64 v7; // [sp+0h] [bp-58h]@1
  unsigned int dwDt; // [sp+30h] [bp-28h]@4
  unsigned int gold; // [sp+34h] [bp-24h]@8
  char *v10; // [sp+38h] [bp-20h]@8
  unsigned int dwNewGold; // [sp+40h] [bp-18h]@8
  CPlayer *v12; // [sp+60h] [bp+8h]@1

  v12 = this;
  v2 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  dwDt = dwDalant;
  if ( dwDalant == -1 )
  {
    dwDt = 2000000000;
  }
  else if ( dwDalant > 0x77359400 )
  {
    dwDt = 2000000000;
  }
  CPlayerDB::SetDalant(&v12->m_Param, dwDt);
  gold = CPlayerDB::GetGold(&v12->m_Param);
  v4 = CPlayerDB::GetDalant(&v12->m_Param);
  CUserDB::Update_Money(v12->m_pUserDB, v4, gold);
  CPlayer::SendMsg_ExchangeMoneyResult(v12, 0);
  v10 = v12->m_szItemHistoryFileName;
  dwNewGold = CPlayerDB::GetGold(&v12->m_Param);
  v5 = CPlayerDB::GetDalant(&v12->m_Param);
  CMgrAvatorItemHistory::cheat_alter_money(&CPlayer::s_MgrItemHistory, v12->m_ObjID.m_wIndex, v5, dwNewGold, v10);
  return 1;
}
