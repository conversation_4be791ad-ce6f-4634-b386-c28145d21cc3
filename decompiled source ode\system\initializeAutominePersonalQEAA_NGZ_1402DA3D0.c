/*
 * Function: ?initialize@AutominePersonal@@QEAA_NG@Z
 * Address: 0x1402DA3D0
 */

char __fastcall AutominePersonal::initialize(AutominePersonal *this, unsigned __int16 wIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@4
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-1A8h]@1
  char _Dest[256]; // [sp+40h] [bp-168h]@4
  _object_id pID; // [sp+154h] [bp-54h]@9
  AP_BatterySlot *v9; // [sp+170h] [bp-38h]@7
  void *v10; // [sp+178h] [bp-30h]@4
  __int64 v11; // [sp+180h] [bp-28h]@4
  AP_BatterySlot *v12; // [sp+188h] [bp-20h]@5
  unsigned __int64 v13; // [sp+190h] [bp-18h]@4
  AutominePersonal *v14; // [sp+1B0h] [bp+8h]@1
  unsigned __int16 v15; // [sp+1B8h] [bp+10h]@1

  v15 = wIndex;
  v14 = this;
  v2 = &v6;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = -2i64;
  v13 = (unsigned __int64)&v6 ^ _security_cookie;
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0xFFui64);
  sprintf_s<256>((char (*)[256])_Dest, "..\\ZoneServerLog\\SystemLog\\Concession\\AminePersonal.log");
  CLogFile::SetWriteLogFile(&v14->m_logProcess, _Dest, 1, 0, 1, 1);
  v4 = GetKorLocalTime();
  sprintf_s<256>((char (*)[256])_Dest, "..\\ZoneServerLog\\SystemLog\\log_AminePersonal_%d.log", v4);
  CLogFile::SetWriteLogFile(&v14->m_logSysErr, _Dest, 1, 0, 1, 1);
  v10 = operator new[](0x6Aui64);
  if ( v10 )
  {
    *(_DWORD *)v10 = 2;
    `eh vector constructor iterator'(
      (char *)v10 + 4,
      0x33ui64,
      2,
      (void (__cdecl *)(void *))AP_BatterySlot::AP_BatterySlot,
      (void (__cdecl *)(void *))AP_BatterySlot::~AP_BatterySlot);
    v12 = (AP_BatterySlot *)((char *)v10 + 4);
  }
  else
  {
    v12 = 0i64;
  }
  v9 = v12;
  v14->m_pBatterySlot = v12;
  if ( v14->m_pBatterySlot )
  {
    _object_id::_object_id(&pID);
    pID.m_byKind = 0;
    pID.m_byID = 11;
    pID.m_wIndex = v15;
    CGameObject::Init((CGameObject *)&v14->vfptr, &pID);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
