/*
 * Function: ?pc_UnitDeliveryRequest@CPlayer@@QEAAXEPEAVCItemStore@@_NPEAMH@Z
 * Address: 0x140105750
 */

void __usercall CPlayer::pc_UnitDeliveryRequest(CPlayer *this@<rcx>, char bySlotIndex@<dl>, CItemStore *pStore@<r8>, bool bPay<PERSON>ee@<r9b>, float a5@<xmm0>, float *pfNewPos, int bUseNPCLinkIntem)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  int v9; // eax@6
  int v10; // eax@6
  float v11; // xmm1_4@6
  float *v12; // rax@21
  unsigned int v13; // eax@28
  int v14; // eax@40
  CMoneySupplyMgr *v15; // rax@45
  unsigned int v16; // eax@47
  __int64 v17; // [sp+0h] [bp-118h]@1
  char v18; // [sp+40h] [bp-D8h]@4
  _UNIT_DB_BASE *v19; // [sp+48h] [bp-D0h]@4
  unsigned int dwSub; // [sp+50h] [bp-C8h]@4
  CParkingUnit *v21; // [sp+58h] [bp-C0h]@4
  char v22; // [sp+60h] [bp-B8h]@4
  unsigned int v23; // [sp+64h] [bp-B4h]@6
  float v24; // [sp+68h] [bp-B0h]@6
  unsigned __int64 v25; // [sp+70h] [bp-A8h]@28
  unsigned int dwParkingUnitSerial; // [sp+78h] [bp-A0h]@34
  _parkingunit_create_setdata Dst; // [sp+88h] [bp-90h]@35
  unsigned int v28; // [sp+C4h] [bp-54h]@35
  _base_fld *v29; // [sp+C8h] [bp-50h]@35
  _base_fld *v30; // [sp+D0h] [bp-48h]@37
  int v31; // [sp+D8h] [bp-40h]@41
  int nLv; // [sp+E8h] [bp-30h]@45
  char *v33; // [sp+F0h] [bp-28h]@47
  unsigned int v34; // [sp+F8h] [bp-20h]@47
  unsigned __int64 v35; // [sp+100h] [bp-18h]@4
  CPlayer *v36; // [sp+120h] [bp+8h]@1
  char v37; // [sp+128h] [bp+10h]@1
  CItemStore *v38; // [sp+130h] [bp+18h]@1
  bool v39; // [sp+138h] [bp+20h]@1

  v39 = bPayFee;
  v38 = pStore;
  v37 = bySlotIndex;
  v36 = this;
  v7 = &v17;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v35 = (unsigned __int64)&v17 ^ _security_cookie;
  v18 = 0;
  v19 = (_UNIT_DB_BASE *)((char *)&v36->m_Param.m_UnitDB + 62 * (unsigned __int8)bySlotIndex);
  dwSub = 0;
  v21 = 0i64;
  v22 = 0;
  if ( !v36->m_Param.m_UnitDB.m_List[(unsigned __int8)bySlotIndex].dwGauge )
  {
    v18 = 35;
    goto LABEL_34;
  }
  v9 = CPlayerDB::GetRaceCode(&v36->m_Param);
  v23 = eGetTexRate(v9) + 10000;
  v10 = CPlayerDB::GetRaceCode(&v36->m_Param);
  eGetTex(v10);
  v11 = a5 + 1.0;
  v24 = a5 + 1.0;
  if ( v36->m_bObserver )
  {
    v18 = 27;
    goto LABEL_34;
  }
  if ( CPlayerDB::GetRaceCode(&v36->m_Param) )
  {
    v18 = 1;
    goto LABEL_34;
  }
  if ( v19->m_List[0].byFrame == 255 )
  {
    v18 = 5;
    goto LABEL_34;
  }
  if ( v36->m_pCurMap->m_pMapSet->m_nMapType && v36->m_pCurMap->m_pMapSet->m_nMapType != 1 )
  {
    v18 = 27;
    goto LABEL_34;
  }
  if ( v36->m_pUsingUnit )
  {
    v18 = 2;
    goto LABEL_34;
  }
  if ( v38 )
  {
    if ( v38->m_pRec->m_nStore_trade != 4 )
    {
      v18 = 23;
      goto LABEL_34;
    }
    if ( !bUseNPCLinkIntem )
    {
      v12 = CItemStore::GetStorePos(v38);
      GetSqrt(v36->m_fCurPos, v12);
      if ( v11 > 540.0 )
      {
        v18 = 21;
        goto LABEL_34;
      }
    }
  }
  else
  {
    v22 = 1;
  }
  if ( !v39 )
    goto LABEL_51;
  dwSub = v19->m_List[0].nPullingFee + 10000;
  if ( v22 == 1 )
    dwSub += 1000;
  v25 = v23 * (unsigned __int64)dwSub;
  dwSub = v25 / 0x2710;
  v13 = CPlayerDB::GetDalant(&v36->m_Param);
  if ( dwSub > v13 )
  {
    v18 = 11;
  }
  else
  {
LABEL_51:
    GetSqrt(v36->m_fCurPos, pfNewPos);
    if ( v11 <= 40.0 )
    {
      v21 = FindEmptyParkingUnit(g_ParkingUnit, 300);
      if ( !v21 )
        v18 = 19;
    }
    else
    {
      v18 = 24;
    }
  }
LABEL_34:
  dwParkingUnitSerial = -1;
  if ( !v18 )
  {
    _parkingunit_create_setdata::_parkingunit_create_setdata(&Dst);
    Dst.byFrame = v19->m_List[0].byFrame;
    memcpy_0(Dst.byPartCode, v19->m_List[0].byPart, 6ui64);
    Dst.m_pRecordSet = CRecordData::GetRecord(&stru_1799C8BA0, v19->m_List[0].byFrame);
    Dst.pOwner = v36;
    Dst.byCreateType = 0;
    Dst.m_pMap = v36->m_pCurMap;
    Dst.m_nLayerIndex = v36->m_wMapLayerIndex;
    memcpy_0(Dst.m_fStartPos, pfNewPos, 0xCui64);
    Dst.byTransDistCode = v22;
    v28 = 10000;
    v29 = CRecordData::GetRecord(&stru_1799C8BA0, v19->m_List[0].byFrame);
    if ( *(_DWORD *)&v29[1].m_strCode[0] > 0 )
      v28 = *(_DWORD *)&v29[1].m_strCode[0];
    Dst.wHPRate = 10000 * v19->m_List[0].dwGauge / v28;
    CParkingUnit::Create(v21, &Dst);
    dwParkingUnitSerial = v21->m_dwObjSerial;
    CPlayer::_LockUnitKey(v36, v37, 1);
    v30 = CRecordData::GetRecord(&stru_1799C86D0 + 5, v19->m_List[0].byPart[5]);
    if ( v30 )
      v19->m_List[0].wBooster = *(_WORD *)&v30[5].m_strCode[56];
    v36->m_pUsingUnit = (_UNIT_DB_BASE::_LIST *)v19;
    v36->m_pParkingUnit = v21;
    v36->m_dwUnitViewOverTime = -1;
    CPlayer::SubDalant(v36, dwSub);
    if ( !v36->m_byUserDgr )
    {
      v14 = CPlayerDB::GetRaceCode(&v36->m_Param);
      eAddDalant(v14, dwSub);
    }
    v31 = CPlayerDB::GetLevel(&v36->m_Param);
    if ( v31 == 30 || v31 == 40 || v31 == 50 || v31 == 60 )
    {
      nLv = CPlayerDB::GetLevel(&v36->m_Param);
      v15 = CMoneySupplyMgr::Instance();
      CMoneySupplyMgr::UpdateBuyUnitData(v15, nLv, dwSub);
    }
    if ( dwSub )
    {
      v33 = v36->m_szItemHistoryFileName;
      v34 = CPlayerDB::GetGold(&v36->m_Param);
      v16 = CPlayerDB::GetDalant(&v36->m_Param);
      CMgrAvatorItemHistory::pay_money(
        &CPlayer::s_MgrItemHistory,
        v36->m_ObjID.m_wIndex,
        "Unit_Delivery",
        dwSub,
        0,
        v16,
        v34,
        v33);
    }
  }
  CPlayer::SendMsg_UnitDeliveryResult(v36, v18, v37, dwParkingUnitSerial, dwSub);
}
