/*
 * Function: ?Room_Initialize@CGuildRoomInfo@@QEAAXXZ
 * Address: 0x1402E59F0
 */

void __fastcall CGuildRoomInfo::Room_Initialize(CGuildRoomInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CGuildRoomInfo *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_bRent = 0;
  v4->m_byRoomType = 2;
  v4->m_iGuildIdx = -1;
  v4->m_dwGuildSerial = 0;
  v4->m_timeBegin = 0;
  v4->m_timer = 0;
  std::vector<RoomCharInfo,std::allocator<RoomCharInfo>>::clear(&v4->m_vecRoomMember);
}
