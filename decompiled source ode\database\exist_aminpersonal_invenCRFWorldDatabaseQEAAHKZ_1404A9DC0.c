/*
 * Function: ?exist_aminpersonal_inven@CRFWorldDatabase@@QEAAHK@Z
 * Address: 0x1404A9DC0
 */

signed __int64 __fastcall CRFWorldDatabase::exist_aminpersonal_inven(CRFWorldDatabase *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v5; // [sp+0h] [bp-178h]@1
  void *SQLStmt; // [sp+20h] [bp-158h]@13
  __int16 v7; // [sp+30h] [bp-148h]@9
  char Dest; // [sp+50h] [bp-128h]@4
  char v9; // [sp+51h] [bp-127h]@4
  unsigned __int8 v10; // [sp+154h] [bp-24h]@16
  unsigned __int64 v11; // [sp+160h] [bp-18h]@4
  CRFWorldDatabase *v12; // [sp+180h] [bp+8h]@1

  v12 = this;
  v2 = &v5;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = (unsigned __int64)&v5 ^ _security_cookie;
  Dest = 0;
  memset(&v9, 0, 0xFFui64);
  sprintf(
    &Dest,
    "select * from [dbo].[tbl_aminepersonal_inven] as a join [dbo].[tbl_base] as b on b.serial = a.avatorserial where a.a"
    "vatorserial = %d and b.accountserial >= **********",
    dwSerial);
  if ( v12->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v12->vfptr, &Dest);
  if ( v12->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v12->vfptr) )
  {
    v7 = SQLExecDirect_0(v12->m_hStmtSelect, &Dest, -3);
    if ( v7 && v7 != 1 )
    {
      if ( v7 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v12->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v12->vfptr, v7, &Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v12->vfptr, v7, v12->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v7 = SQLFetch_0(v12->m_hStmtSelect);
      if ( v7 && v7 != 1 )
      {
        v10 = 0;
        if ( v7 == 100 )
        {
          v10 = 2;
        }
        else
        {
          SQLStmt = v12->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v12->vfptr, v7, &Dest, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v12->vfptr, v7, v12->m_hStmtSelect);
          v10 = 1;
        }
        if ( v12->m_hStmtSelect )
          SQLCloseCursor_0(v12->m_hStmtSelect);
        result = v10;
      }
      else
      {
        if ( v12->m_hStmtSelect )
          SQLCloseCursor_0(v12->m_hStmtSelect);
        if ( v12->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v12->vfptr, "%s Success", &Dest);
        result = 0i64;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v12->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1i64;
  }
  return result;
}
