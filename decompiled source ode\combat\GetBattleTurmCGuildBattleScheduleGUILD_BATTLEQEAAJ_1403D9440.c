/*
 * Function: ?GetBattleTurm@CGuildBattleSchedule@GUILD_BATTLE@@QEAAJXZ
 * Address: 0x1403D9440
 */

__int64 __fastcall GUILD_BATTLE::CGuildBattleSchedule::GetBattleTurm(GUILD_BATTLE::CGuildBattleSchedule *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  ATL::CTimeSpan v5; // [sp+28h] [bp-20h]@4
  GUILD_BATTLE::CGuildBattleSchedule *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5.m_timeSpan = v6->m_kBattleTime.m_timeSpan;
  return ATL::CTimeSpan::GetTotalMinutes(&v5);
}
