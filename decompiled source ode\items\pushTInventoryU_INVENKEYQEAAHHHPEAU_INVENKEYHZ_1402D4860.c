/*
 * Function: ?push@?$TInventory@U_INVENKEY@@@@QEAAHHHPEAU_INVENKEY@@H@Z
 * Address: 0x1402D4860
 */

int __fastcall TInventory<_INVENKEY>::push(TInventory<_INVENKEY> *this, int nP, int nS, _INVENKEY *pItem, int nNum)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@5
  __int64 v8; // [sp+0h] [bp-28h]@1
  TInventory<_INVENKEY> *v9; // [sp+30h] [bp+8h]@1

  v9 = this;
  v5 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( nP < v9->m_nMaxPageNum )
    result = TInvenPage<_INVENKEY>::push(&v9->m_pPage[nP], pItem, nS, nNum);
  else
    result = nNum;
  return result;
}
