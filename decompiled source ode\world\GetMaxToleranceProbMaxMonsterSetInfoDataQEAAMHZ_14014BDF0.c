/*
 * Function: ?GetMaxToleranceProbMax@MonsterSetInfoData@@QEAAMH@Z
 * Address: 0x14014BDF0
 */

float __fastcall MonsterSetInfoData::GetMaxToleranceProbMax(MonsterSetInfoData *this, int nMonGrade)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float result; // xmm0_4@6
  __int64 v5; // [sp+0h] [bp-18h]@1
  MonsterSetInfoData *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( nMonGrade < 0 || nMonGrade >= 7 )
    result = FLOAT_1_0;
  else
    result = v6->m_fToleranceProbMax[nMonGrade];
  return result;
}
