/*
 * Function: j_??$unchecked_uninitialized_copy@PEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@stdext@@YAPEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@00AEAV?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@Z
 * Address: 0x140003AE9
 */

CUnmannedTraderClassInfo **__fastcall stdext::unchecked_uninitialized_copy<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *,std::allocator<CUnmannedTraderClassInfo *>>(CUnmannedTraderClassInfo **_First, CUnmannedTraderClassInfo **_Last, CUnmannedTraderClassInfo **_Dest, std::allocator<CUnmannedTraderClassInfo *> *_Al)
{
  return stdext::unchecked_uninitialized_copy<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *,std::allocator<CUnmannedTraderClassInfo *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
