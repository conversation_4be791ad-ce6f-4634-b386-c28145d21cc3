/*
 * Function: j_??1?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@QEAA@XZ
 * Address: 0x14000BA37
 */

void __fastcall std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this)
{
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(this);
}
