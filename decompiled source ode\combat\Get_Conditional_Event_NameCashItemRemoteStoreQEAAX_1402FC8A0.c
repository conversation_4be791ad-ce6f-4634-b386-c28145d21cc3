/*
 * Function: ?Get_Conditional_Event_Name@CashItemRemoteStore@@QEAAXEPEAD@Z
 * Address: 0x1402FC8A0
 */

void __fastcall CashItemRemoteStore::Get_Conditional_Event_Name(CashItemRemoteStore *this, char byEventType, char *szEventName)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4

  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = byEventType;
  if ( byEventType )
  {
    if ( v6 == 1 )
    {
      wsprintfA(szEventName, "%s", "Set");
    }
    else if ( v6 == 2 )
    {
      wsprintfA(szEventName, "%s", "One&One");
    }
    else
    {
      wsprintfA(szEventName, "%s", "NULL");
    }
  }
  else
  {
    wsprintfA(szEventName, "%s", "Discount");
  }
}
