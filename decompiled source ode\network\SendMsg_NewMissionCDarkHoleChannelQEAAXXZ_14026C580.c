/*
 * Function: ?SendMsg_NewMission@CDarkHoleChannel@@QEAAXXZ
 * Address: 0x14026C580
 */

void __fastcall CDarkHoleChannel::SendMsg_NewMission(CDarkHoleChannel *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@19
  __int64 v4; // [sp+0h] [bp-CB8h]@1
  _darkhole_new_mission_inform_zocl v5; // [sp+40h] [bp-C78h]@4
  int j; // [sp+C64h] [bp-54h]@7
  _dh_job_setup *v7; // [sp+C68h] [bp-50h]@9
  _dh_mission_mgr::_if_change *v8; // [sp+C70h] [bp-48h]@12
  char pbyType; // [sp+C84h] [bp-34h]@16
  char v10; // [sp+C85h] [bp-33h]@16
  _dh_player_mgr *v11; // [sp+C98h] [bp-20h]@18
  unsigned __int64 v12; // [sp+CA8h] [bp-10h]@4
  CDarkHoleChannel *v13; // [sp+CC0h] [bp+8h]@1

  v13 = this;
  v1 = &v4;
  for ( i = 812i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = (unsigned __int64)&v4 ^ _security_cookie;
  _darkhole_new_mission_inform_zocl::_darkhole_new_mission_inform_zocl(&v5);
  strcpy_0(v5.szDescirptCode, v13->m_MissionMgr.pCurMssionPtr->szDescirptCode);
  if ( _dh_mission_mgr::GetLimMSecTime(&v13->m_MissionMgr) == -1 )
    v5.dwLimTimeSec = -1;
  else
    v5.dwLimTimeSec = _dh_mission_mgr::GetLimMSecTime(&v13->m_MissionMgr) / 0x3E8;
  v5.byOrder = v13->m_MissionMgr.pCurMssionPtr->byJobOrder;
  v5.byJobNum = v13->m_MissionMgr.pCurMssionPtr->nEmbJobSetupNum;
  for ( j = 0; j < v13->m_MissionMgr.pCurMssionPtr->nEmbJobSetupNum; ++j )
  {
    v7 = v13->m_MissionMgr.pCurMssionPtr->EmbJobSetup[j];
    v5.Job[j].byType = v7->eventType;
    v5.Job[j].byTableCode = v7->JobSetup.byTable;
    if ( v7->JobSetup.pEventFld )
      v5.Job[j].wRecordIndex = v7->JobSetup.pEventFld->m_dwIndex;
    else
      v5.Job[j].wRecordIndex = -1;
    v5.Job[j].zNeedCount = v7->JobSetup.nEventCount;
    strcpy_0(v5.Job[j].szDescirptCode, v7->szDescirptCode);
    v5.Job[j].bDisable = 1;
    v8 = _dh_mission_mgr::SearchCurMissionCont(&v13->m_MissionMgr);
    if ( v8 )
    {
      if ( v8->pszDespt )
        strcpy_0(v5.szDescirptCode, v8->pszDespt);
    }
  }
  pbyType = 35;
  v10 = 13;
  for ( j = 0; j < 32; ++j )
  {
    v11 = &v13->m_Quester[j];
    if ( _dh_player_mgr::IsFill(v11) )
    {
      v3 = _darkhole_new_mission_inform_zocl::size(&v5);
      CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_Quester[j].pOne->m_ObjID.m_wIndex, &pbyType, v5.szDescirptCode, v3);
    }
  }
}
