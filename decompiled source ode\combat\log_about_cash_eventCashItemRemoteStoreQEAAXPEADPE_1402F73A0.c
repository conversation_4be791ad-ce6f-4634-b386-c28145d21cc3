/*
 * Function: ?log_about_cash_event@CashItemRemoteStore@@QEAAXPEADPEAU_cash_discount_ini_@@@Z
 * Address: 0x1402F73A0
 */

void __fastcall CashItemRemoteStore::log_about_cash_event(CashItemRemoteStore *this, char *szLoadInfo, _cash_discount_ini_ *pIni)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // al@4
  __int64 v6; // [sp+0h] [bp-48h]@1
  int v7; // [sp+20h] [bp-28h]@5
  int v8; // [sp+28h] [bp-20h]@5
  int v9; // [sp+30h] [bp-18h]@5
  CashItemRemoteStore *v10; // [sp+50h] [bp+8h]@1
  _cash_discount_ini_ *v11; // [sp+60h] [bp+18h]@1

  v11 = pIni;
  v10 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CLogFile::Write(&v10->m_cde.m_cde_log, "\t##Begin <%s>", szLoadInfo);
  v5 = CashItemRemoteStore::get_cde_status(v10);
  CLogFile::Write(&v10->m_cde.m_cde_log, "\t\tStatus : %d", (unsigned __int8)v5);
  CLogFile::Write(&v10->m_cde.m_cde_log, "\t\tRepeat : %d", v11->m_bRepeat);
  CLogFile::Write(&v10->m_cde.m_cde_log, "\t\tRepeatDay : %d", v11->m_byRepeatDay);
  if ( v10->m_cde.m_ini.m_bUseCashDiscount )
  {
    v9 = v11->m_byMinute[0];
    v8 = v11->m_byHour[0];
    v7 = v11->m_byDay[0];
    CLogFile::Write(&v10->m_cde.m_cde_log, "\t\tStart : %04d-%02d-%02d %02d:%02d", v11->m_wYear[0], v11->m_byMonth[0]);
    v9 = v11->m_byMinute[1];
    v8 = v11->m_byHour[1];
    v7 = v11->m_byDay[1];
    CLogFile::Write(&v10->m_cde.m_cde_log, "\t\tEnd : %04d-%02d-%02d %02d:%02d", v11->m_wYear[1], v11->m_byMonth[1]);
    v9 = v11->m_byMinute[2];
    v8 = v11->m_byHour[2];
    v7 = v11->m_byDay[2];
    CLogFile::Write(&v10->m_cde.m_cde_log, "\t\tExpire : %04d-%02d-%02d %02d:%02d", v11->m_wYear[2], v11->m_byMonth[2]);
  }
  CLogFile::Write(&v10->m_cde.m_cde_log, "\t##End <Information about loaded Cash Discount-Rate Event>");
}
