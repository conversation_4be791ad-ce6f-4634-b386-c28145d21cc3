/*
 * Function: ?AlterPvPCashBag@CPlayer@@QEAAXNW4PVP_MONEY_ALTER_TYPE@@@Z
 * Address: 0x14005F990
 */

void __usercall CPlayer::AlterPvPCashBag(CPlayer *this@<rcx>, long double dAlter@<xmm1>, PVP_MONEY_ALTER_TYPE IOCode@<r8d>, double a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  double v6; // xmm0_8@10
  __int64 v7; // [sp-20h] [bp-38h]@1
  double v8; // [sp+0h] [bp-18h]@4
  long double v9; // [sp+8h] [bp-10h]@4
  CPlayer *v10; // [sp+20h] [bp+8h]@1
  PVP_MONEY_ALTER_TYPE nIOCode; // [sp+30h] [bp+18h]@1

  nIOCode = IOCode;
  v10 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  CPvpOrderView::GetPvpCash(&v10->m_kPvpOrderView);
  v8 = a4;
  v9 = a4 + dAlter;
  if ( a4 + dAlter > 999999.0 )
    v9 = DOUBLE_999999_0;
  if ( v9 < -999999.0 )
    v9 = DOUBLE_N999999_0;
  if ( ((int (__fastcall *)(CPlayer *))v10->vfptr->GetLevel)(v10) > 39 && v10->m_Param.m_pClassData->m_nGrade >= 1 )
  {
    CPvpOrderView::SetPvpCash(&v10->m_kPvpOrderView, v9);
    v6 = v9;
    if ( v9 > v8 )
    {
      v6 = dAlter;
      if ( dAlter > 0.0 )
        CPlayer::IncCriEffPvPCashBag(v10, dAlter);
    }
    CPvpOrderView::GetPvpCash(&v10->m_kPvpOrderView);
    if ( v8 != v6 )
    {
      CPvpOrderView::UpdatePvpCash(&v10->m_kPvpOrderView, v9);
      CPlayer::SendMsg_AlterPvPCash(v10, nIOCode);
    }
  }
}
