/*
 * Function: ?SendMsg_KickForSail@CTransportShip@@QEAAXH@Z
 * Address: 0x1402653D0
 */

void __fastcall CTransportShip::SendMsg_KickForSail(CTransportShip *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v7; // [sp+55h] [bp-23h]@4

  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  pbyType = 33;
  v7 = 2;
  CNetProcess::LoadSendMsg(unk_1414F2088, n, &pbyType, &szMsg, 1u);
}
