/*
 * Function: j_?AgreeWithEphemeralPrivateKey@?$DL_KeyAgreementAlgorithm_DH@UECPPoint@CryptoPP@@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@2@@CryptoPP@@UEBA?AUECPPoint@2@AEBV?$DL_GroupParameters@UECPPoint@CryptoPP@@@2@AEBV?$DL_FixedBasePrecomputation@UECPPoint@CryptoPP@@@2@AEBVInteger@2@@Z
 * Address: 0x14000A6E1
 */

CryptoPP::ECPPoint *__fastcall CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::ECPPoint,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>>::AgreeWithEphemeralPrivateKey(CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::ECPPoint,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0> > *this, CryptoPP::ECPPoint *result, CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *params, CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint> *publicPrecomputation, CryptoPP::Integer *privateExponent)
{
  return CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::ECPPoint,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>>::AgreeWithEphemeralPrivateKey(
           this,
           result,
           params,
           publicPrecomputation,
           privateExponent);
}
