/*
 * Function: ?pc_MakeItem@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@GE0@Z
 * Address: 0x1400AE750
 */

void __usercall CPlayer::pc_MakeItem(CPlayer *this@<rcx>, _STORAGE_POS_INDIV *pipMakeTool@<rdx>, unsigned __int16 wManualIndex@<r8w>, char byMaterial<PERSON>um@<r9b>, signed __int64 a5@<rax>, _STORAGE_POS_INDIV *pipMaterials)
{
  void *v6; // rsp@1
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  int v9; // eax@8
  int v10; // eax@17
  _class_fld *v11; // rax@43
  float v12; // xmm0_4@100
  unsigned __int16 v13; // ax@110
  int v14; // eax@115
  __int64 v15; // rcx@115
  __int64 v16; // [sp-20h] [bp-1928h]@1
  bool bUpdate[8]; // [sp+0h] [bp-1908h]@115
  bool bSend[8]; // [sp+8h] [bp-1900h]@115
  char v19; // [sp+30h] [bp-18D8h]@6
  _STORAGE_LIST::_db_con *v20; // [sp+38h] [bp-18D0h]@6
  void *Src; // [sp+50h] [bp-18B8h]@70
  int j; // [sp+374h] [bp-1594h]@22
  _base_fld *v23; // [sp+378h] [bp-1590h]@6
  char Dst[8]; // [sp+388h] [bp-1580h]@67
  int v25[17]; // [sp+390h] [bp-1578h]@84
  int nTableCode; // [sp+3D4h] [bp-1534h]@6
  _base_fld *v27; // [sp+3D8h] [bp-1530h]@6
  char v28; // [sp+3E0h] [bp-1528h]@14
  char v29; // [sp+3E1h] [bp-1527h]@6
  char v30; // [sp+3E2h] [bp-1526h]@22
  __int64 v31; // [sp+3E8h] [bp-1520h]@24
  int v32; // [sp+3F0h] [bp-1518h]@58
  unsigned int v33; // [sp+3F4h] [bp-1514h]@58
  int k; // [sp+3F8h] [bp-1510h]@58
  char *Str1; // [sp+400h] [bp-1508h]@60
  unsigned __int8 v36; // [sp+408h] [bp-1500h]@62
  int l; // [sp+40Ch] [bp-14FCh]@76
  char v38; // [sp+410h] [bp-14F8h]@81
  _base_fld *v39; // [sp+418h] [bp-14F0h]@81
  int m; // [sp+420h] [bp-14E8h]@81
  char __t[5040]; // [sp+440h] [bp-14C8h]@95
  char pbyMtrNum[116]; // [sp+17F0h] [bp-118h]@98
  int v43; // [sp+1864h] [bp-A4h]@99
  int v44; // [sp+1868h] [bp-A0h]@99
  int v45; // [sp+186Ch] [bp-9Ch]@100
  int v46; // [sp+1870h] [bp-98h]@100
  _STORAGE_LIST::_db_con pItem; // [sp+1888h] [bp-80h]@116
  char v48; // [sp+18C4h] [bp-44h]@117
  char v49; // [sp+18C5h] [bp-43h]@117
  char v50; // [sp+18C6h] [bp-42h]@120
  _base_fld *v51; // [sp+18C8h] [bp-40h]@127
  int v52; // [sp+18D8h] [bp-30h]@10
  char v53; // [sp+18DCh] [bp-2Ch]@25
  _class_fld *v54; // [sp+18E0h] [bp-28h]@43
  int v55; // [sp+18E8h] [bp-20h]@103
  int v56; // [sp+18ECh] [bp-1Ch]@106
  int v57; // [sp+18F0h] [bp-18h]@118
  unsigned __int64 v58; // [sp+18F8h] [bp-10h]@4
  CPlayer *v59; // [sp+1910h] [bp+8h]@1
  _STORAGE_POS_INDIV *v60; // [sp+1918h] [bp+10h]@1
  unsigned __int16 v61; // [sp+1920h] [bp+18h]@1
  char v62; // [sp+1928h] [bp+20h]@1

  v62 = byMaterialNum;
  v61 = wManualIndex;
  v60 = pipMakeTool;
  v59 = this;
  v6 = alloca(a5);
  v7 = &v16;
  for ( i = 1608i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v58 = (unsigned __int64)&v16 ^ _security_cookie;
  if ( v59->m_bCheat_makeitem_no_use_matrial )
    v62 = 0;
  v19 = 0;
  v20 = 0i64;
  v23 = CRecordData::GetRecord(&stru_1799C67C8, wManualIndex);
  nTableCode = GetItemTableCode(v23->m_strCode);
  v27 = 0i64;
  v29 = 0;
  if ( !v23 )
  {
    v19 = 12;
    goto $RESULT_37;
  }
  v9 = CPlayerDB::GetRaceSexCode(&v59->m_Param);
  if ( v23[1].m_strCode[v9] != 49 )
  {
    v19 = 11;
    goto $RESULT_37;
  }
  v52 = nTableCode;
  if ( nTableCode < 5 )
    goto LABEL_134;
  if ( v52 <= 6 )
  {
    v28 = 0;
    goto LABEL_17;
  }
  if ( v52 == 10 )
    v28 = 2;
  else
LABEL_134:
    v28 = 1;
LABEL_17:
  v10 = _MASTERY_PARAM::GetMasteryPerMast(&v59->m_pmMst, 5, v28);
  if ( (signed int)v23[1].m_dwIndex > v10 )
  {
    v19 = 13;
    goto $RESULT_37;
  }
  if ( GetItemKindCode(nTableCode) )
  {
    v19 = 9;
    goto $RESULT_37;
  }
  if ( !v59->m_bFreeSFByClass )
  {
    v30 = 0;
    for ( j = 0; j < 4; ++j )
    {
      v31 = (__int64)*v59->m_Param.m_ppHistoryEffect[j];
      if ( !v31 )
        break;
      v53 = v28;
      if ( v28 )
      {
        if ( v53 == 1 )
        {
          if ( *(_DWORD *)(v31 + 1452) )
            v30 = 1;
        }
        else if ( v53 == 2 && *(_DWORD *)(v31 + 1456) )
        {
          v30 = 1;
        }
      }
      else if ( *(_DWORD *)(v31 + 1448) )
      {
        v30 = 1;
      }
      if ( v30 )
        break;
    }
    if ( !v30 )
    {
      v19 = 7;
      goto $RESULT_37;
    }
  }
  if ( CPlayerDB::GetPtrCurClass(&v59->m_Param)->m_nGrade > 1 && CPlayerDB::GetPtrCurClass(&v59->m_Param)->m_nClass == 3 )
  {
    v54 = CPlayerDB::GetPtrCurClass(&v59->m_Param);
    v11 = CPlayerDB::GetPtrBaseClass(&v59->m_Param);
    if ( v54->m_nClass == v11->m_nClass && v59->m_Param.m_pClassData->m_dwIndex != 49 )
      v29 = 1;
  }
  v20 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v59->m_Param.m_dbInven.m_nListNum, v60->wItemSerial);
  if ( !v20 )
  {
    v19 = 1;
    goto $RESULT_37;
  }
  if ( v20->m_byTableCode != 11 )
  {
    v19 = 2;
    goto $RESULT_37;
  }
  if ( v20->m_bLock )
  {
    v19 = 10;
    goto $RESULT_37;
  }
  if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&v59->m_Param.m_dbInven.m_nListNum) == 255 )
  {
    v19 = 3;
    goto $RESULT_37;
  }
  if ( v29 )
  {
    v32 = rand() % 10000;
    v33 = 0;
    for ( k = 0; k < 30; ++k )
    {
      Str1 = &v23[2].m_strCode[12 * k + 56];
      if ( !strncmp(Str1, "-1", 2ui64) )
        break;
      v33 += *((_DWORD *)Str1 + 2);
      if ( v32 < v33 )
      {
        v36 = GetItemTableCode(Str1);
        if ( v36 != 255 )
          v27 = CRecordData::GetRecordByHash((CRecordData *)&unk_1799C6AA0 + v36, Str1, 2, 5);
        break;
      }
    }
  }
  else
  {
    v27 = CRecordData::GetRecordByHash((CRecordData *)&unk_1799C6AA0 + nTableCode, v23->m_strCode, 2, 5);
    if ( !v27 )
    {
      v19 = 9;
      goto $RESULT_37;
    }
  }
  if ( v27 )
  {
    memcpy_0(Dst, &v23[2], 0x3Cui64);
    if ( !v59->m_bCheat_makeitem_no_use_matrial )
    {
      for ( j = 0; j < (unsigned __int8)v62; ++j )
      {
        *(&Src + j) = _STORAGE_LIST::GetPtrFromSerial(
                        (_STORAGE_LIST *)&v59->m_Param.m_dbInven.m_nListNum,
                        pipMaterials[j].wItemSerial);
        if ( !*(&Src + j) )
        {
          CPlayer::SendMsg_AdjustAmountInform(v59, 0, pipMaterials[j].wItemSerial, 0);
          v19 = 4;
          goto $RESULT_37;
        }
        if ( *((_BYTE *)*(&Src + j) + 19) )
        {
          v19 = 10;
          goto $RESULT_37;
        }
        if ( (unsigned __int64)pipMaterials[j].byNum > *(_QWORD *)((char *)*(&Src + j) + 5) )
        {
          CPlayer::SendMsg_AdjustAmountInform(v59, 0, pipMaterials[j].wItemSerial, *(_DWORD *)((char *)*(&Src + j) + 5));
          v19 = 5;
          goto $RESULT_37;
        }
        for ( l = 0; l < j; ++l )
        {
          if ( pipMaterials[l].wItemSerial == pipMaterials[j].wItemSerial )
          {
            v19 = 5;
            goto $RESULT_37;
          }
        }
        v38 = 0;
        v39 = CRecordData::GetRecord(
                (CRecordData *)&unk_1799C6AA0 + *((_BYTE *)*(&Src + j) + 1),
                *(_WORD *)((char *)*(&Src + j) + 3));
        for ( m = 0; m < 5; ++m )
        {
          if ( !strncmp(v39->m_strCode, &Dst[12 * m], 7ui64) )
          {
            v25[3 * m] -= pipMaterials[j].byNum;
            v38 = 1;
            break;
          }
        }
        if ( !v38 )
        {
          v19 = 8;
          goto $RESULT_37;
        }
      }
      for ( j = 0; j < 5; ++j )
      {
        if ( v25[3 * j] > 0 )
        {
          v19 = 6;
          break;
        }
      }
    }
  }
  else
  {
    v19 = 12;
  }
$RESULT_37:
  if ( !v19 )
  {
    `vector constructor iterator'(__t, 0x32ui64, 100, (void *(__cdecl *)(void *))_STORAGE_LIST::_db_con::_db_con);
    if ( !v59->m_bCheat_makeitem_no_use_matrial )
    {
      for ( j = 0; j < (unsigned __int8)v62; ++j )
      {
        memcpy_0(&__t[50 * j], *(&Src + j), 0x32ui64);
        pbyMtrNum[j] = pipMaterials[j].byNum;
      }
    }
    v43 = _MASTERY_PARAM::GetMasteryPerMast(&v59->m_pmMst, 5, v28);
    v44 = 0;
    if ( nTableCode == 10 )
    {
      v44 = (signed int)ffloor((float)((float)v43 * 0.5) + 40.0);
    }
    else
    {
      v12 = (float)(150 * v43 + 1300);
      sqrt(v12);
      v45 = (signed int)ffloor((float)((float)(v12 / 2.0) + 0.5) - 18.0);
      v46 = GetItemEquipLevel(nTableCode, v27->m_dwIndex);
      v44 = (signed int)ffloor((float)(50.0 - (float)((float)(v46 - v45) * 11.25)) / 2.0);
    }
    if ( v44 >= 95 )
      v55 = 95;
    else
      v55 = v44;
    v44 = v55;
    if ( v55 <= 0 )
      v56 = 0;
    else
      v56 = v44;
    v44 = v56;
    if ( v59->m_bCheat_100SuccMake )
      v44 = 100;
    v13 = _100_per_random_table::GetRand(&v59->m_MakeRandTable);
    if ( v44 < v13 )
      v19 = 100;
    if ( !v59->m_bCheat_makeitem_no_use_matrial )
    {
      for ( j = 0; j < (unsigned __int8)v62; ++j )
      {
        v14 = -pipMaterials[j].byNum;
        v15 = (__int64)*(&Src + j);
        bSend[0] = 0;
        bUpdate[0] = 0;
        CPlayer::Emb_AlterDurPoint(v59, 0, *(_BYTE *)(v15 + 49), v14, 0, 0);
      }
    }
    _STORAGE_LIST::_db_con::_db_con(&pItem);
    if ( !v19 )
    {
      pItem.m_byTableCode = nTableCode;
      pItem.m_wItemIndex = v27->m_dwIndex;
      pItem.m_dwDur = GetItemDurPoint(nTableCode, v27->m_dwIndex);
      v48 = GetItemKindCode(nTableCode);
      v49 = GetDefItemUpgSocketNum(nTableCode, v27->m_dwIndex);
      if ( (signed int)(unsigned __int8)v49 <= 0 )
        v57 = 0;
      else
        v57 = rand() % (unsigned __int8)v49 + 1;
      v50 = v57;
      pItem.m_dwLv = GetBitAfterSetLimSocket(v57);
      pItem.m_wSerial = CPlayerDB::GetNewItemSerial(&v59->m_Param);
      if ( !CPlayer::Emb_AddStorage(v59, 0, (_STORAGE_LIST::_storage_con *)&pItem.m_bLoad, 0, 1) )
      {
        CPlayer::SendMsg_MakeItemResult(v59, -1);
        return;
      }
      CPlayer::SendMsg_FanfareItem(v59, 6, &pItem, 0i64);
      CPlayer::SendMsg_InsertItemInform(v59, 0, &pItem);
      if ( nTableCode == 10 )
      {
        CPlayer::Emb_AlterStat(v59, 5, v28, 1u, 0, "CPlayer::pc_MakeItem()---1", 1);
      }
      else if ( v45 - v46 <= 4 )
      {
        CPlayer::Emb_AlterStat(v59, 5, v28, 1u, 0, "CPlayer::pc_MakeItem()---0", 1);
      }
      v51 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + nTableCode, v27->m_dwIndex);
      CPlayer::Emb_CheckActForQuest(v59, 5, v51->m_strCode, 1u, 0);
    }
    if ( v59->m_bCheat_makeitem_no_use_matrial )
      CMgrAvatorItemHistory::cheat_make_item_no_material(
        &CPlayer::s_MgrItemHistory,
        v59->m_ObjID.m_wIndex,
        v19,
        &pItem,
        v59->m_szItemHistoryFileName);
    else
      CMgrAvatorItemHistory::make_item(
        &CPlayer::s_MgrItemHistory,
        v59->m_ObjID.m_wIndex,
        (_STORAGE_LIST::_db_con *)__t,
        pbyMtrNum,
        v62,
        v19,
        1,
        &pItem,
        v59->m_szItemHistoryFileName);
  }
  CPlayer::SendMsg_MakeItemResult(v59, v19);
}
