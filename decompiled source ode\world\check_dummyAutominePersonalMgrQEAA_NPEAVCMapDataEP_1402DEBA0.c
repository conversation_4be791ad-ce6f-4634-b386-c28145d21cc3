/*
 * Function: ?check_dummy@AutominePersonalMgr@@QEAA_NPEAVCMapData@@EPEAM@Z
 * Address: 0x1402DEBA0
 */

bool __fastcall AutominePersonalMgr::check_dummy(AutominePersonalMgr *this, CMapData *pMap, char byCurDummyIndex, float *pfCurPos)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  _res_dummy *v8; // [sp+20h] [bp-18h]@6
  int v9; // [sp+28h] [bp-10h]@6

  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( (unsigned __int8)byCurDummyIndex < pMap->m_nResDumNum )
  {
    v8 = &pMap->m_pResDummy[(unsigned __int8)byCurDummyIndex];
    v9 = CMapData::GetResDummySector(pMap, (unsigned __int8)byCurDummyIndex, pfCurPos);
    result = v9 != -1;
  }
  else
  {
    result = 0;
  }
  return result;
}
