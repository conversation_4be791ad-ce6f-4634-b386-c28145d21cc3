/*
 * Function: ?db_Load_Avator@CMainThread@@QEAAEKKPEAU_AVATOR_DATA@@PEA_NPEAK21PEAEPEAN4113_N2@Z
 * Address: 0x1401A34D0
 */

char __fastcall CMainThread::db_Load_Avator(CMainThread *this, unsigned int dwSerial, unsigned int dwAccountSerial, _AVATOR_DATA *pData, bool *pbAddItem, unsigned int *pdwAddDalant, unsigned int *pdwAddGold, bool *pbTrunkAddItem, char *pbyTrunkOldSlot, long double *pdTrunkOldDalant, long double *pdTrunkOldGold, bool *pbCreateTrunkFree, bool *pbExtTrunkAddItem, char *pbyExtTrunkOldSlot, bool bAll, unsigned int *pdwCheckSum)
{
  __int64 *v16; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@10
  AutominePersonalMgr *v19; // rax@70
  signed __int64 v20; // rax@147
  double v21; // xmm0_8@147
  __int64 v22; // [sp+0h] [bp-268h]@1
  _TRUNK_DB_BASE *pTrunk; // [sp+20h] [bp-248h]@62
  unsigned int *pDwItemCode_U; // [sp+28h] [bp-240h]@87
  unsigned int *pDwItemChargeIndex; // [sp+30h] [bp-238h]@87
  int *piTime; // [sp+38h] [bp-230h]@87
  unsigned int *pdwDBID; // [sp+40h] [bp-228h]@131
  int *v28; // [sp+48h] [bp-220h]@131
  int j; // [sp+50h] [bp-218h]@4
  char v30; // [sp+54h] [bp-214h]@4
  char szToday; // [sp+68h] [bp-200h]@62
  char v32; // [sp+69h] [bp-1FFh]@62
  unsigned int pdwRank; // [sp+94h] [bp-1D4h]@62
  unsigned __int16 pwRankRate; // [sp+B4h] [bp-1B4h]@62
  char v35; // [sp+C4h] [bp-1A4h]@62
  char v36; // [sp+C5h] [bp-1A3h]@62
  char v37; // [sp+C6h] [bp-1A2h]@81
  unsigned __int16 v38; // [sp+C8h] [bp-1A0h]@81
  char v39; // [sp+CCh] [bp-19Ch]@86
  unsigned int pDwItemCode_K; // [sp+D4h] [bp-194h]@87
  unsigned int pdwItemCode_U; // [sp+F4h] [bp-174h]@87
  unsigned int dwItemChargeIndex; // [sp+114h] [bp-154h]@87
  unsigned __int64 pDwItemCode_D; // [sp+138h] [bp-130h]@87
  int v44; // [sp+154h] [bp-114h]@86
  char pbyType; // [sp+174h] [bp-F4h]@86
  _TimeItem_fld *v46; // [sp+188h] [bp-E0h]@93
  __time32_t Time; // [sp+194h] [bp-D4h]@97
  long double pdMoney; // [sp+1B8h] [bp-B0h]@129
  char pbyRace; // [sp+1D4h] [bp-94h]@129
  double v50; // [sp+1E8h] [bp-80h]@149
  unsigned int v51; // [sp+1F0h] [bp-78h]@151
  double v52; // [sp+1F8h] [bp-70h]@151
  char v53; // [sp+200h] [bp-68h]@161
  unsigned __int8 k; // [sp+201h] [bp-67h]@176
  _TimeItem_fld *v55; // [sp+208h] [bp-60h]@181
  __time32_t v56; // [sp+214h] [bp-54h]@185
  _QUEST_DB_BASE *v57; // [sp+230h] [bp-38h]@30
  _TRUNK_DB_BASE *v58; // [sp+238h] [bp-30h]@35
  _TRADE_DB_BASE *pTrade; // [sp+240h] [bp-28h]@37
  _PERSONALAMINE_INVEN_DB_BASE *v60; // [sp+248h] [bp-20h]@70
  unsigned __int64 v61; // [sp+250h] [bp-18h]@4
  CMainThread *v62; // [sp+270h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+278h] [bp+10h]@1
  unsigned int dwAccSerial; // [sp+280h] [bp+18h]@1
  _AVATOR_DATA *pCon; // [sp+288h] [bp+20h]@1

  pCon = pData;
  dwAccSerial = dwAccountSerial;
  dwSeriala = dwSerial;
  v62 = this;
  v16 = &v22;
  for ( i = 152i64; i; --i )
  {
    *(_DWORD *)v16 = -*********;
    v16 = (__int64 *)((char *)v16 + 4);
  }
  v61 = (unsigned __int64)&v22 ^ _security_cookie;
  j = 0;
  v30 = 0;
  if ( pdwAddDalant )
    *pdwAddDalant = 0;
  if ( pdwAddGold )
    *pdwAddGold = 0;
  if ( bAll )
  {
    v30 = CMainThread::_db_Load_Base(v62, dwSerial, pData);
    if ( v30 )
    {
      CLogFile::Write(&v62->m_logSystemError, "_db_Load_Base(%d) => failed ..{%d}", dwSeriala, (unsigned __int8)v30);
      return v30;
    }
  }
  else
  {
    CRFWorldDatabase::Select_CheckSumValue(v62->m_pWorldDB, dwSerial, pdwCheckSum);
  }
  pCon->dbAvator.m_dwAccountSerial = dwAccSerial;
  v30 = CMainThread::_db_Load_General(v62, dwSeriala, pCon->dbAvator.m_byRaceSexCode >> 1, pCon);
  if ( v30 )
  {
    CLogFile::Write(&v62->m_logSystemError, "_db_Load_General(%d) => failed ..{%d}", dwSeriala, (unsigned __int8)v30);
    result = v30;
  }
  else
  {
    v30 = CMainThread::_db_Load_Supplement(v62, dwSeriala, &pCon->dbSupplement);
    if ( v30 )
    {
      CLogFile::Write(
        &v62->m_logSystemError,
        "_db_Load_Supplement(%d) => failed ..{%d}",
        dwSeriala,
        (unsigned __int8)v30);
      result = v30;
    }
    else
    {
      v30 = CMainThread::_db_Load_TimeLimitInfo(v62, dwAccSerial, &pCon->dbTimeLimitInfo);
      if ( v30 )
      {
        CLogFile::Write(
          &v62->m_logSystemError,
          "_db_Load_TimeLimitInfo(%d) => failed ..{%d}",
          dwAccSerial,
          (unsigned __int8)v30);
        result = v30;
      }
      else
      {
        v30 = CMainThread::_db_Load_Inven(v62, dwSeriala, pCon->dbAvator.m_byBagNum, &pCon->dbInven);
        if ( v30 )
        {
          CLogFile::Write(
            &v62->m_logSystemError,
            "_db_Load_Inven(%d) => failed ..{%d}",
            dwSeriala,
            (unsigned __int8)v30);
          result = v30;
        }
        else if ( pCon->dbAvator.m_byRaceSexCode >> 1
               || (v30 = CMainThread::_db_Load_Unit(v62, dwSeriala, &pCon->dbUnit)) == 0 )
        {
          v30 = CMainThread::_db_Load_UI(v62, dwSeriala, &pCon->dbLink, &pCon->dbSfcont);
          if ( v30 )
          {
            CLogFile::Write(&v62->m_logSystemError, "_db_Load_UI(%d) => failed ..{%d}", dwSeriala, (unsigned __int8)v30);
            result = v30;
          }
          else
          {
            v30 = CMainThread::_db_Load_Quest(v62, dwSeriala, &pCon->dbQuest);
            if ( v30 )
            {
              CLogFile::Write(
                &v62->m_logSystemError,
                "_db_Load_Quest(%d) => failed ..{%d}",
                dwSeriala,
                (unsigned __int8)v30);
              result = v30;
            }
            else
            {
              v30 = CMainThread::_db_Load_NpcQuest_History(v62, dwSeriala, &pCon->dbQuest);
              if ( v30 )
              {
                CLogFile::Write(
                  &v62->m_logSystemError,
                  "_db_Load_NpcQuest_History(%d) => failed ..{%d}",
                  dwSeriala,
                  (unsigned __int8)v30);
                result = v30;
              }
              else
              {
                v57 = &pCon->dbQuest;
                v30 = CMainThread::_db_Load_Start_NpcQuest_History(
                        v62,
                        dwSeriala,
                        pCon->dbAvator.m_byRaceSexCode >> 1,
                        &pCon->dbQuest);
                if ( v30 )
                {
                  CLogFile::Write(
                    &v62->m_logSystemError,
                    "_db_Load_Start_NpcQuest_History(%d) => failed ..{%d}",
                    dwSeriala,
                    (unsigned __int8)v30);
                  result = v30;
                }
                else
                {
                  v30 = CMainThread::_db_Load_Buddy(v62, dwSeriala, &pCon->dbBuddy);
                  if ( v30 )
                  {
                    CLogFile::Write(
                      &v62->m_logSystemError,
                      "_db_Load_Buddy(%d) => failed ..{%d}",
                      dwSeriala,
                      (unsigned __int8)v30);
                    result = v30;
                  }
                  else if ( dwAccSerial
                         && (v58 = &pCon->dbTrunk,
                             (v30 = CMainThread::_db_Load_Trunk(
                                      v62,
                                      dwSeriala,
                                      dwAccSerial,
                                      pCon->dbAvator.m_byRaceSexCode >> 1,
                                      &pCon->dbTrunk)) != 0) )
                  {
                    CLogFile::Write(
                      &v62->m_logSystemError,
                      "_db_Load_Trunk(%d) => failed ..{%d}",
                      dwSeriala,
                      (unsigned __int8)v30);
                    result = v30;
                  }
                  else
                  {
                    pTrade = &pCon->dbTrade;
                    v30 = CMainThread::_db_Load_Trade(
                            v62,
                            pCon->dbAvator.m_byRaceSexCode >> 1,
                            dwSeriala,
                            &pCon->dbTrade);
                    if ( v30 )
                    {
                      CLogFile::Write(
                        &v62->m_logSystemError,
                        "_db_Load_Trade(%d) => failed ..{%d}",
                        dwSeriala,
                        (unsigned __int8)v30);
                      result = v30;
                    }
                    else
                    {
                      v30 = CMainThread::_db_Load_ItemCombineEx(v62, dwSeriala, &pCon->dbItemCombineEx);
                      if ( v30 )
                      {
                        CLogFile::Write(
                          &v62->m_logSystemError,
                          "_db_Load_ItemCombineEx(%d) => failed ..{%d}",
                          dwSeriala,
                          (unsigned __int8)v30);
                        result = v30;
                      }
                      else
                      {
                        v30 = CMainThread::_db_Load_MacroData(v62, dwSeriala, &pCon->dbMacro);
                        if ( v30 )
                        {
                          CLogFile::Write(
                            &v62->m_logSystemError,
                            "_db_Load_MacroData(%d) => failed..{%d}",
                            dwSeriala,
                            (unsigned __int8)v30);
                          result = v30;
                        }
                        else
                        {
                          pCon->m_byHSKTime = -1;
                          pCon->m_byCristalBattleDBInfo = 3;
                          pCon->m_iPvpPoint = 0;
                          pCon->m_wKillPoint = 0;
                          pCon->m_wDiePoint = 0;
                          v30 = CMainThread::_db_Load_PvpPointLimitData(v62, dwSeriala, &pCon->dbPvpPointLimit);
                          if ( v30 )
                          {
                            CLogFile::Write(
                              &v62->m_logSystemError,
                              "_db_Load_PvpPointLimitData(%d) => failed..{%d}",
                              dwSeriala,
                              (unsigned __int8)v30);
                            result = v30;
                          }
                          else
                          {
                            v30 = CMainThread::_db_Load_CryMsg(v62, dwSeriala, &pCon->dbBossCry);
                            if ( v30 )
                            {
                              CLogFile::Write(
                                &v62->m_logSystemError,
                                "_db_Load_CryMsg(%d) => failed ..{%d}",
                                dwSeriala,
                                (unsigned __int8)v30);
                              result = v30;
                            }
                            else
                            {
                              v30 = CMainThread::_db_Load_PvpOrderView(v62, dwSeriala, &pCon->dbPvpOrderView);
                              if ( v30 )
                              {
                                CLogFile::Write(
                                  &v62->m_logSystemError,
                                  "_db_Load_PvpOrderViewPo(%d) => failed..{%d}",
                                  dwSeriala,
                                  (unsigned __int8)v30);
                                result = v30;
                              }
                              else
                              {
                                v30 = CMainThread::_db_Load_SFDelayData(v62, dwSeriala, &pCon->dbSFDelay);
                                if ( v30 )
                                {
                                  CLogFile::Write(
                                    &v62->m_logSystemError,
                                    "_db_Load_SFDelayData(%d) => failed ..{%d}",
                                    dwSeriala,
                                    (unsigned __int8)v30);
                                  result = v30;
                                }
                                else
                                {
                                  v30 = CMainThread::_db_Load_PrimiumPlayTime(
                                          v62,
                                          dwAccSerial,
                                          &pCon->dbPlayTimeInPcbang);
                                  if ( v30 )
                                  {
                                    CLogFile::Write(
                                      &v62->m_logSystemError,
                                      "_db_Load_PrimiumPlayTime(%d) => failed..{%d}",
                                      dwAccSerial,
                                      (unsigned __int8)v30);
                                    result = v30;
                                  }
                                  else
                                  {
                                    v30 = CMainThread::_db_Load_PotionDelay(v62, dwSeriala, &pCon->dbPotionNextUseTime);
                                    if ( v30 )
                                    {
                                      CLogFile::Write(
                                        &v62->m_logSystemError,
                                        "_db_Load_PotionDelay(%d) => failed..{%d}",
                                        dwSeriala,
                                        (unsigned __int8)v30);
                                      result = v30;
                                    }
                                    else
                                    {
                                      v30 = CMainThread::_db_Load_OreCutting(v62, dwSeriala, &pCon->dbCutting);
                                      if ( v30 )
                                      {
                                        CLogFile::Write(
                                          &v62->m_logSystemError,
                                          "_db_Load_OreCutting(%d) => failed..{%d}",
                                          dwSeriala,
                                          (unsigned __int8)v30);
                                        result = v30;
                                      }
                                      else
                                      {
                                        v30 = CMainThread::_db_Load_PcBangFavor(
                                                v62,
                                                dwSeriala,
                                                &pCon->dbPcBangFavorItem);
                                        if ( v30 )
                                        {
                                          CLogFile::Write(
                                            &v62->m_logSystemError,
                                            "_db_Load_PcBangFavor(%d) => failed..{%d}",
                                            dwSeriala,
                                            (unsigned __int8)v30);
                                          result = v30;
                                        }
                                        else if ( bAll || CMainThread::_db_Check_NpcData(v62, dwSeriala, pCon) != 40 )
                                        {
                                          szToday = 0;
                                          memset(&v32, 0, 9ui64);
                                          GetTodayStr(&szToday);
                                          pdwRank = 0;
                                          pwRankRate = -1;
                                          v35 = 0;
                                          pTrunk = (_TRUNK_DB_BASE *)&pwRankRate;
                                          v36 = CRFWorldDatabase::Select_PvpRate(
                                                  v62->m_pWorldDB,
                                                  dwSeriala,
                                                  &szToday,
                                                  &pdwRank,
                                                  &pwRankRate);
                                          if ( v36 )
                                          {
                                            if ( v36 == 2 )
                                            {
                                              pCon->dbAvator.m_dwPvpRank = -1;
                                              pCon->dbAvator.m_wRankRate = -1;
                                            }
                                            else if ( v36 == 1 )
                                            {
                                              CLogFile::Write(
                                                &v62->m_logSystemError,
                                                "Select_PvpRate(%d) => failed ..{%d}",
                                                dwSeriala,
                                                (unsigned __int8)v30);
                                              pCon->dbAvator.m_dwPvpRank = 0;
                                              pCon->dbAvator.m_wRankRate = -1;
                                            }
                                          }
                                          else
                                          {
                                            pCon->dbAvator.m_dwPvpRank = pdwRank;
                                            pCon->dbAvator.m_wRankRate = pwRankRate;
                                          }
                                          v36 = CMainThread::_db_load_raceboss(v62, dwSeriala, pCon);
                                          if ( v36 )
                                          {
                                            CLogFile::Write(
                                              &v62->m_logSystemError,
                                              "CMainThread::db_Load_Avator(...) : Ret(%u) _db_load_raceboss(dwSerial(%u), pData) Fail!",
                                              (unsigned __int8)v30,
                                              dwSeriala);
                                            result = v36;
                                          }
                                          else
                                          {
                                            v60 = &pCon->dbPersonalAmineInven;
                                            v19 = AutominePersonalMgr::instance();
                                            pCon->dbPersonalAmineInven.bUsable = AutominePersonalMgr::db_load_inven(
                                                                                   v19,
                                                                                   dwSeriala,
                                                                                   v60);
                                            v36 = CMainThread::_db_load_punishment(v62, dwSeriala, pCon);
                                            if ( v36 )
                                            {
                                              CLogFile::Write(
                                                &v62->m_logSystemError,
                                                "CMainThread::_db_load_punishment(...) : Ret(%u) _db_load_punishment(dwSe"
                                                "rial(%u), pData) Fail!",
                                                (unsigned __int8)v30,
                                                dwSeriala);
                                              result = v36;
                                            }
                                            else if ( pbAddItem && pdwAddDalant && pdwAddGold )
                                            {
                                              if ( pCon->dbAvator.m_byLevel )
                                              {
                                                if ( pCon->dbAvator.m_byLevel >= 25
                                                  && (v36 = CMainThread::_db_load_losebattlecount(v62, dwSeriala, pCon)) != 0 )
                                                {
                                                  CLogFile::Write(
                                                    &v62->m_logSystemError,
                                                    "CMainThread::db_Load_Avator(...) : Ret(%u) _db_load_losebattlecount("
                                                    "dwSerial(%u), pData) Fail!",
                                                    (unsigned __int8)v30,
                                                    dwSeriala);
                                                  result = v36;
                                                }
                                                else
                                                {
                                                  v37 = 0;
                                                  v38 = 20 * pCon->dbAvator.m_byBagNum;
                                                  for ( j = 0; j < v38; ++j )
                                                  {
                                                    if ( !_INVENKEY::IsFilled((_INVENKEY *)((char *)&pCon->dbInven
                                                                                          + 37 * j)) )
                                                      ++v37;
                                                  }
                                                  v39 = 0;
                                                  v44 = 0;
                                                  pbyType = -1;
                                                  while ( 1 )
                                                  {
                                                    piTime = &v44;
                                                    pDwItemChargeIndex = &dwItemChargeIndex;
                                                    pDwItemCode_U = &pdwItemCode_U;
                                                    pTrunk = (_TRUNK_DB_BASE *)&pDwItemCode_D;
                                                    if ( !CRFWorldDatabase::Select_ItemCharge(
                                                            v62->m_pWorldDB,
                                                            dwSeriala,
                                                            &pbyType,
                                                            &pDwItemCode_K,
                                                            &pDwItemCode_D,
                                                            &pdwItemCode_U,
                                                            &dwItemChargeIndex,
                                                            &v44) )
                                                      break;
                                                    if ( pbyType )
                                                    {
                                                      if ( pbyType == 1 )
                                                      {
                                                        *pdwAddDalant += pDwItemCode_D;
                                                        CRFWorldDatabase::Delete_ItemCharge(
                                                          v62->m_pWorldDB,
                                                          dwItemChargeIndex);
                                                      }
                                                      else if ( pbyType == 2 )
                                                      {
                                                        *pdwAddGold += pDwItemCode_D;
                                                        CRFWorldDatabase::Delete_ItemCharge(
                                                          v62->m_pWorldDB,
                                                          dwItemChargeIndex);
                                                      }
                                                      else
                                                      {
                                                        CRFWorldDatabase::Delete_ItemCharge(
                                                          v62->m_pWorldDB,
                                                          dwItemChargeIndex);
                                                      }
                                                    }
                                                    else
                                                    {
                                                      for ( j = 0; j < v38; ++j )
                                                      {
                                                        if ( !_INVENKEY::IsFilled((_INVENKEY *)((char *)&pCon->dbInven
                                                                                              + 37 * j)) )
                                                        {
                                                          if ( pbAddItem )
                                                          {
                                                            _INVENKEY::LoadDBKey(
                                                              (_INVENKEY *)((char *)&pCon->dbInven + 37 * j),
                                                              pDwItemCode_K);
                                                            pCon->dbInven.m_List[j].dwDur = pDwItemCode_D;
                                                            pCon->dbInven.m_List[j].dwUpt = pdwItemCode_U;
                                                            v46 = TimeItem::FindTimeRec(
                                                                    pCon->dbInven.m_List[j].Key.byTableCode,
                                                                    pCon->dbInven.m_List[j].Key.wItemIndex);
                                                            if ( v46 && v46->m_nCheckType )
                                                            {
                                                              if ( v44 <= 0 )
                                                                v44 = v46->m_nUseTime;
                                                              _time32(&Time);
                                                              pCon->dbInven.m_List[j].byCsMethod = v46->m_nCheckType;
                                                              if ( v46->m_nCheckType == 1 )
                                                              {
                                                                pCon->dbInven.m_List[j].dwT = v44;
                                                              }
                                                              else if ( v46->m_nCheckType == 2 )
                                                              {
                                                                pCon->dbInven.m_List[j].dwT = v44 + Time;
                                                              }
                                                              pCon->dbInven.m_List[j].dwLendRegdTime = Time;
                                                            }
                                                            CRFWorldDatabase::Delete_ItemCharge(
                                                              v62->m_pWorldDB,
                                                              dwItemChargeIndex);
                                                            pbAddItem[j] = 1;
                                                            v39 = 1;
                                                          }
                                                          break;
                                                        }
                                                      }
                                                      if ( !v39 || j >= v38 )
                                                        break;
                                                    }
                                                  }
                                                  if ( pdTrunkOldDalant )
                                                    *pdTrunkOldDalant = pCon->dbTrunk.dDalant;
                                                  if ( pdTrunkOldGold )
                                                    *pdTrunkOldGold = pCon->dbTrunk.dGold;
                                                  if ( pbyTrunkOldSlot )
                                                    *pbyTrunkOldSlot = pCon->dbTrunk.bySlotNum;
                                                  if ( pbyExtTrunkOldSlot )
                                                    *pbyExtTrunkOldSlot = pCon->dbTrunk.byExtSlotNum;
                                                  if ( pbTrunkAddItem
                                                    && pbyTrunkOldSlot
                                                    && pdTrunkOldDalant
                                                    && pdTrunkOldGold
                                                    && pbExtTrunkAddItem
                                                    && pbyExtTrunkOldSlot )
                                                  {
                                                    pdMoney = 0.0;
                                                    pbyRace = -1;
                                                    pbyType = 1;
                                                    if ( pCon->dbTrunk.bySlotNum )
                                                    {
                                                      for ( j = 0; j < 10000; ++j )
                                                      {
                                                        v28 = &v44;
                                                        pdwDBID = &dwItemChargeIndex;
                                                        piTime = (int *)&pbyRace;
                                                        pDwItemChargeIndex = &pdwItemCode_U;
                                                        pDwItemCode_U = (unsigned int *)&pDwItemCode_D;
                                                        pTrunk = (_TRUNK_DB_BASE *)&pDwItemCode_K;
                                                        if ( !CRFWorldDatabase::Select_AccountItemCharge(
                                                                v62->m_pWorldDB,
                                                                dwAccSerial,
                                                                &pbyType,
                                                                &pdMoney,
                                                                &pDwItemCode_K,
                                                                &pDwItemCode_D,
                                                                &pdwItemCode_U,
                                                                &pbyRace,
                                                                &dwItemChargeIndex,
                                                                &v44) )
                                                          break;
                                                        if ( pDwItemCode_D == 20
                                                          || pDwItemCode_D == 40
                                                          || pDwItemCode_D == 60
                                                          || pDwItemCode_D == 80
                                                          || pDwItemCode_D == 100 )
                                                        {
                                                          if ( pDwItemCode_D == 20 )
                                                          {
                                                            CRFWorldDatabase::Delete_TrunkItemCharge(
                                                              v62->m_pWorldDB,
                                                              dwItemChargeIndex);
                                                          }
                                                          else
                                                          {
                                                            pDwItemCode_D -= 20i64;
                                                            if ( pCon->dbTrunk.bySlotNum == 100 )
                                                            {
                                                              v20 = 500000 * (pDwItemCode_D / 0x14);
                                                              v21 = (double)(signed int)v20;
                                                              if ( v20 < 0 )
                                                                v21 = v21 + 1.844674407370955e19;
                                                              v50 = v21;
                                                              pCon->dbTrunk.dDalant = pCon->dbTrunk.dDalant + v21;
                                                              CRFWorldDatabase::Delete_TrunkItemCharge(
                                                                v62->m_pWorldDB,
                                                                dwItemChargeIndex);
                                                            }
                                                            else
                                                            {
                                                              pCon->dbTrunk.bySlotNum += pDwItemCode_D;
                                                              if ( pCon->dbTrunk.bySlotNum > 100 )
                                                              {
                                                                v51 = pCon->dbTrunk.bySlotNum - 100;
                                                                v52 = (double)(signed int)(500000 * (v51 / 0x14));
                                                                pCon->dbTrunk.dDalant = pCon->dbTrunk.dDalant + v52;
                                                                pCon->dbTrunk.bySlotNum = 100;
                                                              }
                                                              CRFWorldDatabase::Delete_TrunkItemCharge(
                                                                v62->m_pWorldDB,
                                                                dwItemChargeIndex);
                                                            }
                                                          }
                                                        }
                                                        else
                                                        {
                                                          CRFWorldDatabase::Delete_TrunkItemCharge(
                                                            v62->m_pWorldDB,
                                                            dwItemChargeIndex);
                                                        }
                                                      }
                                                      pbyType = 2;
                                                      pbyRace = pCon->dbAvator.m_byRaceSexCode >> 1;
                                                      for ( j = 0; j < 10000; ++j )
                                                      {
                                                        v28 = &v44;
                                                        pdwDBID = &dwItemChargeIndex;
                                                        piTime = (int *)&pbyRace;
                                                        pDwItemChargeIndex = &pdwItemCode_U;
                                                        pDwItemCode_U = (unsigned int *)&pDwItemCode_D;
                                                        pTrunk = (_TRUNK_DB_BASE *)&pDwItemCode_K;
                                                        if ( !CRFWorldDatabase::Select_AccountItemCharge(
                                                                v62->m_pWorldDB,
                                                                dwAccSerial,
                                                                &pbyType,
                                                                &pdMoney,
                                                                &pDwItemCode_K,
                                                                &pDwItemCode_D,
                                                                &pdwItemCode_U,
                                                                &pbyRace,
                                                                &dwItemChargeIndex,
                                                                &v44) )
                                                          break;
                                                        pCon->dbTrunk.dDalant = pCon->dbTrunk.dDalant + pdMoney;
                                                        CRFWorldDatabase::Delete_TrunkItemCharge(
                                                          v62->m_pWorldDB,
                                                          dwItemChargeIndex);
                                                      }
                                                      pbyType = 3;
                                                      pbyRace = pCon->dbAvator.m_byRaceSexCode >> 1;
                                                      for ( j = 0; j < 10000; ++j )
                                                      {
                                                        v28 = &v44;
                                                        pdwDBID = &dwItemChargeIndex;
                                                        piTime = (int *)&pbyRace;
                                                        pDwItemChargeIndex = &pdwItemCode_U;
                                                        pDwItemCode_U = (unsigned int *)&pDwItemCode_D;
                                                        pTrunk = (_TRUNK_DB_BASE *)&pDwItemCode_K;
                                                        if ( !CRFWorldDatabase::Select_AccountItemCharge(
                                                                v62->m_pWorldDB,
                                                                dwAccSerial,
                                                                &pbyType,
                                                                &pdMoney,
                                                                &pDwItemCode_K,
                                                                &pDwItemCode_D,
                                                                &pdwItemCode_U,
                                                                &pbyRace,
                                                                &dwItemChargeIndex,
                                                                &v44) )
                                                          break;
                                                        pCon->dbTrunk.dGold = pCon->dbTrunk.dGold + pdMoney;
                                                        CRFWorldDatabase::Delete_TrunkItemCharge(
                                                          v62->m_pWorldDB,
                                                          dwItemChargeIndex);
                                                      }
                                                      pbyType = 4;
                                                      pbyRace = -1;
                                                      v53 = 0;
                                                      if ( !pCon->dbTrunk.byExtSlotNum && pCon->dbTrunk.bySlotNum > 0 )
                                                      {
                                                        v28 = &v44;
                                                        pdwDBID = &dwItemChargeIndex;
                                                        piTime = (int *)&pbyRace;
                                                        pDwItemChargeIndex = &pdwItemCode_U;
                                                        pDwItemCode_U = (unsigned int *)&pDwItemCode_D;
                                                        pTrunk = (_TRUNK_DB_BASE *)&pDwItemCode_K;
                                                        if ( CRFWorldDatabase::Select_AccountItemCharge(
                                                               v62->m_pWorldDB,
                                                               dwAccSerial,
                                                               &pbyType,
                                                               &pdMoney,
                                                               &pDwItemCode_K,
                                                               &pDwItemCode_D,
                                                               &pdwItemCode_U,
                                                               &pbyRace,
                                                               &dwItemChargeIndex,
                                                               &v44) )
                                                        {
                                                          v53 = 1;
                                                        }
                                                      }
                                                      if ( v53 )
                                                      {
                                                        for ( j = 0; j < 10000; ++j )
                                                        {
                                                          v28 = &v44;
                                                          pdwDBID = &dwItemChargeIndex;
                                                          piTime = (int *)&pbyRace;
                                                          pDwItemChargeIndex = &pdwItemCode_U;
                                                          pDwItemCode_U = (unsigned int *)&pDwItemCode_D;
                                                          pTrunk = (_TRUNK_DB_BASE *)&pDwItemCode_K;
                                                          if ( !CRFWorldDatabase::Select_AccountItemCharge(
                                                                  v62->m_pWorldDB,
                                                                  dwAccSerial,
                                                                  &pbyType,
                                                                  &pdMoney,
                                                                  &pDwItemCode_K,
                                                                  &pDwItemCode_D,
                                                                  &pdwItemCode_U,
                                                                  &pbyRace,
                                                                  &dwItemChargeIndex,
                                                                  &v44) )
                                                            break;
                                                          if ( pDwItemCode_D == 20 || pDwItemCode_D == 40 )
                                                          {
                                                            pCon->dbTrunk.bySlotNum = 100;
                                                            pCon->dbTrunk.byExtSlotNum += pDwItemCode_D;
                                                            if ( pCon->dbTrunk.byExtSlotNum > 40 )
                                                              pCon->dbTrunk.byExtSlotNum = 40;
                                                            CRFWorldDatabase::Delete_TrunkItemCharge(
                                                              v62->m_pWorldDB,
                                                              dwItemChargeIndex);
                                                          }
                                                          else
                                                          {
                                                            CRFWorldDatabase::Delete_TrunkItemCharge(
                                                              v62->m_pWorldDB,
                                                              dwItemChargeIndex);
                                                          }
                                                        }
                                                      }
                                                      pbyType = 0;
                                                      pbyRace = -1;
                                                      for ( k = 0; k < (signed int)pCon->dbTrunk.bySlotNum; ++k )
                                                      {
                                                        if ( !_INVENKEY::IsFilled(&pCon->dbTrunk.m_List[k].Key) )
                                                        {
                                                          v28 = &v44;
                                                          pdwDBID = &dwItemChargeIndex;
                                                          piTime = (int *)&pbyRace;
                                                          pDwItemChargeIndex = &pdwItemCode_U;
                                                          pDwItemCode_U = (unsigned int *)&pDwItemCode_D;
                                                          pTrunk = (_TRUNK_DB_BASE *)&pDwItemCode_K;
                                                          if ( !CRFWorldDatabase::Select_AccountItemCharge(
                                                                  v62->m_pWorldDB,
                                                                  dwAccSerial,
                                                                  &pbyType,
                                                                  &pdMoney,
                                                                  &pDwItemCode_K,
                                                                  &pDwItemCode_D,
                                                                  &pdwItemCode_U,
                                                                  &pbyRace,
                                                                  &dwItemChargeIndex,
                                                                  &v44) )
                                                            break;
                                                          _INVENKEY::LoadDBKey(
                                                            &pCon->dbTrunk.m_List[k].Key,
                                                            pDwItemCode_K);
                                                          pCon->dbTrunk.m_List[k].dwDur = pDwItemCode_D;
                                                          pCon->dbTrunk.m_List[k].dwUpt = pdwItemCode_U;
                                                          pCon->dbTrunk.m_List[k].byRace = pbyRace;
                                                          v55 = TimeItem::FindTimeRec(
                                                                  pCon->dbTrunk.m_List[k].Key.byTableCode,
                                                                  pCon->dbTrunk.m_List[k].Key.wItemIndex);
                                                          if ( v55 && v55->m_nCheckType )
                                                          {
                                                            if ( v44 <= 0 )
                                                              v44 = v55->m_nUseTime;
                                                            _time32(&v56);
                                                            pCon->dbTrunk.m_List[k].byCsMethod = v55->m_nCheckType;
                                                            if ( v55->m_nCheckType == 1 )
                                                            {
                                                              pCon->dbTrunk.m_List[k].dwT = v44;
                                                            }
                                                            else if ( v55->m_nCheckType == 2 )
                                                            {
                                                              pCon->dbTrunk.m_List[k].dwT = v44 + v56;
                                                            }
                                                            pCon->dbTrunk.m_List[k].dwLendRegdTime = v56;
                                                          }
                                                          pbTrunkAddItem[k] = 1;
                                                          CRFWorldDatabase::Delete_TrunkItemCharge(
                                                            v62->m_pWorldDB,
                                                            dwItemChargeIndex);
                                                        }
                                                      }
                                                      result = v30;
                                                    }
                                                    else
                                                    {
                                                      if ( pbCreateTrunkFree )
                                                      {
                                                        v28 = &v44;
                                                        pdwDBID = &dwItemChargeIndex;
                                                        piTime = (int *)&pbyRace;
                                                        pDwItemChargeIndex = &pdwItemCode_U;
                                                        pDwItemCode_U = (unsigned int *)&pDwItemCode_D;
                                                        pTrunk = (_TRUNK_DB_BASE *)&pDwItemCode_K;
                                                        if ( CRFWorldDatabase::Select_AccountItemCharge(
                                                               v62->m_pWorldDB,
                                                               dwAccSerial,
                                                               &pbyType,
                                                               &pdMoney,
                                                               &pDwItemCode_K,
                                                               &pDwItemCode_D,
                                                               &pdwItemCode_U,
                                                               &pbyRace,
                                                               &dwItemChargeIndex,
                                                               &v44) )
                                                        {
                                                          *pbCreateTrunkFree = 1;
                                                        }
                                                      }
                                                      result = v30;
                                                    }
                                                  }
                                                  else
                                                  {
                                                    result = v30;
                                                  }
                                                }
                                              }
                                              else
                                              {
                                                result = v30;
                                              }
                                            }
                                            else
                                            {
                                              result = v30;
                                            }
                                          }
                                        }
                                        else
                                        {
                                          result = 40;
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
        else
        {
          CLogFile::Write(&v62->m_logSystemError, "_db_Load_Unit(%d) => failed ..{%d}", dwSeriala, (unsigned __int8)v30);
          result = v30;
        }
      }
    }
  }
  return result;
}
