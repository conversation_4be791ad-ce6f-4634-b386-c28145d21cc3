/*
 * Function: ?Insert_GuildMoneyHistory@CRFWorldDatabase@@QEAA_NKNNNNPEADK0@Z
 * Address: 0x14049AEF0
 */

bool __fastcall CRFWorldDatabase::Insert_GuildMoneyHistory(CRFWorldDatabase *this, unsigned int dwGuildSerial, long double dInoutDalant, long double dInoutGold, long double dResultDalant, long double dResultGold, char *wszDate, unsigned int dwAvatorSerial, char *pwszName)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v12; // [sp-20h] [bp-488h]@1
  long double v13; // [sp+0h] [bp-468h]@4
  long double v14; // [sp+8h] [bp-460h]@4
  long double v15; // [sp+10h] [bp-458h]@4
  char *v16; // [sp+18h] [bp-450h]@4
  unsigned int v17; // [sp+20h] [bp-448h]@4
  char *v18; // [sp+28h] [bp-440h]@4
  char Dest; // [sp+40h] [bp-428h]@4
  char v20; // [sp+41h] [bp-427h]@4
  unsigned __int64 v21; // [sp+450h] [bp-18h]@4
  CRFWorldDatabase *v22; // [sp+470h] [bp+8h]@1

  v22 = this;
  v9 = &v12;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v21 = (unsigned __int64)&v12 ^ _security_cookie;
  Dest = 0;
  memset(&v20, 0, 0x3FFui64);
  v18 = pwszName;
  v17 = dwAvatorSerial;
  v16 = wszDate;
  v15 = dResultGold;
  v14 = dResultDalant;
  v13 = dInoutGold;
  sprintf(
    &Dest,
    "{ CALL pInsert_GuildMoneyHistory_Log( %d, %.0f, %.0f, %.0f, %.0f, '%s', %d, '%s' ) }",
    dwGuildSerial,
    dInoutDalant);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v22->vfptr, &Dest, 1);
}
