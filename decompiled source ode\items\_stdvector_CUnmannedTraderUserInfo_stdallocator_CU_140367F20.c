/*
 * Function: _std::vector_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo___::erase_::_1_::dtor$2
 * Address: 0x140367F20
 */

void __fastcall std::vector_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo___::erase_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 40) & 1 )
  {
    *(_DWORD *)(a2 + 40) &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::~_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(*(std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > **)(a2 + 88));
  }
}
