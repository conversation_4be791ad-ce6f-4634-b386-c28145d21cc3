/*
 * Function: ?Get@CGuildBattleSchedulePool@GUILD_BATTLE@@QEAAPEAVCGuildBattleSchedule@2@IK@Z
 * Address: 0x1403DEA50
 */

GUILD_BATTLE::CGuildBattleSchedule *__fastcall GUILD_BATTLE::CGuildBattleSchedulePool::Get(GUILD_BATTLE::CGuildBattleSchedulePool *this, unsigned int uiSLID, unsigned int dwStartInx)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int dwSID; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CGuildBattleSchedulePool *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  dwSID = dwStartInx + 23 * uiSLID;
  return GUILD_BATTLE::CGuildBattleSchedulePool::Get(v8, dwStartInx + 23 * uiSLID);
}
