/*
 * Function: _std::_Distance2_std::_Vector_const_iterator_CUnmannedTraderClassInfo_____ptr64_std::allocator_CUnmannedTraderClassInfo_____ptr64____unsigned___int64__::_1_::dtor$0
 * Address: 0x140375EA0
 */

void __fastcall std::_Distance2_std::_Vector_const_iterator_CUnmannedTraderClassInfo_____ptr64_std::allocator_CUnmannedTraderClassInfo_____ptr64____unsigned___int64__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(*(std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > **)(a2 + 72));
}
