/*
 * Function: ?_AssistSF_Cont_Temp@CMonster@@IEAAHPEAVCCharacter@@PEAVCMonsterSkill@@@Z
 * Address: 0x14014D330
 */

__int64 __fastcall CMonster::_AssistSF_Cont_Temp(CMonster *this, CCharacter *pDst, CMonsterSkill *pskill)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  unsigned int v6; // eax@13
  unsigned int v7; // eax@14
  unsigned int v8; // eax@21
  unsigned int v9; // eax@22
  __int64 v10; // [sp+0h] [bp-108h]@1
  unsigned int v11; // [sp+40h] [bp-C8h]@4
  char v12; // [sp+44h] [bp-C4h]@8
  char v13; // [sp+45h] [bp-C3h]@8
  _force_fld *v14; // [sp+48h] [bp-C0h]@11
  _force_fld *pForceFld; // [sp+50h] [bp-B8h]@12
  char v16; // [sp+64h] [bp-A4h]@12
  bool v17; // [sp+84h] [bp-84h]@12
  int nForceLv; // [sp+94h] [bp-74h]@12
  _skill_fld *v19; // [sp+98h] [bp-70h]@16
  _skill_fld *pSkillFld; // [sp+A0h] [bp-68h]@17
  char v21; // [sp+B4h] [bp-54h]@17
  bool v22; // [sp+D4h] [bp-34h]@17
  int nSkillLv; // [sp+E4h] [bp-24h]@17
  int nEffectCode; // [sp+E8h] [bp-20h]@20
  int v25; // [sp+ECh] [bp-1Ch]@8
  int v26; // [sp+F0h] [bp-18h]@18
  CMonster *v27; // [sp+110h] [bp+8h]@1
  CCharacter *pDstChar; // [sp+118h] [bp+10h]@1
  CMonsterSkill *v29; // [sp+120h] [bp+18h]@1

  v29 = pskill;
  pDstChar = pDst;
  v27 = this;
  v3 = &v10;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = 0;
  if ( pskill )
  {
    if ( pDst )
    {
      v12 = 0;
      v13 = 0;
      v25 = CMonsterSkill::GetType(pskill);
      if ( v25 > 0 )
      {
        if ( v25 <= 2 )
        {
          v19 = (_skill_fld *)CMonsterSkill::GetFld(v29);
          if ( v19 )
          {
            pSkillFld = v19;
            v21 = 0;
            v22 = 0;
            nSkillLv = CMonsterSkill::GetSFLv(v29);
            if ( CMonsterSkill::GetType(v29) == 1 )
              v26 = 0;
            else
              v26 = 2;
            nEffectCode = v26;
            if ( CCharacter::AssistSkill((CCharacter *)&v27->vfptr, pDstChar, v26, pSkillFld, nSkillLv, &v21, &v22) )
            {
              CMonster::SendMsg_Assist_Skill(v27, v21, nEffectCode, pDstChar, pSkillFld, nSkillLv);
              v8 = GetLoopTime();
              CMonsterSkill::Use(v29, v8, 1);
              v11 = 1;
            }
            else
            {
              v9 = GetLoopTime();
              CMonsterSkill::Use(v29, v9, 0);
              v11 = 0;
            }
          }
        }
        else if ( v25 == 3 )
        {
          v14 = (_force_fld *)CMonsterSkill::GetFld(v29);
          if ( v14 )
          {
            pForceFld = v14;
            v16 = 0;
            v17 = 0;
            nForceLv = CMonsterSkill::GetSFLv(v29);
            if ( CCharacter::AssistForce((CCharacter *)&v27->vfptr, pDstChar, pForceFld, nForceLv, &v16, &v17) )
            {
              CMonster::SendMsg_Assist_Force(v27, v16, pDstChar, pForceFld, nForceLv);
              v6 = GetLoopTime();
              CMonsterSkill::Use(v29, v6, 1);
              v11 = 1;
            }
            else
            {
              v7 = GetLoopTime();
              CMonsterSkill::Use(v29, v7, 0);
              v11 = 0;
            }
          }
        }
      }
      if ( v27->m_bMove )
      {
        if ( v11 )
          CCharacter::Stop((CCharacter *)&v27->vfptr);
      }
      result = v11;
    }
    else
    {
      result = v11;
    }
  }
  else
  {
    result = v11;
  }
  return result;
}
