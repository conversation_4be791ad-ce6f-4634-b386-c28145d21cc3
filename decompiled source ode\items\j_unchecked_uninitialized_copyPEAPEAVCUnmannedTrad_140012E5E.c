/*
 * Function: j_??$unchecked_uninitialized_copy@PEAPEAVCUnmannedTraderSubClassInfo@@PEAPEAV1@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@stdext@@YAPEAPEAVCUnmannedTraderSubClassInfo@@PEAPEAV1@00AEAV?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@Z
 * Address: 0x140012E5E
 */

CUnmannedTraderSubClassInfo **__fastcall stdext::unchecked_uninitialized_copy<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo * *,std::allocator<CUnmannedTraderSubClassInfo *>>(CUnmannedTraderSubClassInfo **_First, CUnmannedTraderSubClassInfo **_Last, CUnmannedTraderSubClassInfo **_Dest, std::allocator<CUnmannedTraderSubClassInfo *> *_Al)
{
  return stdext::unchecked_uninitialized_copy<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo * *,std::allocator<CUnmannedTraderSubClassInfo *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
