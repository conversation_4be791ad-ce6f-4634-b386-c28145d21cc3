/*
 * Function: j_??$_Destroy_range@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@YAXPEAVCUnmannedTraderItemCodeInfo@@0AEAV?$allocator@VCUnmannedTraderItemCodeInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@@Z
 * Address: 0x140003468
 */

void __fastcall std::_Destroy_range<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(CUnmannedTraderItemCodeInfo *_First, CUnmannedTraderItemCodeInfo *_Last, std::allocator<CUnmannedTraderItemCodeInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal)
{
  std::_Destroy_range<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(
    _First,
    _Last,
    _Al,
    __formal);
}
