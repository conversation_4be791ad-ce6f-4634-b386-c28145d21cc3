/*
 * Function: ?DeleteQuestItem@CQuestMgr@@QEAA_NPEADG@Z
 * Address: 0x14028B040
 */

char __fastcall CQuestMgr::DeleteQuestItem(CQuestMgr *this, char *pszItemCode, unsigned __int16 wCount)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char v6; // al@10
  __int64 v7; // [sp+0h] [bp-78h]@1
  bool bUpdate; // [sp+20h] [bp-58h]@19
  bool bSend[8]; // [sp+28h] [bp-50h]@19
  unsigned __int8 v10; // [sp+30h] [bp-48h]@4
  _base_fld *v11; // [sp+38h] [bp-40h]@6
  int v12; // [sp+40h] [bp-38h]@8
  int v13; // [sp+44h] [bp-34h]@8
  int j; // [sp+48h] [bp-30h]@8
  _STORAGE_LIST::_db_con *pItem; // [sp+50h] [bp-28h]@11
  int v16; // [sp+58h] [bp-20h]@19
  unsigned int dwDur; // [sp+5Ch] [bp-1Ch]@19
  __int64 v18; // [sp+60h] [bp-18h]@17
  CQuestMgr *v19; // [sp+80h] [bp+8h]@1
  const char *psItemCode; // [sp+88h] [bp+10h]@1
  unsigned __int16 v21; // [sp+90h] [bp+18h]@1

  v21 = wCount;
  psItemCode = pszItemCode;
  v19 = this;
  v3 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = GetItemTableCode(pszItemCode);
  if ( v10 == 255 )
  {
    result = 0;
  }
  else
  {
    v11 = CRecordData::GetRecordByHash((CRecordData *)&unk_1799C6AA0 + v10, psItemCode, 2, 5);
    if ( v11 )
    {
      v12 = 0;
      v13 = v21;
      for ( j = 0; ; ++j )
      {
        v6 = CPlayerDB::GetBagNum(&v19->m_pMaster->m_Param);
        if ( j >= 20 * (unsigned __int8)v6 )
          break;
        pItem = &v19->m_pMaster->m_Param.m_dbInven.m_pStorageList[j];
        if ( pItem->m_bLoad
          && pItem->m_byTableCode == v10
          && pItem->m_wItemIndex == LOWORD(v11->m_dwIndex)
          && !pItem->m_bLock )
        {
          if ( IsOverLapItem(pItem->m_byTableCode) )
          {
            v18 = pItem->m_dwDur > v13 ? (signed __int64)v13 : pItem->m_dwDur;
            v16 = v18;
            bSend[0] = 1;
            bUpdate = 1;
            dwDur = CPlayer::Emb_AlterDurPoint(v19->m_pMaster, 0, j, -(signed int)v18, 1, 1);
            if ( dwDur )
              CPlayer::SendMsg_AdjustAmountInform(v19->m_pMaster, 0, pItem->m_wSerial, dwDur);
            else
              CMgrAvatorItemHistory::delete_npc_quest_item(
                &CPlayer::s_MgrItemHistory,
                v19->m_pMaster->m_ObjID.m_wIndex,
                pItem,
                v19->m_pMaster->m_szItemHistoryFileName);
            v13 -= v16;
          }
          else
          {
            *(_QWORD *)bSend = "CQuestMgr::GiveItem() -- 1";
            bUpdate = 1;
            CPlayer::Emb_DelStorage(v19->m_pMaster, 0, j, 0, 1, "CQuestMgr::GiveItem() -- 1");
            CMgrAvatorItemHistory::delete_npc_quest_item(
              &CPlayer::s_MgrItemHistory,
              v19->m_pMaster->m_ObjID.m_wIndex,
              pItem,
              v19->m_pMaster->m_szItemHistoryFileName);
            --v13;
          }
          if ( v13 <= 0 )
            return 1;
        }
      }
      result = 0;
    }
    else
    {
      result = 0;
    }
  }
  return result;
}
