/*
 * Function: ??1table@lua_tinker@@QEAA@XZ
 * Address: 0x140446780
 */

void __fastcall lua_tinker::table::~table(lua_tinker::table *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  lua_tinker::table *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  lua_tinker::table_obj::dec_ref(v4->m_obj);
}
