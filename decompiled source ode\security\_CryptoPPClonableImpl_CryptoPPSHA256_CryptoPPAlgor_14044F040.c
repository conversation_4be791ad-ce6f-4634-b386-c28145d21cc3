/*
 * Function: _CryptoPP::ClonableImpl_CryptoPP::SHA256_CryptoPP::AlgorithmImpl_CryptoPP::IteratedHash_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_CryptoPP::HashTransformation__CryptoPP::SHA256___::Clone_::_1_::dtor$0
 * Address: 0x14044F040
 */

void __fastcall CryptoPP::ClonableImpl_CryptoPP::SHA256_CryptoPP::AlgorithmImpl_CryptoPP::IteratedHash_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_CryptoPP::HashTransformation__CryptoPP::SHA256___::Clone_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  operator delete(*(void **)(a2 + 40));
}
