/*
 * Function: ?ReadSystemPass@CNationSettingDataID@@UEAA_NXZ
 * Address: 0x14022CA80
 */

char __fastcall CNationSettingDataID::ReadSystemPass(CNationSettingDataID *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-78h]@1
  char ReturnedString; // [sp+38h] [bp-40h]@4
  char v6; // [sp+39h] [bp-3Fh]@4
  char v7; // [sp+3Bh] [bp-3Dh]@8
  char v8; // [sp+3Dh] [bp-3Bh]@10
  char v9; // [sp+40h] [bp-38h]@12
  char v10; // [sp+43h] [bp-35h]@14
  unsigned __int64 v11; // [sp+60h] [bp-18h]@4
  CNationSettingDataID *v12; // [sp+80h] [bp+8h]@1

  v12 = this;
  v1 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11 = (unsigned __int64)&v4 ^ _security_cookie;
  ReturnedString = 0;
  memset(&v6, 0, 0xFui64);
  GetPrivateProfileStringA(
    "HolySystem",
    "KeeperCallCode",
    "X",
    &ReturnedString,
    0x10u,
    ".\\Initialize\\NewHolySystem.ini");
  if ( ReturnedString == 88 )
  {
    result = 0;
  }
  else if ( strlen_0(&ReturnedString) == 15 )
  {
    if ( v7 == 107 )
    {
      if ( v8 == 48 )
      {
        if ( v9 == 113 )
        {
          if ( v10 == 102 )
          {
            strcpy_s(v12->m_szVaildKey, 0x11ui64, &ReturnedString);
            result = 1;
          }
          else
          {
            result = 0;
          }
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
