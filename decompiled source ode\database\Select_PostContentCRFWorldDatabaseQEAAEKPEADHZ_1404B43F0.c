/*
 * Function: ?Select_PostContent@CRFWorldDatabase@@QEAAEKPEADH@Z
 * Address: 0x1404B43F0
 */

char __fastcall CRFWorldDatabase::Select_PostContent(CRFWorldDatabase *this, unsigned int dwPostSerial, char *wszContent, int nSize)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v7; // [sp+0h] [bp-108h]@1
  void *SQLStmt; // [sp+20h] [bp-E8h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-E0h]@22
  SQLLEN v10; // [sp+38h] [bp-D0h]@22
  __int16 v11; // [sp+44h] [bp-C4h]@9
  int v12; // [sp+48h] [bp-C0h]@4
  char DstBuf; // [sp+60h] [bp-A8h]@4
  char v14; // [sp+61h] [bp-A7h]@4
  char v15; // [sp+E4h] [bp-24h]@16
  unsigned __int64 v16; // [sp+F0h] [bp-18h]@4
  CRFWorldDatabase *v17; // [sp+110h] [bp+8h]@1
  char *TargetValue; // [sp+120h] [bp+18h]@1
  int v19; // [sp+128h] [bp+20h]@1

  v19 = nSize;
  TargetValue = wszContent;
  v17 = this;
  v4 = &v7;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v16 = (unsigned __int64)&v7 ^ _security_cookie;
  v12 = 0;
  DstBuf = 0;
  memset(&v14, 0, 0x7Fui64);
  sprintf_s(&DstBuf, 0x80ui64, "select content from tbl_PostStorage where serial=%d", dwPostSerial);
  if ( v17->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v17->vfptr, &DstBuf);
  if ( v17->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v17->vfptr) )
  {
    v11 = SQLExecDirectA_0(v17->m_hStmtSelect, &DstBuf, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v17->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v11, &DstBuf, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v11, v17->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v11 = SQLFetch_0(v17->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        v15 = 0;
        if ( v11 == 100 )
        {
          v15 = 2;
        }
        else
        {
          SQLStmt = v17->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v11, &DstBuf, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v11, v17->m_hStmtSelect);
          v15 = 1;
        }
        if ( v17->m_hStmtSelect )
          SQLCloseCursor_0(v17->m_hStmtSelect);
        result = v15;
      }
      else
      {
        StrLen_or_IndPtr = &v10;
        SQLStmt = (void *)v19;
        v11 = SQLGetData_0(v17->m_hStmtSelect, 1u, 1, TargetValue, v19, &v10);
        if ( v11 && v11 != 1 )
        {
          SQLStmt = v17->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v11, &DstBuf, "SQLGetData", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v11, v17->m_hStmtSelect);
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          result = 1;
        }
        else
        {
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          if ( v17->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v17->vfptr, "%s Success", &DstBuf);
          result = 0;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v17->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1;
  }
  return result;
}
