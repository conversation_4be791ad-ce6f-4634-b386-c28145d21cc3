/*
 * Function: ?Convert@CCheckSumGuildConverter@@QEAAXNNPEAVCCheckSumGuildData@@@Z
 * Address: 0x1402C1760
 */

void __usercall CCheckSumGuildConverter::Convert(CCheckSumGuildConverter *this@<rcx>, long double dDalant@<xmm1>, long double dGold@<xmm2>, CCheckSumGuildData *pkCheckSum@<r9>, long double a5@<xmm0>)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp-28h] [bp-28h]@1
  CCheckSumGuildConverter *v8; // [sp+8h] [bp+8h]@1
  CCheckSumGuildData *v9; // [sp+20h] [bp+20h]@1

  v9 = pkCheckSum;
  v8 = this;
  v5 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  CCheckSumBaseConverter::ProcCode(&v8->0, 6, pkCheckSum->m_dwGuildSerial, dDalant);
  CCheckSumGuildData::SetValue(v9, 0, a5);
  CCheckSumBaseConverter::ProcCode(&v8->0, 7, v9->m_dwGuildSerial, dGold);
  CCheckSumGuildData::SetValue(v9, CDT_TRUNK_GOLD, a5);
}
