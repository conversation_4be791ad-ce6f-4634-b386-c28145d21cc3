/*
 * Function: ?pc_UnitSellRequest@CPlayer@@QEAAXEH@Z
 * Address: 0x140103140
 */

void __usercall CPlayer::pc_UnitSellRequest(CPlayer *this@<rcx>, char bySlotIndex@<dl>, int bUseNPCLinkIntem@<r8d>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // xmm0_4@4
  double v7; // xmm0_8@4
  unsigned int v8; // eax@18
  signed __int64 v9; // rax@20
  unsigned int v10; // eax@24
  unsigned int v11; // eax@28
  unsigned int v12; // eax@31
  unsigned int v13; // eax@33
  __int64 v14; // [sp+0h] [bp-D8h]@1
  char v15; // [sp+50h] [bp-88h]@4
  unsigned __int8 v16; // [sp+51h] [bp-87h]@4
  _base_fld *v17; // [sp+58h] [bp-80h]@4
  _UNIT_DB_BASE *v18; // [sp+60h] [bp-78h]@4
  unsigned int dwTotalNonpay; // [sp+68h] [bp-70h]@4
  unsigned int v20; // [sp+6Ch] [bp-6Ch]@4
  unsigned int v21; // [sp+70h] [bp-68h]@4
  int v22; // [sp+74h] [bp-64h]@4
  float v23; // [sp+78h] [bp-60h]@4
  unsigned int v24; // [sp+7Ch] [bp-5Ch]@4
  int nAdd; // [sp+80h] [bp-58h]@4
  float v26; // [sp+84h] [bp-54h]@4
  unsigned __int64 v27; // [sp+88h] [bp-50h]@18
  unsigned __int64 v28; // [sp+90h] [bp-48h]@23
  __int16 v29; // [sp+98h] [bp-40h]@30
  _UNIT_DB_BASE *v30; // [sp+A0h] [bp-38h]@31
  double v31; // [sp+A8h] [bp-30h]@31
  unsigned int v32; // [sp+B0h] [bp-28h]@28
  char *v33; // [sp+B8h] [bp-20h]@31
  unsigned int v34; // [sp+C0h] [bp-18h]@31
  unsigned int dwSumGold; // [sp+C4h] [bp-14h]@33
  CPlayer *p; // [sp+E0h] [bp+8h]@1
  char v37; // [sp+E8h] [bp+10h]@1
  int v38; // [sp+F0h] [bp+18h]@1

  v38 = bUseNPCLinkIntem;
  v37 = bySlotIndex;
  p = this;
  v4 = &v14;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v15 = 0;
  v16 = p->m_Param.m_UnitDB.m_List[(unsigned __int8)bySlotIndex].byFrame;
  v17 = CRecordData::GetRecord(&stru_1799C8BA0, v16);
  v18 = (_UNIT_DB_BASE *)((char *)&p->m_Param.m_UnitDB + 62 * (unsigned __int8)v37);
  dwTotalNonpay = 0;
  v20 = 10000 - eGetTexRate(0);
  v21 = eGetTexRate(0) + 10000;
  eGetTex(0);
  *(float *)&v6 = 1.0 - a4;
  v22 = v6;
  eGetTex(0);
  v23 = *(float *)&v6 + 1.0;
  v24 = 0;
  nAdd = 0;
  *(_QWORD *)&v7 = LODWORD(FLOAT_1_0);
  v26 = FLOAT_1_0;
  if ( p->m_pUserDB )
  {
    if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, p->m_id.wIndex) == 99 )
    {
      v15 = 34;
    }
    else if ( v38 || IsBeNearStore(p, 4) )
    {
      if ( v17 )
      {
        if ( CPlayerDB::GetRaceCode(&p->m_Param) )
        {
          v15 = 1;
        }
        else if ( p->m_pUsingUnit )
        {
          v15 = 2;
        }
        else if ( v16 == 255 )
        {
          v15 = 5;
        }
        else
        {
          v27 = v18->m_List[0].nPullingFee;
          v27 *= v21;
          dwTotalNonpay = v27 / 0x2710;
          v8 = CPlayerDB::GetDalant(&p->m_Param);
          if ( dwTotalNonpay <= v8 )
          {
            v26 = (float)(signed int)v18->m_List[0].dwGauge / (float)*(signed int *)&v17[1].m_strCode[0];
            *(float *)&v7 = (float)((float)*(signed int *)&v17[1].m_strCode[28] * v26) / 2.0;
            v9 = 0i64;
            if ( *(float *)&v7 > 9.223372e18 )
            {
              *(float *)&v7 = *(float *)&v7 - 9.223372e18;
              v9 = 0x7FFFFFFFFFFFFFFFi64;
              if ( *(float *)&v7 > 9.223372e18 )
              {
                *(float *)&v7 = *(float *)&v7 - 9.223372e18;
                v9 = -2i64;
              }
            }
            v28 = v20 * (v9 + (unsigned int)(signed int)ffloor(*(float *)&v7)) + 5000;
            v24 = v28 / 0x2710;
            nAdd = v24 - dwTotalNonpay;
            if ( (signed int)(v24 - dwTotalNonpay) <= 0 )
            {
              if ( nAdd < 0 )
              {
                v32 = -nAdd;
                v11 = CPlayerDB::GetDalant(&p->m_Param);
                if ( v32 > v11 )
                  v15 = 10;
              }
            }
            else
            {
              v10 = CPlayerDB::GetDalant(&p->m_Param);
              if ( !CanAddMoneyForMaxLimMoney(nAdd, v10) )
                v15 = 30;
            }
          }
          else
          {
            v15 = 10;
          }
        }
      }
      else
      {
        v15 = 9;
      }
    }
    else
    {
      v15 = 21;
    }
    v29 = -1;
    if ( !v15 )
    {
      v30 = (_UNIT_DB_BASE *)((char *)&p->m_Param.m_UnitDB + 62 * (unsigned __int8)v37);
      p->m_Param.m_UnitDB.m_List[(unsigned __int8)v37].byFrame = -1;
      v29 = CPlayer::_DeleteUnitKey(p, v37);
      TimeLimitMgr::GetPlayerPenalty(qword_1799CA2D0, p->m_id.wIndex);
      v31 = v7;
      nAdd = (signed int)floor((double)nAdd * v7);
      CPlayer::AlterDalant(p, (double)nAdd);
      CUserDB::Update_UnitDelete(p->m_pUserDB, v37);
      v33 = p->m_szItemHistoryFileName;
      v34 = CPlayerDB::GetGold(&p->m_Param);
      v12 = CPlayerDB::GetDalant(&p->m_Param);
      CMgrAvatorItemHistory::sell_unit(
        &CPlayer::s_MgrItemHistory,
        p->m_ObjID.m_wIndex,
        v37,
        v16,
        v26,
        v24,
        dwTotalNonpay,
        v12,
        v34,
        v33);
      if ( !p->m_byUserDgr )
        eAddDalant(0, nAdd);
    }
    dwSumGold = CPlayerDB::GetGold(&p->m_Param);
    v13 = CPlayerDB::GetDalant(&p->m_Param);
    CPlayer::SendMsg_UnitSellResult(p, v15, v37, v29, v24, dwTotalNonpay, v13, dwSumGold);
  }
}
