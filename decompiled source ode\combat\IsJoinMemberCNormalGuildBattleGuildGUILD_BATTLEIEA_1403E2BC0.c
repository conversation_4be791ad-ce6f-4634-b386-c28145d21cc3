/*
 * Function: ?IsJoinMember@CNormalGuildBattleGuild@GUILD_BATTLE@@IEAA_NK@Z
 * Address: 0x1403E2BC0
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::IsJoinMember(GUILD_BATTLE::CNormalGuildBattleGuild *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@6
  GUILD_BATTLE::CNormalGuildBattleGuild *v7; // [sp+40h] [bp+8h]@1
  unsigned int v8; // [sp+48h] [bp+10h]@1

  v8 = dwSerial;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7->m_pkGuild )
  {
    for ( j = 0; j < 50; ++j )
    {
      if ( !GUILD_BATTLE::CNormalGuildBattleGuildMember::IsEmpty(&v7->m_kMember[j])
        && v8 == GUILD_BATTLE::CNormalGuildBattleGuildMember::GetSerial(&v7->m_kMember[j]) )
      {
        return 1;
      }
    }
    result = 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
