/*
 * Function: ?Kill@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAHKKK@Z
 * Address: 0x1403D4B20
 */

int __fastcall GUILD_BATTLE::CNormalGuildBattleManager::Kill(GUILD_BATTLE::CNormalGuildBattleManager *this, unsigned int dwGuildSerial, unsigned int dwSrcCharacSerial, unsigned int dwDestCharacSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CNormalGuildBattle *v8; // [sp+20h] [bp-18h]@6
  GUILD_BATTLE::CNormalGuildBattleManager *v9; // [sp+40h] [bp+8h]@1
  unsigned int dwSrcCharacSeriala; // [sp+50h] [bp+18h]@1
  unsigned int dwDestCharacSeriala; // [sp+58h] [bp+20h]@1

  dwDestCharacSeriala = dwDestCharacSerial;
  dwSrcCharacSeriala = dwSrcCharacSerial;
  v9 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( dwGuildSerial == -1 )
  {
    result = 141;
  }
  else
  {
    v8 = 0i64;
    v8 = GUILD_BATTLE::CNormalGuildBattleManager::GetBattleByGuildSerial(v9, dwGuildSerial);
    if ( v8 )
      result = GUILD_BATTLE::CNormalGuildBattle::Kill(v8, dwSrcCharacSeriala, dwDestCharacSeriala);
    else
      result = 142;
  }
  return result;
}
