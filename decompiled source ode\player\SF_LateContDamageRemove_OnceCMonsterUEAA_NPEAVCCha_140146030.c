/*
 * Function: ?SF_LateContDamageRemove_Once@CMonster@@UEAA_NPEAVCCharacter@@@Z
 * Address: 0x140146030
 */

char __fastcall CMonster::SF_LateContDamageRemove_Once(CMonster *this, CCharacter *pDstObj)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@14
  __int64 v5; // [sp+0h] [bp-58h]@1
  int v6; // [sp+30h] [bp-28h]@4
  _sf_continous (*v7)[8]; // [sp+38h] [bp-20h]@4
  int j; // [sp+40h] [bp-18h]@4
  _sf_continous (*v9)[8]; // [sp+48h] [bp-10h]@7

  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = -1;
  v7 = 0i64;
  for ( j = 0; j < 8; ++j )
  {
    v9 = (_sf_continous (*)[8])((char *)pDstObj->m_SFCont + 48 * j);
    if ( v9 )
    {
      if ( v6 == -1 )
      {
        v6 = j;
        v7 = v9;
      }
      else if ( (*v9)[0].m_dwStartSec > (*v7)[0].m_dwStartSec )
      {
        v6 = j;
        v7 = v9;
      }
    }
  }
  if ( v6 == -1 )
  {
    result = 0;
  }
  else
  {
    CCharacter::RemoveSFContEffect(pDstObj, 0, v6, 0, 0);
    result = 1;
  }
  return result;
}
