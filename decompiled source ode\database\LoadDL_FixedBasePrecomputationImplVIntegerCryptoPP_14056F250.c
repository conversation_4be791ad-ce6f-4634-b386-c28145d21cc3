/*
 * Function: ?Load@?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@CryptoPP@@UEAAXAEBV?$DL_GroupPrecomputation@VInteger@CryptoPP@@@2@AEAVBufferedTransformation@2@@Z
 * Address: 0x14056F250
 */

void __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::Load(__int64 a1, __int64 a2, struct CryptoPP::BufferedTransformation *a3)
{
  __int64 v3; // rax@3
  __int64 v4; // rax@6
  __int64 v5; // rax@6
  char v6; // [sp+30h] [bp-E8h]@1
  CryptoPP::BERGeneralDecoder v7; // [sp+40h] [bp-D8h]@1
  CryptoPP::Integer v8; // [sp+80h] [bp-98h]@3
  CryptoPP::Integer v9; // [sp+A8h] [bp-70h]@6
  __int64 v10; // [sp+D0h] [bp-48h]@1
  __int64 v11; // [sp+D8h] [bp-40h]@1
  __int64 v12; // [sp+E0h] [bp-38h]@3
  __int64 v13; // [sp+E8h] [bp-30h]@3
  __int64 v14; // [sp+F0h] [bp-28h]@6
  __int64 v15; // [sp+F8h] [bp-20h]@6
  __int64 v16; // [sp+100h] [bp-18h]@6
  __int64 v17; // [sp+120h] [bp+8h]@1
  __int64 v18; // [sp+128h] [bp+10h]@1

  v18 = a2;
  v17 = a1;
  v10 = -2i64;
  CryptoPP::BERSequenceDecoder::BERSequenceDecoder((CryptoPP::BERSequenceDecoder *)&v7, a3, 0x30u);
  CryptoPP::BERDecodeUnsigned<unsigned int>((CryptoPP *)&v7, (int *)&v6, 2u, 1u, 1u);
  v11 = *(_QWORD *)(v17 + 56);
  (*(void (__fastcall **)(signed __int64, CryptoPP::BERGeneralDecoder *))(v11 + 8))(v17 + 56, &v7);
  *(_DWORD *)(v17 + 48) = CryptoPP::Integer::BitCount((CryptoPP::Integer *)(v17 + 56)) - 1;
  std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::clear(v17 + 96);
  while ( !CryptoPP::BERGeneralDecoder::EndReached(&v7) )
  {
    LODWORD(v3) = (*(int (__fastcall **)(__int64, CryptoPP::Integer *, CryptoPP::BERGeneralDecoder *))(*(_QWORD *)v18 + 32i64))(
                    v18,
                    &v8,
                    &v7);
    v12 = v3;
    v13 = v3;
    std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::push_back(v17 + 96, v3);
    CryptoPP::Integer::~Integer(&v8);
  }
  if ( !std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::empty(v17 + 96)
    && (unsigned __int8)(**(int (__fastcall ***)(_QWORD))v18)(v18) )
  {
    LODWORD(v4) = std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::operator[](v17 + 96, 0i64);
    v14 = *(_QWORD *)v18;
    LODWORD(v5) = (*(int (__fastcall **)(__int64, CryptoPP::Integer *, __int64))(v14 + 16))(v18, &v9, v4);
    v15 = v5;
    v16 = v5;
    CryptoPP::Integer::operator=(v17 + 8);
    CryptoPP::Integer::~Integer(&v9);
  }
  CryptoPP::BERGeneralDecoder::MessageEnd(&v7);
  CryptoPP::BERSequenceDecoder::~BERSequenceDecoder((CryptoPP::BERSequenceDecoder *)&v7);
}
