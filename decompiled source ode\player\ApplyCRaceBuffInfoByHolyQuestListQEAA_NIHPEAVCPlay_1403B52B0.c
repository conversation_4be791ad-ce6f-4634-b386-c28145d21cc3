/*
 * Function: ?Apply@CRaceBuffInfoByHolyQuestList@@QEAA_NIHPEAVCPlayer@@@Z
 * Address: 0x1403B52B0
 */

bool __fastcall CRaceBuffInfoByHolyQuestList::Apply(CRaceBuffInfoByHolyQuestList *this, unsigned int uiContinueCnt, int iResultType, CPlayer *pkDest)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v6; // rax@4
  bool result; // al@5
  CRaceBuffInfoByHolyQuestfGroup **v8; // rax@6
  __int64 v9; // [sp+0h] [bp-38h]@1
  unsigned __int64 v10; // [sp+20h] [bp-18h]@4
  CRaceBuffInfoByHolyQuestList *v11; // [sp+40h] [bp+8h]@1
  unsigned int _Pos; // [sp+48h] [bp+10h]@1
  int iResultTypea; // [sp+50h] [bp+18h]@1
  CPlayer *pkDesta; // [sp+58h] [bp+20h]@1

  pkDesta = pkDest;
  iResultTypea = iResultType;
  _Pos = uiContinueCnt;
  v11 = this;
  v4 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v10 = uiContinueCnt;
  v6 = std::vector<CRaceBuffInfoByHolyQuestfGroup *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>::size(&v11->m_vecInfo);
  if ( v10 < v6 )
  {
    v8 = std::vector<CRaceBuffInfoByHolyQuestfGroup *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>::operator[](
           &v11->m_vecInfo,
           _Pos);
    result = CRaceBuffInfoByHolyQuestfGroup::Apply(*v8, iResultTypea, pkDesta);
  }
  else
  {
    result = 0;
  }
  return result;
}
