/*
 * Function: _CUnmannedTraderTaxRateManager::_CUnmannedTraderTaxRateManager_::_1_::dtor$0
 * Address: 0x14038EDF0
 */

void __fastcall CUnmannedTraderTaxRateManager::_CUnmannedTraderTaxRateManager_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::~vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>((std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *)(*(_QWORD *)(a2 + 64) + 8i64));
}
