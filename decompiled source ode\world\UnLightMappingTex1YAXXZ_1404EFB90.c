/*
 * Function: ?UnLightMappingTex1@@YAXXZ
 * Address: 0x1404EFB90
 */

void UnLightMappingTex1(void)
{
  struct IDirect3DDevice8 *v0; // rbx@1

  v0 = GetD3dDevice();
  BlendOff();
  ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64))v0->vfptr[21].QueryInterface)(
    v0,
    0i64,
    11i64);
  ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64))v0->vfptr[16].Release)(
    v0,
    24i64,
    8i64);
  MultiTexOn();
}
