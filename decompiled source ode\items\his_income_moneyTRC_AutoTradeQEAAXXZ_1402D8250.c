/*
 * Function: ?his_income_money@TRC_AutoTrade@@QEAAXXZ
 * Address: 0x1402D8250
 */

void __usercall TRC_AutoTrade::his_income_money(TRC_AutoTrade *this@<rcx>, int a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-48h]@1
  unsigned int v6; // [sp+20h] [bp-28h]@4
  long double v7; // [sp+28h] [bp-20h]@4
  unsigned int v8; // [sp+30h] [bp-18h]@4
  TRC_AutoTrade *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = CPvpUserAndGuildRankingSystem::Instance();
  v8 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v4, v9->m_byRace, 0);
  ControllerTaxRate::getCurTaxRate(&v9->m_Controller);
  v7 = v9->m_dIncomeMoney;
  v6 = v9->m_dwTrade;
  CLogFile::Write(&v9->m_serviceLog, "PS:%d tax:%.2f tcnt:%d money:%.0f", v8, a2);
  *(_QWORD *)&v9->m_dIncomeMoney = 0i64;
  v9->m_dwTrade = 0;
}
