/*
 * Function: ?Load@CGuildBattleSchedule@GUILD_BATTLE@@QEAA_N_NKE_JG@Z
 * Address: 0x1403D9F60
 */

char __fastcall GUILD_BATTLE::CGuildBattleSchedule::Load(GUILD_BATTLE::CGuildBattleSchedule *this, bool bToday, unsigned int dwScheduleID, char ucState, __int64 tTime, unsigned __int16 wTumeMin)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char v8; // al@6
  __int64 *v9; // rax@11
  ATL::CTimeSpan *v10; // rax@11
  ATL::CTimeSpan *v11; // rax@11
  ATL::CTimeSpan *v12; // rax@11
  ATL::CTimeSpan *v13; // rax@11
  ATL::CTimeSpan *v14; // rax@11
  int v15; // eax@16
  int v16; // eax@17
  int v17; // eax@18
  __int64 *v18; // rax@20
  __int64 v19; // [sp+0h] [bp-C8h]@1
  unsigned int v20; // [sp+30h] [bp-98h]@4
  ATL::CTime time; // [sp+48h] [bp-80h]@11
  int v22; // [sp+54h] [bp-74h]@16
  ATL::CTime v23; // [sp+58h] [bp-70h]@11
  ATL::CTimeSpan v24; // [sp+60h] [bp-68h]@11
  ATL::CTimeSpan v25; // [sp+68h] [bp-60h]@11
  ATL::CTimeSpan result; // [sp+70h] [bp-58h]@11
  ATL::CTimeSpan v27; // [sp+78h] [bp-50h]@11
  ATL::CTimeSpan v28; // [sp+80h] [bp-48h]@11
  ATL::CTime v29; // [sp+88h] [bp-40h]@11
  ATL::CTimeSpan v30; // [sp+90h] [bp-38h]@20
  ATL::CTimeSpan *v31; // [sp+98h] [bp-30h]@11
  ATL::CTimeSpan *v32; // [sp+A0h] [bp-28h]@11
  int v33; // [sp+A8h] [bp-20h]@14
  int v34; // [sp+ACh] [bp-1Ch]@16
  int v35; // [sp+B0h] [bp-18h]@17
  GUILD_BATTLE::CGuildBattleSchedule *v36; // [sp+D0h] [bp+8h]@1
  bool v37; // [sp+D8h] [bp+10h]@1

  v37 = bToday;
  v36 = this;
  v6 = &v19;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v20 = 1320;
  if ( (signed int)(unsigned __int8)ucState < 4 && v20 >= wTumeMin )
  {
    if ( dwScheduleID == v36->m_dwScheduleID )
    {
      if ( (signed int)wTumeMin > 30 )
        wTumeMin = 30;
      v36->m_eState = (unsigned __int8)ucState;
      ATL::CTime::CTime(&v23, tTime);
      v36->m_kBattleStartTime.m_time = *v9;
      ATL::CTimeSpan::CTimeSpan(&v27, 0, 0, 1, 0);
      v31 = v10;
      ATL::CTimeSpan::CTimeSpan(&v25, 0, 0, 4, 0);
      v32 = v11;
      ATL::CTimeSpan::CTimeSpan(&v24, 0, 0, 5, 0);
      v13 = ATL::CTimeSpan::operator+(v12, &result, (ATL::CTimeSpan)v32->m_timeSpan);
      v14 = ATL::CTimeSpan::operator+(v13, &v28, (ATL::CTimeSpan)v31->m_timeSpan);
      v36->m_kNextStartTime = (ATL::CTime)ATL::CTime::operator-(
                                            &v36->m_kBattleStartTime,
                                            &v29,
                                            (ATL::CTimeSpan)v14->m_timeSpan)->m_time;
      ATL::CTime::GetTickCount(&time);
      if ( ATL::CTime::operator<(&v36->m_kNextStartTime, time) )
      {
        v8 = 0;
      }
      else
      {
        if ( v37 )
          v33 = GetCurDay();
        else
          v33 = GetNextDay();
        v22 = v33;
        v34 = ATL::CTime::GetYear(&time);
        v15 = ATL::CTime::GetYear(&v36->m_kNextStartTime);
        if ( v34 == v15
          && (v35 = ATL::CTime::GetMonth(&time), v16 = ATL::CTime::GetMonth(&v36->m_kNextStartTime), v35 == v16)
          && (v17 = ATL::CTime::GetDay(&v36->m_kNextStartTime), v22 == v17) )
        {
          ATL::CTimeSpan::CTimeSpan(&v30, 0, 0, wTumeMin, 0);
          v36->m_kBattleTime.m_timeSpan = *v18;
          v8 = 1;
        }
        else
        {
          v8 = 0;
        }
      }
    }
    else
    {
      v8 = 0;
    }
  }
  else
  {
    v8 = 0;
  }
  return v8;
}
