/*
 * Function: _CUnmannedTraderScheduler::FindItem_::_1_::dtor$3
 * Address: 0x1403949D0
 */

void __fastcall CUnmannedTraderScheduler::FindItem_::_1_::dtor_3(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 128) & 1 )
  {
    *(_DWORD *)(a2 + 128) &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(*(std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > **)(a2 + 200));
  }
}
