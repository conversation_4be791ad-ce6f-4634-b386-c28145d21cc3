/*
 * Function: ?Load_Event_INI@CashItemRemoteStore@@QEAAXPEAU_cash_event_ini@@PEAU_FILETIME@@PEAD@Z
 * Address: 0x1402F99B0
 */

void __fastcall CashItemRemoteStore::Load_Event_INI(CashItemRemoteStore *this, _cash_event_ini *pIni, _FILETIME *pft, char *pEventType)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-D8h]@1
  UINT v7; // [sp+20h] [bp-B8h]@8
  int Dst; // [sp+38h] [bp-A0h]@28
  int v9; // [sp+3Ch] [bp-9Ch]@28
  int v10; // [sp+40h] [bp-98h]@28
  int v11; // [sp+44h] [bp-94h]@28
  int v12; // [sp+48h] [bp-90h]@28
  int v13; // [sp+4Ch] [bp-8Ch]@28
  int v14; // [sp+58h] [bp-80h]@28
  _FILETIME ftWrite; // [sp+78h] [bp-60h]@8
  _FILETIME v16; // [sp+98h] [bp-40h]@10
  _FILETIME v17; // [sp+B8h] [bp-20h]@64
  _cash_event_ini *v18; // [sp+E8h] [bp+10h]@1
  _FILETIME *v19; // [sp+F0h] [bp+18h]@1
  const char *Str1; // [sp+F8h] [bp+20h]@1

  Str1 = pEventType;
  v19 = pft;
  v18 = pIni;
  v4 = &v6;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pIni )
  {
    if ( pft )
    {
      if ( strcmp_0(pEventType, "NULL") )
      {
        v7 = 0;
        v18->m_bUseCashEvent = 0;
        v18->m_EventTime[0] = 0;
        v18->m_EventTime[1] = 0;
        v18->m_bRepeat = 0;
        if ( GetLastWriteFileTime("./Initialize/cash_discount_limitsale.ini", &ftWrite) )
        {
          v19->dwHighDateTime = ftWrite.dwHighDateTime;
          v19->dwLowDateTime = ftWrite.dwLowDateTime;
          if ( !strcmp_0(Str1, "Limit Sale") )
          {
            if ( GetLastWriteFileTime("./Initialize/cash_discount_limitsale.ini", &v16) )
            {
              v19->dwHighDateTime = v16.dwHighDateTime;
              v19->dwLowDateTime = v16.dwLowDateTime;
              v7 = GetPrivateProfileIntA(Str1, "USE", 1, "./Initialize/cash_discount_limitsale.ini");
              if ( v7 == 1 )
              {
                v18->m_bUseCashEvent = 0;
              }
              else
              {
                v18->m_bUseCashEvent = 1;
                v18->m_bRepeat = GetPrivateProfileIntA(Str1, "REPEAT", 0, "./Initialize/cash_discount_limitsale.ini");
                v7 = GetPrivateProfileIntA(Str1, "REPEAT_DAY", 0, "./Initialize/cash_discount_limitsale.ini");
                if ( (signed int)v7 > 0 )
                  v18->m_byRepeatDay = v7;
                else
                  v18->m_bRepeat = 0;
                v7 = GetPrivateProfileIntA(Str1, "BEGIN_YEAR", 0, "./Initialize/cash_discount_limitsale.ini");
                if ( v7 )
                {
                  v18->m_wYear[0] = v7;
                  v7 = GetPrivateProfileIntA(Str1, "BEGIN_MONTH", 0, "./Initialize/cash_discount_limitsale.ini");
                  if ( v7 )
                  {
                    v18->m_byMonth[0] = v7;
                    v7 = GetPrivateProfileIntA(Str1, "BEGIN_DAY", 0, "./Initialize/cash_discount_limitsale.ini");
                    if ( v7 )
                    {
                      v18->m_byDay[0] = v7;
                      v7 = GetPrivateProfileIntA(Str1, "BEGIN_HOUR", 0, "./Initialize/cash_discount_limitsale.ini");
                      if ( (v7 & 0x80000000) == 0 && (signed int)v7 <= 23 )
                      {
                        v18->m_byHour[0] = v7;
                        v7 = GetPrivateProfileIntA(Str1, "BEGIN_MINUTE", 0, "./Initialize/cash_discount_limitsale.ini");
                        if ( (v7 & 0x80000000) == 0 && (signed int)v7 <= 59 )
                        {
                          v18->m_byMinute[0] = v7;
                          memset_0(&Dst, 0, 0x24ui64);
                          v13 = v18->m_wYear[0] - 1900;
                          v12 = v18->m_byMonth[0] - 1;
                          v11 = v18->m_byDay[0];
                          v10 = v18->m_byHour[0];
                          v9 = v18->m_byMinute[0];
                          Dst = 0;
                          v14 = -1;
                          v18->m_EventTime[0] = _mktime32((struct tm *)&Dst);
                          if ( v18->m_EventTime[0] == -1 )
                          {
                            v18->m_bUseCashEvent = 0;
                          }
                          else
                          {
                            v7 = GetPrivateProfileIntA(Str1, "END_YEAR", 0, "./Initialize/cash_discount_limitsale.ini");
                            if ( v7 )
                            {
                              v18->m_wYear[1] = v7;
                              v7 = GetPrivateProfileIntA(
                                     Str1,
                                     "END_MONTH",
                                     0,
                                     "./Initialize/cash_discount_limitsale.ini");
                              if ( v7 )
                              {
                                v18->m_byMonth[1] = v7;
                                v7 = GetPrivateProfileIntA(
                                       Str1,
                                       "END_DAY",
                                       0,
                                       "./Initialize/cash_discount_limitsale.ini");
                                if ( v7 )
                                {
                                  v18->m_byDay[1] = v7;
                                  v7 = GetPrivateProfileIntA(
                                         Str1,
                                         "END_HOUR",
                                         0,
                                         "./Initialize/cash_discount_limitsale.ini");
                                  if ( (v7 & 0x80000000) == 0 && (signed int)v7 <= 23 )
                                  {
                                    v18->m_byHour[1] = v7;
                                    v7 = GetPrivateProfileIntA(
                                           Str1,
                                           "END_MINUTE",
                                           0,
                                           "./Initialize/cash_discount_limitsale.ini");
                                    if ( (v7 & 0x80000000) == 0 && (signed int)v7 <= 59 )
                                    {
                                      v18->m_byMinute[1] = v7;
                                      memset_0(&Dst, 0, 0x24ui64);
                                      v13 = v18->m_wYear[1] - 1900;
                                      v12 = v18->m_byMonth[1] - 1;
                                      v11 = v18->m_byDay[1];
                                      v10 = v18->m_byHour[1];
                                      v9 = v18->m_byMinute[1];
                                      Dst = 0;
                                      v14 = -1;
                                      v18->m_EventTime[1] = _mktime32((struct tm *)&Dst);
                                      if ( v18->m_EventTime[1] == -1 )
                                      {
                                        v18->m_bUseCashEvent = 0;
                                      }
                                      else
                                      {
                                        v7 = GetPrivateProfileIntA(
                                               Str1,
                                               "EXPIRE_YEAR",
                                               0,
                                               "./Initialize/cash_discount_limitsale.ini");
                                        if ( (signed int)v7 > 0 )
                                          v18->m_wYear[2] = v7;
                                        else
                                          v18->m_bRepeat = 0;
                                        v7 = GetPrivateProfileIntA(
                                               Str1,
                                               "EXPIRE_MONTH",
                                               0,
                                               "./Initialize/cash_discount_limitsale.ini");
                                        if ( (signed int)v7 > 0 )
                                          v18->m_byMonth[2] = v7;
                                        else
                                          v18->m_bRepeat = 0;
                                        v7 = GetPrivateProfileIntA(
                                               Str1,
                                               "EXPIRE_DAY",
                                               0,
                                               "./Initialize/cash_discount_limitsale.ini");
                                        if ( (signed int)v7 > 0 )
                                          v18->m_byDay[2] = v7;
                                        else
                                          v18->m_bRepeat = 0;
                                        v7 = GetPrivateProfileIntA(
                                               Str1,
                                               "EXPIRE_HOUR",
                                               0,
                                               "./Initialize/cash_discount_limitsale.ini");
                                        if ( (v7 & 0x80000000) == 0 && (signed int)v7 <= 23 )
                                          v18->m_byHour[2] = v7;
                                        else
                                          v18->m_bRepeat = 0;
                                        v7 = GetPrivateProfileIntA(
                                               Str1,
                                               "EXPIRE_MINUTE",
                                               0,
                                               "./Initialize/cash_discount_limitsale.ini");
                                        if ( (v7 & 0x80000000) == 0 && (signed int)v7 <= 59 )
                                          v18->m_byMinute[2] = v7;
                                        else
                                          v18->m_bRepeat = 0;
                                        memset_0(&Dst, 0, 0x24ui64);
                                        v13 = v18->m_wYear[2] - 1900;
                                        v12 = v18->m_byMonth[2] - 1;
                                        v11 = v18->m_byDay[2];
                                        v10 = v18->m_byHour[2];
                                        v9 = v18->m_byMinute[2];
                                        Dst = 0;
                                        v14 = -1;
                                        v18->m_EventTime[2] = _mktime32((struct tm *)&Dst);
                                        if ( v18->m_EventTime[2] == -1 )
                                          v18->m_bRepeat = 0;
                                      }
                                    }
                                    else
                                    {
                                      v18->m_bUseCashEvent = 0;
                                    }
                                  }
                                  else
                                  {
                                    v18->m_bUseCashEvent = 0;
                                  }
                                }
                                else
                                {
                                  v18->m_bUseCashEvent = 0;
                                }
                              }
                              else
                              {
                                v18->m_bUseCashEvent = 0;
                              }
                            }
                            else
                            {
                              v18->m_bUseCashEvent = 0;
                            }
                          }
                        }
                        else
                        {
                          v18->m_bUseCashEvent = 0;
                        }
                      }
                      else
                      {
                        v18->m_bUseCashEvent = 0;
                      }
                    }
                    else
                    {
                      v18->m_bUseCashEvent = 0;
                    }
                  }
                  else
                  {
                    v18->m_bUseCashEvent = 0;
                  }
                }
                else
                {
                  v18->m_bUseCashEvent = 0;
                }
              }
            }
          }
          else
          {
            if ( !GetLastWriteFileTime("./initialize/cash_discount.ini", &v17) )
              return;
            v19->dwHighDateTime = v17.dwHighDateTime;
            v19->dwLowDateTime = v17.dwLowDateTime;
            v7 = GetPrivateProfileIntA(Str1, "USE", 1, "./initialize/cash_discount.ini");
            if ( v7 == 1 )
            {
              v18->m_bUseCashEvent = 0;
              return;
            }
            v18->m_bUseCashEvent = 1;
            v18->m_bRepeat = GetPrivateProfileIntA(Str1, "REPEAT", 0, "./initialize/cash_discount.ini");
            v7 = GetPrivateProfileIntA(Str1, "REPEAT_DAY", 0, "./initialize/cash_discount.ini");
            if ( (signed int)v7 > 0 )
              v18->m_byRepeatDay = v7;
            else
              v18->m_bRepeat = 0;
            v7 = GetPrivateProfileIntA(Str1, "BEGIN_YEAR", 0, "./initialize/cash_discount.ini");
            if ( !v7 )
            {
              v18->m_bUseCashEvent = 0;
              return;
            }
            v18->m_wYear[0] = v7;
            v7 = GetPrivateProfileIntA(Str1, "BEGIN_MONTH", 0, "./initialize/cash_discount.ini");
            if ( !v7 )
            {
              v18->m_bUseCashEvent = 0;
              return;
            }
            v18->m_byMonth[0] = v7;
            v7 = GetPrivateProfileIntA(Str1, "BEGIN_DAY", 0, "./initialize/cash_discount.ini");
            if ( !v7 )
            {
              v18->m_bUseCashEvent = 0;
              return;
            }
            v18->m_byDay[0] = v7;
            v7 = GetPrivateProfileIntA(Str1, "BEGIN_HOUR", 0, "./initialize/cash_discount.ini");
            if ( (v7 & 0x80000000) != 0 || (signed int)v7 > 23 )
            {
              v18->m_bUseCashEvent = 0;
              return;
            }
            v18->m_byHour[0] = v7;
            v7 = GetPrivateProfileIntA(Str1, "BEGIN_MINUTE", 0, "./initialize/cash_discount.ini");
            if ( (v7 & 0x80000000) != 0 || (signed int)v7 > 59 )
            {
              v18->m_bUseCashEvent = 0;
              return;
            }
            v18->m_byMinute[0] = v7;
            memset_0(&Dst, 0, 0x24ui64);
            v13 = v18->m_wYear[0] - 1900;
            v12 = v18->m_byMonth[0] - 1;
            v11 = v18->m_byDay[0];
            v10 = v18->m_byHour[0];
            v9 = v18->m_byMinute[0];
            Dst = 0;
            v14 = -1;
            v18->m_EventTime[0] = _mktime32((struct tm *)&Dst);
            if ( v18->m_EventTime[0] == -1 )
            {
              v18->m_bUseCashEvent = 0;
              return;
            }
            v7 = GetPrivateProfileIntA(Str1, "END_YEAR", 0, "./initialize/cash_discount.ini");
            if ( !v7 )
            {
              v18->m_bUseCashEvent = 0;
              return;
            }
            v18->m_wYear[1] = v7;
            v7 = GetPrivateProfileIntA(Str1, "END_MONTH", 0, "./initialize/cash_discount.ini");
            if ( !v7 )
            {
              v18->m_bUseCashEvent = 0;
              return;
            }
            v18->m_byMonth[1] = v7;
            v7 = GetPrivateProfileIntA(Str1, "END_DAY", 0, "./initialize/cash_discount.ini");
            if ( !v7 )
            {
              v18->m_bUseCashEvent = 0;
              return;
            }
            v18->m_byDay[1] = v7;
            v7 = GetPrivateProfileIntA(Str1, "END_HOUR", 0, "./initialize/cash_discount.ini");
            if ( (v7 & 0x80000000) != 0 || (signed int)v7 > 23 )
            {
              v18->m_bUseCashEvent = 0;
              return;
            }
            v18->m_byHour[1] = v7;
            v7 = GetPrivateProfileIntA(Str1, "END_MINUTE", 0, "./initialize/cash_discount.ini");
            if ( (v7 & 0x80000000) != 0 || (signed int)v7 > 59 )
            {
              v18->m_bUseCashEvent = 0;
              return;
            }
            v18->m_byMinute[1] = v7;
            if ( !strcmp_0(Str1, "Set") )
            {
              v7 = GetPrivateProfileIntA(Str1, "SetA_Discout", 0, "./initialize/cash_discount.ini");
              if ( (v7 & 0x80000000) != 0 || (signed int)v7 >= 100 )
              {
                v18->m_bUseCashEvent = 0;
                return;
              }
              v18->m_byDiscout[0] = v7;
              v7 = GetPrivateProfileIntA(Str1, "SetB_Discout", 0, "./initialize/cash_discount.ini");
              if ( (v7 & 0x80000000) != 0 || (signed int)v7 >= 100 )
              {
                v18->m_bUseCashEvent = 0;
                return;
              }
              v18->m_byDiscout[1] = v7;
              v7 = GetPrivateProfileIntA(Str1, "SetC_Discout", 0, "./initialize/cash_discount.ini");
              if ( (v7 & 0x80000000) != 0 || (signed int)v7 >= 100 )
              {
                v18->m_bUseCashEvent = 0;
                return;
              }
              v18->m_byDiscout[2] = v7;
              v7 = GetPrivateProfileIntA(Str1, "SetAll_Discout", 0, "./initialize/cash_discount.ini");
              if ( (v7 & 0x80000000) != 0 || (signed int)v7 >= 100 )
              {
                v18->m_bUseCashEvent = 0;
                return;
              }
              v18->m_byDiscout[3] = v7;
            }
            memset_0(&Dst, 0, 0x24ui64);
            v13 = v18->m_wYear[1] - 1900;
            v12 = v18->m_byMonth[1] - 1;
            v11 = v18->m_byDay[1];
            v10 = v18->m_byHour[1];
            v9 = v18->m_byMinute[1];
            Dst = 0;
            v14 = -1;
            v18->m_EventTime[1] = _mktime32((struct tm *)&Dst);
            if ( v18->m_EventTime[1] == -1 )
            {
              v18->m_bUseCashEvent = 0;
            }
            else
            {
              v7 = GetPrivateProfileIntA(Str1, "EXPIRE_YEAR", 0, "./initialize/cash_discount.ini");
              if ( (signed int)v7 > 0 )
                v18->m_wYear[2] = v7;
              else
                v18->m_bRepeat = 0;
              v7 = GetPrivateProfileIntA(Str1, "EXPIRE_MONTH", 0, "./initialize/cash_discount.ini");
              if ( (signed int)v7 > 0 )
                v18->m_byMonth[2] = v7;
              else
                v18->m_bRepeat = 0;
              v7 = GetPrivateProfileIntA(Str1, "EXPIRE_DAY", 0, "./initialize/cash_discount.ini");
              if ( (signed int)v7 > 0 )
                v18->m_byDay[2] = v7;
              else
                v18->m_bRepeat = 0;
              v7 = GetPrivateProfileIntA(Str1, "EXPIRE_HOUR", 0, "./initialize/cash_discount.ini");
              if ( (v7 & 0x80000000) == 0 && (signed int)v7 <= 23 )
                v18->m_byHour[2] = v7;
              else
                v18->m_bRepeat = 0;
              v7 = GetPrivateProfileIntA(Str1, "EXPIRE_MINUTE", 0, "./initialize/cash_discount.ini");
              if ( (v7 & 0x80000000) == 0 && (signed int)v7 <= 59 )
                v18->m_byMinute[2] = v7;
              else
                v18->m_bRepeat = 0;
              memset_0(&Dst, 0, 0x24ui64);
              v13 = v18->m_wYear[2] - 1900;
              v12 = v18->m_byMonth[2] - 1;
              v11 = v18->m_byDay[2];
              v10 = v18->m_byHour[2];
              v9 = v18->m_byMinute[2];
              Dst = 0;
              v14 = -1;
              v18->m_EventTime[2] = _mktime32((struct tm *)&Dst);
              if ( v18->m_EventTime[2] == -1 )
                v18->m_bRepeat = 0;
            }
          }
        }
      }
    }
  }
}
