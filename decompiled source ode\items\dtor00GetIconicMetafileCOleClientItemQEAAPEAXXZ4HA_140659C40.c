/*
 * Function: ?dtor$0@?0??GetIconicMetafile@COleClientItem@@QEAAPEAXXZ@4HA_9
 * Address: 0x140659C40
 */

void __fastcall `COleClientItem::GetIconicMetafile'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  std::_Deque_iterator<unsigned int,std::allocator<unsigned int>,0>::~_Deque_iterator<unsigned int,std::allocator<unsigned int>,0>((std::_Ranit<unsigned int,__int64,unsigned int const *,unsigned int const &> *)(a2 + 424));
}
