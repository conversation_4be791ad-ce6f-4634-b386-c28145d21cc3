/*
 * Function: ??$_Construct@UMessageRange@MeterFilter@CryptoPP@@U123@@std@@YAXPEAUMessageRange@MeterFilter@CryptoPP@@AEBU123@@Z
 * Address: 0x1406016C0
 */

char *__fastcall std::_Construct<CryptoPP::MeterFilter::MessageRange,CryptoPP::MeterFilter::MessageRange>(void *a1, const void *a2)
{
  char *result; // rax@1
  char v3; // [sp+30h] [bp-38h]@2
  const void *v4; // [sp+78h] [bp+10h]@1

  v4 = a2;
  result = (char *)operator new(0x18ui64, a1);
  if ( result )
  {
    qmemcpy(&v3, v4, 0x18ui64);
    qmemcpy(result, &v3, 0x18ui64);
  }
  return result;
}
