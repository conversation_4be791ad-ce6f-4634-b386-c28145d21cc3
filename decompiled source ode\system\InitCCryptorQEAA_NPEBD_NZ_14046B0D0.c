/*
 * Function: ?Init@CCryptor@@QEAA_NPEBD_N@Z
 * Address: 0x14046B0D0
 */

bool __fastcall CCryptor::Init(CCryptor *this, const char *sz<PERSON><PERSON><PERSON>ath, bool bUseCreate)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // rax@5
  bool result; // al@8
  __int64 v7; // rax@10
  __int64 v8; // rax@16
  __int64 v9; // rax@20
  __int64 v10; // [sp+0h] [bp-98h]@1
  CryptoPP::AutoSeededRandomPool *v11; // [sp+20h] [bp-78h]@7
  CryptoPP::AutoSeededRandomPool *v12; // [sp+28h] [bp-70h]@4
  CryptoPP::SHA256 *v13; // [sp+30h] [bp-68h]@12
  CryptoPP::SHA256 *v14; // [sp+38h] [bp-60h]@9
  CCryptParam *v15; // [sp+40h] [bp-58h]@18
  CCryptParamCrete *v16; // [sp+48h] [bp-50h]@15
  CCryptParam *v17; // [sp+50h] [bp-48h]@22
  CCryptParam *v18; // [sp+58h] [bp-40h]@19
  __int64 v19; // [sp+60h] [bp-38h]@4
  CryptoPP::AutoSeededRandomPool *v20; // [sp+68h] [bp-30h]@5
  CryptoPP::SHA256 *v21; // [sp+70h] [bp-28h]@10
  CCryptParam *v22; // [sp+78h] [bp-20h]@16
  CCryptParam *v23; // [sp+80h] [bp-18h]@18
  CCryptParam *v24; // [sp+88h] [bp-10h]@20
  CCryptor *v25; // [sp+A0h] [bp+8h]@1
  char *szKeyPatha; // [sp+A8h] [bp+10h]@1
  bool v27; // [sp+B0h] [bp+18h]@1

  v27 = bUseCreate;
  szKeyPatha = (char *)szKeyPath;
  v25 = this;
  v3 = &v10;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v19 = -2i64;
  v12 = (CryptoPP::AutoSeededRandomPool *)operator new(0x78ui64);
  if ( v12 )
  {
    CryptoPP::AutoSeededRandomPool::AutoSeededRandomPool(v12, 0, 0x20u);
    v20 = (CryptoPP::AutoSeededRandomPool *)v5;
  }
  else
  {
    v20 = 0i64;
  }
  v11 = v20;
  v25->m_prng = v20;
  if ( v25->m_prng )
  {
    v14 = (CryptoPP::SHA256 *)operator new(0xC0ui64);
    if ( v14 )
    {
      CryptoPP::SHA256::SHA256(v14);
      v21 = (CryptoPP::SHA256 *)v7;
    }
    else
    {
      v21 = 0i64;
    }
    v13 = v21;
    v25->m_pHash = v21;
    if ( v25->m_pHash )
    {
      if ( v27 )
      {
        v16 = (CCryptParamCrete *)operator new(0xA60ui64);
        if ( v16 )
        {
          CCryptParamCrete::CCryptParamCrete(v16, v25->m_prng);
          v22 = (CCryptParam *)v8;
        }
        else
        {
          v22 = 0i64;
        }
        v15 = v22;
        v23 = v22;
      }
      else
      {
        v18 = (CCryptParam *)operator new(0x9B0ui64);
        if ( v18 )
        {
          CCryptParam::CCryptParam(v18, v25->m_prng);
          v24 = (CCryptParam *)v9;
        }
        else
        {
          v24 = 0i64;
        }
        v17 = v24;
        v23 = v24;
      }
      v25->m_pkParam = v23;
      if ( v25->m_pkParam )
        result = CCryptParam::Load(v25->m_pkParam, szKeyPatha) != 0;
      else
        result = 0;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
