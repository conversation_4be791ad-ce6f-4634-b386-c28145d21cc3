/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAVCUnmannedTraderSchedule@@PEAV1@V?$allocator@VCUnmannedTraderSchedule@@@std@@@stdext@@YAPEAVCUnmannedTraderSchedule@@PEAV1@00AEAV?$allocator@VCUnmannedTraderSchedule@@@std@@@Z
 * Address: 0x14000F060
 */

CUnmannedTraderSchedule *__fastcall stdext::_Unchecked_uninitialized_move<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *,std::allocator<CUnmannedTraderSchedule>>(CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last, CUnmannedTraderSchedule *_Dest, std::allocator<CUnmannedTraderSchedule> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *,std::allocator<CUnmannedTraderSchedule>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
