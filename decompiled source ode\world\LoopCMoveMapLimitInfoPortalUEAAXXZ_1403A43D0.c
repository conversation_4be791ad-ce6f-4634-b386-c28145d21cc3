/*
 * Function: ?Loop@CMoveMapLimitInfoPortal@@UEAAXXZ
 * Address: 0x1403A43D0
 */

void __fastcall CMoveMapLimitInfoPortal::Loop(CMoveMapLimitInfoPortal *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  CMoveMapLimitInfoPortal::FORCE_MOVE_HQ_STATE v4; // [sp+20h] [bp-18h]@4
  CMoveMapLimitInfoPortal *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = v5->m_eNotifyForceMoveHQState;
  switch ( v4 )
  {
    case 1:
      if ( CMyTimer::CountingTimer(v5->m_pkNotifyForceMoveHQTimer) )
      {
        CMyTimer::StopTimer(v5->m_pkNotifyForceMoveHQTimer);
        v5->m_eNotifyForceMoveHQState = 2;
      }
      break;
    case 2:
      CMoveMapLimitInfoPortal::SubProcNotifyForceMoveHQ(v5);
      break;
    case 3:
      if ( CMyTimer::CountingTimer(v5->m_pkNotifyForceMoveHQTimer) )
      {
        CMyTimer::StopTimer(v5->m_pkNotifyForceMoveHQTimer);
        v5->m_eNotifyForceMoveHQState = 4;
      }
      break;
    case 4:
      CMoveMapLimitInfoPortal::SubProcForceMoveHQ(v5);
      break;
  }
}
