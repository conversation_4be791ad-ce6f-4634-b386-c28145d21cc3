/*
 * Function: j_??F?$_Deque_const_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEAAAEAV01@XZ
 * Address: 0x14000AF42
 */

std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator--(std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this)
{
  return std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator--(this);
}
