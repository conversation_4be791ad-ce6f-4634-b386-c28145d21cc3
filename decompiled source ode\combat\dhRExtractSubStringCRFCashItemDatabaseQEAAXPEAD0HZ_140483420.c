/*
 * Function: ?dhRExtractSubString@CRFCashItemDatabase@@QEAAXPEAD0H@Z
 * Address: 0x140483420
 */

void __fastcall CRFCashItemDatabase::dhRExtractSubString(CRFCashItemDatabase *this, char *szSub, char *szFull, int n)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  size_t v6; // rax@8
  __int64 v7; // [sp+0h] [bp-48h]@1
  int v8; // [sp+20h] [bp-28h]@5
  int v9; // [sp+24h] [bp-24h]@5
  void *Src; // [sp+28h] [bp-20h]@5
  int j; // [sp+30h] [bp-18h]@5
  char *Dst; // [sp+58h] [bp+10h]@1
  const char *Str; // [sp+60h] [bp+18h]@1
  int v14; // [sp+68h] [bp+20h]@1

  v14 = n;
  Str = szFull;
  Dst = szSub;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( szFull )
  {
    v8 = 0;
    v9 = strlen_0(szFull);
    Src = (void *)&Str[v9];
    for ( j = 0; j < v14; ++j )
    {
      Src = (char *)Src - 1;
      if ( Src == (void *)Str )
      {
        v6 = strlen_0(Str);
        memcpy_0(Dst, Str, v6);
      }
      ++v8;
    }
    if ( (_DWORD)Src != (_DWORD)Str )
      memcpy_0(Dst, Src, v8);
  }
}
