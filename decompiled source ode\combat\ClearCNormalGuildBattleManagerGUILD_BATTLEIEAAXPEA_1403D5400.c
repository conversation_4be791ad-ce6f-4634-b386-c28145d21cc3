/*
 * Function: ?Clear@CNormalGuildBattleManager@GUILD_BATTLE@@IEAAXPEAPEAVCNormalGuildBattle@2@@Z
 * Address: 0x1403D5400
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleManager::Clear(GUILD_BATTLE::CNormalGuildBattleManager *this, GUILD_BATTLE::CNormalGuildBattle **ppkStart)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int v5; // [sp+20h] [bp-18h]@4
  unsigned int j; // [sp+24h] [bp-14h]@4
  GUILD_BATTLE::CNormalGuildBattleManager *v7; // [sp+40h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattle **v8; // [sp+48h] [bp+10h]@1

  v8 = ppkStart;
  v7 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = 23 * v7->m_uiMapCnt;
  for ( j = 0; j < v5; ++j )
    GUILD_BATTLE::CNormalGuildBattle::Clear(v8[j]);
}
