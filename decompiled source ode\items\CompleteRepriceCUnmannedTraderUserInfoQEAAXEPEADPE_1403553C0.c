/*
 * Function: ?CompleteReprice@CUnmannedTraderUserInfo@@QEAAXEPEADPEAVCLogFile@@@Z
 * Address: 0x1403553C0
 */

void __fastcall CUnmannedTraderUserInfo::CompleteReprice(CUnmannedTraderUserInfo *this, char byRet, char *pLoadData, CLogFile *pkLogger)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v6; // eax@16
  CUnmannedTraderGroupItemInfoTable *v7; // rax@17
  unsigned int v8; // ecx@18
  int v9; // ecx@19
  __int64 v10; // [sp+0h] [bp-68h]@1
  unsigned int dwU; // [sp+20h] [bp-48h]@8
  unsigned int byType[2]; // [sp+28h] [bp-40h]@8
  unsigned int dwNewPrice; // [sp+30h] [bp-38h]@18
  char *pszFileName; // [sp+38h] [bp-30h]@19
  char *v15; // [sp+40h] [bp-28h]@4
  CPlayer *pReceiver; // [sp+48h] [bp-20h]@4
  _STORAGE_LIST::_db_con *pRegItem; // [sp+50h] [bp-18h]@13
  CUnmannedTraderUserInfo *v18; // [sp+70h] [bp+8h]@1
  char v19; // [sp+78h] [bp+10h]@1
  CLogFile *v20; // [sp+88h] [bp+20h]@1

  v20 = pkLogger;
  v19 = byRet;
  v18 = this;
  v4 = &v10;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v15 = pLoadData;
  pReceiver = CUnmannedTraderUserInfo::FindOwner(v18);
  if ( pReceiver && pReceiver->m_bLive )
  {
    if ( v19 || v15[20] )
    {
      CUnmannedTraderUserInfo::SendRepriceErrorResult(v18, pReceiver, v15[20]);
      CPlayer::AddDalant(pReceiver, *((_DWORD *)v15 + 4), 1);
    }
    else
    {
      pRegItem = _STORAGE_LIST::GetPtrFromSerial(
                   (_STORAGE_LIST *)&pReceiver->m_Param.m_dbInven.m_nListNum,
                   *((_WORD *)v15 + 6));
      if ( pRegItem )
      {
        if ( CUnmannedTraderUserInfo::CompleteRepriceItem(
               v18,
               *((_DWORD *)v15 + 6),
               *((_WORD *)v15 + 6),
               *((_DWORD *)v15 + 7)) )
        {
          v7 = CUnmannedTraderGroupItemInfoTable::Instance();
          if ( !CUnmannedTraderGroupItemInfoTable::IncreaseVersion(v7, pRegItem->m_byTableCode, pRegItem->m_wItemIndex) )
          {
            v8 = pRegItem->m_byTableCode;
            dwNewPrice = pRegItem->m_wItemIndex;
            byType[0] = v8;
            dwU = *((_DWORD *)v15 + 7);
            CLogFile::Write(
              v20,
              "CUnmannedTraderUserInfo::CompleteReprice( BYTE byRet(%u), char * pLoadData, CLogFile * pkLogger )\r\n"
              "\t\tpkQuery->dwRegistSerial(%u), pkQuery->dwNewPrice(%u)\r\n"
              "\t\tCUnmannedTraderGroupItemInfoTable::Instance()->IncreaseVersion( pRegItem->m_byTableCode(%u), pRegItem-"
              ">m_wItemIndex(%u) )\r\n",
              0i64,
              *((_DWORD *)v15 + 6));
          }
          byType[0] = *((_DWORD *)v15 + 4);
          dwU = *((_DWORD *)v15 + 6);
          CUnmannedTraderUserInfo::SendRepriceSuccessResult(
            v18,
            pReceiver,
            *((_WORD *)v15 + 6),
            *((_DWORD *)v15 + 7),
            dwU,
            byType[0]);
          v9 = pReceiver->m_ObjID.m_wIndex;
          pszFileName = pReceiver->m_szItemHistoryFileName;
          dwNewPrice = *((_DWORD *)v15 + 7);
          byType[0] = *(_DWORD *)v15;
          dwU = *((_DWORD *)v15 + 4);
          CMgrAvatorItemHistory::price_auto_trade(
            &CPlayer::s_MgrItemHistory,
            v9,
            *((_DWORD *)v15 + 6),
            pRegItem,
            dwU,
            byType[0],
            dwNewPrice,
            pReceiver->m_szItemHistoryFileName);
        }
        else
        {
          v6 = *((_WORD *)v15 + 6);
          dwU = *((_DWORD *)v15 + 7);
          CLogFile::Write(
            v20,
            "CUnmannedTraderUserInfo::CompleteReprice( BYTE byRet, char * pLoadData, CLogFile * pkLogger )\r\n"
            "\t\tCompleteRepriceItem( pkQuery->dwRegistSerial(%u), pkQuery->wItemSerial(%u), pkQuery->dwNewPrice(%u), pkL"
            "ogger ) Fail!\r\n",
            *((_DWORD *)v15 + 6),
            v6);
          CUnmannedTraderUserInfo::SendRepriceErrorResult(v18, pReceiver, 28);
        }
      }
      else
      {
        dwU = *((_WORD *)v15 + 6);
        CLogFile::Write(
          v20,
          "CCUnmannedTraderUserInfo::CompleteReprice( BYTE byRet, char * pLoadData, CLogFile * pkLogger )\r\n"
          "\t\tpkQuery->dwRegistSerial(%u), pkQuery->dwNewPrice(%u)\r\n"
          "\t\tpkRegister->m_Param.m_dbInven.GetPtrFromSerial( wItemSerial(%u) ) NULL!\r\n",
          *((_DWORD *)v15 + 6),
          *((_DWORD *)v15 + 7));
        CUnmannedTraderUserInfo::SendRepriceErrorResult(v18, pReceiver, 8);
      }
    }
  }
  else if ( v19 || v15[20] )
  {
    LOBYTE(byType[0]) = 1;
    dwU = 0xFFFFFFF;
    CMainThread::Push_ChargeItem(&g_Main, *((_DWORD *)v15 + 2), 0xFFFFFFFF, *((_DWORD *)v15 + 4), 0xFFFFFFFu, 1);
  }
}
