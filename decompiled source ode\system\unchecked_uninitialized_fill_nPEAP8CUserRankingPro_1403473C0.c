/*
 * Function: ??$unchecked_uninitialized_fill_n@PEAP8CUserRankingProcess@@EAAXXZ_KP81@EAAXXZV?$allocator@P8CUserRankingProcess@@EAAXXZ@std@@@stdext@@YAXPEAP8CUserRankingProcess@@EAAXXZ_KAEBQ81@EAAXXZAEAV?$allocator@P8CUserRankingProcess@@EAAXXZ@std@@@Z
 * Address: 0x1403473C0
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<void (CUserRankingProcess::**)(void),unsigned __int64,void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>(void (__cdecl **_First)(CUserRankingProcess *this), unsigned __int64 _Count, void (__cdecl *const *_Val)(CUserRankingProcess *this), std::allocator<void (__cdecl CUserRankingProcess::*)(void)> *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v7; // [sp+30h] [bp-18h]@4
  std::_Nonscalar_ptr_iterator_tag v8; // [sp+31h] [bp-17h]@4
  void (__cdecl **__formal)(CUserRankingProcess *); // [sp+50h] [bp+8h]@1
  unsigned __int64 _Counta; // [sp+58h] [bp+10h]@1
  void (__cdecl **_Vala)(CUserRankingProcess *); // [sp+60h] [bp+18h]@1
  std::allocator<void (__cdecl CUserRankingProcess::*)(void)> *_Ala; // [sp+68h] [bp+20h]@1

  _Ala = _Al;
  _Vala = (void (__cdecl **)(CUserRankingProcess *))_Val;
  _Counta = _Count;
  __formal = _First;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v7, 0, sizeof(v7));
  v8 = std::_Ptr_cat<void (CUserRankingProcess::**)(void),void (CUserRankingProcess::**)(void)>(&__formal, &__formal);
  std::_Uninit_fill_n<void (CUserRankingProcess::**)(void),unsigned __int64,void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>(
    __formal,
    _Counta,
    _Vala,
    _Ala,
    v8,
    v7);
}
