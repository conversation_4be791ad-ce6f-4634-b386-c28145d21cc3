/*
 * Function: ??$_Copy_backward_opt@PEAVCUnmannedTraderRegistItemInfo@@PEAV1@Urandom_access_iterator_tag@std@@@std@@YAPEAVCUnmannedTraderRegistItemInfo@@PEAV1@00Urandom_access_iterator_tag@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1403632B0
 */

CUnmannedTraderRegistItemInfo *__fastcall std::_Copy_backward_opt<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *,std::random_access_iterator_tag>(CUnmannedTraderRegistItemInfo *_First, CUnmannedTraderRegistItemInfo *_Last, CUnmannedTraderRegistItemInfo *_Dest, std::random_access_iterator_tag __formal, std::_Nonscalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-28h]@1
  CUnmannedTraderRegistItemInfo *v10; // [sp+30h] [bp+8h]@1
  CUnmannedTraderRegistItemInfo *rhs; // [sp+38h] [bp+10h]@1
  CUnmannedTraderRegistItemInfo *v12; // [sp+40h] [bp+18h]@1

  v12 = _Dest;
  rhs = _Last;
  v10 = _First;
  v6 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  while ( v10 != rhs )
  {
    --rhs;
    --v12;
    CUnmannedTraderRegistItemInfo::operator=(v12, rhs);
  }
  return v12;
}
