/*
 * Function: ?GetGroupID@CUnmannedTraderSubClassInfoLevel@@UEAA_NEGAEAE@Z
 * Address: 0x140384380
 */

char __fastcall CUnmannedTraderSubClassInfoLevel::GetGroupID(CUnmannedTraderSubClassInfoLevel *this, char byTableCode, unsigned __int16 wItemTableIndex, char *bySubClass)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v7; // [sp+0h] [bp-38h]@1
  int v8; // [sp+20h] [bp-18h]@4
  CUnmannedTraderSubClassInfoLevel *v9; // [sp+40h] [bp+8h]@1
  char *v10; // [sp+58h] [bp+20h]@1

  v10 = bySubClass;
  v9 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = GetItemEquipLevel((unsigned __int8)byTableCode, wItemTableIndex);
  if ( v9->m_byMin > v8 || v8 > v9->m_byMax )
  {
    result = 0;
  }
  else
  {
    *v10 = v9->m_dwID;
    result = 1;
  }
  return result;
}
