/*
 * Function: ?pc_EmbellishPart@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@G@Z
 * Address: 0x1400ADE20
 */

void __fastcall CPlayer::pc_EmbellishPart(CPlayer *this, _STORAGE_POS_INDIV *pItem, unsigned __int16 wChangeSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-168h]@1
  bool bDelete; // [sp+20h] [bp-148h]@51
  char *strErrorCodePos; // [sp+28h] [bp-140h]@51
  char v8; // [sp+30h] [bp-138h]@4
  _STORAGE_LIST *v9; // [sp+38h] [bp-130h]@4
  _STORAGE_LIST *v10; // [sp+40h] [bp-128h]@4
  _STORAGE_LIST::_db_con *pFixingItem; // [sp+48h] [bp-120h]@4
  void *Src; // [sp+50h] [bp-118h]@4
  __int64 v13; // [sp+58h] [bp-110h]@4
  int v14; // [sp+60h] [bp-108h]@4
  int j; // [sp+64h] [bp-104h]@29
  int nCashType; // [sp+68h] [bp-100h]@48
  _STORAGE_LIST::_db_con Dst; // [sp+78h] [bp-F0h]@51
  _STORAGE_LIST::_db_con v18; // [sp+C8h] [bp-A0h]@54
  _STORAGE_LIST::_db_con v19; // [sp+118h] [bp-50h]@60
  CPlayer *v20; // [sp+170h] [bp+8h]@1
  _STORAGE_POS_INDIV *v21; // [sp+178h] [bp+10h]@1
  unsigned __int16 v22; // [sp+180h] [bp+18h]@1

  v22 = wChangeSerial;
  v21 = pItem;
  v20 = this;
  v3 = &v5;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = 0;
  v9 = (_STORAGE_LIST *)&v20->m_Param.m_dbInven.m_nListNum;
  v10 = (_STORAGE_LIST *)&v20->m_Param.m_dbEmbellish.m_nListNum;
  pFixingItem = 0i64;
  Src = 0i64;
  v13 = 0i64;
  v14 = 0;
  if ( _effect_parameter::GetEff_State(&v20->m_EP, 20) )
  {
    v8 = 8;
    goto $RESULT_35;
  }
  if ( _effect_parameter::GetEff_State(&v20->m_EP, 28) )
  {
    v8 = 8;
    goto $RESULT_35;
  }
  pFixingItem = _STORAGE_LIST::GetPtrFromSerial(v9, v21->wItemSerial);
  if ( !pFixingItem )
  {
    v8 = 2;
    goto $RESULT_35;
  }
  if ( pFixingItem->m_byTableCode != 8 && pFixingItem->m_byTableCode != 9 && pFixingItem->m_byTableCode != 10 )
  {
    v8 = 3;
    goto $RESULT_35;
  }
  if ( !CPlayer::_check_embel_part(v20, pFixingItem) )
  {
    v8 = 4;
    goto $RESULT_35;
  }
  if ( v22 != 0xFFFF )
  {
    Src = _STORAGE_LIST::GetPtrFromSerial(v10, v22);
    if ( !Src )
    {
      v8 = 2;
      goto $RESULT_35;
    }
    if ( *((_BYTE *)Src + 1) != pFixingItem->m_byTableCode )
    {
      v8 = 6;
      goto $RESULT_35;
    }
  }
  if ( pFixingItem->m_bLock )
  {
    v8 = 10;
    goto $RESULT_35;
  }
  if ( Src && *((_BYTE *)Src + 19) )
  {
    v8 = 10;
    goto $RESULT_35;
  }
  if ( v22 == 0xFFFF && _STORAGE_LIST::GetIndexEmptyCon(v10) == 255 )
  {
    v8 = 5;
    goto $RESULT_35;
  }
  for ( j = 0; j < 7; ++j )
  {
    if ( v10->m_pStorageList[j].m_bLoad && v10->m_pStorageList[j].m_byTableCode == pFixingItem->m_byTableCode )
      ++v14;
  }
  if ( pFixingItem->m_byTableCode == 10 )
  {
    if ( v22 == 0xFFFF && v14 > 2 || v22 != 0xFFFF && v14 > 3 )
    {
      v8 = 5;
      goto $RESULT_35;
    }
  }
  else if ( v22 == 0xFFFF && v14 > 1 || v22 != 0xFFFF && v14 > 2 )
  {
    v8 = 5;
    goto $RESULT_35;
  }
  if ( pFixingItem->m_byTableCode == 10 )
  {
    nCashType = GetUsePcCashType(pFixingItem->m_byTableCode, pFixingItem->m_wItemIndex);
    if ( !CPlayer::IsUsableAccountType(v20, nCashType) )
    {
      CPlayer::SendMsg_PremiumCashItemUse(v20, 0xFFFFu);
      CPlayer::SendMsg_EmbellishResult(v20, 4);
      return;
    }
  }
$RESULT_35:
  if ( !v8 )
  {
    _STORAGE_LIST::_db_con::_db_con(&Dst);
    memcpy_0(&Dst, pFixingItem, 0x32ui64);
    strErrorCodePos = "CPlayer::pc_EmbellishPart() -- 0";
    bDelete = 0;
    if ( !CPlayer::Emb_DelStorage(
            v20,
            v9->m_nListCode,
            pFixingItem->m_byStorageIndex,
            0,
            0,
            "CPlayer::pc_EmbellishPart() -- 0") )
    {
      CPlayer::SendMsg_EmbellishResult(v20, -1);
      return;
    }
    if ( Src )
    {
      _STORAGE_LIST::_db_con::_db_con(&v18);
      memcpy_0(&v18, Src, 0x32ui64);
      bDelete = 0;
      if ( !CPlayer::Emb_AddStorage(v20, v9->m_nListCode, (_STORAGE_LIST::_storage_con *)&v18.m_bLoad, 1, 0) )
      {
        bDelete = 0;
        CPlayer::Emb_AddStorage(v20, v9->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0);
        CPlayer::SendMsg_EmbellishResult(v20, -1);
        return;
      }
      strErrorCodePos = "CPlayer::pc_EmbellishPart() -- 1";
      bDelete = 0;
      if ( !CPlayer::Emb_DelStorage(
              v20,
              v10->m_nListCode,
              *((_BYTE *)Src + 49),
              0,
              0,
              "CPlayer::pc_EmbellishPart() -- 1") )
      {
        strErrorCodePos = "CPlayer::pc_EmbellishPart() -- 1 Fail";
        bDelete = 0;
        CPlayer::Emb_DelStorage(
          v20,
          v9->m_nListCode,
          *((_BYTE *)Src + 49),
          0,
          0,
          "CPlayer::pc_EmbellishPart() -- 1 Fail");
        bDelete = 0;
        CPlayer::Emb_AddStorage(v20, v9->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0);
        CPlayer::SendMsg_EmbellishResult(v20, -1);
      }
    }
    bDelete = 0;
    if ( !CPlayer::Emb_AddStorage(v20, v10->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0) )
    {
      if ( !Src )
      {
        bDelete = 0;
        CPlayer::Emb_AddStorage(v20, v9->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0);
        CPlayer::SendMsg_EmbellishResult(v20, -1);
        return;
      }
      _STORAGE_LIST::_db_con::_db_con(&v19);
      memcpy_0(&v19, Src, 0x32ui64);
      bDelete = 0;
      CPlayer::Emb_AddStorage(v20, v10->m_nListCode, (_STORAGE_LIST::_storage_con *)&v19.m_bLoad, 1, 0);
      strErrorCodePos = "CPlayer::pc_EmbellishPart() -- 1 Fail";
      bDelete = 0;
      CPlayer::Emb_DelStorage(v20, v9->m_nListCode, *((_BYTE *)Src + 49), 0, 0, "CPlayer::pc_EmbellishPart() -- 1 Fail");
      bDelete = 0;
      CPlayer::Emb_AddStorage(v20, v9->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0);
      CPlayer::SendMsg_EmbellishResult(v20, -1);
    }
    CPlayer::Emb_EquipLink(v20);
  }
  CPlayer::SendMsg_EmbellishResult(v20, v8);
}
