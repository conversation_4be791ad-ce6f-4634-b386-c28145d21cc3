/*
 * Function: ?pc_MineComplete@CPlayer@@QEAAXXZ
 * Address: 0x1400D1F40
 */

void __fastcall CPlayer::pc_MineComplete(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  COreAmountMgr *v3; // rax@4
  CGoldenBoxItemMgr *v4; // rax@10
  CGoldenBoxItemMgr *v5; // rax@13
  char v6; // al@13
  CGoldenBoxItemMgr *v7; // rax@14
  unsigned __int16 v8; // ax@14
  CGoldenBoxItemMgr *v9; // rax@17
  CGoldenBoxItemMgr *v10; // rax@18
  int v11; // eax@22
  char v12; // al@26
  int v13; // eax@46
  CGoldenBoxItemMgr *v14; // rax@48
  CGoldenBoxItemMgr *v15; // rax@49
  unsigned int v16; // eax@49
  int v17; // eax@50
  COreAmountMgr *v18; // rax@50
  __int64 v19; // [sp+0h] [bp-138h]@1
  bool bUpdate[8]; // [sp+20h] [bp-118h]@42
  bool bSend[2]; // [sp+28h] [bp-110h]@42
  char v22; // [sp+30h] [bp-108h]@4
  int k; // [sp+34h] [bp-104h]@24
  int v24; // [sp+38h] [bp-100h]@4
  char v25; // [sp+3Ch] [bp-FCh]@18
  _STORAGE_LIST::_db_con *v26; // [sp+40h] [bp-F8h]@4
  char v27; // [sp+48h] [bp-F0h]@10
  char v28; // [sp+49h] [bp-EFh]@10
  int v29; // [sp+4Ch] [bp-ECh]@10
  unsigned int j; // [sp+50h] [bp-E8h]@11
  _base_fld *v31; // [sp+58h] [bp-E0h]@14
  int v32; // [sp+60h] [bp-D8h]@16
  unsigned int v33; // [sp+64h] [bp-D4h]@16
  char *v34; // [sp+68h] [bp-D0h]@27
  unsigned __int16 v35; // [sp+70h] [bp-C8h]@42
  unsigned int v36; // [sp+74h] [bp-C4h]@41
  _STORAGE_LIST::_db_con Dst; // [sp+88h] [bp-B0h]@41
  void *Src; // [sp+C8h] [bp-70h]@42
  _STORAGE_LIST::_db_con pItem; // [sp+D8h] [bp-60h]@43
  unsigned int v40; // [sp+114h] [bp-24h]@46
  _base_fld *v41; // [sp+118h] [bp-20h]@46
  int v42; // [sp+120h] [bp-18h]@16
  int v43; // [sp+124h] [bp-14h]@22
  char *szCharName; // [sp+128h] [bp-10h]@49
  CPlayer *v45; // [sp+140h] [bp+8h]@1

  v45 = this;
  v1 = &v19;
  for ( i = 76i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v22 = 0;
  v24 = 255;
  v26 = 0i64;
  v3 = COreAmountMgr::Instance();
  if ( COreAmountMgr::IsOreRemain(v3) )
  {
    v26 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v45->m_Param.m_dbInven.m_nListNum, v45->m_wBatterySerialTmp);
    if ( v26 )
    {
      if ( v26->m_bLock )
      {
        v22 = 12;
      }
      else
      {
        v27 = 0;
        v28 = 0;
        v29 = 0;
        v4 = CGoldenBoxItemMgr::Instance();
        if ( CGoldenBoxItemMgr::Get_Event_Status(v4) == 2 )
        {
          for ( j = 0; ; ++j )
          {
            v5 = CGoldenBoxItemMgr::Instance();
            v6 = CGoldenBoxItemMgr::GetLoopCount(v5);
            if ( j >= (unsigned int)(unsigned __int8)v6 + v29 )
              break;
            v7 = CGoldenBoxItemMgr::Instance();
            v8 = CGoldenBoxItemMgr::GetGoldBoxItemIndex(v7, j);
            v31 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 17, v8);
            if ( v31 )
            {
              if ( *(_DWORD *)&v31[3].m_strCode[4] )
              {
                v32 = rand();
                v42 = v32 << 16;
                v33 = (rand() + v42) % 0x1770u + 1;
                if ( *(_DWORD *)&v31[3].m_strCode[4] <= v33 )
                {
                  v9 = CGoldenBoxItemMgr::Instance();
                  if ( (signed int)CGoldenBoxItemMgr::Get_Box_Count(v9, j) > 0 )
                  {
                    v10 = CGoldenBoxItemMgr::Instance();
                    v25 = CGoldenBoxItemMgr::GetGoldBoxItemIndex(v10, j);
                    v27 = 1;
                    v28 = j;
                    break;
                  }
                }
              }
            }
          }
        }
        if ( !v27 )
          v25 = v45->m_bySelectOreIndex + rand() % 3;
        v43 = (unsigned __int8)v25;
        v11 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 17);
        if ( v43 < v11 )
        {
          for ( k = 0; ; ++k )
          {
            v12 = CPlayerDB::GetUseSlot(&v45->m_Param);
            if ( k >= (unsigned __int8)v12 )
              break;
            v34 = &v45->m_Param.m_dbInven.m_pStorageList[k].m_bLoad;
            if ( *v34
              && v34[1] == 17
              && *(_WORD *)(v34 + 3) == (unsigned __int8)v25
              && *(_QWORD *)(v34 + 5) < 0x63ui64
              && !v34[19] )
            {
              v24 = k;
              break;
            }
          }
          if ( v24 != 255 || _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&v45->m_Param.m_dbInven.m_nListNum) != 255 )
          {
            if ( v45->m_zMinePos[0] != (signed __int16)(signed int)ffloor(v45->m_fCurPos[0])
              || v45->m_zMinePos[1] != (signed __int16)(signed int)ffloor(v45->m_fCurPos[2]) )
            {
              v22 = 13;
            }
          }
          else
          {
            v22 = 10;
          }
        }
        else
        {
          v22 = 9;
        }
      }
    }
    else
    {
      v22 = 8;
    }
  }
  else
  {
    v22 = 27;
  }
  if ( v22 )
  {
    CPlayer::SendMsg_MineCompleteResult(v45, v22, -1, 0xFFFFu, -1, 0xFFFFu);
  }
  else
  {
    v36 = 0;
    _STORAGE_LIST::_db_con::_db_con(&Dst);
    if ( v24 == 255 )
    {
      _STORAGE_LIST::_db_con::_db_con(&pItem);
      pItem.m_byTableCode = 17;
      pItem.m_dwDur = 1i64;
      pItem.m_wItemIndex = (unsigned __int8)v25;
      pItem.m_dwLv = 0xFFFFFFF;
      pItem.m_wSerial = CPlayerDB::GetNewItemSerial(&v45->m_Param);
      if ( !CPlayer::Emb_AddStorage(v45, 0, (_STORAGE_LIST::_storage_con *)&pItem.m_bLoad, 0, 1) )
        CMgrAvatorItemHistory::add_storage_fail(
          &CPlayer::s_MgrItemHistory,
          v45->m_ObjID.m_wIndex,
          &pItem,
          "CPlayer::pc_MineComplete() - Emb_AddStorage() Fail",
          v45->m_szItemHistoryFileName);
      memcpy_0(&Dst, &pItem, 0x32ui64);
      Dst.m_dwDur = 1i64;
      v35 = pItem.m_wSerial;
      v36 = 1;
    }
    else
    {
      Src = &v45->m_Param.m_dbInven.m_pStorageList[v24];
      bSend[0] = 0;
      bUpdate[0] = 0;
      v36 = CPlayer::Emb_AlterDurPoint(v45, 0, *((_BYTE *)Src + 49), 1, 0, 0);
      v35 = *(_WORD *)((char *)Src + 17);
      memset_0(&Dst, 0, 0x32ui64);
      memcpy_0(&Dst, Src, 0x32ui64);
    }
    v13 = -v45->m_byDelaySec;
    bSend[0] = 0;
    bUpdate[0] = 0;
    v40 = CPlayer::Emb_AlterDurPoint(v45, 0, v26->m_byStorageIndex, v13, 0, 0);
    v41 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 17, (unsigned __int8)v25);
    CPlayer::Emb_CheckActForQuest(v45, 12, v41->m_strCode, 1u, 0);
    CPlayer::SendMsg_MineCompleteResult(v45, v22, v25, v35, v36, v40);
    if ( v27 )
    {
      if ( !v22 )
      {
        v14 = CGoldenBoxItemMgr::Instance();
        if ( CGoldenBoxItemMgr::Get_Event_Status(v14) == 2 )
        {
          v15 = CGoldenBoxItemMgr::Instance();
          CGoldenBoxItemMgr::Set_Box_Count(v15, v28);
          Dst.m_dwDur = 1i64;
          szCharName = CPlayerDB::GetCharNameA(&v45->m_Param);
          v16 = CPlayerDB::GetCharSerial(&v45->m_Param);
          CPlayer::SendMsg_Notify_Get_Golden_Box(v45, 4, v16, szCharName, &Dst, 0);
        }
      }
    }
    v17 = CPlayerDB::GetRaceCode(&v45->m_Param);
    eAddMineOre(v17, v41[3].m_strCode[0], 1);
    v18 = COreAmountMgr::Instance();
    COreAmountMgr::DecreaseOre(v18, 1u);
  }
  v45->m_dwMineNextTime = -1;
  v45->m_bMineMode = 0;
}
