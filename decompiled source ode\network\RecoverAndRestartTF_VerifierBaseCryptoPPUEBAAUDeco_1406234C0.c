/*
 * Function: ?RecoverAndRestart@TF_VerifierBase@CryptoPP@@UEBA?AUDecodingResult@2@PEAEAEAVPK_MessageAccumulator@2@@Z
 * Address: 0x1406234C0
 */

struct CryptoPP::DecodingResult *__fastcall CryptoPP::TF_VerifierBase::RecoverAndRestart(CryptoPP::TF_VerifierBase *this, struct CryptoPP::DecodingResult *retstr, unsigned __int8 *a3, struct CryptoPP::PK_MessageAccumulator *a4)
{
  __int64 *v4; // rax@1
  __int64 v5; // rax@1
  int v6; // eax@1
  unsigned __int64 v7; // rax@1
  unsigned __int64 v8; // rax@1
  __int64 v9; // rax@4
  __int64 v10; // rax@4
  char v11; // ST20_1@4
  char v13; // [sp+40h] [bp-E8h]@1
  __int64 v14; // [sp+48h] [bp-E0h]@1
  struct CryptoPP::PK_MessageAccumulator *v15; // [sp+50h] [bp-D8h]@1
  __int64 *v16; // [sp+58h] [bp-D0h]@1
  char v17; // [sp+60h] [bp-C8h]@4
  CryptoPP::PK_SignatureScheme::KeyTooShort v18; // [sp+70h] [bp-B8h]@2
  char v19; // [sp+C0h] [bp-68h]@4
  CryptoPP::TF_Base<CryptoPP::TrapdoorFunction,CryptoPP::PK_SignatureMessageEncodingMethod>Vtbl *v20; // [sp+D0h] [bp-58h]@1
  __int64 v21; // [sp+D8h] [bp-50h]@1
  __int64 v22; // [sp+E0h] [bp-48h]@1
  unsigned __int64 v23; // [sp+E8h] [bp-40h]@1
  __int64 v24; // [sp+F0h] [bp-38h]@4
  char *v25; // [sp+F8h] [bp-30h]@4
  CryptoPP::ClonableVtbl *v26; // [sp+100h] [bp-28h]@4
  __int64 v27; // [sp+108h] [bp-20h]@4
  CryptoPP::TF_VerifierBase *v28; // [sp+130h] [bp+8h]@1
  struct CryptoPP::DecodingResult *v29; // [sp+138h] [bp+10h]@1

  v29 = retstr;
  v28 = this;
  v15 = a4;
  ((void (__fastcall *)(CryptoPP::TF_VerifierBase *, char *))this->vfptr[1].RecoverablePartFirst)(this, &v13);
  v20 = v28->vfptr;
  LODWORD(v4) = ((int (__fastcall *)(signed __int64))v20->GetMessageEncodingInterface)((signed __int64)&v28->vfptr);
  v16 = v4;
  LODWORD(v5) = ((int (__fastcall *)(struct CryptoPP::PK_MessageAccumulator *))v15->vfptr[9].__vecDelDtor)(v15);
  v21 = v5;
  v6 = (*(int (__fastcall **)(__int64))(*(_QWORD *)v5 + 56i64))(v5);
  v22 = *v16;
  LODWORD(v7) = (*(int (__fastcall **)(__int64 *, __int64, _QWORD))(v22 + 8))(v16, v14, (unsigned int)v6);
  v23 = v7;
  LODWORD(v8) = CryptoPP::TF_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::TF_Base<CryptoPP::TrapdoorFunction,CryptoPP::PK_SignatureMessageEncodingMethod>>::MessageRepresentativeBitLength(v28);
  if ( v8 < v23 )
  {
    CryptoPP::PK_SignatureScheme::KeyTooShort::KeyTooShort(&v18);
    CxxThrowException_0((__int64)&v18, (__int64)&TI4_AVKeyTooShort_PK_SignatureScheme_CryptoPP__);
  }
  LODWORD(v9) = CryptoPP::TF_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::TF_Base<CryptoPP::TrapdoorFunction,CryptoPP::PK_SignatureMessageEncodingMethod>>::MessageRepresentativeBitLength(v28);
  v24 = v9;
  v25 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)&v15[4]);
  qmemcpy(&v19, &v13, 0x10ui64);
  v26 = v15->vfptr;
  LODWORD(v10) = ((int (__fastcall *)(struct CryptoPP::PK_MessageAccumulator *))v26[9].__vecDelDtor)(v15);
  v27 = *v16;
  v11 = (char)v15[23].vfptr;
  (*(void (__fastcall **)(__int64 *, char *, __int64, char *))(v27 + 64))(v16, &v17, v10, &v19);
  LOBYTE(v15[23].vfptr) = 1;
  qmemcpy(v29, &v17, sizeof(struct CryptoPP::DecodingResult));
  return v29;
}
