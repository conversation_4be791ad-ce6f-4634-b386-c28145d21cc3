/*
 * Function: j_??1?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@QEAA@XZ
 * Address: 0x140001F37
 */

void __fastcall std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::~vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this)
{
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::~vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(this);
}
