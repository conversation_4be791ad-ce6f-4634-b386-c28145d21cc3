/*
 * Function: j_??$_Uninit_fill_n@PEAP8CUserRankingProcess@@EAAXXZ_KP81@EAAXXZV?$allocator@P8CUserRankingProcess@@EAAXXZ@std@@@std@@YAXPEAP8CUserRankingProcess@@EAAXXZ_KAEBQ81@EAAXXZAEAV?$allocator@P8CUserRankingProcess@@EAAXXZ@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000B2A8
 */

void __fastcall std::_Uninit_fill_n<void (CUserRankingProcess::**)(void),unsigned __int64,void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>(void (__cdecl **_First)(CUserRankingProcess *this), unsigned __int64 _Count, void (__cdecl *const *_Val)(CUserRankingProcess *this), std::allocator<void (__cdecl CUserRankingProcess::*)(void)> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<void (CUserRankingProcess::**)(void),unsigned __int64,void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>(
    _First,
    _Count,
    _Val,
    _Al,
    __formal,
    a6);
}
