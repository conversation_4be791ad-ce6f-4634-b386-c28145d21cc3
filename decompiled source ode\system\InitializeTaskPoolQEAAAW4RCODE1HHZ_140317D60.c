/*
 * Function: ?Initialize@TaskPool@@QEAA?AW4RCODE@1@HH@Z
 * Address: 0x140317D60
 */

signed __int64 __fastcall TaskPool::Initialize(TaskPool *this, int nTskMaxNum, int nMaxTskSize)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  TaskPool *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7->_nMaxTskNum = nTskMaxNum;
  if ( TaskPool::_create_task(v7, nMaxTskSize) )
  {
    if ( TaskPool::_init_index_lists(v7) )
    {
      v7->_bInit = 1;
      result = 0i64;
    }
    else
    {
      result = 4294967290i64;
    }
  }
  else
  {
    result = 4294967292i64;
  }
  return result;
}
