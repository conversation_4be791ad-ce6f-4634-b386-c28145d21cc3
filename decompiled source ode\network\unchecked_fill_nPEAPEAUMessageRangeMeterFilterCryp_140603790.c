/*
 * Function: ??$unchecked_fill_n@PEAPEAUMessageRange@MeterFilter@CryptoPP@@_KPEAU123@@stdext@@YAXPEAPEAUMessageRange@MeterFilter@CryptoPP@@_KAEBQEAU123@@Z
 * Address: 0x140603790
 */

int __fastcall stdext::unchecked_fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned __int64,CryptoPP::MeterFilter::MessageRange *>(__int64 a1, __int64 a2, __int64 a3)
{
  _BYTE *v3; // rax@1
  char v5; // [sp+30h] [bp-18h]@1
  char v6; // [sp+31h] [bp-17h]@1
  __int64 v7; // [sp+50h] [bp+8h]@1
  __int64 v8; // [sp+58h] [bp+10h]@1
  __int64 v9; // [sp+60h] [bp+18h]@1

  v9 = a3;
  v8 = a2;
  v7 = a1;
  memset(&v5, 0, sizeof(v5));
  LODWORD(v3) = std::_Iter_cat<CryptoPP::MeterFilter::MessageRange * *>(&v6, &v7);
  return std::_Fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned __int64,CryptoPP::MeterFilter::MessageRange *,std::random_access_iterator_tag>(
           v7,
           v8,
           v9,
           *v3);
}
