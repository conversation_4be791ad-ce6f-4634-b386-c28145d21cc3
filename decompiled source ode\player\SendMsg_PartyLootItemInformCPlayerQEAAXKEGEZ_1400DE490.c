/*
 * Function: ?SendMsg_PartyLootItemInform@CPlayer@@QEAAXKEGE@Z
 * Address: 0x1400DE490
 */

void __fastcall CPlayer::SendMsg_PartyLootItemInform(CPlayer *this, unsigned int dwTakerSerial, char byTableCode, unsigned __int16 wItemIndex, char byNum)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-88h]@1
  char szMsg[4]; // [sp+38h] [bp-50h]@4
  char v9; // [sp+3Ch] [bp-4Ch]@4
  unsigned __int16 v10; // [sp+3Dh] [bp-4Bh]@4
  char v11; // [sp+3Fh] [bp-49h]@4
  char pbyType; // [sp+54h] [bp-34h]@4
  char v13; // [sp+55h] [bp-33h]@4
  CPartyPlayer **v14; // [sp+68h] [bp-20h]@4
  int j; // [sp+70h] [bp-18h]@5
  CPlayer *v16; // [sp+90h] [bp+8h]@1

  v16 = this;
  v5 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  *(_DWORD *)szMsg = dwTakerSerial;
  v9 = byTableCode;
  v10 = wItemIndex;
  v11 = byNum;
  pbyType = 16;
  v13 = 30;
  v14 = CPartyPlayer::GetPtrPartyMember(v16->m_pPartyMgr);
  if ( v14 )
  {
    for ( j = 0; j < 8 && v14[j]; ++j )
      CNetProcess::LoadSendMsg(unk_1414F2088, v14[j]->m_wZoneIndex, &pbyType, szMsg, 8u);
  }
}
