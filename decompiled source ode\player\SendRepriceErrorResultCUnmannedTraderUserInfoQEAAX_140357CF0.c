/*
 * Function: ?SendRepriceErrorResult@CUnmannedTraderUserInfo@@QEAAXPEAVCPlayer@@E@Z
 * Address: 0x140357CF0
 */

void __fastcall CUnmannedTraderUserInfo::SendRepriceErrorResult(CUnmannedTraderUserInfo *this, CPlayer *pReceiver, char byRet)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@4
  __int64 v6; // [sp+0h] [bp-88h]@1
  _a_trade_adjust_price_result_zocl v7; // [sp+38h] [bp-50h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v9; // [sp+65h] [bp-23h]@4
  CPlayer *v10; // [sp+98h] [bp+10h]@1

  v10 = pReceiver;
  v3 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  memset(&v7.byTaxRate, 0, 0x13ui64);
  v7.byRetCode = byRet;
  v7.byTaxRate = 1;
  pbyType = 30;
  v9 = 10;
  v5 = _a_trade_adjust_price_result_zocl::size(&v7);
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, &v7.byRetCode, v5);
}
