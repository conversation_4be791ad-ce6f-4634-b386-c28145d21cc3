/*
 * Function: ?SourceInitialize@Source@CryptoPP@@IEAAX_NAEBVNameValuePairs@2@@Z
 * Address: 0x14044CFB0
 */

void __fastcall CryptoPP::Source::SourceInitialize(CryptoPP::Source *this, bool pumpAll, CryptoPP::NameValuePairs *parameters)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CryptoPP::Source *v6; // [sp+30h] [bp+8h]@1
  bool v7; // [sp+38h] [bp+10h]@1

  v7 = pumpAll;
  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  ((void (__fastcall *)(CryptoPP::Source *, CryptoPP::NameValuePairs *))v6->vfptr[3].Clone)(v6, parameters);
  if ( v7 )
    CryptoPP::Source::PumpAll(v6);
}
