/*
 * Function: ?HashMultipleBlocks@?$IteratedHashBase@IV?$SimpleKeyedTransformation@VHashTransformation@CryptoPP@@@CryptoPP@@@CryptoPP@@MEAA_KPEBI_K@Z
 * Address: 0x140572440
 */

unsigned __int64 __fastcall CryptoPP::IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::HashMultipleBlocks(__int64 a1, __int64 a2, unsigned __int64 a3)
{
  CryptoPP::ByteOrder v3; // eax@1
  __int64 v4; // rax@1
  int v5; // eax@4
  bool v7; // [sp+20h] [bp-28h]@1
  __int64 v8; // [sp+28h] [bp-20h]@1
  unsigned int v9; // [sp+30h] [bp-18h]@1
  __int64 v10; // [sp+50h] [bp+8h]@1
  __int64 v11; // [sp+58h] [bp+10h]@1
  unsigned __int64 v12; // [sp+60h] [bp+18h]@1

  v12 = a3;
  v11 = a2;
  v10 = a1;
  v9 = (*(int (**)(void))(*(_QWORD *)a1 + 64i64))();
  v3 = (*(int (__fastcall **)(__int64))(*(_QWORD *)v10 + 152i64))(v10);
  v7 = CryptoPP::NativeByteOrderIs(v3);
  LODWORD(v4) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v10 + 176i64))(v10);
  v8 = v4;
  do
  {
    if ( v7 )
    {
      (*(void (__fastcall **)(__int64, __int64))(*(_QWORD *)v10 + 160i64))(v10, v11);
    }
    else
    {
      v5 = (*(int (__fastcall **)(__int64))(*(_QWORD *)v10 + 64i64))(v10);
      CryptoPP::ByteReverse<unsigned int>(v8, v11, (unsigned int)v5);
      (*(void (__fastcall **)(__int64, __int64))(*(_QWORD *)v10 + 160i64))(v10, v8);
    }
    v11 += 4 * (v9 / 4ui64);
    v12 -= v9;
  }
  while ( v12 >= v9 );
  return v12;
}
