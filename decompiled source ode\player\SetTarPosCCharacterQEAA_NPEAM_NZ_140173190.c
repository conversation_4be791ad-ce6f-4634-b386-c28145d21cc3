/*
 * Function: ?SetTarPos@CCharacter@@QEAA_NPEAM_N@Z
 * Address: 0x140173190
 */

char __fastcall CCharacter::SetTarPos(CCharacter *this, float *fTarPos, bool bColl)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-58h]@1
  char Src; // [sp+28h] [bp-30h]@7
  float v8; // [sp+2Ch] [bp-2Ch]@9
  CCharacter *v9; // [sp+60h] [bp+8h]@1
  float *fPos; // [sp+68h] [bp+10h]@1
  bool v11; // [sp+70h] [bp+18h]@1

  v11 = bColl;
  fPos = fTarPos;
  v9 = this;
  v3 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( CMapData::IsMapIn(v9->m_pCurMap, fTarPos) )
  {
    if ( v11 )
    {
      if ( (unsigned int)CBsp::CanYouGoThere(v9->m_pCurMap->m_Level.mBsp, v9->m_fCurPos, fPos, (float (*)[3])&Src) )
      {
        memcpy_0(v9->m_fTarPos, fPos, 0xCui64);
      }
      else
      {
        v8 = v9->m_fCurPos[1];
        memcpy_0(v9->m_fTarPos, &Src, 0xCui64);
      }
    }
    else
    {
      memcpy_0(v9->m_fTarPos, fPos, 0xCui64);
    }
    CCharacter::Go(v9);
    CCharacter::ResetSlot(v9);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
