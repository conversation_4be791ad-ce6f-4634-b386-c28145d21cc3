/*
 * Function: ??$fill@PEAPEAVCMoveMapLimitInfo@@PEAV1@@std@@YAXPEAPEAVCMoveMapLimitInfo@@0AEBQEAV1@@Z
 * Address: 0x1403AAFE0
 */

void __fastcall std::fill<CMoveMapLimitInfo * *,CMoveMapLimitInfo *>(CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, CMoveMapLimitInfo *const *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMoveMapLimitInfo **_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Fill<CMoveMapLimitInfo * *,CMoveMapLimitInfo *>(_Firsta, _Last, _Val);
}
