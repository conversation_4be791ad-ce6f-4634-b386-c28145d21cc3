/*
 * Function: ?Select_CharacterBaseInfoByName@CRFWorldDatabase@@QEAAEPEADPEAU_worlddb_character_base_info@@@Z
 * Address: 0x14048AAB0
 */

char __fastcall CRFWorldDatabase::Select_CharacterBaseInfoByName(CRFWorldDatabase *this, char *pwszCharacterName, _worlddb_character_base_info *pCharacterData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@22
  SQLLEN v9; // [sp+38h] [bp-150h]@22
  __int16 v10; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  int j; // [sp+164h] [bp-24h]@4
  char v13; // [sp+168h] [bp-20h]@16
  unsigned __int64 v14; // [sp+178h] [bp-10h]@4
  CRFWorldDatabase *v15; // [sp+190h] [bp+8h]@1
  const char *Source; // [sp+198h] [bp+10h]@1
  _worlddb_character_base_info *v17; // [sp+1A0h] [bp+18h]@1

  v17 = pCharacterData;
  Source = pwszCharacterName;
  v15 = this;
  v3 = &v6;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = (unsigned __int64)&v6 ^ _security_cookie;
  j = 0;
  sprintf(&Dest, "{ CALL pSelect_CharacterBaseInfoByName_20061115('%s') }", pwszCharacterName);
  if ( v15->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v15->vfptr, &Dest);
  if ( v15->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v15->vfptr) )
  {
    v10 = SQLExecDirectA_0(v15->m_hStmtSelect, &Dest, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v15->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v10, &Dest, "_SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v10, v15->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v10 = SQLFetch_0(v15->m_hStmtSelect);
      if ( v10 && v10 != 1 )
      {
        v13 = 0;
        if ( v10 == 100 )
        {
          v13 = 2;
        }
        else
        {
          SQLStmt = v15->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v10, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v10, v15->m_hStmtSelect);
          v13 = 1;
        }
        if ( v15->m_hStmtSelect )
          SQLCloseCursor_0(v15->m_hStmtSelect);
        result = v13;
      }
      else
      {
        strcpy_0(v17->wszName, Source);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 1u, 4, &v17->dwSerial, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 2u, -17, &v17->byRace, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)5;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 3u, 1, v17->szClassCode, 5i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 4u, -17, &v17->bySlotIndex, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 5u, -17, &v17->byLevel, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 6u, 4, &v17->dwDalant, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 7u, 4, &v17->dwGold, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 8u, 4, &v17->dwBaseShape, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 9u, 4, &v17->dwLastConnTime, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)17;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 0xAu, 1, v17->szAccount, 17i64, &v9);
        for ( j = 0; j < 8; ++j )
        {
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v15->m_hStmtSelect, j + 11, -15, &v17->shEKArray[j], 0i64, &v9);
        }
        for ( j = 0; j < 8; ++j )
        {
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v15->m_hStmtSelect, j + 19, -18, &v17->dwEUArray[j], 0i64, &v9);
        }
        for ( j = 0; j < 8; ++j )
        {
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v15->m_hStmtSelect, j + 27, -25, &v17->lnUIDArray[j], 0i64, &v9);
        }
        for ( j = 0; j < 8; ++j )
        {
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v15->m_hStmtSelect, j + 35, 4, &v17->dwETArray[j], 0i64, &v9);
        }
        if ( v10 && v10 != 1 )
        {
          SQLStmt = v15->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v10, &Dest, "SQLGetData", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v10, v15->m_hStmtSelect);
          if ( v15->m_hStmtSelect )
            SQLCloseCursor_0(v15->m_hStmtSelect);
          result = 1;
        }
        else
        {
          if ( v15->m_hStmtSelect )
            SQLCloseCursor_0(v15->m_hStmtSelect);
          if ( v15->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v15->vfptr, "%s Success", &Dest);
          result = 0;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v15->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
