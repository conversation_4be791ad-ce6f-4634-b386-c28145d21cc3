/*
 * Function: ??1?$AlgorithmImpl@V?$DL_VerifierBase@UECPPoint@CryptoPP@@@CryptoPP@@V?$DL_SS@U?$DL_Keys_ECDSA@VECP@CryptoPP@@@CryptoPP@@V?$DL_Algorithm_ECDSA@VECP@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@H@2@@CryptoPP@@UEAA@XZ
 * Address: 0x14055F680
 */

int CryptoPP::AlgorithmImpl<CryptoPP::DL_VerifierBase<CryptoPP::ECPPoint>,CryptoPP::DL_SS<CryptoPP::DL_Keys_ECDSA<CryptoPP::ECP>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::ECP>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1,int>>::~AlgorithmImpl<CryptoPP::DL_VerifierBase<CryptoPP::ECPPoint>,CryptoPP::DL_SS<CryptoPP::DL_Keys_ECDSA<CryptoPP::ECP>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::ECP>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1,int>>()
{
  return CryptoPP::DL_VerifierBase<CryptoPP::ECPPoint>::~DL_VerifierBase<CryptoPP::ECPPoint>();
}
