/*
 * Function: ?CheckGetGravityStone@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAXGKHKK@Z
 * Address: 0x1403D4800
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleManager::CheckGetGravityStone(GUILD_BATTLE::CNormalGuildBattleManager *this, unsigned __int16 wIndex, unsigned int dwObjSerial, int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-B8h]@1
  char v9; // [sp+30h] [bp-88h]@4
  char szMsg; // [sp+48h] [bp-70h]@5
  char v11; // [sp+49h] [bp-6Fh]@5
  char pbyType; // [sp+84h] [bp-34h]@5
  char v13; // [sp+85h] [bp-33h]@5
  unsigned __int64 v14; // [sp+A0h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleManager *v15; // [sp+C0h] [bp+8h]@1
  int dwClientIndex; // [sp+D8h] [bp+20h]@1

  dwClientIndex = n;
  v15 = this;
  v6 = &v8;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v14 = (unsigned __int64)&v8 ^ _security_cookie;
  v9 = GUILD_BATTLE::CNormalGuildBattleManager::ProcCheckGetGravityStone(
         v15,
         wIndex,
         dwObjSerial,
         dwGuildSerial,
         dwCharacSerial);
  if ( v9 )
  {
    szMsg = v9;
    memset(&v11, 0, 0x27ui64);
    pbyType = 27;
    v13 = 72;
    CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &szMsg, 0x28u);
  }
}
