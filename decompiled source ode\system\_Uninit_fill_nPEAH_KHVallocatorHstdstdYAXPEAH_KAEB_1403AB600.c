/*
 * Function: ??$_Uninit_fill_n@PEAH_KHV?$allocator@H@std@@@std@@YAXPEAH_KAEBHAEAV?$allocator@H@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1403AB600
 */

void __fastcall std::_Uninit_fill_n<int *,unsigned __int64,int,std::allocator<int>>(int *_First, unsigned __int64 _Count, const int *_Val, std::allocator<int> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-28h]@1
  int *_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  stdext::unchecked_fill_n<int *,unsigned __int64,int>(_Firsta, _Count, _Val);
}
