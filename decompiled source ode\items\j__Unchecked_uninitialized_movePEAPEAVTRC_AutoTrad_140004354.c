/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAPEAVTRC_AutoTrade@@PEAPEAV1@V?$allocator@PEAVTRC_AutoTrade@@@std@@@stdext@@YAPEAPEAVTRC_AutoTrade@@PEAPEAV1@00AEAV?$allocator@PEAVTRC_AutoTrade@@@std@@@Z
 * Address: 0x140004354
 */

TRC_AutoTrade **__fastcall stdext::_Unchecked_uninitialized_move<TRC_AutoTrade * *,TRC_AutoTrade * *,std::allocator<TRC_AutoTrade *>>(TRC_AutoTrade **_First, TRC_AutoTrade **_Last, TRC_AutoTrade **_Dest, std::allocator<TRC_AutoTrade *> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<TRC_AutoTrade * *,TRC_AutoTrade * *,std::allocator<TRC_AutoTrade *>>(
           _First,
           _Last,
           _<PERSON>t,
           _<PERSON>);
}
