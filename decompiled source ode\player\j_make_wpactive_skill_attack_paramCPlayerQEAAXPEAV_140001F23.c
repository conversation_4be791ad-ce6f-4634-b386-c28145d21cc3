/*
 * Function: j_?make_wpactive_skill_attack_param@CPlayer@@QEAAXPEAVCCharacter@@PEAU_skill_fld@@PEAMEHPEAU_db_con@_STORAGE_LIST@@MPEAU_attack_param@@PEAH@Z
 * Address: 0x140001F23
 */

void __fastcall CPlayer::make_wpactive_skill_attack_param(CPlayer *this, CCharacter *pDst, _skill_fld *pSkillFld, float *pfAttackPos, char byEffectCode, int nAttType, _STORAGE_LIST::_db_con *pBulletItem, float fAddBulletFc, _attack_param *pAP, int *nShotNum)
{
  CPlayer::make_wpactive_skill_attack_param(
    this,
    pDst,
    pSkillFld,
    pfAttackPos,
    byEffectCode,
    nAttType,
    pBulletItem,
    fAddBulletFc,
    pAP,
    nShotNum);
}
