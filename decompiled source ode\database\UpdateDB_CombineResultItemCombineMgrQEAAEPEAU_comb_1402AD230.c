/*
 * Function: ?UpdateDB_CombineResult@ItemCombineMgr@@QEAAEPEAU_combine_ex_item_result_zocl@@@Z
 * Address: 0x1402AD230
 */

char __fastcall ItemCombineMgr::UpdateDB_CombineResult(ItemCombineMgr *this, _combine_ex_item_result_zocl *pSaveData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-58h]@1
  char v6; // [sp+20h] [bp-38h]@4
  _ITEMCOMBINE_DB_BASE *pItemCombineDB_IN; // [sp+28h] [bp-30h]@4
  int j; // [sp+30h] [bp-28h]@4
  void *Dst; // [sp+38h] [bp-20h]@9
  int v10; // [sp+40h] [bp-18h]@6
  ItemCombineMgr *v11; // [sp+60h] [bp+8h]@1
  _combine_ex_item_result_zocl *v12; // [sp+68h] [bp+10h]@1

  v12 = pSaveData;
  v11 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  _ITEMCOMBINE_DB_BASE::Init(&v11->m_pMaster->m_Param.m_ItemCombineDB);
  pItemCombineDB_IN = &v11->m_pMaster->m_Param.m_ItemCombineDB;
  pItemCombineDB_IN->m_dwCheckKey = v12->dwCheckKey;
  pItemCombineDB_IN->m_byDlgType = v12->byDlgType;
  pItemCombineDB_IN->m_dwDalant = v12->dwDalant;
  pItemCombineDB_IN->m_bySelectItemCount = v12->bySelectItemCount;
  pItemCombineDB_IN->m_byItemListNum = v12->ItemBuff.byItemListNum;
  for ( j = 0; ; ++j )
  {
    v10 = pItemCombineDB_IN->m_byItemListNum >= 24 ? 24 : pItemCombineDB_IN->m_byItemListNum;
    if ( j >= v10 )
      break;
    Dst = &pItemCombineDB_IN->m_List[j];
    memcpy_0(Dst, &v12->ItemBuff.RewardItemList[j], 4ui64);
    *((_DWORD *)Dst + 1) = v12->ItemBuff.RewardItemList[j].dwDur;
    *((_DWORD *)Dst + 2) = v12->ItemBuff.RewardItemList[j].dwUpt;
  }
  pItemCombineDB_IN->m_bIsResult = 1;
  pItemCombineDB_IN->m_dwResultEffectType = v12->dwResultEffectType;
  pItemCombineDB_IN->m_dwResultEffectMsgCode = v12->dwResultEffectMsgCode;
  CUserDB::Update_CombineExResult_Push(v11->m_pMaster->m_pUserDB, pItemCombineDB_IN);
  return v6;
}
