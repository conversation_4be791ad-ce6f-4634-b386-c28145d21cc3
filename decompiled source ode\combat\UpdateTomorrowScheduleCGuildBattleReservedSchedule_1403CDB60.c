/*
 * Function: ?UpdateTomorrowSchedule@CGuildBattleReservedScheduleListManager@GUILD_BATTLE@@AEAA_NI@Z
 * Address: 0x1403CDB60
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateTomorrowSchedule(GUILD_BATTLE::CGuildBattleReservedScheduleListManager *this, unsigned int uiMapID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  GUILD_BATTLE::CGuildBattleScheduleManager *v5; // rax@6
  GUILD_BATTLE::CGuildBattleLogger *v6; // rax@7
  GUILD_BATTLE::CGuildBattleLogger *v7; // rax@9
  GUILD_BATTLE::CGuildBattleLogger *v8; // rax@11
  __int64 v9; // [sp+0h] [bp-9E8h]@1
  unsigned int v10; // [sp+20h] [bp-9C8h]@7
  unsigned int uiSLID; // [sp+34h] [bp-9B4h]@6
  char Dst; // [sp+60h] [bp-988h]@8
  unsigned __int64 v13; // [sp+9D0h] [bp-18h]@4
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager *v14; // [sp+9F0h] [bp+8h]@1
  unsigned int uiMap; // [sp+9F8h] [bp+10h]@1

  uiMap = uiMapID;
  v14 = this;
  v2 = &v9;
  for ( i = 632i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (unsigned __int64)&v9 ^ _security_cookie;
  if ( v14->m_uiMapCnt > uiMapID )
  {
    uiSLID = 0;
    v5 = GUILD_BATTLE::CGuildBattleScheduleManager::Instance();
    if ( GUILD_BATTLE::CGuildBattleScheduleManager::GetTomorrowSLIDByMap(v5, uiMap, &uiSLID) )
    {
      memset_0(&Dst, 0, 0x95Cui64);
      if ( CRFWorldDatabase::SelectGuildBattleRerservedList(
             pkDB,
             uiSLID,
             uiSLID,
             (_worlddb_guild_battle_reserved_schedule_info *)&Dst) )
      {
        if ( GUILD_BATTLE::CReservedGuildScheduleDayGroup::Load(
               v14->m_pkTomorrow,
               1,
               uiMap,
               (_worlddb_guild_battle_reserved_schedule_info *)&Dst) )
        {
          result = 1;
        }
        else
        {
          v8 = GUILD_BATTLE::CGuildBattleLogger::Instance();
          GUILD_BATTLE::CGuildBattleLogger::Log(
            v8,
            "CGuildBattleReservedScheduleListManager::UpdateTomorrowScheduleLoad(%u) : m_pkTomorrow->Load( %u ) Fail!",
            uiMap,
            uiMap);
          result = 0;
        }
      }
      else
      {
        v7 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        v10 = uiSLID;
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v7,
          "CGuildBattleReservedScheduleListManager::UpdateTomorrowSchedule(%u) : g_Main.m_pWorldDB->SelectGuildBattleRers"
          "ervedList( %u, %u ) Fail!",
          uiMap,
          uiSLID);
        result = 0;
      }
    }
    else
    {
      v6 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v10 = uiSLID;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v6,
        "CGuildBattleReservedScheduleListManager::UpdateTomorrowSchedule(%u) : CGuildBattleScheduler::Instance()->GetTomo"
        "rrowSLIDRange( %u, %u ) Fail!",
        uiMap,
        uiMap);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
