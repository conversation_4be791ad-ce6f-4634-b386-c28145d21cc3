/*
 * Function: ?GetGroupID@CUnmannedTraderGroupIDInfo@@QEAA_NEGAEAE00AEAK@Z
 * Address: 0x1403868B0
 */

char __fastcall CUnmannedTraderGroupIDInfo::GetGroupID(CUnmannedTraderGroupIDInfo *this, char byTableCode, unsigned __int16 wItemTableIndex, char *byDivision, char *byClass, char *bySubClass, unsigned int *dwListIndex)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char v9; // al@5
  CUnmannedTraderDivisionInfo **v10; // rax@8
  CUnmannedTraderDivisionInfo **v11; // rax@10
  unsigned int v12; // eax@10
  __int64 v13; // [sp+0h] [bp-F8h]@1
  char *v14; // [sp+20h] [bp-D8h]@8
  char *v15; // [sp+28h] [bp-D0h]@8
  unsigned int *v16; // [sp+30h] [bp-C8h]@8
  int v17; // [sp+40h] [bp-B8h]@6
  unsigned int v18; // [sp+54h] [bp-A4h]@6
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > result; // [sp+78h] [bp-80h]@6
  int v20; // [sp+94h] [bp-64h]@6
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > v21; // [sp+98h] [bp-60h]@10
  bool v22; // [sp+B0h] [bp-48h]@7
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > v23; // [sp+B8h] [bp-40h]@7
  char v24; // [sp+D0h] [bp-28h]@9
  bool v25; // [sp+D1h] [bp-27h]@11
  __int64 v26; // [sp+D8h] [bp-20h]@4
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *v27; // [sp+E0h] [bp-18h]@7
  std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *_Right; // [sp+E8h] [bp-10h]@7
  CUnmannedTraderGroupIDInfo *v29; // [sp+100h] [bp+8h]@1
  char v30; // [sp+108h] [bp+10h]@1
  unsigned __int16 v31; // [sp+110h] [bp+18h]@1
  char *byDivisiona; // [sp+118h] [bp+20h]@1

  byDivisiona = byDivision;
  v31 = wItemTableIndex;
  v30 = byTableCode;
  v29 = this;
  v7 = &v13;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v26 = -2i64;
  if ( std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::empty(&v29->m_vecDivisionInfo) )
  {
    v9 = 0;
  }
  else
  {
    v17 = 0;
    v18 = 0;
    std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::begin(
      &v29->m_vecDivisionInfo,
      &result);
    v20 = 0;
    while ( 1 )
    {
      v27 = std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::end(
              &v29->m_vecDivisionInfo,
              &v23);
      _Right = (std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)v27;
      v22 = std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator!=(
              (std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)&result._Mycont,
              (std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)&v27->_Mycont);
      std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&v23);
      if ( !v22 )
        break;
      v10 = std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator*(&result);
      v16 = &v18;
      v15 = bySubClass;
      v14 = byClass;
      if ( CUnmannedTraderDivisionInfo::GetGroupID(*v10, v30, v31, byDivisiona, byClass, bySubClass, &v18) )
      {
        *dwListIndex = v18 + v17;
        v24 = 1;
        std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&result);
        return v24;
      }
      v11 = std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator*(&result);
      v12 = CUnmannedTraderDivisionInfo::GetMaxClassCnt(*v11);
      v17 += v12;
      std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator++(
        &result,
        &v21,
        0);
      ++v20;
      std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&v21);
    }
    v25 = 0;
    std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&result);
    v9 = v25;
  }
  return v9;
}
