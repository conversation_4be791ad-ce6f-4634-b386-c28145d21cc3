/*
 * Function: ?GetRecord@CItemUpgradeTable@@QEAAPEAU_ItemUpgrade_fld@@K@Z
 * Address: 0x14007AEE0
 */

_ItemUpgrade_fld *__fastcall CItemUpgradeTable::GetRecord(CItemUpgradeTable *this, unsigned int dwIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _ItemUpgrade_fld *result; // rax@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CItemUpgradeTable *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( dwIndex < v6->m_nResNum )
    result = (_ItemUpgrade_fld *)CRecordData::GetRecord(&v6->m_tblItemUpgrade, dwIndex);
  else
    result = 0i64;
  return result;
}
