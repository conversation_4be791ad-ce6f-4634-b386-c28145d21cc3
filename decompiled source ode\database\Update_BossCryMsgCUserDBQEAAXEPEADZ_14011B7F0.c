/*
 * Function: ?Update_BossCryMsg@CUserDB@@QEAAXEPEAD@Z
 * Address: 0x14011B7F0
 */

void __fastcall CUserDB::Update_BossCryMsg(CUserDB *this, char bySlot, char *pwszCryMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUserDB *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  strcpy_0((char *)&v6->m_AvatorData.dbBossCry + 65 * (unsigned __int8)bySlot, pwszCryMsg);
}
