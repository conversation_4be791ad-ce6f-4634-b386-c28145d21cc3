/*
 * Function: ?Guild_Pop_Money_Complete@CPlayer@@SAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1400AA050
 */

void __fastcall CPlayer::Guild_Pop_Money_Complete(_DB_QRY_SYN_DATA *pData)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@10
  __int64 v4; // [sp+0h] [bp-108h]@1
  unsigned int dwPopGold[2]; // [sp+20h] [bp-E8h]@6
  unsigned int dwLeftDalant[2]; // [sp+28h] [bp-E0h]@6
  unsigned int dwLeftGold[2]; // [sp+30h] [bp-D8h]@6
  char *pbyDate; // [sp+38h] [bp-D0h]@6
  bool bInPut[4]; // [sp+40h] [bp-C8h]@6
  int v10; // [sp+48h] [bp-C0h]@6
  int v11; // [sp+50h] [bp-B8h]@4
  unsigned int v12; // [sp+54h] [bp-B4h]@4
  unsigned int dwIOerSerial; // [sp+58h] [bp-B0h]@4
  char Dest; // [sp+68h] [bp-A0h]@4
  unsigned int dwD; // [sp+84h] [bp-84h]@4
  unsigned int dwPush; // [sp+88h] [bp-80h]@4
  double v17; // [sp+90h] [bp-78h]@4
  long double v18; // [sp+98h] [bp-70h]@4
  char Dst; // [sp+A4h] [bp-64h]@4
  char *v20; // [sp+B8h] [bp-50h]@4
  CGuild *v21; // [sp+C0h] [bp-48h]@4
  _guild_member_info *v22; // [sp+C8h] [bp-40h]@8
  CPlayer *v23; // [sp+D0h] [bp-38h]@9
  char *pszFileName; // [sp+E0h] [bp-28h]@10
  unsigned int v25; // [sp+E8h] [bp-20h]@10
  unsigned __int64 v26; // [sp+F0h] [bp-18h]@4
  _DB_QRY_SYN_DATA *v27; // [sp+110h] [bp+8h]@1

  v27 = pData;
  v1 = &v4;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v26 = (unsigned __int64)&v4 ^ _security_cookie;
  v11 = -1;
  v12 = -1;
  dwIOerSerial = -1;
  dwD = 0;
  dwPush = 0;
  v17 = 0.0;
  v18 = 0.0;
  v20 = v27->m_sData;
  v11 = *(_DWORD *)&v27->m_sData[24];
  v12 = *(_DWORD *)&v27->m_sData[28];
  dwIOerSerial = *(_DWORD *)&v27->m_sData[0];
  dwPush = *(_DWORD *)&v27->m_sData[36];
  dwD = *(_DWORD *)&v27->m_sData[32];
  v18 = *(double *)&v27->m_sData[56];
  v17 = *(double *)&v27->m_sData[48];
  strcpy_0(&Dest, &v27->m_sData[4]);
  memcpy_0(&Dst, v20 + 40, 4ui64);
  v21 = &g_Guild[v11];
  if ( v21->m_dwSerial == v12 )
  {
    v21->m_bIOWait = 0;
    if ( v20[64] )
    {
      v10 = (unsigned __int8)v20[64];
      *(_DWORD *)bInPut = *(_DWORD *)v20;
      pbyDate = &Dest;
      dwLeftGold[0] = dwD;
      dwLeftDalant[0] = dwPush;
      *(double *)dwPopGold = v17;
      CLogFile::Write(
        &stru_1799C8E78,
        "CPlayer::Guild_Pop_Money_Complete(...) : \r\n"
        "\t\tGuild(%u) TotD(%f) TotG(%f) SubD(%u) SubG(%u) %s(%u)\r\n"
        "\t\t_qry_case_outputgmoney Ret(%u) Fail!",
        v12,
        v18);
    }
    else if ( !v27->m_byResult )
    {
      *(double *)dwPopGold = -0.0 - (double)(signed int)dwD;
      CGuild::IOMoney(
        v21,
        &Dest,
        dwIOerSerial,
        -0.0 - (double)(signed int)dwPush,
        *(long double *)dwPopGold,
        v18,
        v17,
        &Dst,
        0);
      v22 = CGuild::GetMemberFromSerial(v21, dwIOerSerial);
      if ( v22 )
      {
        v23 = v22->pPlayer;
        if ( v23 )
        {
          CPlayer::AddDalant(v23, dwPush, 1);
          CPlayer::AddGold(v23, dwD, 1);
          pszFileName = v23->m_szItemHistoryFileName;
          v25 = CPlayerDB::GetGold(&v23->m_Param);
          v3 = CPlayerDB::GetDalant(&v23->m_Param);
          CMgrAvatorItemHistory::guild_pop_money(
            &CPlayer::s_MgrItemHistory,
            v23->m_ObjID.m_wIndex,
            v21->m_aszName,
            dwPush,
            dwD,
            v3,
            v25,
            pszFileName);
          CPlayer::SendMsg_AlterMoneyInform(v23, 0);
        }
        else
        {
          if ( dwPush )
            CMainThread::Push_ChargeItem(&g_Main, dwIOerSerial, 0xFFFFFFFF, dwPush, 0xFFFFFFFu, 1);
          if ( dwD )
            CMainThread::Push_ChargeItem(&g_Main, dwIOerSerial, 0xFFFFFFFF, dwD, 0xFFFFFFFu, 2);
          dwPopGold[0] = dwD;
          CLogFile::Write(
            &stru_1799C8E78,
            "CPlayer::Guild_Pop_Money_Complete(...) : \r\n"
            "\t\tPush Charge Money BECAUSE Poper Connection Closed : Poper Serial(%u) Pop Dalant(%u) Pop Gold(%u)\r\n",
            dwIOerSerial,
            dwPush);
        }
      }
    }
  }
}
