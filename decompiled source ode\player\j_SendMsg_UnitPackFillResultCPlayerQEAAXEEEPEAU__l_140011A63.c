/*
 * Function: j_?SendMsg_UnitPackFillResult@CPlayer@@QEAAXEEEPEAU__list@_unit_pack_fill_request_clzo@@PEAK@Z
 * Address: 0x140011A63
 */

void __fastcall CPlayer::SendMsg_UnitPackFillResult(CPlayer *this, char byRetC<PERSON>, char bySlotIndex, char by<PERSON><PERSON><PERSON><PERSON>, _unit_pack_fill_request_clzo::__list *pList, unsigned int *pdwConsumMoney)
{
  CPlayer::SendMsg_UnitPackFillResult(this, byRetCode, bySlotIndex, byFill<PERSON>um, pList, pdwConsumMoney);
}
