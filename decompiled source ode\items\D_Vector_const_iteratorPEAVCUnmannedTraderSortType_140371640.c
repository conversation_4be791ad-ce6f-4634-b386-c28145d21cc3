/*
 * Function: ??D?$_Vector_const_iterator@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEBAAEBQEAVCUnmannedTraderSortType@@XZ
 * Address: 0x140371640
 */

CUnmannedTraderSortType **__fastcall std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::operator*(std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this)
{
  return this->_Myptr;
}
