/*
 * Function: ?Guild_Insert_Complete@CPlayer@@SAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1400A8A10
 */

void __fastcall CPlayer::Guild_Insert_Complete(_DB_QRY_SYN_DATA *pData)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@14
  unsigned int v4; // eax@14
  CPlayer::CashChangeStateFlag *v5; // rax@26
  CGuildBattleController *v6; // rax@28
  __int64 v7; // [sp+0h] [bp-4C8h]@1
  int v8; // [sp+30h] [bp-498h]@4
  unsigned int dwSerial; // [sp+34h] [bp-494h]@4
  char Dest; // [sp+48h] [bp-480h]@4
  char v11; // [sp+64h] [bp-464h]@4
  unsigned __int8 v12; // [sp+65h] [bp-463h]@4
  int Dst[16]; // [sp+78h] [bp-450h]@4
  unsigned int dwMemberSerial[18]; // [sp+B8h] [bp-410h]@4
  char Source[168]; // [sp+100h] [bp-3C8h]@4
  char v16[32]; // [sp+1A8h] [bp-320h]@4
  char v17[32]; // [sp+1C8h] [bp-300h]@4
  int v18[11]; // [sp+1E8h] [bp-2E0h]@4
  unsigned int v19; // [sp+214h] [bp-2B4h]@4
  int v20; // [sp+218h] [bp-2B0h]@4
  char *v21; // [sp+220h] [bp-2A8h]@4
  CGuild *v22; // [sp+228h] [bp-2A0h]@4
  int j; // [sp+230h] [bp-298h]@4
  CPlayer *v24; // [sp+238h] [bp-290h]@6
  CPlayer *v25; // [sp+240h] [bp-288h]@12
  char szTran; // [sp+260h] [bp-268h]@14
  CPlayer *v27; // [sp+2E8h] [bp-1E0h]@16
  int __t; // [sp+300h] [bp-1C8h]@19
  char v29[17]; // [sp+304h] [bp-1C4h]@21
  char v30; // [sp+315h] [bp-1B3h]@21
  char v31[2]; // [sp+316h] [bp-1B2h]@21
  int v32; // [sp+318h] [bp-1B0h]@21
  char v33[363]; // [sp+31Dh] [bp-1ABh]@21
  CPlayer *pPtr; // [sp+488h] [bp-40h]@24
  _guild_member_info *pMem; // [sp+490h] [bp-38h]@26
  CPlayer::CashChangeStateFlag v36; // [sp+4A0h] [bp-28h]@26
  char *v37; // [sp+4A8h] [bp-20h]@14
  unsigned int v38; // [sp+4B0h] [bp-18h]@14
  unsigned __int64 v39; // [sp+4B8h] [bp-10h]@4
  _DB_QRY_SYN_DATA *v40; // [sp+4D0h] [bp+8h]@1

  v40 = pData;
  v1 = &v7;
  for ( i = 304i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v39 = (unsigned __int64)&v7 ^ _security_cookie;
  v8 = -1;
  dwSerial = -1;
  v11 = 0;
  v12 = 0;
  v19 = -1;
  v20 = -1;
  v21 = v40->m_sData;
  v8 = *(_DWORD *)&v40->m_sData[0];
  dwSerial = *(_DWORD *)&v40->m_sData[272];
  v19 = *(_DWORD *)&v40->m_sData[276];
  v20 = *(_DWORD *)&v40->m_sData[280];
  v11 = v40->m_sData[21];
  v12 = v40->m_sData[22];
  strcpy_0(&Dest, &v40->m_sData[4]);
  memcpy_0(Dst, v21 + 24, 0x20ui64);
  memcpy_0(dwMemberSerial, v21 + 56, 0x20ui64);
  memcpy_0(v18, v21 + 240, 0x20ui64);
  memcpy_0(v16, v21 + 224, 8ui64);
  memcpy_0(v17, v21 + 232, 8ui64);
  memcpy_0(Source, v21 + 88, 0x88ui64);
  v22 = &g_Guild[v8];
  CGuild::ReleaseTemp(v22);
  for ( j = 0; j < v12; ++j )
  {
    v24 = &g_Player + (unsigned int)Dst[j];
    if ( v24->m_bLive )
    {
      if ( v24->m_dwObjSerial == dwMemberSerial[j] )
        v24->m_Param.m_bGuildLock = 0;
    }
  }
  if ( v40->m_byResult || CGuild::IsFill(v22) )
  {
    v25 = &g_Player + v19;
    if ( v25->m_bLive && v25->m_dwObjSerial == v20 )
    {
      CPlayer::SendMsg_GuildEstablishFail(v25, -1);
      v3 = GuildCreateEventInfo::GetEstConsumeDalant(&stru_1799CA220);
      CPlayer::AddDalant(v25, v3, 0);
      W2M(&Dest, &szTran, 0x80u);
      v37 = v25->m_szItemHistoryFileName;
      v38 = CPlayerDB::GetDalant(&v25->m_Param);
      v4 = GuildCreateEventInfo::GetEstConsumeDalant(&stru_1799CA220);
      CMgrAvatorItemHistory::guild_est_money_rollback(
        &CPlayer::s_MgrItemHistory,
        v25->m_ObjID.m_wIndex,
        &szTran,
        v4,
        v38,
        v37);
    }
  }
  else
  {
    v27 = &g_Player + v19;
    if ( v27->m_bLive && v27->m_dwObjSerial == v20 )
      CPlayer::SendMsg_AlterMoneyInform(v27, 0);
    `vector constructor iterator'(&__t, 0x30ui64, 8, (void *(__cdecl *)(void *))_guild_member_info::_guild_member_info);
    for ( j = 0; j < v12; ++j )
    {
      *(&__t + 12 * j) = dwMemberSerial[j];
      strcpy_0(&v29[48 * j], &Source[17 * j]);
      v31[48 * j] = v16[j];
      *(&v32 + 12 * j) = v18[j];
      *(&v30 + 48 * j) = 0;
      v33[48 * j] = v17[j];
    }
    CGuild::EstGuild(v22, dwSerial, &Dest, v11, v12, (_guild_member_info *)&__t);
    for ( j = 0; j < v12; ++j )
    {
      pPtr = &g_Player + (unsigned int)Dst[j];
      if ( pPtr->m_bLive && pPtr->m_dwObjSerial == dwMemberSerial[j] )
      {
        pMem = CGuild::LoginMember(v22, dwMemberSerial[j], pPtr);
        CGuild::SendMsg_DownPacket(v22, 0, pMem);
        pPtr->m_pUserDB->m_AvatorData.dbAvator.m_dwGuildSerial = dwSerial;
        pPtr->m_Param.m_pGuild = v22;
        pPtr->m_Param.m_pGuildMemPtr = pMem;
        CPlayerDB::SetClassInGuild(&pPtr->m_Param, 0);
        CPlayer::CashChangeStateFlag::CashChangeStateFlag(&v36, 0);
        CPlayer::UpdateVisualVer(pPtr, (CPlayer::CashChangeStateFlag)v5->0);
        CPlayer::SendMsg_GuildJoinOtherInform(pPtr);
      }
    }
    v6 = CGuildBattleController::Instance();
    CGuildBattleController::UpdatePossibleBattleGuildList(v6);
  }
}
