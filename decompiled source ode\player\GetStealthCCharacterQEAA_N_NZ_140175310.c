/*
 * Function: ?GetStealth@CCharacter@@QEAA_N_N@Z
 * Address: 0x140175310
 */

bool __usercall CCharacter::GetStealth@<al>(CCharacter *this@<rcx>, bool bInvisible@<dl>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  CCharacter *v7; // [sp+30h] [bp+8h]@1
  bool v8; // [sp+38h] [bp+10h]@1

  v8 = bInvisible;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v7->m_bCorpse )
  {
    result = 0;
  }
  else if ( v7->m_bBreakTranspar )
  {
    result = 0;
  }
  else
  {
    _effect_parameter::GetEff_Plus(&v7->m_EP, 21);
    if ( a3 > 0.0 || _effect_parameter::GetEff_State(&v7->m_EP, 5) )
      result = 1;
    else
      result = v8 && _effect_parameter::GetEff_State(&v7->m_EP, 26);
  }
  return result;
}
