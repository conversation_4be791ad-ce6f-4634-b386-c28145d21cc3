/*
 * Function: ?GetItemStdPrice@@YAHHHHPEAE@Z
 * Address: 0x140039520
 */

__int64 __fastcall GetItemStdPrice(int nTableCode, int nItemIndex, int nRace, char *pbyMoneyKind)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@6
  __int64 v7; // [sp+0h] [bp-118h]@1
  CRecordData *v8; // [sp+20h] [bp-F8h]@4
  _base_fld *v9; // [sp+28h] [bp-F0h]@5
  _base_fld *v10; // [sp+30h] [bp-E8h]@8
  _base_fld *v11; // [sp+38h] [bp-E0h]@11
  _base_fld *v12; // [sp+40h] [bp-D8h]@14
  _base_fld *v13; // [sp+48h] [bp-D0h]@17
  _base_fld *v14; // [sp+50h] [bp-C8h]@20
  _base_fld *v15; // [sp+58h] [bp-C0h]@23
  _base_fld *v16; // [sp+60h] [bp-B8h]@27
  _base_fld *v17; // [sp+68h] [bp-B0h]@30
  _base_fld *v18; // [sp+70h] [bp-A8h]@33
  _base_fld *v19; // [sp+78h] [bp-A0h]@36
  _base_fld *v20; // [sp+80h] [bp-98h]@39
  _base_fld *v21; // [sp+88h] [bp-90h]@42
  _base_fld *v22; // [sp+90h] [bp-88h]@45
  _base_fld *v23; // [sp+98h] [bp-80h]@48
  _base_fld *v24; // [sp+A0h] [bp-78h]@51
  _base_fld *v25; // [sp+A8h] [bp-70h]@54
  _base_fld *v26; // [sp+B0h] [bp-68h]@57
  _base_fld *v27; // [sp+B8h] [bp-60h]@60
  _base_fld *v28; // [sp+C0h] [bp-58h]@63
  _base_fld *v29; // [sp+C8h] [bp-50h]@66
  _base_fld *v30; // [sp+D0h] [bp-48h]@69
  _base_fld *v31; // [sp+D8h] [bp-40h]@72
  _base_fld *v32; // [sp+E0h] [bp-38h]@75
  _base_fld *v33; // [sp+E8h] [bp-30h]@78
  _base_fld *v34; // [sp+F0h] [bp-28h]@81
  _base_fld *v35; // [sp+F8h] [bp-20h]@84
  _base_fld *v36; // [sp+100h] [bp-18h]@87
  int v37; // [sp+108h] [bp-10h]@4
  int v38; // [sp+120h] [bp+8h]@1
  char *v39; // [sp+138h] [bp+20h]@1

  v39 = pbyMoneyKind;
  v38 = nTableCode;
  v4 = &v7;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = &s_ptblItemData[v38];
  v37 = v38;
  switch ( v38 )
  {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 7:
      v9 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v9 )
        goto LABEL_89;
      *v39 = v9[4].m_strCode[36];
      result = *(_DWORD *)&v9[4].m_strCode[40];
      break;
    case 6:
      v10 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v10 )
        goto LABEL_89;
      *v39 = v10[8].m_strCode[36];
      result = *(_DWORD *)&v10[8].m_strCode[40];
      break;
    case 11:
      v11 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v11 )
        goto LABEL_89;
      *v39 = v11[4].m_strCode[8];
      result = *(_DWORD *)&v11[4].m_strCode[12];
      break;
    case 12:
      v12 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v12 )
        goto LABEL_89;
      *v39 = v12[4].m_strCode[0];
      result = *(_DWORD *)&v12[4].m_strCode[4];
      break;
    case 13:
      v13 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v13 )
        goto LABEL_89;
      *v39 = v13[6].m_strCode[44];
      result = *(_DWORD *)&v13[6].m_strCode[48];
      break;
    case 10:
      v14 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v14 )
        goto LABEL_89;
      *v39 = v14[6].m_strCode[0];
      result = *(_DWORD *)&v14[6].m_strCode[4];
      break;
    case 18:
      v15 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v15 )
        goto LABEL_89;
      *v39 = v15[5].m_strCode[8];
      result = *(_DWORD *)&v15[5].m_strCode[12];
      break;
    case 19:
      result = 0i64;
      break;
    case 20:
      v16 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v16 )
        goto LABEL_89;
      *v39 = v16[4].m_strCode[0];
      result = *(_DWORD *)&v16[4].m_strCode[4];
      break;
    case 16:
      v17 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v17 )
        goto LABEL_89;
      *v39 = v17[4].m_strCode[0];
      result = *(_DWORD *)&v17[4].m_strCode[4];
      break;
    case 17:
      v18 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v18 )
        goto LABEL_89;
      *v39 = v18[3].m_strCode[12];
      result = *(_DWORD *)&v18[3].m_strCode[16];
      break;
    case 15:
      v19 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v19 )
        goto LABEL_89;
      *v39 = v19[4].m_strCode[0];
      result = *(_DWORD *)&v19[4].m_strCode[4];
      break;
    case 8:
      v20 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v20 )
        goto LABEL_89;
      *v39 = v20[4].m_strCode[16];
      result = *(_DWORD *)&v20[4].m_strCode[20];
      break;
    case 9:
      v21 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v21 )
        goto LABEL_89;
      *v39 = v21[4].m_strCode[16];
      result = *(_DWORD *)&v21[4].m_strCode[20];
      break;
    case 21:
      v22 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v22 )
        goto LABEL_89;
      *v39 = v22[5].m_dwIndex;
      result = *(_DWORD *)&v22[5].m_strCode[0];
      break;
    case 22:
      v23 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v23 )
        goto LABEL_89;
      *v39 = v23[5].m_strCode[60];
      result = v23[6].m_dwIndex;
      break;
    case 23:
      v24 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v24 )
        goto LABEL_89;
      *v39 = v24[6].m_dwIndex;
      result = *(_DWORD *)&v24[6].m_strCode[0];
      break;
    case 24:
      v25 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v25 )
        goto LABEL_89;
      *v39 = v25[4].m_strCode[4];
      result = *(_DWORD *)&v25[4].m_strCode[8];
      break;
    case 25:
      v26 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v26 )
        goto LABEL_89;
      *v39 = v26[6].m_strCode[44];
      result = *(_DWORD *)&v26[6].m_strCode[48];
      break;
    case 26:
      v27 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v27 )
        goto LABEL_89;
      *v39 = v27[7].m_strCode[48];
      result = *(_DWORD *)&v27[7].m_strCode[52];
      break;
    case 27:
      v28 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v28 )
        goto LABEL_89;
      *v39 = v28[4].m_strCode[32];
      result = *(_DWORD *)&v28[4].m_strCode[36];
      break;
    case 28:
      v29 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v29 )
        goto LABEL_89;
      *v39 = v29[6].m_strCode[0];
      result = *(_DWORD *)&v29[6].m_strCode[4];
      break;
    case 30:
      v30 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v30 )
        goto LABEL_89;
      *v39 = v30[4].m_strCode[8];
      result = *(_DWORD *)&v30[4].m_strCode[12];
      break;
    case 31:
      v31 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v31 )
        goto LABEL_89;
      *v39 = v31[4].m_strCode[12];
      result = *(_DWORD *)&v31[4].m_strCode[16];
      break;
    case 32:
      v32 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v32 )
        goto LABEL_89;
      *v39 = v32[4].m_strCode[8];
      result = *(_DWORD *)&v32[4].m_strCode[12];
      break;
    case 33:
      v33 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v33 )
        goto LABEL_89;
      *v39 = v33[5].m_strCode[48];
      result = *(_DWORD *)&v33[5].m_strCode[52];
      break;
    case 34:
      v34 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v34 )
        goto LABEL_89;
      *v39 = v34[4].m_strCode[48];
      result = *(_DWORD *)&v34[4].m_strCode[52];
      break;
    case 35:
      v35 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v35 )
        goto LABEL_89;
      *v39 = v35[5].m_strCode[36];
      result = *(_DWORD *)&v35[5].m_strCode[40];
      break;
    case 36:
      v36 = CRecordData::GetRecord(v8, nItemIndex);
      if ( !v36 )
        goto LABEL_89;
      *v39 = v36[4].m_strCode[12];
      result = *(_DWORD *)&v36[4].m_strCode[20];
      break;
    default:
LABEL_89:
      result = 0i64;
      break;
  }
  return result;
}
