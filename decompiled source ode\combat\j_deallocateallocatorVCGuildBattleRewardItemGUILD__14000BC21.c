/*
 * Function: j_?deallocate@?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@QEAAXPEAVCGuildBattleRewardItem@GUILD_BATTLE@@_K@Z
 * Address: 0x14000BC21
 */

void __fastcall std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::deallocate(std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *this, GUILD_BATTLE::CGuildBattleRewardItem *_Ptr, unsigned __int64 __formal)
{
  std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::deallocate(this, _Ptr, __formal);
}
