/*
 * Function: ?SendMsg_<PERSON><PERSON>eeper@CHolyStoneSystem@@QEAAXHE@Z
 * Address: 0x14027F7C0
 */

void __fastcall CHolyStoneSystem::SendMsg_WaitKeeper(CHolyStoneSystem *this, int n, char byWaitType)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char v7; // [sp+35h] [bp-43h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4
  unsigned int dwClientIndex; // [sp+64h] [bp-14h]@5
  CHolyStoneSystem *v11; // [sp+80h] [bp+8h]@1
  int v12; // [sp+88h] [bp+10h]@1
  char v13; // [sp+90h] [bp+18h]@1

  v13 = byWaitType;
  v12 = n;
  v11 = this;
  v3 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = CHolyStoneSystem::GetHolyMasterRace(v11);
  szMsg = v13;
  pbyType = 25;
  v9 = 44;
  if ( v12 == -1 )
  {
    for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
    {
      if ( *(&g_Player.m_bLive + 50856 * (signed int)dwClientIndex) )
        CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &szMsg, 2u);
    }
  }
  else
  {
    CNetProcess::LoadSendMsg(unk_1414F2088, v12, &pbyType, &szMsg, 2u);
  }
}
