/*
 * Function: j_??0?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x140012125
 */

void __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>(CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint> *this)
{
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>(this);
}
