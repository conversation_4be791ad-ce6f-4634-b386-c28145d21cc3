/*
 * Function: ??R?$NewObject@V?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$00@CryptoPP@@@CryptoPP@@QEBAPEAV?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$00@1@XZ
 * Address: 0x14063BE90
 */

__int64 CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>>::operator()()
{
  __int64 v0; // rax@2
  void *v2; // [sp+20h] [bp-18h]@1
  __int64 v3; // [sp+28h] [bp-10h]@2

  v2 = operator new(8ui64);
  if ( v2 )
  {
    LODWORD(v0) = CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>(v2);
    v3 = v0;
  }
  else
  {
    v3 = 0i64;
  }
  return v3;
}
