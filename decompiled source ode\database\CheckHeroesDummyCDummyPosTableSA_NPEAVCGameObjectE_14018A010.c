/*
 * Function: ?CheckHeroesDummy@CDummyPosTable@@SA_NPEAVCGameObject@@E@Z
 * Address: 0x14018A010
 */

bool __usercall CDummyPosTable::CheckHeroesDummy@<al>(CGameObject *pObj@<rcx>, char byRaceCode@<dl>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  CGameObject *v7; // [sp+30h] [bp+8h]@1
  char v8; // [sp+38h] [bp+10h]@1

  v8 = byRaceCode;
  v7 = pObj;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v7 )
  {
    if ( (signed int)(unsigned __int8)byRaceCode < 3 )
    {
      if ( CDummyPosTable::ms_pHeroes_Dummy[(unsigned __int64)(unsigned __int8)byRaceCode] )
      {
        if ( (unsigned __int8)((int (__fastcall *)(CGameObject *))v7->vfptr->IsInTown)(v7) )
        {
          GetSqrt(
            (float *)(CDummyPosTable::ms_pHeroes_Dummy[(unsigned __int64)(unsigned __int8)v8] + 128i64),
            v7->m_fCurPos);
          result = a3 <= 100.0;
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 1;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
