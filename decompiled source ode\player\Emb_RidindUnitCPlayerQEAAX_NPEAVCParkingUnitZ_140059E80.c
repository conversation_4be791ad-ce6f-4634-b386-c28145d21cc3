/*
 * Function: ?Emb_RidindUnit@CPlayer@@QEAAX_NPEAVCParkingUnit@@@Z
 * Address: 0x140059E80
 */

void __fastcall CPlayer::Emb_RidindUnit(CPlayer *this, bool bRiding, CParkingUnit *pCreateUnit)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CPlayer::CashChangeStateFlag *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-48h]@1
  _STORAGE_LIST::_db_con *pWeapon; // [sp+20h] [bp-28h]@8
  int j; // [sp+28h] [bp-20h]@12
  _base_fld *v9; // [sp+30h] [bp-18h]@16
  CPlayer::CashChangeStateFlag v10; // [sp+38h] [bp-10h]@4
  CPlayer *v11; // [sp+50h] [bp+8h]@1
  bool v12; // [sp+58h] [bp+10h]@1
  CParkingUnit *pUnit; // [sp+60h] [bp+18h]@1

  pUnit = pCreateUnit;
  v12 = bRiding;
  v11 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CPlayer::CashChangeStateFlag::CashChangeStateFlag(&v10, 0);
  CPlayer::UpdateVisualVer(v11, (CPlayer::CashChangeStateFlag)v5->0);
  if ( v12 )
  {
    CPlayer::SendMsg_UnitRideChange(v11, 1, v11->m_pParkingUnit);
    CParkingUnit::Destroy(v11->m_pParkingUnit, 1);
    v11->m_pParkingUnit = 0i64;
    _effect_parameter::SetLock(&v11->m_EP, 1);
    _WEAPON_PARAM::FixUnit(&v11->m_pmWpn, v11->m_pUsingUnit);
    v11->m_nUnitDefFc = 0;
    for ( j = 0; j < 6; ++j )
    {
      if ( v11->m_pUsingUnit->byPart[j] != 255 )
      {
        v9 = CRecordData::GetRecord(&stru_1799C86D0 + j, v11->m_pUsingUnit->byPart[j]);
        if ( v9 )
          v11->m_nUnitDefFc += *(_DWORD *)&v9[5].m_strCode[8];
      }
    }
  }
  else
  {
    if ( pUnit )
    {
      v11->m_pParkingUnit = pUnit;
      CPlayer::SendMsg_UnitRideChange(v11, 0, pUnit);
    }
    else
    {
      CPlayer::_UnitDestroy(v11, v11->m_pUsingUnit->bySlotIndex);
    }
    v11->m_dwUnitViewOverTime = -1;
    _effect_parameter::SetLock(&v11->m_EP, 0);
    pWeapon = v11->m_Param.m_dbEquip.m_pStorageList + 6;
    if ( pWeapon->m_bLoad )
      _WEAPON_PARAM::FixWeapon(&v11->m_pmWpn, pWeapon);
    else
      _WEAPON_PARAM::FixWeapon(&v11->m_pmWpn, 0i64);
  }
  CPlayer::CalcDefTol(v11);
  CPlayer::SetHaveEffect(v11, 0);
  CPlayer::SetShapeAllBuffer(v11);
  CPlayer::CheckAlterMaxPoint(v11);
}
