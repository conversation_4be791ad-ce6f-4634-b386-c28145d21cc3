/*
 * Function: sub_14053DB10
 * Address: 0x14053DB10
 */

void __fastcall sub_14053DB10(__int64 a1)
{
  __int64 i; // r11@1
  signed __int64 v2; // rbx@4
  __int64 v3; // r9@5
  __int64 v4; // r10@5
  char v5; // cl@8
  __int64 v6; // r10@8
  signed __int64 v7; // rbx@9
  signed __int64 v8; // r9@10
  __int64 v9; // r9@11

  for ( i = a1; i; i = *(_QWORD *)(i + 48) )
  {
    if ( *(_BYTE *)(i + 9) & 0x10 && *(_DWORD *)(i + 56) )
    {
      v2 = 16i64 * *(_DWORD *)(i + 56);
      do
      {
        v2 -= 16i64;
        if ( (unsigned int)sub_14053DAD0(v2 + *(_QWORD *)(i + 24), 0) )
          *(_DWORD *)(v4 + 8) = 0;
      }
      while ( v3 );
    }
    v5 = *(_BYTE *)(i + 11);
    v6 = 1 << v5;
    if ( 1 << v5 )
    {
      v7 = 40 * v6;
      do
      {
        v7 -= 40i64;
        --v6;
        v8 = v7 + *(_QWORD *)(i + 32);
        if ( *(_DWORD *)(v8 + 8) && ((unsigned int)sub_14053DAD0(v8 + 16, 1) || (unsigned int)sub_14053DAD0(v9, 0)) )
        {
          *(_DWORD *)(v9 + 8) = 0;
          sub_14053D230(v9);
        }
      }
      while ( v6 );
    }
  }
}
