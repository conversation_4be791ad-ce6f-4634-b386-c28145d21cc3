/*
 * Function: _std::vector_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64___::_Insert_n_::_1_::catch$0
 * Address: 0x1402C6590
 */

void __fastcall __noreturn std::vector_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64___::_Insert_n_::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1

  v2 = a2;
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Destroy(
    *(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > **)(a2 + 160),
    *(CLogTypeDBTask ***)(a2 + 64),
    *(CLogTypeDBTask ***)(a2 + 72));
  std::allocator<CLogTypeDBTask *>::deallocate(
    (std::allocator<CLogTypeDBTask *> *)(*(_QWORD *)(v2 + 160) + 8i64),
    *(CLogTypeDBTask ***)(v2 + 64),
    *(_QWORD *)(v2 + 56));
  CxxThrowException_0(0i64, 0i64);
}
