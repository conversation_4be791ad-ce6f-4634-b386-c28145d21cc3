/*
 * Function: ?make_minepacket@AutominePersonal@@AEAAXGGEGK@Z
 * Address: 0x1402DC130
 */

void __fastcall AutominePersonal::make_minepacket(AutominePersonal *this, unsigned __int16 wItemIndex, unsigned __int16 wItemSerial, char byStorageIndex, unsigned __int16 nNewOre, unsigned int dwDur)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  char v10; // [sp+24h] [bp-14h]@4
  AutominePersonal *v11; // [sp+40h] [bp+8h]@1
  unsigned __int16 v12; // [sp+48h] [bp+10h]@1
  unsigned __int16 v13; // [sp+50h] [bp+18h]@1

  v13 = wItemSerial;
  v12 = wItemIndex;
  v11 = this;
  v6 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  j = 0;
  v10 = 0;
  v11->m_changed_packet.dwObjSerial = AutominePersonal::get_objserial(v11);
  v11->m_changed_packet.m_byUseBattery = v11->m_byUseBattery;
  v11->m_changed_packet.dwBattery = AutominePersonal::get_battery(v11);
  for ( j = 0; j < v11->m_changed_packet.byChangedNum; ++j )
  {
    if ( v11->m_changed_packet.change[j].wItemSerial == v13 )
    {
      v10 = 1;
      if ( !v11->m_bChanged )
        v11->m_bChanged = 1;
      v11->m_changed_packet.change[j].wItemIndex = v12;
      v11->m_changed_packet.change[j].dwDur = dwDur;
    }
  }
  if ( !v10 )
  {
    if ( !v11->m_bChanged )
      v11->m_bChanged = 1;
    v11->m_changed_packet.change[v11->m_changed_packet.byChangedNum].wItemIndex = v12;
    v11->m_changed_packet.change[v11->m_changed_packet.byChangedNum].wItemSerial = v13;
    v11->m_changed_packet.change[v11->m_changed_packet.byChangedNum++].dwDur = dwDur;
  }
  ++v11->m_dwMineCount[nNewOre];
}
