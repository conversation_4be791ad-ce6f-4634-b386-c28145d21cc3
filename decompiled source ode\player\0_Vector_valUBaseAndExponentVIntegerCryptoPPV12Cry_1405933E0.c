/*
 * Function: ??0?$_Vector_val@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@IEAA@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@1@@Z
 * Address: 0x1405933E0
 */

std::_Container_base *__fastcall std::_Vector_val<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_Vector_val<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>(std::_Container_base *a1, __int64 a2)
{
  std::_Container_base *v3; // [sp+30h] [bp+8h]@1
  __int64 v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  std::_Container_base::_Container_base(a1);
  std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>(
    &v3[1],
    v4);
  return v3;
}
