/*
 * Function: ?SearchAttackTarget@CHoly<PERSON>eeper@@QEAAPEAVCCharacter@@XZ
 * Address: 0x140133F70
 */

<PERSON>haracter *__usercall CHolyKeeper::SearchAttackTarget@<rax>(<PERSON><PERSON><PERSON><PERSON><PERSON> *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v5; // eax@12
  float v6; // xmm0_4@13
  float v7; // xmm0_4@13
  float v8; // xmm0_4@14
  float v9; // xmm0_4@15
  float v10; // xmm0_4@17
  int v11; // eax@22
  float v12; // xmm0_4@31
  float v13; // xmm0_4@34
  float v14; // xmm0_4@34
  float v15; // xmm0_4@35
  float v16; // xmm0_4@36
  unsigned __int32 v17; // ecx@39
  CCharacter *result; // rax@41
  __int64 v19; // [sp-20h] [bp-9F48h]@1
  int v20; // [sp+0h] [bp-9F28h]@22
  double v21; // [sp+8h] [bp-9F20h]@22
  double v22; // [sp+10h] [bp-9F18h]@22
  double v23; // [sp+18h] [bp-9F10h]@22
  double v24; // [sp+20h] [bp-9F08h]@22
  double v25; // [sp+28h] [bp-9F00h]@22
  double v26; // [sp+30h] [bp-9EF8h]@22
  int v27; // [sp+40h] [bp-9EE8h]@4
  float *v28; // [sp+48h] [bp-9EE0h]@4
  __int64 v29[5064]; // [sp+60h] [bp-9EC8h]@19
  int v30; // [sp+9EA4h] [bp-84h]@4
  int j; // [sp+9EA8h] [bp-80h]@6
  CCharacter *v32; // [sp+9EB0h] [bp-78h]@9
  AutominePersonal *v33; // [sp+9EB8h] [bp-70h]@9
  int v34; // [sp+9EC0h] [bp-68h]@16
  char v35; // [sp+9EC4h] [bp-64h]@16
  int v36; // [sp+9EC8h] [bp-60h]@37
  float v37; // [sp+9ECCh] [bp-5Ch]@13
  float v38; // [sp+9ED0h] [bp-58h]@14
  double v39; // [sp+9ED8h] [bp-50h]@22
  double v40; // [sp+9EE0h] [bp-48h]@22
  double v41; // [sp+9EE8h] [bp-40h]@22
  double v42; // [sp+9EF0h] [bp-38h]@22
  double v43; // [sp+9EF8h] [bp-30h]@22
  double v44; // [sp+9F00h] [bp-28h]@22
  float v45; // [sp+9F08h] [bp-20h]@34
  float v46; // [sp+9F0Ch] [bp-1Ch]@35
  CExtDummy *v47; // [sp+9F10h] [bp-18h]@39
  CHolyKeeper *v48; // [sp+9F30h] [bp+8h]@1

  v48 = this;
  v2 = alloca(a2);
  v3 = &v19;
  for ( i = 10192i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v27 = 0;
  v28 = CHolyKeeper::GetAttackPivot(v48);
  v30 = (signed int)ffloor(v48->m_pRec->m_fAttExt);
  if ( v48->m_bChaos )
    v30 /= 2;
  for ( j = 0; j < 2532; ++j )
  {
    v32 = (CCharacter *)(&g_Player.vfptr + 6357 * j);
    v33 = *(AutominePersonal **)&v32[21].m_SFContAura[1][0].m_byLv;
    if ( v33 )
    {
      if ( AutominePersonal::is_installed(v33) )
      {
        if ( ((int (__fastcall *)(AutominePersonal *))v33->vfptr->GetObjRace)(v33) != v48->m_nMasterRace
          || (v5 = MiningTicket::GetLastCriTicket((MiningTicket *)&v32[25].m_SFCont[0][2].m_wszPlayerName[12]),
              !CHolyStoneSystem::AuthMiningTicket(&g_HolySys, v5)) )
        {
          v6 = v33->m_fCurPos[0] - *v28;
          abs(v6);
          v37 = v6;
          v7 = v33->m_fCurPos[2] - v28[2];
          abs(v7);
          if ( v37 <= v7 )
          {
            v9 = v33->m_fCurPos[2] - v28[2];
            abs(v9);
            v38 = v9;
          }
          else
          {
            v8 = v33->m_fCurPos[0] - *v28;
            abs(v8);
            v38 = v8;
          }
          v34 = (signed int)ffloor(v38);
          v35 = 0;
          if ( v33->m_pCurMap == v48->m_pCurMap )
          {
            v10 = v33->m_fCurPos[1] - v28[1];
            abs(v10);
            if ( v10 <= 200.0 && v34 <= (unsigned int)v30 )
            {
              v29[v27++] = (__int64)v33;
              v35 = 1;
            }
          }
          if ( v35 && v34 > (unsigned int)v30 )
          {
            --v27;
            v39 = v28[2];
            v40 = v28[1];
            v41 = *v28;
            v42 = v33->m_fCurPos[2];
            v43 = v33->m_fCurPos[1];
            v44 = v33->m_fCurPos[0];
            v11 = ((int (__fastcall *)(AutominePersonal *))v33->vfptr->GetObjRace)(v33);
            v26 = v39;
            v25 = v40;
            v24 = v41;
            v23 = v42;
            v22 = v43;
            v21 = v44;
            v20 = v34;
            CLogFile::Write(
              &stru_1799C8E78,
              "CHolyKeeper::SearchAttackTarget()[KeeperRace:%d, MachineRace:%d, Len:%d(%d,%d,%d // %d,%d,%d)]",
              v48->m_nMasterRace,
              (unsigned int)v11);
          }
        }
      }
    }
    if ( v48->m_bPlayerCircleList[j]
      && v32->m_bLive
      && !v32->m_bCorpse
      && CPlayerDB::GetRaceCode((CPlayerDB *)&v32[1].m_fOldPos[2]) != v48->m_nMasterRace
      && (!CMainThread::IsReleaseServiceMode(&g_Main)
       || BYTE4(v32[1].vfptr) != 2 && !BYTE2(v32[25].m_SFCont[0][5].m_dwPlayerSerial))
      && !CCharacter::GetStealth(v32, 1) )
    {
      v12 = v32->m_fCurPos[1] - v28[1];
      abs(v12);
      if ( v12 <= 200.0
        && v32->m_pCurMap == v48->m_pCurMap
        && CGameObject::GetCurSecNum((CGameObject *)&v32->vfptr) != -1 )
      {
        v13 = v32->m_fCurPos[0] - *v28;
        abs(v13);
        v45 = v13;
        v14 = v32->m_fCurPos[2] - v28[2];
        abs(v14);
        if ( v45 <= v14 )
        {
          v16 = v32->m_fCurPos[2] - v28[2];
          abs(v16);
          v46 = v16;
        }
        else
        {
          v15 = v32->m_fCurPos[0] - *v28;
          abs(v15);
          v46 = v15;
        }
        v36 = (signed int)ffloor(v46);
        if ( v36 <= v30 )
        {
          if ( v48->m_pLastMoveTarget
            || (v17 = v48->m_pPosActive->m_wLineIndex,
                v47 = &v48->m_pCurMap->m_Dummy,
                CExtDummy::IsInBBox(v47, v17, v32->m_fCurPos)) )
          {
            if ( (CCharacter *)v48->m_pLastMoveTarget == v32 )
              return v32;
            v29[v27++] = (__int64)v32;
            if ( v27 >= 5064 )
              break;
          }
        }
      }
    }
  }
  if ( v27 )
    result = (CCharacter *)v29[rand() % v27];
  else
    result = 0i64;
  return result;
}
