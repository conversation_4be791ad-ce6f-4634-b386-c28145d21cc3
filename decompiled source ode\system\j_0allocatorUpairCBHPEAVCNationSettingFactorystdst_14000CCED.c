/*
 * Function: j_??0?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@std@@QEAA@XZ
 * Address: 0x14000CCED
 */

void __fastcall std::allocator<std::pair<int const,CNationSettingFactory *>>::allocator<std::pair<int const,CNationSettingFactory *>>(std::allocator<std::pair<int const ,CNationSettingFactory *> > *this)
{
  std::allocator<std::pair<int const,CNationSettingFactory *>>::allocator<std::pair<int const,CNationSettingFactory *>>(this);
}
