/*
 * Function: ?GetPlayer@CNormalGuildBattleGuildMember@GUILD_BATTLE@@QEAAPEAVCPlayer@@XZ
 * Address: 0x1403E0290
 */

CPlayer *__fastcall GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(GUILD_BATTLE::CNormalGuildBattleGuildMember *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CPlayer *result; // rax@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( GUILD_BATTLE::CNormalGuildBattleGuildMember::IsEmpty(v5) )
  {
    result = 0i64;
  }
  else if ( v5->m_pkMember->pPlayer )
  {
    result = v5->m_pkMember->pPlayer;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
