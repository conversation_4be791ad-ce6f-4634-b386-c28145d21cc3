/*
 * Function: ?RateCheck@CGoldenBoxItemMgr@@QEAAXE@Z
 * Address: 0x1404145D0
 */

void __fastcall CGoldenBoxItemMgr::RateCheck(CGoldenBoxItemMgr *this, char byIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned __int16 v5; // [sp+20h] [bp-18h]@4
  int j; // [sp+24h] [bp-14h]@4
  int v7; // [sp+28h] [bp-10h]@4
  __int16 v8; // [sp+2Ch] [bp-Ch]@4
  CGoldenBoxItemMgr *v9; // [sp+40h] [bp+8h]@1
  char v10; // [sp+48h] [bp+10h]@1

  v10 = byIndex;
  v9 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = 0;
  v7 = 0;
  v8 = 0;
  for ( j = 0; j < v9->m_golden_box_item.m_bygolden_item_num[(unsigned __int8)v10]; ++j )
  {
    if ( !CGoldenBoxItemMgr::Get_BoxItem_Count(
            v9,
            v10,
            v9->m_golden_box_item.m_golden_box_item_info[(unsigned __int8)v10][j].m_dwIndex) )
    {
      v5 += v9->m_golden_box_item.m_golden_box_item_info[(unsigned __int8)v10][j].m_wRate;
      v9->m_golden_box_item.m_golden_box_item_info[(unsigned __int8)v10][j].m_wRate = 0;
      ++v7;
    }
  }
  if ( v7 >= v9->m_golden_box_item.m_bygolden_item_num[(unsigned __int8)v10] )
    v7 = 0;
  v8 = v5 / (v9->m_golden_box_item.m_bygolden_item_num[(unsigned __int8)v10] - v7);
  for ( j = 0; j < v9->m_golden_box_item.m_bygolden_item_num[(unsigned __int8)v10]; ++j )
  {
    if ( v9->m_golden_box_item.m_golden_box_item_info[(unsigned __int8)v10][j].m_wRate )
      v9->m_golden_box_item.m_golden_box_item_info[(unsigned __int8)v10][j].m_wRate += v8;
  }
}
