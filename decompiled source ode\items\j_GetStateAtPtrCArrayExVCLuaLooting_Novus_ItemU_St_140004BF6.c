/*
 * Function: j_?GetStateAtPtr@?$CArrayEx@VCLuaLooting_Novus_Item@@U_State@1@@US@@QEAAPEAU_State@CLuaLooting_Novus_Item@@K@Z
 * Address: 0x140004BF6
 */

CLuaLooting_Novus_Item::_State *__fastcall US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::GetStateAtPtr(US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State> *this, unsigned int dwIndex)
{
  return US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::GetStateAtPtr(this, dwIndex);
}
