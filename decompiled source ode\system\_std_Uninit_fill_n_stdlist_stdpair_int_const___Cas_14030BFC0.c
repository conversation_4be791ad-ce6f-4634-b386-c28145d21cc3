/*
 * Function: _std::_Uninit_fill_n_std::list_std::pair_int_const___CashShop_fld_const_____ptr64__std::allocator_std::pair_int_const___CashShop_fld_const_____ptr64_____::_Iterator_0______ptr64_unsigned___int64_std::list_std::pair_int_const___CashShop_fld_const_____ptr64__std::allocator_std::pair_int_const___CashShop_fld_const_____ptr64_____::_Iterator_0__std::allocator_std::list_std::pair_int_const___CashShop_fld_const_____ptr64__std::allocator_std::pair_int_const___CashShop_fld_const_____ptr64_____::_Iterator_0______::_1_::catch$0
 * Address: 0x14030BFC0
 */

void __fastcall __noreturn std::_Uninit_fill_n_std::list_std::pair_int_const___CashShop_fld_const_____ptr64__std::allocator_std::pair_int_const___CashShop_fld_const_____ptr64_____::_Iterator_0______ptr64_unsigned___int64_std::list_std::pair_int_const___CashShop_fld_const_____ptr64__std::allocator_std::pair_int_const___CashShop_fld_const_____ptr64_____::_Iterator_0__std::allocator_std::list_std::pair_int_const___CashShop_fld_const_____ptr64__std::allocator_std::pair_int_const___CashShop_fld_const_____ptr64_____::_Iterator_0______::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 64); *(_QWORD *)(i + 32) += 24i64 )
    std::allocator<std::list<std::pair<int const,_CashShop_fld const *>,std::allocator<std::pair<int const,_CashShop_fld const *>>>::_Iterator<0>>::destroy(
      *(std::allocator<std::list<std::pair<int const ,_CashShop_fld const *>,std::allocator<std::pair<int const ,_CashShop_fld const *> > >::_Iterator<0> > **)(i + 88),
      *(std::list<std::pair<int const ,_CashShop_fld const *>,std::allocator<std::pair<int const ,_CashShop_fld const *> > >::_Iterator<0> **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
