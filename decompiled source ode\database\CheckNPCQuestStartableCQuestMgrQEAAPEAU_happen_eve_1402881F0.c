/*
 * Function: ?CheckNPCQuestStartable@CQuestMgr@@QEAAPEAU_happen_event_cont@@PEADEKK@Z
 * Address: 0x1402881F0
 */

_happen_event_cont *__fastcall CQuestMgr::CheckNPCQuestStartable(CQuestMgr *this, char *pszEventCode, char byRaceCode, unsigned int dwQuestIndex, unsigned int dwHappenIndex)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  _happen_event_cont *result; // rax@5
  __int64 v8; // [sp+0h] [bp-68h]@1
  int nRaceCode; // [sp+20h] [bp-48h]@23
  _base_fld *v10; // [sp+30h] [bp-38h]@4
  _base_fld *v11; // [sp+38h] [bp-30h]@8
  _happen_event_node *pPoint; // [sp+40h] [bp-28h]@10
  _base_fld *v13; // [sp+48h] [bp-20h]@12
  char v14; // [sp+50h] [bp-18h]@15
  int j; // [sp+54h] [bp-14h]@15
  _happen_event_condition_node *pCond; // [sp+58h] [bp-10h]@17
  CQuestMgr *v17; // [sp+70h] [bp+8h]@1
  const char *Str1; // [sp+78h] [bp+10h]@1
  char v19; // [sp+80h] [bp+18h]@1
  unsigned int n; // [sp+88h] [bp+20h]@1

  n = dwQuestIndex;
  v19 = byRaceCode;
  Str1 = pszEventCode;
  v17 = this;
  v5 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v10 = CRecordData::GetRecord(CQuestMgr::s_tblQuestHappenEvent + 1, dwHappenIndex);
  if ( v10 )
  {
    if ( !strcmp_0(Str1, v10->m_strCode) )
    {
      v11 = CRecordData::GetRecord(CQuestMgr::s_tblQuest, n);
      if ( v11 )
      {
        pPoint = (_happen_event_node *)&v10[1].m_strCode[704 * (unsigned __int8)v19];
        if ( pPoint->m_bUse )
        {
          v13 = CRecordData::GetRecord(CQuestMgr::s_tblQuest, (const char *)pPoint->m_strLinkQuest);
          if ( v13 && v13 == v11 )
          {
            v14 = 1;
            for ( j = 0; j < 5; ++j )
            {
              pCond = &pPoint->m_CondNode[j];
              if ( pCond->m_nCondType == -1 )
                break;
              if ( !CQuestMgr::_CheckCondition(v17, pCond) )
              {
                v14 = 0;
                break;
              }
            }
            if ( v14 )
            {
              nRaceCode = (unsigned __int8)v19;
              _happen_event_cont::set(
                &v17->m_LastHappenEvent,
                pPoint,
                quest_happen_type_npc,
                v10->m_dwIndex,
                (unsigned __int8)v19);
              result = &v17->m_LastHappenEvent;
            }
            else
            {
              result = 0i64;
            }
          }
          else
          {
            result = 0i64;
          }
        }
        else
        {
          result = 0i64;
        }
      }
      else
      {
        result = 0i64;
      }
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
