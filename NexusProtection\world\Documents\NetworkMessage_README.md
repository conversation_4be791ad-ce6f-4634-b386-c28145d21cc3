# NetworkMessage Module

## Overview

The NetworkMessage module provides network message structures and size calculation utilities for game communication protocols. This module has been refactored from multiple decompiled C source files to create a unified, modern C++17/C++20 network messaging system.

## Original Sources

This module consolidates the following original functions:

- **_add_char_result_zone::size** (Address: `0x14011F870`)
- **_enter_world_result_zone::size** (Address: `0x14011F250`)
- **_reged_char_result_zone::size** (Address: `0x14011F6F0`)
- **_target_monster_contsf_allinform_zocl::size** (Address: `0x1400F0140`)

## Files

- **Header**: `NexusProtection/world/Headers/NetworkMessage.h`
- **Source**: `NexusProtection/world/Source/NetworkMessage.cpp`
- **Documentation**: `NexusProtection/world/Documents/NetworkMessage_README.md`

## Key Features

### Modern C++ Implementation
- Type-safe message structures
- Automatic validation and clamping
- Exception safety with strong guarantees
- Const-correctness throughout
- Modern naming conventions

### Network Protocol Support
- Variable-length message size calculation
- Automatic data validation
- Range clamping for safety
- Legacy C interface compatibility
- Debugging and diagnostic support

### Message Types
- Character management messages
- World entry/exit messages
- Monster effect notifications
- Variable-length data handling

## Message Structures

### AddCharResultZone
```cpp
struct AddCharResultZone {
    int64_t size() const;           // Returns constant size (2 bytes)
    bool validate() const;          // Always returns true
};
```
Simple fixed-size message for character addition results.

### EnterWorldResultZone
```cpp
struct EnterWorldResultZone {
    int64_t size() const;           // Returns constant size (3 bytes)
    bool validate() const;          // Always returns true
};
```
Simple fixed-size message for world entry results.

### RegedCharResultZone
```cpp
struct RegedCharResultZone {
    uint8_t byCharNum;              // Number of characters (0-3)
    
    int64_t size();                 // Variable size calculation
    bool validate() const;          // Validates character count
    void setCharNum(uint8_t count); // Sets with validation
};
```
Variable-length message for character registration results.

### TargetMonsterContsfAllinformZocl
```cpp
struct TargetMonsterContsfAllinformZocl {
    uint8_t byContCount;            // Number of effects (0-8)
    
    int64_t size();                 // Variable size calculation
    bool validate() const;          // Validates effect count
    void setContCount(uint8_t count); // Sets with validation
};
```
Variable-length message for monster continuous effect information.

## API Reference

### Size Calculation Functions

#### Fixed-Size Messages
```cpp
// AddCharResultZone
int64_t size() const;  // Returns 2

// EnterWorldResultZone  
int64_t size() const;  // Returns 3
```

#### Variable-Size Messages
```cpp
// RegedCharResultZone
int64_t size();  // Returns 221 - 69 * (3 - byCharNum)

// TargetMonsterContsfAllinformZocl
int64_t size();  // Returns 21 - 2 * (8 - byContCount)
```

### Utility Functions

#### Validation
```cpp
bool IsValidCharCount(uint8_t charCount);    // Validates 0-3 range
bool IsValidContCount(uint8_t contCount);    // Validates 0-8 range
```

#### Size Calculation Helpers
```cpp
int64_t CalculateVariableCharSize(uint8_t charCount, 
                                  int64_t baseSize, 
                                  int64_t entrySize, 
                                  uint8_t maxCount);

int64_t CalculateVariableEffectSize(uint8_t effectCount, 
                                    int64_t baseSize, 
                                    int64_t entrySize, 
                                    uint8_t maxCount);
```

#### Range Management
```cpp
uint8_t ClampToRange(uint8_t value, uint8_t maxValue);
const char* GetMessageTypeName(int messageType);
```

## Usage Examples

### Fixed-Size Messages
```cpp
#include "NetworkMessage.h"

// Create and use fixed-size messages
NetworkMessage::AddCharResultZone addResult;
int64_t size1 = addResult.size();  // Returns 2

NetworkMessage::EnterWorldResultZone enterResult;
int64_t size2 = enterResult.size();  // Returns 3
```

### Variable-Size Messages
```cpp
// Character registration message
NetworkMessage::RegedCharResultZone regedMsg(2);  // 2 characters
int64_t size = regedMsg.size();  // Returns 221 - 69 * (3 - 2) = 152

// Validate and modify
if (regedMsg.validate()) {
    regedMsg.setCharNum(3);  // Set to maximum
    size = regedMsg.size();  // Returns 221 - 69 * (3 - 3) = 221
}

// Monster effect message
NetworkMessage::TargetMonsterContsfAllinformZocl effectMsg(5);  // 5 effects
size = effectMsg.size();  // Returns 21 - 2 * (8 - 5) = 15
```

### Range Validation and Clamping
```cpp
// Automatic clamping on invalid input
NetworkMessage::RegedCharResultZone invalidMsg(10);  // > 3, will be clamped to 0
int64_t size = invalidMsg.size();  // Returns 221 - 69 * (3 - 0) = 14

// Manual validation
if (!NetworkMessage::IsValidCharCount(10)) {
    // Handle invalid input
}
```

### Utility Functions
```cpp
// Calculate custom variable sizes
int64_t customSize = NetworkMessage::CalculateVariableCharSize(
    2,      // charCount
    100,    // baseSize
    10,     // entrySize
    5       // maxCount
);  // Returns 100 - 10 * (5 - 2) = 70

// Get message type names for debugging
const char* typeName = NetworkMessage::GetMessageTypeName(1);  // "ADD_CHAR_RESULT"
```

## Legacy Compatibility

The module provides C-style interfaces for compatibility with existing code:

```cpp
extern "C" {
    int64_t _add_char_result_zone_size(void* this_ptr);
    int64_t _enter_world_result_zone_size(void* this_ptr);
    int64_t _reged_char_result_zone_size(void* this_ptr);
    int64_t _target_monster_contsf_allinform_zocl_size(void* this_ptr);
}
```

## Size Calculation Formulas

### Original Formulas (Preserved)
1. **AddCharResultZone**: `2`
2. **EnterWorldResultZone**: `3`
3. **RegedCharResultZone**: `221 - 69 * (3 - byCharNum)`
4. **TargetMonsterContsfAllinformZocl**: `21 - 2 * (8 - byContCount)`

### General Formula
For variable-length messages:
```
size = baseSize - entrySize * (maxCount - actualCount)
```

This formula calculates the total size by starting with a base size that assumes maximum entries, then subtracting the size of unused entries.

## Constants and Limits

```cpp
namespace Constants {
    constexpr uint8_t MAX_CHAR_COUNT = 3;           // Maximum characters
    constexpr uint8_t MAX_CONT_COUNT = 8;           // Maximum effects
    constexpr int64_t BASE_REGED_CHAR_SIZE = 221;   // Base character message size
    constexpr int64_t CHAR_ENTRY_SIZE = 69;         // Size per character entry
    constexpr int64_t BASE_TARGET_MONSTER_SIZE = 21; // Base monster message size
    constexpr int64_t CONT_ENTRY_SIZE = 2;          // Size per effect entry
}
```

## Error Handling

The module uses multiple error handling strategies:

1. **Range Clamping**: Invalid values are automatically clamped to valid ranges
2. **Validation Methods**: Each structure provides validation methods
3. **Exception Safety**: Strong exception guarantee for all operations
4. **Legacy Compatibility**: C interface returns -1 on errors

## Performance Considerations

- All size calculations are O(1) constant time
- No dynamic memory allocation
- Minimal computational overhead
- Cache-friendly data structures
- Inlined utility functions for performance

## Integration Notes

- Compatible with Visual Studio 2022 (v143 toolset)
- Requires C++17 or later for constexpr and structured bindings
- No external dependencies beyond standard library
- Thread-safe for read operations
- Exception-safe with strong guarantee

## Future Enhancements

1. **Serialization**: Binary serialization/deserialization support
2. **Compression**: Message compression for network efficiency
3. **Encryption**: Message encryption and authentication
4. **Versioning**: Protocol version management
5. **Metrics**: Performance and usage metrics collection
