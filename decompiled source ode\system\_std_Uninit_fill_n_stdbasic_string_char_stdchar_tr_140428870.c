/*
 * Function: _std::_Uninit_fill_n_std::basic_string_char_std::char_traits_char__std::allocator_char________ptr64_unsigned___int64_std::basic_string_char_std::char_traits_char__std::allocator_char____std::allocator_std::basic_string_char_std::char_traits_char__std::allocator_char________::_1_::catch$0
 * Address: 0x140428870
 */

void __fastcall __noreturn std::_Uninit_fill_n_std::basic_string_char_std::char_traits_char__std::allocator_char________ptr64_unsigned___int64_std::basic_string_char_std::char_traits_char__std::allocator_char____std::allocator_std::basic_string_char_std::char_traits_char__std::allocator_char________::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 64); *(_QWORD *)(i + 32) += 48i64 )
    std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>::destroy(
      *(std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > **)(i + 88),
      *(std::basic_string<char,std::char_traits<char>,std::allocator<char> > **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
