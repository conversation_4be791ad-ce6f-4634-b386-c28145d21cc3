/*
 * Function: ?GetItemTableCode@@YAHPEAD@Z
 * Address: 0x1400362B0
 */

signed __int64 __fastcall GetItemTableCode(char *psItemCode)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  char Dest; // [sp+24h] [bp-24h]@4
  char v6; // [sp+26h] [bp-22h]@4
  const char *Source; // [sp+50h] [bp+8h]@1

  Source = psItemCode;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  strncpy(&Dest, Source, 2ui64);
  v6 = 0;
  if ( !strcmp_0(&Dest, "iu") )
  {
    result = 0i64;
  }
  else if ( !strcmp_0(&Dest, "il") )
  {
    result = 1i64;
  }
  else if ( !strcmp_0(&Dest, "ig") )
  {
    result = 2i64;
  }
  else if ( !strcmp_0(&Dest, "is") )
  {
    result = 3i64;
  }
  else if ( !strcmp_0(&Dest, "ih") )
  {
    result = 4i64;
  }
  else if ( !strcmp_0(&Dest, "id") )
  {
    result = 5i64;
  }
  else if ( !strcmp_0(&Dest, "iw") )
  {
    result = 6i64;
  }
  else if ( !strcmp_0(&Dest, "im") )
  {
    result = 11i64;
  }
  else if ( !strcmp_0(&Dest, "ie") )
  {
    result = 12i64;
  }
  else if ( !strcmp_0(&Dest, "ip") )
  {
    result = 13i64;
  }
  else if ( !strcmp_0(&Dest, "ib") )
  {
    result = 10i64;
  }
  else if ( !strcmp_0(&Dest, "if") )
  {
    result = 14i64;
  }
  else if ( !strcmp_0(&Dest, "ic") )
  {
    result = 15i64;
  }
  else if ( !strcmp_0(&Dest, "it") )
  {
    result = 16i64;
  }
  else if ( !strcmp_0(&Dest, "io") )
  {
    result = 17i64;
  }
  else if ( !strcmp_0(&Dest, "ir") )
  {
    result = 18i64;
  }
  else if ( !strcmp_0(&Dest, "in") )
  {
    result = 19i64;
  }
  else if ( !strcmp_0(&Dest, "iy") )
  {
    result = 20i64;
  }
  else if ( !strcmp_0(&Dest, "ik") )
  {
    result = 7i64;
  }
  else if ( !strcmp_0(&Dest, "ii") )
  {
    result = 8i64;
  }
  else if ( !strcmp_0(&Dest, "ia") )
  {
    result = 9i64;
  }
  else if ( !strcmp_0(&Dest, "iz") )
  {
    result = 21i64;
  }
  else if ( !strcmp_0(&Dest, "iq") )
  {
    result = 22i64;
  }
  else if ( !strcmp_0(&Dest, "ix") )
  {
    result = 23i64;
  }
  else if ( !strcmp_0(&Dest, "ij") )
  {
    result = 24i64;
  }
  else if ( !strcmp_0(&Dest, "gt") )
  {
    result = 25i64;
  }
  else if ( !strcmp_0(&Dest, "tr") )
  {
    result = 26i64;
  }
  else if ( !strcmp_0(&Dest, "sk") )
  {
    result = 27i64;
  }
  else if ( !strcmp_0(&Dest, "ti") )
  {
    result = 28i64;
  }
  else if ( !strcmp_0(&Dest, "ev") )
  {
    result = 29i64;
  }
  else if ( !strcmp_0(&Dest, "re") )
  {
    result = 30i64;
  }
  else if ( !strcmp_0(&Dest, "bx") )
  {
    result = 31i64;
  }
  else if ( !strcmp_0(&Dest, "fi") )
  {
    result = 32i64;
  }
  else if ( !strcmp_0(&Dest, "un") )
  {
    result = 33i64;
  }
  else if ( !strcmp_0(&Dest, "rd") )
  {
    result = 34i64;
  }
  else if ( !strcmp_0(&Dest, "lk") )
  {
    result = 35i64;
  }
  else if ( !strcmp_0(&Dest, "cu") )
  {
    result = 36i64;
  }
  else
  {
    result = 0xFFFFFFFFi64;
  }
  return result;
}
