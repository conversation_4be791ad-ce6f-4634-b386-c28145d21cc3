/*
 * Function: ?_get_player@CashDbWorker@@AEAAPEAVCPlayer@@GK@Z
 * Address: 0x1402F2F10
 */

CPlayer *__fastcall CashDbWorker::_get_player(CashDbWorker *this, unsigned __int16 wSock, unsigned int dwAvator)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CPlayer *result; // rax@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  unsigned __int16 v7; // [sp+38h] [bp+10h]@1

  v7 = wSock;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( CPlayerDB::GetCharSerial((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * wSock)) == dwAvator )
    result = &g_Player + v7;
  else
    result = 0i64;
  return result;
}
