/*
 * Function: ?LogFileOperSetting@CNetProcess@@QEAAX_N00@Z
 * Address: 0x14047A040
 */

void __fastcall CNetProcess::LogFileOperSetting(CNetProcess *this, bool bRecv, bool bSend, bool bSystem)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CNetProcess *v7; // [sp+30h] [bp+8h]@1
  bool v8; // [sp+40h] [bp+18h]@1
  bool v9; // [sp+48h] [bp+20h]@1

  v9 = bSystem;
  v8 = bSend;
  v7 = this;
  v4 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  CLogFile::SetWriteAble(&v7->m_LogFile[1], bRecv);
  CLogFile::SetWriteAble(v7->m_LogFile, v8);
  CLogFile::SetWriteAble(&v7->m_LogFile[2], v9);
}
