/*
 * Function: _CUnmannedTraderScheduler::_CUnmannedTraderScheduler_::_1_::dtor$0
 * Address: 0x140393240
 */

void __fastcall CUnmannedTraderScheduler::_CUnmannedTraderScheduler_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>((std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)(*(_QWORD *)(a2 + 64) + 24i64));
}
