/*
 * Function: ?InitSDM@CLuaScriptMgr@@QEAA_NXZ
 * Address: 0x1403FF5A0
 */

char __fastcall CLuaScriptMgr::InitSDM(CLuaScriptMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CLuaEventMgr *v3; // rax@4
  char result; // al@5
  CLuaLootingMgr *v5; // rax@6
  unsigned int v6; // eax@8
  unsigned int v7; // eax@8
  __int64 v8; // [sp+0h] [bp-168h]@1
  char Dst; // [sp+40h] [bp-128h]@8
  unsigned __int64 v10; // [sp+150h] [bp-18h]@4
  CLuaScriptMgr *v11; // [sp+170h] [bp+8h]@1

  v11 = this;
  v1 = &v8;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = (unsigned __int64)&v8 ^ _security_cookie;
  v3 = CLuaEventMgr::Instance();
  if ( CLuaEventMgr::InitSDM(v3) )
  {
    v5 = CLuaLootingMgr::Instance();
    if ( CLuaLootingMgr::InitSDM(v5, 0xAu, 0x64u) )
    {
      CreateDirectoryA("..\\ZoneServerLog\\LuaScriptLog", 0i64);
      memset_0(&Dst, 0, 0x100ui64);
      v6 = GetKorLocalTime();
      sprintf_s<256>((char (*)[256])&Dst, "..\\ZoneServerLog\\LuaScriptLog\\%LuaScriptError%d.log", v6);
      CLogFile::SetWriteLogFile(&v11->m_LogScriptError, &Dst, 1, 1, 1, 1);
      memset_0(&Dst, 0, 0x100ui64);
      v7 = GetKorLocalTime();
      sprintf_s<256>((char (*)[256])&Dst, "..\\ZoneServerLog\\LuaScriptLog\\%LuaScriptState%d.log", v7);
      CLogFile::SetWriteLogFile(&v11->m_LogScriptState, &Dst, 1, 1, 1, 1);
      v11->m_MasterState = lua_my_open();
      luaopen_base(v11->m_MasterState);
      luaopen_string(v11->m_MasterState);
      luaopen_table(v11->m_MasterState);
      luaopen_math(v11->m_MasterState);
      lua_settop(v11->m_MasterState, 0i64);
      lua_pushcclosure(v11->m_MasterState, LuaScripAlert, 0i64);
      lua_setfield(v11->m_MasterState, 4294957294i64, "_ALERT");
      if ( CLuaScriptMgr::_Regist_Novus(v11) )
      {
        lua_tinker::dofile(v11->m_MasterState, _MASTER_STATE_FILE);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
