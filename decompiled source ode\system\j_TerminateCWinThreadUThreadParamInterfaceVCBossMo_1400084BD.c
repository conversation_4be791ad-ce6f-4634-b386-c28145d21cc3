/*
 * Function: j_?Terminate@?$CWinThread@U?$ThreadParamInterface@VCBossMonsterScheduleSystem@@VAbstractThreadPool@US@@@US@@@US@@UEAAXK@Z
 * Address: 0x1400084BD
 */

void __fastcall US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::Terminate(US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> > *this, unsigned int dwWaitTime)
{
  US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::Terminate(
    this,
    dwWaitTime);
}
