/*
 * Function: ?init@TiXmlString@@AEAAX_K0@Z
 * Address: 0x14052CB00
 */

void __fastcall TiXmlString::init(TiXmlString *this, unsigned __int64 a2, unsigned __int64 a3)
{
  unsigned __int64 v3; // rdi@1
  unsigned __int64 v4; // rsi@1
  TiXmlString *v5; // rbx@1
  TiXmlString::Rep *v6; // rax@2

  v3 = a3;
  v4 = a2;
  v5 = this;
  if ( a3 )
  {
    v6 = (TiXmlString::Rep *)operator new(saturated_mul(4ui64, (a3 + 27) >> 2));
    v5->rep_ = v6;
    v6->size = v4;
    v5->rep_->str[v4] = 0;
    v5->rep_->capacity = v3;
  }
  else
  {
    this->rep_ = (TiXmlString::Rep *)&TiXmlString::nullrep_;
  }
}
