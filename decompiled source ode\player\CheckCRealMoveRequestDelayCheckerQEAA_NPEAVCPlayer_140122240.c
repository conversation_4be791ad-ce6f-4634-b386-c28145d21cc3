/*
 * Function: ?Check@CRealMoveRequestDelayChecker@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x140122240
 */

char __fastcall CRealMoveRequestDelayChecker::Check(CRealMoveRequestDelayChecker *this, CPlayer *pkUser)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned int *v5; // rax@9
  char *v6; // rax@18
  unsigned int *v7; // rax@31
  __int64 v8; // [sp+0h] [bp-108h]@1
  unsigned int pInx; // [sp+34h] [bp-D4h]@6
  unsigned int dwCurTime; // [sp+44h] [bp-C4h]@8
  char v11; // [sp+48h] [bp-C0h]@8
  char Dest; // [sp+60h] [bp-A8h]@18
  char v13; // [sp+61h] [bp-A7h]@18
  unsigned __int64 v14; // [sp+F0h] [bp-18h]@4
  CRealMoveRequestDelayChecker *v15; // [sp+110h] [bp+8h]@1
  CPlayer *v16; // [sp+118h] [bp+10h]@1

  v16 = pkUser;
  v15 = this;
  v2 = &v8;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v14 = (unsigned __int64)&v8 ^ _security_cookie;
  if ( std::vector<unsigned long,std::allocator<unsigned long>>::empty(&v15->m_vecDelayList) )
  {
    result = 0;
  }
  else
  {
    pInx = -1;
    if ( CRealMoveRequestDelayChecker::GetCurInx(v15, &pInx) )
    {
      dwCurTime = clock();
      v11 = 1;
      if ( *std::vector<unsigned long,std::allocator<unsigned long>>::operator[](&v15->m_vecDelayList, pInx) )
      {
        if ( CRealMoveRequestDelayChecker::CheckDelay(v15, dwCurTime, pInx) )
        {
          if ( v15->m_bPrevRet )
            ++v15->m_wContinueValiedCount;
          if ( (signed int)v15->m_wContinueValiedCount >= 3 && (signed int)v15->m_wContinueMissCount >= 1 )
            --v15->m_wContinueMissCount;
          v15->m_bPrevRet = 1;
          if ( (signed int)v15->m_wContinueValiedCount >= 3 )
          {
            v15->m_wContinueValiedCount = 0;
            if ( (signed int)++v15->m_wTotalContinueValiedCount >= 2 )
            {
              v15->m_wTotalContinueValiedCount = 0;
              if ( (signed int)v15->m_wTotalMissCount >= 1 )
                --v15->m_wTotalMissCount;
            }
          }
        }
        else
        {
          v15->m_wContinueValiedCount = 0;
          if ( v15->m_bPrevRet )
          {
            if ( (signed int)++v15->m_wSingleMissCount >= 10 )
            {
              v15->m_wSingleMissCount = 0;
              ++v15->m_wTotalMissCount;
            }
          }
          else
          {
            ++v15->m_wContinueMissCount;
          }
          v15->m_bPrevRet = 0;
          if ( (signed int)v15->m_wContinueMissCount >= 3 )
          {
            v11 = 0;
            v15->m_wContinueMissCount = 0;
            ++v15->m_wTotalMissCount;
          }
          if ( (signed int)v15->m_wTotalMissCount >= 5 )
          {
            v11 = 0;
            v15->m_wTotalMissCount = 0;
            Dest = 0;
            memset(&v13, 0, 0x7Fui64);
            v6 = CPlayerDB::GetCharNameA(&v16->m_Param);
            sprintf(&Dest, "CLOSE>> Invalid Real Move Request, id:%s, char:%s", v16->m_pUserDB->m_szAccountID, v6);
            CNetworkEX::Close(&g_Network, 0, v16->m_ObjID.m_wIndex, 0, &Dest);
          }
        }
        CRealMoveRequestDelayChecker::IncNodeIndex(v15);
        if ( CRealMoveRequestDelayChecker::GetCurInx(v15, &pInx) )
        {
          v7 = std::vector<unsigned long,std::allocator<unsigned long>>::operator[](&v15->m_vecDelayList, pInx);
          *v7 = dwCurTime;
          result = v11;
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        v5 = std::vector<unsigned long,std::allocator<unsigned long>>::operator[](&v15->m_vecDelayList, pInx);
        *v5 = dwCurTime;
        result = 1;
      }
    }
    else
    {
      result = 0;
    }
  }
  return result;
}
