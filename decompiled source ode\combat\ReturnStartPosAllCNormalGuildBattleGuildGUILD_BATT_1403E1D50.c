/*
 * Function: ?ReturnStartPosAll@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAA_NPEAVCNormalGuildBattleField@2@@Z
 * Address: 0x1403E1D50
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::ReturnStartPosAll(GUILD_BATTLE::CNormalGuildBattleGuild *this, GUILD_BATTLE::CNormalGuildBattleField *pkField)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CPlayer *v5; // rax@10
  __int64 v6; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@6
  GUILD_BATTLE::CNormalGuildBattleGuild *v8; // [sp+40h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v9; // [sp+48h] [bp+10h]@1

  v9 = pkField;
  v8 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v8->m_pkGuild )
  {
    for ( j = 0; j < 50; ++j )
    {
      if ( GUILD_BATTLE::CNormalGuildBattleGuildMember::IsExist(&v8->m_kMember[j]) )
      {
        GUILD_BATTLE::CNormalGuildBattleGuildMember::ReturnStartPos(&v8->m_kMember[j]);
        v5 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(&v8->m_kMember[j]);
        if ( !GUILD_BATTLE::CNormalGuildBattleField::MoveStartPos(v9, v8->m_byColorInx, 8, v5) )
          return 0;
      }
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
