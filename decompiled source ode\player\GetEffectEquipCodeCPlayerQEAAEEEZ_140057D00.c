/*
 * Function: ?GetEffectEquipCode@CPlayer@@QEAAEEE@Z
 * Address: 0x140057D00
 */

char __fastcall CPlayer::GetEffectEquipCode(CPlayer *this, char byStorageCode, char bySlotIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  _base_fld **v6; // [sp+0h] [bp-18h]@1
  CPlayer *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v3 = (__int64 *)&v6;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = 0i64;
  if ( byStorageCode == 1 )
    v6 = (_base_fld **)&v7->m_byEffectEquipCode[(unsigned __int8)bySlotIndex];
  else
    v6 = (_base_fld **)((char *)&v7->m_pRecordSet + (unsigned __int8)bySlotIndex + 49386);
  return *(_BYTE *)v6;
}
