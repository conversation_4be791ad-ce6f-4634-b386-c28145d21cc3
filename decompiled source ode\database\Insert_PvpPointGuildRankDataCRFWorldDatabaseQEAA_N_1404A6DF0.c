/*
 * Function: ?Insert_PvpPointGuildRankData@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404A6DF0
 */

bool __fastcall CRFWorldDatabase::Insert_PvpPointGuildRankData(CRFWorldDatabase *this, char *szDate)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-458h]@1
  char Dst; // [sp+30h] [bp-428h]@4
  unsigned __int64 v7; // [sp+440h] [bp-18h]@4
  CRFWorldDatabase *v8; // [sp+460h] [bp+8h]@1
  char *v9; // [sp+468h] [bp+10h]@1

  v9 = szDate;
  v8 = this;
  v2 = &v5;
  for ( i = 276i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = (unsigned __int64)&v5 ^ _security_cookie;
  memset_0(&Dst, 0, 0x400ui64);
  sprintf(
    &Dst,
    "insert into [dbo].[tbl_PvpPointGuildRank%s] select g.serial, 0 as rank, g.id, g.race, g.grade, s.killpvppoint, s.gui"
    "ldbattlepvppoint , s.killpvppoint+s.guildbattlepvppoint as sumpvppoint, 0 as sumlv from [dbo].[tbl_WeeklyGuildPVPPoi"
    "ntSum] as s join tbl_guild as g on s.serial = g.serial and g.dck = 0",
    v9);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v8->vfptr, &Dst, 0);
}
