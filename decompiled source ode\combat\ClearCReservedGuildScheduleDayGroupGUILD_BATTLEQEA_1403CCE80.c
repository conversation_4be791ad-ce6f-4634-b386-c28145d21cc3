/*
 * Function: ?Clear@CReservedGuildScheduleDayGroup@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403CCE80
 */

char __fastcall GUILD_BATTLE::CReservedGuildScheduleDayGroup::Clear(GUILD_BATTLE::CReservedGuildScheduleDayGroup *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CReservedGuildScheduleDayGroup *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < v6->m_uiMapCnt; ++j )
  {
    if ( !GUILD_BATTLE::CReservedGuildScheduleMapGroup::Clear(&v6->m_pkList[j]) )
      return 0;
  }
  v6->m_byToday = GetCurDay();
  v6->m_byTommorow = GetNextDay();
  return 1;
}
