/*
 * Function: ?IsBeDamagedAble@CPlayer@@UEAA_NPEAVCCharacter@@@Z
 * Address: 0x1400F0DC0
 */

bool __fastcall CPlayer::IsBeDamagedAble(CPlayer *this, CCharacter *pAtter)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@10
  CCharacter *v5; // [sp+0h] [bp-28h]@1
  CCharacter *v6; // [sp+8h] [bp-20h]@18
  char v7; // [sp+10h] [bp-18h]@5
  CPlayer *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v2 = (__int64 *)&v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pAtter->m_ObjID.m_byID )
    return 1;
  v7 = pAtter->m_ObjID.m_byID;
  if ( v7 )
  {
    if ( v7 == 3 )
    {
      v6 = pAtter;
      if ( !*(_BYTE *)(*(_QWORD *)&pAtter[1].m_bLive + 1922i64) && v8->m_bInGuildBattle )
        return 0;
      if ( *(_BYTE *)(*(_QWORD *)&v6[1].m_bLive + 1922i64) && !v8->m_bInGuildBattle )
        return 0;
      if ( !*(_BYTE *)(*(_QWORD *)&v6[1].m_bLive + 1922i64) && !v8->m_bInGuildBattle )
        return 1;
      if ( *(_BYTE *)(*(_QWORD *)&v6[1].m_bLive + 1924i64) == v8->m_byGuildBattleColorInx )
        return 0;
    }
    return 1;
  }
  v5 = pAtter;
  if ( BYTE2(pAtter[1].m_fCurPos[2]) || !v8->m_bInGuildBattle )
  {
    if ( !BYTE2(v5[1].m_fCurPos[2]) || v8->m_bInGuildBattle )
    {
      if ( BYTE2(v5[1].m_fCurPos[2]) || v8->m_bInGuildBattle )
        result = v8->m_byGuildBattleColorInx != LOBYTE(v5[1].m_fAbsPos[0]);
      else
        result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
