/*
 * Function: ?SendMsg_<PERSON>D<PERSON>roy@CDarkHoleChannel@@QEAAXPEAEPEADH@Z
 * Address: 0x14026CD30
 */

void __fastcall CDarkHoleChannel::SendMsg_GateDestroy(CDarkHoleChannel *this, char *byType, char *pSend, int nSize)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  int j; // [sp+30h] [bp-18h]@4
  CDarkHoleChannel *v8; // [sp+50h] [bp+8h]@1
  char *pbyType; // [sp+58h] [bp+10h]@1
  char *szMsg; // [sp+60h] [bp+18h]@1
  int v11; // [sp+68h] [bp+20h]@1

  v11 = nSize;
  szMsg = pSend;
  pbyType = byType;
  v8 = this;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  for ( j = 0; j < 32; ++j )
  {
    if ( _dh_player_mgr::IsFill(&v8->m_Quester[j]) )
      CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_Quester[j].pOne->m_ObjID.m_wIndex, pbyType, szMsg, v11);
  }
}
