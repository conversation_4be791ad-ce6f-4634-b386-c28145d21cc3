/*
 * Function: ?GetNpcRaceCode@CItemStore@@AEAA_NPEAE@Z
 * Address: 0x140262380
 */

char __fastcall CItemStore::GetNpcRaceCode(CItemStore *this, char *pbyRaceCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  _base_fld *v6; // [sp+20h] [bp-18h]@4
  CItemStore *v7; // [sp+40h] [bp+8h]@1
  char *v8; // [sp+48h] [bp+10h]@1

  v8 = pbyRaceCode;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = CRecordData::GetRecord(&stru_1799C62C0, v7->m_pRec->m_strStore_NPCcode);
  if ( v6 )
  {
    *v8 = v6[1].m_strCode[60];
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
