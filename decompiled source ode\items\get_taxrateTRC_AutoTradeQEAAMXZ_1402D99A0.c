/*
 * Function: ?get_taxrate@TRC_AutoTrade@@QEAAMXZ
 * Address: 0x1402D99A0
 */

void __fastcall TRC_AutoTrade::get_taxrate(TRC_AutoTrade *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  TRC_AutoTrade *v4; // [sp+40h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  ControllerTaxRate::getCurTaxRate(&v4->m_Controller);
}
