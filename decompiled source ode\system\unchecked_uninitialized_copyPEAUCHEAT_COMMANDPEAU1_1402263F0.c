/*
 * Function: ??$unchecked_uninitialized_copy@PEAUCHEAT_COMMAND@@PEAU1@V?$allocator@UCHEAT_COMMAND@@@std@@@stdext@@YAPEAUCHEAT_COMMAND@@PEAU1@00AEAV?$allocator@UCHEAT_COMMAND@@@std@@@Z
 * Address: 0x1402263F0
 */

CHEAT_COMMAND *__fastcall stdext::unchecked_uninitialized_copy<CHEAT_COMMAND *,CHEAT_COMMAND *,std::allocator<CHEAT_COMMAND>>(CHEAT_COMMAND *_First, CHEAT_COMMAND *_Last, CHEAT_COMMAND *_Dest, std::allocator<CHEAT_COMMAND> *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  std::_Nonscalar_ptr_iterator_tag v9; // [sp+31h] [bp-17h]@4
  CHEAT_COMMAND *__formal; // [sp+50h] [bp+8h]@1
  CHEAT_COMMAND *_Lasta; // [sp+58h] [bp+10h]@1
  CHEAT_COMMAND *_Desta; // [sp+60h] [bp+18h]@1
  std::allocator<CHEAT_COMMAND> *_Ala; // [sp+68h] [bp+20h]@1

  _Ala = _Al;
  _Desta = _Dest;
  _Lasta = _Last;
  __formal = _First;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  v9 = std::_Ptr_cat<CHEAT_COMMAND *,CHEAT_COMMAND *>(&__formal, &_Desta);
  return std::_Uninit_copy<CHEAT_COMMAND *,CHEAT_COMMAND *,std::allocator<CHEAT_COMMAND>>(
           __formal,
           _Lasta,
           _Desta,
           _Ala,
           v9,
           v8);
}
