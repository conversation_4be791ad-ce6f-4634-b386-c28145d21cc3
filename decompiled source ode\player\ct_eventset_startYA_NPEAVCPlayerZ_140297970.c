/*
 * Function: ?ct_eventset_start@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140297970
 */

bool __fastcall ct_eventset_start(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-138h]@1
  char szTran; // [sp+48h] [bp-F0h]@4
  char pwszErrCode; // [sp+90h] [bp-A8h]@6
  bool v7; // [sp+114h] [bp-24h]@6
  unsigned __int64 v8; // [sp+120h] [bp-18h]@4
  CPlayer *pOnea; // [sp+140h] [bp+8h]@1

  pOnea = pOne;
  v1 = &v4;
  for ( i = 76i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = (unsigned __int64)&v4 ^ _security_cookie;
  W2M(s_pwszDstCheat[0], &szTran, 0x20u);
  if ( pOnea )
  {
    v7 = CMonsterEventSet::StartEventSet(g_MonsterEventSet, &szTran, &pwszErrCode, pOnea);
    if ( pOnea )
    {
      if ( v7 )
        sprintf(wszRespon, "Event Set Start Success %s >> %s", s_pwszDstCheat[0], &pwszErrCode);
      else
        sprintf(wszRespon, "Event Set Start Error %s >> %s", s_pwszDstCheat[0], &pwszErrCode);
      CPlayer::SendData_ChatTrans(pOnea, 0, 0xFFFFFFFF, -1, 0, wszRespon, -1, 0i64);
      result = v7;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
