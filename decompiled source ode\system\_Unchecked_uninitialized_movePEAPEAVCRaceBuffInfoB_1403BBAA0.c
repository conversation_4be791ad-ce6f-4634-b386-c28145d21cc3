/*
 * Function: ??$_Unchecked_uninitialized_move@PEAPEAVCRaceBuffInfoByHolyQuest@@PEAPEAV1@V?$allocator@PEAVCRaceBuffInfoByHolyQuest@@@std@@@stdext@@YAPEAPEAVCRaceBuffInfoByHolyQuest@@PEAPEAV1@00AEAV?$allocator@PEAVCRaceBuffInfoByHolyQuest@@@std@@@Z
 * Address: 0x1403BBAA0
 */

CRaceBuffInfoByHolyQuest **__fastcall stdext::_Unchecked_uninitialized_move<CRaceBuffInfoByHolyQuest * *,CRaceBuffInfoByHolyQuest * *,std::allocator<CRaceBuffInfoByHolyQuest *>>(CRaceBuffInfoByHolyQuest **_First, CRaceBuffInfoByHolyQuest **_Last, CRaceBuffInfoByHolyQuest **_Dest, std::allocator<CRaceBuffInfoByHolyQuest *> *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  std::_Undefined_move_tag v9; // [sp+31h] [bp-17h]@4
  CRaceBuffInfoByHolyQuest **_Firsta; // [sp+50h] [bp+8h]@1
  CRaceBuffInfoByHolyQuest **_Lasta; // [sp+58h] [bp+10h]@1
  CRaceBuffInfoByHolyQuest **__formal; // [sp+60h] [bp+18h]@1
  std::allocator<CRaceBuffInfoByHolyQuest *> *_Ala; // [sp+68h] [bp+20h]@1

  _Ala = _Al;
  __formal = _Dest;
  _Lasta = _Last;
  _Firsta = _First;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  v9 = std::_Move_cat<CRaceBuffInfoByHolyQuest * *>(&__formal);
  return std::_Uninit_move<CRaceBuffInfoByHolyQuest * *,CRaceBuffInfoByHolyQuest * *,std::allocator<CRaceBuffInfoByHolyQuest *>,std::_Undefined_move_tag>(
           _Firsta,
           _Lasta,
           __formal,
           _Ala,
           v9,
           v8);
}
