/*
 * Function: ?Init@CPartyPlayer@@QEAAXG@Z
 * Address: 0x140044C30
 */

void __fastcall CPartyPlayer::Init(CPartyPlayer *this, unsigned __int16 wIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CPartyPlayer *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5->m_bLogin = 0;
  v5->m_id.wIndex = wIndex;
  CPartyPlayer::PartyListInit(v5);
  v5->m_pDarkHole = 0i64;
}
