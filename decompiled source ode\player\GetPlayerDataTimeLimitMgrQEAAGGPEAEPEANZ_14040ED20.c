/*
 * Function: ?GetPlayerData@TimeLimitMgr@@QEAAGGPEAEPEAN@Z
 * Address: 0x14040ED20
 */

signed __int16 __fastcall TimeLimitMgr::GetPlayerData(TimeLimitMgr *this, unsigned __int16 wIndex, char *psStatus, long double *pdPercent)
{
  signed __int16 result; // ax@2

  if ( this->m_lstTLStaus[wIndex].m_bUse )
  {
    *psStatus = this->m_lstTLStaus[wIndex].m_byTL_Status;
    *pdPercent = this->m_lstTLStaus[wIndex].m_dPercent;
    result = this->m_lstTLStaus[wIndex].m_byTL_Status;
  }
  else
  {
    result = 100;
  }
  return result;
}
