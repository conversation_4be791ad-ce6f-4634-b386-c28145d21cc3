/*
 * Function: _std::_Copy_opt_std::_Vector_const_iterator_CUnmannedTraderClassInfo_____ptr64_std::allocator_CUnmannedTraderClassInfo_____ptr64____CUnmannedTraderClassInfo_____ptr64_____ptr64_std::random_access_iterator_tag__::_1_::dtor$0
 * Address: 0x140376320
 */

void __fastcall std::_Copy_opt_std::_Vector_const_iterator_CUnmannedTraderClassInfo_____ptr64_std::allocator_CUnmannedTraderClassInfo_____ptr64____CUnmannedTraderClassInfo_____ptr64_____ptr64_std::random_access_iterator_tag__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(*(std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > **)(a2 + 72));
}
