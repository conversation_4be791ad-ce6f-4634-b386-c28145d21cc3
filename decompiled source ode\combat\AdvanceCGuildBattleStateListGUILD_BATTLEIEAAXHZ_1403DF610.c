/*
 * Function: ?Advance@CGuildBattleStateList@GUILD_BATTLE@@IEAAXH@Z
 * Address: 0x1403DF610
 */

void __fastcall GUILD_BATTLE::CGuildBattleStateList::Advance(GUILD_BATTLE::CGuildBattleStateList *this, int iAdvance)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CGuildBattleStateList *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = iAdvance;
  if ( iAdvance )
  {
    switch ( v5 )
    {
      case 1:
        v6->m_iState = v6->STATE_MAX;
        break;
      case 2:
        GUILD_BATTLE::CGuildBattleStateList::Next(v6, 0);
        break;
      case 3:
        GUILD_BATTLE::CGuildBattleStateList::Goto(v6);
        break;
    }
  }
}
