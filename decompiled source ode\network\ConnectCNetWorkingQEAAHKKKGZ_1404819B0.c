/*
 * Function: ?Connect@CNetWorking@@QEAAHKKKG@Z
 * Address: 0x1404819B0
 */

int __fastcall CNetWorking::Connect(CNetWorking *this, unsigned int dwProID, unsigned int dwSocketIndex, unsigned int dwIP, unsigned __int16 wPort)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // rax@5
  __int64 v9; // [sp+0h] [bp-68h]@1
  __int16 Dst; // [sp+28h] [bp-40h]@4
  u_short v11; // [sp+2Ah] [bp-3Eh]@4
  unsigned int v12; // [sp+2Ch] [bp-3Ch]@4
  unsigned __int64 v13; // [sp+50h] [bp-18h]@4
  CNetWorking *v14; // [sp+70h] [bp+8h]@1
  unsigned int v15; // [sp+78h] [bp+10h]@1
  unsigned int n; // [sp+80h] [bp+18h]@1
  unsigned int v17; // [sp+88h] [bp+20h]@1

  v17 = dwIP;
  n = dwSocketIndex;
  v15 = dwProID;
  v14 = this;
  v5 = &v9;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v13 = (unsigned __int64)&v9 ^ _security_cookie;
  memset_0(&Dst, 0, 0x10ui64);
  Dst = 2;
  v11 = htons(wPort);
  v12 = v17;
  if ( v14->m_Process[v15].m_Type.m_bAnSyncConnect )
    v7 = CNetProcess::PushAnsyncConnect(&v14->m_Process[v15], n, (sockaddr_in *)&Dst);
  else
    LODWORD(v7) = CNetSocket::Connect(&v14->m_Process[v15].m_NetSocket, n, (sockaddr_in *)&Dst);
  return v7;
}
