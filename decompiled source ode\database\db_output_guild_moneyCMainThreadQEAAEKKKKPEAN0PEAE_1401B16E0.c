/*
 * Function: ?db_output_guild_money@CMainThread@@QEAAEKKKKPEAN0PEAEPEAD1@Z
 * Address: 0x1401B16E0
 */

char __fastcall CMainThread::db_output_guild_money(CMainThread *this, unsigned int dwPusherSerial, unsigned int dwGuildSerial, unsigned int dwSubDalant, unsigned int dwSubGold, long double *dTotalDalant, long double *dTotalGold, char *byDate, char *pwszName, char *pbyProcRet)
{
  __int64 *v10; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v13; // ecx@13
  int v14; // edx@13
  unsigned int v15; // edi@13
  __int64 v16; // [sp+0h] [bp-E8h]@1
  long double dResultDalant; // [sp+20h] [bp-C8h]@13
  long double dResultGold; // [sp+28h] [bp-C0h]@13
  char *wszDate; // [sp+30h] [bp-B8h]@13
  unsigned int dwAvatorSerial; // [sp+38h] [bp-B0h]@17
  char *v21; // [sp+40h] [bp-A8h]@17
  char v22; // [sp+50h] [bp-98h]@4
  char DstBuf; // [sp+68h] [bp-80h]@13
  char v24; // [sp+69h] [bp-7Fh]@13
  CCheckSumGuildData v25; // [sp+98h] [bp-50h]@15
  char v26; // [sp+C0h] [bp-28h]@16
  char v27; // [sp+C1h] [bp-27h]@18
  char v28; // [sp+C2h] [bp-26h]@19
  __int64 v29; // [sp+C8h] [bp-20h]@4
  unsigned __int64 v30; // [sp+D0h] [bp-18h]@4
  CMainThread *v31; // [sp+F0h] [bp+8h]@1
  unsigned int v32; // [sp+F8h] [bp+10h]@1
  unsigned int dwGuildSeriala; // [sp+100h] [bp+18h]@1
  unsigned int v34; // [sp+108h] [bp+20h]@1

  v34 = dwSubDalant;
  dwGuildSeriala = dwGuildSerial;
  v32 = dwPusherSerial;
  v31 = this;
  v10 = &v16;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v10 = -858993460;
    v10 = (__int64 *)((char *)v10 + 4);
  }
  v29 = -2i64;
  v30 = (unsigned __int64)&v16 ^ _security_cookie;
  v22 = CMainThread::check_min_max_guild_money(v31, dwGuildSerial, dTotalDalant, dTotalGold);
  if ( v22 )
  {
    result = v22;
  }
  else if ( *dTotalDalant == 0.0 && v34
         || *dTotalGold == 0.0 && dwSubGold
         || (double)(signed int)v34 > *dTotalDalant
         || (double)(signed int)dwSubGold > *dTotalGold )
  {
    *pbyProcRet = 1;
    result = 0;
  }
  else
  {
    *dTotalDalant = *dTotalDalant - (double)(signed int)v34;
    *dTotalGold = *dTotalGold - (double)(signed int)dwSubGold;
    DstBuf = 0;
    memset(&v24, 0, 8ui64);
    v13 = (unsigned __int8)byDate[2];
    v14 = (unsigned __int8)byDate[1];
    v15 = (unsigned __int8)*byDate;
    LODWORD(wszDate) = (unsigned __int8)byDate[3];
    LODWORD(dResultGold) = v13;
    LODWORD(dResultDalant) = v14;
    sprintf_s(&DstBuf, 9ui64, "%02d%02d%02d%02d", v15);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v31->m_pWorldDB->vfptr, 0);
    if ( CRFWorldDatabase::Update_SetGuildMoney(v31->m_pWorldDB, dwGuildSeriala, *dTotalDalant, *dTotalGold) )
    {
      CCheckSumGuildData::CCheckSumGuildData(&v25, dwGuildSeriala);
      CCheckSumGuildData::Encode(&v25, *dTotalDalant, *dTotalGold);
      if ( CCheckSumGuildData::Update(&v25, v31->m_pWorldDB) )
      {
        v21 = pwszName;
        dwAvatorSerial = v32;
        wszDate = &DstBuf;
        dResultGold = *dTotalGold;
        dResultDalant = *dTotalDalant;
        if ( CRFWorldDatabase::Insert_GuildMoneyHistory(
               v31->m_pWorldDB,
               dwGuildSeriala,
               -0.0 - (double)(signed int)v34,
               -0.0 - (double)(signed int)dwSubGold,
               dResultDalant,
               dResultGold,
               &DstBuf,
               v32,
               pwszName) )
        {
          CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&v31->m_pWorldDB->vfptr);
          CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v31->m_pWorldDB->vfptr, 1);
          v28 = 0;
          CCheckSumGuildData::~CCheckSumGuildData(&v25);
          result = v28;
        }
        else
        {
          CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v31->m_pWorldDB->vfptr);
          CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v31->m_pWorldDB->vfptr, 1);
          v27 = 24;
          CCheckSumGuildData::~CCheckSumGuildData(&v25);
          result = v27;
        }
      }
      else
      {
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v31->m_pWorldDB->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v31->m_pWorldDB->vfptr, 1);
        v26 = 24;
        CCheckSumGuildData::~CCheckSumGuildData(&v25);
        result = v26;
      }
    }
    else
    {
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v31->m_pWorldDB->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v31->m_pWorldDB->vfptr, 1);
      result = 24;
    }
  }
  return result;
}
