#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <vector>
#include <array>
#include <cmath>

// Forward declarations
class CMonster;
class CMapData;
struct _NEAR_DATA;
struct _monster_create_setdata;

/**
 * @brief CMonsterHelper - Utility class for monster-related helper functions
 * 
 * This class provides static utility functions for monster operations including:
 * - Angle calculations and direction computations
 * - Monster searching and positioning
 * - Sector and area checks
 * - Monster transportation and hierarchy management
 * 
 * All functions are static and thread-safe for read-only operations.
 */
class CMonsterHelper {
public:
    // Constants for calculations
    static constexpr float PI = 3.14159265359f;
    static constexpr float TWO_PI = 6.28318530718f;
    static constexpr float RAD_TO_DEG = 180.0f / PI;
    static constexpr float DEG_TO_RAD = PI / 180.0f;
    static constexpr float EPSILON = 0.001f;

    /**
     * @brief Calculate angle between two positions
     * @param mon Position of the monster (3D coordinates)
     * @param plr Position of the player/target (3D coordinates)
     * @return Angle in radians between the two positions
     */
    static float GetAngle(float* mon, float* plr);

    /**
     * @brief Calculate direction vector from current to target position
     * @param cur Current position (3D coordinates)
     * @param tar Target position (3D coordinates)
     * @param out Output direction vector (3D coordinates)
     * @param deg Degree parameter for calculation
     * @param distance Distance parameter for calculation
     */
    static void GetDirection(float (*cur)[3], float (*tar)[3], float (*out)[3], float deg, float distance);

    /**
     * @brief Check if a position is within a sector defined by angle and radius
     * @param chkpos Position to check (3D coordinates)
     * @param src Source position (3D coordinates)
     * @param dest Destination position (3D coordinates)
     * @param angle Sector angle in degrees
     * @param radius Maximum distance from source
     * @param pfDist Output parameter for calculated distance (optional)
     * @return true if position is within the sector
     */
    static bool IsInSector(float* chkpos, float* src, float* dest, float angle, float radius, float* pfDist = nullptr);

    /**
     * @brief Search for nearby monsters within specified parameters
     * @param pMon Source monster for the search
     * @param NearChar Array to store found nearby monsters
     * @param dwArSize Size of the NearChar array
     * @param bTargetIgnore Flag to ignore certain targets
     * @return Number of monsters found
     */
    static uint32_t SearchNearMonster(CMonster* pMon, _NEAR_DATA* NearChar, uint32_t dwArSize, int bTargetIgnore);

    /**
     * @brief Search for the nearest monster within specified distance
     * @param pMon Source monster for the search
     * @param dwDist Maximum search distance
     * @return Pointer to the nearest monster found, or nullptr if none found
     */
    static CMonster* SearchNearMonsterByDistance(CMonster* pMon, uint32_t dwDist);

    /**
     * @brief Find a suitable patrol movement position for a monster
     * @param pMon Monster to find patrol position for
     * @param pAI Monster AI context
     * @return true if a suitable patrol position was found
     */
    static bool SearchPatrolMovePos(CMonster* pMon, void* pAI);

    /**
     * @brief Transport a monster to a new position
     * @param mon Monster to transport
     * @param tarPos Target position (3D coordinates)
     */
    static void TransPort(CMonster* mon, float* tarPos);

    /**
     * @brief Perform hierarchy help cast operation
     * @param pMon Monster to perform hierarchy operation on
     */
    static void HierarcyHelpCast(CMonster* pMon);

private:
    // Private utility functions
    
    /**
     * @brief Normalize a 3D vector
     * @param vec Vector to normalize (modified in place)
     */
    static void Normalize(float* vec);

    /**
     * @brief Calculate distance between two 3D points
     * @param pos1 First position
     * @param pos2 Second position
     * @return Distance between the points
     */
    static float CalculateDistance(const float* pos1, const float* pos2);

    /**
     * @brief Calculate dot product of two 3D vectors
     * @param vec1 First vector
     * @param vec2 Second vector
     * @return Dot product result
     */
    static float DotProduct(const float* vec1, const float* vec2);

    /**
     * @brief Validate input parameters for position-based functions
     * @param pos Position to validate
     * @return true if position is valid
     */
    static bool ValidatePosition(const float* pos);

    /**
     * @brief Validate monster pointer
     * @param pMon Monster pointer to validate
     * @return true if monster pointer is valid
     */
    static bool ValidateMonster(const CMonster* pMon);

    // Disable instantiation - this is a utility class with only static methods
    CMonsterHelper() = delete;
    ~CMonsterHelper() = delete;
    CMonsterHelper(const CMonsterHelper&) = delete;
    CMonsterHelper& operator=(const CMonsterHelper&) = delete;
};

/**
 * @brief Utility functions for monster helper operations
 */
namespace CMonsterHelperUtils {
    /**
     * @brief Log a monster helper operation
     * @param operation Name of the operation
     * @param details Additional details about the operation
     */
    void LogOperation(const char* operation, const char* details = nullptr);

    /**
     * @brief Log an error in monster helper operations
     * @param errorMessage Error message to log
     * @param context Context where the error occurred
     */
    void LogError(const char* errorMessage, const char* context = nullptr);

    /**
     * @brief Validate angle value
     * @param angle Angle to validate
     * @return true if angle is within valid range
     */
    bool ValidateAngle(float angle);

    /**
     * @brief Validate distance value
     * @param distance Distance to validate
     * @return true if distance is positive and reasonable
     */
    bool ValidateDistance(float distance);

    /**
     * @brief Convert angle from degrees to radians
     * @param degrees Angle in degrees
     * @return Angle in radians
     */
    float DegreesToRadians(float degrees);

    /**
     * @brief Convert angle from radians to degrees
     * @param radians Angle in radians
     * @return Angle in degrees
     */
    float RadiansToDegrees(float radians);
}
