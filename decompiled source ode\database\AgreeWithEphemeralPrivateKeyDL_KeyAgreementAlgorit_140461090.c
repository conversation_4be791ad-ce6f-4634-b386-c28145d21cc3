/*
 * Function: ?AgreeWithEphemeralPrivateKey@?$DL_KeyAgreementAlgorithm_DH@UECPPoint@CryptoPP@@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@2@@CryptoPP@@UEBA?AUECPPoint@2@AEBV?$DL_GroupParameters@UECPPoint@CryptoPP@@@2@AEBV?$DL_FixedBasePrecomputation@UECPPoint@CryptoPP@@@2@AEBVInteger@2@@Z
 * Address: 0x140461090
 */

CryptoPP::ECPPoint *__fastcall CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::ECPPoint,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>>::AgreeWithEphemeralPrivateKey(CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::ECPPoint,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0> > *this, CryptoPP::ECPPoint *result, CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *params, CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint> *publicPrecomputation, CryptoPP::Integer *privateExponent)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  CryptoPP::Integer *v7; // rax@5
  __int64 v8; // rax@7
  __int64 v10; // [sp+0h] [bp-C8h]@1
  CryptoPP::Integer *v11; // [sp+20h] [bp-A8h]@7
  CryptoPP::Integer v12; // [sp+28h] [bp-A0h]@5
  CryptoPP::Integer resulta; // [sp+50h] [bp-78h]@5
  int v14; // [sp+78h] [bp-50h]@4
  __int64 v15; // [sp+80h] [bp-48h]@4
  CryptoPP::Integer *v16; // [sp+88h] [bp-40h]@5
  CryptoPP::Integer *b; // [sp+90h] [bp-38h]@5
  CryptoPP::Integer *v18; // [sp+98h] [bp-30h]@5
  CryptoPP::Integer *v19; // [sp+A0h] [bp-28h]@5
  CryptoPP::Integer *v20; // [sp+A8h] [bp-20h]@5
  CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint>Vtbl *v21; // [sp+B0h] [bp-18h]@7
  CryptoPP::ECPPoint *v22; // [sp+D8h] [bp+10h]@1
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *v23; // [sp+E0h] [bp+18h]@1
  CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint> *v24; // [sp+E8h] [bp+20h]@1

  v24 = publicPrecomputation;
  v23 = params;
  v22 = result;
  v5 = &v10;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v15 = -2i64;
  v14 = 0;
  if ( CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>::ToEnum() == 2 )
  {
    LODWORD(v7) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *, CryptoPP::Integer *))v23->vfptr[11].__vecDelDtor)(
                    v23,
                    &v12);
    v16 = v7;
    b = v7;
    v14 |= 1u;
    v18 = CryptoPP::operator*(&resulta, privateExponent, v7);
    v19 = v18;
    v14 |= 2u;
    v20 = v18;
  }
  else
  {
    v20 = privateExponent;
  }
  v11 = v20;
  LODWORD(v8) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *))v23->vfptr[5].__vecDelDtor)(v23);
  v21 = v24->vfptr;
  ((void (__fastcall *)(CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint> *, CryptoPP::ECPPoint *, __int64, CryptoPP::Integer *))v21->Exponentiate)(
    v24,
    v22,
    v8,
    v11);
  v14 |= 4u;
  if ( v14 & 2 )
  {
    v14 &= 0xFFFFFFFD;
    CryptoPP::Integer::~Integer(&resulta);
  }
  if ( v14 & 1 )
  {
    v14 &= 0xFFFFFFFE;
    CryptoPP::Integer::~Integer(&v12);
  }
  return v22;
}
