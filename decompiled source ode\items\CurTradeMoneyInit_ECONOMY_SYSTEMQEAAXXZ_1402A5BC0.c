/*
 * Function: ?CurTradeMoneyInit@_ECONOMY_SYSTEM@@QEAAXXZ
 * Address: 0x1402A5BC0
 */

void __fastcall _ECONOMY_SYSTEM::CurTradeMoneyInit(_ECONOMY_SYSTEM *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  int k; // [sp+4h] [bp-14h]@6
  _ECONOMY_SYSTEM *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &j;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  for ( j = 0; j < 3; ++j )
  {
    v5->m_dCurTradeGold[j] = DOUBLE_1_0;
    v5->m_dCurTradeDalant[j] = DOUBLE_1_0;
    for ( k = 0; k < 3; ++k )
    {
      v5->m_dCurOreMineCount[j][k] = DOUBLE_1_0;
      v5->m_dCurOreCutCount[j][k] = DOUBLE_1_0;
    }
  }
}
