/*
 * Function: ?FindWaitItem@CUnmannedTraderScheduler@@AEAA_NXZ
 * Address: 0x140393DA0
 */

char __fastcall CUnmannedTraderScheduler::FindWaitItem(CUnmannedTraderScheduler *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v3; // rax@5
  CUnmannedTraderSchedule *v5; // rax@8
  __int64 v6; // [sp+0h] [bp-88h]@1
  int v7; // [sp+20h] [bp-68h]@4
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > result; // [sp+28h] [bp-60h]@9
  bool v9; // [sp+40h] [bp-48h]@6
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > v10; // [sp+48h] [bp-40h]@6
  __int64 v11; // [sp+60h] [bp-28h]@4
  unsigned __int64 v12; // [sp+68h] [bp-20h]@5
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v13; // [sp+70h] [bp-18h]@6
  std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *_Right; // [sp+78h] [bp-10h]@6
  CUnmannedTraderScheduler *v15; // [sp+90h] [bp+8h]@1

  v15 = this;
  v1 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11 = -2i64;
  v7 = 0;
  while ( 1 )
  {
    v12 = v7;
    v3 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::size(&v15->m_veckSchdule);
    if ( v12 > v3 )
      break;
    v13 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::end(&v15->m_veckSchdule, &v10);
    _Right = (std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)v13;
    v9 = std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator==(
           (std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)&v15->m_iterSchedule._Mycont,
           (std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)&v13->_Mycont);
    std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(&v10);
    if ( v9 )
      return 0;
    v5 = std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator*(&v15->m_iterSchedule);
    if ( CUnmannedTraderSchedule::IsWait(v5) )
      break;
    ++v7;
    std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator++(
      &v15->m_iterSchedule,
      &result,
      0);
    std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(&result);
  }
  return 1;
}
