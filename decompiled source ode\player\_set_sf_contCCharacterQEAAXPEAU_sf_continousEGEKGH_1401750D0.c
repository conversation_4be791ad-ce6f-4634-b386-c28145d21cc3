/*
 * Function: ?_set_sf_cont@CCharacter@@QEAAXPEAU_sf_continous@@EGEKGH@Z
 * Address: 0x1401750D0
 */

void __fastcall CCharacter::_set_sf_cont(CCharacter *this, _sf_continous *pCont, char byEffectCode, unsigned __int16 wEffectIndex, char byLv, unsigned int dwStartSec, unsigned __int16 wDurSec, int nCumulCount)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v10; // [sp+0h] [bp-58h]@1
  _base_fld *v11; // [sp+20h] [bp-38h]@4
  char *v12; // [sp+28h] [bp-30h]@4
  int j; // [sp+30h] [bp-28h]@8
  char *v14; // [sp+38h] [bp-20h]@10
  int v15; // [sp+40h] [bp-18h]@11
  CCharacter *v16; // [sp+60h] [bp+8h]@1
  char v17; // [sp+70h] [bp+18h]@1

  v17 = byEffectCode;
  v16 = this;
  v8 = &v10;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  pCont->m_bExist = 1;
  pCont->m_byEffectCode = byEffectCode;
  pCont->m_wEffectIndex = wEffectIndex;
  pCont->m_byLv = byLv;
  pCont->m_dwStartSec = dwStartSec;
  pCont->m_wDurSec = wDurSec;
  pCont->m_dwEffSerial = v16->m_dwEffSerialCounter++;
  pCont->m_nCumulCounter = nCumulCount;
  v11 = CRecordData::GetRecord(&stru_1799C8410 + (unsigned __int8)byEffectCode, wEffectIndex);
  v12 = 0i64;
  if ( v17 == 1 )
    v12 = &v11[12].m_strCode[56];
  else
    v12 = &v11[13].m_strCode[48];
  if ( v12 )
  {
    for ( j = 0; j < 5; ++j )
    {
      v14 = &v12[36 * j];
      if ( *(_DWORD *)v14 == -1 )
        break;
      v15 = *(_DWORD *)v14;
      if ( v15 )
      {
        if ( v15 == 1 )
        {
          _effect_parameter::SetEff_Plus(
            &v16->m_EP,
            *((_DWORD *)v14 + 1),
            *(float *)&v14[4 * ((unsigned __int8)byLv - 1) + 8],
            1);
        }
        else if ( v15 == 2 )
        {
          _effect_parameter::SetEff_State(&v16->m_EP, *((_DWORD *)v14 + 1), 1);
        }
      }
      else
      {
        _effect_parameter::SetEff_Rate(
          &v16->m_EP,
          *((_DWORD *)v14 + 1),
          *(float *)&v14[4 * ((unsigned __int8)byLv - 1) + 8],
          1);
      }
    }
  }
}
