/*
 * Function: ?PushDQSData_GuildInMoney@TRC_AutoTrade@@QEAAXKK@Z
 * Address: 0x1402D8740
 */

void __fastcall TRC_AutoTrade::PushDQSData_GuildInMoney(TRC_AutoTrade *this, unsigned int dwRetPrice, unsigned int dwSeller)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@5
  __int64 v6; // [sp+0h] [bp-88h]@1
  _qry_case_in_atrade_tax v7; // [sp+38h] [bp-50h]@5
  unsigned __int64 v8; // [sp+70h] [bp-18h]@4
  TRC_AutoTrade *v9; // [sp+90h] [bp+8h]@1
  unsigned int v10; // [sp+98h] [bp+10h]@1
  unsigned int v11; // [sp+A0h] [bp+18h]@1

  v11 = dwSeller;
  v10 = dwRetPrice;
  v9 = this;
  v3 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( v9->m_pOwnerGuild )
  {
    _qry_case_in_atrade_tax::_qry_case_in_atrade_tax(&v7);
    v7.byRace = TRC_AutoTrade::get_race(v9);
    v7.dwGuildSerial = v9->m_pOwnerGuild->m_dwSerial;
    v7.in_seller = v11;
    *(_QWORD *)&v7.out_totalgold = 0i64;
    *(_QWORD *)&v7.out_totaldalant = 0i64;
    v7.in_dalant = v10;
    v5 = _qry_case_in_atrade_tax::size(&v7);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 52, &v7.byRace, v5);
  }
}
