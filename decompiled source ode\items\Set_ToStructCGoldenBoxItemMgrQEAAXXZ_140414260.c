/*
 * Function: ?Set_ToStruct@CGoldenBoxItemMgr@@QEAAXXZ
 * Address: 0x140414260
 */

void __fastcall CGoldenBoxItemMgr::Set_ToStruct(CGoldenBoxItemMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  _INVENKEY *v3; // rax@6
  _INVENKEY *v4; // rax@8
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  int k; // [sp+24h] [bp-14h]@6
  _INVENKEY v8; // [sp+28h] [bp-10h]@6
  _INVENKEY v9; // [sp+2Ch] [bp-Ch]@8
  CGoldenBoxItemMgr *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10->m_golden_box_item_New.bydck = v10->m_golden_box_item.m_bydck;
  v10->m_golden_box_item_New.dwStarterBoxCnt = v10->m_golden_box_item.m_dwStarterBoxCnt;
  for ( j = 0; j < v10->m_golden_box_event.m_ini.m_byLoopCnt; ++j )
  {
    _INVENKEY::_INVENKEY(&v8, 0, v10->m_golden_box_item.m_byBoxTableCode[j], v10->m_golden_box_item.m_dwBoxIndex[j]);
    v10->m_golden_box_item_New.nBoxcode[j] = _INVENKEY::CovDBKey(v3);
    v10->m_golden_box_item_New.wBoxMax[j] = v10->m_golden_box_item.m_wBoxMax[j];
    v10->m_golden_box_item_New.bygolden_item_num[j] = v10->m_golden_box_item.m_bygolden_item_num[j];
    for ( k = 0; k < v10->m_golden_box_item.m_bygolden_item_num[j]; ++k )
    {
      _INVENKEY::_INVENKEY(
        &v9,
        0,
        v10->m_golden_box_item.m_golden_box_item_info[j][k].m_byTableCode,
        v10->m_golden_box_item.m_golden_box_item_info[j][k].m_dwIndex);
      v10->m_golden_box_item_New.List[j][k].ncode = _INVENKEY::CovDBKey(v4);
      v10->m_golden_box_item_New.List[j][k].wcount = v10->m_golden_box_item.m_golden_box_item_info[j][k].m_wNum;
    }
  }
}
