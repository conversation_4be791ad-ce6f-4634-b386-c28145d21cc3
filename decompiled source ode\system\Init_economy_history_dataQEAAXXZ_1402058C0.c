/*
 * Function: ?Init@_economy_history_data@@QEAAXXZ
 * Address: 0x1402058C0
 */

void __fastcall _economy_history_data::Init(_economy_history_data *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  int k; // [sp+4h] [bp-14h]@6
  _economy_history_data *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &j;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  for ( j = 0; j < 3; ++j )
  {
    v5->dTradeGold[j] = DOUBLE_1_0;
    v5->dTradeDalant[j] = DOUBLE_1_0;
    v5->wEconomyGuide[j] = 100;
    for ( k = 0; k < 3; ++k )
    {
      v5->dOreMineCount[j][k] = DOUBLE_1_0;
      v5->dOreCutCount[j][k] = DOUBLE_1_0;
    }
  }
}
