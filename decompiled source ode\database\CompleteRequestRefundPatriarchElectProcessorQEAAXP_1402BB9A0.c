/*
 * Function: ?CompleteRequestRefund@PatriarchElectProcessor@@QEAAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1402BB9A0
 */

void __fastcall PatriarchElectProcessor::CompleteRequestRefund(PatriarchElectProcessor *this, _DB_QRY_SYN_DATA *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@7
  int v5; // eax@8
  unsigned int v6; // eax@9
  signed __int64 v7; // rax@10
  double v8; // xmm0_8@10
  unsigned int v9; // eax@12
  int v10; // eax@13
  __int64 v11; // [sp+0h] [bp-58h]@1
  char *pQryData; // [sp+20h] [bp-38h]@5
  _qry_case_request_refund *v13; // [sp+30h] [bp-28h]@4
  CPlayer *v14; // [sp+38h] [bp-20h]@6
  char *pszFileName; // [sp+40h] [bp-18h]@12
  PatriarchElectProcessor *v16; // [sp+60h] [bp+8h]@1

  v16 = this;
  v2 = &v11;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (_qry_case_request_refund *)pData->m_sData;
  if ( pData->m_byResult )
  {
    LODWORD(pQryData) = v13->dwAvatorSerial;
    CLogFile::Write(&v16->_kSysLog, "FAILED DB_RET(%s_%d):%d", "Request_Refund", v16->_dwElectSerial);
  }
  else
  {
    v14 = &g_Player + v13->wIndex;
    if ( v14->m_bOper && (v4 = CPlayerDB::GetCharSerial(&v14->m_Param), v4 == v13->dwAvatorSerial) )
    {
      v6 = CPlayerDB::GetDalant(&v14->m_Param);
      if ( CanAddMoneyForMaxLimMoney(v13->dwRefund, v6) )
      {
        v7 = v13->dwRefund;
        v8 = (double)(signed int)v7;
        if ( v7 < 0 )
          v8 = v8 + 1.844674407370955e19;
        CPlayer::AlterDalant(v14, v8);
        CPlayer::SendMsg_AlterMoneyInform(v14, 0);
        PatriarchElectProcessor::SendMsg_ResultCode(v16, v14->m_id.wIndex, 13);
        pszFileName = v14->m_szItemHistoryFileName;
        v9 = CPlayerDB::GetCharSerial(&v14->m_Param);
        CMgrAvatorItemHistory::raceboss_giveback(&CPlayer::s_MgrItemHistory, v9, v13->dwRefund, pszFileName);
      }
      else
      {
        v10 = _qry_case_request_refund::size(v13);
        CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 124, &v13->byRace, v10);
      }
    }
    else
    {
      v5 = _qry_case_request_refund::size(v13);
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 124, &v13->byRace, v5);
    }
  }
}
