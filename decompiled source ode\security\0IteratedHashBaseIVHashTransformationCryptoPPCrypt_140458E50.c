/*
 * Function: ??0?$IteratedHashBase@IVHashTransformation@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x140458E50
 */

void __fastcall CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation>::IteratedHashBase<unsigned int,CryptoPP::HashTransformation>(CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation> *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CryptoPP::HashTransformation::HashTransformation((CryptoPP::HashTransformation *)&v4->vfptr);
  v4->m_countLo = 0;
  v4->m_countHi = 0;
}
