/*
 * Function: j_??0?$_Vector_val@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@IEAA@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@1@@Z
 * Address: 0x140001802
 */

void __fastcall std::_Vector_val<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Vector_val<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(std::_Vector_val<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *this, std::allocator<CUnmannedTraderSubClassInfo *> _Al)
{
  std::_Vector_val<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Vector_val<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(
    this,
    _Al);
}
