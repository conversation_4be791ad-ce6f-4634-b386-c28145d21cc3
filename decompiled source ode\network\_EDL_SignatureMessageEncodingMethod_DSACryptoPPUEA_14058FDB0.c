/*
 * Function: ??_EDL_SignatureMessageEncodingMethod_DSA@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x14058FDB0
 */

CryptoPP::DL_SignatureMessageEncodingMethod_DSA *__fastcall CryptoPP::DL_SignatureMessageEncodingMethod_DSA::`vector deleting destructor'(CryptoPP::DL_SignatureMessageEncodingMethod_DSA *a1, int a2)
{
  CryptoPP::DL_SignatureMessageEncodingMethod_DSA *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::DL_SignatureMessageEncodingMethod_DSA::~DL_SignatureMessageEncodingMethod_DSA(a1);
  if ( v4 & 1 )
    operator delete(v3);
  return v3;
}
