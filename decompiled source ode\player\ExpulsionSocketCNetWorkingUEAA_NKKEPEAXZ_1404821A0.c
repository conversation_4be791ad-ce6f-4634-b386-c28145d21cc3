/*
 * Function: ?ExpulsionSocket@CNetWorking@@UEAA_NKKEPEAX@Z
 * Address: 0x1404821A0
 */

char __fastcall CNetWorking::ExpulsionSocket(CNetWorking *this, unsigned int dwProID, unsigned int dwIndex, char byReason, void *pvInfo)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-28h]@1
  CNetWorking *v9; // [sp+30h] [bp+8h]@1

  v9 = this;
  v5 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  CNetWorking::CloseSocket(v9, dwProID, dwIndex, 0);
  return 1;
}
