/*
 * Function: ?PushDQSUpdatePlyerVoteInfo@CPlayer@@QEAAXXZ
 * Address: 0x1400699F0
 */

void __fastcall CPlayer::PushDQSUpdatePlyerVoteInfo(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CUserDB *v3; // rcx@4
  int v4; // eax@4
  __int64 v5; // [sp+0h] [bp-D8h]@1
  char *pQryData; // [sp+20h] [bp-B8h]@4
  _qry_case_update_player_vote_info v7; // [sp+38h] [bp-A0h]@4
  char DstBuf; // [sp+88h] [bp-50h]@4
  char v9; // [sp+89h] [bp-4Fh]@4
  unsigned __int64 v10; // [sp+C0h] [bp-18h]@4
  CPlayer *v11; // [sp+E0h] [bp+8h]@1

  v11 = this;
  v1 = &v5;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  v7.dwAccumPlayTime = v11->m_pUserDB->m_AvatorData.dbSupplement.dwAccumPlayTime;
  v7.byIsVoted = v11->m_pUserDB->m_AvatorData.dbSupplement.byVoted;
  v7.byVoteEnable = v11->m_pUserDB->m_AvatorData.dbSupplement.VoteEnable;
  v7.wScaner = v11->m_pUserDB->m_AvatorData.dbSupplement.wScanerCnt;
  DstBuf = 0;
  memset(&v9, 0, 0x1Dui64);
  v3 = v11->m_pUserDB;
  LODWORD(pQryData) = v11->m_pUserDB->m_AvatorData.dbSupplement.wScanerCnt;
  sprintf_s(&DstBuf, 0x1Eui64, "%d%d", v3->m_AvatorData.dbSupplement.dwScanerGetDate);
  v7.dwScanerData = _atoi64(&DstBuf);
  v7.dwCharSerial = CPlayerDB::GetCharSerial(&v11->m_Param);
  v4 = _qry_case_update_player_vote_info::size(&v7);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -106, (char *)&v7, v4);
}
