/*
 * Function: _std::copy_std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0____::_1_::dtor$6
 * Address: 0x14031F290
 */

void __fastcall std::copy_std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0____::_1_::dtor_6(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 192) & 1 )
  {
    *(_DWORD *)(a2 + 192) &= 0xFFFFFFFE;
    std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(*(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> **)(a2 + 272));
  }
}
