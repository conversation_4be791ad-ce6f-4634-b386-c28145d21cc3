/*
 * Function: ?OnChildR<PERSON>n<PERSON>oop@CMonsterHierarchy@@QEAAXXZ
 * Address: 0x140157590
 */

void __fastcall CMonsterHierarchy::OnChildRegenLoop(CMonsterHierarchy *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v3; // rcx@20
  __int64 v4; // [sp+0h] [bp-B8h]@1
  CMonster *pParent; // [sp+20h] [bp-98h]@20
  bool bRobExp; // [sp+28h] [bp-90h]@20
  bool bRewardExp; // [sp+30h] [bp-88h]@20
  bool bDungeon; // [sp+38h] [bp-80h]@20
  bool bWithoutFail; // [sp+40h] [bp-78h]@20
  bool bApplyRopExpField; // [sp+48h] [bp-70h]@20
  _monster_fld *v11; // [sp+50h] [bp-68h]@7
  int nKind; // [sp+58h] [bp-60h]@11
  unsigned int v13; // [sp+5Ch] [bp-5Ch]@17
  unsigned int j; // [sp+60h] [bp-58h]@17
  float pNewPos; // [sp+78h] [bp-40h]@19
  CMonster *pMon; // [sp+98h] [bp-20h]@20
  int v17; // [sp+A0h] [bp-18h]@13
  CMonster *v18; // [sp+A8h] [bp-10h]@20
  CMonsterHierarchy *v19; // [sp+C0h] [bp+8h]@1

  v19 = this;
  v1 = &v4;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v19->m_pThisMon )
  {
    if ( v19->m_pThisMon->m_pMonRec )
    {
      v11 = v19->m_pThisMon->m_pMonRec;
      if ( CMonsterHierarchy::ChildKindCount(v19) )
      {
        if ( !v19->m_pParentMon && GetLoopTime() - v19->m_dwChildRecallTime > v11->m_nGuardRecallTimeMS )
        {
          v19->m_dwChildRecallTime = GetLoopTime();
          for ( nKind = 0; ; ++nKind )
          {
            v17 = v19->m_byChildMonSetNum >= 3 ? 3 : v19->m_byChildMonSetNum;
            if ( nKind >= v17 || v11->m_Child[nKind].nChildMonNum <= 0 )
              break;
            v13 = v11->m_Child[nKind].nChildMonNum - v19->m_dwMonCount[nKind];
            for ( j = 0; j < v13; ++j )
            {
              if ( CMapData::GetRandPosVirtualDum(v19->m_pThisMon->m_pCurMap, v19->m_pThisMon->m_fCurPos, 100, &pNewPos) )
              {
                v3 = &v19->m_pThisMon->m_pRecordSet[nKind + 26].m_strCode[28];
                v18 = v19->m_pThisMon;
                bApplyRopExpField = 0;
                bWithoutFail = 0;
                bDungeon = 0;
                bRewardExp = 0;
                bRobExp = v18->m_bRobExp;
                pParent = v19->m_pThisMon;
                pMon = CreateRepMonster(
                         v18->m_pCurMap,
                         v18->m_wMapLayerIndex,
                         &pNewPos,
                         v3,
                         pParent,
                         bRobExp,
                         0,
                         0,
                         0,
                         0);
                if ( pMon )
                {
                  if ( !CMonsterHierarchy::PushChildMon(v19, nKind, pMon) )
                    CMonsterHierarchy::SetParent(&pMon->m_MonHierarcy, 0i64);
                }
              }
            }
          }
        }
      }
    }
  }
}
