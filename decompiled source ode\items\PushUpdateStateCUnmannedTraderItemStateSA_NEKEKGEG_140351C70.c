/*
 * Function: ?PushUpdateState@CUnmannedTraderItemState@@SA_NEKEKGEG@Z
 * Address: 0x140351C70
 */

char __fastcall CUnmannedTraderItemState::PushUpdateState(char byType, unsigned int dwRegistSerial, char byState, unsigned int dwOwnerSerial, unsigned __int16 wItemSerial, char byItemTableCode, unsigned __int16 wItemTableIndex)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v10; // eax@6
  __int64 v11; // [sp+0h] [bp-68h]@1
  _qry_case_unmandtrader_updateitemstate v12; // [sp+38h] [bp-30h]@6
  char v13; // [sp+70h] [bp+8h]@1
  unsigned int v14; // [sp+78h] [bp+10h]@1
  char v15; // [sp+80h] [bp+18h]@1
  unsigned int v16; // [sp+88h] [bp+20h]@1

  v16 = dwOwnerSerial;
  v15 = byState;
  v14 = dwRegistSerial;
  v13 = byType;
  v7 = &v11;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  if ( CUnmannedTraderItemState::GetMaxStateCnt() > (unsigned __int8)byState )
  {
    v12.byType = v13;
    v12.dwRegistSerial = v14;
    v12.byState = v15;
    v12.wItemSerial = wItemSerial;
    v12.dwOwnerSerial = v16;
    v12.byItemTableCode = byItemTableCode;
    v12.wItemTableIndex = wItemTableIndex;
    v10 = _qry_case_unmandtrader_updateitemstate::size(&v12);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 59, &v12.byType, v10);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
