/*
 * Function: ?InvalidateSky@Sky@@QEAAXXZ
 * Address: 0x1405229B0
 */

void __fastcall Sky::InvalidateSky(Sky *this)
{
  Sky *v1; // rbx@1
  __int64 v2; // rcx@2
  __int64 v3; // rcx@4

  v1 = this;
  if ( *(float *)&dword_184A797B0 >= 1.0 )
  {
    v2 = *((_QWORD *)this + 5);
    if ( v2 )
    {
      (*(void (**)(void))(*(_QWORD *)v2 + 16i64))();
      *((_QWORD *)v1 + 5) = 0i64;
    }
    v3 = *((_QWORD *)v1 + 6);
    if ( v3 )
    {
      (*(void (**)(void))(*(_QWORD *)v3 + 16i64))();
      *((_QWORD *)v1 + 6) = 0i64;
    }
  }
}
