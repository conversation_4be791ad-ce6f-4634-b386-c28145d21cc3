/*
 * Function: ?Init@CUnmannedTraderUserInfoTable@@QEAA_NXZ
 * Address: 0x140363800
 */

char __fastcall CUnmannedTraderUserInfoTable::Init(CUnmannedTraderUserInfoTable *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfo *v3; // rax@4
  char result; // al@5
  CUnmannedTraderUserInfo *v5; // rax@8
  __int64 v6; // [sp+0h] [bp-B8h]@1
  unsigned __int16 j; // [sp+20h] [bp-98h]@6
  CUnmannedTraderUserInfo v8; // [sp+28h] [bp-90h]@4
  __int64 v9; // [sp+90h] [bp-28h]@4
  CUnmannedTraderUserInfo *v10; // [sp+98h] [bp-20h]@4
  CUnmannedTraderUserInfo *_Val; // [sp+A0h] [bp-18h]@4
  CUnmannedTraderUserInfoTable *v12; // [sp+C0h] [bp+8h]@1

  v12 = this;
  v1 = &v6;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = -2i64;
  CUnmannedTraderUserInfo::CUnmannedTraderUserInfo(&v8);
  v10 = v3;
  _Val = v3;
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::assign(&v12->m_veckInfo, 0x9E4ui64, v3);
  CUnmannedTraderUserInfo::~CUnmannedTraderUserInfo(&v8);
  if ( std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::size(&v12->m_veckInfo) == 2532 )
  {
    for ( j = 0; (signed int)j < 2532; ++j )
    {
      v5 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator[](&v12->m_veckInfo, j);
      if ( !CUnmannedTraderUserInfo::Init(v5, j) )
        return 0;
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
