/*
 * Function: ?Update_PostStorageSendToRecver@CRFWorldDatabase@@QEAA_NKKEPEAD000H_KKKEGPEAE_N1@Z
 * Address: 0x1404B2F60
 */

bool __fastcall CRFWorldDatabase::Update_PostStorageSendToRecver(CRFWorldDatabase *this, unsigned int dwOwner, unsigned int dwPostSerial, char byPostState, char *wszSendName, char *wszRecvName, char *wszTitle, char *wszContent, int nK, unsigned __int64 dwD, unsigned int dwU, unsigned int dwGold, char byErr, unsigned __int16 wStorageIndex, char *pbyNumber, bool bGetNumber, unsigned __int64 lnUID)
{
  __int64 *v17; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@10
  unsigned int v20; // edi@32
  __int64 v21; // [sp+0h] [bp-928h]@1
  void *SQLStmt; // [sp+20h] [bp-908h]@6
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-900h]@23
  char *v24; // [sp+30h] [bp-8F8h]@32
  char *v25; // [sp+38h] [bp-8F0h]@32
  char *v26; // [sp+40h] [bp-8E8h]@32
  char *v27; // [sp+48h] [bp-8E0h]@32
  int v28; // [sp+50h] [bp-8D8h]@32
  unsigned __int64 v29; // [sp+58h] [bp-8D0h]@32
  unsigned int v30; // [sp+60h] [bp-8C8h]@32
  unsigned int v31; // [sp+68h] [bp-8C0h]@32
  int v32; // [sp+70h] [bp-8B8h]@32
  unsigned __int64 v33; // [sp+78h] [bp-8B0h]@32
  int v34; // [sp+80h] [bp-8A8h]@32
  unsigned int v35; // [sp+88h] [bp-8A0h]@32
  SQLLEN v36; // [sp+98h] [bp-890h]@23
  __int16 v37; // [sp+A4h] [bp-884h]@11
  char TargetValue; // [sp+B4h] [bp-874h]@4
  char DstBuf; // [sp+E0h] [bp-848h]@4
  char v40; // [sp+E1h] [bp-847h]@4
  char strQuery; // [sp+500h] [bp-428h]@32
  char v42; // [sp+501h] [bp-427h]@32
  unsigned __int64 v43; // [sp+910h] [bp-18h]@4
  CRFWorldDatabase *v44; // [sp+930h] [bp+8h]@1
  unsigned int v45; // [sp+938h] [bp+10h]@1
  unsigned int v46; // [sp+940h] [bp+18h]@1
  char v47; // [sp+948h] [bp+20h]@1

  v47 = byPostState;
  v46 = dwPostSerial;
  v45 = dwOwner;
  v44 = this;
  v17 = &v21;
  for ( i = 584i64; i; --i )
  {
    *(_DWORD *)v17 = -858993460;
    v17 = (__int64 *)((char *)v17 + 4);
  }
  v43 = (unsigned __int64)&v21 ^ _security_cookie;
  TargetValue = 0;
  DstBuf = 0;
  memset(&v40, 0, 0x3FFui64);
  if ( (signed int)(unsigned __int8)byPostState >= 100 || !bGetNumber )
  {
LABEL_32:
    strQuery = 0;
    memset(&v42, 0, 0x3FFui64);
    v20 = (unsigned __int8)*pbyNumber;
    v35 = v46;
    v34 = wStorageIndex;
    v33 = lnUID;
    v32 = (unsigned __int8)byErr;
    v31 = dwGold;
    v30 = dwU;
    v29 = dwD;
    v28 = nK;
    v27 = wszContent;
    v26 = wszTitle;
    v25 = wszRecvName;
    v24 = wszSendName;
    LODWORD(StrLen_or_IndPtr) = (unsigned __int8)v47;
    LODWORD(SQLStmt) = v45;
    sprintf_s(
      &strQuery,
      0x400ui64,
      "update tbl_PostStorage set postinx=%d,owner=%d,dck=0,poststate=%d,sendname='%s',recvname='%s',title='%s',content='"
      "%s',k=%d,d=%I64d,u=%d,gold=%d,err=%d,uid=%I64d,sindex=%d where serial=%d",
      v20);
    return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v44->vfptr, &strQuery, 1) != 0;
  }
  LODWORD(SQLStmt) = 100;
  sprintf_s(
    &DstBuf,
    0x400ui64,
    "select count(Serial) from tbl_PostStorage where owner=%d and dck=0 and poststate<%d",
    dwOwner);
  if ( v44->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v44->vfptr, &DstBuf);
  if ( !v44->m_hStmtSelect && !CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v44->vfptr) )
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v44->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    return 0;
  }
  v37 = SQLExecDirect_0(v44->m_hStmtSelect, &DstBuf, -3);
  if ( v37 && v37 != 1 )
  {
    if ( v37 == 100 )
    {
      result = 0;
    }
    else
    {
      SQLStmt = v44->m_hStmtSelect;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v44->vfptr, v37, &DstBuf, "_SQLExecDirect", SQLStmt);
      CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v44->vfptr, v37, v44->m_hStmtSelect);
      result = 0;
    }
    return result;
  }
  v37 = SQLFetch_0(v44->m_hStmtSelect);
  if ( v37 && v37 != 1 )
  {
    if ( v37 != 100 )
    {
      SQLStmt = v44->m_hStmtSelect;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v44->vfptr, v37, &DstBuf, "SQLFetch", SQLStmt);
      CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v44->vfptr, v37, v44->m_hStmtSelect);
    }
    if ( v44->m_hStmtSelect )
      SQLCloseCursor_0(v44->m_hStmtSelect);
    return 0;
  }
  StrLen_or_IndPtr = &v36;
  SQLStmt = 0i64;
  v37 = SQLGetData_0(v44->m_hStmtSelect, 1u, -6, &TargetValue, 0i64, &v36);
  if ( !v37 || v37 == 1 )
  {
    *pbyNumber = TargetValue;
    if ( v44->m_hStmtSelect )
      SQLCloseCursor_0(v44->m_hStmtSelect);
    if ( v44->m_bSaveDBLog )
      CRFNewDatabase::FmtLog((CRFNewDatabase *)&v44->vfptr, "%s Success", &DstBuf);
    goto LABEL_32;
  }
  SQLStmt = v44->m_hStmtSelect;
  CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v44->vfptr, v37, &DstBuf, "SQLGetData", SQLStmt);
  CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v44->vfptr, v37, v44->m_hStmtSelect);
  if ( v44->m_hStmtSelect )
    SQLCloseCursor_0(v44->m_hStmtSelect);
  return 0;
}
