/*
 * Function: ?CheckReRegist@CUnmannedTraderUserInfo@@AEAAEEPEAVCLogFile@@GEEGKKPEAE11PEAK2@Z
 * Address: 0x14035BBE0
 */

char __fastcall CUnmannedTraderUserInfo::CheckReRegist(CUnmannedTraderUserInfo *this, char byType, CLogFile *pkLogger, unsigned __int16 wItemSerial, char byAmount, char byItemTableCode, unsigned __int16 wItemIndex, unsigned int dwRegistSerial, unsigned int dwPrice, char *pbyDivision, char *pbyClass, char *pbySubClass, unsigned int *pdwTax, unsigned int *pdwListIndex)
{
  __int64 *v14; // rdi@1
  signed __int64 i; // rcx@1
  char v16; // al@6
  CUnmannedTraderRegistItemInfo *v17; // rax@18
  CUnmannedTraderGroupItemInfoTable *v18; // rax@37
  __int64 v19; // [sp+0h] [bp-C8h]@1
  char *byClass; // [sp+20h] [bp-A8h]@37
  char *bySubClass; // [sp+28h] [bp-A0h]@37
  unsigned int *dwListIndex; // [sp+30h] [bp-98h]@37
  CPlayer *v23; // [sp+40h] [bp-88h]@7
  _STORAGE_LIST::_db_con *v24; // [sp+48h] [bp-80h]@14
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > result; // [sp+58h] [bp-70h]@16
  bool v26; // [sp+74h] [bp-54h]@16
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v27; // [sp+78h] [bp-50h]@16
  char v28; // [sp+90h] [bp-38h]@17
  char v29; // [sp+91h] [bp-37h]@19
  char v30; // [sp+92h] [bp-36h]@21
  char v31; // [sp+93h] [bp-35h]@23
  char v32; // [sp+94h] [bp-34h]@25
  char v33; // [sp+95h] [bp-33h]@28
  char v34; // [sp+96h] [bp-32h]@31
  char v35; // [sp+97h] [bp-31h]@33
  char v36; // [sp+98h] [bp-30h]@36
  char v37; // [sp+99h] [bp-2Fh]@38
  char v38; // [sp+9Ah] [bp-2Eh]@40
  char v39; // [sp+9Bh] [bp-2Dh]@41
  __int64 v40; // [sp+A0h] [bp-28h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v41; // [sp+A8h] [bp-20h]@16
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v42; // [sp+B0h] [bp-18h]@16
  CUnmannedTraderUserInfo *v43; // [sp+D0h] [bp+8h]@1
  unsigned __int16 v44; // [sp+E8h] [bp+20h]@1

  v44 = wItemSerial;
  v43 = this;
  v14 = &v19;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v14 = -858993460;
    v14 = (__int64 *)((char *)v14 + 4);
  }
  v40 = -2i64;
  if ( (signed int)v43->m_wInx >= 2532 || (signed int)(unsigned __int8)byType >= 2 )
    return 99;
  v23 = &g_Player + v43->m_wInx;
  if ( v23->m_dwObjSerial != v43->m_dwUserSerial )
    return 99;
  if ( !CUnmannedTraderRequestLimiter::IsEmpty(&v43->m_kRequestState) )
    return 95;
  if ( CMainThread::IsReleaseServiceMode(&g_Main) && v23->m_byUserDgr )
    return 21;
  v24 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v23->m_Param.m_dbInven.m_nListNum, v44);
  if ( !v24 )
    return 8;
  CUnmannedTraderUserInfo::Find(v43, &result, dwRegistSerial);
  v41 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
          &v43->m_vecRegistItemInfo,
          &v27);
  v42 = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)v41;
  v26 = std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator==(
          (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v41->_Mycont,
          (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&result._Mycont);
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v27);
  if ( v26 )
  {
    v28 = 25;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    return v28;
  }
  v17 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(&result);
  if ( !CUnmannedTraderRegistItemInfo::IsWaitNoitfyClose(v17) )
  {
    v29 = -53;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    return v29;
  }
  if ( v43->m_byRegistCnt >= v43->m_byMaxRegistCnt )
  {
    v30 = 6;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    return v30;
  }
  *pdwTax = dwPrice / 0x3E8;
  if ( *pdwTax > CPlayerDB::GetDalant(&v23->m_Param) )
  {
    v31 = -55;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    return v31;
  }
  if ( dwPrice > 0x77359400 )
  {
    v32 = 22;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    return v32;
  }
  if ( v24->m_byTableCode != (unsigned __int8)byItemTableCode || v24->m_wItemIndex != wItemIndex )
  {
    v33 = 9;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    return v33;
  }
  if ( !IsOverLapItem(v24->m_byTableCode) )
    goto LABEL_44;
  if ( (unsigned __int8)byAmount > v24->m_dwDur )
  {
    v34 = 17;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    return v34;
  }
  if ( (unsigned __int8)byAmount < v24->m_dwDur )
  {
    v35 = 9;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    v16 = v35;
  }
  else
  {
LABEL_44:
    if ( v24->m_byTableCode != 15
      || CRecordData::GetRecord(
           &stru_1799C8410 + 1,
           *((_DWORD *)CPlayer::s_pnLinkForceItemToEffect + v24->m_wItemIndex)) )
    {
      v18 = CUnmannedTraderGroupItemInfoTable::Instance();
      dwListIndex = pdwListIndex;
      bySubClass = pbySubClass;
      byClass = pbyClass;
      if ( CUnmannedTraderGroupItemInfoTable::GetGroupID(
             v18,
             v24->m_byTableCode,
             v24->m_wItemIndex,
             pbyDivision,
             pbyClass,
             pbySubClass,
             pdwListIndex) )
      {
        if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, *(&g_Player.m_id.wIndex + 25428 * v43->m_wInx)) == 99 )
        {
          v38 = -52;
          std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
          v16 = v38;
        }
        else
        {
          v39 = 0;
          std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
          v16 = v39;
        }
      }
      else
      {
        v37 = 3;
        std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
        v16 = v37;
      }
    }
    else
    {
      v36 = 23;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
      v16 = v36;
    }
  }
  return v16;
}
