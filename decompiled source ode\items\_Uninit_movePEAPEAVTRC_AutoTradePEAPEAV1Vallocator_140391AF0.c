/*
 * Function: ??$_Uninit_move@PEAPEAVTRC_AutoTrade@@PEAPEAV1@V?$allocator@PEAVTRC_AutoTrade@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAVTRC_AutoTrade@@PEAPEAV1@00AEAV?$allocator@PEAVTRC_AutoTrade@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140391AF0
 */

TRC_AutoTrade **__fastcall std::_Uninit_move<TRC_AutoTrade * *,TRC_AutoTrade * *,std::allocator<TRC_AutoTrade *>,std::_Undefined_move_tag>(TRC_AutoTrade **_First, TRC_AutoTrade **_Last, TRC_AutoTrade **_Dest, std::allocator<TRC_AutoTrade *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-28h]@1
  TRC_AutoTrade **_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  return stdext::unchecked_uninitialized_copy<TRC_AutoTrade * *,TRC_AutoTrade * *,std::allocator<TRC_AutoTrade *>>(
           _Firsta,
           _Last,
           _Dest,
           _Al);
}
