/*
 * Function: _dynamic_initializer_for__g_Player__
 * Address: 0x1406DE100
 */

__int64 dynamic_initializer_for__g_Player__()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1

  v0 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  `eh vector constructor iterator'(
    &g_Player,
    0xC6A8ui64,
    2532,
    (void (__cdecl *)(void *))CPlayer::CPlayer,
    (void (__cdecl *)(void *))CPlayer::~CPlayer);
  return atexit(dynamic_atexit_destructor_for__g_Player__);
}
