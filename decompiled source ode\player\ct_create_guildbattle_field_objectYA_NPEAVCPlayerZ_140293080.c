/*
 * Function: ?ct_create_guildbattle_field_object@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140293080
 */

bool __fastcall ct_create_guildbattle_field_object(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  CGuildBattleController *v4; // rax@6
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *pkPlayer; // [sp+30h] [bp+8h]@1

  pkPlayer = pOne;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( pkPlayer )
  {
    v4 = CGuildBattleController::Instance();
    result = CGuildBattleController::CheatCreateFieldObject(v4, pkPlayer);
  }
  else
  {
    result = 0;
  }
  return result;
}
