/*
 * Function: ?SetL<PERSON>l@CPlayer@@QEAAXE@Z
 * Address: 0x140055630
 */

void __usercall CPlayer::SetLevel(CPlayer *this@<rcx>, char byNew<PERSON><PERSON>l@<dl>, long double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // al@10
  char *v6; // rax@10
  char *v7; // rax@11
  int v8; // eax@12
  char *v9; // rax@13
  int v10; // eax@14
  __int64 v11; // r8@14
  int v12; // eax@14
  int v13; // eax@14
  int v14; // edx@16
  __int64 v15; // [sp+0h] [bp-68h]@1
  int v16; // [sp+30h] [bp-38h]@6
  unsigned __int8 v17; // [sp+34h] [bp-34h]@7
  bool v18; // [sp+35h] [bp-33h]@10
  _quest_fail_result *v19; // [sp+38h] [bp-30h]@16
  int v20; // [sp+40h] [bp-28h]@12
  CGameObjectVtbl *v21; // [sp+48h] [bp-20h]@14
  int n; // [sp+50h] [bp-18h]@16
  CPlayer *v23; // [sp+70h] [bp+8h]@1
  char v24; // [sp+78h] [bp+10h]@1

  v24 = byNewLevel;
  v23 = this;
  v3 = &v15;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v23->m_byUserDgr && v23->m_byUserDgr != 1
    || (v16 = CPlayerDB::GetMaxLevel(&v23->m_Param), (unsigned __int8)v24 <= v16) )
  {
    v17 = CPlayerDB::GetLevel(&v23->m_Param);
    if ( v17 < (signed int)(unsigned __int8)v24 )
    {
      if ( v23->m_pUserDB )
      {
        CPlayerDB::GetExp(&v23->m_Param);
        CUserDB::Update_Level(v23->m_pUserDB, v24, a3);
      }
      CPlayerDB::SetLevel(&v23->m_Param, (unsigned __int8)v24);
      v5 = CPlayerDB::CalcCharGrade(v24, v23->m_Param.m_dbChar.m_wRankRate);
      CPlayer::SetGrade(v23, v5);
      v6 = cvt_string((unsigned __int8)v24);
      v18 = CPlayer::Emb_CreateQuestEvent(v23, quest_happen_type_lv, v6);
      if ( !v18 )
      {
        v7 = cvt_string((unsigned __int8)v24);
        CPlayer::Emb_CheckActForQuest(v23, 6, v7, 1u, 0);
      }
      v20 = (unsigned __int8)v24;
      v8 = CPlayerDB::GetMaxLevel(&v23->m_Param);
      if ( v20 == v8 )
      {
        v9 = cvt_string((unsigned __int8)v24);
        CPlayer::Emb_CreateQuestEvent(v23, quest_happen_type_maxlevel, v9);
      }
      v23->m_bUpCheckEquipEffect = 1;
      CPlayer::ReCalcMaxHFSP(v23, 1, 0);
      v10 = ((int (__fastcall *)(CPlayer *))v23->vfptr->GetMaxHP)(v23);
      v21 = v23->vfptr;
      LOBYTE(v11) = 1;
      ((void (__fastcall *)(CPlayer *, _QWORD, __int64))v21->SetHP)(v23, (unsigned int)v10, v11);
      v12 = CPlayer::GetMaxFP(v23);
      CPlayer::SetFP(v23, v12, 1);
      v13 = CPlayer::GetMaxSP(v23);
      CPlayer::SetSP(v23, v13, 1);
      CPlayer::SendMsg_Level(v23, (unsigned __int8)v24);
      CPlayer::SendData_PartyMemberLv(v23);
      if ( CPlayerDB::IsClassChangeableLv(&v23->m_Param) )
        CPlayer::SendMsg_ChangeClassCommand(v23);
      v14 = v23->m_Param.m_byPvPGrade;
      n = v23->m_ObjID.m_wIndex;
      CMgrAvatorLvHistory::upgrade_lv(
        &CPlayer::s_MgrLvHistory,
        n,
        (unsigned __int8)v24,
        v14,
        v23->m_nMaxPoint,
        v23->m_szLvHistoryFileName);
      v19 = CQuestMgr::CheckLimLv(&v23->m_QuestMgr, (unsigned __int8)v24);
      if ( v19 )
        CPlayer::LimLvNpcQuestDelete(v23, v19->m_List[0].byQuestDBSlot);
    }
  }
}
