/*
 * Function: ?Request@CMoveMapLimitInfoList@@QEAAEHHHKHPEADPEAVCMoveMapLimitRightInfo@@@Z
 * Address: 0x1403A5F80
 */

int __fastcall CMoveMapLimitInfoList::Request(CMoveMapLimitInfoList *this, int iLimitType, int iRequetType, int iMapInx, unsigned int dwStoreRecordIndex, int iUserInx, char *pRequest, CMoveMapLimitRightInfo *pkRight)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@5
  CMoveMapLimitInfoVtbl *v11; // rax@6
  __int64 v12; // [sp+0h] [bp-48h]@1
  CMoveMapLimitRightInfo *v13; // [sp+20h] [bp-28h]@6
  CMoveMapLimitInfo *v14; // [sp+30h] [bp-18h]@4
  CMoveMapLimitInfoList *v15; // [sp+50h] [bp+8h]@1
  int v16; // [sp+60h] [bp+18h]@1

  v16 = iRequetType;
  v15 = this;
  v8 = &v12;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v14 = CMoveMapLimitInfoList::Get(v15, iLimitType, iMapInx, dwStoreRecordIndex);
  if ( v14 )
  {
    v11 = v14->vfptr;
    v13 = pkRight;
    result = ((int (__fastcall *)(CMoveMapLimitInfo *, _QWORD, _QWORD, char *))v11->Request)(
               v14,
               (unsigned int)iUserInx,
               (unsigned int)v16,
               pRequest);
  }
  else
  {
    result = 3;
  }
  return result;
}
