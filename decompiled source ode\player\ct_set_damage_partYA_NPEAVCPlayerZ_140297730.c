/*
 * Function: ?ct_set_damage_part@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140297730
 */

char __fastcall ct_set_damage_part(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1

  v5 = pOne;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !v5 )
    return 0;
  if ( s_nWordCount < 1 )
  {
    result = 0;
  }
  else
  {
    if ( !strcmp_0("body", s_pwszDstCheat[0]) )
    {
      v5->m_byDamagePart = 0;
    }
    else if ( !strcmp_0("leg", s_pwszDstCheat[0]) )
    {
      v5->m_byDamagePart = 1;
    }
    else if ( !strcmp_0("hand", s_pwszDstCheat[0]) )
    {
      v5->m_byDamagePart = 2;
    }
    else if ( !strcmp_0("foot", s_pwszDstCheat[0]) )
    {
      v5->m_byDamagePart = 3;
    }
    else if ( !strcmp_0("head", s_pwszDstCheat[0]) )
    {
      v5->m_byDamagePart = 4;
    }
    else
    {
      if ( strcmp_0("random", s_pwszDstCheat[0]) )
        return 0;
      v5->m_byDamagePart = -1;
    }
    result = 1;
  }
  return result;
}
