/*
 * Function: ?cheat_make_item_no_material@CMgrAvatorItemHistory@@QEAAXHEPEAU_db_con@_STORAGE_LIST@@PEAD@Z
 * Address: 0x14023B910
 */

void __fastcall CMgrAvatorItemHistory::cheat_make_item_no_material(CMgrAvatorItemHistory *this, int n, char byRetCode, _STORAGE_LIST::_db_con *pMakeItem, char *pszFileName)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char *v7; // rax@5
  __int64 v8; // [sp+0h] [bp-68h]@1
  char *v9; // [sp+20h] [bp-48h]@5
  unsigned __int64 v10; // [sp+28h] [bp-40h]@5
  char *v11; // [sp+30h] [bp-38h]@5
  char *v12; // [sp+38h] [bp-30h]@5
  _base_fld *v13; // [sp+40h] [bp-28h]@5
  char *v14; // [sp+48h] [bp-20h]@5
  char *v15; // [sp+50h] [bp-18h]@5
  int nTableCode; // [sp+58h] [bp-10h]@5
  CMgrAvatorItemHistory *v17; // [sp+70h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v18; // [sp+88h] [bp+20h]@1

  v18 = pMakeItem;
  v17 = this;
  v5 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  sData[0] = 0;
  if ( byRetCode )
  {
    sprintf(sBuf, "MAKE(FAIL): [%s %s] ( CHEAT_MATERIAL ) \r\n", v17->m_szCurDate, v17->m_szCurTime);
    strcat_0(sData, sBuf);
  }
  else
  {
    v13 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pMakeItem->m_byTableCode, pMakeItem->m_wItemIndex);
    v14 = v17->m_szCurTime;
    v15 = v17->m_szCurDate;
    nTableCode = v18->m_byTableCode;
    v7 = DisplayItemUpgInfo(nTableCode, v18->m_dwLv);
    v12 = v14;
    v11 = v15;
    v10 = v18->m_lnUID;
    v9 = v7;
    sprintf(sBuf, "MAKE(SUCC): %s_%u_@%s[%I64u] [%s %s] ( CHEAT_MATERIAL ) \r\n", v13->m_strCode, v18->m_dwDur);
    strcat_0(sData, sBuf);
  }
  CMgrAvatorItemHistory::WriteFile(v17, pszFileName, sData);
}
