/*
 * Function: ?coupon_use_buy_item@CMgrAvatorItemHistory@@QEAAXPEAU_db_con@_STORAGE_LIST@@PEAD1@Z
 * Address: 0x140240950
 */

void __fastcall CMgrAvatorItemHistory::coupon_use_buy_item(CMgrAvatorItemHistory *this, _STORAGE_LIST::_db_con *pCouponItem, char *ApplyItem, char *pszFileName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-58h]@1
  char *v7; // [sp+20h] [bp-38h]@4
  unsigned __int64 v8; // [sp+28h] [bp-30h]@4
  char *v9; // [sp+30h] [bp-28h]@4
  char *v10; // [sp+38h] [bp-20h]@4
  _base_fld *v11; // [sp+40h] [bp-18h]@4
  CMgrAvatorItemHistory *v12; // [sp+60h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v13; // [sp+68h] [bp+10h]@1
  char *v14; // [sp+70h] [bp+18h]@1
  char *pszFileNamea; // [sp+78h] [bp+20h]@1

  pszFileNamea = pszFileName;
  v14 = ApplyItem;
  v13 = pCouponItem;
  v12 = this;
  v4 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pCouponItem->m_byTableCode, pCouponItem->m_wItemIndex);
  v10 = v12->m_szCurTime;
  v9 = v12->m_szCurDate;
  v8 = v13->m_lnUID;
  v7 = v11->m_strCode;
  sprintf_s<20000>(
    (char (*)[20000])sData,
    "Use CouponItem at %s(%d down): %s_[%I64u] [%s %s]\r\n",
    v14,
    *(_DWORD *)&v11[4].m_strCode[8]);
  CMgrAvatorItemHistory::WriteFile(v12, pszFileNamea, sData);
}
