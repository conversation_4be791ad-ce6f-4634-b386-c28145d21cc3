/*
 * Function: ?PopFront@CItemDropMgr@@IEAA_NXZ
 * Address: 0x1402D01D0
 */

char __fastcall CItemDropMgr::PopFront(CItemDropMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  unsigned int pdwOutIndex; // [sp+24h] [bp-24h]@6
  CItemDropMgr *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CNetIndexList::size(&v6->m_listTask) > 0 )
  {
    pdwOutIndex = -1;
    CNetIndexList::PopNode_Front(&v6->m_listTask, &pdwOutIndex);
    CNetIndexList::PushNode_Back(&v6->m_listEmpty, pdwOutIndex);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
