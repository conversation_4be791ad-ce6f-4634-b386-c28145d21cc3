/*
 * Function: j_??0?$IteratedHashWithStaticTransform@IU?$EnumToType@W4ByteOrder@CryptoPP@@$00@CryptoPP@@$0EA@$0CA@VSHA256@2@$0A@@CryptoPP@@QEAA@AEBV01@@Z
 * Address: 0x1400034BD
 */

void __fastcall CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,32,CryptoPP::SHA256,0>::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,32,CryptoPP::SHA256,0>(CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,32,CryptoPP::SHA256,0> *this, CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,32,CryptoPP::SHA256,0> *__that)
{
  CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,32,CryptoPP::SHA256,0>::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,32,CryptoPP::SHA256,0>(
    this,
    __that);
}
