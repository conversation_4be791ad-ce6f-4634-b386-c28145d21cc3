/*
 * Function: j_??$unchecked_uninitialized_copy@PEAU?$pair@HH@std@@PEAU12@V?$allocator@U?$pair@HH@std@@@2@@stdext@@YAPEAU?$pair@HH@std@@PEAU12@00AEAV?$allocator@U?$pair@HH@std@@@2@@Z
 * Address: 0x140006230
 */

std::pair<int,int> *__fastcall stdext::unchecked_uninitialized_copy<std::pair<int,int> *,std::pair<int,int> *,std::allocator<std::pair<int,int>>>(std::pair<int,int> *_First, std::pair<int,int> *_Last, std::pair<int,int> *_Dest, std::allocator<std::pair<int,int> > *_Al)
{
  return stdext::unchecked_uninitialized_copy<std::pair<int,int> *,std::pair<int,int> *,std::allocator<std::pair<int,int>>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
