/*
 * Function: ?SendMsg_DropMissile@CNuclearBomb@@QEAAXXZ
 * Address: 0x14013CD10
 */

void __fastcall CNuclearBomb::SendMsg_DropMissile(CNuclearBomb *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  _nuclear_bomb_drop_result_zocl v4; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v6; // [sp+55h] [bp-23h]@4
  CNuclearBomb *v7; // [sp+80h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _nuclear_bomb_drop_result_zocl::_nuclear_bomb_drop_result_zocl(&v4);
  v4.byRaceCode = CPlayerDB::GetRaceCode(&v7->m_pMaster->m_Param);
  v4.byItemTableCode = 26;
  v4.wItemRecIndex = v7->m_wItemIndex;
  v4.byUseClass = CNuclearBomb::GetMasterClass(v7);
  pbyType = 60;
  v6 = 5;
  CGameObject::CircleReport((CGameObject *)&v7->vfptr, &pbyType, &v4.byRaceCode, 5, 0);
}
