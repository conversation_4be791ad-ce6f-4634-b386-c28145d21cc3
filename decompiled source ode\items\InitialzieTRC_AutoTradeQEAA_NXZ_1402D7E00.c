/*
 * Function: ?Initialzie@TRC_AutoTrade@@QEAA_NXZ
 * Address: 0x1402D7E00
 */

char __fastcall TRC_AutoTrade::Initialzie(TRC_AutoTrade *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@4
  unsigned int v4; // eax@4
  __int64 v6; // [sp+0h] [bp-E8h]@1
  char Dest; // [sp+40h] [bp-A8h]@4
  char v8; // [sp+41h] [bp-A7h]@4
  unsigned __int64 v9; // [sp+D0h] [bp-18h]@4
  TRC_AutoTrade *v10; // [sp+F0h] [bp+8h]@1

  v10 = this;
  v1 = &v6;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = (unsigned __int64)&v6 ^ _security_cookie;
  CreateDirectoryA("..\\ZoneServerLog\\ServiceLog\\ATradeTax", 0i64);
  CreateDirectoryA("..\\ZoneServerLog\\SystemLog\\Concession", 0i64);
  Dest = 0;
  memset(&v8, 0, 0x7Fui64);
  v3 = GetKorLocalTime();
  sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\ATradeTax\\atrade_earn_%d_%d.log", v10->m_byRace, v3);
  CLogFile::SetWriteLogFile(&v10->m_serviceLog, &Dest, 1, 0, 1, 1);
  v4 = GetKorLocalTime();
  sprintf(&Dest, "..\\ZoneServerLog\\SystemLog\\Concession\\system_TRC_%d_%d.log", v10->m_byRace, v4);
  CLogFile::SetWriteLogFile(&v10->m_sysLog, &Dest, 1, 0, 1, 1);
  ControllerTaxRate::setLimitTaxRate(&v10->m_Controller, 0.*********, 0.2);
  return 1;
}
