/*
 * Function: ?Cont_UserSave_Complete@CUserDB@@QEAAXEPEAU_AVATOR_DATA@@@Z
 * Address: 0x1401141C0
 */

void __fastcall CUserDB::Cont_UserSave_Complete(CUserDB *this, char byResult, _AVATOR_DATA *pAvatorData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-58h]@1
  _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY *v6; // [sp+30h] [bp-28h]@5
  unsigned int v7; // [sp+38h] [bp-20h]@5
  unsigned int v8; // [sp+3Ch] [bp-1Ch]@5
  unsigned int j; // [sp+40h] [bp-18h]@5
  CUserDB *v10; // [sp+60h] [bp+8h]@1

  v10 = this;
  v3 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( byResult )
  {
    CNetworkEX::Close(&g_Network, 0, v10->m_idWorld.wIndex, 1, "Cont Save Error");
  }
  else
  {
    v6 = v10->m_AvatorData_bk.dbQuest.m_StartHistory;
    v7 = v10->m_AvatorData_bk.dbQuest.dwListCnt;
    memcpy_0(&v10->m_AvatorData_bk, pAvatorData, 0x915Fui64);
    v10->m_AvatorData_bk.dbQuest.m_StartHistory = v6;
    v10->m_AvatorData_bk.dbQuest.dwListCnt = v7;
    v8 = v10->m_AvatorData.dbQuest.dwListCnt - v10->m_AvatorData_bk.dbQuest.dwListCnt;
    for ( j = 0; j < v8; ++j )
    {
      memcpy_0(
        &v10->m_AvatorData_bk.dbQuest.m_StartHistory[v10->m_AvatorData_bk.dbQuest.dwListCnt],
        &v10->m_AvatorData.dbQuest.m_StartHistory[v10->m_AvatorData_bk.dbQuest.dwListCnt],
        0x59ui64);
      ++v10->m_AvatorData_bk.dbQuest.dwListCnt;
    }
    v10->m_bDBWaitState = 0;
    v10->m_pDBPushData = 0i64;
  }
}
