/*
 * Function: j_??0?$_Ranit@VCUnmannedTraderGroupDivisionVersionInfo@@_JPEBV1@AEBV1@@std@@QEAA@XZ
 * Address: 0x14000CAEF
 */

void __fastcall std::_Ranit<CUnmannedTraderGroupDivisionVersionInfo,__int64,CUnmannedTraderGroupDivisionVersionInfo const *,CUnmannedTraderGroupDivisionVersionInfo const &>::_Ranit<CUnmannedTraderGroupDivisionVersionInfo,__int64,CUnmannedTraderGroupDivisionVersionInfo const *,CUnmannedTraderGroupDivisionVersionInfo const &>(std::_Ranit<CUnmannedTraderGroupDivisionVersionInfo,__int64,CUnmannedTraderGroupDivisionVersionInfo const *,CUnmannedTraderGroupDivisionVersionInfo const &> *this)
{
  std::_Ranit<CUnmannedTraderGroupDivisionVersionInfo,__int64,CUnmannedTraderGroupDivisionVersionInfo const *,CUnmannedTraderGroupDivisionVersionInfo const &>::_Ranit<CUnmannedTraderGroupDivisionVersionInfo,__int64,CUnmannedTraderGroupDivisionVersionInfo const *,CUnmannedTraderGroupDivisionVersionInfo const &>(this);
}
