/*
 * Function: ??$_Destroy_range@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@YAXPEAVCUnmannedTraderItemCodeInfo@@0AEAV?$allocator@VCUnmannedTraderItemCodeInfo@@@0@@Z
 * Address: 0x14037A2F0
 */

void __fastcall std::_Destroy_range<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(CUnmannedTraderItemCodeInfo *_First, CUnmannedTraderItemCodeInfo *_Last, std::allocator<CUnmannedTraderItemCodeInfo> *_Al)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  std::_Nonscalar_ptr_iterator_tag v6; // [sp+20h] [bp-18h]@4
  CUnmannedTraderItemCodeInfo *__formal; // [sp+40h] [bp+8h]@1
  CUnmannedTraderItemCodeInfo *_Lasta; // [sp+48h] [bp+10h]@1
  std::allocator<CUnmannedTraderItemCodeInfo> *_Ala; // [sp+50h] [bp+18h]@1

  _Ala = _Al;
  _Lasta = _Last;
  __formal = _First;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = std::_Ptr_cat<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *>(&__formal, &_Lasta);
  std::_Destroy_range<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(
    __formal,
    _Lasta,
    _Ala,
    v6);
}
