/*
 * Function: ?SendMsg_<PERSON><PERSON><PERSON>@CGuild@@QEAAXKNN_NPEAE@Z
 * Address: 0x140256CF0
 */

void __fastcall CGuild::SendMsg_IOMoney(CGuild *this, unsigned int dwIOerSerial, long double dIODalant, long double dIOGold, bool bInPut, char *pbyDate)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp-20h] [bp-B8h]@1
  char szMsg[4]; // [sp+18h] [bp-80h]@4
  char v10; // [sp+1Ch] [bp-7Ch]@4
  bool v11; // [sp+1Dh] [bp-7Bh]@4
  long double v12; // [sp+1Eh] [bp-7Ah]@4
  long double v13; // [sp+26h] [bp-72h]@4
  long double v14; // [sp+2Eh] [bp-6Ah]@4
  long double v15; // [sp+36h] [bp-62h]@4
  char Dst; // [sp+3Eh] [bp-5Ah]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v18; // [sp+65h] [bp-33h]@4
  int j; // [sp+74h] [bp-24h]@4
  unsigned __int64 v20; // [sp+80h] [bp-18h]@4
  CGuild *v21; // [sp+A0h] [bp+8h]@1

  v21 = this;
  v6 = &v8;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v20 = (unsigned __int64)&v8 ^ _security_cookie;
  *(_DWORD *)szMsg = dwIOerSerial;
  v10 = v21->m_byMoneyOutputKind;
  v11 = bInPut;
  v12 = dIODalant;
  v13 = dIOGold;
  CGuild::GetTotalDalant(v21);
  v14 = dIOGold;
  CGuild::GetTotalGold(v21);
  v15 = dIOGold;
  memcpy_0(&Dst, pbyDate, 4ui64);
  pbyType = 27;
  v18 = 37;
  for ( j = 0; j < 50; ++j )
  {
    if ( _guild_member_info::IsFill(&v21->m_MemberData[j]) )
    {
      if ( v21->m_MemberData[j].pPlayer )
        CNetProcess::LoadSendMsg(unk_1414F2088, v21->m_MemberData[j].pPlayer->m_ObjID.m_wIndex, &pbyType, szMsg, 0x2Au);
    }
  }
}
