/*
 * Function: ??_G?$IteratedHashBase@IVHashTransformation@CryptoPP@@@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x14055C010
 */

CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation> *__fastcall CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation>::`scalar deleting destructor'(CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation> *a1, int a2)
{
  CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation> *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation>::~IteratedHashBase<unsigned int,CryptoPP::HashTransformation>(a1);
  if ( v4 & 1 )
    operator delete(v3);
  return v3;
}
