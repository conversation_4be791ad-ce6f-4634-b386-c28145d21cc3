/*
 * Function: ?ChangeTaxRate@TRC_AutoTrade@@QEAAXXZ
 * Address: 0x1402D88D0
 */

void __fastcall TRC_AutoTrade::ChangeTaxRate(TRC_AutoTrade *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v3; // rax@10
  unsigned int v4; // eax@10
  int v5; // eax@12
  CHonorGuild *v6; // rax@13
  __int64 v7; // [sp+0h] [bp-88h]@1
  _SYSTEMTIME SystemTime; // [sp+38h] [bp-50h]@5
  CPlayer *v9; // [sp+58h] [bp-30h]@10
  _qry_case_select_patriarch_comm v10; // [sp+68h] [bp-20h]@12
  TRC_AutoTrade *v11; // [sp+90h] [bp+8h]@1

  v11 = this;
  v1 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v11->m_bInit )
  {
    GetLocalTime(&SystemTime);
    if ( v11->m_byCurDay < (signed int)SystemTime.wDay
      || v11->m_wCurMonth < (signed int)SystemTime.wMonth
      || v11->m_wCurYear < (signed int)SystemTime.wYear )
    {
      if ( v11->m_bChangeTaxRate )
        TRC_AutoTrade::ChangeTaxRate(v11, (float)(signed int)v11->m_suggested.dwNext / 100.0);
      v11->m_byCurDay = SystemTime.wDay;
      v11->m_wCurMonth = SystemTime.wMonth;
      v11->m_wCurYear = SystemTime.wYear;
      TRC_AutoTrade::his_income_money(v11);
      v3 = CPvpUserAndGuildRankingSystem::Instance();
      v4 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v3, v11->m_byRace, 0);
      v9 = GetPtrPlayerFromSerial(&g_Player, 2532, v4);
      if ( v9 )
      {
        if ( v9->m_bOper )
        {
          _qry_case_select_patriarch_comm::_qry_case_select_patriarch_comm(&v10);
          v10.dwSerial = CPlayerDB::GetCharSerial(&v9->m_Param);
          v5 = _qry_case_select_patriarch_comm::size(&v10);
          CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -126, (char *)&v10, v5);
        }
      }
      v6 = CHonorGuild::Instance();
      CHonorGuild::ChangeHonorGuild(v6, v11->m_byRace);
    }
  }
}
