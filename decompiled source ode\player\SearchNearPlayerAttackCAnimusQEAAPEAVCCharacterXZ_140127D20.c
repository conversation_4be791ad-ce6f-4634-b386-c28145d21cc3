/*
 * Function: ?SearchNearPlayerAttack@CAnimus@@QEAAPEAVCCharacter@@XZ
 * Address: 0x140127D20
 */

CCharacter *__usercall CAnimus::SearchNearPlayerAttack@<rax>(CAnimus *this@<rcx>, float a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  float v6; // [sp+20h] [bp-28h]@4
  int v7; // [sp+24h] [bp-24h]@4
  int v8; // [sp+28h] [bp-20h]@4
  int v9; // [sp+2Ch] [bp-1Ch]@4
  int j; // [sp+30h] [bp-18h]@4
  CAnimus *v11; // [sp+50h] [bp+8h]@1

  v11 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  GetAngle(v11->m_fCurPos, v11->m_pMaster->m_fCurPos);
  v6 = a2;
  v7 = (signed int)floor(a2 / (2.0 * 3.1415926535) * 5.0 + 0.5) - 1;
  v8 = v7;
  v9 = v7;
  for ( j = 0; j < 5; ++j )
  {
    if ( v8 >= 5 )
      v8 = 0;
    if ( v9 < 0 )
      v9 = 4;
    if ( v11->m_pMaster->m_AroundSlot[v8] )
      return v11->m_pMaster->m_AroundSlot[v8];
    if ( v11->m_pMaster->m_AroundSlot[v9] )
      return v11->m_pMaster->m_AroundSlot[v9];
    ++v8;
    --v9;
  }
  return 0i64;
}
