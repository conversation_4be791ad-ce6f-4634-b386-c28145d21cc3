/*
 * Function: j_??$_Max_element@PEAVCNormalGuildBattleGuildMember@GUILD_BATTLE@@VCTopGoalPrediCate@CNormalGuildBattleGuild@2@@std@@YAPEAVCNormalGuildBattleGuildMember@GUILD_BATTLE@@PEAV12@0VCTopGoalPrediCate@CNormalGuildBattleGuild@2@@Z
 * Address: 0x1400114E1
 */

GUILD_BATTLE::CNormalGuildBattleGuildMember *__fastcall std::_Max_element<GUILD_BATTLE::CNormalGuildBattleGuildMember *,GUILD_BATTLE::CNormalGuildBattleGuild::CTopGoalPrediCate>(GUILD_BATTLE::CNormalGuildBattleGuildMember *_First, GUILD_BATTLE::CNormalGuildBattleGuildMember *_Last, GUILD_BATTLE::CNormalGuildBattleGuild::CTopGoalPrediCate _Pred)
{
  return std::_Max_element<GUILD_BATTLE::CNormalGuildBattleGuildMember *,GUILD_BATTLE::CNormalGuildBattleGuild::CTopGoalPrediCate>(
           _First,
           _Last,
           _Pred);
}
