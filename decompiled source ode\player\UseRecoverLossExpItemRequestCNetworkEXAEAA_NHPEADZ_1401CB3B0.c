/*
 * Function: ?UseRecoverLossExpItemRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CB3B0
 */

char __fastcall CNetworkEX::UseRecoverLossExpItemRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char v6; // al@6
  __int64 v7; // [sp+0h] [bp-98h]@1
  unsigned __int16 *v8; // [sp+30h] [bp-68h]@4
  CPlayer *v9; // [sp+38h] [bp-60h]@4
  char v10; // [sp+40h] [bp-58h]@6
  char szMsg; // [sp+54h] [bp-44h]@6
  char pbyType; // [sp+74h] [bp-24h]@6
  char v13; // [sp+75h] [bp-23h]@6

  v3 = &v7;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = (unsigned __int16 *)pBuf;
  v9 = &g_Player + n;
  if ( v9->m_bOper )
  {
    v6 = CPlayer::pc_UseRecoverLossExpItem(v9, *v8);
    v10 = v6;
    szMsg = v6;
    pbyType = 7;
    v13 = 27;
    CNetProcess::LoadSendMsg(unk_1414F2088, v9->m_ObjID.m_wIndex, &pbyType, &szMsg, 1u);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
