/*
 * Function: ?ProcCode@CCheckSumBaseConverter@@QEAAKEKK@Z
 * Address: 0x1402C13C0
 */

__int64 __fastcall CCheckSumBaseConverter::ProcCode(CCheckSumBaseConverter *this, char byIndex, unsigned int dwSerial, unsigned int dwValue)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char *v7; // [sp+0h] [bp-18h]@1

  v4 = (__int64 *)&v7;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7 = (char *)&CCheckSumBaseConverter::ms_dwKeys + 40 * (unsigned __int8)byIndex;
  return *(_DWORD *)&v7[4 * (dwSerial % 0xA)] ^ dwValue;
}
