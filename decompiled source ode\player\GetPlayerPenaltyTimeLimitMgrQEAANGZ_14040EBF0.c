/*
 * Function: ?GetPlayerPenalty@TimeLimitMgr@@QEAANG@Z
 * Address: 0x14040EBF0
 */

double __fastcall TimeLimitMgr::GetPlayerPenalty(TimeLimitMgr *this, unsigned __int16 wIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  double result; // xmm0_8@5
  __int64 v5; // [sp+0h] [bp-18h]@1
  TimeLimitMgr *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v6->m_wEnable )
  {
    if ( v6->m_lstTLStaus[wIndex].m_bUse )
    {
      if ( v6->m_lstTLStaus[wIndex].m_bAgeLimit )
        result = v6->m_lstTLStaus[wIndex].m_dPercent;
      else
        result = DOUBLE_1_0;
    }
    else
    {
      result = 0.0;
    }
  }
  else
  {
    result = DOUBLE_1_0;
  }
  return result;
}
