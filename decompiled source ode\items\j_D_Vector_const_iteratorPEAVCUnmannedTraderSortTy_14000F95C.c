/*
 * Function: j_??D?$_Vector_const_iterator@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEBAAEBQEAVCUnmannedTraderSortType@@XZ
 * Address: 0x14000F95C
 */

CUnmannedTraderSortType *const *__fastcall std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::operator*(std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this)
{
  return std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::operator*(this);
}
