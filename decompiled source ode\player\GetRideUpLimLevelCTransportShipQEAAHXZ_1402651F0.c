/*
 * Function: ?GetRideUpLimLevel@CTransportShip@@QEAAHXZ
 * Address: 0x1402651F0
 */

__int64 __fastcall CTransportShip::GetRideUpLimLevel(CTransportShip *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // [sp+0h] [bp-18h]@1
  int v5; // [sp+4h] [bp-14h]@5
  CTransportShip *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v1 = (int *)&v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  v4 = 0;
  if ( v6->m_bAnchor )
  {
    v5 = v6->m_byDirect == 0;
    v4 = v6->m_MgrTicket[v5].pLinkTicketItem->m_nUpLevelLim;
  }
  else
  {
    v4 = v6->m_MgrTicket[v6->m_byDirect].pLinkTicketItem->m_nUpLevelLim;
  }
  return v4;
}
