/*
 * Function: ?GiveItem@CQuestMgr@@QEAA_NEPEAU_action_node@@_N@Z
 * Address: 0x14028ABE0
 */

char __fastcall CQuestMgr::GiveItem(CQuestMgr *this, char byQuestDBSlot, _action_node *pActionNode, bool b<PERSON><PERSON><PERSON>Only)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char v6; // al@16
  __int64 v8; // [sp+0h] [bp-88h]@1
  bool bDelete; // [sp+20h] [bp-68h]@28
  char *strErrorCodePos; // [sp+28h] [bp-60h]@28
  _QUEST_DB_BASE *v11; // [sp+30h] [bp-58h]@4
  int j; // [sp+38h] [bp-50h]@4
  int *v13; // [sp+40h] [bp-48h]@8
  unsigned __int8 v14; // [sp+48h] [bp-40h]@12
  _base_fld *v15; // [sp+50h] [bp-38h]@13
  char v16; // [sp+58h] [bp-30h]@14
  int v17; // [sp+5Ch] [bp-2Ch]@14
  int k; // [sp+60h] [bp-28h]@14
  char *v19; // [sp+68h] [bp-20h]@17
  int v20; // [sp+70h] [bp-18h]@26
  CQuestMgr *v21; // [sp+90h] [bp+8h]@1
  char v22; // [sp+98h] [bp+10h]@1
  _action_node *v23; // [sp+A0h] [bp+18h]@1
  bool v24; // [sp+A8h] [bp+20h]@1

  v24 = bCheckOnly;
  v23 = pActionNode;
  v22 = byQuestDBSlot;
  v21 = this;
  v4 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = (_QUEST_DB_BASE *)((char *)v21->m_pQuestData + 13 * (unsigned __int8)byQuestDBSlot);
  for ( j = 0; ; ++j )
  {
    if ( j >= 3 )
      return 1;
    if ( v11->m_List[0].wNum[j] == 0xFFFF )
    {
      v13 = &v23[j].m_nActType;
      if ( *v13 == -1 )
        return 1;
      if ( *v13 == 4 || *v13 == 5 || *v13 == 12 )
      {
        v14 = GetItemTableCode((char *)v13 + 4);
        if ( v14 != 255 )
        {
          v15 = CRecordData::GetRecordByHash((CRecordData *)&unk_1799C6AA0 + v14, (const char *)v13 + 4, 2, 5);
          if ( !v15 )
          {
            v16 = 0;
            v17 = 0;
            for ( k = 0; ; ++k )
            {
              v6 = CPlayerDB::GetBagNum(&v21->m_pMaster->m_Param);
              if ( k >= 20 * (unsigned __int8)v6 )
                break;
              v19 = &v21->m_pMaster->m_Param.m_dbInven.m_pStorageList[k].m_bLoad;
              if ( *v19 && (unsigned __int8)v19[1] == v14 && *(_WORD *)(v19 + 3) == LOWORD(v15->m_dwIndex) && !v19[19] )
              {
                if ( v24 )
                {
                  if ( IsOverLapItem((unsigned __int8)v19[1]) )
                    v17 += *(_DWORD *)(v19 + 5);
                  else
                    ++v17;
                }
                else
                {
                  v20 = v13[49] - v17;
                  if ( IsOverLapItem((unsigned __int8)v19[1]) )
                  {
                    if ( *(_QWORD *)(v19 + 5) > (unsigned __int64)v20 )
                    {
                      LOBYTE(strErrorCodePos) = 0;
                      bDelete = 0;
                      CPlayer::Emb_AlterDurPoint(v21->m_pMaster, 0, k, -v20, 0, 0);
                      v17 += v20;
                      CQuestMgr::SendMsgToMaster_ReturnItemAfterQuest(v21, *(_WORD *)(v19 + 17), v20, v22);
                    }
                    else
                    {
                      strErrorCodePos = "CQuestMgr::GiveItem() -- 0";
                      bDelete = 1;
                      if ( !CPlayer::Emb_DelStorage(v21->m_pMaster, 0, k, 0, 1, "CQuestMgr::GiveItem() -- 0") )
                        continue;
                      v17 += *(_DWORD *)(v19 + 5);
                      CQuestMgr::SendMsgToMaster_ReturnItemAfterQuest(v21, *(_WORD *)(v19 + 17), -1, v22);
                    }
                  }
                  else
                  {
                    strErrorCodePos = "CQuestMgr::GiveItem() -- 1";
                    bDelete = 1;
                    if ( !CPlayer::Emb_DelStorage(v21->m_pMaster, 0, k, 0, 1, "CQuestMgr::GiveItem() -- 1") )
                      continue;
                    ++v17;
                    CQuestMgr::SendMsgToMaster_ReturnItemAfterQuest(v21, *(_WORD *)(v19 + 17), -1, v22);
                  }
                }
                if ( v17 >= v13[49] )
                {
                  v16 = 1;
                  break;
                }
              }
            }
            if ( !v16 )
              break;
          }
        }
      }
    }
  }
  if ( v24 )
    CQuestMgr::SendMsgToMaster_NoHaveGiveItem(v21, v22);
  return 0;
}
