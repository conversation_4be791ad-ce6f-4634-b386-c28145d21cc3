/*
 * Function: j_?pushdata@_qry_case_post_send@@QEAA_NKEKKPEAD000U_INVENKEY@@_KKK2@Z
 * Address: 0x14000446C
 */

bool __fastcall _qry_case_post_send::pushdata(_qry_case_post_send *this, unsigned int dwIndex, char byErr, unsigned int dwReceiverSerial, unsigned int dwSenderSerial, char *wszSendName, char *wszRecvName, char *wszTitle, char *wszContent, _INVENKEY key, unsigned __int64 dwDur, unsigned int dwUpt, unsigned int dwGold, unsigned __int64 lnUID)
{
  return _qry_case_post_send::pushdata(
           this,
           dwIndex,
           byErr,
           dwReceiverSerial,
           dwSenderSerial,
           wszSendName,
           wszRecvName,
           wszTitle,
           wszContent,
           key,
           dwDur,
           dwUpt,
           dwGold,
           lnUID);
}
