/*
 * Function: ?db_Load_Content@CMainThread@@QEAAEPEAD@Z
 * Address: 0x1401B3F00
 */

char __fastcall CMainThread::db_Load_Content(CMainThread *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  CMainThread *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  pData[4] = CRFWorldDatabase::Select_PostContent(v6->m_pWorldDB, *(_DWORD *)pData, pData + 16, 201);
  return 0;
}
