/*
 * Function: ?pc_CuttingComplete@CPlayer@@QEAAXE@Z
 * Address: 0x1400D32B0
 */

void __usercall CPlayer::pc_CuttingComplete(CPlayer *this@<rcx>, char byNp<PERSON><PERSON><PERSON>@<dl>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@4
  int v6; // eax@4
  __int64 v7; // kr00_8@7
  unsigned int v8; // eax@10
  unsigned int v9; // eax@12
  int v10; // ecx@12
  int v11; // eax@14
  __int64 v12; // [sp+0h] [bp-68h]@1
  unsigned int dwNewGold; // [sp+20h] [bp-48h]@12
  char *pszFileName; // [sp+28h] [bp-40h]@12
  unsigned __int64 ui64AddGold; // [sp+30h] [bp-38h]@4
  unsigned int v16; // [sp+38h] [bp-30h]@4
  char v17; // [sp+3Ch] [bp-2Ch]@4
  float v18; // [sp+40h] [bp-28h]@4
  int n; // [sp+44h] [bp-24h]@4
  _base_fld *v20; // [sp+48h] [bp-20h]@7
  char *v21; // [sp+50h] [bp-18h]@7
  char *v22; // [sp+58h] [bp-10h]@12
  CPlayer *v23; // [sp+70h] [bp+8h]@1

  v23 = this;
  v3 = &v12;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  ui64AddGold = 0i64;
  v5 = CPlayerDB::GetRaceCode(&v23->m_Param);
  v16 = 10000 - eGetTexRate(v5);
  v17 = 0;
  v6 = CPlayerDB::GetRaceCode(&v23->m_Param);
  eGetTex(v6);
  v18 = 1.0 - a3;
  for ( n = 0; n < GetMaxResKind(); ++n )
  {
    if ( (signed int)v23->m_Param.m_wCuttingResBuffer[n] > 0 )
    {
      v20 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 18, n);
      v21 = &v20[5].m_strCode[12];
      v7 = v23->m_Param.m_wCuttingResBuffer[n] * *(_DWORD *)&v20[5].m_strCode[12];
      ui64AddGold += ((signed int)v7 - HIDWORD(v7)) >> 1;
      v17 = 1;
    }
  }
  ui64AddGold *= v16;
  ui64AddGold /= 0x2710ui64;
  if ( v17 )
  {
    v8 = CPlayerDB::GetGold(&v23->m_Param);
    if ( !CanAddMoneyForMaxLimGold(ui64AddGold, v8) )
    {
      CPlayer::SendMsg_CuttingCompleteResult(v23, 1);
      return;
    }
    CPlayer::AddGold(v23, ui64AddGold, 1);
    v22 = v23->m_szItemHistoryFileName;
    v9 = CPlayerDB::GetGold(&v23->m_Param);
    v10 = v23->m_ObjID.m_wIndex;
    pszFileName = v22;
    dwNewGold = v9;
    CMgrAvatorItemHistory::cut_clear_item(
      &CPlayer::s_MgrItemHistory,
      v10,
      v23->m_Param.m_wCuttingResBuffer,
      ui64AddGold,
      v9,
      v22);
  }
  CPlayerDB::InitResBuffer(&v23->m_Param);
  if ( !v23->m_byUserDgr )
  {
    v11 = CPlayerDB::GetRaceCode(&v23->m_Param);
    eAddGold(v11, ui64AddGold);
  }
  if ( v23->m_pUserDB )
    CUserDB::Update_CuttingEmpty(v23->m_pUserDB);
  CPlayer::SendMsg_CuttingCompleteResult(v23, 0);
}
