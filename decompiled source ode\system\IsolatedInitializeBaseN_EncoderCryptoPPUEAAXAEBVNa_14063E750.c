/*
 * Function: ?IsolatedInitialize@BaseN_Encoder@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x14063E750
 */

void __fastcall CryptoPP::BaseN_Encoder::IsolatedInitialize(CryptoPP::BaseN_Encoder *this, const struct CryptoPP::NameValuePairs *a2)
{
  const char *v2; // rax@1
  CryptoPP::Name *v3; // rcx@1
  const char *v4; // rax@1
  CryptoPP::Name *v5; // rcx@1
  const char *v6; // rax@5
  CryptoPP::Name *v7; // rcx@5
  const char *v8; // rax@6
  signed int i; // [sp+20h] [bp-B8h]@11
  char v10; // [sp+24h] [bp-B4h]@6
  unsigned __int8 v11; // [sp+25h] [bp-B3h]@5
  CryptoPP::InvalidArgument v12; // [sp+28h] [bp-B0h]@3
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+78h] [bp-60h]@3
  unsigned __int8 v14; // [sp+A8h] [bp-30h]@3
  __int64 v15; // [sp+B0h] [bp-28h]@1
  CryptoPP::BaseN_Encoder *v16; // [sp+B8h] [bp-20h]@1
  int *v17; // [sp+C0h] [bp-18h]@1
  int v18; // [sp+C8h] [bp-10h]@9
  CryptoPP::BaseN_Encoder *v19; // [sp+E0h] [bp+8h]@1
  const struct CryptoPP::NameValuePairs *v20; // [sp+E8h] [bp+10h]@1

  v20 = a2;
  v19 = this;
  v15 = -2i64;
  v16 = (CryptoPP::BaseN_Encoder *)((char *)this + 48);
  v2 = CryptoPP::Name::EncodingLookupArray(this);
  CryptoPP::NameValuePairs::GetRequiredParameter<unsigned char const *>(v20, "BaseN_Encoder", v2, v16);
  v17 = (int *)((char *)v19 + 60);
  v4 = CryptoPP::Name::Log2Base(v3);
  CryptoPP::NameValuePairs::GetRequiredIntParameter(
    (CryptoPP::NameValuePairs *)v20,
    "BaseN_Encoder",
    v4,
    (int *)v19 + 15);
  if ( *((_DWORD *)v19 + 15) <= 0 || *((_DWORD *)v19 + 15) >= 8 )
  {
    memset(&v14, 0, sizeof(v14));
    std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
      &s,
      "BaseN_Encoder: Log2Base must be between 1 and 7 inclusive",
      v14);
    CryptoPP::InvalidArgument::InvalidArgument(&v12, &s);
    CxxThrowException_0((__int64)&v12, (__int64)&TI3_AVInvalidArgument_CryptoPP__);
  }
  v6 = CryptoPP::Name::PaddingByte(v5);
  if ( (unsigned __int8)CryptoPP::NameValuePairs::GetValue<unsigned char>(v20, v6, &v11) )
  {
    v8 = CryptoPP::Name::Pad(v7);
    v10 = CryptoPP::NameValuePairs::GetValueWithDefault<bool>((__int64)v20, (__int64)v8, 1);
  }
  else
  {
    v10 = 0;
  }
  if ( v10 )
    v18 = v11;
  else
    v18 = -1;
  *((_DWORD *)v19 + 14) = v18;
  *((_DWORD *)v19 + 18) = 0;
  *((_DWORD *)v19 + 17) = 0;
  for ( i = 8; i % *((_DWORD *)v19 + 15); i += 8 )
    ;
  *((_DWORD *)v19 + 16) = i / *((_DWORD *)v19 + 15);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::New(
    (CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)((char *)v19 + 80),
    *((_DWORD *)v19 + 16));
}
