/*
 * Function: ?CheckGetGravityStone@CGuildBattleController@@QEAAXGKPEAVCPlayer@@@Z
 * Address: 0x1403D60F0
 */

void __fastcall CGuildBattleController::CheckGetGravityStone(CGuildBattleController *this, unsigned __int16 wIndex, unsigned int dwObjSerial, CPlayer *pkPlayer)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleManager *v6; // rax@7
  __int64 v7; // [sp+0h] [bp-48h]@1
  int n; // [sp+30h] [bp-18h]@4
  unsigned int v9; // [sp+34h] [bp-14h]@7
  unsigned int v10; // [sp+38h] [bp-10h]@7
  int v11; // [sp+3Ch] [bp-Ch]@5
  unsigned __int16 v12; // [sp+58h] [bp+10h]@1
  unsigned int dwObjSeriala; // [sp+60h] [bp+18h]@1

  dwObjSeriala = dwObjSerial;
  v12 = wIndex;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  n = pkPlayer->m_ObjID.m_wIndex;
  if ( pkPlayer->m_Param.m_pGuild )
    v11 = pkPlayer->m_Param.m_pGuild->m_dwSerial;
  else
    v11 = -1;
  v9 = v11;
  v10 = pkPlayer->m_pUserDB->m_dwSerial;
  v6 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
  GUILD_BATTLE::CNormalGuildBattleManager::CheckGetGravityStone(v6, v12, dwObjSeriala, n, v9, v10);
}
