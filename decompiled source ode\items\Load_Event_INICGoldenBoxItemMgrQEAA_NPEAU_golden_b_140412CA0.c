/*
 * Function: ?Load_Event_INI@CGoldenBoxItemMgr@@QEAA_NPEAU_golden_box_item_ini@@@Z
 * Address: 0x140412CA0
 */

char __fastcall CGoldenBoxItemMgr::Load_Event_INI(CGoldenBoxItemMgr *this, _golden_box_item_ini *pIni)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-178h]@1
  UINT v6; // [sp+30h] [bp-148h]@6
  struct tm Tm; // [sp+48h] [bp-130h]@27
  char Dst; // [sp+88h] [bp-F0h]@6
  char _Dest[20]; // [sp+B8h] [bp-C0h]@6
  char v10[20]; // [sp+E8h] [bp-90h]@6
  UINT v11; // [sp+104h] [bp-74h]@12
  char DstBuf; // [sp+118h] [bp-60h]@43
  char v13; // [sp+119h] [bp-5Fh]@43
  unsigned int j; // [sp+154h] [bp-24h]@43
  unsigned int k; // [sp+158h] [bp-20h]@53
  unsigned __int64 v16; // [sp+168h] [bp-10h]@4
  CGoldenBoxItemMgr *v17; // [sp+180h] [bp+8h]@1
  _golden_box_item_ini *pInia; // [sp+188h] [bp+10h]@1

  pInia = pIni;
  v17 = this;
  v2 = &v5;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v16 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( pIni )
  {
    v6 = 0;
    memset_0(&Dst, 0, 0x14ui64);
    memset_0(_Dest, 0, 0x14ui64);
    memset_0(v10, 0, 0x14ui64);
    pInia->m_bUse_event = 0;
    pInia->m_EventTime[0] = 0;
    pInia->m_EventTime[1] = 0;
    v6 = GetPrivateProfileIntA("GoldBox_Item", "USE", 1, "./initialize/GoldenBox_Item.ini");
    if ( v6 == 1 )
    {
      pInia->m_bUse_event = 0;
      result = 1;
    }
    else
    {
      pInia->m_bUse_event = 1;
      pInia->m_dwStarterBoxCnt = GetPrivateProfileIntA(
                                   "GoldBox_Item",
                                   "StarterBox",
                                   1000,
                                   "./initialize/GoldenBox_Item.ini");
      v17->m_wStarterBoxNum = pInia->m_dwStarterBoxCnt;
      v6 = GetPrivateProfileStringA(
             "GoldBox_Item",
             "StarterBoxCode_0",
             "NULL",
             (LPSTR)pInia->m_szStarterBoxCode,
             0x40u,
             "./initialize/GoldenBox_Item.ini");
      if ( !strcmp_0((const char *)pInia->m_szStarterBoxCode, "NULL") )
      {
        pInia->m_bUse_event = 0;
        result = 0;
      }
      else
      {
        v6 = GetPrivateProfileStringA(
               "GoldBox_Item",
               "StarterBoxCode_1",
               "NULL",
               pInia->m_szStarterBoxCode[1],
               0x40u,
               "./initialize/GoldenBox_Item.ini");
        if ( !strcmp_0(pInia->m_szStarterBoxCode[1], "NULL") )
        {
          pInia->m_bUse_event = 0;
          result = 0;
        }
        else
        {
          v11 = -1;
          v11 = GetPrivateProfileIntA("GoldBox_Item", "GoldBox_Num", 0, "./initialize/GoldenBox_Item.ini");
          if ( (signed int)v11 > 0 && (signed int)v11 <= 255 )
          {
            pInia->m_byLoopCnt = v11;
            v6 = GetPrivateProfileIntA("GoldBox_Item", "BEGIN_YEAR", 0, "./initialize/GoldenBox_Item.ini");
            if ( v6 )
            {
              pInia->m_wYear[0] = v6;
              v6 = GetPrivateProfileIntA("GoldBox_Item", "BEGIN_MONTH", 0, "./initialize/GoldenBox_Item.ini");
              if ( v6 )
              {
                pInia->m_byMonth[0] = v6;
                v6 = GetPrivateProfileIntA("GoldBox_Item", "BEGIN_DAY", 0, "./initialize/GoldenBox_Item.ini");
                if ( v6 )
                {
                  pInia->m_byDay[0] = v6;
                  v6 = GetPrivateProfileIntA("GoldBox_Item", "BEGIN_HOUR", 0, "./initialize/GoldenBox_Item.ini");
                  if ( (v6 & 0x80000000) == 0 && (signed int)v6 <= 23 )
                  {
                    pInia->m_byHour[0] = v6;
                    v6 = GetPrivateProfileIntA("GoldBox_Item", "BEGIN_MINUTE", 0, "./initialize/GoldenBox_Item.ini");
                    if ( (v6 & 0x80000000) == 0 && (signed int)v6 <= 59 )
                    {
                      pInia->m_byMinute[0] = v6;
                      memset_0(&Tm, 0, 0x24ui64);
                      Tm.tm_year = pInia->m_wYear[0] - 1900;
                      Tm.tm_mon = pInia->m_byMonth[0] - 1;
                      Tm.tm_mday = pInia->m_byDay[0];
                      Tm.tm_hour = pInia->m_byHour[0];
                      Tm.tm_min = pInia->m_byMinute[0];
                      Tm.tm_sec = 0;
                      Tm.tm_isdst = -1;
                      pInia->m_EventTime[0] = _mktime32(&Tm);
                      if ( pInia->m_EventTime[0] == -1 )
                      {
                        pInia->m_bUse_event = 0;
                        result = 0;
                      }
                      else
                      {
                        v6 = GetPrivateProfileIntA("GoldBox_Item", "END_YEAR", 0, "./initialize/GoldenBox_Item.ini");
                        if ( v6 )
                        {
                          pInia->m_wYear[1] = v6;
                          v6 = GetPrivateProfileIntA("GoldBox_Item", "END_MONTH", 0, "./initialize/GoldenBox_Item.ini");
                          if ( v6 )
                          {
                            pInia->m_byMonth[1] = v6;
                            v6 = GetPrivateProfileIntA("GoldBox_Item", "END_DAY", 0, "./initialize/GoldenBox_Item.ini");
                            if ( v6 )
                            {
                              pInia->m_byDay[1] = v6;
                              v6 = GetPrivateProfileIntA(
                                     "GoldBox_Item",
                                     "END_HOUR",
                                     0,
                                     "./initialize/GoldenBox_Item.ini");
                              if ( (v6 & 0x80000000) == 0 && (signed int)v6 <= 23 )
                              {
                                pInia->m_byHour[1] = v6;
                                v6 = GetPrivateProfileIntA(
                                       "GoldBox_Item",
                                       "END_MINUTE",
                                       0,
                                       "./initialize/GoldenBox_Item.ini");
                                if ( (v6 & 0x80000000) == 0 && (signed int)v6 <= 59 )
                                {
                                  pInia->m_byMinute[1] = v6;
                                  memset_0(&Tm, 0, 0x24ui64);
                                  Tm.tm_year = pInia->m_wYear[1] - 1900;
                                  Tm.tm_mon = pInia->m_byMonth[1] - 1;
                                  Tm.tm_mday = pInia->m_byDay[1];
                                  Tm.tm_hour = pInia->m_byHour[1];
                                  Tm.tm_min = pInia->m_byMinute[1];
                                  Tm.tm_sec = 0;
                                  Tm.tm_isdst = -1;
                                  pInia->m_EventTime[1] = _mktime32(&Tm);
                                  if ( pInia->m_EventTime[1] == -1 )
                                  {
                                    pInia->m_bUse_event = 0;
                                    result = 0;
                                  }
                                  else
                                  {
                                    DstBuf = 0;
                                    memset(&v13, 0, 0x31ui64);
                                    for ( j = 0; (signed int)j < pInia->m_byLoopCnt; ++j )
                                    {
                                      memset_0(&DstBuf, 0, 0x32ui64);
                                      sprintf_s(&DstBuf, 0x32ui64, "BoxItem_Code_%d", j);
                                      v6 = GetPrivateProfileStringA(
                                             "GoldBox_Item",
                                             &DstBuf,
                                             "NULL",
                                             pInia->m_szGoldenBoxcode[(signed __int64)(signed int)j],
                                             0x40u,
                                             "./initialize/GoldenBox_Item.ini");
                                      if ( !strcmp_0(pInia->m_szGoldenBoxcode[(signed __int64)(signed int)j], "NULL") )
                                      {
                                        pInia->m_bUse_event = 0;
                                        return 0;
                                      }
                                      memset_0(&DstBuf, 0, 0x32ui64);
                                      sprintf_s(&DstBuf, 0x32ui64, "Box_Max_%d", j);
                                      v6 = GetPrivateProfileIntA(
                                             "GoldBox_Item",
                                             &DstBuf,
                                             0,
                                             "./initialize/GoldenBox_Item.ini");
                                      if ( (v6 & 0x80000000) != 0 || (signed int)v6 > 60000 )
                                      {
                                        pInia->m_bUse_event = 0;
                                        return 0;
                                      }
                                      pInia->m_wGoldenBoxmax[j] = v6;
                                      memset_0(&DstBuf, 0, 0x32ui64);
                                      sprintf_s(&DstBuf, 0x32ui64, "GoldBox_Item_Out_List_%d", j);
                                      v6 = GetPrivateProfileIntA(
                                             &DstBuf,
                                             "List_Count",
                                             0,
                                             "./initialize/GoldenBox_Item.ini");
                                      if ( (v6 & 0x80000000) != 0 || (signed int)v6 > 100 )
                                      {
                                        pInia->m_bUse_event = 0;
                                        return 0;
                                      }
                                      pInia->m_bygolden_item_num[j] = v6;
                                      for ( k = 0; (signed int)k < pInia->m_bygolden_item_num[j]; ++k )
                                      {
                                        sprintf_s<20>((char (*)[20])&Dst, "Item%d", k);
                                        sprintf_s<20>((char (*)[20])_Dest, "Num%d", k);
                                        sprintf_s<20>((char (*)[20])v10, "Rate%d", k);
                                        memset_0(&DstBuf, 0, 0x32ui64);
                                        sprintf_s(&DstBuf, 0x32ui64, "GoldBox_Item_Out_List_%d", j);
                                        GetPrivateProfileStringA(
                                          &DstBuf,
                                          &Dst,
                                          "NULL",
                                          pInia->m_golden_box_item_list[j][k].m_szLimcode,
                                          0x40u,
                                          "./initialize/GoldenBox_Item.ini");
                                        if ( !strcmp_0(pInia->m_golden_box_item_list[j][k].m_szLimcode, "NULL") )
                                        {
                                          pInia->m_bUse_event = 0;
                                          return 0;
                                        }
                                        v6 = GetPrivateProfileIntA(&DstBuf, _Dest, 0, "./initialize/GoldenBox_Item.ini");
                                        if ( (v6 & 0x80000000) != 0 || (signed int)v6 > 60000 )
                                        {
                                          pInia->m_bUse_event = 0;
                                          return 0;
                                        }
                                        pInia->m_golden_box_item_list[j][k].m_wLimcount = v6;
                                        v6 = GetPrivateProfileIntA(&DstBuf, v10, 0, "./initialize/GoldenBox_Item.ini");
                                        if ( (v6 & 0x80000000) != 0 || (signed int)v6 >= 10000 )
                                        {
                                          pInia->m_bUse_event = 0;
                                          return 0;
                                        }
                                        pInia->m_golden_box_item_list[j][k].m_wRate = v6;
                                      }
                                    }
                                    CGoldenBoxItemMgr::Set_FromINIToStruct(v17, pInia);
                                    result = 1;
                                  }
                                }
                                else
                                {
                                  pInia->m_bUse_event = 0;
                                  result = 0;
                                }
                              }
                              else
                              {
                                pInia->m_bUse_event = 0;
                                result = 0;
                              }
                            }
                            else
                            {
                              pInia->m_bUse_event = 0;
                              result = 0;
                            }
                          }
                          else
                          {
                            pInia->m_bUse_event = 0;
                            result = 0;
                          }
                        }
                        else
                        {
                          pInia->m_bUse_event = 0;
                          result = 0;
                        }
                      }
                    }
                    else
                    {
                      pInia->m_bUse_event = 0;
                      result = 0;
                    }
                  }
                  else
                  {
                    pInia->m_bUse_event = 0;
                    result = 0;
                  }
                }
                else
                {
                  pInia->m_bUse_event = 0;
                  result = 0;
                }
              }
              else
              {
                pInia->m_bUse_event = 0;
                result = 0;
              }
            }
            else
            {
              pInia->m_bUse_event = 0;
              result = 0;
            }
          }
          else
          {
            result = 0;
          }
        }
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
