/*
 * Function: j_??$_Uninit_copy@PEAU?$pair@HH@std@@PEAU12@V?$allocator@U?$pair@HH@std@@@2@@std@@YAPEAU?$pair@HH@0@PEAU10@00AEAV?$allocator@U?$pair@HH@std@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140008436
 */

std::pair<int,int> *__fastcall std::_Uninit_copy<std::pair<int,int> *,std::pair<int,int> *,std::allocator<std::pair<int,int>>>(std::pair<int,int> *_First, std::pair<int,int> *_Last, std::pair<int,int> *_Dest, std::allocator<std::pair<int,int> > *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<std::pair<int,int> *,std::pair<int,int> *,std::allocator<std::pair<int,int>>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
