/*
 * Function: ?GetBase@?$DL_FixedBasePrecomputationImpl@UEC2NPoint@CryptoPP@@@CryptoPP@@UEBAAEBUEC2NPoint@2@AEBV?$DL_GroupPrecomputation@UEC2NPoint@CryptoPP@@@2@@Z
 * Address: 0x140556170
 */

signed __int64 __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::GetBase(__int64 a1, int (__fastcall ***a2)(_QWORD))
{
  signed __int64 v2; // rax@3
  signed __int64 v4; // [sp+28h] [bp-10h]@2
  __int64 v5; // [sp+40h] [bp+8h]@1

  v5 = a1;
  if ( (unsigned __int8)(**a2)(a2) )
  {
    v4 = v5 + 8;
  }
  else
  {
    LODWORD(v2) = std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::operator[](v5 + 112, 0i64);
    v4 = v2;
  }
  return v4;
}
