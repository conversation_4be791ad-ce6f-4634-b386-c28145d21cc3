/*
 * Function: ?SendMsg_VoteCancelInform@CGuild@@QEAAXXZ
 * Address: 0x140256410
 */

void __fastcall CGuild::SendMsg_VoteCancelInform(CGuild *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v6; // [sp+55h] [bp-23h]@4
  int j; // [sp+64h] [bp-14h]@4
  _guild_member_info *v8; // [sp+68h] [bp-10h]@7
  CGuild *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(_DWORD *)szMsg = v9->m_SuggestedMatter.dwMatterVoteSynKey;
  pbyType = 27;
  v6 = 23;
  for ( j = 0; j < 50; ++j )
  {
    v8 = &v9->m_MemberData[j];
    if ( _guild_member_info::IsFill(v8)
      && v8->pPlayer
      && v8->dwSerial != v9->m_dwSuggesterSerial
      && _suggested_matter::IsVotable(&v9->m_SuggestedMatter, v8->dwSerial) )
    {
      CNetProcess::LoadSendMsg(unk_1414F2088, v8->pPlayer->m_ObjID.m_wIndex, &pbyType, szMsg, 4u);
    }
  }
}
