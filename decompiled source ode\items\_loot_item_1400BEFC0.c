/*
 * Function: _loot_item
 * Address: 0x1400BEFC0
 */

char __fastcall loot_item(CPlayer *pOwner, char *pszItemCode, int nNum, char *pszUpTalCode, int nUpNum)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-108h]@1
  CMapData *pMap; // [sp+30h] [bp-D8h]@40
  unsigned __int16 wLayerIndex; // [sp+38h] [bp-D0h]@40
  float *pStdPos; // [sp+40h] [bp-C8h]@40
  bool bHide; // [sp+48h] [bp-C0h]@40
  int nTableCode; // [sp+50h] [bp-B8h]@4
  _base_fld *v14; // [sp+58h] [bp-B0h]@8
  unsigned __int64 dwExp; // [sp+60h] [bp-A8h]@10
  unsigned __int8 v16; // [sp+68h] [bp-A0h]@16
  char v17; // [sp+69h] [bp-9Fh]@15
  _ItemUpgrade_fld *v18; // [sp+70h] [bp-98h]@20
  int v19; // [sp+78h] [bp-90h]@22
  _base_fld *v20; // [sp+80h] [bp-88h]@24
  int j; // [sp+88h] [bp-80h]@29
  _STORAGE_LIST::_db_con pItem; // [sp+98h] [bp-70h]@31
  _TimeItem_fld *v23; // [sp+D8h] [bp-30h]@31
  __time32_t Time; // [sp+E4h] [bp-24h]@33
  int k; // [sp+F4h] [bp-14h]@37
  CPlayer *pOwnera; // [sp+110h] [bp+8h]@1
  const char *psItemCode; // [sp+118h] [bp+10h]@1
  int v28; // [sp+120h] [bp+18h]@1
  char *szRecordCode; // [sp+128h] [bp+20h]@1

  szRecordCode = pszUpTalCode;
  v28 = nNum;
  psItemCode = pszItemCode;
  pOwnera = pOwner;
  v5 = &v8;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  nTableCode = GetItemTableCode(pszItemCode);
  if ( nTableCode == -1 )
    return 0;
  if ( nTableCode == 19 )
    return 0;
  v14 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + nTableCode, psItemCode);
  if ( !v14 )
    return 0;
  LODWORD(dwExp) = 0;
  if ( IsOverLapItem(nTableCode) )
    LODWORD(dwExp) = 99;
  else
    LODWORD(dwExp) = GetItemDurPoint(nTableCode, v14->m_dwIndex);
  if ( v28 > 100 )
    v28 = 100;
  v17 = GetItemKindCode(nTableCode);
  if ( v17 )
  {
    if ( v17 != 1 )
      return 0;
    HIDWORD(dwExp) = GetMaxParamFromExp(v14->m_dwIndex, (unsigned int)dwExp);
  }
  else
  {
    v16 = GetDefItemUpgSocketNum(nTableCode, v14->m_dwIndex);
    HIDWORD(dwExp) = GetBitAfterSetLimSocket(v16);
  }
  v18 = 0i64;
  if ( (signed int)v16 <= 0 || !szRecordCode )
    goto LABEL_46;
  v19 = GetItemTableCode(szRecordCode);
  if ( v19 != 18 )
    return 0;
  v20 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 18, szRecordCode);
  if ( !v20 )
    return 0;
  v18 = CItemUpgradeTable::GetRecordFromRes(&stru_1799C69D8, v20->m_dwIndex);
  if ( v18 && v18->m_dwIndex < 0xD )
  {
LABEL_46:
    for ( j = 0; j < v28; ++j )
    {
      _STORAGE_LIST::_db_con::_db_con(&pItem);
      pItem.m_byTableCode = nTableCode;
      pItem.m_wItemIndex = v14->m_dwIndex;
      pItem.m_dwDur = (unsigned int)dwExp;
      v23 = TimeItem::FindTimeRec(nTableCode, v14->m_dwIndex);
      if ( v23 && v23->m_nCheckType )
      {
        _time32(&Time);
        pItem.m_byCsMethod = v23->m_nCheckType;
        pItem.m_dwT = v23->m_nUseTime + Time;
        pItem.m_dwLendRegdTime = Time;
      }
      if ( v18 )
      {
        if ( nUpNum > v16 )
          nUpNum = v16;
        for ( k = 0; k < nUpNum; ++k )
          HIDWORD(dwExp) = GetBitAfterUpgrade(HIDWORD(dwExp), v18->m_dwIndex, k);
      }
      pItem.m_dwLv = HIDWORD(dwExp);
      bHide = 1;
      pStdPos = pOwnera->m_fCurPos;
      wLayerIndex = pOwnera->m_wMapLayerIndex;
      pMap = pOwnera->m_pCurMap;
      if ( !CreateItemBox(&pItem, pOwnera, 0xFFFFFFFF, 0, 0i64, 2, pMap, wLayerIndex, pOwnera->m_fCurPos, 1) )
        return 0;
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
