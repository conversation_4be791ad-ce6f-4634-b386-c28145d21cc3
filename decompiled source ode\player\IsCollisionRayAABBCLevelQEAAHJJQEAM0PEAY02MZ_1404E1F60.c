/*
 * Function: ?IsCollisionRayAABB@CLevel@@QEAAHJJQEAM0PEAY02M@Z
 * Address: 0x1404E1F60
 */

__int64 __fastcall CLevel::IsCollisionRayAABB(CLevel *this, int a2, int a3, float *const a4, float *const a5, float (*a6)[3])
{
  float *v6; // rbx@1
  float v7; // xmm8_4@1
  float v8; // xmm6_4@1
  float v10; // [sp+30h] [bp-98h]@1
  float v11; // [sp+34h] [bp-94h]@1
  float v12; // [sp+38h] [bp-90h]@1
  float v13; // [sp+40h] [bp-88h]@1
  int v14; // [sp+44h] [bp-84h]@1
  int v15; // [sp+48h] [bp-80h]@1
  float v16; // [sp+50h] [bp-78h]@1
  float v17; // [sp+54h] [bp-74h]@1
  float v18; // [sp+58h] [bp-70h]@1
  float v19; // [sp+60h] [bp-68h]@1
  float v20; // [sp+64h] [bp-64h]@1
  float v21; // [sp+68h] [bp-60h]@1
  float v22; // [sp+70h] [bp-58h]@1
  float v23; // [sp+74h] [bp-54h]@1
  float v24; // [sp+78h] [bp-50h]@1
  float v25; // [sp+80h] [bp-48h]@1
  int v26; // [sp+84h] [bp-44h]@1
  int v27; // [sp+88h] [bp-40h]@1

  v6 = a4;
  v7 = (float)((float)((float)((float)((float)(a2 - dword_184A79BA8) * 2.0) / (float)dword_184A79BB0) - 1.0)
             / dword_184A79B2C[0])
     * 3660000.0;
  v8 = (float)(-0.0
             - (float)((float)((float)((float)((float)(a3 - dword_184A79BAC) * 2.0) / (float)dword_184A79BB4) - 1.0)
                     / *(float *)&dword_184A79B40))
     * 3660000.0;
  D3DXMatrixInverse_0(&v16, 0i64, &stru_184A79A6C);
  v10 = (float)((float)(v19 * v8) + (float)(v16 * v7)) + (float)(v22 * 3660000.0);
  v13 = v25;
  v12 = (float)((float)(v21 * v8) + (float)(v18 * v7)) + (float)(v24 * 3660000.0);
  v14 = v26;
  v15 = v27;
  v11 = (float)((float)(v20 * v8) + (float)(v17 * v7)) + (float)(v23 * 3660000.0);
  return (unsigned int)IsRayAABB(v6, a5, &v13, &v10, a6) != 0;
}
