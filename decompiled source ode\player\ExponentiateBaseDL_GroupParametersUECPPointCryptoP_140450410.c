/*
 * Function: ?ExponentiateBase@?$DL_GroupParameters@UECPPoint@CryptoPP@@@CryptoPP@@UEBA?AUECPPoint@2@AEBVInteger@2@@Z
 * Address: 0x140450410
 */

CryptoPP::ECPPoint *__fastcall CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::ExponentiateBase(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *this, CryptoPP::ECPPoint *result, CryptoPP::Integer *exponent)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // rax@4
  __int64 v6; // rax@4
  __int64 v8; // [sp+0h] [bp-48h]@1
  int v9; // [sp+20h] [bp-28h]@4
  __int64 v10; // [sp+28h] [bp-20h]@4
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *v11; // [sp+50h] [bp+8h]@1
  CryptoPP::ECPPoint *v12; // [sp+58h] [bp+10h]@1
  CryptoPP::Integer *v13; // [sp+60h] [bp+18h]@1

  v13 = exponent;
  v12 = result;
  v11 = this;
  v3 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = 0;
  LODWORD(v5) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *))v11->vfptr[6].__vecDelDtor)(v11);
  v10 = v5;
  LODWORD(v6) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *))v11->vfptr[5].__vecDelDtor)(v11);
  (*(void (__fastcall **)(__int64, CryptoPP::ECPPoint *, __int64, CryptoPP::Integer *))(*(_QWORD *)v10 + 48i64))(
    v10,
    v12,
    v6,
    v13);
  return v12;
}
