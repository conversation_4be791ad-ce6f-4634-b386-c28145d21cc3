/*
 * Function: ??A?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEAAAEAVCMoveMapLimitRightInfo@@_K@Z
 * Address: 0x1403A2260
 */

CMoveMapLimitRightInfo *__fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::operator[](std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, unsigned __int64 _Pos)
{
  return &this->_Myfirst[_Pos];
}
