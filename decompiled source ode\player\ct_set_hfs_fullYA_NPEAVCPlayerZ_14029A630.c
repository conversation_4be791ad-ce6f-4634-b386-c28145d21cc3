/*
 * Function: ?ct_set_hfs_full@@YA_NPEAVCPlayer@@@Z
 * Address: 0x14029A630
 */

bool __fastcall ct_set_hfs_full(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  __int64 v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1

  v5 = pOne;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5 && v5->m_bOper )
    result = CPlayer::SF_HFSInc_Once(v5, (CCharacter *)&v5->vfptr);
  else
    result = 0;
  return result;
}
