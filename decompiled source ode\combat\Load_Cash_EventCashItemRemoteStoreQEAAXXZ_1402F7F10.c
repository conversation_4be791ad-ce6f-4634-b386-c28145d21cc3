/*
 * Function: ?Load_Cash_Event@CashItemRemoteStore@@QEAAXXZ
 * Address: 0x1402F7F10
 */

void __fastcall CashItemRemoteStore::Load_Cash_Event(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-158h]@1
  char szEventName; // [sp+30h] [bp-128h]@6
  int j; // [sp+134h] [bp-24h]@4
  unsigned __int64 v6; // [sp+140h] [bp-18h]@4
  CashItemRemoteStore *v7; // [sp+160h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (unsigned __int64)&v3 ^ _security_cookie;
  for ( j = 0; j < 3; ++j )
  {
    CNetTimer::BeginTimer(&v7->m_cash_event[j].m_event_timer, 0x3E8u);
    v7->m_cash_event[j].m_event_inform_before[0] = 1800;
    v7->m_cash_event[j].m_event_inform_before[1] = 300;
    CLogFile::Write(&v7->m_cash_event[j].m_event_log, "Server Start and LogFile Loaing");
    CashItemRemoteStore::Set_CashEvent_Status(v7, j, 0);
    Get_CashEvent_Name(j, &szEventName);
    CashItemRemoteStore::Load_Event_INI(
      v7,
      &v7->m_cash_event[j].m_ini,
      &v7->m_cash_event[j].m_event_ini_file_time,
      &szEventName);
    if ( j == 2 )
      CashItemRemoteStore::Load_LimitedSale_Event_INI(
        v7,
        &v7->m_cash_event[2].m_ini,
        &v7->m_cash_event[2].m_event_ini_file_time,
        &szEventName);
    CashItemRemoteStore::Check_Loaded_Event_Status(v7, j);
  }
}
