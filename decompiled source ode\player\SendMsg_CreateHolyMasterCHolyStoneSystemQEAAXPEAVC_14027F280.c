/*
 * Function: ?SendMsg_CreateHolyMaster@CHolyStoneSystem@@QEAAXPEAVCPlayer@@H@Z
 * Address: 0x14027F280
 */

void __fastcall CHolyStoneSystem::SendMsg_CreateHolyMaster(CHolyStoneSystem *this, CPlayer *pkDestroyer, int nControlSec)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@4
  char *v6; // rax@4
  __int64 v7; // [sp+0h] [bp-A8h]@1
  _create_holy_master_zocl v8; // [sp+38h] [bp-70h]@4
  char pbyType; // [sp+74h] [bp-34h]@4
  char v10; // [sp+75h] [bp-33h]@4
  unsigned int dwClientIndex; // [sp+84h] [bp-24h]@4
  unsigned __int64 v12; // [sp+90h] [bp-18h]@4
  CHolyStoneSystem *v13; // [sp+B0h] [bp+8h]@1
  CPlayer *v14; // [sp+B8h] [bp+10h]@1
  int v15; // [sp+C0h] [bp+18h]@1

  v15 = nControlSec;
  v14 = pkDestroyer;
  v13 = this;
  v3 = &v7;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = (unsigned __int64)&v7 ^ _security_cookie;
  _create_holy_master_zocl::_create_holy_master_zocl(&v8);
  v8.byHolyStoneRaceCode = CPlayerDB::GetRaceCode(&v14->m_Param);
  v8.byPlayerRaceCode = v13->m_SaveData.m_nHolyMasterRace;
  v5 = CPlayerDB::GetCharNameW(&v14->m_Param);
  v8.byNameLen = strlen_0(v5) + 1;
  v6 = CPlayerDB::GetCharNameW(&v14->m_Param);
  strcpy_0(v8.wszName, v6);
  v8.nControlSec = v15;
  v8.dwObjSerial = v13->m_SaveData.m_dwDestroyerSerial;
  pbyType = 25;
  v10 = 11;
  for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
  {
    if ( *(&g_Player.m_bLive + 50856 * (signed int)dwClientIndex) )
      CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &v8.byHolyStoneRaceCode, 0x1Cu);
  }
}
