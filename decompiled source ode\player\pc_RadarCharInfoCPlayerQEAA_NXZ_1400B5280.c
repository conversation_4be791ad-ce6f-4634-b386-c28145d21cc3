/*
 * Function: ?pc_RadarCharInfo@CPlayer@@QEAA_NXZ
 * Address: 0x1400B5280
 */

char __fastcall CPlayer::pc_RadarCharInfo(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  CRadarItemMgr *v5; // [sp+20h] [bp-18h]@6
  _RadarItem_fld *pRadarFld; // [sp+28h] [bp-10h]@7
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !v7->m_pUserDB )
    return 0;
  v5 = &v7->m_pUserDB->m_RadarItemMgr;
  if ( CRadarItemMgr::IsUse(v5) )
  {
    pRadarFld = (_RadarItem_fld *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 34, v5->m_strRadarCode);
    if ( !pRadarFld )
      return 0;
    CRadarItemMgr::RadarProc(v5, pRadarFld);
    CPlayer::SendMsg_RadarCharSearchResult(v7);
  }
  return 1;
}
