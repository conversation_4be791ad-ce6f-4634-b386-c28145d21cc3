/*
 * Function: ?IsUseBooster@CEquipItemSFAgent@@QEAA_NXZ
 * Address: 0x140121780
 */

bool __fastcall CEquipItemSFAgent::IsUseBooster(CEquipItemSFAgent *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  _sf_continous *v5; // [sp+20h] [bp-18h]@4
  CEquipItemSFAgent *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = CEquipItemSFAgent::GetEquipSFCont(v6, 7);
  return v5 != 0i64;
}
