/*
 * Function: ?dtor$4@?0??OnInitControl@CMFCLinkCtrl@@IEAA_J_K_J@Z@4HA_1
 * Address: 0x1405AA9F0
 */

int `CMFCLinkCtrl::OnInitControl'::`1'::dtor$4()
{
  return std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
}
