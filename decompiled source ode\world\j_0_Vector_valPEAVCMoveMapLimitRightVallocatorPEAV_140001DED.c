/*
 * Function: j_??0?$_Vector_val@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@IEAA@V?$allocator@PEAVCMoveMapLimitRight@@@1@@Z
 * Address: 0x140001DED
 */

void __fastcall std::_Vector_val<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Vector_val<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(std::_Vector_val<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, std::allocator<CMoveMapLimitRight *> _Al)
{
  std::_Vector_val<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Vector_val<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(
    this,
    _Al);
}
