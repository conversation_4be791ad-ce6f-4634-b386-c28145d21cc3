/*
 * Function: j_?db_buy_emblem@CMainThread@@QEAAEKHKKKPEAN0PEAEPEAD1@Z
 * Address: 0x14000FC4A
 */

char __fastcall CMainThread::db_buy_emblem(CMainThread *this, unsigned int dwGuildSerial, int nEmblemDalant, unsigned int dwEmblemBack, unsigned int dwEmblemMark, unsigned int dwSuggestorSerial, long double *dTotalDalant, long double *dTotalGold, char *byDate, char *pwszName, char *pbyProcRet)
{
  return CMainThread::db_buy_emblem(
           this,
           dwGuildSerial,
           nEmblemDalant,
           dwEmblemBack,
           dwEmblemMark,
           dwSuggestorSerial,
           dTotalDalant,
           dTotalGold,
           byDate,
           pwszName,
           pbyProcRet);
}
