/*
 * Function: ??$unchecked_uninitialized_copy@PEAV?$_Iterator@$0A@@?$list@U?$pair@QEAUScheduleMSG@@K@std@@V?$allocator@U?$pair@QEAUSchedule<PERSON><PERSON>@@K@std@@@2@@std@@PEAV123@V?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@QEAUScheduleMSG@@K@std@@V?$allocator@U?$pair@QEAUScheduleMSG@@K@std@@@2@@std@@@3@@stdext@@YAPEAV?$_Iterator@$0A@@?$list@U?$pair@QEAUScheduleMSG@@K@std@@V?$allocator@U?$pair@QEAUScheduleMSG@@K@std@@@2@@std@@PEAV123@00AEAV?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@QEAUSchedule<PERSON>G@@K@std@@V?$allocator@U?$pair@QEAUScheduleMSG@@K@std@@@2@@std@@@3@@Z
 * Address: 0x14042B330
 */

std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *__fastcall stdext::unchecked_uninitialized_copy<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0> *,std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0> *,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>(std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *_First, std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *_Last, std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *_Dest, std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> > *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  std::_Nonscalar_ptr_iterator_tag v9; // [sp+31h] [bp-17h]@4
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *__formal; // [sp+50h] [bp+8h]@1
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *_Lasta; // [sp+58h] [bp+10h]@1
  std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *_Desta; // [sp+60h] [bp+18h]@1
  std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> > *_Ala; // [sp+68h] [bp+20h]@1

  _Ala = _Al;
  _Desta = _Dest;
  _Lasta = _Last;
  __formal = _First;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  v9 = std::_Ptr_cat<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0> *,std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0> *>(
         &__formal,
         &_Desta);
  return std::_Uninit_copy<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0> *,std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0> *,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>(
           __formal,
           _Lasta,
           _Desta,
           _Ala,
           v9,
           v8);
}
