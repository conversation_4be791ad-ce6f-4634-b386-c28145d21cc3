/*
 * Function: ?On_HS_SCENE_KEEPER_CHAOS_TIME@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x14027C770
 */

void __fastcall CHolyStoneSystem::On_HS_SCENE_KEEPER_CHAOS_TIME(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CHolyStoneSystem *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CHolyStoneSystem::CreateHolyKeeper(v4, 1);
  CHolyKeeper::Set<PERSON><PERSON><PERSON><PERSON><PERSON>(g_Keeper);
  CHolyKeeper::SetDamageAbleState(g_<PERSON>, 0);
  CHolyStoneSystem::SendMsg_HolyKeeperStateChaos(v4);
}
