/*
 * Function: ?IsDone@CUnmannedTraderSchedule@@QEAA_NXZ
 * Address: 0x140394730
 */

bool __fastcall CUnmannedTraderSchedule::IsDone(CUnmannedTraderSchedule *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  __int64 _Time; // [sp+28h] [bp-20h]@4
  CUnmannedTraderSchedule *v7; // [sp+50h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  time_19(&_Time);
  return v7->m_tEndTime <= _Time;
}
