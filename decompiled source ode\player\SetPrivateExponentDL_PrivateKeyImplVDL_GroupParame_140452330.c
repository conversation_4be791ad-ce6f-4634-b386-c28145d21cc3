/*
 * Function: ?SetPrivateExponent@?$DL_PrivateKeyImpl@V?$DL_GroupParameters_EC@VECP@CryptoPP@@@CryptoPP@@@CryptoPP@@UEAAXAEBVInteger@2@@Z
 * Address: 0x140452330
 */

void __fastcall CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>>::SetPrivateExponent(CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> > *this, CryptoPP::Integer *x)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> > *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CryptoPP::Integer::operator=(v5->gap1E0);
}
