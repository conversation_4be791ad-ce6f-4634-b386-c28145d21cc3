/*
 * Function: j_??Y?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAAAEAV01@_J@Z
 * Address: 0x140012E13
 */

std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *__fastcall std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator+=(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, __int64 _Off)
{
  return std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator+=(this, _Off);
}
