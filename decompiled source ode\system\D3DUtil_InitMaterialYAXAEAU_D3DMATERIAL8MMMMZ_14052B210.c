/*
 * Function: ?D3DUtil_InitMaterial@@YAXAEAU_D3DMATERIAL8@@MMMM@Z
 * Address: 0x14052B210
 */

void __fastcall D3DUtil_InitMaterial(struct _D3DMATERIAL8 *a1, float a2, float a3, float a4, float a5)
{
  struct _D3DMATERIAL8 *v5; // rbx@1

  v5 = a1;
  memset_0(a1, 0, 0x44ui64);
  v5->Ambient.r = a2;
  v5->Diffuse.r = a2;
  v5->Ambient.g = a3;
  v5->Diffuse.g = a3;
  v5->Ambient.b = a4;
  v5->Diffuse.b = a4;
  v5->Ambient.a = a5;
  v5->Diffuse.a = a5;
}
