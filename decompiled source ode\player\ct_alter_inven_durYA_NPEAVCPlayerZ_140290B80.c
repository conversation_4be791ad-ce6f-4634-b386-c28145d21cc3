/*
 * Function: ?ct_alter_inven_dur@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140290B80
 */

bool __fastcall ct_alter_inven_dur(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  unsigned __int64 v4; // rax@7
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = pOne;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      v4 = _strtoui64(s_pwszDstCheat[0], 0i64, 10);
      result = CPlayer::dev_half_inven_amount(v6, v4);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
