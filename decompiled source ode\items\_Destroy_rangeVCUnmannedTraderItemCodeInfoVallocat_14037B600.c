/*
 * Function: ??$_Destroy_range@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@YAXPEAVCUnmannedTraderItemCodeInfo@@0AEAV?$allocator@VCUnmannedTraderItemCodeInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@@Z
 * Address: 0x14037B600
 */

void __fastcall std::_Destroy_range<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(CUnmannedTraderItemCodeInfo *_First, CUnmannedTraderItemCodeInfo *_Last, std::allocator<CUnmannedTraderItemCodeInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CUnmannedTraderItemCodeInfo *_Ptr; // [sp+30h] [bp+8h]@1
  CUnmannedTraderItemCodeInfo *v8; // [sp+38h] [bp+10h]@1
  std::allocator<CUnmannedTraderItemCodeInfo> *v9; // [sp+40h] [bp+18h]@1

  v9 = _Al;
  v8 = _Last;
  _Ptr = _First;
  v4 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  while ( _Ptr != v8 )
  {
    std::allocator<CUnmannedTraderItemCodeInfo>::destroy(v9, _Ptr);
    ++_Ptr;
  }
}
