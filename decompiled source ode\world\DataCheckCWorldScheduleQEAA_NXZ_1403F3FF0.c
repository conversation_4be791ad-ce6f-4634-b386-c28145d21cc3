/*
 * Function: ?DataCheck@CWorldSchedule@@QEAA_NXZ
 * Address: 0x1403F3FF0
 */

char __fastcall CWorldSchedule::DataCheck(CWorldSchedule *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  int j; // [sp+24h] [bp-14h]@4
  _base_fld *v7; // [sp+28h] [bp-10h]@6
  CWorldSchedule *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = -1;
  for ( j = 0; j < 2 * v8->m_nMaxSchNum; ++j )
  {
    v7 = CRecordData::GetRecord(&v8->m_tblSch, j % v8->m_nMaxSchNum);
    if ( *(_DWORD *)&v7[1].m_strCode[4] )
    {
      if ( *(_DWORD *)&v7[1].m_strCode[4] == 1 )
      {
        if ( *(_DWORD *)&v7[1].m_strCode[8] && *(_DWORD *)&v7[1].m_strCode[8] != 1 )
        {
          MyMessageBox("CWorldSchedule Error", "%d rec: event_code_holy : pFld->m_nEventInfo1", (unsigned int)j);
          return 0;
        }
        if ( *(_DWORD *)&v7[1].m_strCode[12] < 1 || *(_DWORD *)&v7[1].m_strCode[12] > 3 )
        {
          MyMessageBox("CWorldSchedule Error", "%d rec: event_code_holy : pFld->m_nEventInfo2", (unsigned int)j);
          return 0;
        }
      }
    }
    else
    {
      if ( v5 != -1 )
      {
        if ( !v5 && *(_DWORD *)&v7[1].m_strCode[12] != 1 )
        {
          MyMessageBox("CWorldSchedule Error", "%d rec: Anchor Balance Check Fail", (unsigned int)j);
          return 0;
        }
        if ( v5 == 1 && *(_DWORD *)&v7[1].m_strCode[12] )
        {
          MyMessageBox("CWorldSchedule Error", "%d rec: Anchor Balance Check Fail", (unsigned int)j);
          return 0;
        }
      }
      v5 = *(_DWORD *)&v7[1].m_strCode[12];
    }
  }
  return 1;
}
