/*
 * Function: j_?GetIDInfo@CUnmannedTraderGroupIDInfo@@QEAA_NAEAV?$vector@U?$pair@KK@std@@V?$allocator@U?$pair@KK@std@@@2@@std@@@Z
 * Address: 0x140001B40
 */

bool __fastcall CUnmannedTraderGroupIDInfo::GetIDInfo(CUnmannedTraderGroupIDInfo *this, std::vector<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *vecInfo)
{
  return CUnmannedTraderGroupIDInfo::GetIDInfo(this, vecInfo);
}
