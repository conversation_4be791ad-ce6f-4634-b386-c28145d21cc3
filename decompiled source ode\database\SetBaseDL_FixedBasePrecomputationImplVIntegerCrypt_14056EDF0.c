/*
 * Function: ?SetBase@?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@CryptoPP@@UEAAXAEBV?$DL_GroupPrecomputation@VInteger@CryptoPP@@@2@AEBVInteger@2@@Z
 * Address: 0x14056EDF0
 */

int __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::SetBase(__int64 a1, int (__fastcall ***a2)(_QWORD), __int64 a3)
{
  __int64 v3; // rax@2
  CryptoPP::Integer *v4; // rax@7
  __int64 v5; // rax@8
  __int64 v6; // rax@9
  CryptoPP::Integer v8; // [sp+28h] [bp-60h]@2
  int v9; // [sp+50h] [bp-38h]@1
  __int64 v10; // [sp+58h] [bp-30h]@1
  __int64 v11; // [sp+60h] [bp-28h]@2
  __int64 v12; // [sp+68h] [bp-20h]@2
  __int64 v13; // [sp+70h] [bp-18h]@2
  __int64 v14; // [sp+78h] [bp-10h]@8
  __int64 v15; // [sp+90h] [bp+8h]@1
  int (__fastcall ***v16)(_QWORD); // [sp+98h] [bp+10h]@1
  __int64 v17; // [sp+A0h] [bp+18h]@1

  v17 = a3;
  v16 = a2;
  v15 = a1;
  v10 = -2i64;
  v9 = 0;
  if ( (unsigned __int8)(**a2)(a2) )
  {
    LODWORD(v3) = ((int (__fastcall *)(int (__fastcall ***)(_QWORD), CryptoPP::Integer *, __int64))(*v16)[1])(
                    v16,
                    &v8,
                    v17);
    v11 = v3;
    v12 = v3;
    v9 |= 1u;
    v13 = v3;
  }
  else
  {
    v13 = v17;
  }
  CryptoPP::Integer::operator=(v15 + 8);
  if ( v9 & 1 )
  {
    v9 &= 0xFFFFFFFE;
    CryptoPP::Integer::~Integer(&v8);
  }
  if ( std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::empty(v15 + 96)
    || (LODWORD(v4) = std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::operator[](v15 + 96, 0i64),
        !CryptoPP::operator==((CryptoPP::Integer *)(v15 + 8), v4)) )
  {
    std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::resize(v15 + 96, 1i64);
    v14 = v15 + 8;
    LODWORD(v5) = std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::operator[](v15 + 96, 0i64);
    CryptoPP::Integer::operator=(v5);
  }
  v6 = (unsigned __int8)(**v16)(v16);
  if ( (_BYTE)v6 )
    LODWORD(v6) = CryptoPP::Integer::operator=(v15 + 8);
  return v6;
}
