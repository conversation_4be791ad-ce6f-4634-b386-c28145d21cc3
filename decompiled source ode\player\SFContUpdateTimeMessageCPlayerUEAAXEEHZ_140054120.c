/*
 * Function: ?SFContUpdateTimeMessage@CPlayer@@UEAAXEEH@Z
 * Address: 0x140054120
 */

void __fastcall CPlayer::SFContUpdateTimeMessage(CPlayer *this, char byContCode, char byListIndex, int nLeftTime)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  bool bUpdate; // [sp+20h] [bp-18h]@6
  CPlayer *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v8->m_pUserDB )
  {
    if ( nLeftTime > 0 )
    {
      bUpdate = 1;
      CUserDB::Update_SFContUpdate(v8->m_pUserDB, byContCode, byListIndex, nLeftTime, 1);
    }
  }
}
