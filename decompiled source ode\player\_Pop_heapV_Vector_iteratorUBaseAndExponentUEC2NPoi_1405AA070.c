/*
 * Function: ??$_Pop_heap@V?$_Vector_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@_JU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@YAXV?$_Vector_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@0@00U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@PEA_J@Z
 * Address: 0x1405AA070
 */

void __fastcall std::_Pop_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>(__int64 a1, __int64 a2, __int64 a3, const struct CryptoPP::EC2NPoint *a4)
{
  _BYTE *v4; // rax@1
  _BYTE *v5; // rax@1
  __int64 v6; // rax@1
  char v7; // [sp+20h] [bp-C8h]@1
  char *v8; // [sp+80h] [bp-68h]@1
  char v9; // [sp+88h] [bp-60h]@1
  char *v10; // [sp+A0h] [bp-48h]@1
  __int64 v11; // [sp+A8h] [bp-40h]@1
  _BYTE *v12; // [sp+B0h] [bp-38h]@1
  CryptoPP::EC2NPoint *v13; // [sp+B8h] [bp-30h]@1
  const struct CryptoPP::EC2NPoint *v14; // [sp+C0h] [bp-28h]@1
  __int64 v15; // [sp+C8h] [bp-20h]@1
  __int64 v16; // [sp+D0h] [bp-18h]@1
  const struct CryptoPP::EC2NPoint *v17; // [sp+108h] [bp+20h]@1

  v17 = a4;
  v11 = -2i64;
  LODWORD(v4) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator*();
  v12 = v4;
  LODWORD(v5) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator*();
  CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::operator=(v5, v12);
  v8 = &v7;
  v10 = &v9;
  v13 = CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>(
          (CryptoPP::EC2NPoint *)&v7,
          v17);
  v14 = v13;
  LODWORD(v6) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator-();
  v15 = v6;
  v16 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)v10);
  std::_Adjust_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>(
    v16,
    0i64,
    v15,
    v14);
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
  CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>((__int64)v17);
}
