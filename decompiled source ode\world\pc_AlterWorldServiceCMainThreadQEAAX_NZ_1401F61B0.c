/*
 * Function: ?pc_AlterWorldService@CMainThread@@QEAAX_N@Z
 * Address: 0x1401F61B0
 */

void __fastcall CMainThread::pc_AlterWorldService(CMainThread *this, bool bSerivce)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMainThread *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CMainThread::SerivceForceSet(v5, bSerivce);
}
