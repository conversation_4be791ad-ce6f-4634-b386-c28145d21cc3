/*
 * Function: ?ct_release_punishment@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140296EC0
 */

char __fastcall ct_release_punishment(CPlayer *pOne)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  int j; // [sp+0h] [bp-18h]@1
  CPlayer *v5; // [sp+20h] [bp+8h]@1

  v5 = pOne;
  v1 = &j;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  if ( v5 && v5->m_bOper )
  {
    for ( j = 0; j < 3; ++j )
      v5->m_pUserDB->m_AvatorData.dbAvator.m_dwPunishment[j] = -1;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
