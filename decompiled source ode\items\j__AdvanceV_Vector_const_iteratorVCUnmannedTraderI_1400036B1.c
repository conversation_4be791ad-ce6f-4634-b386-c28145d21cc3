/*
 * Function: j_??$_Advance@V?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@_J@std@@YAXAEAV?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@0@_JUrandom_access_iterator_tag@0@@Z
 * Address: 0x1400036B1
 */

void __fastcall std::_Advance<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,__int64>(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_Where, __int64 _Off, std::random_access_iterator_tag __formal)
{
  std::_Advance<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,__int64>(
    _Where,
    _Off,
    __formal);
}
