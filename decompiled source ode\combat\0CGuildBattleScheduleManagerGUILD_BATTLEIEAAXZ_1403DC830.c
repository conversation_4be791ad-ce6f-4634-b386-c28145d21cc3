/*
 * Function: ??0CGuildBattleScheduleManager@GUILD_BATTLE@@IEAA@XZ
 * Address: 0x1403DC830
 */

void __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::CGuildBattleScheduleManager(GUILD_BATTLE::CGuildBattleScheduleManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CGuildBattleScheduleManager *v4; // [sp+40h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_bLoad = 0;
  v4->m_pkOldDayTime = 0i64;
  v4->m_pkTimer = 0i64;
  `eh vector constructor iterator'(
    v4->m_kSchdule,
    0x18ui64,
    2,
    (void (__cdecl *)(void *))GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::CGuildBattleReservedScheduleMapGroup,
    (void (__cdecl *)(void *))GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::~CGuildBattleReservedScheduleMapGroup);
  v4->m_pkTodaySchedule = v4->m_kSchdule;
  v4->m_pkTomorrowSchedule = &v4->m_kSchdule[1];
}
