/*
 * Function: ?ct_request_npc_quest@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140296790
 */

bool __fastcall ct_request_npc_quest(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  __int64 v4; // [sp+0h] [bp-98h]@1
  char Dest; // [sp+30h] [bp-68h]@9
  unsigned __int64 v6; // [sp+80h] [bp-18h]@4
  CPlayer *v7; // [sp+A0h] [bp+8h]@1

  v7 = pOne;
  v1 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( v7 && v7->m_bOper )
  {
    if ( s_nWordCount == 1 )
    {
      strcpy_0(&Dest, s_pwszDstCheat[0]);
      result = CPlayer::Emb_CreateQuestEvent(v7, quest_happen_type_npc, &Dest);
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
