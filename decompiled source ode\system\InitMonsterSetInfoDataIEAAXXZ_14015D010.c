/*
 * Function: ?Init@MonsterSetInfoData@@IEAAXXZ
 * Address: 0x14015D010
 */

void __fastcall MonsterSetInfoData::Init(MonsterSetInfoData *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  int k; // [sp+24h] [bp-14h]@6
  void *v6; // [sp+28h] [bp-10h]@11
  MonsterSetInfoData *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7->m_bLoad = 0;
  for ( j = 0; j < 4; ++j )
  {
    for ( k = 0; k < 7; ++k )
      v7->m_byLevel_ContSFTime[j][k] = k + 1;
  }
  v7->m_nMonsterLostTargetDistance = 285;
  v7->m_fMonsterForcePowerRate = FLOAT_40_0;
  if ( v7->m_strRotMonBlk_Ar )
  {
    v6 = v7->m_strRotMonBlk_Ar;
    operator delete[](v6);
    v7->m_strRotMonBlk_Ar = 0i64;
  }
  v7->m_nMonBlkCount = 0;
}
