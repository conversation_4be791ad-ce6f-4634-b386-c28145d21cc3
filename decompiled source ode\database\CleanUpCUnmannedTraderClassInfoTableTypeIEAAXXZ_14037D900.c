/*
 * Function: ?CleanUp@CUnmannedTraderClassInfoTableType@@IEAAXXZ
 * Address: 0x14037D900
 */

void __fastcall CUnmannedTraderClassInfoTableType::CleanUp(CUnmannedTraderClassInfoTableType *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v3; // rax@6
  __int64 v4; // [sp+0h] [bp-58h]@1
  int j; // [sp+20h] [bp-38h]@5
  CUnmannedTraderSubClassInfo *v6; // [sp+28h] [bp-30h]@7
  CUnmannedTraderSubClassInfo *v7; // [sp+30h] [bp-28h]@7
  unsigned __int64 v8; // [sp+38h] [bp-20h]@6
  void *v9; // [sp+40h] [bp-18h]@8
  CUnmannedTraderClassInfoTableType *v10; // [sp+60h] [bp+8h]@1

  v10 = this;
  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::empty(&v10->m_vecSubClass) )
  {
    for ( j = 0; ; ++j )
    {
      v8 = j;
      v3 = std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::size(&v10->m_vecSubClass);
      if ( v8 >= v3 )
        break;
      v7 = *std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator[](
              &v10->m_vecSubClass,
              j);
      v6 = v7;
      if ( v7 )
        v9 = CUnmannedTraderSubClassInfo::`scalar deleting destructor'(v6, 1u);
      else
        v9 = 0i64;
    }
    std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::clear(&v10->m_vecSubClass);
  }
}
