/*
 * Function: ?dev_up_all_pt@CPlayer@@QEAA_NH@Z
 * Address: 0x1400B9C50
 */

char __fastcall CPlayer::dev_up_all_pt(CPlayer *this, int nLv)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  float v5; // xmm0_4@7
  float v6; // xmm0_4@7
  float v7; // xmm0_4@10
  float v8; // xmm0_4@43
  float v9; // xmm0_4@45
  float v10; // xmm0_4@47
  __int64 v11; // [sp+0h] [bp-A8h]@1
  unsigned int dwNewData; // [sp+20h] [bp-88h]@7
  unsigned int dwStatIndex; // [sp+24h] [bp-84h]@7
  int v14; // [sp+38h] [bp-70h]@10
  char v15; // [sp+3Ch] [bp-6Ch]@10
  _base_fld *v16; // [sp+68h] [bp-40h]@10
  int n; // [sp+70h] [bp-38h]@10
  _base_fld *v18; // [sp+78h] [bp-30h]@24
  unsigned int dwNewCum; // [sp+80h] [bp-28h]@27
  float v20; // [sp+84h] [bp-24h]@28
  float v21; // [sp+88h] [bp-20h]@7
  float v22; // [sp+8Ch] [bp-1Ch]@10
  float v23; // [sp+90h] [bp-18h]@47
  CPlayer *v24; // [sp+B0h] [bp+8h]@1
  int v25; // [sp+B8h] [bp+10h]@1

  v25 = nLv;
  v24 = this;
  v2 = &v11;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( nLv > 0 && nLv <= 99 )
  {
    pow(1000.0, 2);
    v21 = FLOAT_1000_0;
    pow((float)v25, 2);
    v5 = v21 + (float)((float)(4.0 * (float)v25) * 1000.0);
    sqrt(v5);
    v6 = (float)(v5 + -1000.0) / 2.0;
    pow(v6, 2);
    dwNewData = (signed int)ffloor(v6);
    for ( dwStatIndex = 0; (signed int)dwStatIndex < 2; ++dwStatIndex )
    {
      CPlayer::Emb_UpdateStat(v24, dwStatIndex, dwNewData, 0);
      _MASTERY_PARAM::UpdateCumPerMast(&v24->m_pmMst, 0, dwStatIndex, dwNewData);
      CPlayer::SendMsg_StatInform(v24, dwStatIndex, dwNewData, 0);
    }
    CPlayer::Emb_UpdateStat(v24, 2u, dwNewData, 0);
    _MASTERY_PARAM::UpdateCumPerMast(&v24->m_pmMst, 1, 0, dwNewData);
    CPlayer::SendMsg_StatInform(v24, 2, dwNewData, 0);
    pow(100.0, 2);
    v22 = FLOAT_100_0;
    pow((float)v25, 2);
    v7 = v22 + (float)((float)(4.0 * (float)v25) * 100.0);
    sqrt(v7);
    pow((float)(v7 + -100.0) / 2.0, 2);
    dwNewData = (signed int)ffloor((float)(v7 + -100.0) / 2.0);
    CPlayer::Emb_UpdateStat(v24, 3u, dwNewData, 0);
    _MASTERY_PARAM::UpdateCumPerMast(&v24->m_pmMst, 2, 0, dwNewData);
    CPlayer::SendMsg_StatInform(v24, 3, dwNewData, 0);
    pow((float)v25, 2);
    pow((float)v25, 2);
    dwNewData = (signed int)ffloor((float)(CalcRoundUp((float)v25 / 10.0) - 1) + 0.0099999998);
    v14 = 0;
    memset(&v15, 0, 0x1Cui64);
    v16 = 0i64;
    for ( n = 0; n < 48; ++n )
    {
      v16 = CRecordData::GetRecord(_MASTERY_PARAM::s_pSkillData, n);
      if ( v16 && *(_DWORD *)&v16[1].m_strCode[4] < 8 && *(_DWORD *)&v16[1].m_strCode[4] >= 0 )
      {
        if ( strncmp(v16->m_strCode, "FF", 2ui64) )
          ++*(&v14 + *(_DWORD *)&v16[1].m_strCode[4]);
      }
    }
    for ( n = 0; n < 8; ++n )
      v24->m_pmMst.m_dwSkillMasteryCum[n] = 0;
    for ( dwStatIndex = 0; (signed int)dwStatIndex < 48; ++dwStatIndex )
    {
      v18 = CRecordData::GetRecord(_MASTERY_PARAM::s_pSkillData, dwStatIndex);
      if ( v18 && strncmp(v18->m_strCode, "FF", 2ui64) )
      {
        dwNewCum = dwNewData;
        if ( *(&v14 + *(_DWORD *)&v18[1].m_strCode[4]) > 0 )
        {
          v20 = (float)(signed int)dwNewCum / (float)*(&v14 + *(_DWORD *)&v18[1].m_strCode[4]);
          dwNewCum = CalcRoundUp(v20);
        }
        CPlayer::Emb_UpdateStat(v24, dwStatIndex + 4, dwNewCum, 0);
        _MASTERY_PARAM::UpdateCumPerMast(&v24->m_pmMst, 3, dwStatIndex, dwNewCum);
        CPlayer::SendMsg_StatInform(v24, dwStatIndex + 4, dwNewCum, 0);
      }
    }
    pow((float)v25, 2);
    pow((float)v25, 2);
    dwNewData = (signed int)ffloor((float)(CalcRoundUp((float)v25 / 14.0) - 1) + 0.0099999998);
    for ( dwStatIndex = 0; (signed int)dwStatIndex < 24; ++dwStatIndex )
    {
      CPlayer::Emb_UpdateStat(v24, dwStatIndex + 52, dwNewData, 0);
      _MASTERY_PARAM::UpdateCumPerMast(&v24->m_pmMst, 4, dwStatIndex, dwNewData);
      CPlayer::SendMsg_StatInform(v24, dwStatIndex + 52, dwNewData, 0);
    }
    for ( dwStatIndex = 0; (signed int)dwStatIndex < 3; ++dwStatIndex )
    {
      if ( dwStatIndex )
      {
        if ( dwStatIndex == 1 )
        {
          pow((float)v25, 2);
          dwNewData = (signed int)ffloor((float)((float)((float)((float)v25 - 1.0) / 3.0) * 1.1) + 0.89999998);
        }
        else if ( dwStatIndex == 2 )
        {
          pow((float)v25, 2);
          dwNewData = (signed int)ffloor((float)((float)((float)((float)v25 - 1.0) / 3.0) * 10.0) + 0.89999998);
        }
      }
      else
      {
        pow((float)v25, 2);
        dwNewData = (signed int)ffloor((float)((float)((float)((float)v25 - 1.0) / 3.0) * 1.1) + 0.89999998);
      }
      CPlayer::Emb_UpdateStat(v24, dwStatIndex + 76, dwNewData, 0);
      _MASTERY_PARAM::UpdateCumPerMast(&v24->m_pmMst, 5, dwStatIndex, dwNewData);
      CPlayer::SendMsg_StatInform(v24, dwStatIndex + 76, dwNewData, 0);
    }
    if ( CPlayerDB::GetRaceCode(&v24->m_Param) )
    {
      if ( CPlayerDB::GetRaceCode(&v24->m_Param) == 1 )
      {
        v9 = (float)(v25 - 1);
        pow(v9, 2);
        dwNewData = (signed int)ffloor(v9 * 15000.0);
      }
      else if ( CPlayerDB::GetRaceCode(&v24->m_Param) == 2 )
      {
        pow(1000.0, 2);
        v23 = FLOAT_1000_0;
        pow((float)v25, 2);
        v10 = v23 + (float)((float)(4.0 * (float)v25) * 1000.0);
        sqrt(v10);
        pow((float)(v10 + -1000.0) / 2.0, 2);
        dwNewData = (signed int)ffloor((float)(v10 + -1000.0) / 2.0);
      }
    }
    else
    {
      v8 = (float)(v25 - 1);
      pow(v8, 2);
      dwNewData = (signed int)ffloor(v8 * 15000.0);
    }
    CPlayer::Emb_UpdateStat(v24, 0x4Fu, dwNewData, 0);
    _MASTERY_PARAM::UpdateCumPerMast(&v24->m_pmMst, 6, 0, dwNewData);
    CPlayer::SendMsg_StatInform(v24, 79, dwNewData, 0);
    CPlayer::ReCalcMaxHFSP(v24, 1, 0);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
