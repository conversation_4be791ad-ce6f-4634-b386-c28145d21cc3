/*
 * Function: ?Load_LimitedSale_Event_INI@CashItemRemoteStore@@QEAAXPEAU_cash_event_ini@@PEAU_FILETIME@@PEAD@Z
 * Address: 0x1402FD4A0
 */

void __fastcall CashItemRemoteStore::Load_LimitedSale_Event_INI(CashItemRemoteStore *this, _cash_event_ini *pIni, _FILETIME *pft, char *pEventType)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-B8h]@1
  UINT v7; // [sp+30h] [bp-88h]@8
  char Dst; // [sp+48h] [bp-70h]@8
  char _Dest[20]; // [sp+78h] [bp-40h]@8
  unsigned int j; // [sp+94h] [bp-24h]@9
  unsigned __int64 v11; // [sp+A0h] [bp-18h]@4
  CashItemRemoteStore *v12; // [sp+C0h] [bp+8h]@1
  _cash_event_ini *pInia; // [sp+C8h] [bp+10h]@1
  const char *Str1; // [sp+D8h] [bp+20h]@1

  Str1 = pEventType;
  pInia = pIni;
  v12 = this;
  v4 = &v6;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( pIni && pft && strcmp_0(pEventType, "NULL") )
  {
    v7 = 0;
    memset_0(&Dst, 0, 0x14ui64);
    memset_0(_Dest, 0, 0x14ui64);
    if ( !strcmp_0(Str1, "Limit Sale") )
    {
      pInia->m_byLimited_sale_num = GetPrivateProfileIntA(
                                      Str1,
                                      "LimItem",
                                      0,
                                      "./Initialize/cash_discount_limitsale.ini");
      for ( j = 0; (signed int)j < pInia->m_byLimited_sale_num; ++j )
      {
        sprintf_s<20>((char (*)[20])&Dst, "Limcode%d", j);
        sprintf_s<20>((char (*)[20])_Dest, "Limcount%d", j);
        GetPrivateProfileStringA(
          Str1,
          &Dst,
          &byte_1407D1D3F,
          pInia->m_Limited_sale[j].m_szLimcode,
          0x40u,
          "./Initialize/cash_discount_limitsale.ini");
        v7 = GetPrivateProfileIntA(Str1, _Dest, 0, "./Initialize/cash_discount_limitsale.ini");
        if ( (v7 & 0x80000000) != 0 || (signed int)v7 >= 2001 )
          pInia->m_bUseCashEvent = 0;
        pInia->m_Limited_sale[j].m_wLimcount = v7;
      }
      v7 = GetPrivateProfileIntA(Str1, "Discout", 0, "./Initialize/cash_discount_limitsale.ini");
      if ( (v7 & 0x80000000) != 0 || (signed int)v7 >= 100 )
      {
        pInia->m_bUseCashEvent = 0;
        return;
      }
      pInia->m_byLimDiscout = v7;
    }
    CashItemRemoteStore::Set_LimitedSale_Event_Ini(v12, pInia);
  }
}
