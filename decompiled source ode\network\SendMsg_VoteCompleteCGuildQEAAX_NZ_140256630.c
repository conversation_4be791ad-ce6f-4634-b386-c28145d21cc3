/*
 * Function: ?SendMsg_VoteComplete@CGuild@@QEAAX_N@Z
 * Address: 0x140256630
 */

void __fastcall CGuild::SendMsg_VoteComplete(CGuild *this, bool bPass)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  char v6; // [sp+38h] [bp-40h]@4
  char v7; // [sp+39h] [bp-3Fh]@4
  bool v8; // [sp+3Ah] [bp-3Eh]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v10; // [sp+55h] [bp-23h]@4
  int j; // [sp+64h] [bp-14h]@4
  _guild_member_info *v12; // [sp+68h] [bp-10h]@7
  CGuild *v13; // [sp+80h] [bp+8h]@1

  v13 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  *(_DWORD *)szMsg = v13->m_SuggestedMatter.dwMatterVoteSynKey;
  v6 = v13->m_SuggestedMatter.byVoteState[0];
  v7 = v13->m_SuggestedMatter.byVoteState[1];
  v8 = bPass;
  pbyType = 27;
  v10 = 29;
  for ( j = 0; j < 50; ++j )
  {
    v12 = &v13->m_MemberData[j];
    if ( _guild_member_info::IsFill(v12) )
    {
      if ( v12->pPlayer )
        CNetProcess::LoadSendMsg(unk_1414F2088, v12->pPlayer->m_ObjID.m_wIndex, &pbyType, szMsg, 7u);
    }
  }
}
