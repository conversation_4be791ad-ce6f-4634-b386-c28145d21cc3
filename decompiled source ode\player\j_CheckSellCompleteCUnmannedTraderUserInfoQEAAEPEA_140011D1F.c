/*
 * Function: j_?CheckSellComplete@CUnmannedTraderUserInfo@@QEAAEPEAVCPlayer@@0KKPEAVCLogFile@@@Z
 * Address: 0x140011D1F
 */

char __fastcall CUnmannedTraderUserInfo::CheckSellComplete(CUnmannedTraderUserInfo *this, CPlayer *pkSellPlayer, CPlayer *pkBuyer, unsigned int dwRegistSerial, unsigned int dwRealPrice, CLogFile *pkLogger)
{
  return CUnmannedTraderUserInfo::CheckSellComplete(this, pkSellPlayer, pkBuyer, dwRegistSerial, dwRealPrice, pkLogger);
}
