/*
 * Function: ?Send_Trans@CChiNetworkEX@@QEAAXPEAVCPlayer@@K@Z
 * Address: 0x1404100B0
 */

void __fastcall CChiNetworkEX::Send_Trans(CChiNetworkEX *this, CPlayer *pOne, unsigned int dwRet)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  _apex_id *v5; // rax@4
  char *v6; // rax@4
  CAsyncLogger *v7; // rax@4
  __int64 v8; // [sp+0h] [bp-68h]@1
  unsigned __int16 nLen; // [sp+20h] [bp-48h]@4
  _apex_send_trans v10; // [sp+34h] [bp-34h]@4
  _apex_id v11; // [sp+44h] [bp-24h]@4
  unsigned __int16 v12[2]; // [sp+48h] [bp-20h]@4
  CUserDB *v13; // [sp+50h] [bp-18h]@4
  CUserDB *v14; // [sp+58h] [bp-10h]@4
  CChiNetworkEX *v15; // [sp+70h] [bp+8h]@1
  CPlayer *v16; // [sp+78h] [bp+10h]@1

  v16 = pOne;
  v15 = this;
  v3 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10.m_nRet = dwRet;
  *(_DWORD *)v12 = _apex_send_trans::size(&v10);
  v13 = v16->m_pUserDB;
  _apex_id::_apex_id(&v11, 82);
  v6 = _apex_id::operator&(v5);
  nLen = v12[0];
  CChiNetworkEX::Send(v15, v6, v13->m_dwAccountSerial, (char *)&v10, v12[0]);
  v14 = v16->m_pUserDB;
  v7 = CAsyncLogger::Instance();
  CAsyncLogger::FormatLog(v7, 12, "Send_Trans - %d", v14->m_dwAccountSerial);
}
