/*
 * Function: j_??$_Move_backward_opt@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@Urandom_access_iterator_tag@std@@U_Undefined_move_tag@4@@std@@YAPEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@00Urandom_access_iterator_tag@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140012BB1
 */

GUILD_BATTLE::CGuildBattleRewardItem *__fastcall std::_Move_backward_opt<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *,std::random_access_iterator_tag,std::_Undefined_move_tag>(GUILD_BATTLE::CGuildBattleRewardItem *_First, GUILD_BATTLE::CGuildBattleRewardItem *_Last, GUILD_BATTLE::CGuildBattleRewardItem *_Dest, std::random_access_iterator_tag _First_dest_cat, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Move_backward_opt<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *,std::random_access_iterator_tag,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _First_dest_cat,
           __formal,
           a6);
}
