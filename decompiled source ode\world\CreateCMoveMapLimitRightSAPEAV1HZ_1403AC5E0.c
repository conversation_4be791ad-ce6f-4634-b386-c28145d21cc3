/*
 * Function: ?Create@CMoveMapLimitRight@@SAPEAV1@H@Z
 * Address: 0x1403AC5E0
 */

CMoveMapLimitRight *__fastcall CMoveMapLimitRight::Create(int iType)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CMoveMapLimitRight *v3; // rax@6
  CMoveMapLimitRight *result; // rax@8
  __int64 v5; // [sp+0h] [bp-58h]@1
  CMoveMapLimitRight *v6; // [sp+20h] [bp-38h]@8
  CMoveMapLimitRightPortal *v7; // [sp+28h] [bp-30h]@5
  __int64 v8; // [sp+30h] [bp-28h]@4
  int v9; // [sp+38h] [bp-20h]@4
  CMoveMapLimitRight *v10; // [sp+40h] [bp-18h]@6
  int iTypea; // [sp+60h] [bp+8h]@1

  iTypea = iType;
  v1 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = -2i64;
  v9 = iTypea;
  if ( iTypea )
  {
    result = 0i64;
  }
  else
  {
    v7 = (CMoveMapLimitRightPortal *)operator new(0x20ui64);
    if ( v7 )
    {
      CMoveMapLimitRightPortal::CMoveMapLimitRightPortal(v7, 0);
      v10 = v3;
    }
    else
    {
      v10 = 0i64;
    }
    v6 = v10;
    result = v10;
  }
  return result;
}
