/*
 * Function: ?SendMsg_PartyLeaveCompulsionResult@CPlayer@@QEAAXPEAVCPartyPlayer@@@Z
 * Address: 0x1400DD240
 */

void __fastcall CPlayer::SendMsg_PartyLeaveCompulsionResult(CPlayer *this, CPartyPlayer *pLeaver)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@5
  char pbyType; // [sp+54h] [bp-24h]@7
  char v7; // [sp+55h] [bp-23h]@7
  CPlayer *v8; // [sp+80h] [bp+8h]@1

  v8 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pLeaver )
    *(_DWORD *)szMsg = pLeaver->m_id.dwSerial;
  else
    *(_DWORD *)szMsg = -1;
  pbyType = 16;
  v7 = 12;
  CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, szMsg, 4u);
}
