/*
 * Function: ?_db_Load_Base@CMainThread@@AEAAEKPEAU_AVATOR_DATA@@@Z
 * Address: 0x1401A6620
 */

char __fastcall CMainThread::_db_Load_Base(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pCon)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-138h]@1
  char Dst; // [sp+30h] [bp-108h]@4
  char v8; // [sp+48h] [bp-F0h]@8
  char Source; // [sp+49h] [bp-EFh]@8
  char v10; // [sp+4Eh] [bp-EAh]@8
  char v11; // [sp+4Fh] [bp-E9h]@8
  unsigned int v12; // [sp+50h] [bp-E8h]@8
  unsigned int v13; // [sp+54h] [bp-E4h]@8
  unsigned int v14; // [sp+58h] [bp-E0h]@8
  unsigned int v15; // [sp+5Ch] [bp-DCh]@8
  __int16 v16[9]; // [sp+72h] [bp-C6h]@10
  int v17[8]; // [sp+84h] [bp-B4h]@10
  int v18[9]; // [sp+A4h] [bp-94h]@10
  __int64 v19[9]; // [sp+C8h] [bp-70h]@10
  char v20; // [sp+114h] [bp-24h]@4
  int j; // [sp+118h] [bp-20h]@8
  unsigned __int64 v22; // [sp+128h] [bp-10h]@4
  CMainThread *v23; // [sp+140h] [bp+8h]@1
  unsigned int dwCharacterSerial; // [sp+148h] [bp+10h]@1
  _AVATOR_DATA *Dest; // [sp+150h] [bp+18h]@1

  Dest = pCon;
  dwCharacterSerial = dwSerial;
  v23 = this;
  v3 = &v6;
  for ( i = 76i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v22 = (unsigned __int64)&v6 ^ _security_cookie;
  memset_0(&Dst, 0, 0xE0ui64);
  v20 = CRFWorldDatabase::Select_CharacterBaseInfo(
          v23->m_pWorldDB,
          dwCharacterSerial,
          (_worlddb_character_base_info *)&Dst);
  if ( v20 == 1 )
  {
    result = 24;
  }
  else if ( v20 == 2 )
  {
    result = 24;
  }
  else
  {
    strcpy_0(Dest->dbAvator.m_wszAvatorName, &Dst);
    Dest->dbAvator.m_dwRecordNum = dwCharacterSerial;
    Dest->dbAvator.m_byRaceSexCode = v8;
    strcpy_0(Dest->dbAvator.m_szClassCode, &Source);
    Dest->dbAvator.m_bySlotIndex = v10;
    Dest->dbAvator.m_byLevel = v11;
    Dest->dbAvator.m_dwDalant = v12;
    Dest->dbAvator.m_dwGold = v13;
    Dest->dbAvator.m_dwBaseShape = v14;
    Dest->dbAvator.m_dwLastConnTime = v15;
    for ( j = 0; j < 8; ++j )
    {
      _EQUIPKEY::LoadDBKey(&Dest->dbAvator.m_EquipKey[j], v16[j]);
      Dest->dbAvator.m_dwFixEquipLv[j] = v17[j];
      Dest->dbAvator.m_lnUID[j] = v19[j];
      Dest->dbAvator.m_dwET[j] = v18[j];
    }
    result = 0;
  }
  return result;
}
