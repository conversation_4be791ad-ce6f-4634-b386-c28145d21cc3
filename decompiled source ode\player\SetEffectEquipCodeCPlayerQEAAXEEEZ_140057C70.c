/*
 * Function: ?SetEffectEquipCode@CPlayer@@QEAAXEEE@Z
 * Address: 0x140057C70
 */

void __fastcall CPlayer::SetEffectEquipCode(CPlayer *this, char byStorageCode, char bySlotIndex, char byCode)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char *v6; // [sp+0h] [bp-18h]@1
  CPlayer *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v4 = (__int64 *)&v6;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v6 = 0i64;
  if ( byStorageCode == 1 )
    v6 = &v7->m_byEffectEquipCode[(unsigned __int8)bySlotIndex];
  else
    v6 = (char *)&(&v7->m_pRecordSet)[726] + (unsigned __int8)bySlotIndex + 18;
  *v6 = byCode;
}
