/*
 * Function: ?Update_ItemDelete@CUserDB@@QEAA_NEE_N@Z
 * Address: 0x140114850
 */

char __fastcall CUserDB::Update_ItemDelete(CUserDB *this, char storage, char slot, bool bUpdate)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-48h]@1
  int v8; // [sp+20h] [bp-28h]@5
  bool v9; // [sp+30h] [bp-18h]@6
  int v10; // [sp+34h] [bp-14h]@6
  CUserDB *v11; // [sp+50h] [bp+8h]@1
  char v12; // [sp+58h] [bp+10h]@1
  char v13; // [sp+60h] [bp+18h]@1

  v13 = slot;
  v12 = storage;
  v11 = this;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( IsStorageRange(storage, slot) )
  {
    v9 = 0;
    v10 = (unsigned __int8)v12;
    switch ( v12 )
    {
      case 0:
        v9 = _INVEN_DB_BASE::_LIST::Release((_INVEN_DB_BASE::_LIST *)&v11->m_AvatorData.dbInven + (unsigned __int8)v13);
        break;
      case 1:
        v9 = _REGED::Release((_REGED *)v11->m_AvatorData.dbAvator.m_wszAvatorName, v13);
        break;
      case 2:
        v9 = _EQUIP_DB_BASE::_EMBELLISH_LIST::Release((_EQUIP_DB_BASE::_EMBELLISH_LIST *)&v11->m_AvatorData.dbEquip + (unsigned __int8)v13);
        break;
      case 3:
        v9 = _FORCE_DB_BASE::_LIST::Release((_FORCE_DB_BASE::_LIST *)&v11->m_AvatorData.dbForce + (unsigned __int8)v13);
        break;
      case 4:
        v9 = _ANIMUS_DB_BASE::_LIST::Release((_ANIMUS_DB_BASE::_LIST *)&v11->m_AvatorData.dbAnimus + (unsigned __int8)v13);
        break;
      case 5:
        v9 = _TRUNK_DB_BASE::_LIST::Release(&v11->m_AvatorData.dbTrunk.m_List[(unsigned __int8)v13]);
        break;
      case 6:
        v9 = _PERSONALAMINE_INVEN_DB_BASE::_LIST::Release(&v11->m_AvatorData.dbPersonalAmineInven.m_List[(unsigned __int8)v13]);
        break;
      case 7:
        v9 = _TRUNK_DB_BASE::_LIST::Release(&v11->m_AvatorData.dbTrunk.m_ExtList[(unsigned __int8)v13]);
        break;
      default:
        break;
    }
    if ( v9 )
    {
      v11->m_bDataUpdate = 1;
      result = 1;
    }
    else
    {
      v8 = (unsigned __int8)v13;
      CLogFile::Write(
        &stru_1799C8E78,
        "%s:UpdateItemDelete(%s, Idx:%d)",
        v11->m_aszAvatorName,
        *(_QWORD *)&s_szStorage[8 * (unsigned __int8)v12]);
      result = 0;
    }
  }
  else
  {
    v8 = (unsigned __int8)v13;
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : UpdateItemDelete(CODE) : scode : %d, icode : %d  ",
      v11->m_aszAvatorName,
      (unsigned __int8)v12);
    result = 0;
  }
  return result;
}
