/*
 * Function: ?Init@CItemStore@@QEAA_NHPEAVCMapData@@PEAU_store_dummy@@PEAU_base_fld@@@Z
 * Address: 0x140260720
 */

char __fastcall CItemStore::Init(CItemStore *this, int nIndex, CMapData *pExistMap, _store_dummy *pDum, _base_fld *pRec)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  _map_fld *v8; // rcx@7
  _map_fld *v9; // rcx@12
  __int64 v10; // [sp+0h] [bp-98h]@1
  char *v11; // [sp+20h] [bp-78h]@7
  int nTableCode; // [sp+30h] [bp-68h]@23
  unsigned int v13; // [sp+34h] [bp-64h]@21
  unsigned int j; // [sp+38h] [bp-60h]@21
  _base_fld *v15; // [sp+40h] [bp-58h]@24
  _base_fld *v16; // [sp+48h] [bp-50h]@34
  void *v17; // [sp+50h] [bp-48h]@18
  void *__t; // [sp+58h] [bp-40h]@15
  void *v19; // [sp+60h] [bp-38h]@21
  void *v20; // [sp+68h] [bp-30h]@18
  __int64 v21; // [sp+70h] [bp-28h]@4
  void *v22; // [sp+78h] [bp-20h]@16
  void *v23; // [sp+80h] [bp-18h]@19
  CItemStore *v24; // [sp+A0h] [bp+8h]@1
  int v25; // [sp+A8h] [bp+10h]@1
  CMapData *v26; // [sp+B0h] [bp+18h]@1

  v26 = pExistMap;
  v25 = nIndex;
  v24 = this;
  v5 = &v10;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v21 = -2i64;
  if ( v24->m_bLive )
  {
    result = 0;
  }
  else
  {
    v24->m_bLive = 1;
    v24->m_nIndex = nIndex;
    v24->m_nStorageItemNum = 0;
    v24->m_pExistMap = pExistMap;
    v24->m_pDum = pDum;
    v24->m_pRec = (_StoreList_fld *)pRec;
    v24->m_bUpdate = 0;
    v24->m_bDBDataCheck = 0;
    if ( CItemStore::GetNpcRaceCode(v24, &v24->m_byNpcRaceCode) )
    {
      if ( v24->m_byNpcRaceCode && v24->m_byNpcRaceCode != 1 && v24->m_byNpcRaceCode != 2 && v24->m_byNpcRaceCode != 255 )
      {
        v9 = v26->m_pMapSet;
        LODWORD(v11) = v24->m_byNpcRaceCode;
        MyMessageBox(
          "CItemStore::Init error",
          "Map:%s, Store:%d.. unregistered race code (%d) ",
          v9->m_strCode,
          (unsigned int)v25);
        result = 0;
      }
      else
      {
        v24->m_nStorageItemNum = 0;
        if ( v24->m_pRec->m_nStore_trade != 7 && v24->m_pRec->m_nStore_trade != 10 )
        {
          __t = operator new[](0x2880ui64);
          if ( __t )
          {
            `vector constructor iterator'(
              __t,
              0x30ui64,
              216,
              (void *(__cdecl *)(void *))_good_storage_info::_good_storage_info);
            v22 = __t;
          }
          else
          {
            v22 = 0i64;
          }
          v17 = v22;
          v24->m_pStorageItem = (_good_storage_info *)v22;
          v20 = operator new[](0x100ui64);
          if ( v20 )
          {
            `vector constructor iterator'(
              v20,
              0x10ui64,
              16,
              (void *(__cdecl *)(void *))_limit_item_info::_limit_item_info);
            v23 = v20;
          }
          else
          {
            v23 = 0i64;
          }
          v19 = v23;
          v24->m_pLimitStorageItem = (_limit_item_info *)v23;
          v13 = 0;
          for ( j = 0; (signed int)j < 16; ++j )
          {
            nTableCode = GetItemTableCode(v24->m_pRec->m_sellLimitList[j].m_strItemCode);
            if ( nTableCode == -1 )
              break;
            v24->m_pStorageItem[j].byItemTableCode = nTableCode;
            v15 = CRecordData::GetRecord(
                    (CRecordData *)&unk_1799C6AA0 + nTableCode,
                    v24->m_pRec->m_sellLimitList[j].m_strItemCode);
            if ( !v15 )
            {
              MyMessageBox(
                "CItemStore::Init error",
                "%d Store %s Limit Item can't find index",
                (unsigned int)v25,
                &v24->m_pRec->m_sellLimitList[j]);
              break;
            }
            v24->m_pStorageItem[j].wItemIndex = v15->m_dwIndex;
            v24->m_pStorageItem[j].nStdPrice = GetItemStdPrice(
                                                 nTableCode,
                                                 v15->m_dwIndex,
                                                 v24->m_byNpcRaceCode,
                                                 &v24->m_pStorageItem[j].byMoneyUnit);
            v24->m_pStorageItem[j].nStdPoint = GetItemStdPoint(
                                                 nTableCode,
                                                 v15->m_dwIndex,
                                                 v24->m_byNpcRaceCode,
                                                 &v24->m_pStorageItem[j].byMoneyUnit);
            v24->m_pStorageItem[j].nResPoint = GetItemProcPoint(
                                                 nTableCode,
                                                 v15->m_dwIndex,
                                                 v24->m_byNpcRaceCode,
                                                 &v24->m_pStorageItem[j].byMoneyUnit);
            v24->m_pStorageItem[j].nKillPoint = GetItemKillPoint(
                                                  nTableCode,
                                                  v15->m_dwIndex,
                                                  v24->m_byNpcRaceCode,
                                                  &v24->m_pStorageItem[j].byMoneyUnit);
            v24->m_pStorageItem[j].nGoldPoint = GetItemGoldPoint(
                                                  nTableCode,
                                                  v15->m_dwIndex,
                                                  v24->m_byNpcRaceCode,
                                                  &v24->m_pStorageItem[j].byMoneyUnit);
            v24->m_pStorageItem[j].nStdPrice *= v24->m_pRec->m_nPriceSet;
            v24->m_pStorageItem[j].dwUpCode = v24->m_pRec->m_nItemUpCode;
            if ( v24->m_pStorageItem[j].nStdPrice == -1 )
            {
              MyMessageBox(
                "CItemStore::Init error",
                "price load error..(nTable: %d, pFld->m_dwIndex: %d)",
                (unsigned int)nTableCode,
                v15->m_dwIndex);
              break;
            }
            v24->m_pStorageItem[j].bExist = LOBYTE(v15[1].m_dwIndex);
            v24->m_pStorageItem[j].dwDurPoint = GetItemDurPoint(nTableCode, v15->m_dwIndex);
            v24->m_pStorageItem[j].byType = 1;
            v24->m_pStorageItem[j].dwLimitIndex = j;
            ++v24->m_nStorageItemNum;
            v24->m_pLimitStorageItem[j].bLoad = 1;
            v24->m_pLimitStorageItem[j].dwStorageIndex = j;
            v24->m_pLimitStorageItem[j].Key.byTableCode = nTableCode;
            v24->m_pLimitStorageItem[j].Key.wItemIndex = v15->m_dwIndex;
            v24->m_pLimitStorageItem[j].nLimitNum = v24->m_pRec->m_sellLimitList[j].m_nMaxCount;
            ++v24->m_nLimitStorageItemNum;
            ++v13;
          }
          if ( v24->m_nLimitStorageItemNum > 0 )
            CItemStore::SetLimitItemInitTime(v24);
          for ( j = v13; (signed int)j < 216; ++j )
          {
            nTableCode = GetItemTableCode(v24->m_pRec->m_strItemlist[(signed __int64)(signed int)j]);
            if ( nTableCode == -1 )
              break;
            v24->m_pStorageItem[j].byItemTableCode = nTableCode;
            v16 = CRecordData::GetRecord(
                    (CRecordData *)&unk_1799C6AA0 + nTableCode,
                    v24->m_pRec->m_strItemlist[(signed __int64)(signed int)j]);
            if ( !v16 )
            {
              MyMessageBox(
                "CItemStore::Init error",
                "%d Store %s Item can't find index",
                (unsigned int)v25,
                v24->m_pRec->m_strItemlist[(signed __int64)(signed int)j]);
              break;
            }
            v24->m_pStorageItem[j].wItemIndex = v16->m_dwIndex;
            v24->m_pStorageItem[j].nStdPrice = GetItemStdPrice(
                                                 nTableCode,
                                                 v16->m_dwIndex,
                                                 v24->m_byNpcRaceCode,
                                                 &v24->m_pStorageItem[j].byMoneyUnit);
            v24->m_pStorageItem[j].nStdPoint = GetItemStdPoint(
                                                 nTableCode,
                                                 v16->m_dwIndex,
                                                 v24->m_byNpcRaceCode,
                                                 &v24->m_pStorageItem[j].byMoneyUnit);
            v24->m_pStorageItem[j].nResPoint = GetItemProcPoint(
                                                 nTableCode,
                                                 v16->m_dwIndex,
                                                 v24->m_byNpcRaceCode,
                                                 &v24->m_pStorageItem[j].byMoneyUnit);
            v24->m_pStorageItem[j].nKillPoint = GetItemKillPoint(
                                                  nTableCode,
                                                  v16->m_dwIndex,
                                                  v24->m_byNpcRaceCode,
                                                  &v24->m_pStorageItem[j].byMoneyUnit);
            v24->m_pStorageItem[j].nGoldPoint = GetItemGoldPoint(
                                                  nTableCode,
                                                  v16->m_dwIndex,
                                                  v24->m_byNpcRaceCode,
                                                  &v24->m_pStorageItem[j].byMoneyUnit);
            v24->m_pStorageItem[j].nStdPrice *= v24->m_pRec->m_nPriceSet;
            v24->m_pStorageItem[j].dwUpCode = v24->m_pRec->m_nItemUpCode;
            if ( v24->m_pStorageItem[j].nStdPrice == -1 )
            {
              MyMessageBox(
                "CItemStore::Init error",
                "price load error..(nTable: %d, pFld->m_dwIndex: %d)",
                (unsigned int)nTableCode,
                v16->m_dwIndex);
              break;
            }
            v24->m_pStorageItem[j].bExist = LOBYTE(v16[1].m_dwIndex);
            v24->m_pStorageItem[j].dwDurPoint = GetItemDurPoint(nTableCode, v16->m_dwIndex);
            ++v24->m_nStorageItemNum;
          }
        }
        v24->m_dwSecIndex = CItemStore::CalcSecIndex(
                              v24,
                              v24->m_pDum->m_pDumPos->m_fCenterPos[0],
                              v24->m_pDum->m_pDumPos->m_fCenterPos[2]);
        result = 1;
      }
    }
    else
    {
      v8 = v26->m_pMapSet;
      v11 = v24->m_pRec->m_strStore_NPCcode;
      MyMessageBox(
        "CItemStore::Init error",
        "Map:%s, Store:%d..  unregistered npc code (%s) ",
        v8->m_strCode,
        (unsigned int)v25);
      result = 0;
    }
  }
  return result;
}
