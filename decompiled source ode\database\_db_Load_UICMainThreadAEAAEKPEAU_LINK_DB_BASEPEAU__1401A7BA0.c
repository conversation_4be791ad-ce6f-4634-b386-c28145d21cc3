/*
 * Function: ?_db_Load_UI@CMainThread@@AEAAEKPEAU_LINK_DB_BASE@@PEAU_SFCONT_DB_BASE@@@Z
 * Address: 0x1401A7BA0
 */

char __fastcall CMainThread::_db_Load_UI(CMainThread *this, unsigned int dwSerial, _LINK_DB_BASE *pLink, _SFCONT_DB_BASE *pSfcont)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-138h]@1
  __int16 Dst[50]; // [sp+30h] [bp-108h]@4
  int v9[8]; // [sp+94h] [bp-A4h]@16
  int v10[8]; // [sp+B4h] [bp-84h]@16
  char Src; // [sp+D4h] [bp-64h]@17
  char v12; // [sp+DCh] [bp-5Ch]@17
  char v13; // [sp+E4h] [bp-54h]@17
  char v14; // [sp+ECh] [bp-4Ch]@17
  unsigned int v15; // [sp+F4h] [bp-44h]@17
  char v16; // [sp+F8h] [bp-40h]@17
  char v17; // [sp+10Ch] [bp-2Ch]@17
  char v18; // [sp+114h] [bp-24h]@4
  int j; // [sp+118h] [bp-20h]@11
  unsigned __int64 v20; // [sp+128h] [bp-10h]@4
  CMainThread *v21; // [sp+140h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+148h] [bp+10h]@1
  _LINK_DB_BASE *v23; // [sp+150h] [bp+18h]@1
  _SFCONT_DB_BASE *v24; // [sp+158h] [bp+20h]@1

  v24 = pSfcont;
  v23 = pLink;
  dwSeriala = dwSerial;
  v21 = this;
  v4 = &v7;
  for ( i = 76i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v20 = (unsigned __int64)&v7 ^ _security_cookie;
  memset_0(Dst, 0, 0xE0ui64);
  v18 = CRFWorldDatabase::Select_UserInterface(v21->m_pWorldDB, dwSeriala, (_worlddb_userinterface_info *)Dst);
  if ( v18 == 1 )
    return 24;
  if ( v18 != 2 )
    goto LABEL_20;
  if ( !CRFWorldDatabase::Insert_UserInterface(v21->m_pWorldDB, dwSeriala) )
    return 24;
  if ( CRFWorldDatabase::Select_UserInterface(v21->m_pWorldDB, dwSeriala, (_worlddb_userinterface_info *)Dst) )
  {
    result = 24;
  }
  else
  {
LABEL_20:
    for ( j = 0; j < 50; ++j )
      _LINKKEY::LoadDBKey((_LINKKEY *)v23 + j, Dst[j]);
    for ( j = 0; j < 8; ++j )
    {
      v24->m_List[0][j].dwKey = v9[j];
      v24->m_List[1][j].dwKey = v10[j];
    }
    memcpy_0(v23->m_dwSkill, &Src, 8ui64);
    memcpy_0(v23->m_dwForce, &v12, 8ui64);
    memcpy_0(v23->m_dwCharacter, &v13, 8ui64);
    memcpy_0(v23->m_dwAnimus, &v14, 8ui64);
    memcpy_0(v23->m_dwInvenBag, &v16, 0x14ui64);
    v23->m_dwInven = v15;
    v23->m_byLinkBoardLock = v17;
    result = 0;
  }
  return result;
}
