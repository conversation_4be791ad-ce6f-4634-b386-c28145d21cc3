/*
 * Function: j_?Select_AccountItemCharge_Extend@CRFWorldDatabase@@QEAA_NKPEAEPEAKPEA_K101PEAH@Z
 * Address: 0x140007252
 */

bool __fastcall CRFWorldDatabase::Select_AccountItemCharge_Extend(CRFWorldDatabase *this, unsigned int dwAccountSerial, char *pbyType, unsigned int *pdwItemCode_K, unsigned __int64 *pdwItemCode_D, unsigned int *pdwItemCode_U, char *pbyRace, unsigned int *pdwDBID, int *piTime)
{
  return CRFWorldDatabase::Select_AccountItemCharge_Extend(
           this,
           dwAccountSerial,
           pbyType,
           pdwItemCode_K,
           pdwItemCode_D,
           pdwItemCode_U,
           pbyRace,
           pdwDBID,
           piTime);
}
