/*
 * Function: ??0HashVerificationFailed@HashVerificationFilter@CryptoPP@@QEAA@AEBV012@@Z
 * Address: 0x1405FF880
 */

CryptoPP::HashVerificationFilter::HashVerificationFailed *__fastcall CryptoPP::HashVerificationFilter::HashVerificationFailed::HashVerificationFailed(CryptoPP::HashVerificationFilter::HashVerificationFailed *this, const struct CryptoPP::HashVerificationFilter::HashVerificationFailed *a2)
{
  CryptoPP::HashVerificationFilter::HashVerificationFailed *v3; // [sp+30h] [bp+8h]@1

  v3 = this;
  CryptoPP::Exception::Exception((CryptoPP::Exception *)&this->vfptr, (CryptoPP::Exception *)&a2->vfptr);
  v3->vfptr = (std::exceptionVtbl *)&CryptoPP::HashVerificationFilter::HashVerificationFailed::`vftable';
  return v3;
}
