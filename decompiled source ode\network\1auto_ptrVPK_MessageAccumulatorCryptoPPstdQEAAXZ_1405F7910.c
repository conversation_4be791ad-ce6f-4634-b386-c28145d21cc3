/*
 * Function: ??1?$auto_ptr@VPK_MessageAccumulator@CryptoPP@@@std@@QEAA@XZ
 * Address: 0x1405F7910
 */

int __fastcall std::auto_ptr<CryptoPP::PK_MessageAccumulator>::~auto_ptr<CryptoPP::PK_MessageAccumulator>(_QWORD *a1)
{
  int (__fastcall ***v1)(_QWORD, _QWORD); // rax@1

  v1 = (int (__fastcall ***)(_QWORD, _QWORD))*a1;
  if ( *a1 )
    LODWORD(v1) = (**(int (__fastcall ***)(int (__fastcall ***)(_QWORD, _QWORD), signed __int64))*a1)(
                    (int (__fastcall ***)(_QWORD, _QWORD))*a1,
                    1i64);
  return (unsigned __int64)v1;
}
