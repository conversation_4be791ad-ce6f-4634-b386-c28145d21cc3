/*
 * Function: ?SendMsg_PatriarchInform@ClassOrderProcessor@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x1402B8F50
 */

void __fastcall ClassOrderProcessor::SendMsg_PatriarchInform(ClassOrderProcessor *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v4; // rax@4
  unsigned int v5; // eax@4
  int v6; // eax@5
  int v7; // eax@5
  __int64 v8; // [sp+0h] [bp-48h]@1
  unsigned int v9; // [sp+30h] [bp-18h]@4
  int v10; // [sp+34h] [bp-14h]@4
  unsigned __int16 v11[2]; // [sp+38h] [bp-10h]@5
  ClassOrderProcessor *v12; // [sp+50h] [bp+8h]@1
  CPlayer *v13; // [sp+58h] [bp+10h]@1

  v13 = pOne;
  v12 = this;
  v2 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = CPlayerDB::GetCharSerial(&pOne->m_Param);
  v10 = CPlayerDB::GetRaceCode(&v13->m_Param);
  v4 = CPvpUserAndGuildRankingSystem::Instance();
  v5 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v4, v10, 0);
  if ( v9 == v5 )
  {
    v6 = CPlayerDB::GetRaceCode(&v13->m_Param);
    *(_DWORD *)v11 = _pt_appoint_inform_request_zocl::size(&v12->_kSend[v6]);
    v7 = CPlayerDB::GetRaceCode(&v13->m_Param);
    CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, v12->_byPtType, (char *)&v12->_kSend[v7], v11[0]);
  }
}
