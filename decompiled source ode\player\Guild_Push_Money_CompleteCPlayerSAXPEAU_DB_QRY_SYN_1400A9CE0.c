/*
 * Function: ?Guild_Push_Money_Complete@CPlayer@@SAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1400A9CE0
 */

void __fastcall CPlayer::Guild_Push_Money_Complete(_DB_QRY_SYN_DATA *pData)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@9
  __int64 v4; // [sp+0h] [bp-108h]@1
  int v5; // [sp+50h] [bp-B8h]@4
  int v6; // [sp+54h] [bp-B4h]@4
  unsigned int dwIOerSerial; // [sp+58h] [bp-B0h]@4
  char Dest; // [sp+68h] [bp-A0h]@4
  unsigned int dwPush; // [sp+84h] [bp-84h]@4
  unsigned int dwPopDalant; // [sp+88h] [bp-80h]@4
  long double v11; // [sp+90h] [bp-78h]@4
  long double v12; // [sp+98h] [bp-70h]@4
  char Dst; // [sp+A4h] [bp-64h]@4
  char *v14; // [sp+B8h] [bp-50h]@4
  CGuild *v15; // [sp+C0h] [bp-48h]@4
  _guild_member_info *v16; // [sp+C8h] [bp-40h]@7
  CPlayer *v17; // [sp+D0h] [bp-38h]@8
  char *pszFileName; // [sp+E0h] [bp-28h]@9
  unsigned int v19; // [sp+E8h] [bp-20h]@9
  unsigned __int64 v20; // [sp+F0h] [bp-18h]@4
  _DB_QRY_SYN_DATA *v21; // [sp+110h] [bp+8h]@1

  v21 = pData;
  v1 = &v4;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v20 = (unsigned __int64)&v4 ^ _security_cookie;
  v5 = -1;
  v6 = -1;
  dwIOerSerial = -1;
  dwPush = 0;
  dwPopDalant = 0;
  v11 = 0.0;
  v12 = 0.0;
  v14 = v21->m_sData;
  v5 = *(_DWORD *)&v21->m_sData[24];
  v6 = *(_DWORD *)&v21->m_sData[28];
  dwIOerSerial = *(_DWORD *)&v21->m_sData[0];
  dwPopDalant = *(_DWORD *)&v21->m_sData[36];
  dwPush = *(_DWORD *)&v21->m_sData[32];
  v12 = *(double *)&v21->m_sData[56];
  v11 = *(double *)&v21->m_sData[48];
  strcpy_0(&Dest, &v21->m_sData[4]);
  memcpy_0(&Dst, v14 + 40, 4ui64);
  v15 = &g_Guild[v5];
  if ( v15->m_dwSerial == v6 )
  {
    v15->m_bIOWait = 0;
    if ( v21->m_byResult )
    {
      v16 = CGuild::GetMemberFromSerial(v15, dwIOerSerial);
      if ( v16 )
      {
        v17 = v16->pPlayer;
        if ( v17 )
        {
          CPlayer::AddGold(v17, dwPush, 1);
          CPlayer::AddDalant(v17, dwPopDalant, 1);
          pszFileName = v17->m_szItemHistoryFileName;
          v19 = CPlayerDB::GetGold(&v17->m_Param);
          v3 = CPlayerDB::GetDalant(&v17->m_Param);
          CMgrAvatorItemHistory::guild_pop_money_rollback(
            &CPlayer::s_MgrItemHistory,
            v17->m_ObjID.m_wIndex,
            v15->m_aszName,
            dwPopDalant,
            dwPush,
            v3,
            v19,
            pszFileName);
        }
      }
    }
    else
    {
      v15->m_byMoneyOutputKind = 0;
      CGuild::IOMoney(
        v15,
        &Dest,
        dwIOerSerial,
        (double)(signed int)dwPopDalant,
        (double)(signed int)dwPush,
        v12,
        v11,
        &Dst,
        1);
    }
  }
}
