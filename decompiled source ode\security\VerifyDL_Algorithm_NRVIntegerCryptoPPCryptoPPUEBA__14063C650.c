/*
 * Function: ?Verify@?$DL_Algorithm_NR@VInteger@CryptoPP@@@CryptoPP@@UEBA_NAEBV?$DL_GroupParameters@VInteger@CryptoPP@@@2@AEBV?$DL_PublicKey@VInteger@CryptoPP@@@2@AEBVInteger@2@22@Z
 * Address: 0x14063C650
 */

char __fastcall CryptoPP::DL_Algorithm_NR<CryptoPP::Integer>::Verify(CryptoPP::Integer *a, __int64 a2, __int64 a3, const struct CryptoPP::Integer *a4, CryptoPP::Integer *aa, CryptoPP::Integer *a6)
{
  const struct CryptoPP::Integer *v6; // rax@1
  char v7; // al@9
  __int64 v8; // rax@10
  CryptoPP::Integer *v9; // rax@10
  CryptoPP::Integer *v10; // rax@10
  CryptoPP::Integer *v11; // rax@10
  CryptoPP::Integer *v12; // [sp+20h] [bp-148h]@1
  char v13; // [sp+28h] [bp-140h]@6
  CryptoPP::Integer b; // [sp+30h] [bp-138h]@2
  bool v15; // [sp+58h] [bp-110h]@10
  CryptoPP::Integer v16; // [sp+60h] [bp-108h]@10
  CryptoPP::Integer v17; // [sp+88h] [bp-E0h]@10
  CryptoPP::Integer v18; // [sp+B0h] [bp-B8h]@10
  CryptoPP::Integer result; // [sp+D8h] [bp-90h]@10
  int v20; // [sp+100h] [bp-68h]@1
  __int64 v21; // [sp+108h] [bp-60h]@1
  int v22; // [sp+110h] [bp-58h]@4
  __int64 v23; // [sp+118h] [bp-50h]@10
  __int64 v24; // [sp+120h] [bp-48h]@10
  CryptoPP::Integer *v25; // [sp+128h] [bp-40h]@10
  CryptoPP::Integer *v26; // [sp+130h] [bp-38h]@10
  CryptoPP::Integer *v27; // [sp+138h] [bp-30h]@10
  CryptoPP::Integer *v28; // [sp+140h] [bp-28h]@10
  CryptoPP::Integer *v29; // [sp+148h] [bp-20h]@10
  CryptoPP::Integer *v30; // [sp+150h] [bp-18h]@10
  __int64 v31; // [sp+178h] [bp+10h]@1
  __int64 v32; // [sp+180h] [bp+18h]@1
  const struct CryptoPP::Integer *v33; // [sp+188h] [bp+20h]@1

  v33 = a4;
  v32 = a3;
  v31 = a2;
  v21 = -2i64;
  v20 = 0;
  LODWORD(v6) = (*(int (__fastcall **)(__int64))(*(_QWORD *)a2 + 64i64))(a2);
  v12 = (CryptoPP::Integer *)v6;
  v22 = CryptoPP::operator>=(aa, v6)
     || (CryptoPP::Integer::Integer(&b, 1), v20 |= 1u, CryptoPP::operator<(aa, &b))
     || CryptoPP::operator>=(a6, v12);
  v13 = v22;
  if ( v20 & 1 )
  {
    v20 &= 0xFFFFFFFE;
    CryptoPP::Integer::~Integer(&b);
  }
  if ( v13 )
  {
    v7 = 0;
  }
  else
  {
    LODWORD(v8) = (*(int (__fastcall **)(__int64, CryptoPP::Integer *, CryptoPP::Integer *, CryptoPP::Integer *))(*(_QWORD *)v32 + 40i64))(
                    v32,
                    &v16,
                    a6,
                    aa);
    v23 = v8;
    v24 = v8;
    LODWORD(v9) = (*(int (__fastcall **)(__int64, CryptoPP::Integer *, __int64))(*(_QWORD *)v31 + 120i64))(
                    v31,
                    &v17,
                    v8);
    v25 = v9;
    v26 = v9;
    v10 = CryptoPP::operator+(&v18, v9, v33);
    v27 = v10;
    v28 = v10;
    v11 = CryptoPP::operator%(&result, v10, v12);
    v29 = v11;
    v30 = v11;
    v15 = CryptoPP::operator==(aa, v11);
    CryptoPP::Integer::~Integer(&result);
    CryptoPP::Integer::~Integer(&v18);
    CryptoPP::Integer::~Integer(&v17);
    CryptoPP::Integer::~Integer(&v16);
    v7 = v15;
  }
  return v7;
}
