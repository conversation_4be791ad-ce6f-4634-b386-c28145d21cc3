/*
 * Function: ?CreateGuildBattleRankTable@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404A3810
 */

bool __fastcall CRFWorldDatabase::CreateGuildBattleRankTable(CRFWorldDatabase *this, char *szDate)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-458h]@1
  char Dest; // [sp+30h] [bp-428h]@4
  unsigned __int64 v7; // [sp+440h] [bp-18h]@4
  CRFWorldDatabase *v8; // [sp+460h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 276i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = (unsigned __int64)&v5 ^ _security_cookie;
  sprintf(
    &Dest,
    "select identity(int,1,1) as  rank, g.serial, g.race, g.grade, g.id, r.win, r.draw, r.lose, r.score into [dbo].[tbl_G"
    "uildBattleRank%s] from [dbo].[tbl_GuildBattleRank] as r join [dbo].[tbl_Guild] as g on r.serial = g.Serial and g.dck"
    " = 0 order by score desc, win desc, draw desc, lose",
    szDate);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v8->vfptr, &Dest, 1);
}
