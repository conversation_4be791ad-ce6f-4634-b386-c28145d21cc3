/*
 * Function: ?pc_TrunkDownloadRequest@CPlayer@@QEAAXPEAD@Z
 * Address: 0x1400F7C30
 */

void __fastcall CPlayer::pc_TrunkDownloadRequest(CPlayer *this, char *pwszPassword)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@8
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  CPlayer *p; // [sp+40h] [bp+8h]@1
  const char *Str1; // [sp+48h] [bp+10h]@1

  Str1 = pwszPassword;
  p = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  if ( IsBeNearStore(p, 10) )
  {
    if ( (signed int)(unsigned __int8)CPlayerDB::GetTrunkSlotNum(&p->m_Param) > 0 )
    {
      v4 = CPlayerDB::GetTrunkPasswdW(&p->m_Param);
      if ( strcmp_0(Str1, v4) )
        v6 = 1;
    }
    else
    {
      v6 = 2;
    }
  }
  else
  {
    v6 = 13;
  }
  if ( !v6 )
    p->m_Param.m_bTrunkOpen = 1;
  CPlayer::SendMsg_TrunkDownloadResult(p, v6);
}
