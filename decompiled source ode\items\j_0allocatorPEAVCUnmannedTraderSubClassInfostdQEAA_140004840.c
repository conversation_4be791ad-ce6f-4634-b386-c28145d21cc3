/*
 * Function: j_??0?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@QEAA@AEBV01@@Z
 * Address: 0x140004840
 */

void __fastcall std::allocator<CUnmannedTraderSubClassInfo *>::allocator<CUnmannedTraderSubClassInfo *>(std::allocator<CUnmannedTraderSubClassInfo *> *this, std::allocator<CUnmannedTraderSubClassInfo *> *__formal)
{
  std::allocator<CUnmannedTraderSubClassInfo *>::allocator<CUnmannedTraderSubClassInfo *>(this, __formal);
}
