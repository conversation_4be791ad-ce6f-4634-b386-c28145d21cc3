/*
 * Function: ?GetPrimeTable@CryptoPP@@YAPEBGAEAI@Z
 * Address: 0x140640560
 */

int __fastcall CryptoPP::GetPrimeTable(CryptoPP *this, unsigned int *a2)
{
  __int64 v2; // rax@1
  __int64 v3; // rax@1
  __int64 v4; // ST20_8@1
  char v6; // [sp+28h] [bp-10h]@1
  unsigned __int8 v7; // [sp+29h] [bp-Fh]@1
  CryptoPP *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  memset(&v7, 0, sizeof(v7));
  LODWORD(v2) = CryptoPP::Singleton<std::vector<unsigned short,std::allocator<unsigned short>>,CryptoPP::NewPrimeTable,0>::Singleton<std::vector<unsigned short,std::allocator<unsigned short>>,CryptoPP::NewPrimeTable,0>(
                  &v6,
                  v7);
  LODWORD(v3) = CryptoPP::Singleton<std::vector<unsigned short,std::allocator<unsigned short>>,CryptoPP::NewPrimeTable,0>::Ref(v2);
  v4 = v3;
  *(_DWORD *)v8 = std::vector<unsigned short,std::allocator<unsigned short>>::size(v3);
  return std::vector<unsigned short,std::allocator<unsigned short>>::operator[](v4, 0i64);
}
