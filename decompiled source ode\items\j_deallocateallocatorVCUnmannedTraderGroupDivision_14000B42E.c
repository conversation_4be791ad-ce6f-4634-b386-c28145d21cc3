/*
 * Function: j_?deallocate@?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@QEAAXPEAVCUnmannedTraderGroupDivisionVersionInfo@@_K@Z
 * Address: 0x14000B42E
 */

void __fastcall std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::deallocate(std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *this, CUnmannedTraderGroupDivisionVersionInfo *_Ptr, unsigned __int64 __formal)
{
  std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::deallocate(this, _Ptr, __formal);
}
