/*
 * Function: ?Update_DelPost@CUserDB@@QEAA_NKH@Z
 * Address: 0x140117B00
 */

char __fastcall CUserDB::Update_DelPost(CUserDB *this, unsigned int dwSerial, int nIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  bool *v6; // [sp+0h] [bp-18h]@1
  CUserDB *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v3 = (__int64 *)&v6;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( nIndex >= 0 && nIndex < 50 )
  {
    if ( v7->m_AvatorData.dbPostData.dbPost.m_PostList[nIndex].bNew )
    {
      result = 1;
    }
    else
    {
      v6 = &v7->m_AvatorData.dbPostData.dbDelPost.m_bUpdate;
      if ( v7->m_AvatorData.dbPostData.dbDelPost.m_nCum < v7->m_AvatorData.dbPostData.dbDelPost.m_nMax )
      {
        *(_DWORD *)&v6[8 * *(_DWORD *)(v6 + 5) + 9] = dwSerial;
        *(_DWORD *)&v6[8 * (*(_DWORD *)(v6 + 5))++ + 13] = nIndex;
        *v6 = 1;
        v7->m_bDataUpdate = 1;
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
