/*
 * Function: ?CompleteDisableInstanceStore@CItemStoreManager@@QEAAXPEAD@Z
 * Address: 0x14034A710
 */

void __fastcall CItemStoreManager::CompleteDisableInstanceStore(CItemStoreManager *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  char *v5; // [sp+20h] [bp-28h]@4
  int j; // [sp+28h] [bp-20h]@4
  __int64 v7; // [sp+30h] [bp-18h]@6
  void *v8; // [sp+38h] [bp-10h]@10
  CItemStoreManager *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = pData;
  for ( j = 0; j < *(_DWORD *)v5; ++j )
  {
    v7 = *((_QWORD *)v5 + 1) + 8i64 * j;
    if ( !*(_BYTE *)v7 )
      CItemStoreManager::Log(
        v9,
        "CItemStoreManager::CompleteDisableInstanceStore\r\n\t\tInstanceStore Disable Fail! (DBSerial:%d)\r\n",
        *(_DWORD *)(v7 + 4));
  }
  if ( *(_DWORD *)v5 > 0 )
  {
    v8 = (void *)*((_QWORD *)v5 + 1);
    operator delete[](v8);
  }
}
