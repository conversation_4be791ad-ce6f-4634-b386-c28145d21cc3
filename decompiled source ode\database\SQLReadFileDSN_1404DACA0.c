/*
 * Function: SQLReadFileDSN
 * Address: 0x1404DACA0
 */

int __fastcall SQLReadFileDSN(const char *lpszFileName, const char *lpszAppName, const char *lpszKeyName, char *lpszString, unsigned __int16 cbString, unsigned __int16 *pcbString)
{
  const char *v6; // rbp@1
  char *v7; // rbx@1
  const char *v8; // rdi@1
  const char *v9; // rsi@1
  __int64 (__cdecl *v10)(); // rax@1
  int result; // eax@2

  v6 = lpszFileName;
  v7 = lpszString;
  v8 = lpszKeyName;
  v9 = lpszAppName;
  v10 = ODBC___GetSetupProc("SQLReadFileDSN");
  if ( v10 )
    result = ((int (__fastcall *)(const char *, const char *, const char *, char *))v10)(v6, v9, v8, v7);
  else
    result = 0;
  return result;
}
