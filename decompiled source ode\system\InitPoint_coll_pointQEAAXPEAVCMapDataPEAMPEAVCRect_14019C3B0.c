/*
 * Function: ?InitPoint@_coll_point@@QEAAXPEAVCMapData@@PEAMPEAVCRect@@@Z
 * Address: 0x14019C3B0
 */

void __fastcall _coll_point::InitPoint(_coll_point *this, CMapData *pMap, float *pPos, CRect *prcWnd)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  _bsp_info *v7; // [sp+20h] [bp-18h]@4
  _coll_point *v8; // [sp+40h] [bp+8h]@1
  float *v9; // [sp+50h] [bp+18h]@1
  CRect *v10; // [sp+58h] [bp+20h]@1

  v10 = prcWnd;
  v9 = pPos;
  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7 = CMapData::GetBspInfo(pMap);
  v8->m_fPosAbs[0] = (float)(-0.0 - (float)v7->m_nMapMinSize[0]) + *v9;
  v8->m_fPosAbs[1] = (float)v7->m_nMapMaxSize[2] - v9[2];
  v8->m_fScrNor[0] = (float)(v8->m_fPosAbs[0] * (float)v10->right) / (float)v7->m_nMapSize[0];
  v8->m_fScrNor[1] = (float)(v8->m_fPosAbs[1] * (float)v10->bottom) / (float)v7->m_nMapSize[2];
}
