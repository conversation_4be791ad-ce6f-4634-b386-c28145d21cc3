/*
 * Function: j_?Get<PERSON><PERSON>@CNormalGuildBattleFieldList@GUILD_BATTLE@@QEAAPEAVCNormalGuildBattleField@2@EK@Z
 * Address: 0x1400038F5
 */

GUILD_BATTLE::CNormalGuildBattleField *__fastcall GUILD_BATTLE::CNormalGuildBattleFieldList::GetField(GUILD_BATTLE::CNormalGuildBattleFieldList *this, char byR<PERSON>, unsigned int dwMapCode)
{
  return GUILD_BATTLE::CNormalGuildBattleFieldList::Get<PERSON>ield(this, byRace, dwMapCode);
}
