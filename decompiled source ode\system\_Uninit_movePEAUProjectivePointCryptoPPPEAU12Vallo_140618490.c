/*
 * Function: ??$_Uninit_move@PEAUProjectivePoint@CryptoPP@@PEAU12@V?$allocator@UProjectivePoint@CryptoPP@@@std@@U_Undefined_move_tag@4@@std@@YAPEAUProjectivePoint@CryptoPP@@PEAU12@00AEAV?$allocator@UProjectivePoint@CryptoPP@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140618490
 */

int std::_Uninit_move<CryptoPP::ProjectivePoint *,CryptoPP::ProjectivePoint *,std::allocator<CryptoPP::ProjectivePoint>,std::_Undefined_move_tag>()
{
  return stdext::unchecked_uninitialized_copy<CryptoPP::ProjectivePoint *,CryptoPP::ProjectivePoint *,std::allocator<CryptoPP::ProjectivePoint>>();
}
