/*
 * Function: ?InitFullScreenEffect@@YAXXZ
 * Address: 0x140514070
 */

void InitFullScreenEffect(void)
{
  signed int v0; // ebx@2
  unsigned __int32 v1; // edi@4
  unsigned __int32 v2; // ebx@4
  struct IDirect3DDevice8 *v3; // rax@4
  struct IDirect3DDevice8 *v4; // rax@4
  struct IDirect3DDevice8 *v5; // rax@4
  struct IDirect3DDevice8 *v6; // rax@4
  struct IDirect3DDevice8 *v7; // rax@4

  if ( *(float *)&dword_184A7978C <= 1024.0 || (v0 = 2, *(float *)&dword_184A79790 <= 768.0) )
    v0 = 1;
  dword_184A89270 = 512;
  GetD3dDevice();
  v1 = v0 * dword_184A89270;
  v2 = 2 * dword_184A89270;
  v3 = GetD3dDevice();
  CTextureRender::CreateTexture((CTextureRender *)&stRToTMain1024, v3, v2, v1, 1);
  v4 = GetD3dDevice();
  CTextureRender::CreateTexture((CTextureRender *)&stRToTMain512, v4, dword_184A89270, dword_184A89270, 1);
  v5 = GetD3dDevice();
  CTextureRender::CreateTexture((CTextureRender *)&stRToTBlur, v5, dword_184A89270, dword_184A89270, 0);
  v6 = GetD3dDevice();
  CTextureRender::CreateTexture((CTextureRender *)&stShadow, v6, 0x100u, 0x100u, 1);
  v7 = GetD3dDevice();
  CTextureRender::CreateTexture((CTextureRender *)&stShadowTemp, v7, 0x100u, 0x100u, 0);
}
