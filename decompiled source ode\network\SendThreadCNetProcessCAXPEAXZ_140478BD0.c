/*
 * Function: ?SendThread@CNetProcess@@CAXPEAX@Z
 * Address: 0x140478BD0
 */

void __fastcall CNetProcess::SendThread(void *pv)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-68h]@1
  _DWORD *v4; // [sp+20h] [bp-48h]@4
  _DWORD *v5; // [sp+28h] [bp-40h]@4
  CNetProcess *v6; // [sp+30h] [bp-38h]@4
  CNetSocket *v7; // [sp+38h] [bp-30h]@4
  unsigned int dwIndex; // [sp+40h] [bp-28h]@6
  int v9; // [sp+44h] [bp-24h]@4
  DWORD v10; // [sp+48h] [bp-20h]@6
  _socket *v11; // [sp+50h] [bp-18h]@9
  DWORD v12; // [sp+58h] [bp-10h]@11
  _DWORD *v13; // [sp+70h] [bp+8h]@1

  v13 = pv;
  v1 = &v3;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = v13;
  v5 = v13;
  v6 = (CNetProcess *)*((_QWORD *)v13 + 1);
  v7 = &v6->m_NetSocket;
  v9 = 0;
  while ( *v5 )
  {
    CNetFrameRate::CalcFrameRate(&v6->m_fpsSendThread);
    v10 = timeGetTime();
    for ( dwIndex = 0; dwIndex < v6->m_Type.m_wSocketMaxNum; ++dwIndex )
    {
      v11 = CNetSocket::GetSocket(v7, dwIndex);
      if ( !v11->m_bAccept )
        continue;
      if ( !v11->m_bSendable )
      {
        v12 = v10 - v11->m_dwSendBlockTime;
        if ( v12 <= 0x3E8 )
          continue;
        v11->m_bSendable = 1;
      }
      CNetProcess::_SendLoop(v6, dwIndex);
      if ( !(dwIndex % 0x64) )
        Sleep(0);
    }
    if ( ++v9 > v6->m_Type.m_bySendSleepTime )
    {
      Sleep(1u);
      v9 = 0;
    }
  }
  _endthreadex(0);
}
