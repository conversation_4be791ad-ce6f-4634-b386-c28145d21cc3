/*
 * Function: ?AutoCharge_Booster@CPlayer@@QEAAXXZ
 * Address: 0x1400570B0
 */

void __fastcall CPlayer::AutoCharge_Booster(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  _base_fld *v4; // [sp+20h] [bp-28h]@5
  int v5; // [sp+28h] [bp-20h]@7
  int v6; // [sp+2Ch] [bp-1Ch]@7
  int v7; // [sp+30h] [bp-18h]@14
  CPlayer *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v8->m_pUsingUnit )
  {
    v4 = CRecordData::GetRecord(&stru_1799C86D0 + 5, v8->m_pUsingUnit->byPart[5]);
    if ( v4 )
    {
      if ( *(_DWORD *)&v4[5].m_strCode[56] )
      {
        v5 = v8->m_pUsingUnit->wBooster;
        v6 = 0;
        if ( v8->m_bMove && !v8->m_pParkingUnit )
        {
          if ( v8->m_bMove && v8->m_byMoveType == 1 )
            v6 = -2;
        }
        else
        {
          v6 = 2;
        }
        if ( v6 )
        {
          v7 = v6 + v5;
          if ( v6 + v5 >= 0 )
          {
            if ( v7 > *(_DWORD *)&v4[5].m_strCode[56] )
              v7 = *(_DWORD *)&v4[5].m_strCode[56];
          }
          else
          {
            v7 = 0;
          }
          v8->m_pUsingUnit->wBooster = v7;
          if ( v5 != v7 )
            CPlayer::SendMsg_AlterBooster(v8);
        }
      }
    }
  }
}
