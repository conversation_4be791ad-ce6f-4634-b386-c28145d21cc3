/*
 * Function: ?CheckPos_Region@CPlayer@@QEAAXXZ
 * Address: 0x1400C7C50
 */

void __fastcall CPlayer::CheckPos_Region(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  float v3; // xmm0_4@4
  __int64 v4; // [sp+0h] [bp-78h]@1
  unsigned int dwMaxY; // [sp+20h] [bp-58h]@6
  _bsp_info *v6; // [sp+30h] [bp-48h]@4
  float v7; // [sp+38h] [bp-40h]@4
  float v8; // [sp+3Ch] [bp-3Ch]@4
  int v9; // [sp+40h] [bp-38h]@4
  DWORD v10; // [sp+44h] [bp-34h]@5
  unsigned __int16 v11; // [sp+48h] [bp-30h]@6
  DWORD v12; // [sp+4Ch] [bp-2Ch]@7
  DWORD v13; // [sp+50h] [bp-28h]@7
  int j; // [sp+54h] [bp-24h]@11
  _BUDDY_LIST::__list *v15; // [sp+58h] [bp-20h]@14
  int *v16; // [sp+60h] [bp-18h]@6
  CPlayer *v17; // [sp+80h] [bp+8h]@1

  v17 = this;
  v1 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = CMapData::GetBspInfo(v17->m_pCurMap);
  v7 = v17->m_fCurPos[0] - (float)v6->m_nMapMinSize[0];
  v3 = (float)v6->m_nMapMaxSize[2] - v17->m_fCurPos[2];
  v8 = (float)v6->m_nMapMaxSize[2] - v17->m_fCurPos[2];
  v9 = 0;
  if ( !CMainThread::IsReleaseServiceMode(&g_Main) )
    v10 = timeGetTime();
  v16 = (int *)v17->m_pCurMap->m_pMapSet;
  dwMaxY = v6->m_nMapSize[2];
  v11 = GetRegionIndex(*v16, (signed int)ffloor(v7), (signed int)ffloor(v8), v6->m_nMapSize[0], dwMaxY);
  if ( !CMainThread::IsReleaseServiceMode(&g_Main) )
  {
    v12 = timeGetTime();
    v13 = v12 - v9;
    if ( v12 != v9 )
      __trace("Calc Region time : %d", v13);
  }
  if ( v11 != v17->m_wRegionIndex || v17->m_wRegionMapIndex != v17->m_pCurMap->m_pMapSet->m_dwIndex )
  {
    v17->m_wRegionIndex = v11;
    v17->m_wRegionMapIndex = v17->m_pCurMap->m_pMapSet->m_dwIndex;
    CPlayer::SendMsg_AlterRegionInform(v17, v17->m_wRegionIndex);
    for ( j = 0; j < 50; ++j )
    {
      v15 = &v17->m_pmBuddy.m_List[j];
      if ( _BUDDY_LIST::__list::fill(v15) && v15->pPtr )
      {
        _effect_parameter::GetEff_Have(&v15->pPtr->m_EP, 50);
        if ( v3 <= 0.0 )
          CPlayer::SendMsg_BuddyPosInform(
            v15->pPtr,
            v17->m_dwObjSerial,
            v17->m_pCurMap->m_pMapSet->m_dwIndex,
            v17->m_wRegionIndex);
      }
    }
    if ( v17->m_Param.m_pGuild )
    {
      _effect_parameter::GetEff_Have(&v17->m_EP, 50);
      if ( v3 <= 0.0 )
        CGuild::SendMsg_GuildMemberPosInform(
          v17->m_Param.m_pGuild,
          v17->m_dwObjSerial,
          v17->m_wRegionMapIndex,
          v17->m_wRegionIndex);
    }
  }
  v17->m_dwLastCheckRegionTime = GetLoopTime();
}
