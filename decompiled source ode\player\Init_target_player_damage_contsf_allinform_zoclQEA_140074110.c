/*
 * Function: ?Init@_target_player_damage_contsf_allinform_zocl@@QEAAXXZ
 * Address: 0x140074110
 */

void __fastcall _target_player_damage_contsf_allinform_zocl::Init(_target_player_damage_contsf_allinform_zocl *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  _target_player_damage_contsf_allinform_zocl *v4; // [sp+20h] [bp+8h]@1

  v4 = this;
  v1 = &j;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  v4->dwSerial = -1;
  v4->byContCount = 0;
  for ( j = 0; j < 8; ++j )
  {
    v4->m_PlayerContSf[j].wSfcode = -1;
    v4->m_PlayerContSf[j].byContCount = 0;
  }
}
