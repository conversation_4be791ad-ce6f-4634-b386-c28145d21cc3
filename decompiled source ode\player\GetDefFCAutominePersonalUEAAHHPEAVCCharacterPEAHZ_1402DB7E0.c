/*
 * Function: ?GetDef<PERSON>@AutominePersonal@@UEAAHHPEAVCCharacter@@PEAH@Z
 * Address: 0x1402DB7E0
 */

signed __int64 __fastcall AutominePersonal::GetDefFC(AutominePersonal *this, int nAttactPart, CCharacter *pAttChar, int *pnConvertPart)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  _base_fld *v8; // [sp+20h] [bp-18h]@6
  unsigned int v9; // [sp+28h] [bp-10h]@7
  AutominePersonal *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v10->m_pItem )
  {
    v8 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 33, v10->m_pItem->m_wItemIndex);
    if ( v8 )
      v9 = *(_DWORD *)&v8[5].m_strCode[16];
    else
      v9 = 1;
    result = v9;
  }
  else
  {
    result = 1i64;
  }
  return result;
}
