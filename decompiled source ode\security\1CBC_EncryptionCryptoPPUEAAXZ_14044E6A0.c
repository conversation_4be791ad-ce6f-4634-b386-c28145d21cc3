/*
 * Function: ??1CBC_Encryption@CryptoPP@@UEAA@XZ
 * Address: 0x14044E6A0
 */

void __fastcall CryptoPP::CBC_Encryption::~CBC_Encryption(CryptoPP::CBC_Encryption *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CryptoPP::CBC_Encryption *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CryptoPP::CBC_ModeBase::~CBC_ModeBase((CryptoPP::CBC_ModeBase *)&v4->vfptr);
}
