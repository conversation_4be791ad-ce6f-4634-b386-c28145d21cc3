/*
 * Function: ?CallProc_RFONLINE_Cancel@CRFCashItemDatabase@@QEAAHAEAU__list@_param_cash_rollback@@@Z
 * Address: 0x140482D90
 */

signed __int64 __fastcall CRFCashItemDatabase::CallProc_RFONLINE_Cancel(CRFCashItemDatabase *this, _param_cash_rollback::__list *list)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v5; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@22
  char DstBuf; // [sp+40h] [bp-148h]@4
  char v9; // [sp+41h] [bp-147h]@4
  SQLLEN v10; // [sp+158h] [bp-30h]@22
  __int16 v11; // [sp+164h] [bp-24h]@9
  unsigned __int8 v12; // [sp+168h] [bp-20h]@16
  unsigned __int8 v13; // [sp+169h] [bp-1Fh]@24
  unsigned __int8 v14; // [sp+16Ah] [bp-1Eh]@32
  unsigned __int64 v15; // [sp+178h] [bp-10h]@4
  CRFCashItemDatabase *v16; // [sp+190h] [bp+8h]@1
  _param_cash_rollback::__list *v17; // [sp+198h] [bp+10h]@1

  v17 = list;
  v16 = this;
  v2 = &v5;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v15 = (unsigned __int64)&v5 ^ _security_cookie;
  DstBuf = 0;
  memset(&v9, 0, 0xFFui64);
  sprintf_s(
    &DstBuf,
    0x100ui64,
    "declare @out_status varchar(1) declare @out_amount int exec dbo.prc_rfonline_cancel %I64d, @s_status = @out_status o"
    "utput,@s_amount = @out_amount output select @out_status, @out_amount",
    list->in_lnUID);
  if ( v16->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v16->vfptr, &DstBuf);
  if ( v16->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v16->vfptr) )
  {
    v11 = SQLExecDirect_0(v16->m_hStmtSelect, &DstBuf, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v16->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v11 = SQLFetch_0(v16->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        v12 = 0;
        if ( v11 == 100 )
        {
          v12 = 2;
        }
        else
        {
          SQLStmt = v16->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &DstBuf, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
          v12 = 1;
        }
        if ( v16->m_hStmtSelect )
          SQLCloseCursor_0(v16->m_hStmtSelect);
        result = v12;
      }
      else
      {
        StrLen_or_IndPtr = &v10;
        SQLStmt = (void *)2;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 1u, 1, &v17->out_cStatus, 2i64, &v10);
        if ( v11 && v11 != 1 )
        {
          v13 = 0;
          if ( v11 == 100 )
          {
            v13 = 2;
          }
          else
          {
            SQLStmt = v16->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &DstBuf, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
            v13 = 1;
          }
          if ( v16->m_hStmtSelect )
            SQLCloseCursor_0(v16->m_hStmtSelect);
          result = v13;
        }
        else
        {
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v16->m_hStmtSelect, 2u, 4, &v17->out_nCashAmount, 0i64, &v10);
          if ( v11 && v11 != 1 )
          {
            v14 = 0;
            if ( v11 == 100 )
            {
              v14 = 2;
            }
            else
            {
              SQLStmt = v16->m_hStmtSelect;
              CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &DstBuf, "SQLExecDirect", SQLStmt);
              CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
              v14 = 1;
            }
            if ( v16->m_hStmtSelect )
              SQLCloseCursor_0(v16->m_hStmtSelect);
            result = v14;
          }
          else
          {
            if ( v16->m_hStmtSelect )
              SQLCloseCursor_0(v16->m_hStmtSelect);
            if ( v16->m_bSaveDBLog )
              CRFNewDatabase::FmtLog((CRFNewDatabase *)&v16->vfptr, "%s Success", &DstBuf);
            result = 0i64;
          }
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v16->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1i64;
  }
  return result;
}
