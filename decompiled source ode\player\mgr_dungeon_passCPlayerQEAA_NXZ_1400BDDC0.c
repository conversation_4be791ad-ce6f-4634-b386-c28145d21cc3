/*
 * Function: ?mgr_dungeon_pass@CPlayer@@QEAA_NXZ
 * Address: 0x1400BDDC0
 */

char __fastcall CPlayer::mgr_dungeon_pass(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@9
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_pCurMap->m_pMapSet->m_nMapType == 1 )
  {
    if ( v6->m_bMapLoading )
    {
      result = 0;
    }
    else
    {
      if ( v6->m_pDHChannel )
      {
        for ( j = 0; j < v6->m_pDHChannel->m_MissionMgr.pCurMssionPtr->nEmbJobSetupNum; ++j )
          v6->m_pDHChannel->m_MissionMgr.Count[j].bPass = 1;
        _dh_mission_mgr::OpenPortal(&v6->m_pDHChannel->m_MissionMgr, -1);
      }
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
