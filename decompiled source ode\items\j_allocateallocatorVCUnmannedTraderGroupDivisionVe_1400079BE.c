/*
 * Function: j_?allocate@?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@QEAAPEAVCUnmannedTraderGroupDivisionVersionInfo@@_K@Z
 * Address: 0x1400079BE
 */

CUnmannedTraderGroupDivisionVersionInfo *__fastcall std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::allocate(std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *this, unsigned __int64 _Count)
{
  return std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::allocate(this, _Count);
}
