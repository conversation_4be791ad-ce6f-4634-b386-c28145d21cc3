/*
 * Function: ?invoke@?$user2type@P8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@lua_tinker@@SAP8CMonster@@EAAPEAVCLuaSignalReActor@@XZPEAUlua_State@@H@Z
 * Address: 0x14040A770
 */

CLuaSignalReActor *(__cdecl *__fastcall lua_tinker::user2type<CLuaSignalReActor * (CMonster::*)(void)>::invoke(lua_tinker::user2type<CLuaSignalReActor * (__cdecl CMonster::*)(void)> *this, struct lua_State *L, int index))(CMonster *this)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  lua_tinker::void2type<CLuaSignalReActor * (__cdecl CMonster::*)(void)> *v5; // rax@4
  void *v6; // rdx@4
  __int64 v8; // [sp+0h] [bp-28h]@1
  lua_tinker::user2type<CLuaSignalReActor * (__cdecl CMonster::*)(void)> *v9; // [sp+30h] [bp+8h]@1

  v9 = this;
  v3 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  LODWORD(v5) = lua_touserdata(v9, (unsigned int)L);
  return lua_tinker::void2type<CLuaSignalReActor * (CMonster::*)(void)>::invoke(v5, v6);
}
