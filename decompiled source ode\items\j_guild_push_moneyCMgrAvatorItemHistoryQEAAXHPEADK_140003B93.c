/*
 * Function: j_?guild_push_money@CMgrAvatorItemHistory@@QEAAXHPEADKKKK0@Z
 * Address: 0x140003B93
 */

void __fastcall CMgrAvatorItemHistory::guild_push_money(CMgrAvatorItemHistory *this, int n, char *pszGuildName, unsigned int dwPushDalant, unsigned int dwPushGold, unsigned int dwLeftDalant, unsigned int dwLeftGold, char *pszFileName)
{
  CMgrAvatorItemHistory::guild_push_money(
    this,
    n,
    pszGuildName,
    dwPushDalant,
    dwPushGold,
    dwLeftDalant,
    dwLeftGold,
    pszFileName);
}
