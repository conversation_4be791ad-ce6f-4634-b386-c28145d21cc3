/*
 * Function: ?_CalcSkillAttPnt@CPlayerAttack@@AEAAH_N@Z
 * Address: 0x14016EC00
 */

__int64 __fastcall CPlayerAttack::_CalcSkillAttPnt(CPlayerAttack *this, bool bUseEffBullet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _attack_param *v4; // rcx@8
  _attack_param *v5; // rdx@8
  __int64 result; // rax@10
  float v7; // xmm0_4@13
  float v8; // xmm0_4@13
  int v9; // eax@15
  float v10; // xmm0_4@18
  float v11; // xmm1_4@18
  int v12; // eax@20
  __int64 v13; // [sp+0h] [bp-A8h]@1
  double v14; // [sp+20h] [bp-88h]@8
  int v15; // [sp+28h] [bp-80h]@8
  int v16; // [sp+30h] [bp-78h]@8
  int v17; // [sp+38h] [bp-70h]@8
  int v18; // [sp+40h] [bp-68h]@8
  _base_fld *v19; // [sp+50h] [bp-58h]@4
  float v20; // [sp+58h] [bp-50h]@4
  float v21; // [sp+5Ch] [bp-4Ch]@4
  int v22; // [sp+60h] [bp-48h]@4
  int v23; // [sp+64h] [bp-44h]@4
  int v24; // [sp+68h] [bp-40h]@4
  int v25; // [sp+6Ch] [bp-3Ch]@9
  int v26; // [sp+70h] [bp-38h]@13
  int v27; // [sp+74h] [bp-34h]@13
  int v28; // [sp+88h] [bp-20h]@13
  int v29; // [sp+8Ch] [bp-1Ch]@18
  float v30; // [sp+94h] [bp-14h]@13
  float v31; // [sp+98h] [bp-10h]@18
  CPlayerAttack *v32; // [sp+B0h] [bp+8h]@1

  v32 = this;
  v2 = &v13;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v19 = v32->m_pp->pFld;
  v20 = (float)v32->m_pp->nLevel + (float)((float)(7.0 - (float)v32->m_pp->nLevel) * 0.5);
  v21 = *(float *)&v19[11].m_strCode[56];
  v22 = *(_DWORD *)&v19[11].m_strCode[4 * (v32->m_pp->nLevel - 1) + 28];
  v23 = 0;
  v24 = 5;
  if ( bUseEffBullet )
  {
    v23 = (signed int)floor((float)((float)((float)v22 / 788.0)
                                  * (float)((float)((float)v32->m_pp->nMinAFPlus
                                                  * (float)((float)(fR_1 + (float)((float)(v20 / 7.0) * fRLf_1))
                                                          + (float)((float)((float)v32->m_pp->nMastery / 99.0) * fRMf_1)))
                                          * v21)) + 0.5);
    v24 = (signed int)floor((float)((float)((float)v22 / 788.0)
                                  * (float)((float)((float)v32->m_pp->nMaxAFPlus
                                                  * (float)((float)(fR_1 + (float)((float)(v20 / 7.0) * fRLf_1))
                                                          + (float)((float)((float)v32->m_pp->nMastery / 99.0) * fRMf_1)))
                                          * v21)) + 0.5);
  }
  else
  {
    v23 = (signed int)floor((float)((float)((float)v22 / 788.0)
                                  * (float)((float)((float)v32->m_pp->nMinAF
                                                  * (float)((float)(fR_1 + (float)((float)(v20 / 7.0) * fRLf_1))
                                                          + (float)((float)((float)v32->m_pp->nMastery / 99.0) * fRMf_1)))
                                          * v21)) + 0.5);
    v24 = (signed int)floor((float)((float)((float)v22 / 788.0)
                                  * (float)((float)((float)v32->m_pp->nMaxAF
                                                  * (float)((float)(fR_1 + (float)((float)(v20 / 7.0) * fRLf_1))
                                                          + (float)((float)((float)v32->m_pp->nMastery / 99.0) * fRMf_1)))
                                          * v21)) + 0.5);
  }
  if ( v24 < 0 )
  {
    v4 = v32->m_pp;
    v5 = v32->m_pp;
    v18 = v32->m_pp->nMinAF;
    v17 = v4->nMaxAF;
    v16 = v5->nMastery;
    v15 = v22;
    v14 = v21;
    CLogFile::Write(
      &stru_1799C8E78,
      "Skill Attack Error : Skill(%s), SIndex(%d), l_fConst(%f), l_nLvConst(%d), nMastery(%d), nMaxAF(%d), nMinAF(%d)",
      v19->m_strCode,
      v19->m_dwIndex);
    v23 = 1;
    v24 = 1;
  }
  v25 = (signed int)floor((double)((v24 + 125) / (v24 + 50) * v24) + 0.5);
  if ( v32->m_pp->nMaxAttackPnt <= 0 )
  {
    if ( v32->m_pp->nMaxAttackPnt >= 0 )
    {
      v26 = (signed int)floor((float)((float)(v24 + v23) / 2.0) + 0.5);
      v27 = _100_per_random_table::GetRand(&v32->m_pAttChar->m_rtPer100);
      v30 = (float)v32->m_pp->nMinSel;
      v7 = v30;
      _effect_parameter::GetEff_Plus(&v32->m_pAttChar->m_EP, 14);
      v8 = v30 - v7;
      v28 = (signed int)ffloor(v8);
      if ( v32->m_pp->pDst && v32->m_pp->pDst != v32->m_pAttChar )
      {
        _effect_parameter::GetEff_Plus(&v32->m_pp->pDst->m_EP, 37);
        v28 = (signed int)ffloor((float)v28 + v8);
        v9 = CAttack::MonsterCritical_Exception_Rate((CAttack *)&v32->m_pp, v32->m_pp->pDst, v32->m_pp->bBackAttack);
        v28 += v9;
      }
      if ( v28 < 0 )
        v28 = 0;
      v31 = (float)(v32->m_pp->nMaxSel + v32->m_pp->nMinSel);
      v10 = v31;
      _effect_parameter::GetEff_Plus(&v32->m_pAttChar->m_EP, 14);
      v11 = v31 - v10;
      v29 = (signed int)ffloor(v31 - v10);
      if ( v32->m_pp->pDst && v32->m_pp->pDst != v32->m_pAttChar )
      {
        _effect_parameter::GetEff_Plus(&v32->m_pp->pDst->m_EP, 37);
        v29 = (signed int)ffloor((float)v29 + v11);
        v12 = CAttack::MonsterCritical_Exception_Rate((CAttack *)&v32->m_pp, v32->m_pp->pDst, v32->m_pp->bBackAttack);
        v29 += v12;
      }
      if ( v29 < 0 )
        v29 = 0;
      if ( v27 >= v28 )
      {
        if ( v27 >= v29 )
        {
          v32->m_bIsCrtAtt = 1;
          result = (unsigned int)v25;
        }
        else if ( v24 - v26 <= 0 )
        {
          result = (unsigned int)v26;
        }
        else
        {
          result = (unsigned int)(rand() % (v24 - v26) + v26);
        }
      }
      else if ( v26 - v23 <= 0 )
      {
        result = (unsigned int)v23;
      }
      else
      {
        result = (unsigned int)(rand() % (v26 - v23) + v23);
      }
    }
    else
    {
      result = (unsigned int)v23;
    }
  }
  else
  {
    result = (unsigned int)v25;
  }
  return result;
}
