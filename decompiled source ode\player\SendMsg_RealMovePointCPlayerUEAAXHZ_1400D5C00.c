/*
 * Function: ?SendMsg_RealMovePoint@CPlayer@@UEAAXH@Z
 * Address: 0x1400D5C00
 */

void __usercall CPlayer::SendMsg_RealMovePoint(CPlayer *this@<rcx>, int n@<edx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-A8h]@1
  char szMsg[2]; // [sp+38h] [bp-70h]@6
  unsigned int v7; // [sp+3Ah] [bp-6Eh]@6
  __int16 v8; // [sp+3Eh] [bp-6Ah]@6
  char v9; // [sp+40h] [bp-68h]@6
  __int16 pShort; // [sp+41h] [bp-67h]@6
  __int16 v11; // [sp+47h] [bp-61h]@6
  __int16 v12; // [sp+49h] [bp-5Fh]@6
  unsigned __int16 v13; // [sp+4Bh] [bp-5Dh]@6
  unsigned __int64 v14; // [sp+4Dh] [bp-5Bh]@6
  __int16 v15; // [sp+55h] [bp-53h]@6
  char v16; // [sp+57h] [bp-51h]@6
  char v17; // [sp+58h] [bp-50h]@6
  char pbyType; // [sp+74h] [bp-34h]@6
  char v19; // [sp+75h] [bp-33h]@6
  unsigned __int64 v20; // [sp+90h] [bp-18h]@4
  CPlayer *v21; // [sp+B0h] [bp+8h]@1
  int dwClientIndex; // [sp+B8h] [bp+10h]@1

  dwClientIndex = n;
  v21 = this;
  v3 = &v5;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v20 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( !v21->m_bObserver || *(&g_Player.m_byUserDgr + 50856 * n) )
  {
    v9 = CPlayerDB::GetRaceSexCode(&v21->m_Param);
    *(_WORD *)szMsg = v21->m_ObjID.m_wIndex;
    v7 = v21->m_dwObjSerial;
    v8 = CPlayer::GetVisualVer(v21);
    FloatToShort(v21->m_fCurPos, &pShort, 3);
    v11 = (signed int)ffloor(v21->m_fTarPos[0]);
    v12 = (signed int)ffloor(v21->m_fTarPos[2]);
    v13 = v21->m_wLastContEffect;
    v14 = CPlayer::GetStateFlag(v21);
    CPlayer::GetAddSpeed(v21);
    v15 = (signed int)ffloor(a3);
    v16 = v21->m_byMoveDirect;
    v17 = v21->m_byGuildBattleColorInx;
    pbyType = 4;
    v19 = 21;
    CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, szMsg, 0x21u);
  }
}
