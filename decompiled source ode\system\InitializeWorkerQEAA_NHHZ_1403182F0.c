/*
 * Function: ?Initialize@Worker@@QEAA_NHH@Z
 * Address: 0x1403182F0
 */

char __fastcall Worker::Initialize(Worker *this, int nLoop, int nTerm)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // rax@5
  char result; // al@8
  __int64 v7; // [sp+0h] [bp-58h]@1
  TaskPool::RCODE v8; // [sp+20h] [bp-38h]@9
  TaskPool *v9; // [sp+28h] [bp-30h]@7
  TaskPool *v10; // [sp+30h] [bp-28h]@4
  __int64 v11; // [sp+38h] [bp-20h]@4
  TaskPool *v12; // [sp+40h] [bp-18h]@5
  Worker *ArgList; // [sp+60h] [bp+8h]@1

  ArgList = this;
  v3 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = -2i64;
  ArgList->_nLoop = nLoop;
  ArgList->_nTerm = nTerm;
  v10 = (TaskPool *)operator new(0x1F8ui64);
  if ( v10 )
  {
    TaskPool::TaskPool(v10);
    v12 = (TaskPool *)v5;
  }
  else
  {
    v12 = 0i64;
  }
  v9 = v12;
  ArgList->_pkPool = v12;
  if ( ArgList->_pkPool )
  {
    v8 = TaskPool::Initialize(ArgList->_pkPool, ArgList->_nMaxTskNum, ArgList->_nMaxTskBufSize);
    if ( v8 )
    {
      result = 0;
    }
    else
    {
      ArgList->_bRun = 1;
      ArgList->_hRunEvent = CreateEventA(0i64, 0, 0, 0i64);
      _beginthread((void (__cdecl *)(void *))Worker::s_loop, 0, ArgList);
      ArgList->_bInit = 1;
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
