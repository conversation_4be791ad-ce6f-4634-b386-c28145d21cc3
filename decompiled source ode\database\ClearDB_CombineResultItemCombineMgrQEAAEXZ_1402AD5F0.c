/*
 * Function: ?ClearDB_CombineResult@ItemCombineMgr@@QEAAEXZ
 * Address: 0x1402AD5F0
 */

char __fastcall ItemCombineMgr::ClearDB_CombineResult(ItemCombineMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  ItemCombineMgr *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = 0;
  _ITEMCOMBINE_DB_BASE::Init(&v6->m_pMaster->m_Param.m_ItemCombineDB);
  CUserDB::Update_CombineExResult_Pop(v6->m_pMaster->m_pUserDB);
  return v5;
}
