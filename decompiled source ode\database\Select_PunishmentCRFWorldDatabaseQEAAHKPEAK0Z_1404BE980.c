/*
 * Function: ?Select_Punishment@CRFWorldDatabase@@QEAAHKPEAK0@Z
 * Address: 0x1404BE980
 */

signed __int64 __fastcall CRFWorldDatabase::Select_Punishment(CRFWorldDatabase *this, unsigned int dwSerial, unsigned int *dwESerial, unsigned int *dwValue)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  PatriarchElectProcessor *v6; // rax@4
  unsigned int v7; // eax@4
  signed __int64 result; // rax@8
  __int64 v9; // [sp+0h] [bp-1A8h]@1
  void *SQLStmt; // [sp+20h] [bp-188h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-180h]@22
  SQLLEN v12; // [sp+38h] [bp-170h]@22
  __int16 v13; // [sp+44h] [bp-164h]@9
  char Dest; // [sp+60h] [bp-148h]@4
  unsigned __int8 v15; // [sp+164h] [bp-44h]@16
  unsigned __int8 TargetValue; // [sp+174h] [bp-34h]@22
  unsigned __int8 v17; // [sp+184h] [bp-24h]@26
  unsigned __int64 v18; // [sp+190h] [bp-18h]@4
  CRFWorldDatabase *v19; // [sp+1B0h] [bp+8h]@1
  unsigned int v20; // [sp+1B8h] [bp+10h]@1
  unsigned int *v21; // [sp+1C0h] [bp+18h]@1
  unsigned int *v22; // [sp+1C8h] [bp+20h]@1

  v22 = dwValue;
  v21 = dwESerial;
  v20 = dwSerial;
  v19 = this;
  v4 = &v9;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v18 = (unsigned __int64)&v9 ^ _security_cookie;
  v6 = PatriarchElectProcessor::Instance();
  v7 = PatriarchElectProcessor::GetCurrPatriarchElectSerial(v6);
  sprintf(&Dest, "{ CALL pSelect_Punishment( %d, %d ) }", v20, v7);
  if ( v19->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v19->vfptr, &Dest);
  if ( v19->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v19->vfptr) )
  {
    v13 = SQLExecDirectA_0(v19->m_hStmtSelect, &Dest, -3);
    if ( v13 && v13 != 1 )
    {
      if ( v13 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v19->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v19->vfptr, v13, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v19->vfptr, v13, v19->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      do
      {
        v13 = SQLFetch_0(v19->m_hStmtSelect);
        if ( v13 && v13 != 1 )
        {
          v15 = 0;
          if ( v13 == 100 )
          {
            v15 = 2;
          }
          else
          {
            SQLStmt = v19->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v19->vfptr, v13, &Dest, "SQLFetch", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v19->vfptr, v13, v19->m_hStmtSelect);
            v15 = 1;
          }
          if ( v19->m_hStmtSelect )
            SQLCloseCursor_0(v19->m_hStmtSelect);
          return v15;
        }
        StrLen_or_IndPtr = &v12;
        SQLStmt = 0i64;
        v13 = SQLGetData_0(v19->m_hStmtSelect, 1u, -6, &TargetValue, 0i64, &v12);
        if ( (signed int)TargetValue <= 2 )
        {
          StrLen_or_IndPtr = &v12;
          SQLStmt = 0i64;
          v13 = SQLGetData_0(v19->m_hStmtSelect, 2u, -18, &v22[TargetValue], 0i64, &v12);
          StrLen_or_IndPtr = &v12;
          SQLStmt = 0i64;
          v13 = SQLGetData_0(v19->m_hStmtSelect, 3u, 4, &v21[TargetValue], 0i64, &v12);
        }
      }
      while ( !v13 || v13 == 1 );
      v17 = 0;
      if ( v13 == 100 )
      {
        v17 = 2;
      }
      else
      {
        SQLStmt = v19->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v19->vfptr, v13, &Dest, "SQLFetch", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v19->vfptr, v13, v19->m_hStmtSelect);
        v17 = 1;
      }
      if ( v19->m_hStmtSelect )
        SQLCloseCursor_0(v19->m_hStmtSelect);
      result = v17;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v19->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1i64;
  }
  return result;
}
