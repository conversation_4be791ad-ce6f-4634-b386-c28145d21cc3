/*
 * Function: j_??1?$_Ranit@PEAVCUnmannedTraderClassInfo@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@XZ
 * Address: 0x140012369
 */

void __fastcall std::_Ranit<CUnmannedTraderClassInfo *,__int64,CUnmannedTraderClassInfo * const *,CUnmannedTraderClassInfo * const &>::~_<PERSON>t<CUnmannedTraderClassInfo *,__int64,CUnmannedTraderClassInfo * const *,CUnmannedTraderClassInfo * const &>(std::_Ranit<CUnmannedTraderClassInfo *,__int64,CUnmannedTraderClassInfo * const *,CUnmannedTraderClassInfo * const &> *this)
{
  std::_Ranit<CUnmannedTraderClassInfo *,__int64,CUnmannedTraderClassInfo * const *,CUnmannedTraderClassInfo * const &>::~_Ranit<CUnmannedTraderClassInfo *,__int64,CUnmannedTraderClassInfo * const *,CUnmannedTraderClassInfo * const &>(this);
}
