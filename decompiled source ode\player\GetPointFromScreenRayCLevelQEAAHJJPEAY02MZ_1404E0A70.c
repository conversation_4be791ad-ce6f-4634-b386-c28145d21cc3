/*
 * Function: ?GetPointFromScreenRay@CLevel@@QEAAHJJPEAY02M@Z
 * Address: 0x1404E0A70
 */

__int64 __fastcall CLevel::GetPointFromScreenRay(CLevel *this, int a2, int a3, float (*a4)[3])
{
  float v5; // [sp+30h] [bp-18h]@1

  return CBsp::GetPointFromScreenRay(
           this->mBsp,
           (float)(a2 - dword_184A79BA8),
           (float)(a3 - dword_184A79BAC),
           (float *const )a4,
           &v5);
}
