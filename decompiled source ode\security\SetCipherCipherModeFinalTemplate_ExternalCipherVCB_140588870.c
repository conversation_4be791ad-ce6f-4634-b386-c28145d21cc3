/*
 * Function: ?SetCipher@?$CipherModeFinalTemplate_ExternalCipher@VCBC_Decryption@CryptoPP@@@CryptoPP@@QEAAXAEAV?$SimpleKeyedTransformation@VBlockTransformation@CryptoPP@@@2@@Z
 * Address: 0x140588870
 */

int __fastcall CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_Decryption>::SetCipher(__int64 a1, __int64 a2)
{
  __int64 v3; // [sp+30h] [bp+8h]@1
  __int64 v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::SimpleKeyingInterface::ThrowIfResynchronizable((CryptoPP::SimpleKeyingInterface *)(a1 + 8));
  *(_QWORD *)(v3 + 16) = v4;
  return (*(int (__fastcall **)(__int64))(*(_QWORD *)v3 + 120i64))(v3);
}
