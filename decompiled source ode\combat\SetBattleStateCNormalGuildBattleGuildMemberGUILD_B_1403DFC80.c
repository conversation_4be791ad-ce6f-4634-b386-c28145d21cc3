/*
 * Function: ?SetBattleState@CNormalGuildBattleGuildMember@GUILD_BATTLE@@QEAAX_NE@Z
 * Address: 0x1403DFC80
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuildMember::SetBattleState(GUILD_BATTLE::CNormalGuildBattleGuildMember *this, bool bFlag, char byColorInx)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CPlayer::pc_SetInGuildBattle(v6->m_pkMember->pPlayer, bFlag, byColorInx);
}
