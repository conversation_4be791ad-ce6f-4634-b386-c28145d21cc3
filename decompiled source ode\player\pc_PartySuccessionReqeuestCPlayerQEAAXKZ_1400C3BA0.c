/*
 * Function: ?pc_PartySuccessionReqeuest@CPlayer@@QEAAXK@Z
 * Address: 0x1400C3BA0
 */

void __fastcall CPlayer::pc_PartySuccessionReqeuest(CPlayer *this, unsigned int dwSuccessorSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  CPartyPlayer *v5; // [sp+20h] [bp-18h]@6
  CPlayer *v6; // [sp+40h] [bp+8h]@1
  unsigned int dwWorldSerial; // [sp+48h] [bp+10h]@1

  dwWorldSerial = dwSuccessorSerial;
  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CPartyPlayer::IsPartyBoss(v6->m_pPartyMgr) )
  {
    v5 = CPartyPlayer::GetPtrFromSerial(v6->m_pPartyMgr, dwWorldSerial);
    if ( v5 )
      wa_PartySuccession(&v6->m_id, &v5->m_id);
    else
      CPlayer::SendMsg_PartySuccessResult(v6, 0i64);
  }
  else
  {
    CPlayer::SendMsg_PartySuccessResult(v6, 0i64);
  }
}
