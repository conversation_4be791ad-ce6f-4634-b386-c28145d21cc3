/*
 * Function: ??1CReservedGuildScheduleMapGroup@GUILD_BATTLE@@QEAA@XZ
 * Address: 0x1403CC3C0
 */

void __fastcall GUILD_BATTLE::CReservedGuildScheduleMapGroup::~CReservedGuildScheduleMapGroup(GUILD_BATTLE::CReservedGuildScheduleMapGroup *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CReservedGuildScheduleMapGroup *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  `eh vector destructor iterator'(
    v4->m_k<PERSON><PERSON>,
    0x38ui64,
    6,
    (void (__cdecl *)(void *))GUILD_BATTLE::CReservedGuildSchedulePage::~CReservedGuildSchedulePage);
}
