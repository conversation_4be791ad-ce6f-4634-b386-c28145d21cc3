/*
 * Function: ?Select_TrunkMoney@CRFWorldDatabase@@QEAA_NKPEAN@Z
 * Address: 0x14049F9F0
 */

char __fastcall CRFWorldDatabase::Select_TrunkMoney(CRFWorldDatabase *this, unsigned int dwSerial, long double *pVal)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@15
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@23
  SQLLEN v9; // [sp+38h] [bp-150h]@23
  __int16 v10; // [sp+44h] [bp-144h]@11
  char Dest; // [sp+60h] [bp-128h]@6
  unsigned __int64 v12; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v13; // [sp+190h] [bp+8h]@1
  long double *TargetValue; // [sp+1A0h] [bp+18h]@1

  TargetValue = pVal;
  v13 = this;
  v3 = &v6;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( pVal )
  {
    sprintf(&Dest, "{ CALL pSelect_TrunkMoney( %u ) }", dwSerial);
    if ( v13->m_bSaveDBLog )
      CRFNewDatabase::Log((CRFNewDatabase *)&v13->vfptr, &Dest);
    if ( v13->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v13->vfptr) )
    {
      v10 = SQLExecDirect_0(v13->m_hStmtSelect, &Dest, -3);
      if ( v10 && v10 != 1 )
      {
        if ( v10 == 100 )
        {
          result = 0;
        }
        else
        {
          SQLStmt = v13->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v10, &Dest, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v10, v13->m_hStmtSelect);
          result = 0;
        }
      }
      else
      {
        v10 = SQLFetch_0(v13->m_hStmtSelect);
        if ( v10 && v10 != 1 )
        {
          if ( v10 != 100 )
          {
            SQLStmt = v13->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v10, &Dest, "SQLFetch", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v10, v13->m_hStmtSelect);
          }
          if ( v13->m_hStmtSelect )
            SQLCloseCursor_0(v13->m_hStmtSelect);
          result = 0;
        }
        else
        {
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v13->m_hStmtSelect, 1u, 8, TargetValue, 0i64, &v9);
          if ( v10 && v10 != 1 )
          {
            SQLStmt = v13->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v10, &Dest, "SQLGetData", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v10, v13->m_hStmtSelect);
            if ( v13->m_hStmtSelect )
              SQLCloseCursor_0(v13->m_hStmtSelect);
            result = 0;
          }
          else
          {
            StrLen_or_IndPtr = &v9;
            SQLStmt = 0i64;
            v10 = SQLGetData_0(v13->m_hStmtSelect, 2u, 8, TargetValue + 1, 0i64, &v9);
            if ( v10 && v10 != 1 )
            {
              SQLStmt = v13->m_hStmtSelect;
              CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v10, &Dest, "SQLGetData", SQLStmt);
              CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v10, v13->m_hStmtSelect);
              if ( v13->m_hStmtSelect )
                SQLCloseCursor_0(v13->m_hStmtSelect);
              result = 0;
            }
            else
            {
              StrLen_or_IndPtr = &v9;
              SQLStmt = 0i64;
              v10 = SQLGetData_0(v13->m_hStmtSelect, 3u, 8, TargetValue + 2, 0i64, &v9);
              if ( v10 && v10 != 1 )
              {
                SQLStmt = v13->m_hStmtSelect;
                CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v10, &Dest, "SQLGetData", SQLStmt);
                CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v10, v13->m_hStmtSelect);
                if ( v13->m_hStmtSelect )
                  SQLCloseCursor_0(v13->m_hStmtSelect);
                result = 0;
              }
              else
              {
                StrLen_or_IndPtr = &v9;
                SQLStmt = 0i64;
                v10 = SQLGetData_0(v13->m_hStmtSelect, 4u, 8, TargetValue + 3, 0i64, &v9);
                if ( v10 && v10 != 1 )
                {
                  SQLStmt = v13->m_hStmtSelect;
                  CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v10, &Dest, "SQLGetData", SQLStmt);
                  CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v10, v13->m_hStmtSelect);
                  if ( v13->m_hStmtSelect )
                    SQLCloseCursor_0(v13->m_hStmtSelect);
                  result = 0;
                }
                else
                {
                  StrLen_or_IndPtr = &v9;
                  SQLStmt = 0i64;
                  v10 = SQLGetData_0(v13->m_hStmtSelect, 5u, 8, TargetValue + 4, 0i64, &v9);
                  if ( v10 && v10 != 1 )
                  {
                    SQLStmt = v13->m_hStmtSelect;
                    CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v10, &Dest, "SQLGetData", SQLStmt);
                    CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v10, v13->m_hStmtSelect);
                    if ( v13->m_hStmtSelect )
                      SQLCloseCursor_0(v13->m_hStmtSelect);
                    result = 0;
                  }
                  else
                  {
                    StrLen_or_IndPtr = &v9;
                    SQLStmt = 0i64;
                    v10 = SQLGetData_0(v13->m_hStmtSelect, 6u, 8, TargetValue + 5, 0i64, &v9);
                    if ( v10 && v10 != 1 )
                    {
                      SQLStmt = v13->m_hStmtSelect;
                      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v10, &Dest, "SQLGetData", SQLStmt);
                      CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v10, v13->m_hStmtSelect);
                      if ( v13->m_hStmtSelect )
                        SQLCloseCursor_0(v13->m_hStmtSelect);
                      result = 0;
                    }
                    else
                    {
                      if ( v13->m_hStmtSelect )
                        SQLCloseCursor_0(v13->m_hStmtSelect);
                      if ( v13->m_bSaveDBLog )
                        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v13->vfptr, "%s Success", &Dest);
                      result = 1;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    else
    {
      CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v13->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
