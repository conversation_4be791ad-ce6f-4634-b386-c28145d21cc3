/*
 * Function: ?GetControlLeftTime@CHolyStoneSystem@@QEAAHXZ
 * Address: 0x14027E690
 */

__int64 __fastcall CHolyStoneSystem::GetControlLeftTime(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@6
  CHolyStoneSystem *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CHolyStoneSystem::GetHolyMasterRace(v6) == -1 )
  {
    v5 = CHolyStoneSystem::GetSceneCode(v6);
    if ( v5 != 2 && v5 != 3 && v5 != 4 || v6->m_dwCheckTime[5] <= GetLoopTime() )
      result = 0i64;
    else
      result = v6->m_dwCheckTime[5] - GetLoopTime();
  }
  else
  {
    result = 0i64;
  }
  return result;
}
