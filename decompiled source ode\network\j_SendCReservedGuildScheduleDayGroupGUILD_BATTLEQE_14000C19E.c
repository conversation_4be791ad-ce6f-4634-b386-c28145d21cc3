/*
 * Function: j_?Send@CReservedGuildScheduleDayGroup@GUILD_BATTLE@@QEAAXEIHKEK@Z
 * Address: 0x14000C19E
 */

void __fastcall GUILD_BATTLE::CReservedGuildScheduleDayGroup::Send(GUILD_BATTLE::CReservedGuildScheduleDayGroup *this, char byDayID, unsigned int uiMapID, int n, unsigned int dwVer, char byPage, unsigned int dwGuildSerial)
{
  GUILD_BATTLE::CReservedGuildScheduleDayGroup::Send(this, byDayID, uiMapID, n, dwVer, byPage, dwGuildSerial);
}
