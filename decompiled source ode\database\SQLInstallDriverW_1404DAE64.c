/*
 * Function: SQLInstallDriverW
 * Address: 0x1404DAE64
 */

int __fastcall SQLInstallDriverW(const unsigned __int16 *lpszInfFile, const unsigned __int16 *lpszDriver, unsigned __int16 *lpszPath, unsigned __int16 cbPathMax, unsigned __int16 *pcbPathOut)
{
  const unsigned __int16 *v5; // rbp@1
  unsigned __int16 v6; // bx@1
  unsigned __int16 *v7; // rdi@1
  const unsigned __int16 *v8; // rsi@1
  __int64 (__cdecl *v9)(); // rax@1
  int result; // eax@2

  v5 = lpszInfFile;
  v6 = cbPathMax;
  v7 = lpszPath;
  v8 = lpszDriver;
  v9 = ODBC___GetSetupProc("SQLInstallDriverW");
  if ( v9 )
    result = ((int (__fastcall *)(const unsigned __int16 *, const unsigned __int16 *, unsigned __int16 *, _QWORD))v9)(
               v5,
               v8,
               v7,
               v6);
  else
    result = 0;
  return result;
}
