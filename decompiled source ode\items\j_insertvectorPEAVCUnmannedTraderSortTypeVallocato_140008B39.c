/*
 * Function: j_?insert@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEAA?AV?$_Vector_iterator@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@2@V32@AEBQEAVCUnmannedTraderSortType@@@Z
 * Address: 0x140008B39
 */

std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *__fastcall std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::insert(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *result, std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *_Where, CUnmannedTraderSortType *const *_Val)
{
  return std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::insert(
           this,
           result,
           _Where,
           _Val);
}
