/*
 * Function: ?Update@MonsterSFContDamageToleracne@@QEAAXXZ
 * Address: 0x140158080
 */

void __fastcall MonsterSFContDamageToleracne::Update(MonsterSFContDamageToleracne *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int v4; // [sp+20h] [bp-18h]@7
  unsigned int v5; // [sp+24h] [bp-14h]@7
  MonsterSFContDamageToleracne *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_pMonster && v6->m_pMonster->m_bLive )
  {
    v4 = GetLoopTime();
    v5 = 0;
    if ( CMonster::GetMyDMGSFContCount(v6->m_pMonster) <= 0 )
    {
      if ( v6->m_dwLastUpdateTime < v4 )
        v5 = GetLoopTime() - v6->m_dwLastUpdateTime;
      else
        v5 = 0;
      if ( v5 >= 0x3E8 )
      {
        MonsterSFContDamageToleracne::SetSFDamageToleracne_Variation(v6, 0.02);
        v6->m_dwLastUpdateTime = v4;
      }
    }
    else
    {
      v6->m_dwLastUpdateTime = v4;
    }
  }
}
