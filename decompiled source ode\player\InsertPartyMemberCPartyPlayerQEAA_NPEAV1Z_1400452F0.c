/*
 * Function: ?InsertPartyMember@CPartyPlayer@@QEAA_NPEAV1@@Z
 * Address: 0x1400452F0
 */

char __fastcall CPartyPlayer::InsertPartyMember(CPartyPlayer *this, CPartyPlayer *pJoiner)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@6
  CPartyPlayer *v7; // [sp+40h] [bp+8h]@1
  CPartyPlayer *v8; // [sp+48h] [bp+10h]@1

  v8 = pJoiner;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CPartyPlayer::IsPartyBoss(v7) )
  {
    for ( j = 0; j < 8; ++j )
    {
      if ( !v7->m_pPartyMember[j] )
      {
        v7->m_pPartyMember[j] = v8;
        v8->m_pPartyBoss = v7;
        return 1;
      }
    }
    result = 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
