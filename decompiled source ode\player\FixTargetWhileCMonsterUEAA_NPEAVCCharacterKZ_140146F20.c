/*
 * Function: ?FixTargetWhile@CMonster@@UEAA_NPEAVCCharacter@@K@Z
 * Address: 0x140146F20
 */

char __usercall CMonster::FixTargetWhile@<al>(CMonster *this@<rcx>, CCharacter *pkTarget@<rdx>, unsigned int dwMiliSecond@<r8d>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  CMonster *v8; // [sp+50h] [bp+8h]@1
  CCharacter *pCharacter; // [sp+58h] [bp+10h]@1

  pCharacter = pkTarget;
  v8 = this;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pkTarget )
  {
    if ( v8->m_bMove )
    {
      CMonster::GetMoveSpeed(v8);
      CCharacter::MoveBreak((CCharacter *)&v8->vfptr, a4);
      CCharacter::Stop((CCharacter *)&v8->vfptr);
      CGameObject::SendMsg_BreakStop((CGameObject *)&v8->vfptr);
    }
    CMonsterAggroMgr::SetAggro(&v8->m_AggroMgr, pCharacter, 0, -2, 0, 0, 0);
    CMonsterAggroMgr::SetTopAggroCharacter(&v8->m_AggroMgr, pCharacter);
    CMonster::CheckEventEmotionPresentation(v8, 7, pCharacter);
    Us_HFSM::SendExternMsg((Us_HFSM *)&v8->m_AI.vfptr, 0, pCharacter, 1);
    CMonster::SetAttackTarget(v8, pCharacter);
    CMonsterAggroMgr::ShortRankDelay(&v8->m_AggroMgr, 0x1388u);
  }
  return 1;
}
