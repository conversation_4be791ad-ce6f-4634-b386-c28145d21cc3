/*
 * Function: ?IsItemEquipCivil@@YAHHHE@Z
 * Address: 0x14003BDB0
 */

signed __int64 __fastcall IsItemEquipCivil(int nTableCode, int nItemIndex, char byRaceSex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  CRecordData *v7; // [sp+20h] [bp-18h]@4
  char *v8; // [sp+28h] [bp-10h]@6
  int nTableCodea; // [sp+40h] [bp+8h]@1
  char v10; // [sp+50h] [bp+18h]@1

  v10 = byRaceSex;
  nTableCodea = nTableCode;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = &s_ptblItemData[nTableCodea];
  if ( (signed int)(unsigned __int8)byRaceSex <= 5 )
  {
    v8 = GetItemEquipCivil(nTableCodea, nItemIndex);
    if ( v8 )
      result = v8[(unsigned __int8)v10] == 49;
    else
      result = 1i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
