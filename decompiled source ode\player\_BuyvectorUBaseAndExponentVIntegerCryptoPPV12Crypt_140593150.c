/*
 * Function: ?_Buy@?$vector@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@IEAA_N_K@Z
 * Address: 0x140593150
 */

char __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_Buy(__int64 a1, unsigned __int64 a2)
{
  char result; // al@2
  unsigned __int64 v3; // rax@3
  __int64 v4; // rax@5
  __int64 v5; // [sp+30h] [bp+8h]@1
  unsigned __int64 v6; // [sp+38h] [bp+10h]@1

  v6 = a2;
  v5 = a1;
  *(_QWORD *)(a1 + 16) = 0i64;
  *(_QWORD *)(a1 + 24) = 0i64;
  *(_QWORD *)(a1 + 32) = 0i64;
  if ( a2 )
  {
    LODWORD(v3) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::max_size(a1);
    if ( v3 < v6 )
      std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_Xlen();
    LODWORD(v4) = std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>::allocate(v5 + 8, v6);
    *(_QWORD *)(v5 + 16) = v4;
    *(_QWORD *)(v5 + 24) = *(_QWORD *)(v5 + 16);
    *(_QWORD *)(v5 + 32) = *(_QWORD *)(v5 + 16) + 80 * v6;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
