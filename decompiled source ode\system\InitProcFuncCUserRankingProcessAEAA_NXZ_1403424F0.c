/*
 * Function: ?InitProcFunc@CUserRankingProcess@@AEAA_NXZ
 * Address: 0x1403424F0
 */

char __fastcall CUserRankingProcess::InitProcFunc(CUserRankingProcess *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  void (__cdecl *_Val)(CUserRankingProcess *); // [sp+20h] [bp-18h]@4
  CUserRankingProcess *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _Val = 0i64;
  std::vector<void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>::assign(
    &v6->m_vecProc,
    0xAui64,
    &_Val);
  if ( std::vector<void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>::size(&v6->m_vecProc) == 10 )
  {
    *std::vector<void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>::operator[](
       &v6->m_vecProc,
       0i64) = (void (__cdecl *)(CUserRankingProcess *))CUserRankingProcess::ProcWait;
    *std::vector<void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>::operator[](
       &v6->m_vecProc,
       1ui64) = (void (__cdecl *)(CUserRankingProcess *))CUserRankingProcess::ProcSaveTargetList;
    *std::vector<void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>::operator[](
       &v6->m_vecProc,
       2ui64) = (void (__cdecl *)(CUserRankingProcess *))CUserRankingProcess::ProcRankStart;
    *std::vector<void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>::operator[](
       &v6->m_vecProc,
       3ui64) = (void (__cdecl *)(CUserRankingProcess *))CUserRankingProcess::ProcRankComplete;
    *std::vector<void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>::operator[](
       &v6->m_vecProc,
       4ui64) = (void (__cdecl *)(CUserRankingProcess *))CUserRankingProcess::ProcWaitDayChanged;
    *std::vector<void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>::operator[](
       &v6->m_vecProc,
       5ui64) = (void (__cdecl *)(CUserRankingProcess *))CUserRankingProcess::ProcNotifyVersionUp;
    *std::vector<void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>::operator[](
       &v6->m_vecProc,
       6ui64) = (void (__cdecl *)(CUserRankingProcess *))CUserRankingProcess::ProcApplyGuildGrade;
    *std::vector<void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>::operator[](
       &v6->m_vecProc,
       7ui64) = (void (__cdecl *)(CUserRankingProcess *))CUserRankingProcess::ProcApplyRankInGuild;
    *std::vector<void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>::operator[](
       &v6->m_vecProc,
       8ui64) = (void (__cdecl *)(CUserRankingProcess *))CUserRankingProcess::ProcFailedWait;
    *std::vector<void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>::operator[](
       &v6->m_vecProc,
       9ui64) = (void (__cdecl *)(CUserRankingProcess *))CUserRankingProcess::ProcRankSuccess;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
