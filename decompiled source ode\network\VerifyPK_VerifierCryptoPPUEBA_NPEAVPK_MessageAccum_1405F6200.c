/*
 * Function: ?Verify@PK_Verifier@CryptoPP@@UEBA_NPEAVPK_MessageAccumulator@2@@Z
 * Address: 0x1405F6200
 */

char __fastcall CryptoPP::PK_Verifier::Verify(CryptoPP::PK_Verifier *this, struct CryptoPP::PK_MessageAccumulator *a2)
{
  __int64 v2; // rax@1
  char v4; // [sp+20h] [bp-28h]@1
  char v5; // [sp+28h] [bp-20h]@1
  __int64 v6; // [sp+30h] [bp-18h]@1
  CryptoPP::PK_SignatureSchemeVtbl *v7; // [sp+38h] [bp-10h]@1
  CryptoPP::PK_Verifier *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v6 = -2i64;
  std::auto_ptr<CryptoPP::PK_MessageAccumulator>::auto_ptr<CryptoPP::PK_MessageAccumulator>(&v4, a2);
  LODWORD(v2) = std::auto_ptr<CryptoPP::PK_MessageAccumulator>::operator*(&v4);
  v7 = v8->vfptr;
  v5 = ((int (__fastcall *)(CryptoPP::PK_Verifier *, __int64))v7[1].MaxRecoverableLength)(v8, v2);
  std::auto_ptr<CryptoPP::PK_MessageAccumulator>::~auto_ptr<CryptoPP::PK_MessageAccumulator>(&v4);
  return v5;
}
