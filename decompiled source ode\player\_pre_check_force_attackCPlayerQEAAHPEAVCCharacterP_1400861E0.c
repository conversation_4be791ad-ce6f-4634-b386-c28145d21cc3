/*
 * Function: ?_pre_check_force_attack@CPlayer@@QEAAHPEAVCCharacter@@PEAMGPEAPEAU_force_fld@@PEAPEAU_db_con@_STORAGE_LIST@@PEAGG3PEAPEAU_BulletItem_fld@@@Z
 * Address: 0x1400861E0
 */

signed __int64 __usercall CPlayer::_pre_check_force_attack@<rax>(CPlayer *this@<rcx>, CCharacter *pDst@<rdx>, float *pfTarPos@<r8>, unsigned __int16 wForceItemSerial@<r9w>, float a5@<xmm0>, _force_fld **ppForceFld, _STORAGE_LIST::_db_con **ppForceItem, unsigned __int16 *pdwDecPoint, unsigned __int16 wEffBtSerial, _STORAGE_LIST::_db_con **ppEffBtProp, _BulletItem_fld **ppfldEffBt)
{
  __int64 *v11; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v14; // rdx@44
  int v15; // eax@81
  float v16; // xmm0_4@81
  float v17; // xmm0_4@81
  float v18; // xmm1_4@81
  __int64 v19; // [sp+0h] [bp-68h]@1
  _base_fld *pSFFld; // [sp+20h] [bp-48h]@4
  _STORAGE_LIST::_db_con *v21; // [sp+28h] [bp-40h]@4
  _STORAGE_LIST::_db_con *v22; // [sp+30h] [bp-38h]@4
  _BulletItem_fld *v23; // [sp+38h] [bp-30h]@4
  char v24; // [sp+40h] [bp-28h]@20
  int v25; // [sp+44h] [bp-24h]@44
  int v26; // [sp+48h] [bp-20h]@81
  int v27; // [sp+4Ch] [bp-1Ch]@86
  float v28; // [sp+50h] [bp-18h]@81
  float v29; // [sp+54h] [bp-14h]@81
  CPlayer *v30; // [sp+70h] [bp+8h]@1
  CCharacter *v31; // [sp+78h] [bp+10h]@1
  float *fPos; // [sp+80h] [bp+18h]@1
  unsigned __int16 v33; // [sp+88h] [bp+20h]@1

  v33 = wForceItemSerial;
  fPos = pfTarPos;
  v31 = pDst;
  v30 = this;
  v11 = &v19;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v11 = -858993460;
    v11 = (__int64 *)((char *)v11 + 4);
  }
  pSFFld = 0i64;
  v21 = 0i64;
  v22 = 0i64;
  v23 = 0i64;
  if ( CPlayer::IsRidingUnit(v30) )
    return 4294967275i64;
  if ( _effect_parameter::GetEff_State(&v30->m_EP, 20) )
    return 4294967259i64;
  if ( _effect_parameter::GetEff_State(&v30->m_EP, 28) )
    return 4294967259i64;
  if ( _effect_parameter::GetEff_State(&v30->m_EP, 21) )
    return 4294967258i64;
  if ( v30->m_byMoveType == 2 )
    return 4294967255i64;
  v21 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v30->m_Param.m_dbForce.m_nListNum, v33);
  if ( !v21 )
    return 4294967280i64;
  if ( v21->m_bLock )
    return 4294967266i64;
  pSFFld = CRecordData::GetRecord(
             &stru_1799C8410 + 1,
             *((_DWORD *)CPlayer::s_pnLinkForceItemToEffect + v21->m_wItemIndex));
  if ( !pSFFld )
    return 4294967280i64;
  v24 = -1;
  if ( *(_DWORD *)&pSFFld[1].m_strCode[4] < 0x18u )
    v24 = pSFFld[1].m_strCode[4];
  if ( !v30->m_bSFDelayNotCheck && !_ATTACK_DELAY_CHECKER::IsDelay(&v30->m_AttDelayChker, 1, pSFFld->m_dwIndex, v24) )
    return 4294967291i64;
  if ( v30->m_pmWpn.byWpType == 11 || v30->m_pmWpn.byWpType == 10 )
    return 4294967287i64;
  if ( *(_DWORD *)&pSFFld[11].m_strCode[4] == 4 || *(_DWORD *)&pSFFld[11].m_strCode[4] == 6 )
    v31 = 0i64;
  if ( CPlayerDB::GetRaceCode(&v30->m_Param) == 2 && CPlayer::IsActingSiegeMode(v30) )
    return 4294967236i64;
  if ( v31 )
  {
    if ( (CPlayer *)v31 == v30 )
      return 4294967290i64;
    if ( !v31->m_bLive
      || v31->m_bCorpse
      || v31->m_pCurMap != v30->m_pCurMap
      || CGameObject::GetCurSecNum((CGameObject *)&v31->vfptr) == -1 )
    {
      return 4294967290i64;
    }
    if ( v31 == (CCharacter *)v30->m_pRecalledAnimusChar )
      return 4294967254i64;
    v25 = CPlayer::_pre_check_in_guild_battle(v30, v31);
    if ( v25 )
      return (unsigned int)v25;
    LOBYTE(v14) = 1;
    if ( !(unsigned __int8)((int (__fastcall *)(CCharacter *, __int64))v31->vfptr->IsBeAttackedAble)(v31, v14) )
      return 4294967290i64;
    if ( !(unsigned __int8)((int (__fastcall *)(CPlayer *))v30->vfptr->IsAttackableInTown)(v30)
      && !(unsigned __int8)((int (__fastcall *)(CCharacter *))v31->vfptr->IsAttackableInTown)(v31)
      && ((unsigned __int8)((int (__fastcall *)(CPlayer *))v30->vfptr->IsInTown)(v30)
       || (unsigned __int8)((int (__fastcall *)(CCharacter *))v31->vfptr->IsInTown)(v31)) )
    {
      return 4294967265i64;
    }
    if ( !(unsigned __int8)((int (__fastcall *)(CCharacter *, CPlayer *))v31->vfptr->IsBeDamagedAble)(v31, v30) )
      return 4294967290i64;
  }
  else if ( *(_DWORD *)&pSFFld[11].m_strCode[4] != 4 && *(_DWORD *)&pSFFld[11].m_strCode[4] != 6 )
  {
    return 4294967290i64;
  }
  if ( !CPlayer::IsSFUsableGauge(v30, 1, pSFFld->m_dwIndex, pdwDecPoint) )
    return 4294967271i64;
  if ( !CPlayer::IsSFUsableSFMastery(v30, 4, *(_DWORD *)&pSFFld[1].m_strCode[4]) )
    return 4294967282i64;
  if ( !CPlayer::IsSFActableByClass(v30, 1, pSFFld) )
    return 4294967272i64;
  if ( _effect_parameter::GetEff_State(&v30->m_EP, 1) )
    return 4294967246i64;
  if ( _effect_parameter::GetEff_State(&v30->m_EP, 20) )
    return 4294967259i64;
  if ( _effect_parameter::GetEff_State(&v30->m_EP, 28) )
    return 4294967259i64;
  if ( _effect_parameter::GetEff_State(&v30->m_EP, 21) )
    return 4294967258i64;
  if ( v30->m_pmWpn.byWpType != 9 )
    return 4294967287i64;
  if ( !pSFFld[11].m_dwIndex )
    return 4294967246i64;
  if ( *(_DWORD *)&pSFFld[11].m_strCode[4] == 6 && !CMapData::IsMapIn(v30->m_pCurMap, fPos) )
    return 4294967246i64;
  if ( v31 )
  {
    v15 = *(_DWORD *)&pSFFld[11].m_strCode[12];
    v16 = (float)(v15 + 40);
    v28 = (float)(v15 + 40);
    ((void (__fastcall *)(CCharacter *))v31->vfptr->GetWidth)(v31);
    v17 = v28 + (float)(v16 / 2.0);
    v29 = v17;
    _effect_parameter::GetEff_Plus(&v30->m_EP, 8);
    v18 = v29 + v17;
    v26 = (signed int)ffloor(v29 + v17);
    GetSqrt(v31->m_fCurPos, v30->m_fCurPos);
    if ( v18 > (float)v26 )
    {
      GetSqrt(v31->m_fOldPos, v30->m_fCurPos);
      if ( v18 > (float)v26 )
        return 4294967293i64;
    }
  }
  else if ( *(_DWORD *)&pSFFld[11].m_strCode[4] != 4 )
  {
    v27 = *(_DWORD *)&pSFFld[11].m_strCode[12] + 40;
    if ( *(_DWORD *)&pSFFld[11].m_strCode[4] == 6
      || *(_DWORD *)&pSFFld[11].m_strCode[4] == 5
      || *(_DWORD *)&pSFFld[11].m_strCode[4] == 7 )
    {
      _effect_parameter::GetEff_Plus(&v30->m_EP, 8);
      a5 = (float)v27 + a5;
      v27 = (signed int)ffloor(a5);
    }
    GetSqrt(fPos, v30->m_fCurPos);
    if ( a5 > (float)v27 )
    {
      GetSqrt(fPos, v30->m_fOldPos);
      if ( a5 > (float)v27 )
        return 4294967293i64;
    }
  }
  *ppForceFld = (_force_fld *)pSFFld;
  *ppForceItem = v21;
  if ( wEffBtSerial != 0xFFFF )
  {
    v22 = CPlayer::IsEffBulletValidity(v30, wEffBtSerial);
    if ( !v22 )
    {
      CPlayer::SendMsg_AdjustAmountInform(v30, 2, wEffBtSerial, 0);
      return 4294967233i64;
    }
    v23 = (_BulletItem_fld *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 10, v22->m_wItemIndex);
    *ppEffBtProp = v22;
    *ppfldEffBt = v23;
  }
  return 0i64;
}
