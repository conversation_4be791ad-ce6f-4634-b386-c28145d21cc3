/*
 * Function: j_?insert@?$_Hash@V?$_Hmap_traits@HPEAVCNationSettingFactory@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@std@@$0A@@stdext@@@stdext@@QEAA?AU?$pair@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@std@@_N@std@@AEBU?$pair@$$CBHPEAVCNationSettingFactory@@@4@@Z
 * Address: 0x14001005F
 */

std::pair<std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0>,bool> *__fastcall stdext::_Hash<stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>,0>>::insert(stdext::_Hash<stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationSettingFactory *> >,0> > *this, std::pair<std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0>,bool> *result, std::pair<int const ,CNationSettingFactory *> *_Val)
{
  return stdext::_Hash<stdext::_Hmap_traits<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>,0>>::insert(
           this,
           result,
           _Val);
}
