/*
 * Function: ?OnInitDialog@CObjectSearchDlg@@MEAAHXZ
 * Address: 0x14002F700
 */

signed __int64 __fastcall CObjectSearchDlg::OnInitDialog(CObjectSearchDlg *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  CGameObject *v5; // [sp+20h] [bp-18h]@4
  CObjectSearchDlg *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CDialog::OnInitDialog((CDialog *)&v6->vfptr);
  CComboBox::InsertString(&v6->m_cdObjectKind, 0, "Character");
  CComboBox::InsertString(&v6->m_cdObjectKind, 1, "Item");
  v5 = CGameObject::s_pSelectObject;
  if ( CGameObject::s_pSelectObject )
  {
    CComboBox::SetCurSel(&v6->m_cdObjectKind, v5->m_ObjID.m_byKind);
    CObjectSearchDlg::OnSelchangeCOMBOObjectKind(v6);
    CComboBox::SetCurSel(&v6->m_cdObjectID, v5->m_ObjID.m_byID);
    v6->m_edIndex = v5->m_ObjID.m_wIndex;
  }
  else
  {
    CComboBox::SetCurSel(&v6->m_cdObjectKind, 0);
    CObjectSearchDlg::OnSelchangeCOMBOObjectKind(v6);
    v6->m_edIndex = 0;
  }
  v6->m_szCharName[0] = 0;
  CWnd::UpdateData((CWnd *)&v6->vfptr, 0);
  return 1i64;
}
