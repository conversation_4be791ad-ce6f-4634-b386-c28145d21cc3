/*
 * Function: ?Update_RaceRank_Step7@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404B6E70
 */

char __fastcall CRFWorldDatabase::Update_RaceRank_Step7(CRFWorldDatabase *this, char *szDate)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-458h]@1
  char Dest; // [sp+30h] [bp-428h]@4
  char v7; // [sp+31h] [bp-427h]@4
  unsigned int j; // [sp+434h] [bp-24h]@4
  unsigned __int64 v9; // [sp+440h] [bp-18h]@4
  CRFWorldDatabase *v10; // [sp+460h] [bp+8h]@1
  char *v11; // [sp+468h] [bp+10h]@1

  v11 = szDate;
  v10 = this;
  v2 = &v5;
  for ( i = 276i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v10->vfptr,
    "CRFWorldDatabase::Update_RaceRank_Step7(szDate(%s)) : Start Update Rank tbl_PvpRank%s Table",
    szDate,
    szDate);
  Dest = 0;
  memset(&v7, 0, 0x3FFui64);
  for ( j = 0; (signed int)j < 3; ++j )
  {
    sprintf(
      &Dest,
      "select IDENTITY(int, 1, 1) AS Rank, Serial into #tbl_PvpRank from tbl_PvpRank%s where race=%d order by Grade desc, Rate",
      v11,
      j);
    if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &Dest, 0) )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v10->vfptr,
        "CRFWorldDatabase::Update_RaceRank_Step7(szDate(%s)) : %s Fail!",
        v11,
        &Dest);
      return 0;
    }
    sprintf(
      &Dest,
      "update tbl_PvpRank%s set rank = t1.rank from(select serial, rank from #tbl_PvpRank) as t1 where tbl_PvpRank%s.serial = t1.serial",
      v11,
      v11);
    if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &Dest, 0) )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v10->vfptr,
        "CRFWorldDatabase::Update_RaceRank_Step7(szDate(%s)) : %s Fail!",
        v11,
        &Dest);
      return 0;
    }
    sprintf(&Dest, "drop table #tbl_PvpRank");
    if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &Dest, 0) )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v10->vfptr,
        "CRFWorldDatabase::Update_RaceRank_Step7(szDate(%s)) : %s Fail!",
        v11,
        &Dest);
      return 0;
    }
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v10->vfptr,
    "CRFWorldDatabase::Update_RaceRank_Step7(szDate(%s)) : End Update Rank tbl_PvpRank%s Table",
    v11,
    v11);
  return 1;
}
