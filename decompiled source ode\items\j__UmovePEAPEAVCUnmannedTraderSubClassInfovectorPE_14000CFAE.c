/*
 * Function: j_??$_Umove@PEAPEAVCUnmannedTraderSubClassInfo@@@?$vector@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@IEAAPEAPEAVCUnmannedTraderSubClassInfo@@PEAPEAV2@00@Z
 * Address: 0x14000CFAE
 */

CUnmannedTraderSubClassInfo **__fastcall std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Umove<CUnmannedTraderSubClassInfo * *>(std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *this, CUnmannedTraderSubClassInfo **_First, CUnmannedTraderSubClassInfo **_Last, CUnmannedTraderSubClassInfo **_Ptr)
{
  return std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Umove<CUnmannedTraderSubClassInfo * *>(
           this,
           _First,
           _Last,
           _Ptr);
}
