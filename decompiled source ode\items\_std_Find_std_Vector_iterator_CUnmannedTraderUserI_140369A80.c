/*
 * Function: _std::_Find_std::_Vector_iterator_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo____unsigned_long__::_1_::dtor$2
 * Address: 0x140369A80
 */

void __fastcall std::_Find_std::_Vector_iterator_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo____unsigned_long__::_1_::dtor_2(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 32) & 1 )
  {
    *(_DWORD *)(a2 + 32) &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::~_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(*(std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > **)(a2 + 64));
  }
}
