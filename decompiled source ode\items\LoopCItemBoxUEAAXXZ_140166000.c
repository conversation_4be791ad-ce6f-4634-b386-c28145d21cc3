/*
 * Function: ?Loop@CItemBox@@UEAAXXZ
 * Address: 0x140166000
 */

void __fastcall CItemBox::Loop(CItemBox *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int v4; // [sp+20h] [bp-18h]@4
  unsigned int v5; // [sp+24h] [bp-14h]@6
  unsigned int v6; // [sp+28h] [bp-10h]@15
  unsigned int v7; // [sp+2Ch] [bp-Ch]@19
  CItemBox *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = GetLoopTime();
  if ( v4 > v8->m_dwLootStartTime )
  {
    if ( v8->m_nStateCode )
    {
      if ( v8->m_nStateCode == 1 )
      {
        v6 = v4 - v8->m_dwLootStartTime;
        if ( v6 > 0xC350 )
        {
          v8->m_nStateCode = 2;
          CItemBox::SendMsg_StateChange(v8);
        }
      }
      else if ( v8->m_nStateCode == 2 )
      {
        v7 = v4 - v8->m_dwLootStartTime;
        if ( v7 > 0xEA60 )
          CItemBox::Destroy(v8);
      }
    }
    else
    {
      v5 = v4 - v8->m_dwLootStartTime;
      if ( v5 > 0x4E20 )
      {
        if ( v8->m_byCreateCode != 2 && v8->m_byCreateCode != 3 && v8->m_byCreateCode != 6 && v8->m_byCreateCode != 4 )
        {
          v8->m_nStateCode = 1;
          CItemBox::SendMsg_StateChange(v8);
        }
        else
        {
          CItemBox::Destroy(v8);
        }
      }
    }
  }
}
