/*
 * Function: ?GetMap@CMapOperation@@QEAAPEAVCMapData@@PEAD@Z
 * Address: 0x140197A30
 */

CMapData *__fastcall CMapOperation::GetMap(CMapOperation *this, char *szMapCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CMapOperation *v7; // [sp+40h] [bp+8h]@1
  const char *Str2; // [sp+48h] [bp+10h]@1

  Str2 = szMapCode;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < v7->m_nMapNum; ++j )
  {
    if ( !strcmp_0(v7->m_Map[j].m_pMapSet->m_strCode, Str2) )
      return &v7->m_Map[j];
  }
  return 0i64;
}
