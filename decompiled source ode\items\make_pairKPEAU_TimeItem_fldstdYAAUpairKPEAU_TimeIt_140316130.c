/*
 * Function: ??$make_pair@KPEAU_TimeItem_fld@@@std@@YA?AU?$pair@KPEAU_TimeItem_fld@@@0@KPEAU_TimeItem_fld@@@Z
 * Address: 0x140316130
 */

std::pair<unsigned long,_TimeItem_fld *> *__fastcall std::make_pair<unsigned long,_TimeItem_fld *>(std::pair<unsigned long,_TimeItem_fld *> *result, unsigned int _Val1, _TimeItem_fld *_Val2)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  std::pair<unsigned long,_TimeItem_fld *> *v7; // [sp+30h] [bp+8h]@1
  unsigned int _Val1a; // [sp+38h] [bp+10h]@1
  _TimeItem_fld *_Val2a; // [sp+40h] [bp+18h]@1

  _Val2a = _Val2;
  _Val1a = _Val1;
  v7 = result;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::pair<unsigned long,_TimeItem_fld *>::pair<unsigned long,_TimeItem_fld *>(v7, &_Val1a, &_Val2a);
  return v7;
}
