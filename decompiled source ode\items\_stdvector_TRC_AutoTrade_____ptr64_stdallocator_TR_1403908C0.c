/*
 * Function: _std::vector_TRC_AutoTrade_____ptr64_std::allocator_TRC_AutoTrade_____ptr64___::_Insert_n_::_1_::catch$0
 * Address: 0x1403908C0
 */

void __fastcall __noreturn std::vector_TRC_AutoTrade_____ptr64_std::allocator_TRC_AutoTrade_____ptr64___::_Insert_n_::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1

  v2 = a2;
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Destroy(
    *(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > **)(a2 + 160),
    *(TRC_AutoTrade ***)(a2 + 64),
    *(TRC_AutoTrade ***)(a2 + 72));
  std::allocator<TRC_AutoTrade *>::deallocate(
    (std::allocator<TRC_AutoTrade *> *)(*(_QWORD *)(v2 + 160) + 8i64),
    *(TRC_AutoTrade ***)(v2 + 64),
    *(_QWORD *)(v2 + 56));
  CxxThrowException_0(0i64, 0i64);
}
