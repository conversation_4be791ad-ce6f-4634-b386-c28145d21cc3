/*
 * Function: ?UseBuffPotion@CExtPotionBuf@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x14039FBD0
 */

void __fastcall CExtPotionBuf::UseBuffPotion(CExtPotionBuf *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int nEndHour; // [sp+20h] [bp-18h]@4
  int nEndMin; // [sp+28h] [bp-10h]@4
  CExtPotionBuf *v7; // [sp+40h] [bp+8h]@1
  CPlayer *v8; // [sp+48h] [bp+10h]@1

  v8 = pOne;
  v7 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7->m_bExtPotionBufUse = 1;
  v7->m_dwEndPotionTime = GetConnectTime_AddBySec(2592000);
  v8->m_pUserDB->m_AvatorData.dbSupplement.dwBufPotionEndTime = v7->m_dwEndPotionTime;
  nEndMin = 0;
  nEndHour = 0;
  CExtPotionBuf::SendMsg_RemainBufUseTime(v7, 1, v8->m_ObjID.m_wIndex, 30, 0, 0);
}
