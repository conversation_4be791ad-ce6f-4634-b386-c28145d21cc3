/*
 * Function: ?_db_Update_SFDelayData@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@@Z
 * Address: 0x1401B5540
 */

bool __fastcall CMainThread::_db_Update_SFDelayData(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  _worlddb_sf_delay_info *pSFDelay; // [sp+20h] [bp-18h]@4
  CMainThread *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pSFDelay = &pNewData->dbSFDelay;
  return CRFWorldDatabase::Update_SFDelayInfo(v8->m_pWorldDB, dwSerial, &pNewData->dbSFDelay);
}
