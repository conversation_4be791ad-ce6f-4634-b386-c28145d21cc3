/*
 * Function: ?HSKQuestEnd_Att@CPlayer@@QEAAXEPEAV1@@Z
 * Address: 0x1400CCBC0
 */

void __fastcall CPlayer::HSKQuestEnd_Att(CPlayer *this, char byD<PERSON>royStoneRaceCode, CPlayer *pDestroyer)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@4
  int v6; // eax@8
  unsigned __int16 v7; // ax@11
  unsigned __int16 v8; // ax@14
  int v9; // eax@17
  __int64 v10; // [sp+0h] [bp-68h]@1
  bool v11; // [sp+30h] [bp-38h]@4
  _Quest_fld *pQuestFld; // [sp+38h] [bp-30h]@4
  int nAlterPoint; // [sp+40h] [bp-28h]@18
  int v14; // [sp+44h] [bp-24h]@8
  char v15; // [sp+48h] [bp-20h]@11
  char v16; // [sp+49h] [bp-1Fh]@11
  char v17; // [sp+4Ah] [bp-1Eh]@11
  char v18; // [sp+4Bh] [bp-1Dh]@11
  char v19; // [sp+4Ch] [bp-1Ch]@14
  char v20; // [sp+4Dh] [bp-1Bh]@14
  char v21; // [sp+4Eh] [bp-1Ah]@14
  char v22; // [sp+4Fh] [bp-19h]@14
  int v23; // [sp+50h] [bp-18h]@17
  CPlayer *v24; // [sp+70h] [bp+8h]@1
  CPlayer *v25; // [sp+80h] [bp+18h]@1

  v25 = pDestroyer;
  v24 = this;
  v3 = &v10;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = 0;
  v5 = CPlayerDB::GetRaceCode(&v24->m_Param);
  pQuestFld = (_Quest_fld *)CRecordData::GetRecord(&g_HolySys.m_tblQuest, v5);
  if ( pQuestFld )
  {
    if ( v25 == v24 )
    {
      v11 = 1;
    }
    else
    {
      v14 = CPlayerDB::GetRaceCode(&v25->m_Param);
      v6 = CPlayerDB::GetRaceCode(&v24->m_Param);
      if ( v14 == v6 )
        v11 = 1;
    }
  }
  else
  {
    v11 = 0;
  }
  if ( v11 )
  {
    v15 = CHolyStoneSystem::GetNumOfTime(&g_HolySys);
    v16 = CHolyStoneSystem::GetStartHour(&g_HolySys);
    v17 = CHolyStoneSystem::GetStartDay(&g_HolySys);
    v18 = CHolyStoneSystem::GetStartMonth(&g_HolySys);
    v7 = CHolyStoneSystem::GetStartYear(&g_HolySys);
    if ( !MiningTicket::AuthLastMentalTicket(&v24->m_MinigTicket, v7, v18, v17, v16, v15) )
      v11 = CHolyStoneSystem::IsMentalPass(&g_HolySys) != 0;
  }
  if ( v11 )
  {
    v19 = CHolyStoneSystem::GetNumOfTime(&g_HolySys);
    v20 = CHolyStoneSystem::GetStartHour(&g_HolySys);
    v21 = CHolyStoneSystem::GetStartDay(&g_HolySys);
    v22 = CHolyStoneSystem::GetStartMonth(&g_HolySys);
    v8 = CHolyStoneSystem::GetStartYear(&g_HolySys);
    CPlayer::UpdateLastCriTicket(v24, v8, v22, v21, v20, v19);
    CPlayer::_Reward_Quest(v24, pQuestFld, -1);
    CPlayer::SendMsg_HSKQuestSucc(v24, 0, 1);
    if ( pQuestFld->m_nConsContribution > 0 )
      CPlayer::SendMsg_RaceBattlePenelty(v24, pQuestFld->m_nConsContribution, 0);
  }
  else
  {
    v23 = CPlayerDB::GetRaceCode(&v25->m_Param);
    v9 = CPlayerDB::GetRaceCode(&v24->m_Param);
    if ( v23 == v9 )
    {
      nAlterPoint = g_HolySys.m_nRaceBattlePoint[0][1];
      CPlayer::AlterPvPPoint(v24, (double)g_HolySys.m_nRaceBattlePoint[0][1], holy_dec, 0xFFFFFFFF);
      CPlayer::SendMsg_RaceBattlePenelty(v24, nAlterPoint, 0);
    }
    CPlayer::SendMsg_HSKQuestSucc(v24, 0, 0);
  }
  v24->m_byHSKQuestCode = 100;
}
