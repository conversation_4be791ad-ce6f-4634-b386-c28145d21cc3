/*
 * Function: ?Init@_NPC_QUEST_HISTORY@_QUEST_DB_BASE@@QEAAXXZ
 * Address: 0x1400769D0
 */

void __fastcall _QUEST_DB_BASE::_NPC_QUEST_HISTORY::Init(_QUEST_DB_BASE::_NPC_QUEST_HISTORY *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  _QUEST_DB_BASE::_NPC_QUEST_HISTORY *Dst; // [sp+40h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(Dst, 0, 8ui64);
  Dst->byLevel = -1;
}
