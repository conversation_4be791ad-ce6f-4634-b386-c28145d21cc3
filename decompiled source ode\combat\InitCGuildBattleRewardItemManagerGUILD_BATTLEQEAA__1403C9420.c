/*
 * Function: ?Init@CGuildBattleRewardItemManager@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403C9420
 */

char __fastcall GUILD_BATTLE::CGuildBattleRewardItemManager::Init(GUILD_BATTLE::CGuildBattleRewardItemManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v3; // rax@6
  char result; // al@6
  GUILD_BATTLE::CGuildBattleRewardItem *v5; // rax@7
  GUILD_BATTLE::CGuildBattleRewardItem *v6; // rax@9
  GUILD_BATTLE::CGuildBattleLogger *v7; // rax@10
  __int64 v8; // [sp+0h] [bp-48h]@1
  unsigned __int64 _Count; // [sp+20h] [bp-28h]@4
  GUILD_BATTLE::CGuildBattleRewardItem v10; // [sp+28h] [bp-20h]@7
  GUILD_BATTLE::CGuildBattleRewardItemManager *v11; // [sp+50h] [bp+8h]@1

  v11 = this;
  v1 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  LODWORD(_Count) = GetPrivateProfileIntA("RewardItem", "ItemCnt", 0, "./Initialize/NormalGuildBattle.ini");
  if ( (_DWORD)_Count && (unsigned int)_Count <= 0xA )
  {
    GUILD_BATTLE::CGuildBattleRewardItem::CGuildBattleRewardItem(&v10);
    std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::assign(
      &v11->m_kItem,
      (unsigned int)_Count,
      v5);
    for ( HIDWORD(_Count) = 0; HIDWORD(_Count) < (unsigned int)_Count; ++HIDWORD(_Count) )
    {
      v6 = std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::operator[](
             &v11->m_kItem,
             SHIDWORD(_Count));
      if ( !GUILD_BATTLE::CGuildBattleRewardItem::Init(v6, WORD2(_Count)) )
      {
        v7 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v7,
          "CGuildBattleRewardItemManager::Init() : m_kItem[%d].Init()",
          HIDWORD(_Count));
        return 0;
      }
    }
    result = 1;
  }
  else
  {
    v3 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v3,
      "CGuildBattleRewardItemManager::Init() : ItemCnt = %u MAX_CNT(%u)!",
      (unsigned int)_Count,
      10i64);
    result = 0;
  }
  return result;
}
