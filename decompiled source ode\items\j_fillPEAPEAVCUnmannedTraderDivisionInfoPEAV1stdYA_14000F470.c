/*
 * Function: j_??$fill@PEAPEAVCUnmannedTraderDivisionInfo@@PEAV1@@std@@YAXPEAPEAVCUnmannedTraderDivisionInfo@@0AEBQEAV1@@Z
 * Address: 0x14000F470
 */

void __fastcall std::fill<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo *>(CUnmannedTraderDivisionInfo **_First, CUnmannedTraderDivisionInfo **_Last, CUnmannedTraderDivisionInfo *const *_Val)
{
  std::fill<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo *>(_First, _Last, _Val);
}
