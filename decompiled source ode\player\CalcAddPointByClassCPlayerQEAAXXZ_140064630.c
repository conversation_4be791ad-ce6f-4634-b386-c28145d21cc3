/*
 * Function: ?CalcAddPointByClass@CPlayer@@QEAAXXZ
 * Address: 0x140064630
 */

void __fastcall CPlayer::CalcAddPointByClass(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CPlayer *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(v5->m_nAddPointByClass, 0, 0x10ui64);
  v5->m_nAddPointByClass[0] += v5->m_Param.m_pClassData->m_nBnsForHP;
  v5->m_nAddPointByClass[1] += v5->m_Param.m_pClassData->m_nBnsForFP;
  v5->m_nAddPointByClass[2] += v5->m_Param.m_pClassData->m_nBnsForSP;
  for ( j = 0; j < 3 && v5->m_Param.m_pClassHistory[j]; ++j )
  {
    v5->m_nAddPointByClass[0] += v5->m_Param.m_pClassHistory[j]->m_nBnsForHP;
    v5->m_nAddPointByClass[1] += v5->m_Param.m_pClassHistory[j]->m_nBnsForFP;
    v5->m_nAddPointByClass[2] += v5->m_Param.m_pClassHistory[j]->m_nBnsForSP;
  }
}
