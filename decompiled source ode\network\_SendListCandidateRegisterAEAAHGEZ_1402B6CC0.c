/*
 * Function: ?_SendList@CandidateRegister@@AEAAHGE@Z
 * Address: 0x1402B6CC0
 */

__int64 __fastcall CandidateRegister::_SendList(CandidateRegister *this, unsigned __int16 wSock, char byRace)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@4
  __int64 v7; // [sp+0h] [bp-48h]@1
  unsigned int dwClientIndex; // [sp+30h] [bp-18h]@4
  CandidateRegister *v9; // [sp+50h] [bp+8h]@1
  unsigned __int16 v10; // [sp+58h] [bp+10h]@1
  char v11; // [sp+60h] [bp+18h]@1

  v11 = byRace;
  v10 = wSock;
  v9 = this;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = _pt_result_fcandidacy_list_zocl::size(&v9->_kSend[(unsigned __int8)byRace]);
  dwClientIndex = v10;
  CNetProcess::LoadSendMsg(unk_1414F2088, v10, v9->_byPtType, &v9->_kSend[(unsigned __int8)v11].byCnt, v5);
  return 0i64;
}
