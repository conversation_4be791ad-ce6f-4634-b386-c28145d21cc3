/*
 * Function: ?pop_front@?$deque@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@QEAAXXZ
 * Address: 0x14031E6D0
 */

void __fastcall std::deque<RECV_DATA,std::allocator<RECV_DATA>>::pop_front(std::deque<RECV_DATA,std::allocator<RECV_DATA> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned __int64 v4; // [sp+20h] [bp-18h]@5
  std::deque<RECV_DATA,std::allocator<RECV_DATA> > *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !std::deque<RECV_DATA,std::allocator<RECV_DATA>>::empty(v5) )
  {
    v4 = v5->_Myoff;
    std::allocator<RECV_DATA>::destroy(&v5->_Alval, v5->_Map[v4]);
    if ( v5->_Mapsize <= ++v5->_Myoff )
      v5->_Myoff = 0i64;
    if ( !--v5->_Mysize )
      v5->_Myoff = 0i64;
  }
}
