/*
 * Function: ?GuildBattleCurrentBattleInfoRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C8770
 */

char __fastcall CNetworkEX::GuildBattleCurrentBattleInfoRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CGuildBattleController *v6; // rax@6
  __int64 v7; // [sp+0h] [bp-48h]@1
  char *v8; // [sp+20h] [bp-28h]@4
  CPlayer *v9; // [sp+28h] [bp-20h]@4
  unsigned int uiMapID; // [sp+30h] [bp-18h]@6
  int na; // [sp+58h] [bp+10h]@1

  na = n;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = pBuf;
  v9 = &g_Player + n;
  if ( v9->m_bOper )
  {
    uiMapID = CPlayerDB::GetRaceCode(&v9->m_Param);
    v6 = CGuildBattleController::Instance();
    CGuildBattleController::SendCurrentBattleInfoRequest(v6, na, uiMapID);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
