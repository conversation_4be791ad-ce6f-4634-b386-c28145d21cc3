/*
 * Function: ?CalcSecIndex@CItemStore@@AEAAHMM@Z
 * Address: 0x1402622B0
 */

__int64 __fastcall CItemStore::CalcSecIndex(CItemStore *this, float x, float z)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  _bsp_info *v5; // rax@4
  __int64 v7; // [sp+0h] [bp-48h]@1
  _bsp_info *v8; // [sp+20h] [bp-28h]@4
  float v9; // [sp+28h] [bp-20h]@4
  float v10; // [sp+2Ch] [bp-1Ch]@4
  int v11; // [sp+30h] [bp-18h]@4
  int v12; // [sp+34h] [bp-14h]@4
  _sec_info *v13; // [sp+38h] [bp-10h]@4
  CItemStore *v14; // [sp+50h] [bp+8h]@1

  v14 = this;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = CMapData::GetBspInfo(v14->m_pExistMap);
  v8 = v5;
  v9 = (float)-v5->m_nMapMinSize[0] + x;
  v10 = (float)v5->m_nMapMaxSize[2] - z;
  v11 = (signed int)ffloor(v9) / 100;
  v12 = (signed int)ffloor(v10) / 100;
  v13 = CMapData::GetSecInfo(v14->m_pExistMap);
  return (unsigned int)(v13->m_nSecNumW * v12 + v11);
}
