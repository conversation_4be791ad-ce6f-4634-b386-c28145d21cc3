/*
 * Function: ?UpdateRank@CGuildBattleController@@QEAA_NEPEAE@Z
 * Address: 0x1403D6D80
 */

bool __fastcall CGuildBattleController::UpdateRank(CGuildBattleController *this, char byRace, char *byOutData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleRankManager *v5; // rax@4
  __int64 v7; // [sp+0h] [bp-28h]@1
  char v8; // [sp+38h] [bp+10h]@1
  char *pOutData; // [sp+40h] [bp+18h]@1

  pOutData = byOutData;
  v8 = byRace;
  v3 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = GUILD_BATTLE::CGuildBattleRankManager::Instance();
  return GUILD_BATTLE::CGuildBattleRankManager::SelectGuildBattleRankList(v5, v8, pOutData);
}
