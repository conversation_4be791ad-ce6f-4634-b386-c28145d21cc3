/*
 * Function: ?get_guidlname@TRC_AutoTrade@@QEAAPEADXZ
 * Address: 0x1402D9A10
 */

char *__fastcall TRC_AutoTrade::get_guidlname(TRC_AutoTrade *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // [sp+0h] [bp-18h]@1
  TRC_AutoTrade *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = (__int64 *)&v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5->m_pOwnerGuild )
    v4 = v5->m_pOwnerGuild->m_aszName;
  else
    v4 = 0i64;
  return v4;
}
