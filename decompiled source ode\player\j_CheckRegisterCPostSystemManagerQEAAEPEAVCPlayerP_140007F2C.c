/*
 * Function: j_?CheckRegister@CPostSystemManager@@QEAAEPEAVCPlayer@@PEAU_STORAGE_POS_INDIV@@KAEAPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x140007F2C
 */

char __fastcall CPostSystemManager::CheckRegister(CPostSystemManager *this, CPlayer *pOne, _STORAGE_POS_INDIV *pItemInfo, unsigned int dwGold, _STORAGE_LIST::_db_con **pItem)
{
  return CPostSystemManager::CheckRegister(this, pOne, pItemInfo, dwGold, pItem);
}
