/*
 * Function: ??1?$AlgorithmImpl@V?$DL_SignerBase@UECPPoint@CryptoPP@@@CryptoPP@@V?$DL_SS@U?$DL_Keys_ECDSA@VECP@CryptoPP@@@CryptoPP@@V?$DL_Algorithm_ECDSA@VECP@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@H@2@@CryptoPP@@UEAA@XZ
 * Address: 0x14055F630
 */

int CryptoPP::AlgorithmImpl<CryptoPP::DL_SignerBase<CryptoPP::ECPPoint>,CryptoPP::DL_SS<CryptoPP::DL_Keys_ECDSA<CryptoPP::ECP>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::ECP>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1,int>>::~AlgorithmImpl<CryptoPP::DL_SignerBase<CryptoPP::ECPPoint>,CryptoPP::DL_SS<CryptoPP::DL_Keys_ECDSA<CryptoPP::ECP>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::ECP>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1,int>>()
{
  return CryptoPP::DL_SignerBase<CryptoPP::ECPPoint>::~DL_SignerBase<CryptoPP::ECPPoint>();
}
