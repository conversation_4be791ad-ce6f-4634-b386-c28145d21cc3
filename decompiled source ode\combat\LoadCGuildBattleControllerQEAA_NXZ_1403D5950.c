/*
 * Function: ?Load@CGuildBattleController@@QEAA_NXZ
 * Address: 0x1403D5950
 */

char __fastcall CGuildBattleController::Load(CGuildBattleController *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  GUILD_BATTLE::CGuildBattleRankManager *v4; // rax@8
  GUILD_BATTLE::CGuildBattleScheduleManager *v5; // rax@10
  GUILD_BATTLE::CNormalGuildBattleManager *v6; // rax@12
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager *v7; // rax@14
  GUILD_BATTLE::CPossibleBattleGuildListManager *v8; // rax@16
  __int64 v9; // [sp+0h] [bp-F8h]@1
  GUILD_BATTLE::CGuildBattleLogger *v10; // [sp+40h] [bp-B8h]@4
  int iCurDay; // [sp+48h] [bp-B0h]@4
  unsigned int uiMapCnt; // [sp+54h] [bp-A4h]@6
  int iToday; // [sp+74h] [bp-84h]@6
  int iTodayDayID; // [sp+94h] [bp-64h]@6
  int v15; // [sp+B4h] [bp-44h]@6
  int v16; // [sp+D4h] [bp-24h]@6
  CGuildBattleController *v17; // [sp+100h] [bp+8h]@1

  v17 = this;
  v1 = &v9;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = GUILD_BATTLE::CGuildBattleLogger::Instance();
  iCurDay = GetCurDay();
  if ( iCurDay >= 0 )
  {
    uiMapCnt = 0;
    iToday = 0;
    iTodayDayID = 0;
    v15 = 0;
    v16 = 1;
    if ( !CGuildBattleController::LoadINI(v17, &uiMapCnt, &iToday, &iTodayDayID, &v15, &v16) )
      GUILD_BATTLE::CGuildBattleLogger::Log(v10, "CGuildBattleController::Load(%d) LoadINI() Fail!");
    v4 = GUILD_BATTLE::CGuildBattleRankManager::Instance();
    if ( GUILD_BATTLE::CGuildBattleRankManager::Load(v4) )
    {
      v5 = GUILD_BATTLE::CGuildBattleScheduleManager::Instance();
      if ( GUILD_BATTLE::CGuildBattleScheduleManager::Load(v5, iCurDay, uiMapCnt, iToday, iTodayDayID, v15, v16) )
      {
        v6 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
        if ( GUILD_BATTLE::CNormalGuildBattleManager::Load(v6, iCurDay, uiMapCnt, iToday, iTodayDayID, v15, v16) )
        {
          v7 = GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Instance();
          if ( GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Load(v7, iCurDay, uiMapCnt, iToday, v15) )
          {
            v8 = GUILD_BATTLE::CPossibleBattleGuildListManager::Instance();
            if ( GUILD_BATTLE::CPossibleBattleGuildListManager::Load(v8) )
            {
              if ( CGuildBattleController::SaveINI(v17) )
              {
                result = 1;
              }
              else
              {
                GUILD_BATTLE::CGuildBattleLogger::Log(v10, "CGuildBattleController::Load() SaveINI() Fail!");
                result = 0;
              }
            }
            else
            {
              GUILD_BATTLE::CGuildBattleLogger::Log(
                v10,
                "CGuildBattleController::Load() CPossibleBattleGuildListManager::Instance()->Load() Fail!");
              result = 0;
            }
          }
          else
          {
            GUILD_BATTLE::CGuildBattleLogger::Log(
              v10,
              "CGuildBattleController::Load() CGuildBattleReservedScheduleListManager::Instance()->Load() Fail!");
            result = 0;
          }
        }
        else
        {
          GUILD_BATTLE::CGuildBattleLogger::Log(
            v10,
            "CGuildBattleController::Load() CNormalGuildBattleManager::Instance()->Load() Fail!");
          result = 0;
        }
      }
      else
      {
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v10,
          "CGuildBattleScheduleManager::Load() CGuildBattleScheduleManager::Instance()->Load() Fail!");
        result = 0;
      }
    }
    else
    {
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v10,
        "CGuildBattleController::Load() CGuildBattleRankManager::Instance()->Load() Fail!");
      result = 0;
    }
  }
  else
  {
    GUILD_BATTLE::CGuildBattleLogger::Log(v10, "CGuildBattleController::Load(%d) ::GetCurDay() Fail!");
    result = 0;
  }
  return result;
}
