/*
 * Function: ?check_cash_discount_ini@CashItemRemoteStore@@QEAAXXZ
 * Address: 0x1402F6970
 */

void __fastcall CashItemRemoteStore::check_cash_discount_ini(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-F8h]@1
  _cash_discount_ini_ pIni; // [sp+30h] [bp-C8h]@4
  _FILETIME pft; // [sp+B8h] [bp-40h]@4
  char v6; // [sp+C4h] [bp-34h]@11
  __time32_t Time; // [sp+D4h] [bp-24h]@11
  int v8; // [sp+E4h] [bp-14h]@16
  CashItemRemoteStore *v9; // [sp+100h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _cash_discount_ini_::_cash_discount_ini_(&pIni);
  CashItemRemoteStore::load_cde_ini(v9, &pIni, &pft);
  if ( v9->m_cde.m_ini.m_wCsDiscount != pIni.m_wCsDiscount )
    v9->m_cde.m_ini.m_wCsDiscount = pIni.m_wCsDiscount;
  if ( pft.dwHighDateTime != v9->m_cde.m_cde_ini_file_time.dwHighDateTime
    || pft.dwLowDateTime != v9->m_cde.m_cde_ini_file_time.dwLowDateTime )
  {
    v9->m_cde.m_cde_ini_file_time.dwHighDateTime = pft.dwHighDateTime;
    v9->m_cde.m_cde_ini_file_time.dwLowDateTime = pft.dwLowDateTime;
    CashItemRemoteStore::log_about_cash_event(v9, "Loaded From Ini File <When Server Running>", &pIni);
    if ( v9->m_cde.m_ini.m_bUseCashDiscount && CashItemRemoteStore::is_cde_time(v9) )
    {
      CLogFile::Write(&v9->m_cde.m_cde_log, "The New Ini File was disregarded. Other Event is on");
    }
    else
    {
      v6 = 0;
      _time32(&Time);
      if ( pIni.m_bUseCashDiscount )
      {
        if ( Time >= pIni.m_cdeTime[0] )
        {
          if ( Time < pIni.m_cdeTime[0] || Time > pIni.m_cdeTime[1] )
          {
            if ( Time > pIni.m_cdeTime[1] )
              v6 = 5;
          }
          else
          {
            v8 = pIni.m_cdeTime[1] - Time;
            if ( pIni.m_cdeTime[1] == Time )
            {
              v6 = 0;
            }
            else if ( v8 && (unsigned int)v8 <= v9->m_cde.m_cde_inform_before[1] )
            {
              v6 = 4;
            }
            else if ( (unsigned int)v8 <= v9->m_cde.m_cde_inform_before[1]
                   || (unsigned int)v8 > v9->m_cde.m_cde_inform_before[0] )
            {
              if ( (unsigned int)v8 <= v9->m_cde.m_cde_inform_before[0] )
                v6 = 0;
              else
                v6 = 2;
            }
            else
            {
              v6 = 3;
            }
          }
        }
        else
        {
          v6 = 1;
        }
      }
      CashItemRemoteStore::set_cde_status(v9, v6);
      CashItemRemoteStore::update_ini(v9, &pIni);
    }
  }
}
