/*
 * Function: ?_CheckSend@CNetProcess@@AEAAXG@Z
 * Address: 0x140479A30
 */

void __fastcall CNetProcess::_CheckSend(CNetProcess *this, unsigned __int16 wSocketIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@7
  unsigned __int16 v5; // ax@10
  __int64 v6; // [sp+0h] [bp-48h]@1
  _socket *v7; // [sp+30h] [bp-18h]@4
  unsigned int v8; // [sp+38h] [bp-10h]@5
  CNetProcess *v9; // [sp+50h] [bp+8h]@1
  unsigned __int16 v10; // [sp+58h] [bp+10h]@1

  v10 = wSocketIndex;
  v9 = this;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = &v9->m_NetSocket.m_Socket[wSocketIndex];
  if ( v7->m_bAccept )
  {
    v8 = v9->m_dwCurTime - v7->m_dwLastSendTime;
    if ( v9->m_nIndex )
    {
      if ( v8 > 0x9C4 )
      {
        v5 = _check_query::size(&sQry);
        CNetProcess::LoadSendMsg(v9, v10, sbyQryType, &sQry.sDum, v5);
      }
    }
    else if ( v8 > 0x9C4 )
    {
      v4 = _check_query::size(&sQry);
      CNetProcess::LoadSendMsg(v9, v10, sbyQryType, &sQry.sDum, v4);
    }
  }
}
