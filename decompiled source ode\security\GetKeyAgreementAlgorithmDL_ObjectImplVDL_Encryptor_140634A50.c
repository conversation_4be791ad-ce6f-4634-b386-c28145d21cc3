/*
 * Function: ?GetKeyAgreementAlgorithm@?$DL_ObjectImpl@V?$DL_EncryptorBase@VInteger@CryptoPP@@@CryptoPP@@U?$DL_CryptoSchemeOptions@U?$DLIES@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@CryptoPP@@$00@CryptoPP@@UDL_CryptoKeys_GFP@2@V?$DL_KeyAgreementAlgorithm_DH@VInteger@CryptoPP@@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@2@@2@V?$DL_KeyDerivationAlgorithm_P1363@VInteger@CryptoPP@@$00V?$P1363_KDF2@VSHA1@CryptoPP@@@2@@2@V?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$00@2@@2@V?$DL_PublicKey_GFP@VDL_GroupParameters_GFP_DefaultSafePrime@CryptoPP@@@2@@CryptoPP@@MEBAAEBV?$DL_KeyAgreementAlgorithm@VInteger@CryptoPP@@@2@XZ
 * Address: 0x140634A50
 */

__int64 CryptoPP::DL_ObjectImpl<CryptoPP::DL_EncryptorBase<CryptoPP::Integer>,CryptoPP::DL_CryptoSchemeOptions<CryptoPP::DLIES<CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,1>,CryptoPP::DL_CryptoKeys_GFP,CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::Integer,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>>,CryptoPP::DL_KeyDerivationAlgorithm_P1363<CryptoPP::Integer,1,CryptoPP::P1363_KDF2<CryptoPP::SHA1>>,CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>>,CryptoPP::DL_PublicKey_GFP<CryptoPP::DL_GroupParameters_GFP_DefaultSafePrime>>::GetKeyAgreementAlgorithm()
{
  __int64 v0; // rax@1
  char v2; // [sp+20h] [bp-18h]@1
  char v3; // [sp+21h] [bp-17h]@1

  memset(&v3, 0, sizeof(v3));
  v0 = CryptoPP::Singleton<CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::Integer,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>>,CryptoPP::NewObject<CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::Integer,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>>>,0>::Singleton<CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::Integer,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>>,CryptoPP::NewObject<CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::Integer,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>>>,0>((__int64)&v2);
  return CryptoPP::Singleton<CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::Integer,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>>,CryptoPP::NewObject<CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::Integer,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>>>,0>::Ref(v0);
}
