/*
 * Function: ?UpdateCancelRegist@CUnmannedTraderController@@QEAAEPEAD@Z
 * Address: 0x14034D320
 */

char __fastcall CUnmannedTraderController::UpdateCancelRegist(CUnmannedTraderController *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-D8h]@1
  char *byProcRet; // [sp+20h] [bp-B8h]@4
  char *v7; // [sp+30h] [bp-A8h]@4
  char byState; // [sp+44h] [bp-94h]@4
  char v9; // [sp+54h] [bp-84h]@4
  char Dst; // [sp+68h] [bp-70h]@6
  _unmannedtrader_buy_item_info kData; // [sp+98h] [bp-40h]@6
  CUnmannedTraderController *v12; // [sp+E0h] [bp+8h]@1

  v12 = this;
  v2 = &v5;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = pData;
  pData[45] = 0;
  byState = -1;
  byProcRet = v7 + 45;
  v9 = CUnmannedTraderController::CheckDBItemState(v12, v7[38], *((_DWORD *)v7 + 10), &byState, v7 + 45);
  if ( v7[45] )
  {
    result = v9;
  }
  else
  {
    memset_0(&Dst, 0, 0x10ui64);
    GetLocalTime((LPSYSTEMTIME)&Dst);
    memset_0(&kData, 0, 0x28ui64);
    v9 = CRFWorldDatabase::Select_UnmannedTraderBuySingleItemInfo(pkDB, v7[38], *((_DWORD *)v7 + 10), &kData);
    if ( v9 == 1 )
    {
      v7[45] = 49;
      result = 24;
    }
    else if ( v9 == 2 )
    {
      v7[45] = 50;
      result = 0;
    }
    else
    {
      byProcRet = &Dst;
      if ( CRFWorldDatabase::Update_UnmannedTraderItemState(
             pkDB,
             v7[38],
             *((_DWORD *)v7 + 10),
             v7[44],
             (_SYSTEMTIME *)&Dst) )
      {
        result = 0;
      }
      else
      {
        v7[45] = 30;
        result = 24;
      }
    }
  }
  return result;
}
