/*
 * Function: ?Initialize@FinalDecisionApplyer@@UEAA_NXZ
 * Address: 0x1402BD7F0
 */

char __fastcall FinalDecisionApplyer::Initialize(FinalDecisionApplyer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@4
  __int64 v5; // [sp+0h] [bp-168h]@1
  char _Dest[256]; // [sp+40h] [bp-128h]@4
  unsigned __int64 v7; // [sp+150h] [bp-18h]@4
  FinalDecisionApplyer *v8; // [sp+170h] [bp+8h]@1

  v8 = this;
  v1 = &v5;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = (unsigned __int64)&v5 ^ _security_cookie;
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0xFFui64);
  v3 = GetKorLocalTime();
  sprintf_s<256>((char (*)[256])_Dest, "..\\ZoneServerLog\\SystemLog\\Patriarch\\CandidateRegister_%d.log", v3);
  CLogFile::SetWriteLogFile(&v8->_kSysLog, _Dest, 1, 0, 1, 1);
  ElectProcessor::Initialize((ElectProcessor *)&v8->vfptr);
  ((void (__fastcall *)(FinalDecisionApplyer *, signed __int64, _QWORD, _QWORD))v8->vfptr->Doit)(v8, 11i64, 0i64, 0i64);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 120, 0i64, 0);
  return 1;
}
