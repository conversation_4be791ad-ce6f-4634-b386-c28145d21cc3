/*
 * Function: ??1?$_<PERSON><PERSON>@UMessageRange@MeterFilter@CryptoPP@@_JPEBU123@AEBU123@@std@@QEAA@XZ
 * Address: 0x1405FE8A0
 */

void __fastcall std::_Ranit<CryptoPP::MeterFilter::MessageRange,__int64,CryptoPP::MeterFilter::MessageRange const *,CryptoPP::MeterFilter::MessageRange const &>::~_Ranit<CryptoPP::MeterFilter::MessageRange,__int64,CryptoPP::MeterFilter::MessageRange const *,CryptoPP::MeterFilter::MessageRange const &>(std::_Iterator_base *a1)
{
  std::_Iterator_base::~_Iterator_base(a1);
}
