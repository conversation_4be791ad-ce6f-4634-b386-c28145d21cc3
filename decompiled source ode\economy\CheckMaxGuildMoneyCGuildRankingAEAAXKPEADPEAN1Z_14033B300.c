/*
 * Function: ?CheckMax<PERSON><PERSON>Money@CGuildRanking@@AEAAXKPEADPEAN1@Z
 * Address: 0x14033B300
 */

void __fastcall CGuildRanking::CheckMaxGuildMoney(CGuildRanking *this, unsigned int dwGuildSerial, char *wszGuildName, long double *pdDalant, long double *pdGold)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  long double *v8; // [sp+20h] [bp-18h]@5
  int v9; // [sp+28h] [bp-10h]@5
  unsigned int v10; // [sp+48h] [bp+10h]@1
  char *v11; // [sp+50h] [bp+18h]@1
  long double *v12; // [sp+58h] [bp+20h]@1

  v12 = pdDalant;
  v11 = wszGuildName;
  v10 = dwGuildSerial;
  v5 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( *pdDalant > 1000000000.0 )
  {
    v9 = 1000000000;
    v8 = *(long double **)pdDalant;
    CLogFile::Write(&stru_1799C8F30, "%d : %s > Guild DALANT (%d) (ADJUST %d)", dwGuildSerial, wszGuildName);
    *v12 = DOUBLE_1_0e9;
  }
  if ( *pdGold > 500000.0 )
  {
    v9 = 500000;
    v8 = v12;
    CLogFile::Write(&stru_1799C8F30, "%d : %s > Guild GOLD (%d) (ADJUST %d)", v10, v11);
    *pdGold = DOUBLE_500000_0;
  }
}
