/*
 * Function: ??$?8U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@U01@@std@@YA_NAEBV?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@0@0@Z
 * Address: 0x1403C7690
 */

char __fastcall std::operator==<std::pair<int const,CAsyncLogInfo *>,std::pair<int const,CAsyncLogInfo *>>(std::allocator<std::pair<int const ,CAsyncLogInfo *> > *__formal, std::allocator<std::pair<int const ,CAsyncLogInfo *> > *a2)
{
  return 1;
}
