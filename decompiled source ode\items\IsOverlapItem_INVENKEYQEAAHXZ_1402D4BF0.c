/*
 * Function: ?IsOverlapItem@_INVENKEY@@QEAAHXZ
 * Address: 0x1402D4BF0
 */

int __fastcall _INVENKEY::IsOverlapItem(_INVENKEY *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  _INVENKEY *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return IsOverLapItem(v5->byTableCode);
}
