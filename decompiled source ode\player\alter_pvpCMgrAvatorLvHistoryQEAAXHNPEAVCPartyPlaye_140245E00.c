/*
 * Function: ?alter_pvp@CMgrAvatorLvHistory@@QEAAXHNPEAVCPartyPlayer@@PEAD@Z
 * Address: 0x140245E00
 */

void __fastcall CMgrAvatorLvHistory::alter_pvp(CMgrAvatorLvHistory *this, int n, long double dPvpVariation, CPartyPlayer *pParty, char *pszFileName)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp-20h] [bp-E8h]@1
  char *v8; // [sp+0h] [bp-C8h]@4
  char DstBuf; // [sp+20h] [bp-A8h]@4
  char v10; // [sp+21h] [bp-A7h]@4
  bool v11; // [sp+A4h] [bp-24h]@4
  int j; // [sp+A8h] [bp-20h]@5
  unsigned __int64 v13; // [sp+B8h] [bp-10h]@4
  CMgrAvatorLvHistory *v14; // [sp+D0h] [bp+8h]@1
  CPartyPlayer *v15; // [sp+E8h] [bp+20h]@1

  v15 = pParty;
  v14 = this;
  v5 = &v7;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v13 = (unsigned __int64)&v7 ^ _security_cookie;
  DstBuf = 0;
  memset(&v10, 0, 0x7Fui64);
  v11 = CPartyPlayer::IsPartyMode(pParty);
  LODWORD(v8) = v11;
  sprintf_s(sData_0, 0x2710ui64, "Pvp_Variation: %.0f, Party: %d, ", dPvpVariation);
  if ( v11 )
  {
    sprintf_s(&DstBuf, 0x80ui64, "Boss: %s, Member: ", v15->m_pPartyBoss->m_wszName);
    strcat_s(sData_0, 0x2710ui64, &DstBuf);
    for ( j = 0; j < 8; ++j )
    {
      if ( v15->m_pPartyBoss->m_pPartyMember[j] )
      {
        sprintf_s(&DstBuf, 0x80ui64, "%s, ", v15->m_pPartyBoss->m_pPartyMember[j]->m_wszName);
        strcat_s(sData_0, 0x2710ui64, &DstBuf);
      }
    }
  }
  v8 = v14->m_szCurTime;
  sprintf_s(&DstBuf, 0x80ui64, "[%s %s]\r\n\r\n", v14->m_szCurDate);
  strcat_s(sData_0, 0x2710ui64, &DstBuf);
  CMgrAvatorLvHistory::WriteFile(v14, pszFileName, sData_0);
}
