/*
 * Function: ?Get<PERSON><PERSON>FC@CMonster@@UEAAHHPEAVCCharacter@@PEAH@Z
 * Address: 0x140146790
 */

signed __int64 __fastcall CMonster::GetDefFC(CMonster *this, int nAttactPart, CCharacter *pAttChar, int *pnConvertPart)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  int v7; // eax@13
  __int64 v8; // rax@13
  float v9; // xmm0_4@13
  __int64 v10; // [sp+0h] [bp-38h]@1
  float v11; // [sp+20h] [bp-18h]@6
  int v12; // [sp+24h] [bp-14h]@8
  int v13; // [sp+28h] [bp-10h]@13
  CMonster *v14; // [sp+40h] [bp+8h]@1
  int v15; // [sp+48h] [bp+10h]@1

  v15 = nAttactPart;
  v14 = this;
  v4 = &v10;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v14->m_pMonRec )
  {
    v11 = 0.0;
    if ( !pAttChar || v14->m_pMonRec->m_nShieldBlock != 1 )
      goto LABEL_18;
    v12 = v14->m_pMonRec->m_nBlockPer;
    if ( v12 > 100 )
      v12 = 100;
    if ( rand() % 100 < v12 )
    {
      result = 4294967294i64;
    }
    else
    {
LABEL_18:
      if ( v15 == -1 )
      {
        v7 = rand();
        v13 = v7 % 5;
        v8 = v7 % 5;
        v9 = (float)v14->m_DefPart[v8];
        v11 = (float)v14->m_DefPart[v8];
      }
      else
      {
        v9 = (float)v14->m_DefPart[v15];
        v11 = (float)v14->m_DefPart[v15];
      }
      _effect_parameter::GetEff_Rate(&v14->m_EP, 6);
      result = (unsigned int)(signed int)ffloor(v11 * v9);
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
