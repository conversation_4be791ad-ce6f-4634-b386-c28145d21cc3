/*
 * Function: ?_CheckPlayerInfo@CandidateRegister@@AEAAHPEAVCPlayer@@@Z
 * Address: 0x1402B6D70
 */

signed __int64 __fastcall CandidateRegister::_CheckPlayerInfo(CandidateRegister *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  CandidateMgr *v5; // rax@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int dwAvatorSerial; // [sp+20h] [bp-18h]@6
  int v8; // [sp+24h] [bp-14h]@6
  CPlayer *v9; // [sp+48h] [bp+10h]@1

  v9 = pOne;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pOne->m_Param.m_byPvPGrade >= 3 )
  {
    dwAvatorSerial = CPlayerDB::GetCharSerial(&pOne->m_Param);
    v8 = CPlayerDB::GetRaceCode(&v9->m_Param);
    v5 = CandidateMgr::Instance();
    if ( CandidateMgr::IsRegistedAvator_1(v5, v8, dwAvatorSerial) )
    {
      result = 3i64;
    }
    else if ( CPlayerDB::GetDalant(&v9->m_Param) >= 0x989680 )
    {
      result = 0i64;
    }
    else
    {
      result = 5i64;
    }
  }
  else
  {
    result = 4i64;
  }
  return result;
}
