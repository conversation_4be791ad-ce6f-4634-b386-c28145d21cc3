/*
 * Function: j_??$fill@PEAVCUnmannedTraderItemCodeInfo@@V1@@std@@YAXPEAVCUnmannedTraderItemCodeInfo@@0AEBV1@@Z
 * Address: 0x14000506A
 */

void __fastcall std::fill<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo>(CUnmannedTraderItemCodeInfo *_First, CUnmannedTraderItemCodeInfo *_Last, CUnmannedTraderItemCodeInfo *_Val)
{
  std::fill<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo>(_First, _Last, _Val);
}
