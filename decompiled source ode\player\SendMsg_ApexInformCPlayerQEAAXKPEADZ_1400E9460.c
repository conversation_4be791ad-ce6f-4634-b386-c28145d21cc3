/*
 * Function: ?SendMsg_ApexInform@CPlayer@@QEAAXKPEAD@Z
 * Address: 0x1400E9460
 */

void __fastcall CPlayer::SendMsg_ApexInform(CPlayer *this, unsigned int dwRecvSize, char *pMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-58h]@1
  char pbyType; // [sp+34h] [bp-24h]@4
  char v7; // [sp+35h] [bp-23h]@4
  CPlayer *v8; // [sp+60h] [bp+8h]@1

  v8 = this;
  v3 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pbyType = 99;
  v7 = 3;
  CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, pMsg, dwRecvSize);
}
