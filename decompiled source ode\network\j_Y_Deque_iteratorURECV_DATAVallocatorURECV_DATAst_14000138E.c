/*
 * Function: j_??Y?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEAAAEAV01@_J@Z
 * Address: 0x14000138E
 */

std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator+=(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this, __int64 _Off)
{
  return std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator+=(this, _Off);
}
