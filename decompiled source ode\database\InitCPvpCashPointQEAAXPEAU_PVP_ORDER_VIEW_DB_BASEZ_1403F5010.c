/*
 * Function: ?Init@CPvpCashPoint@@QEAAXPEAU_PVP_ORDER_VIEW_DB_BASE@@@Z
 * Address: 0x1403F5010
 */

void __fastcall CPvpCashPoint::Init(CPvpCashPoint *this, _PVP_ORDER_VIEW_DB_BASE *pkInfo)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CPvpCashPoint *v6; // [sp+40h] [bp+8h]@1
  _PVP_ORDER_VIEW_DB_BASE *v7; // [sp+48h] [bp+10h]@1

  v7 = pkInfo;
  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CIndexList::ResetList(&v6->m_KillerList);
  for ( j = 0; j < 10; ++j )
  {
    if ( v7->dwKillerSerial[j] )
      CIndexList::PushNode_Back(&v6->m_KillerList, v7->dwKillerSerial[j], 0i64);
  }
  v6->m_byContPvpHave = v7->byContHaveCash;
  v6->m_byContPvpLose = v7->byContLoseCash;
  v6->m_bRaceWarRecvr = v7->bRaceWarRecvr;
}
