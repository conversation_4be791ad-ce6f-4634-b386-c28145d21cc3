/*
 * Function: ?init@_qry_case_lobby_logout@@QEAAXXZ
 * Address: 0x14011F2E0
 */

void __fastcall _qry_case_lobby_logout::init(_qry_case_lobby_logout *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _qry_case_lobby_logout *Dst; // [sp+40h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(Dst, 0, 0xC4ui64);
  for ( j = 0; j < 3; ++j )
  {
    Dst->RegeList[j].bySlotIndex = -1;
    Dst->RegeList[j].dwCharSerial = -1;
  }
}
