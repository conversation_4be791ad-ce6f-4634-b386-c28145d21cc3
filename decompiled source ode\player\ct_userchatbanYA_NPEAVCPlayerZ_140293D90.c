/*
 * Function: ?ct_userchatban@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140293D90
 */

bool __fastcall ct_userchatban(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-198h]@1
  char _Dest[128]; // [sp+50h] [bp-148h]@9
  char v6[128]; // [sp+F0h] [bp-A8h]@12
  int iPeriod; // [sp+174h] [bp-24h]@13
  unsigned __int64 v8; // [sp+180h] [bp-18h]@4
  CPlayer *v9; // [sp+1A0h] [bp+8h]@1

  v9 = pOne;
  v1 = &v4;
  for ( i = 100i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( v9 )
  {
    if ( v9->m_byUserDgr )
    {
      if ( s_nWordCount == 3 )
      {
        if ( strlen_0(s_pwszDstCheat[2]) && strlen_0(s_pwszDstCheat[2]) <= 0x7F )
        {
          iPeriod = 0;
          iPeriod = atoi(s_pwszDstCheat[1]);
          result = CPlayer::mgr_user_ban(v9, s_pwszDstCheat[0], iPeriod, s_pwszDstCheat[2], 1) != 0;
        }
        else
        {
          v6[0] = 0;
          memset(&v6[1], 0, 0x7Fui64);
          sprintf_s<128>((char (*)[128])v6, "Invaled Reason Str!");
          CPlayer::SendData_ChatTrans(v9, 0, 0xFFFFFFFF, -1, 0, v6, -1, 0i64);
          result = 0;
        }
      }
      else
      {
        _Dest[0] = 0;
        memset(&_Dest[1], 0, 0x7Fui64);
        sprintf_s<128>((char (*)[128])_Dest, "Invaled Parameter Count!");
        CPlayer::SendData_ChatTrans(v9, 0, 0xFFFFFFFF, -1, 0, _Dest, -1, 0i64);
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
