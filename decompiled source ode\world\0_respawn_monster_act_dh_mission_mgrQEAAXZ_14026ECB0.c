/*
 * Function: ??0_respawn_monster_act@_dh_mission_mgr@@QEAA@XZ
 * Address: 0x14026ECB0
 */

void __fastcall _dh_mission_mgr::_respawn_monster_act::_respawn_monster_act(_dh_mission_mgr::_respawn_monster_act *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _dh_mission_mgr::_respawn_monster_act *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _dh_mission_mgr::_respawn_monster_act::init(v4);
}
