/*
 * Function: ?CancelRegist@CUnmannedTraderController@@QEAAXGPEAU_a_trade_clear_item_request_clzo@@@Z
 * Address: 0x1401D4810
 */

void __fastcall CUnmannedTraderController::CancelRegist(CUnmannedTraderController *this, unsigned __int16 wInx, _a_trade_clear_item_request_clzo *pRequest)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfoTable *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-28h]@1
  unsigned __int16 v7; // [sp+38h] [bp+10h]@1
  _a_trade_clear_item_request_clzo *pRequesta; // [sp+40h] [bp+18h]@1

  pRequesta = pRequest;
  v7 = wInx;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = CUnmannedTraderUserInfoTable::Instance();
  CUnmannedTraderUserInfoTable::CancelRegist(v5, v7, 0, pRequesta);
}
