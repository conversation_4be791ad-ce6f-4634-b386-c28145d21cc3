/*
 * Function: _std::vector_CUnmannedTraderGroupDivisionVersionInfo_std::allocator_CUnmannedTraderGroupDivisionVersionInfo___::_Insert_n_::_1_::catch$1
 * Address: 0x140399310
 */

void __fastcall __noreturn std::vector_CUnmannedTraderGroupDivisionVersionInfo_std::allocator_CUnmannedTraderGroupDivisionVersionInfo___::_Insert_n_::_1_::catch_1(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Destroy(
    *(std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > **)(a2 + 208),
    (CUnmannedTraderGroupDivisionVersionInfo *)(*(_QWORD *)(*(_QWORD *)(a2 + 216) + 16i64)
                                              + 48i64 * *(_QWORD *)(a2 + 224)),
    (CUnmannedTraderGroupDivisionVersionInfo *)(*(_QWORD *)(*(_QWORD *)(a2 + 208) + 24i64)
                                              + 48i64 * *(_QWORD *)(a2 + 224)));
  CxxThrowException_0(0i64, 0i64);
}
