/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAU_PVP_RANK_REFRESH_USER@@_KPEAU1@V?$allocator@PEAU_PVP_RANK_REFRESH_USER@@@std@@@stdext@@YAXPEAPEAU_PVP_RANK_REFRESH_USER@@_KAEBQEAU1@AEAV?$allocator@PEAU_PVP_RANK_REFRESH_USER@@@std@@@Z
 * Address: 0x140004368
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<_PVP_RANK_REFRESH_USER * *,unsigned __int64,_PVP_RANK_REFRESH_USER *,std::allocator<_PVP_RANK_REFRESH_USER *>>(_PVP_RANK_REFRESH_USER **_First, unsigned __int64 _Count, _PVP_RANK_REFRESH_USER *const *_Val, std::allocator<_PVP_RANK_REFRESH_USER *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<_PVP_RANK_REFRESH_USER * *,unsigned __int64,_PVP_RANK_REFRESH_USER *,std::allocator<_PVP_RANK_REFRESH_USER *>>(
    _First,
    _Count,
    _Val,
    _Al);
}
