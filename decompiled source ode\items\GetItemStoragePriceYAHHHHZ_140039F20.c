/*
 * Function: ?GetItemStoragePrice@@YAHHHH@Z
 * Address: 0x140039F20
 */

__int64 __fastcall GetItemStoragePrice(int nTableCode, int nItemIndex, int nRace)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@6
  __int64 v6; // [sp+0h] [bp-118h]@1
  CRecordData *v7; // [sp+20h] [bp-F8h]@4
  _base_fld *v8; // [sp+28h] [bp-F0h]@5
  _base_fld *v9; // [sp+30h] [bp-E8h]@8
  _base_fld *v10; // [sp+38h] [bp-E0h]@11
  _base_fld *v11; // [sp+40h] [bp-D8h]@15
  _base_fld *v12; // [sp+48h] [bp-D0h]@18
  _base_fld *v13; // [sp+50h] [bp-C8h]@21
  _base_fld *v14; // [sp+58h] [bp-C0h]@25
  _base_fld *v15; // [sp+60h] [bp-B8h]@28
  _base_fld *v16; // [sp+68h] [bp-B0h]@31
  _base_fld *v17; // [sp+70h] [bp-A8h]@34
  _base_fld *v18; // [sp+78h] [bp-A0h]@37
  _base_fld *v19; // [sp+80h] [bp-98h]@40
  _base_fld *v20; // [sp+88h] [bp-90h]@43
  _base_fld *v21; // [sp+90h] [bp-88h]@46
  _base_fld *v22; // [sp+98h] [bp-80h]@49
  _base_fld *v23; // [sp+A0h] [bp-78h]@52
  _base_fld *v24; // [sp+A8h] [bp-70h]@55
  _base_fld *v25; // [sp+B0h] [bp-68h]@58
  _base_fld *v26; // [sp+B8h] [bp-60h]@61
  _base_fld *v27; // [sp+C0h] [bp-58h]@64
  _base_fld *v28; // [sp+C8h] [bp-50h]@67
  _base_fld *v29; // [sp+D0h] [bp-48h]@70
  _base_fld *v30; // [sp+D8h] [bp-40h]@73
  _base_fld *v31; // [sp+E0h] [bp-38h]@76
  _base_fld *v32; // [sp+E8h] [bp-30h]@79
  _base_fld *v33; // [sp+F0h] [bp-28h]@82
  _base_fld *v34; // [sp+F8h] [bp-20h]@85
  int v35; // [sp+100h] [bp-18h]@4
  int v36; // [sp+120h] [bp+8h]@1

  v36 = nTableCode;
  v3 = &v6;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = &s_ptblItemData[v36];
  v35 = v36;
  switch ( v36 )
  {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 7:
      v8 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v8 )
        goto LABEL_87;
      result = *(_DWORD *)&v8[4].m_strCode[60];
      break;
    case 6:
      v9 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v9 )
        goto LABEL_87;
      result = *(_DWORD *)&v9[8].m_strCode[60];
      break;
    case 11:
      v10 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v10 )
        goto LABEL_87;
      result = *(_DWORD *)&v10[4].m_strCode[32];
      break;
    case 12:
      result = 0i64;
      break;
    case 13:
      v11 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v11 )
        goto LABEL_87;
      result = *(_DWORD *)&v11[7].m_strCode[0];
      break;
    case 10:
      v12 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v12 )
        goto LABEL_87;
      result = *(_DWORD *)&v12[6].m_strCode[24];
      break;
    case 18:
      v13 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v13 )
        goto LABEL_87;
      result = *(_DWORD *)&v13[5].m_strCode[40];
      break;
    case 19:
      result = 0i64;
      break;
    case 20:
      v14 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v14 )
        goto LABEL_87;
      result = *(_DWORD *)&v14[4].m_strCode[24];
      break;
    case 16:
      v15 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v15 )
        goto LABEL_87;
      result = *(_DWORD *)&v15[4].m_strCode[24];
      break;
    case 17:
      v16 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v16 )
        goto LABEL_87;
      result = *(_DWORD *)&v16[3].m_strCode[40];
      break;
    case 15:
      v17 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v17 )
        goto LABEL_87;
      result = *(_DWORD *)&v17[4].m_strCode[24];
      break;
    case 8:
      v18 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v18 )
        goto LABEL_87;
      result = *(_DWORD *)&v18[4].m_strCode[40];
      break;
    case 9:
      v19 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v19 )
        goto LABEL_87;
      result = *(_DWORD *)&v19[4].m_strCode[40];
      break;
    case 21:
      v20 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v20 )
        goto LABEL_87;
      result = *(_DWORD *)&v20[5].m_strCode[20];
      break;
    case 22:
      v21 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v21 )
        goto LABEL_87;
      result = *(_DWORD *)&v21[6].m_strCode[16];
      break;
    case 23:
      v22 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v22 )
        goto LABEL_87;
      result = *(_DWORD *)&v22[6].m_strCode[20];
      break;
    case 24:
      v23 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v23 )
        goto LABEL_87;
      result = *(_DWORD *)&v23[4].m_strCode[28];
      break;
    case 25:
      v24 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v24 )
        goto LABEL_87;
      result = *(_DWORD *)&v24[7].m_strCode[0];
      break;
    case 26:
      v25 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v25 )
        goto LABEL_87;
      result = *(_DWORD *)&v25[8].m_strCode[4];
      break;
    case 27:
      v26 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v26 )
        goto LABEL_87;
      result = *(_DWORD *)&v26[4].m_strCode[56];
      break;
    case 28:
      v27 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v27 )
        goto LABEL_87;
      result = *(_DWORD *)&v27[6].m_strCode[24];
      break;
    case 30:
      v28 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v28 )
        goto LABEL_87;
      result = *(_DWORD *)&v28[4].m_strCode[32];
      break;
    case 31:
      v29 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v29 )
        goto LABEL_87;
      result = *(_DWORD *)&v29[4].m_strCode[36];
      break;
    case 32:
      v30 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v30 )
        goto LABEL_87;
      result = *(_DWORD *)&v30[4].m_strCode[32];
      break;
    case 33:
      v31 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v31 )
        goto LABEL_87;
      result = *(_DWORD *)&v31[6].m_strCode[4];
      break;
    case 34:
      v32 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v32 )
        goto LABEL_87;
      result = *(_DWORD *)&v32[5].m_strCode[4];
      break;
    case 35:
      v33 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v33 )
        goto LABEL_87;
      result = *(_DWORD *)&v33[5].m_strCode[44];
      break;
    case 36:
      v34 = CRecordData::GetRecord(v7, nItemIndex);
      if ( !v34 )
        goto LABEL_87;
      result = *(_DWORD *)&v34[4].m_strCode[36];
      break;
    default:
LABEL_87:
      result = 0i64;
      break;
  }
  return result;
}
