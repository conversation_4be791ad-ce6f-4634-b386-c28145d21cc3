/*
 * Function: ?Update_PostRegistry@CRFWorldDatabase@@QEAA_NKKPEAD000H_KKKEE1@Z
 * Address: 0x1404B1DB0
 */

bool __fastcall CRFWorldDatabase::Update_PostRegistry(CRFWorldDatabase *this, unsigned int dwIndex, unsigned int dwSenderSerial, char *wszSendName, char *wszRecvName, char *wszTitle, char *wszContent, int nK, unsigned __int64 dwD, unsigned int dwU, unsigned int dwGold, char bySendRace, char bySenderDgr, unsigned __int64 lnUID)
{
  __int64 *v14; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v17; // [sp+0h] [bp-4B8h]@1
  char *v18; // [sp+20h] [bp-498h]@4
  char *v19; // [sp+28h] [bp-490h]@4
  char *v20; // [sp+30h] [bp-488h]@4
  char *v21; // [sp+38h] [bp-480h]@4
  int v22; // [sp+40h] [bp-478h]@4
  unsigned __int64 v23; // [sp+48h] [bp-470h]@4
  unsigned int v24; // [sp+50h] [bp-468h]@4
  unsigned int v25; // [sp+58h] [bp-460h]@4
  int v26; // [sp+60h] [bp-458h]@4
  int v27; // [sp+68h] [bp-450h]@4
  unsigned __int64 v28; // [sp+70h] [bp-448h]@4
  unsigned int v29; // [sp+78h] [bp-440h]@4
  char DstBuf; // [sp+90h] [bp-428h]@4
  char v31; // [sp+91h] [bp-427h]@4
  unsigned __int64 v32; // [sp+4A0h] [bp-18h]@4
  CRFWorldDatabase *v33; // [sp+4C0h] [bp+8h]@1

  v33 = this;
  v14 = &v17;
  for ( i = 300i64; i; --i )
  {
    *(_DWORD *)v14 = -858993460;
    v14 = (__int64 *)((char *)v14 + 4);
  }
  v32 = (unsigned __int64)&v17 ^ _security_cookie;
  DstBuf = 0;
  memset(&v31, 0, 0x3FFui64);
  v29 = dwIndex;
  v28 = lnUID;
  v27 = (unsigned __int8)bySenderDgr;
  v26 = (unsigned __int8)bySendRace;
  v25 = dwGold;
  v24 = dwU;
  v23 = dwD;
  v22 = nK;
  v21 = wszContent;
  v20 = wszTitle;
  v19 = wszRecvName;
  v18 = wszSendName;
  sprintf_s(
    &DstBuf,
    0x400ui64,
    "update tbl_PostRegistry set dck=0,sendserial=%d,sendname='%s',recvname='%s',title='%s',content='%s',k=%d,d=%I64d,u=%"
    "d,gold=%d,sendrace=%d,userdgr=%d,uid=%I64d where serial=%d",
    dwSenderSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v33->vfptr, &DstBuf, 1);
}
