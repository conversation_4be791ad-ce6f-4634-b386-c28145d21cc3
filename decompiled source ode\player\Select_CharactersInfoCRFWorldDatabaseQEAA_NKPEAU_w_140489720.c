/*
 * Function: ?Select_CharactersInfo@CRFWorldDatabase@@QEAA_NKPEAU_worlddb_character_array_info@@@Z
 * Address: 0x140489720
 */

char __fastcall CRFWorldDatabase::Select_CharactersInfo(CRFWorldDatabase *this, unsigned int dwAccountSerial, _worlddb_character_array_info *pCharacterData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-1B8h]@1
  void *SQLStmt; // [sp+20h] [bp-198h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-190h]@16
  SQLLEN v9; // [sp+38h] [bp-180h]@16
  __int16 v10; // [sp+44h] [bp-174h]@9
  char Dest; // [sp+60h] [bp-158h]@4
  char Dst; // [sp+178h] [bp-40h]@16
  int v13; // [sp+194h] [bp-24h]@4
  unsigned __int64 v14; // [sp+1A0h] [bp-18h]@4
  CRFWorldDatabase *v15; // [sp+1C0h] [bp+8h]@1
  _worlddb_character_array_info *v16; // [sp+1D0h] [bp+18h]@1

  v16 = pCharacterData;
  v15 = this;
  v3 = &v6;
  for ( i = 108i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = (unsigned __int64)&v6 ^ _security_cookie;
  v13 = 0;
  sprintf(&Dest, "{ CALL pSelect_CharactersInfo( %d ) }", dwAccountSerial);
  if ( v15->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v15->vfptr, &Dest);
  if ( v15->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v15->vfptr) )
  {
    v10 = SQLExecDirectA_0(v15->m_hStmtSelect, &Dest, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 0;
      }
      else
      {
        SQLStmt = v15->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v10, &Dest, "_SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v10, v15->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      do
      {
        v10 = SQLFetch_0(v15->m_hStmtSelect);
        if ( v10 && v10 != 1 )
          break;
        memset_0(&Dst, 0, 0x11ui64);
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)17;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 1u, 1, v16->CharacterInfo[v13].wszAvatorName, 17i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 2u, -17, &v16->CharacterInfo[v13].bySlotIndex, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 3u, -17, &v16->CharacterInfo[v13].byRaceCode, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 4u, -17, &v16->CharacterInfo[v13].byLevel, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 5u, 4, &v16->CharacterInfo[v13].dwSerial, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 6u, -17, &v16->CharacterInfo[v13].byDck, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)17;
        v10 = SQLGetData_0(v15->m_hStmtSelect, 7u, 1, &Dst, 17i64, &v9);
        if ( v16->CharacterInfo[v13].byDck == 1 )
          strcpy_0(v16->CharacterInfo[v13].wszAvatorName, &Dst);
        if ( ++v13 >= 100 )
          break;
      }
      while ( !v10 || v10 == 1 );
      v16->wCharacterCount = v13;
      if ( v15->m_hStmtSelect )
        SQLCloseCursor_0(v15->m_hStmtSelect);
      if ( v15->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v15->vfptr, "%s Success", &Dest);
      result = 1;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v15->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
