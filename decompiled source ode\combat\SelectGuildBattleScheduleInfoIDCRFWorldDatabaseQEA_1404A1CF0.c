/*
 * Function: ?SelectGuildBattleScheduleInfoID@CRFWorldDatabase@@QEAAEK@Z
 * Address: 0x1404A1CF0
 */

char __fastcall CRFWorldDatabase::SelectGuildBattleScheduleInfoID(CRFWorldDatabase *this, unsigned int dwID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-4A8h]@1
  void *SQLStmt; // [sp+20h] [bp-488h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-480h]@22
  SQLLEN v8; // [sp+38h] [bp-470h]@22
  __int16 v9; // [sp+44h] [bp-464h]@9
  char Dest; // [sp+60h] [bp-448h]@4
  char v11; // [sp+464h] [bp-44h]@16
  int TargetValue; // [sp+474h] [bp-34h]@22
  unsigned __int64 v13; // [sp+490h] [bp-18h]@4
  CRFWorldDatabase *v14; // [sp+4B0h] [bp+8h]@1
  unsigned int v15; // [sp+4B8h] [bp+10h]@1

  v15 = dwID;
  v14 = this;
  v2 = &v5;
  for ( i = 296i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  sprintf(&Dest, "{ CALL pSelect_GuildBattleScheduleID( %u ) }", dwID);
  if ( v14->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v14->vfptr, &Dest);
  if ( v14->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v14->vfptr) )
  {
    v9 = SQLExecDirectA_0(v14->m_hStmtSelect, &Dest, -3);
    if ( v9 && v9 != 1 )
    {
      if ( v9 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v14->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v9, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v9, v14->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v9 = SQLFetch_0(v14->m_hStmtSelect);
      if ( v9 && v9 != 1 )
      {
        v11 = 0;
        if ( v9 == 100 )
        {
          v11 = 2;
        }
        else
        {
          SQLStmt = v14->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v9, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v9, v14->m_hStmtSelect);
          v11 = 1;
        }
        if ( v14->m_hStmtSelect )
          SQLCloseCursor_0(v14->m_hStmtSelect);
        result = v11;
      }
      else
      {
        TargetValue = 0;
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v14->m_hStmtSelect, 1u, -18, &TargetValue, 0i64, &v8);
        if ( v9 == 100 )
        {
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          result = 2;
        }
        else if ( TargetValue == v15 )
        {
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          if ( v14->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v14->vfptr, "%s Success", &Dest);
          result = 0;
        }
        else
        {
          result = 2;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v14->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
