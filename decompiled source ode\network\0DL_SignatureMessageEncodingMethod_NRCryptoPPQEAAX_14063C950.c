/*
 * Function: ??0DL_SignatureMessageEncodingMethod_NR@CryptoPP@@QEAA@XZ
 * Address: 0x14063C950
 */

CryptoPP::DL_SignatureMessageEncodingMethod_NR *__fastcall CryptoPP::DL_SignatureMessageEncodingMethod_NR::DL_SignatureMessageEncodingMethod_NR(CryptoPP::DL_SignatureMessageEncodingMethod_NR *this)
{
  CryptoPP::DL_SignatureMessageEncodingMethod_NR *v2; // [sp+30h] [bp+8h]@1

  v2 = this;
  CryptoPP::PK_DeterministicSignatureMessageEncodingMethod::PK_DeterministicSignatureMessageEncodingMethod((CryptoPP::PK_DeterministicSignatureMessageEncodingMethod *)&this->vfptr);
  v2->vfptr = (CryptoPP::PK_SignatureMessageEncodingMethodVtbl *)&CryptoPP::DL_SignatureMessageEncodingMethod_NR::`vftable';
  return v2;
}
