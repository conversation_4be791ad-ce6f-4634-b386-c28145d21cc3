/*
 * Function: ?GetBillingType@CPlayer@@QEAAHXZ
 * Address: 0x1400600A0
 */

__int64 __fastcall CPlayer::GetBillingType(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@5
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_pUserDB )
    v5 = CUserDB::GetBillingType(v6->m_pUserDB);
  else
    v5 = 0;
  return (unsigned int)v5;
}
