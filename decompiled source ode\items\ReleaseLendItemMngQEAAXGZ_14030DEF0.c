/*
 * Function: ?Release@LendItemMng@@QEAAXG@Z
 * Address: 0x14030DEF0
 */

void __fastcall LendItemMng::Release(LendItemMng *this, unsigned __int16 wIdx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  LendItemMng *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( (signed int)wIdx < 2532 )
    LendItemSheet::Release(v5->_ppkLendItem[wIdx]);
}
