/*
 * Function: ??0table@lua_tinker@@QEAA@AEBU01@@Z
 * Address: 0x140446720
 */

void __fastcall lua_tinker::table::table(lua_tinker::table *this, lua_tinker::table *input)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  lua_tinker::table *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5->m_obj = input->m_obj;
  lua_tinker::table_obj::inc_ref(v5->m_obj);
}
