/*
 * Function: ?__make_item@@YAEPEAVCPlayer@@PEAU__item@_combine_ex_item_result_zocl@@PEAU_ItemCombine_exp_fld@@HE@Z
 * Address: 0x1402ADED0
 */

char __fastcall __make_item(CPlayer *pMaster, _combine_ex_item_result_zocl::__item *pItem, _ItemCombine_exp_fld *pfld, int nIndex, char byLinkTableIndex)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-58h]@1
  int nTableCode; // [sp+20h] [bp-38h]@4
  _base_fld *v10; // [sp+28h] [bp-30h]@4
  char *szRecordCode; // [sp+30h] [bp-28h]@6
  _base_fld *v12; // [sp+38h] [bp-20h]@7
  char v13; // [sp+40h] [bp-18h]@15
  unsigned __int64 dwExp; // [sp+44h] [bp-14h]@15
  int v15; // [sp+4Ch] [bp-Ch]@18
  _combine_ex_item_result_zocl::__item *v16; // [sp+68h] [bp+10h]@1
  _ItemCombine_exp_fld *v17; // [sp+70h] [bp+18h]@1
  int v18; // [sp+78h] [bp+20h]@1

  v18 = nIndex;
  v17 = pfld;
  v16 = pItem;
  v5 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  nTableCode = -1;
  v10 = 0i64;
  if ( nIndex >= 24 )
    return 12;
  szRecordCode = pfld->m_listOutput[nIndex].m_itmPdOutput;
  if ( !strncmp(szRecordCode, "LR", 2ui64) )
  {
    v12 = CRecordData::GetRecord(&ItemCombineMgr::ms_tbl_ItemCombine_Link_Result, szRecordCode);
    if ( !v12 || (signed int)(unsigned __int8)byLinkTableIndex >= 100 )
      return 12;
    szRecordCode = (char *)&v12[1] + 64 * (unsigned __int64)(unsigned __int8)byLinkTableIndex;
  }
  nTableCode = GetItemTableCode(szRecordCode);
  if ( nTableCode == -1 )
  {
    result = 12;
  }
  else
  {
    v10 = CRecordData::GetRecordByHash((CRecordData *)&unk_1799C6AA0 + nTableCode, szRecordCode, 2, 5);
    if ( v10 )
    {
      v16->Key.byTableCode = nTableCode;
      v16->Key.wItemIndex = v10->m_dwIndex;
      v16->Key.byRewardIndex = v18;
      v16->dwDur = v17->m_listOutput[v18].m_nOutNum;
      v13 = GetItemKindCode(nTableCode);
      LODWORD(dwExp) = v17->m_listOutput[v18].m_dwUpt;
      if ( v13 )
      {
        if ( v13 == 1 )
        {
          v16->dwDur = dwExp;
          v16->dwUpt = GetMaxParamFromExp(v10->m_dwIndex, (unsigned int)dwExp);
        }
      }
      else if ( (_DWORD)dwExp == -1 )
      {
        BYTE4(dwExp) = GetDefItemUpgSocketNum(nTableCode, v10->m_dwIndex);
        if ( (signed int)BYTE4(dwExp) <= 0 )
          v15 = 0;
        else
          v15 = rand() % BYTE4(dwExp) + 1;
        BYTE5(dwExp) = v15;
        v16->dwUpt = GetBitAfterSetLimSocket(v15);
      }
      else
      {
        v16->dwUpt = dwExp;
      }
      result = 0;
    }
    else
    {
      result = 12;
    }
  }
  return result;
}
