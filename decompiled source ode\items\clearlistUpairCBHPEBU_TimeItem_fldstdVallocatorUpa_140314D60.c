/*
 * Function: ?clear@?$list@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@2@@std@@QEAAXXZ
 * Address: 0x140314D60
 */

void __fastcall std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::clear(std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *this, std::_List_nod<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Node *a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  std::_List_nod<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Node *v4; // rdx@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  std::_List_nod<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Node *v6; // [sp+20h] [bp-18h]@6
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *v7; // [sp+28h] [bp-10h]@4
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = (std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *)*std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Nextnode((std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *)v8->_Myhead, a2);
  *std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Nextnode(
     (std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *)v8->_Myhead,
     v4) = v8->_Myhead;
  *std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Prevnode(
     (std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *)v8->_Myhead,
     v4) = v8->_Myhead;
  v8->_Mysize = 0i64;
  while ( v7 != (std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *)v8->_Myhead )
  {
    v6 = *std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Nextnode(
            v7,
            v4);
    std::allocator<std::_List_nod<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Node>::destroy(
      &v8->_Alnod,
      (std::_List_nod<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Node *)v7);
    std::allocator<std::_List_nod<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Node>::deallocate(
      &v8->_Alnod,
      (std::_List_nod<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Node *)v7,
      1ui64);
    v7 = (std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *)v6;
  }
}
