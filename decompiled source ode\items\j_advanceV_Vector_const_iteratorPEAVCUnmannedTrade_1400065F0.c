/*
 * Function: j_??$advance@V?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@_J@std@@YAXAEAV?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@0@_J@Z
 * Address: 0x1400065F0
 */

void __fastcall std::advance<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,__int64>(std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_Where, __int64 _Off)
{
  std::advance<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,__int64>(
    _Where,
    _Off);
}
