/*
 * Function: ?SendMsg_PatriarchTaxRate@TRC_AutoTrade@@QEAAXH@Z
 * Address: 0x1402D8480
 */

void __usercall TRC_AutoTrade::SendMsg_PatriarchTaxRate(TRC_AutoTrade *this@<rcx>, int n@<edx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  float v5; // xmm0_4@4
  unsigned __int16 v6; // ax@4
  __int64 v7; // [sp+0h] [bp-78h]@1
  _pt_inform_tax_rate_zocl v8; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v10; // [sp+55h] [bp-23h]@4
  TRC_AutoTrade *v11; // [sp+80h] [bp+8h]@1
  int dwClientIndex; // [sp+88h] [bp+10h]@1

  dwClientIndex = n;
  v11 = this;
  v3 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  _pt_inform_tax_rate_zocl::_pt_inform_tax_rate_zocl(&v8);
  TRC_AutoTrade::get_taxrate(v11);
  v5 = a3 * 100.0;
  v8.byCurrTax = (signed int)ffloor(v5);
  TRC_AutoTrade::get_next_tax(v11);
  v8.byNextTax = (signed int)ffloor(v5);
  pbyType = 13;
  v10 = 119;
  v6 = _pt_inform_tax_rate_zocl::size(&v8);
  CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &v8.byCurrTax, v6);
}
