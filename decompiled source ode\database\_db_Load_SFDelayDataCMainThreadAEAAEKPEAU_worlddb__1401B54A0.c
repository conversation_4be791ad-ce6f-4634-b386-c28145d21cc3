/*
 * Function: ?_db_Load_SFDelayData@CMainThread@@AEAAEKPEAU_worlddb_sf_delay_info@@@Z
 * Address: 0x1401B54A0
 */

char __fastcall CMainThread::_db_Load_SFDelayData(CMainThread *this, unsigned int dwSerial, _worlddb_sf_delay_info *pDbSFDelayInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  char v7; // [sp+20h] [bp-18h]@4
  CMainThread *v8; // [sp+40h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+48h] [bp+10h]@1
  _worlddb_sf_delay_info *pSFDelay; // [sp+50h] [bp+18h]@1

  pSFDelay = pDbSFDelayInfo;
  dwSeriala = dwSerial;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = CRFWorldDatabase::Select_SFDelayInfo(v8->m_pWorldDB, dwSerial, pDbSFDelayInfo);
  if ( v7 == 1 )
  {
    result = 24;
  }
  else if ( v7 != 2 || CRFWorldDatabase::Insert_SFDelayInfo(v8->m_pWorldDB, dwSeriala, pSFDelay) )
  {
    result = 0;
  }
  else
  {
    result = 24;
  }
  return result;
}
