/*
 * Function: ?SimultaneousExponentiate@?$DL_GroupParameters_EC@VEC2N@CryptoPP@@@CryptoPP@@UEBAXPEAUEC2NPoint@2@AEBU32@PEBVInteger@2@I@Z
 * Address: 0x1405841C0
 */

int __fastcall CryptoPP::DL_GroupParameters_EC<CryptoPP::EC2N>::SimultaneousExponentiate(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  __int64 v4; // rax@1
  __int64 v6; // [sp+58h] [bp+10h]@1
  __int64 v7; // [sp+60h] [bp+18h]@1
  __int64 v8; // [sp+68h] [bp+20h]@1

  v8 = a4;
  v7 = a3;
  v6 = a2;
  LODWORD(v4) = CryptoPP::DL_GroupParameters_EC<CryptoPP::EC2N>::GetCurve(a1);
  return (*(int (__fastcall **)(__int64, __int64, __int64, __int64))(*(_QWORD *)v4 + 96i64))(v4, v6, v7, v8);
}
