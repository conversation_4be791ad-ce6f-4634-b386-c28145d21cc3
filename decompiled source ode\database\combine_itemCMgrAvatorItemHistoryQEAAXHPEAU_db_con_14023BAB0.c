/*
 * Function: ?combine_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@PEAEE0KKPEAD@Z
 * Address: 0x14023BAB0
 */

void __fastcall CMgrAvatorItemHistory::combine_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pMaterial, char *pbyMtrNum, char byMaterialNum, _STORAGE_LIST::_db_con *pMakeItem, unsigned int dwFee, unsigned int dwLeftDalant, char *pszFileName)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  char *v11; // rax@4
  __int64 v12; // [sp+0h] [bp-88h]@1
  char *v13; // [sp+20h] [bp-68h]@4
  unsigned __int64 v14; // [sp+28h] [bp-60h]@4
  unsigned int v15; // [sp+30h] [bp-58h]@4
  unsigned int v16; // [sp+38h] [bp-50h]@4
  char *v17; // [sp+40h] [bp-48h]@4
  char *v18; // [sp+48h] [bp-40h]@4
  _base_fld *v19; // [sp+50h] [bp-38h]@4
  int j; // [sp+58h] [bp-30h]@4
  _base_fld *v21; // [sp+60h] [bp-28h]@6
  char *v22; // [sp+68h] [bp-20h]@4
  char *v23; // [sp+70h] [bp-18h]@4
  int nTableCode; // [sp+78h] [bp-10h]@4
  CMgrAvatorItemHistory *v25; // [sp+90h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v26; // [sp+A0h] [bp+18h]@1
  char *v27; // [sp+A8h] [bp+20h]@1

  v27 = pbyMtrNum;
  v26 = pMaterial;
  v25 = this;
  v9 = &v12;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  sData[0] = 0;
  v19 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pMakeItem->m_byTableCode, pMakeItem->m_wItemIndex);
  v22 = v25->m_szCurTime;
  v23 = v25->m_szCurDate;
  nTableCode = pMakeItem->m_byTableCode;
  v11 = DisplayItemUpgInfo(nTableCode, pMakeItem->m_dwLv);
  v18 = v22;
  v17 = v23;
  v16 = dwLeftDalant;
  v15 = dwFee;
  v14 = pMakeItem->m_lnUID;
  v13 = v11;
  sprintf(sBuf, "COMBINE: %s_%u_@%s[%I64u] pay(D:%u) $D:%u [%s %s]\r\n", v19->m_strCode, pMakeItem->m_dwDur);
  strcat_0(sData, sBuf);
  for ( j = 0; j < (unsigned __int8)byMaterialNum; ++j )
  {
    v21 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v26[j].m_byTableCode, v26[j].m_wItemIndex);
    sprintf(sBuf, "\t- %s_%d\r\n", v21->m_strCode, (unsigned __int8)v27[j]);
    strcat_0(sData, sBuf);
  }
  CMgrAvatorItemHistory::WriteFile(v25, pszFileName, sData);
}
