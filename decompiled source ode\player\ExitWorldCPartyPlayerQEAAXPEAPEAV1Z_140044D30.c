/*
 * Function: ?ExitWorld@CPartyPlayer@@QEAAXPEAPEAV1@@Z
 * Address: 0x140044D30
 */

void __fastcall CPartyPlayer::ExitWorld(CPartyPlayer *this, CPartyPlayer **ppoutNewBoss)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CPartyPlayer *pExiter; // [sp+30h] [bp+8h]@1
  CPartyPlayer **ppoutNewBossa; // [sp+38h] [bp+10h]@1

  ppoutNewBossa = ppoutNewBoss;
  pExiter = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  pExiter->m_bLogin = 0;
  pExiter->m_id.dwSerial = -1;
  if ( CPartyPlayer::IsPartyMode(pExiter) )
    CPartyPlayer::RemovePartyMember(pExiter->m_pPartyBoss, pExiter, ppoutNewBossa);
}
