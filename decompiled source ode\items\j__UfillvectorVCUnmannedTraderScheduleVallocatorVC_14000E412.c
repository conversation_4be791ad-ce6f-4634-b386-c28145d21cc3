/*
 * Function: j_?_Ufill@?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@IEAAPEAVCUnmannedTraderSchedule@@PEAV3@_KAEBV3@@Z
 * Address: 0x14000E412
 */

CUnmannedTraderSchedule *__fastcall std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Ufill(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, CUnmannedTraderSchedule *_Ptr, unsigned __int64 _Count, CUnmannedTraderSchedule *_Val)
{
  return std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Ufill(this, _Ptr, _Count, _Val);
}
