/*
 * Function: ?CheckName<PERSON><PERSON><PERSON>@CPlayer@@QEAAXXZ
 * Address: 0x140069080
 */

void __fastcall CPlayer::CheckNameChange(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@11
  unsigned int v4; // eax@16
  char *v5; // rax@17
  char *v6; // rax@18
  __int64 v7; // [sp+0h] [bp-48h]@1
  int v8; // [sp+20h] [bp-28h]@5
  int j; // [sp+24h] [bp-24h]@12
  CPlayer *v10; // [sp+28h] [bp-20h]@9
  _BUDDY_LIST::__list *v11; // [sp+30h] [bp-18h]@15
  int v12; // [sp+38h] [bp-10h]@11
  CPlayer *v13; // [sp+50h] [bp+8h]@1

  v13 = this;
  v1 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v13->m_NameChangeBuddyInfo.bNameChange )
  {
    v8 = 0;
    while ( v13->m_NameChangeBuddyInfo.nSendNum < 2532 && v8 < 100 )
    {
      ++v8;
      v10 = &g_Player + v13->m_NameChangeBuddyInfo.nSendNum;
      if ( v10->m_bLive )
      {
        if ( v13 != v10 )
        {
          v12 = CPlayerDB::GetRaceCode(&v10->m_Param);
          v3 = CPlayerDB::GetRaceCode(&v13->m_Param);
          if ( v12 == v3 )
          {
            for ( j = 0; j < 50; ++j )
            {
              v11 = &v10->m_pmBuddy.m_List[j];
              if ( _BUDDY_LIST::__list::fill(v11) )
              {
                v4 = CPlayerDB::GetCharSerial(&v13->m_Param);
                if ( v4 == v11->dwSerial )
                {
                  v5 = CPlayerDB::GetCharNameW(&v13->m_Param);
                  if ( strcmp_0(v11->wszName, v5) )
                  {
                    v6 = CPlayerDB::GetCharNameW(&v13->m_Param);
                    strcpy_s<17>((char (*)[17])v11->wszName, v6);
                    CPlayer::SendMsg_BuddyNameReNewal(v10, v11->dwSerial, v11->wszName);
                    break;
                  }
                }
              }
            }
          }
        }
      }
      ++v13->m_NameChangeBuddyInfo.nSendNum;
    }
    if ( v13->m_NameChangeBuddyInfo.nSendNum >= 2532 )
      _NameChangeBuddyInfo::Init(&v13->m_NameChangeBuddyInfo);
  }
}
