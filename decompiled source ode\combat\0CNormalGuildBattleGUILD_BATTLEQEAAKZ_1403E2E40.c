/*
 * Function: ??0CNormalGuildBattle@GUILD_BATTLE@@QEAA@K@Z
 * Address: 0x1403E2E40
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::CNormalGuildBattle(GUILD_BATTLE::CNormalGuildBattle *this, unsigned int dwID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  __int64 v5; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattle *v6; // [sp+40h] [bp+8h]@1
  unsigned int v7; // [sp+48h] [bp+10h]@1

  v7 = dwID;
  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = -2i64;
  GUILD_BATTLE::CGuildBattle::CGuildBattle((GUILD_BATTLE::CGuildBattle *)&v6->vfptr);
  v6->vfptr = (GUILD_BATTLE::CGuildBattleVtbl *)&GUILD_BATTLE::CNormalGuildBattle::`vftable';
  v6->m_dwID = v7;
  GUILD_BATTLE::CNormalGuildBattleLogger::CNormalGuildBattleLogger(&v6->m_kLogger);
  GUILD_BATTLE::CNormalGuildBattleGuild::CNormalGuildBattleGuild(&v6->m_k1P, 0);
  GUILD_BATTLE::CNormalGuildBattleGuild::CNormalGuildBattleGuild(&v6->m_k2P, 1);
  GUILD_BATTLE::CNormalGuildBattle::Clear(v6);
}
