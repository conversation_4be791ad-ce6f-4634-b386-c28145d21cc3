/*
 * Function: ?invoke@?$void2type@P8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@lua_tinker@@SAP8CMonster@@EAAPEAVCLuaSignalReActor@@XZPEAX@Z
 * Address: 0x14040AC30
 */

CLuaSignalReActor *(__cdecl *__fastcall lua_tinker::void2type<CLuaSignalReActor * (CMonster::*)(void)>::invoke(lua_tinker::void2type<CLuaSignalReActor * (__cdecl CMonster::*)(void)> *this, void *ptr))(CMonster *this)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  lua_tinker::void2val<CLuaSignalReActor * (__cdecl CMonster::*)(void)> *v6; // [sp+30h] [bp+8h]@1

  v6 = (lua_tinker::void2val<CLuaSignalReActor * (__cdecl CMonster::*)(void)> *)this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return lua_tinker::void2val<CLuaSignalReActor * (CMonster::*)(void)>::invoke(v6, ptr);
}
