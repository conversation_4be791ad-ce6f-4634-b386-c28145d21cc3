/*
 * Function: ?CompleteBuy@CUnmannedTraderUserInfoTable@@QEAAXEPEADPEAVCUnmannedTraderTradeInfo@@@Z
 * Address: 0x140363F80
 */

void __fastcall CUnmannedTraderUserInfoTable::CompleteBuy(CUnmannedTraderUserInfoTable *this, char byRet, char *pLoadData, CUnmannedTraderTradeInfo *pkTaradInfo)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v6; // ax@19
  __int64 v7; // [sp+0h] [bp-2A8h]@1
  _qry_case_unmandtrader_buy_update_wait::__list *pkQueryList; // [sp+20h] [bp-288h]@7
  _unmannedtrader_buy_item_result_zocl::__list *pSendResultList; // [sp+28h] [bp-280h]@7
  _qry_case_unmandtrader_buy_update_complete::__list *pUpdateCompleteList; // [sp+30h] [bp-278h]@7
  char *byCompleteUpdateNum; // [sp+38h] [bp-270h]@7
  unsigned int *pdwPayDalant; // [sp+40h] [bp-268h]@7
  CUnmannedTraderTradeInfo *pkTaradInfoa; // [sp+48h] [bp-260h]@7
  _qry_case_unmandtrader_buy_update_wait *pkQuery; // [sp+50h] [bp-258h]@4
  CUnmannedTraderUserInfo *ppkBuyUser; // [sp+68h] [bp-240h]@4
  CPlayer *ppkBuyPlayer; // [sp+88h] [bp-220h]@4
  _unmannedtrader_buy_item_result_zocl Dst; // [sp+B0h] [bp-1F8h]@5
  char pQryData[2]; // [sp+1C0h] [bp-E8h]@5
  unsigned int v19; // [sp+1C4h] [bp-E4h]@5
  char v20; // [sp+1C8h] [bp-E0h]@5
  char v21; // [sp+1CAh] [bp-DEh]@5
  char v22; // [sp+1CBh] [bp-DDh]@7
  _qry_case_unmandtrader_buy_update_complete::__list v23[10]; // [sp+1CCh] [bp-DCh]@7
  unsigned __int8 v24; // [sp+274h] [bp-34h]@5
  unsigned __int8 v25; // [sp+275h] [bp-33h]@5
  int j; // [sp+278h] [bp-30h]@5
  char pbyType; // [sp+284h] [bp-24h]@19
  char v28; // [sp+285h] [bp-23h]@19
  CUnmannedTraderUserInfoTable *v29; // [sp+2B0h] [bp+8h]@1
  CUnmannedTraderTradeInfo *v30; // [sp+2C8h] [bp+20h]@1

  v30 = pkTaradInfo;
  v29 = this;
  v4 = &v7;
  for ( i = 168i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  pkQuery = (_qry_case_unmandtrader_buy_update_wait *)pLoadData;
  ppkBuyUser = 0i64;
  ppkBuyPlayer = 0i64;
  if ( CUnmannedTraderUserInfoTable::SubCompleteBuyFindBuyer(
         v29,
         (_qry_case_unmandtrader_buy_update_wait *)pLoadData,
         &ppkBuyUser,
         &ppkBuyPlayer) )
  {
    CUnmannedTraderUserInfo::ClearRequest(ppkBuyUser);
    _unmannedtrader_buy_item_result_zocl::_unmannedtrader_buy_item_result_zocl(&Dst);
    memset_0(&Dst, 0, 0xE6ui64);
    Dst.byRetCode = 0;
    Dst.byNum = pkQuery->byNum;
    memset_0(pQryData, 0, 0xACui64);
    *(_WORD *)pQryData = ppkBuyPlayer->m_ObjID.m_wIndex;
    v19 = pkQuery->dwBuyer;
    v20 = CPlayerDB::GetRaceCode(&ppkBuyPlayer->m_Param);
    v21 = pkQuery->byType;
    v24 = 0;
    v25 = 0;
    for ( j = 0; j < pkQuery->byNum; ++j )
    {
      pkTaradInfoa = v30;
      pdwPayDalant = &Dst.dwPayDalant;
      byCompleteUpdateNum = &v22;
      pUpdateCompleteList = &v23[j];
      pSendResultList = &Dst.List[j];
      pkQueryList = &pkQuery->List[j];
      if ( CUnmannedTraderUserInfoTable::SubCompleteBuyProcBuy(
             v29,
             ppkBuyPlayer,
             ppkBuyUser,
             pkQuery->tResultTime,
             pkQueryList,
             pSendResultList,
             pUpdateCompleteList,
             &v22,
             &Dst.dwPayDalant,
             v30) )
      {
        ++v24;
      }
      else if ( v23[j].byUpdateState == 8 )
      {
        ++v25;
      }
    }
    if ( (signed int)v25 > 0 || (signed int)v24 > 0 )
      CUnmannedTraderUserInfoTable::SubCompleteBuyIncreaseVesion(v29, pkQuery->byDivision, pkQuery->byClass);
    if ( (signed int)(unsigned __int8)v22 > 0 )
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 68, pQryData, 172);
    if ( v24 < (signed int)(unsigned __int8)Dst.byNum )
      Dst.byRetCode = -1;
    Dst.dwLeftDalant = CPlayerDB::GetDalant(&ppkBuyPlayer->m_Param);
    pbyType = 30;
    v28 = 31;
    v6 = _unmannedtrader_buy_item_result_zocl::size(&Dst);
    CNetProcess::LoadSendMsg(unk_1414F2088, ppkBuyPlayer->m_ObjID.m_wIndex, &pbyType, &Dst.byRetCode, v6);
  }
}
