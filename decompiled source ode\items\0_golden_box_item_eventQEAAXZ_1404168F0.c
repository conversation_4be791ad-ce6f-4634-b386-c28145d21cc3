/*
 * Function: ??0_golden_box_item_event@@QEAA@XZ
 * Address: 0x1404168F0
 */

void __fastcall _golden_box_item_event::_golden_box_item_event(_golden_box_item_event *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  _golden_box_item_event *Dst; // [sp+40h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CNetTimer::CNetTimer(&Dst->m_event_timer);
  CLogFile::CLogFile(&Dst->m_event_log);
  _golden_box_item_ini::_golden_box_item_ini(&Dst->m_ini);
  memset_0(Dst, 0, 0x3730ui64);
}
