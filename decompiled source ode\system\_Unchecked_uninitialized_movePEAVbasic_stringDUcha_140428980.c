/*
 * Function: ??$_Unchecked_uninitialized_move@PEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAV12@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@stdext@@YAPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAV12@00AEAV?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@Z
 * Address: 0x140428980
 */

std::basic_string<char,std::char_traits<char>,std::allocator<char> > *__fastcall stdext::_Unchecked_uninitialized_move<std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_First, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_Last, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_Dest, std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  std::_Swap_move_tag v9; // [sp+31h] [bp-17h]@4
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_Firsta; // [sp+50h] [bp+8h]@1
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_Lasta; // [sp+58h] [bp+10h]@1
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *__formal; // [sp+60h] [bp+18h]@1
  std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *_Ala; // [sp+68h] [bp+20h]@1

  _Ala = _Al;
  __formal = _Dest;
  _Lasta = _Last;
  _Firsta = _First;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  v9 = std::_Move_cat<std::basic_string<char,std::char_traits<char>,std::allocator<char>> *>(&__formal);
  return std::_Uninit_move<std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(
           _Firsta,
           _Lasta,
           __formal,
           _Ala,
           v9,
           v8);
}
