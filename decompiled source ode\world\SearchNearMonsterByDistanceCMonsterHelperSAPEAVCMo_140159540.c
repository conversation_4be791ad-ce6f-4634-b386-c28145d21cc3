/*
 * Function: ?SearchNearMonsterByDistance@CMonsterHelper@@SAPEAVCMonster@@PEAV2@K@Z
 * Address: 0x140159540
 */

CMonster *__fastcall CMonsterHelper::SearchNearMonsterByDistance(CMonster *pMon, unsigned int dwDist)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CMonster *result; // rax@5
  int v5; // eax@6
  _sec_info *v6; // rax@11
  float v7; // xmm0_4@17
  __int64 v8; // [sp+0h] [bp-98h]@1
  float v9; // [sp+20h] [bp-78h]@4
  CMonster *v10; // [sp+28h] [bp-70h]@4
  _pnt_rect pRect; // [sp+38h] [bp-60h]@6
  int j; // [sp+54h] [bp-44h]@6
  int k; // [sp+58h] [bp-40h]@8
  unsigned int dwSecIndex; // [sp+5Ch] [bp-3Ch]@11
  CObjectList *v15; // [sp+60h] [bp-38h]@11
  _object_list_point *v16; // [sp+68h] [bp-30h]@12
  CMonster *v17; // [sp+70h] [bp-28h]@14
  char *v18; // [sp+78h] [bp-20h]@14
  float v19; // [sp+80h] [bp-18h]@18
  CMonster *v20; // [sp+A0h] [bp+8h]@1

  v20 = pMon;
  v2 = &v8;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (float)(signed int)dwDist;
  v10 = 0i64;
  if ( v20 )
  {
    v5 = CGameObject::GetCurSecNum((CGameObject *)&v20->vfptr);
    CMapData::GetRectInRadius(v20->m_pCurMap, &pRect, 1, v5);
    for ( j = pRect.nStarty; j <= pRect.nEndy; ++j )
    {
      for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
      {
        v6 = CMapData::GetSecInfo(v20->m_pCurMap);
        dwSecIndex = v6->m_nSecNumW * j + k;
        v15 = CMapData::GetSectorListObj(v20->m_pCurMap, v20->m_wMapLayerIndex, dwSecIndex);
        if ( v15 )
        {
          v16 = v15->m_Head.m_pNext;
          while ( v16 != &v15->m_Tail )
          {
            v17 = (CMonster *)v16->m_pItem;
            v16 = v16->m_pNext;
            v18 = &v17->m_ObjID.m_byKind;
            if ( !v17->m_ObjID.m_byKind && v18[1] == 1 && *((_WORD *)v18 + 1) != v20->m_ObjID.m_wIndex )
            {
              v7 = v17->m_fCurPos[1] - v20->m_fCurPos[1];
              abs(v7);
              if ( v7 <= 50.0 )
              {
                GetSqrt(v20->m_fCurPos, v17->m_fCurPos);
                v19 = v7;
                if ( v9 > v7 )
                {
                  v9 = v19;
                  v10 = v17;
                }
              }
            }
          }
        }
      }
    }
    result = v10;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
