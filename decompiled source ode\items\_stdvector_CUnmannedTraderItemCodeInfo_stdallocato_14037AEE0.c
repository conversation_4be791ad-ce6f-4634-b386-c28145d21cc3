/*
 * Function: _std::vector_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo___::_Insert_std::_Vector_const_iterator_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo______::_1_::catch$0
 * Address: 0x14037AEE0
 */

void __fastcall __noreturn std::vector_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo___::_Insert_std::_Vector_const_iterator_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo______::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1

  v2 = a2;
  std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Destroy(
    *(std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > **)(a2 + 736),
    *(CUnmannedTraderItemCodeInfo **)(a2 + 64),
    *(CUnmannedTraderItemCodeInfo **)(a2 + 72));
  std::allocator<CUnmannedTraderItemCodeInfo>::deallocate(
    (std::allocator<CUnmannedTraderItemCodeInfo> *)(*(_QWORD *)(v2 + 736) + 8i64),
    *(CUnmannedTraderItemCodeInfo **)(v2 + 64),
    *(_QWORD *)(v2 + 56));
  CxxThrowException_0(0i64, 0i64);
}
