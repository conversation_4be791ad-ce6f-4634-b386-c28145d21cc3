/*
 * Function: ??0_action_point_system_ini@@QEAA@XZ
 * Address: 0x140411DE0
 */

void __fastcall _action_point_system_ini::_action_point_system_ini(_action_point_system_ini *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _action_point_system_ini *Dst; // [sp+30h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(Dst, 0, 0x38ui64);
}
