/*
 * Function: ?SendMsg_ClassSkillResult@CPlayer@@QEAAXEPEAU_CHRID@@G@Z
 * Address: 0x1400DFB00
 */

void __fastcall CPlayer::SendMsg_ClassSkillResult(CPlayer *this, char byErrCode, _CHRID *pidDst, unsigned __int16 wSkillIndex)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-C8h]@1
  char szMsg; // [sp+34h] [bp-94h]@4
  char pbyType; // [sp+54h] [bp-74h]@4
  char v9; // [sp+55h] [bp-73h]@4
  char v10; // [sp+78h] [bp-50h]@9
  char v11; // [sp+79h] [bp-4Fh]@9
  unsigned __int16 v12; // [sp+7Ah] [bp-4Eh]@9
  unsigned int v13; // [sp+7Ch] [bp-4Ch]@9
  char Dst; // [sp+80h] [bp-48h]@9
  unsigned __int16 v15; // [sp+87h] [bp-41h]@9
  char v16; // [sp+A4h] [bp-24h]@9
  char v17; // [sp+A5h] [bp-23h]@9
  CPlayer *v18; // [sp+D0h] [bp+8h]@1
  char v19; // [sp+D8h] [bp+10h]@1
  _CHRID *Src; // [sp+E0h] [bp+18h]@1
  unsigned __int16 v21; // [sp+E8h] [bp+20h]@1

  v21 = wSkillIndex;
  Src = pidDst;
  v19 = byErrCode;
  v18 = this;
  v4 = &v6;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = byErrCode;
  pbyType = 17;
  v9 = 8;
  CNetProcess::LoadSendMsg(unk_1414F2088, v18->m_ObjID.m_wIndex, &pbyType, &szMsg, 2u);
  if ( (!v19 || v19 == 100)
    && (v18->m_nCirclePlayerNum <= 500 || v18->m_ObjID.m_byID != Src->byID || v18->m_dwObjSerial != Src->dwSerial) )
  {
    v10 = v19;
    memcpy_0(&Dst, Src, 7ui64);
    v11 = v18->m_ObjID.m_byID;
    v12 = v18->m_ObjID.m_wIndex;
    v13 = v18->m_dwObjSerial;
    v15 = v21;
    v16 = 17;
    v17 = 9;
    CGameObject::CircleReport((CGameObject *)&v18->vfptr, &v16, &v10, 18, 0);
  }
}
