/*
 * Function: ??0HashInputTooLong@CryptoPP@@QEAA@AEBV01@@Z
 * Address: 0x14058A4D0
 */

CryptoPP::HashInputTooLong *__fastcall CryptoPP::HashInputTooLong::HashInputTooLong(CryptoPP::HashInputTooLong *this, const struct CryptoPP::HashInputTooLong *a2)
{
  CryptoPP::HashInputTooLong *v3; // [sp+30h] [bp+8h]@1

  v3 = this;
  CryptoPP::InvalidDataFormat::InvalidDataFormat(
    (CryptoPP::InvalidDataFormat *)&this->vfptr,
    (CryptoPP::InvalidDataFormat *)&a2->vfptr);
  v3->vfptr = (std::exceptionVtbl *)&CryptoPP::HashInputTooLong::`vftable';
  return v3;
}
