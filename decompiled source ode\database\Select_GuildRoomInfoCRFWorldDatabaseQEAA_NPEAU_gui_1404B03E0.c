/*
 * Function: ?Select_GuildRoomInfo@CRFWorldDatabase@@QEAA_NPEAU_guildroom_info@@@Z
 * Address: 0x1404B03E0
 */

char __fastcall CRFWorldDatabase::Select_GuildRoomInfo(CRFWorldDatabase *this, _guildroom_info *pInfo)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@17
  SQLLEN v8; // [sp+38h] [bp-150h]@17
  __int16 v9; // [sp+44h] [bp-144h]@9
  unsigned __int8 v10; // [sp+48h] [bp-140h]@4
  char Dst; // [sp+60h] [bp-128h]@4
  unsigned __int64 v12; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v13; // [sp+190h] [bp+8h]@1
  _guildroom_info *v14; // [sp+198h] [bp+10h]@1

  v14 = pInfo;
  v13 = this;
  v2 = &v5;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = (unsigned __int64)&v5 ^ _security_cookie;
  v10 = 0;
  memset_0(&Dst, 0, 0x100ui64);
  sprintf_s<256>(
    (char (*)[256])&Dst,
    "select gr.guildserial, g.id, gr.roomtype, gr.roomrace, gr.roomrentdate from tbl_GuildRoom as gr inner join tbl_Guild"
    " as g on gr.guildserial = g.serial where gr.dck = 0 order by g.serial desc");
  if ( v13->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v13->vfptr, &Dst);
  if ( v13->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v13->vfptr) )
  {
    v9 = SQLExecDirect_0(v13->m_hStmtSelect, &Dst, -3);
    if ( v9 && v9 != 1 )
    {
      if ( v9 == 100 )
      {
        result = 0;
      }
      else
      {
        SQLStmt = v13->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v9, &Dst, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v9, v13->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      v10 = 0;
      do
      {
        v9 = SQLFetch_0(v13->m_hStmtSelect);
        if ( v9 && v9 != 1 )
          break;
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v13->m_hStmtSelect, 1u, -18, &v14->info[v10], 0i64, &v8);
        StrLen_or_IndPtr = &v8;
        SQLStmt = (void *)17;
        v9 = SQLGetData_0(v13->m_hStmtSelect, 2u, 1, v14->info[v10].uszGuildName, 17i64, &v8);
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v13->m_hStmtSelect, 3u, -6, &v14->info[v10].byRoomType, 0i64, &v8);
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v13->m_hStmtSelect, 4u, -6, &v14->info[v10].byRace, 0i64, &v8);
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v13->m_hStmtSelect, 5u, 93, &v14->info[v10++].ts, 0i64, &v8);
        if ( (signed int)v10 >= 90 )
          break;
      }
      while ( !v9 || v9 == 1 );
      v14->byCount = v10;
      if ( v13->m_hStmtSelect )
        SQLCloseCursor_0(v13->m_hStmtSelect);
      if ( v13->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v13->vfptr, "%s Success", &Dst);
      result = 1;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v13->vfptr, "ReConnectDataBase Fail. Query : %s", &Dst);
    result = 0;
  }
  return result;
}
