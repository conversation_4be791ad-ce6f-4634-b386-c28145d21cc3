/*
 * Function: ?CleanUp@CMoveMapLimitRightInfo@@AEAAXXZ
 * Address: 0x1403AD710
 */

void __fastcall CMoveMapLimitRightInfo::CleanUp(CMoveMapLimitRightInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v3; // rax@5
  __int64 v4; // [sp+0h] [bp-58h]@1
  int j; // [sp+20h] [bp-38h]@4
  CMoveMapLimitRight *v6; // [sp+28h] [bp-30h]@6
  CMoveMapLimitRight *v7; // [sp+30h] [bp-28h]@6
  unsigned __int64 v8; // [sp+38h] [bp-20h]@5
  CMoveMapLimitRight *v9; // [sp+40h] [bp-18h]@6
  void *v10; // [sp+48h] [bp-10h]@7
  CMoveMapLimitRightInfo *v11; // [sp+60h] [bp+8h]@1

  v11 = this;
  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; ; ++j )
  {
    v8 = j;
    v3 = std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::size(&v11->m_vecRight);
    if ( v8 >= v3 )
      break;
    v9 = *std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator[](&v11->m_vecRight, j);
    ((void (__fastcall *)(CMoveMapLimitRight *))v9->vfptr->CleanUp)(v9);
    v7 = *std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator[](&v11->m_vecRight, j);
    v6 = v7;
    if ( v7 )
      v10 = CMoveMapLimitRight::`scalar deleting destructor'(v6, 1u);
    else
      v10 = 0i64;
  }
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::clear(&v11->m_vecRight);
}
