/*
 * Function: ?SendFirst@CPossibleBattleGuildListManager@GUILD_BATTLE@@QEAAXHE@Z
 * Address: 0x1403D9920
 */

void __fastcall GUILD_BATTLE::CPossibleBattleGuildListManager::SendFirst(GUILD_BATTLE::CPossibleBattleGuildListManager *this, int n, char byRace)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CPossibleBattleGuildListManager *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  GUILD_BATTLE::CPossibleBattleGuildListManager::SendInfo(v6, n, byRace, 0, 0);
}
