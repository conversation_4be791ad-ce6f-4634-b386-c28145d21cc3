/*
 * Function: ??0?$_Vector_val@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@IEAA@V?$allocator@PEAVCMoveMapLimitInfo@@@1@@Z
 * Address: 0x1403A2530
 */

void __fastcall std::_Vector_val<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Vector_val<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(std::_Vector_val<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, __int64 _Al)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Vector_val<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v5; // [sp+30h] [bp+8h]@1
  std::allocator<CMoveMapLimitInfo *> *__formal; // [sp+38h] [bp+10h]@1

  __formal = (std::allocator<CMoveMapLimitInfo *> *)_Al;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Container_base::_Container_base((std::_Container_base *)&v5->_Myfirstiter);
  std::allocator<CMoveMapLimitInfo *>::allocator<CMoveMapLimitInfo *>(&v5->_Alval, __formal);
}
