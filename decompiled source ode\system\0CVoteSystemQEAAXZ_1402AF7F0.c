/*
 * Function: ??0CVoteSystem@@QEAA@XZ
 * Address: 0x1402AF7F0
 */

void __fastcall CVoteSystem::CVoteSystem(CVoteSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CVoteSystem *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CNetIndexList::CNetIndexList(&v5->m_listVote);
  _started_vote_inform_zocl::_started_vote_inform_zocl(&v5->m_SendStarted);
  v5->m_bActive = 0;
  v5->m_nSerial = 0;
  v5->m_byRaceCode = -1;
  CNetIndexList::SetList(&v5->m_listVote, 0x13C8u);
}
