/*
 * Function: ?DrawCollisionLine@CMapDisplay@@AEAAXXZ
 * Address: 0x1401A02A0
 */

void __fastcall CMapDisplay::DrawCollisionLine(CMapDisplay *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int v4; // [sp+20h] [bp-18h]@4
  CRect *prcArea; // [sp+28h] [bp-10h]@4
  CMapDisplay *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = CMapOperation::GetMap(&g_MapOper, v6->m_pActMap);
  prcArea = 0i64;
  if ( v6->m_MapExtend.m_bExtendMode )
    prcArea = &v6->m_MapExtend.m_rcExtend;
  CCollLineDraw::Draw(&v6->m_CollLineDraw[(signed __int64)v4], v6->m_pSFMap, prcArea);
}
