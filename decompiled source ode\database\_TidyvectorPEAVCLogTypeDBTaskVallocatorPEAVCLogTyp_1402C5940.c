/*
 * Function: ?_Tidy@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@IEAAXXZ
 * Address: 0x1402C5940
 */

void __fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Tidy(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v4->_Myfirst )
  {
    std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Destroy(v4, v4->_Myfirst, v4->_Mylast);
    std::allocator<CLogTypeDBTask *>::deallocate(&v4->_Alval, v4->_Myfirst, v4->_Myend - v4->_Myfirst);
  }
  v4->_Myfirst = 0i64;
  v4->_Mylast = 0i64;
  v4->_Myend = 0i64;
}
