/*
 * Function: ?Init@CReservedGuildScheduleMapGroup@GUILD_BATTLE@@QEAA_NI@Z
 * Address: 0x1403CC420
 */

char __fastcall GUILD_BATTLE::CReservedGuildScheduleMapGroup::Init(GUILD_BATTLE::CReservedGuildScheduleMapGroup *this, unsigned int uiMapInx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CReservedGuildScheduleMapGroup *v7; // [sp+40h] [bp+8h]@1
  unsigned int v8; // [sp+48h] [bp+10h]@1

  v8 = uiMapInx;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 6; ++j )
  {
    if ( !GUILD_BATTLE::CReservedGuildSchedulePage::Init(&v7->m_kList[j], j) )
      return 0;
  }
  v7->m_uiMapInx = v8;
  v7->m_byMaxPage = 0;
  return 1;
}
