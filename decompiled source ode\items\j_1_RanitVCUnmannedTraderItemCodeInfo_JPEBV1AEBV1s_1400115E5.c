/*
 * Function: j_??1?$_<PERSON>t@VCUnmannedTraderItemCodeInfo@@_JPEBV1@AEBV1@@std@@QEAA@XZ
 * Address: 0x1400115E5
 */

void __fastcall std::_<PERSON>t<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &>::~_<PERSON>t<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &>(std::_Ranit<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &> *this)
{
  std::_Ranit<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &>::~_Ranit<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &>(this);
}
