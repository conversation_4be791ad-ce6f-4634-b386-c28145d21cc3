/*
 * Function: ?ConfigUserODBC@CRFNewDatabase@@QEAA_NPEBD00G@Z
 * Address: 0x1404880C0
 */

bool __fastcall CRFNewDatabase::ConfigUserODBC(CRFNewDatabase *this, const char *szDSN, const char *szServer, const char *szDatabase, unsigned __int16 wPort)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v7; // ax@4
  __int16 v8; // ax@4
  __int16 v9; // ax@4
  __int16 v10; // ax@4
  __int64 v12; // [sp+0h] [bp-178h]@1
  __int64 v13; // [sp+20h] [bp-158h]@4
  unsigned __int16 v14; // [sp+30h] [bp-148h]@4
  char Dst[272]; // [sp+50h] [bp-128h]@4
  unsigned __int64 v16; // [sp+160h] [bp-18h]@4
  const char *v17; // [sp+188h] [bp+10h]@1
  const char *v18; // [sp+190h] [bp+18h]@1
  const char *v19; // [sp+198h] [bp+20h]@1

  v19 = szDatabase;
  v18 = szServer;
  v17 = szDSN;
  v5 = &v12;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v16 = (unsigned __int64)&v12 ^ _security_cookie;
  v14 = 0;
  memset_0(Dst, 0, 0x100ui64);
  v7 = sprintf(Dst, "DSN=%s%c", v17, 0i64);
  v14 = v7;
  v8 = sprintf(&Dst[v7], "DESCRIPTION=%s%c", v19, 0i64);
  v14 += v8;
  v13 = 0i64;
  v9 = sprintf(&Dst[v14], "SERVER=%s,%u%c", v18, wPort);
  v14 += v9;
  v10 = sprintf(&Dst[v14], "DATABASE=%s%c", v19, 0i64);
  v14 += v10;
  return SQLConfigDataSource(0i64, 4u, "SQL Server", Dst) != 0;
}
