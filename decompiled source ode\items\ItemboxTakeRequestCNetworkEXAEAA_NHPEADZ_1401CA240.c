/*
 * Function: ?ItemboxTakeRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CA240
 */

char __fastcall CNetworkEX::ItemboxTakeRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@9
  __int64 v7; // [sp+0h] [bp-48h]@1
  char *v8; // [sp+20h] [bp-28h]@4
  CPlayer *v9; // [sp+28h] [bp-20h]@4
  CItemBox *pBox; // [sp+30h] [bp-18h]@12
  CNetworkEX *v11; // [sp+50h] [bp+8h]@1

  v11 = this;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = pBuf;
  v9 = &g_Player + n;
  if ( !v9->m_bOper || v9->m_pmTrd.bDTradeMode || v9->m_bCorpse )
  {
    result = 1;
  }
  else if ( (signed int)*(_WORD *)v8 < 5064 )
  {
    if ( _effect_parameter::GetEff_State(&v9->m_EP, 26) )
    {
      result = 1;
    }
    else
    {
      pBox = &g_ItemBox[*(_WORD *)v8];
      CPlayer::pc_TakeGroundingItem(v9, pBox, *((_WORD *)v8 + 1));
      result = 1;
    }
  }
  else
  {
    v6 = CPlayerDB::GetCharNameA(&v9->m_Param);
    CLogFile::Write(&v11->m_LogFile, "odd.. %s: ItemboxTakeRequest()..  if(pRecv->wItemBoxIndex >= MAX_ITEMBOX)", v6);
    result = 0;
  }
  return result;
}
