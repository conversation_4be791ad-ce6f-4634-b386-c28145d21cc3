/*
 * Function: ??4?$DL_GroupParametersImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@QEAAAEAV01@AEBV01@@Z
 * Address: 0x14055E150
 */

__int64 __fastcall CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::operator=(__int64 a1, __int64 a2)
{
  __int64 v3; // [sp+30h] [bp+8h]@1
  __int64 v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::DL_GroupParameters_IntegerBased::operator=();
  CryptoPP::ModExpPrecomputation::operator=(v3 + 72, v4 + 72);
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::operator=(v3 + 88, v4 + 88);
  return v3;
}
