/*
 * Function: ?_db_update_event_classrefine@CMainThread@@QEAAEGKEK@Z
 * Address: 0x1401BFCC0
 */

char __fastcall CMainThread::_db_update_event_classrefine(CMainThread *this, unsigned __int16 wSock, unsigned int dwAvatorSerial, char byRefinedCnt, unsigned int dwRefineDate)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-28h]@1
  CMainThread *v9; // [sp+30h] [bp+8h]@1

  v9 = this;
  v5 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( CRFWorldDatabase::Update_RFEvent_ClassRefine(v9->m_pWorldDB, dwAvatorSerial, byRefinedCnt, dwRefineDate) )
    result = 0;
  else
    result = 24;
  return result;
}
