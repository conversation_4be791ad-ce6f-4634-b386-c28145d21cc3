/*
 * Function: ?Push_Data@TimeLimitMgr@@QEAAXPEAUPlayer_TL_Status@@G@Z
 * Address: 0x14040E1F0
 */

void __fastcall TimeLimitMgr::Push_Data(TimeLimitMgr *this, Player_TL_Status *data, unsigned __int16 wIndex)
{
  this->m_lstTLStaus[wIndex].m_bUse = 1;
  this->m_lstTLStaus[wIndex].m_byTL_Status = data->m_byTL_Status;
  this->m_lstTLStaus[wIndex].m_dwStartTime = data->m_dwStartTime;
  this->m_lstTLStaus[wIndex].m_dwAccountSerial = data->m_dwAccountSerial;
  this->m_lstTLStaus[wIndex].m_dwLastLogoutTime = data->m_dwLastLogoutTime;
  this->m_lstTLStaus[wIndex].m_dwFatigue = data->m_dwFatigue;
  this->m_lstTLStaus[wIndex].m_bAgeLimit = data->m_bAgeLimit;
  ++TimeLimitMgr::m_dwCnt;
}
