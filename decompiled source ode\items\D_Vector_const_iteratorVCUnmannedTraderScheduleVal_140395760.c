/*
 * Function: ??D?$_Vector_const_iterator@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@QEBAAEBVCUnmannedTraderSchedule@@XZ
 * Address: 0x140395760
 */

CUnmannedTraderSchedule *__fastcall std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator*(std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this)
{
  return this->_Myptr;
}
