/*
 * Function: ?CheatBuy@CashItemRemoteStore@@QEAA_NGPEBDH@Z
 * Address: 0x1402F5B50
 */

bool __fastcall CashItemRemoteStore::CheatBuy(CashItemRemoteStore *this, unsigned __int16 wSock, const char *szItemCode, int nNum)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v7; // [sp+0h] [bp-3E8h]@1
  char *psItemCode; // [sp+20h] [bp-3C8h]@4
  int nTbl; // [sp+28h] [bp-3C0h]@4
  _base_fld *v10; // [sp+30h] [bp-3B8h]@6
  _CashShop_fld *v11; // [sp+38h] [bp-3B0h]@8
  char pPacket; // [sp+50h] [bp-398h]@10
  int v13; // [sp+5Fh] [bp-389h]@10
  char v14; // [sp+63h] [bp-385h]@10
  char v15; // [sp+64h] [bp-384h]@10
  __int16 v16; // [sp+65h] [bp-383h]@10
  __int16 v17; // [sp+67h] [bp-381h]@10
  char v18; // [sp+69h] [bp-37Fh]@10
  CashItemRemoteStore *v19; // [sp+3F0h] [bp+8h]@1
  unsigned __int16 v20; // [sp+3F8h] [bp+10h]@1
  int v21; // [sp+408h] [bp+20h]@1

  v21 = nNum;
  v20 = wSock;
  v19 = this;
  v4 = &v7;
  for ( i = 248i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  psItemCode = (char *)szItemCode;
  nTbl = GetItemTableCode((char *)szItemCode);
  if ( nTbl == -1 )
  {
    result = 0;
  }
  else
  {
    v10 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + nTbl, psItemCode, 8);
    if ( v10 )
    {
      v11 = CashItemRemoteStore::FindCashRec(nTbl, v10->m_dwIndex);
      if ( v11 )
      {
        pPacket = 1;
        v13 = v11->m_nCsPrice;
        v14 = v11->m_nCsDiscount;
        v15 = nTbl;
        v16 = v10->m_dwIndex;
        v17 = v11->m_dwIndex;
        v18 = v21;
        result = CashItemRemoteStore::Buy(v19, v20, &pPacket);
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  return result;
}
