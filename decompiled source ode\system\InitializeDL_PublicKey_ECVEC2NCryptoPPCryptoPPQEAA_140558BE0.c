/*
 * Function: ?Initialize@?$DL_PublicKey_EC@VEC2N@CryptoPP@@@CryptoPP@@QEAAXAEBV?$DL_GroupParameters_EC@VEC2N@CryptoPP@@@2@AEBUEC2NPoint@2@@Z
 * Address: 0x140558BE0
 */

int __fastcall CryptoPP::DL_PublicKey_EC<CryptoPP::EC2N>::Initialize(__int64 a1, __int64 a2, __int64 a3)
{
  __int64 v3; // rax@1
  __int64 v5; // [sp+30h] [bp+8h]@1
  __int64 v6; // [sp+38h] [bp+10h]@1
  __int64 v7; // [sp+40h] [bp+18h]@1

  v7 = a3;
  v6 = a2;
  v5 = a1;
  LODWORD(v3) = CryptoPP::DL_KeyImpl<CryptoPP::X509PublicKey,CryptoPP::DL_GroupParameters_EC<CryptoPP::EC2N>,CryptoPP::OID>::AccessGroupParameters(a1 + 8);
  CryptoPP::DL_GroupParameters_EC<CryptoPP::EC2N>::operator=(v3, v6);
  return (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v5 + 24i64))(v5, v7);
}
