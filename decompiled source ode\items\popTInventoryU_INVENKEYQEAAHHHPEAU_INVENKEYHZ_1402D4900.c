/*
 * Function: ?pop@?$TInventory@U_INVENKEY@@@@QEAAHHHPEAU_INVENKEY@@H@Z
 * Address: 0x1402D4900
 */

int __fastcall TInventory<_INVENKEY>::pop(TInventory<_INVENKEY> *this, int nP, int nS, _INVENKEY *pItem, int nNum)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@6
  __int64 v8; // [sp+0h] [bp-28h]@1
  TInventory<_INVENKEY> *v9; // [sp+30h] [bp+8h]@1

  v9 = this;
  v5 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( pItem && v9->m_nMaxPageNum > nP )
    result = TInvenPage<_INVENKEY>::pop(&v9->m_pPage[nP], pItem, nS, nNum);
  else
    result = -1;
  return result;
}
