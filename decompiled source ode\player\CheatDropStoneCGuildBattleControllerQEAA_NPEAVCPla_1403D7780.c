/*
 * Function: ?CheatDropStone@CGuildBattleController@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x1403D7780
 */

bool __fastcall CGuildBattleController::CheatDropStone(CGuildBattleController *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleFieldList *v4; // rax@5
  bool result; // al@7
  __int64 v6; // [sp+0h] [bp-48h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v7; // [sp+20h] [bp-28h]@4
  CMapData *v8; // [sp+28h] [bp-20h]@5
  int v9; // [sp+30h] [bp-18h]@5
  int v10; // [sp+34h] [bp-14h]@8
  CPlayer *pkPlayera; // [sp+58h] [bp+10h]@1

  pkPlayera = pkPlayer;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = 0i64;
  if ( pkPlayer->m_pCurMap )
  {
    v8 = pkPlayer->m_pCurMap;
    v9 = CPlayerDB::GetRaceCode(&pkPlayer->m_Param);
    v4 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
    v7 = GUILD_BATTLE::CNormalGuildBattleFieldList::GetField(v4, v9, v8->m_nMapCode);
  }
  if ( v7 )
  {
    v10 = GUILD_BATTLE::CNormalGuildBattleField::CheatDropStone(v7, pkPlayera) == 0;
    result = v10;
  }
  else
  {
    result = 0;
  }
  return result;
}
