/*
 * Function: ??0InitializeInteger@CryptoPP@@QEAA@XZ
 * Address: 0x1405E4030
 */

CryptoPP::InitializeInteger *__fastcall CryptoPP::InitializeInteger::InitializeInteger(CryptoPP::InitializeInteger *this)
{
  CryptoPP::InitializeInteger *v2; // [sp+30h] [bp+8h]@1

  v2 = this;
  if ( !CryptoPP::g_pAssignIntToInteger )
  {
    cfltcvt_init();
    CryptoPP::g_pAssignIntToInteger = (bool (__stdcall *)(const struct type_info *, void *, const void *))CryptoPP::AssignIntToInteger;
  }
  return v2;
}
