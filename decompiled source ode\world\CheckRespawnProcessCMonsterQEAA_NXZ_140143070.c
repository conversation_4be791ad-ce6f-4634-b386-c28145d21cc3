/*
 * Function: ?CheckRespawnProcess@CMonster@@QEAA_NXZ
 * Address: 0x140143070
 */

char __usercall CMonster::CheckRespawnProcess@<al>(CMonster *this@<rcx>, float a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float v4; // xmm0_4@11
  __int64 v6; // [sp+0h] [bp-98h]@1
  unsigned int v7; // [sp+20h] [bp-78h]@4
  unsigned int v8; // [sp+24h] [bp-74h]@9
  _monster_create_setdata Dst; // [sp+40h] [bp-58h]@13
  CMonster *v10; // [sp+A0h] [bp+8h]@1

  v10 = this;
  v2 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = GetLoopTime();
  if ( v10->m_pActiveRec )
  {
    if ( !v10->m_bDungeon
      && v10->m_pMonRec->m_bMonsterCondition != 1
      && !CMonsterHierarchy::GetParent(&v10->m_MonHierarcy)
      && !CMonsterHierarchy::ChildKindCount(&v10->m_MonHierarcy) )
    {
      v8 = v7 - v10->m_LifeCicle;
      if ( v8 > v10->m_LifeMax )
      {
        GetSqrt(v10->m_fCurPos, v10->m_fCreatePos);
        if ( a2 >= 100.0 || (v4 = v10->m_fCurPos[1] - v10->m_fCreatePos[1], abs(v4), v4 >= 50.0) )
        {
          _monster_create_setdata::_monster_create_setdata(&Dst);
          memcpy_0(Dst.m_fStartPos, v10->m_fCreatePos, 0xCui64);
          Dst.m_nLayerIndex = v10->m_wMapLayerIndex;
          Dst.m_pMap = v10->m_pCurMap;
          Dst.m_pRecordSet = v10->m_pRecordSet;
          Dst.pActiveRec = v10->m_pActiveRec;
          Dst.bDungeon = v10->m_bDungeon;
          Dst.pDumPosition = v10->m_pDumPosition;
          Dst.pParent = CMonsterHierarchy::GetParent(&v10->m_MonHierarcy);
          Dst.bRobExp = v10->m_bRobExp;
          CMonster::Destroy(v10, 1, 0i64);
          CMonster::Create(v10, &Dst);
          return 1;
        }
        v10->m_LifeCicle = v7;
      }
    }
  }
  return 0;
}
