/*
 * Function: ?Frame<PERSON>ove@CLevel@@QEAAXXZ
 * Address: 0x1404E2E60
 */

void __fastcall CLevel::FrameMove(CLevel *this)
{
  CLevel *v1; // rbx@1
  struct IDirect3DDevice8 *v2; // rax@1
  struct IDirect3DDevice8 *v3; // rax@1
  float v4; // xmm0_4@1
  signed __int64 v5; // rax@1
  float v6; // [sp+20h] [bp-48h]@1
  int v7; // [sp+24h] [bp-44h]@1
  int v8; // [sp+28h] [bp-40h]@1
  int v9; // [sp+2Ch] [bp-3Ch]@1
  int v10; // [sp+30h] [bp-38h]@1
  float v11; // [sp+34h] [bp-34h]@1
  int v12; // [sp+38h] [bp-30h]@1
  int v13; // [sp+3Ch] [bp-2Ch]@1
  int v14; // [sp+40h] [bp-28h]@1
  int v15; // [sp+44h] [bp-24h]@1
  float v16; // [sp+48h] [bp-20h]@1
  int v17; // [sp+4Ch] [bp-1Ch]@1
  int v18; // [sp+50h] [bp-18h]@1
  int v19; // [sp+54h] [bp-14h]@1
  int v20; // [sp+58h] [bp-10h]@1
  float v21; // [sp+5Ch] [bp-Ch]@1

  v1 = this;
  RTMovieFrameMove();
  v20 = 0;
  v19 = 0;
  v18 = 0;
  v17 = 0;
  v15 = 0;
  v14 = 0;
  v13 = 0;
  v12 = 0;
  v10 = 0;
  v9 = 0;
  v8 = 0;
  v7 = 0;
  v21 = FLOAT_1_0;
  v16 = FLOAT_1_0;
  v11 = FLOAT_1_0;
  v6 = FLOAT_1_0;
  v2 = GetD3dDevice();
  ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, int *))v2->vfptr[12].AddRef)(v2, 256i64, (int *)&v6);
  v3 = GetD3dDevice();
  ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64))v3->vfptr[12].AddRef)(
    v3,
    2i64,
    (signed __int64)&v1->mMatView);
  SetDefaultFlags();
  SetFrustumNormalPlane();
  v4 = R3GetTime();
  SetMultiLayerTime(v4);
  CN_NatureFrameMove();
  ClearDynamicLight();
  ResetVertexBufferCache();
  CBsp::ReadyBspRender(v1->mBsp, v1->mCamPos);
  CBsp::FrameMoveEnvironment(v1->mBsp);
  CBsp::CalcObjectLoop(v1->mBsp);
  UpdateStreamMP3();
  CLevel::CalcR3Fog(v1, v5);
}
