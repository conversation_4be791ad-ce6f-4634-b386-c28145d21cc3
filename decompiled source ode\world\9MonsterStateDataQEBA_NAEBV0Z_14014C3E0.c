/*
 * Function: ??9MonsterStateData@@QEBA_NAEBV0@@Z
 * Address: 0x14014C3E0
 */

bool __fastcall MonsterStateData::operator!=(MonsterStateData *this, MonsterStateData *rhs)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // [sp+0h] [bp-18h]@1
  MonsterStateData *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  return v6->m_wSendChunkData != rhs->m_wSendChunkData;
}
