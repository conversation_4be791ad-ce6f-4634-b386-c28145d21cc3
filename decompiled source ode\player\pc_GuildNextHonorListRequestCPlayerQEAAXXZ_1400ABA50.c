/*
 * Function: ?pc_GuildNextHonorListRequest@CPlayer@@QEAAXXZ
 * Address: 0x1400ABA50
 */

void __fastcall CPlayer::pc_GuildNextHonorListRequest(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v3; // rax@4
  unsigned int v4; // eax@4
  CHonorGuild *v5; // rax@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int v7; // [sp+20h] [bp-18h]@4
  int v8; // [sp+24h] [bp-14h]@4
  int v9; // [sp+28h] [bp-10h]@5
  CPlayer *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v1 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = CPlayerDB::GetCharSerial(&v10->m_Param);
  v8 = CPlayerDB::GetRaceCode(&v10->m_Param);
  v3 = CPvpUserAndGuildRankingSystem::Instance();
  v4 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v3, v8, 0);
  if ( v7 == v4 )
  {
    v9 = CPlayerDB::GetRaceCode(&v10->m_Param);
    v5 = CHonorGuild::Instance();
    CHonorGuild::SendNextHonorGuildList(v5, v10->m_ObjID.m_wIndex, v9);
  }
}
