/*
 * Function: ?Alloc@?$CArrayEx@VCLuaLooting_Novus_Item@@U_State@1@@US@@QEAAXK@Z
 * Address: 0x140405690
 */

void __fastcall US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::Alloc(US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State> *this, unsigned int dwCount)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State> *v5; // [sp+30h] [bp+8h]@1
  unsigned int dwCounta; // [sp+38h] [bp+10h]@1

  dwCounta = dwCount;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  US::CArray<CLuaLooting_Novus_Item>::Alloc(&v5->m_DataAr, dwCount);
  US::CArray<CLuaLooting_Novus_Item::_State>::Alloc(&v5->m_StateAr, dwCounta);
}
