/*
 * Function: ?ChangeMonsterApparition@CDarkHoleChannel@@QEAAXH@Z
 * Address: 0x140269700
 */

void __fastcall CDarkHoleChannel::ChangeMonsterApparition(CDarkHoleChannel *this, int nTermMSec)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int32 v4; // ecx@12
  __int64 v5; // [sp+0h] [bp-58h]@1
  int j; // [sp+20h] [bp-38h]@4
  CMonster *v7; // [sp+28h] [bp-30h]@7
  char v8; // [sp+30h] [bp-28h]@11
  CGameObject *pOri; // [sp+38h] [bp-20h]@15
  CExtDummy *v10; // [sp+40h] [bp-18h]@12
  CDarkHoleChannel *v11; // [sp+60h] [bp+8h]@1
  int dwAfterKillTerm; // [sp+68h] [bp+10h]@1

  dwAfterKillTerm = nTermMSec;
  v11 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 30000; ++j )
  {
    v7 = (CMonster *)((char *)g_Monster + 6424 * j);
    if ( v7->m_bLive
      && v7->m_pCurMap == v11->m_pQuestSetup->pUseMap
      && v7->m_wMapLayerIndex == v11->m_wLayerIndex
      && CMonster::IsMovable(v7) )
    {
      v8 = 1;
      if ( v11->m_MissionMgr.pCurMssionPtr->pAreaDummy )
      {
        v4 = v11->m_MissionMgr.pCurMssionPtr->pAreaDummy->m_wLineIndex;
        v10 = &v11->m_pQuestSetup->pUseMap->m_Dummy;
        if ( !CExtDummy::IsInBBox(v10, v4, v7->m_fCurPos) )
          v8 = 0;
      }
      if ( v8 )
      {
        pOri = (CGameObject *)CMonster::SearchNearPlayer(v7);
        if ( pOri )
          CMonster::AttackObject(v7, 10, pOri);
        CMonster::ChangeApparition(v7, 1, dwAfterKillTerm);
      }
    }
  }
}
