/*
 * Function: ?GetRecordByHash@CRecordData@@QEAAPEAU_base_fld@@PEBDHH@Z
 * Address: 0x14008F2F0
 */

_base_fld *__fastcall CRecordData::GetRecordByHash(CRecordData *this, const char *szRecordCode, int offset, int len)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  _base_fld *result; // rax@8
  __int64 v7; // [sp+0h] [bp-38h]@1
  unsigned int v8; // [sp+20h] [bp-18h]@5
  int n; // [sp+24h] [bp-14h]@5
  CRecordData *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v10->m_pdwHashList )
  {
    v8 = CRecordData::MakeHash(&szRecordCode[offset], len);
    for ( n = 0; n < v10->m_Header.m_nRecordNum; ++n )
    {
      if ( v10->m_pdwHashList[n] == v8 )
        return CRecordData::GetRecord(v10, n);
    }
    result = 0i64;
  }
  else
  {
    result = CRecordData::GetRecord(v10, szRecordCode, len + offset);
  }
  return result;
}
