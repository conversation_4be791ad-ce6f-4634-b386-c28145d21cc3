/*
 * Function: ?Init@CAnimus@@QEAA_NPEAU_object_id@@@Z
 * Address: 0x140128760
 */

char __fastcall CAnimus::Init(CAnimus *this, _object_id *pID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CAnimus *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CCharacter::Init((CCharacter *)&v7->vfptr, pID);
  v7->m_dwLastDestroyTime = 0;
  v7->m_byClassCode = -1;
  v7->m_nHP = 0;
  v7->m_nFP = 0;
  v7->m_dwExp = 0i64;
  v7->m_pMaster = 0i64;
  v7->m_dwMasterSerial = 0;
  memset_0(v7->m_wszMasterName, 0, 0x11ui64);
  memset_0(v7->m_aszMasterName, 0, 0x11ui64);
  v7->m_byRoleCode = -1;
  LODWORD(v7->m_fMoveSpeed) = 0;
  v7->m_byPosRaceTown = -1;
  v7->m_pBeforeTownCheckMap = 0i64;
  LODWORD(v7->m_fBeforeTownCheckPos[0]) = 0;
  LODWORD(v7->m_fBeforeTownCheckPos[1]) = 0;
  v7->m_dwStunTime = 0;
  v7->m_dwBeAttackedTargetTime = 0;
  v7->m_pNextTarget = 0i64;
  v7->m_nMaxAttackPnt = 0;
  v7->m_pRecord = 0i64;
  v7->m_nMaxHP = 0;
  v7->m_nMaxFP = 0;
  LODWORD(v7->m_Mightiness) = 0;
  for ( j = 0; j < 5; ++j )
    v7->m_DefPart[j] = 0;
  v7->m_dwAIMode = 0;
  v7->m_pTarget = 0i64;
  return 1;
}
