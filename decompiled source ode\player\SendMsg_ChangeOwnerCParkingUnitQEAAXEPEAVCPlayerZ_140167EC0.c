/*
 * Function: ?SendMsg_ChangeOwner@CParkingUnit@@QEAAXEPEAVCPlayer@@@Z
 * Address: 0x140167EC0
 */

void __fastcall CParkingUnit::SendMsg_ChangeOwner(CParkingUnit *this, char byUnitSlotIndex, CPlayer *pOldOwner)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  char szMsg[2]; // [sp+38h] [bp-50h]@4
  unsigned int v7; // [sp+3Ah] [bp-4Eh]@4
  unsigned int v8; // [sp+3Eh] [bp-4Ah]@4
  unsigned int v9; // [sp+42h] [bp-46h]@4
  char v10; // [sp+46h] [bp-42h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v12; // [sp+65h] [bp-23h]@4
  CParkingUnit *v13; // [sp+90h] [bp+8h]@1
  CPlayer *v14; // [sp+A0h] [bp+18h]@1

  v14 = pOldOwner;
  v13 = this;
  v3 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  *(_WORD *)szMsg = v13->m_ObjID.m_wIndex;
  v7 = v13->m_dwObjSerial;
  v8 = pOldOwner->m_dwObjSerial;
  v9 = v13->m_pOwner->m_dwObjSerial;
  v10 = byUnitSlotIndex;
  pbyType = 23;
  v12 = 22;
  CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_pOwner->m_ObjID.m_wIndex, &pbyType, szMsg, 0xFu);
  CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_ObjID.m_wIndex, &pbyType, szMsg, 0xFu);
}
