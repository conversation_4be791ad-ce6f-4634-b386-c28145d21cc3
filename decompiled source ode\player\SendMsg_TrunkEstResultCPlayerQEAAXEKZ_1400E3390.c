/*
 * Function: ?SendMsg_TrunkEstResult@CPlayer@@QEAAXEK@Z
 * Address: 0x1400E3390
 */

void __fastcall CPlayer::SendMsg_TrunkEstResult(CPlayer *this, char byRetCode, unsigned int dwLeftDalant)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager *v5; // rax@5
  char *v6; // rax@5
  __int64 v7; // [sp+0h] [bp-98h]@1
  _trunk_est_result_zocl v8; // [sp+38h] [bp-60h]@4
  char pbyType; // [sp+64h] [bp-34h]@6
  char v10; // [sp+65h] [bp-33h]@6
  unsigned __int64 v11; // [sp+80h] [bp-18h]@4
  CPlayer *v12; // [sp+A0h] [bp+8h]@1
  char v13; // [sp+A8h] [bp+10h]@1
  unsigned int v14; // [sp+B0h] [bp+18h]@1

  v14 = dwLeftDalant;
  v13 = byRetCode;
  v12 = this;
  v3 = &v7;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = (unsigned __int64)&v7 ^ _security_cookie;
  _trunk_est_result_zocl::_trunk_est_result_zocl(&v8);
  v8.byRetCode = v13;
  v8.dwLeftDalant = v14;
  if ( !v13 )
  {
    v5 = CTSingleton<CNationSettingManager>::Instance();
    v6 = CNationSettingManager::GetServerVaildKey(v5);
    strcpy_s(v8.szPW, 0x10ui64, v6);
  }
  pbyType = 34;
  v10 = 2;
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, &v8.byRetCode, 0x15u);
}
