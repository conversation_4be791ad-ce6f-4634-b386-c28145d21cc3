/*
 * Function: j_?deallocate@?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@QEAAXPEAPEAVCUnmannedTraderSubClassInfo@@_K@Z
 * Address: 0x14000950C
 */

void __fastcall std::allocator<CUnmannedTraderSubClassInfo *>::deallocate(std::allocator<CUnmannedTraderSubClassInfo *> *this, CUnmannedTraderSubClassInfo **_Ptr, unsigned __int64 __formal)
{
  std::allocator<CUnmannedTraderSubClassInfo *>::deallocate(this, _Ptr, __formal);
}
