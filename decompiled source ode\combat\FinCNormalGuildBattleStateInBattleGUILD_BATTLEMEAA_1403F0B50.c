/*
 * Function: ?Fin@CNormalGuildBattleStateInBattle@GUILD_BATTLE@@MEAAHPEAVCNormalGuildBattle@2@@Z
 * Address: 0x1403F0B50
 */

__int64 __fastcall GUILD_BATTLE::CNormalGuildBattleStateInBattle::Fin(GUILD_BATTLE::CNormalGuildBattleStateInBattle *this, GUILD_BATTLE::CNormalGuildBattle *pkBattle)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@8
  char *v5; // rax@9
  char *v6; // rax@10
  GUILD_BATTLE::CCurrentGuildBattleInfoManager *v7; // rax@11
  __int64 v9; // [sp+0h] [bp-78h]@1
  char *v10; // [sp+20h] [bp-58h]@8
  GUILD_BATTLE::CNormalGuildBattleGuild *v11; // [sp+30h] [bp-48h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v12; // [sp+38h] [bp-40h]@4
  GUILD_BATTLE::CNormalGuildBattleField *v13; // [sp+40h] [bp-38h]@4
  char v14; // [sp+48h] [bp-30h]@4
  char v15; // [sp+4Ch] [bp-2Ch]@4
  char *v16; // [sp+50h] [bp-28h]@8
  char *v17; // [sp+58h] [bp-20h]@9
  char *v18; // [sp+60h] [bp-18h]@10
  unsigned int uiMapID; // [sp+68h] [bp-10h]@11
  GUILD_BATTLE::CNormalGuildBattleStateInBattle *v20; // [sp+80h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattle *pkBattlea; // [sp+88h] [bp+10h]@1

  pkBattlea = pkBattle;
  v20 = this;
  v2 = &v9;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = GUILD_BATTLE::CNormalGuildBattle::GetRed(pkBattle);
  v12 = GUILD_BATTLE::CNormalGuildBattle::GetBlue(pkBattlea);
  v13 = GUILD_BATTLE::CNormalGuildBattle::GetField(pkBattlea);
  GUILD_BATTLE::CNormalGuildBattleField::ClearBall(v13);
  GUILD_BATTLE::CNormalGuildBattleField::DestroyFieldObject(v13);
  v14 = GUILD_BATTLE::CNormalGuildBattle::JudgeBattle(pkBattlea);
  v15 = v14;
  if ( v14 )
  {
    if ( v15 == 1 )
    {
      v17 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v12);
      v5 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v11);
      v10 = v17;
      GUILD_BATTLE::CNormalGuildBattleState::Log(
        (GUILD_BATTLE::CNormalGuildBattleState *)&v20->vfptr,
        pkBattlea,
        "Fin : JudgeBattle : Red Win! Red(%s), Blue(%s)",
        v5);
    }
    else if ( v15 == 2 )
    {
      v18 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v12);
      v6 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v11);
      v10 = v18;
      GUILD_BATTLE::CNormalGuildBattleState::Log(
        (GUILD_BATTLE::CNormalGuildBattleState *)&v20->vfptr,
        pkBattlea,
        "Fin : JudgeBattle : Blue Win! Red(%s), Blue(%s)",
        v6);
    }
  }
  else
  {
    v16 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v12);
    v4 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v11);
    v10 = v16;
    GUILD_BATTLE::CNormalGuildBattleState::Log(
      (GUILD_BATTLE::CNormalGuildBattleState *)&v20->vfptr,
      pkBattlea,
      "Fin : JudgeBattle : Draw( %s, %s )",
      v4);
  }
  GUILD_BATTLE::CNormalGuildBattle::NotifyBattleResult(pkBattlea, v14);
  GUILD_BATTLE::CNormalGuildBattleGuild::ClearInBattleState(v11);
  GUILD_BATTLE::CNormalGuildBattleGuild::ClearInBattleState(v12);
  uiMapID = GUILD_BATTLE::CNormalGuildBattleField::GetMapID(v13);
  v7 = GUILD_BATTLE::CCurrentGuildBattleInfoManager::Instance();
  GUILD_BATTLE::CCurrentGuildBattleInfoManager::Clear(v7, uiMapID);
  return 0i64;
}
