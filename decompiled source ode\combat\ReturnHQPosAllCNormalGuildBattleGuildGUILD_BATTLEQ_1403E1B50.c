/*
 * Function: ?ReturnHQPosAll@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAEXZ
 * Address: 0x1403E1B50
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::ReturnHQPosAll(GUILD_BATTLE::CNormalGuildBattleGuild *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-78h]@1
  float pfoutPos; // [sp+38h] [bp-40h]@10
  CMapData *pIntoMap; // [sp+58h] [bp-20h]@6
  CPlayer *v7; // [sp+60h] [bp-18h]@6
  char v8; // [sp+68h] [bp-10h]@6
  int j; // [sp+6Ch] [bp-Ch]@6
  GUILD_BATTLE::CNormalGuildBattleGuild *v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v1 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v10->m_pkGuild )
  {
    pIntoMap = 0i64;
    v7 = 0i64;
    v8 = v10->m_pkGuild->m_byRace;
    for ( j = 0; j < 50; ++j )
    {
      if ( GUILD_BATTLE::CNormalGuildBattleGuildMember::IsExist(&v10->m_kMember[j]) )
      {
        v7 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(&v10->m_kMember[j]);
        pIntoMap = CMapOperation::GetPosStartMap(&g_MapOper, v8, 0, &pfoutPos);
        if ( !pIntoMap )
          return 110;
        CPlayer::pc_Resurrect(v7, 0);
        CPlayer::ForcePullUnit(v7, 0);
        CPlayer::OutOfMap(v7, pIntoMap, 0, 3, &pfoutPos);
        CPlayer::SendMsg_GotoBasePortalResult(v7, 0);
      }
    }
    result = 0;
  }
  else
  {
    result = 110;
  }
  return result;
}
