/*
 * Function: ?Init@CDarkHoleChannel@@QEAAXXZ
 * Address: 0x140266980
 */

void __fastcall CDarkHoleChannel::Init(CDarkHoleChannel *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CDarkHoleChannel *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->m_pQuestSetup = 0i64;
  v5->m_wLayerIndex = -1;
  v5->m_pLayerSet = 0i64;
  _dh_mission_mgr::Init(&v5->m_MissionMgr);
  v5->m_pHoleObj = 0i64;
  v5->m_dwHoleSerial = -1;
  v5->m_dwOpenerSerial = -1;
  for ( j = 0; j < 32; ++j )
    _dh_player_mgr::Init(&v5->m_Quester[j]);
  v5->m_pLeaderPtr = 0i64;
  v5->m_dwEnterOrderCounter = 0;
  CIndexList::ResetList(&v5->m_listEnterMember);
  v5->m_dwNextCloseTime = -1;
  v5->m_bMoveNextMission = 0;
  v5->m_dwSendNewMissionMsgNextTime = -1;
  v5->m_dwQuestStartTime = 0;
  v5->m_bCheckMemberClose = 0;
}
