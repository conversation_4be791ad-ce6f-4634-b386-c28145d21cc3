/*
 * Function: ?_db_Load_PatriarchComm@CMainThread@@AEAAHPEAD@Z
 * Address: 0x1401A7250
 */

signed __int64 __fastcall CMainThread::_db_Load_PatriarchComm(CMainThread *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@4
  unsigned int v6; // eax@15
  char *v7; // rax@18
  __int64 v8; // [sp+0h] [bp-118h]@1
  char *pszFileName; // [sp+20h] [bp-F8h]@18
  char *v10; // [sp+30h] [bp-E8h]@4
  unsigned int Dst; // [sp+50h] [bp-C8h]@4
  unsigned __int64 ui64AddMoney[16]; // [sp+54h] [bp-C4h]@15
  CPlayer *v13; // [sp+D8h] [bp-40h]@10
  unsigned int j; // [sp+E0h] [bp-38h]@13
  char *v15; // [sp+F0h] [bp-28h]@18
  unsigned int dwLeftDalant; // [sp+F8h] [bp-20h]@18
  __int64 v17; // [sp+100h] [bp-18h]@18
  unsigned __int64 v18; // [sp+108h] [bp-10h]@4
  CMainThread *v19; // [sp+120h] [bp+8h]@1

  v19 = this;
  v2 = &v8;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v18 = (unsigned __int64)&v8 ^ _security_cookie;
  v10 = pData;
  memset_0(&Dst, 0, 0x74ui64);
  v4 = CRFWorldDatabase::Select_PatriarchComm(v19->m_pWorldDB, *(_DWORD *)v10, (_patriarch_comm_list *)&Dst);
  v10[4] = v4;
  if ( v10[4] == 1 )
    return 24i64;
  if ( v10[4] == 2 )
    return 0i64;
  if ( !v10[4] && Dst )
  {
    v13 = GetPtrPlayerFromSerial(&g_Player, 2532, *(_DWORD *)v10);
    if ( !v13 || !v13->m_bOper )
      return 0i64;
    for ( j = 0; j < Dst; ++j )
    {
      v6 = CPlayerDB::GetDalant(&v13->m_Param);
      if ( !CanAddMoneyForMaxLimMoney(LODWORD(ui64AddMoney[2 * (signed int)j]), v6) )
      {
        CPlayer::SendMsg_InformTaxIncome(
          v13,
          1,
          ui64AddMoney[2 * (signed int)j],
          (char *)&ui64AddMoney[2 * (signed int)j] + 4);
        return 0i64;
      }
      CPlayer::AlterDalant(v13, (double)SLODWORD(ui64AddMoney[2 * (signed int)j]));
      if ( !CRFWorldDatabase::Delete_PatriarchComm(
              v19->m_pWorldDB,
              *(_DWORD *)v10,
              (char *)&ui64AddMoney[2 * (signed int)j] + 4) )
      {
        CPlayer::AlterDalant(v13, (double)-LODWORD(ui64AddMoney[2 * (signed int)j]));
        return 0i64;
      }
      CPlayer::SendMsg_ExchangeMoneyResult(v13, 0);
      CPlayer::SendMsg_InformTaxIncome(
        v13,
        0,
        ui64AddMoney[2 * (signed int)j],
        (char *)&ui64AddMoney[2 * (signed int)j] + 4);
      v15 = v13->m_szItemHistoryFileName;
      dwLeftDalant = CPlayerDB::GetDalant(&v13->m_Param);
      v17 = 16i64 * (signed int)j;
      v7 = CPlayerDB::GetCharNameW(&v13->m_Param);
      pszFileName = v15;
      CMgrAvatorItemHistory::patriarch_push_money(
        &CPlayer::s_MgrItemHistory,
        v7,
        ui64AddMoney[(unsigned __int64)v17 / 8],
        dwLeftDalant,
        v15);
    }
  }
  return 0i64;
}
