/*
 * Function: ?IOThread@CMgrAvatorItemHistory@@SAXPEAX@Z
 * Address: 0x14023FAE0
 */

void __fastcall CMgrAvatorItemHistory::IOThread(void *pv)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-68h]@1
  void *v4; // [sp+20h] [bp-48h]@4
  unsigned int pdwOutIndex; // [sp+34h] [bp-34h]@7
  char *pszFileName; // [sp+48h] [bp-20h]@8
  char *v7; // [sp+50h] [bp-18h]@10
  char *v8; // [sp+58h] [bp-10h]@12
  void *v9; // [sp+70h] [bp+8h]@1

  v9 = pv;
  v1 = &v3;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = v9;
  while ( *((_BYTE *)v4 + 3508304) )
  {
    CFrameRate::CalcSpeedPerFrame((CFrameRate *)((char *)v4 + 3508312));
    while ( CNetIndexList::PopNode_Front((CNetIndexList *)((char *)v4 + 2557496), &pdwOutIndex) )
    {
      pszFileName = (char *)v4 + 10068 * pdwOutIndex + 224;
      IOFileWrite_0(pszFileName, *((_DWORD *)v4 + 2517 * pdwOutIndex + 72), (char *)v4 + 10068 * pdwOutIndex + 292);
      CNetIndexList::PushNode_Back((CNetIndexList *)((char *)v4 + 2557656), pdwOutIndex);
      Sleep(0);
    }
    while ( CNetIndexList::PopNode_Front((CNetIndexList *)((char *)v4 + 2829088), &pdwOutIndex) )
    {
      v7 = (char *)v4 + 1068 * pdwOutIndex + 2557816;
      IOFileWrite_0(v7, *((_DWORD *)v4 + 267 * pdwOutIndex + 639470), (char *)v4 + 1068 * pdwOutIndex + 2557884);
      CNetIndexList::PushNode_Back((CNetIndexList *)((char *)v4 + 2829248), pdwOutIndex);
      Sleep(0);
    }
    while ( CNetIndexList::PopNode_Front((CNetIndexList *)((char *)v4 + 3507984), &pdwOutIndex) )
    {
      v8 = (char *)v4 + 268 * pdwOutIndex + 2829408;
      IOFileWrite_0(v8, *((_DWORD *)v4 + 67 * pdwOutIndex + 707368), (char *)v4 + 268 * pdwOutIndex + 2829476);
      CNetIndexList::PushNode_Back((CNetIndexList *)((char *)v4 + 3508144), pdwOutIndex);
      Sleep(0);
    }
    Sleep(1u);
  }
  _endthreadex(0);
}
