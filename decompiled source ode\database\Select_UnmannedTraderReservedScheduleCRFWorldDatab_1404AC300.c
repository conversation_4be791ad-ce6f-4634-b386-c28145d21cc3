/*
 * Function: ?Select_UnmannedTraderReservedSchedule@CRFWorldDatabase@@QEAAEKPEAU_unmannedtrader_reserved_schedule_info@@@Z
 * Address: 0x1404AC300
 */

char __fastcall CRFWorldDatabase::Select_UnmannedTraderReservedSchedule(CRFWorldDatabase *this, unsigned int dwMaxCnt, _unmannedtrader_reserved_schedule_info *pkInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  _unmannedtrader_reserved_schedule_info::__list *v6; // rax@19
  unsigned int *v7; // rax@19
  unsigned int *v8; // rax@19
  unsigned int *v9; // rax@19
  __int64 v10; // [sp+0h] [bp-4F8h]@1
  void *SQLStmt; // [sp+20h] [bp-4D8h]@15
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-4D0h]@19
  char Dest; // [sp+40h] [bp-4B8h]@6
  SQLLEN v14; // [sp+458h] [bp-A0h]@19
  __int16 v15; // [sp+464h] [bp-94h]@11
  int v16; // [sp+468h] [bp-90h]@6
  __int16 TargetValue; // [sp+478h] [bp-80h]@19
  unsigned __int16 v18; // [sp+47Ah] [bp-7Eh]@19
  unsigned __int16 v19; // [sp+47Ch] [bp-7Ch]@19
  unsigned __int16 v20; // [sp+47Eh] [bp-7Ah]@19
  unsigned __int16 v21; // [sp+480h] [bp-78h]@19
  unsigned __int16 v22; // [sp+482h] [bp-76h]@19
  tm _Tm; // [sp+4A8h] [bp-50h]@19
  __int64 v24; // [sp+4D8h] [bp-20h]@19
  unsigned __int64 v25; // [sp+4E8h] [bp-10h]@4
  CRFWorldDatabase *v26; // [sp+500h] [bp+8h]@1
  _unmannedtrader_reserved_schedule_info *v27; // [sp+510h] [bp+18h]@1

  v27 = pkInfo;
  v26 = this;
  v3 = &v10;
  for ( i = 316i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v25 = (unsigned __int64)&v10 ^ _security_cookie;
  if ( pkInfo )
  {
    v16 = 0;
    sprintf(
      &Dest,
      "select top %u s.type, s.serial, dateadd( hh, s.sellturm, s.regdate ) as endtime, s.owner, si.k from [dbo].[tbl_uts"
      "ellinfo] as s join [dbo].[tbl_utresultinfo] as r on s.serial = r.serial and r.state in ( 1, 2 ) join [dbo].[tbl_ut"
      "singleiteminfo] as si on r.serial = si.serial order by endtime",
      dwMaxCnt);
    if ( v26->m_bSaveDBLog )
      CRFNewDatabase::Log((CRFNewDatabase *)&v26->vfptr, &Dest);
    if ( v26->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v26->vfptr) )
    {
      v15 = SQLExecDirect_0(v26->m_hStmtSelect, &Dest, -3);
      if ( v15 && v15 != 1 )
      {
        if ( v15 == 100 )
        {
          result = 2;
        }
        else
        {
          SQLStmt = v26->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v26->vfptr, v15, &Dest, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v26->vfptr, v15, v26->m_hStmtSelect);
          result = 1;
        }
      }
      else
      {
        for ( v27->dwCnt = 0; ; ++v27->dwCnt )
        {
          v15 = SQLFetch_0(v26->m_hStmtSelect);
          if ( v15 )
          {
            if ( v15 != 1 )
              break;
          }
          v6 = &v27->list[v27->dwCnt];
          StrLen_or_IndPtr = &v14;
          SQLStmt = 0i64;
          v15 = SQLGetData_0(v26->m_hStmtSelect, 1u, -6, v6, 0i64, &v14);
          v7 = &v27->list[v27->dwCnt].dwItemSerial;
          StrLen_or_IndPtr = &v14;
          SQLStmt = 0i64;
          v15 = SQLGetData_0(v26->m_hStmtSelect, 2u, -18, v7, 0i64, &v14);
          StrLen_or_IndPtr = &v14;
          SQLStmt = 0i64;
          v15 = SQLGetData_0(v26->m_hStmtSelect, 3u, 93, &TargetValue, 0i64, &v14);
          v8 = &v27->list[v27->dwCnt].dwOwnerSerial;
          StrLen_or_IndPtr = &v14;
          SQLStmt = 0i64;
          v15 = SQLGetData_0(v26->m_hStmtSelect, 4u, -18, v8, 0i64, &v14);
          v9 = &v27->list[v27->dwCnt].dwK;
          StrLen_or_IndPtr = &v14;
          SQLStmt = 0i64;
          v15 = SQLGetData_0(v26->m_hStmtSelect, 5u, -18, v9, 0i64, &v14);
          _Tm.tm_year = TargetValue - 1900;
          _Tm.tm_mon = v18 - 1;
          _Tm.tm_mday = v19;
          _Tm.tm_hour = v20;
          _Tm.tm_min = v21;
          _Tm.tm_sec = v22;
          _Tm.tm_isdst = -1;
          v24 = mktime_3(&_Tm);
          if ( v24 == -1 )
            v24 = 0i64;
          v27->list[v27->dwCnt].tEndTime = v24;
        }
        if ( v26->m_hStmtSelect )
          SQLCloseCursor_0(v26->m_hStmtSelect);
        if ( v26->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v26->vfptr, "%s Success", &Dest);
        result = 0;
      }
    }
    else
    {
      CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v26->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
      result = 1;
    }
  }
  else
  {
    result = 2;
  }
  return result;
}
