/*
 * Function: ?Valid<PERSON>ac<PERSON>ddress@CNationSettingDataTW@@UEAA_NXZ
 * Address: 0x14022FB30
 */

bool __fastcall CNationSettingDataTW::ValidMacAddress(CNationSettingDataTW *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-338h]@1
  int v5; // [sp+20h] [bp-318h]@4
  char Str2[13]; // [sp+40h] [bp-2F8h]@4
  char v7; // [sp+4Dh] [bp-2EBh]@4
  char v8; // [sp+60h] [bp-2D8h]@4
  char v9; // [sp+6Dh] [bp-2CBh]@4
  char v10; // [sp+80h] [bp-2B8h]@4
  char v11; // [sp+8Dh] [bp-2ABh]@4
  char v12; // [sp+A0h] [bp-298h]@4
  char v13; // [sp+ADh] [bp-28Bh]@4
  char v14; // [sp+C0h] [bp-278h]@4
  char v15; // [sp+CDh] [bp-26Bh]@4
  char v16; // [sp+E0h] [bp-258h]@4
  char v17; // [sp+EDh] [bp-24Bh]@4
  char v18; // [sp+100h] [bp-238h]@4
  char v19; // [sp+10Dh] [bp-22Bh]@4
  char v20; // [sp+120h] [bp-218h]@4
  char v21; // [sp+12Dh] [bp-20Bh]@4
  char v22; // [sp+140h] [bp-1F8h]@4
  char v23; // [sp+14Dh] [bp-1EBh]@4
  char v24; // [sp+160h] [bp-1D8h]@4
  char v25; // [sp+16Dh] [bp-1CBh]@4
  char v26; // [sp+180h] [bp-1B8h]@4
  char v27; // [sp+18Dh] [bp-1ABh]@4
  char v28; // [sp+1A0h] [bp-198h]@4
  char v29; // [sp+1ADh] [bp-18Bh]@4
  char v30; // [sp+1C0h] [bp-178h]@4
  char v31; // [sp+1CDh] [bp-16Bh]@4
  char v32; // [sp+1E0h] [bp-158h]@4
  char v33; // [sp+1EDh] [bp-14Bh]@4
  char v34; // [sp+200h] [bp-138h]@4
  char v35; // [sp+20Dh] [bp-12Bh]@4
  char v36; // [sp+220h] [bp-118h]@4
  char v37; // [sp+22Dh] [bp-10Bh]@4
  char v38; // [sp+240h] [bp-F8h]@4
  char v39; // [sp+24Dh] [bp-EBh]@4
  char v40; // [sp+260h] [bp-D8h]@4
  char v41; // [sp+26Dh] [bp-CBh]@4
  char v42; // [sp+280h] [bp-B8h]@4
  char v43; // [sp+28Dh] [bp-ABh]@4
  char v44; // [sp+2A0h] [bp-98h]@4
  char v45; // [sp+2ADh] [bp-8Bh]@4
  char szMac; // [sp+2D8h] [bp-60h]@4
  char v47; // [sp+2D9h] [bp-5Fh]@4
  int j; // [sp+304h] [bp-34h]@4
  unsigned __int64 v49; // [sp+310h] [bp-28h]@4

  v1 = &v4;
  for ( i = 202i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v49 = (unsigned __int64)&v4 ^ _security_cookie;
  v5 = 20;
  qmemcpy(Str2, "6efa20524153", sizeof(Str2));
  memset(&v7, 0, 0x13ui64);
  qmemcpy(&v8, "00e08160e64c", 0xDui64);
  memset(&v9, 0, 0x13ui64);
  qmemcpy(&v10, "00e08160e618", 0xDui64);
  memset(&v11, 0, 0x13ui64);
  qmemcpy(&v12, "00e08161907a", 0xDui64);
  memset(&v13, 0, 0x13ui64);
  qmemcpy(&v14, "00e0816448b9", 0xDui64);
  memset(&v15, 0, 0x13ui64);
  qmemcpy(&v16, "00e0816190a2", 0xDui64);
  memset(&v17, 0, 0x13ui64);
  qmemcpy(&v18, "000e0c6ed227", 0xDui64);
  memset(&v19, 0, 0x13ui64);
  qmemcpy(&v20, "000e0c6ed267", 0xDui64);
  memset(&v21, 0, 0x13ui64);
  qmemcpy(&v22, "00e08160e5b2", 0xDui64);
  memset(&v23, 0, 0x13ui64);
  qmemcpy(&v24, "00e08161cfe5", 0xDui64);
  memset(&v25, 0, 0x13ui64);
  qmemcpy(&v26, "00e0816447f5", 0xDui64);
  memset(&v27, 0, 0x13ui64);
  qmemcpy(&v28, "00e0816448af", 0xDui64);
  memset(&v29, 0, 0x13ui64);
  qmemcpy(&v30, "00e08164489d", 0xDui64);
  memset(&v31, 0, 0x13ui64);
  qmemcpy(&v32, "00e0816448b5", 0xDui64);
  memset(&v33, 0, 0x13ui64);
  qmemcpy(&v34, "00e0816447b0", 0xDui64);
  memset(&v35, 0, 0x13ui64);
  qmemcpy(&v36, "00e0816487cb", 0xDui64);
  memset(&v37, 0, 0x13ui64);
  qmemcpy(&v38, "00e08164880e", 0xDui64);
  memset(&v39, 0, 0x13ui64);
  qmemcpy(&v40, "000e0c6ed2c7", 0xDui64);
  memset(&v41, 0, 0x13ui64);
  qmemcpy(&v42, "000e0c6ed2b4", 0xDui64);
  memset(&v43, 0, 0x13ui64);
  qmemcpy(&v44, "000e0c6edc1f", 0xDui64);
  memset(&v45, 0, 0x13ui64);
  szMac = 0;
  memset(&v47, 0, 0x1Fui64);
  GetMacAddrString(&szMac, 0x20ui64);
  for ( j = 0; j < 20 && strcmp_0(&szMac, &Str2[32 * j]); ++j )
    ;
  return j < 20;
}
