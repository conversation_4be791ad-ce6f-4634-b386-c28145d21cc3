/*
 * Function: ?CrtDbgReport@CAtlTraceModule@@QEAAXP6AHHPEBDH00ZZ@Z
 * Address: 0x1406760D0
 */

void __fastcall CAtlTraceModule::CrtDbgReport(CAtlTraceModule *this, int (*pfnCrtDbgReport)(int, const char *, int, const char *, const char *, ...))
{
  int (*v2)(int, const char *, int, const char *, const char *, ...); // [sp+0h] [bp-18h]@2

  if ( pfnCrtDbgReport )
    v2 = pfnCrtDbgReport;
  else
    v2 = CrtDbgReport_0;
  this->m_pfnCrtDbgReport = v2;
}
