/*
 * Function: ??4_INVEN_DB_BASE@@QEAAAEAU0@AEAU0@@Z
 * Address: 0x1401BF600
 */

_INVEN_DB_BASE *__fastcall _INVEN_DB_BASE::operator=(_INVEN_DB_BASE *this, _INVEN_DB_BASE *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@4
  _INVEN_DB_BASE *v7; // [sp+40h] [bp+8h]@1
  _INVEN_DB_BASE *v8; // [sp+48h] [bp+10h]@1

  v8 = __that;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 0x64; ++j )
    _INVEN_DB_BASE::_LIST::operator=((_INVEN_DB_BASE::_LIST *)v7 + j, (_INVEN_DB_BASE::_LIST *)v8 + j);
  return v7;
}
