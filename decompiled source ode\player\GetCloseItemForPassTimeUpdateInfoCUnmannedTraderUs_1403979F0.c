/*
 * Function: ?GetCloseItemForPassTimeUpdateInfo@CUnmannedTraderUserInfoTable@@QEAA?AW4STATE@CUnmannedTraderItemState@@KKAEAPEAVCPlayer@@@Z
 * Address: 0x1403979F0
 */

CUnmannedTraderItemState::STATE __fastcall CUnmannedTraderUserInfoTable::GetCloseItemForPassTimeUpdateInfo(CUnmannedTraderUserInfoTable *this, unsigned int dwOwnerSerial, unsigned int dwRegistSerial, CPlayer **pkOwner)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfo *v6; // rax@4
  __int64 v8; // [sp+0h] [bp-28h]@1
  CUnmannedTraderUserInfoTable *v9; // [sp+30h] [bp+8h]@1
  unsigned int dwRegistSeriala; // [sp+40h] [bp+18h]@1
  CPlayer **pkOwnera; // [sp+48h] [bp+20h]@1

  pkOwnera = pkOwner;
  dwRegistSeriala = dwRegistSerial;
  v9 = this;
  v4 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v6 = CUnmannedTraderUserInfoTable::Find(v9, dwOwnerSerial);
  return CUnmannedTraderUserInfo::GetCloseItemForPassTimeUpdateInfo(v6, dwRegistSeriala, pkOwnera);
}
