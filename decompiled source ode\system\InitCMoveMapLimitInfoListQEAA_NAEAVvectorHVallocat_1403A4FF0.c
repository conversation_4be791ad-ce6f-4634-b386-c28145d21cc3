/*
 * Function: ?Init@CMoveMapLimitInfoList@@QEAA_NAEAV?$vector@HV?$allocator@H@std@@@std@@@Z
 * Address: 0x1403A4FF0
 */

char __fastcall CMoveMapLimitInfoList::Init(CMoveMapLimitInfoList *this, std::vector<int,std::allocator<int> > *vecRightTypeList)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@5
  unsigned __int64 v5; // rax@6
  CMoveMapLimitInfo **v6; // rax@18
  __int64 v7; // [sp+0h] [bp-588h]@1
  unsigned __int64 _Count; // [sp+20h] [bp-568h]@4
  int iType; // [sp+34h] [bp-554h]@8
  CMoveMapLimitInfo *v10; // [sp+48h] [bp-540h]@8
  std::_Vector_iterator<int,std::allocator<int> > result; // [sp+58h] [bp-530h]@8
  unsigned int uiInx; // [sp+74h] [bp-514h]@8
  char DstBuf; // [sp+90h] [bp-4F8h]@10
  char v14; // [sp+91h] [bp-4F7h]@10
  CMoveMapLimitInfo *_Val; // [sp+4A0h] [bp-E8h]@6
  char v16; // [sp+4A8h] [bp-E0h]@11
  char v17; // [sp+4B0h] [bp-D8h]@12
  std::_Vector_iterator<int,std::allocator<int> > *v18; // [sp+4C8h] [bp-C0h]@12
  char v19; // [sp+4D0h] [bp-B8h]@12
  std::_Vector_iterator<int,std::allocator<int> > *v20; // [sp+4E8h] [bp-A0h]@12
  std::_Vector_iterator<int,std::allocator<int> > v21; // [sp+4F0h] [bp-98h]@12
  bool v22; // [sp+508h] [bp-80h]@12
  std::_Vector_iterator<int,std::allocator<int> > v23; // [sp+510h] [bp-78h]@12
  char v24; // [sp+528h] [bp-60h]@15
  char v25; // [sp+529h] [bp-5Fh]@17
  char v26; // [sp+52Ah] [bp-5Eh]@19
  __int64 v27; // [sp+530h] [bp-58h]@4
  __int64 v28; // [sp+538h] [bp-50h]@6
  std::_Vector_iterator<int,std::allocator<int> > *v29; // [sp+540h] [bp-48h]@12
  std::_Vector_iterator<int,std::allocator<int> > *v30; // [sp+548h] [bp-40h]@12
  std::_Vector_iterator<int,std::allocator<int> > *v31; // [sp+550h] [bp-38h]@12
  std::_Vector_iterator<int,std::allocator<int> > *v32; // [sp+558h] [bp-30h]@12
  std::_Vector_iterator<int,std::allocator<int> > *__that; // [sp+560h] [bp-28h]@12
  std::_Vector_iterator<int,std::allocator<int> > *v34; // [sp+568h] [bp-20h]@12
  std::_Vector_const_iterator<int,std::allocator<int> > *v35; // [sp+570h] [bp-18h]@12
  unsigned __int64 v36; // [sp+578h] [bp-10h]@4
  CMoveMapLimitInfoList *v37; // [sp+590h] [bp+8h]@1
  std::vector<int,std::allocator<int> > *v38; // [sp+598h] [bp+10h]@1

  v38 = vecRightTypeList;
  v37 = this;
  v2 = &v7;
  for ( i = 352i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v27 = -2i64;
  v36 = (unsigned __int64)&v7 ^ _security_cookie;
  LODWORD(_Count) = GetPrivateProfileIntA("MoveMapLimitInfo", "Cnt", 0, "./Initialize/MoveMapLimit.ini");
  if ( (_DWORD)_Count )
  {
    _Val = 0i64;
    std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::assign(
      &v37->m_vecLimitInfo,
      (unsigned int)_Count,
      &_Val);
    v28 = (unsigned int)_Count;
    v5 = std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::size(&v37->m_vecLimitInfo);
    if ( v28 == v5 )
    {
      iType = 0;
      v10 = 0i64;
      std::vector<int,std::allocator<int>>::end(v38, &result);
      for ( uiInx = 0; uiInx < (unsigned int)_Count; ++uiInx )
      {
        DstBuf = 0;
        memset(&v14, 0, 0x3FFui64);
        sprintf_s(&DstBuf, 0x400ui64, "Type%u", uiInx);
        iType = GetPrivateProfileIntA("MoveMapLimitInfo", &DstBuf, -1, "./Initialize/MoveMapLimit.ini");
        if ( iType == -1 )
        {
          CLogFile::Write(
            &stru_1799C8F30,
            "CMoveMapLimitInfoList::Init() : [%s] Type%u == -1!",
            "MoveMapLimitInfo",
            uiInx);
          v16 = 0;
          std::_Vector_iterator<int,std::allocator<int>>::~_Vector_iterator<int,std::allocator<int>>(&result);
          return v16;
        }
        v18 = (std::_Vector_iterator<int,std::allocator<int> > *)&v17;
        v20 = (std::_Vector_iterator<int,std::allocator<int> > *)&v19;
        v29 = std::vector<int,std::allocator<int>>::end(v38, (std::_Vector_iterator<int,std::allocator<int> > *)&v17);
        v30 = v29;
        v31 = std::vector<int,std::allocator<int>>::begin(v38, v20);
        v32 = std::find<std::_Vector_iterator<int,std::allocator<int>>,int>(&v21, v31, v30, &iType);
        __that = v32;
        std::_Vector_iterator<int,std::allocator<int>>::operator=(&result, v32);
        std::_Vector_iterator<int,std::allocator<int>>::~_Vector_iterator<int,std::allocator<int>>(&v21);
        v34 = std::vector<int,std::allocator<int>>::end(v38, &v23);
        v35 = (std::_Vector_const_iterator<int,std::allocator<int> > *)v34;
        v22 = std::_Vector_const_iterator<int,std::allocator<int>>::operator==(
                (std::_Vector_const_iterator<int,std::allocator<int> > *)&v34->_Mycont,
                (std::_Vector_const_iterator<int,std::allocator<int> > *)&result._Mycont);
        std::_Vector_iterator<int,std::allocator<int>>::~_Vector_iterator<int,std::allocator<int>>(&v23);
        if ( v22 )
          std::vector<int,std::allocator<int>>::push_back(v38, &iType);
        v10 = CMoveMapLimitInfo::Create(uiInx, iType);
        if ( !v10 )
        {
          CLogFile::Write(
            &stru_1799C8F30,
            "CMoveMapLimitInfoList::Init() : CMoveMapLimitInfo::Create( i(%u), iType(%d) ) NULL!",
            uiInx,
            (unsigned int)iType);
          v24 = 0;
          std::_Vector_iterator<int,std::allocator<int>>::~_Vector_iterator<int,std::allocator<int>>(&result);
          return v24;
        }
        if ( !(unsigned __int8)((int (__fastcall *)(CMoveMapLimitInfo *))v10->vfptr->Init)(v10) )
        {
          CLogFile::Write(
            &stru_1799C8F30,
            "CMoveMapLimitInfoList::Init() : %uth Type(%d) pkInfo->Init() Fail!",
            uiInx,
            (unsigned int)iType);
          v25 = 0;
          std::_Vector_iterator<int,std::allocator<int>>::~_Vector_iterator<int,std::allocator<int>>(&result);
          return v25;
        }
        v6 = std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::operator[](
               &v37->m_vecLimitInfo,
               uiInx);
        *v6 = v10;
      }
      v26 = 1;
      std::_Vector_iterator<int,std::allocator<int>>::~_Vector_iterator<int,std::allocator<int>>(&result);
      v4 = v26;
    }
    else
    {
      CLogFile::Write(
        &stru_1799C8F30,
        "CMoveMapLimitInfoList::Init() : uiCnt(%u) != m_vecLimitInfo.size()(%d)",
        (unsigned int)_Count);
      v4 = 0;
    }
  }
  else
  {
    v4 = 1;
  }
  return v4;
}
