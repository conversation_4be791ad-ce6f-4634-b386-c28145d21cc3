/*
 * Function: ?CreateTblLtd_Expend@CRFDBItemLog@@QEAA_NH@Z
 * Address: 0x140485730
 */

bool __fastcall CRFDBItemLog::CreateTblLtd_Expend(CRFDBItemLog *this, int nKorTime)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-468h]@1
  int v6; // [sp+20h] [bp-448h]@4
  char Dest; // [sp+40h] [bp-428h]@4
  char v8; // [sp+41h] [bp-427h]@4
  unsigned __int64 v9; // [sp+450h] [bp-18h]@4
  CRFDBItemLog *v10; // [sp+470h] [bp+8h]@1

  v10 = this;
  v2 = &v5;
  for ( i = 280i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  Dest = 0;
  memset(&v8, 0, 0x3FFui64);
  v6 = nKorTime;
  sprintf(
    &Dest,
    " CREATE TABLE [dbo].[tbl_ltd_expend_%d] ( [LogSerial] [datetime] NOT NULL, [SubType] [tinyint] NOT NULL, [Expend] [n"
    "varchar] (128) NOT NULL ) ON [PRIMARY] CREATE INDEX [IX_tbl_ltd_expend_LogSerial] ON [dbo].[tbl_ltd_expend_%d]([LogS"
    "erial]) ON [PRIMARY] CREATE INDEX [IX_tbl_ltd_expend_SubType] ON [dbo].[tbl_ltd_expend_%d]([SubType]) ON [PRIMARY]",
    (unsigned int)nKorTime,
    (unsigned int)nKorTime);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &Dest, 1) != 0;
}
