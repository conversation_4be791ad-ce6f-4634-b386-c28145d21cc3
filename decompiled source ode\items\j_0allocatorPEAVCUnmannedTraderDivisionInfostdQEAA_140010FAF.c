/*
 * Function: j_??0?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@QEAA@AEBV01@@Z
 * Address: 0x140010FAF
 */

void __fastcall std::allocator<CUnmannedTraderDivisionInfo *>::allocator<CUnmannedTraderDivisionInfo *>(std::allocator<CUnmannedTraderDivisionInfo *> *this, std::allocator<CUnmannedTraderDivisionInfo *> *__formal)
{
  std::allocator<CUnmannedTraderDivisionInfo *>::allocator<CUnmannedTraderDivisionInfo *>(this, __formal);
}
