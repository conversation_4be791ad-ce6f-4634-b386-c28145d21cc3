/*
 * Function: _CUnmannedTraderUserInfo::CompleteUpdateState_::_1_::dtor$1
 * Address: 0x140354A50
 */

void __fastcall CUnmannedTraderUserInfo::CompleteUpdateState_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>((std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)(a2 + 72));
}
