/*
 * Function: ?EnterMember@CTransportShip@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x140264830
 */

void __fastcall CTransportShip::EnterMember(CTransportShip *this, CPlayer *pEnter)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@6
  CTransportShip::__mgr_member *v6; // [sp+28h] [bp-20h]@9
  CTransportShip::__mgr_member *v7; // [sp+30h] [bp-18h]@17
  CTransportShip::__mgr_member *v8; // [sp+38h] [bp-10h]@22
  CTransportShip *v9; // [sp+50h] [bp+8h]@1
  CPlayer *pExiter; // [sp+58h] [bp+10h]@1

  pExiter = pEnter;
  v9 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v9->m_bAnchor || CTransportShip::Ticketting(v9, pEnter) )
  {
    for ( j = 0; j < 2532; ++j )
    {
      v6 = &v9->m_OldMember[j];
      if ( CTransportShip::__mgr_member::is_fill(v6) && v6->pPtr == pExiter && v6->dwSerial == pExiter->m_dwObjSerial )
        CTransportShip::__mgr_member::init(v6);
    }
    for ( j = 0; j < 2532; ++j )
    {
      v7 = &v9->m_NewMember[j];
      if ( CTransportShip::__mgr_member::is_fill(v7) && v7->pPtr == pExiter && v7->dwSerial == pExiter->m_dwObjSerial )
        CTransportShip::__mgr_member::init(v7);
    }
    v8 = CTransportShip::GetEmptyNewMember(v9);
    if ( v8 )
    {
      v8->pPtr = pExiter;
      v8->dwSerial = pExiter->m_dwObjSerial;
    }
  }
}
