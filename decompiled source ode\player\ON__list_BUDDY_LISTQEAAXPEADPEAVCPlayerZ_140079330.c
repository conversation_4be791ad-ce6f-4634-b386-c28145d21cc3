/*
 * Function: ?ON@__list@_BUDDY_LIST@@QEAAXPEADPEAVCPlayer@@@Z
 * Address: 0x140079330
 */

void __fastcall _BUDDY_LIST::__list::ON(_BUDDY_LIST::__list *this, char *pwszName, CPlayer *ptr)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  _BUDDY_LIST::__list *v6; // [sp+30h] [bp+8h]@1
  CPlayer *v7; // [sp+40h] [bp+18h]@1

  v7 = ptr;
  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  strcpy_0(v6->wszN<PERSON>, pwszName);
  v6->pPtr = v7;
}
