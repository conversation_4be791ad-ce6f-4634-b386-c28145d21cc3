/*
 * Function: j_?Max<PERSON>laintextLength@?$DL_CryptoSystemBase@VPK_Decryptor@CryptoPP@@V?$DL_PrivateKey@UECPPoint@CryptoPP@@@2@@CryptoPP@@UEBA_K_K@Z
 * Address: 0x1400125F8
 */

unsigned __int64 __fastcall CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::MaxPlaintextLength(CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *this, unsigned __int64 ciphertextLength)
{
  return CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::Max<PERSON>laintextLength(
           this,
           ciphertextLength);
}
