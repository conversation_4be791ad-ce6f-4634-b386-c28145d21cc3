/*
 * Function: ?Encrypt@?$DL_EncryptorBase@UECPPoint@CryptoPP@@@CryptoPP@@UEBAXAEAVRandomNumberGenerator@2@PEBE_KPEAEAEBVNameValuePairs@2@@Z
 * Address: 0x1404551D0
 */

void __fastcall CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint>::Encrypt(CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint> *this, CryptoPP::RandomNumberGenerator *rng, const char *plaintext, unsigned __int64 plaintextLength, char *ciphertext, CryptoPP::NameValuePairs *parameters)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  void (__fastcall ***v8)(_QWORD, _QWORD, _QWORD, _QWORD); // rax@4
  __int64 *v9; // rax@4
  __int64 v10; // rax@4
  __int64 v11; // rax@4
  struct CryptoPP::Integer *v12; // rax@4
  const struct CryptoPP::Integer *v13; // rax@4
  __int64 v14; // rdx@4
  __int64 v15; // rdx@4
  __int64 v16; // rax@4
  unsigned __int64 v17; // rax@4
  char *v18; // rax@4
  char *v19; // rax@4
  __int64 v20; // [sp+0h] [bp-268h]@1
  enum CryptoPP::Integer::RandomNumberType v21[2]; // [sp+20h] [bp-248h]@4
  struct CryptoPP::Integer *v22; // [sp+28h] [bp-240h]@4
  struct CryptoPP::Integer *v23; // [sp+30h] [bp-238h]@4
  void (__fastcall ***v24)(_QWORD, _QWORD, _QWORD, _QWORD); // [sp+40h] [bp-228h]@4
  __int64 *v25; // [sp+48h] [bp-220h]@4
  __int64 v26; // [sp+50h] [bp-218h]@4
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *v27; // [sp+58h] [bp-210h]@4
  __int64 v28; // [sp+60h] [bp-208h]@4
  CryptoPP::Integer v29; // [sp+78h] [bp-1F0h]@4
  CryptoPP::ECPPoint v30; // [sp+C0h] [bp-1A8h]@4
  int v31; // [sp+124h] [bp-144h]@4
  CryptoPP::ECPPoint v32; // [sp+140h] [bp-128h]@4
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v33; // [sp+1B8h] [bp-B0h]@4
  CryptoPP::Integer v34; // [sp+1D8h] [bp-90h]@4
  __int64 v35; // [sp+200h] [bp-68h]@4
  CryptoPP::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> >Vtbl *v36; // [sp+208h] [bp-60h]@4
  struct CryptoPP::Integer *v37; // [sp+210h] [bp-58h]@4
  struct CryptoPP::Integer *v38; // [sp+218h] [bp-50h]@4
  CryptoPP::GeneratableCryptoMaterialVtbl *v39; // [sp+220h] [bp-48h]@4
  struct CryptoPP::Integer *v40; // [sp+228h] [bp-40h]@4
  struct CryptoPP::Integer *v41; // [sp+230h] [bp-38h]@4
  void (__fastcall **v42)(_QWORD, _QWORD, _QWORD, _QWORD); // [sp+238h] [bp-30h]@4
  unsigned __int64 v43; // [sp+240h] [bp-28h]@4
  __int64 v44; // [sp+248h] [bp-20h]@4
  __int64 v45; // [sp+250h] [bp-18h]@4
  CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint> *v46; // [sp+270h] [bp+8h]@1
  struct CryptoPP::RandomNumberGenerator *v47; // [sp+278h] [bp+10h]@1
  const char *v48; // [sp+280h] [bp+18h]@1
  unsigned __int64 v49; // [sp+288h] [bp+20h]@1
  char *ciphertexta; // [sp+290h] [bp+28h]@4

  v49 = plaintextLength;
  v48 = plaintext;
  v47 = rng;
  v46 = this;
  v6 = &v20;
  for ( i = 152i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v35 = -2i64;
  LODWORD(v8) = ((int (__fastcall *)(CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint> *))v46->vfptr[1].CiphertextLength)(v46);
  v24 = v8;
  LODWORD(v9) = ((int (__fastcall *)(CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint> *))v46->vfptr[1].ParameterSupported)(v46);
  v25 = v9;
  LODWORD(v10) = ((int (__fastcall *)(CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint> *))v46->vfptr[1].FixedCiphertextLength)(v46);
  v26 = v10;
  v27 = CryptoPP::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>>::GetAbstractGroupParameters((CryptoPP::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> > *)&v46->vfptr);
  v36 = v46->vfptr;
  LODWORD(v11) = ((int (__fastcall *)(signed __int64))v36->GetKeyInterface)((signed __int64)&v46->vfptr);
  v28 = v11;
  v37 = (struct CryptoPP::Integer *)CryptoPP::Integer::One();
  v38 = (struct CryptoPP::Integer *)CryptoPP::Integer::Zero();
  v39 = v27->vfptr;
  LODWORD(v12) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *, CryptoPP::Integer *))v39[9].__vecDelDtor)(
                   v27,
                   &v34);
  v40 = v12;
  v41 = v12;
  v13 = CryptoPP::Integer::One();
  CryptoPP::Integer::Integer((enum CryptoPP::Integer::RandomNumberType)&v29, v47, v13, v41, 0, v38, v37);
  CryptoPP::Integer::~Integer(&v34);
  ((void (__fastcall *)(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *, CryptoPP::ECPPoint *, CryptoPP::Integer *))v27->vfptr[3].__vecDelDtor)(
    v27,
    &v30,
    &v29);
  LOBYTE(v14) = 1;
  ((void (__fastcall *)(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *, __int64, CryptoPP::ECPPoint *, char *))v27->vfptr[13].__vecDelDtor)(
    v27,
    v14,
    &v30,
    ciphertext);
  LOBYTE(v15) = 1;
  v31 = ((int (__fastcall *)(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *, __int64))v27->vfptr[12].__vecDelDtor)(
          v27,
          v15);
  ciphertexta = &ciphertext[v31];
  LODWORD(v16) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v28 + 48i64))(v28);
  v42 = *v24;
  *(_QWORD *)v21 = &v29;
  (*v42)(v24, &v32, v27, v16);
  LODWORD(v17) = (*(int (__fastcall **)(__int64, unsigned __int64))(*(_QWORD *)v26 + 8i64))(v26, v49);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v33,
    v17);
  v43 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v33);
  v18 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v33);
  v44 = *v25;
  v23 = (struct CryptoPP::Integer *)parameters;
  v22 = (struct CryptoPP::Integer *)&v30;
  *(_QWORD *)v21 = &v32;
  (*(void (__fastcall **)(__int64 *, CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *, char *, unsigned __int64))(v44 + 8))(
    v25,
    v27,
    v18,
    v43);
  v19 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v33);
  v45 = *(_QWORD *)v26;
  v23 = (struct CryptoPP::Integer *)parameters;
  v22 = (struct CryptoPP::Integer *)ciphertexta;
  *(_QWORD *)v21 = v49;
  (*(void (__fastcall **)(__int64, struct CryptoPP::RandomNumberGenerator *, char *, const char *))(v45 + 32))(
    v26,
    v47,
    v19,
    v48);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v33);
  CryptoPP::ECPPoint::~ECPPoint(&v32);
  CryptoPP::ECPPoint::~ECPPoint(&v30);
  CryptoPP::Integer::~Integer(&v29);
}
