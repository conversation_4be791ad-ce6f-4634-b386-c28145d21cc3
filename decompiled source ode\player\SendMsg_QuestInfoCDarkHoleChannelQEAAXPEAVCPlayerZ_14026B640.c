/*
 * Function: ?SendMsg_QuestInfo@CDarkHoleChannel@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x14026B640
 */

void __fastcall CDarkHoleChannel::SendMsg_QuestInfo(CDarkHoleChannel *this, CPlayer *pDst)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@7
  __int64 v5; // [sp+0h] [bp-B8h]@1
  char Dest; // [sp+38h] [bp-80h]@4
  int v7; // [sp+49h] [bp-6Fh]@4
  DWORD v8; // [sp+4Dh] [bp-6Bh]@4
  long double v9; // [sp+51h] [bp-67h]@7
  unsigned int v10; // [sp+59h] [bp-5Fh]@7
  unsigned int v11; // [sp+5Dh] [bp-5Bh]@7
  _dh_reward_sub_setup *v12; // [sp+78h] [bp-40h]@4
  char pbyType; // [sp+84h] [bp-34h]@7
  char v14; // [sp+85h] [bp-33h]@7
  unsigned __int64 v15; // [sp+A0h] [bp-18h]@4
  CDarkHoleChannel *v16; // [sp+C0h] [bp+8h]@1
  CPlayer *v17; // [sp+C8h] [bp+10h]@1

  v17 = pDst;
  v16 = this;
  v2 = &v5;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v15 = (unsigned __int64)&v5 ^ _security_cookie;
  v7 = -1;
  strcpy_0(&Dest, v16->m_pQuestSetup->szDescirptCode);
  v8 = (timeGetTime() - v16->m_dwQuestStartTime) / 0x3E8;
  v12 = 0i64;
  if ( v17->m_dwObjSerial == v16->m_dwOpenerSerial )
    v12 = &v16->m_pQuestSetup->RewardOne;
  else
    v12 = &v16->m_pQuestSetup->RewardOther;
  v9 = v12->dExp;
  v10 = v12->dwPvp;
  v11 = v12->dwDalant;
  pbyType = 35;
  v14 = 4;
  v4 = _darkhole_quest_info_inform_zocl::size((_darkhole_quest_info_inform_zocl *)&Dest);
  CNetProcess::LoadSendMsg(unk_1414F2088, v17->m_ObjID.m_wIndex, &pbyType, &Dest, v4);
}
