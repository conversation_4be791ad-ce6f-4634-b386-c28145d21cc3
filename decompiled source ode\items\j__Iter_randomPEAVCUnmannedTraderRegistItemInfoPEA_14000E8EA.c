/*
 * Function: j_??$_Iter_random@PEAVCUnmannedTraderRegistItemInfo@@PEAV1@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAVCUnmannedTraderRegistItemInfo@@0@Z
 * Address: 0x14000E8EA
 */

std::random_access_iterator_tag __fastcall std::_Iter_random<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *>(CUnmannedTraderRegistItemInfo *const *__formal, CUnmannedTraderRegistItemInfo *const *a2)
{
  return std::_Iter_random<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *>(__formal, a2);
}
