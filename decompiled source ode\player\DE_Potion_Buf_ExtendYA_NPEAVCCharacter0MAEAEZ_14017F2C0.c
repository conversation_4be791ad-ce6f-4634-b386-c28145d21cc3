/*
 * Function: ?DE_Potion_Buf_Extend@@YA_NPEAVCCharacter@@0MAEAE@Z
 * Address: 0x14017F2C0
 */

char __fastcall DE_Potion_Buf_Extend(CCharacter *pActChar, CCharacter *pTargetChar, float fEffectValue, char *byRet)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  CPlayer *v8; // [sp+20h] [bp-18h]@10
  CPlayer *v9; // [sp+40h] [bp+8h]@1

  v9 = (CPlayer *)pActChar;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v9 )
  {
    if ( v9->m_ObjID.m_byID )
    {
      result = 0;
    }
    else if ( (CCharacter *)v9 == pTargetChar )
    {
      v8 = v9;
      CPlayer::Potion_Buf_Extend(v9);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
