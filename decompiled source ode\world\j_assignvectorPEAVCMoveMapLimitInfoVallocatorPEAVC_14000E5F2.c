/*
 * Function: j_?assign@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEAAX_KAEBQEAVCMoveMapLimitInfo@@@Z
 * Address: 0x14000E5F2
 */

void __fastcall std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::assign(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, unsigned __int64 _Count, CMoveMapLimitInfo *const *_Val)
{
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::assign(this, _Count, _Val);
}
