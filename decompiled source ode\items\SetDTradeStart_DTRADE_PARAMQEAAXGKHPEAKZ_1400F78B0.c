/*
 * Function: ?SetDTradeStart@_DTRADE_PARAM@@QEAAXGKHPEAK@Z
 * Address: 0x1400F78B0
 */

void __fastcall _DTRADE_PARAM::SetDTradeStart(_DTRADE_PARAM *this, unsigned __int16 pl_dwDstIndex, unsigned int pl_dwDstSerial, int pl_mEmptyInvenNum, unsigned int *pl_dwKey)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _DTRADE_PARAM *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v5 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v9->bDTradeMode = 1;
  v9->bDTradeLock = 0;
  v9->bDTradeOK = 0;
  v9->wDTradeDstIndex = pl_dwDstIndex;
  v9->dwDTradeDstSerial = pl_dwDstSerial;
  v9->dwDTrade_Dalant = 0;
  v9->dwDTrade_Gold = 0;
  v9->byEmptyInvenNum = pl_mEmptyInvenNum;
  v9->bySellItemNum = 0;
  for ( j = 0; j < 15; ++j )
    _DTRADE_ITEM::ReleaseData(&v9->DItemNode[j]);
  memcpy_0(v9->dwKey, pl_dwKey, 0x10ui64);
}
