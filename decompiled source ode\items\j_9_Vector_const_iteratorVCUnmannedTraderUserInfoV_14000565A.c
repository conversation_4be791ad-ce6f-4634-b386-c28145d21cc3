/*
 * Function: j_??9?$_Vector_const_iterator@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@QEBA_NAEBV01@@Z
 * Address: 0x14000565A
 */

bool __fastcall std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator!=(std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this, std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *_Right)
{
  return std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator!=(
           this,
           _Right);
}
