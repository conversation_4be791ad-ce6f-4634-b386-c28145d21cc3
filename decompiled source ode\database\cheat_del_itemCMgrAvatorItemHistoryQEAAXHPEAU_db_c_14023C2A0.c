/*
 * Function: ?cheat_del_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@EPEAD@Z
 * Address: 0x14023C2A0
 */

void __fastcall CMgrAvatorItemHistory::cheat_del_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pItem, char byDelNum, char *pszFileName)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char *v7; // rax@7
  __int64 v8; // [sp+0h] [bp-58h]@1
  char *v9; // [sp+20h] [bp-38h]@4
  unsigned __int64 v10; // [sp+28h] [bp-30h]@7
  int j; // [sp+30h] [bp-28h]@4
  _base_fld *v12; // [sp+38h] [bp-20h]@7
  __int64 v13; // [sp+40h] [bp-18h]@7
  int nTableCode; // [sp+48h] [bp-10h]@7
  CMgrAvatorItemHistory *v15; // [sp+60h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v16; // [sp+70h] [bp+18h]@1
  char v17; // [sp+78h] [bp+20h]@1

  v17 = byDelNum;
  v16 = pItem;
  v15 = this;
  v5 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  sData[0] = 0;
  v9 = v15->m_szCurTime;
  sprintf(sBuf, "CHEAT(ITEM-): num:%d [%s %s]\r\n", (unsigned __int8)byDelNum, v15->m_szCurDate);
  strcat_0(sData, sBuf);
  for ( j = 0; j < (unsigned __int8)v17; ++j )
  {
    if ( IsProtectItem(v16[j].m_byTableCode) )
    {
      v12 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v16[j].m_byTableCode, v16[j].m_wItemIndex);
      v13 = 50i64 * j;
      nTableCode = v16[j].m_byTableCode;
      v7 = DisplayItemUpgInfo(nTableCode, v16[j].m_dwLv);
      v10 = v16[(unsigned __int64)v13 / 0x32].m_lnUID;
      v9 = v7;
      sprintf(sBuf, "\t- %s_%u_@%s[%I64u]\r\n", v12->m_strCode, v16[j].m_dwDur);
      strcat_0(sData, sBuf);
    }
  }
  CMgrAvatorItemHistory::WriteFile(v15, pszFileName, sData);
}
