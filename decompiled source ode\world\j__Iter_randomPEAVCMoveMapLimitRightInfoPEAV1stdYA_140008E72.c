/*
 * Function: j_??$_Iter_random@PEAVCMoveMapLimitRightInfo@@PEAV1@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAVCMoveMapLimitRightInfo@@0@Z
 * Address: 0x140008E72
 */

std::random_access_iterator_tag __fastcall std::_Iter_random<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *>(CMoveMapLimitRightInfo *const *__formal, CMoveMapLimitRightInfo *const *a2)
{
  return std::_Iter_random<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *>(__formal, a2);
}
