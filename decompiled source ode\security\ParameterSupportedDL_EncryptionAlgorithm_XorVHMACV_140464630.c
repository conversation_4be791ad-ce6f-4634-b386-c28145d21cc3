/*
 * Function: ?ParameterSupported@?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@CryptoPP@@UEBA_NPEBD@Z
 * Address: 0x140464630
 */

bool __fastcall CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>::ParameterSupported(CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *this, const char *name)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  const char *v4; // rax@4
  __int64 v6; // [sp+0h] [bp-38h]@1
  char *Str1; // [sp+48h] [bp+10h]@1

  Str1 = (char *)name;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = CryptoPP::Name::EncodingParameters();
  return strcmp_0(Str1, v4) == 0;
}
