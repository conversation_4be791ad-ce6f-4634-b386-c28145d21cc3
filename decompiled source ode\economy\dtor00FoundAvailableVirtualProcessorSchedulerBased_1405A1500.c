/*
 * Function: ?dtor$0@?0??FoundAvailableVirtualProcessor@SchedulerBase@details@Concurrency@@QEAA_NAEAVClaimTicket@VirtualProcessor@23@Vlocation@3@K@Z@4HA_3
 * Address: 0x1405A1500
 */

int __fastcall `Concurrency::details::SchedulerBase::FoundAvailableVirtualProcessor'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  __int64 v2; // rcx@1

  v2 = *(_QWORD *)(a2 + 240);
  return std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
}
