/*
 * Function: ?AddEventItem@CMonster@@QEAA_NPEAU_event_loot_item@@@Z
 * Address: 0x140142AB0
 */

char __fastcall CMonster::AddEventItem(CMonster *this, _event_loot_item *pItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMonster *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v6->m_nEventItemNum < 16 )
  {
    memcpy_0(&v6->m_eventItem[v6->m_nEventItemNum], pItem, 4ui64);
    ++v6->m_nEventItemNum;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
