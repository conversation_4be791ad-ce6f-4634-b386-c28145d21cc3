/*
 * Function: ?FindEmpty@CUnmannedTraderUserInfo@@AEAA?AV?$_Vector_iterator@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@XZ
 * Address: 0x140359DB0
 */

std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *__fastcall CUnmannedTraderUserInfo::FindEmpty(CUnmannedTraderUserInfo *this, std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *result)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderRegistItemInfo *v4; // rax@6
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v5; // rax@7
  __int64 v7; // [sp+0h] [bp-78h]@1
  unsigned __int8 j; // [sp+20h] [bp-58h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > resulta; // [sp+28h] [bp-50h]@7
  int v10; // [sp+40h] [bp-38h]@4
  __int64 v11; // [sp+48h] [bp-30h]@4
  __int64 _Off; // [sp+50h] [bp-28h]@7
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v13; // [sp+58h] [bp-20h]@7
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v14; // [sp+60h] [bp-18h]@7
  CUnmannedTraderUserInfo *v15; // [sp+80h] [bp+8h]@1
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v16; // [sp+88h] [bp+10h]@1

  v16 = result;
  v15 = this;
  v2 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = -2i64;
  v10 = 0;
  for ( j = 0; j < (signed int)v15->m_byMaxRegistCnt; ++j )
  {
    v4 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
           &v15->m_vecRegistItemInfo,
           j);
    if ( CUnmannedTraderRegistItemInfo::IsEmpty(v4) )
    {
      _Off = j;
      v5 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::begin(
             &v15->m_vecRegistItemInfo,
             &resulta);
      v13 = v5;
      v14 = v5;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator+(
        v5,
        v16,
        _Off);
      v10 |= 1u;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&resulta);
      return v16;
    }
  }
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
    &v15->m_vecRegistItemInfo,
    v16);
  return v16;
}
