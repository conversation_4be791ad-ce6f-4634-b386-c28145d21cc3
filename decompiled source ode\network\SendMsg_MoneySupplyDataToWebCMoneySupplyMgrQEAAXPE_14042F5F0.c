/*
 * Function: ?SendMsg_MoneySupplyDataToWeb@CMoneySupplyMgr@@QEAAXPEAU_MONEY_SUPPLY_DATA@@@Z
 * Address: 0x14042F5F0
 */

void __fastcall CMoneySupplyMgr::SendMsg_MoneySupplyDataToWeb(CMoneySupplyMgr *this, _MONEY_SUPPLY_DATA *pMSData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@4
  __int64 v5; // [sp+0h] [bp-548h]@1
  _money_supply_gatering_inform_zowb Dst; // [sp+40h] [bp-508h]@4
  char pbyType; // [sp+524h] [bp-24h]@4
  char v8; // [sp+525h] [bp-23h]@4
  _MONEY_SUPPLY_DATA *v9; // [sp+558h] [bp+10h]@1

  v9 = pMSData;
  v2 = &v5;
  for ( i = 336i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _money_supply_gatering_inform_zowb::init(&Dst);
  v4 = _MONEY_SUPPLY_DATA::size(v9);
  memcpy_0(&Dst, v9, v4);
  pbyType = 51;
  v8 = 20;
  if ( unk_1799C9ADE )
    CNetProcess::LoadSendMsg(unk_1414F2098, unk_1799C9ADD, &pbyType, (char *)&Dst, 0x4CCu);
}
