/*
 * Function: ?CheckGuildBattleLimit@CAttack@@IEAA_NPEAVCGameObject@@PEA_N@Z
 * Address: 0x14016B570
 */

bool __fastcall CAttack::CheckGuildBattleLimit(CAttack *this, CGameObject *pObject, bool *pbInGuildBattle)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@7
  CGameObject *v6; // [sp+0h] [bp-18h]@1
  CCharacter *v7; // [sp+8h] [bp-10h]@16
  CAttack *v8; // [sp+20h] [bp+8h]@1

  v8 = this;
  v3 = (__int64 *)&v6;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pbInGuildBattle )
    *pbInGuildBattle = 0;
  if ( v8->m_pAttChar->m_ObjID.m_byID )
  {
    result = 0;
  }
  else if ( pObject->m_ObjID.m_byKind )
  {
    result = 0;
  }
  else
  {
    v6 = 0i64;
    if ( pObject->m_ObjID.m_byID )
    {
      if ( !pObject->m_ObjID.m_byID )
        v6 = *(CGameObject **)&pObject[9].m_bBreakTranspar;
    }
    else
    {
      v6 = pObject;
    }
    if ( v6 )
    {
      v7 = v8->m_pAttChar;
      if ( pbInGuildBattle && (BYTE2(v6[10].vfptr) || BYTE2(v7[1].m_fCurPos[2])) )
        *pbInGuildBattle = 1;
      if ( BYTE2(v7[1].m_fCurPos[2]) )
      {
        if ( LOBYTE(v7[1].m_fCurPos[2]) )
        {
          result = 1;
        }
        else if ( BYTE2(v6[10].vfptr) )
        {
          if ( LOBYTE(v6[10].vfptr) )
            result = 1;
          else
            result = LOBYTE(v7[1].m_fAbsPos[0]) == BYTE4(v6[10].vfptr);
        }
        else
        {
          result = 1;
        }
      }
      else
      {
        result = BYTE2(v6[10].vfptr) != 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  return result;
}
