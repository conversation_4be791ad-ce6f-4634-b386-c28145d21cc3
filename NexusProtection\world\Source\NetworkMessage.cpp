/**
 * @file NetworkMessage.cpp
 * @brief Implementation of network message structures and utilities
 * 
 * This file contains the implementation of network message structures and
 * their size calculation methods, refactored from decompiled C source files.
 * 
 * @note Refactored from multiple decompiled network message size functions:
 * - _add_char_result_zone::size (Address: 0x14011F870)
 * - _enter_world_result_zone::size (Address: 0x14011F250)
 * - _reged_char_result_zone::size (Address: 0x14011F6F0)
 * - _target_monster_contsf_allinform_zocl::size (Address: 0x1400F0140)
 */

#include "NetworkMessage.h"
#include <stdexcept>
#include <cstring>

namespace NetworkMessage {

// AddCharResultZone implementation

int64_t AddCharResultZone::size() const {
    // Original implementation: return 2i64;
    return Constants::BASE_ADD_CHAR_SIZE;
}

bool AddCharResultZone::validate() const {
    // Basic validation - always valid for this simple message type
    return true;
}

// EnterWorldResultZone implementation

int64_t EnterWorldResultZone::size() const {
    // Original implementation: return 3i64;
    return Constants::BASE_ENTER_WORLD_SIZE;
}

bool EnterWorldResultZone::validate() const {
    // Basic validation - always valid for this simple message type
    return true;
}

// RegedCharResultZone implementation

int64_t RegedCharResultZone::size() {
    // Original implementation:
    // if ( this->byCharNum > 3 )
    //     this->byCharNum = 0;
    // return 221 - 69i64 * (3 - this->byCharNum);
    
    clampCharNum();
    
    return CalculateVariableCharSize(byCharNum, 
                                     Constants::BASE_REGED_CHAR_SIZE,
                                     Constants::CHAR_ENTRY_SIZE,
                                     Constants::MAX_CHAR_COUNT);
}

bool RegedCharResultZone::validate() const {
    return IsValidCharCount(byCharNum);
}

void RegedCharResultZone::setCharNum(uint8_t charCount) {
    byCharNum = charCount;
    clampCharNum();
}

void RegedCharResultZone::clampCharNum() {
    if (byCharNum > Constants::MAX_CHAR_COUNT) {
        byCharNum = 0;
    }
}

// TargetMonsterContsfAllinformZocl implementation

int64_t TargetMonsterContsfAllinformZocl::size() {
    // Original implementation:
    // if ( this->byContCount > 8 )
    //     this->byContCount = 0;
    // return 21 - 2i64 * (8 - this->byContCount);
    
    clampContCount();
    
    return CalculateVariableEffectSize(byContCount,
                                       Constants::BASE_TARGET_MONSTER_SIZE,
                                       Constants::CONT_ENTRY_SIZE,
                                       Constants::MAX_CONT_COUNT);
}

bool TargetMonsterContsfAllinformZocl::validate() const {
    return IsValidContCount(byContCount);
}

void TargetMonsterContsfAllinformZocl::setContCount(uint8_t contCount) {
    byContCount = contCount;
    clampContCount();
}

void TargetMonsterContsfAllinformZocl::clampContCount() {
    if (byContCount > Constants::MAX_CONT_COUNT) {
        byContCount = 0;
    }
}

// Utility functions implementation

bool IsValidCharCount(uint8_t charCount) {
    return charCount <= Constants::MAX_CHAR_COUNT;
}

bool IsValidContCount(uint8_t contCount) {
    return contCount <= Constants::MAX_CONT_COUNT;
}

int64_t CalculateVariableCharSize(uint8_t charCount, 
                                  int64_t baseSize, 
                                  int64_t entrySize, 
                                  uint8_t maxCount) {
    // Ensure charCount is within valid range
    uint8_t validCharCount = ClampToRange(charCount, maxCount);
    
    // Calculate size: baseSize - entrySize * (maxCount - actualCount)
    // This matches the original formula: 221 - 69 * (3 - byCharNum)
    return baseSize - entrySize * (maxCount - validCharCount);
}

int64_t CalculateVariableEffectSize(uint8_t effectCount, 
                                    int64_t baseSize, 
                                    int64_t entrySize, 
                                    uint8_t maxCount) {
    // Ensure effectCount is within valid range
    uint8_t validEffectCount = ClampToRange(effectCount, maxCount);
    
    // Calculate size: baseSize - entrySize * (maxCount - actualCount)
    // This matches the original formula: 21 - 2 * (8 - byContCount)
    return baseSize - entrySize * (maxCount - validEffectCount);
}

uint8_t ClampToRange(uint8_t value, uint8_t maxValue) {
    return (value > maxValue) ? 0 : value;
}

const char* GetMessageTypeName(int messageType) {
    switch (messageType) {
        case 1: return "ADD_CHAR_RESULT";
        case 2: return "ENTER_WORLD_RESULT";
        case 3: return "REGED_CHAR_RESULT";
        case 4: return "TARGET_MONSTER_CONTSF_ALLINFORM";
        default: return "UNKNOWN_MESSAGE_TYPE";
    }
}

} // namespace NetworkMessage

// Legacy C-style interface implementation

extern "C" {

int64_t _add_char_result_zone_size(void* this_ptr) {
    if (!this_ptr) {
        return -1;  // Error indicator
    }
    
    try {
        NetworkMessage::AddCharResultZone* msg = 
            static_cast<NetworkMessage::AddCharResultZone*>(this_ptr);
        return msg->size();
    } catch (...) {
        return -1;  // Error indicator
    }
}

int64_t _enter_world_result_zone_size(void* this_ptr) {
    if (!this_ptr) {
        return -1;  // Error indicator
    }
    
    try {
        NetworkMessage::EnterWorldResultZone* msg = 
            static_cast<NetworkMessage::EnterWorldResultZone*>(this_ptr);
        return msg->size();
    } catch (...) {
        return -1;  // Error indicator
    }
}

int64_t _reged_char_result_zone_size(void* this_ptr) {
    if (!this_ptr) {
        return -1;  // Error indicator
    }
    
    try {
        NetworkMessage::RegedCharResultZone* msg = 
            static_cast<NetworkMessage::RegedCharResultZone*>(this_ptr);
        return msg->size();
    } catch (...) {
        return -1;  // Error indicator
    }
}

int64_t _target_monster_contsf_allinform_zocl_size(void* this_ptr) {
    if (!this_ptr) {
        return -1;  // Error indicator
    }
    
    try {
        NetworkMessage::TargetMonsterContsfAllinformZocl* msg = 
            static_cast<NetworkMessage::TargetMonsterContsfAllinformZocl*>(this_ptr);
        return msg->size();
    } catch (...) {
        return -1;  // Error indicator
    }
}

} // extern "C"
