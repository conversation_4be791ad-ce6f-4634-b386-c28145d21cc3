/*
 * Function: ?mgr_recall_party_player@CPlayer@@QEAA_NPEAD@Z
 * Address: 0x1400BF5C0
 */

char __fastcall CPlayer::mgr_recall_party_player(CPlayer *this, char *wszDestCharName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-58h]@1
  int iRange; // [sp+20h] [bp-38h]@13
  CPlayer *v7; // [sp+30h] [bp-28h]@8
  CPartyPlayer **v8; // [sp+38h] [bp-20h]@14
  CPlayer *v9; // [sp+40h] [bp-18h]@16
  int j; // [sp+48h] [bp-10h]@16
  CPlayer *v11; // [sp+60h] [bp+8h]@1

  v11 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( wszDestCharName )
  {
    if ( v11->m_pCurMap->m_pMapSet->m_nMapType )
    {
      result = 0;
    }
    else
    {
      v7 = GetPtrPlayerFromName(&g_Player, 2532, wszDestCharName);
      if ( v7 )
      {
        if ( CPartyPlayer::IsPartyMode(v7->m_pPartyMgr) )
        {
          v8 = CPartyPlayer::GetPtrPartyMember(v7->m_pPartyMgr);
          if ( v8 )
          {
            v9 = 0i64;
            for ( j = 0; j < 8 && v8[j]; ++j )
            {
              v9 = &g_Player + v8[j]->m_wZoneIndex;
              if ( v9->m_bLive && v11->m_dwObjSerial != v9->m_dwObjSerial )
              {
                iRange = 120;
                CPlayer::RecallRandomPositionInRange(v9, v11->m_pCurMap, v11->m_wMapLayerIndex, v11->m_fCurPos, 120);
              }
            }
            result = 1;
          }
          else
          {
            result = 0;
          }
        }
        else if ( v11->m_dwObjSerial == v7->m_dwObjSerial )
        {
          result = 0;
        }
        else
        {
          iRange = 120;
          CPlayer::RecallRandomPositionInRange(v7, v11->m_pCurMap, v11->m_wMapLayerIndex, v11->m_fCurPos, 120);
          result = 1;
        }
      }
      else
      {
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
