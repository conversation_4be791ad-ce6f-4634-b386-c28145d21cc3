/*
 * Function: ?GetItemEquipGrade@@YAHHPEBD@Z
 * Address: 0x14003F470
 */

signed __int64 __fastcall GetItemEquipGrade(int nTableCode, const char *szRecordCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  __int64 v5; // [sp+0h] [bp-68h]@1
  CRecordData *v6; // [sp+20h] [bp-48h]@4
  _base_fld *v7; // [sp+28h] [bp-40h]@5
  _base_fld *v8; // [sp+30h] [bp-38h]@8
  _base_fld *v9; // [sp+38h] [bp-30h]@11
  _base_fld *v10; // [sp+40h] [bp-28h]@14
  _base_fld *v11; // [sp+48h] [bp-20h]@17
  int v12; // [sp+50h] [bp-18h]@4
  int v13; // [sp+70h] [bp+8h]@1

  v13 = nTableCode;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = &s_ptblItemData[v13];
  v12 = v13;
  switch ( v13 )
  {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      v7 = CRecordData::GetRecord(v6, szRecordCode);
      if ( !v7 )
        goto LABEL_19;
      result = *(_DWORD *)&v7[4].m_strCode[16];
      break;
    case 6:
      v8 = CRecordData::GetRecord(v6, szRecordCode);
      if ( !v8 )
        goto LABEL_19;
      result = *(_DWORD *)&v8[8].m_strCode[16];
      break;
    case 9:
      v9 = CRecordData::GetRecord(v6, szRecordCode);
      if ( !v9 )
        goto LABEL_19;
      result = *(_DWORD *)&v9[4].m_strCode[12];
      break;
    case 8:
      v10 = CRecordData::GetRecord(v6, szRecordCode);
      if ( !v10 )
        goto LABEL_19;
      result = *(_DWORD *)&v10[4].m_strCode[12];
      break;
    case 7:
      v11 = CRecordData::GetRecord(v6, szRecordCode);
      if ( !v11 )
        goto LABEL_19;
      result = *(_DWORD *)&v11[4].m_strCode[16];
      break;
    default:
LABEL_19:
      result = 1i64;
      break;
  }
  return result;
}
