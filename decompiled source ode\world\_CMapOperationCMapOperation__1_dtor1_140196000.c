/*
 * Function: _CMapOperation::CMapOperation_::_1_::dtor$1
 * Address: 0x140196000
 */

void __fastcall CMapOperation::CMapOperation_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::vector<std::pair<int,int>,std::allocator<std::pair<int,int>>>::~vector<std::pair<int,int>,std::allocator<std::pair<int,int>>>((std::vector<std::pair<int,int>,std::allocator<std::pair<int,int> > > *)(*(_QWORD *)(a2 + 64) + 48i64));
}
