/*
 * Function: ?GetNextYposForServer@CLevel@@QEAAHQEAMPEAM@Z
 * Address: 0x1404E0F50
 */

__int64 __fastcall CLevel::GetNextYposForServer(CLevel *this, float *const a2, float *a3)
{
  CLevel *v3; // r12@1
  CBsp *v4; // rcx@1
  float *v5; // r13@1
  float *v6; // rbp@1
  __int16 v7; // ax@1
  float v8; // xmm0_4@1
  CBsp *v9; // rcx@1
  __int16 v10; // bx@1
  signed int v11; // edi@1
  float v12; // xmm1_4@1
  int v13; // ecx@1
  __int64 v14; // rbx@1
  float v15; // xmm6_4@1
  float v16; // xmm3_4@1
  __int64 v17; // rsi@1
  float v18; // xmm0_4@2
  CBsp *v19; // rcx@2
  float v20; // xmm0_4@2
  __int64 v22; // [sp+0h] [bp-2B8h]@1
  int v23; // [sp+28h] [bp-290h]@2
  float v24; // [sp+30h] [bp-288h]@1
  float v25; // [sp+34h] [bp-284h]@1
  int v26; // [sp+38h] [bp-280h]@1
  float v27; // [sp+40h] [bp-278h]@1
  float v28; // [sp+44h] [bp-274h]@1
  int v29; // [sp+48h] [bp-270h]@1
  __int16 v30; // [sp+50h] [bp-268h]@1
  __int16 v31; // [sp+52h] [bp-266h]@1
  unsigned __int64 v32; // [sp+250h] [bp-68h]@1

  v32 = (unsigned __int64)&v22 ^ _security_cookie;
  v3 = this;
  v4 = this->mBsp;
  v5 = a3;
  v6 = a2;
  a2[1] = a2[1] - 23.0;
  v7 = CBsp::GetLeafNum(v4, a2);
  v8 = v6[1];
  v9 = v3->mBsp;
  v10 = v7;
  v30 = v7;
  v6[1] = v8 + 46.0;
  v11 = 0;
  v31 = CBsp::GetLeafNum(v9, v6);
  v12 = *v6;
  v13 = v10 != v31;
  v14 = 0i64;
  v15 = FLOAT_N100000_0;
  v16 = v6[1] - 23.0;
  v26 = *((_DWORD *)v6 + 2);
  v17 = ++v13;
  v29 = v26;
  v24 = v12;
  v27 = v12;
  v6[1] = v16;
  v25 = v16 + 9999.0;
  v28 = v16 + (float)(0.0 - 9999.0);
  if ( v13 > 0 )
  {
    do
    {
      v18 = v6[1];
      v19 = v3->mBsp;
      v23 = *(&v30 + v14);
      v20 = CBsp::GetYposInLeafUseEdgeNormal(v19, &v24, &v27, 23.0, v18, v23);
      if ( v20 != -32000.0 && v20 > v15 )
      {
        v11 = 1;
        v15 = v20;
      }
      ++v14;
    }
    while ( v14 < v17 );
    if ( v11 )
      *v5 = v15;
  }
  return (unsigned int)v11;
}
