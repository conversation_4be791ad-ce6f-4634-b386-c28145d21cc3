/*
 * Function: ?Init@CUnmannedTraderScheduler@@QEAA_NXZ
 * Address: 0x1403933B0
 */

char __fastcall CUnmannedTraderScheduler::Init(CUnmannedTraderScheduler *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@5
  char v4; // al@8
  CUnmannedTraderSchedule *v5; // rax@9
  unsigned __int64 v6; // rax@9
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v7; // rax@11
  __int64 v8; // [sp+0h] [bp-A8h]@1
  CMyTimer *v9; // [sp+20h] [bp-88h]@7
  CMyTimer *v10; // [sp+28h] [bp-80h]@4
  CUnmannedTraderSchedule v11; // [sp+30h] [bp-78h]@9
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > result; // [sp+50h] [bp-58h]@11
  __int64 v13; // [sp+68h] [bp-40h]@4
  CMyTimer *v14; // [sp+70h] [bp-38h]@5
  CUnmannedTraderSchedule *v15; // [sp+78h] [bp-30h]@9
  CUnmannedTraderSchedule *_Val; // [sp+80h] [bp-28h]@9
  __int64 v17; // [sp+88h] [bp-20h]@9
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v18; // [sp+90h] [bp-18h]@11
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *__that; // [sp+98h] [bp-10h]@11
  CUnmannedTraderScheduler *v20; // [sp+B0h] [bp+8h]@1

  v20 = this;
  v1 = &v8;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v13 = -2i64;
  v10 = (CMyTimer *)operator new(0x18ui64);
  if ( v10 )
  {
    CMyTimer::CMyTimer(v10);
    v14 = (CMyTimer *)v3;
  }
  else
  {
    v14 = 0i64;
  }
  v9 = v14;
  v20->m_pkTimer = v14;
  if ( v20->m_pkTimer )
  {
    CUnmannedTraderSchedule::CUnmannedTraderSchedule(&v11);
    v15 = v5;
    _Val = v5;
    std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::assign(
      &v20->m_veckSchdule,
      CUnmannedTraderEnvironmentValue::Unmanned_Trader_Max_Schdule_Cnt,
      v5);
    CUnmannedTraderSchedule::~CUnmannedTraderSchedule(&v11);
    v17 = CUnmannedTraderEnvironmentValue::Unmanned_Trader_Max_Schdule_Cnt;
    v6 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::size(&v20->m_veckSchdule);
    if ( v17 == v6 )
    {
      v7 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::begin(
             &v20->m_veckSchdule,
             &result);
      v18 = v7;
      __that = v7;
      std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator=(
        &v20->m_iterSchedule,
        v7);
      std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(&result);
      CMyTimer::BeginTimer(v20->m_pkTimer, CUnmannedTraderEnvironmentValue::Unmanned_Trader_Check_Schedule_Delay);
      v20->m_iOldDay = GetCurrentDay();
      v4 = 1;
    }
    else
    {
      v4 = 0;
    }
  }
  else
  {
    v4 = 0;
  }
  return v4;
}
