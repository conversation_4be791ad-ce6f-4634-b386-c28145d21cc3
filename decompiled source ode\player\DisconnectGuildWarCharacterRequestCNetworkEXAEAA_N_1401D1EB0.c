/*
 * Function: ?DisconnectGuildWarCharacterRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D1EB0
 */

char __fastcall CNetworkEX::DisconnectGuildWarCharacterRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@5
  char result; // al@5
  unsigned __int16 v7; // ax@6
  __int64 v8; // [sp+0h] [bp-A8h]@1
  char *v9; // [sp+30h] [bp-78h]@4
  _character_disconnect_result_wrac v10; // [sp+44h] [bp-64h]@4
  CUserDB *v11; // [sp+58h] [bp-50h]@4
  char pbyType; // [sp+64h] [bp-44h]@5
  char v13; // [sp+65h] [bp-43h]@5
  char v14; // [sp+84h] [bp-24h]@6
  char v15; // [sp+85h] [bp-23h]@6

  v3 = &v8;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = pBuf;
  v10.wClientIndex = 0;
  memset(&v10.byResult, 0, sizeof(v10.byResult));
  v10.wClientIndex = *(_WORD *)pBuf;
  v11 = SearchAvatorWithName(g_UserDB, 2532, pBuf + 2);
  if ( v11 )
  {
    CUserDB::ForceCloseCommand(v11, 0, 0, 1, "Kick By GM-Tool");
    v10.byResult = 0;
    pbyType = 50;
    v13 = 25;
    v5 = _character_disconnect_result_wrac::size(&v10);
    CNetProcess::LoadSendMsg(unk_1414F2090, 0, &pbyType, (char *)&v10, v5);
    result = 1;
  }
  else
  {
    v10.byResult = 1;
    v14 = 50;
    v15 = 25;
    v7 = _character_disconnect_result_wrac::size(&v10);
    CNetProcess::LoadSendMsg(unk_1414F2090, 0, &v14, (char *)&v10, v7);
    result = 1;
  }
  return result;
}
