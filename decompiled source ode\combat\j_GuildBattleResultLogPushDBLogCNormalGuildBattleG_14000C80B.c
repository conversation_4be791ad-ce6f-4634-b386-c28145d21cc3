/*
 * Function: j_?GuildBattleResultLogPushDBLog@CNormalGuildBattle@GUILD_BATTLE@@IEAAXAEAV_qry_case_guild_battel_result_log@@PEAVCNormalGuildBattleGuildMember@2@1@Z
 * Address: 0x14000C80B
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::GuildBattleResultLogPushDBLog(GUILD_BATTLE::CNormalGuildBattle *this, _qry_case_guild_battel_result_log *Sheet, GUILD_BATTLE::CNormalGuildBattleGuildMember *pkTopGoalMember, GUILD_BATTLE::CNormalGuildBattleGuildMember *pkTopKillMember)
{
  GUILD_BATTLE::CNormalGuildBattle::GuildBattleResultLogPushDBLog(this, Sheet, pkTopGoalMember, pkTopKillMember);
}
