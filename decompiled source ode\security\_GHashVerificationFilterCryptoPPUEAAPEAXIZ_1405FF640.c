/*
 * Function: ??_GHashVerificationFilter@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x1405FF640
 */

CryptoPP::HashVerificationFilter *__fastcall CryptoPP::HashVerificationFilter::`scalar deleting destructor'(CryptoPP::HashVerificationFilter *a1, int a2)
{
  CryptoPP::HashVerificationFilter *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::HashVerificationFilter::~HashVerificationFilter(a1);
  if ( v4 & 1 )
    operator delete(v3);
  return v3;
}
