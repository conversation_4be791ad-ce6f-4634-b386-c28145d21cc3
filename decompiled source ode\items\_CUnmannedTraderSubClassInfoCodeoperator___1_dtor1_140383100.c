/*
 * Function: _CUnmannedTraderSubClassInfoCode::operator__::_1_::dtor$1
 * Address: 0x140383100
 */

void __fastcall CUnmannedTraderSubClassInfoCode::operator__::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(*(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > **)(a2 + 88));
}
