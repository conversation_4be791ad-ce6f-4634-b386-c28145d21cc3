/*
 * Function: ?CreateTblLtd_ItemInfo@CRFDBItemLog@@QEAA_NH@Z
 * Address: 0x140485630
 */

bool __fastcall CRFDBItemLog::CreateTblLtd_ItemInfo(CRFDBItemLog *this, int nKorTime)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-468h]@1
  int v6; // [sp+20h] [bp-448h]@4
  int v7; // [sp+28h] [bp-440h]@4
  char Dst; // [sp+40h] [bp-428h]@4
  char v9; // [sp+41h] [bp-427h]@4
  unsigned __int64 v10; // [sp+450h] [bp-18h]@4
  CRFDBItemLog *v11; // [sp+470h] [bp+8h]@1
  int v12; // [sp+478h] [bp+10h]@1

  v12 = nKorTime;
  v11 = this;
  v2 = &v5;
  for ( i = 280i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  Dst = 0;
  memset(&v9, 0, 0x3FFui64);
  memset_0(&Dst, 0, 0x400ui64);
  v7 = v12;
  v6 = v12;
  sprintf(
    &Dst,
    " CREATE TABLE [dbo].[tbl_ltd_iteminfo_%d] ( [LogSerial] [datetime] NOT NULL, [SubType] [tinyint] NOT NULL, [ItemC] ["
    "varchar] (10) NOT NULL, [ItemU] [varchar] (10) NOT NULL, [ItemD] [int] NOT NULL, [ItemO] [tinyint] NULL, [Name] [var"
    "char] (64) NULL  ) ON [PRIMARY] CREATE INDEX [IX_tbl_ltd_iteminfo_LogSerial] ON [dbo].[tbl_ltd_iteminfo_%d]([LogSeri"
    "al]) ON [PRIMARY] CREATE INDEX [IX_tbl_ltd_iteminfo_SubType] ON [dbo].[tbl_ltd_iteminfo_%d]([SubType]) ON [PRIMARY]",
    (unsigned int)v12,
    (unsigned int)v12);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dst, 1) != 0;
}
