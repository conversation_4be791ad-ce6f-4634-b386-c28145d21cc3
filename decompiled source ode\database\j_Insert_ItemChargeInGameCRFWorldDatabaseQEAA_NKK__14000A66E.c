/*
 * Function: j_?Insert_ItemChargeInGame@CRFWorldDatabase@@QEAA_NKK_KKE@Z
 * Address: 0x14000A66E
 */

bool __fastcall CRFWorldDatabase::Insert_ItemChargeInGame(CRFWorldDatabase *this, unsigned int dwAvatorSerial, unsigned int dwItemCode_K, unsigned __int64 dwItemCode_D, unsigned int dwItemCode_U, char byType)
{
  return CRFWorldDatabase::Insert_ItemChargeInGame(
           this,
           dwAvatorSerial,
           dwItemCode_K,
           dwItemCode_D,
           dwItemCode_U,
           byType);
}
