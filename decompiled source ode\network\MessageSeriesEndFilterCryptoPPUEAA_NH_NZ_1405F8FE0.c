/*
 * Function: ?MessageSeriesEnd@Filter@CryptoPP@@UEAA_NH_N@Z
 * Address: 0x1405F8FE0
 */

char __fastcall CryptoPP::Filter::MessageSeriesEnd(__int64 a1, __int64 a2, unsigned __int8 a3)
{
  int v4; // [sp+30h] [bp-18h]@1
  __int64 v5; // [sp+50h] [bp+8h]@1

  v5 = a1;
  v4 = *(_DWORD *)(a1 + 40);
  if ( v4 )
  {
    if ( v4 != 1 )
      return 0;
  }
  else if ( (unsigned __int8)(*(int (__fastcall **)(__int64, _QWORD))(*(_QWORD *)a1 + 72i64))(a1, a3) )
  {
    return 1;
  }
  if ( (unsigned __int8)(*(int (__fastcall **)(__int64))(*(_QWORD *)v5 + 368i64))(v5)
    && (unsigned __int8)CryptoPP::Filter::OutputMessageSeriesEnd(v5) )
  {
    return 1;
  }
  return 0;
}
