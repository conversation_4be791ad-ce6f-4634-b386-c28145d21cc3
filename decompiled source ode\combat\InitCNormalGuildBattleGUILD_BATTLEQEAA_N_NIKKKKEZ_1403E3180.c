/*
 * Function: ?Init@CNormalGuildBattle@GUILD_BATTLE@@QEAA_N_NIKKKKE@Z
 * Address: 0x1403E3180
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattle::Init(GUILD_BATTLE::CNormalGuildBattle *this, bool bToday, unsigned int uiDayID, unsigned int dwID, unsigned int dwP1GuildSerial, unsigned int dwP2GuildSerial, unsigned int dwMapID, char byNumber)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v10; // rax@5
  char v11; // al@5
  GUILD_BATTLE::CGuildBattleLogger *v12; // rax@8
  ATL::CTimeSpan *v13; // rax@22
  GUILD_BATTLE::CGuildBattleLogger *v14; // rax@25
  GUILD_BATTLE::CGuildBattleLogger *v15; // rax@29
  __int64 v16; // [sp+0h] [bp-108h]@1
  char v17[4]; // [sp+20h] [bp-E8h]@5
  GUILD_BATTLE::CNormalGuildBattleStateList *v18; // [sp+28h] [bp-E0h]@5
  unsigned int v19; // [sp+30h] [bp-D8h]@8
  int v20; // [sp+38h] [bp-D0h]@25
  const char *v21; // [sp+40h] [bp-C8h]@25
  char *v22; // [sp+48h] [bp-C0h]@25
  char *v23; // [sp+50h] [bp-B8h]@25
  unsigned int v24; // [sp+58h] [bp-B0h]@25
  int v25; // [sp+60h] [bp-A8h]@25
  GUILD_BATTLE::CNormalGuildBattleFieldList *v26; // [sp+70h] [bp-98h]@9
  GUILD_BATTLE::CNormalGuildBattleStateListPool *v27; // [sp+78h] [bp-90h]@9
  GUILD_BATTLE::CGuildBattleScheduleManager *v28; // [sp+80h] [bp-88h]@9
  CGuild *pk1P; // [sp+88h] [bp-80h]@9
  CGuild *pk2P; // [sp+90h] [bp-78h]@9
  GUILD_BATTLE::CNormalGuildBattleField *pkField; // [sp+98h] [bp-70h]@9
  GUILD_BATTLE::CGuildBattleStateList *pkStateList; // [sp+A0h] [bp-68h]@9
  GUILD_BATTLE::CGuildBattleSchedule *v33; // [sp+A8h] [bp-60h]@9
  ATL::CTimeSpan result; // [sp+B0h] [bp-58h]@22
  unsigned int v35; // [sp+B8h] [bp-50h]@8
  const char *v36; // [sp+C0h] [bp-48h]@23
  int v37; // [sp+C8h] [bp-40h]@25
  unsigned int v38; // [sp+CCh] [bp-3Ch]@25
  char *v39; // [sp+D0h] [bp-38h]@25
  char *v40; // [sp+D8h] [bp-30h]@25
  int v41; // [sp+E0h] [bp-28h]@25
  const char *v42; // [sp+E8h] [bp-20h]@27
  int v43; // [sp+F0h] [bp-18h]@29
  GUILD_BATTLE::CNormalGuildBattle *v44; // [sp+110h] [bp+8h]@1
  bool v45; // [sp+118h] [bp+10h]@1
  unsigned int uiDayIDa; // [sp+120h] [bp+18h]@1
  unsigned int v47; // [sp+128h] [bp+20h]@1

  v47 = dwID;
  uiDayIDa = uiDayID;
  v45 = bToday;
  v44 = this;
  v8 = &v16;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  if ( v44->m_dwID == dwID )
  {
    if ( byNumber && (signed int)(unsigned __int8)byNumber <= 50 )
    {
      v26 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
      v27 = GUILD_BATTLE::CNormalGuildBattleStateListPool::Instance();
      v28 = GUILD_BATTLE::CGuildBattleScheduleManager::Instance();
      pk1P = GetGuildDataFromSerial(g_Guild, 500, dwP1GuildSerial);
      pk2P = GetGuildDataFromSerial(g_Guild, 500, dwP2GuildSerial);
      pkField = 0i64;
      pkStateList = 0i64;
      v33 = 0i64;
      if ( pk1P )
        pkField = GUILD_BATTLE::CNormalGuildBattleFieldList::GetField(v26, dwMapID);
      if ( pkField )
      {
        pkStateList = (GUILD_BATTLE::CGuildBattleStateList *)GUILD_BATTLE::CNormalGuildBattleStateListPool::Get(
                                                               v27,
                                                               v44->m_dwID);
        v33 = GUILD_BATTLE::CGuildBattleScheduleManager::UpdateUseFlag(v28, uiDayIDa, dwMapID, v44->m_dwID);
        if ( pkStateList )
        {
          if ( v33 )
            GUILD_BATTLE::CGuildBattleSchedule::SetStateList(v33, pkStateList);
        }
      }
      if ( v33 && pkField && pk1P && pk2P && pkStateList )
      {
        GUILD_BATTLE::CNormalGuildBattle::Init(
          v44,
          pk1P,
          pk2P,
          pkField,
          byNumber,
          (GUILD_BATTLE::CNormalGuildBattleStateList *)pkStateList);
        if ( !v45 )
        {
          CGuild::SetCopmlteGuildBattleSuggest(pk1P);
          CGuild::SetCopmlteGuildBattleSuggest(pk2P);
        }
        GUILD_BATTLE::CGuildBattleStateList::SetReady(pkStateList);
        v13 = GUILD_BATTLE::CGuildBattleSchedule::GetBattleTime(v33, &result);
        GUILD_BATTLE::CNormalGuildBattleStateList::SetBattleTime(
          (GUILD_BATTLE::CNormalGuildBattleStateList *)pkStateList,
          (ATL::CTimeSpan)v13->m_timeSpan);
        if ( v45 )
          v36 = "today";
        else
          v36 = "tommorow";
        v37 = (unsigned __int8)byNumber;
        v38 = GUILD_BATTLE::CNormalGuildBattleField::GetMapID(v44->m_pkField);
        v39 = pk2P->m_aszName;
        v40 = pk1P->m_aszName;
        v41 = (unsigned __int8)byNumber;
        v14 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        v25 = v37;
        v24 = v38;
        v23 = v39;
        v22 = v40;
        v21 = v36;
        v20 = v41;
        v19 = dwMapID;
        LODWORD(v18) = dwP2GuildSerial;
        *(_DWORD *)v17 = dwP1GuildSerial;
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v14,
          "CNormalGuildBattle::Init( %u, %u, %u, %u, %u, %u ) : %s : Init( %s, %s, %d, %u, pkStateList )",
          uiDayIDa,
          v47);
        v11 = 1;
      }
      else
      {
        if ( v45 )
          v42 = "today";
        else
          v42 = "tommorow";
        v43 = (unsigned __int8)byNumber;
        v15 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        v21 = v42;
        v20 = v43;
        v19 = dwMapID;
        LODWORD(v18) = dwP2GuildSerial;
        *(_DWORD *)v17 = dwP1GuildSerial;
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v15,
          "CNormalGuildBattle::Init( %u, %u, %u, %u, %u, %u ) : %s : Init( ... ) Skip!",
          uiDayIDa,
          v47);
        v11 = 0;
      }
    }
    else
    {
      v35 = (unsigned __int8)byNumber;
      v12 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v19 = v35;
      LODWORD(v18) = dwMapID;
      *(_DWORD *)v17 = dwP2GuildSerial;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v12,
        "CNormalGuildBattle::Init(%u,%u,%u,%u)byNumber(%u) Invalid!",
        v47,
        dwP1GuildSerial);
      v11 = 0;
    }
  }
  else
  {
    v10 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    LODWORD(v18) = dwMapID;
    *(_DWORD *)v17 = dwP2GuildSerial;
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v10,
      "CNormalGuildBattle::Init(%u,%u,%u,%u)GetID() != dwID Fail!",
      v47,
      dwP1GuildSerial);
    v11 = 0;
  }
  return v11;
}
