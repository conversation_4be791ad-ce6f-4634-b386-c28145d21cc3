/*
 * Function: _std::_Copy_opt_std::_Vector_const_iterator_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64____CUnmannedTraderSubClassInfo_____ptr64_____ptr64_std::random_access_iterator_tag__::_1_::dtor$0
 * Address: 0x140382770
 */

void __fastcall std::_Copy_opt_std::_Vector_const_iterator_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64____CUnmannedTraderSubClassInfo_____ptr64_____ptr64_std::random_access_iterator_tag__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(*(std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > **)(a2 + 72));
}
