/*
 * Function: ?NotifyIncome@CUnmannedTraderTradeInfo@@QEAAXEG@Z
 * Address: 0x140392110
 */

void __fastcall CUnmannedTraderTradeInfo::NotifyIncome(CUnmannedTraderTradeInfo *this, char byRace, unsigned __int16 wIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CandidateMgr *v5; // rax@4
  CNationSettingManager *v6; // rax@6
  __int64 v7; // [sp+0h] [bp-B8h]@1
  char szMsg[8]; // [sp+38h] [bp-80h]@4
  char Dst; // [sp+40h] [bp-78h]@4
  char pbyType; // [sp+74h] [bp-44h]@4
  char v11; // [sp+75h] [bp-43h]@4
  _candidate_info *v12; // [sp+88h] [bp-30h]@4
  char *Src; // [sp+98h] [bp-20h]@5
  unsigned __int64 v14; // [sp+A0h] [bp-18h]@4
  CUnmannedTraderTradeInfo *v15; // [sp+C0h] [bp+8h]@1
  char v16; // [sp+C8h] [bp+10h]@1
  unsigned __int16 v17; // [sp+D0h] [bp+18h]@1

  v17 = wIndex;
  v16 = byRace;
  v15 = this;
  v3 = &v7;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = (unsigned __int64)&v7 ^ _security_cookie;
  *(_QWORD *)szMsg = v15->m_ui64TotalOldIncome;
  memset(&Dst, 0, 0x11ui64);
  pbyType = 13;
  v11 = 102;
  v5 = CandidateMgr::Instance();
  v12 = CandidateMgr::GetPatriarchGroup(v5, v16, 0);
  if ( v12 )
  {
    Src = v12->wszName;
  }
  else
  {
    v6 = CTSingleton<CNationSettingManager>::Instance();
    Src = CNationSettingManager::GetNoneString(v6);
  }
  strcpy_s(&Dst, 0x11ui64, Src);
  CNetProcess::LoadSendMsg(unk_1414F2088, v17, &pbyType, szMsg, 0x19u);
}
