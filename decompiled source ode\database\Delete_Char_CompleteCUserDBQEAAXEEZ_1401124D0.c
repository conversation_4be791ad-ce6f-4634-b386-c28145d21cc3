/*
 * Function: ?Delete_Char_Complete@CUserDB@@QEAAXEE@Z
 * Address: 0x1401124D0
 */

void __fastcall CUserDB::Delete_Char_Complete(CUserDB *this, char byRetCode, char bySlotIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@6
  __int64 v6; // [sp+0h] [bp-78h]@1
  _del_char_result_zone v7; // [sp+34h] [bp-44h]@6
  char pbyType; // [sp+54h] [bp-24h]@6
  char v9; // [sp+55h] [bp-23h]@6
  CUserDB *v10; // [sp+80h] [bp+8h]@1
  char v11; // [sp+88h] [bp+10h]@1
  char v12; // [sp+90h] [bp+18h]@1

  v12 = bySlotIndex;
  v11 = byRetCode;
  v10 = this;
  v3 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10->m_bDBWaitState = 0;
  v10->m_dwOperLobbyTime = timeGetTime();
  if ( !v11 )
    v10->m_RegedList[(unsigned __int8)v12].m_bySlotIndex = -1;
  CMgrAccountLobbyHistory::del_char_complete(&CUserDB::s_MgrLobbyHistory, v11, v10->m_szLobbyHistoryFileName);
  v7.byRetCode = v11;
  v7.bySlotIndex = v12;
  pbyType = 1;
  v9 = 13;
  v5 = _del_char_result_zone::size(&v7);
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_idWorld.wIndex, &pbyType, &v7.byRetCode, v5);
}
