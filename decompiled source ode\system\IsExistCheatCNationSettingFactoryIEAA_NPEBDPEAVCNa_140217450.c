/*
 * Function: ?IsExistCheat@CNationSettingFactory@@IEAA_NPEBDPEAVCNationSettingData@@@Z
 * Address: 0x140217450
 */

char __fastcall CNationSettingFactory::IsExistCheat(CNationSettingFactory *this, const char *szCheat, CNationSettingData *pkData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-118h]@1
  size_t MaxCount; // [sp+20h] [bp-F8h]@4
  size_t v8; // [sp+28h] [bp-F0h]@4
  std::vector<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *v9; // [sp+30h] [bp-E8h]@4
  std::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > _Right; // [sp+48h] [bp-D0h]@4
  std::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > v11; // [sp+78h] [bp-A0h]@4
  CHEAT_COMMAND *v12; // [sp+98h] [bp-80h]@6
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > result; // [sp+A0h] [bp-78h]@4
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > v14; // [sp+B8h] [bp-60h]@4
  char v15; // [sp+D0h] [bp-48h]@10
  __int64 v16; // [sp+D8h] [bp-40h]@4
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *v17; // [sp+E0h] [bp-38h]@4
  std::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *__that; // [sp+E8h] [bp-30h]@4
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *v19; // [sp+F0h] [bp-28h]@4
  std::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *v20; // [sp+F8h] [bp-20h]@4
  size_t v21; // [sp+100h] [bp-18h]@7
  char *Str; // [sp+128h] [bp+10h]@1
  CNationSettingData *v23; // [sp+130h] [bp+18h]@1

  v23 = pkData;
  Str = (char *)szCheat;
  v3 = &v6;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v16 = -2i64;
  MaxCount = 0i64;
  v8 = strlen_0(szCheat);
  v9 = &v23->m_vecCheatData;
  v17 = std::vector<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::end(&v23->m_vecCheatData, &result);
  __that = (std::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *)v17;
  std::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>(
    &_Right,
    (std::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *)&v17->_Mycont);
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::~_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>(&result);
  v19 = std::vector<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::begin(v9, &v14);
  v20 = (std::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *)v19;
  std::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>(
    &v11,
    (std::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *)&v19->_Mycont);
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::~_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>(&v14);
  while ( std::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::operator!=(&v11, &_Right) )
  {
    v12 = std::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::operator*(&v11);
    if ( v12->uiCmdLen <= v8 )
      v21 = v8;
    else
      v21 = v12->uiCmdLen;
    MaxCount = v21;
    if ( !_strnicmp(v12->pwszCommand, Str, v21) )
    {
      v15 = 1;
      std::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::~_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>(&v11);
      std::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::~_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>(&_Right);
      return v15;
    }
    std::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::operator++(&v11);
  }
  std::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::~_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>(&v11);
  std::_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::~_Vector_const_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>(&_Right);
  return 0;
}
