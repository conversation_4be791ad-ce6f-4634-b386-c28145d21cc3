/*
 * Function: ?CheckSendNewMissionMsg@CDarkHoleChannel@@QEAAXXZ
 * Address: 0x140267640
 */

void __fastcall CDarkHoleChannel::CheckSendNewMissionMsg(CDarkHoleChannel *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  DWORD v4; // [sp+20h] [bp-18h]@4
  CDarkHoleChannel *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = timeGetTime();
  if ( v4 > v5->m_dwSendNewMissionMsgNextTime )
  {
    v5->m_dwSendNewMissionMsgNextTime = -1;
    CDarkHoleChannel::SendMsg_NewMission(v5);
  }
}
