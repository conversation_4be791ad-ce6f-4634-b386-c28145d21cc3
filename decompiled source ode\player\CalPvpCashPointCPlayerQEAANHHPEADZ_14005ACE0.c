/*
 * Function: ?CalPvpCashPoint@CPlayer@@QEAANHHPEAD@Z
 * Address: 0x14005ACE0
 */

void __fastcall CPlayer::CalPvpCashPoint(CPlayer *this, int nDstLv, int nSrcLv, char *pSrcClass)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  CPlayer *v7; // [sp+50h] [bp+8h]@1

  v7 = this;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  CPvpCashPoint::CalPvpCashPoint(&v7->m_kPvpCashPoint, nDstLv, nSrcLv, pSrcClass, 1);
}
