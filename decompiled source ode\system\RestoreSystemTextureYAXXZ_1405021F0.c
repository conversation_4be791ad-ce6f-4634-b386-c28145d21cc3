/*
 * Function: ?RestoreSystemTexture@@YAXXZ
 * Address: 0x1405021F0
 */

void RestoreSystemTexture(void)
{
  __int64 v0; // rcx@1
  FILE *v1; // rax@4

  v0 = qword_184A79C20;
  if ( qword_184A79C20 )
  {
    (*(void (**)(void))(*(_QWORD *)qword_184A79C20 + 16i64))();
    v0 = qword_184A79C20;
  }
  if ( qword_184A79C18 )
  {
    v1 = R3LoadDDS(".\\system\\logo.dds", 2u, 0x800u, 0x800u);
    v0 = qword_184A79C20;
    qword_184A79C18 = v1;
  }
  if ( v0 )
    qword_184A79C20 = (__int64)R3LoadDDS(".\\system\\dlight.dds", 2u, 0x800u, 0x800u);
}
