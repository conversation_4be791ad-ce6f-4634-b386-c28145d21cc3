/*
 * Function: j_??$_Uninit_copy@PEAVCUnmannedTraderUserInfo@@PEAV1@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@YAPEAVCUnmannedTraderUserInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderUserInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140006D3E
 */

CUnmannedTraderUserInfo *__fastcall std::_Uninit_copy<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo *,std::allocator<CUnmannedTraderUserInfo>>(CUnmannedTraderUserInfo *_First, CUnmannedTraderUserInfo *_Last, CUnmannedTraderUserInfo *_Dest, std::allocator<CUnmannedTraderUserInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo *,std::allocator<CUnmannedTraderUserInfo>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
