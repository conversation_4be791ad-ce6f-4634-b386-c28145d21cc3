/*
 * Function: j_?NetClose@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAA_N_NKAEAVCNormalGuildBattleLogger@2@@Z
 * Address: 0x140002FE5
 */

bool __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::NetClose(GUILD_BATTLE::CNormalGuildBattleGuild *this, bool bInGuildBattle, unsigned int dwSerial, GUILD_BATTLE::CNormalGuildBattleLogger *kLogger)
{
  return GUILD_BATTLE::CNormalGuildBattleGuild::NetClose(this, bInGuildBattle, dwSerial, kLogger);
}
