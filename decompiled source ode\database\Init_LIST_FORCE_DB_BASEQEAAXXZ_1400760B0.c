/*
 * Function: ?Init@_LIST@_FORCE_DB_BASE@@QEAAXXZ
 * Address: 0x1400760B0
 */

void __fastcall _FORCE_DB_BASE::_LIST::Init(_FORCE_DB_BASE::_LIST *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _FORCE_DB_BASE::_LIST *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _FORCEKEY::SetRelease(&v4->Key);
  v4->dwItemETSerial = 0;
  v4->lnUID = 0i64;
  v4->dwT = -1;
  v4->byCsMethod = 0;
  v4->m_dwLendRegdTime = -1;
}
