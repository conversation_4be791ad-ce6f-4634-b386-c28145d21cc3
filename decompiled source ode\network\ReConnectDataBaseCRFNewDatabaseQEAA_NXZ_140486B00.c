/*
 * Function: ?ReConnectDataBase@CRFNewDatabase@@QEAA_NXZ
 * Address: 0x140486B00
 */

char __fastcall CRFNewDatabase::ReConnectDataBase(CRFNewDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@4
  CRFNewDatabase *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CRFNewDatabase::Log(v6, " ");
  CRFNewDatabase::Log(v6, "ReConnectDataBase : Start");
  CRFNewDatabase::ErrLog(v6, " ");
  CRFNewDatabase::ErrLog(v6, "ReConnectDataBase : Start");
  CRFNewDatabase::EndDataBase(v6);
  for ( j = 0; (signed int)j < 5; ++j )
  {
    if ( CRFNewDatabase::StartDataBase(v6, v6->m_szOdbcName, v6->m_szAccountName, v6->m_szPassword) )
    {
      CRFNewDatabase::FmtLog(v6, "ReConnectDataBase Count : %d Success.", j);
      CRFNewDatabase::ErrFmtLog(v6, "ReConnectDataBase Count : %d Success.", j);
      return 1;
    }
    CRFNewDatabase::FmtLog(v6, "ReConnectDataBase Fail. Count : %d", j);
    CRFNewDatabase::ErrFmtLog(v6, "ReConnectDataBase Fail. Count : %d", j);
    Sleep(0x3E8u);
  }
  CRFNewDatabase::Log(v6, "ReConnectDataBase : Fail End");
  CRFNewDatabase::ErrLog(v6, "ReConnectDataBase : Fail End");
  return 0;
}
