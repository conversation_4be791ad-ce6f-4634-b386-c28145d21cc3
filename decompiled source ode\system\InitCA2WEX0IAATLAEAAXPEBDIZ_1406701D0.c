/*
 * Function: ?Init@?$CA2WEX@$0IA@@ATL@@AEAAXPEBDI@Z
 * Address: 0x1406701D0
 */

void __fastcall ATL::CA2WEX<128>::Init(ATL::CA2WEX<128> *this, const char *psz, unsigned int nCodePage)
{
  int v3; // eax@3
  int nLength; // ST38_4@3
  int v5; // ST38_4@5
  int v6; // [sp+30h] [bp-28h]@3
  int cbMultiByte; // [sp+34h] [bp-24h]@3
  wchar_t **ppBuff; // [sp+60h] [bp+8h]@1
  LPCSTR lpString; // [sp+68h] [bp+10h]@1
  unsigned int CodePage; // [sp+70h] [bp+18h]@1

  CodePage = nCodePage;
  lpString = psz;
  ppBuff = (wchar_t **)this;
  if ( psz )
  {
    v3 = lstrlenA(psz);
    cbMultiByte = v3 + 1;
    nLength = v3 + 1;
    ATL::AtlConvAllocMemory<wchar_t>(ppBuff, v3 + 1, (wchar_t *)ppBuff + 4, 128);
    v6 = MultiByteToWideChar(CodePage, 0, lpString, cbMultiByte, *ppBuff, nLength) == 0;
    if ( v6 && GetLastError() == 122 )
    {
      v5 = MultiByteToWideChar(CodePage, 0, lpString, cbMultiByte, 0i64, 0);
      ATL::AtlConvAllocMemory<wchar_t>(ppBuff, v5, (wchar_t *)ppBuff + 4, 128);
      v6 = MultiByteToWideChar(CodePage, 0, lpString, cbMultiByte, *ppBuff, v5) == 0;
    }
    if ( v6 )
      ATL::AtlThrowLastWin32();
  }
  else
  {
    *(_QWORD *)this = 0i64;
  }
}
