/*
 * Function: j_?_Assign_n@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@IEAAX_KAEBVCMoveMapLimitRightInfo@@@Z
 * Address: 0x14000D90E
 */

void __fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Assign_n(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, unsigned __int64 _Count, CMoveMapLimitRightInfo *_Val)
{
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Assign_n(this, _Count, _Val);
}
