/*
 * Function: ?InsertCharacData@CCheckSumCharacAccountTrunkData@@IEAA_NPEAVCRFWorldDatabase@@@Z
 * Address: 0x1402C0CC0
 */

bool __fastcall CCheckSumCharacAccountTrunkData::InsertCharacData(CCheckSumCharacAccountTrunkData *this, CRFWorldDatabase *pkDB)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CCheckSumCharacAccountTrunkData *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pkDB )
    result = CRFWorldDatabase::Insert_NpcData(pkDB, v6->m_dwSerial, v6->m_dwValues);
  else
    result = 0;
  return result;
}
