/*
 * Function: ??$_Uninit_fill_n@PEAV?$vector@VPolynomialMod2@CryptoPP@@V?$allocator@VPolynomialMod2@CryptoPP@@@std@@@std@@_KV12@V?$allocator@V?$vector@VPolynomialMod2@CryptoPP@@V?$allocator@VPolynomialMod2@CryptoPP@@@std@@@std@@@2@@std@@YAXPEAV?$vector@VPolynomialMod2@CryptoPP@@V?$allocator@VPolynomialMod2@CryptoPP@@@std@@@0@_KAEBV10@AEAV?$allocator@V?$vector@VPolynomialMod2@CryptoPP@@V?$allocator@VPolynomialMod2@CryptoPP@@@std@@@std@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405A4590
 */

signed __int64 __fastcall std::_Uninit_fill_n<std::vector<CryptoPP::PolynomialMod2,std::allocator<CryptoPP::PolynomialMod2>> *,unsigned __int64,std::vector<CryptoPP::PolynomialMod2,std::allocator<CryptoPP::PolynomialMod2>>,std::allocator<std::vector<CryptoPP::PolynomialMod2,std::allocator<CryptoPP::PolynomialMod2>>>>(signed __int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  signed __int64 result; // rax@1
  signed __int64 v5; // [sp+40h] [bp+8h]@1
  __int64 v6; // [sp+48h] [bp+10h]@1
  __int64 v7; // [sp+50h] [bp+18h]@1
  __int64 v8; // [sp+58h] [bp+20h]@1

  v8 = a4;
  v7 = a3;
  v6 = a2;
  v5 = a1;
  result = a1;
  while ( v6 )
  {
    std::allocator<std::vector<CryptoPP::PolynomialMod2,std::allocator<CryptoPP::PolynomialMod2>>>::construct(
      v8,
      v5,
      v7);
    --v6;
    result = v5 + 40;
    v5 += 40i64;
  }
  return result;
}
