/*
 * Function: ?GetItemGrade@@YAEHH@Z
 * Address: 0x14003E970
 */

char __fastcall GetItemGrade(int nTableCode, int nItemIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  CRecordData *v6; // [sp+20h] [bp-28h]@4
  _base_fld *v7; // [sp+28h] [bp-20h]@8
  _base_fld *v8; // [sp+30h] [bp-18h]@11
  int v9; // [sp+38h] [bp-10h]@4
  int v10; // [sp+50h] [bp+8h]@1

  v10 = nTableCode;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = &s_ptblItemData[v10];
  v9 = v10;
  if ( v10 >= 0 )
  {
    if ( v9 <= 5 )
      goto LABEL_8;
    if ( v9 == 6 )
    {
      v8 = CRecordData::GetRecord(v6, nItemIndex);
      if ( v8 )
        return v8[3].m_strCode[0];
    }
    else if ( v9 == 7 )
    {
LABEL_8:
      v7 = CRecordData::GetRecord(v6, nItemIndex);
      if ( v7 )
        return v7[3].m_strCode[0];
      return 0;
    }
  }
  return 0;
}
