/*
 * Function: ?Select_PlayerTimeLimitInfo@CRFWorldDatabase@@QEAAHKPEAKPEAE@Z
 * Address: 0x1404C7F10
 */

signed __int64 __fastcall CRFWorldDatabase::Select_PlayerTimeLimitInfo(CRFWorldDatabase *this, unsigned int dwAccountSerial, unsigned int *pdwFatigue, char *pbyStatus)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v7; // [sp+0h] [bp-488h]@1
  void *SQLStmt; // [sp+20h] [bp-468h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-460h]@22
  char DstBuf; // [sp+40h] [bp-448h]@4
  SQLLEN v11; // [sp+458h] [bp-30h]@22
  __int16 v12; // [sp+464h] [bp-24h]@9
  unsigned __int8 v13; // [sp+468h] [bp-20h]@16
  unsigned __int64 v14; // [sp+478h] [bp-10h]@4
  CRFWorldDatabase *v15; // [sp+490h] [bp+8h]@1
  unsigned int *TargetValue; // [sp+4A0h] [bp+18h]@1
  char *v17; // [sp+4A8h] [bp+20h]@1

  v17 = pbyStatus;
  TargetValue = pdwFatigue;
  v15 = this;
  v4 = &v7;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v14 = (unsigned __int64)&v7 ^ _security_cookie;
  sprintf_s(
    &DstBuf,
    0x400ui64,
    "Select Fatigue, TLStatus From [dbo].[tbl_TimeLimitInfo] Where AccountSerial = %d",
    dwAccountSerial);
  if ( v15->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v15->vfptr, &DstBuf);
  if ( v15->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v15->vfptr) )
  {
    v12 = SQLExecDirect_0(v15->m_hStmtSelect, &DstBuf, -3);
    if ( v12 && v12 != 1 )
    {
      if ( v12 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v15->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v12, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v12, v15->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v12 = SQLFetch_0(v15->m_hStmtSelect);
      if ( v12 && v12 != 1 )
      {
        v13 = 0;
        if ( v12 == 100 )
        {
          v13 = 2;
        }
        else
        {
          SQLStmt = v15->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v12, &DstBuf, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v12, v15->m_hStmtSelect);
          v13 = 1;
        }
        if ( v15->m_hStmtSelect )
          SQLCloseCursor_0(v15->m_hStmtSelect);
        result = v13;
      }
      else
      {
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v15->m_hStmtSelect, 1u, 4, TargetValue, 0i64, &v11);
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v15->m_hStmtSelect, 2u, -6, v17, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          if ( v12 != 100 )
          {
            SQLStmt = v15->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v12, &DstBuf, "SQLGetData", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v12, v15->m_hStmtSelect);
          }
          if ( v15->m_hStmtSelect )
            SQLCloseCursor_0(v15->m_hStmtSelect);
          result = 0i64;
        }
        else
        {
          if ( v15->m_hStmtSelect )
            SQLCloseCursor_0(v15->m_hStmtSelect);
          if ( v15->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v15->vfptr, "%s Success", &DstBuf);
          result = 0i64;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v15->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1i64;
  }
  return result;
}
