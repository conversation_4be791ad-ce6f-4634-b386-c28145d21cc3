/*
 * Function: ?sendmsg_taxrate@TRC_AutoTrade@@QEAAXHE@Z
 * Address: 0x1402D8320
 */

void __usercall TRC_AutoTrade::sendmsg_taxrate(TRC_AutoTrade *this@<rcx>, int n@<edx>, char byRet@<r8b>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v6; // ax@9
  __int64 v7; // [sp+0h] [bp-B8h]@1
  char Dst; // [sp+38h] [bp-80h]@4
  unsigned int v9; // [sp+39h] [bp-7Fh]@5
  unsigned int v10; // [sp+3Dh] [bp-7Bh]@5
  char v11; // [sp+41h] [bp-77h]@6
  CGuild *v12; // [sp+68h] [bp-50h]@4
  void *Src; // [sp+70h] [bp-48h]@5
  char pbyType; // [sp+84h] [bp-34h]@9
  char v15; // [sp+85h] [bp-33h]@9
  unsigned __int64 v16; // [sp+A0h] [bp-18h]@4
  TRC_AutoTrade *v17; // [sp+C0h] [bp+8h]@1
  int dwClientIndex; // [sp+C8h] [bp+10h]@1

  dwClientIndex = n;
  v17 = this;
  v4 = &v7;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v16 = (unsigned __int64)&v7 ^ _security_cookie;
  memset_0(&Dst, 0, 0x1Aui64);
  TRC_AutoTrade::get_taxrate(v17);
  Dst = (signed int)ffloor(a4 * 100.0);
  v12 = TRC_AutoTrade::getOwnerGuild(v17);
  if ( v12 )
  {
    v9 = v12->m_dwEmblemBack;
    v10 = v12->m_dwEmblemMark;
    Src = TRC_AutoTrade::get_guidlname(v17);
    if ( Src )
      memcpy_0(&v11, Src, 0x11ui64);
  }
  else
  {
    v9 = -1;
    v10 = -1;
  }
  pbyType = 30;
  v15 = 24;
  v6 = _atrade_taxrate_result_zocl::size((_atrade_taxrate_result_zocl *)&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &Dst, v6);
}
