/*
 * Function: ?Select_CharacterReName@CRFWorldDatabase@@QEAA_NPEADPEAK@Z
 * Address: 0x1404C4340
 */

char __fastcall CRFWorldDatabase::Select_CharacterReName(CRFWorldDatabase *this, char *pwszName, unsigned int *pSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@22
  SQLLEN v9; // [sp+38h] [bp-150h]@22
  __int16 v10; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  char v12; // [sp+164h] [bp-24h]@16
  unsigned __int64 v13; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v14; // [sp+190h] [bp+8h]@1
  unsigned int *TargetValue; // [sp+1A0h] [bp+18h]@1

  TargetValue = pSerial;
  v14 = this;
  v3 = &v6;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = (unsigned __int64)&v6 ^ _security_cookie;
  sprintf(&Dest, "{ CALL pSelect_CharacterSerial ( '%s' ) }", pwszName);
  if ( v14->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v14->vfptr, &Dest);
  if ( v14->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v14->vfptr) )
  {
    v10 = SQLExecDirectA_0(v14->m_hStmtSelect, &Dest, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 1;
      }
      else
      {
        SQLStmt = v14->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v10, &Dest, "_SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v10, v14->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      v10 = SQLFetch_0(v14->m_hStmtSelect);
      if ( v10 && v10 != 1 )
      {
        v12 = 1;
        if ( v10 == 100 )
        {
          v12 = 1;
        }
        else
        {
          SQLStmt = v14->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v10, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v10, v14->m_hStmtSelect);
          v12 = 0;
        }
        if ( v14->m_hStmtSelect )
          SQLCloseCursor_0(v14->m_hStmtSelect);
        result = v12;
      }
      else
      {
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v14->m_hStmtSelect, 1u, 4, TargetValue, 0i64, &v9);
        if ( v10 && v10 != 1 )
        {
          if ( v10 != 100 )
          {
            SQLStmt = v14->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v10, &Dest, "SQLGetData", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v10, v14->m_hStmtSelect);
          }
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          result = 0;
        }
        else
        {
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          if ( v14->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v14->vfptr, "%s Success", &Dest);
          result = 1;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v14->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
