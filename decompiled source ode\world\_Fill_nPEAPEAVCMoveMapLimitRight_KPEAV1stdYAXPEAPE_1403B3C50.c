/*
 * Function: ??$_Fill_n@PEAPEAVCMoveMapLimitRight@@_KPEAV1@@std@@YAXPEAPEAVCMoveMapLimitRight@@_KAEBQEAV1@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1403B3C50
 */

void __fastcall std::_Fill_n<CMoveMapLimitRight * *,unsigned __int64,CMoveMapLimitRight *>(CMoveMapLimitRight **_First, unsigned __int64 _Count, CMoveMapLimitRight *const *_Val, std::_Range_checked_iterator_tag __formal)
{
  memset64(_First, (unsigned __int64)*_Val, _Count);
}
