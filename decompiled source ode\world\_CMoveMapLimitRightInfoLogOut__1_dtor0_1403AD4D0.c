/*
 * Function: _CMoveMapLimitRightInfo::LogOut_::_1_::dtor$0
 * Address: 0x1403AD4D0
 */

void __fastcall CMoveMapLimitRightInfo::LogOut_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>((std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)(a2 + 144));
}
