/*
 * Function: ??4CUnmannedTraderRegistItemInfo@@QEAAAEBV0@AEBV0@@Z
 * Address: 0x1403528E0
 */

CUnmannedTraderRegistItemInfo *__fastcall CUnmannedTraderRegistItemInfo::operator=(CUnmannedTraderRegistItemInfo *this, CUnmannedTraderRegistItemInfo *rhs)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@4
  __int64 v6; // [sp+0h] [bp-28h]@1
  CUnmannedTraderRegistItemInfo *v7; // [sp+30h] [bp+8h]@1
  CUnmannedTraderRegistItemInfo *v8; // [sp+38h] [bp+10h]@1

  v8 = rhs;
  v7 = this;
  v2 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7->m_dwRegistSerial = rhs->m_dwRegistSerial;
  v7->m_wItemSerial = rhs->m_wItemSerial;
  v7->m_dwETSerialNumber = rhs->m_dwETSerialNumber;
  v7->m_dwPrice = rhs->m_dwPrice;
  v7->m_tStartTime = rhs->m_tStartTime;
  v7->m_bySellTurm = rhs->m_bySellTurm;
  v7->m_dwBuyerSerial = rhs->m_dwBuyerSerial;
  v7->m_dwTax = rhs->m_dwTax;
  v7->m_tResultTime = rhs->m_tResultTime;
  strcpy_s(v7->m_wszBuyerName, 0x11ui64, rhs->m_wszBuyerName);
  strcpy_s(v7->m_szBuyerAccount, 0xDui64, v8->m_szBuyerAccount);
  v7->m_byTableCode = v8->m_byTableCode;
  v7->m_wItemIndex = v8->m_wItemIndex;
  v7->m_byStorageIndex = v8->m_byStorageIndex;
  v7->m_dwD = v8->m_dwD;
  v7->m_dwU = v8->m_dwU;
  v4 = CUnmannedTraderItemState::GetState(&v8->m_kState);
  CUnmannedTraderItemState::Set(&v7->m_kState, v4);
  return v7;
}
