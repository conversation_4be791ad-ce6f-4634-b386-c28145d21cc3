/*
 * Function: ?WPActiveAttackSkill@CPlayerAttack@@QEAAXPEAU_attack_param@@@Z
 * Address: 0x1401700F0
 */

void __usercall CPlayerAttack::WPActiveAttackSkill(CPlayerAttack *this@<rcx>, _attack_param *pParam@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v5; // rax@11
  __int64 v6; // rax@18
  __int64 v7; // rdx@18
  float *v8; // rax@19
  __int64 v9; // rcx@19
  _attack_param *v10; // rax@20
  _attack_param *v11; // rcx@20
  __int64 v12; // rdx@20
  CCharacter **v13; // rcx@22
  _attack_param *v14; // rdx@22
  _attack_param *v15; // r8@22
  __int64 v16; // [sp+0h] [bp-78h]@1
  int nEffAttPower[2]; // [sp+20h] [bp-58h]@18
  int bUseEffBullet[2]; // [sp+28h] [bp-50h]@18
  int v19; // [sp+30h] [bp-48h]@20
  bool v20; // [sp+38h] [bp-40h]@20
  _base_fld *v21; // [sp+40h] [bp-38h]@4
  int v22; // [sp+48h] [bp-30h]@5
  int nAttPower; // [sp+4Ch] [bp-2Ch]@7
  char v24; // [sp+50h] [bp-28h]@11
  unsigned int dwSerial; // [sp+54h] [bp-24h]@11
  int v26; // [sp+58h] [bp-20h]@11
  char v27; // [sp+5Ch] [bp-1Ch]@11
  int v28; // [sp+60h] [bp-18h]@17
  int *v29; // [sp+68h] [bp-10h]@18
  CPlayerAttack *v30; // [sp+80h] [bp+8h]@1

  v30 = this;
  v3 = &v16;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v30->m_nDamagedObjNum = 0;
  v30->m_bIsCrtAtt = 0;
  v30->m_pp = pParam;
  v21 = v30->m_pp->pFld;
  CCharacter::BreakStealth(v30->m_pAttChar);
  if ( v30->m_pp->byEffectCode )
    v22 = *(_DWORD *)&v21[11].m_strCode[0];
  else
    v22 = *(_DWORD *)&v21[11].m_strCode[4 * (v30->m_pp->nLevel - 1)];
  nAttPower = v30->m_pp->nAddAttPnt + CPlayerAttack::_CalcSkillAttPnt(v30, 0);
  if ( v30->m_pp->nWpType == 7 )
  {
    _effect_parameter::GetEff_Rate(&v30->m_pAttChar->m_EP, 29);
    nAttPower = (signed int)ffloor((float)nAttPower * a3);
  }
  else
  {
    _effect_parameter::GetEff_Rate(&v30->m_pAttChar->m_EP, v30->m_pp->nClass + 2);
    nAttPower = (signed int)ffloor((float)nAttPower * a3);
  }
  if ( !v30->m_pAttPlayer->m_bInGuildBattle )
  {
    dwSerial = CPlayerDB::GetCharSerial(&v30->m_pAttPlayer->m_Param);
    v26 = CPlayerDB::GetRaceCode(&v30->m_pAttPlayer->m_Param);
    v5 = CPvpUserAndGuildRankingSystem::Instance();
    v24 = CPvpUserAndGuildRankingSystem::GetBossType(v5, v26, dwSerial);
    v27 = v24;
    if ( v24 )
    {
      if ( v27 == 2 || v27 == 6 )
        nAttPower = (signed int)ffloor((float)nAttPower * 1.2);
    }
    else
    {
      nAttPower = (signed int)ffloor((float)nAttPower * 1.3);
    }
  }
  v28 = v22;
  switch ( v22 )
  {
    case 5:
      v6 = *(_DWORD *)&v21[4].m_strCode[60];
      v7 = *(_DWORD *)&v21[4].m_strCode[60];
      v29 = s_nLimitDist;
      LOBYTE(bUseEffBullet[0]) = 0;
      nEffAttPower[0] = 0;
      CAttack::FlashDamageProc((CAttack *)&v30->m_pp, s_nLimitDist[v7], nAttPower, s_nLimitAngle[0][v6], 0, 0);
      goto LABEL_23;
    case 4:
    case 6:
      v8 = v30->m_pp->fArea;
      v9 = *(_DWORD *)&v21[4].m_strCode[60];
      LOBYTE(bUseEffBullet[0]) = 0;
      nEffAttPower[0] = 0;
      CAttack::AreaDamageProc((CAttack *)&v30->m_pp, s_nLimitRadius[v9], nAttPower, v8, 0, 0);
      goto LABEL_23;
    case 7:
      v10 = v30->m_pp;
      v11 = v30->m_pp;
      v12 = *(_DWORD *)&v21[4].m_strCode[60];
      v20 = 0;
      v19 = 0;
      bUseEffBullet[0] = v10->nExtentRange;
      nEffAttPower[0] = v11->nShotNum;
      CAttack::SectorDamageProc(
        (CAttack *)&v30->m_pp,
        *(_DWORD *)&v21[4].m_strCode[60],
        nAttPower,
        s_nLimitAngle[0][v12],
        nEffAttPower[0],
        bUseEffBullet[0],
        0,
        0);
      goto LABEL_23;
    case 0:
    case 1:
    case 2:
    case 3:
      if ( v30->m_pp->pDst )
      {
        v30->m_DamList[0].m_pChar = v30->m_pp->pDst;
        v13 = &v30->m_pp->pDst;
        v14 = v30->m_pp;
        v15 = v30->m_pp;
        LOBYTE(bUseEffBullet[0]) = v30->m_pp->bBackAttack;
        *(_QWORD *)nEffAttPower = *v13;
        v30->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                        v30->m_pAttChar,
                                        nAttPower,
                                        v15->nPart,
                                        v14->nTol,
                                        *(CCharacter **)nEffAttPower,
                                        bUseEffBullet[0]);
        v30->m_nDamagedObjNum = 1;
      }
LABEL_23:
      CAttack::CalcAvgDamage((CAttack *)&v30->m_pp);
      break;
    default:
      return;
  }
}
