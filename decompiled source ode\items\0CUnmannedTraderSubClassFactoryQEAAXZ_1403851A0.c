/*
 * Function: ??0CUnmannedTraderSubClassFactory@@QEAA@XZ
 * Address: 0x1403851A0
 */

void __fastcall CUnmannedTraderSubClassFactory::CUnmannedTraderSubClassFactory(CUnmannedTraderSubClassFactory *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderSubClassInfo *v3; // rax@5
  CUnmannedTraderSubClassInfo *v4; // rax@8
  CUnmannedTraderSubClassInfo *v5; // rax@11
  CUnmannedTraderSubClassInfo *v6; // rax@14
  __int64 v7; // [sp+0h] [bp-98h]@1
  CUnmannedTraderSubClassInfo *pkType; // [sp+20h] [bp-78h]@7
  CUnmannedTraderSubClassInfoDefault *v9; // [sp+28h] [bp-70h]@4
  CUnmannedTraderSubClassInfo *v10; // [sp+30h] [bp-68h]@10
  CUnmannedTraderSubClassInfoLevel *v11; // [sp+38h] [bp-60h]@7
  CUnmannedTraderSubClassInfo *v12; // [sp+40h] [bp-58h]@13
  CUnmannedTraderSubClassInfoCode *v13; // [sp+48h] [bp-50h]@10
  CUnmannedTraderSubClassInfo *v14; // [sp+50h] [bp-48h]@16
  CUnmannedTraderSubClassInfoForceLiverGrade *v15; // [sp+58h] [bp-40h]@13
  __int64 v16; // [sp+60h] [bp-38h]@4
  CUnmannedTraderSubClassInfo *v17; // [sp+68h] [bp-30h]@5
  CUnmannedTraderSubClassInfo *v18; // [sp+70h] [bp-28h]@8
  CUnmannedTraderSubClassInfo *v19; // [sp+78h] [bp-20h]@11
  CUnmannedTraderSubClassInfo *v20; // [sp+80h] [bp-18h]@14
  CUnmannedTraderSubClassFactory *v21; // [sp+A0h] [bp+8h]@1

  v21 = this;
  v1 = &v7;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v16 = -2i64;
  std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(&v21->m_vecTable);
  v9 = (CUnmannedTraderSubClassInfoDefault *)operator new(0x28ui64);
  if ( v9 )
  {
    CUnmannedTraderSubClassInfoDefault::CUnmannedTraderSubClassInfoDefault(v9, 0xFFFFFFFF);
    v17 = v3;
  }
  else
  {
    v17 = 0i64;
  }
  pkType = v17;
  CUnmannedTraderSubClassFactory::Regist(v21, v17);
  v11 = (CUnmannedTraderSubClassInfoLevel *)operator new(0x30ui64);
  if ( v11 )
  {
    CUnmannedTraderSubClassInfoLevel::CUnmannedTraderSubClassInfoLevel(v11, 0xFFFFFFFF);
    v18 = v4;
  }
  else
  {
    v18 = 0i64;
  }
  v10 = v18;
  CUnmannedTraderSubClassFactory::Regist(v21, v18);
  v13 = (CUnmannedTraderSubClassInfoCode *)operator new(0x50ui64);
  if ( v13 )
  {
    CUnmannedTraderSubClassInfoCode::CUnmannedTraderSubClassInfoCode(v13, 0xFFFFFFFF);
    v19 = v5;
  }
  else
  {
    v19 = 0i64;
  }
  v12 = v19;
  CUnmannedTraderSubClassFactory::Regist(v21, v19);
  v15 = (CUnmannedTraderSubClassInfoForceLiverGrade *)operator new(0x30ui64);
  if ( v15 )
  {
    CUnmannedTraderSubClassInfoForceLiverGrade::CUnmannedTraderSubClassInfoForceLiverGrade(v15, 0xFFFFFFFF);
    v20 = v6;
  }
  else
  {
    v20 = 0i64;
  }
  v14 = v20;
  CUnmannedTraderSubClassFactory::Regist(v21, v20);
}
