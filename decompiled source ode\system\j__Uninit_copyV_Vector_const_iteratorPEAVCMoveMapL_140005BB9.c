/*
 * Function: j_??$_Uninit_copy@V?$_Vector_const_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@PEAPEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@2@@std@@YAPEAPEAVCMoveMapLimitRight@@V?$_Vector_const_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@0@0PEAPEAV1@AEAV?$allocator@PEAVCMoveMapLimitRight@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140005BB9
 */

CMoveMapLimitRight **__fastcall std::_Uninit_copy<std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>,CMoveMapLimitRight * *,std::allocator<CMoveMapLimitRight *>>(std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_First, std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_Last, CMoveMapLimitRight **_Dest, std::allocator<CMoveMapLimitRight *> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>,CMoveMapLimitRight * *,std::allocator<CMoveMapLimitRight *>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
