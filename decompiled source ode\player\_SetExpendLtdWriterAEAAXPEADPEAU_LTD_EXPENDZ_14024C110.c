/*
 * Function: ?_SetExpend@LtdWriter@@AEAAXPEADPEAU_LTD_EXPEND@@@Z
 * Address: 0x14024C110
 */

void __fastcall LtdWriter::_SetExpend(LtdWriter *this, char *pszExpend, _LTD_EXPEND *pe)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1

  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  strcpy_0(pe->m_wszEtcInfo, pszExpend);
}
