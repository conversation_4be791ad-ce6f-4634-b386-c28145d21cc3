/*
 * Function: ?PushTask@CashDbWorker@@QEAA_NHPEAE_K@Z
 * Address: 0x1402EECA0
 */

bool __fastcall CashDbWorker::PushTask(CashDbWorker *this, int nTaskCode, char *p, unsigned __int64 size)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  CashDbWorker *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return TaskPool::PushWaitTsk(v8->_pkPool, nTaskCode, p, size) == 0;
}
