/*
 * Function: ??8_State@CLuaLooting_Novus_Item@@QEBA_NAEBU01@@Z
 * Address: 0x140406080
 */

bool __fastcall CLuaLooting_Novus_Item::_State::operator==(CLuaLooting_Novus_Item::_State *this, CLuaLooting_Novus_Item::_State *rhs)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // [sp+0h] [bp-18h]@1
  CLuaLooting_Novus_Item::_State *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  return v6->m_bExist == rhs->m_bExist;
}
