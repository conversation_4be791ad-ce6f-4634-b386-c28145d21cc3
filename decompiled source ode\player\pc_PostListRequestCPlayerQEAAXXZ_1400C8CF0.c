/*
 * Function: ?pc_PostListRequest@CPlayer@@QEAAXXZ
 * Address: 0x1400C8CF0
 */

void __usercall CPlayer::pc_PostListRequest(CPlayer *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@4
  __int64 v6; // [sp-20h] [bp-1018h]@1
  _qry_case_post_storage_list_get v7; // [sp+20h] [bp-FD8h]@4
  unsigned __int64 v8; // [sp+FE0h] [bp-18h]@4
  CPlayer *v9; // [sp+1000h] [bp+8h]@1

  v9 = this;
  v2 = alloca(a2);
  v3 = &v6;
  for ( i = 1028i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = (unsigned __int64)&v6 ^ _security_cookie;
  _qry_case_post_storage_list_get::_qry_case_post_storage_list_get(&v7);
  v7.dwMasterSerial = v9->m_pUserDB->m_dwSerial;
  v5 = _qry_case_post_storage_list_get::size(&v7);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 79, (char *)&v7, v5);
}
