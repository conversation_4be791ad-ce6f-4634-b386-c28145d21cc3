/*
 * Function: ?ParameterSupported@?$DL_CryptoSystemBase@VPK_Decryptor@CryptoPP@@V?$DL_PrivateKey@UECPPoint@CryptoPP@@@2@@CryptoPP@@UEBA_NPEBD@Z
 * Address: 0x140456650
 */

bool __fastcall CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::ParameterSupported(CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *this, const char *name)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int (__fastcall ***v4)(_QWORD, _QWORD); // rax@4
  int (__fastcall ***v5)(_QWORD, _QWORD); // rax@5
  __int64 v7; // [sp+0h] [bp-48h]@1
  int (__fastcall ***v8)(_QWORD, _QWORD); // [sp+20h] [bp-28h]@4
  int (__fastcall ***v9)(_QWORD, _QWORD); // [sp+28h] [bp-20h]@5
  int v10; // [sp+30h] [bp-18h]@6
  CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *v11; // [sp+50h] [bp+8h]@1
  const char *v12; // [sp+58h] [bp+10h]@1

  v12 = name;
  v11 = this;
  v2 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  LODWORD(v4) = ((int (__fastcall *)(CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *))v11->vfptr[1].ParameterSupported)(v11);
  v8 = v4;
  v10 = (unsigned __int8)(**v4)(v4, v12)
     || (LODWORD(v5) = ((int (__fastcall *)(CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *))v11->vfptr[1].FixedCiphertextLength)(v11),
         v9 = v5,
         (unsigned __int8)(**v5)(v5, v12));
  return v10;
}
