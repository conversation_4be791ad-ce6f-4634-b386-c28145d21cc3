/*
 * Function: ?SenseState@CPlayer@@QEAAXXZ
 * Address: 0x1400577F0
 */

void __fastcall CPlayer::SenseState(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned __int64 v4; // [sp+20h] [bp-18h]@4
  CPlayer *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = CPlayer::GetStateFlag(v5);
  CPlayer::SetStateFlag(v5);
  if ( v4 != v5->m_dwLastState )
    CPlayer::SendMsg_StateInform(v5, v5->m_dwLastState);
}
