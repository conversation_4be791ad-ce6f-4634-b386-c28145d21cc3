/*
 * Function: ?pc_ExchangeItem@CPlayer@@QEAAXGG@Z
 * Address: 0x1400B0690
 */

void __fastcall CPlayer::pc_ExchangeItem(CPlayer *this, unsigned __int16 wManualIndex, unsigned __int16 wItemSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CGoldenBoxItemMgr *v5; // rax@18
  CGoldenBoxItemMgr *v6; // rax@20
  char *v7; // rax@20
  CGoldenBoxItemMgr *v8; // rax@21
  char v9; // al@21
  CGoldenBoxItemMgr *v10; // rax@25
  CGoldenBoxItemMgr *v11; // rax@29
  int v12; // eax@39
  int v13; // eax@42
  int v14; // eax@56
  CGoldenBoxItemMgr *v15; // rax@87
  CGoldenBoxItemMgr *v16; // rax@91
  unsigned int v17; // eax@93
  CGoldenBoxItemMgr *v18; // rax@96
  CGoldenBoxItemMgr *v19; // rax@97
  __int64 v20; // [sp+0h] [bp-458h]@1
  bool bDelete[8]; // [sp+20h] [bp-438h]@64
  char *strErrorCodePos; // [sp+28h] [bp-430h]@64
  char v23; // [sp+30h] [bp-428h]@4
  int k; // [sp+34h] [bp-424h]@46
  _base_fld *v25; // [sp+38h] [bp-420h]@4
  _base_fld *v26; // [sp+40h] [bp-418h]@4
  char v27; // [sp+48h] [bp-410h]@4
  unsigned __int8 v28; // [sp+49h] [bp-40Fh]@4
  _base_fld *v29; // [sp+50h] [bp-408h]@4
  _STORAGE_LIST::_storage_con *pCon; // [sp+58h] [bp-400h]@4
  char v31; // [sp+60h] [bp-3F8h]@10
  unsigned __int16 v32; // [sp+64h] [bp-3F4h]@10
  int nCashType; // [sp+68h] [bp-3F0h]@10
  _base_fld *v34; // [sp+70h] [bp-3E8h]@13
  char v35; // [sp+78h] [bp-3E0h]@20
  char Dst; // [sp+90h] [bp-3C8h]@20
  char v37; // [sp+91h] [bp-3C7h]@20
  int j; // [sp+314h] [bp-144h]@20
  char *psItemCode; // [sp+318h] [bp-140h]@25
  int v40; // [sp+320h] [bp-138h]@26
  _base_fld *v41; // [sp+328h] [bp-130h]@37
  int v42; // [sp+330h] [bp-128h]@46
  unsigned int v43; // [sp+334h] [bp-124h]@46
  char *Str; // [sp+338h] [bp-120h]@49
  int v45; // [sp+340h] [bp-118h]@54
  char *v46; // [sp+348h] [bp-110h]@56
  _TimeItem_fld *v47; // [sp+350h] [bp-108h]@62
  _STORAGE_LIST::_db_con pNewItem; // [sp+368h] [bp-F0h]@62
  char v49; // [sp+3A4h] [bp-B4h]@75
  char v50; // [sp+3A5h] [bp-B3h]@75
  char v51; // [sp+3A6h] [bp-B2h]@78
  __time32_t Time; // [sp+3B4h] [bp-A4h]@80
  char pbyType; // [sp+3D4h] [bp-84h]@88
  bool bCircle; // [sp+3F4h] [bp-64h]@88
  _base_fld *v55; // [sp+408h] [bp-50h]@88
  _base_fld *v56; // [sp+410h] [bp-48h]@88
  int v57; // [sp+420h] [bp-38h]@76
  char *szNewItem; // [sp+428h] [bp-30h]@91
  char *szUseItem; // [sp+430h] [bp-28h]@91
  char *szCharName; // [sp+438h] [bp-20h]@93
  char v61; // [sp+440h] [bp-18h]@93
  unsigned __int64 v62; // [sp+448h] [bp-10h]@4
  CPlayer *pOne; // [sp+460h] [bp+8h]@1
  unsigned __int16 v64; // [sp+470h] [bp+18h]@1

  v64 = wItemSerial;
  pOne = this;
  v3 = &v20;
  for ( i = 276i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v62 = (unsigned __int64)&v20 ^ _security_cookie;
  v23 = 0;
  v25 = 0i64;
  v26 = 0i64;
  v27 = -1;
  v28 = 0;
  v29 = CRecordData::GetRecord(&stru_1799C6928, wManualIndex);
  pCon = (_STORAGE_LIST::_storage_con *)_STORAGE_LIST::GetPtrFromSerial(
                                          (_STORAGE_LIST *)&pOne->m_Param.m_dbInven.m_nListNum,
                                          v64);
  if ( !pCon )
  {
    v23 = 4;
    goto $RESULT_39;
  }
  if ( pCon->m_bLock )
  {
    v23 = 10;
    goto $RESULT_39;
  }
  v25 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pCon->m_byTableCode, pCon->m_wItemIndex);
  if ( !v25 )
  {
    v23 = 12;
    goto $RESULT_39;
  }
  v31 = pCon->m_byTableCode;
  v32 = pCon->m_wItemIndex;
  nCashType = GetUsePcCashType(pCon->m_byTableCode, pCon->m_wItemIndex);
  if ( !CPlayer::IsUsableAccountType(pOne, nCashType) )
  {
    CPlayer::SendMsg_PremiumCashItemUse(pOne, 0xFFFFu);
    v23 = 12;
    goto $RESULT_39;
  }
  if ( pCon->m_byTableCode == 17 )
  {
    v34 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 17, pCon->m_wItemIndex);
    if ( !v34 )
    {
      v23 = 12;
      goto $RESULT_39;
    }
    if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&pOne->m_Param.m_dbInven.m_nListNum) == 255 )
    {
      v23 = 3;
      goto $RESULT_39;
    }
    if ( *(_DWORD *)&v34[3].m_strCode[4] )
    {
      v5 = CGoldenBoxItemMgr::Instance();
      if ( CGoldenBoxItemMgr::Get_Event_Status(v5) != 2 )
      {
        v23 = 12;
        goto $RESULT_39;
      }
      v35 = 0;
      Dst = 0;
      memset(&v37, 0, 0x27Fui64);
      v6 = CGoldenBoxItemMgr::Instance();
      v7 = CGoldenBoxItemMgr::GetGoldBoxItemPtr(v6);
      memcpy_0(&Dst, v7, 0x280ui64);
      for ( j = 0; ; ++j )
      {
        v8 = CGoldenBoxItemMgr::Instance();
        v9 = CGoldenBoxItemMgr::GetLoopCount(v8);
        if ( j >= (unsigned __int8)v9 )
          break;
        if ( !strcmp_0(v34->m_strCode, &Dst + 64 * (signed __int64)j) )
        {
          v35 = j;
          break;
        }
      }
      v10 = CGoldenBoxItemMgr::Instance();
      psItemCode = (char *)CGoldenBoxItemMgr::BoxItemOpen(v10, v35);
      if ( psItemCode )
      {
        v40 = GetItemTableCode(psItemCode);
        if ( v40 < 0 )
        {
          v23 = 12;
          goto $RESULT_39;
        }
        v27 = v40;
        v26 = CRecordData::GetRecordByHash((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)v40, psItemCode, 2, 5);
        v28 = psItemCode[8];
        if ( v26 )
        {
          v11 = CGoldenBoxItemMgr::Instance();
          CGoldenBoxItemMgr::Set_BoxItem_Count(v11, v35, v26->m_dwIndex);
        }
      }
    }
  }
  else
  {
    if ( !v29 )
    {
      v23 = 12;
      goto $RESULT_39;
    }
    if ( pCon->m_byTableCode != 31 )
    {
      v23 = 12;
      goto $RESULT_39;
    }
    if ( !CheckSameItemFromString_CodeIndex(v29->m_strCode, pCon->m_byTableCode, pCon->m_wItemIndex) )
    {
      v23 = 12;
      goto $RESULT_39;
    }
    v41 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pCon->m_byTableCode, pCon->m_wItemIndex);
    if ( !v41 )
    {
      v23 = 12;
      goto $RESULT_39;
    }
    v12 = CPlayerDB::GetLevel(&pOne->m_Param);
    if ( v12 < *(_DWORD *)&v41[4].m_strCode[0] )
    {
      v23 = 21;
      goto $RESULT_39;
    }
    if ( *(_DWORD *)&v41[4].m_strCode[8] != -1 )
    {
      v13 = CPlayerDB::GetLevel(&pOne->m_Param);
      if ( v13 > *(_DWORD *)&v41[4].m_strCode[8] )
      {
        v23 = 21;
        goto $RESULT_39;
      }
    }
    if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&pOne->m_Param.m_dbInven.m_nListNum) == 255 )
    {
      v23 = 3;
      goto $RESULT_39;
    }
    v42 = rand() % 10000;
    v43 = 0;
    for ( k = 0; k < 61; ++k )
    {
      Str = (char *)&v29[1] + 16 * k;
      if ( strlen_0(Str) < 2 || !strncmp(Str, "-", 1ui64) || !strncmp(Str, "-1", 2ui64) )
        break;
      v43 += *((_DWORD *)Str + 3);
      if ( v42 < v43 )
      {
        v45 = GetItemTableCode(Str);
        if ( v45 < 0 )
          break;
        v27 = v45;
        v26 = CRecordData::GetRecordByHash((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)v45, Str, 2, 5);
        v28 = Str[8];
        if ( v26 )
        {
          v46 = GetItemEquipCivil((unsigned __int8)v27, v26->m_dwIndex);
          v14 = CPlayerDB::GetRaceSexCode(&pOne->m_Param);
          if ( v46[v14] != 49 )
          {
            Str = (char *)&v29[1];
            v27 = GetItemTableCode((char *)&v29[1]);
            v26 = CRecordData::GetRecordByHash((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)v27, Str, 2, 5);
            v28 = Str[8];
          }
          break;
        }
      }
    }
  }
  if ( !v26 )
    v23 = 12;
$RESULT_39:
  v47 = 0i64;
  _STORAGE_LIST::_db_con::_db_con(&pNewItem);
  if ( !v23 )
  {
    if ( IsOverLapItem(pCon->m_byTableCode) )
    {
      LOBYTE(strErrorCodePos) = 1;
      bDelete[0] = 1;
      CPlayer::Emb_AlterDurPoint(pOne, 0, BYTE3(pCon[1].m_dwDur), -1, 1, 1);
    }
    else
    {
      strErrorCodePos = "CPlayer::pc_ExchangeItem()";
      bDelete[0] = 1;
      if ( !CPlayer::Emb_DelStorage(pOne, 0, BYTE3(pCon[1].m_dwDur), 0, 1, "CPlayer::pc_ExchangeItem()") )
      {
        if ( v47 && v47->m_nCheckType )
          CPlayer::SendMsg_ExchangeLendItemResult(pOne, -1, &pNewItem);
        else
          CPlayer::SendMsg_ExchangeItemResult(pOne, -1, &pNewItem);
        return;
      }
    }
    v47 = TimeItem::FindTimeRec((unsigned __int8)v27, v26->m_dwIndex);
    pNewItem.m_byTableCode = v27;
    pNewItem.m_wItemIndex = v26->m_dwIndex;
    if ( IsOverLapItem((unsigned __int8)v27) )
      pNewItem.m_dwDur = v28;
    else
      pNewItem.m_dwDur = GetItemDurPoint((unsigned __int8)v27, v26->m_dwIndex);
    v49 = GetItemKindCode((unsigned __int8)v27);
    v50 = GetDefItemUpgSocketNum((unsigned __int8)v27, v26->m_dwIndex);
    if ( (signed int)(unsigned __int8)v50 <= 0 )
      v57 = 0;
    else
      v57 = rand() % (unsigned __int8)v50 + 1;
    v51 = v57;
    pNewItem.m_dwLv = GetBitAfterSetLimSocket(v57);
    pNewItem.m_wSerial = CPlayerDB::GetNewItemSerial(&pOne->m_Param);
    if ( v47 && v47->m_nCheckType )
    {
      pNewItem.m_byCsMethod = v47->m_nCheckType;
      _time32(&Time);
      pNewItem.m_dwT = v47->m_nUseTime + Time;
      pNewItem.m_dwLendRegdTime = Time;
    }
    if ( !CPlayer::Emb_AddStorage(pOne, 0, (_STORAGE_LIST::_storage_con *)&pNewItem.m_bLoad, 0, 1) )
    {
      CPlayer::Emb_AddStorage(pOne, 0, pCon, 0, 1);
      if ( v47 && v47->m_nCheckType )
        CPlayer::SendMsg_ExchangeLendItemResult(pOne, -1, &pNewItem);
      else
        CPlayer::SendMsg_ExchangeItemResult(pOne, -1, &pNewItem);
      return;
    }
    CPlayer::SendMsg_FanfareItem(pOne, 2, &pNewItem, 0i64);
    v15 = CGoldenBoxItemMgr::Instance();
    if ( CGoldenBoxItemMgr::Get_Event_Status(v15) == 2 )
    {
      pbyType = 0;
      bCircle = 1;
      v55 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)v31, v32);
      v56 = CRecordData::GetRecord(
              (CRecordData *)&unk_1799C6AA0 + (unsigned __int8)pNewItem.m_byTableCode,
              pNewItem.m_wItemIndex);
      if ( !v55 || !v56 )
        return;
      szNewItem = v56->m_strCode;
      szUseItem = v55->m_strCode;
      v16 = CGoldenBoxItemMgr::Instance();
      CGoldenBoxItemMgr::BoxItemOpenEffectType(v16, szUseItem, szNewItem, &pbyType, &bCircle);
      if ( pbyType != 6 && pbyType != 8 )
      {
        CPlayer::SendMsg_Notify_Me_Get_Golden_Box(pOne, pbyType, &pNewItem);
      }
      else
      {
        szCharName = CPlayerDB::GetCharNameA(&pOne->m_Param);
        v17 = CPlayerDB::GetCharSerial(&pOne->m_Param);
        CPlayer::SendMsg_Notify_Get_Golden_Box(pOne, pbyType, v17, szCharName, &pNewItem, 0);
        v61 = pbyType;
        if ( pbyType == 6 )
        {
          v18 = CGoldenBoxItemMgr::Instance();
          CGoldenBoxItemMgr::WriteGetGoldBarLog(v18, pOne, &pNewItem);
        }
        else if ( v61 == 8 )
        {
          v19 = CGoldenBoxItemMgr::Instance();
          CGoldenBoxItemMgr::WriteEventCouponLog(v19, pOne, &pNewItem);
        }
      }
    }
    CMgrAvatorItemHistory::exchange_item(
      &CPlayer::s_MgrItemHistory,
      pOne->m_ObjID.m_wIndex,
      (_STORAGE_LIST::_db_con *)pCon,
      &pNewItem,
      pOne->m_szItemHistoryFileName);
  }
  if ( v47 && v47->m_nCheckType )
    CPlayer::SendMsg_ExchangeLendItemResult(pOne, v23, &pNewItem);
  else
    CPlayer::SendMsg_ExchangeItemResult(pOne, v23, &pNewItem);
}
