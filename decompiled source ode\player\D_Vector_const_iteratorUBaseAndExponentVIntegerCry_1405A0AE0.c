/*
 * Function: ??D?$_Vector_const_iterator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@QEBAAEBU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@XZ
 * Address: 0x1405A0AE0
 */

__int64 __fastcall std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::operator*(__int64 a1)
{
  return *(_QWORD *)(a1 + 16);
}
