/*
 * Function: ?InitializeDerivedAndReturnNewSizes@SignatureVerificationFilter@CryptoPP@@MEAAXAEBVNameValuePairs@2@AEA_K11@Z
 * Address: 0x1405FD9B0
 */

void __fastcall CryptoPP::SignatureVerificationFilter::InitializeDerivedAndReturnNewSizes(CryptoPP::SignatureVerificationFilter *this, const struct CryptoPP::NameValuePairs *a2, unsigned __int64 *a3, unsigned __int64 *a4, unsigned __int64 *a5)
{
  const char *v5; // rax@1
  __int64 v6; // rax@1
  unsigned __int64 v7; // rax@1
  unsigned __int64 v8; // [sp+28h] [bp-20h]@4
  unsigned __int64 v9; // [sp+30h] [bp-18h]@7
  CryptoPP::SignatureVerificationFilter *v10; // [sp+50h] [bp+8h]@1
  const struct CryptoPP::NameValuePairs *v11; // [sp+58h] [bp+10h]@1
  unsigned __int64 *v12; // [sp+60h] [bp+18h]@1
  unsigned __int64 *v13; // [sp+68h] [bp+20h]@1

  v13 = a4;
  v12 = a3;
  v11 = a2;
  v10 = this;
  v5 = CryptoPP::Name::SignatureVerificationFilterFlags((CryptoPP::Name *)this);
  v10->m_flags = CryptoPP::NameValuePairs::GetValueWithDefault<unsigned int>(v11, v5, 9i64);
  LODWORD(v6) = ((int (__fastcall *)(CryptoPP::PK_Verifier *))v10->m_verifier->vfptr[1].__vecDelDtor)(v10->m_verifier);
  CryptoPP::member_ptr<CryptoPP::PK_MessageAccumulator>::reset(&v10->m_messageAccumulator, v6);
  LODWORD(v7) = (*(int (__fastcall **)(CryptoPP::PK_Verifier *))&v10->m_verifier->vfptr->gap8[0])(v10->m_verifier);
  if ( !v7 )
    _wassert(L"size != 0", L"D:\\RF Project\\RF_Server64\\28 Crypto++\\filters.cpp", 0x33Fu);
  v10->m_verified = 0;
  if ( v10->m_flags & 1 )
    v8 = v7;
  else
    v8 = 0i64;
  *v12 = v8;
  *v13 = 1i64;
  if ( v10->m_flags & 1 )
    v9 = 0i64;
  else
    v9 = v7;
  *a5 = v9;
}
