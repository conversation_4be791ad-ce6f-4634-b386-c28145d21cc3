/*
 * Function: ?ct_circle_user_num@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140291870
 */

char __fastcall ct_circle_user_num(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v4; // eax@8
  __int64 v5; // [sp+0h] [bp-188h]@1
  bool bFilter[4]; // [sp+20h] [bp-168h]@20
  char *pwszMessage; // [sp+28h] [bp-160h]@20
  unsigned int v8; // [sp+48h] [bp-140h]@8
  int v9; // [sp+4Ch] [bp-13Ch]@8
  int v10; // [sp+50h] [bp-138h]@20
  unsigned int v11; // [sp+64h] [bp-124h]@8
  _pnt_rect pRect; // [sp+78h] [bp-110h]@8
  _sec_info *v13; // [sp+98h] [bp-F0h]@8
  int j; // [sp+A0h] [bp-E8h]@8
  int k; // [sp+A4h] [bp-E4h]@10
  unsigned int dwSecIndex; // [sp+A8h] [bp-E0h]@13
  CObjectList *v17; // [sp+B0h] [bp-D8h]@13
  CObjectList *v18; // [sp+B8h] [bp-D0h]@14
  CObjectListVtbl *v19; // [sp+C0h] [bp-C8h]@16
  unsigned int v20; // [sp+C8h] [bp-C0h]@16
  char Dest; // [sp+E0h] [bp-A8h]@20
  int nSecNum; // [sp+170h] [bp-18h]@8
  unsigned __int64 v23; // [sp+178h] [bp-10h]@4
  CPlayer *v24; // [sp+190h] [bp+8h]@1

  v24 = pOne;
  v1 = &v5;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v23 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( v24 )
  {
    if ( CGameObject::GetCurSecNum((CGameObject *)&v24->vfptr) == -1 )
    {
      result = 0;
    }
    else
    {
      v8 = 0;
      memset(&v9, 0, 8ui64);
      v11 = 0;
      v13 = CMapData::GetSecInfo(v24->m_pCurMap);
      nSecNum = CGameObject::GetCurSecNum((CGameObject *)&v24->vfptr);
      v4 = CGameObject::GetUseSectorRange((CGameObject *)&v24->vfptr);
      CMapData::GetRectInRadius(v24->m_pCurMap, &pRect, v4, nSecNum);
      for ( j = pRect.nStarty; j <= pRect.nEndy; ++j )
      {
        for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
        {
          dwSecIndex = v13->m_nSecNumW * j + k;
          v17 = CMapData::GetSectorListPlayer(v24->m_pCurMap, v24->m_wMapLayerIndex, dwSecIndex);
          if ( v17 )
          {
            v18 = (CObjectList *)v17->m_Head.m_pNext;
            while ( (_object_list_point *)v18 != &v17->m_Tail )
            {
              v19 = v18->vfptr;
              v18 = (CObjectList *)v18->m_Head.m_pItem;
              v20 = (*((int (__fastcall **)(__int64))v19->__vecDelDtor + 41))((__int64)v19);
              if ( v20 < 3 )
              {
                ++*(&v8 + v20);
                ++v11;
              }
            }
          }
        }
      }
      LODWORD(pwszMessage) = v10;
      *(_DWORD *)bFilter = v9;
      sprintf(&Dest, "Circle: Total( %d ), B( %d ), C( %d ), A( %d )", v11, v8);
      CPlayer::SendData_ChatTrans(v24, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
