/*
 * Function: ?Update_CharSlot@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x140498DD0
 */

char __fastcall CRFWorldDatabase::Update_CharSlot(CRFWorldDatabase *this, unsigned int dwAvatorSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-1D8h]@1
  void *SQLStmt; // [sp+20h] [bp-1B8h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-1B0h]@21
  SQLLEN v8; // [sp+38h] [bp-1A0h]@21
  __int16 v9; // [sp+44h] [bp-194h]@9
  char Dest; // [sp+60h] [bp-178h]@4
  char v11; // [sp+61h] [bp-177h]@4
  unsigned int TargetValue; // [sp+174h] [bp-64h]@4
  int v13; // [sp+198h] [bp-40h]@28
  char v14; // [sp+19Ch] [bp-3Ch]@28
  unsigned int j; // [sp+1B4h] [bp-24h]@33
  unsigned __int64 v16; // [sp+1C0h] [bp-18h]@4
  CRFWorldDatabase *v17; // [sp+1E0h] [bp+8h]@1

  v17 = this;
  v2 = &v5;
  for ( i = 116i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v16 = (unsigned __int64)&v5 ^ _security_cookie;
  Dest = 0;
  memset(&v11, 0, 0xFFui64);
  TargetValue = 0;
  sprintf(&Dest, "select AccountSerial from tbl_base where serial=%d and dck=0", dwAvatorSerial);
  if ( v17->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v17->vfptr, "Update_CharSlot");
  if ( v17->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v17->vfptr) )
  {
    v9 = SQLExecDirectA_0(v17->m_hStmtSelect, &Dest, -3);
    if ( v9 && v9 != 1 )
    {
      if ( v9 == 100 )
      {
        result = 0;
      }
      else
      {
        SQLStmt = v17->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v9, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v9, v17->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      v9 = SQLFetch_0(v17->m_hStmtSelect);
      if ( v9 && v9 != 1 )
      {
        if ( v9 != 100 )
        {
          SQLStmt = v17->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v9, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v9, v17->m_hStmtSelect);
        }
        if ( v17->m_hStmtSelect )
          SQLCloseCursor_0(v17->m_hStmtSelect);
        result = 0;
      }
      else
      {
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v17->m_hStmtSelect, 1u, -18, &TargetValue, 0i64, &v8);
        if ( v9 && v9 != 1 )
        {
          SQLStmt = v17->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v9, &Dest, "SQLGetData", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v9, v17->m_hStmtSelect);
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          result = 0;
        }
        else
        {
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          v13 = 0;
          memset(&v14, 0, 8ui64);
          sprintf(&Dest, "select serial from tbl_base where AccountSerial=%d and dck=0 order by slot", TargetValue);
          v9 = SQLExecDirectA_0(v17->m_hStmtSelect, &Dest, -3);
          if ( v9 && v9 != 1 )
          {
            if ( v9 == 100 )
            {
              result = 0;
            }
            else
            {
              SQLStmt = v17->m_hStmtSelect;
              CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v9, &Dest, "SQLExecDirectA", SQLStmt);
              CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v9, v17->m_hStmtSelect);
              result = 0;
            }
          }
          else
          {
            for ( j = 0; (signed int)j < 3; ++j )
            {
              v9 = SQLFetch_0(v17->m_hStmtSelect);
              if ( v9 )
              {
                if ( v9 != 1 )
                  break;
              }
              StrLen_or_IndPtr = &v8;
              SQLStmt = 0i64;
              v9 = SQLGetData_0(v17->m_hStmtSelect, 1u, -18, &v13 + (signed int)j, 0i64, &v8);
            }
            if ( v17->m_hStmtSelect )
              SQLCloseCursor_0(v17->m_hStmtSelect);
            for ( j = 0; (signed int)j < 3; ++j )
            {
              if ( *(&v13 + (signed int)j) )
              {
                sprintf(&Dest, "update tbl_base set slot=%d where serial=%d", j, (unsigned int)*(&v13 + (signed int)j));
                CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v17->vfptr, &Dest, 1);
              }
            }
            if ( v17->m_bSaveDBLog )
              CRFNewDatabase::Log((CRFNewDatabase *)&v17->vfptr, "Update_CharSlot Success");
            result = 1;
          }
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::FmtLog((CRFNewDatabase *)&v17->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
