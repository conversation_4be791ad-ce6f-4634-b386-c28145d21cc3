/*
 * Function: j_?SetSuggested@CUnmannedTraderTaxRateManager@@QEAAXEEKPEADK@Z
 * Address: 0x140006528
 */

void __fastcall CUnmannedTraderTaxRateManager::SetSuggested(CUnmannedTraderTaxRateManager *this, char byRace, char byMatterType, unsigned int dwMatterDst, char *wszMatterDst, unsigned int dwNext)
{
  CUnmannedTraderTaxRateManager::SetSuggested(this, byRace, byMatterType, dwMatterDst, wszMatterDst, dwNext);
}
