/*
 * Function: ?CheckBattleMode@CPlayer@@QEAAXXZ
 * Address: 0x1400685B0
 */

void __fastcall CPlayer::CheckBattleMode(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  DWORD v4; // [sp+20h] [bp-18h]@5
  CPlayer *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5->m_byBattleMode )
  {
    v4 = 0;
    if ( v5->m_byBattleMode == 1 )
      v4 = 15000;
    else
      v4 = 10000;
    if ( timeGetTime() - v5->m_dwBattleTime >= v4 )
    {
      v5->m_byBattleMode = 0;
      v5->m_dwBattleTime = 0;
    }
  }
}
