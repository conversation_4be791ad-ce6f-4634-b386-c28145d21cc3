/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAU?$pair@HH@std@@_KU12@V?$allocator@U?$pair@HH@std@@@2@@stdext@@YAXPEAU?$pair@HH@std@@_KAEBU12@AEAV?$allocator@U?$pair@HH@std@@@2@@Z
 * Address: 0x14000DE95
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<std::pair<int,int> *,unsigned __int64,std::pair<int,int>,std::allocator<std::pair<int,int>>>(std::pair<int,int> *_First, unsigned __int64 _Count, std::pair<int,int> *_Val, std::allocator<std::pair<int,int> > *_Al)
{
  stdext::unchecked_uninitialized_fill_n<std::pair<int,int> *,unsigned __int64,std::pair<int,int>,std::allocator<std::pair<int,int>>>(
    _First,
    _Count,
    _Val,
    _Al);
}
