/*
 * Function: ?pc_MovePortal@CPlayer@@QEAAXHPEAG@Z
 * Address: 0x1400C54A0
 */

void __fastcall CPlayer::pc_MovePortal(CPlayer *this, int nPortalIndex, unsigned __int16 *pConsumeSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@18
  unsigned __int32 v6; // ecx@20
  int v7; // eax@36
  int v8; // eax@41
  int v9; // eax@44
  CMapData *v10; // rax@60
  int v11; // eax@62
  int v12; // eax@77
  int v13; // eax@82
  int v14; // eax@83
  CGuildRoomSystem *v15; // rax@89
  CGuildRoomSystem *v16; // rax@90
  CGuildRoomSystem *v17; // rax@91
  __int64 v18; // [sp+0h] [bp-1E8h]@1
  char v19; // [sp+30h] [bp-1B8h]@4
  char v20; // [sp+31h] [bp-1B7h]@4
  char v21; // [sp+32h] [bp-1B6h]@4
  _portal_dummy *v22; // [sp+38h] [bp-1B0h]@4
  _portal_dummy *v23; // [sp+40h] [bp-1A8h]@4
  CMapData *pIntoMap; // [sp+48h] [bp-1A0h]@4
  float pNewPos; // [sp+58h] [bp-190h]@70
  int v26; // [sp+74h] [bp-174h]@4
  unsigned __int16 v27; // [sp+78h] [bp-170h]@4
  CTransportShip *v28; // [sp+80h] [bp-168h]@37
  char v29; // [sp+88h] [bp-160h]@46
  int j; // [sp+8Ch] [bp-15Ch]@46
  char *v31; // [sp+90h] [bp-158h]@49
  _TicketItem_fld *pTicketFld; // [sp+98h] [bp-150h]@51
  CTransportShip *v33; // [sp+A0h] [bp-148h]@58
  _portal_fld *v34; // [sp+A8h] [bp-140h]@75
  _STORAGE_LIST::_db_con *ppConsumeItems; // [sp+B8h] [bp-130h]@78
  char v36; // [sp+C0h] [bp-128h]@78
  int v37; // [sp+E8h] [bp-100h]@78
  char v38; // [sp+ECh] [bp-FCh]@78
  bool v39; // [sp+114h] [bp-D4h]@78
  char v40; // [sp+115h] [bp-D3h]@78
  _STORAGE_LIST::_db_con *v41; // [sp+138h] [bp-B0h]@84
  char v42; // [sp+140h] [bp-A8h]@84
  int v43; // [sp+168h] [bp-80h]@84
  char v44; // [sp+16Ch] [bp-7Ch]@84
  bool v45; // [sp+194h] [bp-54h]@84
  char v46; // [sp+195h] [bp-53h]@84
  _portal_fld *v47; // [sp+1A8h] [bp-40h]@18
  CExtDummy *v48; // [sp+1B0h] [bp-38h]@20
  int v49; // [sp+1B8h] [bp-30h]@41
  int v50; // [sp+1BCh] [bp-2Ch]@44
  int v51; // [sp+1C0h] [bp-28h]@89
  int v52; // [sp+1C4h] [bp-24h]@90
  CUserDB *v53; // [sp+1C8h] [bp-20h]@91
  int n; // [sp+1D0h] [bp-18h]@91
  CGuild *v55; // [sp+1D8h] [bp-10h]@91
  CPlayer *v56; // [sp+1F0h] [bp+8h]@1
  int nPortalIndexa; // [sp+1F8h] [bp+10h]@1
  unsigned __int16 *pItemSerials; // [sp+200h] [bp+18h]@1

  pItemSerials = pConsumeSerial;
  nPortalIndexa = nPortalIndex;
  v56 = this;
  v3 = &v18;
  for ( i = 120i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v19 = 0;
  v20 = -1;
  v21 = -1;
  v22 = 0i64;
  v23 = 0i64;
  pIntoMap = 0i64;
  v26 = 0;
  v27 = 0;
  if ( v56->m_bInGuildBattle )
  {
    v19 = 19;
  }
  else if ( CGameObject::GetCurSecNum((CGameObject *)&v56->vfptr) == -1 || v56->m_bMapLoading )
  {
    v19 = 1;
  }
  else if ( v56->m_pmTrd.bDTradeMode )
  {
    v19 = 2;
  }
  else if ( v56->m_byStandType == 1 )
  {
    v19 = 3;
  }
  else if ( CPlayer::IsSiegeMode(v56) )
  {
    v19 = 4;
  }
  else
  {
    v22 = CMapData::GetPortal(v56->m_pCurMap, nPortalIndexa);
    if ( v22 )
    {
      if ( !v56->m_byUserDgr )
      {
        v47 = v22->m_pPortalRec;
        v5 = CPlayerDB::GetRaceSexCode(&v56->m_Param);
        if ( v47->m_strUseRace[v5] == 48 )
        {
          v19 = 6;
          goto $RESULT_48;
        }
        v6 = v22->m_pDumPos->m_wLineIndex;
        v48 = &v56->m_pCurMap->m_Dummy;
        if ( !CExtDummy::IsInBBox(v48, v6, v56->m_fCurPos) )
        {
          v19 = 7;
          goto $RESULT_48;
        }
      }
      if ( v22->m_pPortalRec )
      {
        pIntoMap = CMapOperation::GetMap(&g_MapOper, v22->m_pPortalRec->m_strLinkMapCode);
        if ( pIntoMap )
        {
          v20 = pIntoMap->m_pMapSet->m_dwIndex;
          if ( v56->m_pDHChannel )
          {
            if ( v56->m_pDHChannel->m_pQuestSetup->pUseMap != pIntoMap )
            {
              v19 = 15;
              goto $RESULT_48;
            }
            if ( CDarkHoleChannel::GetLeaderSerial(v56->m_pDHChannel) != v56->m_dwObjSerial )
            {
              v19 = 16;
              goto $RESULT_48;
            }
            if ( !CDarkHoleChannel::IsMoveNextMission(v56->m_pDHChannel, nPortalIndexa) )
            {
              v19 = 17;
              goto $RESULT_48;
            }
            if ( !CDarkHoleChannel::IsAllMemberNearPosition(v56->m_pDHChannel, v22->m_pDumPos->m_fCenterPos, 50) )
            {
              v19 = 18;
              goto $RESULT_48;
            }
            v27 = v56->m_pDHChannel->m_wLayerIndex;
          }
          else
          {
            v7 = CPlayerDB::GetRaceCode(&v56->m_Param);
            if ( pIntoMap == g_TransportShip[10162 * v7 + 2] )
            {
              v28 = (CTransportShip *)&g_TransportShip[10162 * CPlayerDB::GetRaceCode(&v56->m_Param)];
              if ( !v28->m_bAnchor )
              {
                v19 = 9;
                goto $RESULT_48;
              }
              if ( CTransportShip::GetMapCurDirect(v28) != v56->m_pCurMap )
              {
                v19 = 11;
                goto $RESULT_48;
              }
              v49 = ((int (__fastcall *)(CPlayer *))v56->vfptr->GetLevel)(v56);
              v8 = CTransportShip::GetRideLimLevel(v28);
              if ( v49 < v8 )
              {
                v19 = 14;
                goto $RESULT_48;
              }
              if ( CTransportShip::GetRideUpLimLevel(v28) != -1 )
              {
                v50 = ((int (__fastcall *)(CPlayer *))v56->vfptr->GetLevel)(v56);
                v9 = CTransportShip::GetRideUpLimLevel(v28);
                if ( v50 > v9 )
                {
                  v19 = 14;
                  goto $RESULT_48;
                }
              }
              v29 = 0;
              for ( j = 0; j < v56->m_Param.m_dbInven.m_nUsedNum; ++j )
              {
                v31 = &v56->m_Param.m_dbInven.m_pStorageList[j].m_bLoad;
                if ( *v31 )
                {
                  if ( v31[1] == 28 )
                  {
                    pTicketFld = (_TicketItem_fld *)CRecordData::GetRecord(
                                                      (CRecordData *)&unk_1799C6AA0 + 28,
                                                      *(_WORD *)(v31 + 3));
                    if ( CTransportShip::GetCurRideShipThisTicket(v28, pTicketFld) )
                    {
                      v29 = 1;
                      break;
                    }
                  }
                }
              }
              if ( !v29 )
              {
                v19 = 12;
                goto $RESULT_48;
              }
              v27 = CPlayerDB::GetRaceCode(&v56->m_Param);
            }
            else if ( v56->m_pCurMap == g_TransportShip[10162 * CPlayerDB::GetRaceCode(&v56->m_Param) + 2] )
            {
              v33 = (CTransportShip *)&g_TransportShip[10162 * CPlayerDB::GetRaceCode(&v56->m_Param)];
              if ( !v33->m_bAnchor )
              {
                v19 = 9;
                goto $RESULT_48;
              }
              v10 = CTransportShip::GetMapCurDirect(v33);
              if ( v10 != pIntoMap )
              {
                v19 = 11;
                goto $RESULT_48;
              }
              v11 = CPlayerDB::GetRaceCode(&v56->m_Param);
              if ( nPortalIndexa != 3 * v33->m_byDirect + v11 )
              {
                v19 = 5;
                goto $RESULT_48;
              }
            }
            else if ( !strcmp_0(pIntoMap->m_pMapSet->m_strCode, "Cauldron01") && CPlayerDB::GetLevel(&v56->m_Param) < 40 )
            {
              v19 = 14;
              goto $RESULT_48;
            }
          }
          v23 = CMapData::GetPortal(pIntoMap, v22->m_pPortalRec->m_strLinkPortalCode);
          if ( v23 )
          {
            v21 = v23->m_pPortalRec->m_dwIndex;
            if ( CMapData::GetRandPosInDummy(pIntoMap, v23->m_pDumPos, &pNewPos, 1) )
            {
              if ( !v56->m_pDHChannel || CDarkHoleChannel::SearchMissionFromPos(v56->m_pDHChannel, &pNewPos) )
              {
                v34 = v22->m_pPortalRec;
                if ( v34->m_bNeedConCheck )
                {
                  if ( v34->m_nUpLevelLim > -1 )
                  {
                    v13 = ((int (__fastcall *)(CPlayer *))v56->vfptr->GetLevel)(v56);
                    if ( v13 < v34->m_nNeedChrLevel
                      || (v14 = ((int (__fastcall *)(_QWORD))v56->vfptr->GetLevel)(v56), v14 >= v34->m_nUpLevelLim) )
                    {
                      v19 = 21;
                      goto $RESULT_48;
                    }
                    v41 = 0i64;
                    memset(&v42, 0, 0x10ui64);
                    v43 = 0;
                    memset(&v44, 0, 8ui64);
                    v45 = 0;
                    memset(&v46, 0, 2ui64);
                    if ( !CPlayer::GetUseConsumeItem(v56, v34->m_ConsumeItemList, pItemSerials, &v41, &v43, &v45) )
                    {
                      v19 = 20;
                      goto $RESULT_48;
                    }
                    CPlayer::DeleteUseConsumeItem(v56, &v41, &v43, &v45);
                  }
                  else
                  {
                    v12 = ((int (__fastcall *)(_QWORD))v56->vfptr->GetLevel)(v56);
                    if ( v12 < v34->m_nNeedChrLevel )
                    {
                      v19 = 21;
                      goto $RESULT_48;
                    }
                    ppConsumeItems = 0i64;
                    memset(&v36, 0, 0x10ui64);
                    v37 = 0;
                    memset(&v38, 0, 8ui64);
                    v39 = 0;
                    memset(&v40, 0, 2ui64);
                    if ( !CPlayer::GetUseConsumeItem(
                            v56,
                            v34->m_ConsumeItemList,
                            pItemSerials,
                            &ppConsumeItems,
                            &v37,
                            &v39) )
                    {
                      v19 = 20;
                      goto $RESULT_48;
                    }
                    CPlayer::DeleteUseConsumeItem(v56, &ppConsumeItems, &v37, &v39);
                  }
                }
                if ( v56->m_Param.m_pGuild )
                {
                  v51 = CPlayerDB::GetRaceCode(&v56->m_Param);
                  v15 = CGuildRoomSystem::GetInstance();
                  if ( v56->m_pCurMap == CGuildRoomSystem::GetMapData(v15, v51, 0)
                    || (v52 = CPlayerDB::GetRaceCode(&v56->m_Param),
                        v16 = CGuildRoomSystem::GetInstance(),
                        v56->m_pCurMap == CGuildRoomSystem::GetMapData(v16, v52, 1)) )
                  {
                    v53 = v56->m_pUserDB;
                    n = v56->m_ObjID.m_wIndex;
                    v55 = v56->m_Param.m_pGuild;
                    v17 = CGuildRoomSystem::GetInstance();
                    CGuildRoomSystem::RoomOut(v17, v55->m_dwSerial, n, v53->m_dwSerial);
                  }
                }
                if ( Major_Sette_Mine_Elan_Map )
                {
                  if ( !strcmp_0(pIntoMap->m_pMapSet->m_strCode, "resources")
                    || !strcmp_0(pIntoMap->m_pMapSet->m_strCode, "Sette")
                    || !strcmp_0(pIntoMap->m_pMapSet->m_strCode, "elan")
                    || !strcmp_0(pIntoMap->m_pMapSet->m_strCode, "NeutralA")
                    || !strcmp_0(pIntoMap->m_pMapSet->m_strCode, "NeutralB")
                    || !strcmp_0(pIntoMap->m_pMapSet->m_strCode, "NeutralC") )
                  {
                    v19 = 0;
                  }
                  else
                  {
                    v19 = 8;
                  }
                }
              }
              else
              {
                v19 = 8;
              }
            }
            else
            {
              v19 = 8;
            }
          }
          else
          {
            v19 = 8;
          }
        }
        else
        {
          v19 = 8;
        }
      }
      else
      {
        v19 = 8;
      }
    }
    else
    {
      v19 = 5;
    }
  }
$RESULT_48:
  if ( !v19 )
  {
    CPlayer::OutOfMap(v56, pIntoMap, v27, 1, &pNewPos);
    if ( v56->m_pDHChannel )
    {
      if ( CDarkHoleChannel::GotoNextMissionByPosition(v56->m_pDHChannel, &pNewPos) )
        CDarkHoleChannel::NextMissionOtherQuester(v56->m_pDHChannel, v56, v56->m_pDHChannel->m_MissionMgr.pCurMssionPtr);
    }
  }
  CPlayer::SendMsg_MovePortal(v56, v19, v20, v21, &pNewPos, 1);
}
