/*
 * Function: ?SetItem@CPvpCashMng@@QEAA_NPEADH@Z
 * Address: 0x1403F5B50
 */

char __fastcall CPvpCashMng::SetItem(CPvpCashMng *this, char *szItemCode, int nInx)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-38h]@1
  _base_fld *v7; // [sp+20h] [bp-18h]@4
  unsigned __int8 j; // [sp+28h] [bp-10h]@4
  CPvpCashMng *v9; // [sp+40h] [bp+8h]@1
  const char *szRecordCode; // [sp+48h] [bp+10h]@1
  int v11; // [sp+50h] [bp+18h]@1

  v11 = nInx;
  szRecordCode = szItemCode;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = 0i64;
  for ( j = 0; ; ++j )
  {
    if ( (signed int)j >= 37 )
      return 0;
    v7 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + j, szRecordCode);
    if ( v7 )
      break;
  }
  if ( GetItemKindCode(j) )
  {
    result = 0;
  }
  else
  {
    v9->m_TalikList.TalikInfo[v11].byTableCode = j;
    v9->m_TalikList.TalikInfo[v11].m_pFld = v7;
    result = 1;
  }
  return result;
}
