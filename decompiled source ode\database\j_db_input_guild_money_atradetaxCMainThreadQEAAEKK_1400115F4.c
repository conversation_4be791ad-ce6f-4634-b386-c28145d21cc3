/*
 * Function: j_?db_input_guild_money_atradetax@CMainThread@@QEAAEKKKPEAN0PEAE@Z
 * Address: 0x1400115F4
 */

char __fastcall CMainThread::db_input_guild_money_atradetax(CMainThread *this, unsigned int dwPusherSerial, unsigned int dwGuildSerial, unsigned int dwAddDalant, long double *dTotalDalant, long double *dTotalGold, char *byDate)
{
  return CMainThread::db_input_guild_money_atradetax(
           this,
           dwPusherSerial,
           dwGuildSerial,
           dwAddDalant,
           dTotalDalant,
           dTotalGold,
           byDate);
}
