/*
 * Function: j_??0?$_Ranit@VCUnmannedTraderItemCodeInfo@@_JPEBV1@AEBV1@@std@@QEAA@XZ
 * Address: 0x1400081CA
 */

void __fastcall std::_<PERSON>t<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &>::_<PERSON>t<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &>(std::_Ranit<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &> *this)
{
  std::_Ranit<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &>::_Ranit<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &>(this);
}
