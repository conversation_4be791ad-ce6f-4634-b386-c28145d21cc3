/*
 * Function: ?db_Insert_Economy_History@CMainThread@@QEAAEKPEAU_worlddb_economy_history_info@_worlddb_economy_history_info_array@@@Z
 * Address: 0x1401A64E0
 */

bool __fastcall CMainThread::db_Insert_Economy_History(CMainThread *this, unsigned int dwDate, _worlddb_economy_history_info_array::_worlddb_economy_history_info *pEconomyData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CMainThread *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return CRFWorldDatabase::Insert_Economy_History(v7->m_pWorldDB, dwDate, pEconomyData);
}
