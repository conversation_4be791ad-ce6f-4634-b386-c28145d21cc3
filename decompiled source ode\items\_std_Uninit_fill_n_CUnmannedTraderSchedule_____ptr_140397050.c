/*
 * Function: _std::_Uninit_fill_n_CUnmannedTraderSchedule_____ptr64_unsigned___int64_CUnmannedTraderSchedule_std::allocator_CUnmannedTraderSchedule____::_1_::catch$0
 * Address: 0x140397050
 */

void __fastcall __noreturn std::_Uninit_fill_n_CUnmannedTraderSchedule_____ptr64_unsigned___int64_CUnmannedTraderSchedule_std::allocator_CUnmannedTraderSchedule____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 64); *(_QWORD *)(i + 32) += 32i64 )
    std::allocator<CUnmannedTraderSchedule>::destroy(
      *(std::allocator<CUnmannedTraderSchedule> **)(i + 88),
      *(CUnmannedTraderSchedule **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
