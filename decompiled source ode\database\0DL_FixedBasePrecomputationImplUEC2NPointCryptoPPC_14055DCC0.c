/*
 * Function: ??0?$DL_FixedBasePrecomputationImpl@UEC2NPoint@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x14055DCC0
 */

__int64 __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>(__int64 a1)
{
  __int64 v2; // [sp+40h] [bp+8h]@1

  v2 = a1;
  CryptoPP::DL_FixedBasePrecomputation<CryptoPP::EC2NPoint>::DL_FixedBasePrecomputation<CryptoPP::EC2NPoint>();
  *(_QWORD *)v2 = &CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::`vftable';
  CryptoPP::EC2NPoint::EC2NPoint((CryptoPP::EC2NPoint *)(v2 + 8));
  CryptoPP::Integer::Integer((void *)(v2 + 72));
  std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>(v2 + 112);
  return v2;
}
