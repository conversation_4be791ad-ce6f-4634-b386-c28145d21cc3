/*
 * Function: _std::_Vector_iterator_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo___::operator___::_1_::dtor$1
 * Address: 0x140378280
 */

void __fastcall std::_Vector_iterator_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo___::operator___::_1_::dtor_1(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 68) & 1 )
  {
    *(_DWORD *)(a2 + 68) &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(*(std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > **)(a2 + 104));
  }
}
