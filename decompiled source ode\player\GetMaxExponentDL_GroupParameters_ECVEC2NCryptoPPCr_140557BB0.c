/*
 * Function: ?GetMaxExponent@?$DL_GroupParameters_EC@VEC2N@CryptoPP@@@CryptoPP@@UEBA?AVInteger@2@XZ
 * Address: 0x140557BB0
 */

CryptoPP::Integer *__fastcall CryptoPP::DL_GroupParameters_EC<CryptoPP::EC2N>::GetMaxExponent(__int64 a1, CryptoPP::Integer *a2)
{
  CryptoPP::Integer *v2; // rax@1
  CryptoPP::Integer b; // [sp+20h] [bp-48h]@1
  int v5; // [sp+48h] [bp-20h]@1
  __int64 v6; // [sp+50h] [bp-18h]@1
  __int64 v7; // [sp+70h] [bp+8h]@1
  CryptoPP::Integer *result; // [sp+78h] [bp+10h]@1

  result = a2;
  v7 = a1;
  v6 = -2i64;
  v5 = 0;
  CryptoPP::Integer::Integer(&b, 1);
  LODWORD(v2) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v7 + 64i64))(v7);
  CryptoPP::operator-(result, v2, &b);
  v5 |= 1u;
  CryptoPP::Integer::~Integer(&b);
  return result;
}
