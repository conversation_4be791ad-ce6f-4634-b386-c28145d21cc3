/*
 * Function: ?PutMessage@@YAXPEAD0@Z
 * Address: 0x140509BF0
 */

void __fastcall PutMessage(char *a1, char *a2)
{
  char *v2; // r8@1
  signed __int64 v3; // rdx@1
  char v4; // al@2
  __int64 v5; // rcx@3
  char *v6; // rdi@3
  char v7; // al@4
  char Text[256]; // [sp+20h] [bp-118h]@1

  v2 = a2;
  v3 = Text - a1;
  do
  {
    v4 = *a1++;
    a1[v3 - 1] = v4;
  }
  while ( v4 );
  v6 = &Text[strlen(Text) + 1];
  v5 = 0i64;
  do
  {
    v7 = v2[v5++];
    v6[v5 - 2] = v7;
  }
  while ( v7 );
  MessageBoxA(0i64, Text, "message", 0);
}
