/*
 * Function: ?Start@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAEPEAVCPlayer@@KK@Z
 * Address: 0x1403D44E0
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleManager::Start(GUILD_BATTLE::CNormalGuildBattleManager *this, CPlayer *pkPlayer, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  GUILD_BATTLE::CGuildBattleLogger *v7; // rax@12
  GUILD_BATTLE::CGuildBattleLogger *v8; // rax@15
  __int64 v9; // [sp+0h] [bp-78h]@1
  int v10; // [sp+20h] [bp-58h]@12
  int v11; // [sp+28h] [bp-50h]@12
  int v12; // [sp+30h] [bp-48h]@12
  int v13; // [sp+38h] [bp-40h]@12
  GUILD_BATTLE::CNormalGuildBattle *v14; // [sp+40h] [bp-38h]@6
  char v15; // [sp+48h] [bp-30h]@8
  int v16; // [sp+4Ch] [bp-2Ch]@8
  int v17; // [sp+50h] [bp-28h]@12
  char *v18; // [sp+58h] [bp-20h]@12
  int v19; // [sp+60h] [bp-18h]@15
  char *v20; // [sp+68h] [bp-10h]@15
  GUILD_BATTLE::CNormalGuildBattleManager *v21; // [sp+80h] [bp+8h]@1
  CPlayer *pkPlayera; // [sp+88h] [bp+10h]@1
  int dwGuildSeriala; // [sp+90h] [bp+18h]@1
  int dwCharacSeriala; // [sp+98h] [bp+20h]@1

  dwCharacSeriala = dwCharacSerial;
  dwGuildSeriala = dwGuildSerial;
  pkPlayera = pkPlayer;
  v21 = this;
  v4 = &v9;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( dwGuildSerial == -1 )
  {
    result = -115;
  }
  else
  {
    v14 = 0i64;
    v14 = GUILD_BATTLE::CNormalGuildBattleManager::GetBattleByGuildSerial(v21, dwGuildSerial);
    if ( v14 )
    {
      v15 = 0;
      v16 = GUILD_BATTLE::CNormalGuildBattle::IsReStart(v14, dwGuildSeriala, dwCharacSeriala);
      if ( v16 <= 0 )
      {
        if ( v16 )
        {
          v15 = GUILD_BATTLE::CNormalGuildBattle::Start(v14, pkPlayera, dwGuildSeriala, dwCharacSeriala);
          if ( v15 )
          {
            v19 = (unsigned __int8)v15;
            v20 = CPlayerDB::GetCharNameW(&pkPlayera->m_Param);
            v8 = GUILD_BATTLE::CGuildBattleLogger::Instance();
            v13 = v19;
            v12 = dwCharacSeriala;
            v11 = dwGuildSeriala;
            v10 = dwCharacSeriala;
            GUILD_BATTLE::CGuildBattleLogger::Log(
              v8,
              "CNormalGuildBattleManager::Start(%s,%u,%u) : pkBattle->Start( %u, %u ) Ret(%u!) Fail!",
              v20,
              (unsigned int)dwGuildSeriala);
            result = v15;
          }
          else
          {
            result = 0;
          }
        }
        else
        {
          v15 = GUILD_BATTLE::CNormalGuildBattle::ReStart(v14, pkPlayera, dwGuildSeriala, dwCharacSeriala);
          if ( v15 )
          {
            v17 = (unsigned __int8)v15;
            v18 = CPlayerDB::GetCharNameW(&pkPlayera->m_Param);
            v7 = GUILD_BATTLE::CGuildBattleLogger::Instance();
            v13 = v17;
            v12 = dwCharacSeriala;
            v11 = dwGuildSeriala;
            v10 = dwCharacSeriala;
            GUILD_BATTLE::CGuildBattleLogger::Log(
              v7,
              "CNormalGuildBattleManager::Start(%s,%u,%u) : pkBattle->ReStart( %u, %u ) Ret(%u!) Fail!",
              v18,
              (unsigned int)dwGuildSeriala);
            result = v15;
          }
          else
          {
            result = 0;
          }
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = -114;
    }
  }
  return result;
}
