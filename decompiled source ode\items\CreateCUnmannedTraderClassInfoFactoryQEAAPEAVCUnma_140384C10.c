/*
 * Function: ?Create@CUnmannedTraderClassInfoFactory@@QEAAPEAVCUnmannedTraderClassInfo@@PEBDK@Z
 * Address: 0x140384C10
 */

CUnmannedTraderClassInfo *__fastcall CUnmannedTraderClassInfoFactory::Create(CUnmannedTraderClassInfoFactory *this, const char *szType, unsigned int dwID)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderClassInfo *v5; // rax@6
  CUnmannedTraderClassInfo **v6; // rax@11
  const char *v7; // rax@11
  __int64 v8; // rax@12
  __int64 v9; // [sp+0h] [bp-98h]@1
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > result; // [sp+28h] [bp-70h]@7
  bool v11; // [sp+44h] [bp-54h]@9
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > v12; // [sp+48h] [bp-50h]@9
  __int64 v13; // [sp+60h] [bp-38h]@12
  CUnmannedTraderClassInfo *v14; // [sp+68h] [bp-30h]@14
  __int64 v15; // [sp+70h] [bp-28h]@4
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v16; // [sp+78h] [bp-20h]@9
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_Right; // [sp+80h] [bp-18h]@9
  CUnmannedTraderClassInfo *v18; // [sp+88h] [bp-10h]@12
  CUnmannedTraderClassInfoFactory *v19; // [sp+A0h] [bp+8h]@1
  char *Str2; // [sp+A8h] [bp+10h]@1
  unsigned int v21; // [sp+B0h] [bp+18h]@1

  v21 = dwID;
  Str2 = (char *)szType;
  v19 = this;
  v3 = &v9;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = -2i64;
  if ( std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::empty(&v19->m_vecTable)
    || v21 == -1 )
  {
    v5 = 0i64;
  }
  else
  {
    std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::begin(&v19->m_vecTable, &result);
    while ( 1 )
    {
      v16 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::end(
              &v19->m_vecTable,
              &v12);
      _Right = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)v16;
      v11 = std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator!=(
              (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&result._Mycont,
              (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v16->_Mycont);
      std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&v12);
      if ( !v11 )
        break;
      if ( *std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator*(&result) )
      {
        v6 = std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator*(&result);
        v7 = CUnmannedTraderClassInfo::GetTypeName(*v6);
        if ( !strcmp_0(v7, Str2) )
        {
          v18 = *std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator*(&result);
          LODWORD(v8) = ((int (__fastcall *)(CUnmannedTraderClassInfo *, _QWORD))v18->vfptr->Create)(v18, v21);
          v13 = v8;
          std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&result);
          return (CUnmannedTraderClassInfo *)v13;
        }
      }
      std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator++(&result);
    }
    v14 = 0i64;
    std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&result);
    v5 = v14;
  }
  return v5;
}
