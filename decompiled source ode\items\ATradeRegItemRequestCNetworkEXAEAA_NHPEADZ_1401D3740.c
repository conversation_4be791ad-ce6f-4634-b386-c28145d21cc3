/*
 * Function: ?ATradeRegItemRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D3740
 */

char __fastcall CNetworkEX::ATradeRegItemRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@9
  int v7; // eax@10
  char *v8; // rax@11
  char *v9; // rax@13
  CUnmannedTraderController *v10; // rax@14
  __int64 v11; // [sp+0h] [bp-48h]@1
  _a_trade_reg_item_request_clzo *pRequest; // [sp+20h] [bp-28h]@4
  CPlayer *v13; // [sp+28h] [bp-20h]@4
  int v14; // [sp+30h] [bp-18h]@10
  CNetworkEX *v15; // [sp+50h] [bp+8h]@1
  int v16; // [sp+58h] [bp+10h]@1

  v16 = n;
  v15 = this;
  v3 = &v11;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pRequest = (_a_trade_reg_item_request_clzo *)pBuf;
  v13 = &g_Player + n;
  if ( !v13->m_bOper || v13->m_pmTrd.bDTradeMode || v13->m_bCorpse )
  {
    result = 1;
  }
  else if ( pRequest->byItemTableCode < 37 )
  {
    v14 = pRequest->wItemIndex;
    v7 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + pRequest->byItemTableCode);
    if ( v14 < v7 )
    {
      if ( pRequest->byAmount <= 99 )
      {
        v10 = CUnmannedTraderController::Instance();
        CUnmannedTraderController::Regist(v10, v16, pRequest);
        result = 1;
      }
      else
      {
        v9 = CPlayerDB::GetCharNameA(&v13->m_Param);
        CLogFile::Write(&v15->m_LogFile, "odd.. %s: ATradeRegItemRequest().. if(pRecv->byAmount > max_overlap_num)", v9);
        result = 0;
      }
    }
    else
    {
      v8 = CPlayerDB::GetCharNameA(&v13->m_Param);
      CLogFile::Write(
        &v15->m_LogFile,
        "odd.. %s: ATradeRegItemRequest().. if(pRecv->wItemIndex >= g_Main.m_tblItemData[pRecv->byItemTableCode].GetRecordNum())",
        v8);
      result = 0;
    }
  }
  else
  {
    v6 = CPlayerDB::GetCharNameA(&v13->m_Param);
    CLogFile::Write(
      &v15->m_LogFile,
      "odd.. %s: ATradeRegItemRequest().. if(pRecv->byItemTableCode >= item_tbl_num)",
      v6);
    result = 0;
  }
  return result;
}
