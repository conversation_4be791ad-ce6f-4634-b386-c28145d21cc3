/*
 * Function: ?Init@CRadarItemMgr@@QEAAXXZ
 * Address: 0x1402E4A20
 */

void __fastcall CRadarItemMgr::Init(CRadarItemMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CRadarItemMgr *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_bUse = 0;
  v4->m_bPlayerEnd = 0;
  v4->m_bMonEnd = 0;
  memset_0(v4->m_strRadarCode, 0, 0x40ui64);
  v4->m_pDestMap = 0i64;
  v4->m_dwDelayTime = 0;
  v4->m_dwDurTime = 0;
  v4->m_dwStartTime = 0;
  v4->m_pMaster = 0i64;
  v4->m_nPlayerNum = 0;
  v4->m_nMonNum = 0;
  CRadarItemMgr::ResetFlags(v4);
  CRadarItemMgr::ResetUpdate(v4);
  _detected_char_list::init(&v4->m_RadarResult);
}
