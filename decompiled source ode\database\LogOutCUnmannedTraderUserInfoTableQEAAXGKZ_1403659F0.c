/*
 * Function: ?LogOut@CUnmannedTraderUserInfoTable@@QEAAXGK@Z
 * Address: 0x1403659F0
 */

void __fastcall CUnmannedTraderUserInfoTable::LogOut(CUnmannedTraderUserInfoTable *this, unsigned __int16 wInx, unsigned int dwSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfo *v5; // rax@7
  __int64 v6; // [sp+0h] [bp-28h]@1
  CUnmannedTraderUserInfoTable *v7; // [sp+30h] [bp+8h]@1
  unsigned __int16 v8; // [sp+38h] [bp+10h]@1
  unsigned int dwSeriala; // [sp+40h] [bp+18h]@1

  dwSeriala = dwSerial;
  v8 = wInx;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( !std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::empty(&v7->m_veckInfo)
    && std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::size(&v7->m_veckInfo) > v8 )
  {
    v5 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator[](&v7->m_veckInfo, v8);
    CUnmannedTraderUserInfo::LogOut(v5, dwSeriala, v7->m_pkLogger);
  }
}
