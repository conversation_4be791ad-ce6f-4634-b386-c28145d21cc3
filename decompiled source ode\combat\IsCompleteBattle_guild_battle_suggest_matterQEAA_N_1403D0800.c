/*
 * Function: ?IsCompleteBattle@_guild_battle_suggest_matter@@QEAA_NXZ
 * Address: 0x1403D0800
 */

bool __fastcall _guild_battle_suggest_matter::IsCompleteBattle(_guild_battle_suggest_matter *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // [sp+0h] [bp-18h]@1
  _guild_battle_suggest_matter *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  return v5->eState == 4;
}
