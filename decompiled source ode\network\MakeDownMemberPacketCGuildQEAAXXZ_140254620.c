/*
 * Function: ?MakeDownMemberPacket@CGuild@@QEAAXXZ
 * Address: 0x140254620
 */

void __fastcall CGuild::MakeDownMemberPacket(CGuild *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  long double v3; // xmm0_8@4
  CUnmannedTraderTaxRateManager *v4; // rax@4
  CUnmannedTraderTaxRateManager *v5; // rax@5
  __int64 v6; // [sp+0h] [bp-58h]@1
  int v7; // [sp+20h] [bp-38h]@6
  void *Dst; // [sp+28h] [bp-30h]@6
  unsigned __int8 Src; // [sp+34h] [bp-24h]@6
  int j; // [sp+44h] [bp-14h]@6
  _guild_member_info *v11; // [sp+48h] [bp-10h]@9
  CGuild *v12; // [sp+60h] [bp+8h]@1

  v12 = this;
  v1 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12->m_DownPacket_Member->dwGuildSerial = v12->m_dwSerial;
  v12->m_DownPacket_Member->byGuildGrade = v12->m_byGrade;
  v12->m_DownPacket_Member->dwEmblemBack = v12->m_dwEmblemBack;
  v12->m_DownPacket_Member->dwEmblemMark = v12->m_dwEmblemMark;
  v12->m_DownPacket_Member->dDalant = v12->m_dTotalDalant;
  v3 = v12->m_dTotalGold;
  v12->m_DownPacket_Member->dGold = v3;
  v12->m_DownPacket_Member->byCurTax = -1;
  v4 = CUnmannedTraderTaxRateManager::Instance();
  if ( CUnmannedTraderTaxRateManager::IsOwnerGuild(v4, v12->m_byRace, v12->m_dwSerial) )
  {
    v5 = CUnmannedTraderTaxRateManager::Instance();
    CUnmannedTraderTaxRateManager::GetTaxRate(v5, v12->m_byRace);
    v12->m_DownPacket_Member->byCurTax = (signed int)ffloor(*(float *)&v3 * 100.0);
  }
  v12->m_DownPacket_Member->dwTotWin = v12->m_dwGuildBattleTotWin;
  v12->m_DownPacket_Member->dwTotDraw = v12->m_dwGuildBattleTotDraw;
  v12->m_DownPacket_Member->dwTotLose = v12->m_dwGuildBattleTotLose;
  v12->m_DownPacket_Member->bPossibleElectMaster = v12->m_bPossibleElectMaster;
  v12->m_DownPacket_Member->wDataSize = 0;
  v7 = 0;
  Dst = v12->m_DownPacket_Member->sData;
  Src = strlen_0(v12->m_wszName);
  memcpy_0(Dst, &Src, 1ui64);
  Dst = (char *)Dst + 1;
  ++v7;
  memcpy_0(Dst, v12->m_wszName, Src);
  Dst = (char *)Dst + Src;
  v7 += Src;
  memcpy_0(Dst, &v12->m_nMemberNum, 1ui64);
  Dst = (char *)Dst + 1;
  ++v7;
  for ( j = 0; j < 50; ++j )
  {
    v11 = &v12->m_MemberData[j];
    if ( _guild_member_info::IsFill(v11) )
    {
      Src = strlen_0(v11->wszName);
      memcpy_0(Dst, &Src, 1ui64);
      Dst = (char *)Dst + 1;
      ++v7;
      memcpy_0(Dst, v11->wszName, Src);
      Dst = (char *)Dst + Src;
      v7 += Src;
      memcpy_0(Dst, v11, 4ui64);
      Dst = (char *)Dst + 4;
      v7 += 4;
      memcpy_0(Dst, &v11->byLv, 1ui64);
      Dst = (char *)Dst + 1;
      ++v7;
      memcpy_0(Dst, &v11->dwPvpPoint, 4ui64);
      Dst = (char *)Dst + 4;
      v7 += 4;
      memcpy_0(Dst, &v11->byClassInGuild, 1ui64);
      Dst = (char *)Dst + 1;
      ++v7;
      memcpy_0(Dst, &v11->byRank, 1ui64);
      Dst = (char *)Dst + 1;
      ++v7;
    }
  }
  v12->m_DownPacket_Member->wDataSize = v7;
}
