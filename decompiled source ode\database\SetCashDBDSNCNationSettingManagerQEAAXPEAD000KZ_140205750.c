/*
 * Function: ?SetCashDBDSN@CNationSettingManager@@QEAAXPEAD000K@Z
 * Address: 0x140205750
 */

void __fastcall CNationSettingManager::SetCashDBDSN(CNationSettingManager *this, char *szIP, char *szDBName, char *szAccount, char *szPassword, unsigned int dwPort)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-38h]@1
  char *v9; // [sp+20h] [bp-18h]@4
  unsigned int v10; // [sp+28h] [bp-10h]@4
  CNationSettingManager *v11; // [sp+40h] [bp+8h]@1

  v11 = this;
  v6 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -*********;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10 = dwPort;
  v9 = szPassword;
  CNationSettingData::SetCashDBDSN(v11->m_pData, szIP, szDBName, szAccount, szPassword, dwPort);
}
