/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAK_KKV?$allocator@K@std@@@stdext@@YAXPEAK_KAEBKAEAV?$allocator@K@std@@@Z
 * Address: 0x1400019AB
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<unsigned long *,unsigned __int64,unsigned long,std::allocator<unsigned long>>(unsigned int *_First, unsigned __int64 _Count, const unsigned int *_Val, std::allocator<unsigned long> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<unsigned long *,unsigned __int64,unsigned long,std::allocator<unsigned long>>(
    _First,
    _Count,
    _Val,
    _Al);
}
