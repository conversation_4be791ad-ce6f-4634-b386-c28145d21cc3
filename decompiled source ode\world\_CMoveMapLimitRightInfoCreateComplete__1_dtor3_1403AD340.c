/*
 * Function: _CMoveMapLimitRightInfo::CreateComplete_::_1_::dtor$3
 * Address: 0x1403AD340
 */

void __fastcall CMoveMapLimitRightInfo::CreateComplete_::_1_::dtor_3(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>((std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)(a2 + 104));
}
