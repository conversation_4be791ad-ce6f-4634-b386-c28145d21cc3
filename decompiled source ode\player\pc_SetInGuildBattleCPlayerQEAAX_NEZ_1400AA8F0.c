/*
 * Function: ?pc_SetInGuildBattle@CPlayer@@QEAAX_NE@Z
 * Address: 0x1400AA8F0
 */

void __fastcall CPlayer::pc_SetInGuildBattle(CPlayer *this, bool bInGuildBattle, char byColorInx)
{
  this->m_bInGuildBattle = bInGuildBattle;
  this->m_byGuildBattleColorInx = byColorInx;
  this->m_bufShapeAll.byColor = this->m_byGuildBattleColorInx;
  if ( !this->m_bInGuildBattle )
    this->m_bTakeGravityStone = 0;
}
