/*
 * Function: ?LoadDatabase@CandidateMgr@@QEAA_NXZ
 * Address: 0x1402B1730
 */

char __fastcall CandidateMgr::LoadDatabase(CandidateMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  PatriarchElectProcessor *v3; // rax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int dwSerial; // [sp+20h] [bp-18h]@4
  int j; // [sp+24h] [bp-14h]@5
  int k; // [sp+28h] [bp-10h]@10
  int l; // [sp+2Ch] [bp-Ch]@12
  CandidateMgr *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CandidateMgr::InitCandidate(v10);
  v3 = PatriarchElectProcessor::Instance();
  dwSerial = PatriarchElectProcessor::GetElectSerial(v3);
  if ( dwSerial )
  {
    for ( j = 0; j < 3; ++j )
    {
      if ( CRFWorldDatabase::Select_PatriarchCandidate(pkDB, dwSerial, j, v10->_kCandidate[j]) == 1 )
        return 0;
    }
  }
  memcpy_0(v10->_kCandidate_old[0], v10->_kCandidate[0], 88i64 * v10->_nMaxNum);
  memcpy_0(v10->_kCandidate_old[1], v10->_kCandidate[1], 88i64 * v10->_nMaxNum);
  memcpy_0(v10->_kCandidate_old[2], v10->_kCandidate[2], 88i64 * v10->_nMaxNum);
  for ( k = 0; k < 3; ++k )
  {
    for ( l = 0; l < 500; ++l )
    {
      if ( v10->_kCandidate[k][l].byRace != 255 )
      {
        if ( v10->_kCandidate[k][l].eStatus == 1 )
        {
          v10->_kCandidate[k][l].bLoad = 1;
          v10->_pkCandidateLink_1st[k][v10->_nCandidateCnt_1st[k]++] = &v10->_kCandidate[k][l];
          if ( CRFWorldDatabase::Select_PatriarchWinCnt(
                 pkDB,
                 k,
                 v10->_kCandidate[k][l].dwAvatorSerial,
                 &v10->_kCandidate[k][l].dwWinCnt) == 1 )
            return 0;
        }
        else if ( v10->_kCandidate[k][l].eStatus == 2 )
        {
          v10->_kCandidate[k][l].bLoad = 1;
          v10->_pkCandidateLink_2st[(signed __int64)k][v10->_nCandidateCnt_2st[k]++] = &v10->_kCandidate[k][l];
        }
        if ( v10->_kCandidate[k][l].eClassType < 9 )
        {
          v10->_kCandidate[k][l].bLoad = 1;
          v10->_pkLeader[k][v10->_kCandidate[k][l].eClassType] = &v10->_kCandidate[k][l];
        }
      }
    }
  }
  return 1;
}
