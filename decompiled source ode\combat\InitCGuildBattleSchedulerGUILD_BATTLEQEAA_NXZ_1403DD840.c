/*
 * Function: ?Init@CGuildBattleScheduler@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403DD840
 */

bool __fastcall GUILD_BATTLE::CGuildBattleScheduler::Init(GUILD_BATTLE::CGuildBattleScheduler *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleFieldList *v3; // rax@4
  GUILD_BATTLE::CGuildBattleSchedulePool *v4; // rax@4
  bool result; // al@5
  GUILD_BATTLE::CGuildBattleScheduleManager *v6; // rax@6
  __int64 v7; // [sp+0h] [bp-38h]@1
  unsigned int uiMapCnt; // [sp+20h] [bp-18h]@4

  v1 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
  uiMapCnt = GUILD_BATTLE::CNormalGuildBattleFieldList::GetMapCnt(v3);
  v4 = GUILD_BATTLE::CGuildBattleSchedulePool::Instance();
  if ( GUILD_BATTLE::CGuildBattleSchedulePool::Init(v4, uiMapCnt) )
  {
    v6 = GUILD_BATTLE::CGuildBattleScheduleManager::Instance();
    result = GUILD_BATTLE::CGuildBattleScheduleManager::Init(v6, uiMapCnt);
  }
  else
  {
    result = 0;
  }
  return result;
}
