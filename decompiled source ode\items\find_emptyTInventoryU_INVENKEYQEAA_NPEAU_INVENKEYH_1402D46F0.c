/*
 * Function: ?find_empty@?$TInventory@U_INVENKEY@@@@QEAA_NPEAU_INVENKEY@@HAEAH1@Z
 * Address: 0x1402D46F0
 */

char __fastcall TInventory<_INVENKEY>::find_empty(TInventory<_INVENKEY> *this, _INVENKEY *pItem, int nNum, int *nP, int *nS)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-28h]@1
  TInventory<_INVENKEY> *v9; // [sp+30h] [bp+8h]@1
  _INVENKEY *pItema; // [sp+38h] [bp+10h]@1
  int nNuma; // [sp+40h] [bp+18h]@1
  int *v12; // [sp+48h] [bp+20h]@1

  v12 = nP;
  nNuma = nNum;
  pItema = pItem;
  v9 = this;
  v5 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  *nS = -1;
  *nP = -1;
  *nS = TInvenPage<_INVENKEY>::find_empty(&v9->m_pPage[v9->m_nUsingPage], pItem, nNum);
  if ( *nS == -1 )
  {
    while ( *nS == -1 )
    {
      if ( ++*v12 >= v9->m_nMaxPageNum )
        break;
      if ( v9->m_nUsingPage != *v12 )
        *nS = TInvenPage<_INVENKEY>::find_empty(&v9->m_pPage[*v12], pItema, nNuma);
    }
    if ( *nS == -1 )
    {
      *v12 = -1;
      result = 0;
    }
    else
    {
      result = 1;
    }
  }
  else
  {
    *v12 = v9->m_nUsingPage;
    result = 1;
  }
  return result;
}
