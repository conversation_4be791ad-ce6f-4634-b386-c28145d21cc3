/*
 * Function: ?OpenWorldFailureResult@CNetworkEX@@AEAA_NKPEAD@Z
 * Address: 0x1401C0420
 */

char __fastcall CNetworkEX::OpenWorldFailureResult(CNetworkEX *this, unsigned int n, char *pMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  char *szMsg; // [sp+20h] [bp-18h]@4

  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szMsg = pMsg;
  CMainThread::pc_OpenWorldFailureResult(&g_Main, pMsg);
  return 1;
}
