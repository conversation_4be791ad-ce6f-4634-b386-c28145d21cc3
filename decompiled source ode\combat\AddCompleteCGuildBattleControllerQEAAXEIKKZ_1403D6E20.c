/*
 * Function: ?AddComplete@CGuildBattleController@@QEAAXEIKK@Z
 * Address: 0x1403D6E20
 */

void __fastcall CGuildBattleController::AddComplete(CGuildBattleController *this, char byR<PERSON><PERSON>, unsigned int uiMapID, unsigned int dwID, unsigned int dwSLID)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleManager *v7; // rax@4
  GUILD_BATTLE::CPossibleBattleGuildListManager *v8; // rax@5
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager *v9; // rax@5
  GUILD_BATTLE::CGuildBattleScheduleManager *v10; // rax@6
  __int64 v11; // [sp+0h] [bp-28h]@1
  char v12; // [sp+38h] [bp+10h]@1
  unsigned int dwMapID; // [sp+40h] [bp+18h]@1
  unsigned int dwBattleID; // [sp+48h] [bp+20h]@1

  dwBattleID = dwID;
  dwMapID = uiMapID;
  v12 = byResult;
  v5 = &v11;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v7 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
  GUILD_BATTLE::CNormalGuildBattleManager::AddComplete(v7, v12, dwBattleID);
  if ( v12 )
  {
    v10 = GUILD_BATTLE::CGuildBattleScheduleManager::Instance();
    GUILD_BATTLE::CGuildBattleScheduleManager::ClearTommorowScheduleByID(v10, dwMapID, dwBattleID);
  }
  else
  {
    v8 = GUILD_BATTLE::CPossibleBattleGuildListManager::Instance();
    GUILD_BATTLE::CPossibleBattleGuildListManager::UpdateGuildList(v8);
    v9 = GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Instance();
    GUILD_BATTLE::CGuildBattleReservedScheduleListManager::PushDQS(v9, dwMapID, dwSLID);
  }
}
