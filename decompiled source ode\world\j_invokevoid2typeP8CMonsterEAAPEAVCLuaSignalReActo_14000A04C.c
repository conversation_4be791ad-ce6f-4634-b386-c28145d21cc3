/*
 * Function: j_?invoke@?$void2type@P8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@lua_tinker@@SAP8CMonster@@EAAPEAVCLuaSignalReActor@@XZPEAX@Z
 * Address: 0x14000A04C
 */

CLuaSignalReActor *(__cdecl *__fastcall lua_tinker::void2type<CLuaSignalReActor * (CMonster::*)(void)>::invoke(lua_tinker::void2type<CLuaSignalReActor * (__cdecl CMonster::*)(void)> *this, void *ptr))(CMonster *this)
{
  return lua_tinker::void2type<CLuaSignalReActor * (CMonster::*)(void)>::invoke(this, ptr);
}
