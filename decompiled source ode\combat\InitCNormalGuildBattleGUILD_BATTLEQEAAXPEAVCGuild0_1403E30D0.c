/*
 * Function: ?Init@CNormalGuildBattle@GUILD_BATTLE@@QEAAXPEAVCGuild@@0PEAVCNormalGuildBattleField@2@EPEAVCNormalGuildBattleStateList@2@@Z
 * Address: 0x1403E30D0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::Init(GUILD_BATTLE::CNormalGuildBattle *this, CGuild *pk1P, CGuild *pk2P, GUILD_BATTLE::CNormalGuildBattleField *pkField, char byNumber, GUILD_BATTLE::CNormalGuildBattleStateList *pkStateList)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattle *v9; // [sp+30h] [bp+8h]@1
  CGuild *v10; // [sp+40h] [bp+18h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v11; // [sp+48h] [bp+20h]@1

  v11 = pkField;
  v10 = pk2P;
  v9 = this;
  v6 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  GUILD_BATTLE::CNormalGuildBattleGuild::SetGuild(&v9->m_k1P, pk1P);
  GUILD_BATTLE::CNormalGuildBattleGuild::SetGuild(&v9->m_k2P, v10);
  v9->m_pkField = v11;
  v9->m_byGuildBattleNumber = byNumber;
  v9->m_pkStateList = pkStateList;
  v9->m_bInit = 1;
}
