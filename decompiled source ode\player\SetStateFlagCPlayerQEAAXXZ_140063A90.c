/*
 * Function: ?SetStateFlag@CPlayer@@QEAAXXZ
 * Address: 0x140063A90
 */

void __usercall CPlayer::SetStateFlag(CPlayer *this@<rcx>, float a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v4; // rax@36
  CPvpUserAndGuildRankingSystem *v5; // rax@38
  CPvpUserAndGuildRankingSystem *v6; // rax@73
  __int64 v7; // [sp+0h] [bp-58h]@1
  unsigned __int64 v8; // [sp+20h] [bp-38h]@5
  __int64 v9; // [sp+28h] [bp-30h]@5
  int j; // [sp+30h] [bp-28h]@64
  char v11; // [sp+34h] [bp-24h]@73
  int v12; // [sp+38h] [bp-20h]@36
  int v13; // [sp+3Ch] [bp-1Ch]@38
  unsigned int dwSerial; // [sp+40h] [bp-18h]@73
  int v15; // [sp+44h] [bp-14h]@73
  int v16; // [sp+48h] [bp-10h]@73
  CPlayer *v17; // [sp+60h] [bp+8h]@1

  v17 = this;
  v2 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v17->m_pPartyMgr )
  {
    v8 = 0i64;
    v9 = 1i64;
    if ( v17->m_byMoveType == 1 )
      v8 |= v9;
    if ( v17->m_byModeType == 1 )
      v8 |= 2 * v9;
    if ( CCharacter::GetStealth((CCharacter *)&v17->vfptr, 0) )
      v8 |= 4 * v9;
    if ( v17->m_bStun )
      v8 |= 8 * v9;
    if ( CPartyPlayer::IsPartyMode(v17->m_pPartyMgr) )
      v8 |= 16 * v9;
    if ( v17->m_bCorpse || !CPlayerDB::GetHP(&v17->m_Param) )
      v8 |= 32 * v9;
    if ( CPartyPlayer::IsPartyBoss(v17->m_pPartyMgr) )
      v8 |= v9 << 6;
    if ( v17->m_pmTrd.bDTradeMode )
      v8 |= v9 << 7;
    _effect_parameter::GetEff_Plus(&v17->m_EP, 22);
    if ( a2 > 0.0 )
      v8 |= v9 << 8;
    if ( v17->m_bObserver )
      v8 |= v9 << 9;
    if ( CPartyPlayer::IsPartyLock(v17->m_pPartyMgr) )
      v8 |= v9 << 10;
    if ( v17->m_bMineMode )
      v8 |= v9 << 11;
    if ( v17->m_byStandType )
      v8 |= v9 << 13;
    if ( v17->m_bBlockParty )
      v8 |= v9 << 14;
    if ( v17->m_bBlockTrade )
      v8 |= v9 << 15;
    v12 = CPlayerDB::GetRaceCode(&v17->m_Param);
    v4 = CPvpUserAndGuildRankingSystem::Instance();
    if ( v17->m_dwObjSerial == CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v4, v12, 0) )
      v8 |= v9 << 16;
    v13 = CPlayerDB::GetRaceCode(&v17->m_Param);
    v5 = CPvpUserAndGuildRankingSystem::Instance();
    if ( CPvpUserAndGuildRankingSystem::IsRaceViceBoss(v5, v13, v17->m_dwObjSerial) )
      v8 |= v9 << 17;
    if ( _effect_parameter::GetEff_State(&v17->m_EP, 20) )
      v8 |= v9 << 18;
    if ( _effect_parameter::GetEff_State(&v17->m_EP, 21) )
      v8 |= v9 << 19;
    if ( _effect_parameter::GetEff_State(&v17->m_EP, 22) )
      v8 |= v9 << 20;
    if ( v17->m_dwSelfDestructionTime )
      v8 |= v9 << 21;
    if ( CHolyStoneSystem::GetDestroyerSerial(&g_HolySys) == v17->m_dwObjSerial )
      v8 |= v9 << 22;
    if ( v17->m_bTakeGravityStone )
      v8 |= v9 << 23;
    if ( v17->m_bInGuildBattle )
      v8 |= v9 << 24;
    if ( v17->m_bTakeSoccerBall )
      v8 |= v9 << 25;
    if ( v17->m_byMoveType == 2 )
      v8 |= v9 << 26;
    if ( v17->m_nChaosMode == 1 )
      v8 |= v9 << 27;
    if ( v17->m_nChaosMode == 2 )
      v8 |= v9 << 28;
    if ( v17->m_bSnowMan )
      v8 |= v9 << 30;
    for ( j = 0; j < 2; ++j )
    {
      if ( v17->m_PotionParam.m_ContCommonPotionData[j].m_dwPotionEffectIndex == 237 )
        v8 |= v9 << 29;
      if ( v17->m_PotionParam.m_ContCommonPotionData[j].m_dwPotionEffectIndex == 238 )
        v8 |= v9 << 31;
    }
    if ( CPlayer::IsPunished(v17, 1, 0) )
      v8 |= v9 << 32;
    dwSerial = CPlayerDB::GetCharSerial(&v17->m_Param);
    v15 = CPlayerDB::GetRaceCode(&v17->m_Param);
    v6 = CPvpUserAndGuildRankingSystem::Instance();
    v11 = CPvpUserAndGuildRankingSystem::GetBossType(v6, v15, dwSerial);
    v16 = (unsigned __int8)v11 - 1;
    switch ( v11 )
    {
      case 1:
        v8 |= v9 << 33;
        break;
      case 2:
        v8 |= v9 << 34;
        break;
      case 3:
        v8 |= v9 << 35;
        break;
      case 4:
        v8 |= v9 << 36;
        break;
      case 5:
        v8 |= v9 << 37;
        break;
      case 6:
        v8 |= v9 << 38;
        break;
      case 7:
        v8 |= v9 << 39;
        break;
      case 8:
        v8 |= v9 << 40;
        break;
      default:
        break;
    }
    if ( v17->m_bAfterEffect )
      v8 |= v9 << 41;
    if ( v17->m_Param.m_pGuild && v17->m_Param.m_byClassInGuild == 2 )
      v8 |= v9 << 42;
    if ( v17->m_tmrAuraSkill.m_bOper )
      v8 |= v9 << 43;
    _effect_parameter::GetEff_Have(&v17->m_EP, 50);
    if ( a2 > 0.0 )
      v8 |= v9 << 44;
    _effect_parameter::GetEff_Have(&v17->m_EP, 58);
    if ( a2 > 0.0 )
      v8 |= v9 << 45;
    if ( CPlayer::IsLastAttBuff(v17) )
      v8 |= v9 << 46;
    if ( v17->m_byBattleTournamentGrade != 255 )
    {
      if ( v17->m_byBattleTournamentGrade )
      {
        if ( v17->m_byBattleTournamentGrade == 1 )
        {
          v8 |= v9 << 48;
        }
        else if ( v17->m_byBattleTournamentGrade == 2 )
        {
          v8 |= v9 << 49;
        }
      }
      else
      {
        v8 |= v9 << 47;
      }
    }
    if ( CCharacter::GetInvisible((CCharacter *)&v17->vfptr) )
      v8 |= v9 << 51;
    if ( _effect_parameter::GetEff_State(&v17->m_EP, 28) )
      v8 |= v9 << 52;
    v17->m_dwLastState = v8;
  }
}
