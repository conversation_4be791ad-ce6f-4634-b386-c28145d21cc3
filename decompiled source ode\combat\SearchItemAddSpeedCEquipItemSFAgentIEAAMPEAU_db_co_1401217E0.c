/*
 * Function: ?SearchItemAddSpeed@CEquipItemSFAgent@@IEAAMPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1401217E0
 */

float __fastcall CEquipItemSFAgent::SearchItemAddSpeed(CEquipItemSFAgent *this, _STORAGE_LIST::_db_con *pItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float result; // xmm0_4@5
  __int64 v5; // [sp+0h] [bp-48h]@1
  _ITEM_EFFECT *v6; // [sp+30h] [bp-18h]@6
  int j; // [sp+38h] [bp-10h]@8
  CEquipItemSFAgent *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pItem )
  {
    v6 = CPlayer::_GetItemEffect(v8->m_pMaster, pItem);
    if ( v6 )
    {
      for ( j = 0; j < 4; ++j )
      {
        if ( v6[j].nEffectCode == 12 )
          return v6[j].fEffectValue;
      }
      result = 0.0;
    }
    else
    {
      result = 0.0;
    }
  }
  else
  {
    result = 0.0;
  }
  return result;
}
