/*
 * Function: ??0?$_Vector_iterator@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEAA@AEBV01@@Z
 * Address: 0x1403B0B90
 */

void __fastcall std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(
    (std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *)&v5->_Mycont,
    (std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *)&__that->_Mycont);
}
