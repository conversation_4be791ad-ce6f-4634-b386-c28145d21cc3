/*
 * Function: ?check_machine@AutominePersonalMgr@@QEAA_NHKPEAVAutominePersonal@@PEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1402DEC40
 */

char __fastcall AutominePersonalMgr::check_machine(AutominePersonalMgr *this, int n, unsigned int dwSerial, AutominePersonal *pMachine, _STORAGE_LIST::_db_con *pcitem)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-28h]@1
  AutominePersonalMgr *v9; // [sp+30h] [bp+8h]@1
  int na; // [sp+38h] [bp+10h]@1
  unsigned int v11; // [sp+40h] [bp+18h]@1
  AutominePersonal *v12; // [sp+48h] [bp+20h]@1

  v12 = pMachine;
  v11 = dwSerial;
  na = n;
  v9 = this;
  v5 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( pMachine )
  {
    if ( AutominePersonal::is_installed(pMachine) )
    {
      if ( AutominePersonal::get_objserial(v12) == v11 )
      {
        if ( pcitem )
        {
          if ( AutominePersonal::get_item(v12) == pcitem )
          {
            result = 1;
          }
          else
          {
            AutominePersonalMgr::send_ecode(v9, na, 5);
            result = 0;
          }
        }
        else
        {
          result = 1;
        }
      }
      else
      {
        AutominePersonalMgr::send_ecode(v9, na, 18);
        result = 0;
      }
    }
    else
    {
      AutominePersonalMgr::send_ecode(v9, na, 4);
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(&v9->m_logError, "AutominePersonalMgr::check_machine() >> not exist machine");
    result = 0;
  }
  return result;
}
