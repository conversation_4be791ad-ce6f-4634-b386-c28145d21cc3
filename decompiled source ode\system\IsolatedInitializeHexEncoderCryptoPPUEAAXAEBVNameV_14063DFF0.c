/*
 * Function: ?IsolatedInitialize@HexEncoder@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x14063DFF0
 */

void __fastcall CryptoPP::HexEncoder::IsolatedInitialize(CryptoPP::HexEncoder *this, const struct CryptoPP::NameValuePairs *a2)
{
  const char *v2; // rax@1
  CryptoPP::Name *v3; // rcx@4
  CryptoPP::Name *v4; // rcx@4
  CryptoPP::CombinedNameValuePairs v5; // [sp+38h] [bp-F0h]@4
  const unsigned __int8 *v6; // [sp+50h] [bp-D8h]@4
  char v7; // [sp+58h] [bp-D0h]@4
  int v8; // [sp+80h] [bp-A8h]@1
  char v9; // [sp+88h] [bp-A0h]@4
  __int64 v10; // [sp+D0h] [bp-58h]@1
  const unsigned __int8 *v11; // [sp+D8h] [bp-50h]@2
  __int64 v12; // [sp+E0h] [bp-48h]@4
  const char *v13; // [sp+E8h] [bp-40h]@4
  __int64 v14; // [sp+F0h] [bp-38h]@4
  __int64 v15; // [sp+F8h] [bp-30h]@4
  struct CryptoPP::NameValuePairs *v16; // [sp+100h] [bp-28h]@4
  struct CryptoPP::NameValuePairs *v17; // [sp+108h] [bp-20h]@4
  __int64 v18; // [sp+110h] [bp-18h]@4
  __int64 v19; // [sp+118h] [bp-10h]@4
  CryptoPP::HexEncoder *v20; // [sp+130h] [bp+8h]@1
  struct CryptoPP::NameValuePairs *v21; // [sp+138h] [bp+10h]@1

  v21 = (struct CryptoPP::NameValuePairs *)a2;
  v20 = this;
  v10 = -2i64;
  v2 = CryptoPP::Name::Uppercase(this);
  v8 = 4;
  if ( CryptoPP::NameValuePairs::GetValueWithDefault<bool>((__int64)v21, (__int64)v2, 1) )
    v11 = "0123456789ABCDEF";
  else
    v11 = (const unsigned __int8 *)"0123456789abcdef";
  v6 = v11;
  v12 = CryptoPP::member_ptr<CryptoPP::BufferedTransformation>::operator->((__int64)v20 + 136);
  v13 = CryptoPP::Name::Log2Base(v3);
  CryptoPP::Name::EncodingLookupArray(v4);
  v14 = CryptoPP::MakeParameters<unsigned char const *>((__int64)&v7);
  v15 = v14;
  v16 = (struct CryptoPP::NameValuePairs *)CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,unsigned char const *>::operator()<int>(v14);
  v17 = v16;
  v18 = CryptoPP::CombinedNameValuePairs::CombinedNameValuePairs(&v5, v21, v16);
  v19 = v18;
  (*(void (__fastcall **)(__int64, __int64, signed __int64))(*(_QWORD *)v12 + 80i64))(v12, v18, 0xFFFFFFFFi64);
  CryptoPP::CombinedNameValuePairs::~CombinedNameValuePairs(&v5);
  CryptoPP::AlgorithmParameters<CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,unsigned char const *>,int>::~AlgorithmParameters<CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,unsigned char const *>,int>(&v9);
  CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,unsigned char const *>::~AlgorithmParameters<CryptoPP::NullNameValuePairs,unsigned char const *>((__int64)&v7);
}
