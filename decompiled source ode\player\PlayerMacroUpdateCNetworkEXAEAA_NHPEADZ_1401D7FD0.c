/*
 * Function: ?PlayerMacroUpdate@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D7FD0
 */

char __fastcall CNetworkEX::PlayerMacroUpdate(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  CPlayer *v7; // [sp+20h] [bp-18h]@4

  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = &g_Player + n;
  if ( v7->m_bOper )
  {
    CPlayer::pc_MacroUpdate(v7, pBuf);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
