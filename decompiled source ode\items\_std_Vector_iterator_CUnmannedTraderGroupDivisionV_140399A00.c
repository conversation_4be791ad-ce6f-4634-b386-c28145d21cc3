/*
 * Function: _std::_Vector_iterator_CUnmannedTraderGroupDivisionVersionInfo_std::allocator_CUnmannedTraderGroupDivisionVersionInfo___::operator__::_1_::dtor$1_0
 * Address: 0x140399A00
 */

void __fastcall std::_Vector_iterator_CUnmannedTraderGroupDivisionVersionInfo_std::allocator_CUnmannedTraderGroupDivisionVersionInfo___::operator__::_1_::dtor_1_0(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 68) & 1 )
  {
    *(_DWORD *)(a2 + 68) &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::~_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(*(std::_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > **)(a2 + 104));
  }
}
