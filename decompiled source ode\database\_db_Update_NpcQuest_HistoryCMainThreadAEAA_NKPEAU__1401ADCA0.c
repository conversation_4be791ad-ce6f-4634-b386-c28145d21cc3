/*
 * Function: ?_db_Update_NpcQuest_History@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD@Z
 * Address: 0x1401ADCA0
 */

char __fastcall CMainThread::_db_Update_NpcQuest_History(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *pSzQuery)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // edx@8
  size_t v8; // rax@10
  __int64 v10; // [sp+0h] [bp-588h]@1
  int v11; // [sp+20h] [bp-568h]@8
  int v12; // [sp+28h] [bp-560h]@8
  int v13; // [sp+30h] [bp-558h]@8
  unsigned int v14; // [sp+38h] [bp-550h]@8
  char Source; // [sp+50h] [bp-538h]@4
  char v16; // [sp+51h] [bp-537h]@4
  char *Dest; // [sp+558h] [bp-30h]@4
  size_t Size; // [sp+560h] [bp-28h]@4
  int v19; // [sp+570h] [bp-18h]@8
  unsigned __int64 v20; // [sp+578h] [bp-10h]@4
  unsigned int v21; // [sp+598h] [bp+10h]@1
  _AVATOR_DATA *v22; // [sp+5A0h] [bp+18h]@1
  _AVATOR_DATA *v23; // [sp+5A8h] [bp+20h]@1

  v23 = pOldData;
  v22 = pNewData;
  v21 = dwSerial;
  v5 = &v10;
  for ( i = 352i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v20 = (unsigned __int64)&v10 ^ _security_cookie;
  Source = 0;
  memset(&v16, 0, 0x4FFui64);
  Dest = pSzQuery;
  strcpy_0(pSzQuery, "UPDATE tbl_npc_quest_history SET ");
  for ( Size = (unsigned int)strlen_0(Dest); SHIDWORD(Size) < 70; ++HIDWORD(Size) )
  {
    if ( v22->dbQuest.m_History[SHIDWORD(Size)].byLevel != v23->dbQuest.m_History[SHIDWORD(Size)].byLevel
      || strncmp(
           v22->dbQuest.m_History[SHIDWORD(Size)].szQuestCode,
           v23->dbQuest.m_History[SHIDWORD(Size)].szQuestCode,
           7ui64) )
    {
      v7 = v22->dbQuest.m_History[SHIDWORD(Size)].byLevel;
      v19 = HIDWORD(Size) + 1;
      v14 = v22->dbQuest.m_History[SHIDWORD(Size)].dwEventEndTime;
      v13 = HIDWORD(Size) + 1;
      v12 = v7;
      v11 = HIDWORD(Size) + 1;
      sprintf(
        &Source,
        "Code%d='%s',Level%d=%d ,Time%d=%d,",
        (unsigned int)(HIDWORD(Size) + 1),
        &v22->dbQuest.m_History[SHIDWORD(Size)]);
      strcat_0(Dest, &Source);
    }
  }
  v8 = strlen_0(Dest);
  if ( v8 <= (unsigned int)Size )
  {
    memset_0(Dest, 0, (unsigned int)Size);
  }
  else
  {
    sprintf(&Source, "WHERE Serial=%d", v21);
    Dest[strlen_0(Dest) - 1] = 32;
    strcat_0(Dest, &Source);
  }
  return 1;
}
