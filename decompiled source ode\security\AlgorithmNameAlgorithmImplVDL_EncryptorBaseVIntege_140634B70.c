/*
 * Function: ?AlgorithmName@?$AlgorithmImpl@V?$DL_EncryptorBase@VInteger@CryptoPP@@@CryptoPP@@U?$DLIES@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@CryptoPP@@$00@2@@CryptoPP@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
 * Address: 0x140634B70
 */

__int64 __fastcall CryptoPP::AlgorithmImpl<CryptoPP::DL_EncryptorBase<CryptoPP::Integer>,CryptoPP::DLIES<CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,1>>::AlgorithmName(__int64 a1, __int64 a2)
{
  __int64 v3; // [sp+48h] [bp+10h]@1

  v3 = a2;
  CryptoPP::DLIES<CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,1>::StaticAlgorithmName(a2);
  return v3;
}
