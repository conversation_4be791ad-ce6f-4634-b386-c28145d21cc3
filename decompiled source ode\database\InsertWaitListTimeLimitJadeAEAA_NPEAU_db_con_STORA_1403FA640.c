/*
 * Function: ?InsertWaitList@TimeLimitJade@@AEAA_NPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1403FA640
 */

bool __fastcall TimeLimitJade::InsertWaitList(TimeLimitJade *this, _STORAGE_LIST::_db_con *pkItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  TimeLimitJade::WaitCell *v5; // rax@9
  __int64 v6; // [sp+0h] [bp-48h]@1
  _base_fld *v7; // [sp+20h] [bp-28h]@4
  TimeLimitJade::WaitCell v8; // [sp+28h] [bp-20h]@9
  TimeLimitJade *v9; // [sp+50h] [bp+8h]@1
  _STORAGE_LIST::_db_con *pItem; // [sp+58h] [bp+10h]@1

  pItem = pkItem;
  v9 = this;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pkItem->m_byTableCode, pkItem->m_wItemIndex);
  if ( v7 )
  {
    if ( *(_DWORD *)&v7[5].m_strCode[44] >= 0 || *(_DWORD *)&v7[5].m_strCode[44] <= 23 )
    {
      TimeLimitJade::WaitCell::WaitCell(
        &v8,
        pItem,
        *(_WORD *)&v7[5].m_strCode[44],
        60 * *(_DWORD *)&v7[5].m_strCode[48]);
      result = ListHeap<TimeLimitJade::WaitCell>::push(&v9->_heapWaitRow, v5);
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
