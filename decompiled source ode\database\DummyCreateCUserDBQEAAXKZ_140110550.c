/*
 * Function: ?DummyCreate@CUserDB@@QEAAXK@Z
 * Address: 0x140110550
 */

void __fastcall CUserDB::DummyCreate(CUserDB *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CUserDB *v5; // [sp+30h] [bp+8h]@1
  unsigned int v6; // [sp+38h] [bp+10h]@1

  v6 = dwSerial;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CUserDB::Init(v5, 0);
  v5->m_bActive = 1;
  v5->m_dwSerial = v6;
}
