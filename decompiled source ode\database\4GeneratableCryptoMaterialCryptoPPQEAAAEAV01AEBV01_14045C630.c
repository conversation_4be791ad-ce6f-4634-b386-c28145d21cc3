/*
 * Function: ??4GeneratableCryptoMaterial@CryptoPP@@QEAAAEAV01@AEBV01@@Z
 * Address: 0x14045C630
 */

CryptoPP::GeneratableCryptoMaterial *__fastcall CryptoPP::GeneratableCryptoMaterial::operator=(CryptoPP::GeneratableCryptoMaterial *this, CryptoPP::GeneratableCryptoMaterial *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  CryptoPP::CryptoMaterial *__thata; // [sp+20h] [bp-18h]@5
  CryptoPP::GeneratableCryptoMaterial *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( __that )
    __thata = (CryptoPP::CryptoMaterial *)&__that->gap8[*(_DWORD *)(*(_QWORD *)&__that->gap8[0] + 4i64)];
  else
    __thata = 0i64;
  CryptoPP::CryptoMaterial::operator=(
    (CryptoPP::CryptoMaterial *)&v7->gap8[*(_DWORD *)(*(_QWORD *)&v7->gap8[0] + 4i64)],
    __thata);
  return v7;
}
