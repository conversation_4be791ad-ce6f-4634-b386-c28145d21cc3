/*
 * Function: ?SendMsg_AttackActEffect@CCharacter@@QEAAXEPEAV1@@Z
 * Address: 0x140176F40
 */

void __fastcall CCharacter::SendMsg_AttackActEffect(CCharacter *this, char byActEffect, CCharacter *pDamer)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  char v7; // [sp+39h] [bp-4Fh]@4
  unsigned int v8; // [sp+3Ah] [bp-4Eh]@4
  char v9; // [sp+3Eh] [bp-4Ah]@4
  unsigned int v10; // [sp+3Fh] [bp-49h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v12; // [sp+65h] [bp-23h]@4
  CCharacter *v13; // [sp+90h] [bp+8h]@1

  v13 = this;
  v3 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szMsg = byActEffect;
  v7 = v13->m_ObjID.m_byID;
  v8 = v13->m_dwObjSerial;
  v9 = pDamer->m_ObjID.m_byID;
  v10 = pDamer->m_dwObjSerial;
  pbyType = 5;
  v12 = 24;
  CGameObject::CircleReport((CGameObject *)&v13->vfptr, &pbyType, &szMsg, 11, 1);
}
