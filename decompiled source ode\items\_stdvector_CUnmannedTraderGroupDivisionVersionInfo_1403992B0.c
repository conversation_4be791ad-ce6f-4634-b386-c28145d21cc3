/*
 * Function: _std::vector_CUnmannedTraderGroupDivisionVersionInfo_std::allocator_CUnmannedTraderGroupDivisionVersionInfo___::_Insert_n_::_1_::catch$0
 * Address: 0x1403992B0
 */

void __fastcall __noreturn std::vector_CUnmannedTraderGroupDivisionVersionInfo_std::allocator_CUnmannedTraderGroupDivisionVersionInfo___::_Insert_n_::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1

  v2 = a2;
  std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Destroy(
    *(std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > **)(a2 + 208),
    *(CUnmannedTraderGroupDivisionVersionInfo **)(a2 + 112),
    *(CUnmannedTraderGroupDivisionVersionInfo **)(a2 + 120));
  std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::deallocate(
    (std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *)(*(_QWORD *)(v2 + 208) + 8i64),
    *(CUnmannedTraderGroupDivisionVersionInfo **)(v2 + 112),
    *(_QWORD *)(v2 + 104));
  CxxThrowException_0(0i64, 0i64);
}
