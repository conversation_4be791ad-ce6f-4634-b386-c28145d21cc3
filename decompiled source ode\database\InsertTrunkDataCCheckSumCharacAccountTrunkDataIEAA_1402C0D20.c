/*
 * Function: ?InsertTrunkData@CCheckSumCharacAccountTrunkData@@IEAA_NPEAVCRFWorldDatabase@@@Z
 * Address: 0x1402C0D20
 */

bool __fastcall CCheckSumCharacAccountTrunkData::InsertTrunkData(CCheckSumCharacAccountTrunkData *this, CRFWorldDatabase *pkDB)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-98h]@1
  long double pVal; // [sp+28h] [bp-70h]@6
  __int64 v7; // [sp+30h] [bp-68h]@6
  __int64 v8; // [sp+38h] [bp-60h]@6
  __int64 v9; // [sp+40h] [bp-58h]@6
  __int64 v10; // [sp+48h] [bp-50h]@6
  __int64 v11; // [sp+50h] [bp-48h]@6
  char v12; // [sp+74h] [bp-24h]@8
  CCheckSumCharacAccountTrunkData *v13; // [sp+A0h] [bp+8h]@1
  CRFWorldDatabase *v14; // [sp+A8h] [bp+10h]@1

  v14 = pkDB;
  v13 = this;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pkDB )
  {
    pVal = 0.0;
    v7 = 0i64;
    v8 = 0i64;
    v9 = 0i64;
    v10 = 0i64;
    v11 = 0i64;
    if ( CRFWorldDatabase::Select_TrunkMoney(pkDB, v13->m_dwAccountSerial, &pVal) )
    {
      CCheckSumCharacTrunkConverter::ConvertTrunk((CCheckSumCharacTrunkConverter *)&v12, v13->m_dwAccountSerial, &pVal);
      result = CRFWorldDatabase::Insert_AnimusData(v14, v13->m_dwAccountSerial, &pVal);
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
