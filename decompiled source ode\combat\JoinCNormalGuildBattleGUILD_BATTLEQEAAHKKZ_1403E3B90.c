/*
 * Function: ?Join@CNormalGuildBattle@GUILD_BATTLE@@QEAAHKK@Z
 * Address: 0x1403E3B90
 */

signed __int64 __fastcall GUILD_BATTLE::CNormalGuildBattle::Join(GUILD_BATTLE::CNormalGuildBattle *this, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // al@5
  signed __int64 result; // rax@8
  char v7; // al@12
  __int64 v8; // [sp+0h] [bp-68h]@1
  GUILD_BATTLE::CNormalGuildBattleLogger *kLogger; // [sp+20h] [bp-48h]@5
  unsigned int v10; // [sp+30h] [bp-38h]@4
  int iMemberInx; // [sp+44h] [bp-24h]@4
  __int64 v12; // [sp+58h] [bp-10h]@4
  GUILD_BATTLE::CNormalGuildBattle *v13; // [sp+70h] [bp+8h]@1
  unsigned int v14; // [sp+78h] [bp+10h]@1
  unsigned int dwSerial; // [sp+80h] [bp+18h]@1

  dwSerial = dwCharacSerial;
  v14 = dwGuildSerial;
  v13 = this;
  v3 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = 0;
  iMemberInx = -1;
  v12 = 0i64;
  if ( dwGuildSerial == GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v13->m_k1P) )
  {
    kLogger = &v13->m_kLogger;
    v5 = GUILD_BATTLE::CNormalGuildBattleGuild::Join(
           &v13->m_k1P,
           dwSerial,
           v13->m_byGuildBattleNumber,
           &iMemberInx,
           &v13->m_kLogger);
    v10 = (unsigned __int8)v5;
    if ( v5 || !GUILD_BATTLE::CNormalGuildBattle::IsInBattle(v13) )
    {
      result = v10;
    }
    else
    {
      kLogger = &v13->m_kLogger;
      if ( GUILD_BATTLE::CNormalGuildBattleGuild::MoveMember(
             &v13->m_k1P,
             iMemberInx,
             v13->m_dwID,
             v13->m_pkField,
             &v13->m_kLogger) )
      {
        GUILD_BATTLE::CNormalGuildBattleGuild::SendStartNotifyCommitteeMemberPosition(&v13->m_k1P, iMemberInx);
        result = 0xFFFFFFFFi64;
      }
      else
      {
        GUILD_BATTLE::CNormalGuildBattleLogger::Log(
          &v13->m_kLogger,
          "CNormalGuildBattle::Join( %u, %u ) : m_k1P.MoveMember( %d, %u, m_pkField ) Fail!",
          (unsigned int)iMemberInx,
          v13->m_dwID);
        result = 110i64;
      }
    }
  }
  else if ( v14 == GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v13->m_k2P) )
  {
    kLogger = &v13->m_kLogger;
    v7 = GUILD_BATTLE::CNormalGuildBattleGuild::Join(
           &v13->m_k2P,
           dwSerial,
           v13->m_byGuildBattleNumber,
           &iMemberInx,
           &v13->m_kLogger);
    v10 = (unsigned __int8)v7;
    if ( v7 || !GUILD_BATTLE::CNormalGuildBattle::IsInBattle(v13) )
    {
      result = v10;
    }
    else
    {
      kLogger = &v13->m_kLogger;
      if ( GUILD_BATTLE::CNormalGuildBattleGuild::MoveMember(
             &v13->m_k2P,
             iMemberInx,
             v13->m_dwID,
             v13->m_pkField,
             &v13->m_kLogger) )
      {
        GUILD_BATTLE::CNormalGuildBattleGuild::SendStartNotifyCommitteeMemberPosition(&v13->m_k2P, iMemberInx);
        result = 0xFFFFFFFFi64;
      }
      else
      {
        GUILD_BATTLE::CNormalGuildBattleLogger::Log(
          &v13->m_kLogger,
          "CNormalGuildBattle::Join( %u, %u ) : m_k2P.MoveMember( %d, %u, m_pkField ) Fail!",
          (unsigned int)iMemberInx,
          v13->m_dwID);
        result = 110i64;
      }
    }
  }
  else
  {
    result = 141i64;
  }
  return result;
}
