/*
 * Function: ?Check_Event_Status@CActionPointSystemMgr@@QEAAXXZ
 * Address: 0x140411380
 */

void __fastcall CActionPointSystemMgr::Check_Event_Status(CActionPointSystemMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-58h]@1
  int j; // [sp+20h] [bp-38h]@4
  char v5; // [sp+24h] [bp-34h]@6
  __time32_t Time; // [sp+34h] [bp-24h]@6
  char v7; // [sp+44h] [bp-14h]@6
  CActionPointSystemMgr *v8; // [sp+60h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 3; ++j )
  {
    v5 = CActionPointSystemMgr::GetEventStatus(v8, j);
    _time32(&Time);
    v7 = v5;
    if ( v5 )
    {
      if ( v7 == 1 )
      {
        if ( Time >= v8->m_st_ini_list[j].m_EventTime[0] )
          CActionPointSystemMgr::SetEventStatus(v8, j, 2);
      }
      else if ( v7 == 2 )
      {
        if ( v8->m_st_ini_list[j].m_EventTime[1] <= Time )
          CActionPointSystemMgr::SetEventStatus(v8, j, 3);
      }
      else if ( v7 == 3 && v8->m_st_ini_list[j].m_EventTime[1] <= Time )
      {
        CActionPointSystemMgr::SetEventStatus(v8, j, 3);
      }
    }
    else if ( !v8->m_st_ini_list[j].m_bUse_event )
    {
      CActionPointSystemMgr::SetEventStatus(v8, j, 0);
    }
  }
}
