/*
 * Function: ??$_Sort_heap@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@@std@@YAXV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@0@Z
 * Address: 0x1406045D0
 */

int __fastcall std::_Sort_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(__int64 a1, __int64 a2)
{
  signed __int64 v2; // rax@2
  char v4; // [sp+20h] [bp-78h]@3
  char *v5; // [sp+40h] [bp-58h]@3
  char v6; // [sp+48h] [bp-50h]@3
  char *v7; // [sp+68h] [bp-30h]@3
  __int64 v8; // [sp+70h] [bp-28h]@1
  __int64 v9; // [sp+78h] [bp-20h]@3
  __int64 v10; // [sp+80h] [bp-18h]@3
  __int64 v11; // [sp+88h] [bp-10h]@3
  __int64 v12; // [sp+A8h] [bp+10h]@1

  v12 = a2;
  v8 = -2i64;
  while ( 1 )
  {
    LODWORD(v2) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-();
    if ( v2 <= 1 )
      break;
    v5 = &v4;
    v7 = &v6;
    v9 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)&v4);
    v10 = v9;
    v11 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v7);
    std::pop_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
      v11,
      v10);
    std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator--(v12);
  }
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
}
