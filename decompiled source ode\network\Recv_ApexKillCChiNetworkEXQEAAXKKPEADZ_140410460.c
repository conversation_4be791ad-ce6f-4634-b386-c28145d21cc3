/*
 * Function: ?Recv_ApexKill@CChiNetworkEX@@QEAAXKKPEAD@Z
 * Address: 0x140410460
 */

void __fastcall CChiNetworkEX::Recv_ApexKill(CChiNetworkEX *this, unsigned int dwSID, unsigned int dwRecvSize, char *pMsg)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CAsyncLogger *v6; // rax@5
  CAsyncLogger *v7; // rax@8
  CAsyncLogger *v8; // rax@10
  CAsyncLogger *v9; // rax@12
  __int64 v10; // [sp+0h] [bp-48h]@1
  char *pszCause; // [sp+20h] [bp-28h]@8
  CPlayer *v12; // [sp+30h] [bp-18h]@4
  int v13; // [sp+38h] [bp-10h]@6
  CChiNetworkEX *v14; // [sp+50h] [bp+8h]@1
  unsigned int dwSerial; // [sp+58h] [bp+10h]@1
  char *v16; // [sp+68h] [bp+20h]@1

  v16 = pMsg;
  dwSerial = dwSID;
  v14 = this;
  v4 = &v10;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = GetPtrPlayerFromAccountSerial(&g_Player, 2532, dwSID);
  if ( !v12 )
  {
    v6 = CAsyncLogger::Instance();
    CAsyncLogger::FormatLog(v6, 12, "ApexMsg(K)..Recv_ApexKill - !pOne:%d", dwSerial);
  }
  v13 = *(_DWORD *)v16;
  if ( v13 == 1 )
  {
    if ( v12 )
    {
      pszCause = "ApexItemServer Kick";
      CUserDB::ForceCloseCommand(v12->m_pUserDB, 12, 0xFFFFFFFu, 1, "ApexItemServer Kick");
      v7 = CAsyncLogger::Instance();
      LODWORD(pszCause) = CPlayer::s_nLiveNum;
      CAsyncLogger::FormatLog(v7, 12, "APEX_USER_KICK - %d, CPlayer::s_nLiveNum : %d", dwSerial);
    }
  }
  else if ( v13 )
  {
    v9 = CAsyncLogger::Instance();
    CAsyncLogger::FormatLog(
      v9,
      12,
      "ApexMsg(K)..Recv_ApexKill - %d, if(dwAction == 1 || dwAction == 0) else?? ",
      dwSerial);
  }
  else
  {
    CChiNetworkEX::Inform_For_Exit_By_ApexBlock(v14, dwSerial);
    v8 = CAsyncLogger::Instance();
    CAsyncLogger::FormatLog(v8, 12, "Inform_For_Exit_By_ApexBlock - %d", dwSerial);
    if ( v12 )
    {
      pszCause = "ApexItemServer Block";
      CUserDB::ForceCloseCommand(v12->m_pUserDB, 13, 0xFFFFFFFu, 1, "ApexItemServer Block");
    }
  }
}
