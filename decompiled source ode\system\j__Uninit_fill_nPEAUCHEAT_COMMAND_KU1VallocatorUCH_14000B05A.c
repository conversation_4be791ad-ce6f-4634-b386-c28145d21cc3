/*
 * Function: j_??$_Uninit_fill_n@PEAUCHEAT_COMMAND@@_KU1@V?$allocator@UCHEAT_COMMAND@@@std@@@std@@YAXPEAUCHEAT_COMMAND@@_KAEBU1@AEAV?$allocator@UCHEAT_COMMAND@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000B05A
 */

void __fastcall std::_Uninit_fill_n<CHEAT_COMMAND *,unsigned __int64,CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>(CHEAT_COMMAND *_First, unsigned __int64 _Count, CHEAT_COMMAND *_Val, std::allocator<CHEAT_COMMAND> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CHEAT_COMMAND *,unsigned __int64,CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>(
    _First,
    _Count,
    _Val,
    _Al,
    __formal,
    a6);
}
