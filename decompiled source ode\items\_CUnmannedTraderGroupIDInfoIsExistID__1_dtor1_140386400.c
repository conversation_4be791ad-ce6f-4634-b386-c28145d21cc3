/*
 * Function: _CUnmannedTraderGroupIDInfo::IsExistID_::_1_::dtor$1
 * Address: 0x140386400
 */

void __fastcall CUnmannedTraderGroupIDInfo::IsExistID_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>((std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)(a2 + 72));
}
