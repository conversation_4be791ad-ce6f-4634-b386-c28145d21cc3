/*
 * Function: ??0CBossMonsterScheduleSystem@@QEAA@XZ
 * Address: 0x140418610
 */

void __fastcall CBossMonsterScheduleSystem::CBossMonsterScheduleSystem(CBossMonsterScheduleSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CBossMonsterScheduleSystem *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>((US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> > *)&v5->vfptr);
  v5->vfptr = (US::AbstractThreadVtbl *)&CBossMonsterScheduleSystem::`vftable';
  v5->m_pMapOper = 0i64;
  v5->m_pCurTBL = 0i64;
  US::CDynamicTaskPool<ScheduleMSG,US::CCircularFIFO<unsigned long,US::CriticalSection,0>>::CDynamicTaskPool<ScheduleMSG,US::CCircularFIFO<unsigned long,US::CriticalSection,0>>(&v5->m_MSG_POOL);
  v5->m_bRespawnMonster = 0;
}
