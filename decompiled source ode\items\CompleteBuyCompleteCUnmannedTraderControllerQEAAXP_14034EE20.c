/*
 * Function: ?CompleteBuyComplete@CUnmannedTraderController@@QEAAXPEAD@Z
 * Address: 0x14034EE20
 */

void __fastcall CUnmannedTraderController::CompleteBuyComplete(CUnmannedTraderController *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@5
  unsigned int v5; // ecx@5
  unsigned int v6; // edx@5
  int v7; // ecx@11
  __int64 v8; // [sp+0h] [bp-58h]@1
  int v9; // [sp+20h] [bp-38h]@5
  int v10; // [sp+28h] [bp-30h]@5
  int v11; // [sp+30h] [bp-28h]@11
  char *v12; // [sp+40h] [bp-18h]@4
  unsigned int j; // [sp+48h] [bp-10h]@5
  CUnmannedTraderController *v14; // [sp+60h] [bp+8h]@1

  v14 = this;
  v2 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = pData;
  if ( !pData[9] )
  {
    v4 = (unsigned __int8)v12[8];
    v5 = *(_WORD *)v12;
    v6 = (unsigned __int8)v12[10];
    v10 = *((_DWORD *)v12 + 1);
    v9 = v4;
    CUnmannedTraderController::Log(
      v14,
      "CUnmannedTraderController::CompleteBuyRollBack( BYTE byRet, char * pLoadData )\r\n"
      "\t\tType(%u) wInx(%u) Race(%u) BuyerSerial(%u)\r\n",
      v6,
      v5);
    for ( j = 0; (signed int)j < (unsigned __int8)v12[11]; ++j )
    {
      if ( (unsigned __int8)v12[16 * j + 13] != 255 )
      {
        if ( v12[16 * j + 12] )
        {
          v7 = (unsigned __int8)v12[16 * j + 24];
          v11 = (unsigned __int8)v12[16 * j + 13];
          v10 = v7;
          v9 = *(_DWORD *)&v12[16 * j + 16];
          CUnmannedTraderController::Log(
            v14,
            "\t\t(%d)Nth Regist Serial(%u) dwSeller(%u) UpdateState(%u) byProcUpdate(%u) DB Error!\r\n",
            j,
            *(_DWORD *)&v12[16 * j + 20]);
        }
      }
    }
  }
}
