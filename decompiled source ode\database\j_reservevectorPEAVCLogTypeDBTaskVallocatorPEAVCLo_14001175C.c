/*
 * Function: j_?reserve@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAAX_K@Z
 * Address: 0x14001175C
 */

void __fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::reserve(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, unsigned __int64 _Count)
{
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::reserve(this, _Count);
}
