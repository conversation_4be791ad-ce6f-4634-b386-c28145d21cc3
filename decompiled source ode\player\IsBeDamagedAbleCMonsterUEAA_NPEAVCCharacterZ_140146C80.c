/*
 * Function: ?IsBeDamagedAble@CMonster@@UEAA_NPEAVCCharacter@@@Z
 * Address: 0x140146C80
 */

char __fastcall CMonster::IsBeDamagedAble(CMonster *this, CCharacter *pAtter)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-48h]@1
  CCharacter *v6; // [sp+20h] [bp-28h]@9
  CCharacter *v7; // [sp+28h] [bp-20h]@13
  char v8; // [sp+30h] [bp-18h]@4
  CMonster *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = pAtter->m_ObjID.m_byID;
  if ( !v8 )
  {
    v7 = pAtter;
    if ( BYTE2(pAtter[1].m_fCurPos[2]) )
      return 0;
    goto LABEL_15;
  }
  if ( v8 == 1 )
    return 0;
  if ( v8 != 3 )
  {
LABEL_15:
    if ( v9->m_pMonRec->m_nRaceCode == -1 )
    {
      result = 1;
    }
    else if ( v9->m_pMonRec->m_nRaceCode == 3 )
    {
      result = 0;
    }
    else
    {
      result = ((int (__fastcall *)(CCharacter *))pAtter->vfptr->GetObjRace)(pAtter) != v9->m_pMonRec->m_nRaceCode;
    }
    return result;
  }
  v6 = pAtter;
  return !*(_QWORD *)&pAtter[1].m_bLive || !*(_BYTE *)(*(_QWORD *)&v6[1].m_bLive + 1922i64);
}
