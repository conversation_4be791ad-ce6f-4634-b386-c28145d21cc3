/*
 * Function: ?wa_ExitWorld@@YAXPEAU_CLID@@@Z
 * Address: 0x140046190
 */

void __fastcall wa_ExitWorld(_CLID *pidWorld)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-88h]@1
  CPartyPlayer *pLeaver; // [sp+20h] [bp-68h]@4
  CPartyPlayer *v5; // [sp+28h] [bp-60h]@6
  CPartyPlayer *ppoutNewBoss; // [sp+38h] [bp-50h]@6
  CPartyPlayer **v7; // [sp+48h] [bp-40h]@7
  int j; // [sp+50h] [bp-38h]@7
  CPlayer *v9; // [sp+58h] [bp-30h]@12
  CPartyPlayer **v10; // [sp+60h] [bp-28h]@15
  int k; // [sp+68h] [bp-20h]@15
  CPlayer *v12; // [sp+70h] [bp-18h]@18
  _CLID *v13; // [sp+90h] [bp+8h]@1

  v13 = pidWorld;
  v1 = &v3;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  pLeaver = (CPartyPlayer *)((char *)&g_PartyPlayer + 128 * (unsigned __int64)v13->wIndex);
  if ( pLeaver->m_id.dwSerial == v13->dwSerial && pLeaver->m_bLogin )
  {
    v5 = pLeaver->m_pPartyBoss;
    ppoutNewBoss = pLeaver->m_pPartyBoss;
    if ( CPartyPlayer::IsPartyMode(pLeaver) )
    {
      v7 = CPartyPlayer::GetPtrPartyMember(pLeaver);
      for ( j = 0; j < 8 && v7[j]; ++j )
      {
        if ( v7[j] != pLeaver )
        {
          v9 = &g_Player + v7[j]->m_wZoneIndex;
          CPlayer::SendMsg_PartyLeaveSelfResult(v9, pLeaver, 1);
        }
      }
    }
    CPartyPlayer::ExitWorld(pLeaver, &ppoutNewBoss);
    if ( ppoutNewBoss && ppoutNewBoss != v5 )
    {
      v10 = CPartyPlayer::GetPtrPartyMember(ppoutNewBoss);
      for ( k = 0; k < 8 && v10[k]; ++k )
      {
        v12 = &g_Player + v10[k]->m_wZoneIndex;
        CPlayer::SendMsg_PartySuccessResult(v12, ppoutNewBoss);
      }
    }
  }
}
