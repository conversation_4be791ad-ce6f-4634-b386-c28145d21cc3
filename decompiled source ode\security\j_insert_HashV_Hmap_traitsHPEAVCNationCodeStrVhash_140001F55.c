/*
 * Function: j_?insert@?$_Hash@V?$_Hmap_traits@HPEAVCNationCodeStr@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@@std@@$0A@@stdext@@@stdext@@QEAA?AU?$pair@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@@2@@std@@_N@std@@AEBU?$pair@$$CBHPEAVCNationCodeStr@@@4@@Z
 * Address: 0x140001F55
 */

std::pair<std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0>,bool> *__fastcall stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationCodeStr *>>,0>>::insert(stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationCodeStr *> >,0> > *this, std::pair<std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0>,bool> *result, std::pair<int const ,CNationCodeStr *> *_Val)
{
  return stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationCodeStr *>>,0>>::insert(
           this,
           result,
           _Val);
}
