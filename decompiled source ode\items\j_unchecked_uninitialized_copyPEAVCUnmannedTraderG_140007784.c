/*
 * Function: j_??$unchecked_uninitialized_copy@PEAVCUnmannedTraderGroupDivisionVersionInfo@@PEAV1@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@stdext@@YAPEAVCUnmannedTraderGroupDivisionVersionInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@Z
 * Address: 0x140007784
 */

CUnmannedTraderGroupDivisionVersionInfo *__fastcall stdext::unchecked_uninitialized_copy<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo *,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(CUnmannedTraderGroupDivisionVersionInfo *_First, CUnmannedTraderGroupDivisionVersionInfo *_Last, CUnmannedTraderGroupDivisionVersionInfo *_Dest, std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *_Al)
{
  return stdext::unchecked_uninitialized_copy<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo *,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
