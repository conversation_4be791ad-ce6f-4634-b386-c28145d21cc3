/*
 * Function: j_??$_Uninit_copy@PEAPEAU_guild_member_refresh_data@@PEAPEAU1@V?$allocator@PEAU_guild_member_refresh_data@@@std@@@std@@YAPEAPEAU_guild_member_refresh_data@@PEAPEAU1@00AEAV?$allocator@PEAU_guild_member_refresh_data@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000F376
 */

_guild_member_refresh_data **__fastcall std::_Uninit_copy<_guild_member_refresh_data * *,_guild_member_refresh_data * *,std::allocator<_guild_member_refresh_data *>>(_guild_member_refresh_data **_First, _guild_member_refresh_data **_Last, _guild_member_refresh_data **_Dest, std::allocator<_guild_member_refresh_data *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<_guild_member_refresh_data * *,_guild_member_refresh_data * *,std::allocator<_guild_member_refresh_data *>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
