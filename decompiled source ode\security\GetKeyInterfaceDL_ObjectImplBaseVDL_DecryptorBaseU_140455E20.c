/*
 * Function: ?GetKeyInterface@?$DL_ObjectImplBase@V?$DL_DecryptorBase@UECPPoint@CryptoPP@@@CryptoPP@@U?$DL_CryptoSchemeOptions@U?$ECIES@VECP@CryptoPP@@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@2@$0A@@CryptoPP@@U?$DL_Keys_EC@VECP@CryptoPP@@@2@V?$DL_KeyAgreementAlgorithm_DH@UECPPoint@CryptoPP@@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@2@@2@V?$DL_KeyDerivationAlgorithm_P1363@UECPPoint@CryptoPP@@$0A@V?$P1363_KDF2@VSHA1@CryptoPP@@@2@@2@V?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@2@@2@V?$DL_PrivateKey_EC@VECP@CryptoPP@@@2@@CryptoPP@@MEBAAEBV?$DL_PrivateKey@UECPPoint@CryptoPP@@@2@XZ
 * Address: 0x140455E20
 */

CryptoPP::ClonableVtbl **__fastcall CryptoPP::DL_ObjectImplBase<CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>,CryptoPP::DL_CryptoSchemeOptions<CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,0>,CryptoPP::DL_Keys_EC<CryptoPP::ECP>,CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::ECPPoint,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>>,CryptoPP::DL_KeyDerivationAlgorithm_P1363<CryptoPP::ECPPoint,0,CryptoPP::P1363_KDF2<CryptoPP::SHA1>>,CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>,CryptoPP::DL_PrivateKey_EC<CryptoPP::ECP>>::GetKeyInterface(CryptoPP::DL_ObjectImplBase<CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>,CryptoPP::DL_CryptoSchemeOptions<CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0>,0>,CryptoPP::DL_Keys_EC<CryptoPP::ECP>,CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::ECPPoint,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0> >,CryptoPP::DL_KeyDerivationAlgorithm_P1363<CryptoPP::ECPPoint,0,CryptoPP::P1363_KDF2<CryptoPP::SHA1> >,CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> >,CryptoPP::DL_PrivateKey_EC<CryptoPP::ECP> > *this)
{
  return &this->vfptr;
}
