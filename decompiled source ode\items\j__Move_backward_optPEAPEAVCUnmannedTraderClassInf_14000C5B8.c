/*
 * Function: j_??$_Move_backward_opt@PEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@Urandom_access_iterator_tag@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@00Urandom_access_iterator_tag@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000C5B8
 */

CUnmannedTraderClassInfo **__fastcall std::_Move_backward_opt<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *,std::random_access_iterator_tag,std::_Undefined_move_tag>(CUnmannedTraderClassInfo **_First, CUnmannedTraderClassInfo **_Last, CUnmannedTraderClassInfo **_Dest, std::random_access_iterator_tag _First_dest_cat, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Move_backward_opt<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *,std::random_access_iterator_tag,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _First_dest_cat,
           __formal,
           a6);
}
