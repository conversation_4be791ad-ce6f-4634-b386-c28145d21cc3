/*
 * Function: ?IsExistGroupID@CUnmannedTraderGroupItemInfoTable@@QEAA_NEEEEAEAK@Z
 * Address: 0x140360A40
 */

bool __fastcall CUnmannedTraderGroupItemInfoTable::IsExistGroupID(CUnmannedTraderGroupItemInfoTable *this, char byDivision, char byClass, char bySubClass, char bySortType, unsigned int *dwListIndex)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-38h]@1
  CUnmannedTraderGroupItemInfoTable *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  return CUnmannedTraderGroupIDInfo::IsExistGroupID(
           &v10->m_kGroupIDInfo,
           byDivision,
           byClass,
           bySubClass,
           bySortType,
           dwListIndex);
}
