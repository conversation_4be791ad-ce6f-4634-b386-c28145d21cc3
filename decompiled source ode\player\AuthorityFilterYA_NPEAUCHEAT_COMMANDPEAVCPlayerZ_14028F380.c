/*
 * Function: ?AuthorityFilter@@YA_NPEAUCHEAT_COMMAND@@PEAVCPlayer@@@Z
 * Address: 0x14028F380
 */

char __fastcall AuthorityFilter(CHEAT_COMMAND *pCmd, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@6
  char v6; // al@9
  __int64 v7; // [sp+0h] [bp-38h]@1
  int v8; // [sp+20h] [bp-18h]@6
  int v9; // [sp+24h] [bp-14h]@9
  int v10; // [sp+28h] [bp-10h]@6
  int v11; // [sp+2Ch] [bp-Ch]@9
  CHEAT_COMMAND *v12; // [sp+40h] [bp+8h]@1
  CPlayer *v13; // [sp+48h] [bp+10h]@1

  v13 = pOne;
  v12 = pCmd;
  v2 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pOne )
  {
    if ( CMainThread::IsReleaseServiceMode(&g_Main) )
    {
      v8 = 0;
      v4 = v13->m_byUserDgr;
      v10 = 1;
      v8 = 1 << v4;
      if ( !((1 << v4) & v12->nUseDegree) )
        return 0;
      if ( v13->m_byUserDgr == 2 )
      {
        v9 = 0;
        v6 = v13->m_bySubDgr;
        v11 = 1;
        v9 = 1 << v6;
        if ( !((1 << v6) & v12->nMgrDegree) )
          return 0;
      }
    }
  }
  else if ( !(v12->nUseDegree & 0x20) )
  {
    return 0;
  }
  return 1;
}
