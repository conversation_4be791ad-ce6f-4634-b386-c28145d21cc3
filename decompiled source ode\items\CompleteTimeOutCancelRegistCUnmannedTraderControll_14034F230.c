/*
 * Function: ?CompleteTimeOutCancelRegist@CUnmannedTraderController@@QEAAXEPEAD@Z
 * Address: 0x14034F230
 */

void __fastcall CUnmannedTraderController::CompleteTimeOutCancelRegist(CUnmannedTraderController *this, char byRet, char *pLoadData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfoTable *v5; // rax@6
  CUnmannedTraderGroupItemInfoTable *v6; // rax@6
  int v7; // ecx@13
  int v8; // edx@13
  int v9; // er8@13
  unsigned int v10; // er9@13
  unsigned int v11; // er10@13
  CUnmannedTraderScheduler *v12; // rax@14
  __int64 v13; // [sp+0h] [bp-88h]@1
  unsigned int dwRegistSerial; // [sp+20h] [bp-68h]@13
  const char *v15; // [sp+28h] [bp-60h]@13
  int v16; // [sp+30h] [bp-58h]@13
  const char *v17; // [sp+38h] [bp-50h]@13
  int v18; // [sp+40h] [bp-48h]@13
  int v19; // [sp+48h] [bp-40h]@13
  int v20; // [sp+50h] [bp-38h]@13
  int v21; // [sp+58h] [bp-30h]@13
  char *v22; // [sp+60h] [bp-28h]@4
  const char *v23; // [sp+68h] [bp-20h]@8
  const char *v24; // [sp+70h] [bp-18h]@11
  CUnmannedTraderController *v25; // [sp+90h] [bp+8h]@1
  char v26; // [sp+98h] [bp+10h]@1
  char *pLoadDataa; // [sp+A0h] [bp+18h]@1

  pLoadDataa = pLoadData;
  v26 = byRet;
  v25 = this;
  v3 = &v13;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v22 = pLoadData;
  if ( byRet || v22[50] )
  {
    if ( v22[20] )
      v23 = v22 + 20;
    else
      v23 = "NULL";
    if ( v22[33] )
      v24 = v22 + 33;
    else
      v24 = "NULL";
    v7 = (unsigned __int8)v22[50];
    v8 = *((_WORD *)v22 + 9);
    v9 = (unsigned __int8)v22[16];
    v10 = (unsigned __int8)v22[8];
    v11 = (unsigned __int8)*v22;
    v21 = (unsigned __int8)v26;
    v20 = v7;
    v19 = v8;
    v18 = v9;
    v17 = v23;
    v16 = *((_DWORD *)v22 + 3);
    v15 = v24;
    dwRegistSerial = v10;
    CUnmannedTraderController::Log(
      v25,
      "CUnmannedTraderController::CompleteTimeOutCancelRegist( byRet, char * pLoadData )\r\n"
      "\t\tType(%u) RegistSerial(%u) State(%u) : %s(%u) ID(%s) TableCode(%u) TableIndex(%u)\r\n"
      "\t\tProcRet(%u) RET_CODE_SUCCESS != byRet(%u)\r\n",
      v11,
      *((_DWORD *)v22 + 1));
  }
  else
  {
    v5 = CUnmannedTraderUserInfoTable::Instance();
    CUnmannedTraderUserInfoTable::CompleteTimeOutClear(v5, pLoadDataa);
    v6 = CUnmannedTraderGroupItemInfoTable::Instance();
    CUnmannedTraderGroupItemInfoTable::IncreaseVersion(v6, v22[16], *((_WORD *)v22 + 9));
  }
  v12 = CUnmannedTraderScheduler::Instance();
  dwRegistSerial = *((_DWORD *)v22 + 1);
  CUnmannedTraderScheduler::CompleteClear(v12, v26, v22[50], *v22, dwRegistSerial);
}
