/*
 * Function: ?ErrorAction@CRFNewDatabase@@IEAAXFPEAX@Z
 * Address: 0x140486C70
 */

void __fastcall CRFNewDatabase::ErrorAction(CRFNewDatabase *this, __int16 sqlRet, void *SQLStmt)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  CRFNewDatabase *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = sqlRet;
  if ( sqlRet == -2 )
  {
    CRFNewDatabase::ErrFmtLog(v7, "SQL_INVALID_HANDLE -> ReConnectDataBase()");
    if ( !CRFNewDatabase::ReConnectDataBase(v7) && v7->m_bReconnectFailExit )
      ServerProgramExit("CRFNewDatabase::ErrorAction : DB Connection Refused! ReConnectDataBase Fail!", 1);
  }
  else if ( v6 == -1 && !CRFNewDatabase::EroorActionProcSQL_ERROR(v7, SQLStmt) )
  {
    if ( v7->m_bReconnectFailExit )
      ServerProgramExit("CRFNewDatabase::ErrorAction : DB Connection Refused! ReConnectDataBase Fail!", 1);
  }
}
