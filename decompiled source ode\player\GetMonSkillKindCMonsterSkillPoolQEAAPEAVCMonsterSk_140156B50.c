/*
 * Function: ?GetMonSkillKind@CMonsterSkillPool@@QEAAPEAVCMonsterSkill@@H@Z
 * Address: 0x140156B50
 */

CMonsterSkill *__fastcall CMonsterSkillPool::GetMonSkillKind(CMonsterSkillPool *this, int nKind)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CMonsterSkillPool *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  if ( nKind >= 0 && nKind < 4 )
  {
    for ( j = 0; j < 16; ++j )
    {
      if ( v6->m_MonSkill[j].m_bExit && v6->m_MonSkill[j].m_nSFCode == nKind )
        return &v6->m_MonSkill[j];
    }
  }
  return 0i64;
}
