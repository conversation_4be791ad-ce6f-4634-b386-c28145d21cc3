/*
 * Function: ?CheckLogFileHour@CRFNewDatabase@@QEAAXXZ
 * Address: 0x140487A50
 */

void __fastcall CRFNewDatabase::CheckLogFileHour(CRFNewDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@4
  __int64 v4; // [sp+0h] [bp-268h]@1
  bool bDate[4]; // [sp+20h] [bp-248h]@5
  char Dst; // [sp+40h] [bp-228h]@5
  int v7; // [sp+250h] [bp-18h]@4
  unsigned __int64 v8; // [sp+258h] [bp-10h]@4
  CRFNewDatabase *v9; // [sp+270h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 152i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = (unsigned __int64)&v4 ^ _security_cookie;
  v7 = v9->m_byLogFileHour;
  v3 = (unsigned __int8)CRFNewDatabase::GetLocalHour(v9);
  if ( v7 != (unsigned __int8)v3 )
  {
    memset_0(&Dst, 0, 0x200ui64);
    *(_DWORD *)bDate = GetKorLocalTime();
    sprintf_s<512>((char (*)[512])&Dst, "%sDBLog\\DBProcess_%s_%d_U.log", v9->m_szLogUpperPath, v9->m_szOdbcName);
    if ( !v9->m_ProcessLogW.m_bInit )
      CLogFile::SetWriteLogFile(&v9->m_ProcessLogW, &Dst, 1, 1, 1, 1);
    memset_0(&Dst, 0, 0x200ui64);
    *(_DWORD *)bDate = GetKorLocalTime();
    sprintf_s<512>((char (*)[512])&Dst, "%sDBLog\\DBProcess_%s_%d_A.log", v9->m_szLogUpperPath, v9->m_szOdbcName);
    if ( !v9->m_ProcessLogA.m_bInit )
      CLogFile::SetWriteLogFile(&v9->m_ProcessLogA, &Dst, 1, 1, 1, 1);
    memset_0(&Dst, 0, 0x200ui64);
    *(_DWORD *)bDate = GetKorLocalTime();
    sprintf_s<512>((char (*)[512])&Dst, "%sDBLog\\DBError_%s_%d_U.log", v9->m_szLogUpperPath, v9->m_szOdbcName);
    if ( !v9->m_ErrorLogW.m_bInit )
      CLogFile::SetWriteLogFile(&v9->m_ErrorLogW, &Dst, 1, 1, 1, 1);
    memset_0(&Dst, 0, 0x200ui64);
    *(_DWORD *)bDate = GetKorLocalTime();
    sprintf_s<512>((char (*)[512])&Dst, "%sDBLog\\DBError_%s_%d_A.log", v9->m_szLogUpperPath, v9->m_szOdbcName);
    if ( !v9->m_ErrorLogA.m_bInit )
      CLogFile::SetWriteLogFile(&v9->m_ErrorLogA, &Dst, 1, 1, 1, 1);
    v9->m_byLogFileHour = CRFNewDatabase::GetLocalHour(v9);
  }
}
