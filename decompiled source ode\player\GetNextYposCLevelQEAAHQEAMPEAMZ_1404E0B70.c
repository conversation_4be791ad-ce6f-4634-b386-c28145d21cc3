/*
 * Function: ?GetNextYpos@CLevel@@QEAAHQEAMPEAM@Z
 * Address: 0x1404E0B70
 */

__int64 __fastcall CLevel::GetNextYpos(CLevel *this, float *const a2, float *a3)
{
  float v3; // xmm6_4@1
  float *v4; // r13@1
  float *v5; // rbp@1
  CLevel *v6; // rsi@1
  __int64 result; // rax@2
  __int16 v8; // bx@3
  signed int v9; // edi@3
  float v10; // xmm1_4@3
  int v11; // ecx@3
  __int64 v12; // rbx@3
  float v13; // xmm3_4@3
  __int64 v14; // r12@3
  float v15; // xmm0_4@4
  CBsp *v16; // rcx@4
  float v17; // xmm0_4@4
  __int64 v18; // [sp+0h] [bp-2B8h]@1
  int v19; // [sp+28h] [bp-290h]@4
  float v20; // [sp+30h] [bp-288h]@3
  float v21; // [sp+34h] [bp-284h]@3
  int v22; // [sp+38h] [bp-280h]@3
  float v23; // [sp+40h] [bp-278h]@3
  float v24; // [sp+44h] [bp-274h]@3
  int v25; // [sp+48h] [bp-270h]@3
  __int16 v26; // [sp+50h] [bp-268h]@3
  __int16 v27; // [sp+52h] [bp-266h]@3
  unsigned __int64 v28; // [sp+250h] [bp-68h]@1

  v28 = (unsigned __int64)&v18 ^ _security_cookie;
  v3 = FLOAT_N100000_0;
  v4 = a3;
  v5 = a2;
  v6 = this;
  if ( this->mIsLoadedBsp )
  {
    a2[1] = a2[1] - 23.0;
    v8 = CBsp::GetLeafNum(this->mBsp, a2);
    v26 = v8;
    v5[1] = v5[1] + 46.0;
    v9 = 0;
    v27 = CBsp::GetLeafNum(v6->mBsp, v5);
    v10 = *v5;
    v11 = v8 != v27;
    v12 = 0i64;
    v13 = v5[1] - 23.0;
    v22 = *((_DWORD *)v5 + 2);
    v25 = v22;
    v14 = ++v11;
    v20 = v10;
    v23 = v10;
    v5[1] = v13;
    v21 = v13 + 30000.0;
    v24 = v13 + (float)(0.0 - 30000.0);
    if ( v11 > 0 )
    {
      do
      {
        v15 = v5[1];
        v16 = v6->mBsp;
        v19 = *(&v26 + v12);
        v17 = CBsp::GetYposInLeaf(v16, &v20, &v23, 23.0, v15, v19);
        if ( v17 != -32000.0 && v17 > v3 )
        {
          v9 = 1;
          v3 = v17;
        }
        ++v12;
      }
      while ( v12 < v14 );
      if ( v9 )
        *v4 = v3;
    }
    result = (unsigned int)v9;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
