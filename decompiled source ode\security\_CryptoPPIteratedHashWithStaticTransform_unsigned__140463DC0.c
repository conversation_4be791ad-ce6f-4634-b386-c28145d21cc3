/*
 * Function: _CryptoPP::IteratedHashWithStaticTransform_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_20_CryptoPP::SHA1_0_::_IteratedHashWithStaticTransform_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_20_CryptoPP::SHA1_0__::_1_::dtor$0
 * Address: 0x140463DC0
 */

void __fastcall CryptoPP::IteratedHashWithStaticTransform_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_20_CryptoPP::SHA1_0_::_IteratedHashWithStaticTransform_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_20_CryptoPP::SHA1_0__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  CryptoPP::ClonableImpl<CryptoPP::SHA1,CryptoPP::AlgorithmImpl<CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>,CryptoPP::SHA1>>::~ClonableImpl<CryptoPP::SHA1,CryptoPP::AlgorithmImpl<CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>,CryptoPP::SHA1>>(*(CryptoPP::ClonableImpl<CryptoPP::SHA1,CryptoPP::AlgorithmImpl<CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>,CryptoPP::SHA1> > **)(a2 + 64));
}
