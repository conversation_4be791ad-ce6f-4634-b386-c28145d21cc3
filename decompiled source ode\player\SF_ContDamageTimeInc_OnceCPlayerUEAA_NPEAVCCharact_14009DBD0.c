/*
 * Function: ?SF_ContDamageTimeInc_Once@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 * Address: 0x14009DBD0
 */

bool __fastcall CPlayer::SF_ContDamageTimeInc_Once(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-68h]@1
  int v7; // [sp+30h] [bp-38h]@4
  int v8; // [sp+34h] [bp-34h]@4
  unsigned int dwStartSec; // [sp+38h] [bp-30h]@4
  int j; // [sp+3Ch] [bp-2Ch]@4
  _sf_continous (*v11)[8]; // [sp+40h] [bp-28h]@7
  _base_fld *v12; // [sp+48h] [bp-20h]@8
  unsigned int v13; // [sp+50h] [bp-18h]@8
  unsigned int v14; // [sp+54h] [bp-14h]@13
  unsigned __int16 v15; // [sp+58h] [bp-10h]@15
  CCharacter *v16; // [sp+78h] [bp+10h]@1

  v16 = pDstObj;
  v3 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = 0;
  v8 = 0;
  dwStartSec = _sf_continous::GetSFContCurTime();
  for ( j = 0; j < 8; ++j )
  {
    v11 = (_sf_continous (*)[8])((char *)v16->m_SFCont + 48 * j);
    if ( v11 )
    {
      v12 = CRecordData::GetRecord(&stru_1799C8410 + (*v11)[0].m_byEffectCode, (*v11)[0].m_wEffectIndex);
      v13 = 0;
      if ( (*v11)[0].m_byEffectCode && (*v11)[0].m_byEffectCode != 2 )
      {
        if ( (*v11)[0].m_byEffectCode == 1 )
          v13 = *(_DWORD *)&v12[15].m_strCode[4 * ((*v11)[0].m_byLv - 1) + 32];
      }
      else
      {
        v13 = *(_DWORD *)&v12[16].m_strCode[4 * ((*v11)[0].m_byLv - 1) + 24];
      }
      v14 = (*v11)[0].m_wDurSec - (dwStartSec - (*v11)[0].m_dwStartSec);
      if ( v13 > v14 )
        ++v7;
      v15 = (signed int)ffloor((float)(signed int)v14 + (float)((float)(signed int)v13 * fEffectValue));
      if ( v15 > 2 * v13 )
        v15 = 2 * v13;
      CCharacter::AlterContDurSec(v16, 0, j, dwStartSec, v15);
      ++v8;
    }
  }
  if ( v8 > 0 && !v16->m_ObjID.m_byID )
    CPlayer::SendMsg_AlterContEffectTime((CPlayer *)v16, 0);
  return v7 > 0;
}
