/*
 * Function: ?GetDefFC@CAnimus@@UEAAHHPEAVCCharacter@@PEAH@Z
 * Address: 0x140129610
 */

__int64 __usercall CAnimus::GetDefFC@<rax>(CAnimus *this@<rcx>, int nAttactPart@<edx>, CCharacter *pAttChar@<r8>, int *pnConvertPart@<r9>, float a5@<xmm0>)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-38h]@1
  int v9; // [sp+20h] [bp-18h]@4
  CAnimus *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v5 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v9 = v10->m_pRecord->m_nStdDefFc;
  if ( v10->m_pMaster && v10->m_byRoleCode == 1 )
  {
    _effect_parameter::GetEff_Rate(&v10->m_pMaster->m_EP, 30);
    v9 = (signed int)ffloor((float)v9 * a5);
  }
  return (unsigned int)v9;
}
