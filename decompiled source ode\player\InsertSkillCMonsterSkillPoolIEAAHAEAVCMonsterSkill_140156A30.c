/*
 * Function: ?InsertSkill@CMonsterSkillPool@@IEAAHAEAVCMonsterSkill@@@Z
 * Address: 0x140156A30
 */

signed __int64 __fastcall CMonsterSkillPool::InsertSkill(CMonsterSkillPool *this, CMonsterSkill *skill)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@8
  CMonsterSkillPool *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7->m_nSize < 16 )
  {
    if ( skill->m_bExit )
    {
      for ( j = 0; j < 16; ++j )
      {
        if ( !v7->m_MonSkill[j].m_bExit )
        {
          CMonsterSkill::Copy(&v7->m_MonSkill[j], skill);
          ++v7->m_nSize;
          return 1i64;
        }
      }
      result = 0i64;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
