/*
 * Function: ?Update_Map@CUserDB@@QEAA_NEPEAM@Z
 * Address: 0x140116240
 */

char __fastcall CUserDB::Update_Map(CUserDB *this, char map, float *pos)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  CUserDB *v7; // [sp+30h] [bp+8h]@1
  char v8; // [sp+38h] [bp+10h]@1
  float *v9; // [sp+40h] [bp+18h]@1

  v9 = pos;
  v8 = map;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( CMapOperation::IsExistStdMapID(&g_MapOper, (unsigned __int8)map) )
  {
    v7->m_AvatorData.dbAvator.m_byMapCode = v8;
    v7->m_AvatorData.dbAvator.m_fStartPos[0] = *v9;
    v7->m_AvatorData.dbAvator.m_fStartPos[1] = v9[1];
    v7->m_AvatorData.dbAvator.m_fStartPos[2] = v9[2];
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
