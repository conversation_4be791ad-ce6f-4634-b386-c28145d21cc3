/*
 * Function: ??1?$DL_FixedBasePrecomputationImpl@UEC2NPoint@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x14055DD70
 */

void __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::~DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>(__int64 a1)
{
  __int64 v1; // [sp+40h] [bp+8h]@1

  v1 = a1;
  std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::~vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>(a1 + 112);
  CryptoPP::Integer::~Integer((CryptoPP::Integer *)(v1 + 72));
  CryptoPP::EC2NPoint::~EC2NPoint((CryptoPP::EC2NPoint *)(v1 + 8));
}
