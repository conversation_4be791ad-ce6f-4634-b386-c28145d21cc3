/*
 * Function: ??0_map_fld@@QEAA@XZ
 * Address: 0x140198FF0
 */

void __fastcall _map_fld::_map_fld(_map_fld *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _map_fld *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _base_fld::_base_fld((_base_fld *)&v4->m_dwIndex);
  memset_0(v4, 0, 0xB4ui64);
}
