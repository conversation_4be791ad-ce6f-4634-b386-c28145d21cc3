/*
 * Function: ??E?$_Vector_iterator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEAAAEAV01@XZ
 * Address: 0x1405ACB90
 */

__int64 __fastcall std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator++(__int64 a1)
{
  __int64 v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator++();
  return v2;
}
