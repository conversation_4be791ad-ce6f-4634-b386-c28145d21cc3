/*
 * Function: ??0table_obj@lua_tinker@@QEAA@PEAUlua_State@@H@Z
 * Address: 0x140446140
 */

void __fastcall lua_tinker::table_obj::table_obj(lua_tinker::table_obj *this, struct lua_State *L, int index)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  const void *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-28h]@1
  lua_tinker::table_obj *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7->m_L = L;
  v7->m_index = index;
  v7->m_ref = 0;
  LODWORD(v5) = lua_topointer(v7->m_L, v7->m_index);
  v7->m_pointer = v5;
}
