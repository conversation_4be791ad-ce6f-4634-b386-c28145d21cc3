/*
 * Function: ?cash_item_use@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@PEAD@Z
 * Address: 0x14023BCE0
 */

void __fastcall CMgrAvatorItemHistory::cash_item_use(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pUseItem, char *pszFileName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char *v6; // rax@4
  __int64 v7; // [sp+0h] [bp-68h]@1
  char *v8; // [sp+20h] [bp-48h]@4
  unsigned __int64 v9; // [sp+28h] [bp-40h]@4
  char *v10; // [sp+30h] [bp-38h]@4
  char *v11; // [sp+38h] [bp-30h]@4
  _base_fld *v12; // [sp+40h] [bp-28h]@4
  char *v13; // [sp+48h] [bp-20h]@4
  char *v14; // [sp+50h] [bp-18h]@4
  int nTableCode; // [sp+58h] [bp-10h]@4
  CMgrAvatorItemHistory *v16; // [sp+70h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v17; // [sp+80h] [bp+18h]@1
  char *pszFileNamea; // [sp+88h] [bp+20h]@1

  pszFileNamea = pszFileName;
  v17 = pUseItem;
  v16 = this;
  v4 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  sData[0] = 0;
  v12 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pUseItem->m_byTableCode, pUseItem->m_wItemIndex);
  v13 = v16->m_szCurTime;
  v14 = v16->m_szCurDate;
  nTableCode = v17->m_byTableCode;
  v6 = DisplayItemUpgInfo(nTableCode, v17->m_dwLv);
  v11 = v13;
  v10 = v14;
  v9 = v17->m_lnUID;
  v8 = v6;
  sprintf(sBuf, "USE CASH ITEM: %s_%u_@%s[%I64u]  [%s %s]\r\n", v12->m_strCode, v17->m_dwDur);
  strcat_0(sData, sBuf);
  CMgrAvatorItemHistory::WriteFile(v16, pszFileNamea, sData);
}
