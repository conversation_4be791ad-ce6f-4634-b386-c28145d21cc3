/*
 * Function: ?CompleteSend@CPostSystemManager@@QEAAXPEAD@Z
 * Address: 0x1403261A0
 */

void __fastcall CPostSystemManager::CompleteSend(CPostSystemManager *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@11
  __int64 v5; // [sp+0h] [bp-A8h]@1
  char *wszRecvName; // [sp+20h] [bp-88h]@10
  char *wszTitle; // [sp+28h] [bp-80h]@10
  char *wszContent; // [sp+30h] [bp-78h]@10
  _INVENKEY Key; // [sp+38h] [bp-70h]@10
  unsigned __int64 dwDur; // [sp+40h] [bp-68h]@10
  unsigned int dwUpt; // [sp+48h] [bp-60h]@10
  unsigned int dwGold; // [sp+50h] [bp-58h]@10
  unsigned __int64 lnUID; // [sp+58h] [bp-50h]@10
  char *v14; // [sp+60h] [bp-48h]@4
  unsigned int j; // [sp+68h] [bp-40h]@4
  char *v16; // [sp+70h] [bp-38h]@6
  CPlayer *v17; // [sp+78h] [bp-30h]@7
  int *v18; // [sp+80h] [bp-28h]@10
  CPostData *pPost; // [sp+88h] [bp-20h]@10
  CPostReturnStorage *v20; // [sp+90h] [bp-18h]@10
  CPostSystemManager *v21; // [sp+B0h] [bp+8h]@1

  v21 = this;
  v2 = &v5;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v14 = pData;
  for ( j = 0; j < *(_DWORD *)v14; ++j )
  {
    v16 = &v14[312 * j + 8];
    if ( *v16 )
    {
      ++v21->m_nPostReturnCountPerDay;
      v17 = GetPtrPlayerFromSerial(&g_Player, 2532, *((_DWORD *)v16 + 4));
      if ( v17 )
      {
        if ( v17->m_bOper && v17->m_bPostLoad )
        {
          v18 = &v21->m_PostData[*((_DWORD *)v16 + 1)].m_nNumber;
          v20 = &v17->m_Param.m_ReturnPostStorage;
          lnUID = *((_QWORD *)v18 + 36);
          dwGold = v18[74];
          dwUpt = v18[70];
          dwDur = *((_QWORD *)v18 + 34);
          Key = (_INVENKEY)v18[67];
          wszContent = (char *)v18 + 67;
          wszTitle = (char *)v18 + 46;
          wszRecvName = (char *)v18 + 29;
          pPost = CPostReturnStorage::AddReturnPost(
                    &v17->m_Param.m_ReturnPostStorage,
                    *v16,
                    *((_DWORD *)v16 + 2),
                    100,
                    (char *)v18 + 29,
                    (char *)v18 + 46,
                    (char *)v18 + 67,
                    Key,
                    dwDur,
                    dwUpt,
                    dwGold,
                    lnUID);
          if ( pPost )
          {
            if ( _INVENKEY::IsFilled(&pPost->m_Key) || pPost->m_dwGold )
              CMgrAvatorItemHistory::post_returnreceive(&CPlayer::s_MgrItemHistory, pPost, v17->m_szItemHistoryFileName);
            dwGold = pPost->m_dwGold;
            dwUpt = pPost->m_dwUpt;
            dwDur = pPost->m_dwDur;
            *(_WORD *)&Key.bySlotIndex = pPost->m_Key.wItemIndex;
            LOBYTE(wszContent) = pPost->m_Key.byTableCode;
            wszTitle = pPost->m_wszContent;
            wszRecvName = pPost->m_wszTitle;
            CPlayer::SendMsg_PostReturn(
              v17,
              pPost->m_byErrCode,
              pPost->m_dwPSSerial,
              pPost->m_wszRecvName,
              pPost->m_wszTitle,
              pPost->m_wszContent,
              (char)wszContent,
              *(unsigned __int16 *)&Key.bySlotIndex,
              dwDur,
              dwUpt,
              dwGold);
          }
          else
          {
            v4 = CPostReturnStorage::GetSize(&v17->m_Param.m_ReturnPostStorage);
            CPostSystemManager::Log(
              v21,
              "CPostSystemManager::CompletePostReturn() :AddReturnPost: SenderSerial(%d),PSSize(%d)",
              *((_DWORD *)v16 + 4),
              (unsigned int)v4);
          }
        }
      }
    }
    CNetIndexList::PushNode_Back(&v21->m_listEmpty, *((_DWORD *)v16 + 1));
  }
}
