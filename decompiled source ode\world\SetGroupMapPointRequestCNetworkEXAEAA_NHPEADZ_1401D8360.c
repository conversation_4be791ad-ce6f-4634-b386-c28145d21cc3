/*
 * Function: ?SetGroupMapPointRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D8360
 */

char __fastcall CNetworkEX::SetGroupMapPointRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // rax@6
  char *v7; // rax@7
  __int64 v8; // [sp+0h] [bp-38h]@1
  char *v9; // [sp+20h] [bp-18h]@4
  CPlayer *v10; // [sp+28h] [bp-10h]@4
  CNetworkEX *v11; // [sp+40h] [bp+8h]@1

  v11 = this;
  v3 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = pBuf;
  v10 = &g_Player + n;
  if ( v10->m_bOper )
  {
    v6 = (unsigned __int8)*v9;
    if ( (signed int)(unsigned __int8)*v9 < 3 )
    {
      CPlayer::pc_SetGroupMapPointRequest(v10, *v9, (float *)(v9 + 1));
      result = 1;
    }
    else
    {
      v7 = CPlayerDB::GetCharNameA(&v10->m_Param);
      CLogFile::Write(
        &v11->m_LogFile,
        "odd.. %s: SetGroupMapPointRequest()..  if(pRecv->byGroupType < 0 || pRecv->byGroupType >= GROUP_TYPE_NUM)",
        v7);
      result = 0;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
