/*
 * Function: ??$unchecked_uninitialized_copy@PEAVCUnmannedTraderRegistItemInfo@@PEAV1@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@stdext@@YAPEAVCUnmannedTraderRegistItemInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@Z
 * Address: 0x1403634F0
 */

CUnmannedTraderRegistItemInfo *__fastcall stdext::unchecked_uninitialized_copy<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *,std::allocator<CUnmannedTraderRegistItemInfo>>(CUnmannedTraderRegistItemInfo *_First, CUnmannedTraderRegistItemInfo *_Last, CUnmannedTraderRegistItemInfo *_Dest, std::allocator<CUnmannedTraderRegistItemInfo> *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  std::_Nonscalar_ptr_iterator_tag v9; // [sp+31h] [bp-17h]@4
  CUnmannedTraderRegistItemInfo *__formal; // [sp+50h] [bp+8h]@1
  CUnmannedTraderRegistItemInfo *_Lasta; // [sp+58h] [bp+10h]@1
  CUnmannedTraderRegistItemInfo *_Desta; // [sp+60h] [bp+18h]@1
  std::allocator<CUnmannedTraderRegistItemInfo> *_Ala; // [sp+68h] [bp+20h]@1

  _Ala = _Al;
  _Desta = _Dest;
  _Lasta = _Last;
  __formal = _First;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  v9 = std::_Ptr_cat<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *>(&__formal, &_Desta);
  return std::_Uninit_copy<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *,std::allocator<CUnmannedTraderRegistItemInfo>>(
           __formal,
           _Lasta,
           _Desta,
           _Ala,
           v9,
           v8);
}
