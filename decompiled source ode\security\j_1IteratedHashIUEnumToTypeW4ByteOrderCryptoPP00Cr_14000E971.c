/*
 * Function: j_??1?$IteratedHash@IU?$EnumToType@W4ByteOrder@CryptoPP@@$00@CryptoPP@@$0EA@VHashTransformation@2@@CryptoPP@@UEAA@XZ
 * Address: 0x14000E971
 */

void __fastcall CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>::~IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>(CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation> *this)
{
  CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>::~IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>(this);
}
