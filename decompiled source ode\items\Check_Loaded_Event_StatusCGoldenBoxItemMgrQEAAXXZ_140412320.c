/*
 * Function: ?Check_Loaded_Event_Status@CGoldenBoxItemMgr@@QEAAXXZ
 * Address: 0x140412320
 */

void __fastcall CGoldenBoxItemMgr::Check_Loaded_Event_Status(CGoldenBoxItemMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@17
  __int64 v4; // [sp+0h] [bp-98h]@1
  int v5; // [sp+20h] [bp-78h]@17
  int v6; // [sp+28h] [bp-70h]@17
  int v7; // [sp+30h] [bp-68h]@17
  int v8; // [sp+38h] [bp-60h]@17
  int v9; // [sp+40h] [bp-58h]@17
  int v10; // [sp+48h] [bp-50h]@17
  int v11; // [sp+50h] [bp-48h]@17
  int v12; // [sp+58h] [bp-40h]@17
  int v13; // [sp+60h] [bp-38h]@17
  __time32_t Time; // [sp+74h] [bp-24h]@6
  int v15; // [sp+84h] [bp-14h]@10
  CGoldenBoxItemMgr *v16; // [sp+A0h] [bp+8h]@1

  v16 = this;
  v1 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v16->m_golden_box_event.m_ini.m_bUse_event )
  {
    _time32(&Time);
    if ( Time <= v16->m_golden_box_event.m_ini.m_EventTime[1] )
    {
      if ( Time < v16->m_golden_box_event.m_ini.m_EventTime[0] || Time > v16->m_golden_box_event.m_ini.m_EventTime[1] )
      {
        if ( Time < v16->m_golden_box_event.m_ini.m_EventTime[0] )
        {
          CGoldenBoxItemMgr::Set_Event_Status(v16, 1);
          v3 = v16->m_golden_box_event.m_event_status;
          v13 = v16->m_golden_box_event.m_ini.m_byMinute[1];
          v12 = v16->m_golden_box_event.m_ini.m_byHour[1];
          v11 = v16->m_golden_box_event.m_ini.m_byDay[1];
          v10 = v16->m_golden_box_event.m_ini.m_byMonth[1];
          v9 = v16->m_golden_box_event.m_ini.m_wYear[1];
          v8 = v16->m_golden_box_event.m_ini.m_byMinute[0];
          v7 = v16->m_golden_box_event.m_ini.m_byHour[0];
          v6 = v16->m_golden_box_event.m_ini.m_byDay[0];
          v5 = v16->m_golden_box_event.m_ini.m_byMonth[0];
          CLogFile::Write(
            &v16->m_golden_box_event.m_event_log,
            "[golden_box_item_event Loaded] [EventState : %d] [EventTime : %d/%d/%d %d:%d  ~ %d/%d/%d %d:%d ]",
            v3,
            v16->m_golden_box_event.m_ini.m_wYear[0]);
        }
      }
      else
      {
        v15 = v16->m_golden_box_event.m_ini.m_EventTime[1] - Time;
        if ( v15 > 0 )
        {
          if ( v15 <= 0 )
            CGoldenBoxItemMgr::Set_Event_Status(v16, 0);
          else
            CGoldenBoxItemMgr::Set_Event_Status(v16, 2);
        }
        else
        {
          CGoldenBoxItemMgr::Set_Event_Status(v16, 3);
        }
      }
    }
    else
    {
      CGoldenBoxItemMgr::Set_Event_Status(v16, 3);
    }
  }
  else
  {
    CGoldenBoxItemMgr::Set_Event_Status(v16, 0);
  }
}
