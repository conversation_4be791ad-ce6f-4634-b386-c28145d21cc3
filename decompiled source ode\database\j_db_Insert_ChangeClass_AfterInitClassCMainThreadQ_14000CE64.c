/*
 * Function: j_?db_Insert_ChangeClass_AfterInitClass@CMainThread@@QEAAEKEPEAD0HEGEEEEE@Z
 * Address: 0x14000CE64
 */

char __fastcall CMainThread::db_Insert_ChangeClass_AfterInitClass(CMainThread *this, unsigned int dwCharacSerial, char byType, char *szPrevClassCode, char *szNextClassCode, int nClassInitCnt, char byLastClassGrade, unsigned __int16 dwYear, char byMonth, char byDay, char byHour, char byMin, char bySec)
{
  return CMainThread::db_Insert_ChangeClass_AfterInitClass(
           this,
           dwCharacSerial,
           byType,
           szPrevClassCode,
           szNextClassCode,
           nClassInitCnt,
           byLastClassGrade,
           dwYear,
           byMonth,
           byDay,
           byHour,
           byMin,
           bySec);
}
