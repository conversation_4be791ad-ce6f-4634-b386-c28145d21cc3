/*
 * Function: ?dev_loot_bag@CPlayer@@QEAA_NXZ
 * Address: 0x1400BD540
 */

char __fastcall CPlayer::dev_loot_bag(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@5
  int v4; // eax@6
  __int64 v6; // [sp+0h] [bp-58h]@1
  int nItemIndex; // [sp+30h] [bp-28h]@4
  char *v8; // [sp+38h] [bp-20h]@6
  _base_fld *v9; // [sp+40h] [bp-18h]@7
  CPlayer *pOwner; // [sp+60h] [bp+8h]@1

  pOwner = this;
  v1 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( nItemIndex = 0; ; ++nItemIndex )
  {
    v3 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 12);
    if ( nItemIndex >= v3 )
      break;
    v8 = GetItemEquipCivil(12, nItemIndex);
    v4 = CPlayerDB::GetRaceSexCode(&pOwner->m_Param);
    if ( v8[v4] == 49 )
    {
      v9 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 12, nItemIndex);
      loot_item(pOwner, v9->m_strCode, 4, 0i64, 0);
      return 1;
    }
  }
  return 1;
}
