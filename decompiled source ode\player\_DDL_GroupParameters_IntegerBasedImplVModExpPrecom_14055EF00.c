/*
 * Function: ??_D?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@QEAAXXZ
 * Address: 0x14055EF00
 */

void __fastcall CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::`vbase destructor(__int64 a1)
{
  __int64 v1; // [sp+30h] [bp+8h]@1

  v1 = a1;
  CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::~DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>();
  CryptoPP::CryptoMaterial::~CryptoMaterial((CryptoPP::CryptoMaterial *)(v1 + 232));
}
