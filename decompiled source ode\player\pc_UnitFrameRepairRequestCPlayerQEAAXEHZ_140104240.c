/*
 * Function: ?pc_UnitFrameRepairRequest@CPlayer@@QEAAXEH@Z
 * Address: 0x140104240
 */

void __usercall CPlayer::pc_UnitFrameRepairRequest(CPlayer *this@<rcx>, char by<PERSON>lotIndex@<dl>, int bUseNPCLinkIntem@<r8d>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v6; // eax@34
  unsigned int v7; // eax@38
  CMoneySupplyMgr *v8; // rax@45
  __int64 v9; // [sp+0h] [bp-B8h]@1
  char v10; // [sp+40h] [bp-78h]@4
  _UNIT_DB_BASE::_LIST *pData; // [sp+48h] [bp-70h]@4
  unsigned __int8 v12; // [sp+50h] [bp-68h]@4
  _base_fld *v13; // [sp+58h] [bp-60h]@4
  unsigned int dwSub; // [sp+60h] [bp-58h]@4
  unsigned int dwNewGauge; // [sp+64h] [bp-54h]@4
  unsigned int v16; // [sp+68h] [bp-50h]@4
  float v17; // [sp+6Ch] [bp-4Ch]@4
  int j; // [sp+70h] [bp-48h]@26
  unsigned int v19; // [sp+74h] [bp-44h]@4
  _base_fld *v20; // [sp+78h] [bp-40h]@10
  int v21; // [sp+80h] [bp-38h]@26
  _base_fld *v22; // [sp+88h] [bp-30h]@28
  unsigned __int64 v23; // [sp+90h] [bp-28h]@34
  int v24; // [sp+98h] [bp-20h]@41
  char *v25; // [sp+A0h] [bp-18h]@38
  unsigned int v26; // [sp+A8h] [bp-10h]@38
  int nLv; // [sp+ACh] [bp-Ch]@45
  CPlayer *p; // [sp+C0h] [bp+8h]@1
  char v29; // [sp+C8h] [bp+10h]@1
  int v30; // [sp+D0h] [bp+18h]@1

  v30 = bUseNPCLinkIntem;
  v29 = bySlotIndex;
  p = this;
  v4 = &v9;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v10 = 0;
  pData = &p->m_Param.m_UnitDB.m_List[(unsigned __int8)bySlotIndex];
  v12 = p->m_Param.m_UnitDB.m_List[(unsigned __int8)bySlotIndex].byFrame;
  v13 = CRecordData::GetRecord(&stru_1799C8BA0, v12);
  dwSub = 0;
  dwNewGauge = 0;
  v16 = eGetTexRate(0) + 10000;
  eGetTex(0);
  v17 = a4 + 1.0;
  v19 = 0;
  if ( p->m_pUserDB )
  {
    if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, p->m_id.wIndex) == 99 )
    {
      v10 = 34;
    }
    else if ( v30 || IsBeNearStore(p, 4) )
    {
      v20 = CRecordData::GetRecord(&stru_1799C8BA0, v12);
      if ( v20 )
      {
        if ( *(_DWORD *)&v20[1].m_strCode[60] )
        {
          if ( CPlayerDB::GetRaceCode(&p->m_Param) )
          {
            v10 = 1;
          }
          else if ( p->m_pUsingUnit )
          {
            v10 = 2;
          }
          else if ( v13 )
          {
            if ( v12 == 255 )
            {
              v10 = 5;
            }
            else
            {
              dwNewGauge = *(_DWORD *)&v13[1].m_strCode[0];
              v19 = dwNewGauge - pData->dwGauge;
              if ( (signed int)v19 > 0 )
              {
                if ( *(_DWORD *)&v13[1].m_strCode[52] > 0 )
                  dwSub = *(_DWORD *)&v13[1].m_strCode[52];
                v21 = 0;
                for ( j = 0; j < 6; ++j )
                {
                  v22 = CRecordData::GetRecord(&stru_1799C86D0 + j, pData->byPart[j]);
                  if ( v22 )
                  {
                    if ( *(_DWORD *)&v22[5].m_strCode[48] > 0 )
                      dwSub += *(_DWORD *)&v22[5].m_strCode[48];
                    if ( !pData->dwGauge )
                      v21 += *(_DWORD *)&v22[5].m_strCode[52];
                  }
                }
                v23 = v16
                    * (unsigned __int64)(unsigned int)(signed int)ffloor((float)(signed int)dwSub * (float)((float)(signed int)(dwNewGauge - pData->dwGauge) * p->m_fUnitPv_RepPr));
                dwSub = v21 + v23 / 0x2710;
                v6 = CPlayerDB::GetDalant(&p->m_Param);
                if ( dwSub > v6 )
                  v10 = 7;
              }
              else
              {
                v10 = 14;
              }
            }
          }
          else
          {
            v10 = 9;
          }
        }
        else
        {
          v10 = 37;
        }
      }
      else
      {
        v10 = 5;
      }
    }
    else
    {
      v10 = 21;
    }
    if ( !v10 )
    {
      pData->dwGauge = dwNewGauge;
      CPlayer::SubDalant(p, dwSub);
      CUserDB::Update_UnitData(p->m_pUserDB, v29, pData);
      if ( dwSub )
      {
        v25 = p->m_szItemHistoryFileName;
        v26 = CPlayerDB::GetGold(&p->m_Param);
        v7 = CPlayerDB::GetDalant(&p->m_Param);
        CMgrAvatorItemHistory::pay_money(
          &CPlayer::s_MgrItemHistory,
          p->m_ObjID.m_wIndex,
          "Unit_Repair",
          dwSub,
          0,
          v7,
          v26,
          v25);
      }
      if ( !p->m_byUserDgr )
        eAddDalant(0, dwSub);
      v24 = CPlayerDB::GetLevel(&p->m_Param);
      if ( v24 == 30 || v24 == 40 || v24 == 50 || v24 == 60 )
      {
        nLv = CPlayerDB::GetLevel(&p->m_Param);
        v8 = CMoneySupplyMgr::Instance();
        CMoneySupplyMgr::UpdateUnitRepairingChargesData(v8, nLv, dwSub);
      }
    }
    CPlayer::SendMsg_UnitFrameRepairResult(p, v10, v29, dwNewGauge, dwSub);
  }
}
