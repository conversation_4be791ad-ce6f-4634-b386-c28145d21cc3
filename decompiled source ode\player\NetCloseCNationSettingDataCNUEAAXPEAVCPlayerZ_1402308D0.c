/*
 * Function: ?NetClose@CNationSettingDataCN@@UEAAXPEAVCPlayer@@@Z
 * Address: 0x1402308D0
 */

void __fastcall CNationSettingDataCN::NetClose(CNationSettingDataCN *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CChiNetworkEX *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *pOnea; // [sp+38h] [bp+10h]@1

  pOnea = pOne;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = CChiNetworkEX::Instance();
  CChiNetworkEX::Send_Logout(v4, pOnea);
}
