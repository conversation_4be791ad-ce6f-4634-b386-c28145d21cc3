/*
 * Function: ?initialize@AutominePersonalMgr@@QEAA_NXZ
 * Address: 0x1402DE760
 */

char __fastcall AutominePersonalMgr::initialize(AutominePersonalMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@4
  unsigned int v4; // eax@4
  char result; // al@7
  __int64 v6; // [sp+0h] [bp-F8h]@1
  char _Dest[128]; // [sp+40h] [bp-B8h]@4
  int j; // [sp+C4h] [bp-34h]@4
  _base_fld *v9; // [sp+C8h] [bp-30h]@6
  CRecordData *v10; // [sp+D8h] [bp-20h]@6
  unsigned __int64 v11; // [sp+E0h] [bp-18h]@4
  AutominePersonalMgr *v12; // [sp+100h] [bp+8h]@1

  v12 = this;
  v1 = &v6;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11 = (unsigned __int64)&v6 ^ _security_cookie;
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0x7Fui64);
  v3 = GetKorLocalTime();
  sprintf_s<128>((char (*)[128])_Dest, "..\\ZoneServerLog\\SystemLog\\Concession\\ampersonalmgr_%d.log", v3);
  CLogFile::SetWriteLogFile(&v12->m_logService, _Dest, 1, 0, 1, 1);
  v4 = GetKorLocalTime();
  sprintf_s<128>((char (*)[128])_Dest, "..\\ZoneServerLog\\systemlog\\log_ampersonalmgr_%d.log", v4);
  CLogFile::SetWriteLogFile(&v12->m_logError, _Dest, 1, 0, 1, 1);
  for ( j = 0; j < 5; ++j )
  {
    v10 = (CRecordData *)((char *)&unk_1799C6AA0 + 2992);
    v9 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 17, *(const char **)&s_szAMPOreKind[8 * j]);
    if ( !v9 )
      return 0;
    v12->m_wOreIndex[j] = v9->m_dwIndex;
  }
  if ( AutominePersonalMgr::init_objects(v12) )
  {
    result = 1;
  }
  else
  {
    CLogFile::Write(&v12->m_logError, "AutominePersonalMgr::initialize() >> init_objects() failed.");
    result = 0;
  }
  return result;
}
