/*
 * Function: ??$_Unchecked_move_backward@PEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@PEAU12@@stdext@@YAPEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@PEAU12@00@Z
 * Address: 0x14059EB00
 */

int __fastcall stdext::_Unchecked_move_backward<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *>(__int64 a1, __int64 a2, __int64 a3)
{
  _BYTE *v3; // rax@1
  char v5; // [sp+30h] [bp-18h]@1
  char v6; // [sp+31h] [bp-17h]@1
  char v7; // [sp+32h] [bp-16h]@1
  __int64 v8; // [sp+50h] [bp+8h]@1
  __int64 v9; // [sp+58h] [bp+10h]@1
  __int64 v10; // [sp+60h] [bp+18h]@1

  v10 = a3;
  v9 = a2;
  v8 = a1;
  memset(&v5, 0, sizeof(v5));
  v6 = std::_Move_cat<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *>(&v10);
  LODWORD(v3) = std::_Iter_random<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *>(
                  &v7,
                  &v8,
                  &v10);
  return std::_Move_backward_opt<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,std::random_access_iterator_tag,std::_Undefined_move_tag>(
           v8,
           v9,
           v10,
           *v3);
}
