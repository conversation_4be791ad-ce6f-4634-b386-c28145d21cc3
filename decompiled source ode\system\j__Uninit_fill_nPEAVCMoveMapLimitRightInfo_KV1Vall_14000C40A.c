/*
 * Function: j_??$_Uninit_fill_n@PEAVCMoveMapLimitRightInfo@@_KV1@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@YAXPEAVCMoveMapLimitRightInfo@@_KAEBV1@AEAV?$allocator@VCMoveMapLimitRightInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000C40A
 */

void __fastcall std::_Uninit_fill_n<CMoveMapLimitRightInfo *,unsigned __int64,CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(CMoveMapLimitRightInfo *_First, unsigned __int64 _Count, CMoveMapLimitRightInfo *_Val, std::allocator<CMoveMapLimitRightInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CMoveMapLimitRightInfo *,unsigned __int64,CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(
    _First,
    _Count,
    _Val,
    _Al,
    __formal,
    a6);
}
