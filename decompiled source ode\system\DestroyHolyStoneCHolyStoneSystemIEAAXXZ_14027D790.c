/*
 * Function: ?DestroyHolyStone@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x14027D790
 */

void __fastcall CHolyStoneSystem::DestroyHolyStone(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-58h]@1
  int j; // [sp+40h] [bp-18h]@4
  CPlayer *v5; // [sp+48h] [bp-10h]@12
  CHolyStoneSystem *v6; // [sp+60h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < v6->m_nHolyStoneNum; ++j )
  {
    if ( g_Stone[j].m_bLive )
      CHolyStone::SetOper(&g_Stone[j], 0, 1.0);
  }
  CHolyStoneSystem::InitQuestCash(v6);
  for ( j = 0; j < 2532; ++j )
  {
    v5 = &g_Player + j;
    if ( v5->m_bLive )
      CPlayer::RecvHSKQuest(v5, 100, 1, 0, 0, 0, -1);
  }
}
