/*
 * Function: ?ThrowIfInvalidTruncatedSize@HashTransformation@CryptoPP@@IEBAX_K@Z
 * Address: 0x1405F4420
 */

void __fastcall CryptoPP::HashTransformation::ThrowIfInvalidTruncatedSize(CryptoPP::HashTransformation *this, unsigned __int64 a2)
{
  __int64 v2; // rax@2
  int v3; // eax@2
  __int64 v4; // rax@2
  __int64 v5; // rax@2
  __int64 v6; // rax@2
  __int64 v7; // rax@2
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v8; // rax@2
  CryptoPP::InvalidArgument v9; // [sp+20h] [bp-1E8h]@2
  char v10; // [sp+70h] [bp-198h]@2
  char v11; // [sp+A0h] [bp-168h]@2
  char v12; // [sp+D0h] [bp-138h]@2
  char v13; // [sp+100h] [bp-108h]@2
  char v14; // [sp+130h] [bp-D8h]@2
  char v15; // [sp+160h] [bp-A8h]@2
  __int64 v16; // [sp+190h] [bp-78h]@1
  __int64 v17; // [sp+198h] [bp-70h]@2
  __int64 v18; // [sp+1A0h] [bp-68h]@2
  __int64 v19; // [sp+1A8h] [bp-60h]@2
  __int64 v20; // [sp+1B0h] [bp-58h]@2
  __int64 v21; // [sp+1B8h] [bp-50h]@2
  __int64 v22; // [sp+1C0h] [bp-48h]@2
  __int64 v23; // [sp+1C8h] [bp-40h]@2
  __int64 v24; // [sp+1D0h] [bp-38h]@2
  __int64 v25; // [sp+1D8h] [bp-30h]@2
  __int64 v26; // [sp+1E0h] [bp-28h]@2
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v27; // [sp+1E8h] [bp-20h]@2
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *s; // [sp+1F0h] [bp-18h]@2
  CryptoPP::HashTransformation *v29; // [sp+210h] [bp+8h]@1
  unsigned __int64 v30; // [sp+218h] [bp+10h]@1

  v30 = a2;
  v29 = this;
  v16 = -2i64;
  if ( a2 > (unsigned int)((int (*)(void))this->vfptr[3].Clone)() )
  {
    LODWORD(v2) = CryptoPP::IntToString<unsigned __int64>(&v10, v30, 10i64);
    v17 = v2;
    v18 = v2;
    v3 = ((int (__fastcall *)(CryptoPP::HashTransformation *))v29->vfptr[3].Clone)(v29);
    LODWORD(v4) = CryptoPP::IntToString<unsigned int>(&v11, (unsigned int)v3, 10i64);
    v19 = v4;
    v20 = v4;
    LODWORD(v5) = std::operator+<char,std::char_traits<char>,std::allocator<char>>(
                    &v12,
                    "HashTransformation: can't truncate a ",
                    v4);
    v21 = v5;
    v22 = v5;
    LODWORD(v6) = std::operator+<char,std::char_traits<char>,std::allocator<char>>(&v13, v5, " byte digest to ");
    v23 = v6;
    v24 = v6;
    LODWORD(v7) = std::operator+<char,std::char_traits<char>,std::allocator<char>>(&v14, v6, v18);
    v25 = v7;
    v26 = v7;
    LODWORD(v8) = std::operator+<char,std::char_traits<char>,std::allocator<char>>(&v15, v7, " bytes");
    v27 = v8;
    s = v8;
    CryptoPP::InvalidArgument::InvalidArgument(&v9, v8);
    CxxThrowException_0((__int64)&v9, (__int64)&TI3_AVInvalidArgument_CryptoPP__);
  }
}
