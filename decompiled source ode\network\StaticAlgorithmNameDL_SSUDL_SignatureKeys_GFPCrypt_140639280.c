/*
 * Function: ?StaticAlgorithmName@?$DL_SS@UDL_SignatureKeys_GFP@CryptoPP@@V?$DL_Algorithm_GDSA@VInteger@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@H@CryptoPP@@SA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
 * Address: 0x140639280
 */

__int64 __fastcall CryptoPP::DL_SS<CryptoPP::DL_SignatureKeys_GFP,CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1,int>::StaticAlgorithmName(__int64 a1)
{
  __int64 v1; // rax@1
  const char *v2; // rax@1
  __int64 v3; // rax@1
  __int64 v4; // rax@1
  char v6; // [sp+20h] [bp-E8h]@1
  unsigned __int8 v7; // [sp+50h] [bp-B8h]@1
  char v8; // [sp+58h] [bp-B0h]@1
  char v9; // [sp+88h] [bp-80h]@1
  int v10; // [sp+B8h] [bp-50h]@1
  __int64 v11; // [sp+C0h] [bp-48h]@1
  const char *v12; // [sp+C8h] [bp-40h]@1
  __int64 v13; // [sp+D0h] [bp-38h]@1
  __int64 v14; // [sp+D8h] [bp-30h]@1
  __int64 v15; // [sp+E0h] [bp-28h]@1
  __int64 v16; // [sp+E8h] [bp-20h]@1
  __int64 v17; // [sp+F0h] [bp-18h]@1
  __int64 v18; // [sp+F8h] [bp-10h]@1
  __int64 v19; // [sp+110h] [bp+8h]@1

  v19 = a1;
  v11 = -2i64;
  v10 = 0;
  memset(&v7, 0, sizeof(v7));
  v12 = CryptoPP::SHA1::StaticAlgorithmName();
  LODWORD(v1) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
                  &v6,
                  "/EMSA1(",
                  v7);
  v13 = v1;
  v14 = v1;
  v2 = CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>::StaticAlgorithmName();
  LODWORD(v3) = std::operator+<char,std::char_traits<char>,std::allocator<char>>(&v8, v2, v14);
  v15 = v3;
  v16 = v3;
  LODWORD(v4) = std::operator+<char,std::char_traits<char>,std::allocator<char>>(&v9, v3, v12);
  v17 = v4;
  v18 = v4;
  std::operator+<char,std::char_traits<char>,std::allocator<char>>(v19, v4, ")");
  v10 |= 1u;
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(&v9);
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(&v8);
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(&v6);
  return v19;
}
