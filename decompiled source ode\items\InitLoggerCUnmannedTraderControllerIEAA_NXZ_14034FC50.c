/*
 * Function: ?InitLogger@CUnmannedTraderController@@IEAA_NXZ
 * Address: 0x14034FC50
 */

char __fastcall CUnmannedTraderController::InitLogger(CUnmannedTraderController *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@5
  char result; // al@8
  __int64 v5; // rax@10
  CUnmannedTraderGroupItemInfoTable *v6; // rax@14
  CUnmannedTraderUserInfoTable *v7; // rax@14
  CUnmannedTraderScheduler *v8; // rax@14
  __int64 v9; // [sp+0h] [bp-128h]@1
  bool bDate; // [sp+20h] [bp-108h]@9
  bool bAddCount; // [sp+28h] [bp-100h]@9
  unsigned int v12; // [sp+30h] [bp-F8h]@9
  char _Dest[128]; // [sp+50h] [bp-D8h]@9
  CLogFile *v14; // [sp+E0h] [bp-48h]@7
  CLogFile *v15; // [sp+E8h] [bp-40h]@4
  CLogFile *v16; // [sp+F0h] [bp-38h]@12
  CLogFile *v17; // [sp+F8h] [bp-30h]@9
  __int64 v18; // [sp+100h] [bp-28h]@4
  CLogFile *v19; // [sp+108h] [bp-20h]@5
  CLogFile *v20; // [sp+110h] [bp-18h]@10
  unsigned __int64 v21; // [sp+118h] [bp-10h]@4
  CUnmannedTraderController *v22; // [sp+130h] [bp+8h]@1

  v22 = this;
  v1 = &v9;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v18 = -2i64;
  v21 = (unsigned __int64)&v9 ^ _security_cookie;
  CreateDirectoryA("..\\ZoneServerLog\\Systemlog\\UnmannedTrader", 0i64);
  clear_file("..\\ZoneServerLog\\Systemlog\\UnmannedTrader", 0xFu);
  v15 = (CLogFile *)operator new(0xB8ui64);
  if ( v15 )
  {
    CLogFile::CLogFile(v15);
    v19 = (CLogFile *)v3;
  }
  else
  {
    v19 = 0i64;
  }
  v14 = v19;
  v22->m_pkLogger = v19;
  if ( v22->m_pkLogger )
  {
    v12 = GetKorLocalTime();
    sprintf_s<128>((char (*)[128])_Dest, "..\\ZoneServerLog\\Systemlog\\UnmannedTrader\\UnmannedTrader%d.log", v12);
    bAddCount = 1;
    bDate = 1;
    CLogFile::SetWriteLogFile(v22->m_pkLogger, _Dest, 1, 0, 1, 1);
    CreateDirectoryA("..\\ServiceLog\\UnmannedTrader", 0i64);
    clear_file("..\\ServiceLog\\UnmannedTrader", 0xFu);
    v17 = (CLogFile *)operator new(0xB8ui64);
    if ( v17 )
    {
      CLogFile::CLogFile(v17);
      v20 = (CLogFile *)v5;
    }
    else
    {
      v20 = 0i64;
    }
    v16 = v20;
    v22->m_pkServiceLogger = v20;
    if ( v22->m_pkServiceLogger )
    {
      sprintf_s<128>(
        (char (*)[128])_Dest,
        "..\\ZoneServerLog\\ServiceLog\\UnmannedTrader\\UnmannedTraderService.log",
        v12);
      bAddCount = 1;
      bDate = 1;
      CLogFile::SetWriteLogFile(v22->m_pkServiceLogger, _Dest, 1, 0, 1, 1);
      v6 = CUnmannedTraderGroupItemInfoTable::Instance();
      CUnmannedTraderGroupItemInfoTable::SetLogger(v6, v22->m_pkLogger);
      v7 = CUnmannedTraderUserInfoTable::Instance();
      CUnmannedTraderUserInfoTable::SetLogger(v7, v22->m_pkLogger, v22->m_pkServiceLogger);
      v8 = CUnmannedTraderScheduler::Instance();
      CUnmannedTraderScheduler::SetLogger(v8, v22->m_pkLogger);
      result = 1;
    }
    else
    {
      CLogFile::Write(&stru_1799C8F30, "CUnmannedTraderController::InitLogger() NULL == new CLogFile!\r\n");
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(&stru_1799C8F30, "CUnmannedTraderController::InitLogger() NULL == new CLogFile!\r\n");
    result = 0;
  }
  return result;
}
