/*
 * Function: sub_14055FDB0
 * Address: 0x14055FDB0
 */

int __fastcall sub_14055FDB0(__int64 a1, __int64 a2)
{
  if ( *(_QWORD *)(a2 + 64) )
    *(_QWORD *)(a2 + 40) = *(_QWORD *)(a2 + 64) + 8i64;
  else
    *(_QWORD *)(a2 + 40) = 0i64;
  return CryptoPP::DL_KeyImpl<CryptoPP::X509PublicKey,CryptoPP::DL_GroupParameters_DSA,CryptoPP::OID>::~DL_KeyImpl<CryptoPP::X509PublicKey,CryptoPP::DL_GroupParameters_DSA,CryptoPP::OID>(*(_QWORD *)(a2 + 40));
}
