/*
 * Function: ?DisjointParty@CPartyPlayer@@QEAA_NXZ
 * Address: 0x140045190
 */

char __fastcall CPartyPlayer::DisjointParty(CPartyPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@8
  CPlayer *v6; // [sp+28h] [bp-20h]@11
  int k; // [sp+30h] [bp-18h]@15
  CPartyPlayer *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CPartyPlayer::IsPartyBoss(v8) )
  {
    if ( v8->m_pDarkHole )
    {
      CDarkHole::Destroy(v8->m_pDarkHole);
      v8->m_pDarkHole = 0i64;
    }
    for ( j = 0; j < 8 && v8->m_pPartyMember[j]; ++j )
    {
      v6 = &g_Player + v8->m_pPartyMember[j]->m_wZoneIndex;
      if ( v6->m_bOper )
      {
        if ( CPlayer::GetGroupTarget(v6, 0)->pObject )
          CPlayer::SendMsg_ReleaseGroupTargetObjectResult(v6, 0);
      }
    }
    for ( k = 1; k < 8 && v8->m_pPartyMember[k]; ++k )
      CPartyPlayer::PartyListInit(v8->m_pPartyMember[k]);
    CPartyPlayer::PartyListInit(v8);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
