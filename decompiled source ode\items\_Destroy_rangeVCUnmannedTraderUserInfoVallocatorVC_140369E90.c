/*
 * Function: ??$_Destroy_range@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@YAXPEAVCUnmannedTraderUserInfo@@0AEAV?$allocator@VCUnmannedTraderUserInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@@Z
 * Address: 0x140369E90
 */

void __fastcall std::_Destroy_range<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(CUnmannedTraderUserInfo *_First, CUnmannedTraderUserInfo *_Last, std::allocator<CUnmannedTraderUserInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CUnmannedTraderUserInfo *_Ptr; // [sp+30h] [bp+8h]@1
  CUnmannedTraderUserInfo *v8; // [sp+38h] [bp+10h]@1
  std::allocator<CUnmannedTraderUserInfo> *v9; // [sp+40h] [bp+18h]@1

  v9 = _Al;
  v8 = _Last;
  _Ptr = _First;
  v4 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  while ( _Ptr != v8 )
  {
    std::allocator<CUnmannedTraderUserInfo>::destroy(v9, _Ptr);
    ++_Ptr;
  }
}
