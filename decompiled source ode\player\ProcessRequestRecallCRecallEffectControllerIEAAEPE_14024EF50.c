/*
 * Function: ?ProcessRequestRecall@CRecallEffectController@@IEAAEPEAVCPlayer@@PEAVCCharacter@@AEAPEAVCRecallRequest@@_N33@Z
 * Address: 0x14024EF50
 */

char __fastcall CRecallEffectController::ProcessRequestRecall(CRecallEffectController *this, CPlayer *pkPerformer, CCharacter *pkDest, CRecallRequest **pkRequest, bool bRecallParty, bool bStone, bool bBattleModeUse)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned __int16 v10; // ax@8
  __int64 v11; // [sp+0h] [bp-48h]@1
  bool v12; // [sp+20h] [bp-28h]@6
  bool v13; // [sp+28h] [bp-20h]@6
  char v14; // [sp+30h] [bp-18h]@6
  CRecallEffectController *v15; // [sp+50h] [bp+8h]@1
  CPlayer *pkObj; // [sp+58h] [bp+10h]@1
  CCharacter *pkDesta; // [sp+60h] [bp+18h]@1
  CRecallRequest **v18; // [sp+68h] [bp+20h]@1

  v18 = pkRequest;
  pkDesta = pkDest;
  pkObj = pkPerformer;
  v15 = this;
  v7 = &v11;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  *pkRequest = CRecallEffectController::GetEmpty(v15);
  if ( *v18 )
  {
    v13 = bBattleModeUse;
    v12 = bStone;
    v14 = CRecallRequest::Regist(*v18, pkObj, pkDesta, bRecallParty, bStone, bBattleModeUse);
    if ( v14 )
    {
      result = v14;
    }
    else
    {
      v10 = CRecallRequest::GetID(*v18);
      if ( CNetIndexList::PushNode_Back(v15->m_pkUseInxList, v10) )
      {
        result = 0;
      }
      else
      {
        CRecallEffectController::Close(v15, *v18, 0);
        result = 2;
      }
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
