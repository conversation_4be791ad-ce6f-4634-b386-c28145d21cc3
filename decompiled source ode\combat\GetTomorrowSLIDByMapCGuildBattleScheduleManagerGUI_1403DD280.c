/*
 * Function: ?GetTomorrowSLIDByMap@CGuildBattleScheduleManager@GUILD_BATTLE@@QEAA_NIAEAI@Z
 * Address: 0x1403DD280
 */

bool __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::GetTomorrowSLIDByMap(GUILD_BATTLE::CGuildBattleScheduleManager *this, unsigned int uiMap, unsigned int *uiSLID)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleScheduleManager *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::GetSLID(v7->m_pkTomorrowSchedule, uiMap, uiSLID);
}
