/*
 * Function: j_??0?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAA@PEAPEAVCLogTypeDBTask@@@Z
 * Address: 0x14000B5D7
 */

void __fastcall std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, CLogTypeDBTask **_Ptr)
{
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(
    this,
    _Ptr);
}
