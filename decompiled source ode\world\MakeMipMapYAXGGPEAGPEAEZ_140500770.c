/*
 * Function: ?MakeMipMap@@YAXGGPEAGPEAE@Z
 * Address: 0x140500770
 */

void __fastcall MakeMipMap(unsigned __int16 a1, __int64 a2, unsigned __int16 *a3, unsigned __int8 *a4)
{
  unsigned __int16 *v4; // rbp@1
  int v5; // er8@1
  __int64 v6; // rbx@1
  int v7; // ecx@1
  __int64 v8; // r15@1
  unsigned __int8 *v9; // r10@2
  unsigned __int8 *v10; // rdi@2
  signed __int64 v11; // r11@2
  __int64 v12; // rax@2
  unsigned __int8 *v13; // rsi@2
  unsigned __int8 *v14; // r12@4
  unsigned __int8 *v15; // r13@4
  _BYTE *v16; // r14@4
  unsigned __int16 *v17; // r11@4
  unsigned __int8 *v18; // rbp@4
  int v19; // er8@5
  int v20; // ecx@5
  int v21; // edx@5
  int v22; // er9@5
  int v23; // er8@5
  int v24; // eax@5
  int v25; // ecx@5
  int v26; // eax@5
  __int64 v27; // [sp+0h] [bp-58h]@2
  signed __int64 v28; // [sp+10h] [bp-48h]@2
  unsigned __int16 v29; // [sp+60h] [bp+8h]@1
  int v30; // [sp+68h] [bp+10h]@1
  unsigned __int16 *v31; // [sp+70h] [bp+18h]@1

  v31 = a3;
  v29 = a1;
  v4 = a3;
  v5 = a1;
  a2 = (unsigned __int16)a2;
  v6 = 0i64;
  v7 = 2 * a1;
  LODWORD(v8) = v5;
  v30 = v5;
  if ( (signed int)(unsigned __int16)a2 > 0 )
  {
    v9 = a4 + 2;
    v10 = &a4[4 * v7 + 2];
    v11 = 8i64 * v7;
    v28 = 8i64 * v7;
    v12 = a2;
    v13 = &a4[4 * (v7 + 1) + 1];
    v27 = a2;
    do
    {
      if ( (signed int)v8 > 0 )
      {
        v14 = v9;
        v15 = v13;
        v16 = v9 + 4;
        v17 = &v4[v6 * (unsigned __int16)v5];
        v8 = (unsigned int)v8;
        v18 = v10;
        do
        {
          v19 = *(v14 - 1);
          v20 = *v14;
          v21 = v15[1];
          v22 = *(v16 - 2) + *(v14 - 2);
          ++v17;
          v14 += 8;
          v23 = *(v16 - 1) + v19;
          v24 = *v16;
          v16 += 8;
          v25 = v24 + v20;
          v26 = *(v18 - 2);
          v18 += 8;
          v15 += 8;
          --v8;
          *(v17 - 1) = ((v26 + v22 + (unsigned int)*(v15 - 9)) >> 5) | 32
                                                                     * (((*(v18 - 9) + v23 + (unsigned int)*(v15 - 8)) >> 4) | ((unsigned __int16)(((unsigned int)*(v18 - 8) + v25 + v21) >> 5) << 6));
        }
        while ( v8 );
        v11 = v28;
        LODWORD(v8) = v30;
        v12 = v27;
        LOWORD(v5) = v29;
        v4 = v31;
      }
      v10 += v11;
      v13 += v11;
      v9 += v11;
      ++v6;
      v27 = --v12;
    }
    while ( v12 );
  }
}
