/*
 * Function: ?CompleteUpdateState@CUnmannedTraderUserInfoTable@@QEAA_NKKE@Z
 * Address: 0x140363AB0
 */

bool __fastcall CUnmannedTraderUserInfoTable::CompleteUpdateState(CUnmannedTraderUserInfoTable *this, unsigned int dwOwnerSerial, unsigned int dwRegistSerial, char byState)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  CUnmannedTraderUserInfo *v8; // [sp+20h] [bp-18h]@4
  CUnmannedTraderUserInfoTable *v9; // [sp+40h] [bp+8h]@1
  unsigned int dwRegistSeriala; // [sp+50h] [bp+18h]@1
  char v11; // [sp+58h] [bp+20h]@1

  v11 = byState;
  dwRegistSeriala = dwRegistSerial;
  v9 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = CUnmannedTraderUserInfoTable::Find(v9, dwOwnerSerial);
  if ( CUnmannedTraderUserInfo::IsNull(v8) )
    result = 0;
  else
    result = CUnmannedTraderUserInfo::CompleteUpdateState(v8, dwRegistSeriala, v11, 1);
  return result;
}
