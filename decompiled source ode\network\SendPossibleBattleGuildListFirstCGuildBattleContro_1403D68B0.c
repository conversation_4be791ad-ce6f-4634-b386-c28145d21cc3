/*
 * Function: ?SendPossibleBattleGuildListFirst@CGuildBattleController@@QEAAXHE@Z
 * Address: 0x1403D68B0
 */

void __fastcall CGuildBattleController::SendPossibleBattleGuildListFirst(CGuildBattleController *this, int n, char byRace)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CPossibleBattleGuildListManager *v6; // [sp+20h] [bp-18h]@4
  int na; // [sp+48h] [bp+10h]@1
  char v8; // [sp+50h] [bp+18h]@1

  v8 = byRace;
  na = n;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = GUILD_BATTLE::CPossibleBattleGuildListManager::Instance();
  GUILD_BATTLE::CPossibleBattleGuildListManager::SendFirst(v6, na, v8);
}
