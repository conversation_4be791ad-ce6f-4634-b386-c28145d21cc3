/*
 * Function: ?Update_RaceWarRecvr@CPvpOrderView@@QEAAX_N@Z
 * Address: 0x1403F8140
 */

void __fastcall CPvpOrderView::Update_RaceWarRecvr(CPvpOrderView *this, bool bUse)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  __int64 _Time; // [sp+28h] [bp-20h]@5
  CPvpOrderView *v6; // [sp+50h] [bp+8h]@1
  bool v7; // [sp+58h] [bp+10h]@1

  v7 = bUse;
  v6 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v6->m_pkInfo )
  {
    _Time = 0i64;
    time_20(&_Time);
    v6->m_pkInfo->tUpdatedate = _Time;
    v6->m_pkInfo->bRaceWarRecvr = v7;
  }
}
