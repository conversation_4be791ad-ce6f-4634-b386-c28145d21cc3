/*
 * Function: j_??$_Uninit_copy@PEAURoomCharInfo@@PEAU1@V?$allocator@URoomCharInfo@@@std@@@std@@YAPEAURoomCharInfo@@PEAU1@00AEAV?$allocator@URoomCharInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140008251
 */

RoomCharInfo *__fastcall std::_Uninit_copy<RoomCharInfo *,RoomCharInfo *,std::allocator<RoomCharInfo>>(RoomCharInfo *_First, RoomCharInfo *_Last, RoomCharInfo *_Dest, std::allocator<RoomCharInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<RoomCharInfo *,RoomCharInfo *,std::allocator<RoomCharInfo>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
