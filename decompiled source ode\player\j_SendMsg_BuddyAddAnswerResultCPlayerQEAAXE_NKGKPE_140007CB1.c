/*
 * Function: j_?SendMsg_BuddyAddAnswerResult@CPlayer@@QEAAXE_NKGKPEAD@Z
 * Address: 0x140007CB1
 */

void __fastcall CPlayer::SendMsg_BuddyAddAnswerResult(CPlayer *this, char byRet<PERSON><PERSON>, bool bAccept, unsigned int dwAskerSerial, unsigned __int16 wIndex, unsigned int dwSerial, char *pwszCharName)
{
  CPlayer::SendMsg_BuddyAddAnswerResult(this, byRetCode, bAccept, dwAskerSerial, wIndex, dwSerial, pwszCharName);
}
