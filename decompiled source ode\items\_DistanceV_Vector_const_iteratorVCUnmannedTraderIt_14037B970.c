/*
 * Function: ??$_Distance@V?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@_K@std@@YAXV?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@0@0AEA_K@Z
 * Address: 0x14037B970
 */

void __fastcall std::_Distance<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,unsigned __int64>(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_First, std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_Last, unsigned __int64 *_Off)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  std::random_access_iterator_tag *v5; // rax@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v6; // rax@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v7; // rax@4
  __int64 v8; // [sp+0h] [bp-98h]@1
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > __formal; // [sp+20h] [bp-78h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v10; // [sp+40h] [bp-58h]@4
  char v11; // [sp+48h] [bp-50h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v12; // [sp+60h] [bp-38h]@4
  __int64 v13; // [sp+68h] [bp-30h]@4
  std::random_access_iterator_tag *v14; // [sp+70h] [bp-28h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v15; // [sp+78h] [bp-20h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v16; // [sp+80h] [bp-18h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v17; // [sp+88h] [bp-10h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v18; // [sp+A0h] [bp+8h]@1
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *__that; // [sp+A8h] [bp+10h]@1
  unsigned __int64 *v20; // [sp+B0h] [bp+18h]@1

  v20 = _Off;
  __that = _Last;
  v18 = _First;
  v3 = &v8;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = -2i64;
  v10 = (std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)((char *)&__formal + 8);
  v12 = (std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)&v11;
  LOBYTE(v5) = std::_Iter_cat<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>>(&__formal);
  v14 = v5;
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(
    v10,
    __that);
  v15 = v6;
  v16 = v6;
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(
    v12,
    v18);
  v17 = v7;
  std::_Distance2<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,unsigned __int64>(
    v7,
    v16,
    v20,
    (std::random_access_iterator_tag)v14->0);
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(v18);
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(__that);
}
