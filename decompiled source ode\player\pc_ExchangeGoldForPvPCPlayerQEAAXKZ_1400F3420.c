/*
 * Function: ?pc_ExchangeGoldForPvP@CPlayer@@QEAAXK@Z
 * Address: 0x1400F3420
 */

void __fastcall CPlayer::pc_ExchangeGoldForPvP(CPlayer *this, unsigned int dwGold)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  double v4; // xmm0_8@4
  unsigned int v5; // eax@7
  unsigned int v6; // eax@10
  __int64 v7; // [sp+0h] [bp-58h]@1
  char v8; // [sp+30h] [bp-28h]@4
  double v9; // [sp+38h] [bp-20h]@4
  char *v10; // [sp+40h] [bp-18h]@10
  unsigned int v11; // [sp+48h] [bp-10h]@10
  CPlayer *v12; // [sp+60h] [bp+8h]@1
  unsigned int ui64AddGold; // [sp+68h] [bp+10h]@1

  ui64AddGold = dwGold;
  v12 = this;
  v2 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = 0;
  v4 = (double)(signed int)(200 * dwGold);
  v9 = (double)(signed int)(200 * dwGold);
  CPlayerDB::GetPvPCashBag(&v12->m_Param);
  if ( v9 <= v4 )
  {
    if ( ui64AddGold )
    {
      v5 = CPlayerDB::GetGold(&v12->m_Param);
      if ( !CanAddMoneyForMaxLimGold(ui64AddGold, v5) )
        v8 = 2;
    }
  }
  else
  {
    v8 = 1;
  }
  if ( !v8 )
  {
    CPlayer::AlterPvPCashBag(v12, -0.0 - v9, 0);
    CPlayer::AddGold(v12, ui64AddGold, 1);
    v10 = v12->m_szItemHistoryFileName;
    v11 = CPlayerDB::GetGold(&v12->m_Param);
    v6 = CPlayerDB::GetDalant(&v12->m_Param);
    CMgrAvatorItemHistory::exchange_pvp_gold(
      &CPlayer::s_MgrItemHistory,
      v12->m_ObjID.m_wIndex,
      ui64AddGold,
      v6,
      v11,
      v10);
  }
  CPlayer::SendMsg_ExchangeMoneyResult(v12, v8);
}
