/*
 * Function: ?Send@CReservedGuildScheduleMapGroup@GUILD_BATTLE@@QEAAXHKEPEAVCReservedGuildSchedulePage@2@@Z
 * Address: 0x1403CCAD0
 */

void __fastcall GUILD_BATTLE::CReservedGuildScheduleMapGroup::Send(GUILD_BATTLE::CReservedGuildScheduleMapGroup *this, int n, unsigned int dwVer, char byPage, GUILD_BATTLE::CReservedGuildSchedulePage *pkSelfInfoPage)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CReservedGuildScheduleMapGroup *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v5 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( v8->m_byMaxPage > (signed int)(unsigned __int8)byPage )
    GUILD_BATTLE::CReservedGuildSchedulePage::Send(&v8->m_kList[(unsigned __int8)byPage], n, dwVer, pkSelfInfoPage);
}
