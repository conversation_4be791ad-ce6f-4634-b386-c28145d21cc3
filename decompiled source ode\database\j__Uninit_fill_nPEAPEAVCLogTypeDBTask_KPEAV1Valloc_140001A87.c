/*
 * Function: j_??$_Uninit_fill_n@PEAPEAVCLogTypeDBTask@@_KPEAV1@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@YAXPEAPEAVCLogTypeDBTask@@_KAEBQEAV1@AEAV?$allocator@PEAVCLogTypeDBTask@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140001A87
 */

void __fastcall std::_Uninit_fill_n<CLogTypeDBTask * *,unsigned __int64,CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(CLogTypeDBTask **_First, unsigned __int64 _Count, CLogTypeDBTask *const *_Val, std::allocator<CLogTypeDBTask *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CLogTypeDBTask * *,unsigned __int64,CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(
    _First,
    _Count,
    _Val,
    __formal,
    a5,
    a6);
}
