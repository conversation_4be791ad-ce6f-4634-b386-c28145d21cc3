/*
 * Function: ?dtor$1@?0??do_get@?$money_get@DV?$istreambuf_iterator@DU?$char_traits@D@std@@@std@@@std@@MEBA?AV?$istreambuf_iterator@DU?$char_traits@D@std@@@2@V32@0_NAEAVios_base@2@AEAHAEAO@Z@4HA
 * Address: 0x140599480
 */

void __fastcall `std::money_get<char,std::istreambuf_iterator<char,std::char_traits<char>>>::do_get'::`1'::dtor$1(__int64 a1, __int64 a2)
{
  CryptoPP::Integer::~Integer((CryptoPP::Integer *)(a2 + 112));
}
