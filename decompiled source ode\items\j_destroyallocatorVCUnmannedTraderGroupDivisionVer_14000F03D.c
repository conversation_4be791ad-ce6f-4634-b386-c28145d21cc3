/*
 * Function: j_?destroy@?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@QEAAXPEAVCUnmannedTraderGroupDivisionVersionInfo@@@Z
 * Address: 0x14000F03D
 */

void __fastcall std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::destroy(std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *this, CUnmannedTraderGroupDivisionVersionInfo *_Ptr)
{
  std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::destroy(this, _Ptr);
}
