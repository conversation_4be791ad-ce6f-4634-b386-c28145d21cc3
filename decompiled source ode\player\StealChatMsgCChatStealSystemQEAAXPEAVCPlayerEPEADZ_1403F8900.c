/*
 * Function: ?StealChatMsg@CChatStealSystem@@QEAAXPEAVCPlayer@@EPEAD@Z
 * Address: 0x1403F8900
 */

void __fastcall CChatStealSystem::StealChatMsg(CChatStealSystem *this, CPlayer *pPlayer, char byChatType, char *szChatMsg)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v6; // eax@10
  __int64 v7; // [sp+0h] [bp-58h]@1
  char *pwszSender; // [sp+20h] [bp-38h]@10
  char byRaceCode; // [sp+28h] [bp-30h]@10
  char *pwszMessage; // [sp+30h] [bp-28h]@10
  unsigned __int8 v11; // [sp+40h] [bp-18h]@6
  int v12; // [sp+44h] [bp-14h]@10
  char *v13; // [sp+48h] [bp-10h]@10
  CChatStealSystem *v14; // [sp+60h] [bp+8h]@1
  CPlayer *v15; // [sp+68h] [bp+10h]@1
  char v16; // [sp+70h] [bp+18h]@1
  char *v17; // [sp+78h] [bp+20h]@1

  v17 = szChatMsg;
  v16 = byChatType;
  v15 = pPlayer;
  v14 = this;
  v4 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v14->m_pGM )
  {
    if ( v14->m_TargetInfo.m_byStealType )
    {
      v11 = v14->m_TargetInfo.m_byStealType;
      if ( v11 )
      {
        if ( v11 <= 6u && pPlayer && v14->m_TargetInfo.m_dwTargetSerial == CPlayerDB::GetCharSerial(&pPlayer->m_Param) )
        {
          v12 = CPlayerDB::GetRaceCode(&v15->m_Param);
          v13 = CPlayerDB::GetCharNameW(&v15->m_Param);
          v6 = CPlayerDB::GetCharSerial(&v15->m_Param);
          pwszMessage = v17;
          byRaceCode = v12;
          pwszSender = v13;
          CChatStealSystem::SendStealMsg(v14, v14->m_pGM, v16, v6, v13, v12, v17);
        }
      }
    }
  }
}
