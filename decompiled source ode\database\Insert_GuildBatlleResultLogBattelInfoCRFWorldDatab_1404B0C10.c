/*
 * Function: ?Insert_GuildBatlleResultLogBattelInfo@CRFWorldDatabase@@QEAA_NPEAD0K0K0KKKKKKKKEK0K0EK0@Z
 * Address: 0x1404B0C10
 */

bool __fastcall CRFWorldDatabase::Insert_GuildBatlleResultLogBattelInfo(CRFWorldDatabase *this, char *szStartTime, char *szEndTime, unsigned int dwRedSerial, char *wszRedName, unsigned int dwBlueSerial, char *wszBlueName, unsigned int dwRedScore, unsigned int dwBlueScore, unsigned int dwRedMaxJoinCnt, unsigned int dwBlueMaxJoinCnt, unsigned int dwRedGoalCntSum, unsigned int dwBlueGoalCntSum, unsigned int dwRedKillCntSum, unsigned int dwBlueKillCntSum, char byBattleResult, unsigned int dwMaxGoalCharacSerial, char *wszMaxGoalCharacName, unsigned int dwMaxKillCharacSerial, char *wszMaxKillCharacName, char byJoinLimit, unsigned int dwGuildBattleCostGold, char *szBattleMapCode)
{
  __int64 *v23; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v26; // [sp+0h] [bp-508h]@1
  char *v27; // [sp+20h] [bp-4E8h]@4
  unsigned int v28; // [sp+28h] [bp-4E0h]@4
  char *v29; // [sp+30h] [bp-4D8h]@4
  unsigned int v30; // [sp+38h] [bp-4D0h]@4
  char *v31; // [sp+40h] [bp-4C8h]@4
  unsigned int v32; // [sp+48h] [bp-4C0h]@4
  unsigned int v33; // [sp+50h] [bp-4B8h]@4
  unsigned int v34; // [sp+58h] [bp-4B0h]@4
  unsigned int v35; // [sp+60h] [bp-4A8h]@4
  unsigned int v36; // [sp+68h] [bp-4A0h]@4
  unsigned int v37; // [sp+70h] [bp-498h]@4
  unsigned int v38; // [sp+78h] [bp-490h]@4
  unsigned int v39; // [sp+80h] [bp-488h]@4
  int v40; // [sp+88h] [bp-480h]@4
  unsigned int v41; // [sp+90h] [bp-478h]@4
  char *v42; // [sp+98h] [bp-470h]@4
  unsigned int v43; // [sp+A0h] [bp-468h]@4
  char *v44; // [sp+A8h] [bp-460h]@4
  int v45; // [sp+B0h] [bp-458h]@4
  unsigned int v46; // [sp+B8h] [bp-450h]@4
  char *v47; // [sp+C0h] [bp-448h]@4
  char DstBuf; // [sp+E0h] [bp-428h]@4
  unsigned __int64 v49; // [sp+4F0h] [bp-18h]@4
  CRFWorldDatabase *v50; // [sp+510h] [bp+8h]@1

  v50 = this;
  v23 = &v26;
  for ( i = 320i64; i; --i )
  {
    *(_DWORD *)v23 = -*********;
    v23 = (__int64 *)((char *)v23 + 4);
  }
  v49 = (unsigned __int64)&v26 ^ _security_cookie;
  v47 = szBattleMapCode;
  v46 = dwGuildBattleCostGold;
  v45 = (unsigned __int8)byJoinLimit;
  v44 = wszMaxKillCharacName;
  v43 = dwMaxKillCharacSerial;
  v42 = wszMaxGoalCharacName;
  v41 = dwMaxGoalCharacSerial;
  v40 = (unsigned __int8)byBattleResult;
  v39 = dwBlueKillCntSum;
  v38 = dwRedKillCntSum;
  v37 = dwBlueGoalCntSum;
  v36 = dwRedGoalCntSum;
  v35 = dwBlueMaxJoinCnt;
  v34 = dwRedMaxJoinCnt;
  v33 = dwBlueScore;
  v32 = dwRedScore;
  v31 = wszBlueName;
  v30 = dwBlueSerial;
  v29 = wszRedName;
  v28 = dwRedSerial;
  v27 = szEndTime;
  sprintf_s(
    &DstBuf,
    0x400ui64,
    "{ CALL pInsert_guildbattleresultlog('%s', '%s' , %d, '%s', %d, '%s', %d, %d, %d, %d, %d, %d, %d, %d, %u, %d, '%s', %"
    "d, '%s', %u, %d, '%s') }",
    szStartTime);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v50->vfptr, &DstBuf, 1);
}
