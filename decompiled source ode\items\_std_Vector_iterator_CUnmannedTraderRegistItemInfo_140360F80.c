/*
 * Function: _std::_Vector_iterator_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo___::operator__::_1_::dtor$1
 * Address: 0x140360F80
 */

void __fastcall std::_Vector_iterator_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo___::operator__::_1_::dtor_1(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 68) & 1 )
  {
    *(_DWORD *)(a2 + 68) &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(*(std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > **)(a2 + 104));
  }
}
